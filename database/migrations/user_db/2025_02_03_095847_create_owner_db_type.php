<?php

declare(strict_types=1);

use App\Database\Migrations\UserDatabaseMigration;
use Illuminate\Support\Facades\DB;

return new class () extends UserDatabaseMigration {
    public function up(): void
    {
        // Check if the type 'owner' already exists
        $typeExists = DB::select("SELECT 1 FROM pg_type WHERE typname = 'owner'");

        if ($typeExists) {
            return;
        }

        DB::statement(
            'create TYPE owner AS (
                owner_id int ,
                is_dead boolean,
                is_heritor boolean,
                has_heritors boolean,
                owner_name text ,
                greatest_grandparent int,
                greatest_grandparent_name text,
                "path" ltree,
                rep_name text
            );'
        );
    }

    public function down(): void
    {
        DB::statement('DROP TYPE owner');
    }
};
