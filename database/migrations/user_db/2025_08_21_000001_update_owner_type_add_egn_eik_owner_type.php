<?php

declare(strict_types=1);

use App\Database\Migrations\UserDatabaseMigration;
use Illuminate\Support\Facades\DB;

return new class () extends UserDatabaseMigration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Drop the existing owner type
        DB::statement('DROP TYPE IF EXISTS owner CASCADE');

        // Recreate the owner type with the new fields
        DB::statement(
            'CREATE TYPE owner AS (
                owner_id int,
                is_dead boolean,
                is_heritor boolean,
                has_heritors boolean,
                owner_name text,
                greatest_grandparent int,
                greatest_grandparent_name text,
                "path" ltree,
                rep_name text,
                egn text,
                eik text,
                owner_type int
            );'
        );
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop the updated type
        DB::statement('DROP TYPE IF EXISTS owner CASCADE');

        // Recreate the original owner type without the new fields
        DB::statement(
            'CREATE TYPE owner AS (
                owner_id int,
                is_dead boolean,
                is_heritor boolean,
                has_heritors boolean,
                owner_name text,
                greatest_grandparent int,
                greatest_grandparent_name text,
                "path" ltree,
                rep_name text
            );'
        );
    }
};
