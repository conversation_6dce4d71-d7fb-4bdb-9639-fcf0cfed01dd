<?php

declare(strict_types=1);

use App\Database\Migrations\UserDatabaseMigration;
use Illuminate\Support\Facades\DB;

return new class () extends UserDatabaseMigration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::unprepared("CREATE OR REPLACE FUNCTION
            get_farming_year_by_date(p_date DATE)
                RETURNS INT AS $$
                BEGIN
                    RETURN
                        CASE
                            WHEN p_date > (EXTRACT(YEAR FROM p_date) || '-09-30')::DATE
                                THEN (EXTRACT(YEAR FROM p_date) + 1)
                                ELSE EXTRACT(YEAR FROM p_date)
                        END;
            END;
            $$ LANGUAGE plpgsql;
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::unprepared('DROP FUNCTION IF EXISTS get_farming_year_by_date(p_date DATE);');
    }
};
