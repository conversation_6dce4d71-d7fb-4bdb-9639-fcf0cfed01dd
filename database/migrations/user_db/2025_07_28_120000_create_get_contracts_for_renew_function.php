<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Drop the existing function first to allow changing the return type
        DB::unprepared('DROP FUNCTION IF EXISTS get_contracts_for_renew(integer, boolean)');

        $sql = "CREATE FUNCTION get_contracts_for_renew(p_year INTEGER, p_is_sublease BOOLEAN)
                RETURNS TABLE (
                    cp_rel_id INTEGER,
                    c_num VARCHAR,
                    curr_contract_area NUMERIC,
                    curr_total_contract_area NUMERIC,
                    next_total_contract_area NUMERIC,
                    next_cp_rel_ids JSONB,
                    can_renew BOOLEAN,
                    can_renew_contract BOOLEAN,
                    has_decreased_area_next_year BOOLEAN,
                    sublease_cp_id INTEGER,
                    next_sublease_cp_ids JSONB
                ) AS \$\$
                BEGIN
                    RETURN QUERY
                        WITH next_year_plot_total_contract_area AS (
                            SELECT
                                next.plot_id,
                                next.document_area,
                                ROUND(SUM(next.contract_area)::NUMERIC, 3) AS total_contract_area,
                                JSONB_AGG(next.cp_rel_id) AS cp_rel_ids,
                                JSONB_AGG(next.sublease_cp_id) FILTER (WHERE next.sublease_cp_id IS NOT NULL) AS sublease_cp_ids
                            FROM
                                get_plots_in_contracts_for_year(p_year + 1, p_is_sublease) AS next
                            GROUP BY
                                next.plot_id,
                                next.document_area
                        ),
                        current_year_plot_total_contract_area AS (
                            SELECT
                                curr.plot_id,
                                curr.document_area,
                                ROUND(SUM(curr.contract_area)::NUMERIC, 3) AS total_contract_area
                            FROM
                                get_plots_in_contracts_for_year(p_year, p_is_sublease) AS curr
                            GROUP BY
                                curr.plot_id,
                                curr.document_area
                        ),
                        -- Legal basis check for subleases only
                        next_year_legal_basis AS (
                            SELECT
                                regular_contracts.plot_id,
                                regular_contracts.total_regular_area,
                                COALESCE(existing_subleases.total_sublease_area, 0) AS total_sublease_area,
                                (regular_contracts.total_regular_area - COALESCE(existing_subleases.total_sublease_area, 0)) AS available_area_for_sublease
                            FROM (
                                -- Calculate total area from regular contracts for next year
                                SELECT
                                    scpr.plot_id,
                                    ROUND(SUM(scpr.contract_area)::NUMERIC, 3) AS total_regular_area
                                FROM su_contracts AS sc
                                LEFT JOIN su_contracts AS a
                                    ON a.parent_id = sc.id
                                    AND a.active = true
                                    AND a.is_annex = true
                                    AND a.start_date < COALESCE(a.due_date, '9999-12-31'::DATE)
                                    AND TSRANGE(((p_year) || '-10-01')::DATE, ((p_year + 1) || '-09-30')::DATE, '[]') && TSRANGE(a.start_date, COALESCE(a.due_date, '9999-12-31'::DATE), '[]')
                                JOIN su_contracts_plots_rel AS scpr
                                    ON scpr.contract_id = COALESCE(a.id, sc.id)
                                    AND scpr.annex_action = 'added'
                                WHERE
                                    sc.active = TRUE
                                    AND sc.is_sublease = FALSE  -- Only regular contracts provide legal basis
                                    AND sc.is_annex = FALSE
                                    AND sc.start_date < COALESCE(sc.due_date, '9999-12-31'::DATE)
                                    AND TSRANGE(((p_year) || '-10-01')::DATE, ((p_year + 1) || '-09-30')::DATE, '[]') && TSRANGE(sc.start_date, COALESCE(sc.due_date, '9999-12-31'::DATE), '[]')
                                GROUP BY scpr.plot_id
                            ) AS regular_contracts
                            LEFT JOIN (
                                -- Calculate total area already committed to existing subleases for next year
                                SELECT
                                    scpr.plot_id,
                                    ROUND(SUM(CASE WHEN sc.is_sublease = TRUE THEN spa.contract_area ELSE scpr.contract_area END)::NUMERIC, 3) AS total_sublease_area
                                FROM su_contracts AS sc
                                LEFT JOIN su_contracts AS a
                                    ON a.parent_id = sc.id
                                    AND a.active = true
                                    AND a.is_annex = true
                                    AND a.start_date < COALESCE(a.due_date, '9999-12-31'::DATE)
                                    AND TSRANGE(((p_year) || '-10-01')::DATE, ((p_year + 1) || '-09-30')::DATE, '[]') && TSRANGE(a.start_date, COALESCE(a.due_date, '9999-12-31'::DATE), '[]')
                                LEFT JOIN su_subleases_plots_contracts_rel AS sspc
                                    ON sspc.sublease_id = sc.id AND sc.is_sublease = TRUE
                                LEFT JOIN su_subleases_plots_area AS spa
                                    ON spa.sublease_id = sc.id AND sc.is_sublease = true
                                JOIN su_contracts_plots_rel AS scpr
                                    ON (
                                        (scpr.contract_id = COALESCE(a.id, sc.id) AND sc.is_sublease = FALSE)
                                        OR
                                        (scpr.id = sspc.pc_rel_id AND sc.is_sublease = TRUE)
                                    )
                                    AND scpr.annex_action = 'added'
                                WHERE
                                    sc.active = TRUE
                                    AND sc.is_sublease = TRUE  -- Only existing subleases
                                    AND sc.is_annex = FALSE
                                    AND sc.start_date < COALESCE(sc.due_date, '9999-12-31'::DATE)
                                    AND TSRANGE(((p_year) || '-10-01')::DATE, ((p_year + 1) || '-09-30')::DATE, '[]') && TSRANGE(sc.start_date, COALESCE(sc.due_date, '9999-12-31'::DATE), '[]')
                                GROUP BY scpr.plot_id
                            ) AS existing_subleases
                                ON existing_subleases.plot_id = regular_contracts.plot_id
                        )
                        SELECT
                            curr.cp_rel_id,
                            curr.c_num,
                            curr.contract_area AS curr_contract_area,
                            curr_total.total_contract_area AS curr_total_contract_area,
                            next_total.total_contract_area AS next_total_contract_area,
                            next_total.cp_rel_ids as next_cp_rel_ids,
                            (
                                ROUND((COALESCE(next_total.total_contract_area, 0) + curr.contract_area)::NUMERIC, 3) <= ROUND(curr_total.document_area::NUMERIC, 3)
                                AND
                                (curr.contract_start_date = ((curr.year - 1) || '-10-01')::DATE AND curr.contract_due_date = ((curr.year || '-09-30')::DATE))
                                AND (
                                    -- For regular contracts (is_sublease = false), no legal basis check needed
                                    p_is_sublease = FALSE
                                    OR
                                    -- For subleases (is_sublease = true), check legal basis and area availability
                                    (legal_basis.plot_id IS NOT NULL AND curr.contract_area <= legal_basis.available_area_for_sublease)
                                )
                            ) AS can_renew,
                            -- Contract-level can_renew: ALL plots in the contract must be renewable
                            -- If ANY plot in the contract cannot be renewed, the entire contract is marked as non-renewable
                            bool_and(
                                (
                                    ROUND((COALESCE(next_total.total_contract_area, 0) + curr.contract_area)::NUMERIC, 3) <= ROUND(curr_total.document_area::NUMERIC, 3)
                                    AND
                                    (curr.contract_start_date = ((curr.year - 1) || '-10-01')::DATE AND curr.contract_due_date = ((curr.year || '-09-30')::DATE))
                                    AND (
                                        -- For regular contracts (is_sublease = false), no legal basis check needed
                                        p_is_sublease = FALSE
                                        OR
                                        -- For subleases (is_sublease = true), check legal basis and area availability
                                        (legal_basis.plot_id IS NOT NULL AND curr.contract_area <= legal_basis.available_area_for_sublease)
                                    )
                                )
                            ) OVER (PARTITION BY curr.contract_id) AS can_renew_contract,
                            COALESCE(next_total.total_contract_area, 0) < curr_total.total_contract_area AS has_decreased_area_next_year,
                            curr.sublease_cp_id AS sublease_cp_id,
                            next_total.sublease_cp_ids AS next_sublease_cp_ids
                        FROM
                            get_plots_in_contracts_for_year(p_year, p_is_sublease) AS curr
                        JOIN current_year_plot_total_contract_area AS curr_total
                            ON curr_total.plot_id = curr.plot_id
                        LEFT JOIN next_year_plot_total_contract_area AS next_total
                            ON next_total.plot_id = curr.plot_id
                        LEFT JOIN next_year_legal_basis AS legal_basis
                            ON legal_basis.plot_id = curr.plot_id;
                END;
                $$ LANGUAGE plpgsql;
        ";

        DB::unprepared($sql);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::unprepared('DROP FUNCTION get_contracts_for_renew(p_year integer, p_is_sublease BOOLEAN)');
    }
};
