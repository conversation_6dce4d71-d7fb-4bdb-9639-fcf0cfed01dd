<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::unprepared('DROP FUNCTION IF EXISTS get_plots_in_contracts_for_year(integer, boolean)');

        $sql = "CREATE OR REPLACE FUNCTION get_plots_in_contracts_for_year(p_year integer, p_is_sublease BOOLEAN)
                RETURNS TABLE (
                    year INTEGER,
                    cp_rel_id INTEGER,
                    contract_id INTEGER,
                    c_num VARCHAR,
                    contract_area NUMERIC,
                    contract_start_date DATE,
                    contract_due_date DATE,
                    plot_id INTEGER,
                    kad_ident VARCHAR(50),
                    document_area FLOAT4,
                    sublease_cp_id INTEGER
                ) AS \$\$
                BEGIN
                    RETURN QUERY
                        SELECT DISTINCT
                            p_year AS year,
                            MIN(scpr.id) AS cp_rel_id, -- Use MIN to get a consistent representative ID
                            CASE WHEN sc.is_sublease = TRUE
                                THEN sc.id
                                ELSE MIN(scpr.contract_id)
                            END AS contract_id,
                            COALESCE(a.c_num, sc.c_num) AS c_num,
                            (CASE WHEN sc.is_sublease = TRUE
                                THEN SUM(spa.contract_area)
                                ELSE SUM(scpr.contract_area)
                            END)::NUMERIC AS contract_area,
                            CASE WHEN a.id notnull
                                THEN a.start_date::DATE
                                ELSE sc.start_date::DATE
                            END AS contract_start_date,
                            CASE WHEN a.id notnull
                                THEN a.due_date::DATE
                                ELSE coalesce(sc.due_date::DATE, '9999-12-31'::DATE)
                            END AS contract_due_date,
                            scpr.plot_id,
                            kvs.kad_ident,
                            kvs.document_area,
                            CASE WHEN sc.is_sublease = TRUE
                                THEN MIN(sspc.id) -- Use MIN to get a consistent single ID for partitioning
                                ELSE NULL
                            END AS sublease_cp_id
                        FROM
                            su_contracts AS sc
                        LEFT JOIN su_contracts AS a
                            ON a.parent_id = sc.id
                            AND a.active = true
                            AND a.is_annex = true
                            AND a.start_date < COALESCE(a.due_date, '9999-12-31'::DATE)
                            AND TSRANGE(((p_year - 1) || '-10-01')::DATE, (p_year || '-09-30')::DATE, '[]') && TSRANGE(a.start_date, COALESCE(a.due_date, '9999-12-31'::DATE), '[]')
                        LEFT JOIN su_subleases_plots_contracts_rel AS sspc
                            ON sspc.sublease_id = sc.id AND sc.is_sublease = TRUE
                        LEFT JOIN su_subleases_plots_area AS spa
                            ON spa.sublease_id = sc.id AND sc.is_sublease = true
                        JOIN su_contracts_plots_rel AS scpr
                            ON (
                                (scpr.contract_id = coalesce(a.id, sc.id) AND sc.is_sublease = FALSE)
                                OR
                                (sc.is_sublease = TRUE AND scpr.id = sspc.pc_rel_id AND spa.plot_id = scpr.plot_id)
                            )
                            AND scpr.annex_action = 'added'
                        JOIN layer_kvs AS kvs
                            ON kvs.gid = scpr.plot_id
                        WHERE
                            sc.active = TRUE
                            AND sc.is_sublease = p_is_sublease
                            AND sc.start_date < COALESCE(sc.due_date, '9999-12-31'::DATE)
                            AND TSRANGE(((p_year - 1) || '-10-01')::DATE, (p_year || '-09-30')::DATE, '[]') && TSRANGE(sc.start_date, COALESCE(sc.due_date, '9999-12-31'::DATE), '[]')
                        GROUP BY
                            p_year,
                            sc.id, -- Group by the actual contract (sublease or regular)
                            scpr.plot_id,
                            COALESCE(a.c_num, sc.c_num),
                            CASE WHEN a.id notnull
                                THEN a.start_date::DATE
                                ELSE sc.start_date::DATE
                            END,
                            CASE WHEN a.id notnull
                                THEN a.due_date::DATE
                                ELSE coalesce(sc.due_date::DATE, '9999-12-31'::DATE)
                            END,
                            kvs.kad_ident,
                            kvs.document_area;
                END;
                $$ LANGUAGE plpgsql;
        ";

        DB::unprepared($sql);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::unprepared('DROP FUNCTION get_plots_in_contracts_for_year(p_year integer, p_is_sublease BOOLEAN)');
    }
};
