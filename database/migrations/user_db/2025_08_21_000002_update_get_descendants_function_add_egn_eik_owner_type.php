<?php

declare(strict_types=1);

use App\Database\Migrations\UserDatabaseMigration;
use Illuminate\Support\Facades\DB;

return new class () extends UserDatabaseMigration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement(
            "CREATE OR REPLACE FUNCTION get_descendants(_rows owner[], _path ltree, _level int)
                        RETURNS JSONB
                        LANGUAGE sql
                    AS $$
                    WITH children AS (
                        SELECT 
                            f.owner_id,
                            f.is_dead,
                            f.is_heritor,
                            f.has_heritors,
                            f.owner_name,
                            f.greatest_grandparent,
                            f.greatest_grandparent_name,
                            f.path,
                            f.rep_name,
                            f.egn,
                            f.eik,
                            f.owner_type
                        FROM unnest(_rows) AS f
                        WHERE subpath(f.path, 0, nlevel(f.path) - 1) = _path
                    )
                    SELECT jsonb_agg(
                        jsonb_build_object(
                            'owner_id', c.owner_id,
                            'is_dead', c.is_dead,
                            'is_heritor', c.is_heritor,
                            'isLeaf', get_descendants(_rows, c.path, _level+1) isnull,
                            'title', c.owner_name,
                            'greatest_grandparent', c.greatest_grandparent,
                            'greatest_grandparent_name', c.greatest_grandparent_name,
                            'key', c.path,
                            'rep_name', c.rep_name,
                            'egn', c.egn,
                            'eik', c.eik,
                            'owner_type', c.owner_type,
                            'level', _level+1,
                        'children',   COALESCE(get_descendants(_rows, c.path, _level+1), '[]'::jsonb)
                        )
                    )
                    FROM children c;
                    $$;"
        );
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Restore the original function without the new fields
        DB::statement(
            "CREATE OR REPLACE FUNCTION get_descendants(_rows owner[], _path ltree, _level int)
                        RETURNS JSONB
                        LANGUAGE sql
                    AS $$
                    WITH children AS (
                        SELECT 
                            f.owner_id,
                            f.is_dead,
                            f.is_heritor,
                            f.has_heritors,
                            f.owner_name,
                            f.greatest_grandparent,
                            f.greatest_grandparent_name,
                            f.path,
                            f.rep_name
                        FROM unnest(_rows) AS f
                        WHERE subpath(f.path, 0, nlevel(f.path) - 1) = _path
                    )
                    SELECT jsonb_agg(
                        jsonb_build_object(
                            'owner_id', c.owner_id,
                            'is_dead', c.is_dead,
                            'is_heritor', c.is_heritor,
                            'isLeaf', get_descendants(_rows, c.path, _level+1) isnull,
                            'title', c.owner_name,
                            'greatest_grandparent', c.greatest_grandparent,
                            'greatest_grandparent_name', c.greatest_grandparent_name,
                            'key', c.path,
                            'rep_name', c.rep_name,
                            'level', _level+1,
                        'children',   COALESCE(get_descendants(_rows, c.path, _level+1), '[]'::jsonb)
                        )
                    )
                    FROM children c;
                    $$;"
        );
    }
};
