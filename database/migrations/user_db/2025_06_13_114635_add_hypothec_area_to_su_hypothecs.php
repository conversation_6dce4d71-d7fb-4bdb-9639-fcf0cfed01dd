<?php

declare(strict_types=1);

use App\Database\Migrations\UserDatabaseMigration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends UserDatabaseMigration {
    /**
     * Run the migrations.
     */
    public function up()
    {
        // Add the 'hypothec_area' column to the 'su_hypothecs' table.
        // This column will store the calculated sum of hypothecated plot areas.
        Schema::table('su_hypothecs', function (Blueprint $table) {
            $table->float('hypothec_area', 3)->nullable()->comment('Calculated area from related hypothec plots');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        // Drop the 'hypothec_area' column if the migration is rolled back.
        Schema::table('su_hypothecs', function (Blueprint $table) {
            $table->dropColumn('hypothec_area');
        });
    }
};
