<?php

declare(strict_types=1);

use App\Database\Migrations\UserDatabaseMigration;
use Illuminate\Support\Facades\DB;

return new class () extends UserDatabaseMigration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("CREATE OR REPLACE FUNCTION percent_to_fraction(percent DOUBLE PRECISION)
                                RETURNS TEXT AS $$
                                DECLARE
                                    numerator INTEGER;
                                    denominator INTEGER := 100;
                                    gcd_value INTEGER;
                                    fraction TEXT;
                                BEGIN
                                    -- Round the percentage and convert it to INTEGER
                                    numerator := ROUND(percent)::INTEGER;

                                    -- Find the greatest common divisor (GCD)
                                    gcd_value := gcd(numerator, denominator);

                                    -- Generate the fractional format
                                    IF numerator % denominator = 0 THEN
                                        fraction := (numerator / gcd_value)::TEXT;  -- Integer only if no fractional part
                                    ELSE
                                        fraction := (numerator / gcd_value)::TEXT || '/' || (denominator / gcd_value)::TEXT;
                                    END IF;

                                    RETURN fraction;
                                END;
                                $$ LANGUAGE plpgsql IMMUTABLE;
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::statement('DROP FUNCTION IF EXISTS percent_to_fraction(percent)');
    }
};
