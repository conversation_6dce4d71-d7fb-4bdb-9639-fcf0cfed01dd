<?php

declare(strict_types=1);

use App\Database\Migrations\UserDatabaseMigration;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends UserDatabaseMigration {
    /**
     * Run the migrations.
     */
    public function up()
    {
        // Add the 'total_contract_area' column to the 'su_contracts' table.
        // This column will store the calculated sum of plot areas.
        Schema::table('su_contracts', function (Blueprint $table) {
            $table->float('total_contract_area', 3)->nullable()->comment('Calculated total area from related plots with annex_action = added');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        // Drop the 'total_contract_area' column if the migration is rolled back.
        Schema::table('su_contracts', function (Blueprint $table) {
            $table->dropColumn('total_contract_area');
        });
    }
};
