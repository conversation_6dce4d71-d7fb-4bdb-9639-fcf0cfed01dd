<?php

declare(strict_types=1);

use App\Database\Migrations\UserDatabaseMigration;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class () extends UserDatabaseMigration {
    /**
     * Run the migrations.
     */
    public function up()
    {
        // This function takes a hypothec ID as input.
        // It calculates the sum of 'hypothec_area' from 'su_hypothecs_plots_rel'
        // for a specific hypothec_id.
        // COALESCE is used to return 0 if there are no matching plots, preventing NULL results.
        DB::unprepared('
            CREATE OR REPLACE FUNCTION calculate_hypothec_area(p_hypothec_id INT)
            RETURNS FLOAT4 AS $$
            DECLARE
                total_area FLOAT4;
            BEGIN
                SELECT COALESCE(SUM(hypothec_area), 0)
                INTO total_area
                FROM su_hypothecs_plots_rel
                WHERE hypothec_id = p_hypothec_id;

                RETURN total_area;
            END;
            $$ LANGUAGE plpgsql;
        ');
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        // Drops the function if the migration is rolled back.
        DB::unprepared('DROP FUNCTION IF EXISTS calculate_hypothec_area(INT);');
    }
};
