<?php

declare(strict_types=1);

use App\Database\Migrations\UserDatabaseMigration;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class () extends UserDatabaseMigration {
    /**
     * Run the migrations.
     */
    public function up()
    {
        // This function takes a sales contract ID as input.
        // It calculates the sum of 'contract_area' from 'su_sales_contracts_plots_rel' for the given sales contract ID.
        // COALESCE is used to return 0 if there are no matching plots, preventing NULL results.
        DB::unprepared('
            CREATE OR REPLACE FUNCTION calculate_sales_contract_area(p_sales_contract_id INT)
            RETURNS FLOAT4 AS $$
            DECLARE
                total_area FLOAT4;
            BEGIN
                SELECT COALESCE(SUM(contract_area), 0)
                INTO total_area
                FROM su_sales_contracts_plots_rel
                WHERE sales_contract_id = p_sales_contract_id;

                RETURN total_area;
            END;
            $$ LANGUAGE plpgsql;
        ');
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        // Drops the function if the migration is rolled back.
        DB::unprepared('DROP FUNCTION IF EXISTS calculate_sales_contract_area(INT);');
    }
};
