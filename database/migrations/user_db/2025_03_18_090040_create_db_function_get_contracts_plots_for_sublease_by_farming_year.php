<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("CREATE OR REPLACE FUNCTION get_contracts_plots_for_sublease_by_farming_year(f_year INT)
            RETURNS TABLE (contract_id INT, plot_id INT) AS
            $$
            DECLARE
                f_year_start DATE;
                f_year_end	DATE;
                f_year_text VARCHAR;
            BEGIN
                f_year_start := ((f_year -1) || '-10-01')::DATE;
                f_year_end := (f_year || '-09-30')::DATE;
                f_year_text := (f_year -1) || '/' || f_year;
                
                RETURN QUERY 
                SELECT
                    DISTINCT c.id, kvs.gid
                FROM
                    layer_kvs kvs
                LEFT JOIN su_contracts_plots_rel pc
                    ON pc.plot_id = kvs.gid
                LEFT JOIN su_contracts c 
                    ON c.id = pc.contract_id
                LEFT JOIN (
                        SELECT
                            scpr.contract_id,
                            scpr.plot_id,
                            SUM(scpr.contract_area_for_sale) OVER (
                                PARTITION BY scpr.contract_id
                                ORDER BY sc.start_date 
                                ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
                            ) AS cumulative_sold_area,
                            MAX(scpr.contract_area) AS total_area,
                            sc.start_date AS sale_start_date
                        FROM
                            su_sales_contracts sc
                        JOIN su_sales_contracts_plots_rel scpr
                            ON sc.id = scpr.sales_contract_id
                        LEFT JOIN su_contracts_plots_rel cpr 
                            ON scpr.contract_id = cpr.contract_id
                            AND cpr.plot_id = scpr.plot_id
                        LEFT JOIN su_contracts c
                            ON c.id = cpr.contract_id
                        GROUP BY
                            scpr.contract_id,
                            scpr.plot_id,
                            sc.start_date,
                            scpr.contract_area_for_sale
                ) sp 
                    ON sp.plot_id = kvs.gid
                    AND sp.contract_id = c.id
                LEFT JOIN get_subleased_plots_for_farming_year(f_year) as sub_p
                    ON sub_p.gid = kvs.gid 
                    AND c.id IN (sub_p.contract_id, sub_p.annex_id, sub_p.parent_id)
                    AND (sub_p.total_area_for_rent - sub_p.total_subleased_area) <= 0.002
                LEFT JOIN su_contracts a ON
                    (
                        a.parent_id = c.id
                            AND a.active = TRUE
                            AND (
                                -- The annex starts before the beginning of the farming year and ends after it
                                (a.start_date::date < f_year_start AND a.due_date::date > f_year_end)
                                -- The annex starts before the beginning of the farming year and ends within it
                                OR (a.start_date::date < f_year_start AND a.due_date::date BETWEEN f_year_start AND f_year_end)
                                --The annex starts in the farming year and ends after it
                                OR (a.start_date::date BETWEEN f_year_start AND f_year_end AND a.due_date::date > f_year_end)
                                -- The annex starts and ends within the farming year
                                OR (a.start_date::date BETWEEN f_year_start AND f_year_end AND a.due_date::date BETWEEN f_year_start AND f_year_end)
                            )
                    )
                LEFT JOIN su_contracts_plots_rel a_pc
                    ON a_pc.plot_id = kvs.gid
                    AND a_pc.contract_id = a.id
                WHERE
                    (
                        c.active = TRUE
                        AND (
                            CASE WHEN a.id NOTNULL 
                                THEN a_pc.annex_action <> 'removed' 
                                ELSE pc.annex_action <> 'removed' 
                            END
                        ) = TRUE
                        AND (
                            CASE WHEN sub_p.farming_year_text = f_year_text
                                THEN FALSE
                                ELSE TRUE
                            END
                        ) = TRUE
                    )
                    AND (
                        kvs.is_edited = FALSE
                        OR (kvs.is_edited = TRUE AND kvs.edit_active_from::date >= now())
                    ) = TRUE
                    AND (
                        (
                            c.due_date NOTNULL
                            AND (
                                -- The contract starts before the beginning of the farming year and ends after it
                                (c.start_date::date < f_year_start AND c.due_date::date > f_year_end)
                                -- The contract starts before the beginning of the farming year and ends within it
                                OR (c.start_date::date < f_year_start AND c.due_date::date BETWEEN f_year_start AND f_year_end)
                                --The contract starts in the farming year and ends after it
                                OR (c.start_date::date BETWEEN f_year_start AND f_year_end AND c.due_date::date >f_year_end)
                                -- The contract starts and ends within the farming year
                                OR (c.start_date::date BETWEEN f_year_start AND f_year_end AND c.due_date::date BETWEEN f_year_start AND f_year_end)
                            )
                        ) = TRUE
                        OR (
                            c.due_date IS NULL
                            AND c.start_date::date < f_year_end
                            AND (
                                CASE
                                    WHEN sp.sale_start_date IS NULL 
                                        THEN TRUE
                                    WHEN sp.sale_start_date NOTNULL
                                    AND c.start_date < sp.sale_start_date
                                    AND c.start_date < f_year_end
                                    AND sp.sale_start_date > f_year_end
                                        THEN TRUE
                                    WHEN sp.sale_start_date NOTNULL
                                    AND c.start_date < sp.sale_start_date
                                    AND c.start_date < f_year_end
                                    AND sp.sale_start_date BETWEEN f_year_start AND f_year_end
                                    AND sp.cumulative_sold_area < sp.total_area
                                        THEN TRUE
                                END
                            )
                        ) = TRUE
                    )
                    ;
            END;
            $$ LANGUAGE plpgsql;
       ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::statement('DROP FUNCTION IF EXISTS get_contracts_plots_for_sublease_by_farming_year(f_year INT);');
    }
};
