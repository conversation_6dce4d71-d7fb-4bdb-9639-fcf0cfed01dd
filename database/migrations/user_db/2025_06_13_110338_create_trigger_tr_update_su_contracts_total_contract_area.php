<?php

declare(strict_types=1);

use App\Database\Migrations\UserDatabaseMigration;
use Illuminate\Support\Facades\DB;

return new class () extends UserDatabaseMigration {
    /**
     * Run the migrations.
     */
    public function up()
    {
        // Create the trigger function
        // This function handles the logic for getting the correct contract_id
        // based on the operation (INSERT, UPDATE, or DELETE) and then
        // calls the 'calculate_contract_area' function to get the new total area.
        // It then updates the 'contract_area' column in the 'su_contracts' table.
        DB::unprepared("
            CREATE OR REPLACE FUNCTION update_su_contracts_total_contract_area()
            RETURNS TRIGGER AS $$
            BEGIN
               -- Update the 'total_contract_area' column in the corresponding contracts.
                IF (TG_OP = 'DELETE') THEN
                    UPDATE su_contracts
                    SET total_contract_area = calculate_contract_area(OLD.contract_id)
                    WHERE id = OLD.contract_id;
                ELSIF (TG_OP = 'UPDATE') THEN
                    UPDATE su_contracts
                    SET total_contract_area = calculate_contract_area(NEW.contract_id)
                    WHERE id = NEW.contract_id;

                    UPDATE su_contracts
                    SET total_contract_area = calculate_contract_area(OLD.contract_id)
                    WHERE id = OLD.contract_id;
                ELSE
                    UPDATE su_contracts
                    SET total_contract_area = calculate_contract_area(NEW.contract_id)
                    WHERE id = NEW.contract_id;
                END IF;

                -- Return the appropriate record to complete the trigger operation.
                IF (TG_OP = 'DELETE') THEN
                    RETURN OLD;
                ELSE
                    RETURN NEW;
                END IF;
            END;
            $$ LANGUAGE plpgsql;
        ");

        // Create the actual trigger on the 'su_contracts_plots_rel' table.
        // This trigger will fire AFTER any INSERT, UPDATE, or DELETE operation,
        // for each row that is affected.
        DB::unprepared('
            CREATE TRIGGER tr_update_su_contracts_total_contract_area
            AFTER INSERT OR DELETE OR UPDATE OF contract_area, contract_id ON su_contracts_plots_rel
            FOR EACH ROW EXECUTE FUNCTION update_su_contracts_total_contract_area();
        ');


        // Initialize the total_contract_area for existing records in su_contracts_plots_rel.
        DB::unprepared('UPDATE su_contracts SET total_contract_area = calculate_contract_area(id) WHERE is_sublease=false;');
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        DB::unprepared('DROP TRIGGER IF EXISTS tr_update_su_contracts_total_contract_area ON su_contracts_plots_rel;');
        DB::unprepared('DROP FUNCTION IF EXISTS update_su_contracts_total_contract_area();');
    }
};
