<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement(
            "CREATE OR REPLACE FUNCTION get_subleased_plots_for_farming_year(f_year INT)
            RETURNS TABLE (
                contract_id INT,
                annex_id INT,
                parent_id INT,
                gid INT,
                farming_year_start DATE,
                farming_year_end DATE,
                farming_year INT,
                farming_year_text TEXT,
                total_subleased_area DOUBLE PRECISION,
                total_area_for_rent DOUBLE PRECISION
            ) AS $$
            BEGIN
                RETURN QUERY 
                WITH farming_year AS (
                    SELECT
                        ((f_year - 1) || '-10-01')::date AS start_date,
                        (f_year || '-09-30')::date AS end_date,
                        f_year AS \"year\",
                        (f_year - 1) || '/' || f_year AS year_text
                )
                SELECT
                    pc.contract_id,
                    a.id AS annex_id,
                    c1.parent_id,
                    kvs.gid,
                    fy.start_date AS farming_year_start,
                    fy.end_date AS farming_year_end,
                    fy.\"year\" AS farming_year,
                    fy.year_text AS farming_year_text,
                    SUM(COALESCE(spa.contract_area, 0))::double precision AS total_subleased_area,
                    MAX(pc.contract_area)::double precision  AS total_area_for_rent
                FROM 
                    layer_kvs kvs
                INNER JOIN su_contracts_plots_rel pc 
                    ON pc.plot_id = kvs.gid
                INNER JOIN su_subleases_plots_contracts_rel spc 
                    ON spc.pc_rel_id = pc.id
                LEFT JOIN su_subleases_plots_area spa
                    ON spa.plot_id = pc.plot_id
                    AND spa.sublease_id = spc.sublease_id
                INNER JOIN su_contracts c 
                    ON c.id = spc.sublease_id
                INNER JOIN farming_year AS fy 
                    ON c.start_date <= fy.end_date
                    AND c.due_date >= fy.start_date
                INNER JOIN su_contracts c1 
                    ON c1.id = pc.contract_id
                LEFT JOIN su_contracts a 
                    ON a.parent_id = c1.id
                    AND a.active = TRUE
                LEFT JOIN (
                    SELECT
                        scpr.contract_id,
                        scpr.plot_id,
                        SUM(scpr.contract_area_for_sale) AS sold_area,
                        MAX(scpr.contract_area) AS total_area
                    FROM 
                        su_sales_contracts_plots_rel scpr
                    INNER JOIN su_sales_contracts sc 
                        ON sc.id = scpr.sales_contract_id
                    GROUP BY
                         scpr.contract_id, scpr.plot_id
                ) AS t2 ON t2.contract_id = c.id AND t2.plot_id = pc.plot_id
                WHERE
                    (CASE WHEN t2.total_area IS NOT NULL 
                        THEN t2.total_area > t2.sold_area
                        ELSE c.id > 0
                    END)
                    AND kvs.is_edited = FALSE
                    AND (kvs.edit_active_from <= now() OR kvs.edit_active_from IS NULL)
                    AND (
                        c.is_sublease = TRUE
                        AND c.active = TRUE
                    )
                GROUP BY
                    pc.contract_id,
                    a.id,
                    c1.parent_id,
                    kvs.gid,
                    fy.start_date,
                    fy.end_date,
                    fy.\"year\",
                    fy.year_text;
            END
        $$
        LANGUAGE plpgsql;"
        );
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::statement('DROP FUNCTION IF EXISTS get_subleased_plots_for_farming_year(f_year INT);');
    }
};
