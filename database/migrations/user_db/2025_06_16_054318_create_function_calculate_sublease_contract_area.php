<?php

declare(strict_types=1);

use App\Database\Migrations\UserDatabaseMigration;
use Illuminate\Support\Facades\DB;

return new class () extends UserDatabaseMigration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // This function takes a sublease contract ID as input.
        // It calculates the sum of 'contract_area' from 'su_subleases_plots_area' for a specific contract_id
        // COALESCE is used to return 0 if there are no matching plots, preventing NULL results.
        DB::unprepared('
            CREATE OR REPLACE FUNCTION calculate_sublease_contract_area(p_contract_id INT)
            RETURNS FLOAT4 AS $$
            DECLARE
                total_area FLOAT4;
            BEGIN
                SELECT COALESCE(SUM(sspa.contract_area), 0) INTO total_area
                FROM su_contracts
                JOIN su_subleases_plots_area AS sspa 
                    ON sspa.sublease_id = su_contracts.id 
                WHERE 
                    su_contracts.is_sublease = TRUE
                    AND su_contracts.id = p_contract_id;

                RETURN total_area;
            END;
            $$ LANGUAGE plpgsql;
        ');
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        // Drops the function if the migration is rolled back.
        DB::unprepared('DROP FUNCTION IF EXISTS calculate_sublease_contract_area(INT);');
    }
};
