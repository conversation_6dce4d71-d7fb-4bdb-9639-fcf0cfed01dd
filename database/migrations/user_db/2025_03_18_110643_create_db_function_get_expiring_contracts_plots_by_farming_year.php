<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("CREATE OR REPLACE FUNCTION get_expiring_contracts_plots_by_farming_year(f_year INT)
            RETURNS TABLE (contract_id INT, plot_id INT) AS
            $$
            DECLARE
                f_year_start DATE;
                f_year_end	DATE;
                f_year_text VARCHAR;
            BEGIN
                f_year_start := ((f_year -1) || '-10-01')::DATE;
                f_year_end := (f_year || '-09-30')::DATE;
                f_year_text := (f_year -1) || '/' || f_year;
                
                RETURN QUERY 
                SELECT
                    DISTINCT c.id, kvs.gid
                FROM
                    layer_kvs kvs
                LEFT JOIN su_contracts_plots_rel pc
                    ON pc.plot_id = kvs.gid
                LEFT JOIN su_contracts c 
                    ON c.id = pc.contract_id
                LEFT JOIN (
                        SELECT
                            scpr.contract_id,
                            scpr.plot_id,
                            SUM(scpr.contract_area_for_sale) OVER (
                                PARTITION BY scpr.contract_id
                                ORDER BY sc.start_date 
                                ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
                            ) AS cumulative_sold_area,
                            MAX(scpr.contract_area) AS total_area,
                            sc.start_date AS sale_start_date
                        FROM
                            su_sales_contracts sc
                        JOIN su_sales_contracts_plots_rel scpr
                            ON sc.id = scpr.sales_contract_id
                        LEFT JOIN su_contracts_plots_rel cpr 
                            ON scpr.contract_id = cpr.contract_id
                            AND cpr.plot_id = scpr.plot_id
                        LEFT JOIN su_contracts c
                            ON c.id = cpr.contract_id
                        GROUP BY
                            scpr.contract_id,
                            scpr.plot_id,
                            sc.start_date,
                            scpr.contract_area_for_sale
                ) sp 
                    ON sp.plot_id = kvs.gid
                    AND sp.contract_id = c.id
				LEFT JOIN (
					SELECT
						cpr.plot_id AS next_plot_id,
						c.start_date AS next_contract_start_date,
						c.id AS contract_id,
						c.start_date,
						c.due_date
					FROM
						su_contracts c
					JOIN su_contracts_plots_rel cpr ON
						c.id = cpr.contract_id
					WHERE
						c.active = TRUE
				) next_contract 
					ON next_contract.next_plot_id = kvs.gid
					AND next_contract.contract_id > c.id
					AND (
						next_contract.next_contract_start_date BETWEEN (f_year_start + INTERVAL '1 year') AND (f_year_end + INTERVAL '1 year')
					)
                LEFT JOIN su_contracts a 
					ON a.parent_id = c.id
					AND a.active = true
                LEFT JOIN su_contracts_plots_rel a_pc
                    ON a_pc.plot_id = kvs.gid
                    AND a_pc.contract_id = a.id
                WHERE
                    c.active = TRUE
                    AND (
                        CASE WHEN a.id NOTNULL 
                            THEN a_pc.annex_action <> 'removed' 
                            ELSE pc.annex_action <> 'removed' 
                        END
                    ) = TRUE
                    AND (
                        kvs.is_edited = FALSE
                        OR (kvs.is_edited = TRUE AND kvs.edit_active_from::date >= now())
                    ) = TRUE
					AND (
						CASE
							WHEN c.due_date IS NULL
							AND sp.sale_start_date BETWEEN (f_year_start + INTERVAL '1 year') AND (f_year_end + INTERVAL '1 year')
                                THEN TRUE
							WHEN c.due_date BETWEEN f_year_start AND f_year_end
							AND next_contract.next_contract_start_date IS NULL 
                                THEN TRUE
							ELSE 
								FALSE
						END
					) = TRUE;
            END;
            $$ LANGUAGE plpgsql;
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::statement('DROP FUNCTION IF EXISTS get_expiring_contracts_plots_by_farming_year(f_year INT)');
    }
};
