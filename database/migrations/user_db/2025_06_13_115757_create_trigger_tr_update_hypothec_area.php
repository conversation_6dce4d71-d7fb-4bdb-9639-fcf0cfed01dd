<?php

declare(strict_types=1);

use App\Database\Migrations\UserDatabaseMigration;
use Illuminate\Support\Facades\DB;

return new class () extends UserDatabaseMigration {
    /**
     * Run the migrations.
     */
    public function up()
    {
        // Create the trigger function that will be executed by the trigger for hypothecs.
        // This function determines the correct hypothec_id based on the operation,
        // calls the calculation function, and updates the 'hypothec_area' column
        // in the 'su_hypothecs' table.
        DB::unprepared("
            CREATE OR REPLACE FUNCTION update_su_hypothecs_hypothec_area()
            RETURNS TRIGGER AS $$
            BEGIN
                IF (TG_OP = 'DELETE') THEN
                    UPDATE su_hypothecs
                    SET hypothec_area = calculate_hypothec_area(OLD.hypothec_id)
                    WHERE id = OLD.hypothec_id;
                ELSIF (TG_OP = 'UPDATE') THEN
                    UPDATE su_hypothecs
                    SET hypothec_area = calculate_hypothec_area(NEW.hypothec_id)
                    WHERE id = NEW.hypothec_id;

                    UPDATE su_hypothecs
                    SET hypothec_area = calculate_hypothec_area(OLD.hypothec_id)
                    WHERE id = OLD.hypothec_id;
                ELSE
                    UPDATE su_hypothecs
                    SET hypothec_area = calculate_hypothec_area(NEW.hypothec_id)
                    WHERE id = NEW.hypothec_id;
                END IF;

                -- Return the appropriate record to complete the trigger operation.
                IF (TG_OP = 'DELETE') THEN
                    RETURN OLD;
                ELSE
                    RETURN NEW;
                END IF;
            END;
            $$ LANGUAGE plpgsql;
        ");

        // Create the trigger on the 'su_hypothecs_plots_rel' table.
        // This trigger will fire AFTER any INSERT or DELETE operation, or when
        // the 'hypothec_area' column is updated.
        DB::unprepared('
            CREATE TRIGGER tr_update_hypothec_area
            AFTER INSERT OR DELETE OR UPDATE OF hypothec_area, hypothec_id ON su_hypothecs_plots_rel
            FOR EACH ROW EXECUTE FUNCTION update_su_hypothecs_hypothec_area();
        ');

        // Initialize the hypothec_area for existing records in su_hypothecs_plots_rel.
        DB::unprepared('UPDATE su_hypothecs SET hypothec_area = calculate_hypothec_area(id);');
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        // To roll back, we must first drop the trigger from the table,
        // and then drop the trigger function itself.
        DB::unprepared('DROP TRIGGER IF EXISTS tr_update_hypothec_area ON su_hypothecs_plots_rel;');
        DB::unprepared('DROP FUNCTION IF EXISTS update_su_hypothecs_hypothec_area();');
    }
};
