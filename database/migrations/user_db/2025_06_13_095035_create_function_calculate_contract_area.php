<?php

declare(strict_types=1);

use App\Database\Migrations\UserDatabaseMigration;
use Illuminate\Support\Facades\DB;

return new class () extends UserDatabaseMigration {
    /**
     * Run the migrations.
     */
    public function up()
    {
        // This function takes a contract ID as input.
        // It calculates the sum of 'contract_area' from 'su_contracts_plots_rel'
        // for a specific contract_id, but only includes plots where annex_action is 'added'.
        // COALESCE is used to return 0 if there are no matching plots, preventing NULL results.
        DB::unprepared("
            CREATE OR REPLACE FUNCTION calculate_contract_area(p_contract_id INT)
            RETURNS FLOAT4 AS $$
            DECLARE
                total_area FLOAT4;
            BEGIN
                SELECT COALESCE(SUM(scpr.contract_area), 0) INTO total_area
                FROM su_contracts AS c
                JOIN su_contracts_plots_rel AS scpr
                    ON scpr.contract_id = c.id
                WHERE 
                    c.id = p_contract_id
                    and c.is_sublease = false
                    AND scpr.annex_action = 'added';

                RETURN total_area;
            END;
            $$ LANGUAGE plpgsql;
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        // Drops the function if the migration is rolled back.
        DB::unprepared('DROP FUNCTION IF EXISTS calculate_contract_area(INT);');
    }
};
