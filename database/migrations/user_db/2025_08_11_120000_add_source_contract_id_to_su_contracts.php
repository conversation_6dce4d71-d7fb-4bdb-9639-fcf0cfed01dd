<?php

declare(strict_types=1);

use App\Database\Migrations\UserDatabaseMigration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends UserDatabaseMigration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('su_contracts', function (Blueprint $table) {
            $table->integer('source_contract_id')->nullable();
            $table->foreign('source_contract_id')->references('id')->on('su_contracts');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('su_contracts', function (Blueprint $table) {
            $table->dropForeign(['source_contract_id']);
            $table->dropColumn('source_contract_id');
        });
    }
};
