<?php

declare(strict_types=1);

use App\Database\Migrations\UserDatabaseMigration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class () extends UserDatabaseMigration {
    public function up(): void
    {
        Schema::table('su_sales_contracts', function (Blueprint $table) {
            $table->string('farming_name')->nullable();
        });

        DB::statement(
            'CREATE OR REPLACE TRIGGER tr_fill_farming_name_on_insert
            BEFORE INSERT ON su_sales_contracts
            FOR EACH ROW
            EXECUTE FUNCTION fill_su_contracts_farming_name();
        '
        );

        DB::statement(
            'CREATE OR REPLACE TRIGGER tr_fill_farming_name_on_update
            BEFORE UPDATE OF farming_id ON su_sales_contracts
            FOR EACH ROW
            EXECUTE FUNCTION fill_su_contracts_farming_name();
        '
        );

        DB::statement(
            'UPDATE su_sales_contracts
                    SET farming_id = farming_id;
        '
        );


    }

    public function down(): void
    {
        DB::statement('DROP TRIGGER IF EXISTS tr_fill_farming_name_on_insert ON su_sales_contracts');
        DB::statement('DROP TRIGGER IF EXISTS tr_fill_farming_name_on_update ON su_sales_contracts');

        Schema::table('su_sales_contracts', function (Blueprint $table) {
            $table->dropColumn('farming_name');
        });
    }
};
