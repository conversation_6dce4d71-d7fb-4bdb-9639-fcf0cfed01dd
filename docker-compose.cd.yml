version: "3.7"

services:
    app:
        build:
            context: .
            dockerfile: ./.docker/Dockerfile
            args:
                - APP_ENV=prod
                - GROUP_ID=${GROUP_ID:-1000}
                - USER_ID=${USER_ID:-1000}
                - USER_NAME=${USER_NAME:-appuser}
                - USER_GROUP=${USER_GROUP:-appgroup}
                - APP_DIR=${APP_DIR:-/var/www/html/app}
        image: ${BITBUCKET_REPO_SLUG}:${BITBUCKET_COMMIT}
        environment:
            - APP_ENV=prod
            - APP_DEBUG=false
    web:
        image: ${BITBUCKET_REPO_SLUG}-nginx:${BITBUCKET_COMMIT}
        build:
            context: .
            dockerfile: ./.docker/nginx/Dockerfile
            args:
                - GROUP_ID=${GROUP_ID:-1000}
                - USER_ID=${USER_ID:-1000}
                - USER_NAME=${USER_NAME:-appuser}
                - USER_GROUP=${USER_GROUP:-appgroup}
                - APP_DIR=${APP_DIR:-/var/www/html/app}
        environment:
          - CONTAINER_NAME=${CONTAINER_NAME}