apiVersion: batch/v1
kind: CronJob
metadata:
  name: s3-backup-user-files
  namespace: REPLACE_NAMESPACE
spec:
  schedule: "0 2 * * *"
  successfulJobsHistoryLimit: 3
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: tf-technofarm-rclone
            image: rclone/rclone
            imagePullPolicy: "Always"
            command:
            - /bin/sh
            - -c
            - "rclone sync -P /var/www/html/app/public/files/ tf-s3://tf-user-files/technofarm/"
            volumeMounts:
            - name: maps
              mountPath: /var/www/html/app/maps
            - name: files
              mountPath: /var/www/html/app/public/files
            - name: rclone
              mountPath: /root/.config/rclone/rclone.conf
              subPath: rclone.conf
              readOnly: true
          restartPolicy: OnFailure
          imagePullSecrets:
          - name: docker-technofarm
          volumes:
          - name: maps
            nfs:
              server: REPLACE_NFS_SERVER
              path: REPLACE_NFS_DIR_MAPS
          - name: files
            nfs:
              server: REPLACE_NFS_SERVER
              path: REPLACE_NFS_DIR_FILES
          - name: rclone
            secret:
              secretName: rclone-s3