---
  apiVersion: "networking.k8s.io/v1"
  kind: "Ingress"
  metadata:
    name: REPLACE_HOSTNAME
    namespace: REPLACE_KUBE_NAMESPACE
    annotations:
      kubernetes.io/ingress.class: nginx
      cert-manager.io/cluster-issuer: letsencrypt-production
      nginx.ingress.kubernetes.io/proxy-buffer-size: 16k
      nginx.ingress.kubernetes.io/proxy-buffers-number: '4'
      nginx.ingress.kubernetes.io/rewrite-target: /$1
  spec:
    rules:
    - host: "REPLACE_HOSTNAME"
      http:
        paths:
        - backend:
            service:
              name: "REPLACE_CONTAINER_NAME-nginx"
              port:
                number: 80
          path: "/(.*)"
          pathType: "ImplementationSpecific"
    tls:
    - hosts:
      - REPLACE_HOSTNAME
      secretName: REPLACE_HOSTNAME