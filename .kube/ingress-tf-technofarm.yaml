apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: REPLA<PERSON>_KUBE_INGRESS_HOSTNAME
  namespace: REPLACE_NAMESPACE
  annotations:
    kubernetes.io/ingress.class: nginx
spec:
  tls:
    - hosts:
        - REPLACE_KUBE_INGRESS_HOSTNAME
      secretName: REPLACE_KUBE_INGRESS_HOSTNAME
  rules:
    - host: REPLACE_KUBE_INGRESS_HOSTNAME
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: tf-technofarm-nginx
                port:
                  number: 8080
          - path: /mapserv
            pathType: Prefix
            backend:
              service:
                name: tf-technofarm-mapserver
                port:
                  number: 6005
          - path: /mapcache
            pathType: Prefix
            backend:
              service:
                name: tf-technofarm-mapcache
                port:
                  number: 6004
          - path: /common
            pathType: Prefix
            backend:
              service:
                name: tf-common-services-nginx
                port:
                  number: 80