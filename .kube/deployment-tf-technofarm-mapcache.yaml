apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: tf-technofarm-mapcache
  name: tf-technofarm-mapcache
  namespace: REPLACE_NAMESPACE
spec:
  replicas: 1
  selector:
    matchLabels:
      app: tf-technofarm-mapcache
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: tf-technofarm-mapcache
    spec:
      serviceAccount: bitbucket
      containers:
      - image: technofarm/tf-mapcache
        imagePullPolicy: Always
        livenessProbe:
          httpGet:
            path: /index.html
            port: 80
          initialDelaySeconds: 3
          periodSeconds: 3
        readinessProbe:
          httpGet:
            path: /index.html
            port: 80
          initialDelaySeconds: 1
          periodSeconds: 5
        name: tf-technofarm-mapcache
        envFrom:
        - configMapRef:
            name: REPLACE_MAPCACHE_CONFIGMAP
        volumeMounts:
        - mountPath: /var/www/html/app/static_maps
          name: staticmaps
        - mountPath: /var/sig/tiles
          name: tiles
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
      volumes:
      - name: staticmaps
        nfs:
          path: REPLACE_NFS_DIR_STATIC
          server: REPLACE_NFS_SERVER
      - name: tiles
        nfs:
          path: REPLACE_NFS_DIR_MAPCACHE
          server: REPLACE_NFS_SERVER