apiVersion: apps/v1
kind: Deployment
metadata:
  creationTimestamp: null
  labels:
    app: REPLACE_CONTAINER_NAME-queue
  name: REPLACE_CONTAINER_NAME-queue
  namespace: REPLACE_KUBE_NAMESPACE
spec:
  replicas: 1
  selector:
    matchLabels:
      app: REPLACE_CONTAINER_NAME-queue
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: REPLACE_CONTAINER_NAME-queue
    spec:
      serviceAccount: bitbucket
      containers:
      - image: REPLACE_REPO:REPLACE_TAG
        name: REPLACE_CONTAINER_NAME-queue
        env:
        - name: CONTAINER_ROLE
          value: "queue"
        envFrom:
        - configMapRef:
            name: REPLACE_CONFIGMAP
        imagePullPolicy: "Always"
      imagePullSecrets:
      - name: docker-technofarm