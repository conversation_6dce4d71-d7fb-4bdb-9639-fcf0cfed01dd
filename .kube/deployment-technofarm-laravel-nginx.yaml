apiVersion: apps/v1
kind: Deployment
metadata:
  creationTimestamp: null
  labels:
    app: REPLACE_CONTAINER_NAME-nginx
  name: REPLACE_CONTAINER_NAME-nginx
  namespace: REPLACE_KUBE_NAMESPACE
spec:
  replicas: 1
  selector:
    matchLabels:
      app: REPLACE_CONTAINER_NAME-nginx
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: REPLACE_CONTAINER_NAME-nginx
    spec:
      serviceAccount: bitbucket
      containers:
      - image: REPLACE_REPO-nginx:REPLACE_TAG
        name: REPLACE_CONTAINER_NAME-nginx
        livenessProbe:
          httpGet:
            path: /healthcheck.html
            port: 80
            httpHeaders:
            - name: X-Health-Check
              value: Ok
          initialDelaySeconds: 3
          periodSeconds: 3
        readinessProbe:
          httpGet:
            path: /healthcheck.html
            port: 80
            httpHeaders:
            - name: X-Health-Check
              value: Ok
          initialDelaySeconds: 1
          periodSeconds: 5
        envFrom:
        - configMapRef:
            name: REPLACE_CONFIGMAP
        imagePullPolicy: "Always"
      imagePullSecrets:
      - name: docker-technofarm