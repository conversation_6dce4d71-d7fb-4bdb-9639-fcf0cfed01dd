apiVersion: apps/v1
kind: Deployment
metadata:
  creationTimestamp: null
  labels:
    app: tf-technofarm
  name: tf-technofarm
  namespace: REPLACE_NAMESPACE
spec:
  replicas: REPLACE_KUBE_REPLICAS
  selector:
    matchLabels:
      app: tf-technofarm
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: tf-technofarm
    spec:
      serviceAccount: bitbucket
      containers:
      - image: REPLACE_REPO:REPLACE_TAG
        name: tf-technofarm
        readinessProbe:
            exec:
                command:
                    - php-fpm-healthcheck # a simple ping since this means it's ready to handle traffic
            initialDelaySeconds: 1
            periodSeconds: 5
        livenessProbe:
            exec:
                command:
                    - php-fpm-healthcheck
                    - --listen-queue=10 # fails if there are more than 10 processes waiting in the fpm queue
            initialDelaySeconds: 0
            periodSeconds: 10
        envFrom:
        - configMapRef:
            name: R<PERSON><PERSON><PERSON>_CONFIG_MAP
        imagePullPolicy: "Always"
        volumeMounts:
        - name: maps
          mountPath: /var/www/html/app/maps
        - name: files
          mountPath: /var/www/html/app/public/files
        - name: logs
          mountPath: /var/www/html/app/logs/group_logs
        - name: static
          mountPath: /var/www/html/app/static_maps
        - name: key
          mountPath: /var/www/html/app/config/jwt/private.pem
          subPath: private.pem
        - name: mapcache
          mountPath: /var/www/html/app/.docker/mapcache
        - name: envfiles
          mountPath: /var/www/html/app/.env
          subPath: .env.example
          readOnly: false
        - name: envfiles
          mountPath: /usr/local/etc/php/php.ini
          subPath: php.ini
          readOnly: false
      volumes:
      - name: maps
        nfs:
          server: REPLACE_NFS_SERVER
          path: REPLACE_NFS_DIR_MAPS
      - name: files
        nfs:
          server: REPLACE_NFS_SERVER
          path: REPLACE_NFS_DIR_FILES
      - name: logs
        nfs:
          server: REPLACE_NFS_SERVER
          path: REPLACE_NFS_DIR_LOGS
      - name: static
        nfs:
          server: REPLACE_NFS_SERVER
          path: REPLACE_NFS_DIR_STATIC
      - name: key
        configMap:
          name: tf-private
          defaultMode: 420
      - name: envfiles
        configMap:
          name: REPLACE_CONFIG_MAP_FILES
      - name: mapcache
        nfs:
          server: REPLACE_NFS_SERVER
          path: REPLACE_NFS_DIR_MAPCACHE
      imagePullSecrets:
      - name: docker-technofarm