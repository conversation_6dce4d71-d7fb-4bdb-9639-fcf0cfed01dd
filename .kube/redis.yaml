---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: REPLACE_CONTAINER_NAME-redis-pvc
  namespace: REPLACE_KUBE_NAMESPACE
spec:
  storageClassName: openebs-lvmpv
  accessModes:
   - ReadWriteOnce
  resources:
   requests:
    storage: 2Gi
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: REPLACE_CONTAINER_NAME-redis
  namespace: REPLACE_KUBE_NAMESPACE
data:
  redis-config: |
    dir "/data"
    maxmemory 256mb
    maxmemory-policy allkeys-lru
    save 60 1000
    appendfsync everysec
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: REPLACE_CONTAINER_NAME-redis
  name: REPLACE_CONTAINER_NAME-redis
  namespace: REPLACE_KUBE_NAMESPACE
spec:
  replicas: 1
  selector:
    matchLabels:
      app: REPLACE_CONTAINER_NAME-redis
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: REPLACE_CONTAINER_NAME-redis
    spec:
      containers:
      - image: redis:7.0
        name: REPLACE_CONTAINER_NAME-redis
        command:
          - redis-server
          - "/redis-master/redis.conf"
        env:
        - name: MASTER
          value: "true"
        ports:
        - containerPort: 6379
        volumeMounts:
        - name: data
          mountPath: /data
        - name: config
          mountPath: /redis-master
      volumes:
      - name: data
        persistentVolumeClaim:
          claimName: REPLACE_CONTAINER_NAME-redis-pvc
      - name: config
        configMap:
          name: REPLACE_CONTAINER_NAME-redis
          items:
          - key: redis-config
            path: redis.conf
---
apiVersion: v1
kind: Service
metadata:
  creationTimestamp: null
  labels:
    app: REPLACE_CONTAINER_NAME-redis
  name: REPLACE_CONTAINER_NAME-redis
  namespace: REPLACE_KUBE_NAMESPACE
spec:
  ports:
  - name: http
    port: 6379
    protocol: TCP
    targetPort: 6379
  selector:
    app: REPLACE_CONTAINER_NAME-redis
  type: ClusterIP