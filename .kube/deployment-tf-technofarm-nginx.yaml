apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: tf-technofarm-nginx
  name: tf-technofarm-nginx
  namespace: REPLACE_NAMESPACE
spec:
  replicas: REPLACE_KUBE_REPLICAS
  selector:
    matchLabels:
      app: tf-technofarm-nginx
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: tf-technofarm-nginx
    spec:
      serviceAccount: bitbucket
      containers:
      - image: REPLACE_REPO-nginx:REPLACE_TAG
        name: tf-technofarm-nginx
        imagePullPolicy: "Always"
        livenessProbe:
          httpGet:
            path: /healthcheck.html
            port: 80
            httpHeaders:
            - name: X-Health-Check
              value: Ok
          initialDelaySeconds: 3
          periodSeconds: 3
        readinessProbe:
          httpGet:
            path: /healthcheck.html
            port: 80
            httpHeaders:
            - name: X-Health-Check
              value: Ok
          initialDelaySeconds: 1
          periodSeconds: 5
        volumeMounts:
        - name: maps
          mountPath: /var/www/html/app/maps
        - name: files
          mountPath: /var/www/html/app/public/files
        envFrom:
        - configMapRef:
            name: REPLACE_NGINX_CONFIG_MAP
      volumes:
      - name: maps
        nfs:
          server: REPLACE_NFS_SERVER
          path: REPLACE_NFS_DIR_MAPS
      - name: files
        nfs:
          server: REPLACE_NFS_SERVER
          path: REPLACE_NFS_DIR_FILES
      imagePullSecrets:
      - name: docker-technofarm