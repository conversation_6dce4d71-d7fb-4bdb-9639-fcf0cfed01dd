apiVersion: batch/v1
kind: CronJob
metadata:
  name: s3-database-backup
  namespace: REPLACE_NAMESPACE
spec:
  schedule: "0 23 * * *"
  startingDeadlineSeconds: 15
  successfulJobsHistoryLimit: 3
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: tf-technofarm-dbs-backup
            image: technofarm/db-backup-s3:REPLACE_TAG
            imagePullPolicy: "Always"
            command:
            - /usr/bin/backup
            env:
              - name: DEFAULT_DB_HOST
                valueFrom:
                  configMapKeyRef:
                    name: tf-technofarm
                    key: DEFAULT_DB_HOST
              - name: DEFAULT_DB_PASSWORD
                valueFrom:
                  configMapKeyRef:
                    name: tf-technofarm
                    key: DEFAULT_DB_PASSWORD
              - name: DEFAULT_DB_PORT
                valueFrom:
                  configMapKeyRef:
                    name: tf-technofarm
                    key: DEFAULT_DB_PORT
              - name: DEFAULT_DB_USERNAME
                valueFrom:
                  configMapKeyRef:
                    name: tf-technofarm
                    key: DEFAULT_DB_USERNAME
            volumeMounts:
            - name: s3
              mountPath: /root/.aws/credentials
              subPath: credentials
              readOnly: true
          restartPolicy: OnFailure
          imagePullSecrets:
          - name: docker-technofarm
          volumes:
          - name: s3
            secret:
              secretName: s3-credentials