apiVersion: apps/v1
kind: Deployment
metadata:
  creationTimestamp: null
  labels:
    app: REPLACE_CONTAINER_NAME
  name: REPLACE_CONTAINER_NAME
  namespace: REPLACE_KUBE_NAMESPACE
spec:
  replicas: 1
  selector:
    matchLabels:
      app: REPLACE_CONTAINER_NAME
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: REPLACE_CONTAINER_NAME
    spec:
      serviceAccount: bitbucket
      containers:
      - image: REPLACE_REPO:REPLACE_TAG
        name: REPLACE_CONTAINER_NAME
        readinessProbe:
            exec:
                command:
                    - php-fpm-healthcheck # a simple ping since this means it's ready to handle traffic
            initialDelaySeconds: 5
            periodSeconds: 5
        livenessProbe:
            exec:
                command:
                    - php-fpm-healthcheck
                    - --listen-queue=10 # fails if there are more than 10 processes waiting in the fpm queue
            initialDelaySeconds: 240
            periodSeconds: 10
        envFrom:
        - configMapRef:
            name: <PERSON><PERSON><PERSON><PERSON>_CONFIGMAP
        imagePullPolicy: "Always"
      imagePullSecrets:
      - name: docker-technofarm