apiVersion: batch/v1
kind: CronJob
metadata:
  name: tf-technofarm-crontab-delete-downloaded-files
  namespace: REPLACE_NAMESPACE
spec:
  schedule: "* * * * *"
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: tf-technofarm-crontab
            image: REPLACE_REPO-cron:REPLACE_TAG
            imagePullPolicy: "Always"
            envFrom:
            - configMapRef:
                name: REPLACE_CONFIG_MAP
            command:
            - /bin/sh
            - -c
            - "/usr/bin/flock -n /tmp/cron_delete_downloaded_files.lockfile /usr/local/bin/php /var/www/html/app/run.php crons:DeleteDownloadedFiles > /dev/stdout"
            volumeMounts:
            - name: maps
              mountPath: /var/www/html/app/maps
            - name: files
              mountPath: /var/www/html/app/public/files
            - name: logs
              mountPath: /var/www/html/app/logs/group_logs
            - name: envfiles
              mountPath: /var/www/html/app/.env
              subPath: .env.example
              readOnly: false
          restartPolicy: OnFailure
          imagePullSecrets:
          - name: docker-technofarm
          volumes:
          - name: maps
            nfs:
              server: REPLACE_NFS_SERVER
              path: REPLACE_NFS_DIR_MAPS
          - name: files
            nfs:
              server: REPLACE_NFS_SERVER
              path: REPLACE_NFS_DIR_FILES
          - name: logs
            nfs:
              server: REPLACE_NFS_SERVER
              path: REPLACE_NFS_DIR_LOGS
          - name: envfiles
            configMap:
              name: REPLACE_CONFIG_MAP_FILES