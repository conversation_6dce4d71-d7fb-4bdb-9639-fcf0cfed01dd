apiVersion: apps/v1
kind: Deployment
metadata:
    labels:
        app: tf-technofarm-mapserver
    name: tf-technofarm-mapserver
    namespace: REPLACE_NAMESPACE
spec:
    replicas: 4
    selector:
        matchLabels:
            app: tf-technofarm-mapserver
    strategy:
        rollingUpdate:
            maxSurge: 25%
            maxUnavailable: 25%
        type: RollingUpdate
    template:
        metadata:
            labels:
                app: tf-technofarm-mapserver
        spec:
            serviceAccount: bitbucket
            containers:
                - image: REPLACE_REPO-mapserver:REPLACE_TAG
                  imagePullPolicy: Always
                  readinessProbe:
                      exec:
                          command:
                              - curl
                              - -I
                              - localhost:8080
                      initialDelaySeconds: 1
                      periodSeconds: 5
                  livenessProbe:
                      exec:
                          command:
                              - curl
                              - -I
                              - localhost:8080
                      failureThreshold: 3
                      initialDelaySeconds: 5
                      periodSeconds: 5
                      successThreshold: 1
                      timeoutSeconds: 10
                  name: tf-technofarm-mapserver
                  envFrom:
                      - configMapRef:
                            name: REPLACE_CONFIG_MAP
                  terminationMessagePath: /dev/termination-log
                  terminationMessagePolicy: File
                  volumeMounts:
                      - mountPath: /var/www/html/app/maps
                        name: maps
                      - mountPath: /var/www/html/app/public/files
                        name: files
                      - mountPath: /var/www/html/app/logs/group_logs
                        name: logs
                      - mountPath: /var/www/html/app/static_maps
                        name: static
            imagePullSecrets:
                - name: docker-technofarm
            restartPolicy: Always
            schedulerName: default-scheduler
            volumes:
                - name: maps
                  nfs:
                      path: REPLACE_NFS_DIR_MAPS
                      server: REPLACE_NFS_SERVER
                - name: files
                  nfs:
                      path: REPLACE_NFS_DIR_FILES
                      server: REPLACE_NFS_SERVER
                - name: logs
                  nfs:
                      path: REPLACE_NFS_DIR_LOGS
                      server: REPLACE_NFS_SERVER
                - name: static
                  nfs:
                      path: REPLACE_NFS_DIR_STATIC
                      server: REPLACE_NFS_SERVER
