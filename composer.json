{"$schema": "https://getcomposer.org/schema.json", "name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "laravel/framework": "^11.39.1", "laravel/tinker": "^2.10", "league/oauth2-server": "^9.1", "sentry/sentry-laravel": "^4.11", "staudenmeir/laravel-cte": "^1.0"}, "require-dev": {"fakerphp/faker": "^1.24.1", "larastan/larastan": "^3.0.2", "laravel/pail": "^1.2.1", "laravel/pint": "^1.20", "laravel/sail": "^1.40", "mockery/mockery": "^1.6.12", "nunomaduro/collision": "^8.5", "pestphp/pest": "^3.7.2", "pestphp/pest-plugin-laravel": "^3.0", "pestphp/pest-plugin-stressless": "*", "rector/rector": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"php artisan pail --timeout=0\" \"npm run dev\" --names=server,queue,logs,vite"], "refactor": "rector", "lint": "pint", "test:lint": "pint --test", "test:unit": "pest --parallel", "test:types": "phpstan", "test:refactor": "rector --dry-run", "test": ["@test:unit", "@test:types", "@test:lint", "@test:refactor"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}