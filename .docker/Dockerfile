FROM technofarm/php-base:8.2 as backend

ARG USER_NAME=appuser
ARG USER_GROUP=appgroup

COPY ./.docker/php/php.ini /usr/local/etc/php/php.ini
COPY ./.docker/php/www.conf /usr/local/etc/php-fpm.d/www.conf

# K8s Health Check
RUN apk add --no-cache fcgi
COPY ./.docker/php/php-fpm-healthcheck /usr/local/bin/
RUN chmod +x /usr/local/bin/php-fpm-healthcheck
# K8s Health Check End

COPY ./.docker/docker-entrypoint.sh /usr/local/bin
RUN chmod +x /usr/local/bin/docker-entrypoint.sh
#change the user to execute any further composer commands with ${USER_NAME}
USER ${USER_NAME}

COPY --chown=${USER_NAME}:${USER_GROUP} ./composer.json composer.json
COPY --chown=${USER_NAME}:${USER_GROUP} ./composer.lock composer.lock
COPY --chown=${USER_NAME}:${USER_GROUP} ./package.json ./package.json
COPY --chown=${USER_NAME}:${USER_GROUP} ./package-lock.json ./package-lock.json
COPY --chown=${USER_NAME}:${USER_GROUP} . .
COPY --chown=${USER_NAME}:${USER_GROUP} ./.docker/php/supervisord /home/<USER>/supervisord

RUN composer install --no-scripts $([ "$APP_ENV" == "prod" ] && echo "--no-dev") && \
    npm install $([ "$APP_ENV" == "prod" ] && echo "--production --ignore-scripts")

ENTRYPOINT ["/usr/local/bin/docker-entrypoint.sh"]