FROM technofarm/php:7.4

ARG GROUP_ID=1000
ARG USER_ID=1000
ARG USER_NAME=appuser
ARG USER_GROUP=appgroup
ARG APP_DIR=/var/www/html/app
ARG APP_ENV

WORKDIR ${APP_DIR}

RUN addgroup -g ${GROUP_ID} -S ${USER_GROUP} \
    && adduser -u ${USER_ID} -D -S ${USER_NAME} -G ${USER_GROUP} \
    #change the app folder permissions
    && chown -R ${USER_NAME}:${USER_GROUP} ${APP_DIR} \
    && chmod -R g+rwx ${APP_DIR}

COPY ./.docker/php/php.ini /usr/local/etc/php/php.ini
COPY ./.docker/php/www.conf /usr/local/etc/php-fpm.d/www.conf
COPY ./.docker/docker-entrypoint.sh /usr/local/bin

RUN mkdir maps

COPY ./maps/symbolset maps/symbolset
COPY ./fonts maps/fonts
COPY ./fonts /usr/share/fonts

RUN chmod +x /usr/local/bin/docker-entrypoint.sh \
    && chown -R ${USER_NAME}:${USER_GROUP} maps

COPY ./requirements.txt requirements.txt
COPY .docker/php/csv_to_xls.patch .docker/php/csv_to_xls.patch
COPY ./.docker/mapcache/mapcache.xml .docker/mapcache/mapcache.xml
# K8s Health Check
COPY ./.docker/php/php-fpm-healthcheck /usr/local/bin/
RUN chmod +x /usr/local/bin/php-fpm-healthcheck
# K8s Health Check End
COPY --chown=appuser:appgroup . .
USER ${USER_NAME}

RUN composer install --no-scripts $([ "$APP_ENV" == "prod" ] && echo "--no-dev") \
    && npm install $([ "$APP_ENV" == "prod" ] && echo "--production --ignore-scripts")

EXPOSE 9000

ENTRYPOINT ["/usr/local/bin/docker-entrypoint.sh"]