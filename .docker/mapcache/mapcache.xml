<?xml version="1.0" encoding="UTF-8"?>
<!-- see the accompanying mapcache.xml.sample for a fully commented configuration file -->
<mapcache>
    <cache name="layer_allowable_final_sqlite" type="sqlite3">
        <dbfile>/var/sig/tiles/.layers/layer_allowable_final.sqlite3</dbfile>
    </cache>
    <source name="vmap123" type="wms">
        <getmap>
            <params>
                <FORMAT>image/png</FORMAT>
                <LAYERS>layer_allowable_final</LAYERS>
                <MAP>/var/www/html/app/static_maps/layer_allowable_final.map</MAP>
            </params>
        </getmap>
        <getfeatureinfo>
            <info_formats>text/plain,application/vnd.ogc.gml</info_formats>
            <params>
                <QUERY_LAYERS>layer_allowable_final</QUERY_LAYERS>
            </params>
        </getfeatureinfo>
        <http>
            <url>http://tf-technofarm-mapserver:5000</url>
        </http>
    </source>
    <tileset name="layer_allowable_final">
        <source>vmap123</source>
        <cache>layer_allowable_final_sqlite</cache>
        <grid>WGS84</grid>
        <grid>g</grid>
        <format>PNG</format>
        <metatile>8 8</metatile>
        <expires>0</expires>
    </tileset>

    <cache name="layer_lfa_sqlite" type="sqlite3">
        <dbfile>/var/sig/tiles/.layers/layer_lfa.sqlite3</dbfile>
    </cache>
    <source name="vmap3" type="wms">
        <getmap>
            <params>
                <FORMAT>image/png</FORMAT>
                <LAYERS>layer_lfa</LAYERS>
                <MAP>/var/www/html/app/static_maps/layer_lfa.map</MAP>
            </params>
        </getmap>
        <http>
            <url>http://tf-technofarm-mapserver:5000</url>
        </http>
    </source>
    <tileset name="layer_lfa">
        <source>vmap3</source>
        <cache>layer_lfa_sqlite</cache>
        <grid>WGS84</grid>
        <grid>g</grid>
        <format>PNG</format>
        <metatile>5 5</metatile>
        <expires>0</expires>
    </tileset>

    <cache name="layer_natura_2000_sqlite" type="sqlite3">
        <dbfile>/var/sig/tiles/.layers/layer_natura_2000.sqlite3</dbfile>
    </cache>
    <source name="vmap4" type="wms">
        <getmap>
            <params>
                <FORMAT>image/png</FORMAT>
                <LAYERS>layer_natura_2000</LAYERS>
                <MAP>/var/www/html/app/static_maps/layer_natura_2000.map</MAP>
            </params>
        </getmap>
        <http>
            <url>http://tf-technofarm-mapserver:5000</url>
        </http>
    </source>
    <tileset name="layer_natura_2000">
        <source>vmap4</source>
        <cache>layer_natura_2000_sqlite</cache>
        <grid>WGS84</grid>
        <grid>g</grid>
        <format>PNG</format>
        <metatile>5 5</metatile>
        <expires>0</expires>
    </tileset>

    <cache name="layer_pzp_sqlite" type="sqlite3">
        <dbfile>/var/sig/tiles/.layers/layer_pzp.sqlite3</dbfile>
    </cache>
    <source name="vmap5" type="wms">
        <getmap>
            <params>
                <FORMAT>image/png</FORMAT>
                <LAYERS>layer_pzp</LAYERS>
                <MAP>/var/www/html/app/static_maps/layer_pzp.map</MAP>
            </params>
        </getmap>
        <http>
            <url>http://tf-technofarm-mapserver:5000</url>
        </http>
    </source>
    <tileset name="layer_pzp">
        <source>vmap5</source>
        <cache>layer_pzp_sqlite</cache>
        <grid>WGS84</grid>
        <grid>g</grid>
        <format>PNG</format>
        <metatile>5 5</metatile>
        <expires>0</expires>
    </tileset>

    <cache name="layer_vps_merg_sqlite" type="sqlite3">
        <dbfile>/var/sig/tiles/.layers/layer_vps_merg.sqlite3</dbfile>
    </cache>
    <source name="vmap6" type="wms">
        <getmap>
            <params>
                <FORMAT>image/png</FORMAT>
                <LAYERS>layer_vps_merg</LAYERS>
                <MAP>/var/www/html/app/static_maps/layer_vps_merg.map</MAP>
            </params>
        </getmap>
        <http>
            <url>http://tf-technofarm-mapserver:5000</url>
        </http>
    </source>
    <tileset name="layer_vps_merg">
        <source>vmap6</source>
        <cache>layer_vps_merg_sqlite</cache>
        <grid>WGS84</grid>
        <grid>g</grid>
        <format>PNG</format>
        <metatile>5 5</metatile>
        <expires>0</expires>
    </tileset>

    <cache name="layer_vps_gaski_zimni_sqlite" type="sqlite3">
        <dbfile>/var/sig/tiles/.layers/layer_vps_gaski_zimni.sqlite3</dbfile>
    </cache>
    <source name="vmap8" type="wms">
        <getmap>
            <params>
                <FORMAT>image/png</FORMAT>
                <LAYERS>layer_vps_gaski_zimni</LAYERS>
                <MAP>/var/www/html/app/static_maps/layer_vps_gaski_zimni.map</MAP>
            </params>
        </getmap>
        <http>
            <url>http://tf-technofarm-mapserver:5000</url>
        </http>
    </source>
    <tileset name="layer_vps_gaski_zimni">
        <source>vmap8</source>
        <cache>layer_vps_gaski_zimni_sqlite</cache>
        <grid>WGS84</grid>
        <grid>g</grid>
        <format>PNG</format>
        <metatile>5 5</metatile>
        <expires>0</expires>
    </tileset>

    <cache name="layer_vps_gaski_chervenogushi_sqlite" type="sqlite3">
        <dbfile>/var/sig/tiles/.layers/layer_vps_gaski_chervenogushi.sqlite3</dbfile>
    </cache>
    <source name="vmap7" type="wms">
        <getmap>
            <params>
                <FORMAT>image/png</FORMAT>
                <LAYERS>layer_vps_gaski_chervenogushi</LAYERS>
                <MAP>/var/www/html/app/static_maps/layer_vps_gaski_chervenogushi.map</MAP>
            </params>
        </getmap>
        <http>
            <url>http://tf-technofarm-mapserver:5000</url>
        </http>
    </source>
    <tileset name="layer_vps_gaski_chervenogushi">
        <source>vmap7</source>
        <cache>layer_vps_gaski_chervenogushi_sqlite</cache>
        <grid>WGS84</grid>
        <grid>g</grid>
        <format>PNG</format>
        <metatile>5 5</metatile>
        <expires>0</expires>
    </tileset>

    <cache name="layer_vps_livaden_blatar_sqlite" type="sqlite3">
        <dbfile>/var/sig/tiles/.layers/layer_vps_livaden_blatar.sqlite3</dbfile>
    </cache>
    <source name="vmap9" type="wms">
        <getmap>
            <params>
                <FORMAT>image/png</FORMAT>
                <LAYERS>layer_vps_livaden_blatar</LAYERS>
                <MAP>/var/www/html/app/static_maps/layer_vps_livaden_blatar.map</MAP>
            </params>
        </getmap>
        <http>
            <url>http://tf-technofarm-mapserver:5000</url>
        </http>
    </source>
    <tileset name="layer_vps_livaden_blatar">
        <source>vmap9</source>
        <cache>layer_vps_livaden_blatar_sqlite</cache>
        <grid>WGS84</grid>
        <grid>g</grid>
        <format>PNG</format>
        <metatile>5 5</metatile>
        <expires>0</expires>
    </tileset>

    <cache name="layer_vps_orli_leshoyadi_sqlite" type="sqlite3">
        <dbfile>/var/sig/tiles/.layers/layer_vps_orli_leshoyadi.sqlite3</dbfile>
    </cache>
    <source name="vmap10" type="wms">
        <getmap>
            <params>
                <FORMAT>image/png</FORMAT>
                <LAYERS>layer_vps_orli_leshoyadi</LAYERS>
                <MAP>/var/www/html/app/static_maps/layer_vps_orli_leshoyadi.map</MAP>
            </params>
        </getmap>
        <http>
            <url>http://tf-technofarm-mapserver:5000</url>
        </http>
    </source>
    <tileset name="layer_vps_orli_leshoyadi">
        <source>vmap10</source>
        <cache>layer_vps_orli_leshoyadi_sqlite</cache>
        <grid>WGS84</grid>
        <grid>g</grid>
        <format>PNG</format>
        <metatile>5 5</metatile>
        <expires>0</expires>
    </tileset>

    <default_format>PNG</default_format>
    <service enabled="true" type="wms">
        <full_wms>assemble</full_wms>
        <resample_mode>bilinear</resample_mode>
        <format>PNG</format>
        <maxsize>4096</maxsize>
    </service>
    <errors>report</errors>
    <lock_dir>/tmp</lock_dir>
    <log_level>info</log_level>
</mapcache>