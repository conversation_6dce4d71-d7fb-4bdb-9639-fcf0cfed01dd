FROM alpine:3.19.0

RUN apk update

RUN apk add --no-cache \
        python3 \
        py3-pip \
 	bash \
	curl \
	coreutils \
	date \
	zip \
	jq \
    && pip3 install --upgrade pip --break-system-packages \
    && pip3 install --no-cache-dir --break-system-packages \
        awscli \
    && rm -rf /var/cache/apk/*

RUN apk add --no-cache postgresql14-client

COPY ./backup /usr/bin/backup

RUN chmod +x /usr/bin/backup