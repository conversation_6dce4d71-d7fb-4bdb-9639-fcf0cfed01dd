#!/bin/bash

echo "${DEFAULT_DB_HOST}:${DEFAULT_DB_PORT}:*:${DEFAULT_DB_USERNAME}:${DEFAULT_DB_PASSWORD}" > /root/.pgpass
chmod 0600 /root/.pgpass

DAY=`date +%a`
DB_DIR=/backup/$DAY
mkdir -p $DB_DIR

cd $DB_DIR

for db in `psql -h hippo-ha-primary.crunchy-pgsql.svc -U postgres -q -x -t -c "\l" | grep 'Name' | sed 's/ //g' | sed 's/Name|//g'`;
do
  mkdir -p $DB_DIR/$db
  pg_dump -h hippo-ha-primary.crunchy-pgsql.svc -U postgres -j 4 -Fd -f $DB_DIR/$db $db
  zip -r $DB_DIR/$db.zip $DB_DIR/$db
  /usr/bin/aws s3 cp  $DB_DIR/$db.zip s3://tf-dbs/technofarm/$DAY/$db.zip

  #Check for first day or 15th day of month.
  today=`date +%d`
  if [ $today -eq 1 ];
  then
    /usr/bin/aws s3 cp s3://tf-dbs/technofarm/$DAY/$db.zip s3://tf-dbs/technofarm/first-day-of-month/$db.zip
  elif [ $today -eq 15 ];
  then
    /usr/bin/aws s3 cp s3://tf-dbs/technofarm/$DAY/$db.zip s3://tf-dbs/technofarm/fifteenth-day-of-month/$db.zip
  fi

  #Check S3 Backup
  response="$(aws s3api head-object --bucket tf-dbs --key technofarm/"$DAY"/"$db".zip --output json --query "[ ContentLength , LastModified]")"

  today="$(date +'%F')"

  contentlength="$(jq .[0]  <<< $response)"
  field2="$(jq .[1]  <<< $response)"
  lastmodifiedaws=`echo ${field2} | sed  's/"//g'`
  lastmodified="$(date -d "${lastmodifiedaws}" "+%F")"

  unix_todate="$(date -d "${today}" "+%s")"
  unix_last_modified="$(date -d "${lastmodified}" "+%s")"

  if [ $contentlength -lt 10 ] || [ $unix_todate -gt $unix_last_modified ]
  then
    curl -X POST --data-urlencode "payload={\"channel\": \"#kubernetes-alerts\", \"username\": \"S3 Backup - Technofarm - ALERT\", \"text\": \"Please check daily backup of ${db}\", \"icon_emoji\": \":ghost:\"}" *******************************************************************************
  fi
  #End Check S3 Backup

  rm -rf $DB_DIR/$db*
done