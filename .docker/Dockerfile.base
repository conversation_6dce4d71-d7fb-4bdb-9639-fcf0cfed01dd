FROM php:8.2-fpm-alpine as backend

ARG GROUP_ID=1000
ARG USER_ID=1000
ARG USER_NAME=appuser
ARG USER_GROUP=appgroup
ARG APP_DIR=/var/www/html/app

WORKDIR ${APP_DIR}

ARG APP_ENV

RUN apk add --no-cache build-base autoconf libpq unixodbc-dev freetype libpng libjpeg-turbo freetype-dev libpng-dev libjpeg-turbo-dev libzip-dev zip openldap-dev libxml2-dev wget
RUN apk add \
    --repository https://dl-cdn.alpinelinux.org/alpine/v3.15/community \
    --no-cache \
    php7-pear

RUN set -ex && \
    apk --no-cache add postgresql-libs postgresql-dev

RUN docker-php-ext-configure pgsql --with-pgsql=/usr/local/pgsql && \
    docker-php-ext-configure gd --with-jpeg=/usr/lib && \
    docker-php-ext-install pdo pdo_pgsql gd zip pcntl

RUN apk del postgresql-dev

RUN docker-php-ext-install exif
RUN docker-php-ext-configure exif \
    --enable-exif
RUN apk add --no-cache \
    icu-dev \
    git \
    autoconf \
    g++ \
    make \
    cmake \
    openssl-dev \
    postgresql-libs \
    postgresql-dev \
    && CPPFLAGS="-DHAVE_SYS_FILE_H" pecl install redis \
    && docker-php-ext-enable redis.so 

RUN apk add --no-cache --update libmemcached-libs zlib
RUN set -xe && \
    cd /tmp/ && \
    apk add --no-cache --update --virtual .phpize-deps $PHPIZE_DEPS && \
    apk add --no-cache --update --virtual .memcached-deps zlib-dev libmemcached-dev cyrus-sasl-dev && \
    pecl install igbinary && \
    ( \
    pecl install --nobuild memcached && \
    cd "$(pecl config-get temp_dir)/memcached" && \
    phpize && \
    ./configure --enable-memcached-igbinary && \
    make -j$(nproc) && \
    make install && \
    cd /tmp/ \
    ) && \
    docker-php-ext-enable igbinary memcached && \
    rm -rf /tmp/* && \
    apk del .memcached-deps .phpize-deps

RUN apk add \
    --repository https://dl-cdn.alpinelinux.org/alpine/v3.8/main \
    --no-cache \
    rabbitmq-c-dev \
    && pecl install amqp \
    && docker-php-ext-enable amqp

RUN  addgroup -g ${GROUP_ID} -S ${USER_GROUP} \
    && adduser -u ${USER_ID} -D -S ${USER_NAME} -G ${USER_GROUP} \
    #change the app folder permissions
    && chown -R ${USER_NAME}:${USER_GROUP} ${APP_DIR} \
    && chmod -R g+rwx ${APP_DIR} \
    && docker-php-ext-install intl opcache mysqli pdo_mysql

RUN apk add --repository=https://dl-cdn.alpinelinux.org/alpine/v3.16/main -u alpine-keys

RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer



RUN apk add ttf-ubuntu-font-family --no-cache --repository=https://dl-cdn.alpinelinux.org/alpine/v3.13/main

# [END]


RUN apk add postgresql-client --no-cache --repository=https://dl-cdn.alpinelinux.org/alpine/v3.8/main
RUN apk add --no-cache python3 python3-dev gdal-dev gdal-tools gdal-driver-PG py3-numpy-dev py3-numpy py3-gdal py3-pip py3-wheel py3-matplotlib py3-pillow py3-rasterio py3-shapely
RUN apk add supervisor gettext nodejs npm

# K8s Health Check
RUN apk add --no-cache fcgi
COPY ./.docker/php/php-fpm-healthcheck /usr/local/bin/
RUN chmod +x /usr/local/bin/php-fpm-healthcheck
# K8s Health Check End

EXPOSE 9000