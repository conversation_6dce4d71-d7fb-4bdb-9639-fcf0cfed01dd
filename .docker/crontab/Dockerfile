FROM technofarm/php:7.4

ARG GROUP_ID=1000
ARG USER_ID=1000
ARG USER_NAME=appuser
ARG USER_GROUP=appgroup
ARG APP_DIR=/var/www/html/app
ARG APP_ENV

WORKDIR ${APP_DIR}

RUN addgroup -g ${GROUP_ID} -S ${USER_GROUP} \
    && adduser -u ${USER_ID} -D -S ${USER_NAME} -G ${USER_GROUP} \
    #change the app folder permissions
    && chown -R ${USER_NAME}:${USER_GROUP} ${APP_DIR} \
    && chmod -R g+rwx ${APP_DIR} \
    && chown appuser:appgroup /usr/sbin/crond \
    && setcap cap_setgid=ep /usr/sbin/crond

COPY ./.docker/php/php.ini /usr/local/etc/php/php.ini
COPY ./.docker/php/www.conf /usr/local/etc/php-fpm.d/www.conf
COPY ./.docker/docker-entrypoint.sh /usr/local/bin

RUN mkdir maps

COPY ./maps/symbolset maps/symbolset
COPY ./fonts maps/fonts
COPY ./fonts /usr/share/fonts

RUN chmod +x /usr/local/bin/docker-entrypoint.sh \
    && chown -R ${USER_NAME}:${USER_GROUP} maps

COPY ./requirements.txt requirements.txt
COPY .docker/php/csv_to_xls.patch .docker/php/csv_to_xls.patch
COPY ./.docker/mapcache/mapcache.xml .docker/mapcache/mapcache.xml
COPY --chown=appuser:appgroup . .

COPY --chown=appuser:appgroup .docker/crontab/crons /home/<USER>/crontab
RUN crontab -u appuser /home/<USER>/crontab

COPY --chown=appuser:appgroup . .

RUN chmod +x ./crons/everymin.sh
RUN chmod +x ./crons/2am.sh

RUN mkdir -p /var/log/cron && mkdir -m 0644 -p /var/spool/cron/crontabs && touch /var/log/cron/cron.log && mkdir -m 0644 -p /etc/cron.d

COPY ./.docker/crontab/scripts /scripts
RUN chmod -R +x /scripts

# USER ${USER_NAME}

RUN composer install --no-scripts $([ "$APP_ENV" == "prod" ] && echo "--no-dev") \
    && npm install $([ "$APP_ENV" == "prod" ] && echo "--production --ignore-scripts")

ENTRYPOINT ["/scripts/docker-entrypoint.sh"]

# TODO: run as none root user.
# USER appuser
#USER appuser
