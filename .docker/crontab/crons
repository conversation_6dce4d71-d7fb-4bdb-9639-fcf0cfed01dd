* * * * * /usr/bin/flock -n /tmp/cron_layer_processing.lockfile /usr/local/bin/php /var/www/html/app/run.php crons:LayerProcessing > /dev/stdout
* * * * * /usr/bin/flock -n /tmp/cron_kvs_processing.lockfile /usr/local/bin/php /var/www/html/app/run.php crons:KvsProcessing > /dev/stdout
* * * * * /usr/bin/flock -n /tmp/cron_croplayer_processing.lockfile /usr/local/bin/php /var/www/html/app/run.php crons:CroplayerProcessing > /dev/stdout
* * * * * /usr/bin/flock -n /tmp/cron_overlaps_processing.lockfile /usr/local/bin/php /var/www/html/app/run.php crons:OverlapsProcessing > /dev/stdout
* * * * * /usr/bin/flock -n /tmp/cron_tmp_processing.lockfile /usr/local/bin/php /var/www/html/app/run.php crons:TmpProcessing > /dev/stdout
* * * * * /usr/bin/flock -n /tmp/cron_agreements_proccesing.lockfile /usr/local/bin/php /var/www/html/app/run.php crons:AgreementsProcessing > /dev/stdout
* * * * * /usr/bin/flock -n /tmp/cron_coverage_processing.lockfile /usr/local/bin/php /var/www/html/app/run.php crons:CoverageProcessing > /dev/stdout
* * * * * /usr/bin/flock -n /tmp/cron_kvs_osz_processing.lockfile /usr/local/bin/php /var/www/html/app/run.php crons:OszKvsProcessing > /dev/stdout
* * * * * /usr/bin/flock -n /tmp/cron_payroll_exports.lockfile /usr/local/bin/php /var/www/html/app/run.php crons:PayrollExports > /dev/stdout
* * * * * /usr/bin/flock -n /tmp/cron_work_layer_processing.lockfile /usr/local/bin/php /var/www/html/app/run.php crons:WorkLayerProcessing > /dev/stdout
0 * * * * /usr/bin/flock -n /tmp/cron_delete_plot_images.lockfile /usr/local/bin/php /var/www/html/app/run.php crons:DeletePlotImages > /dev/stdout
0 2 * * * /usr/bin/flock -n /tmp/cron_delete_plot_images.lockfile /usr/local/bin/php /var/www/html/app/run.php crons:RegenerateHashes > /dev/stdout
0 2 * * * /usr/bin/flock -n /tmp/cron_layers_que.lockfile /usr/bin/find /var/www/html/app/public/files/layers_queue/ -type f -mtime +5 -exec rm -r {} \; 2>&1
#0 */2 * * * /usr/bin/flock -n /tmp/cron_delete_public_files_uploads_files.lockfile /usr/local/bin/php /var/www/html/app/crons/cron_delete_public_files_uploads_files.php > /dev/stdout
0 */2 * * * /usr/bin/flock -n /tmp/cron_delete_downloaded_files.lockfile /usr/local/bin/php /var/www/html/app/run.php crons:DeleteDownloadedFiles > /dev/stdout
0 2 * * * /usr/bin/flock -n /tmp/update_users_product_usage.lockfile /usr/local/bin/php /var/www/html/app/run.php crons:UpdateUsersProductUsage > /dev/stdout
* * * * * /usr/bin/flock -n /tmp/cron_import_excel_kvs.lockfile /usr/local/bin/php /var/www/html/app/run.php crons:ImportExcelKvs > /dev/stdout
* * * * * /usr/bin/flock -n /tmp/cron_csd_processing.lockfile /usr/local/bin/php /var/www/html/app/run.php crons:CsdProcessing > /dev/stdout