--- error_copy.c	2021-03-25 00:49:40.050000000 +0200
+++ error.c	2021-03-25 00:49:59.150000000 +0200
@@ -30,7 +30,14 @@
  **********************************************************************/
 
 #include "php_mapscript.h"
-
+#undef ZVAL_STRING
+#define ZVAL_STRING(z, s, duplicate) do {       \
+                 const char *__s=(s);                            \
+                 zval *__z = (z);                                        \
+                 Z_STRLEN_P(__z) = strlen(__s);          \
+                 Z_STRVAL_P(__z) = (duplicate?estrndup(__s, Z_STRLEN_P(__z)):(char*)__s);\
+                 Z_TYPE_P(__z) = IS_STRING;                      \
+         } while (0)
 zend_class_entry *mapscript_ce_error;
 
 ZEND_BEGIN_ARG_INFO_EX(error___get_args, 0, 0, 1)
