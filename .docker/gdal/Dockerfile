##
# osgeo/gdal:alpine-small

# This file is available at the option of the licensee under:
# Public domain
# <AUTHOR> <EMAIL>

ARG ALPINE_VERSION=3.14
FROM alpine:${ALPINE_VERSION} as gdal_builder

# Derived from osgeo/proj by <PERSON> <<EMAIL>>
LABEL maintainer="Even Rouault <<EMAIL>>"

# Setup build env for PROJ
RUN apk add --no-cache wget curl unzip make libtool autoconf automake pkgconfig g++ sqlite sqlite-dev

# For PROJ and GDAL
RUN apk add --no-cache \
    linux-headers \
    curl-dev tiff-dev \
    zlib-dev zstd-dev \
    libjpeg-turbo-dev libpng-dev libwebp-dev expat-dev postgresql-dev openjpeg-dev

# Build openjpeg
#ARG OPENJPEG_VERSION=2.3.1
RUN if test "${OPENJPEG_VERSION}" != ""; then ( \
    apk add --no-cache cmake \
    && wget -q https://github.com/uclouvain/openjpeg/archive/v${OPENJPEG_VERSION}.tar.gz \
    && tar xzf v${OPENJPEG_VERSION}.tar.gz \
    && rm -f v${OPENJPEG_VERSION}.tar.gz \
    && cd openjpeg-${OPENJPEG_VERSION} \
    && cmake . -DBUILD_SHARED_LIBS=ON  -DBUILD_STATIC_LIBS=OFF -DCMAKE_BUILD_TYPE=Release \
    -DCMAKE_INSTALL_PREFIX=/usr \
    && make -j$(nproc) \
    && make install \
    && mkdir -p /build_thirdparty/usr/lib \
    && cp -P /usr/lib/libopenjp2*.so* /build_thirdparty/usr/lib \
    && for i in /build_thirdparty/usr/lib/*; do strip -s $i 2>/dev/null || /bin/true; done \
    && cd .. \
    && rm -rf openjpeg-${OPENJPEG_VERSION} \
    && apk del cmake \
    ); fi

RUN apk add --no-cache rsync ccache

ARG PROJ_DATUMGRID_LATEST_LAST_MODIFIED
RUN \
    mkdir -p /build_projgrids/usr/share/proj \
    && curl -LOs http://download.osgeo.org/proj/proj-datumgrid-latest.zip \
    && unzip -q -j -u -o proj-datumgrid-latest.zip  -d /build_projgrids/usr/share/proj \
    && rm -f *.zip

ARG RSYNC_REMOTE

# Build PROJ
ARG PROJ_VERSION=master
RUN mkdir proj \
    && wget -q https://github.com/OSGeo/PROJ/archive/${PROJ_VERSION}.tar.gz -O - \
    | tar xz -C proj --strip-components=1 \
    && cd proj \
    && ./autogen.sh \
    && if test "${RSYNC_REMOTE}" != ""; then \
    echo "Downloading cache..."; \
    rsync -ra ${RSYNC_REMOTE}/proj/ $HOME/; \
    echo "Finished"; \
    export CC="ccache gcc"; \
    export CXX="ccache g++"; \
    export PROJ_DB_CACHE_DIR="$HOME/.ccache"; \
    ccache -M 100M; \
    fi \
    && ./configure --prefix=/usr --disable-static --enable-lto --disable-dependency-tracking \
    && make -j$(nproc) \
    && make install \
    && make install DESTDIR="/build_proj" \
    && if test "${RSYNC_REMOTE}" != ""; then \
    ccache -s; \
    echo "Uploading cache..."; \
    rsync -ra --delete $HOME/.ccache ${RSYNC_REMOTE}/proj/; \
    echo "Finished"; \
    rm -rf $HOME/.ccache; \
    unset CC; \
    unset CXX; \
    fi \
    && cd .. \
    && rm -rf proj \
    && for i in /build_proj/usr/lib/*; do strip -s $i 2>/dev/null || /bin/true; done \
    && for i in /build_proj/usr/bin/*; do strip -s $i 2>/dev/null || /bin/true; done

# Build GDAL
ARG GDAL_VERSION=master
ARG GDAL_RELEASE_DATE
ARG GDAL_BUILD_IS_RELEASE
RUN if test "${GDAL_VERSION}" = "master"; then \
    export GDAL_VERSION=$(curl -Ls https://api.github.com/repos/OSGeo/gdal/commits/HEAD -H "Accept: application/vnd.github.VERSION.sha"); \
    export GDAL_RELEASE_DATE=$(date "+%Y%m%d"); \
    fi \
    && if test "x${GDAL_BUILD_IS_RELEASE}" = "x"; then \
    export GDAL_SHA1SUM=${GDAL_VERSION}; \
    fi \
    && if test "${RSYNC_REMOTE}" != ""; then \
    echo "Downloading cache..."; \
    rsync -ra ${RSYNC_REMOTE}/gdal/ $HOME/; \
    echo "Finished"; \
    export CC="ccache gcc"; \
    export CXX="ccache g++"; \
    ccache -M 1G; \
    fi \
    && mkdir gdal \
    && wget -q https://github.com/OSGeo/gdal/archive/${GDAL_VERSION}.tar.gz -O - \
    | tar xz -C gdal --strip-components=1 \
    && cd gdal/gdal \
    && ./configure --prefix=/usr --without-libtool --without-sqlite3 --without-spatialite \
    --with-hide-internal-symbols \
    --with-proj=/usr \
    --with-libtiff=internal --with-rename-internal-libtiff-symbols \
    --with-geotiff=internal --with-rename-internal-libgeotiff-symbols \
    --enable-lto \
    && make -j$(nproc) \
    && make install DESTDIR="/build" \
    && if test "${RSYNC_REMOTE}" != ""; then \
    ccache -s; \
    echo "Uploading cache..."; \
    rsync -ra --delete $HOME/.ccache ${RSYNC_REMOTE}/gdal/; \
    echo "Finished"; \
    rm -rf $HOME/.ccache; \
    unset CC; \
    unset CXX; \
    fi \
    && cd ../.. \
    && rm -rf gdal \
    && mkdir -p /build_gdal_version_changing/usr/include \
    && mv /build/usr/lib                    /build_gdal_version_changing/usr \
    && mv /build/usr/include/gdal_version.h /build_gdal_version_changing/usr/include \
    && mv /build/usr/bin                    /build_gdal_version_changing/usr \
    && for i in /build_gdal_version_changing/usr/lib/*; do strip -s $i 2>/dev/null || /bin/true; done \
    && for i in /build_gdal_version_changing/usr/bin/*; do strip -s $i 2>/dev/null || /bin/true; done \
    # Remove resource files of uncompiled drivers
    && (for i in \
    # BAG driver
    /build/usr/share/gdal/bag*.xml \
    # unused
    /build/usr/share/gdal/*.svg \
    # unused
    /build/usr/share/gdal/*.png \
    # GMLAS driver
    /build/usr/share/gdal/gmlas* \
    # netCDF driver
    /build/usr/share/gdal/netcdf_config.xsd \
    ;do rm $i; done)
FROM alpine:3.14 as mapserver_builder
RUN apk --no-cache add cmake wget curl unzip make libtool autoconf automake pkgconfig g++ 

RUN apk add --no-cache libpq libjpeg-turbo-dev fribidi-dev libpng \
    freetype-dev libxml2-dev postgresql-dev curl-dev php7-dev \
    libstdc++ \
    sqlite sqlite-dev \
    libcurl tiff \
    zlib zstd-libs \
    libjpeg openjpeg libwebp expat

RUN apk add --no-cache libcrypto1.1 --repository=http://dl-cdn.alpinelinux.org/alpine/v3.14/main

RUN apk add --no-cache swig

# Build PROJ
COPY --from=gdal_builder /build_projgrids/usr/ /usr/
COPY --from=gdal_builder /build_proj/usr/share/proj/ /usr/share/proj/
COPY --from=gdal_builder /build_proj/usr/include/ /usr/include/
COPY --from=gdal_builder /build_proj/usr/bin/ /usr/bin/
COPY --from=gdal_builder /build_proj/usr/lib/ /usr/lib/
COPY --from=gdal_builder /build/usr/share/gdal/ /usr/share/gdal/
COPY --from=gdal_builder /build/usr/include/ /usr/include/
COPY --from=gdal_builder /build_gdal_version_changing/usr/ /usr/

WORKDIR /tmp
COPY error.c.patch error.c.patch
ARG MAPSERVER_VERSION=7-6
RUN wget https://github.com/MapServer/MapServer/archive/branch-${MAPSERVER_VERSION}.zip \
    && unzip branch-${MAPSERVER_VERSION}.zip \
    && rm branch-${MAPSERVER_VERSION}.zip \
    && cd MapServer-branch-${MAPSERVER_VERSION} \
    #&& patch mapscript/php/error.c < /tmp/error.c.patch \
    && mkdir build \
    #&& mkdir /opt \
    && cd build \
    && CFLAGS="-DACCEPT_USE_OF_DEPRECATED_PROJ_API_H" CXXFLAGS="-DACCEPT_USE_OF_DEPRECATED_PROJ_API_H" LDFLAGS="-rdynamic" cmake \
    -DBUILD_STATIC=1 \
    -DCMAKE_INSTALL_PREFIX=/usr \
    -DCMAKE_INSTALL_LIBDIR=lib \
    -DCMAKE_PREFIX_PATH=/usr/lib:/usr/local:/usr/include:/opt:/usr/share/proj \
    -DWITH_PROJ=1 \
    -DWITH_PROTOBUFC=0 \
    -DWITH_HARFBUZZ=0 \
    -DWITH_FRIBIDI=0\
    -DPROJ_INCLUDE_DIR=/usr/lib \
    -DPROJ_INCLUDE_DIR=/usr/include \
    -DWITH_CLIENT_WMS=1 \
    -DWITH_CURL=1 \
    -DWITH_FCGI=0 \
    -DWITH_LIBXML2=1 \
    -DWITH_SOS=1 \
    -DWITH_PHP=1 \
    -DPHP_EXTENSION_DIR=/ext \
    -DPHP_CONFIG_EXECUTABLE=/usr/bin/php-config7 \
    -DWITH_PERL=0 \
    -DWITH_RUBY=0 \
    -DWITH_JAVA=0 \
    -DWITH_CSHARP=0 \
    -DWITH_PYTHON=0 \
    -DWITH_CAIRO=0 \
    -DWITH_SVGCAIRO=0 \
    -DWITH_MSSQL2008=0 \
    -DWITH_GIF=0 \
    -DWITH_ORACLESPATIAL=0 \
    -DWITH_GEOS=0 \
    ../ \
    && make -j$(nproc) \
    && make install DESTDIR="/build" \
    && cd ../.. \
    && mkdir -p /build_mapserver_version_changing/usr \
    && mv /build/usr/lib /build_mapserver_version_changing/usr \
    && mv /build/usr/bin /build_mapserver_version_changing/usr \
    && for i in /build_mapserver_version_changing/usr/lib/*; do strip -s $i 2>/dev/null || /bin/true; done \
    && for i in /build_mapserver_version_changing/usr/bin/*; do strip -s $i 2>/dev/null || /bin/true; done

FROM alpine:${ALPINE_VERSION}

RUN apk add --no-cache libcrypto1.1 --repository=http://dl-cdn.alpinelinux.org/alpine/v3.14/main

RUN apk add --no-cache libstdc++ \
    sqlite-libs \
    libcurl tiff \
    zlib zstd-libs \
    libjpeg-turbo libpng openjpeg \ 
    libwebp expat libpq fribidi freetype libxml2 libjpeg

COPY --from=gdal_builder /build_projgrids/usr/ /build_projgrids/usr/
COPY --from=gdal_builder /build_proj/usr/share/proj/ /build_proj/usr/share/proj/
COPY --from=gdal_builder /build_proj/usr/include/ /build_proj/usr/include/
COPY --from=gdal_builder /build_proj/usr/bin/ /build_proj/usr/bin/
COPY --from=gdal_builder /build_proj/usr/lib/ /build_proj/usr/lib/
COPY --from=gdal_builder /build/usr/share/gdal/ /build/usr/share/gdal/
COPY --from=gdal_builder /build/usr/include/ /build/usr/include/
COPY --from=gdal_builder /build_gdal_version_changing/usr/ /build_gdal_version_changing/usr/

COPY --from=mapserver_builder /build_mapserver_version_changing/usr/lib/ /build_mapserver_version_changing/usr/lib/
COPY --from=mapserver_builder /build_mapserver_version_changing/usr/bin/ /build_mapserver_version_changing/usr/bin/