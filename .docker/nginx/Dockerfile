FROM nginx:stable-alpine

ARG GROUP_ID=1000
ARG USER_ID=1000

RUN  addgroup -g ${GROUP_ID} -S appgroup \
    && adduser -u ${USER_ID} -D -S appuser -G appgroup

#COPY template/default.conf /etc/nginx/conf.d/
COPY ./.docker/nginx/config/nginx.conf /etc/nginx/nginx.conf
COPY ./.docker/nginx/config/cors_support /etc/nginx/cors_support
COPY ./.docker/nginx/template/default.conf /etc/nginx/templates/default.conf.template
#COPY App
COPY --chown=appuser:appgroup . /var/www/html/app/