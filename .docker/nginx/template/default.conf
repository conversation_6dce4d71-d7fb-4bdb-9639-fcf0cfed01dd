
server {
    listen 80 default_server;
    listen [::]:80 default_server;

    index index.php;
    error_log  /var/log/nginx/error.log;
    access_log /var/log/nginx/access.log;
    root /var/www/html/app/public/;

    location /healthcheck.html {
        root /var/www/html/app;
        add_header    X-Health-Check    Ok;
    }

    location /mapserv {
        resolver 127.0.0.11;
        proxy_set_header    Host                $http_host;
        proxy_set_header    X-Real-IP           $remote_addr;
        proxy_set_header    X-Forwarded-For     $proxy_add_x_forwarded_for;
        set $mapserver ${CONTAINER_NAME}-mapserver:8080;
        proxy_pass http://$mapserver;
    }

    location /mapcache {
        resolver 127.0.0.11;
        proxy_set_header    Host                $http_host;
        proxy_set_header    X-Real-IP           $remote_addr;
        proxy_set_header    X-Forwarded-For     $proxy_add_x_forwarded_for;
        set $mapcache ${CONTAINER_NAME}-mapcache:80;
        proxy_pass http://$mapcache;
    }

    location ~ \.php$ {
        include cors_support;
        try_files $uri =404;

        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_pass ${CONTAINER_NAME}:9000;
        fastcgi_index index.php;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param PATH_INFO $fastcgi_path_info;
    }

    location / {
        include cors_support;
        proxy_read_timeout 1800;
        proxy_connect_timeout 1800;
        proxy_send_timeout 1800;
        send_timeout 1800;
        try_files $uri $uri/ /index.php?$query_string;
        gzip_static on;
    }
}
