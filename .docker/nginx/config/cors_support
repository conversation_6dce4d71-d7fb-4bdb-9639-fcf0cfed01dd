#more_set_headers "allow: OPTIONS GET, POST, HEAD, PURGE, PUT, DELETE, PATCH" always;
if ($request_method ~* "(GET|HEAD|POST|PUT|DELETE|PATCH)") {
       add_header 'Access-Control-Allow-Origin' '*' always;
       add_header 'Access-Control-Allow_Credentials' 'true';
       add_header 'Access-Control-Allow-Headers' 'Authorization,Accept,Origin,DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Content-Range,Range,X-AUTH-TOKEN,X-Frame-Options,Sentry-Trace,Baggage' ;
       add_header 'Access-Control-Allow-Methods' 'GET,HEAD,POST,OPTIONS,PUT,DELETE,PATCH';
}
if ($request_method = 'OPTIONS') {
     add_header 'Access-Control-Allow-Origin' '*' always;
     add_header 'Access-Control-Allow-Methods' 'GET, HEAD, POST, OPTIONS, DELETE, PUT';
     #
     # Custom headers and headers various browsers *should* be OK with but aren't
     #
     add_header 'Access-Control-Allow-Headers' 'Accept, Origin, Content-type, Authorization,DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,X-AUTH-TOKEN,X-Frame-Options,Sentry-Trace,Baggage';
     #
     # Tell client that this pre-flight info is valid for 20 days
     #
     add_header 'Access-Control-Max-Age' 1728000;
     add_header 'Content-Type' 'text/plain; charset=utf-8';
     add_header 'Content-Length' 0;
     return 204;
 }
