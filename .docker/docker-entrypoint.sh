#! /bin/sh

# set -e

# Initialize working environment

# For prod/staging composer install is already called on build step.
if [[ "$APP_ENV" == "dev" ]]
then
    composer install --no-scripts
    npm install
fi

ROLE=${CONTAINER_ROLE:-app}

if [[ ! -d $APP_DIR/storage/framework/views ]]
then
    mkdir $APP_DIR/storage/framework/views
fi

php artisan migrate --force
# Combines all cache clear commands.
php artisan optimize:clear

if [ "$ROLE" = "app" ]; then
    #start php
    exec php-fpm -F

elif [ "$ROLE" = "queue" ]; then
    echo "Running Supervisor ..."
    exec supervisord -c /home/<USER>/supervisord/supervisord.conf -n
    
elif [ "$ROLE" = "scheduler" ]; then
    echo "Scheduler role"
    while [ true ]
    do
      php $APP_DIR/artisan schedule:run --verbose --no-interaction &
      # Sleep for 1 minute.
      sleep 60
    done
else
    echo "Could not match the container role \"$ROLE\""
    exit 1
fi
