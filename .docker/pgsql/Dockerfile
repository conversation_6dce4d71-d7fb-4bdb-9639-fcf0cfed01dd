FROM kartoza/postgis:12.4 as postgre

ARG POSTGRES_PASS

WORKDIR /home

RUN apt-get update && \
    apt-get install -y git make zip unzip gcc postgresql-server-dev-12

RUN wget --quiet https://github.com/Zeleo/pg_natural_sort_order/archive/master.zip \
    && unzip *.zip \
    && rm -rf ./*.zip \
    && cd * \
    && make \
    && make install

RUN echo "127.0.0.1:*:*:postgres:${POSTGRES_PASS}" > /root/.pgpass && chmod 0600 /root/.pgpass