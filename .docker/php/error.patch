--- error.c	2020-03-19 10:33:18.000000000 +0200
+++ error_copy.c	2020-03-19 10:33:23.000000000 +0200
@@ -30,6 +30,16 @@
  **********************************************************************/
 
 #include "php_mapscript.h"
+#undef ZVAL_STRING
+#define ZVAL_STRING(z, s, duplicate)                                                  \
+    do                                                                                \
+    {                                                                                 \
+        const char *__s = (s);                                                        \
+        zval *__z = (z);                                                              \
+        Z_STRLEN_P(__z) = strlen(__s);                                                \
+        Z_STRVAL_P(__z) = (duplicate ? estrndup(__s, Z_STRLEN_P(__z)) : (char *)__s); \
+        Z_TYPE_P(__z) = IS_STRING;                                                    \
+    } while (0)
 
 zend_class_entry *mapscript_ce_error;
 
