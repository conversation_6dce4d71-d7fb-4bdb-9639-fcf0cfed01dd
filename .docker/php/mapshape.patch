--- mapshape.c	2020-03-19 10:24:48.000000000 +0200
+++ mapshape_copy.c	2020-03-19 10:26:26.000000000 +0200
@@ -1951,7 +1951,7 @@
     free(tSHP);
     return MS_FAILURE;
   }
-  
+
   tSHP->shpfile->isopen = MS_FALSE; /* in case of error: do not try to close the shpfile */
   tSHP->tileshpfile = NULL; /* may need this if not using a tile layer, look for malloc later */
   layer->layerinfo = tSHP;
@@ -2593,7 +2593,7 @@
       return MS_FAILURE;
     }
   }
-  
+
   if (layer->projection.numargs > 0 &&
       EQUAL(layer->projection.args[0], "auto"))
   {
@@ -2637,7 +2637,7 @@
         }
     }
 #else /* !(defined(USE_GDAL) || defined(USE_OGR)) */
-    if( layer->debug layer->map->debug ) {
+    if( layer->debug || layer->map->debug ) {
         msDebug( "Unable to get SRS from shapefile '%s' for layer '%s'. GDAL or OGR support needed\n", szPath, layer->name );
     }
 #endif /* defined(USE_GDAL) || defined(USE_OGR) */
