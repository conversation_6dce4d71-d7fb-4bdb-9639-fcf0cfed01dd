worker_processes      1;

events {
  worker_connections  1024;
}

http {
  include             mime.types;
  default_type        application/octet-stream;

  sendfile on;

  keepalive_timeout   65;

  server {
    listen            80;
    server_name       localhost;
    index             index.html index.htm;

    location / {
      alias            /usr/share/nginx/html/;

      if ($request_method = 'OPTIONS') {
          # Add security headers
          add_header 'X-Frame-Options' 'deny always';
          add_header 'X-XSS-Protection' '"1; mode=block" always';
          add_header 'X-Content-Type-Options' 'nosniff always';
          add_header 'Referrer-Policy' 'strict-origin-when-cross-origin';

          # Set access control header
          add_header 'Access-Control-Allow-Origin' '*';
          add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
          #
          # Custom headers and headers various browsers *should* be OK with but aren't
          #
          add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type';
          #
          # Tell client that this pre-flight info is valid for 20 days
          #
          add_header 'Access-Control-Max-Age' 1728000;
          add_header 'Content-Type' 'text/plain charset=UTF-8';
          add_header 'Content-Length' 0;
          return 204;
      }
      if ($request_method = 'POST') {
          # Add security headers
          add_header 'X-Frame-Options' 'deny always';
          add_header 'X-XSS-Protection' '"1; mode=block" always';
          add_header 'X-Content-Type-Options' 'nosniff always';
          add_header 'Referrer-Policy' 'strict-origin-when-cross-origin';

          # Set access control header
          add_header 'Access-Control-Allow-Origin' '*';
          add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
          add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type';
      }
      if ($request_method = 'GET') {
          # Add security headers
          add_header 'X-Frame-Options' 'deny always';
          add_header 'X-XSS-Protection' '"1; mode=block" always';
          add_header 'X-Content-Type-Options' 'nosniff always';
          add_header 'Referrer-Policy' 'strict-origin-when-cross-origin';

          # Set access control header
          add_header 'Access-Control-Allow-Origin' '*';
          add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
          add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type';
      }
    }
  }
}