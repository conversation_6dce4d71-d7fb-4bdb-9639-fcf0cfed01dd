ARG MS_VERSION=8.1

FROM technofarm/mapserver:${MS_VERSION}

RUN addgroup --gid 1000 appgroup --disabled-password \
    && adduser appuser --disabled-password \
    && adduser appuser appgroup --disabled-password \
    && chown appuser:appgroup /var/lib/apache2/fcgid/sock/

# RUN echo "<900913> +proj=merc +a=6378137 +b=6378137 +lat_ts=0.0 +lon_0=0.0 +x_0=0.0 +y_0=0 +k=1.0 +units=m +nadgrids=@null +no_defs <>" >> /usr/share/proj/epsg

COPY .docker/mapserver/lighttpd.conf /lighttpd.conf

USER appuser