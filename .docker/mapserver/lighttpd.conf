server.document-root = env.DOC_ROOT

server.port = env.PORT

server.modules += ( "mod_fastcgi", "mod_status" )

status.status-url = "/server-status"

fastcgi.debug = env.DEBUG

fastcgi.server = (
  "/mapserv" => (
    "mapserver" => (
      "socket" => "/tmp/mapserver-fastcgi.socket",
      "check-local" => "disable",
      "bin-path" => "/usr/bin/mapserv",
      "min-procs" => env.MIN_PROCS,
      "max-procs" => env.MAX_PROCS,
      "max-load-per-proc" => env.MAX_LOAD_PER_PROC,
      "idle-timeout" => env.IDLE_TIMEOUT
    )
  )
)