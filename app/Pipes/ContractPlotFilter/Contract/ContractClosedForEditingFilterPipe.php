<?php

declare(strict_types=1);

namespace App\Pipes\ContractPlotFilter\Contract;

use Closure;
use Illuminate\Database\Query\Builder;
use Exception;

class ContractClosedForEditingFilterPipe extends BaseContractFilterPipe
{
    public function __construct(private ?bool $filterValues) {}

    public function handle(Builder $query, Closure $next)
    {
        if (is_null($this->filterValues)) {
            return $next($query);
        }

        $this->joinRequiredTables($query);

        $query->where(function ($query) {
            $className = self::class;

            return match ($query->from) {
                'su_contracts',
                'layer_kvs' => $query->where('su_contracts.is_closed_for_editing', $this->filterValues),
                default => throw new Exception("Invalid table for filter '{$className}' selection")
            };
        });

        return $next($query);
    }

    public function handleFilterItems(Builder $query, Closure $next)
    {
        $className = self::class;

        throw new Exception("The {$className} does not support filter items selection");
    }
}
