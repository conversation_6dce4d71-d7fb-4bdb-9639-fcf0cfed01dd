<?php

declare(strict_types=1);

namespace App\Pipes\ContractPlotFilter\Contract;

use App\Types\Enums\Documents\ContractTypeEnum;
use App\Types\Enums\Documents\DocumentTypeEnum;
use Closure;
use Exception;
use Illuminate\Database\Query\Builder;

class ContractTypeFilterPipe extends BaseContractFilterPipe
{
    public function __construct(private ?array $filterValues) {}

    public function handle(Builder $query, Closure $next)
    {
        if (empty($this->filterValues) || $this->skipFilter($query->from)) {
            return $next($query);
        }

        $this->joinRequiredTables($query);

        $query->where(function ($query) {
            $hasNullValue = in_array(null, $this->filterValues);
            $isSublease = in_array(ContractTypeEnum::Sublease->value, $this->filterValues);
            $isSale = in_array(ContractTypeEnum::Sales->value, $this->filterValues);
            $filterValues = array_filter(
                $this->filterValues,
                fn ($value) => ! in_array($value, [null, ContractTypeEnum::Sublease->value, ContractTypeEnum::Sales->value])
            );

            if (DocumentTypeEnum::tableNames()->doesntContain($query->from)) {
                // If the base table is not a contract table (e.g. layer_kvs), we need to filter by both contract tables - su_contracts and the su_sales_contracts using the OR operator

                // If we don't have 'Sales' in the filter values, remove the sales contract from the results
                $query->when(
                    ! $isSale,
                    fn () => $query->whereNull('su_sales_contracts.id')
                );

                // Filter su_contracts table
                $query->where(function ($subQ) use ($filterValues, $hasNullValue, $isSublease) {
                    $subQ->when(
                        count($filterValues) > 0,
                        fn () => $subQ->whereIn('su_contracts.nm_usage_rights', $filterValues)
                    )
                        ->when(
                            $hasNullValue,
                            fn () => $subQ->orWhereNull('su_contracts.nm_usage_rights')
                        )
                        ->when(
                            $isSublease,
                            fn () => $subQ->orWhereRaw('su_contracts.is_sublease = TRUE')
                        );
                });


                // Filter su_sales_contracts table
                $query->orWhere(function ($subQ) use ($isSale) {
                    $subQ->when(
                        $isSale,
                        fn () => $subQ->whereNotNull('su_sales_contracts.id')
                    );
                });
            } else {
                $query->when(
                    count($filterValues) > 0, // Filter by su_contracts table
                    fn () => $query->whereIn("{$query->from}.nm_usage_rights", $filterValues)
                )
                    ->when(
                        $isSublease,
                        fn () => $query->orWhereRaw("{$query->from}.is_sublease = TRUE")
                    );
            }
        });

        return $next($query);
    }

    public function handleFilterItems(Builder $query, Closure $next)
    {
        $className = self::class;

        if ($this->skipFilter($query->from)) {
            throw new Exception("Invalid table for filter '{$className}' selection");
        }

        $valueExpression = 'su_contracts.nm_usage_rights';

        $this->joinRequiredTables($query);
        $query->selectRaw("{$valueExpression} AS value, {$valueExpression} AS label")->distinct();

        return $next($query);
    }

    private function skipFilter(?string $baseTable): bool
    {
        // The documents of type 'Sales' and 'Mortgage' uses different table strucure and does not have the 'nm_usage_rights' column
        // So we skip the filter for these document types
        return collect([
            DocumentTypeEnum::Sales->tableName(),
            DocumentTypeEnum::Mortgage->tableName(),
        ])->contains($baseTable);
    }
}
