<?php

declare(strict_types=1);

namespace App\Pipes\ContractPlotFilter\Contract;

use App\Models\UserDb\Users\UserFarmingPermission;
use App\Types\Interfaces\Query\QueryFilterPipe;
use Closure;
use Exception;
use Illuminate\Database\Query\Builder;

class UserFarmingPermissionFilterPipe implements QueryFilterPipe
{
    public function __construct(private int $userId) {}

    public function handle(Builder $query, Closure $next)
    {
        if (! $this->userId) {
            return $next($query);
        }

        $allowedFarmingIds = UserFarmingPermission::where('user_id', $this->userId)
            ->where('permission', UserFarmingPermission::PERMISSION_READ)
            ->pluck('farming_id');

        $this->joinRequiredTables($query);

        $joinedTables = collect($query->joins)->pluck('table');

        match($query->from) {
            'su_contracts' => $query->whereIn('su_contracts.farming_id', $allowedFarmingIds),
            'su_sales_contracts' => $query->whereIn('su_sales_contracts.farming_id', $allowedFarmingIds),
            'su_hypothecs' => $query->whereIn('su_hypothecs.farming_id', $allowedFarmingIds),
            'layer_kvs' => $query->where(
                fn ($subQ) => $subQ
                    ->when(
                        $joinedTables->contains('su_contracts'),
                        fn () => $subQ->orWhereIn('su_contracts.farming_id', $allowedFarmingIds)
                    )
                    ->when(
                        $joinedTables->contains('su_sales_contracts'),
                        fn () => $subQ->orWhereIn('su_sales_contracts.farming_id', $allowedFarmingIds)
                    )
            ),
            default => throw new Exception('Error joining required tables for filtering by user farming permissions')
        };

        return $next($query);
    }

    public function joinRequiredTables(Builder $query)
    {
        $joinedTables = collect($query->joins)->pluck('table');

        return match($query->from) {
            'su_contracts',
            'su_hypothecs' => $query,
            'su_sales_contracts' => $query,
            'layer_kvs' => $query
                ->when(
                    ! $joinedTables->contains('su_contracts_plots_rel'),
                    fn () => $query->leftJoin('su_contracts_plots_rel', 'su_contracts_plots_rel.plot_id', '=', 'layer_kvs.gid')
                )
                ->when(
                    ! $joinedTables->contains('su_contracts'),
                    fn () => $query->leftJoin('su_contracts', 'su_contracts.id', '=', 'su_contracts_plots_rel.contract_id')
                )
                ->when(
                    ! $joinedTables->contains('su_sales_contracts_plots_rel'),
                    fn () => $query->leftJoin('su_sales_contracts_plots_rel', 'su_sales_contracts_plots_rel.plot_id', '=', 'layer_kvs.gid')
                )
                ->when(
                    ! $joinedTables->contains('su_sales_contracts'),
                    fn () => $query->leftJoin('su_sales_contracts', 'su_sales_contracts.id', '=', 'su_sales_contracts_plots_rel.sales_contract_id')
                ),

            default => throw new Exception('Error joining required tables for filtering by user farming permissions')
        };

        return $query;
    }
}
