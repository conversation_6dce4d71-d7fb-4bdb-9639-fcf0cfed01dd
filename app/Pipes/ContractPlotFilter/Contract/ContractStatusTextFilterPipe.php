<?php

declare(strict_types=1);

namespace App\Pipes\ContractPlotFilter\Contract;

use App\Types\Enums\Documents\DocumentTypeEnum;
use Closure;
use Exception;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;

class ContractStatusTextFilterPipe extends BaseContractFilterPipe
{
    public function __construct(private ?array $filterValues) {}

    public function handle(Builder $query, Closure $next)
    {
        if (empty($this->filterValues)) {
            return $next($query);
        }

        $this->joinRequiredTables($query);

        $hasNullValue = in_array(null, $this->filterValues);
        $filterValues = array_filter($this->filterValues, fn ($value) => null !== $value);

        $query->where(function ($query) use ($filterValues, $hasNullValue) {

            if (DocumentTypeEnum::tableNames()->doesntContain($query->from)) {
                // If the base table is not a contract table (e.g. layer_kvs), we need to filter by both contract tables - su_contracts and the su_sales_contracts using the OR operator
                // Note that both tables have the same columns 'active' and 'due_date'

                // Filter su_contracts table
                $query->where(function ($subQ) use ($filterValues, $hasNullValue) {
                    $subQ->whereIn(DB::raw('get_contract_status(su_contracts.contract_id, active, su_contracts.start_date, su_contracts.due_date)'), $filterValues)
                        ->when(
                            $hasNullValue,
                            fn () => $subQ->orWhereNull(DB::raw('get_contract_status(su_contracts.contract_id, su_contracts.active, su_contracts.start_date, su_contracts.due_date)'))
                        );
                });

                // Filter su_sales_contracts table
                $query->orWhere(function ($subQ) use ($filterValues, $hasNullValue) {
                    $subQ->whereIn(DB::raw('get_contract_status(su_sales_contracts.id, su_sales_contracts.active, su_sales_contracts.start_date, su_sales_contracts.due_date, false)'), $filterValues)
                        ->when(
                            $hasNullValue,
                            fn () => $subQ->orWhereNull(DB::raw('get_contract_status(su_sales_contracts.id, su_sales_contracts.active, su_sales_contracts.start_date, su_sales_contracts.due_date, false)'))
                        );
                });
            } else {
                $checkAnnexes = 'su_sales_contracts' === $query->from ? 'false' : 'true';

                // If the base table is a contract table, we need to filter by this specific contract table
                $query->whereIn(DB::raw("get_contract_status({$query->from}.id, {$query->from}.active, {$query->from}.start_date, {$query->from}.due_date, {$checkAnnexes})"), $filterValues)
                    ->when(
                        $hasNullValue,
                        fn () => $query->orWhereNull(DB::raw("get_contract_status({$query->from}.id, {$query->from}.active, {$query->from}.start_date, {$query->from}.due_date, {$checkAnnexes})"))
                    );
            }
        });

        return $next($query);
    }

    public function handleFilterItems(Builder $query, Closure $next)
    {
        $checkAnnexes = 'su_sales_contracts' === $query->from ? 'false' : 'true';
        $className = self::class;

        $valueExpression = match($query->from) {
            'su_contracts',
            'su_sales_contracts' => "get_contract_status({$query->from}.id, {$query->from}.active, {$query->from}.start_date, {$query->from}.due_date, {$checkAnnexes})",
            'layer_kvs' => "UNNEST(
                ARRAY_REMOVE(
                    ARRAY[
                        get_contract_status(su_contracts.id, su_contracts.active, su_contracts.start_date, su_contracts.due_date, {$checkAnnexes}),
                        get_contract_status(su_sales_contracts.id, su_sales_contracts.active, su_sales_contracts.start_date, su_sales_contracts.due_date, {$checkAnnexes})
                    ],
                    NULL
                )
            )",
            default => throw new Exception("Invalid table for filter '{$className}' selection")

        };

        $this->joinRequiredTables($query);
        $query->selectRaw("{$valueExpression} AS value, {$valueExpression} AS label")->distinct();

        return $next($query);
    }
}
