<?php

declare(strict_types=1);

namespace App\Pipes\ContractPlotFilter\Contract;

use App\Types\Enums\Documents\DocumentTypeEnum;
use Closure;
use Exception;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;

class ContractDateFromFilterPipe extends BaseContractFilterPipe
{
    public function __construct(private ?string $filterValues) {}

    public function handle(Builder $query, Closure $next)
    {
        if (empty($this->filterValues)) {
            return $next($query);
        }

        $this->joinRequiredTables($query);

        $query->where(function ($query) {
            if (DocumentTypeEnum::tableNames()->doesntContain($query->from)) {
                // If the base table is not a contract table (e.g. layer_kvs), we need to filter by both contract tables - su_contracts and the su_sales_contracts using the OR operator
                // Note that both tables have the same column 'c_date'

                // Filter su_contracts table
                $query->where('su_contracts.c_date', '>=', DB::raw("'{$this->filterValues}'::date"));

                // Filter su_sales_contracts table
                $query->orWhere('su_sales_contracts.c_date', '>=', DB::raw("'{$this->filterValues}'::date"));
            } else {
                // If the base table is a contract table, we need to filter by this specific contract table
                $query->where("{$query->from}.c_date", '>=', DB::raw("'{$this->filterValues}'::date"));
            }
        });

        return $next($query);
    }

    public function handleFilterItems(Builder $query, Closure $next)
    {
        $className = self::class;

        throw new Exception("The {$className} does not support filter items selection");
    }
}
