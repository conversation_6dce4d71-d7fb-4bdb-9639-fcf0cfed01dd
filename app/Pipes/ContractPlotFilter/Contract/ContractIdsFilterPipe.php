<?php

declare(strict_types=1);

namespace App\Pipes\ContractPlotFilter\Contract;

use App\Types\Enums\Documents\DocumentTypeEnum;
use Closure;
use Illuminate\Database\Query\Builder;
use Exception;

class ContractIdsFilterPipe extends BaseContractFilterPipe
{
    public function __construct(private ?array $filterValues) {}

    public function handle(Builder $query, Closure $next)
    {
        if (empty($this->filterValues)) {
            return $next($query);
        }

        $this->joinRequiredTables($query);

        $filterValues = collect($this->filterValues)->filter(fn ($value) => null !== $value && is_numeric($value))->toArray();

        if (empty($filterValues)) {
            return $next($query);
        }

        $query->where(function ($query) use ($filterValues) {
            if (DocumentTypeEnum::tableNames()->doesntContain($query->from)) {
                // If the base table is not a contract table (e.g. layer_kvs), we need to filter by both contract tables
                // Filter su_contracts table
                $query->whereIn('su_contracts.id', $filterValues);

                // Filter su_sales_contracts table
                $query->orWhereIn('su_sales_contracts.id', $filterValues);
            } else {
                // If the base table is a contract table, we need to filter by this specific contract table
                $query->whereIn("{$query->from}.id", $filterValues);
            }
        });

        return $next($query);
    }

    public function handleFilterItems(Builder $query, Closure $next)
    {
        // Contract IDs should not be returned as filter items for security reasons
        throw new Exception('handleFilterItems method is not supported for contract_ids filter');
    }
}
