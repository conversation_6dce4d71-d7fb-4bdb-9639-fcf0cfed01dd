<?php

declare(strict_types=1);

namespace App\Pipes\ContractPlotFilter\Contract;

use Closure;
use Illuminate\Database\Query\Builder;
use Exception;

class FromSubleaseFilterPipe extends BaseContractFilterPipe
{
    public function __construct(private ?array $filterValues) {}

    public function handle(Builder $query, Closure $next)
    {
        if (empty($this->filterValues)) {
            return $next($query);
        }

        if (! in_array($query->from, ['su_contracts', 'layer_kvs'])) {
            throw new Exception('The from_sublease filter is not applicable for this table');
        }

        $this->joinRequiredTables($query);

        $hasNullValue = in_array(null, $this->filterValues);
        $filterValues = array_filter($this->filterValues, fn ($value) => null !== $value);

        $query->where(function ($subQ) use ($filterValues, $hasNullValue) {
            $subQ->whereIn('su_contracts.from_sublease', $filterValues)
                ->when(
                    $hasNullValue,
                    fn () => $subQ->orWhereNull('su_contracts.from_sublease')
                );
        });


        return $next($query);
    }

    public function handleFilterItems(Builder $query, Closure $next)
    {

        $className = self::class;

        throw new Exception("The {$className} does not support filter items selection");
    }
}
