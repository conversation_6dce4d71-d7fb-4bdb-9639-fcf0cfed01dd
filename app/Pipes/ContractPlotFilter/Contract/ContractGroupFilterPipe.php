<?php

declare(strict_types=1);

namespace App\Pipes\ContractPlotFilter\Contract;

use Closure;
use Illuminate\Database\Query\Builder;
use Exception;

class ContractGroupFilterPipe extends BaseContractFilterPipe
{
    public function __construct(private ?array $filterValues) {}

    public function handle(Builder $query, Closure $next)
    {
        if (empty($this->filterValues)) {
            return $next($query);
        }

        $this->joinRequiredTables($query);

        $hasNullValue = in_array(null, $this->filterValues);
        $filterValues = array_filter($this->filterValues, fn ($value) => null !== $value);

        $query->where(function ($query) use ($filterValues, $hasNullValue) {
            $className = self::class;

            return match ($query->from) {
                'su_contracts',
                'layer_kvs' => $query->whereIn('su_contract_group.name', $filterValues)
                    ->when(
                        $hasNullValue,
                        fn () => $query->orWhereNull('su_contracts.group')
                    ),
                default => throw new Exception("Invalid table for filter '{$className}' selection")
            };
        });

        return $next($query);
    }

    public function handleFilterItems(Builder $query, Closure $next)
    {
        $className = self::class;
        $valueExpression = match($query->from) {
            'su_contracts',
            'layer_kvs' => 'su_contract_group.name',
            default => throw new Exception("Invalid table for filter '{$className}' selection")

        };

        $this->joinRequiredTables($query);
        $query->selectRaw("{$valueExpression} AS value, {$valueExpression} AS label")->distinct();

        return $next($query);
    }

    public function joinRequiredTables(Builder $query): void
    {
        parent::joinRequiredTables($query);

        $query->leftJoin('su_contract_group', 'su_contract_group.id', '=', 'su_contracts.group');
    }
}
