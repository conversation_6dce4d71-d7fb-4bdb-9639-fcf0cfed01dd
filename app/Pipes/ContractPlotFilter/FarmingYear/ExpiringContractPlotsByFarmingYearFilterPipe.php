<?php

declare(strict_types=1);

namespace App\Pipes\ContractPlotFilter\FarmingYear;

use App\Types\Enums\Documents\DocumentTypeEnum;
use App\Types\Interfaces\ContractPlot\ContractPlotFilterPipe;
use Closure;
use Exception;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;

class ExpiringContractPlotsByFarmingYearFilterPipe implements ContractPlotFilterPipe
{
    public function __construct(private ?string $farmingYear) {}

    public function handle(Builder $query, Closure $next)
    {
        if (! $this->farmingYear || $query->from === DocumentTypeEnum::Sales->tableName()) {
            // This filter is not applicable for sales contracts
            return $next($query);
        }

        [$startYear, $endYear] = explode('/', $this->farmingYear);

        if (! is_numeric($endYear)) {
            throw new Exception('The farming year must be in the format "YYYY/YYYY"');
        }

        $query->when(
            DocumentTypeEnum::tableNames()->contains($query->from),
            fn ($query) => $query->whereRaw("su_contracts.id IN (SELECT DISTINCT contract_id FROM get_expiring_contracts_plots_by_farming_year({$endYear}))")
        );

        $query->when(
            'layer_kvs' === $query->from,
            fn ($query) => $query->whereRaw("layer_kvs.gid IN (SELECT DISTINCT plot_id FROM get_expiring_contracts_plots_by_farming_year({$endYear}))")
        );


        return $next($query);

    }

    public function handleFilterItems(Builder $query, Closure $next)
    {
        $className = self::class;
        if (! in_array($query->from, ['su_contracts', 'layer_kvs'])) {
            throw new Exception("The {$className} is not applicable for this table");
        }

        $startYear = 2004;
        $endYear = 2100;

        $expiringContractsFarmingYears = DB::table(
            DB::raw("
                GENERATE_SERIES(
				    {$startYear},
				    {$endYear},
                    1
			    ) AS farming_years(f_year)
            ")
        )
            ->selectRaw("
            f_year - 1 || '/' || f_year as f_year_text,
			f_year,
			array_agg(distinct expiring_cp.contract_id) as contract_ids,
			array_agg(distinct expiring_cp.plot_id) as plot_ids
        ")
            ->join(
                DB::raw('get_expiring_contracts_plots_by_farming_year(f_year) as expiring_cp(contract_id, plot_id)'),
                fn ($join) => $join->whereRaw('expiring_cp.contract_id notnull')
            )
            ->groupBy('f_year');

        // Join the CTE to the main query so we can access the expiring_contracts_farming_years table when selecting the returned value expression
        $query->withExpression('expiring_contracts_farming_years', $expiringContractsFarmingYears);
        $query->join(
            'expiring_contracts_farming_years',
            fn ($join) => match($query->from) {
                'su_contracts' => $join->on('su_contracts.id', '=', DB::raw('ANY(expiring_contracts_farming_years.contract_ids)')),
                'layer_kvs' => $join->on('layer_kvs.gid', '=', DB::raw('ANY(expiring_contracts_farming_years.plot_ids)'))
            }
        );

        $query->selectRaw('
            expiring_contracts_farming_years.f_year_text as value,
            expiring_contracts_farming_years.f_year_text as label,
            expiring_contracts_farming_years.f_year - 1 = extract(year from now()::date) as has_delimiter,
            CASE WHEN 
                expiring_contracts_farming_years.f_year = extract(year from now()::date) 
                OR expiring_contracts_farming_years.f_year - 1 = extract(year from now()::date)
                    THEN 1
                    ELSE 2
            END AS sort_priority
        ')->distinct();

        return $next($query);
    }

    public function joinRequiredTables(Builder $query): void
    {
        // No need to join tables for this filter
    }
}
