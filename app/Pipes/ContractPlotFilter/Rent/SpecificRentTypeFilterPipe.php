<?php

declare(strict_types=1);

namespace App\Pipes\ContractPlotFilter\Rent;

use App\Types\Interfaces\ContractPlot\ContractPlotFilterPipe;
use Closure;
use Exception;
use Illuminate\Database\Query\Builder;

class SpecificRentTypeFilterPipe implements ContractPlotFilterPipe
{
    public function __construct(private ?array $filterValues) {}

    public function handle(Builder $query, Closure $next)
    {
        if (empty($this->filterValues)) {
            return $next($query);
        }

        $this->joinRequiredTables($query);

        $filterValues = collect($this->filterValues);
        $hasNullValue = $filterValues->contains(null);
        $filterValues = $filterValues->filter(fn ($value) => ! is_null($value));

        $query->whereIn('su_contracts_rents_types.type', $filterValues->toArray())
            ->when(
                $hasNullValue,
                fn (Builder $subQ) => $subQ->orWhereNull('su_contracts_rents_types.id')
            );

        return $next($query);
    }

    public function handleFilterItems(Builder $query, Closure $next)
    {
        $this->joinRequiredTables($query);

        $valueExpression = 'su_contracts_rents_types.type';

        $query->selectRaw("{$valueExpression} AS value, {$valueExpression} AS label")->distinct();

        return $next($query);
    }

    public function joinRequiredTables(Builder $query): void
    {
        $joinedTables = collect($query->joins)->pluck('table');
        $className = self::class;

        match($query->from) {
            'su_contracts' => $query
                ->when(
                    ! $joinedTables->contains('su_contracts_rents_types'),
                    fn () => $query->leftJoin('su_contracts_rents_types', 'su_contracts_rents_types.contract_id', '=', 'su_contracts.id')
                ),

            'layer_kvs' => $query
                ->when(
                    ! $joinedTables->contains('su_contracts_plots_rel'),
                    fn () => $query->leftJoin('su_contracts_plots_rel', 'su_contracts_plots_rel.plot_id', '=', 'layer_kvs.gid')
                )
                ->when(
                    ! $joinedTables->contains('su_contracts_rents_types'),
                    fn () => $query->leftJoin('su_contracts_rents_types', 'su_contracts_rents_types.contract_id', '=', 'su_contracts_plots_rel.contract_id')
                ),

            default => throw new Exception("Error joining required tables for '{$className}'")
        };
    }
}
