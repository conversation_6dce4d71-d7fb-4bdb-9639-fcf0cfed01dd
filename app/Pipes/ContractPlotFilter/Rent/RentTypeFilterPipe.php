<?php

declare(strict_types=1);

namespace App\Pipes\ContractPlotFilter\Rent;

use App\Enums\Documents\RentTypeEnum;
use App\Types\Interfaces\ContractPlot\ContractPlotFilterPipe;
use Closure;
use Exception;
use Illuminate\Database\Query\Builder;

class RentTypeFilterPipe implements ContractPlotFilterPipe
{
    public function __construct(private ?array $filterValues) {}

    public function handle(Builder $query, Closure $next)
    {
        if (empty($this->filterValues)) {
            return $next($query);
        }

        $this->joinRequiredTables($query);

        $filterValues = collect($this->filterValues);
        $hasNullValue = $filterValues->contains(null);

        $filterValues->filter(fn ($value) => ! is_null($value))
            ->each(function (string $rentType) use ($query) {
                $rentType = RentTypeEnum::tryFrom($rentType);
                $rentTypeColumn = $this->getRentTypeColumn($rentType);
                $query->orWhereNotNull($rentTypeColumn);
            });

        $query->when(
            $hasNullValue,
            fn ($subQ) => $subQ->orWhere(
                fn (Builder $subQ2) => $subQ2->whereNull('su_contracts.renta')
                    ->whereNull('su_contracts_plots_rel.rent_per_plot')
                    ->whereNull('su_contracts_rents_types.id')
                    ->whereNull('su_contracts.overall_renta')
            )
        );

        return $next($query);
    }

    public function handleFilterItems(Builder $query, Closure $next)
    {
        $this->joinRequiredTables($query);

        $standartRent = RentTypeEnum::Standart->value;
        $individualRent = RentTypeEnum::Individual->value;
        $specificRent = RentTypeEnum::Specific->value;
        $fixedRent = RentTypeEnum::Fixed->value;


        $valueExpression = "
            UNNEST(
                ARRAY[
                    CASE WHEN su_contracts.overall_renta IS NOT NULL THEN '{$fixedRent}' END,
                    CASE WHEN su_contracts_plots_rel.rent_per_plot IS NOT NULL THEN '{$individualRent}' END,
                    CASE WHEN su_contracts.renta IS NOT NULL THEN '{$standartRent}' END,
                    CASE WHEN su_contracts_rents_types.id IS NOT NULL THEN '{$specificRent}' END
                ]
            )
        ";

        $query->selectRaw("{$valueExpression} AS value, {$valueExpression} AS label")->distinct();

        return $next($query);
    }

    public function joinRequiredTables(Builder $query): void
    {
        $joinedTables = collect($query->joins)->pluck('table');
        $className = self::class;

        match($query->from) {
            'su_contracts' => $query
                ->when(
                    ! $joinedTables->contains('su_contracts_plots_rel'),
                    fn () => $query->leftJoin('su_contracts_plots_rel', 'su_contracts_plots_rel.contract_id', '=', 'su_contracts.id')
                )
                ->when(
                    ! $joinedTables->contains('su_contracts_rents_types'),
                    fn () => $query->leftJoin('su_contracts_rents_types', 'su_contracts_rents_types.contract_id', '=', 'su_contracts.id')
                ),

            'layer_kvs' => $query
                ->when(
                    ! $joinedTables->contains('su_contracts_plots_rel'),
                    fn () => $query->leftJoin('su_contracts_plots_rel', 'su_contracts_plots_rel.plot_id', '=', 'layer_kvs.gid')
                )
                ->when(
                    ! $joinedTables->contains('su_contracts'),
                    fn () => $query->leftJoin('su_contracts', 'su_contracts.id', '=', 'su_contracts_plots_rel.contract_id')
                )
                ->when(
                    ! $joinedTables->contains('su_contracts_rents_types'),
                    fn () => $query->leftJoin('su_contracts_rents_types', 'su_contracts_rents_types.contract_id', '=', 'su_contracts.id')
                ),

            default => throw new Exception("Error joining required tables for '{$className}'")
        };
    }

    public function getRentTypeColumn(RentTypeEnum $rentType): string
    {
        return match ($rentType) {
            RentTypeEnum::Standart => 'su_contracts.renta',
            RentTypeEnum::Individual => 'su_contracts_plots_rel.rent_per_plot',
            RentTypeEnum::Specific => 'su_contracts_rents_types.id',
            RentTypeEnum::Fixed => 'su_contracts.overall_renta',
        };
    }
}
