<?php

declare(strict_types=1);

namespace App\Pipes\ContractPlotFilter\Plot;

use App\Types\Interfaces\ContractPlot\ContractPlotFilterPipe;
use Closure;
use Exception;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;

abstract class BasePlotFilterPipe implements ContractPlotFilterPipe
{
    protected ?array $filterValues;
    protected ?string $filterColumn;

    public function handle(Builder $query, Closure $next)
    {
        if (empty($this->filterValues) || empty($this->filterColumn)) {
            return $next($query);
        }

        $this->joinRequiredTables($query);

        $this->applyAnnexActionFilter($query, 'added');

        $query->where(function ($query) {
            $hasNullValue = in_array(null, $this->filterValues);
            $filterValues = array_filter($this->filterValues, fn ($value) => null !== $value);

            $query->whereIn(DB::raw($this->filterColumn), $filterValues)
                ->when(
                    $hasNullValue,
                    fn () => $query->orWhereNull(DB::raw($this->filterColumn))
                );
        });

        return $next($query);
    }

    public function handleFilterItems(Builder $query, Closure $next)
    {
        if (empty($this->filterColumn)) {
            throw new Exception('Filter column is not set for BasePlotFilterPipe');
        }

        $this->joinRequiredTables($query);
        $query->selectRaw("{$this->filterColumn} AS value, {$this->filterColumn} AS label")->distinct();

        return $next($query);
    }

    public function joinRequiredTables(Builder $query): void
    {
        $joinedTables = collect($query->joins)->pluck('table');

        match($query->from) {
            // If the table in the from clause is 'layer_kvs' we don't need to join any tables
            'layer_kvs' => $query,

            // If the table in the from clause is 'su_contracts' we need to join 'layer_kvs' table through 'su_contracts_plots_rel' and 'su_subleases_plots_contracts_rel' for subleases
            'su_contracts' =>  $query
                ->when(
                    ! $joinedTables->contains('su_subleases_plots_contracts_rel'),
                    fn () => $query->leftJoin('su_subleases_plots_contracts_rel', 'su_subleases_plots_contracts_rel.sublease_id', '=', 'su_contracts.id')
                )
                ->when(
                    ! $joinedTables->contains('su_contracts_plots_rel'),
                    fn () => $query->leftJoin(
                        'su_contracts_plots_rel',
                        fn ($join) => $join
                            ->on('su_contracts_plots_rel.contract_id', '=', 'su_contracts.id')
                            ->orOn('su_contracts_plots_rel.id', '=', 'su_subleases_plots_contracts_rel.pc_rel_id')
                    )
                )
                ->when(
                    ! $joinedTables->contains('layer_kvs'),
                    fn () => $query->leftJoin('layer_kvs', 'layer_kvs.gid', '=', 'su_contracts_plots_rel.plot_id')
                ),

            // If the table in the from clause is 'su_sales_contracts' we need to join 'layer_kvs' table through 'su_sales_contracts_plots_rel'
            'su_sales_contracts' => $query
                ->when(
                    ! $joinedTables->contains('su_sales_contracts_plots_rel'),
                    fn () => $query->leftJoin('su_sales_contracts_plots_rel', 'su_sales_contracts_plots_rel.sales_contract_id', '=', 'su_sales_contracts.id')
                )
                ->when(
                    ! $joinedTables->contains('layer_kvs'),
                    fn () => $query->leftJoin('layer_kvs', 'layer_kvs.gid', '=', 'su_sales_contracts_plots_rel.plot_id')
                ),

            'su_hypothecs' => $query
                ->when(
                    ! $joinedTables->contains('su_hypothecs_plots_rel'),
                    fn () => $query->leftJoin('su_hypothecs_plots_rel', 'su_hypothecs_plots_rel.hypothec_id', '=', 'su_hypothecs.id')
                )
                ->when(
                    ! $joinedTables->contains('layer_kvs'),
                    fn () => $query->leftJoin('layer_kvs', 'layer_kvs.gid', '=', 'su_hypothecs_plots_rel.plot_id')
                ),

            // If the table in the from clause is 'su_owners' we need to join through owner relationships to get to plots
            'su_owners' => $query
                ->when(
                    ! $joinedTables->contains('su_plots_owners_rel'),
                    fn () => $query->leftJoin('su_plots_owners_rel', 'su_plots_owners_rel.owner_id', '=', 'su_owners.id')
                )
                ->when(
                    ! $joinedTables->contains('su_contracts_plots_rel'),
                    fn () => $query->leftJoin('su_contracts_plots_rel', 'su_contracts_plots_rel.id', '=', 'su_plots_owners_rel.pc_rel_id')
                )
                ->when(
                    ! $joinedTables->contains('layer_kvs'),
                    fn () => $query->leftJoin('layer_kvs', 'layer_kvs.gid', '=', 'su_contracts_plots_rel.plot_id')
                )
                ->when(
                    ! $joinedTables->contains('su_contracts'),
                    fn () => $query->leftJoin('su_contracts', 'su_contracts.id', '=', 'su_contracts_plots_rel.contract_id')
                ),

            default => throw new Exception('Error joining required tables for filtering by plot properties')
        };
    }

    /**
     * If the query contains 'su_contracts_plots_rel' table, applies the annex action filter to the query.
     */
    public function applyAnnexActionFilter(Builder $query, string $annexAction): void
    {
        $joinedTables = collect($query->joins)->pluck('table');

        $query->when(
            $joinedTables->contains('su_contracts_plots_rel'),
            fn () => $query->whereRaw("su_contracts_plots_rel.annex_action = '{$annexAction}'")
        );
    }
}
