<?php

declare(strict_types=1);

namespace App\Pipes\ContractPlotFilter\Plot;

use Closure;
use Exception;
use Illuminate\Database\Query\Builder;

class PlotIdFilterPipe extends BasePlotFilterPipe
{
    public function __construct(protected ?array $filterValues)
    {
        $this->filterColumn = 'layer_kvs.gid';
    }

    public function handleFilterItems(Builder $query, Closure $next)
    {
        // Plot ids should not be returned as a filter item because of security reasons.
        throw new Exception('handleFilterItems method is not supported for plot_id filter');
    }
}
