<?php

declare(strict_types=1);

namespace App\Pipes\ContractPlotFilter\Plot;

use App\Types\Interfaces\ContractPlot\ContractPlotFilterPipe;
use Closure;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;
use Exception;

/**
 * <PERSON><PERSON> that adds available area calculation to the query using CTEs.
 * This calculates available plot area by subtracting overlapping contract areas from document_area.
 * Formula: available_area = document_area - max_overlapping_area
 * 
 * Based on the same logic as getNotAvailableContractsPlots method.
 */
class AvailableAreaCalculationPipe implements ContractPlotFilterPipe
{
    public function __construct(
        private ?array $filterParams = null
    ) {}

    public function handle(Builder $query, Closure $next)
    {
        $this->addAvailableAreaCalculation($query, $this->filterParams['start_date'], $this->filterParams['due_date']);

        return $next($query);
    }

    public function handleFilterItems(Builder $query, Closure $next)
    {
        return $next($query);
    }

    public function joinRequiredTables(Builder $query): void
    {
    }

    /**
     * Add the available area calculation using the DB function
     * This approach eliminates parameter binding issues and improves performance
     * Also filters out plots with zero or negative available area
     */
    private function addAvailableAreaCalculation(Builder $query, string $startDate, string $dueDate): void
    {
        $query->leftJoin(
            DB::raw("get_plot_available_areas('{$startDate}', '{$dueDate}') as moa"),
            'moa.plot_id',
            '=',
            'layer_kvs.gid'
        );

        $query->whereRaw('ROUND((
            COALESCE(layer_kvs.document_area, ST_Area(layer_kvs.geom)/1000) -
            COALESCE(moa.max_overlapping_area, 0)
        )::numeric, 3) > 0');
    }
}
