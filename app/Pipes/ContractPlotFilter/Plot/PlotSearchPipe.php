<?php

declare(strict_types=1);

namespace App\Pipes\ContractPlotFilter\Plot;

use App\Types\Interfaces\ContractPlot\ContractPlotFilterPipe;
use Closure;
use Exception;
use Illuminate\Database\Query\Builder;

/**
 * Search pipe for plots using ILIKE pattern matching across multiple columns.
 * Accepts an array of search terms and searches across multiple plot identifier fields.
 *
 * Searches in:
 * - kad_ident: Full identifier (e.g., 06865.0.205)
 * - ekate: Settlement code (first part of kad_ident)
 * - masiv: Massif code (middle part of kad_ident)
 *
 * kad_ident format: {ekate}.{masiv}.{number} (e.g., 06865.0.205)
 * This pipe allows flexible searching across all related identifier fields.
 */
class PlotSearchPipe implements ContractPlotFilterPipe
{

    public function __construct(protected array $filterValues)
    {
       $this->filterValues = $filterValues;
    }

    public function handle(Builder $query, Closure $next)
    {
        if (empty($this->filterValues)) {
            return $next($query);
        }

        $this->joinRequiredTables($query);

        // Use ILIKE for partial matching across multiple columns
        // Search in kad_ident, ekate, and masiv columns
        $query->where(function ($mainQuery) {
            foreach ($this->filterValues as $searchTerm) {
                if (empty($searchTerm)) continue;

                $searchTerm = trim($searchTerm);
                if (!empty($searchTerm)) {
                    $mainQuery->orWhere(function ($subQuery) use ($searchTerm) {
                        $subQuery->where('layer_kvs.kad_ident', 'ILIKE', "%{$searchTerm}%")
                                 ->orWhere('layer_kvs.ekate', 'ILIKE', "%{$searchTerm}%")
                                 ->orWhere('layer_kvs.masiv', 'ILIKE', "%{$searchTerm}%");
                    });
                }
            }
        });

        return $next($query);
    }

    public function handleFilterItems(Builder $query, Closure $next)
    {
        $this->joinRequiredTables($query);
        $query->selectRaw("
            CONCAT(layer_kvs.kad_ident, ' (', layer_kvs.ekate, '.', layer_kvs.masiv, ')') AS label,
            layer_kvs.kad_ident AS value
        ")->distinct();

        return $next($query);
    }

    public function joinRequiredTables(Builder $query): void
    {
        // For layer_kvs table, no additional joins needed
        if ($query->from === 'layer_kvs') {
            return;
        }

        // For other tables, we might need to join layer_kvs
        $joinedTables = collect($query->joins)->pluck('table');

        if (!$joinedTables->contains('layer_kvs')) {
            match($query->from) {
                'su_contracts' => $query
                    ->leftJoin('su_contracts_plots_rel', 'su_contracts_plots_rel.contract_id', '=', 'su_contracts.id')
                    ->leftJoin('layer_kvs', 'layer_kvs.gid', '=', 'su_contracts_plots_rel.plot_id'),
                'su_sales_contracts' => $query
                    ->leftJoin('su_sales_contracts_plots_rel', 'su_sales_contracts_plots_rel.sales_contract_id', '=', 'su_sales_contracts.id')
                    ->leftJoin('layer_kvs', 'layer_kvs.gid', '=', 'su_sales_contracts_plots_rel.plot_id'),
                'su_hypothecs' => $query
                    ->leftJoin('su_hypothecs_plots_rel', 'su_hypothecs_plots_rel.hypothec_id', '=', 'su_hypothecs.id')
                    ->leftJoin('layer_kvs', 'layer_kvs.gid', '=', 'su_hypothecs_plots_rel.plot_id'),
                default => throw new Exception('Unsupported table for plot search: ' . $query->from)
            };
        }
    }

    /**
     * Create an instance with a search term (backward compatibility)
     */
    public static function withSearchTerm(?string $searchTerm): self
    {
        return new self($searchTerm ? [$searchTerm] : null);
    }
}
