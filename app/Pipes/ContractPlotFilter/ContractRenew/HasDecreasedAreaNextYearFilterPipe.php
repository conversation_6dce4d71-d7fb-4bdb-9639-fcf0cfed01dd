<?php

declare(strict_types=1);

namespace App\Pipes\ContractPlotFilter\ContractRenew;

/**
 * Filters the contracts containing plots that have smaller area in the next farming year compared to the current farming year.
 *
 * @param ?array $filterValue
 */
class HasDecreasedAreaNextYearFilterPipe extends BaseContractRenewFilterPipe
{
    public function __construct(protected array $filterValue)
    {
        $this->filterColumn = 'contracts_for_renew.has_decreased_area_next_year';
    }
}
