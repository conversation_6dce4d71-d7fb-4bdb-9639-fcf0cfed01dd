<?php

declare(strict_types=1);

namespace App\Pipes\ContractPlotFilter\ContractRenew;

/**
 * Filters contracts by contract-level can_renew status using bool_and logic.
 * Uses the can_renew_contract column from get_contracts_for_renew function.
 *
 * @param ?array $filterValue
 */
class CanRenewContractFilterPipe extends BaseContractRenewFilterPipe
{
    public function __construct(protected array $filterValue)
    {
        $this->filterColumn = 'contracts_for_renew.can_renew_contract';
    }
}
