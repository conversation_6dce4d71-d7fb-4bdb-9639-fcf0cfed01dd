<?php

declare(strict_types=1);

namespace App\Pipes\Owners;

use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;
use App\Types\Interfaces\Owners\OwnerFilterPipe;


class OwnerRepsMultiFieldSearchFilterPipe implements OwnerFilterPipe
{
    private string $search;

    public function withParams(array $filterParams): self
    {
        $this->search = $filterParams['search'];
        return $this;
    }

    public function handle(Builder $query, \Closure $next): Builder
    {
        if (!empty($this->search)) {
            $this->applySearchFilter($query);
        }

        return $next($query);
    }

    public function joinRequiredTables(Builder $query): void
    {
    }

    /**
     * Apply search filter to the query
     * Searches in rep_name, rep_surname, rep_lastname, and rep_egn fields
     */
    private function applySearchFilter(Builder $query): void
    {
        $searchTerm = '%' . $this->search . '%';

        $query->where(function ($subQuery) use ($searchTerm) {
            $subQuery->where('su_owners_reps.rep_name', 'ILIKE', $searchTerm)
                ->orWhere('su_owners_reps.rep_surname', 'ILIKE', $searchTerm)
                ->orWhere('su_owners_reps.rep_lastname', 'ILIKE', $searchTerm)
                ->orWhere('su_owners_reps.rep_egn', 'ILIKE', $searchTerm);
        });
    }
}
