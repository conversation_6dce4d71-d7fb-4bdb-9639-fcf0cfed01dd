<?php

declare(strict_types=1);

namespace App\Pipes\Owners;

use Closure;
use Exception;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * Multi-field search pipe for owner searching.
 * Searches across name, surname, lastname, egn, eik, company_name fields
 * using search terms from frontend input.
 * Follows the same pattern as other owner filter pipes.
 */
class OwnerMultiFieldSearchFilterPipe extends BaseOwnerFilterPipe
{
    public function __construct(protected $filterValues)
    {
    }

    public function handle(Builder $query, Closure $next)
    {
        $className = self::class;
        if (!is_array($this->filterValues)) {
            throw new Exception("Filter values must be an array for '{$className}'");
        }

        if (empty($this->filterValues)) {
            return $next($query);
        }

        $this->joinRequiredTables($query);

        $query->where(function ($mainQuery) {
            foreach ($this->filterValues as $searchTerm) {
                if (empty($searchTerm)) continue;

                Log::info('OwnerMultiFieldSearchFilterPipe: Searching for term', [
                    'searchTerm' => $searchTerm
                ]);
                $searchPattern = "%{$searchTerm}%";
                $mainQuery->orWhere(function ($subQuery) use ($searchPattern) {
                    $subQuery->where(DB::raw("TRIM(COALESCE(su_owners.name, '')) || ' ' || TRIM(COALESCE(su_owners.surname, '')) || ' ' || TRIM(COALESCE(su_owners.lastname, ''))"), 'ILIKE', $searchPattern)
                        ->orWhere('su_owners.egn', 'ILIKE', $searchPattern)
                        ->orWhere('su_owners.eik', 'ILIKE', $searchPattern)
                        ->orWhere('su_owners.company_name', 'ILIKE', $searchPattern);
                });
            }
        });

        return $next($query);
    }

    public function handleFilterItems(Builder $query, Closure $next)
    {
        $valueExpression = "
            COALESCE(
                NULLIF(TRIM(CONCAT_WS(' ', su_owners.name, su_owners.surname, su_owners.lastname)), ''),
                su_owners.company_name
            )
        ";

        $this->joinRequiredTables($query);
        $query->selectRaw("{$valueExpression} AS value, {$valueExpression} AS label")
            ->whereNotNull('su_owners.id')
            ->distinct();

        return $next($query);
    }

}
