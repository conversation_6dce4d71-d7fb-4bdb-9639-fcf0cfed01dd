<?php

declare(strict_types=1);

namespace App\Pipes\Owners;

use Closure;
use Exception;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;

class ContractSignerFilterPipe extends BaseOwnerFilterPipe
{
    public function __construct(protected $filterValues)
    {
        $this->includeRepresentatives = true;
        $this->filterColumn = 'TRIM(su_owners.name) || \' \' || TRIM(su_owners.surname) || \' \' || TRIM(su_owners.lastname)';
    }

    public function handle(Builder $query, Closure $next)
    {
        $className = self::class;
        if (! is_array($this->filterValues)) {
            throw new Exception("Filter values must be an array for '{$className}'");
        }

        if (empty($this->filterValues)) {
            return $next($query);
        }

        $this->joinRequiredTables($query);
        $query->where(function ($query) {
            $hasNullValue = in_array(null, $this->filterValues);
            $filterValues = array_filter($this->filterValues, fn ($value) => null !== $value);

            $query->where(
                fn ($subQ) => $subQ
                    ->whereIn(
                        DB::raw($this->filterColumn),
                        $filterValues
                    )
                    ->where('su_plots_owners_rel.is_signer', 1)
            )
                ->when(
                    $hasNullValue,
                    fn () => $query->orWhereNull(DB::raw($this->filterColumn))
                );
        });

        return $next($query);
    }

    public function handleFilterItems(Builder $query, Closure $next)
    {
        $valueExpression = "CASE WHEN su_plots_owners_rel.id ISNULL THEN NULL ELSE {$this->filterColumn} END";

        $this->joinRequiredTables($query);
        $query->selectRaw("{$valueExpression} AS value, {$valueExpression} AS label")
            ->where(
                fn ($subQ) => $subQ
                    ->where('su_plots_owners_rel.is_signer', 1)
                    ->orWhereNull('su_plots_owners_rel.id')
            )
            ->distinct();

        return $next($query);
    }
}
