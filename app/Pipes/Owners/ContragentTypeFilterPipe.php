<?php

declare(strict_types=1);

namespace App\Pipes\Owners;

use App\Enums\Documents\ContragentTypeEnum;
use Closure;
use Illuminate\Database\Query\Builder;

class ContragentTypeFilterPipe extends BaseOwnerFilterPipe
{
    public function __construct(protected $filterValues)
    {
        $this->includeRepresentatives = true;
    }

    public function handle(Builder $query, Closure $next)
    {
        if (empty($this->filterValues)) {
            return $next($query);
        }

        $filterValues = collect($this->filterValues);

        $this->joinRequiredTables($query);

        $filterValues = collect($this->filterValues);

        $representativeCondition = '(su_owners_reps.owner_id NOTNULL AND su_owners_reps.id = su_plots_owners_rel.rep_id)';

        $query->when(
            $filterValues->contains(ContragentTypeEnum::Owner->value),
            fn ($subQ) => $subQ->orWhereRaw("(su_plots_owners_rel.is_heritor = FALSE AND NOT {$representativeCondition})")
        )
            ->when(
                $filterValues->contains(ContragentTypeEnum::Heritor->value),
                fn ($subQ) => $subQ->orWhere('su_plots_owners_rel.is_heritor', true)
            )
            ->when(
                $filterValues->contains(ContragentTypeEnum::Representative->value),
                fn ($subQ) => $subQ->orWhereRaw(
                    $representativeCondition
                )
            );


        return $next($query);
    }

    public function handleFilterItems(Builder $query, Closure $next)
    {
        $contragentTypesStr = "'" . collect(ContragentTypeEnum::values())->join("', '") . "'";

        $valueExpression = "
            UNNEST(
                ARRAY[{$contragentTypesStr}]
            )
        ";

        $query->selectRaw("{$valueExpression} AS value, {$valueExpression} AS label")->distinct();

        return $next($query);
    }
}
