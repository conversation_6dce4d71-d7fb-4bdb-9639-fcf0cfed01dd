<?php

declare(strict_types=1);

namespace App\Pipes\Owners;

use App\Types\Interfaces\Query\QueryFilterPipe;

class HeritorNameFilterPipe extends BaseOwnerFilterPipe implements QueryFilterPipe
{
    public function __construct(protected $filterValues)
    {
        $this->filterColumn = '(TRIM(su_owners.name) || \' \' || TRIM(su_owners.surname) || \' \' || TRIM(su_owners.lastname))';
        $this->isHeritor = true;
    }
}
