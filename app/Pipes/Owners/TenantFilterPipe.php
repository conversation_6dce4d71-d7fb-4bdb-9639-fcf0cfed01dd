<?php

declare(strict_types=1);

namespace App\Pipes\Owners;

use Exception;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;

class TenantFilterPipe extends BaseOwnerFilterPipe
{
    public function __construct(protected $filterValues)
    {
        $this->filterColumn = "COALESCE(
            NULLIF(TRIM(uf.company), ''),
            NULLIF(TRIM(su_owners.company_name), ''),
            TRIM(CONCAT_WS(' ', TRIM(su_owners.name), TRIM(su_owners.surname), TRIM(su_owners.lastname)))
        )";

    }

    public function joinRequiredTables(Builder $query): void
    {
        $className = self::class;
        $joinedTables = collect($query->joins)->pluck('table');

        $mainDB = Config::get('database.connections.main');
        $connectionString = "host={$mainDB['host']} port={$mainDB['port']} user={$mainDB['username']} dbname={$mainDB['database']} password={$mainDB['password']}";


        match($query->from) {
            'layer_kvs' => $query
                ->when(
                    ! $joinedTables->contains('su_contracts_plots_rel'),
                    fn () => $query->leftJoin('su_contracts_plots_rel', 'su_contracts_plots_rel.plot_id', '=', 'layer_kvs.gid')
                )
                ->when(
                    ! $joinedTables->contains('su_contracts'),
                    fn () => $query->leftJoin('su_contracts', 'su_contracts.id', '=', 'su_contracts_plots_rel.contract_id')
                ),


            'su_contracts' =>  $query,

            default => throw new Exception("Error joining required tables for filtering by plot {$className}")
        };

        $query
            ->when(
                ! $joinedTables->contains('su_contracts_farming_contragents'),
                fn () => $query->leftJoin('su_contracts_farming_contragents', 'su_contracts_farming_contragents.contract_id', '=', 'su_contracts.id')
            )
            ->when(
                ! $joinedTables->contains(DB::raw("dblink('{$connectionString}', 'SELECT id, company FROM su_users_farming') AS uf(id int, company text)")),
                fn () => $query->leftJoin(DB::raw("dblink('{$connectionString}', 'SELECT id, company FROM su_users_farming') AS uf(id int, company text)"), 'uf.id', '=', 'su_contracts_farming_contragents.farming_id')
            )
            ->when(
                ! $joinedTables->contains('su_contracts_contragents'),
                fn () => $query->leftJoin('su_contracts_contragents', 'su_contracts_contragents.contract_id', '=', 'su_contracts.id')
            )
            ->when(
                ! $joinedTables->contains('su_owners'),
                fn () => $query->leftJoin('su_owners', 'su_owners.id', '=', 'su_contracts_contragents.owner_id')
            );
    }
}
