<?php

declare(strict_types=1);

namespace App\Pipes\Owners;



/**
 * Filter owners by EKATTE code (settlement/locality identifier)
 *
 * Relationship: su_owners → su_plots_owners_rel → su_contracts_plots_rel → layer_kvs (by plot_id = gid)
 */
class EkatteFilterPipe extends BaseOwnerFilterPipe
{
    public function __construct(?array $filterValues)
    {
        $this->filterValues = $filterValues;
        $this->filterColumn = 'layer_kvs.ekate';
    }
}
