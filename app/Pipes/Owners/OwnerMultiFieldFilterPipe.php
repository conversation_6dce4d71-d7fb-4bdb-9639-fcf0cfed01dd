<?php

declare(strict_types=1);

namespace App\Pipes\Owners;

use App\Types\Interfaces\ContractPlot\ContractPlotFilterPipe;
use Closure;
use Exception;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;

class OwnerMultiFieldFilterPipe implements ContractPlotFilterPipe
{
    public const COLUMN_EXPRESSION = "
        UNNEST(
            ARRAY_REMOVE(
                ARRAY[
                    TRIM(topic_layer_kvs_by_owner_name_label_items.owner_name),
                    TRIM(su_owners.name) || ' ' || TRIM(su_owners.surname) || ' ' || TRIM(su_owners.lastname),
                    TRIM(su_owners_reps.rep_name) || ' ' || TRIM(su_owners_reps.rep_surname) || ' ' || TRIM(su_owners_reps.rep_lastname)
                ],
                NULL
            )
        )
    ";

    public function __construct(private ?array $filterValues) {}

    public function handle(Builder $query, Closure $next)
    {
        if (empty($this->filterValues)) {
            return $next($query);
        }

        $this->joinRequiredTables($query);

        $query->whereIn(DB::raw(self::COLUMN_EXPRESSION), $this->filterValues);

        return $next($query);
    }

    public function handleFilterItems(Builder $query, Closure $next)
    {
        $valueExpression = self::COLUMN_EXPRESSION;

        $this->joinRequiredTables($query);
        $query->selectRaw("{$valueExpression} AS value, {$valueExpression} AS label")->distinct();

        return $next($query);
    }

    public function joinRequiredTables(Builder $query): void
    {
        $joinedTables = collect($query->joins)->pluck('table');

        match($query->from) {
            'layer_kvs' => $query
                ->when(
                    ! $joinedTables->contains('su_contracts_plots_rel'),
                    fn () => $query->leftJoin('su_contracts_plots_rel', 'su_contracts_plots_rel.plot_id', '=', 'layer_kvs.gid')
                )
                ->when(
                    ! $joinedTables->contains('su_sales_contracts_plots_rel'),
                    fn () => $query->leftJoin('su_sales_contracts_plots_rel', 'su_sales_contracts_plots_rel.plot_id', '=', 'layer_kvs.gid')
                )
                ->when(
                    ! $joinedTables->contains('su_plots_owners_rel'),
                    fn () => $query
                        ->leftJoin(
                            'su_plots_owners_rel',
                            fn ($join) => $join
                                ->on(
                                    fn ($subJoin) => $subJoin->on('su_plots_owners_rel.pc_rel_id', '=', 'su_contracts_plots_rel.id')
                                        ->orOn('su_plots_owners_rel.pc_rel_id', '=', 'su_sales_contracts_plots_rel.pc_rel_id')
                                )
                        )
                )
                ->when(
                    ! $joinedTables->contains('su_owners'),
                    fn () => $query->leftJoin('su_owners', 'su_owners.id', '=', 'su_plots_owners_rel.owner_id')
                )
                ->when(
                    ! $joinedTables->contains('su_owners_reps'),
                    fn () => $query->leftJoin('su_owners_reps', 'su_owners_reps.id', '=', 'su_plots_owners_rel.rep_id')
                )
                ->when(
                    ! $joinedTables->contains('topic_layer_kvs_by_owner_name_label_items'),
                    fn () => $query->leftJoin('topic_layer_kvs_by_owner_name_label_items', 'topic_layer_kvs_by_owner_name_label_items.gid', '=', 'layer_kvs.gid')
                ),

            'su_contracts' =>  $query
                ->when(
                    ! $joinedTables->contains('su_contracts_plots_rel'),
                    fn () => $query->leftJoin('su_contracts_plots_rel', 'su_contracts_plots_rel.contract_id', '=', 'su_contracts.id')
                )
                ->when(
                    ! $joinedTables->contains('su_plots_owners_rel'),
                    fn () => $query->leftJoin('su_plots_owners_rel', 'su_plots_owners_rel.pc_rel_id', '=', 'su_contracts_plots_rel.id')
                )
                ->when(
                    ! $joinedTables->contains('su_owners'),
                    fn () => $query->leftJoin('su_owners', 'su_owners.id', '=', 'su_plots_owners_rel.owner_id')
                )
                ->when(
                    ! $joinedTables->contains('su_owners_reps'),
                    fn () => $query->leftJoin('su_owners_reps', 'su_owners_reps.id', '=', 'su_plots_owners_rel.rep_id')
                )
                ->when(
                    ! $joinedTables->contains('topic_layer_kvs_by_owner_name_label_items'),
                    fn () => $query->leftJoin('topic_layer_kvs_by_owner_name_label_items', 'topic_layer_kvs_by_owner_name_label_items.gid', '=', 'su_contracts_plots_rel.plot_id')
                ),

            'su_sales_contracts' => $query
                ->when(
                    ! $joinedTables->contains('su_sales_contracts_plots_rel'),
                    fn () => $query->leftJoin('su_sales_contracts_plots_rel', 'su_sales_contracts_plots_rel.sales_contract_id', '=', 'su_sales_contracts.id')
                )
                ->when(
                    ! $joinedTables->contains('su_plots_owners_rel'),
                    fn () => $query->leftJoin('su_plots_owners_rel', 'su_plots_owners_rel.pc_rel_id', '=', 'su_sales_contracts_plots_rel.pc_rel_id')
                )
                ->when(
                    ! $joinedTables->contains('su_owners'),
                    fn () => $query->leftJoin('su_owners', 'su_owners.id', '=', 'su_plots_owners_rel.owner_id')
                )
                ->when(
                    ! $joinedTables->contains('su_owners_reps'),
                    fn () => $query->leftJoin('su_owners_reps', 'su_owners_reps.id', '=', 'su_plots_owners_rel.rep_id')
                )
                ->when(
                    ! $joinedTables->contains('topic_layer_kvs_by_owner_name_label_items'),
                    fn () => $query->leftJoin('topic_layer_kvs_by_owner_name_label_items', 'topic_layer_kvs_by_owner_name_label_items.gid', '=', 'su_sales_contracts_plots_rel.plot_id')
                ),

            default => throw new Exception('Error joining required tables for filtering by plot properties')
        };
    }
}
