<?php

declare(strict_types=1);

namespace App\Pipes\Owners;

use Closure;
use Exception;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;

class IncompleteOwnershipDetailsFilterPipe extends BaseOwnerFilterPipe
{
    public function __construct(protected $filterValues) {}

    public function handle(Builder $query, Closure $next)
    {
        if (is_null($this->filterValues)) {
            return $next($query);
        }

        $className = self::class;
        if (! is_bool($this->filterValues)) {
            throw new Exception("Filter values must be a boolean for '{$className}'");
        }

        $this->joinRequiredTables($query);

        $query->when(
            $this->filterValues,
            fn ($subQ) => $subQ->where('contract_ownership_percent.percent', '<', 99.9)
        )
            ->when(
                ! $this->filterValues,
                fn ($subQ) => $subQ->where('contract_ownership_percent.percent', '>=', 99.9)
            );

        return $next($query);
    }

    public function joinRequiredTables(Builder $query): void
    {
        parent::joinRequiredTables($query);

        $contractOwnershipPercentQuery = DB::table('su_contracts', 'c')
            ->leftJoin('su_contracts_plots_rel AS scpr', 'c.id', '=', 'scpr.contract_id')
            ->leftJoin('su_plots_owners_rel AS spor', 'spor.pc_rel_id', '=', 'scpr.id')
            ->whereRaw('spor.is_heritor = FALSE OR spor.is_heritor ISNULL')
            ->select(
                'c.id AS contract_id',
                DB::raw('COALESCE(SUM(spor.percent), 0) AS percent')
            )
            ->groupBy('c.id');

        $query->joinSub(
            $contractOwnershipPercentQuery,
            DB::raw('contract_ownership_percent(contract_id, percent)'),
            fn ($join) => match($query->from) {
                'laryer_kvs' => $join
                    ->on('su_contracts_plots_rel.contract_id', '=', 'contract_ownership_percent.contract_id')
                    ->orOn('su_sales_contracts_plots_rel.contract_id', '=', 'contract_ownership_percent.contract_id'),
                'su_contracts' => $join
                    ->on('su_contracts_plots_rel.contract_id', '=', 'contract_ownership_percent.contract_id'),
                'su_sales_contracts' => $join
                    ->on('su_sales_contracts_plots_rel.contract_id', '=', 'contract_ownership_percent.contract_id'),
            }
        );


    }

    public function handleFilterItems(Builder $query, Closure $next)
    {
        $className = self::class;

        throw new Exception("The {$className} does not support filter items selection");
    }
}
