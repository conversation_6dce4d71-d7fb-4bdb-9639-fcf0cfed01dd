<?php

declare(strict_types=1);

namespace App\Pipes\Query;

use App\Types\Interfaces\Query\QueryFilterPipe;
use App\Traits\Database\QueryHelper;
use Closure;
use Illuminate\Database\Query\Builder;
use Illuminate\Pipeline\Pipeline;
use Illuminate\Support\Collection;
use Exception;

/**
 * Class QueryFilterGroupPipe.
 *
 * This class is responsible for building a group 'WHERE' clause and applying it to a query.
 * It allows you to specify the logical operation (AND/OR) to be used between the filters in the group.
 * Each filter must implement the QueryFilterPipe and apply a where condition to the passed value.
 */
class QueryFilterGroupPipe implements QueryFilterPipe
{
    use QueryHelper;

    /**
     * @var Collection<QueryFilterPipe>
     */
    private Collection $filters;

    /**
     * @var string the logical operation to be applied between the filters
     *
     * @see QueryFilterPipe
     */
    private string $logicalOperation;


    /**
     * @var bool indicates if the handle() method has been executed
     */
    private $isHandled = false;

    /**
     * @param string $logicalOperation the logical operation to be applied between the filters
     * @param null|array|Collection<QueryFilterPipe> $filters the filters to be applied
     *
     * @throws Exception
     */
    public function __construct(string $logicalOperation, $filters = null)
    {
        $this->validateLogicalOperation($logicalOperation);
        $this->logicalOperation = $logicalOperation;

        if (! $filters) {
            $this->filters = collect();

            return;
        }

        if (is_array($filters)) {
            $filters = collect($filters);
        }

        $filters->each(fn ($filter) => $this->validateFilter($filter));
        $this->filters = $filters;
    }

    /**
     * @param array|Collection<QueryFilterPipe>|QueryFilterPipe $value
     *
     * @return static
     */
    public function add($filter): self
    {
        if ($this->isHandled) {
            throw new Exception('Cannot add filter after the handle() method has been executed.');
        }

        if (is_array($filter)) {
            $filter = collect($filter);
        }

        if ($filter instanceof Collection) {
            $filter->each(fn ($f) => $this->validateFilter($f));
            $this->filters = $this->filters->merge($filter);

            return $this;
        }

        $this->validateFilter($filter);
        $this->filters->push($filter);

        return $this;
    }

    /**
     * @return Collection<QueryFilterPipe>
     */
    public function getFilters(): Collection
    {
        return $this->filters;
    }

    public function isEmpty(): bool
    {
        return $this->filters->isEmpty();
    }

    public function handle(Builder $query, Closure $next)
    {

        $this->isHandled = true;

        if ($this->filters->isEmpty()) {
            return $next($query);
        }

        foreach ($this->filters as $filter) {
            $query->where(
                function ($subQ) use ($query, $filter) {
                    app(Pipeline::class)
                        ->send($subQ)
                        ->through($filter)
                        ->thenReturn();

                    /*
                     * Since $this->filterGroup pipes are executed over $subQ which is a new instance of Builder,
                     * the main $query does not inherit the joins from $subQ.
                     * That's why we need tranfer all the required joins from $subQ to the main $query.
                     */
                    $this->transferJoins($subQ, $query);
                },
                null,
                null,
                $this->logicalOperation
            );
        }


        return $next($query);
    }

    /**
     * Throws an exception if the logical operation is not valid.
     *
     * @throws Exception
     */
    private function validateLogicalOperation(string $operation)
    {
        $validOperations = [
            QueryFilterPipe::OPERATION_AND,
            QueryFilterPipe::OPERATION_OR,
        ];

        if (! in_array($operation, $validOperations)) {
            throw new Exception('Invalid logical operation. Allowed values are: \'' . implode('\', \'', $validOperations) . '\'');
        }
    }

    /**
     * Throws an exception if the filter is not valid.
     *
     * @throws Exception
     */
    private function validateFilter($filter)
    {
        if (! ($filter instanceof QueryFilterPipe)) {
            throw new Exception('Invalid filter type. Expected: ' . QueryFilterPipe::class);
        }
    }
}
