<?php

declare(strict_types=1);

namespace App\Pipes\Contracts\Renew;

use App\Models\UserDb\Documents\Contracts\Contract;
use App\Traits\Contracts\UpdatesContractDates;
use Closure;
use Illuminate\Support\Facades\Log;

/**
 * Create Renewed Contract Pipe
 * 
 * Creates a new contract record based on the original contract.
 * Updates contract number, dates, and resets annex fields.
 */
class CreateRenewedContract
{
    use UpdatesContractDates;

    /**
     * Handle the single contract renewal context through the pipeline
     */
    public function handle(ContractRenewalContext $context, Closure $next): mixed
    {
        $renewedContract = $this->createRenewedContract($context->originalContract);
        $context->setRenewedContract($renewedContract);

        return $next($context);
    }

    /**
     * Create a renewed contract based on the original
     */
    private function createRenewedContract(Contract $originalContract): Contract
    {
        $renewedContract = $originalContract->replicate([
            'virtual_contract_type',
            'virtual_contract_status',
            'total_contract_area'
        ]);

        $this->updateContractDates($renewedContract, $originalContract);

        $renewedContract->source_contract_id = $originalContract->id;
        $renewedContract->save();

        return $renewedContract;
    }
}
