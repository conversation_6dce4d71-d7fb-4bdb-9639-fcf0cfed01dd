<?php

declare(strict_types=1);

namespace App\Pipes\Contracts\Renew;

use App\Factories\Contracts\ContractRenewalStrategyFactory;
use App\Models\UserDb\Documents\Contracts\Contract;
use App\Types\Enums\Documents\DocumentTypeEnum;
use Closure;
use Illuminate\Pipeline\Pipeline;
use Illuminate\Support\Facades\Log;

/**
 * Copy Related Main Contract Pipe
 *
 * For sublease contracts with farming contragents, this pipe:
 * 1. Checks if the original sublease has farming contragents
 * 2. Finds any main contract where from_sublease = original_sublease_id
 * 3. Creates a renewed copy of the main contract
 * 4. Updates the new main contract's from_sublease to point to the renewed sublease
 *
 * This ensures the complete contract hierarchy is maintained during sublease renewal.
 */
class CopyRelatedMainContract
{
    /**
     * Handle the single contract renewal context through the pipeline
     */
    public function handle(ContractRenewalContext $context, Closure $next): mixed
    {
        if (!$context->hasRenewedContract()) {
            throw new \Exception('Renewed contract must be created before copying related main contracts');
        }

        if ($context->renewedContract->is_sublease) {
            $this->copyRelatedMainContractIfNeeded($context);
        }

        return $next($context);
    }

    /**
     * Copy related main contract if the sublease has farming contragents
     */
    private function copyRelatedMainContractIfNeeded(ContractRenewalContext $context): void
    {
        $hasFarmingContragents = $context->originalContract->farmingContragents()->exists();

        if (!$hasFarmingContragents) {
            return;
        }

        $relatedMainContract = Contract::where('from_sublease', $context->originalContract->id)
            ->where('is_sublease', false)
            ->first();

        if (!$relatedMainContract) {
            return;
        }

        $renewedMainContract = $this->renewMainContract($relatedMainContract, $context->renewedContract->id, $context);
        $context->addRelatedMainContract($relatedMainContract, $renewedMainContract);
    }

    /**
     * Renew the main contract using the existing pipeline architecture
     */
    private function renewMainContract(Contract $originalMainContract, int $renewedSubleaseId, ContractRenewalContext $parentContext): Contract
    {
        $mainContractContext = new ContractRenewalContext(
            $originalMainContract,
            $parentContext->attributes,
            DocumentTypeEnum::Contracts
        );

        $strategy = ContractRenewalStrategyFactory::create(DocumentTypeEnum::Contracts);
        $pipeline = app(Pipeline::class);

        $pipeline
            ->send($mainContractContext)
            ->through($strategy->getPipelineConfiguration())
            ->thenReturn();

        $renewedMainContract = $mainContractContext->renewedContract;
        $renewedMainContract->from_sublease = $renewedSubleaseId;
        $renewedMainContract->save();

        return $renewedMainContract;
    }


}
