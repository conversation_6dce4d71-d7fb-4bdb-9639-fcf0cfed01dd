<?php

declare(strict_types=1);

namespace App\Pipes\Contracts\Renew;

use Closure;
use Illuminate\Support\Facades\DB;

/**
 * Copy Contract Files Pipe
 *
 * Copies contract files from original contract to renewed contract.
 * Files are always copied as this is now the default behavior.
 */
class CopyContractFiles
{
    /**
     * Handle the single contract renewal context through the pipeline
     */
    public function handle(ContractRenewalContext $context, Closure $next): mixed
    {
        if (!$context->hasRenewedContract()) {
            throw new \Exception('Renewed contract must be created before copying files');
        }

        $this->copyFilesForContract($context);

        return $next($context);
    }

    /**
     * Copy files for the contract
     */
    private function copyFilesForContract(ContractRenewalContext $context): void
    {
        $fileIds = $context->originalContract->files->pluck('file_id')->toArray();

        if (!empty($fileIds)) {
            $insertData = [];
            foreach ($fileIds as $fileId) {
                $insertData[] = [
                    'file_id' => $fileId,
                    'contract_id' => $context->renewedContract->id,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }

            DB::table('su_contracts_files_rel')->insert($insertData);
        }
    }
}
