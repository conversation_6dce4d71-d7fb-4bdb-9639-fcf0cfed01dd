<?php

declare(strict_types=1);

namespace App\Pipes\Contracts\Renew;

use Closure;

/**
 * Copy Plot Owners Pipe
 * 
 * Copies plot owner relationships from original plots to renewed plots.
 * Uses the plot mappings created by the previous CopyContractPlots pipe.
 */
class CopyPlotOwners
{
    /**
     * Handle the single contract renewal context through the pipeline
     */
    public function handle(ContractRenewalContext $context, Closure $next): mixed
    {
        $this->copyPlotOwnersForContract($context);

        return $next($context);
    }

    /**
     * Copy plot owners for the contract
     */
    private function copyPlotOwnersForContract(ContractRenewalContext $context): void
    {
        foreach ($context->originalContract->getPlotsCollection() as $originalPlot) {
            $renewedPlot = $context->getRenewedPlot($originalPlot->id);
            
            if (!$renewedPlot) {
                continue;
            }
            
            $this->copyOwnersForPlot($originalPlot, $renewedPlot);
        }
    }

    /**
     * Copy owners for a single plot
     */
    private function copyOwnersForPlot($originalPlot, $renewedPlot): void
    {
        foreach ($originalPlot->plotOwners as $originalOwner) {
            $renewedOwner = $originalOwner->replicate();
            $renewedOwner->pc_rel_id = $renewedPlot->id;
            $renewedOwner->save();
        }
    }
}
