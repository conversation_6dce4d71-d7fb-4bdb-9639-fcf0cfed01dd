<?php

declare(strict_types=1);

namespace App\Pipes\Contracts\Renew;

use Closure;

/**
 * Copy Contract Contragents Pipe
 * 
 * Copies contract contragent relationships from original contract to renewed contract.
 * Handles both regular contracts and sublease contracts.
 * 
 * Contragents are the parties/stakeholders involved in the contract (owners, representatives, etc.)
 */
class CopyContractContragents
{
    /**
     * Handle the single contract renewal context through the pipeline
     */
    public function handle(ContractRenewalContext $context, Closure $next): mixed
    {
        if (!$context->hasRenewedContract()) {
            throw new \Exception('Renewed contract must be created before copying contragents');
        }

        $this->copyContragentsForContract($context);

        return $next($context);
    }

    /**
     * Copy contragents for the contract (handles both regular contracts and subleases)
     */
    private function copyContragentsForContract(ContractRenewalContext $context): void
    {
        foreach ($context->originalContract->contragents as $originalContragent) {
            $renewedContragent = $originalContragent->replicate();
            $renewedContragent->contract_id = $context->renewedContract->id;
            $renewedContragent->save();
        }
    }
}
