<?php

declare(strict_types=1);

namespace App\Pipes\Contracts\Renew;

use App\Models\UserDb\Documents\Contracts\SubleaseContractPlot;
use Closure;
use Illuminate\Support\Facades\Log;

/**
 * Copy Sublease Contract Plots Pipe
 * 
 * Handles copying plot relationships for sublease contracts (is_sublease = true).
 * Creates records in both:
 * - su_subleases_plots_area (SubleaseContractPlotArea) - Direct plot areas
 * - su_subleases_plots_contracts_rel (SubleaseContractPlot) - Links to original contract plots
 */
class CopySubleaseContractPlots
{
    /**
     * Handle the single contract renewal context through the pipeline
     */
    public function handle(ContractRenewalContext $context, Closure $next): mixed
    {
        if (!$context->hasRenewedContract()) {
            throw new \Exception('Renewed contract must be created before copying plots');
        }

        if ($context->renewedContract->is_sublease) {
            $this->copyPlotsForContract($context);
        }

        return $next($context);
    }

    /**
     * Copy plots for sublease contracts
     * Creates records in both su_subleases_plots_area and su_subleases_plots_contracts_rel
     */
    private function copyPlotsForContract(ContractRenewalContext $context): void
    {
        foreach ($context->originalContract->getPlotsCollection() as $originalPlotArea) {
            $renewedPlotArea = $originalPlotArea->replicate();
            $renewedPlotArea->sublease_id = $context->renewedContract->id;
            $renewedPlotArea->save();

            $context->addPlotMapping($originalPlotArea->id, $renewedPlotArea);
        }

        $this->copySubleaseContractPlotRelationships($context);
    }

    /**
     * Copy sublease contract plot relationships from su_subleases_plots_contracts_rel
     * This replicates the existing relationships that link the sublease to contract plots
     */
    private function copySubleaseContractPlotRelationships(ContractRenewalContext $context): void
    {
        $originalSubleaseContractPlots = SubleaseContractPlot::where('sublease_id', $context->originalContract->id)->get();

        foreach ($originalSubleaseContractPlots as $originalSubleaseContractPlot) {
            $renewedSubleaseContractPlot = $originalSubleaseContractPlot->replicate();
            $renewedSubleaseContractPlot->sublease_id = $context->renewedContract->id;
            $renewedSubleaseContractPlot->save();
        }
    }
}
