<?php

declare(strict_types=1);

namespace App\Pipes\Contracts\Renew;

use App\Models\UserDb\Documents\Contracts\Contract;
use App\Types\Enums\Documents\DocumentTypeEnum;
use Illuminate\Support\Facades\Log;

/**
 * Single Contract Renewal Context
 * 
 * Data transfer object for processing a single contract through the renewal pipeline.
 * Each pipe receives this context, processes it, and passes it to the next pipe.
 */
class ContractRenewalContext
{
    public Contract $originalContract;
    public ?Contract $renewedContract = null;
    public array $plotMappings = [];
    public array $relatedMainContracts = [];
    public array $attributes;
    public DocumentTypeEnum $documentType;

    public function __construct(Contract $originalContract, array $attributes, DocumentTypeEnum $documentType)
    {
        $this->originalContract = $originalContract;
        $this->attributes = $attributes;
        $this->documentType = $documentType;
    }

    /**
     * Set the renewed contract
     */
    public function setRenewedContract(Contract $renewedContract): void
    {
        $this->renewedContract = $renewedContract;
    }

    /**
     * Add a plot mapping
     */
    public function addPlotMapping(int $originalPlotId, $renewedPlot): void
    {
        $this->plotMappings[$originalPlotId] = $renewedPlot;
    }

    /**
     * Get renewed plot for an original plot ID
     */
    public function getRenewedPlot(int $originalPlotId)
    {
        return $this->plotMappings[$originalPlotId] ?? null;
    }

    /**
     * Get plot mapping (renewed plot ID) for an original plot ID
     * Returns the renewed plot ID or null if not found
     */
    public function getPlotMapping(int $originalPlotId): ?int
    {
        $renewedPlot = $this->plotMappings[$originalPlotId] ?? null;

        // Handle both cases: renewed plot could be an object or an ID
        if ($renewedPlot === null) {
            return null;
        }

        // If it's an object, return its ID
        if (is_object($renewedPlot) && isset($renewedPlot->id)) {
            return $renewedPlot->id;
        }

        // If it's already an ID, return it
        if (is_numeric($renewedPlot)) {
            return (int) $renewedPlot;
        }

        return null;
    }

    /**
     * Check if contract has been created
     */
    public function hasRenewedContract(): bool
    {
        return $this->renewedContract !== null;
    }

    /**
     * Add a related main contract mapping
     */
    public function addRelatedMainContract(Contract $originalMainContract, Contract $renewedMainContract): void
    {
        $this->relatedMainContracts[$originalMainContract->id] = [
            'original' => $originalMainContract,
            'renewed' => $renewedMainContract,
        ];
    }

    /**
     * Get all related main contracts
     */
    public function getRelatedMainContracts(): array
    {
        return $this->relatedMainContracts;
    }

    /**
     * Check if any related main contracts were created
     */
    public function hasRelatedMainContracts(): bool
    {
        return !empty($this->relatedMainContracts);
    }
}
