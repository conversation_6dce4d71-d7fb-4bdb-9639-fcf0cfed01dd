<?php

declare(strict_types=1);

namespace App\Pipes\Contracts\Renew;

use Closure;

/**
 * Copy Regular Contract Plots Pipe
 * 
 * Handles copying plot relationships for regular contracts (is_sublease = false).
 * Copies records from su_contracts_plots_rel table.
 */
class CopyContractPlots
{
    /**
     * Handle the single contract renewal context through the pipeline
     */
    public function handle(ContractRenewalContext $context, Closure $next): mixed
    {
        if (!$context->hasRenewedContract()) {
            throw new \Exception('Renewed contract must be created before copying plots');
        }

        if (!$context->renewedContract->is_sublease) {
            $this->copyPlotsForContract($context);
        }

        return $next($context);
    }

    /**
     * Copy plots for regular contracts
     */
    private function copyPlotsForContract(ContractRenewalContext $context): void
    {
        foreach ($context->originalContract->getPlotsCollection() as $originalPlot) {
            $renewedPlot = $originalPlot->replicate(['virtual_non_arable_area']);
            $renewedPlot->contract_id = $context->renewedContract->id;
            $renewedPlot->save();
            $context->addPlotMapping($originalPlot->id, $renewedPlot);
        }
    }
}
