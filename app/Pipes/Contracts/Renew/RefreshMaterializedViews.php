<?php

declare(strict_types=1);

namespace App\Pipes\Contracts\Renew;

use App\Services\Database\MaterializedViewService;
use Closure;
use Illuminate\Support\Facades\Log;

/**
 * Refresh Materialized Views Pipe
 * 
 * Refreshes PostgreSQL materialized views after bulk contract operations
 * to ensure data consistency. This matches the legacy TechnoFarm7 behavior
 * where views are refreshed after multi-contract operations complete.
 * 
 * This pipe should run at the very end of bulk operations, after triggers are enabled.
 */
class RefreshMaterializedViews
{
    private MaterializedViewService $materializedViewService;

    public function __construct(MaterializedViewService $materializedViewService)
    {
        $this->materializedViewService = $materializedViewService;
    }

    /**
     * Handle the contract renewal context through the pipeline
     */
    public function handle(ContractRenewalContext $context, Closure $next): mixed
    {
        $this->materializedViewService->refreshRentaViews();

        return $next($context);
    }
}
