<?php

declare(strict_types=1);

namespace App\Pipes\Contracts\Renew;

use App\Exceptions\PlotNotRenewableException;
use App\Models\UserDb\Documents\Contracts\AbstractContractModel;
use App\Services\ContractPlotFilter\ContractPlotFilter;
use App\Types\Enums\Documents\DocumentTypeEnum;
use Closure;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;

/**
 * Validates that a contract's plots are all renewable using ContractPlotFilter
 *
 * This pipe uses the same logic as resolveContractIdsFromRequest but for a single contract,
 * leveraging the contract_ids filter pipe for consistency. It validates against the current
 * database state, automatically reflecting any previously renewed contracts in the batch.
 */
class ValidateContractRenewability
{

    public function handle(ContractRenewalContext $context, Closure $next): ContractRenewalContext
    {
        $contract = $context->originalContract;

        // Skip validation for automatically created contracts from subleases
        if (!$contract->from_sublease && !$this->validateContractRenewabilityById($contract, $context)) {
            throw new PlotNotRenewableException($contract->id, []);
        }

        return $next($context);
    }

    private function validateContractRenewabilityById(AbstractContractModel $contract, ContractRenewalContext $context): bool
    {
        $baseTable = $context->documentType->tableName();
        $contractPlotFilter = new ContractPlotFilter($baseTable);

        $result = $contractPlotFilter
            ->withParams($context->attributes['filter_params'])
            ->getQuery()
            ->select(["su_contracts.id"])
            ->selectRaw("contracts_for_renew.can_renew_contract as can_renew_contract")
            ->where('su_contracts.id', $contract->id)
            ->groupBy('su_contracts.id')
            ->groupBy('contracts_for_renew.can_renew_contract')
            ->havingRaw("contracts_for_renew.can_renew_contract = true")
            ->first();


        return $result !== null;
    }
}
