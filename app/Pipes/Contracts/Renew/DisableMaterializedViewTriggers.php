<?php

declare(strict_types=1);

namespace App\Pipes\Contracts\Renew;

use App\Services\Database\MaterializedViewService;
use Closure;
use Illuminate\Support\Facades\Log;

/**
 * Disable Materialized View Triggers Pipe
 * 
 * Disables PostgreSQL materialized view triggers before bulk contract operations
 * to improve performance. This matches the legacy TechnoFarm7 behavior where
 * triggers are disabled during multi-contract operations.
 * 
 * This pipe should run at the very beginning of bulk operations.
 */
class DisableMaterializedViewTriggers
{
    private MaterializedViewService $materializedViewService;

    public function __construct(MaterializedViewService $materializedViewService)
    {
        $this->materializedViewService = $materializedViewService;
    }

    /**
     * Handle the contract renewal context through the pipeline
     */
    public function handle(ContractRenewalContext $context, Closure $next): mixed
    {
        $this->materializedViewService->disableRentaMatViewTriggers();

        return $next($context);
    }
}
