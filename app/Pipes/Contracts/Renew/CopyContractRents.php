<?php

declare(strict_types=1);

namespace App\Pipes\Contracts\Renew;

use Closure;

/**
 * Copy Contract Rents Pipe
 * 
 * Copies contract rents from original contract to renewed contract.
 * Contract rents define the natural rent types and values for the contract.
 */
class CopyContractRents
{
    /**
     * Handle the single contract renewal context through the pipeline
     */
    public function handle(ContractRenewalContext $context, Closure $next): mixed
    {
        if (!$context->hasRenewedContract()) {
            throw new \Exception('Renewed contract must be created before copying rents');
        }

        $this->copyRentsForContract($context);

        return $next($context);
    }

    /**
     * Copy rents for the contract
     */
    private function copyRentsForContract(ContractRenewalContext $context): void
    {
        foreach ($context->originalContract->rents as $originalRent) {
            $renewedRent = $originalRent->replicate();
            $renewedRent->contract_id = $context->renewedContract->id;
            $renewedRent->save();
        }
    }
}
