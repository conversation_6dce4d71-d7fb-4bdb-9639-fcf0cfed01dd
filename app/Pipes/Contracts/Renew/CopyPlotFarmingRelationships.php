<?php

declare(strict_types=1);

namespace App\Pipes\Contracts\Renew;

use App\Models\UserDb\Documents\Contracts\PlotFarmingRelationship;
use Closure;
use Illuminate\Support\Facades\Log;

/**
 * Copy Plot Farming Relationships Pipe
 * 
 * Copies plot farming relationships from original contract to renewed contract.
 * This pipe handles the su_plots_farming_rel table data, which defines
 * farming relationships for contract plots including ownership percentages,
 * representatives, and proxy information.
 * 
 * Works for both regular contracts and sublease contracts.
 */
class CopyPlotFarmingRelationships
{
    /**
     * Handle the single contract renewal context through the pipeline
     */
    public function handle(ContractRenewalContext $context, Closure $next): mixed
    {
        if (!$context->hasRenewedContract()) {
            throw new \Exception('Renewed contract must be created before copying plot farming relationships');
        }

        $this->copyPlotFarmingRelationships($context);

        return $next($context);
    }

    /**
     * Copy plot farming relationships for the contract
     */
    private function copyPlotFarmingRelationships(ContractRenewalContext $context): void
    {
        $originalContractPlots = $context->originalContract->contractPlots()
            ->with('plotFarmingRelationships')
            ->get();

        if ($originalContractPlots->isEmpty()) {
            return;
        }

        $copiedRelationshipsCount = 0;

        foreach ($originalContractPlots as $originalContractPlot) {
            $renewedContractPlotId = $context->getPlotMapping($originalContractPlot->id);

            if (!$renewedContractPlotId) {
                continue;
            }

            // Copy all farming relationships for this contract plot
            foreach ($originalContractPlot->plotFarmingRelationships as $originalRelationship) {
                $renewedRelationship = $originalRelationship->replicate();
                $renewedRelationship->pc_rel_id = $renewedContractPlotId;
                $renewedRelationship->save();

                $copiedRelationshipsCount++;
            }
        }
    }
}
