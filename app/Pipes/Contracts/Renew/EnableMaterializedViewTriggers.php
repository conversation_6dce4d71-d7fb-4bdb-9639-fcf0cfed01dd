<?php

declare(strict_types=1);

namespace App\Pipes\Contracts\Renew;

use App\Services\Database\MaterializedViewService;
use Closure;
use Illuminate\Support\Facades\Log;

/**
 * Enable Materialized View Triggers Pipe
 * 
 * Re-enables PostgreSQL materialized view triggers after bulk contract operations
 * are completed. This matches the legacy TechnoFarm7 behavior where triggers
 * are re-enabled after multi-contract operations.
 * 
 * This pipe should run at the very end of bulk operations, before view refresh.
 */
class EnableMaterializedViewTriggers
{
    private MaterializedViewService $materializedViewService;

    public function __construct(MaterializedViewService $materializedViewService)
    {
        $this->materializedViewService = $materializedViewService;
    }

    /**
     * Handle the contract renewal context through the pipeline
     */
    public function handle(ContractRenewalContext $context, Closure $next): mixed
    {
        $this->materializedViewService->enableRentaMatViewTriggers();

        return $next($context);
    }
}
