<?php

declare(strict_types=1);

namespace App\Pipes\Contracts\Renew;

use Closure;

/**
 * Copy Contract Farming Contragents Pipe
 * 
 * Copies farming contragents (farming parties/stakeholders) from original contract to renewed contract.
 * Works for both regular contracts and sublease contracts.
 */
class CopyContractFarmingContragents
{
    /**
     * Handle the single contract renewal context through the pipeline
     */
    public function handle(ContractRenewalContext $context, Closure $next): mixed
    {
        if (!$context->hasRenewedContract()) {
            throw new \Exception('Renewed contract must be created before copying farming contragents');
        }

        $this->copyFarmingContragentsForContract($context);

        return $next($context);
    }

    /**
     * Copy farming contragents for the contract (works for both regular contracts and subleases)
     */
    private function copyFarmingContragentsForContract(ContractRenewalContext $context): void
    {
        foreach ($context->originalContract->farmingContragents as $originalFarmingContragent) {
            $renewedFarmingContragent = $originalFarmingContragent->replicate();
            $renewedFarmingContragent->contract_id = $context->renewedContract->id;
            $renewedFarmingContragent->save();
        }
    }
}
