<?php

declare(strict_types=1);

namespace App\Factories\Contracts;

use App\Strategies\Contracts\Renewal\ContractRenewalStrategyInterface;
use App\Strategies\Contracts\Renewal\ContractRenewalStrategy;
use App\Strategies\Contracts\Renewal\SubleaseContractRenewalStrategy;
use App\Types\Enums\Documents\DocumentTypeEnum;
use InvalidArgumentException;

/**
 * Contract Renewal Strategy Factory
 * 
 * Factory class responsible for creating the appropriate renewal strategy
 * based on the document type. This centralizes strategy creation and makes
 * it easy to add new contract types in the future.
 */
class ContractRenewalStrategyFactory
{
    /**
     * Available renewal strategies
     */
    private static array $strategies = [
        ContractRenewalStrategy::class,
        SubleaseContractRenewalStrategy::class,
    ];

    /**
     * Create the appropriate renewal strategy for the given document type
     */
    public static function create(DocumentTypeEnum $documentType): ContractRenewalStrategyInterface
    {
        foreach (self::$strategies as $strategyClass) {
            $strategy = new $strategyClass();
            
            if ($strategy->supports($documentType)) {
                return $strategy;
            }
        }

        throw new InvalidArgumentException(
            "No renewal strategy found for document type: {$documentType->value}"
        );
    }

    /**
     * Get all available strategies
     */
    public static function getAllStrategies(): array
    {
        return array_map(fn($class) => new $class(), self::$strategies);
    }

    /**
     * Register a new strategy (useful for extending the system)
     */
    public static function registerStrategy(string $strategyClass): void
    {
        if (!in_array($strategyClass, self::$strategies)) {
            self::$strategies[] = $strategyClass;
        }
    }

    /**
     * Get strategy by name
     */
    public static function getByName(string $name): ?ContractRenewalStrategyInterface
    {
        foreach (self::getAllStrategies() as $strategy) {
            if ($strategy->getName() === $name) {
                return $strategy;
            }
        }

        return null;
    }
}
