<?php

declare(strict_types=1);

namespace App\Models\Main;

use App\Models\UserDb\Users\UserFarmingPermission;
use App\Services\Auth\Traits\HasInMemoryTokens;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

final class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory;
    use HasInMemoryTokens;
    use Notifiable;


    public $timestamps = true;

    protected $table = 'su_users';


    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'username',
        'password',
        'address',
        'phone',
        'email',
        'comment',
        'is_superadmin',
        'hash',
        'can_create',
        'level',
        'group_id',
        'active',
        'server',
        'start_date',
        'due_date',
        'entry_flag',
        'entries_left',
        'date_flag',
        'map_type',
        'is_trial',
        'allowed_farmings',
        'track_username',
        'track_password',
        'creation_date',
        'last_login_date',
        'last_login_ip',
        'app_version',
        'app_critical_upd',
        'paid_support',
        'track_token',
        'login_token',
        'salesperson',
        'salesperson_id',
        'ekatte_count',
        'total_plot_area',
        'paid_support_start_date',
        'paid_support_due_date',
        'name',
        'database',
        'parent_id',
        'ao_id',
        'ao_db',
        'identity_number',
        'keycloak_uid',
        'gs_organization_id',
        'waiting_gs_integration',
        'is_from_ao_migration',
        'create_cms_contracts',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'start_date' => 'datetime',
        'due_date' => 'datetime',
        'creation_date' => 'datetime',
        'last_login_date' => 'datetime',
        'paid_support_start_date' => 'datetime',
        'paid_support_due_date' => 'datetime',
    ];

    public function getFarmingIdsByPermissions(array $permissions = [UserFarmingPermission::PERMISSION_READ,  UserFarmingPermission::PERMISSION_WRITE])
    {
        return UserFarmingPermission::where('user_id', $this->getAuthIdentifier())
            ->whereIn('permission', $permissions)
            ->pluck('farming_id');
    }
}
