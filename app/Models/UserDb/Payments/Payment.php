<?php

declare(strict_types=1);

namespace App\Models\UserDb\Payments;

use App\Models\UserDb\Documents\Contracts\Contract;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Payment extends Model
{
    protected $table = 'su_payments';

    protected $fillable = [
        'contract_id',
        'amount',
        'payment_date',
        'payment_type',
        'description',
        'status',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'payment_date' => 'date',
    ];

    /**
     * Get the contract that owns this payment
     */
    public function contract(): BelongsTo
    {
        return $this->belongsTo(Contract::class, 'contract_id');
    }
}
