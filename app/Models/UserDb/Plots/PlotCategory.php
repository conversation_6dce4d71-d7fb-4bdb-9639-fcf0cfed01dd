<?php

declare(strict_types=1);

namespace App\Models\UserDb\Plots;

use Illuminate\Database\Eloquent\Model;

/**
 * Plot Category Model
 *
 * Represents categories for plots that can be used for classification.
 * Each category has an id and a title for display.
 *
 * @property int $id
 * @property string $title Display title for the category
 */
class PlotCategory extends Model
{
    protected $table = 'su_plot_categories';
    
    public $timestamps = false;

    protected $fillable = [
        'title',
    ];

    protected $casts = [
        'id' => 'integer',
        'title' => 'string',
    ];
}
