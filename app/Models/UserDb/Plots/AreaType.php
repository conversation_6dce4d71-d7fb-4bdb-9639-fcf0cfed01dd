<?php

declare(strict_types=1);

namespace App\Models\UserDb\Plots;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

/**
 * Area Type Model
 *
 * Represents area types from the su_area_types materialized view.
 * Each area type has an id, title, category, and additional codes.
 *
 * @property int $id
 * @property string $title Display title for the area type
 * @property int $cat Category identifier
 * @property array $additional_codes Additional codes associated with this area type
 */
class AreaType extends Model
{
    protected $table = 'su_area_types';
    
    public $timestamps = false;

    protected $fillable = [
        'id',
        'title',
        'cat',
        'additional_codes',
    ];

    protected $casts = [
        'id' => 'integer',
        'title' => 'string',
        'cat' => 'integer',
        'additional_codes' => 'array',
    ];

    /**
     * Scope to search by both ID and title
     * Searches for exact ID match or partial title match (case-insensitive)
     */
    public function scopeSearch(Builder $query, ?string $search): Builder
    {
        if (empty($search)) {
            return $query;
        }

        return $query->where(function ($q) use ($search) {
            $q->orWhere('title', 'ILIKE', "%{$search}%");
            $q->orWhere('id', 'ILIKE', "%{$search}%");
        });
    }


}
