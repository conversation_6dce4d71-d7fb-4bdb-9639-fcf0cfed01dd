<?php

declare(strict_types=1);

namespace App\Models\UserDb\Files;

use App\Models\UserDb\Documents\Contracts\ContractFile;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class UserFile extends Model
{
    protected $table = 'su_user_files';

    protected $fillable = [
        'filename',
        'date',
        'user_id',
        'group_id'
    ];

    /**
     * Get the contract files for this file
     */
    public function contractFiles(): HasMany
    {
        return $this->hasMany(ContractFile::class, 'file_id');
    }
}
