<?php

declare(strict_types=1);

namespace App\Models\UserDb\Rent;

use Illuminate\Database\Eloquent\Model;

/**
 * Rent Type Option Model
 *
 * Represents options for rent types that can be used for filtering or categorization.
 * Each option has a title for display and a value for internal use.
 *
 * @property int $id
 * @property string $title Display title for the option
 * @property string $value Internal value for the option
 */
class RentTypeOption extends Model
{
    protected $table = 'su_renta_types_options';
    
    public $timestamps = false;

    protected $fillable = [
        'title',
        'value',
    ];

    protected $casts = [
        'id' => 'integer',
        'title' => 'string',
        'value' => 'string',
    ];
}
