<?php

declare(strict_types=1);

namespace App\Models\UserDb\Rent;

use App\Models\UserDb\Documents\Contracts\ContractRent;
use App\Models\UserDb\Units\Unit;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Rent-in-Kind Model
 *
 * Represents types of rent that can be paid in natural products (crops, livestock, etc.)
 * instead of monetary payments. Each rent type is associated with a unit of measurement.
 *
 * @property int $id
 * @property string|null $name Name of the rent type (e.g., "Wheat", "Corn", "Milk")
 * @property int $unit Foreign key to su_units_of_measure
 * @property float|null $unit_value Value per unit
 * @property float|null $avg_yield Average yield for this rent type
 * @property float|null $treatment_price Treatment/processing price
 * @property string|null $comment Additional comments
 * @property bool $auto_crops_divide Whether to automatically divide crops
 *
 * @property-read Unit $unitOfMeasure Unit of measurement relationship
 * @property-read \Illuminate\Database\Eloquent\Collection<ContractRent> $contractRents
 */
class RentInKind extends Model
{
    protected $table = 'su_renta_types';
    
    public $timestamps = false;

    protected $fillable = [
        'name',
        'unit', // should be renamed to unit_id cause conficts with the unit() method
        'unit_value',
        'avg_yield',
        'treatment_price',
        'comment',
        'auto_crops_divide',
    ];

    protected $casts = [
        'unit_value' => 'float',
        'avg_yield' => 'decimal:4',
        'treatment_price' => 'decimal:2',
        'auto_crops_divide' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function unitOfMeasure(): BelongsTo
    {
        return $this->belongsTo(Unit::class, 'unit');
    }

    public function contractRents(): HasMany
    {
        return $this->hasMany(ContractRent::class, 'renta_id');
    }
}
