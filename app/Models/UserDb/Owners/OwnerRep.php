<?php

declare(strict_types=1);

namespace App\Models\UserDb\Owners;

use Illuminate\Database\Eloquent\Model;
use App\Models\UserDb\Documents\Contracts\ContractContragent;
use App\Models\UserDb\Documents\Contracts\ContractFarmingContragent;
use App\Models\UserDb\Documents\Contracts\PlotOwner;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Owner Representative Model
 *
 * Represents legal representatives for owners, containing personal information,
 * banking details, and contact information.
 *
 * @property int $id
 * @property string $rep_name
 * @property string $rep_surname
 * @property string $rep_lastname
 * @property string $rep_egn
 * @property string|null $rep_lk
 * @property string|null $rep_lk_izdavane
 * @property string|null $rep_address
 * @property int|null $owner_id
 * @property string|null $rent_place
 * @property string|null $iban
 * @property string|null $rep_phone
 * @property string|null $bic
 * @property string|null $bank_name
 * @property array|null $post_payment_fields
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 *
 * @property-read Owner|null $owner
 * @property-read string $full_name
 * @property-read string $full_name_with_egn
 */
class OwnerRep extends Model
{
    protected $table = 'su_owners_reps';

    protected $fillable = [
        'rep_name',
        'rep_surname',
        'rep_lastname',
        'rep_egn',
        'rep_lk',
        'rep_lk_izdavane',
        'rep_address',
        'owner_id',
        'rent_place',
        'iban',
        'rep_phone',
        'bic',
        'bank_name',
        'post_payment_fields',
    ];

    protected $casts = [
        'id' => 'integer',
        'owner_id' => 'integer',
        'post_payment_fields' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the owner that this representative belongs to (if any)
     */
    public function owner(): BelongsTo
    {
        return $this->belongsTo(Owner::class, 'owner_id');
    }

    /**
     * Get the contract contragents for this representative
     */
    public function contractContragents(): HasMany
    {
        return $this->hasMany(ContractContragent::class, 'rep_id');
    }

    /**
     * Get the farming contragents for this representative
     */
    public function farmingContragents(): HasMany
    {
        return $this->hasMany(ContractFarmingContragent::class, 'rep_id');
    }

    /**
     * Get the plot owners for this representative
     */
    public function plotOwners(): HasMany
    {
        return $this->hasMany(PlotOwner::class, 'rep_id');
    }

    /**
     * Check if the representative has banking information.
     */
    public function hasBankingInfo(): bool
    {
        return !empty($this->iban) || !empty($this->bic) || !empty($this->bank_name);
    }

    /**
     * Check if the representative has complete banking information.
     */
    public function hasCompleteBankingInfo(): bool
    {
        return !empty($this->iban) && !empty($this->bic) && !empty($this->bank_name);
    }

    /**
     * Scope to filter by owner ID.
     */
    public function scopeForOwner($query, int $ownerId)
    {
        return $query->where('owner_id', $ownerId);
    }

    /**
     * Scope to filter by EGN.
     */
    public function scopeByEgn($query, string $egn)
    {
        return $query->where('rep_egn', $egn);
    }

    /**
     * Scope to search by name (any part of the full name).
     */
    public function scopeSearchByName($query, string $searchTerm)
    {
        return $query->where(function ($q) use ($searchTerm) {
            $q->where('rep_name', 'ILIKE', "%{$searchTerm}%")
              ->orWhere('rep_surname', 'ILIKE', "%{$searchTerm}%")
              ->orWhere('rep_lastname', 'ILIKE', "%{$searchTerm}%");
        });
    }

    /**
     * Scope to filter representatives with banking information.
     */
    public function scopeWithBankingInfo($query)
    {
        return $query->where(function ($q) {
            $q->whereNotNull('iban')
              ->orWhereNotNull('bic')
              ->orWhereNotNull('bank_name');
        });
    }
}
