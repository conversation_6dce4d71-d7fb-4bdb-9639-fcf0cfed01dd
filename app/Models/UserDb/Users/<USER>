<?php

declare(strict_types=1);

namespace App\Models\UserDb\Users;

use Illuminate\Database\Eloquent\Model;

class UserFarmingPermission extends Model
{
    public const PERMISSION_READ = 'read';
    public const PERMISSION_WRITE = 'write';

    // This model represents the user_farming_permissions view
    protected $table = 'user_farming_permissions';
    protected $primaryKey = 'user_id';
    protected $fillable = [];
}
