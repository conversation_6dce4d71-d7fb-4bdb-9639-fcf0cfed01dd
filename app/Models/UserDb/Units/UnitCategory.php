<?php

declare(strict_types=1);

namespace App\Models\UserDb\Units;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Unit Category Model
 * 
 * Represents categories for units of measurement such as:
 * - Weight (kg, g, t)
 * - Volume (l, ml, m³)
 * - Area (m², ha, km²)
 * - Length (m, cm, km)
 * - Time (s, min, h)
 * 
 * @property int $id
 * @property string $name Category name (e.g., "Weight", "Volume")
 * @property string|null $icon SVG icon for the category
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * 
 * @property-read \Illuminate\Database\Eloquent\Collection<Unit> $units
 */
class UnitCategory extends Model
{
    protected $table = 'su_units_of_measure_categories';

    protected $fillable = [
        'name',
        'icon',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function units(): HasMany
    {
        return $this->hasMany(Unit::class, 'category_id');
    }

    public function baseUnits(): HasMany
    {
        return $this->hasMany(Unit::class, 'category_id')
            ->whereNull('base_unit_id');
    }

    public function derivedUnits(): HasMany
    {
        return $this->hasMany(Unit::class, 'category_id')
            ->whereNotNull('base_unit_id');
    }

    public function compoundUnits(): HasMany
    {
        return $this->hasMany(Unit::class, 'category_id')
            ->whereNotNull('numerator_unit_id')
            ->whereNotNull('denominator_unit_id');
    }

    public function getIconAttribute($value): ?string
    {
        return $value;
    }

    public function hasUnits(): bool
    {
        return $this->units()->exists();
    }

    public function getUnitsCountAttribute(): int
    {
        return $this->units()->count();
    }
}
