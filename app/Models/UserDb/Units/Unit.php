<?php

declare(strict_types=1);

namespace App\Models\UserDb\Units;

use App\Models\UserDb\Rent\RentInKind;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Unit of Measure Model
 *
 * Represents units of measurement with support for:
 * - Categories (Weight, Volume, Area, etc.)
 * - Unit conversions via base_unit_id and coefficient
 * - Compound units (numerator/denominator relationships)
 * - Self-referencing relationships for unit hierarchies
 *
 * @property int $id
 * @property string $full_name Full name of the unit (e.g., "Kilogram")
 * @property string $short_name Short abbreviation (e.g., "kg")
 * @property int $category_id Foreign key to unit category
 * @property int|null $base_unit_id Base unit for conversions
 * @property float $coefficient Conversion coefficient to base unit
 * @property int|null $numerator_unit_id For compound units (e.g., kg in kg/m²)
 * @property int|null $denominator_unit_id For compound units (e.g., m² in kg/m²)
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 *
 * @property-read UnitCategory $category
 * @property-read Unit|null $baseUnit
 * @property-read Unit|null $numeratorUnit
 * @property-read Unit|null $denominatorUnit
 * @property-read \Illuminate\Database\Eloquent\Collection<Unit> $derivedUnits
 * @property-read \Illuminate\Database\Eloquent\Collection<RentInKind> $rentInKindTypes
 */
class Unit extends Model
{
    protected $table = 'su_units_of_measure';

    protected $fillable = [
        'full_name',
        'short_name',
        'category_id',
        'base_unit_id',
        'coefficient',
        'numerator_unit_id',
        'denominator_unit_id',
    ];

    protected $casts = [
        'coefficient' => 'decimal:6',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function category(): BelongsTo
    {
        return $this->belongsTo(UnitCategory::class, 'category_id');
    }

    public function baseUnit(): BelongsTo
    {
        return $this->belongsTo(Unit::class, 'base_unit_id');
    }

    public function derivedUnits(): HasMany
    {
        return $this->hasMany(Unit::class, 'base_unit_id');
    }

    public function numeratorUnit(): BelongsTo
    {
        return $this->belongsTo(Unit::class, 'numerator_unit_id');
    }

    public function denominatorUnit(): BelongsTo
    {
        return $this->belongsTo(Unit::class, 'denominator_unit_id');
    }

    public function asNumeratorIn(): HasMany
    {
        return $this->hasMany(Unit::class, 'numerator_unit_id');
    }

    public function asDenominatorIn(): HasMany
    {
        return $this->hasMany(Unit::class, 'denominator_unit_id');
    }

    public function rentInKindTypes(): HasMany
    {
        return $this->hasMany(RentInKind::class, 'unit');
    }

    /**
     * Check if this is a base unit (has no base_unit_id)
     */
    public function isBaseUnit(): bool
    {
        return is_null($this->base_unit_id);
    }

    /**
     * Check if this is a compound unit (has numerator and denominator)
     */
    public function isCompoundUnit(): bool
    {
        return !is_null($this->numerator_unit_id) && !is_null($this->denominator_unit_id);
    }

    /**
     * Get the display name with proper formatting
     * For compound units, returns "numerator/denominator"
     * For simple units, returns short_name
     */
    public function getDisplayNameAttribute(): string
    {
        if ($this->isCompoundUnit()) {
            $numerator = $this->numeratorUnit?->short_name;
            $denominator = $this->denominatorUnit?->short_name;
            return "{$numerator}/{$denominator}";
        }

        return $this->short_name;
    }

    /**
     * Convert a value from this unit to the base unit
     */
    public function convertToBaseUnit(float $value): float
    {
        if ($this->isBaseUnit()) {
            return $value;
        }

        return $value * $this->coefficient;
    }

    /**
     * Convert a value from the base unit to this unit
     */
    public function convertFromBaseUnit(float $value): float
    {
        if ($this->isBaseUnit()) {
            return $value;
        }

        return $value / $this->coefficient;
    }
}
