<?php

declare(strict_types=1);

namespace App\Models\UserDb\Documents\Contracts;

use App\Models\UserDb\Documents\Contracts\PlotFarmingRelationship;
use App\Models\UserDb\Layers\LayerKVS;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Services\ContractPlotFilter\ContractPlotFilter;
use App\Traits\Common\FarmingYearsTrait;
use App\Types\Enums\Documents\DocumentTypeEnum;

class ContractPlot extends Model
{
    use FarmingYearsTrait;
    protected $table = 'su_contracts_plots_rel';
    protected $primaryKey = 'id';

    protected $fillable = [
        'contract_id',
        'plot_id',
        'contract_area',
        'price_per_acre',
        'price_sum',
        'annex_action',
        'contract_end_date',
        'fraction',
        'percent',
        'area_for_rent',
        'rent_per_plot',
        'comment',
        'kvs_allowable_area',
        'ao_id',
        'ao_db',
        'ao_type',
    ];

    protected $casts = [
        'contract_area' => 'decimal:4',
        'price_per_acre' => 'decimal:2',
        'price_sum' => 'decimal:2',
        'contract_end_date' => 'date',
        'fraction' => 'decimal:4',
        'percent' => 'decimal:2',
        'area_for_rent' => 'decimal:4',
        'rent_per_plot' => 'decimal:2',
        'kvs_allowable_area' => 'decimal:4',
    ];

    /**
     * Get the contract that owns this plot relationship
     */
    public function contract(): BelongsTo
    {
        return $this->belongsTo(Contract::class, 'contract_id');
    }

    /**
     * Get the cadastral plot that this contract plot references
     */
    public function plot(): BelongsTo
    {
        return $this->belongsTo(LayerKVS::class, 'plot_id', 'gid');
    }

    /**
     * Get the plot owners for this contract plot
     */
    public function plotOwners(): HasMany
    {
        return $this->hasMany(PlotOwner::class, 'pc_rel_id');
    }

    /**
     * Get the plot farming relationships for this contract plot
     */
    public function plotFarmingRelationships(): HasMany
    {
        return $this->hasMany(PlotFarmingRelationship::class, 'pc_rel_id');
    }

    /**
     * Scope for added plots (not removed by annex)
     */
    public function scopeAdded($query)
    {
        return $query->where('annex_action', 'added');      
    }

    /**
     * Scope for removed plots (removed by annex)
     */
    public function scopeRemoved($query)
    {
        return $query->where('annex_action', 'removed');
    }
}
