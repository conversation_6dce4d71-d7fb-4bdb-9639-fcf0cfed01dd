<?php

declare(strict_types=1);

namespace App\Models\UserDb\Documents\Contracts;

use App\Models\UserDb\Owners\OwnerRep;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Plot Farming Relationship Model
 * 
 * Represents farming relationships for contract plots.
 * Maps to su_plots_farming_rel table.
 * 
 * This model defines the relationship between contract plots and farming entities,
 * including ownership percentages, representatives, and proxy information.
 */
class PlotFarmingRelationship extends Model
{
    protected $table = 'su_plots_farming_rel';

    public $timestamps = false;

    protected $fillable = [
        'pc_rel_id',
        'farming_id',
        'percent',
        'rep_id',
        'proxy_num',
        'proxy_date',
        'numerator',
        'denominator',
    ];

    protected $casts = [
        'farming_id' => 'integer',
        'percent' => 'decimal:2',
        'proxy_date' => 'datetime',
        'numerator' => 'integer',
        'denominator' => 'integer',
    ];

    /**
     * Get the contract plot that owns this farming relationship
     */
    public function contractPlot(): BelongsTo
    {
        return $this->belongsTo(ContractPlot::class, 'pc_rel_id');
    }

    /**
     * Get the owner representative for this farming relationship
     */
    public function ownerRep(): BelongsTo
    {
        return $this->belongsTo(OwnerRep::class, 'rep_id');
    }

    /**
     * Get the farming ID (references farming entity in Main database)
     * Note: farming_id is an integer reference to a farming entity in a different database
     */
    public function getFarmingIdAttribute(): int
    {
        return $this->attributes['farming_id'];
    }

    /**
     * Get the percentage as a decimal value
     */
    public function getPercentageDecimal(): float
    {
        return (float) $this->percent / 100;
    }

    /**
     * Get the fraction as a decimal value
     */
    public function getFractionDecimal(): ?float
    {
        if ($this->denominator && $this->denominator > 0) {
            return (float) $this->numerator / $this->denominator;
        }
        
        return null;
    }

    /**
     * Get the fraction as a formatted string
     */
    public function getFractionString(): ?string
    {
        if ($this->numerator && $this->denominator) {
            return "{$this->numerator}/{$this->denominator}";
        }
        
        return null;
    }

    /**
     * Check if this relationship has proxy information
     */
    public function hasProxyInfo(): bool
    {
        return !empty($this->proxy_num) || !empty($this->proxy_date);
    }
}
