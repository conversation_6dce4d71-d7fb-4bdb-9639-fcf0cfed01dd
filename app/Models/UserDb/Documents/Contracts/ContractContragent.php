<?php

declare(strict_types=1);

namespace App\Models\UserDb\Documents\Contracts;

use App\Models\UserDb\Documents\Contracts\Contract;
use App\Models\UserDb\Owners\Owner;
use App\Models\UserDb\Owners\OwnerRep;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ContractContragent extends Model
{
    protected $table = 'su_contracts_contragents';

    protected $fillable = [
        'contract_id',
        'owner_id',
        'rep_id',
        'proxy_num',
        'proxy_date',
        'notary_name',
        'notary_number',
        'notary_address',
    ];

    protected $casts = [
        'proxy_date' => 'datetime',
        'notary_number' => 'integer',
    ];

    /**
     * Get the contract that owns this contragent
     */
    public function contract(): BelongsTo
    {
        return $this->belongsTo(Contract::class, 'contract_id');
    }

    /**
     * Get the owner for this contragent
     */
    public function owner(): BelongsTo
    {
        return $this->belongsTo(Owner::class, 'owner_id');
    }

    /**
     * Get the owner representative for this contragent
     */
    public function ownerRep(): BelongsTo
    {
        return $this->belongsTo(OwnerRep::class, 'rep_id');
    }
}
