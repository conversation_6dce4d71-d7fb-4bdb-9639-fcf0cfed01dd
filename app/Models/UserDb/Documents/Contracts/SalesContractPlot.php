<?php

declare(strict_types=1);

namespace App\Models\UserDb\Documents\Contracts;

use App\Models\UserDb\Documents\Contracts\Contract;
use App\Models\UserDb\Documents\Contracts\SalesContract;
// Plot data is accessed through plot_id directly
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SalesContractPlot extends Model
{
    protected $table = 'su_sales_contracts_plots_rel';

    protected $fillable = [
        'sales_contract_id',
        'contract_id',
        'sublease_contract_id',
        'plot_id',
        'contract_area',
        'contract_area_for_sale',
        'price_per_acre',
        'price_sum',
        'pc_rel_id',
    ];

    protected $casts = [
        'contract_area' => 'decimal:4',
        'contract_area_for_sale' => 'decimal:4',
        'price_per_acre' => 'decimal:2',
        'price_sum' => 'decimal:2',
    ];

    /**
     * Get the sales contract that owns this plot relationship
     */
    public function salesContract(): BelongsTo
    {
        return $this->belongsTo(SalesContract::class, 'sales_contract_id');
    }

    /**
     * Get the original contract
     */
    public function contract(): BelongsTo
    {
        return $this->belongsTo(Contract::class, 'contract_id');
    }

    /**
     * Get the sublease contract
     */
    public function subleaseContract(): BelongsTo
    {
        return $this->belongsTo(Contract::class, 'sublease_contract_id');
    }

    // Plot data is accessed via plot_id directly

    /**
     * Get the plot contract relationship
     */
    public function plotContractRelation(): BelongsTo
    {
        return $this->belongsTo(ContractPlot::class, 'pc_rel_id');
    }
}
