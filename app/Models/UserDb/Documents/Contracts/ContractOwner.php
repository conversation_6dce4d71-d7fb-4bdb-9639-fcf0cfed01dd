<?php

declare(strict_types=1);

namespace App\Models\UserDb\Documents\Contracts;

use App\Models\UserDb\Documents\Contracts\Contract;
use App\Models\UserDb\Owners\Owner;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ContractOwner extends Model
{
    protected $table = 'su_contract_owner_rel';

    protected $fillable = [
        'owner_id',
        'contract_id',
        'is_personal_use_proportionally_distributed',
    ];

    protected $casts = [
        'is_personal_use_proportionally_distributed' => 'boolean',
    ];

    /**
     * Get the contract that owns this relationship
     */
    public function contract(): BelongsTo
    {
        return $this->belongsTo(Contract::class, 'contract_id');
    }

    /**
     * Get the owner in this relationship
     */
    public function owner(): BelongsTo
    {
        return $this->belongsTo(Owner::class, 'owner_id');
    }
}
