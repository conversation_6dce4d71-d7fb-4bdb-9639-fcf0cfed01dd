<?php

declare(strict_types=1);

namespace App\Models\UserDb\Documents\Contracts;

use App\Models\UserDb\Layers\LayerKVS;

class MortgageContract extends AbstractContractModel
{
    protected $table = 'su_hypothecs';

    protected $primaryKey = 'id';

    // Define the fillable fields
    protected $fillable = [
        'num',
        '"date"',
        'start_date',
        'due_date',
        'comment',
        'creditor_id',
        'farming_id',
        'na_num',
        'tom',
        'delo',
        'court',
        'is_active',
        'deactivate_num',
        'deactivate_date',
    ];

    // Define the attributes that should be cast to native types
    protected $casts = [
        'date' => 'datetime',
        'start_date' => 'datetime',
        'due_date' => 'datetime',
        'deactivate_date' => 'datetime',
    ];

    public function plots()
    {
        return $this->hasManyThrough(LayerKVS::class, MortgageContractPlot::class, 'hypothec_id', 'gid', 'id', 'plot_id');
    }
}
