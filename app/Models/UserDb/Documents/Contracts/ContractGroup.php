<?php

declare(strict_types=1);

namespace App\Models\UserDb\Documents\Contracts;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ContractGroup extends Model
{
    protected $table = 'su_contract_group';

    public $timestamps = false;

    protected $fillable = [
        'name',
    ];

    /**
     * Get the contracts that belong to this group
     */
    public function contracts(): HasMany
    {
        return $this->hasMany(Contract::class, 'group');
    }
}
