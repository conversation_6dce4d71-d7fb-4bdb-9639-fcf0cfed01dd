<?php

declare(strict_types=1);

namespace App\Models\UserDb\Documents\Contracts;

use App\Models\UserDb\Documents\Contracts\Contract;
use App\Models\UserDb\Owners\OwnerRep;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Contract Farming Contragent Model
 *
 * Represents farming parties/stakeholders involved in contracts.
 * Maps to su_contracts_farming_contragents table.
 *
 * Note: farming_id is an integer reference to a farming entity in the Main database,
 * not a local Eloquent relationship.
 */
class ContractFarmingContragent extends Model
{
    protected $table = 'su_contracts_farming_contragents';

    protected $fillable = [
        'contract_id',
        'farming_id',
        'rep_id',
        'proxy_num',
        'proxy_date',
        'notary_name',
        'notary_number',
        'notary_address',
    ];

    protected $casts = [
        'farming_id' => 'integer',
        'proxy_date' => 'datetime',
        'notary_number' => 'integer',
    ];

    /**
     * Get the contract that owns this farming contragent
     */
    public function contract(): BelongsTo
    {
        return $this->belongsTo(Contract::class, 'contract_id');
    }

    /**
     * Get the owner representative for this contragent
     */
    public function ownerRep(): BelongsTo
    {
        return $this->belongsTo(OwnerRep::class, 'rep_id');
    }

    /**
     * Get the farming ID (references farming entity in Main database)
     * Note: farming_id is an integer reference to a farming entity in a different database
     */
    public function getFarmingIdAttribute(): int
    {
        return $this->attributes['farming_id'];
    }
}
