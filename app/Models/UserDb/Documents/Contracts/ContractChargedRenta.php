<?php

declare(strict_types=1);

namespace App\Models\UserDb\Documents\Contracts;

use App\Models\UserDb\Documents\Contracts\Contract;
use App\Models\UserDb\Owners\Owner;
// Plot data is accessed through plot_id directly
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ContractChargedRenta extends Model
{
    protected $table = 'su_charged_renta';

    protected $fillable = [
        'contract_id',
        'year',
        'renta',
        'renta_nat',
        'plot_id',
        'nat_is_converted',
        'nat_unit_price',
        'owner_id',
    ];

    protected $casts = [
        'year' => 'integer',
        'renta' => 'decimal:2',
        'renta_nat' => 'decimal:2',
        'nat_is_converted' => 'boolean',
        'nat_unit_price' => 'decimal:2',
    ];

    /**
     * Get the contract that owns this charged renta
     */
    public function contract(): BelongsTo
    {
        return $this->belongsTo(Contract::class, 'contract_id');
    }

    // Plot data is accessed via plot_id directly

    /**
     * Get the owner for this charged renta
     */
    public function owner(): BelongsTo
    {
        return $this->belongsTo(Owner::class, 'owner_id');
    }

    /**
     * Scope for a specific year
     */
    public function scopeForYear($query, int $year)
    {
        return $query->where('year', $year);
    }
}
