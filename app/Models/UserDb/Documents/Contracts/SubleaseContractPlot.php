<?php

declare(strict_types=1);

namespace App\Models\UserDb\Documents\Contracts;

use App\Models\UserDb\Documents\Contracts\Contract;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SubleaseContractPlot extends Model
{
    protected $table = 'su_subleases_plots_contracts_rel';

    public $timestamps = false;

    protected $fillable = [
        'sublease_id',
        'pc_rel_id',
    ];

    /**
     * Get the sublease contract that owns this relationship
     */
    public function sublease(): BelongsTo
    {
        return $this->belongsTo(Contract::class, 'sublease_id');
    }

    /**
     * Get the plot contract relationship
     */
    public function plotContractRelation(): BelongsTo
    {
        return $this->belongsTo(ContractPlot::class, 'pc_rel_id');
    }
}
