<?php

declare(strict_types=1);

namespace App\Models\UserDb\Documents\Contracts;

use App\Models\UserDb\Documents\Contracts\Contract;
use App\Models\UserDb\Files\UserFile;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ContractFile extends Model
{
    protected $table = 'su_contracts_files_rel';

    // This is a pivot table without an auto-incrementing id
    public $incrementing = false;
    protected $primaryKey = null;

    protected $fillable = [
        'file_id',
        'contract_id',
    ];

    /**
     * Get the contract that owns this file relationship
     */
    public function contract(): BelongsTo
    {
        return $this->belongsTo(Contract::class, 'contract_id');
    }

    /**
     * Get the file in this relationship
     */
    public function file(): BelongsTo
    {
        return $this->belongsTo(UserFile::class, 'file_id');
    }
}
