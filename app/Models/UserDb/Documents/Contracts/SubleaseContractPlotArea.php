<?php

declare(strict_types=1);

namespace App\Models\UserDb\Documents\Contracts;

use App\Models\UserDb\Documents\Contracts\Contract;
use App\Models\UserDb\Documents\Contracts\PlotOwner;
use App\Models\UserDb\Layers\LayerKVS;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class SubleaseContractPlotArea extends Model
{
    protected $table = 'su_subleases_plots_area';

    public $timestamps = false;

    protected $fillable = [
        'sublease_id',
        'plot_id',
        'contract_area',
        'comment',
        'rent_area',
    ];

    protected $casts = [
        'contract_area' => 'decimal:4',
        'rent_area' => 'decimal:4',
    ];

    /**
     * Get the sublease contract that owns this plot area
     */
    public function sublease(): BelongsTo
    {
        return $this->belongsTo(Contract::class, 'sublease_id');
    }

    /**
     * Get the plot (LayerKVS) that this relationship references
     */
    public function plot(): BelongsTo
    {
        return $this->belongsTo(LayerKVS::class, 'plot_id', 'gid');
    }

    /**
     * Get the plot owners for this sublease plot area
     */
    public function plotOwners(): HasMany
    {
        return $this->hasMany(PlotOwner::class, 'pc_rel_id');
    }
}
