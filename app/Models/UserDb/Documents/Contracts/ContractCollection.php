<?php

declare(strict_types=1);

namespace App\Models\UserDb\Documents\Contracts;

use App\Models\UserDb\Documents\Contracts\Contract;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ContractCollection extends Model
{
    protected $table = 'su_collections';

    protected $fillable = [
        'contract_id',
        'date',
        'amount',
        'recieved_from',
        'user_name',
        'bank_payment',
        'payment_order',
        'farming_year',
        'payment_data',
        'type',
        'status',
        'cancellation_info',
    ];

    protected $casts = [
        'date' => 'date',
        'amount' => 'decimal:2',
        'farming_year' => 'integer',
        'payment_data' => 'array',
        'cancellation_info' => 'array',
    ];

    /**
     * Get the contract that owns this collection
     */
    public function contract(): BelongsTo
    {
        return $this->belongsTo(Contract::class, 'contract_id');
    }

    /**
     * Scope for a specific farming year
     */
    public function scopeForFarmingYear($query, int $year)
    {
        return $query->where('farming_year', $year);
    }

    /**
     * Scope for a specific status
     */
    public function scopeWithStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope for a specific type
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('type', $type);
    }
}
