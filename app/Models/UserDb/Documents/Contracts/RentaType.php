<?php

declare(strict_types=1);

namespace App\Models\UserDb\Documents\Contracts;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class RentaType extends Model
{
    protected $table = 'su_renta_types';

    protected $fillable = [
        'name',
        'description',
        'unit',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get the contract rents that use this renta type
     */
    public function contractRents(): HasMany
    {
        return $this->hasMany(ContractRent::class, 'renta_id');
    }
}
