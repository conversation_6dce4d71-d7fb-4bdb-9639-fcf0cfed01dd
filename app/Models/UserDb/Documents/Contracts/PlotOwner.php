<?php

declare(strict_types=1);

namespace App\Models\UserDb\Documents\Contracts;

use App\Models\UserDb\Owners\Owner;
use App\Models\UserDb\Owners\OwnerRep;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * PlotOwner Model
 * 
 * Represents the su_plots_owners_rel table - the junction table that connects
 * contract plots with their owners, including ownership percentages and 
 * legal documentation details.
 */
class PlotOwner extends Model
{
    protected $table = 'su_plots_owners_rel';

    public $timestamps = false;

    protected $fillable = [
        'pc_rel_id',          
        'owner_id',            
        'percent',             
        'owner_document_id',   
        'rep_id',              
        'proxy_num',           
        'proxy_date',          
        'path',                
        'is_heritor',         
        'numerator',          
        'denominator',        
        'notary_name',        
        'notary_number',       
        'notary_address',     
        'is_set_manual',      
        'is_signer',          
    ];

    protected $casts = [
        'percent' => 'decimal:4',
        'proxy_date' => 'date',
        'is_heritor' => 'boolean',
        'numerator' => 'integer',
        'denominator' => 'integer',
        'is_set_manual' => 'boolean',
        'is_signer' => 'integer',
    ];

    /**
     * Get the contract plot that owns this plot owner relationship
     */
    public function contractPlot(): BelongsTo
    {
        return $this->belongsTo(ContractPlot::class, 'pc_rel_id');
    }

    /**
     * Get the owner in this relationship
     */
    public function owner(): BelongsTo
    {
        return $this->belongsTo(Owner::class, 'owner_id');
    }

    /**
     * Get the owner representative for this plot owner
     */
    public function ownerRep(): BelongsTo
    {
        return $this->belongsTo(OwnerRep::class, 'rep_id');
    }

    /**
     * Scope for heritors only
     */
    public function scopeHeritors($query)
    {
        return $query->where('is_heritor', true);
    }

    /**
     * Scope for non-heritors only
     */
    public function scopeNonHeritors($query)
    {
        return $query->where('is_heritor', false);
    }

    /**
     * Scope for signers only
     */
    public function scopeSigners($query)
    {
        return $query->where('is_signer', true);
    }

    /**
     * Scope for manually set records
     */
    public function scopeManuallySet($query)
    {
        return $query->where('is_set_manual', true);
    }

    /**
     * Scope for automatically set records
     */
    public function scopeAutomaticallySet($query)
    {
        return $query->where('is_set_manual', false);
    }

    /**
     * Get the ownership fraction as a string
     */
    public function getFractionAttribute(): ?string
    {
        if ($this->numerator && $this->denominator) {
            return "{$this->numerator}/{$this->denominator}";
        }
        
        return null;
    }

    /**
     * Get the ownership percentage as a formatted string
     */
    public function getFormattedPercentAttribute(): string
    {
        return number_format((float) $this->percent, 2) . '%';
    }
}
