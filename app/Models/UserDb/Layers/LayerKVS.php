<?php

declare(strict_types=1);

namespace App\Models\UserDb\Layers;

use Illuminate\Database\Eloquent\Model;

class LayerKVS extends Model
{
    protected $table = 'layer_kvs';
    protected $primaryKey = 'gid';
    protected $fillable = [
        'kad_ident',
        'geom',
        'ekate',
        'masiv',
        'number',
        'category',
        'area_type',
        'has_contracts',
        'mestnost',
        'include',
        'participate',
        'white_spots',
        'used_area_by',
        'area_farming',
        'area_year',
        'used_area',
        'usable',
        'document_area',
        'is_edited',
        'edit_date',
        'edit_active_from',
        'waiting_update',
        'irrigated_area',
        'comment',
        'old_kad_ident',
        'allowable_area',
        'allowable_type',
        'block',
        'fill_color',
        'border_color',
        'label',
    ];
}
