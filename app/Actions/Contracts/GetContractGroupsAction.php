<?php

declare(strict_types=1);

namespace App\Actions\Contracts;

use App\Http\Resources\Contracts\ContractGroupResourceCollection;
use App\Models\UserDb\Documents\Contracts\ContractGroup;
use Illuminate\Http\JsonResponse;

/**
 * Get all contract groups
 * This action handles fetching all contract groups from the database
 */
class GetContractGroupsAction
{
    public function __construct() {}

    /**
     * Execute the action to get all contract groups
     *
     * @return JsonResponse
     */
    public function execute(): JsonResponse
    {
        try {
            $contractGroups = ContractGroup::orderBy('name')->get();

            return response()->json(
                (new ContractGroupResourceCollection($contractGroups))->resolve()
            );

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to fetch contract groups',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
