<?php

declare(strict_types=1);

namespace App\Actions\Contracts;

use App\Config\MaterializedViews\RentaConfig;
use App\Factories\Contracts\ContractRenewalStrategyFactory;
use App\Pipes\Contracts\Renew\ContractRenewalContext;
use App\Exceptions\PlotNotRenewableException;
use App\Models\UserDb\Documents\Contracts\Contract;
use App\Services\ContractPlotFilter\ContractPlotFilter;
use App\Traits\MaterializedViewTrait;
use App\Types\Enums\Documents\ContractTypeEnum;
use App\Types\Enums\Documents\DocumentTypeEnum;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pipeline\Pipeline;
use Illuminate\Support\Facades\DB;

/**
 * Renew Contracts Action
 *
 * Handles the complex process of renewing multiple contracts with all their relations.
 * Creates new contracts based on existing ones with updated dates and contract numbers.
 *
 * The renewal process includes:
 * 1. Contract creation with updated dates and numbers
 * 2. Plot renewability validation (throws PlotNotRenewableException if plots can't be renewed)
 * 3. Contract plots and their relationships copying
 * 4. Plot owners and their relationships copying
 * 5. Contract rents (natural rent types and values) copying
 * 6. Contract files and attachments copying
 *
 * Each contract is processed in its own transaction for resilience - if one contract
 * fails to renew (e.g., plot validation fails), the others will still be processed successfully.
 */
class RenewContractsAction
{
    use MaterializedViewTrait;

    /**
     * Execute the contract renewal process
     *
     * @param array $attributes Request attributes containing either contract_ids or filters
     * @param DocumentTypeEnum $documentType The document type (contracts or subleases)
     * @throws \Exception If renewal fails
     */
    public function execute(array $attributes, DocumentTypeEnum $documentType): void
    {
        $this->withManagedViews(
            RentaConfig::TRIGGER_TABLES,
            RentaConfig::MATERIALIZED_VIEWS,
            fn() => $this->executeRenewal($attributes, $documentType)
        );
    }

    /**
     * Execute the actual contract renewal process with materialized view management
     *
     * @return bool True if any contracts were successfully renewed, false otherwise
     */
    private function executeRenewal(array $attributes, DocumentTypeEnum $documentType): bool
    {
        $successfulRenewals = 0;

        try {
            $contractIds = $this->resolveContractIdsFromRequest($attributes['filter_params'], $documentType);
            $contracts = $this->loadContracts($contractIds, $documentType);

            $contracts->each(function ($contract) use ($attributes, $documentType, &$successfulRenewals) {
                DB::beginTransaction();

                try {
                    $context = new ContractRenewalContext($contract, $attributes, $documentType);
                    $strategy = ContractRenewalStrategyFactory::create($documentType);

                    app(Pipeline::class)
                        ->send($context)
                        ->through($strategy->getPipelineConfiguration())
                        ->thenReturn();

                    DB::commit();
                    $successfulRenewals++;

                } catch (PlotNotRenewableException $e) {
                    DB::rollBack();
                    return;

                } catch (\Exception $e) {
                    DB::rollBack();
                    throw $e;
                }
            });

        } catch (\Exception $e) {
            throw $e;
        }

        return $successfulRenewals > 0;
    }

    /**
     * Load contracts with optimized eager loading (handles both regular contracts and subleases)
     *
     */
    private function loadContracts(array $contractIds, DocumentTypeEnum $documentType): Collection
    {
        $strategy = ContractRenewalStrategyFactory::create($documentType);
        $relationships = $strategy->getEagerLoadingRelationships();

        return Contract::with($relationships)
            ->whereIn('id', $contractIds)
            ->whereIn('nm_usage_rights', ContractTypeEnum::renewableTypes())
            ->get();
    }

    /**
     * Resolve contract IDs from filter params using the unified filter pipeline
     */
    private function resolveContractIdsFromRequest(array $filterParams, DocumentTypeEnum $documentType): array
    {
        $baseTable = $documentType->tableName();
        $contractPlotFilter = new ContractPlotFilter($baseTable);

        $contractIds = $contractPlotFilter
            ->withParams($filterParams)
            ->getQuery()
            ->select(["su_contracts.id"])
            ->selectRaw("contracts_for_renew.can_renew_contract as can_renew_contract")
            ->groupBy('su_contracts.id')
            ->groupBy('contracts_for_renew.can_renew_contract')
            ->havingRaw("contracts_for_renew.can_renew_contract = true")
            ->get()
            ->pluck('id')
            ->toArray();

        return $contractIds;
    }




}
