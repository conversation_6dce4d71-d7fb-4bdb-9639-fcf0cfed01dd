<?php

declare(strict_types=1);

namespace App\Actions\Contracts;

use App\Http\Resources\Contracts\ContractGroupResource;
use App\Models\UserDb\Documents\Contracts\ContractGroup;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

/**
 * Store a new contract group
 * This action handles creating a new contract group
 */
class StoreContractGroupAction
{
    public function __construct() {}

    /**
     * Execute the action to create a new contract group
     *
     * @param array $attributes
     * @return JsonResponse
     */
    public function execute(array $attributes): JsonResponse
    {
        try {
            $this->validateAttributes($attributes);

            $contractGroup = ContractGroup::create([
                'name' => $attributes['name'],
            ]);

            return response()->json(
                (new ContractGroupResource($contractGroup))->resolve(),
                201
            );

        } catch (ValidationException $e) {
            return response()->json([
                'error' => 'Validation failed',
                'message' => 'The given data was invalid.',
                'errors' => $e->errors()
            ], 422);

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to create contract group',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Validate the input attributes
     *
     * @param array $attributes
     * @throws ValidationException
     */
    private function validateAttributes(array $attributes): void
    {
        $validator = validator($attributes, [
            'name' => 'required|string|max:255|unique:su_contract_group,name',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }
    }
}
