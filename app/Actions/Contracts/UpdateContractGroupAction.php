<?php

declare(strict_types=1);

namespace App\Actions\Contracts;

use App\Http\Resources\Contracts\ContractGroupResource;
use App\Models\UserDb\Documents\Contracts\ContractGroup;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

/**
 * Update an existing contract group
 * This action handles updating an existing contract group
 */
class UpdateContractGroupAction
{
    public function __construct() {}

    /**
     * Execute the action to update a contract group
     *
     * @param ContractGroup $contractGroup
     * @param array $attributes
     * @return JsonResponse
     */
    public function execute(ContractGroup $contractGroup, array $attributes): JsonResponse
    {
        try {
            $this->validateAttributes($attributes, $contractGroup->id);

            $contractGroup->update([
                'name' => $attributes['name'],
            ]);

            return response()->json(
                (new ContractGroupResource($contractGroup->fresh()))->resolve()
            );

        } catch (ValidationException $e) {
            return response()->json([
                'error' => 'Validation failed',
                'message' => 'The given data was invalid.',
                'errors' => $e->errors()
            ], 422);

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to update contract group',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Validate the input attributes
     *
     * @param array $attributes
     * @param int $excludeId
     * @throws ValidationException
     */
    private function validateAttributes(array $attributes, int $excludeId): void
    {
        $validator = validator($attributes, [
            'name' => "required|string|max:255|unique:su_contract_group,name,{$excludeId}",
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }
    }
}
