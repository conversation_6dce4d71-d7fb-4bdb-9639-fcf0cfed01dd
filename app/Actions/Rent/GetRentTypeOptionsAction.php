<?php

declare(strict_types=1);

namespace App\Actions\Rent;

use App\Http\Resources\Rent\RentTypeOptionResourceCollection;
use App\Models\UserDb\Rent\RentTypeOption;
use Illuminate\Http\JsonResponse;

/**
 * Get all rent type options
 * This action handles fetching all rent type options without pagination
 */
class GetRentTypeOptionsAction
{
    public function __construct() {}

    /**
     * Execute the action to get all rent type options
     *
     * @return JsonResponse
     */
    public function execute(): JsonResponse
    {
        try {
            $rentTypeOptions = RentTypeOption::orderBy('title')
                ->get();

            return response()->json(
                (new RentTypeOptionResourceCollection($rentTypeOptions))->resolve()
            );

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to fetch rent type options',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
