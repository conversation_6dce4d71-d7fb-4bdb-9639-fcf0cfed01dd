<?php

declare(strict_types=1);

namespace App\Actions\Rent;


use App\Http\Resources\Rent\RentInKindResourceCollection;
use App\Models\UserDb\Rent\RentInKind;
use Illuminate\Http\JsonResponse;

/**
 * Get all rent-in-kind types with their unit information
 * This action handles fetching all rent types with proper relationships
 */
class GetRentInKindTypesAction
{
    public function __construct() {}

    /**
     * Execute the action to get all rent-in-kind types
     *
     * @return JsonResponse
     */
    public function execute(): JsonResponse
    {
        try {
            $rentTypes = RentInKind::with('unitOfMeasure')
                ->orderBy('name')
                ->get();

            return response()->json(
                (new RentInKindResourceCollection($rentTypes))->resolve()
            );

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to fetch rent-in-kind types',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
