<?php

declare(strict_types=1);

namespace App\Actions\Plots;

use App\Http\Resources\Plots\PlotResource;
use App\Pipes\ContractPlotFilter\Plot\AvailableAreaCalculationPipe;
use App\Pipes\ContractPlotFilter\Plot\PlotSearchPipe;
use App\Services\Plots\PlotFilter;
use Illuminate\Http\JsonResponse;

/**
 * Get available plots for contract/sublease creation
 * This action specifically returns plots that are available for new contracts
 * with calculated available areas based on existing contract overlaps
 */
class GetAvailablePlotsAction
{
    public function execute(array $filterParams, int $page = 1, int $limit = 10): JsonResponse
    {
        $plotFilter = new PlotFilter('layer_kvs');

        $plotFilter->addFilter(new AvailableAreaCalculationPipe($filterParams['available_area_calculation']))
                    ->addFilter(new PlotSearchPipe($filterParams['search']))
                    ->withParams([]);
        

        $query = $plotFilter->getAvailablePlots();

        $total = $query->count();
        $plots = $query
            ->offset(($page - 1) * $limit)
            ->limit($limit)
            ->get();

        return response()->json([
            'rows' => PlotResource::collection($plots),
            'total' => $total,
            'header' => PlotResource::getPlotHeaders(),
        ]);
    }
}
