<?php

declare(strict_types=1);

namespace App\Actions\Plots;

use App\Http\Resources\Plots\PlotCategoryResourceCollection;
use App\Models\UserDb\Plots\PlotCategory;
use Illuminate\Http\JsonResponse;

/**
 * Get all plot categories
 * This action handles fetching all plot categories without pagination
 */
class GetPlotCategoriesAction
{
    public function __construct() {}

    /**
     * Execute the action to get all plot categories
     *
     * @return JsonResponse
     */
    public function execute(): JsonResponse
    {
        try {
            $plotCategories = PlotCategory::orderBy('title')
                ->get();

            return response()->json(
                (new PlotCategoryResourceCollection($plotCategories))->resolve()
            );

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to fetch plot categories',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
