<?php

declare(strict_types=1);

namespace App\Actions\Plots;

use App\Http\Resources\Plots\AreaTypeResourceCollection;
use App\Models\UserDb\Plots\AreaType;
use Illuminate\Http\JsonResponse;

/**
 * Get area types with optional filtering
 * This action handles fetching area types from the su_area_types materialized view
 * with support for filtering by title and id parameters
 */
class GetAreaTypesAction
{
    public function __construct() {}

    /**
     * Execute the action to get area types with optional search and pagination
     *
     * @param array $params Validated parameters from request
     * @return JsonResponse
     */
    public function execute(array $params): JsonResponse
    {
        try {
            $query = AreaType::query();

            $query->search($params['search'] ?? null)
                  ->orderBy('title');

            $perPage = $params['limit'] ?? 10;
            $page = $params['page'] ?? 1;

            $paginatedResults = $query->paginate($perPage, ['*'], 'page', $page);

            return response()->json((new AreaTypeResourceCollection($paginatedResults))->resolve());

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to fetch area types',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
