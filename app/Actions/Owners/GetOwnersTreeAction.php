<?php

declare(strict_types=1);

namespace App\Actions\Owners;

use App\Pipes\Owners\OwnerMultiFieldSearchFilterPipe;
use App\Services\Owners\OwnersFilter;
use Illuminate\Http\JsonResponse;

/**
 * Get owners tree with hierarchical structure
 * This action handles the business logic for fetching owners in tree format
 * with proper filtering and pagination
 */
class GetOwnersTreeAction
{
    public function __construct() {}

    /**
     * Execute the action to get owners tree with filtering and pagination
     *
     * @param array $requestParams The validated request parameters
     * @return JsonResponse
     */
    public function execute(array $requestParams): JsonResponse
    {
        $search = $requestParams['search'] ?? null;
        $page = (int) ($requestParams['page'] ?? 1);
        $limit = (int) ($requestParams['limit'] ?? 10);

        try {
            $result = (new OwnersFilter('su_owners'))
                ->addFilter(new OwnerMultiFieldSearchFilterPipe([$search]))
                ->getOwnersTree($page, $limit);

            return response()->json($result);

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to fetch owners tree',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
