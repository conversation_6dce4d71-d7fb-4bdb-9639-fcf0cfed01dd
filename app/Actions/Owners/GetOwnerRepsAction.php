<?php

declare(strict_types=1);

namespace App\Actions\Owners;

use App\Services\Owners\OwnerRepsFilter;
use Illuminate\Http\JsonResponse;

class GetOwnerRepsAction
{
    public function __construct(
        private OwnerRepsFilter $ownerRepsFilter
    ) {}

    /**
     * Execute the action to get owner representatives
     */
    public function execute(array $params, int $page = 1, int $limit = 10): JsonResponse
    {
        try {
            $result = $this->ownerRepsFilter
                ->withParams($params)
                ->getOwnerReps($page, $limit);

            return response()->json($result);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to fetch owner representatives',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
