<?php

declare(strict_types=1);

namespace App\Actions\Owners;

use App\Http\Resources\Owners\OwnerResourceCollection;
use App\Models\UserDb\Owners\Owner;
use App\Services\Owners\OwnersFilter;
use App\Traits\Common\FarmingYearsTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;

/**
 * Get paginated list of owners with filtering and contract statistics
 * This action handles all the logic for fetching owners with their contract data
 */
class GetOwnersAction
{
    use FarmingYearsTrait;

    public function __construct() {}

    /**
     * Execute the action to get paginated owners with filtering
     *
     * @param array $requestParams The validated request parameters
     * @return JsonResponse
     */
    public function execute(array $requestParams): JsonResponse
    {
        $page = (int) ($requestParams['page'] ?? 1);
        $limit = (int) ($requestParams['limit'] ?? 10);
        $sort = $requestParams['sort'] ?? null;

        try {
            $filterParams = $requestParams['filter_params'] ?? [];

            $currentFarmingYear = $this->getCurrentFarmingYear();
            $farmingYear = (int) $currentFarmingYear['year'];

            $ownersFilter = new OwnersFilter('su_owners');

            $ownersFilter
                ->withParams($filterParams)
                ->withOwnerFields()
                ->withChildren()
                ->withContractStatistics($farmingYear)
                ->withSorting($sort);

            $ownersFilter->getQuery()
                ->groupBy([
                    'su_owners.id',
                    'su_owners.name',
                    'su_owners.surname',
                    'su_owners.lastname',
                    'su_owners.company_name',
                    'su_owners.egn',
                    'su_owners.eik',
                    'su_owners.phone',
                    'su_owners.mobile',
                    'su_owners.email',
                    'su_owners.is_dead',
                    'su_owners.owner_type',
                    'su_owners.remark',
                    'su_owners.created_at',
                    'su_owners.updated_at'
                ]);

            $results = $ownersFilter->getQuery()->paginate($limit, ['*'], 'page', $page);

            return response()->json((new OwnerResourceCollection($results))->resolve());

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to fetch owners',
                'message' => $e->getMessage()
            ], 500);
        }
    }


}
