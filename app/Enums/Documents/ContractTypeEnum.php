<?php

declare(strict_types=1);

namespace App\Enums\Documents;

enum ContractTypeEnum: int
{
    case Ownership = 1;
    case Rent = 2;
    case Lease = 3;
    case Agreement = 4;
    case JointProcessing = 5;
    case Sales = 6;

    public function name(): string
    {
        return match($this) {
            ContractTypeEnum::Ownership => 'Собственост',
            ContractTypeEnum::Rent => 'Аренда',
            ContractTypeEnum::Lease => 'Наем',
            ContractTypeEnum::Agreement => 'Споразумение',
            ContractTypeEnum::JointProcessing => 'Съвместна обработка',
            ContractTypeEnum::Sales => 'Продажба',
        };
    }

    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }
}
