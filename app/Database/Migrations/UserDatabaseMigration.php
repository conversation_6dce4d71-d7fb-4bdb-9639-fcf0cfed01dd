<?php

declare(strict_types=1);

namespace App\Database\Migrations;

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class UserDatabaseMigration extends Migration
{
    protected $connection = 'user_db';

    public function __construct()
    {
        $this->setConnection();
    }

    public function runOnAllUserDatabases(callable $callback): void
    {
        $userDatabases = DB::connection('main')->table('users')->pluck('database');
        foreach ($userDatabases as $database) {
            DB::setDefaultConnection($database);
            $callback();
        }
    }

    protected function setConnection(): void
    {
        $this->connection = 'user_db';
    }
}
