<?php

declare(strict_types=1);

namespace App\Services\Owners;

use App\Services\Filters\BaseFilterService;
use App\Services\Filters\FilterProviders\OwnerRepsFilterItemProvider;
use App\Services\Filters\Contracts\FilterItemProviderInterface;

class OwnerRepsFilter extends BaseFilterService
{
    /**
     * Constructor - uses 'su_owners_reps' as base table
     */
    public function __construct()
    {
        parent::__construct('su_owners_reps');
    }

    /**
     * Create the appropriate item provider for this filter service
     *
     * @return FilterItemProviderInterface
     */
    protected function createItemProvider(): FilterItemProviderInterface
    {
        return new OwnerRepsFilterItemProvider();
    }



    /**
     * Get owner representatives with pagination using unified API pattern
     * Similar to OwnersFilter::getOwners() and PlotFilter::getAvailablePlots()
     *
     * @param int $page Page number
     * @param int $limit Results per page
     * @return array Formatted results with pagination info
     */
    public function getOwnerReps(int $page = 1, int $limit = 10): array
    {
        $nameExpr = "COALESCE(NULLIF(TRIM(CONCAT_WS(' ', su_owners_reps.rep_name, su_owners_reps.rep_surname, su_owners_reps.rep_lastname)), ''), '')";

        $paginated = $this->getQuery()
            ->selectRaw("
                su_owners_reps.id,
                su_owners_reps.rep_name,
                su_owners_reps.rep_surname,
                su_owners_reps.rep_lastname,
                su_owners_reps.rep_egn,
                su_owners_reps.rep_lk,
                su_owners_reps.rep_lk_izdavane,
                su_owners_reps.rep_address,
                su_owners_reps.owner_id,
                su_owners_reps.rent_place,
                su_owners_reps.iban,
                su_owners_reps.rep_phone,
                su_owners_reps.bic,
                su_owners_reps.bank_name,
                {$nameExpr} AS name,
                CASE
                    WHEN su_owners_reps.rep_egn IS NOT NULL AND su_owners_reps.rep_egn != ''
                    THEN {$nameExpr} || ' (' || su_owners_reps.rep_egn || ')'
                    ELSE {$nameExpr}
                END AS title
            ")
            ->orderBy('su_owners_reps.rep_name')
            ->orderBy('su_owners_reps.rep_surname')
            ->orderBy('su_owners_reps.rep_lastname')
            ->orderBy('su_owners_reps.id')
            ->paginate($limit, ['*'], 'page', $page);

        return [
            'rows' => $paginated->items(),
            'total' => $paginated->total(),
        ];
    }
}
