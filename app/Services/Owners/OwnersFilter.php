<?php

declare(strict_types=1);

namespace App\Services\Owners;

use App\Services\Filters\BaseFilterService;
use App\Services\Filters\FilterProviders\OwnersFilterItemProvider;
use App\Services\Filters\Contracts\FilterItemProviderInterface;
use App\Traits\Database\QueryHelper;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use InvalidArgumentException;

/**
 * Unified OwnersFilter service using the new FilterBuilder architecture
 * Provides methods for filtering and querying owners with consistent withParams() API
 */
class OwnersFilter extends BaseFilterService
{
    use QueryHelper;
    /**
     * Constructor - requires explicit base table specification
     */
    public function __construct(string $baseTable)
    {
        parent::__construct($baseTable);
    }

    /**
     * Create the appropriate item provider for this filter service
     *
     * @return FilterItemProviderInterface
     */
    protected function createItemProvider(): FilterItemProviderInterface
    {
        return new OwnersFilterItemProvider();
    }



    /**
     * Add basic owner fields and computed fields to the query
     */
    public function withOwnerFields(): self
    {
        $this->getQuery()->select([
            'su_owners.id',
            'su_owners.name',
            'su_owners.surname',
            'su_owners.lastname',
            'su_owners.company_name',
            'su_owners.egn',
            'su_owners.eik',
            'su_owners.phone',
            'su_owners.mobile',
            'su_owners.email',
            'su_owners.is_dead',
            'su_owners.owner_type',
            'su_owners.remark',
            'su_owners.created_at',
            'su_owners.updated_at'
        ]);

        $this->getQuery()->selectRaw("
            COALESCE(
                NULLIF(TRIM(CONCAT_WS(' ', su_owners.name, su_owners.surname, su_owners.lastname)), ''),
                su_owners.company_name
            ) as owner_name
        ");

        $this->getQuery()->whereNotNull('su_owners.id');

        return $this;
    }

    /**
     * Add children data for all owners with complete owner information
     */
    public function withChildren(): self
    {
        $this->getQuery()->selectRaw("
            COALESCE(
                (
                    SELECT JSON_AGG(
                        JSON_BUILD_OBJECT(
                            'id', h_owners.id,
                            'name', h_owners.name,
                            'surname', h_owners.surname,
                            'lastname', h_owners.lastname,
                            'company_name', h_owners.company_name,
                            'owner_name', COALESCE(NULLIF(TRIM(CONCAT_WS(' ', h_owners.name, h_owners.surname, h_owners.lastname)), ''), h_owners.company_name),
                            'egn', h_owners.egn,
                            'eik', h_owners.eik,
                            'owner_type', h_owners.owner_type,
                            'is_dead', h_owners.is_dead,
                            'phone', h_owners.phone,
                            'mobile', h_owners.mobile,
                            'email', h_owners.email,
                            'created_at', h_owners.created_at,
                            'updated_at', h_owners.updated_at
                        )
                    )
                    FROM su_heritors h
                    JOIN su_owners h_owners ON h_owners.id = (string_to_array(h.path::text, '.'))[array_length(string_to_array(h.path::text, '.'), 1)]::int
                    WHERE (string_to_array(h.path::text, '.'))[1]::int = su_owners.id
                    AND h_owners.id != su_owners.id
                ),
                '[]'::json
            ) as children
        ");

        return $this;
    }

    /**
     * Add contract statistics using direct aggregation on joined data
     */
    public function withContractStatistics(int $farmingYear): self
    {
        $query = $this->getQuery();
        $joinedTables = collect($query->joins)->pluck('table');

        if ($query->from === 'su_owners' && !$joinedTables->contains('su_contracts')) {
            $query->leftJoin('su_plots_owners_rel', 'su_plots_owners_rel.owner_id', '=', 'su_owners.id')
                  ->leftJoin('su_contracts_plots_rel', 'su_contracts_plots_rel.id', '=', 'su_plots_owners_rel.pc_rel_id')
                  ->leftJoin('su_contracts', 'su_contracts.id', '=', 'su_contracts_plots_rel.contract_id');
        }
        $query->leftJoin('su_contracts as annex_contracts', function($join) use ($farmingYear) {
            $join->on('annex_contracts.parent_id', '=', 'su_contracts.id')
                 ->where('annex_contracts.is_annex', '=', true)
                 ->where('annex_contracts.active', '=', true)
                 ->whereRaw('get_farming_year_by_date(annex_contracts.start_date::date) = ?', [$farmingYear]);
        });
        $query->leftJoin('su_contracts_plots_rel as annex_scpr', function($join) {
            $join->on('annex_scpr.contract_id', '=', 'annex_contracts.id')
                 ->on('annex_scpr.plot_id', '=', 'su_contracts_plots_rel.plot_id');
        });
        $query->selectRaw("
            COUNT(DISTINCT
                CASE
                    WHEN su_contracts_plots_rel.annex_action != 'removed'
                    AND (
                        CASE WHEN annex_contracts.id IS NOT NULL
                            THEN get_farming_year_by_date(annex_contracts.start_date::date) = ?
                            ELSE get_farming_year_by_date(su_contracts.start_date::date) = ?
                        END
                    )
                    THEN COALESCE(annex_contracts.id, su_contracts.id)
                    ELSE NULL
                END
            ) as current_farming_year_contracts
        ", [$farmingYear, $farmingYear]);
        $query->selectRaw("
            COALESCE(SUM(
                CASE
                    WHEN su_contracts_plots_rel.annex_action != 'removed'
                    AND (
                        CASE WHEN annex_contracts.id IS NOT NULL
                            THEN get_farming_year_by_date(annex_contracts.start_date::date) = ?
                            ELSE get_farming_year_by_date(su_contracts.start_date::date) = ?
                        END
                    )
                    THEN
                        CASE WHEN annex_contracts.id IS NOT NULL
                            THEN annex_scpr.contract_area
                            ELSE su_contracts_plots_rel.contract_area
                        END
                    ELSE 0
                END
            ), 0) as plots_area_sum
        ", [$farmingYear, $farmingYear]);

        return $this;
    }

    /**
     * Get owners in tree format (hierarchical structure with heritors)
     */
    public function getOwnersTree(int $page = 1, int $limit = 10): array
    {
        $nameExpr = "COALESCE(NULLIF(TRIM(CONCAT_WS(' ', su_owners.name, su_owners.surname, su_owners.lastname)), ''), su_owners.company_name)";

        $paginatedIds = $this->getQuery()
            ->select([
                'su_owners.id',
                'su_owners.is_dead',
                DB::raw("{$nameExpr} as owner_name")
            ])
            ->distinct()
            ->orderBy('su_owners.is_dead')
            ->orderBy('owner_name')
            ->orderBy('su_owners.id')
            ->paginate($limit, ['*'], 'page', $page);


        $rootOwnerIds = collect($paginatedIds->items())->pluck('id');
        $tree = $this->buildTreeFromOwnerIds($rootOwnerIds);

        return [
            'rows' => $tree,
            'total' => count($tree),
        ];
    }

    /**
     * Build tree from filtered owner IDs using PostgreSQL get_descendants function
     */
    public function buildTreeFromOwnerIds(Collection $filteredOwnerIds): array
    {

        $contractorsQuery = DB::table('su_owners', 'so')
            ->whereIn('so.id', $filteredOwnerIds)
            ->selectRaw("
                so.id AS owner_id,
                so.is_dead,
                false AS is_heritor,
                EXISTS (
                    SELECT 1 FROM su_heritors sh WHERE so.id::text::ltree @> sh.path
                ) AS has_heritors,
                COALESCE(NULLIF(TRIM(CONCAT_WS(' ', so.name, so.surname, so.lastname)), ''), so.company_name) AS owner_name,
                so.id AS greatest_grandparent,
                COALESCE(NULLIF(TRIM(CONCAT_WS(' ', so.name, so.surname, so.lastname)), ''), so.company_name) AS greatest_grandparent_name,
                so.id::text::ltree AS path,
                null AS rep_name,
                so.egn,
                so.eik,
                so.owner_type
            ");
        $heritorsQuery = DB::table('su_heritors', 'h')
            ->join('su_owners AS so', 'so.id', '=', DB::raw("(string_to_array(h.path::text, '.'))[array_length(string_to_array(h.path::text, '.'), 1)]::int"))
            ->join('su_owners AS root_so', 'root_so.id', '=', DB::raw("(string_to_array(h.path::text, '.'))[1]::int"))
            ->whereIn(DB::raw("(string_to_array(h.path::text, '.'))[1]::int"), $filteredOwnerIds)
            ->selectRaw("
                so.id AS owner_id,
                so.is_dead,
                true AS is_heritor,
                false AS has_heritors,
                COALESCE(NULLIF(TRIM(CONCAT_WS(' ', so.name, so.surname, so.lastname)), ''), so.company_name) AS owner_name,
                root_so.id AS greatest_grandparent,
                COALESCE(NULLIF(TRIM(CONCAT_WS(' ', root_so.name, root_so.surname, root_so.lastname)), ''), root_so.company_name) AS greatest_grandparent_name,
                h.path,
                null AS rep_name,
                so.egn,
                so.eik,
                so.owner_type
            ");
        $combinedQuery = $contractorsQuery->unionAll($heritorsQuery);

        $result = DB::table('tree_result')
            ->withExpression('combined_data', $combinedQuery)
            ->withExpression('tree_result', function ($query) {
                return $query->selectRaw("
                    get_descendants(
                        ARRAY_AGG(DISTINCT
                            (cd.owner_id, cd.is_dead, cd.is_heritor, cd.has_heritors, cd.owner_name,
                             cd.greatest_grandparent, cd.greatest_grandparent_name, cd.path, cd.rep_name,
                             cd.egn, cd.eik, cd.owner_type)::owner
                        ), ''::ltree, 0
                    ) AS contractors_tree
                ")
                ->from('combined_data AS cd');
            })
            ->selectRaw('tree_result.contractors_tree')
            ->first();

        return json_decode($result->contractors_tree ?? '[]', true);
    }

    /**
     * Generate owner title expression with EGN/EIK in parentheses
     */
    private function ownerTitleExpression(string $tableAlias): string
    {
        $nameExpr = "COALESCE(NULLIF(TRIM(CONCAT_WS(' ', {$tableAlias}.name, {$tableAlias}.surname, {$tableAlias}.lastname)), ''), {$tableAlias}.company_name)";

        return "CASE
            WHEN {$tableAlias}.egn IS NOT NULL AND {$tableAlias}.egn != ''
            THEN {$nameExpr} || ' (' || {$tableAlias}.egn || ')'
            WHEN {$tableAlias}.eik IS NOT NULL AND {$tableAlias}.eik != ''
            THEN {$nameExpr} || ' (' || {$tableAlias}.eik || ')'
            ELSE {$nameExpr}
        END";
    }

    /**
     * Apply sorting to the query based on provided sort parameters
     *
     * @param array|null $sort Array of sort options with 'column' and 'direction' keys
     * @return self Returns the same instance for method chaining
     */
    public function withSorting(?array $sort): self
    {
        $orderByClause = $this->generateOrderByClause($sort);
        $this->getQuery()->orderByRaw($orderByClause);
        return $this;
    }

    /**
     * Generate ORDER BY clause for owners queries (without "ORDER BY" prefix)
     * Maps frontend column names to actual database columns and validates sort directions
     * Defaults to sorting by owner_name if no sort is provided
     *
     * @param array|null $sort Array of sort options
     * @return string ORDER BY clause, defaults to 'owner_name asc' if no sort provided
     */
    private function generateOrderByClause(?array $sort): string
    {
        if (!$sort || empty($sort)) {
            return 'owner_name asc';
        }

        $columnMapFn = fn ($sortKey) => match($sortKey) {
            'owner_name' => 'owner_name',
            'current_farming_year_contracts' => 'current_farming_year_contracts',
            'plots_area_sum' => 'plots_area_sum',
            default => throw new InvalidArgumentException("Invalid sort column: {$sortKey}"),
        };

        $validSortOptions = collect($sort)
            ->filter(fn ($option) => isset($option['column'], $option['direction']))
            ->filter(fn ($option) => in_array(strtolower($option['direction']), ['asc', 'desc'], true))
            ->map(fn ($option) => [
                'column' => $columnMapFn($option['column']),
                'direction' => strtolower($option['direction'])
            ]);

        if ($validSortOptions->isEmpty()) {
            return '';
        }

        return $validSortOptions
            ->map(fn ($option) => "{$option['column']} {$option['direction']}")
            ->join(', ');
    }



}
