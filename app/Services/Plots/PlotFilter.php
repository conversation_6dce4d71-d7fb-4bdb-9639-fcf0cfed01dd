<?php

declare(strict_types=1);

namespace App\Services\Plots;

use App\Services\Filters\BaseFilterService;
use App\Services\Filters\FilterProviders\PlotFilterItemProvider;
use App\Services\Filters\Contracts\FilterItemProviderInterface;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;

/**
 * Unified PlotFilter service using the new FilterBuilder architecture
 * Provides methods for filtering and querying available plots with consistent withParams() API
 */
class PlotFilter extends BaseFilterService
{
    /**
     * Create the appropriate item provider for this filter service
     *
     * @return FilterItemProviderInterface
     */
    protected function createItemProvider(): FilterItemProviderInterface
    {
        return new PlotFilterItemProvider();
    }



    /**
     * Get available plots with full details (maintains backward compatibility)
     * This replaces the old getAvailablePlots() method
     */
    public function getAvailablePlots(): Builder
    {
        $query = $this->getQuery();

        $query->select([
            ...$this->getBasicPlotFields(),
            ...$this->getCalculatedAreaFields(),
            ...$this->getJsonFields(),
        ]);

        $query->orderBy('layer_kvs.kad_ident', 'asc');

        return $query;
    }

    /**
     * Get basic plot fields
     */
    private function getBasicPlotFields(): array
    {
        return [
            'layer_kvs.gid',
            'layer_kvs.kad_ident',
            'layer_kvs.mestnost',
            DB::raw('ROUND(COALESCE(layer_kvs.document_area, ST_Area(layer_kvs.geom)/1000)::numeric, 3) as document_area'),
            'layer_kvs.category',
            'layer_kvs.virtual_ekatte_name',
            'layer_kvs.virtual_category_title',
            'layer_kvs.masiv',
            'layer_kvs.number',
            'layer_kvs.block',
            'layer_kvs.allowable_type',
            'layer_kvs.irrigated_area',
            'layer_kvs.comment',
            'layer_kvs.area_type',
            'layer_kvs.virtual_ntp_title',
            'layer_kvs.allowable_area',
            'layer_kvs.include',
            'layer_kvs.participate',
            'layer_kvs.white_spots',
        ];
    }

    /**
     * Get calculated area fields
     */
    private function getCalculatedAreaFields(): array
    {
        return [
            DB::raw($this->getContractAreaSql() . ' as contract_area'),
            DB::raw($this->getContractAreaSql() . ' as rent_area'),
            DB::raw($this->getRentAllowableAreaSql() . ' as rent_allowable_area'),
        ];
    }

    /**
     * Get JSON structured fields
     */
    private function getJsonFields(): array
    {
        return [
            DB::raw('json_build_object(
                \'kad_ident\', layer_kvs.kad_ident,
                \'mestnost\', layer_kvs.mestnost,
                \'document_area\', ROUND(COALESCE(layer_kvs.document_area, ST_Area(layer_kvs.geom)/1000)::numeric, 3)
            ) as plot_info'),
            DB::raw('json_build_object(
                \'virtual_ntp_title\', layer_kvs.virtual_ntp_title,
                \'virtual_category_title\', layer_kvs.virtual_category_title
            ) as plot_type'),
            DB::raw($this->getPlotAreasArraySql() . ' as plot_areas'),
        ];
    }

    private function getRentAllowableAreaSql(): string
    {
        return 'ROUND((
            CASE
                WHEN COALESCE(layer_kvs.document_area, ST_Area(layer_kvs.geom)/1000) > 0
                THEN 
                    COALESCE(layer_kvs.allowable_area, 0) *
                    (
                        COALESCE(layer_kvs.document_area, ST_Area(layer_kvs.geom)/1000) / 
                        (COALESCE(layer_kvs.document_area, ST_Area(layer_kvs.geom)/1000) - COALESCE(moa.max_overlapping_area, 0))
                    )
                ELSE 0
            END
        )::numeric, 3)';
    }

    private function getContractAreaSql(): string
    {
        return 'ROUND((
            COALESCE(layer_kvs.document_area, ST_Area(layer_kvs.geom)/1000) -
            COALESCE(moa.max_overlapping_area, 0)
        )::numeric, 3)';
    }

    private function getPlotAreasArraySql(): string
    {
        return 'json_build_array(
            json_build_object(
                \'title\', \'by contract\',
                \'value\', ' . $this->getContractAreaSql() . '
            ),
            json_build_object(
                \'title\', \'for rent\',
                \'value\', ' . $this->getContractAreaSql() . '
            ),
            json_build_object(
                \'title\', \'allowable\',
                \'value\', ROUND(COALESCE(layer_kvs.allowable_area, 0)::numeric, 3)
            ),
            json_build_object(
                \'title\', \'by document\',
                \'value\', ROUND(COALESCE(layer_kvs.document_area, ST_Area(layer_kvs.geom)/1000)::numeric, 3)
            )
        )';
    }
}
