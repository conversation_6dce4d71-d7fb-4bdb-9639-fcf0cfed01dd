<?php

declare(strict_types=1);

namespace App\Services\ContractPlotFilter;

use App\Pipes\ContractPlotFilter\Contract\BaseContractFilterPipe;
use App\Pipes\ContractPlotFilter\Contract\UserFarmingPermissionFilterPipe;
use App\Pipes\ContractPlotFilter\ContractRenew\BaseContractRenewFilterPipe;
use App\Pipes\Query\QueryFilterGroupPipe;
use App\Pipes\ContractPlotFilter\FarmingYear\ActiveContractPlotsByFarmingYearFilterPipe;
use App\Pipes\ContractPlotFilter\FarmingYear\ExpiringContractPlotsByFarmingYearFilterPipe;
use App\Pipes\ContractPlotFilter\FarmingYear\ForSubleaseContractPlotsByFarmingYearFilterPipe;
use App\Pipes\Owners\BaseOwnerFilterPipe;
use App\Pipes\Owners\OwnerMultiFieldFilterPipe;
use App\Services\Filters\BaseFilterService;
use App\Services\Filters\FilterProviders\ContractPlotFilterItemProvider;
use App\Services\Filters\Contracts\FilterItemProviderInterface;
use App\Services\Filters\ConditionalFilterFactory;
use App\Types\Interfaces\Query\QueryFilterPipe;

use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ContractPlotFilter extends BaseFilterService
{
    /**
     * Create the appropriate item provider for this filter service
     *
     * @return FilterItemProviderInterface
     */
    protected function createItemProvider(): FilterItemProviderInterface
    {
        return new ContractPlotFilterItemProvider();
    }

    /**
     * Create parameter-based filters specific to ContractPlotFilter
     */
    protected function createParameterFilters(array $filterParams): QueryFilterGroupPipe
    {
        $userFarmingPermissionsFilter = $this->createUserFarmingPermissionsFilter();
        $contractTypesGroupFilter = $this->createContractTypesFilter($filterParams, $userFarmingPermissionsFilter);

        // Create conditional filters for groups
        // Apply farming permissions filter when:
        // 1. No contract types filter exists AND group requires permission filtering
        $conditionalFilters = [
            ConditionalFilterFactory::customCallback(
                $userFarmingPermissionsFilter,
                function($groupFilters) use ($contractTypesGroupFilter) {
                    // Only apply if no contract types filter exists AND group has filters that require permissions
                    if (!$contractTypesGroupFilter->isEmpty()) {
                        return false; // Contract types filter already handles permissions
                    }

                    // Check if any filter in the group requires farming permissions
                    foreach ($groupFilters as $filter) {
                        if ($this->requiresPermissionFilter($filter)) {
                            return true;
                        }
                    }

                    return false;
                }
            )
        ];

        $groupsFilter = $this->createGroupsFilter($filterParams, $conditionalFilters);

        // Create parameter filters group (always AND)
        return new QueryFilterGroupPipe(
            QueryFilterPipe::OPERATION_AND,
            [
                $contractTypesGroupFilter,
                $groupsFilter
            ]
        );
    }

    /**
     * Create user farming permissions filter
     */
    private function createUserFarmingPermissionsFilter(): UserFarmingPermissionFilterPipe
    {
        return new UserFarmingPermissionFilterPipe(Auth::user()->getAuthIdentifier());
    }

    /**
     * Create contract types filter based on parameters
     * Applies farming permissions only when contract_types are specified
     */
    private function createContractTypesFilter(array $filterParams, UserFarmingPermissionFilterPipe $userFarmingPermissionsFilter): QueryFilterGroupPipe
    {
        $contractTypesGroupFilter = new QueryFilterGroupPipe(QueryFilterPipe::OPERATION_AND, []);

        if (isset($filterParams['contract_types'])) {
            $contractTypesFilter = $this->filterBuilder->getItemProvider()->getFilter('contract_types', $filterParams['contract_types']);

            // Create group with both contract types filter and user farming permissions
            $contractTypesGroupFilter = new QueryFilterGroupPipe(
                QueryFilterPipe::OPERATION_AND,
                [$contractTypesFilter, $userFarmingPermissionsFilter]
            );
        }

        return $contractTypesGroupFilter;
    }

    /**
     * Check if the filter requires farming permissions
     */
    private function requiresPermissionFilter($pipe): bool
    {
        return $pipe instanceof BaseContractFilterPipe
            || $pipe instanceof BaseContractRenewFilterPipe
            || $pipe instanceof BaseOwnerFilterPipe
            || $pipe instanceof ActiveContractPlotsByFarmingYearFilterPipe
            || $pipe instanceof ExpiringContractPlotsByFarmingYearFilterPipe
            || $pipe instanceof ForSubleaseContractPlotsByFarmingYearFilterPipe
            || $pipe instanceof OwnerMultiFieldFilterPipe;
    }





    public function getQuery(): Builder
    {
        return $this->filterBuilder->getBuiltQuery();
    }





    /**
     * Build the searched items query with filtering and sorting.
     * Override parent method to handle ContractPlot-specific JSON formatting and sorting.
     */
    protected function buildSearchedItemsQuery(?string $search, ?string $sort, ?string $order): Builder
    {
        return DB::table('filter_items_json')
            ->selectRaw("
                JSONB_BUILD_OBJECT(
                    'label', item->'label',
                    'value', item->'value'
                )
                ||
                CASE WHEN (item->'has_delimiter')::BOOL = TRUE
                    THEN JSONB_BUILD_OBJECT('has_delimiter', TRUE)
                    ELSE '{}'::JSONB
                END AS item
            ")
            ->when(
                $search,
                fn ($q) => $q->whereRaw("item->>'value' ILIKE ?", ["%{$search}%"])
            )
            ->orderBy(DB::raw("item->>'sort_priority'"), 'asc')
            ->when(
                $sort && $order,
                fn ($q) => $q->orderBy(DB::raw($sort), $order)
            );
    }





    /**
     * Apply filters to the query using the pipeline.
     * @deprecated Use filterBuilder->applyFilters() instead
     */
    private function applyFilters(QueryFilterGroupPipe $filters): void
    {
        $this->filterBuilder->applyFilters($filters);
    }

}
