<?php

declare(strict_types=1);

namespace App\Services\Filters;

use App\Services\Filters\Contracts\FilterItemProviderInterface;
use App\Pipes\Query\QueryFilterGroupPipe;
use App\Types\Interfaces\Query\QueryFilterPipe;
use Closure;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Database\Query\Builder;
use Illuminate\Pipeline\Pipeline;
use Illuminate\Support\Facades\DB;

/**
 * Unified filter builder that combines all building responsibilities:
 * - Incremental filter structure building (from FilterStructureBuilder)
 * - Group filter building with conditional logic (from GroupFilterBuilder)
 * - Query building and execution (from FilterQueryBuilder)
 *
 * @template TProvider of FilterItemProviderInterface
 */
class FilterBuilder
{
    /**
     * Collection of built filter structures
     *
     * @var Collection<int, QueryFilterPipe>
     */
    private Collection $filters;

    /**
     * The base query builder instance
     */
    private Builder $query;

    /**
     * The base table name
     *
     * @var non-empty-string
     */
    private string $baseTable;

    /**
     * The filter item provider for creating filters
     *
     * @var TProvider
     */
    private FilterItemProviderInterface $itemProvider;

    public function __construct(string $baseTable, FilterItemProviderInterface $itemProvider)
    {
        if (empty($baseTable)) {
            throw new Exception('Invalid base table');
        }

        $this->baseTable = $baseTable;
        $this->itemProvider = $itemProvider;
        $this->filters = collect();
        $this->query = DB::table($baseTable);
    }

    public function addFilter(QueryFilterPipe $filter, string $operation = QueryFilterPipe::OPERATION_AND): self
    {
        if ($this->filters->isEmpty()) {
            $firstGroup = new QueryFilterGroupPipe($operation, [$filter]);
            $this->filters->push($firstGroup);
        } else {
            $newGroup = new QueryFilterGroupPipe(
                $operation,
                $this->filters->merge([$filter])->toArray()
            );

            $this->filters = collect([$newGroup]);
        }
        
        return $this;
    }

    public function addFilterGroup(Closure $callback, string $operation = QueryFilterPipe::OPERATION_AND, ?Closure $filterFactory = null): self
    {
        if ($filterFactory === null) {
            throw new Exception('addFilterGroup requires a filterFactory callback to create filter instances');
        }

        $groupFilter = $filterFactory();
        $callback($groupFilter);

        if (!$groupFilter->hasFilters()) {
            return $this;
        }

        $groupStructure = $groupFilter->getBuiltStructure();
        return $this->addFilter($groupStructure, $operation);
    }

    public function addFilters(Collection $filters, string $operation = QueryFilterPipe::OPERATION_AND): self
    {
        foreach ($filters as $filter) {
            $this->addFilter($filter, $operation);
        }
        
        return $this;
    }

    public function createGroupsFilter(array $filterParams, array $conditionalFilters = []): QueryFilterGroupPipe
    {
        $groupsFilter = new QueryFilterGroupPipe(QueryFilterPipe::OPERATION_OR, []);

        foreach (($filterParams['groups'] ?? []) as $groupParams) {
            $group = $this->createSingleGroupFilter($groupParams, $conditionalFilters);
            if (!$group->isEmpty()) {
                $groupsFilter = new QueryFilterGroupPipe(
                    QueryFilterPipe::OPERATION_OR,
                    array_merge($groupsFilter->getFilters()->toArray(), [$group])
                );
            }
        }

        return $groupsFilter;
    }

    public function createSingleGroupFilter(array $groupParams, array $conditionalFilters = []): QueryFilterGroupPipe
    {
        $group = new QueryFilterGroupPipe(QueryFilterPipe::OPERATION_AND, []);
        $groupFilters = [];

        foreach ($groupParams as $filterKey => $filterValues) {
            if (!empty($filterValues)) {
                $filter = $this->itemProvider->getFilter($filterKey, $filterValues);
                $groupFilters[] = $filter;
            }
        }

        foreach ($conditionalFilters as $conditionalFilter) {
            if ($this->shouldApplyConditionalFilter($conditionalFilter, $groupFilters)) {
                $groupFilters[] = $conditionalFilter['filter'];
            }
        }

        if (!empty($groupFilters)) {
            $group = new QueryFilterGroupPipe(QueryFilterPipe::OPERATION_AND, $groupFilters);
        }

        return $group;
    }


    public function withParams(array $filterParams = []): self
    {
        foreach ($filterParams as $filterKey => $filterValues) {
            if (!empty($filterValues) && $this->itemProvider->isValidFilterKey($filterKey)) {
                $filter = $this->itemProvider->getFilter($filterKey, $filterValues);
                $this->addFilter($filter);
            }
        }
        
        return $this;
    }

    public function applyFilters(QueryFilterGroupPipe $filters): self
    {
        $this->query = app(Pipeline::class)
            ->send($this->query)
            ->through($filters)
            ->thenReturn();

        return $this;
    }

    public function applyBuiltFilters(): self
    {
        if ($this->hasFilters()) {
            $filters = $this->getBuiltStructure();
            $this->applyFilters($filters);
        }
        
        return $this;
    }

 
    public function getQuery(): Builder
    {
        return $this->query;
    }

    /**
     * Get the built query with all filters applied
     */
    public function getBuiltQuery(): Builder
    {
        $this->applyBuiltFilters();
        return $this->query;
    }

    public function getBuiltStructure(): QueryFilterPipe
    {
        if ($this->filters->isEmpty()) {
            throw new Exception('No filters have been added to this builder');
        }
        
        return $this->filters->first();
    }

    public function hasFilters(): bool
    {
        return $this->filters->isNotEmpty();
    }


    public function clear(): self
    {
        $this->filters = collect();
        $this->query = DB::table($this->baseTable);
        return $this;
    }


    public function getBaseTable(): string
    {
        return $this->baseTable;
    }

    /**
     * Get filter items for dropdowns/autocomplete
     */
    public function getFilterItems(string $filterKey, ?string $search = null): array
    {
        return $this->itemProvider->getFilterItems($filterKey, $search);
    }


    public function getItemProvider(): FilterItemProviderInterface
    {
        return $this->itemProvider;
    }


    private function shouldApplyConditionalFilter(array $conditionalFilter, array $groupFilters): bool
    {
        $condition = $conditionalFilter['condition'] ?? null;
        
        if (!$condition) {
            return false;
        }

        return match($condition['type']) {
            'requires_permission_filter' => $this->requiresPermissionFilter($groupFilters, $condition),
            'custom_callback' => $this->evaluateCustomCallback($condition, $groupFilters),
            default => false
        };
    }

    private function requiresPermissionFilter(array $filters, array $condition): bool
    {
        $requiredFilterTypes = $condition['required_filter_types'] ?? [];
        
        foreach ($filters as $filter) {
            foreach ($requiredFilterTypes as $requiredType) {
                if ($filter instanceof $requiredType) {
                    return true;
                }
            }
        }
        
        return false;
    }



    private function evaluateCustomCallback(array $condition, array $groupFilters): bool
    {
        $callback = $condition['callback'] ?? null;
        
        if (!$callback instanceof Closure) {
            return false;
        }
        
        return $callback($groupFilters);
    }
}
