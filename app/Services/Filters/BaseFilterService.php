<?php

declare(strict_types=1);

namespace App\Services\Filters;

use App\Services\Filters\Contracts\FilterItemProviderInterface;
use App\Types\Interfaces\Query\QueryFilterPipe;
use App\Pipes\Query\QueryFilterGroupPipe;
use App\Types\DTOs\Common\ListDTO;
use Closure;
use Illuminate\Database\Query\Builder;
use Illuminate\Pipeline\Pipeline;
use Illuminate\Support\Facades\DB;

/**
 * Base class for all filter services
 * Contains common functionality shared across ContractPlotFilter, OwnersFilter, PlotFilter, etc.
 *
 * @template TFilterProvider of FilterItemProviderInterface
 */
abstract class BaseFilterService
{
    /**
     * Unified filter builder that handles all building responsibilities:
     * - Incremental filter structure building
     * - Group filter building with conditional logic
     * - Query building and execution
     */
    protected FilterBuilder $filterBuilder;

    /**
     * The base table for this filter service
     *
     * @var non-empty-string
     */
    protected string $baseTable;

    public function __construct(string $baseTable)
    {
        $this->baseTable = $baseTable;

        $itemProvider = $this->createItemProvider();
        $this->filterBuilder = new FilterBuilder($baseTable, $itemProvider);
    }

    /**
     * Create the appropriate item provider for this filter service
     * Each subclass must implement this to return its specific provider
     *
     * @return TFilterProvider The filter item provider specific to this service
     */
    abstract protected function createItemProvider(): FilterItemProviderInterface;

    /**
     * Add a single filter with specified logic operation
     *
     * @param QueryFilterPipe $filter The filter to add
     * @param QueryFilterPipe::OPERATION_* $operation The logical operation (AND/OR) - defaults to AND
     * @return static Returns the same instance for method chaining
     *
     * @phpstan-param QueryFilterPipe::OPERATION_* $operation
     */
    public function addFilter(QueryFilterPipe $filter, string $operation = QueryFilterPipe::OPERATION_AND): self
    {
        $this->filterBuilder->addFilter($filter, $operation);
        return $this;
    }

    /**
     * Add a group of filters with their own internal logic
     *
     * @param Closure(static): void $callback Callback that receives a new filter service instance to build the group
     * @param QueryFilterPipe::OPERATION_* $operation The logical operation (AND/OR) to apply this group - defaults to AND
     * @return static Returns the same instance for method chaining
     *
     * @phpstan-param Closure(static): void $callback
     * @phpstan-param QueryFilterPipe::OPERATION_* $operation
     */
    public function addFilterGroup(Closure $callback, string $operation = QueryFilterPipe::OPERATION_AND): self
    {
        $this->filterBuilder->addFilterGroup(
            $callback,
            $operation,
            fn() => new static($this->baseTable) // Factory callback creates the same type as current instance
        );

        return $this;
    }

    /**
     * Apply filters from parameters using service-specific implementations
     * This is the main entry point for filtering
     *
     * @param array<string, mixed> $filterParams Associative array of filter parameters
     * @return static Returns the same instance for method chaining
     *
     * @phpstan-param array<non-empty-string, mixed> $filterParams
     */
    public function withParams(array $filterParams = []): self
    {
        $parameterFilters = $this->createParameterFilters($filterParams);
        $this->applyAllFilters($parameterFilters);
        return $this;
    }

    /**
     * Apply all filters (both manually added and parameter-based) to the query
     * This method combines manually added filters with parameter filters and applies them
     *
     * @param QueryFilterGroupPipe $parameterFilters The parameter-based filters
     * @return void
     */
    protected function applyAllFilters(QueryFilterGroupPipe $parameterFilters): void
    {
        if ($this->filterBuilder->hasFilters()) {
            $addedFilters = $this->filterBuilder->getBuiltStructure();
            $combinedFilters = new QueryFilterGroupPipe(
                QueryFilterPipe::OPERATION_AND,
                [$parameterFilters, $addedFilters]
            );
            $this->filterBuilder->applyFilters($combinedFilters);
        } else {
            $this->filterBuilder->applyFilters($parameterFilters);
        }
    }

    /**
     * Create parameter-based filters specific to each filter service
     * Default implementation creates a groups filter - subclasses can override for custom logic
     *
     * @param array $filterParams The filter parameters
     * @return QueryFilterGroupPipe The parameter filters group
     */
    protected function createParameterFilters(array $filterParams): QueryFilterGroupPipe
    {
        $groupsFilter = $this->createGroupsFilter($filterParams, []);

        return new QueryFilterGroupPipe(
            QueryFilterPipe::OPERATION_AND,
            [
                $groupsFilter
            ]
        );
    }

    /**
     * Get filter items for dropdowns/autocomplete
     *
     * @param string $filterKey The filter key
     * @param string|null $search Optional search term
     * @return array Array of filter items
     */
    public function getFilterItems(string $filterKey, ?string $search = null): array
    {
        return $this->filterBuilder->getFilterItems($filterKey, $search);
    }

    /**
     * Check if a filter key is valid for this service
     *
     * @param string $filterKey The filter key to check
     * @return bool True if the filter key is valid
     */
    public function isValidFilterKey(string $filterKey): bool
    {
        return $this->filterBuilder->getItemProvider()->isValidFilterKey($filterKey);
    }

    /**
     * Get the context/service name this filter service is for
     *
     * @return string The context name
     */
    public function getContext(): string
    {
        return $this->filterBuilder->getItemProvider()->getContext();
    }

    /**
     * Get the current query builder with applied filters
     *
     * @return Builder
     */
    public function getQuery(): Builder
    {
        return $this->filterBuilder->getBuiltQuery();
    }

    /**
     * Check if any filters have been added
     *
     * @return bool True if filters have been added
     */
    public function hasFilters(): bool
    {
        return $this->filterBuilder->hasFilters();
    }

    /**
     * Clear all filters
     *
     * @return $this
     */
    public function clearFilters(): self
    {
        $this->filterBuilder->clear();
        return $this;
    }

    /**
     * Create groups filter with conditional filters (available to all services)
     *
     * @param array $filterParams The filter parameters containing 'groups'
     * @param array $conditionalFilters Array of conditional filters to apply
     * @return \App\Pipes\Query\QueryFilterGroupPipe
     */
    protected function createGroupsFilter(array $filterParams, array $conditionalFilters = []): \App\Pipes\Query\QueryFilterGroupPipe
    {
        return $this->filterBuilder->createGroupsFilter($filterParams, $conditionalFilters);
    }

    /**
     * Create a single group filter with conditional filters (available to all services)
     *
     * @param array $groupParams Parameters for this specific group
     * @param array $conditionalFilters Array of conditional filters to apply
     * @return \App\Pipes\Query\QueryFilterGroupPipe
     */
    protected function createSingleGroupFilter(array $groupParams, array $conditionalFilters = []): \App\Pipes\Query\QueryFilterGroupPipe
    {
        return $this->filterBuilder->createSingleGroupFilter($groupParams, $conditionalFilters);
    }

    /**
     * Get the base table name
     *
     * @return string
     */
    public function getBaseTable(): string
    {
        return $this->baseTable;
    }

    /**
     * Execute the complete filter items query with search, pagination and aggregation.
     * This method is shared across all filter services that need paginated filter items.
     */
    protected function executeFilterItemsQuery(Builder $filterItemsQuery, ?string $search, ?int $page, ?int $limit, ?string $sort, ?string $order): array
    {
        $filterItemsJsonQuery = $this->buildFilterItemsJsonQuery();
        $searchedItemsQuery = $this->buildSearchedItemsQuery($search, $sort, $order);
        $paginatedItemsQuery = $this->buildPaginatedItemsQuery($page, $limit);
        $rowsQuery = $this->buildRowsQuery();
        $totalQuery = $this->buildTotalQuery();

        $result = DB::table('rows')
            ->withExpression('filter_items', $filterItemsQuery)
            ->withExpression('filter_items_json', $filterItemsJsonQuery)
            ->withExpression('searched_items', $searchedItemsQuery)
            ->withExpression('paginated_items', $paginatedItemsQuery)
            ->withExpression('rows', $rowsQuery)
            ->withExpression('total', $totalQuery)
            ->crossJoin('total')
            ->selectRaw("
                JSONB_BUILD_OBJECT(
                    'rows', COALESCE(rows.data, '[]'::JSONB),
                    'total', COALESCE(total.data, 0)
                ) AS data
            ")
            ->first();

        return json_decode($result->data, true);
    }

    /**
     * Build the filter items JSON query.
     */
    protected function buildFilterItemsJsonQuery(): Builder
    {
        return DB::table('filter_items')
            ->selectRaw('ROW_TO_JSON(filter_items)::JSONB AS item');
    }

    /**
     * Build the searched items query with optional search and sorting.
     */
    protected function buildSearchedItemsQuery(?string $search, ?string $sort, ?string $order): Builder
    {
        return DB::table('filter_items_json')
            ->selectRaw('filter_items_json.item')
            ->when(
                $search,
                fn ($q) => $q->whereRaw("filter_items_json.item->>'value' ILIKE ?", ["%{$search}%"])
            )
            ->when(
                $sort && $order,
                fn ($q) => $q->orderByRaw("{$sort} {$order}")
            );
    }

    /**
     * Build the paginated items query.
     */
    protected function buildPaginatedItemsQuery(?int $page, ?int $limit): Builder
    {
        return DB::table('searched_items')
            ->selectRaw('searched_items.item')
            ->when(
                $page && $limit,
                fn ($q) => $q->limit($limit)->offset(($page - 1) * $limit)
            );
    }

    /**
     * Build the rows aggregation query.
     */
    protected function buildRowsQuery(): Builder
    {
        return DB::table('paginated_items')
            ->selectRaw('JSONB_AGG(item) AS data');
    }

    /**
     * Build the total count query.
     */
    protected function buildTotalQuery(): Builder
    {
        return DB::table('searched_items')
            ->selectRaw('COUNT(*) AS data');
    }

    /**
     * Get paginated filter items with advanced options
     * Common implementation for all filter services
     */
    public function getFilterItemsPaginated(string $filterKey, ?string $search, ?int $page, ?int $limit, ?string $sort = "item->>'value'", ?string $order = 'asc'): ListDTO
    {
        $filter = $this->filterBuilder->getItemProvider()->getFilter($filterKey);
        $filterItemsQuery = $this->buildFilterItemsQuery($filter);

        $result = $this->executeFilterItemsQuery($filterItemsQuery, $search, $page, $limit, $sort, $order);

        return new ListDTO($result);
    }

    /**
     * Build the base filter items query using the filter pipe
     * Flexible implementation that works with both direct method calls and Pipeline
     */
    protected function buildFilterItemsQuery($filter): Builder
    {
        $query = $this->getQuery();

        if (method_exists($filter, 'handle')) {
            return app(Pipeline::class)
                ->send($query)
                ->through($filter)
                ->via('handleFilterItems')
                ->thenReturn();
        }

        if (method_exists($filter, 'handleFilterItems')) {
            $filter->handleFilterItems($query, fn($q) => $q);
        }

        return $query;
    }


}
