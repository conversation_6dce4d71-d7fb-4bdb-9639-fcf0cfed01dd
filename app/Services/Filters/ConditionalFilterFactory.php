<?php

declare(strict_types=1);

namespace App\Services\Filters;

use App\Types\Interfaces\Query\QueryFilterPipe;
use Closure;

/**
 * Factory for creating conditional filters that can be applied based on specific conditions
 * This allows services to define their own conditional logic while reusing the group building logic
 */
class ConditionalFilterFactory
{
    /**
     * Create a conditional filter that applies when specific filter types are present
     * 
     * @param QueryFilterPipe $filter The filter to apply conditionally
     * @param array $requiredFilterTypes Array of filter class names that trigger this condition
     * @return array Conditional filter configuration
     */
    public static function requiresPermissionFilter(QueryFilterPipe $filter, array $requiredFilterTypes): array
    {
        return [
            'filter' => $filter,
            'condition' => [
                'type' => 'requires_permission_filter',
                'required_filter_types' => $requiredFilterTypes
            ]
        ];
    }



    /**
     * Create a conditional filter with custom callback logic
     * 
     * @param QueryFilterPipe $filter The filter to apply conditionally
     * @param Closure $callback Custom callback that receives group filters and returns bool
     * @return array Conditional filter configuration
     */
    public static function customCallback(QueryFilterPipe $filter, Closure $callback): array
    {
        return [
            'filter' => $filter,
            'condition' => [
                'type' => 'custom_callback',
                'callback' => $callback
            ]
        ];
    }


}
