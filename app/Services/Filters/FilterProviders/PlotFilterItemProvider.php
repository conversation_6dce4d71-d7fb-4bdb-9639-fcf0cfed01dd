<?php

declare(strict_types=1);

namespace App\Services\Filters\FilterProviders;

use App\Services\Filters\Contracts\FilterItemProviderInterface;
use App\Types\Interfaces\ContractPlot\ContractPlotFilterPipe;
use InvalidArgumentException;

/**
 * Filter item provider specifically for PlotFilter service
 * Handles filter keys with plot-specific pipe implementations
 * 
 * Note: This is an example implementation. You would create actual
 * plot-specific filter pipes in app/Pipes/PlotFilter/ directory
 */
class PlotFilterItemProvider implements FilterItemProviderInterface
{
    /**
     * Get filter items for dropdowns/autocomplete
     * 
     * @param string $filterKey The filter key to get items for
     * @param string|null $search Optional search term to filter items
     * @return array Array of filter items
     */
    public function getFilterItems(string $filterKey, ?string $search = null): array
    {
        $filter = $this->getFilter($filterKey);
        
        if (!method_exists($filter, 'getFilterItems')) {
            return [];
        }
        
        return $filter->getFilterItems($search);
    }

    /**
     * Get a filter instance by key
     *
     * @param string $filterKey The filter key
     * @param mixed $filterValues Optional filter values
     * @return ContractPlotFilterPipe The filter instance
     * @throws InvalidArgumentException If the filter key is unknown
     */
    public function getFilter(string $filterKey, $filterValues = null): mixed
    {
        return match($filterKey) {
            'search' => new \App\Pipes\ContractPlotFilter\Plot\PlotSearchPipe($filterValues),
            'available_area_calculation' => new \App\Pipes\ContractPlotFilter\Plot\AvailableAreaCalculationPipe($filterValues),

            default => throw new InvalidArgumentException("Unknown filter key for Plot context: {$filterKey}")
        };
    }

    /**
     * Check if a filter key is valid by attempting to create the filter
     * 
     * @param string $filterKey The filter key to check
     * @return bool True if the filter key is valid
     */
    public function isValidFilterKey(string $filterKey): bool
    {
        try {
            $this->getFilter($filterKey);
            return true;
        } catch (InvalidArgumentException) {
            return false;
        }
    }

    /**
     * Get the context/service name this provider is for
     * 
     * @return string The context name
     */
    public function getContext(): string
    {
        return 'plots';
    }
}
