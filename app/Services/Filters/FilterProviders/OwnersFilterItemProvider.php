<?php

declare(strict_types=1);

namespace App\Services\Filters\FilterProviders;

use App\Services\Filters\Contracts\FilterItemProviderInterface;
use InvalidArgumentException;

/**
 * Filter item provider for Owners context
 * Provides filters specific to owner search and filtering operations
 */
class OwnersFilterItemProvider implements FilterItemProviderInterface
{
    /**
     * Get filter items for dropdowns/autocomplete
     * 
     * @param string $filterKey The filter key to get items for
     * @param string|null $search Optional search term to filter items
     * @return array Array of filter items
     */
    public function getFilterItems(string $filterKey, ?string $search = null): array
    {
        $filter = $this->getFilter($filterKey);
        
        if (!method_exists($filter, 'getFilterItems')) {
            return [];
        }
        
        return $filter->getFilterItems($search);
    }

    /**
     * Get a filter instance by key
     *
     * @param string $filterKey The filter key
     * @param mixed $filterValues Optional filter values
     * @return mixed The filter instance
     * @throws InvalidArgumentException If the filter key is unknown
     */
    public function getFilter(string $filterKey, $filterValues = null): mixed
    {
        return match($filterKey) {
            'search' => new \App\Pipes\Owners\OwnerMultiFieldSearchFilterPipe($filterValues),
            'contragent_phone' => new \App\Pipes\Owners\ContragentPhoneFilterPipe($filterValues),
            'contragent_comment' => new \App\Pipes\Owners\ContragentCommentFilterPipe($filterValues),
            'cnum' => new \App\Pipes\ContractPlotFilter\Contract\CnumFilterPipe($filterValues),
            'kad_ident' => new \App\Pipes\ContractPlotFilter\Plot\KadIdentFilterPipe($filterValues),
            'virtual_ekatte_name' => new \App\Pipes\ContractPlotFilter\Plot\VirtualEkatteNameFilterPipe($filterValues),

            default => throw new InvalidArgumentException("Unknown filter key for Owners context: {$filterKey}")
        };
    }

    /**
     * Check if a filter key is valid
     *
     * @param string $filterKey The filter key to check
     * @return bool True if the filter key is valid
     */
    public function isValidFilterKey(string $filterKey): bool
    {
        try {
            $this->getFilter($filterKey);
            return true;
        } catch (InvalidArgumentException) {
            return false;
        }
    }

    /**
     * Get the context/service name this provider is for
     * 
     * @return string The context name
     */
    public function getContext(): string
    {
        return 'owners';
    }
}
