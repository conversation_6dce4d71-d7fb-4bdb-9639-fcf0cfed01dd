<?php

declare(strict_types=1);

namespace App\Services\Filters\FilterProviders;

use App\Services\Filters\Contracts\FilterItemProviderInterface;
use App\Types\Interfaces\ContractPlot\ContractPlotFilterPipe;
use InvalidArgumentException;

/**
 * Filter item provider specifically for ContractPlotFilter service
 * Handles filter keys with contract-plot specific pipe implementations
 *
 * Supported filter keys:
 * - kad_ident: Plot cadastral identifier
 * - plot_search: Multi-field plot search
 * - virtual_ekatte_name: Settlement name
 * - contract_ids: Specific contract IDs
 * - owner_search: Owner multi-field search
 * - farming_year: Farming year filter
 * - has_decreased_area_next_year: Contract renewal area check
 * - can_renew: Contract renewability check
 */
class ContractPlotFilterItemProvider implements FilterItemProviderInterface
{
    /**
     * Get filter items for dropdowns/autocomplete
     *
     * @param non-empty-string $filterKey The filter key to get items for
     * @param string|null $search Optional search term to filter items
     * @return array<int, array{id: mixed, name: string, ...}> Array of filter items with id/name structure
     *
     * @phpstan-param non-empty-string $filterKey
     * @phpstan-return array<int, array{id: mixed, name: string, ...}>
     */
    public function getFilterItems(string $filterKey, ?string $search = null): array
    {
        $filter = $this->getFilter($filterKey);
        
        if (!method_exists($filter, 'getFilterItems')) {
            return [];
        }
        
        return $filter->getFilterItems($search);
    }

    /**
     * Get a filter instance by key - ContractPlot context implementations
     *
     * @param non-empty-string $filterKey The filter key (see class docblock for supported keys)
     * @param mixed $filterValues Optional filter values (string, array, int, etc.)
     * @return ContractPlotFilterPipe The filter instance specific to ContractPlot context
     * @throws InvalidArgumentException If the filter key is unknown
     *
     * @phpstan-param non-empty-string $filterKey
     * @phpstan-return ContractPlotFilterPipe
     */
    public function getFilter(string $filterKey, $filterValues = null): mixed
    {
        return match($filterKey) {
            'kad_ident' => new \App\Pipes\ContractPlotFilter\Plot\KadIdentFilterPipe($filterValues),
            'plot_search' => new \App\Pipes\ContractPlotFilter\Plot\PlotSearchPipe($filterValues),
            'virtual_ekatte_name' => new \App\Pipes\ContractPlotFilter\Plot\VirtualEkatteNameFilterPipe($filterValues),
            'masiv' => new \App\Pipes\ContractPlotFilter\Plot\MasivFilterPipe($filterValues),
            'number' => new \App\Pipes\ContractPlotFilter\Plot\NumberFilterPipe($filterValues),
            'virtual_category_title' => new \App\Pipes\ContractPlotFilter\Plot\VirtualCategoryTitleFilterPipe($filterValues),
            'virtual_ntp_title' => new \App\Pipes\ContractPlotFilter\Plot\VirtualNtpTitleFilterPipe($filterValues),
            'mestnost' => new \App\Pipes\ContractPlotFilter\Plot\MestnostFilterPipe($filterValues),
            'block' => new \App\Pipes\ContractPlotFilter\Plot\BlockFilterPipe($filterValues),
            'allowable_type' => new \App\Pipes\ContractPlotFilter\Plot\AllowableTypeFilterPipe($filterValues),
            'irrigated_area' => new \App\Pipes\ContractPlotFilter\Plot\IrrigatedAreaFilterPipe($filterValues),
            'participation' => new \App\Pipes\ContractPlotFilter\Plot\ParticipationFilterPipe($filterValues),
            'comment' => new \App\Pipes\ContractPlotFilter\Plot\CommentFilterPipe($filterValues),
            'plot_id' => new \App\Pipes\ContractPlotFilter\Plot\PlotIdFilterPipe($filterValues),
            'virtual_contract_type' => new \App\Pipes\ContractPlotFilter\Plot\VirtualContractTypeFilterPipe($filterValues),
            'cnum' => new \App\Pipes\ContractPlotFilter\Contract\CnumFilterPipe($filterValues),
            'group' => new \App\Pipes\ContractPlotFilter\Contract\ContractGroupFilterPipe($filterValues),
            'contract_ids' => new \App\Pipes\ContractPlotFilter\Contract\ContractIdsFilterPipe($filterValues),
            'notary_number' => new \App\Pipes\ContractPlotFilter\Contract\NotaryNumberFilterPipe($filterValues),
            'contract_comment' => new \App\Pipes\ContractPlotFilter\Contract\ContractCommentFilterPipe($filterValues),
            'closed_for_editing' => new \App\Pipes\ContractPlotFilter\Contract\ContractClosedForEditingFilterPipe($filterValues),
            'contract_date_from' => new \App\Pipes\ContractPlotFilter\Contract\ContractDateFromFilterPipe($filterValues),
            'contract_date_to' => new \App\Pipes\ContractPlotFilter\Contract\ContractDateToFilterPipe($filterValues),
            'contract_status_text' => new \App\Pipes\ContractPlotFilter\Contract\ContractStatusTextFilterPipe($filterValues),
            'farming_name' => new \App\Pipes\ContractPlotFilter\Contract\FarmingNameFilterPipe($filterValues),
            'farming_years' => new \App\Pipes\ContractPlotFilter\Contract\FarmingYearsFilterPipe($filterValues),
            'farming_year' => new \App\Pipes\ContractPlotFilter\Contract\FarmingYearsFilterPipe([$filterValues]),
            'contract_types' => new \App\Pipes\ContractPlotFilter\Contract\ContractTypeFilterPipe($filterValues),
            'from_sublease' => new \App\Pipes\ContractPlotFilter\Contract\FromSubleaseFilterPipe($filterValues),
            'rent_type' => new \App\Pipes\ContractPlotFilter\Rent\RentTypeFilterPipe($filterValues),
            'specific_rent_type' => new \App\Pipes\ContractPlotFilter\Rent\SpecificRentTypeFilterPipe($filterValues),
            'rent_kind_type' => new \App\Pipes\ContractPlotFilter\Rent\RentKindTypeFilterPipe($filterValues),
            'creditor' => new \App\Pipes\Owners\CreditorFilterPipe($filterValues),
            'contragent' => new \App\Pipes\Owners\ContragentFilterPipe($filterValues),
            'buyer' => new \App\Pipes\Owners\BuyerFilterPipe($filterValues),
            'tenant' => new \App\Pipes\Owners\TenantFilterPipe($filterValues),
            'contragent_type' => new \App\Pipes\Owners\ContragentTypeFilterPipe($filterValues),
            'contragent_phone' => new \App\Pipes\Owners\ContragentPhoneFilterPipe($filterValues),
            'contragent_comment' => new \App\Pipes\Owners\ContragentCommentFilterPipe($filterValues),
            'contract_signer' => new \App\Pipes\Owners\ContractSignerFilterPipe($filterValues),
            'owner_name' => new \App\Pipes\Owners\OwnerNameFilterPipe($filterValues),
            'owner_egn' => new \App\Pipes\Owners\OwnerEgnFilterPipe($filterValues),
            'owner_name_egn' => new \App\Pipes\Owners\OwnerNameEgnFilterPipe($filterValues),
            'company_name' => new \App\Pipes\Owners\CompanyNameFilterPipe($filterValues),
            'company_eik' => new \App\Pipes\Owners\CompanyEikFilterPipe($filterValues),
            'heritor_name' => new \App\Pipes\Owners\HeritorNameFilterPipe($filterValues),
            'heritor_egn' => new \App\Pipes\Owners\HeritorEgnFilterPipe($filterValues),
            'rep_egn' => new \App\Pipes\Owners\RepresentativeEgnFilterPipe($filterValues),
            'rep_name' => new \App\Pipes\Owners\RepresentativeNameFilterPipe($filterValues),
            'person_name' => new \App\Pipes\Owners\PersonNameFilterPipe($filterValues),
            'person_egn' => new \App\Pipes\Owners\PersonEgnFilterPipe($filterValues),
            'egn_subekt' => new \App\Pipes\Owners\OwnerOSZEgnFilterPipe($filterValues),
            'osz_ime_subekt' => new \App\Pipes\Owners\OwnerOSZNameFilterPipe($filterValues),
            'owner_multi_field_search' => new \App\Pipes\Owners\OwnerMultiFieldFilterPipe($filterValues),
            'incomplete_ownership_details' => new \App\Pipes\Owners\IncompleteOwnershipDetailsFilterPipe($filterValues),
            'farm_year_active_contract_plots' => new \App\Pipes\ContractPlotFilter\FarmingYear\ActiveContractPlotsByFarmingYearFilterPipe($filterValues),
            'expiring_contracts_for_farm_year' => new \App\Pipes\ContractPlotFilter\FarmingYear\ExpiringContractPlotsByFarmingYearFilterPipe($filterValues),
            'for_sublease_farm_years' => new \App\Pipes\ContractPlotFilter\FarmingYear\ForSubleaseContractPlotsByFarmingYearFilterPipe($filterValues),
            'has_decreased_area_next_year' => new \App\Pipes\ContractPlotFilter\ContractRenew\HasDecreasedAreaNextYearFilterPipe($filterValues),
            'can_renew' => new \App\Pipes\ContractPlotFilter\ContractRenew\CanRenewFilterPipe($filterValues),
            'can_renew_contract' => new \App\Pipes\ContractPlotFilter\ContractRenew\CanRenewContractFilterPipe($filterValues),

            default => throw new InvalidArgumentException("Unknown filter key for ContractPlot context: {$filterKey}")
        };
    }

    /**
     * Check if a filter key is valid by attempting to create the filter
     * 
     * @param string $filterKey The filter key to check
     * @return bool True if the filter key is valid
     */
    public function isValidFilterKey(string $filterKey): bool
    {
        try {
            $this->getFilter($filterKey);
            return true;
        } catch (InvalidArgumentException) {
            return false;
        }
    }

    /**
     * Get the context/service name this provider is for
     * 
     * @return string The context name
     */
    public function getContext(): string
    {
        return 'contract_plot';
    }
}
