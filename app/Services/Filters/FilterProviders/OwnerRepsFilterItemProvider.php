<?php

declare(strict_types=1);

namespace App\Services\Filters\FilterProviders;

use App\Services\Filters\Contracts\FilterItemProviderInterface;
use App\Pipes\Owners\OwnerRepsMultiFieldSearchFilterPipe;
use App\Types\Interfaces\Owners\OwnerFilterPipe;
use InvalidArgumentException;

/**
 * Filter item provider specifically for OwnerRepsFilter service
 * Handles filter keys with owner representatives specific pipe implementations
 */
class OwnerRepsFilterItemProvider implements FilterItemProviderInterface
{
    /**
     * Get filter items for dropdowns/autocomplete
     * 
     * @param string $filterKey The filter key to get items for
     * @param string|null $search Optional search term to filter items
     * @return array Array of filter items
     */
    public function getFilterItems(string $filterKey, ?string $search = null): array
    {
        $filter = $this->getFilter($filterKey);
        
        if (!method_exists($filter, 'getFilterItems')) {
            return [];
        }
        
        return $filter->getFilterItems($search);
    }

    /**
     * Get a filter instance by key
     *
     * @param string $filterKey The filter key
     * @param mixed $filterValues Optional filter values
     * @return mixed The filter instance
     * @throws InvalidArgumentException If the filter key is unknown
     */
    public function getFilter(string $filterKey, $filterValues = null): mixed
    {
        $filter = match($filterKey) {
            'search' => new OwnerRepsMultiFieldSearchFilterPipe(),

            default => throw new InvalidArgumentException("Unknown filter key for OwnerReps context: {$filterKey}")
        };
        if ($filterValues !== null && method_exists($filter, 'withParams')) {
            $filter->withParams([$filterKey => $filterValues]);
        }

        return $filter;
    }

    /**
     * Check if a filter key is valid by attempting to create the filter
     * 
     * @param string $filterKey The filter key to check
     * @return bool True if the filter key is valid
     */
    public function isValidFilterKey(string $filterKey): bool
    {
        try {
            $this->getFilter($filterKey);
            return true;
        } catch (InvalidArgumentException) {
            return false;
        }
    }

    /**
     * Get the context/service name this provider is for
     * 
     * @return string The context name
     */
    public function getContext(): string
    {
        return 'owner_reps';
    }
}
