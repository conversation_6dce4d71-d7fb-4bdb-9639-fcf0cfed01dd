<?php

declare(strict_types=1);

namespace App\Services\Filters\Contracts;

use App\Types\Interfaces\Query\QueryFilterPipe;

/**
 * Interface for filter item providers
 * Each service (ContractPlotFilter, OwnersFilter, etc.) should have its own implementation
 * to handle the same filter keys with different pipe implementations
 */
interface FilterItemProviderInterface
{
    /**
     * Get filter items for dropdowns/autocomplete
     *
     * @param non-empty-string $filterKey The filter key to get items for
     * @param string|null $search Optional search term to filter items
     * @return array<int, array{id: mixed, name: string, ...}> Array of filter items with id/name structure
     *
     * @phpstan-param non-empty-string $filterKey
     * @phpstan-return array<int, array{id: mixed, name: string, ...}>
     */
    public function getFilterItems(string $filterKey, ?string $search = null): array;

    /**
     * Get a filter instance by key
     *
     * @param non-empty-string $filterKey The filter key
     * @param mixed $filterValues Optional filter values (can be string, array, int, etc.)
     * @return QueryFilterPipe The filter instance (specific pipe implementation)
     * @throws \InvalidArgumentException If the filter key is unknown
     *
     * @phpstan-param non-empty-string $filterKey
     * @phpstan-return QueryFilterPipe
     */
    public function getFilter(string $filterKey, $filterValues = null): mixed;

    /**
     * Check if a filter key is valid by attempting to create the filter
     * 
     * @param string $filterKey The filter key to check
     * @return bool True if the filter key is valid
     */
    public function isValidFilterKey(string $filterKey): bool;

    /**
     * Get the context/service name this provider is for
     * 
     * @return string The context name (e.g., 'contract_plot', 'owners', 'plots')
     */
    public function getContext(): string;
}
