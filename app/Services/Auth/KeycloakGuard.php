<?php

declare(strict_types=1);

namespace App\Services\Auth;

use App\Services\Auth\Exceptions\TokenException;
use App\Services\Auth\Exceptions\UserNotFoundException;
use Exception;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Contracts\Auth\UserProvider;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use League\OAuth2\Server\Exception\OAuthServerException;
use LogicException;
use UnexpectedValueException;

class KeycloakGuard implements Guard
{
    /**
     * @var array<string, string>
     */
    private $config;
    private ?Authenticatable $user = null;
    private ?KeycloakToken $decodedToken = null;

    public function __construct(private readonly ?UserProvider $provider, private readonly Request $request, private readonly KeycloakResourceServer $resourceServer)
    {
        $this->config = config('keycloak');

        $this->authenticate();
    }

    /**
     * Determine if the current user is authenticated.
     *
     * @return bool
     */
    public function check()
    {
        return ! is_null($this->user());
    }

    /**
     * Determine if the guard has a user instance.
     *
     * @return bool
     */
    public function hasUser()
    {
        return ! is_null($this->user());
    }

    /**
     * Determine if the current user is a guest.
     *
     * @return bool
     */
    public function guest()
    {
        return ! $this->check();
    }

    /**
     * Set the current user.
     */
    public function setUser(Authenticatable $user)
    {
        $this->user = $user;

        return $this;
    }

    /**
     * Get the currently authenticated user.
     */
    public function user(): ?Authenticatable
    {
        if (is_null($this->user)) {
            return null;
        }

        return $this->user->withAccessToken($this->decodedToken);
    }

    /**
     * Get the ID for the currently authenticated user.
     */
    public function id(): ?int
    {
        if ($user = $this->user()) {
            return $user->id;
        }

        return null;
    }

    /**
     * Validate a user's credentials.
     *
     * @param array<string, mixed> $credentials
     */
    public function validate(array $credentials = []): bool
    {
        if (! $user = $this->provider->retrieveByCredentials($credentials)) {
            throw new UserNotFoundException('User not found. Credentials: ' . json_encode([$credentials, $this->provider::class]));
        }

        $this->setUser($user->withAccessToken($this->decodedToken));

        return true;
    }

    /**
     * Decode token, validate and authenticate user.
     */
    private function authenticate(): void
    {
        try {
            $this->decodedToken = $this->resourceServer->getTokenValidator()->verifyToken(
                $this->request
            );
        } catch (OAuthServerException $e) {
            // inactive user sessions, no bearer token, disabled users
            Log::error($e->getMessage());
        } catch (LogicException $e) {
            // errors having to do with environmental setup or malformed JWT Keys
            Log::error($e->getMessage());
        } catch (UnexpectedValueException $e) {
            // errors having to do with JWT signature and claims
            Log::error($e->getMessage());
        } catch (Exception $e) {
            throw new TokenException($e->getMessage(), $e->getCode(), $e);
        }

        if ($this->decodedToken instanceof KeycloakToken) {
            $this->validate([
                $this->config['user_provider_credential'] => $this->decodedToken->{$this->config['token_principal_attribute']},
            ]);
        }
    }
}
