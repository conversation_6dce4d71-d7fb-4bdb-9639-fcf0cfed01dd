<?php

declare(strict_types=1);

namespace App\Services\Auth\Exceptions;

use Exception;
use Illuminate\Support\Arr;

class KeycloakGuardException extends Exception
{
    protected $code = 401;

    public function render()
    {
        return response(config('app.debug') ? [
            'error' => 'Keycloak Guard',
            'error_description' => $this->getMessage(),
            'message' => $this->getMessage(),
            'exception' => static::class,
            'file' => $this->getFile(),
            'line' => $this->getLine(),
            'trace' => collect($this->getTrace())->map(fn ($trace) => Arr::except($trace, ['args']))->all(),
        ] : [
            'error' => 'Keycloak Guard',
            'error_description' => $this->getMessage(),
        ], $this->code);
    }
}
