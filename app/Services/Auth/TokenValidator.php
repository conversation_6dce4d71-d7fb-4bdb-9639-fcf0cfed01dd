<?php

declare(strict_types=1);

namespace App\Services\Auth;

use Illuminate\Http\Request;
use League\OAuth2\Server\CryptKey;
use League\OAuth2\Server\Exception\OAuthServerException;

class TokenValidator
{
    protected CryptKey $publicKey;

    /**
     * @var callable
     */
    protected $introspectTokenCallable;

    public function setPublicKey(CryptKey $key): void
    {
        $this->publicKey = $key;
    }

    public function setIntrospectTokenCallable(callable $callable): void
    {
        $this->introspectTokenCallable = $callable;
    }

    public function verifyToken(Request $request): KeycloakToken
    {
        if (! $request->bearerToken()) {
            throw OAuthServerException::accessDenied('Missing "Authorization" header');
        }

        $accesstToken = $this->introspectToken(
            $this->introspectTokenCallable,
            $request->bearerToken()
        );

        if (false === $accesstToken->active) {
            throw OAuthServerException::accessDenied('Invalid token');
        }

        return $accesstToken;
    }

    /**
     * Introspect the token.
     */
    public function introspectToken(callable $introspectTokenCallable, string $token): KeycloakToken
    {
        $decodedToken = $introspectTokenCallable($token);
        $decodedToken->access_token = $token;

        return new KeycloakToken(
            $decodedToken
        );
    }
}
