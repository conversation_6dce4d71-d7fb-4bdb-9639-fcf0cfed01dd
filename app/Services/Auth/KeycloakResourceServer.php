<?php

declare(strict_types=1);

namespace App\Services\Auth;

use App\Services\Auth\Traits\KeycloakUrlsTrait;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\ConnectException;
use GuzzleHttp\Exception\ServerException;
use GuzzleHttp\Exception\TooManyRedirectsException;

class KeycloakResourceServer
{
    use KeycloakUrlsTrait;
    /**
     * @var array<string, string>
     */
    private $config;
    private Client $httpClient;

    public function __construct(private TokenValidator $tokenValidator)
    {
        $this->config = config('keycloak');
        $this->httpClient = new Client([
            'base_uri' => $this->config['keycloak_base_url'],
        ]);

        $this->initTokenConfiguration();
    }

    /**
     * Exception handling and rethrowing is for debug reason.
     *
     * @see https://datatracker.ietf.org/doc/html/rfc7662
     * @see https://docs.guzzlephp.org/en/latest/quickstart.html#exceptions
     *
     * @throws Exception
     */
    public function introspectToken(string $accessToken): object
    {
        $credentials = base64_encode($this->config['keycloak_client_id'] . ':' . $this->config['keycloak_client_secret']);

        try {
            $response = $this->httpClient->request('POST', $this->getTokenIntrospectUrl(), [
                'headers' => [
                    'Authorization' => 'Basic ' . $credentials,
                ],
                'form_params' => [
                    'token' => $accessToken,
                ],
            ]);
        } catch (ClientException|ServerException $exception) {
            // BadResponseException (ServerException status 500 and ClientException status 400)
            $response = $exception->getResponse();

            throw new Exception($response->getReasonPhrase(), $response->getStatusCode(), $exception);
        } catch (TooManyRedirectsException $exception) {
            throw new Exception('Too many redirects', $exception->getCode(), $exception);
        } catch (ConnectException $exception) {
            $handlerContext = $exception->getHandlerContext();
            if ($handlerContext['errno'] ?? 0) {
                // this is the lowlevel error code, not the HTTP status code!!!
                // for example 6 for "Couldn't resolve host" (for libcurl)
                $errno = (int) ($handlerContext['errno']);
            }
            // get a description of the error
            $errorMessage = $handlerContext['error'] ?? $exception->getMessage();

            throw new Exception($errorMessage, $exception->getCode(), $exception);
        } catch (Exception $exception) {
            // fallback, in case of other exception
            throw $exception;
        }

        return (object) json_decode(
            $response->getBody()->getContents()
        );
    }

    public function getTokenValidator(): TokenValidator
    {
        return $this->tokenValidator;
    }

    private function initTokenConfiguration(): void
    {
        $this->tokenValidator->setIntrospectTokenCallable(
            $this->introspectToken(...)
        );
    }
}
