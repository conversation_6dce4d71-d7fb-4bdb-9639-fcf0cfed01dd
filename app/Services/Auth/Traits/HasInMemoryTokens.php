<?php

declare(strict_types=1);

namespace App\Services\Auth\Traits;

use App\Services\Auth\KeycloakToken;

trait HasInMemoryTokens
{
    protected ?KeycloakToken $accessToken;

    public function token(): ?KeycloakToken
    {
        return $this->accessToken;
    }

    /**
     * Determine if the current API token has a given scope.
     *
     * @return bool
     */
    public function tokenCan(string $scope)
    {
        return $this->accessToken ? $this->accessToken->can($scope) : false;
    }

    /**
     * Set the current access token for the user.
     *
     * @return $this
     */
    public function withAccessToken(?KeycloakToken $accessToken)
    {
        $this->accessToken = $accessToken;

        return $this;
    }
}
