<?php

declare(strict_types=1);

namespace App\Services\Auth\Traits;

trait KeycloakUrlsTrait
{
    private function getCertsUrl(): string
    {
        return $this->getBaseUrl() . '/protocol/openid-connect/certs';
    }

    private function getUserInfoUrl(): string
    {
        return $this->getBaseUrl() . '/protocol/openid-connect/userinfo';
    }

    private function getBaseUrl(): string
    {
        return 'realms/' . $this->config['realm'];
    }

    private function getTokenIntrospectUrl(): string
    {
        return $this->getBaseUrl() . '/protocol/openid-connect/token/introspect';
    }
}
