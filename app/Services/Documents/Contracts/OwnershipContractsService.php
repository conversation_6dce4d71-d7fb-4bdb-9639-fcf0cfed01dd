<?php

declare(strict_types=1);

namespace App\Services\Documents\Contracts;

use App\Types\Enums\Documents\ContractTypeEnum;
use App\Models\UserDb\Documents\Contracts\Contract;
use App\Types\DTOs\Common\ListDTO;
use App\Traits\Database\QueryHelper;
use App\Types\Enums\Documents\DocumentTypeEnum;
use Illuminate\Support\Facades\DB;
use InvalidArgumentException;

class OwnershipContractsService extends AbstractContractsService
{
    use QueryHelper;

    public function getContractsListFooter(array $contractIds): array
    {
        $areasByContractQuery = DB::table('su_sales_contracts AS c')
            ->selectRaw('
                c.id as contract_id
                , SUM(COALESCE(sscpr.contract_area, 0)) as contract_area
                , ROUND(
                    SUM(((sscpr.contract_area_for_sale * COALESCE(sscpr.price_per_acre, 0)) 
                    - (own_scpr.contract_area * COALESCE(own_scpr.price_per_acre, 0))))::numeric
                , 2) as plot_profit
        ')
            ->join('su_sales_contracts_plots_rel AS sscpr', 'sscpr.sales_contract_id', '=', 'c.id')
            ->join('layer_kvs AS lk', 'lk.gid', '=', 'sscpr.plot_id')
            ->join('su_contracts AS own_c', 'own_c.id', '=', 'sscpr.contract_id')
            ->join('su_contracts_plots_rel AS own_scpr', 'own_scpr.contract_id', '=', 'own_c.id')
            ->when(
                ! empty($contractIds),
                fn ($q) => $q->whereIn('c.id', $contractIds)
            )
            ->groupBy('c.id');

        $footer = DB::table('areas_by_contract')
            ->withExpression('areas_by_contract', $areasByContractQuery)
            ->selectRaw('
                SUM(contract_area) as total_contract_area,
                SUM(plot_profit) as total_plots_profit
            ')
            ->first();

        return (array) $footer;
    }

    public function getContracts(array $contractIds, array $allowedFarmingIds, ?int $page, ?int $limit, ?array $sort): array
    {
        $orderByExpr = $this->generateOrderByExpr($sort);
        $ownershipContractType = ContractTypeEnum::Ownership->value;
        $salesContractType = ContractTypeEnum::Sales->value;
        $salesContractName = ContractTypeEnum::Sales->name();
        $mortgageContractType = ContractTypeEnum::Mortgage->value;
        $mortgageContractName = ContractTypeEnum::Mortgage->name();

        // Main contracts query
        $contractsQuery = Contract::selectRaw("
            ROW_NUMBER() OVER ({$orderByExpr}) as row_num,
            su_contracts.id,
            su_contracts.c_num, -- Contract number
            su_contracts.nm_usage_rights AS c_type, -- Contract type
            TO_CHAR(su_contracts.c_date, 'DD.MM.YYYY') AS c_date,
            TO_CHAR(su_contracts.start_date, 'DD.MM.YYYY') AS start_date,
            TO_CHAR(su_contracts.due_date, 'DD.MM.YYYY') AS due_date,
            su_contracts.active,
            get_contract_status(su_contracts.id, su_contracts.active, su_contracts.start_date, su_contracts.due_date) AS status,
            su_contracts.virtual_contract_type AS type,
            su_contracts.is_sublease,
            su_contracts.from_sublease,
            case when su_contracts.from_sublease notnull then true else false end as is_from_sublease,
            su_contracts.is_annex,
            su_contracts.farming_name,
            su_contracts.overall_renta,
            su_contracts.total_contract_area,
            su_contracts.renta,
            su_contracts.renta_nat,
            su_contracts.osz_num, -- OSZ contract number 
            TO_CHAR(su_contracts.osz_date, 'DD.MM.YYYY') AS osz_date,-- OSZ contract date
            su_contracts.sv_num, -- Registry Agency contract number
            su_contracts.na_num, 
            su_contracts.tom,
            su_contracts.delo,
            su_contracts.court,
            su_contracts.comment,
            TO_CHAR(su_contracts.sv_date, 'DD.MM.YYYY') AS sv_date, -- Registry Agency date of contract
            COALESCE (
                    JSON_AGG(
                        JSON_BUILD_OBJECT(
                            'file_id', suf.id,
                            'file_name', suf.filename
                        ) 
                        ORDER BY suf.id
                    ) FILTER (WHERE suf.id IS NOT NULL),
                    '[]'::json
            ) AS files,
            COALESCE(
                JSON_AGG(
                    DISTINCT JSON_BUILD_OBJECT(
                    'name', srt.name,
                    'quantity', scr.renta_value,
                    'unit', sum.short_name)::JSONB 
                ) FILTER (WHERE scr.id NOTNULL), 
                '[]'::json
                ) AS renta_nat
        ")
            ->leftJoin('su_contracts_files_rel AS scfr', 'scfr.contract_id', '=', 'su_contracts.id')
            ->leftJoin('su_user_files AS suf', 'suf.id', '=', 'scfr.file_id')
            ->leftJoin('su_contracts_rents AS scr', 'scr.contract_id', '=', 'su_contracts.id')
            ->leftJoin('su_renta_types AS srt', 'srt.id', '=', 'scr.renta_id')
            ->leftJoin('su_units_of_measure AS sum', 'sum.id', '=', 'srt.unit')
            ->leftJoin(
                'su_contracts AS parent',
                fn ($q) => $q->on('parent.id', '=', 'su_contracts.parent_id')
                    ->whereIn('parent.farming_id', $allowedFarmingIds)
            )
            ->where('su_contracts.nm_usage_rights', $ownershipContractType)
            ->when(
                ! empty($contractIds),
                fn ($q) => $q->whereIn('su_contracts.id', $contractIds)
            )
            ->where('su_contracts.is_sublease', false)
            ->when(
                isset($page) && isset($limit),
                fn ($q) => $q->limit($limit)->offset(($page - 1) * $limit)
            )
            ->groupBy('su_contracts.id', 'parent.id');

        // Plots list query
        $plotsListQuery = DB::table('contracts AS c')
            ->selectRaw("
            scpr.contract_id
            , scpr.plot_id AS id
            , kad_ident
            , lk.virtual_ekatte_name AS ekatte_name
            , lk.virtual_ntp_title AS ntp_title
            -- temporary trim the category title to get some space for the other columns in FE documents drawer
            , TRIM(LEADING 'Категория ' FROM lk.virtual_category_title) AS category_title
            , COALESCE(scpr.area_for_rent, 0) AS area_for_rent
            , scpr.rent_per_plot
            , lk.allowable_area
            , scpr.contract_area
            , scpr.annex_action
            , CASE WHEN lk.is_edited = true AND lk.edit_active_from < now() THEN true
                   ELSE false
              END AS is_archive
            , lk.document_area 
            , scpr.comment
            , COALESCE(
                JSONB_AGG(
                    DISTINCT CASE 
                        WHEN active_sc.id IS NOT NULL AND NOT (active_sc.nm_usage_rights = {$ownershipContractType} AND active_sc.id = scpr.contract_id) THEN
                            JSONB_BUILD_OBJECT(
                                'id', active_sc.id,
                                'name', active_sc.c_num,
                                'c_type', active_sc.nm_usage_rights,
                                'type', active_sc.virtual_contract_type,
                                'contract_area', ROUND(active_scpr.contract_area::numeric, 3),
                                'is_sublease', COALESCE(active_sc.is_sublease, false),
                                'is_from_sublease', COALESCE(CASE WHEN active_sc.from_sublease IS NOT NULL THEN true ELSE false END, false),
                                'start_date', TO_CHAR(active_sc.start_date, 'DD.MM.YYYY'),
                                'due_date', TO_CHAR(active_sc.due_date, 'DD.MM.YYYY'),
                                'status', get_contract_status(active_sc.id, active_sc.active, active_sc.start_date, active_sc.due_date)::text
                            )
                        WHEN active_ssc.id IS NOT NULL THEN
                            JSONB_BUILD_OBJECT(
                                'id', active_ssc.id,
                                'name', active_ssc.c_num,
                                'c_type', {$salesContractType},
                                'type', '{$salesContractName}',
                                'contract_area', ROUND(active_sspr.contract_area_for_sale::numeric, 3),
                                'is_sublease', false,
                                'is_from_sublease', false,
                                'start_date', TO_CHAR(active_ssc.start_date, 'DD.MM.YYYY'),
                                'due_date', TO_CHAR(active_ssc.due_date, 'DD.MM.YYYY'),
                                'status', get_contract_status(active_ssc.id, active_ssc.active, active_ssc.start_date, active_ssc.due_date, false)::text
                            )
                        WHEN active_sh.id IS NOT NULL THEN
                            JSONB_BUILD_OBJECT(
                                'id', active_sh.id,
                                'name', active_sh.num,
                                'c_type', {$mortgageContractType},
                                'type', '{$mortgageContractName}',
                                'contract_area', ROUND(active_hpr.hypothec_area::numeric, 3),
                                'is_sublease', false,
                                'is_from_sublease', false,
                                'start_date', TO_CHAR(active_sh.start_date, 'DD.MM.YYYY'),
                                'due_date', TO_CHAR(active_sh.due_date, 'DD.MM.YYYY'),
                                'status', get_contract_status(active_sh.id, active_sh.is_active, active_sh.start_date, active_sh.due_date)::text
                            )
                    END
                ) FILTER (WHERE 
                    CASE 
                        WHEN active_sc.id IS NOT NULL AND NOT (active_sc.nm_usage_rights = {$ownershipContractType} AND active_sc.id = scpr.contract_id) THEN true
                        WHEN active_ssc.id IS NOT NULL THEN true
                        WHEN active_sh.id IS NOT NULL THEN true
                        ELSE false
                    END
                ),
                '[]'::jsonb
            ) AS contracts,
            JSONB_BUILD_ARRAY(
                JSONB_BUILD_OBJECT('title', 'own', 'value', COALESCE(scpr.contract_area, 0)),
                JSONB_BUILD_OBJECT('title', 'allowable', 'value', COALESCE(lk.allowable_area, 0)),
                JSONB_BUILD_OBJECT('title', 'by document', 'value', COALESCE(lk.document_area, 0))
            ) as plot_areas
        ")
            ->join('su_contracts_plots_rel AS scpr', 'scpr.contract_id', '=', 'c.id')
            ->join('layer_kvs AS lk', 'lk.gid', '=', 'scpr.plot_id')
            ->leftJoin('su_plots_owners_rel AS spor', 'spor.pc_rel_id', '=', 'scpr.id')
            // JOIN for regular contracts (Rents, Leases, Subleases and other Ownership contracts)
            ->leftJoin('su_contracts_plots_rel AS active_scpr', function ($join) {
                $join->on('active_scpr.plot_id', '=', 'lk.gid')
                    ->where('active_scpr.annex_action', '<>', 'removed');
            })
            ->leftJoin('su_contracts AS active_sc', function ($join) use ($allowedFarmingIds) {
                $join->on('active_sc.id', '=', 'active_scpr.contract_id')
                    ->where('active_sc.active', '=', true)
                    ->whereIn('active_sc.farming_id', $allowedFarmingIds);
            })
            // JOIN for sales contracts
            ->leftJoin('su_sales_contracts_plots_rel AS active_sspr', function ($join) {
                $join->on('active_sspr.plot_id', '=', 'lk.gid');
            })
            ->leftJoin('su_sales_contracts AS active_ssc', function ($join) use ($allowedFarmingIds) {
                $join->on('active_ssc.id', '=', 'active_sspr.sales_contract_id')
                    ->where('active_ssc.active', '=', true)
                    ->whereIn('active_ssc.farming_id', $allowedFarmingIds);
            })
            // JOIN for mortgage contracts
            ->leftJoin('su_hypothecs_plots_rel AS active_hpr', function ($join) {
                $join->on('active_hpr.plot_id', '=', 'lk.gid');
            })
            ->leftJoin('su_hypothecs AS active_sh', function ($join) use ($allowedFarmingIds) {
                $join->on('active_sh.id', '=', 'active_hpr.hypothec_id')
                    ->where('active_sh.is_active', '=', true)
                    ->whereIn('active_sh.farming_id', $allowedFarmingIds);
            })
            ->where('scpr.annex_action', '<>', 'removed')
            ->groupBy(
                'scpr.plot_id',
                'kad_ident',
                'ekatte_name',
                'ntp_title',
                'category_title',
                'scpr.comment',
                'scpr.area_for_rent',
                'scpr.contract_area',
                'scpr.rent_per_plot',
                'lk.allowable_area',
                'scpr.contract_id',
                'lk.document_area',
                'scpr.annex_action',
                'lk.is_edited',
                'lk.edit_active_from',
                'c.id'
            );

        // Plots list JSON query
        $plotsListJsonQuery = DB::table('plots_list AS pl')
            ->selectRaw('
                pl.contract_id
                , JSONB_AGG(ROW_TO_JSON(pl)) AS plots_json
                , ARRAY_AGG(DISTINCT pl.ekatte_name) AS ekatte_names
                , ROUND(SUM(pl.contract_area)::numeric, 3) AS total_contract_area
                , ROUND(SUM(pl.document_area)::numeric, 3) AS total_document_area
                , SUM(pl.allowable_area) as total_allowable_area
            ')
            ->groupBy('pl.contract_id');

        // Result query
        $resultQuery = DB::table('contracts AS c')
            ->selectRaw("
                JSONB_BUILD_OBJECT(
                    'details', row_to_json(c)::JSONB || JSONB_BUILD_OBJECT(
                        'ekatte_names', plj.ekatte_names,
                        'total_contract_area', COALESCE(c.total_contract_area, 0)
                    ),
                    'contractors', null,
                    'contractors_tree', null,
                    'plots', JSONB_BUILD_OBJECT(
                        'rows', plj.plots_json,
                        'footer', JSONB_BUILD_ARRAY(
                            JSONB_BUILD_OBJECT(
                                'title', 'own',
                                'value', COALESCE(plj.total_contract_area, 0)
                            ),
                            JSONB_BUILD_OBJECT(
                                'title', 'allowable',
                                'value', COALESCE(plj.total_allowable_area, 0)
                            ),
                            JSONB_BUILD_OBJECT(
                                'title', 'by documents',
                                'value', COALESCE(plj.total_document_area, 0)
                            )
                        )
                    )
                ) AS row
            ")
            ->leftJoin('plots_list_json AS plj', 'plj.contract_id', '=', 'c.id')
            ->orderBy('c.row_num', 'asc');

        // Execute complex query with CTEs
        return DB::table('result')
            ->withExpression('contracts', $contractsQuery)
            ->withExpression('plots_list', $plotsListQuery)
            ->withExpression('plots_list_json', $plotsListJsonQuery)
            ->withExpression('result', $resultQuery)
            ->selectRaw('result.row')
            ->get()
            ->pluck('row')
            ->map(fn ($row) => json_decode($row, true))
            ->toArray();
    }

    public function getContractsTotal(array $contractIds): int
    {
        return DB::table('su_contracts AS c')
            ->selectRaw('COUNT(DISTINCT c.id) AS total')
            ->when(
                ! empty($contractIds),
                fn ($q) => $q->whereIn('c.id', $contractIds)
            )
            ->where('c.is_sublease', false)
            ->first()->total ?? 0;
    }

    public function getPlotsForRenew(DocumentTypeEnum $documentType, array $filterParams, ?bool $canRenew = null, ?int $page = null, ?int $limit = null, ?array $sort = null): ListDTO
    {
        return new ListDTO([
            'rows' => [],
            'total' => 0,
        ]);
    }

    private function generateOrderByExpr(?array $sort): string
    {
        $columnMapFn = fn ($sortKey) => match($sortKey) {
            'id' => 'su_contracts.id',
            'c_num' => 'su_contracts.c_num',
            'start_date' => 'su_contracts.start_date',
            'farming_name' => 'su_contracts.farming_name',
            'total_contract_area' => 'su_contracts.total_contract_area',
            'type' => 'su_contracts.virtual_contract_type',
            'due_date' => 'su_contracts.due_date',
            'status' => 'su_contracts.virtual_contract_status',
            'comment' => 'su_contracts.comment',
            default => throw new InvalidArgumentException("Invalid sort column: {$sortKey}"),
        };


        $sort = array_map(
            fn ($sortOptions) => [
                'column' => $columnMapFn($sortOptions['column']),
                'direction' => $sortOptions['direction'],
            ],
            $sort ?? []
        );

        return $this->generateOrderBySQL($sort);
    }
}
