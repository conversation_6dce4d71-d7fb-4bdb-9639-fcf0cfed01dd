<?php

declare(strict_types=1);

namespace App\Services\Documents\Contracts;

use App\Models\UserDb\Documents\Contracts\MortgageContract;
use App\Types\DTOs\Common\ListDTO;
use App\Traits\Database\QueryHelper;
use App\Types\Enums\Documents\ContractTypeEnum;
use App\Types\Enums\Documents\DocumentTypeEnum;
use Illuminate\Support\Facades\DB;
use InvalidArgumentException;

class MortgageContractsService extends AbstractContractsService
{
    use QueryHelper;

    public function getContractsListFooter(array $contractIds): array
    {

        $footer = MortgageContract::selectRaw('
            SUM(hypothec_area) as total_contract_area
        ')
            ->when(
                ! empty($contractIds),
                fn ($q) => $q->whereIn('su_hypothecs.id', $contractIds)
            )
            ->first()->toArray();

        return (array) $footer;

    }

    public function getContracts(array $contractIds, array $allowedFarmingIds, ?int $page, ?int $limit, ?array $sort): array
    {
        $contractType = ContractTypeEnum::Mortgage->value;
        $contractTypeName = ContractTypeEnum::Mortgage->name();
        $ownershipContractType = ContractTypeEnum::Ownership->value;
        $orderByExpr = $this->generateOrderByExpr($sort);

        // Contracts
        $contractsQuery = MortgageContract::selectRaw("
            ROW_NUMBER() OVER ({$orderByExpr}) as row_num
            , su_hypothecs.id
            , su_hypothecs.hypothec_area as total_contract_area
            , su_hypothecs.num as c_num -- Contract number
            , {$contractType} as c_type
            , '{$contractTypeName}' as type
            , TO_CHAR(su_hypothecs.date,'DD.MM.YYYY') as c_date
            , TO_CHAR(su_hypothecs.start_date,'DD.MM.YYYY') as start_date
            , TO_CHAR(su_hypothecs.due_date,'DD.MM.YYYY') as due_date
            , su_hypothecs.is_active as active
            , false is_annex
            , false is_sublease
            , false is_internal_sublease
            , false is_from_sublease
            , su_hypothecs.farming_name as farming_name
            , su_hypothecs.na_num
            , su_hypothecs.tom
            , su_hypothecs.delo
            , su_hypothecs.court
            , su_hypothecs.creditor_id
            , su_hypothecs.comment
            , COALESCE (
                    JSON_AGG(
                        JSON_BUILD_OBJECT(
                            'file_id', hf.id,
                            'file_name', hf.filename
                        )
                    order by hf.id
                    ) filter (where hf.id is not null),
                '[]'::json
            ) as files
        ")
            ->leftJoin('su_hypothecs_files AS hf', 'hf.hypothec_id', '=', 'su_hypothecs.id')
            ->when(
                ! empty($contractIds),
                fn ($q) => $q->whereIn('su_hypothecs.id', $contractIds)
            )
            ->when(
                isset($page) && isset($limit),
                fn ($q) => $q->limit($limit)->offset(($page - 1) * $limit)
            )
            ->groupBy('su_hypothecs.id');

        // Contractors list
        $contractorsListQuery = DB::table('contracts AS c')
            ->selectRaw('
                c.id as contract_id
                , hc.name
            ')
            ->leftJoin('su_hypothecs_creditors AS hc', 'hc.id', '=', 'c.creditor_id')
            ->orderBy('c.id');

        // Contractors list in json format
        $contractorsListJsonQuery = DB::table('contractors_list AS ol')
            ->selectRaw('
                ol.contract_id,
                JSONB_AGG(ol) AS contractors_json
            ')
            ->groupBy('ol.contract_id');

        // Plots list
        $plotsListQuery = DB::table('contracts AS c')
            ->selectRaw("
                c.id AS contract_id
                , hpr.plot_id as id
                , lk.kad_ident
                , lk.virtual_ekatte_name AS ekatte_name
                , lk.virtual_ntp_title AS ntp_title
                , lk.document_area
                -- temporary trim the category title to get some space for the other columns in FE documents drawer      
                , TRIM(leading 'Категория ' from lk.virtual_category_title) AS category_title 
                , lk.allowable_area
                , hpr.hypothec_area as contract_area
                , '' as comment
                , COALESCE(
                    JSONB_AGG(
                        DISTINCT CASE 
                            WHEN ownership_sc.id IS NOT NULL THEN
                                JSONB_BUILD_OBJECT(
                                    'id', ownership_sc.id,
                                    'name', ownership_sc.c_num,
                                    'c_type', ownership_sc.nm_usage_rights,
                                    'type', ownership_sc.virtual_contract_type,
                                    'contract_area', ROUND(ownership_scpr.contract_area::numeric, 3),
                                    'is_sublease', false,
                                    'is_from_sublease', false,
                                    'start_date', TO_CHAR(ownership_sc.start_date, 'DD.MM.YYYY'),
                                    'due_date', TO_CHAR(ownership_sc.due_date, 'DD.MM.YYYY'),
                                    'status', get_contract_status(ownership_sc.id, ownership_sc.active, ownership_sc.start_date, ownership_sc.due_date)::text
                                )
                            ELSE NULL
                        END
                    ) FILTER (WHERE ownership_sc.id IS NOT NULL),
                    '[]'::jsonb
                ) AS contracts,
                JSONB_BUILD_ARRAY(
                    JSONB_BUILD_OBJECT('title', 'mortgaged', 'value', COALESCE(hpr.hypothec_area, 0)),
                    JSONB_BUILD_OBJECT('title', 'by document', 'value', COALESCE(lk.document_area, 0))
                ) as plot_areas
        ")
            ->join('su_hypothecs_plots_rel AS hpr', 'hpr.hypothec_id', '=', 'c.id')
            ->join('layer_kvs AS lk', 'lk.gid', '=', 'hpr.plot_id')
            ->leftJoin('su_contracts_plots_rel AS ownership_scpr', function ($join) {
                $join->on('ownership_scpr.plot_id', '=', 'lk.gid')
                    ->where('ownership_scpr.annex_action', '<>', 'removed');
            })
            ->leftJoin('su_contracts AS ownership_sc', function ($join) use ($ownershipContractType, $allowedFarmingIds) {
                $join->on('ownership_sc.id', '=', 'ownership_scpr.contract_id')
                    ->where('ownership_sc.active', '=', true)
                    ->where('ownership_sc.nm_usage_rights', '=', $ownershipContractType)
                    ->whereIn('ownership_sc.farming_id', $allowedFarmingIds);
            })
            ->groupBy(
                'c.id',
                'hpr.plot_id',
                'lk.kad_ident',
                'lk.virtual_ekatte_name',
                'lk.virtual_ntp_title',
                'lk.document_area',
                'lk.virtual_category_title',
                'lk.allowable_area',
                'hpr.hypothec_area'
            );

        // Plots list json
        $plotsListJsonQuery = DB::table('plots_list AS pl')
            ->selectRaw('
                pl.contract_id
                , JSONB_AGG(ROW_TO_JSON(pl)) AS plots_json
                , ARRAY_AGG(DISTINCT pl.ekatte_name) AS ekatte_names
                , ROUND(SUM(pl.document_area)::numeric, 3) AS total_document_area
                , ROUND(SUM(pl.contract_area)::numeric, 3) AS total_contract_area
            ')
            ->groupBy('pl.contract_id');

        $resultQuery = DB::table('contracts AS c')
            ->selectRaw("
                JSONB_BUILD_OBJECT(
                    'details', row_to_json(c)::JSONB || JSONB_BUILD_OBJECT(
                        'ekatte_names', plj.ekatte_names,
                        'total_contract_area', c.total_contract_area
                    ),
                    'contractors', oj.contractors_json,
                    'contractors_tree', null,
                    'plots', JSONB_BUILD_OBJECT(
                        'rows', plj.plots_json,
                        'footer', JSONB_BUILD_ARRAY(
                            JSONB_BUILD_OBJECT(
                                'title', 'mortgaged',
                                'value', COALESCE(plj.total_contract_area, 0)
                            ),
                            JSONB_BUILD_OBJECT(
                                'title', 'by document',
                                'value', COALESCE(plj.total_document_area, 0)
                            )
                        )
                    )
                ) AS row
            ")
            ->leftJoin('contractors_list_json AS oj', 'oj.contract_id', '=', 'c.id')
            ->leftJoin('plots_list_json AS plj', 'plj.contract_id', '=', 'c.id')
            ->orderBy('c.row_num', 'asc');

        return DB::table('result')
            ->withExpression('contracts', $contractsQuery)
            ->withExpression('contractors_list', $contractorsListQuery)
            ->withExpression('contractors_list_json', $contractorsListJsonQuery)
            ->withExpression('plots_list', $plotsListQuery)
            ->withExpression('plots_list_json', $plotsListJsonQuery)
            ->withExpression('result', $resultQuery)
            ->selectRaw('result.row')
            ->get()
            ->pluck('row')
            ->map(fn ($row) => json_decode($row, true))
            ->toArray();
    }

    public function getContractsTotal(array $contractIds): int
    {
        return MortgageContract::selectRaw('COUNT(*) as total')
            ->when(
                ! empty($contractIds),
                fn ($q) => $q->whereIn('id', $contractIds)
            )
            ->first()->total ?? 0;
    }

    public function getPlotsForRenew(DocumentTypeEnum $documentType, array $filterParams, ?bool $canRenew = null, ?int $page = null, ?int $limit = null, ?array $sort = null): ListDTO
    {
        return new ListDTO([
            'rows' => [],
            'total' => 0,
        ]);
    }

    private function generateOrderByExpr(?array $sort): string
    {
        $columnMapFn = fn ($sortKey) => match($sortKey) {
            'id' => 'su_contracts.id',
            'c_num' => 'su_hypothecs.num',
            'start_date' => 'su_hypothecs.start_date',
            'farming_name' => 'su_hypothecs.farming_name',
            'total_contract_area' => 'su_hypothecs.hypothec_area',
            default => throw new InvalidArgumentException("Invalid sort column: {$sortKey}"),
        };

        $sort = array_map(
            fn ($sortOptions) => [
                'column' => $columnMapFn($sortOptions['column']),
                'direction' => $sortOptions['direction'],
            ],
            $sort ?? []
        );

        return $this->generateOrderBySQL($sort);
    }
}
