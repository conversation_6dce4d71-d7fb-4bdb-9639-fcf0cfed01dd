<?php

declare(strict_types=1);

namespace App\Services\Documents\Contracts;

use App\Models\UserDb\Documents\Contracts\Contract;
use App\Types\DTOs\Common\ListDTO;
use App\Traits\Database\QueryHelper;
use App\Types\Enums\Documents\ContractTypeEnum;
use App\Types\Enums\Documents\DocumentTypeEnum;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use InvalidArgumentException;
use App\Services\ContractPlotFilter\ContractPlotFilter;
use App\Traits\Common\FarmingYearsTrait;

class SubleasesContractsService extends AbstractContractsService
{
    use QueryHelper;
    use FarmingYearsTrait;
    public function getContractsListFooter(array $contractIds): array
    {
        $contractsQuery = Contract::select('id', 'from_sublease')->when(
            ! empty($contractIds),
            fn ($q) => $q
                ->whereIn('su_contracts.id', $contractIds)
                ->where(
                    fn ($w) => $w
                        ->where('su_contracts.is_sublease', true)
                        ->orWhereNotNull('su_contracts.from_sublease')
                )
        )
            ->whereIn(DB::raw('get_contract_status(su_contracts.id, su_contracts.active, su_contracts.start_date, su_contracts.due_date, FALSE)::TEXT'), [
                'Active',
            ]);


        $subleaseAreaQuery = DB::table('contracts AS c')
            ->selectRaw('
                c.id AS contract_id
                , SUM(COALESCE(sspa.contract_area, 0)) AS contract_area
            ')
            ->join('su_subleases_plots_contracts_rel AS sspcr', 'sspcr.sublease_id', '=', 'c.id')
            ->join('su_contracts_plots_rel AS scpr', 'scpr.id', '=', 'sspcr.pc_rel_id')
            ->join('su_subleases_plots_area AS sspa', function (Builder $join) {
                $join->on('sspa.sublease_id', '=', 'c.id')
                    ->on('sspa.plot_id', '=', 'scpr.plot_id');
            })
            ->groupBy(
                'c.id'
            );

        $areaByContractQuery = DB::table('contracts AS c')
            ->selectRaw('
                c.id AS contract_id
                , SUM(COALESCE(sspa.contract_area, 0)) AS contract_area
        ')
            ->join('su_contracts_plots_rel AS scpr', 'scpr.contract_id', '=', 'c.id')
            ->join('su_subleases_plots_area AS sspa', function (Builder $join) {
                $join->on('sspa.sublease_id', '=', 'c.from_sublease')
                    ->on('sspa.plot_id', '=', 'scpr.plot_id');
            })
            ->unionAll($subleaseAreaQuery)
            ->groupBy(
                'c.id',
            );


        $footer = DB::table('area_by_contract')
            ->withExpression('contracts', $contractsQuery)
            ->withExpression('area_by_contract', $areaByContractQuery)
            ->selectRaw('
                SUM(contract_area) as total_contract_area
            ')
            ->first();



        return (array) $footer;
    }

    public function getContracts(array $contractIds, array $allowedFarmingIds, ?int $page, ?int $limit, ?array $sort): array
    {
        $orderByExpr = $this->generateOrderByExpr($sort);

        $typeInt =  ContractTypeEnum::Sublease->value;
        $typeName = ContractTypeEnum::Sublease->name();

        // Administrative generated contracts
        $administrativeGeneratedContracts = Contract::selectRaw("
                su_contracts.id,
                su_contracts.c_num as name,
                TO_CHAR(su_contracts.c_date,'DD.MM.YYYY') as c_date,
                TO_CHAR(su_contracts.start_date,'DD.MM.YYYY') as start_date,
                TO_CHAR(su_contracts.due_date,'DD.MM.YYYY') as due_date,
                get_contract_status(su_contracts.id,su_contracts.active, su_contracts.start_date, su_contracts.due_date) as status,
                su_contracts.is_annex,
                su_contracts.is_sublease,
                case when su_contracts.from_sublease notnull then true else false end as is_from_sublease,
                su_contracts.from_sublease
        ")
            ->when(
                ! empty($contractIds),
                fn ($q) => $q->whereIn('su_contracts.from_sublease', $contractIds)
                    ->orWhere('su_contracts.id', $contractIds)
            );

        // Contracts
        $contractsQuery = Contract::selectRaw("
            ROW_NUMBER() OVER ({$orderByExpr}) as row_num,
            su_contracts.id,
            su_contracts.c_num,-- Contract number
            {$typeInt} as c_type,-- Contract type
            TO_CHAR(su_contracts.c_date,'DD.MM.YYYY') as c_date,
            TO_CHAR(su_contracts.start_date,'DD.MM.YYYY') as start_date,
            TO_CHAR(su_contracts.due_date,'DD.MM.YYYY') as due_date,
            su_contracts.active,
            get_contract_status(su_contracts.id,su_contracts.active, su_contracts.start_date, su_contracts.due_date) as status,
            '{$typeName}' as type,
            su_contracts.is_annex,
            su_contracts.is_sublease,
            case when max(cfc.id) is null then false else true end as  is_internal_sublease,
            su_contracts.from_sublease,
            case when su_contracts.from_sublease notnull then true else false end as is_from_sublease,
            su_contracts.farming_name,
        	su_contracts.osz_num, -- OSZ contract number 
        	TO_CHAR(su_contracts.osz_date,'DD.MM.YYYY') as osz_date, -- OSZ contract date
            su_contracts.sv_num, -- Registry Agency contract number
            TO_CHAR(su_contracts.sv_date,'DD.MM.YYYY') as sv_date,	-- Registry Agency date of contract
        	su_contracts.na_num,
        	su_contracts.tom,
        	su_contracts.total_contract_area,
        	su_contracts.delo,
        	su_contracts.court,
            su_contracts.comment,
            coalesce (
                JSON_AGG(
                    JSON_BUILD_OBJECT(
                        'file_id',suf.id,
                        'file_name',suf.filename
                    )
                order by suf.id
                ) filter (where suf.id is not null),
            '[]'::json
            ) as files,
            coalesce(
                JSON_AGG(
                    JSON_BUILD_OBJECT(
                        'id', COALESCE(agc.id, parent.id),
                        'name', COALESCE(agc.name, parent.c_num),
                        'c_date', COALESCE(agc.c_date, TO_CHAR(parent.c_date,'DD.MM.YYYY')),
                        'start_date', COALESCE(agc.start_date, TO_CHAR(parent.start_date,'DD.MM.YYYY')),
                        'due_date', COALESCE(agc.due_date, TO_CHAR(parent.due_date,'DD.MM.YYYY')),
                        'status', COALESCE(agc.status, get_contract_status(parent.id, parent.active, parent.start_date, parent.due_date)),
                        'c_type', {$typeInt},
                        'type', '{$typeName}',
                        'is_annex', COALESCE(agc.is_annex, parent.is_annex, false),
                        'is_sublease', COALESCE(agc.is_sublease, parent.is_sublease, false),
                        'is_from_sublease', COALESCE(agc.is_from_sublease, case when parent.from_sublease notnull then true else false end, false)
                    )
                ) filter (where agc.id is not null or parent.id is not null),
                '[]'::json
            ) as related_contracts
        ")
            ->leftJoin('su_contracts_files_rel AS scfr', 'scfr.contract_id', '=', 'su_contracts.id')
            ->leftJoin('su_user_files AS suf', 'suf.id', '=', 'scfr.file_id')
            ->leftJoin('su_contracts_rents AS scr', 'scr.contract_id', '=', 'su_contracts.id')
            ->leftJoin('su_renta_types AS srt', 'srt.id', '=', 'scr.renta_id')
            ->leftJoin('su_units_of_measure AS sum', 'sum.id', '=', 'srt.unit')
            ->leftJoin('administrative_generated_contracts AS agc', 'agc.from_sublease', '=', 'su_contracts.id')
            ->leftJoin(
                'su_contracts AS parent',
                fn ($q) => $q->on('parent.id', '=', 'su_contracts.from_sublease')
                    ->whereIn('parent.farming_id', $allowedFarmingIds)
            )
            ->leftJoin('su_contracts_farming_contragents AS cfc', 'cfc.contract_id', '=', 'su_contracts.id')
            ->when(
                empty($contractIds),
                fn ($q) => $q->where('su_contracts.is_sublease', true)
            )
            ->when(
                ! empty($contractIds),
                fn ($q) => $q->whereIn('su_contracts.id', $contractIds)
                    ->where(fn ($w) => $w->where('su_contracts.is_sublease', true)
                        ->orWhereNotNull('su_contracts.from_sublease'))
            )
            ->when(
                isset($page) && isset($limit),
                fn ($q) => $q->limit($limit)->offset(($page - 1) * $limit)
            )
            ->groupBy('su_contracts.id', 'parent.id');

        $mainDB = Config::get('database.connections.main');
        $connectionString = "host={$mainDB['host']} port={$mainDB['port']} user={$mainDB['username']} dbname={$mainDB['database']} password={$mainDB['password']}";

        // Contractors list
        $contractorsListQuery = DB::table('contracts AS c')
            ->selectRaw("
                c.id as contract_id,
                COALESCE(
                    NULLIF(uf.company, ''),
                    NULLIF(o.company_name, ''),
                    CONCAT_WS(' ', o.name, o.surname, o.lastname)
                ) AS name,
                coalesce(CONCAT_WS(' ', fsor.rep_name, fsor.rep_surname, fsor.rep_lastname), CONCAT_WS(' ', csor.rep_name, csor.rep_surname, csor.rep_lastname)) as rep_name
            ")
            ->leftJoin('su_contracts_farming_contragents AS cfc', 'cfc.contract_id', '=', 'c.id')
            ->leftJoin(DB::raw("dblink('{$connectionString}', 'SELECT id, company FROM su_users_farming') AS uf(id int, company text)"), 'uf.id', '=', 'cfc.farming_id')
            ->leftJoin('su_owners_reps AS fsor', 'fsor.id', '=', 'cfc.rep_id')
            ->leftJoin('su_contracts_contragents AS cc', 'cc.contract_id', '=', 'c.id')
            ->leftJoin('su_owners AS o', 'o.id', '=', 'cc.owner_id')
            ->leftJoin('su_owners_reps AS csor', 'csor.id', '=', 'cc.rep_id')
            ->orWhereNotNull('uf.id')
            ->orWhereNotNull('o.id')
            ->orWhereNotNull('fsor.id')
            ->orWhereNotNull('csor.id');

        // Contractors list in json format
        $contractorsListJsonQuery = DB::table('contractors_list AS ol')
            ->selectRaw('
                ol.contract_id,
                JSONB_AGG(ol) AS contractors_json
            ')
            ->groupBy('ol.contract_id');

        // SubleasesPlots list
        $SubleasesPlotsListUnionQuery = DB::table('contracts AS c')
            ->selectRaw("
                c.id AS contract_id,
                scpr.plot_id as id,
                lk.kad_ident,
                lk.virtual_ekatte_name AS ekatte_name,
                lk.virtual_ntp_title AS ntp_title,
                lk.document_area,
                TRIM(leading 'Категория ' from lk.virtual_category_title) AS category_title,
                coalesce(sspa.rent_area, 0) AS area_for_rent,
                lk.allowable_area,
                sspa.contract_area,
                sspa.comment,
                jsonb_agg(
                    distinct jsonb_build_object(
                        'id', pc.id,
                        'name', pc.c_num,
                        'contract_area', scpr.contract_area,
                        'c_date', TO_CHAR(pc.c_date,'DD.MM.YYYY'),
                        'start_date', TO_CHAR(pc.start_date,'DD.MM.YYYY'),
                        'due_date', TO_CHAR(pc.due_date,'DD.MM.YYYY'),
                        'status', get_contract_status(pc.id, pc.active, pc.start_date, pc.due_date),
                        'c_type', pc.nm_usage_rights,
                        'type', pc.virtual_contract_type
                    )
                ) AS contracts,
                JSONB_BUILD_ARRAY(
                    JSONB_BUILD_OBJECT('title', 'sublease', 'value', COALESCE(sspa.contract_area, 0)),
                    JSONB_BUILD_OBJECT('title', 'for rent', 'value', COALESCE(sspa.rent_area, 0)),
                    JSONB_BUILD_OBJECT('title', 'by document', 'value', COALESCE(lk.document_area, 0))
                ) as plot_areas
            ")
            ->join('su_subleases_plots_contracts_rel AS sspcr', 'sspcr.sublease_id', '=', 'c.id')
            ->join('su_contracts_plots_rel AS scpr', 'scpr.id', '=', 'sspcr.pc_rel_id')
            ->join('layer_kvs AS lk', 'lk.gid', '=', 'scpr.plot_id')
            ->join('su_subleases_plots_area AS sspa', function (Builder $join) {
                $join->on('sspa.sublease_id', '=', 'c.id')
                    ->on('sspa.plot_id', '=', 'scpr.plot_id');
            })
            ->leftJoin('su_contracts AS pc', function (Builder $join) {
                $join->on('pc.id', '=', 'scpr.contract_id')
                    ->where('pc.active', '=', true);
            })
            ->groupBy(
                'c.id',
                'scpr.plot_id',
                'kad_ident',
                'lk.virtual_ekatte_name',
                'lk.virtual_ntp_title',
                'lk.virtual_category_title',
                'sspa.comment',
                'sspa.rent_area',
                'sspa.contract_area',
                'lk.allowable_area',
                'lk.document_area'
            );

        // Plots list
        $plotsListQuery = DB::table('contracts AS c')
            ->selectRaw("
            c.id AS contract_id,
            scpr.plot_id as id,
            lk.kad_ident,
            lk.virtual_ekatte_name AS ekatte_name,
            lk.virtual_ntp_title AS ntp_title,
            lk.document_area,
            -- temporary trim the category title to get some space for the other columns in FE documents drawer      
            TRIM(leading 'Категория ' from lk.virtual_category_title) AS category_title     ,
            coalesce(sspa.rent_area, 0) AS area_for_rent,
            lk.allowable_area,
            sspa.contract_area,
            sspa.comment,
            '{}' as contracts,
            JSONB_BUILD_ARRAY(
                JSONB_BUILD_OBJECT('title', 'sublease', 'value', COALESCE(sspa.contract_area, 0)),
                JSONB_BUILD_OBJECT('title', 'for rent', 'value', COALESCE(sspa.rent_area, 0)),
                JSONB_BUILD_OBJECT('title', 'by document', 'value', COALESCE(lk.document_area, 0))
            ) as plot_areas
        ")
            ->join('su_contracts_plots_rel AS scpr', 'scpr.contract_id', '=', 'c.id')
            ->join('layer_kvs AS lk', 'lk.gid', '=', 'scpr.plot_id')
            ->leftJoin('su_contracts_plots_rel AS pscpr', 'pscpr.plot_id', '=', 'lk.gid')
            ->leftJoin('su_contracts AS pc', function (Builder $join) {
                $join->on('pc.id', '=', 'scpr.contract_id')
                    ->where('pc.active', '=', true);
            })
            ->join('su_subleases_plots_area AS sspa', function (Builder $join) {
                $join->on('sspa.sublease_id', '=', 'c.from_sublease')
                    ->on('sspa.plot_id', '=', 'scpr.plot_id');
            })
            ->unionAll($SubleasesPlotsListUnionQuery)
            ->groupBy(
                'c.id',
                'scpr.plot_id',
                'kad_ident',
                'lk.virtual_ekatte_name',
                'lk.virtual_ntp_title',
                'lk.virtual_category_title',
                'sspa.comment',
                'sspa.rent_area',
                'sspa.contract_area',
                'lk.allowable_area',
                'lk.document_area',
                'plot_areas'
            );

        // Plots list json
        $plotsListJsonQuery = DB::table('plots_list AS pl')
            ->selectRaw('
                pl.contract_id,
                JSONB_AGG(ROW_TO_JSON(pl)) AS plots_json,
                ARRAY_AGG(DISTINCT pl.ekatte_name) AS ekatte_names,
                ROUND(SUM(pl.contract_area)::numeric, 3) AS total_contract_area,
                ROUND(SUM(pl.area_for_rent)::numeric, 3) AS total_area_for_rent,
                ROUND(SUM(pl.document_area)::numeric, 3) AS total_document_area
            ')
            ->groupBy('pl.contract_id');

        $resultQuery = DB::table('contracts AS c')
            ->selectRaw("
                JSONB_BUILD_OBJECT(
                    'details', row_to_json(c)::JSONB || JSONB_BUILD_OBJECT(
                        'ekatte_names', plj.ekatte_names
                    ),
                    'contractors', oj.contractors_json,
                    'contractors_tree', null,
                    'plots', JSONB_BUILD_OBJECT(
                        'rows', plj.plots_json,
                        'footer', JSONB_BUILD_ARRAY(
                            JSONB_BUILD_OBJECT(
                                'title', 'sublease',
                                'value', COALESCE(plj.total_contract_area, 0)
                            ),
                            JSONB_BUILD_OBJECT(
                                'title', 'for rent',
                                'value', COALESCE(plj.total_area_for_rent, 0)
                            ),
                            JSONB_BUILD_OBJECT(
                                'title', 'by document',
                                'value', COALESCE(plj.total_document_area, 0)
                            )
                        )
                    )
                ) AS row
            ")
            ->leftJoin('contractors_list_json AS oj', 'oj.contract_id', '=', 'c.id')
            ->leftJoin('plots_list_json AS plj', 'plj.contract_id', '=', 'c.id')
            ->orderBy('c.row_num', 'asc');

        return DB::table('result')
            ->withExpression('administrative_generated_contracts', $administrativeGeneratedContracts)
            ->withExpression('contracts', $contractsQuery)
            ->withExpression('contractors_list', $contractorsListQuery)
            ->withExpression('contractors_list_json', $contractorsListJsonQuery)
            ->withExpression('plots_list', $plotsListQuery)
            ->withExpression('plots_list_json', $plotsListJsonQuery)
            ->withExpression('result', $resultQuery)
            ->selectRaw('result.row')
            ->get()
            ->pluck('row')
            ->map(fn ($row) => json_decode($row, true))
            ->toArray();
    }

    public function getContractsTotal(array $contractIds): int
    {
        return DB::table('su_contracts AS c')
            ->selectRaw('COUNT(DISTINCT c.id) AS total')
            ->when(
                ! empty($contractIds),
                fn ($q) => $q->whereIn('c.id', $contractIds)
            )
            ->where('c.is_sublease', true)
            ->first()->total ?? 0;
    }

    /**
     * Get plots for renewal based on sublease contract relationships and renewal criteria.
     *
     * @param DocumentTypeEnum $documentType The type of document
     * @param array $filterParams The filter parameters
     * @param null|bool $canRenew Optional filter for can_renew status (true/false/null for all)
     * @param null|int $page Page number for pagination (1-based)
     * @param null|int $limit Number of records per page
     * @param null|array $sort Sorting configuration array
     */
    public function getPlotsForRenew(DocumentTypeEnum $documentType, array $filterParams, ?bool $canRenew = null, ?int $page = null, ?int $limit = null, ?array $sort = null): ListDTO
    {
        $baseTable = $documentType->tableName();
        $contractPlotFilter = new ContractPlotFilter($baseTable);
        $farmingYear = self::getStartEndDatesByFarmingYearText($filterParams['farming_year']);



        $filterQuery = $contractPlotFilter
            ->withParams($filterParams)
            ->getQuery()
            ->select(['contracts_for_renew.*', 'su_contracts.id as contract_id'])
            ->distinct();



        $baseYearContractsQuery = DB::table('filter', 'f')
            ->select([
                'scpr.plot_id',
                'f.cp_rel_id AS pc_rel_id',
                'kvs.kad_ident',
                'kvs.document_area',
                'f.curr_contract_area AS contract_area', // Use the area from the filter (this is the sublease area)
                'c.id AS contract_id',
                'c.c_num',
                'c.start_date',
                'c.due_date',
                'c.virtual_contract_type',
                DB::raw('c.nm_usage_rights AS contract_type'),
                'c.farming_name',
                'c.is_sublease',
                'c.from_sublease',
                DB::raw('case when c.from_sublease notnull then true else false end as is_from_sublease'),
                DB::raw("CASE
                            WHEN c.start_date::date < '{$farmingYear['start_date']}'::date OR c.due_date::date > '{$farmingYear['end_date']}'::date THEN true
                            ELSE false
                        END as multi_year_contract"),
                DB::raw("CASE
                            WHEN c.start_date::date > '{$farmingYear['start_date']}'::date OR c.due_date::date < '{$farmingYear['end_date']}'::date THEN true
                            ELSE false
                        END as wrong_contract_period"),
                'c.is_annex',
                'f.can_renew',
                'f.can_renew_contract'
            ])
            ->join('su_contracts_plots_rel AS scpr', 'f.cp_rel_id', '=', 'scpr.id')
            ->join('su_subleases_plots_contracts_rel AS sspc', function ($join) {
                $join->on('sspc.pc_rel_id', '=', 'scpr.id')
                     ->on('sspc.sublease_id', '=', 'f.contract_id');
            })
            ->join('su_contracts AS c', function ($join) {
                $join->on('c.id', '=', 'sspc.sublease_id');
            })
            ->join('layer_kvs AS kvs', 'kvs.gid', '=', 'scpr.plot_id');

        // Existing contracts in next year CTE
        $existingContractsQuery = DB::table('filter', 'f')
            ->select([
                'scpr.plot_id',
                'scpr.id AS pc_rel_id',
                'f.curr_contract_area AS contract_area',
                'scpr.contract_id as c_id',
                'c.source_contract_id',
                DB::raw("jsonb_build_object(
                        'contract_id', c.id,
                        'source_contract_id', c.source_contract_id,
                        'c_num', c.c_num,
                        'is_sublease', c.is_sublease,
                        'from_sublease', c.from_sublease,
                        'is_from_sublease',case when c.from_sublease notnull then true else false end,
                        'is_annex', c.is_annex,
                        'start_date', to_char(c.start_date, 'dd.mm.YYYY'),
                        'due_date', to_char(c.due_date, 'dd.mm.YYYY'),
                        'virtual_contract_type', c.virtual_contract_type,
                        'contract_type', c.nm_usage_rights,
                        'virtual_farming_name', c.farming_name,
                        'contract_area', sspa.contract_area
                ) AS existing")
            ])
            ->join('su_contracts_plots_rel AS scpr', DB::raw('f.next_cp_rel_ids'), '@>', DB::raw('scpr.id::text::jsonb'))
            ->join('su_subleases_plots_contracts_rel AS sspc', function ($join) {
                $join->on('sspc.pc_rel_id', '=', 'scpr.id')
                     ->whereRaw('f.next_sublease_cp_ids @> sspc.id::text::jsonb'); // Use sublease relationship IDs for precise filtering
            })
            ->join('su_subleases_plots_area AS sspa', function ($join) {
                $join->on('scpr.plot_id', '=', 'sspa.plot_id')
                     ->on('sspc.sublease_id', '=', 'sspa.sublease_id');
            })
            ->leftJoin('su_contracts AS c', function ($join) {
                $join->on('c.id', '=', 'sspc.sublease_id');
            })
            ->groupBy(['c.id', 'scpr.plot_id', 'scpr.id', 'f.curr_contract_area', 'c.source_contract_id', 'sspa.contract_area']);

        // Total document area CTE. It is needed because document area should be sum only once per plot
        $plotsTotalQuery = DB::table(DB::raw('(
            SELECT DISTINCT plot_id, document_area
            FROM base_year_contracts
        ) as sub'))
        ->selectRaw('SUM(document_area) as total_document_area, COUNT(plot_id) as count_plots');

        // Prepared data CTE
        $preparedQuery = DB::table('base_year_contracts', 'b')
            ->select([
                DB::raw('COALESCE(b.plot_id, e.plot_id) AS plot_id'),
                'b.pc_rel_id',
                'b.kad_ident',
                'b.document_area',
                'b.contract_area',
                'b.contract_id',
                'b.is_sublease',
                'b.from_sublease',
                'b.is_from_sublease',
                'b.is_annex',
                'b.c_num',
                'b.start_date',
                'b.due_date',
                'b.virtual_contract_type',
                'b.contract_type',
                'b.farming_name',
                'b.multi_year_contract',
                'b.wrong_contract_period',
                't.total_document_area',
                't.count_plots as total_rows',
                // Contract-level can_renew: ALL plots in the contract must be renewable
                // If ANY plot in the contract cannot be renewed, the entire contract is marked as non-renewable
                'b.can_renew as can_renew',
                'b.can_renew_contract as can_renew_contract',
                DB::raw('SUM(b.contract_area) over() AS total_contract_area_base_period'),
                DB::raw('SUM(COALESCE(b.contract_area, 0)) FILTER (WHERE b.can_renew = true) over() AS total_can_renew_area'),
                DB::raw('SUM(COALESCE(e.contract_area, 0)) over() AS total_contract_area_next_period'),
                DB::raw('COALESCE(e.contract_area, 0) AS new_contract_area'),
                DB::raw('SUM(COALESCE(e.contract_area, 0)) over(partition by coalesce(b.plot_id, e.plot_id)) AS new_contract_area_sum'),
                DB::raw("COALESCE(e.existing, null) AS existing"),
                DB::raw("(
                    case
                        when b.multi_year_contract = true then 1
                        when b.wrong_contract_period = true then 2
                        when b.can_renew = false and b.wrong_contract_period = false and b.multi_year_contract = false and b.contract_type != 1 then 1
                        when b.contract_type = 1 then 4
                        when b.can_renew = true then 4
                        else 9
                    end
                ) as order")
            ])
            ->from('base_year_contracts', 'b')
            ->join(DB::raw('existing AS e'), function ($join) {
                $join->on('b.contract_id', '=', DB::raw('COALESCE(e.source_contract_id, e.c_id)'));
            }, null, null, 'full outer')
            ->crossJoinSub($plotsTotalQuery, 't');

        $orderByClause = $this->generateOrderByClause($sort);

        $resultQuery = DB::table('prepared', 'p')
            ->select([
                'p.plot_id',
                DB::raw('MAX(p.kad_ident) AS kad_ident'),
                DB::raw('CAST(MAX(p.document_area) AS NUMERIC) AS document_area'),
                DB::raw('ROUND(SUM(p.contract_area)::numeric, 3) AS contract_area'),
                DB::raw('ROUND(SUM(p.new_contract_area)::numeric, 3) AS contract_area_next_period'),
                DB::raw('max(total_rows) AS total_rows'),
                DB::raw('ROUND(max(total_document_area)::numeric, 3) AS total_document_area'),
                DB::raw('ROUND(max(total_contract_area_base_period)::numeric, 3) AS total_contract_area_base_period'),
                DB::raw('ROUND(max(total_can_renew_area)::numeric, 3) AS total_can_renew_area'),
                DB::raw('ROUND(max(total_contract_area_next_period)::numeric, 3) AS total_contract_area_next_period'),
                DB::raw('COUNT(*) FILTER (WHERE p.can_renew) AS can_renew_count'),
                DB::raw('COUNT(*) FILTER (WHERE NOT p.can_renew) AS cannot_renew_count'),
                DB::raw("jsonb_agg(
                    jsonb_build_object(
                        'pc_rel_id', p.pc_rel_id,
                        'contract_id', p.contract_id,
                        'is_sublease', p.is_sublease,
                        'from_sublease', p.from_sublease,
                        'is_from_sublease', p.is_from_sublease,
                        'is_annex', p.is_annex,
                        'c_num', p.c_num,
                        'virtual_contract_type', p.virtual_contract_type,
                        'contract_type', p.contract_type,
                        'farming_name', p.farming_name,
                        'contract_area', ROUND(p.contract_area::numeric, 3),
                        'new_contract_area', ROUND(p.new_contract_area::numeric, 3),
                        'document_area', ROUND(p.document_area::numeric, 3),
                        'start_date', to_char(p.start_date, 'dd.mm.YYYY'),
                        'due_date', to_char(p.due_date, 'dd.mm.YYYY'),
                        'renew_area', ROUND((p.contract_area - p.new_contract_area)::numeric, 3),
                        'not_enought_area', CASE
                            WHEN (p.document_area - (p.contract_area + p.new_contract_area_sum)) <= 0
                            THEN ABS(ROUND((p.document_area - (p.contract_area + p.new_contract_area_sum))::numeric, 3))
                            ELSE null
                        END,
                        'multi_year_contract', p.multi_year_contract,
                        'wrong_contract_period', p.wrong_contract_period,
                        'existing', p.existing,
                        'can_renew', p.can_renew,
                        'can_renew_contract', p.can_renew_contract
                    )
                    ORDER BY p.order ASC
                ) AS contracts_json")
            ])
            ->groupBy(['p.plot_id'])
            ->when($orderByClause, fn ($query) => $query->orderByRaw($orderByClause))
            ->when($page && $limit, fn ($query) => $query->offset(($page - 1) * $limit)->limit($limit));

        // Execute the query with CTEs
        $contractsForRenew = DB::table('result')
            ->withExpression('filter', $filterQuery)
            ->withExpression('base_year_contracts', $baseYearContractsQuery)
            ->withExpression('existing', $existingContractsQuery)
            ->withExpression('total_document_area', $plotsTotalQuery)
            ->withExpression('prepared', $preparedQuery)
            ->withExpression('result', $resultQuery)
            ->selectRaw('result.*')
            ->get()
            ->map(function ($row) {
                $row->contracts_json = json_decode($row->contracts_json, true);
                return (array) $row;
            })
            ->toArray();

        return new ListDTO([
            'rows' => $contractsForRenew,
        ]);
    }

    private function generateOrderByExpr(?array $sort): string
    {
        $columnMapFn = fn ($sortKey) => match($sortKey) {
            'id' => 'su_contracts.id',
            'c_num' => 'su_contracts.c_num',
            'start_date' => 'su_contracts.start_date',
            'farming_name' => 'su_contracts.farming_name',
            'total_contract_area' => 'su_contracts.total_contract_area',
            'type' => 'su_contracts.virtual_contract_type',
            'due_date' => 'su_contracts.due_date',
            'status' => 'su_contracts.virtual_contract_status',
            'comment' => 'su_contracts.comment',
            default => throw new InvalidArgumentException("Invalid sort column: {$sortKey}"),
        };


        $sort = array_map(
            fn ($sortOptions) => [
                'column' => $columnMapFn($sortOptions['column']),
                'direction' => $sortOptions['direction'],
            ],
            $sort ?? []
        );

        return $this->generateOrderBySQL($sort);
    }

    /**
     * Generate ORDER BY clause for plot renewal queries
     *
     * @param array|null $sort Sorting configuration array with 'column' and 'direction' keys
     * @return string|null ORDER BY clause or null if no sorting specified
     */
    private function generateOrderByClause(?array $sort): ?string
    {
        if (!$sort || !isset($sort['column']) || !isset($sort['direction'])) {
            return null;
        }

        $column = $sort['column'];
        $direction = strtoupper($sort['direction']);

        // Validate direction
        if (!in_array($direction, ['ASC', 'DESC'])) {
            $direction = 'ASC';
        }

        // Map sort columns to actual database columns/expressions
        $columnMapping = [
            'plot_id' => 'p.plot_id',
            'kad_ident' => 'p.kad_ident',
            'document_area' => 'p.document_area',
            'contract_area' => 'SUM(p.contract_area)',
            'contract_area_next_period' => 'SUM(p.new_contract_area)',
            'total_rows' => 'COUNT(*)',
            'can_renew_count' => 'COUNT(*) FILTER (WHERE p.can_renew)',
            'cannot_renew_count' => 'COUNT(*) FILTER (WHERE NOT p.can_renew)',
        ];

        // Check if the column is valid
        if (!array_key_exists($column, $columnMapping)) {
            return null;
        }

        return $columnMapping[$column] . ' ' . $direction;
    }


}
