<?php

declare(strict_types=1);

namespace App\Services\Documents\Contracts;

use App\Models\UserDb\Documents\Contracts\AbstractContractModel;
use App\Models\UserDb\Users\UserFarmingPermission;
use App\Types\DTOs\Common\ListDTO;
use Illuminate\Support\Facades\Auth;
use App\Types\Enums\Documents\DocumentTypeEnum;

abstract class AbstractContractsService
{
    public function getContractsList(array $contractIds, ?int $page = 1, ?int $limit = 10, ?array $sort = null): ListDTO
    {
        $allowedFarmingIds = $this->getAllowedFarmingIds();
        $contracts = $this->getContracts($contractIds, $allowedFarmingIds, $page, $limit, $sort);
        $total = $this->getContractsTotal($contractIds);

        return new ListDTO([
            'rows' => $contracts,
            'total' => $total,
        ]);
    }

    abstract public function getPlotsForRenew(DocumentTypeEnum $documentType, array $filterParams, ?bool $canRenew = null, ?int $page = null, ?int $limit = null, ?array $sort = null): ListDTO;

    abstract public function getContracts(
        array $contractIds,
        array $allowedFarmingIds,
        ?int $page,
        ?int $limit,
        ?array $sort
    ): array;

    abstract public function getContractsTotal(array $contractIds): int;

    abstract public function getContractsListFooter(array $contractIds): ?array;

    public function getContractDetails(AbstractContractModel $contract): ?array
    {
        $result = $this->getContractsList([
            $contract->id,
        ]);

        if (empty($result->rows)) {
            return null;
        }

        [$firstContract] = $result->rows;

        return $firstContract;
    }

    public function getAllowedFarmingIds(): array
    {
        $user = Auth::user();

        return $user->getFarmingIdsByPermissions([UserFarmingPermission::PERMISSION_READ])->toArray();
    }
}
