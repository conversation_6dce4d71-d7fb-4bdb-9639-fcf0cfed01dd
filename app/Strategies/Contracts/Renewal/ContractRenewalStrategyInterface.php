<?php

declare(strict_types=1);

namespace App\Strategies\Contracts\Renewal;

use App\Types\Enums\Documents\DocumentTypeEnum;

/**
 * Contract Renewal Strategy Interface
 * 
 * Defines the contract for different contract renewal strategies.
 * Each strategy knows how to configure the pipeline for its specific contract type.
 */
interface ContractRenewalStrategyInterface
{
    /**
     * Get the pipeline configuration for this contract type
     */
    public function getPipelineConfiguration(): array;

    /**
     * Get the eager loading relationships for this contract type
     */
    public function getEagerLoadingRelationships(): array;

    /**
     * Check if this strategy supports the given document type
     */
    public function supports(DocumentTypeEnum $documentType): bool;

    /**
     * Get the strategy name for identification
     */
    public function getName(): string;
}
