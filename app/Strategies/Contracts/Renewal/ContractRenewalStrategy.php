<?php

declare(strict_types=1);

namespace App\Strategies\Contracts\Renewal;

use App\Pipes\Contracts\Renew\CopyContractFiles;
use App\Pipes\Contracts\Renew\CopyContractPlots;
use App\Pipes\Contracts\Renew\CopyContractRents;
use App\Pipes\Contracts\Renew\CopyPlotFarmingRelationships;
use App\Pipes\Contracts\Renew\CopyPlotOwners;
use App\Pipes\Contracts\Renew\CreateRenewedContract;
use App\Pipes\Contracts\Renew\ValidateContractRenewability;
use App\Types\Enums\Documents\DocumentTypeEnum;

/**
 * Contract Renewal Strategy
 *
 * Handles the pipeline configuration for standard contracts (is_sublease = false).
 * Defines the specific pipes and relationships needed for contract renewal.
 */
class ContractRenewalStrategy implements ContractRenewalStrategyInterface
{
    public function getPipelineConfiguration(): array
    {
        return [
            ValidateContractRenewability::class,
            CreateRenewedContract::class,
            CopyContractPlots::class,
            CopyPlotOwners::class,
            CopyPlotFarmingRelationships::class,
            CopyContractRents::class,
            CopyContractFiles::class,
        ];
    }

    public function getEagerLoadingRelationships(): array
    {
        return [
            'rents',
            'files',
            'contractPlots.plot',
            'contractPlots.plotOwners.owner',
            'contractPlots.plotFarmingRelationships.ownerRep',
        ];
    }

    public function supports(DocumentTypeEnum $documentType): bool
    {
        return $documentType === DocumentTypeEnum::Contracts;
    }

    public function getName(): string
    {
        return 'contract';
    }
}