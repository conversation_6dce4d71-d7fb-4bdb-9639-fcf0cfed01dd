<?php

declare(strict_types=1);

namespace App\Strategies\Contracts\Renewal;

use App\Pipes\Contracts\Renew\CopyContractContragents;
use App\Pipes\Contracts\Renew\CopyContractFarmingContragents;
use App\Pipes\Contracts\Renew\CopyContractFiles;
use App\Pipes\Contracts\Renew\CopyContractRents;
use App\Pipes\Contracts\Renew\CopyPlotOwners;
use App\Pipes\Contracts\Renew\CopyRelatedMainContract;
use App\Pipes\Contracts\Renew\CopySubleaseContractPlots;
use App\Pipes\Contracts\Renew\CreateRenewedContract;
use App\Pipes\Contracts\Renew\ValidateContractRenewability;
use App\Types\Enums\Documents\DocumentTypeEnum;

/**
 * Sublease Contract Renewal Strategy
 * 
 * Handles the pipeline configuration for sublease contracts (is_sublease = true).
 * Defines the specific pipes and relationships needed for sublease contract renewal.
 */
class SubleaseContractRenewalStrategy implements ContractRenewalStrategyInterface
{
    public function getPipelineConfiguration(): array
    {
        return [
            ValidateContractRenewability::class,
            CreateRenewedContract::class,
            CopySubleaseContractPlots::class,
            CopyPlotOwners::class,
            CopyContractContragents::class,
            CopyContractFarmingContragents::class,
            CopyContractRents::class,
            CopyContractFiles::class,
            CopyRelatedMainContract::class,
        ];
    }

    public function getEagerLoadingRelationships(): array
    {
        return [
            'rents',
            'files',
            'contragents.owner',
            'contragents.ownerRep',
            'farmingContragents.ownerRep',
            'subleasePlotAreas.plot',
            'subleasePlotAreas.plotOwners.owner',
            'subleaseContractPlots.plotContractRelation',
        ];
    }

    public function supports(DocumentTypeEnum $documentType): bool
    {
        return $documentType === DocumentTypeEnum::Subleases;
    }

    public function getName(): string
    {
        return 'sublease_contract';
    }
}
