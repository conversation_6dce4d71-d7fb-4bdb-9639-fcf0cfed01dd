<?php

declare(strict_types=1);

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class JsonIntegerArray implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (is_string($value)) {
            $data = json_decode($value, true);

            if (JSON_ERROR_NONE !== json_last_error() || ! is_array($data)) {
                $fail('The :attribute must be a valid JSON array.');

                return;
            }

            foreach ($data as $item) {
                if (! is_int($item)) {
                    $fail('The :attribute must be an array of integers.');

                    return;
                }
            }
        } else {
            $fail('The :attribute must be a valid JSON string.');
        }
    }
}
