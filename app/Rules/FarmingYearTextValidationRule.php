<?php

declare(strict_types=1);

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class FarmingYearTextValidationRule implements ValidationRule
{
    /**
     * Validate that the value matches the farming year text format: YYYY/YYYY
     * and the second year equals the first year plus one.
     *
     * @param Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // Defer type validation to the 'string' rule if present
        if (! is_string($value)) {
            return;
        }

        if (! preg_match('/^(\d{4})\/(\d{4})$/', $value, $matches)) {
            $fail('The :attribute must be in the format "YYYY/YYYY".');

            return;
        }

        $startYear = (int) $matches[1];
        $endYear = (int) $matches[2];

        if ($endYear !== $startYear + 1) {
            $fail('The :attribute second year must be exactly the first year plus one (e.g., 2023/2024).');

            return;
        }
    }
}
