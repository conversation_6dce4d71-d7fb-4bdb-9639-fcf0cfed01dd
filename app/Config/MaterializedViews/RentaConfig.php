<?php

declare(strict_types=1);

namespace App\Config\MaterializedViews;

/**
 * Configuration for Renta-related materialized views and triggers
 * 
 * This class centralizes all configuration related to renta materialized view management,
 * including table names that have triggers and the materialized views that need refreshing.
 */
class RentaConfig
{
    /**
     * Tables that have triggers calling refresh_rentas_materialized_views()
     * 
     * These tables will have their triggers disabled during bulk operations
     * to prevent automatic materialized view refreshes.
     */
    public const TRIGGER_TABLES = [
        'layer_kvs',
        'su_charged_renta_natura',
        'su_charged_renta',
        'su_contracts_plots_rel',
        'su_contracts',
        'su_personal_use',
        'su_plots_owners_rel',
        'su_renta_types'
    ];

    /**
     * Renta-related materialized views that need manual refreshing
     * 
     * These views will be refreshed after bulk operations are completed
     * to ensure data consistency.
     */
    public const MATERIALIZED_VIEWS = [
        'charged_rentas_mat_view',
        'charged_rentas_annexes_mat_view',
        'renta_nats_mat_view',
        'renta_nats_annexes_mat_view',
    ];

    /**
     * Configuration for checking trigger state
     * 
     * Used by areTriggersDisabled() method to check if triggers are currently disabled.
     * Uses a representative table/trigger pair to determine the state.
     */
    public const TRIGGER_CHECK = [
        'table' => 'su_contracts',
        'trigger' => 'SU_CONTRACTS_TRIGGER'
    ];


}
