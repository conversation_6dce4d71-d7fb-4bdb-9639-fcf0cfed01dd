<?php

declare(strict_types=1);

namespace App\Providers;

use App\Models\UserDb\Documents\Contracts\AbstractContractModel;
use App\Services\Documents\Contracts\AbstractContractsService;
use App\Models\UserDb\Documents\Contracts\Contract;
use App\Models\UserDb\Documents\Contracts\MortgageContract;
use App\Models\UserDb\Documents\Contracts\SalesContract;
use App\Services\Documents\Contracts\ContractsService;
use App\Services\Documents\Contracts\MortgageContractsService;
use App\Services\Documents\Contracts\OwnershipContractsService;
use App\Services\Documents\Contracts\SalesContractsService;
use App\Services\Documents\Contracts\SubleasesContractsService;
use App\Types\Enums\Documents\DocumentTypeEnum;
use Illuminate\Support\ServiceProvider;

class ContractServiceProvider extends ServiceProvider
{
    public function register()
    {
        $this->app->singleton(AbstractContractModel::class, function ($app) {
            $documentType = request()->route('type');
            $contractId = request()->route('id');

            return match ($documentType) {
                DocumentTypeEnum::Sales->value => SalesContract::findOrFail($contractId),
                DocumentTypeEnum::Mortgage->value => MortgageContract::findOrFail($contractId),
                default => Contract::findOrFail($contractId),
            };

        });

        $this->app->bind(AbstractContractsService::class, function ($app) {
            $documentType = request()->route('type');

            return match ($documentType) {
                DocumentTypeEnum::Subleases->value => $app->make(SubleasesContractsService::class),
                DocumentTypeEnum::Sales->value => $app->make(SalesContractsService::class),
                DocumentTypeEnum::Mortgage->value => $app->make(MortgageContractsService::class),
                DocumentTypeEnum::Ownership->value => $app->make(OwnershipContractsService::class),
                default => $app->make(ContractsService::class),
            };
        });

    }
}
