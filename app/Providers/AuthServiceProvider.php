<?php

declare(strict_types=1);

namespace App\Providers;

use App\Services\Auth\KeycloakGuard;
use App\Services\Auth\KeycloakResourceServer;
use App\Services\Auth\TokenValidator;
use Illuminate\Contracts\Auth\Access\Gate as GateContract;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Auth;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(GateContract $gate): void
    {
        parent::registerPolicies();

        Auth::provider('keycloak', fn ($app, array $config): KeycloakUserProvider => new KeycloakUserProvider($app['hash'], $config['model']));

        Auth::extend('keycloak', fn ($app, $name, array $config): KeycloakGuard => new KeycloakGuard(
            Auth::createUserProvider($config['provider']),
            $app->make('request'),
            new KeycloakResourceServer(
                new TokenValidator()
            )
        ));
    }
}
