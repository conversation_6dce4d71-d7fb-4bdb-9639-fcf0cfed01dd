<?php

declare(strict_types=1);

namespace App\Providers;

use App\Factories\Contracts\ContractRenewalStrategyFactory;
use App\Strategies\Contracts\Renewal\RegularContractRenewalStrategy;
use App\Strategies\Contracts\Renewal\SubleaseContractRenewalStrategy;
use Illuminate\Support\ServiceProvider;

/**
 * Contract Renewal Service Provider
 * 
 * Registers contract renewal strategies and provides a centralized place
 * for configuring the renewal system. This makes it easy to add new
 * contract types or modify existing strategies.
 */
class ContractRenewalServiceProvider extends ServiceProvider
{
    /**
     * Register services
     */
    public function register(): void
    {
        // Register strategies in the factory
        $this->registerRenewalStrategies();
    }

    /**
     * Bootstrap services
     */
    public function boot(): void
    {
        //
    }

    /**
     * Register all available renewal strategies
     */
    private function registerRenewalStrategies(): void
    {
        // Core strategies are already registered in the factory
        // This method can be used to register additional strategies
        // from modules, plugins, or configuration
        
        // Example of registering additional strategies:
        // ContractRenewalStrategyFactory::registerStrategy(CustomContractRenewalStrategy::class);
    }
}
