<?php

declare(strict_types=1);

namespace App\Providers;

use App\Models\Main\User;
use Illuminate\Auth\EloquentUserProvider;
use Illuminate\Contracts\Auth\Authenticatable;

class KeycloakUserProvider extends EloquentUserProvider
{
    /**
     * Summary of validateCredentials.
     *
     * @param array<string,mixed> $credentials
     */
    public function validateCredentials(Authenticatable $user, array $credentials): bool
    {
        return true;
    }

    /**
     * Summary of retrieveByCredentials.
     *
     * @param array<string,mixed> $credentials
     *
     * @return null|User
     */
    public function retrieveByCredentials(array $credentials): ?Authenticatable
    {
        $credentials = array_filter(
            $credentials,
            fn ($key): bool => ! str_contains((string) $key, 'password'),
            ARRAY_FILTER_USE_KEY
        );

        if ([] === $credentials) {
            return null;
        }

        // First we will add each credential element to the query as a where clause.
        // Then we can execute the query and, if we found a user, return it in a
        // Eloquent User "model" that will be utilized by the Guard instances.
        $query = $this->newModelQuery(new User());

        $query->whereRaw('LOWER(username) = ?', $credentials['username']);

        return $query->first();
    }
}
