<?php

declare(strict_types=1);

namespace App\Exceptions;

use Exception;

/**
 * Exception thrown when a contract contains plots that cannot be renewed
 */
class PlotNotRenewableException extends Exception
{
    private int $contractId;
    private array $nonRenewablePlots;

    public function __construct(int $contractId, array $nonRenewablePlots, string $message = '')
    {
        $this->contractId = $contractId;
        $this->nonRenewablePlots = $nonRenewablePlots;

        if (empty($message)) {
            $plotCount = count($nonRenewablePlots);
            $plotIds = array_column($nonRenewablePlots, 'plot_id');
            $message = "Contract {$contractId} cannot be renewed: {$plotCount} plot(s) are not renewable (Plot IDs: " . implode(', ', $plotIds) . ")";
        }

        parent::__construct($message);
    }

    public function getContractId(): int
    {
        return $this->contractId;
    }

    public function getNonRenewablePlots(): array
    {
        return $this->nonRenewablePlots;
    }
}
