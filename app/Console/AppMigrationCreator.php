<?php

declare(strict_types=1);

namespace App\Console;

use Illuminate\Database\Migrations\MigrationCreator;
use Illuminate\Filesystem\Filesystem;

class AppMigrationCreator extends MigrationCreator
{
    protected string $stubFolder;

    public function __construct(Filesystem $files, ?string $customStubPath = null, string $stubFolder = 'main')
    {
        parent::__construct($files, $customStubPath);
        $this->stubFolder = $stubFolder;
    }

    /**
     * Dynamically determine which stub file to use.
     */
    protected function getStub($table, $create)
    {
        // Determine correct stub file
        $stubPath = app_path("Console/Commands/stubs/{$this->stubFolder}");

        if (is_null($table)) {
            $stubFile = "{$stubPath}/migration.stub";
        } elseif ($create) {
            $stubFile = "{$stubPath}/migration.create.stub";
        } else {
            $stubFile = "{$stubPath}/migration.update.stub";
        }

        // Return the stub content (<PERSON><PERSON> expects the actual content, not the file path)
        return $this->files->get($stubFile);
    }
}
