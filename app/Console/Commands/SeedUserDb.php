<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Models\Main\User;
use Illuminate\Database\Console\Seeds\SeedCommand;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Config;

class SeedUserDb extends SeedCommand
{
    protected $signature = 'db:seed:userdb {--database=} {--all} {class? : The name of the seeder class to run} {--force}';
    protected $description = 'Seed a specific user_db or all user_db databases';

    public function handle()
    {
        if ($this->option('all')) {
            // Get all user databases and seed each
            $userDatabases = User::all()->pluck('database')->unique();
            foreach ($userDatabases as $database) {
                $this->info("Seeding database: {$database}");
                $this->runSeederOnDatabase($database);
            }
        } elseif ($this->option('database')) {
            // Seed the specified user_db
            $database = $this->option('database');
            $this->info("Seeding database: {$database}");
            $this->runSeederOnDatabase($database);
        } else {
            $this->error('You must specify either the --database option or the --all option.');

            return 1; // Exit with an error code
        }

        return 0; // Exit with a success code
    }

    protected function runSeederOnDatabase($database)
    {
        Config::set('database.connections.user_db.database', $database);
        DB::reconnect('user_db');

        $this->input->setOption('database', 'user_db');
        parent::handle();
    }
}
