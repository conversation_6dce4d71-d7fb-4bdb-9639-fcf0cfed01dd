<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Models\Main\User;
use Illuminate\Database\Console\Migrations\RollbackCommand;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Migrations\Migrator;
use Illuminate\Support\Facades\Config;

class RollbackUserDb extends RollbackCommand
{
    protected $signature = 'migrate:userdb-rollback {--database=}  {--all} {--force} {--path=*} {--realpath} {--pretend} {--step=} {--batch=}';

    protected $description = 'Rollback migrations on a specific user_db or all user_db databases';

    public function __construct(Migrator $migrator)
    {
        parent::__construct($migrator);
    }

    public function handle()
    {
        $migrationPaths = $this->getMigrationPaths();

        if ($this->option('all')) {
            // Get all user databases and rollback migrations on each
            $userDatabases = User::all()->pluck('database')->unique();
            $userDatabases->push('new_users_db');
            foreach ($userDatabases as $database) {
                $this->info("Rolling back migrations on database: {$database}");
                $this->runRollbackOnDatabase($database, $migrationPaths);
            }
        } elseif ($this->option('database')) {
            // Rollback migrations on the specified user_db
            $database = $this->option('database');
            $this->info("Rolling back migrations on database: {$database}");
            $this->runRollbackOnDatabase($database, $migrationPaths);
        } else {
            $this->error('You must specify either the --database option or the --all option.');

            return 1; // Exit with an error code
        }

        return 0; // Exit with a success code
    }

    protected function runRollbackOnDatabase($database, $migrationPaths)
    {
        Config::set('database.connections.user_db.database', $database);
        DB::reconnect('user_db');

        $this->migrator->setConnection('user_db');

        // Get the migration files
        $files = $this->migrator->getMigrationFiles($migrationPaths);

        // Rollback the migrations
        $this->migrator->rollback($files, [
            'pretend' => $this->option('pretend'),
            'step' => $this->option('step'),
        ]);
    }

    protected function getMigrationPaths(): array
    {
        return [database_path('migrations/user_db')];
    }
}
