<?php

declare(strict_types=1);

namespace App\Console\Commands;

use Illuminate\Database\Console\Seeds\SeedCommand;
use Illuminate\Support\Facades\Config;

class SeedMainDb extends SeedCommand
{
    protected $signature = 'db:seed:main {class? : The name of the seeder class to run} {--database=main} {--force}';
    protected $description = 'Seed the main database';

    public function handle()
    {
        Config::set('database.default', 'main');
        parent::handle();
    }
}
