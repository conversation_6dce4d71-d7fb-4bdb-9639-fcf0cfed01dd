<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Models\Main\User;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Config;

class AddUserDbMigrationsTable extends Command
{
    protected $signature = 'migrate:add-users-migrations-table';
    protected $description = 'Add the migrations table for all user databases';

    public function handle()
    {
        // Get a list of all user databases
        $databases = User::all()->pluck(value: 'database')->unique();
        $databases->push('new_users_db');

        foreach ($databases as $db) {
            $this->info("Adding migrations table in database: {$db}");

            Config::set('database.connections.user_db.database', $db);

            DB::reconnect('user_db');

            if (! $this->databaseExists()) {
                $this->warn("Database {$db} does not exist. Skipping...");

                continue;
            }

            try {
                Artisan::call('migrate:install', ['--database' => 'user_db']);

            } catch (Exception $e) {
                $this->error('Error: ' . $e->getMessage());

                continue;
            }

            $this->info(Artisan::output());
        }
    }

    protected function databaseExists(): bool
    {
        try {
            DB::connection('user_db')->getPdo();

            return true;
        } catch (Exception $e) {
            return false;
        }
    }
}
