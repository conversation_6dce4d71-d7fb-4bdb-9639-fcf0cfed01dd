<?php

declare(strict_types=1);

namespace App\Console\Commands;

use Illuminate\Database\Console\Migrations\MigrateMakeCommand;
use Illuminate\Database\Migrations\MigrationCreator;
use Illuminate\Support\Composer;
use Illuminate\Filesystem\Filesystem;

class MakeMainMigration extends MigrateMakeCommand
{
    protected $signature = 'make:main-migration {name} {--create=} {--table=}';

    protected $description = 'Create a new migration file in the main folder and extend the MainDatabaseMigration class';

    public function __construct(Filesystem $files, Composer $composer)
    {
        $stubPath = app_path('Console/Commands/stubs/main');

        parent::__construct(new MigrationCreator($files, $stubPath), $composer);
    }

    /**
     * Get the migration path for storing migrations in "database/migrations/main".
     */
    protected function getMigrationPath(): string
    {
        return database_path('migrations/main');
    }
}
