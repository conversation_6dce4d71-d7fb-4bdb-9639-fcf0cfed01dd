<?php

declare(strict_types=1);

namespace App\Console\Commands;

use Illuminate\Database\Console\Migrations\RollbackCommand;
use Illuminate\Database\Migrations\Migrator;

class RollbackMainDb extends RollbackCommand
{
    protected $signature = 'migrate:main-rollback {--database=} {--force} {--path=*} {--realpath} {--pretend} {--step=} {--batch=}';
    protected $description = 'Rollback migrations on the main database';

    public function __construct(Migrator $migrator)
    {
        parent::__construct($migrator);
    }

    /**
     * Override the migration path to point to `migrations/main/`.
     */
    protected function getMigrationPaths(): array
    {
        return [database_path('migrations/main')];
    }
}
