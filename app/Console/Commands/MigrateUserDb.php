<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Models\Main\User;
use Illuminate\Database\Console\Migrations\MigrateCommand;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Migrations\Migrator;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Support\Facades\Config;

class MigrateUserDb extends MigrateCommand
{
    protected $signature = 'migrate:userdb {--database=} {--all} {--force} {--path=*} {--realpath} {--schema-path=} {--pretend} {--seed} {--step} {--continue-on-failure}';

    protected $description = 'Run migrations on a specific user_db or all user_db databases';

    public function __construct(Migrator $migrator, Dispatcher $dispatcher)
    {
        parent::__construct($migrator, $dispatcher);
    }

    public function handle()
    {

        $migrationPaths = $this->getMigrationPaths();

        if ($this->option('all')) {
            // Get all existing user databases and run migrations on each
            $userDatabases = User::select('database')
                ->join('pg_database', 'datname', '=', 'database')
                ->where('datistemplate', false)
                ->get()->pluck('database')->unique();
            $userDatabases->push('new_users_db');

            foreach ($userDatabases as $database) {
                $this->info("Adding migrations table in database: {$database}");

                $this->runMigrationsOnDatabase($database, $migrationPaths);
            }
        } elseif ($this->option('database')) {
            // Run migrations on the specified user_db
            $database = $this->option('database');
            $this->info("Running migrations on database: {$database}");
            $this->runMigrationsOnDatabase($database, $migrationPaths);
        } else {
            $this->error('You must specify either the --database option or the --all option.');

            return 1; // Exit with an error code
        }

        return 0; // Exit with a success code
    }

    protected function getMigrationPaths(): array
    {
        return [database_path('migrations/user_db')];
    }

    protected function runMigrationsOnDatabase($database, $migrationPaths)
    {
        Config::set('database.connections.user_db.database', $database);
        DB::reconnect('user_db');

        $this->migrator->setConnection('user_db');

        // Get the migration files
        $files = $this->migrator->getMigrationFiles($migrationPaths);

        if ($this->option('continue-on-failure')) {
            // Run migrations with error handling
            $this->runMigrationsWithErrorHandling($files);
        } else {
            // Run the migrations normally
            $this->migrator->run($files, [
                'pretend' => $this->option('pretend'),
                'step' => $this->option('step'),
            ]);
        }
    }

    protected function runMigrationsWithErrorHandling($files)
    {
        $repository = $this->migrator->getRepository();

        // Ensure the migration repository exists
        if (!$repository->repositoryExists()) {
            $repository->createRepository();
        }

        $ran = $repository->getRan();

        $migrations = collect($files)->reject(function ($file) use ($ran) {
            return in_array($this->migrator->getMigrationName($file), $ran);
        });

        foreach ($migrations as $file) {
            try {
                $this->info("Migrating: " . basename($file));
                $this->migrator->run([$file], [
                    'pretend' => $this->option('pretend'),
                    'step' => $this->option('step'),
                ]);
                $this->info("Migrated: " . basename($file));
            } catch (\Exception $e) {
                $this->error("Failed to migrate: " . basename($file));
                $this->error("Error: " . $e->getMessage());
                $this->warn("Continuing with next migration...");
            }
        }
    }
}
