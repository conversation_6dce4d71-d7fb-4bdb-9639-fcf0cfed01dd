<?php

declare(strict_types=1);

namespace App\Console\Commands;

use Illuminate\Database\Console\Migrations\MigrateMakeCommand;
use Illuminate\Database\Migrations\MigrationCreator;
use Illuminate\Support\Composer;
use Illuminate\Filesystem\Filesystem;

class MakeUserDbMigration extends MigrateMakeCommand
{
    protected $signature = 'make:userdb-migration {name} {--create=} {--table=}';
    protected $description = 'Create a new migration file in the user_db folder and extend the UserDbMigration class';

    public function __construct(Filesystem $files, Composer $composer)
    {
        $stubPath = app_path('Console/Commands/stubs/user_db');

        parent::__construct(new MigrationCreator($files, $stubPath), $composer);
    }

    protected function getMigrationPath(): string
    {
        return database_path('migrations/user_db');
    }
}
