<?php

use App\Database\Migrations\MainDatabaseMigration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends MainDatabaseMigration {
    public function up(): void
    {
        Schema::connection($this->connection)->table('{{table}}', function (Blueprint $table) {
            // Add columns or modifications
        });
    }

    public function down(): void
    {
         Schema::connection($this->connection)->table('{{table}}', function (Blueprint $table) {
            // Reverse changes
        });
    }
};
