<?php

use App\Database\Migrations\UserDatabaseMigration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends UserDatabaseMigration {
    public function up(): void
    {
        Schema::connection($this->connection)->table('{{table}}', function (Blueprint $table) {
            // Add columns or modifications
        });
    }

    public function down(): void
    {
         Schema::connection($this->connection)->table('{{table}}', function (Blueprint $table) {
            // Reverse changes
        });
    }
};
