<?php

declare(strict_types=1);

namespace App\Console\Commands;

use Illuminate\Database\Console\Migrations\MigrateCommand;
use Illuminate\Database\Migrations\Migrator;
use Illuminate\Contracts\Events\Dispatcher;

class MigrateMainDb extends MigrateCommand
{
    protected $signature = 'migrate:main {--database=} {--force} {--path=*} {--realpath} {--schema-path=} {--pretend} {--seed} {--step} {--refresh} {--graceful}';
    protected $description = 'Run migrations on the main database';

    public function __construct(Migrator $migrator, Dispatcher $dispatcher)
    {
        parent::__construct($migrator, $dispatcher);
    }

    /**
     * Override the migration path to point to `migrations/main/`.
     */
    protected function getMigrationPaths(): array
    {
        return [database_path('migrations/main')];
    }
}
