<?php

declare(strict_types=1);

namespace App\Console;

use App\Console\Commands\MakeUserDbMigration;
use App\Console\Commands\RollbackMainDb;
use App\Console\Commands\RollbackUserDb;
use App\Console\Commands\SeedMainDb;
use App\Console\Commands\SeedUserDb;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
use App\Console\Commands\MigrateMainDb;
use App\Console\Commands\MigrateUserDb;
use App\Console\Commands\MakeMainMigration;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        MigrateMainDb::class,
        MigrateUserDb::class,
        MakeMainMigration::class,
        MakeUserDbMigration::class,
        RollbackMainDb::class,
        RollbackUserDb::class,
        SeedMainDb::class,
        SeedUserDb::class,
    ];

    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule) {}

    /**
     * Register the commands for the application.
     */
    protected function commands()
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
