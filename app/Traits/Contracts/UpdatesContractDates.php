<?php

declare(strict_types=1);

namespace App\Traits\Contracts;

use App\Models\UserDb\Documents\Contracts\Contract;
use Carbon\Carbon;

/**
 * Updates Contract Dates Trait
 * 
 * Provides contract date calculation logic based on the legacy TechnoFarm system.
 * Implements the same business rules as the original replaceContractDates method
 * from ContractsTree.php using modern Laravel/Carbon methods.
 */
trait UpdatesContractDates
{
    /**
     * Update contract dates using legacy TechnoFarm logic
     * Based on replaceContractDates method from ContractsTree.php
     * 
     * @param Contract $renewedContract The contract to update dates for
     * @param Contract $originalContract The original contract to base calculations on
     */
    protected function updateContractDates(Contract $renewedContract, Contract $originalContract): void
    {
        // Extract years from original contract dates
        $startYear = Carbon::parse($originalContract->start_date)->year;
        $dueYear = Carbon::parse($originalContract->due_date)->year;
        
        // Create base dates (October 1st to September 30th - standard contract year)
        $baseStartDate = Carbon::create($startYear, 10, 1); // October 1st of start year
        $baseDueDate = Carbon::create($dueYear, 9, 30);     // September 30th of due year
        
        // Calculate period between original dates
        $period = $dueYear - $startYear;
        
        // Calculate new dates based on period
        if ($period < 1) {
            // Less than 1 year - add days
            $periodInDays = $baseDueDate->diffInDays($baseStartDate) + 1;
            $renewedContract->start_date = $baseStartDate->copy()->addDays($periodInDays);
            $renewedContract->due_date = $baseDueDate->copy()->addDays($periodInDays);
        } else {
            // 1 year or more - add years
            $renewedContract->start_date = $baseStartDate->copy()->addYears($period);
            $renewedContract->due_date = $baseDueDate->copy()->addYears($period);
        }
        
        // Contract date is always today
        $renewedContract->c_date = Carbon::now();
    }

    /**
     * Calculate the period between contract dates
     * 
     * @param Contract $contract The contract to analyze
     * @return array ['years' => int, 'isLongerThanYear' => bool]
     */
    protected function calculateContractPeriod(Contract $contract): array
    {
        $startYear = Carbon::parse($contract->start_date)->year;
        $dueYear = Carbon::parse($contract->due_date)->year;
        
        $years = $dueYear - $startYear;
        $isLongerThanYear = $years > 1;
        
        return [
            'years' => $years,
            'isLongerThanYear' => $isLongerThanYear,
        ];
    }

    /**
     * Get the standard contract year base dates (October 1st to September 30th)
     * 
     * @param int $startYear The year to base the calculation on
     * @return array ['start' => Carbon, 'end' => Carbon]
     */
    protected function getStandardContractYearDates(int $startYear): array
    {
        return [
            'start' => Carbon::create($startYear, 10, 1),      // October 1st
            'end' => Carbon::create($startYear + 1, 9, 30),    // September 30th next year
        ];
    }
}
