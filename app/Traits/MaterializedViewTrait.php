<?php

declare(strict_types=1);

namespace App\Traits;

use Illuminate\Support\Facades\DB;

trait MaterializedViewTrait
{
    /**
     * Disable materialized view triggers during bulk operations
     *
     * Uses PostgreSQL ALTER TABLE DISABLE TRIGGER ALL syntax to disable
     * all triggers on specified tables.
     * This prevents automatic refreshes during bulk operations.
     */
    public function disableMatViewTriggers(array $tableNames): void
    {
        try {
            foreach ($tableNames as $table) {
                DB::statement("ALTER TABLE public.{$table} DISABLE TRIGGER ALL");
            }
        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * Enable materialized view triggers after bulk operations
     *
     * Uses PostgreSQL ALTER TABLE ENABLE TRIGGER ALL syntax to re-enable
     * all triggers on specified tables.
     * This restores automatic refreshes after bulk operations.
     */
    public function enableMatViewTriggers(array $tableNames): void
    {
        try {
            foreach ($tableNames as $table) {
                DB::statement("ALTER TABLE public.{$table} ENABLE TRIGGER ALL");
            }
        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * Refresh multiple materialized views
     *
     * Manually refreshes specified materialized views to ensure data consistency
     * after bulk operations are completed.
     */
    public function refreshMatViews(array $viewNames): void
    {
        try {
            foreach ($viewNames as $view) {
                DB::statement("REFRESH MATERIALIZED VIEW public.{$view}");
            }
        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * Refresh a specific materialized view
     */
    public function refreshView(string $viewName): void
    {
        $sql = "REFRESH MATERIALIZED VIEW public.{$viewName}";

        try {
            DB::statement($sql);
        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * Execute a callback with disabled triggers
     *
     * Provides a safe way to execute bulk operations with automatic
     * trigger management and cleanup.
     */
    public function withDisabledTriggers(array $tableNames, callable $callback): mixed
    {
        $this->disableMatViewTriggers($tableNames);

        try {
            $result = $callback();
            return $result;
        } finally {
            $this->enableMatViewTriggers($tableNames);
        }
    }

    /**
     * Execute a callback with full materialized view management
     *
     * Disables triggers, executes callback, enables triggers, and refreshes views.
     * This is the complete workflow for bulk operations.
     */
    public function withManagedViews(array $tableNames, array $viewNames, callable $callback): mixed
    {
        $this->disableMatViewTriggers($tableNames);

        try {
            $result = $callback();
            return $result;
        } finally {
            $this->enableMatViewTriggers($tableNames);
            $this->refreshMatViews($viewNames);
        }
    }

    /**
     * Check if materialized view triggers are currently disabled
     *
     * This is a diagnostic method to check if triggers are disabled on the tables.
     * Checks a specific trigger on a specific table to determine trigger state.
     */
    public function areTriggersDisabled(string $tableName, string $triggerName, string $procName = null): bool
    {
        $sql = "SELECT tgenabled FROM pg_trigger
                WHERE tgname = '{$triggerName}'
                AND tgrelid = 'public.{$tableName}'::regclass";

        try {
            $result = DB::selectOne($sql);

            if ($result && isset($result->tgenabled)) {
                // tgenabled: 'O' = enabled, 'D' = disabled
                return $result->tgenabled === 'D';
            }

            return false;
        } catch (\Exception $e) {
            return false;
        }
    }
}
