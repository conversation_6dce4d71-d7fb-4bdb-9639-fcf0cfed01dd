<?php

declare(strict_types=1);

namespace App\Traits\Common;

use DateTime;
use DateInterval;
use DatePeriod;
use Exception;

/**
 * Trait FarmingYearsTrait.
 *
 * Provides functionality for handling farming years in agricultural applications.
 * A farming year typically runs from October 1st to September 30th of the following year,
 * which aligns with agricultural seasons and crop cycles.
 *
 * Example: Farming year 2023/2024 runs from October 1, 2023 to September 30, 2024
 */
trait FarmingYearsTrait
{
    /**
     * Base year coefficient used for generating unique farming year IDs
     * All farming year IDs are calculated relative to this base year.
     */
    protected const KEY_FROM_YEAR_COEFFICIENT = 2009;

    /**
     * Start date of farming year (October 1st)
     * Format: MM-DD.
     */
    protected const START_FARMING_YEAR = '10-01';

    /**
     * End date of farming year (September 30th)
     * Format: MM-DD.
     */
    protected const END_FARMING_YEAR = '09-30';

    /**
     * Get the current farming year information based on today's date.
     *
     * This method determines which farming year we are currently in based on the current date.
     * Since farming years run from October to September, dates from October onwards belong
     * to the next calendar year's farming season.
     *
     * @return array Farming year information array containing:
     *               - id: Unique identifier for the farming year
     *               - title: Display title (e.g., "2023 г.")
     *               - default: Boolean indicating if this is the default year
     *               - year: Starting calendar year
     *               - farming_year: Full farming year text (e.g., "Стопанска 2023/2024 г.")
     *               - farming_year_short: Short farming year text (e.g., "2023/2024 г.")
     *               - start_date: Farming year start date (YYYY-MM-DD format)
     *               - end_date: Farming year end date (YYYY-MM-DD format)
     *
     * @example
     * // If called on November 15, 2023, returns info for farming year 2023/2024
     * $currentYear = FarmingYearsTrait::getCurrentFarmingYear();
     * // Returns: ['id' => 15, 'year' => '2023', 'farming_year' => 'Стопанска 2023/2024 г.', ...]
     */
    public static function getCurrentFarmingYear()
    {
        return self::getFarmingYearInfoFromDate(new DateTime('now'));
    }

    /**
     * Get all unique farming years that occur within a given date period.
     *
     * This method analyzes a date range and returns information about all farming years
     * that overlap with the specified period. It iterates through each day in the period
     * and collects unique farming year information.
     *
     * @param string $start Start date of the period (any valid date format)
     * @param string $end End date of the period (any valid date format)
     *
     * @throws Exception If the date strings cannot be parsed
     *
     * @return array Associative array where keys are farming year IDs and values are
     *               farming year information arrays (same structure as getFarmingYearInfoFromDate)
     *
     * @example
     * // Get farming years between January 2023 and January 2024
     * $years = FarmingYearsTrait::getFarmingYearsInPeriod('2023-01-01', '2024-01-31');
     * // Returns: [14 => [...2022/2023 info...], 15 => [...2023/2024 info...]]
     */
    public static function getFarmingYearsInPeriod($start, $end)
    {
        $result = [];
        $interval = DateInterval::createFromDateString('1 day');
        $period = new DatePeriod(new DateTime($start), $interval, new DateTime($end));

        // Iterate through each day in the period to find all unique farming years
        foreach ($period as $dt) {
            $fy = self::getFarmingYearInfoFromDate($dt);
            // Only add if we haven't seen this farming year ID before
            if (! array_key_exists($fy['id'], $result)) {
                $result[$fy['id']] = $fy;
            }
        }

        return $result;
    }

    /**
     * Parse farming year filter parameter and return farming year information.
     *
     * This method takes a farming year string in the format "YYYY/YYYY" (e.g., "2023/2024")
     * and returns the complete farming year information. It's commonly used when processing
     * filter parameters from user interfaces or API requests.
     *
     * @param string $farmingYear Farming year string in format "YYYY/YYYY" (e.g., "2023/2024")
     *
     * @throws Exception If the farming year string format is invalid or cannot be parsed
     *
     * @return array Complete farming year information array (same structure as getFarmingYearInfoFromDate)
     *
     * @example
     * $info = FarmingYearsTrait::getFarmingYearInfoByFilterParam('2023/2024');
     * // Returns complete info for farming year 2023/2024
     */
    public static function getFarmingYearInfoByFilterParam($farmingYear)
    {
        [$farmingYearOffset, $farmingYear] = explode('/', $farmingYear);

        return self::getFarmingYearInfoFromDate(new DateTime($farmingYearOffset . '-' . self::START_FARMING_YEAR));
    }

    /**
     * Extract start and end dates from farming year text.
     *
     * This method parses a farming year string and returns only the start and end dates
     * without the full farming year information. It's useful when you only need the
     * date boundaries for queries or calculations.
     *
     * @param string $farmingYear Farming year string in format "YYYY/YYYY" (e.g., "2023/2024")
     *
     * @throws Exception If the farming year string format is invalid
     *
     * @return array Associative array with 'start_date' and 'end_date' keys:
     *               - start_date: Farming year start date in YYYY-MM-DD format
     *               - end_date: Farming year end date in YYYY-MM-DD format
     *
     * @example
     * $dates = FarmingYearsTrait::getStartEndDatesByFarmingYearText('2023/2024');
     * // Returns: ['start_date' => '2023-10-01', 'end_date' => '2024-09-30']
     */
    public static function getStartEndDatesByFarmingYearText($farmingYear)
    {
        [$farmingYearOffset, $farmingYear] = explode('/', $farmingYear);

        return [
            'start_date' => $farmingYearOffset . '-' . self::START_FARMING_YEAR,
            'end_date' => $farmingYear . '-' . self::END_FARMING_YEAR,
        ];
    }

    /**
     * Generate complete farming year information from a given date.
     *
     * This is the core method that determines which farming year a specific date belongs to
     * and returns comprehensive information about that farming year. The farming year logic:
     * - Dates from January 1 to September 30: belong to the farming year that started in the previous October
     * - Dates from October 1 to December 31: belong to the farming year that starts in the current October
     *
     * The method generates a unique ID for each farming year based on the KEY_FROM_YEAR_COEFFICIENT,
     * which allows for consistent identification across the application.
     *
     * @param DateTime|string $date Date to analyze (DateTime object or any valid date string)
     *
     * @throws Exception If the date string cannot be parsed into a DateTime object
     *
     * @return array Comprehensive farming year information:
     *               - id: Unique numeric identifier (farming_year - 2009)
     *               - title: Display title with Bulgarian suffix (e.g., "2023 г.")
     *               - default: Always false (used for UI selection states)
     *               - year: Starting calendar year of the farming year
     *               - farming_year: Full Bulgarian text (e.g., "Стопанска 2023/2024 г.")
     *               - farming_year_short: Short format (e.g., "2023/2024")
     *               - start_date: Farming year start date (YYYY-10-01)
     *               - end_date: Farming year end date (YYYY-09-30)
     *
     * @example
     * // Date in January 2024 belongs to farming year 2023/2024
     * $info = FarmingYearsTrait::getFarmingYearInfoFromDate('2024-01-15');
     * // Returns: ['id' => 15, 'year' => '2023', 'farming_year' => 'Стопанска 2023/2024 г.', ...]
     *
     * // Date in November 2024 belongs to farming year 2024/2025
     * $info = FarmingYearsTrait::getFarmingYearInfoFromDate('2024-11-15');
     * // Returns: ['id' => 16, 'year' => '2024', 'farming_year' => 'Стопанска 2024/2025 г.', ...]
     */
    public static function getFarmingYearInfoFromDate($date)
    {
        // Ensure we have a DateTime object
        if (! $date instanceof DateTime) {
            $date = new DateTime($date);
        }

        // Get the calendar year from the date
        $year = $farmingYear = $date->format('Y');

        // If the date is on or after October 1st of the current year,
        // it belongs to the farming year that ends in the following year
        if ($date >= new DateTime($year . '-' . self::START_FARMING_YEAR)) {
            $farmingYear++;
        }

        // Generate unique ID based on the ending year of the farming year
        $key = $farmingYear - self::KEY_FROM_YEAR_COEFFICIENT;

        return [
            'id' => $key,
            'title' => $year . ' г.',
            'default' => false,
            'year' => $year,
            'farming_year' => 'Стопанска ' . $year . '/' . $farmingYear . ' г.',
            'farming_year_short' => $year . '/' . $farmingYear,
            'start_date' => $year . '-' . self::START_FARMING_YEAR,
            'end_date' => $farmingYear . '-' . self::END_FARMING_YEAR,
        ];
    }
}
