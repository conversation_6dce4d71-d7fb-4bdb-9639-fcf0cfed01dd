<?php

declare(strict_types=1);

namespace App\Types\DTOs\Common;

class ListDTO
{
    public array $rows;
    public int $total;

    public function __construct(array $data = [])
    {
        $this->rows = $data['rows'] ?? [];
        $this->total = $data['total'] ?? 0;
    }

    public function toArray(): array
    {
        return [
            'rows' => $this->rows,
            'total' => $this->total,
        ];
    }
}
