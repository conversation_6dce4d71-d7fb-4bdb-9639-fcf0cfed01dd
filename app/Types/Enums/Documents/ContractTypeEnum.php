<?php

declare(strict_types=1);

namespace App\Types\Enums\Documents;

use Illuminate\Support\Collection;

enum ContractTypeEnum: int
{
    case Ownership = 1;
    case Lease = 2;
    case Rent = 3;
    case Agreement = 4;
    case JointProcessing = 5;
    case Sales = 6;
    case Mortgage = 7;
    case Sublease = 8;

    public function name(): string
    {
        return match($this) {
            ContractTypeEnum::Ownership => 'Собственост',
            ContractTypeEnum::Lease => 'Аренда',
            ContractTypeEnum::Rent => 'Наем',
            ContractTypeEnum::Agreement => 'Споразумение',
            ContractTypeEnum::JointProcessing => 'Съвместна обработка',
            ContractTypeEnum::Sales => 'Продажба',
            ContractTypeEnum::Mortgage => 'Ипотека',
            ContractTypeEnum::Sublease => 'Преотдаден',
        };
    }

    public static function values(): Collection
    {
        return collect(self::cases())->map(fn ($case) => $case->value);
    }
    
    public static function renewableTypes(): array
    {
        return [
            self::Lease->value,
            self::Rent->value,
            self::JointProcessing->value,
        ];
    }
}
