<?php

declare(strict_types=1);

namespace App\Types\Enums\Plots;

enum Article37vInclusionEnum: string
{
    case YES = 'Yes';
    case NO = 'No';
    case YES_WHITE_SPOTS = 'Yes - white spots';
    case WITHOUT = 'Without';

    /**
     * Determine the Article 37v inclusion status based on database columns
     *
     * @param bool|null $include
     * @param bool|null $participate
     * @param bool|null $whiteSpots
     * @return self
     */
    public static function fromDatabaseColumns(?bool $include, ?bool $participate, ?bool $whiteSpots): self
    {
        // If white_spots is true, always return YES_WHITE_SPOTS
        if ($whiteSpots === true) {
            return self::YES_WHITE_SPOTS;
        }

        // If participate is true, return YES
        if ($participate === true) {
            return self::YES;
        }

        // If include is true, participate is false, and white_spots is false, return NO
        if ($include === true && $participate === false && $whiteSpots === false) {
            return self::NO;
        }

        // If all are false (or null), return WITHOUT
        if ($include === false && $participate === false && $whiteSpots === false) {
            return self::WITHOUT;
        }

        // Default case - return WITHOUT
        return self::WITHOUT;
    }

    /**
     * Get all possible values as an array
     *
     * @return array<string>
     */
    public static function values(): array
    {
        return array_map(fn($case) => $case->value, self::cases());
    }


}
