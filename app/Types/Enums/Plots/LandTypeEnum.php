<?php

declare(strict_types=1);

namespace App\Types\Enums\Plots;

use Illuminate\Support\Collection;

enum LandTypeEnum: string
{
    case Arable = 'Arable';
    case NonArable = 'NonArable';

    public static function getOptions(): Collection
    {
        return collect([
            ['key' => true, 'value' => self::Arable->value],
            ['key' => false, 'value' => self::NonArable->value],
        ]);
    }
}
