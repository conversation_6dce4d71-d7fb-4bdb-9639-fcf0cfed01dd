<?php

declare(strict_types=1);

namespace App\Http\Controllers\Plots;

use App\Actions\Plots\GetPlotCategoriesAction;
use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;

/**
 * Controller for managing plot categories
 * 
 * This controller handles API endpoints for plot categories management,
 * providing access to plot category data with key and value fields.
 */
class PlotCategoriesController extends Controller
{
    /**
     * Display a listing of plot categories.
     * 
     * Returns all plot categories without pagination, ordered by title.
     * Each category includes key (id) and value (title) fields.
     *
     * @param GetPlotCategoriesAction $action
     * @return JsonResponse
     */
    public function index(GetPlotCategoriesAction $action): JsonResponse
    {
        return $action->execute();
    }
}
