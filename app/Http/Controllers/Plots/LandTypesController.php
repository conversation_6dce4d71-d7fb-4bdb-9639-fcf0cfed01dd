<?php

declare(strict_types=1);

namespace App\Http\Controllers\Plots;

use App\Http\Controllers\Controller;
use App\Http\Resources\Plots\LandTypeResourceCollection;
use App\Types\Enums\Plots\LandTypeEnum;
use Illuminate\Http\JsonResponse;

class LandTypesController extends Controller
{
    public function index(): JsonResponse
    {
        return response()->json((
            new LandTypeResourceCollection(
                LandTypeEnum::getOptions()
            ))->resolve());
    }
}
