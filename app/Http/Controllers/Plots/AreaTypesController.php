<?php

declare(strict_types=1);

namespace App\Http\Controllers\Plots;

use App\Actions\Plots\GetAreaTypesAction;
use App\Http\Controllers\Controller;
use App\Http\Requests\Plots\GetAreaTypesRequest;
use Illuminate\Http\JsonResponse;

/**
 * Controller for managing area types
 * 
 * This controller handles API endpoints for area types management,
 * providing access to area type data from the su_area_types materialized view
 * with filtering capabilities by title and id.
 */
class AreaTypesController extends Controller
{
    /**
     * Display a listing of area types with optional search and pagination.
     *
     * Query parameters:
     * - search: searches both ID (exact match if numeric) and title (partial case-insensitive match)
     * - page: page number (default: 1)
     * - limit: items per page (default: 10)
     *
     * If no search parameter is provided, returns first 10 results.
     * If search parameter is provided, returns paginated filtered results.
     * Each area type includes id and title fields.
     *
     * @param GetAreaTypesRequest $request
     * @param GetAreaTypesAction $action
     * @return JsonResponse
     */
    public function index(GetAreaTypesRequest $request, GetAreaTypesAction $action): JsonResponse
    {
        return $action->execute($request->validated());
    }
}
