<?php

declare(strict_types=1);

namespace App\Http\Controllers\Rent;

use App\Actions\Rent\GetRentInKindTypesAction;
use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;

/**
 * Controller for managing rent-in-kind types
 * 
 * This controller handles API endpoints for rent-in-kind type management,
 * providing RESTful access to rent type data with proper unit relationships.
 */
class RentInKindController extends Controller
{
    /**
     * Display a listing of rent-in-kind types.
     * 
     * Returns all rent-in-kind types with their associated unit information,
     * including the unit's short_name for display purposes.
     *
     * @param GetRentInKindTypesAction $action
     * @return JsonResponse

     */
    public function index(GetRentInKindTypesAction $action): JsonResponse
    {
        return $action->execute();
    }
}
