<?php

declare(strict_types=1);

namespace App\Http\Controllers\Rent;

use App\Actions\Rent\GetRentTypeOptionsAction;
use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;

/**
 * Controller for managing rent type options
 * 
 * This controller handles API endpoints for rent type options management,
 * providing access to rent type option data with id, title, and value fields.
 */
class RentTypeOptionsController extends Controller
{
    /**
     * Display a listing of rent type options.
     * 
     * Returns all rent type options without pagination, ordered by title.
     * Each option includes id, title, and value fields.
     *
     * @param GetRentTypeOptionsAction $action
     * @return JsonResponse
     */
    public function index(GetRentTypeOptionsAction $action): JsonResponse
    {
        return $action->execute();
    }
}
