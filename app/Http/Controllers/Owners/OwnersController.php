<?php

declare(strict_types=1);

namespace App\Http\Controllers\Owners;

use App\Actions\Owners\GetOwnersAction;
use App\Actions\Owners\GetOwnersTreeAction;
use App\Http\Controllers\Controller;
use App\Http\Requests\Owners\GetOwnersFilterRequest;
use App\Http\Requests\Owners\GetOwnersFilterItemsRequest;
use App\Http\Requests\Owners\GetOwnersTreeRequest;
use App\Services\Owners\OwnersFilter;
use Illuminate\Http\JsonResponse;

class OwnersController extends Controller
{
    /**
     * Get paginated list of owners with filtering
     */
    public function index(GetOwnersFilterRequest $request, GetOwnersAction $action): JsonResponse
    {
        return $action->execute($request->validated());
    }

    public function tree(GetOwnersTreeRequest $request, GetOwnersTreeAction $action): JsonResponse
    {
        return $action->execute($request->validated());
    }

    /**
     * Get filter items for dropdowns/autocomplete
     */
    public function getFilterItems(GetOwnersFilterItemsRequest $request): JsonResponse
    {
        $reqParams = $request->validated();
        $page = (int) $request->input('page', 1);
        $limit = (int) $request->input('limit', 10);
        $colName = $request->input('col_name');
        $search = $request->input('search');

        // Use su_owners as base table to include owners without contracts
        $ownersFilter = new OwnersFilter('su_owners');
        $filterItems = $ownersFilter
            ->withParams($reqParams['filter_params'] ?? [])
            ->getFilterItemsPaginated($colName, $search, $page, $limit);

        return response()->json($filterItems->toArray());
    }
}
