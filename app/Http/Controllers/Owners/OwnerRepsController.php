<?php

declare(strict_types=1);

namespace App\Http\Controllers\Owners;

use App\Actions\Owners\GetOwnerRepsAction;
use App\Http\Controllers\Controller;
use App\Http\Requests\Owners\GetOwnerRepsRequest;
use Illuminate\Http\JsonResponse;

class OwnerRepsController extends Controller
{
    /**
     * Get paginated list of owner representatives with search functionality
     */
    public function index(GetOwnerRepsRequest $request, GetOwnerRepsAction $getOwnerRepsAction): JsonResponse
    {
        $validated = $request->validated();
        $page = (int) ($validated['page'] ?? 1);
        $limit = (int) ($validated['limit'] ?? 10);

        return $getOwnerRepsAction->execute($validated, $page, $limit);
    }
}
