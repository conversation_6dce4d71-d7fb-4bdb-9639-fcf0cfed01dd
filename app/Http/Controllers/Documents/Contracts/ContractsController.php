<?php

declare(strict_types=1);

namespace App\Http\Controllers\Documents\Contracts;

use App\Http\Controllers\Controller;
use App\Http\Requests\Documents\Contracts\GetContractsRequest;
use App\Http\Requests\Documents\Contracts\GetContractsFilterItemsRequest;
use App\Http\Resources\Documents\ContractCollection;
use App\Http\Resources\Documents\ContractResource;
use App\Models\UserDb\Documents\Contracts\AbstractContractModel;
use App\Services\ContractPlotFilter\ContractPlotFilter;
use App\Services\Documents\Contracts\AbstractContractsService;
use App\Types\Enums\Documents\DocumentTypeEnum;
use Illuminate\Http\JsonResponse;

class ContractsController extends Controller
{
    public function show(string $type, AbstractContractModel $contract, AbstractContractsService $contractsService): JsonResponse
    {
        $contractData = $contractsService->getContractDetails($contract);

        if (null === $contractData) {
            return response()->json(['message' => 'Contract not found'], 404);
        }

        return response()->json(new ContractResource($contractData));
    }

    public function index(string $type, GetContractsRequest $request, AbstractContractsService $contractsService): JsonResponse
    {
        $reqParams = $request->validated();
        $page = (int) $request->input('page', 1);
        $limit = (int) $request->input('limit', 10);
        $sort = $request->input('sort', ['column' => 'id', 'direction' => 'desc']);
        $documentType = DocumentTypeEnum::from($type);


        $baseTable = $documentType->tableName();

        $contractPlotFilter = new ContractPlotFilter($baseTable);
        $contractIds = $contractPlotFilter
            ->withParams($reqParams['filter_params'])
            ->getQuery()
            ->select("{$baseTable}.id")
            ->distinct()
            ->get()
            ->pluck('id')
            ->toArray();

        if (empty($contractIds)) {
            return response()->json([
                'rows' => [],
                'total' => 0,
                'footer' => null,
            ]);
        }

        $contractsList = $contractsService->getContractsList($contractIds, $page, $limit, $sort);

        $contractsListFooter = null;
        if ($request->includeFooter()) {
            $contractsListFooter = $contractsService->getContractsListFooter($contractIds);
        }

        $collection = new ContractCollection($contractsList->rows);
        $collection->additional([
            'total' => $contractsList->total,
            'footer' => $contractsListFooter,
        ]);

        return response()->json($collection);
    }

    public function getFilterItems(string $type, GetContractsFilterItemsRequest $request): JsonResponse
    {
        $reqParams = $request->validated();
        $page = (int) $request->input('page', 1);
        $limit = (int) $request->input('limit', 10);
        $colName = $request->input('col_name');
        $search = $request->input('search');
        $documentType = DocumentTypeEnum::from($type);

        $baseTable = $documentType->tableName();
        $contractPlotFilter = new ContractPlotFilter($baseTable);
        $filterItems = $contractPlotFilter
            ->withParams($reqParams['filter_params'])
            ->getFilterItemsPaginated($colName, $search, $page, $limit);

        return response()->json($filterItems->toArray());
    }
}
