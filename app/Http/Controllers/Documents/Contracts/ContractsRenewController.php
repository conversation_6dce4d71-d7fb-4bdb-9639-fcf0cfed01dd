<?php

declare(strict_types=1);

namespace App\Http\Controllers\Documents\Contracts;

use App\Actions\Contracts\RenewContractsAction;
use App\Http\Controllers\Controller;
use App\Http\Requests\Documents\Contracts\GetContractsRenewFilterItemsRequest;
use App\Http\Requests\Documents\Contracts\GetContractsRenewRequest;
use App\Http\Resources\Documents\RenewContractsCollection;
use App\Services\ContractPlotFilter\ContractPlotFilter;
use App\Services\Documents\Contracts\AbstractContractsService;
use App\Traits\Common\FarmingYearsTrait;
use App\Types\Enums\Documents\DocumentTypeEnum;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use App\Http\Requests\Documents\Contracts\RenewContractsRequest;

class ContractsRenewController extends Controller
{
    use FarmingYearsTrait;

    public function index(string $type, GetContractsRenewRequest $request, AbstractContractsService $contractsService): JsonResponse
    {
        $page = (int) $request->input('page', 1);
        $limit = (int) $request->input('limit', 10);
        $sort = $request->input('sort', ['column' => 'id', 'direction' => 'desc']);
        $documentType = DocumentTypeEnum::from($type);

        $requestParams = $request->validated();

        $renewContractsList = $contractsService->getPlotsForRenew(
            $documentType,
            $requestParams['filter_params'],
            null,
            $page,
            $limit,
            $sort
        );

        $collection = new RenewContractsCollection($renewContractsList->rows);

        return response()->json($collection);
    }

    public function getFilterItems(string $type, GetContractsRenewFilterItemsRequest $request): JsonResponse
    {
        $page = (int) $request->input('page', 1);
        $limit = (int) $request->input('limit', 10);
        $colName = $request->input('col_name');
        $search = $request->input('search');
        $documentType = DocumentTypeEnum::from($type);

        $baseTable = $documentType->tableName();
        $contractPlotFilter = new ContractPlotFilter($baseTable);
        $requestParams = $request->validated();
        $filterItems = $contractPlotFilter
            ->withParams($requestParams['filter_params'])
            ->getFilterItemsPaginated($colName, $search, $page, $limit);

        return response()->json($filterItems->toArray());
    }

    /**
     * Renew multiple contracts with their relations
     *
     * @param string $type Document type (contracts or subleases)
     * @param RenewContractsRequest $request
     * @param RenewContractsAction $action
     * @return JsonResponse
     */
    public function renewContracts(
        string $type,
        RenewContractsRequest $request,
        RenewContractsAction $action
    ): JsonResponse {
        try {
            $documentType = DocumentTypeEnum::from($type);
            $requestParams = $request->validated();


            $action->execute($requestParams, $documentType);

            return response()->json([], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An unexpected error occurred while renewing contracts.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

}
