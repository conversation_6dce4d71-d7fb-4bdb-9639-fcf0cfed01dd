<?php

declare(strict_types=1);

namespace App\Http\Controllers\Documents;

use App\Actions\Plots\GetAvailablePlotsAction;
use App\Http\Controllers\Controller;
use App\Http\Requests\Plots\GetAvailablePlotsRequest;
use Illuminate\Http\JsonResponse;

class DocumentPlotsController extends Controller
{
    public function available(string $documentType, GetAvailablePlotsRequest $request, GetAvailablePlotsAction $getAvailablePlotsAction): JsonResponse
    {
        $validated = $request->validated();
        $filterParams = $request->getFilterParams();

        $filterParams['document_type'] = $documentType;

        $page = (int) ($validated['page'] ?? 1);
        $limit = (int) ($validated['limit'] ?? 10);

        return $getAvailablePlotsAction->execute($filterParams, $page, $limit);
    }
}
