<?php

declare(strict_types=1);

namespace App\Http\Controllers\Contracts;

use App\Actions\Contracts\GetContractGroupsAction;
use App\Actions\Contracts\StoreContractGroupAction;
use App\Actions\Contracts\UpdateContractGroupAction;
use App\Http\Controllers\Controller;
use App\Models\UserDb\Documents\Contracts\ContractGroup;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Controller for managing contract groups
 * 
 * This controller handles API endpoints for contract group management,
 * providing RESTful access to contract group data.
 */
class ContractGroupController extends Controller
{
    /**
     * Display a listing of contract groups.
     *
     * Returns all contract groups available in the system.
     *
     * @param GetContractGroupsAction $action
     * @return JsonResponse
     */
    public function index(GetContractGroupsAction $action): JsonResponse
    {
        return $action->execute();
    }

    /**
     * Store a newly created contract group.
     *
     * @param Request $request
     * @param StoreContractGroupAction $action
     * @return JsonResponse
     */
    public function store(Request $request, StoreContractGroupAction $action): JsonResponse
    {
        return $action->execute($request->all());
    }

    /**
     * Update the specified contract group.
     *
     * @param Request $request
     * @param ContractGroup $contractGroup
     * @param UpdateContractGroupAction $action
     * @return JsonResponse
     */
    public function update(Request $request, ContractGroup $contractGroup, UpdateContractGroupAction $action): JsonResponse
    {
        return $action->execute($contractGroup, $request->all());
    }
}
