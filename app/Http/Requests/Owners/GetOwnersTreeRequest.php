<?php

declare(strict_types=1);

namespace App\Http\Requests\Owners;

use Illuminate\Foundation\Http\FormRequest;

class GetOwnersTreeRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'search' => ['string', 'nullable'],

            'page' => ['integer', 'nullable', 'min:1'],
            'limit' => ['integer', 'nullable', 'min:1', 'max:100']
        ];
    }
}
