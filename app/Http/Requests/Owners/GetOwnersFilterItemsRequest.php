<?php

declare(strict_types=1);

namespace App\Http\Requests\Owners;

use Illuminate\Validation\Rule;

class GetOwnersFilterItemsRequest extends GetOwnersFilterRequest
{
    public function rules(): array
    {
        return [
            'col_name' => ['string', Rule::in([
                'search',
                'cnum',
                'contragent_phone',
                'contragent_comment',
                'kad_ident',
                'virtual_ekatte_name',
            ])],
            ...parent::rules(),
        ];
    }
}
