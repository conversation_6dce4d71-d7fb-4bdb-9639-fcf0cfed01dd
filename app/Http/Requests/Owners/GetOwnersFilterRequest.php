<?php

declare(strict_types=1);

namespace App\Http\Requests\Owners;

use Illuminate\Foundation\Http\FormRequest;

class GetOwnersFilterRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'filter_params' => ['required', 'array'],
            'filter_params.groups' => ['array'],
            'filter_params.groups.*.kad_ident' => ['array'],
            'filter_params.groups.*.kad_ident.*' => ['string', 'nullable'],
            'filter_params.groups.*.ekatte' => ['array'],
            'filter_params.groups.*.ekatte.*' => ['string', 'nullable'],
            'filter_params.groups.*.cnum' => ['array'],
            'filter_params.groups.*.cnum.*' => ['string', 'nullable'],
            'filter_params.groups.*.search' => ['array'],
            'filter_params.groups.*.search.*' => ['string', 'nullable'],
            'filter_params.groups.*.contragent_phone' => ['array'],
            'filter_params.groups.*.contragent_phone.*' => ['string', 'nullable'],
            'filter_params.groups.*.contragent_comment' => ['array'],
            'filter_params.groups.*.contragent_comment.*' => ['string', 'nullable'],
            'page' => ['integer', 'nullable', 'min:1'],
            'limit' => ['integer', 'nullable', 'min:1', 'max:100'],
            'sort' => ['array', 'nullable'],
            'sort.*.column' => ['string'],
            'sort.*.direction' => ['string', 'in:asc,desc'],
        ];
    }

    protected function prepareForValidation()
    {
        // Decode the filter_params JSON string into an associative array
        $filterParamsDecoded = json_decode($this->input('filter_params', '[]'), true);

        if (is_array($filterParamsDecoded)) {
            $this->merge(['filter_params' => $filterParamsDecoded]);
        }

        // Decode the sort JSON string into an associative array
        $sortDecoded = json_decode($this->input('sort', '[]'), true);
        if (is_array($sortDecoded)) {
            $this->merge(['sort' => $sortDecoded]);
        }
    }
}
