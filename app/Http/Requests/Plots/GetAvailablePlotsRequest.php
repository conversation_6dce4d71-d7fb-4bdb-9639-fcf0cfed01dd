<?php

declare(strict_types=1);

namespace App\Http\Requests\Plots;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Arr;

/**
 * Request for getting available plots for contract/sublease creation
 * Requires date range for availability calculations
 */
class GetAvailablePlotsRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'search' => ['string', 'nullable'],
            'start_date' => ['date', 'required'],
            'due_date' => ['date', 'required', 'after_or_equal:start_date'],
            'page' => ['integer', 'nullable', 'min:1'],
            'limit' => ['integer', 'nullable', 'min:1', 'max:100'],
        ];
    }

    public function getFilterParams(): array
    {
        $validated = $this->validated();
        $filterParams = [];

        $filterParams['search'] = [Arr::get($validated, 'search', null)];
        
        $filterParams['available_area_calculation'] = [
            'start_date' => $validated['start_date'],
            'due_date' => $validated['due_date']
        ];

        return $filterParams;
    }
}
