<?php

declare(strict_types=1);

namespace App\Http\Requests\Documents\Contracts;

use App\Rules\FarmingYearTextValidationRule;

class GetContractsRenewRequest extends BaseGetContractsRenewRequest
{
    public function rules(): array
    {
        return [
            ...parent::rules(),
            'filter_params.farming_year' => ['required', 'string', new FarmingYearTextValidationRule()],
        ];
    }
}
