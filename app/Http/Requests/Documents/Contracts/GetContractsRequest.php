<?php

declare(strict_types=1);

namespace App\Http\Requests\Documents\Contracts;

class GetContractsRequest extends BaseGetContractsRequest
{
    public function includeFooter(): bool
    {
        $reqParams = $this->validated();
        $filterGroups = collect($reqParams['filter_params']['groups'] ?? []);

        return count($filterGroups) > 0 && $filterGroups->every(function ($group) {
            return 1 === count($group['farming_years'] ?? []) && 1 === count($group['farming_name'] ?? []);
        });
    }
}
