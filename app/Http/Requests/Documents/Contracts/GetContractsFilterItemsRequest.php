<?php

declare(strict_types=1);

namespace App\Http\Requests\Documents\Contracts;

use Illuminate\Validation\Rule;

class GetContractsFilterItemsRequest extends BaseGetContractsRequest
{
    public function rules(): array
    {
        return [
            'col_name' => ['string', Rule::in(
                'kad_ident',
                'virtual_ekatte_name',
                'masiv',
                'number',
                'virtual_category_title',
                'virtual_ntp_title',
                'mestnost',
                'block',
                'allowable_type',
                'irrigated_area',
                'participation',
                'comment',
                'plot_id',
                'virtual_contract_type',
                'cnum',
                'contract_date_from',
                'contract_date_to',
                'incomplete_ownership_details',
                'group',
                'closed_for_editing',
                'notary_number',
                'contract_comment',
                'contract_status_text',
                'farming_name',
                'farming_years',
                'rent_type',
                'specific_rent_type',
                'rent_kind_type',
                'creditor',
                'buyer',
                'tenant',
                'contragent',
                'contragent_type',
                'contract_signer',
                'contragent_phone',
                'contragent_comment',
                'owner_name',
                'owner_egn',
                'owner_name_egn',
                'company_name',
                'company_eik',
                'heritor_name',
                'heritor_egn',
                'rep_egn',
                'rep_name',
                'person_name',
                'person_egn',
                'egn_subekt',
                'osz_ime_subekt',
                'owner_multi_field_search',
                'farm_year_active_contract_plots',
                'expiring_contracts_for_farm_year',
                'for_sublease_farm_years',
            )],
            ...parent::rules(),
        ];
    }
}
