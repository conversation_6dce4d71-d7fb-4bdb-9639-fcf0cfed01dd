<?php

declare(strict_types=1);

namespace App\Http\Requests\Documents\Contracts;

use Illuminate\Validation\Rule;
use App\Rules\FarmingYearTextValidationRule;

class GetContractsRenewFilterItemsRequest extends BaseGetContractsRenewRequest
{
    public function rules(): array
    {
        return [
            ...parent::rules(),
            'col_name' => ['string', Rule::in(
                'kad_ident',
                'virtual_ekatte_name',
                'cnum',
                'virtual_contract_type',
                'farming_name',
                'farming_year',
                'contragent',
                'tenant',
                'buyer',
                'creditor'
            )],
            'filter_params.farming_year' => ['string', new FarmingYearTextValidationRule()],
        ];
    }
}
