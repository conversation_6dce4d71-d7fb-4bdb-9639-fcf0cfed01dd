<?php

declare(strict_types=1);

namespace App\Http\Requests\Documents\Contracts;

use App\Rules\FarmingYearTextValidationRule;



/**
 * Renew Contracts Request
 *
 * Validates the input for renewing multiple contracts with their relations.
 * Uses filter_params structure with optional contract_ids filter for direct selection:
 *
 * Example with contract_ids:
 * {
 *   "filter_params": {
 *     "contract_ids": [1, 2, 3]
 *   }
 * }
 *
 * Example with other filters:
 * {
 *   "filter_params": {
 *     "kad_ident": ["123.45.67"],
 *     "can_renew": true
 *   }
 * }
 */
class RenewContractsRequest extends BaseGetContractsRenewRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; 
    }

    /**
     * Prepare the data for validation.
     * Extends parent logic to handle contract_ids in addition to filter_params
     */
    protected function prepareForValidation()
    {
        parent::prepareForValidation();
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $rules = parent::rules();

        // Add validation for contract_ids as an optional filter within filter_params
        $rules['filter_params.contract_ids'] = ['sometimes', 'array', 'min:1'];
        $rules['filter_params.contract_ids.*'] = ['integer', 'min:1', 'exists:su_contracts,id'];
        $rules['filter_params.farming_year'] = ['string', new FarmingYearTextValidationRule()];

        return $rules;
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return array_merge(parent::messages(), [
            'filter_params.contract_ids.array' => 'Contract IDs must be an array.',
            'filter_params.contract_ids.min' => 'At least one contract ID is required.',
            'filter_params.contract_ids.*.integer' => 'Each contract ID must be an integer.',
            'filter_params.contract_ids.*.min' => 'Each contract ID must be greater than 0.',
            'filter_params.contract_ids.*.exists' => 'One or more contracts do not exist.',
        ]);
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return array_merge(parent::attributes(), [
            'filter_params.contract_ids' => 'contract IDs',
            'filter_params.contract_ids.*' => 'contract ID',
        ]);
    }


}
