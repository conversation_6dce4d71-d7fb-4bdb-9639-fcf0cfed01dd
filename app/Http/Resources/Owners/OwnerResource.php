<?php

declare(strict_types=1);

namespace App\Http\Resources\Owners;

use Illuminate\Database\Eloquent\Casts\Json;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OwnerResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'surname' => $this->surname,
            'lastname' => $this->lastname,
            'company_name' => $this->company_name,
            'owner_name' => $this->owner_name,
            'egn' => $this->egn,
            'eik' => $this->eik,
            'phone' => $this->phone,
            'mobile' => $this->mobile,
            'email' => $this->email,
            'is_dead' => (bool) $this->is_dead,
            'owner_type' => $this->owner_type,
            'remark' => $this->remark,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'children' => is_string($this->children) ? Json::decode($this->children) : $this->children,
            'current_farming_year_contracts' => $this->when(
                property_exists($this->resource, 'current_farming_year_contracts'),
                function () { return $this->current_farming_year_contracts; }
            ),
            'plots_area_sum' => $this->when(
                property_exists($this->resource, 'plots_area_sum'),
                function () { return $this->plots_area_sum; }
            ),
            'is_heritor' => $this->when(
                property_exists($this->resource, 'is_heritor'),
                function () { return (bool) $this->is_heritor; }
            ),
            'has_heritors' => $this->when(
                property_exists($this->resource, 'has_heritors'),
                function () { return (bool) $this->has_heritors; }
            ),
            'greatest_grandparent' => $this->when(
                property_exists($this->resource, 'greatest_grandparent'),
                function () { return $this->greatest_grandparent; }
            ),
            'greatest_grandparent_name' => $this->when(
                property_exists($this->resource, 'greatest_grandparent_name'),
                function () { return $this->greatest_grandparent_name; }
            ),
            'path' => $this->when(
                property_exists($this->resource, 'path'),
                function () { return $this->path; }
            ),
            'rep_name' => $this->when(
                property_exists($this->resource, 'rep_name'),
                function () { return $this->rep_name; }
            ),

        ];
    }
}
