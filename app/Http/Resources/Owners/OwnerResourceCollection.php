<?php

declare(strict_types=1);

namespace App\Http\Resources\Owners;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class OwnerResourceCollection extends ResourceCollection
{
    /**
     * The resource that this resource collects.
     *
     * @var string
     */
    public $collects = OwnerResource::class;

    /**
     * Transform the resource collection into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'rows' => $this->collection,
            'total' => $this->when(
                $this->resource instanceof \Illuminate\Pagination\LengthAwarePaginator,
                $this->resource->total()
            )
            ];
    }
}
