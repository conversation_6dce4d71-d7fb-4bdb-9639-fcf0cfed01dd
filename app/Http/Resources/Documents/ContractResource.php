<?php

declare(strict_types=1);

namespace App\Http\Resources\Documents;

use App\Types\Enums\Documents\ContractTypeEnum as ContractType;
use App\Types\Enums\Documents\ContractCardTypeEnum;
use Illuminate\Http\Resources\Json\JsonResource;

class ContractResource extends JsonResource
{
    public const TOP_LEFT = 'topLeft';

    /**
     * Transform the resource into an array.
     */
    public function toArray($request): array
    {
        $details = $this->resource['details'];
        $plots = $this->resource['plots'];

        return [
            ...$this->resource,
            'details' => [
                ...$details, // Keep all existing details data
                'cards' => $this->buildDetailsCards($details), // Add cards
            ],
            'plots' => [
                ...$plots, // Keep all existing plots data
                'columns' => $this->getPlotsColumns(),
            ],
        ];
    }

    /**
     * Build annuity card data based on contract details.
     *
     * @param array $details Contract details containing rent information
     *
     * @return array Formatted card data with rent items
     */
    private function buildDetailsCards(array $details): array
    {
        $cards = [];
        $contractType = $details['c_type'];

        // Annuity card for types Rent, Lease, JointProcessing, and Sublease
        if (in_array($contractType, [ContractType::Rent->value, ContractType::Lease->value, ContractType::JointProcessing->value, ContractType::Sublease->value])) {
            $cards[] = $this->buildAnnuityCard($details);
        }

        // Registration Service card for all types EXCEPT JointProcessing
        if ($contractType !== ContractType::JointProcessing->value) {
            $cards[] = $this->buildRegistrationServiceCard($details);
        }

        // Notarial deed card for Sales, Mortgage, and Ownership
        if (in_array($contractType, [ContractType::Sales->value, ContractType::Mortgage->value, ContractType::Ownership->value])) {
            $cards[] = $this->buildNotarialDeedCard($details);
        }

        // OSZ card for types Rent, and Lease
        if (in_array($contractType, [ContractType::Rent->value, ContractType::Lease->value])) {
            $cards[] = $this->buildOSZCard($details);
        }

        return $cards;
    }

    /**
     * Build annuity card data based on contract details.
     *
     * @param array $details Contract details containing rent information
     *
     * @return array Formatted card data with rent items
     */
    private function buildAnnuityCard(array $details): array
    {
        $items = [];

        // Cash rent - check if we have overall_renta or renta
        if (! empty($details['overall_renta'])) {
            $items[] = [
                'title' => 'In cash-fixed',
                'value' => [
                    [
                        'name' => null,
                        'unit' => 'lv/dka',
                        'quantity' => $details['overall_renta'],
                    ],
                ],
            ];
        } elseif (! empty($details['renta'])) {
            $items[] = [
                'title' => 'In cash',
                'value' => [
                    [
                        'name' => null,
                        'unit' => 'lv/dka',
                        'quantity' => $details['renta'],
                    ],
                ],
            ];
        }

        // In kind rent - use directly from database as it's already in the correct format
        $items[] = [
            'title' => 'In kind',
            'value' => $details['renta_nat'] ?? [],
        ];

        return [
            'title' => ContractCardTypeEnum::Annuity->value,
            'items' => $items,
        ];
    }

    private function buildRegistrationServiceCard(array $details): array
    {
        return [
            'title' => ContractCardTypeEnum::RegistrationService->value,
            'items' => [
                [
                    'title' => 'Contract number',
                    'value' => [
                        [
                            'name' => null,
                            'unit' => null,
                            'quantity' => $details['sv_num'] ?? '',
                        ],
                    ],
                ],
                [
                    'title' => 'Date',
                    'value' => [
                        [
                            'name' => null,
                            'unit' => null,
                            'quantity' => $details['sv_date'] ?? '',
                        ],
                    ],
                ],
            ],
        ];
    }

    private function buildNotarialDeedCard(array $details): array
    {
        return [
            'title' => ContractCardTypeEnum::NotarialDeed->value,
            'items' => [
                [
                    'title' => 'Notarial number',
                    'value' => [
                        [
                            'name' => null,
                            'unit' => null,
                            'quantity' => $details['na_num'] ?? '',
                        ],
                    ],
                ],
                [
                    'title' => 'Delo',
                    'value' => [
                        [
                            'name' => null,
                            'unit' => null,
                            'quantity' => $details['delo'] ?? '',
                        ],
                    ],
                ],
                [
                    'title' => 'Tom',
                    'value' => [
                        [
                            'name' => null,
                            'unit' => null,
                            'quantity' => $details['tom'] ?? '',
                        ],
                    ],
                ],
                [
                    'title' => 'Court',
                    'value' => [
                        [
                            'name' => null,
                            'unit' => null,
                            'quantity' => $details['court'] ?? '',
                        ],
                    ],
                ],
            ],
        ];
    }

    private function buildOSZCard(array $details): array
    {
        return [
            'title' => ContractCardTypeEnum::OSZ->value,
            'items' => [
                [
                    'title' => 'Number',
                    'value' => [
                        [
                            'name' => null,
                            'unit' => null,
                            'quantity' => $details['osz_num'] ?? '',
                        ],
                    ],
                ],
                [
                    'title' => 'Date',
                    'value' => [
                        [
                            'name' => null,
                            'unit' => null,
                            'quantity' => $details['osz_date'] ?? '',
                        ],
                    ],
                ],
            ],
        ];
    }

    private function getPlotsColumns(): array
    {
        $contractType = $this->resource['details']['c_type'];

        return match($contractType) {
            ContractType::Sales->value => $this->getSalesContractColumns(),
            ContractType::Mortgage->value => $this->getMortgageContractColumns(),
            ContractType::Ownership->value => $this->getOwnershipContractColumns(),
            ContractType::Sublease->value => $this->getSubleaseContractColumns(),
            default => $this->getDefaultContractColumns()
        };
    }

    private function getSalesContractColumns(): array
    {
        return [
            [
                'name' => null,
                'title' => [],
                'sortable' => false,
                'tooltip' => null,
                'rowspan' => 2,
                'colspan' => null,
                'width' => '3%',
                'dataType' => null,
            ],
            [
                'name' => 'kad_ident',
                'title' => ['Identifier'],
                'sortable' => true,
                'tooltip' => [
                    'title' => ['Identifier'],
                    'placement' => self::TOP_LEFT,
                ],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '15%',
                'dataType' => 'text',
            ],
            [
                'name' => 'ekatte_name',
                'title' => ['Land'],
                'sortable' => true,
                'tooltip' => [
                    'title' => ['Land'],
                    'placement' => self::TOP_LEFT,
                ],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '10%',
                'dataType' => 'text',
            ],
            [
                'name' => 'area_group',
                'title' => ['Area (dka)'],
                'sortable' => false,
                'tooltip' => [
                    'title' => ['Area (dka)'],
                    'placement' => self::TOP_LEFT,
                ],
                'rowspan' => null,
                'colspan' => 2,
                'width' => null,
                'dataType' => null,
                'children' => [
                    [
                        'name' => 'contract_area',
                        'title' => ['sales'],
                        'sortable' => true,
                        'tooltip' => [
                            'title' => ['sales'],
                            'placement' => self::TOP_LEFT,
                        ],
                        'width' => '11%',
                        'dataType' => 'number',
                    ],
                    [
                        'name' => 'document_area',
                        'title' => ['by document'],
                        'sortable' => true,
                        'tooltip' => [
                            'title' => ['by document'],
                            'placement' => self::TOP_LEFT,
                        ],
                        'width' => '11%',
                        'dataType' => 'number',
                    ],
                ],
            ],
            [
                'name' => 'price',
                'title' => ['Sold area (dka)'],
                'sortable' => true,
                'tooltip' => [
                    'title' => ['Sold area (dka)'],
                    'placement' => self::TOP_LEFT,
                ],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '10%',
                'dataType' => 'number',
            ],
            [
                'name' => 'plot_profit',
                'title' => ['Amount'],
                'sortable' => true,
                'tooltip' => [
                    'title' => ['Amount'],
                    'placement' => self::TOP_LEFT,
                ],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '10%',
                'dataType' => 'currency',
            ],
            [
                'name' => 'ntp_title',
                'title' => ['NTP'],
                'sortable' => true,
                'tooltip' => [
                    'title' => ['NTP'],
                    'placement' => self::TOP_LEFT,
                ],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '10%',
                'dataType' => 'text',
            ],
            [
                'name' => 'category_title',
                'title' => ['Category'],
                'sortable' => true,
                'tooltip' => [
                    'title' => ['Category'],
                    'placement' => self::TOP_LEFT,
                ],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '10%',
                'dataType' => 'text',
            ],
            [
                'name' => 'comment',
                'title' => ['Note'],
                'sortable' => true,
                'tooltip' => [
                    'title' => ['Note'],
                    'placement' => self::TOP_LEFT,
                ],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '10%',
                'dataType' => 'text',
            ],
        ];
    }

    private function getDefaultContractColumns(): array
    {
        return [
            [
                'name' => null,
                'title' => [],
                'sortable' => false,
                'tooltip' => null,
                'rowspan' => 2,
                'colspan' => null,
                'width' => '3%',
                'dataType' => null,
            ],
            [
                'name' => 'kad_ident',
                'title' => ['Identifier'],
                'sortable' => true,
                'tooltip' => [
                    'title' => ['Identifier'],
                    'placement' => self::TOP_LEFT,
                ],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '15%',
                'dataType' => 'text',
            ],
            [
                'name' => 'ekatte_name',
                'title' => ['Land'],
                'sortable' => true,
                'tooltip' => [
                    'title' => ['Land'],
                    'placement' => self::TOP_LEFT,
                ],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '10%',
                'dataType' => 'text',
            ],
            [
                'name' => 'area_group',
                'title' => ['Area (dka)'],
                'sortable' => false,
                'tooltip' => [
                    'title' => ['Area (dka)'],
                    'placement' => self::TOP_LEFT,
                ],
                'rowspan' => null,
                'colspan' => 4,
                'width' => null,
                'dataType' => null,
                'children' => [
                    [
                        'name' => 'contract_area',
                        'title' => ['by contract'],
                        'sortable' => true,
                        'tooltip' => [
                            'title' => ['by contract'],
                            'placement' => self::TOP_LEFT,
                        ],
                        'width' => '9%',
                        'dataType' => 'number',
                    ],
                    [
                        'name' => 'area_for_rent',
                        'title' => ['for rent'],
                        'sortable' => true,
                        'tooltip' => [
                            'title' => ['for rent'],
                            'placement' => self::TOP_LEFT,
                        ],
                        'width' => '9%',
                        'dataType' => 'number',
                    ],
                    [
                        'name' => 'allowable_area',
                        'title' => ['allowable'],
                        'sortable' => true,
                        'tooltip' => [
                            'title' => ['allowable'],
                            'placement' => self::TOP_LEFT,
                        ],
                        'width' => '9%',
                        'dataType' => 'number',
                    ],
                    [
                        'name' => 'document_area',
                        'title' => ['by document'],
                        'sortable' => true,
                        'tooltip' => [
                            'title' => ['by document'],
                            'placement' => self::TOP_LEFT,
                        ],
                        'width' => '9%',
                        'dataType' => 'number',
                    ],
                ],
            ],
            [
                'name' => 'rent_per_plot',
                'title' => ['Rent per plot'],
                'sortable' => true,
                'tooltip' => [
                    'title' => ['Rent per plot'],
                    'placement' => self::TOP_LEFT,
                ],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '9%',
                'dataType' => 'number',
            ],
            [
                'name' => 'ntp_title',
                'title' => ['NTP'],
                'sortable' => true,
                'tooltip' => [
                    'title' => ['NTP'],
                    'placement' => self::TOP_LEFT,
                ],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '9%',
                'dataType' => 'text',
            ],
            [
                'name' => 'category_title',
                'title' => ['Category'],
                'sortable' => true,
                'tooltip' => [
                    'title' => ['Category'],
                    'placement' => self::TOP_LEFT,
                ],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '9%',
                'dataType' => 'text',
            ],
            [
                'name' => 'comment',
                'title' => ['Note'],
                'sortable' => true,
                'tooltip' => [
                    'title' => ['Note'],
                    'placement' => self::TOP_LEFT,
                ],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '9%',
                'dataType' => 'text',
            ],
        ];
    }

    private function getMortgageContractColumns(): array
    {
        return [
            [
                'name' => null,
                'title' => [],
                'sortable' => false,
                'tooltip' => null,
                'rowspan' => 2,
                'colspan' => null,
                'width' => '3%',
                'dataType' => null,
            ],
            [
                'name' => 'kad_ident',
                'title' => ['Identifier'],
                'sortable' => true,
                'tooltip' => [
                    'title' => ['Identifier'],
                    'placement' => self::TOP_LEFT,
                ],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '15%',
                'dataType' => 'text',
            ],
            [
                'name' => 'ekatte_name',
                'title' => ['Land'],
                'sortable' => true,
                'tooltip' => [
                    'title' => ['Land'],
                    'placement' => self::TOP_LEFT,
                ],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '13%',
                'dataType' => 'text',
            ],
            [
                'name' => 'area_group',
                'title' => ['Area (dka)'],
                'sortable' => false,
                'tooltip' => [
                    'title' => ['Area (dka)'],
                    'placement' => self::TOP_LEFT,
                ],
                'rowspan' => null,
                'colspan' => 2,
                'width' => null,
                'dataType' => null,
                'children' => [
                    [
                        'name' => 'contract_area',
                        'title' => ['mortgaged'],
                        'sortable' => true,
                        'tooltip' => [
                            'title' => ['mortgaged'],
                            'placement' => self::TOP_LEFT,
                        ],
                        'width' => '14%',
                        'dataType' => 'number',
                    ],
                    [
                        'name' => 'document_area',
                        'title' => ['by document'],
                        'sortable' => true,
                        'tooltip' => [
                            'title' => ['by document'],
                            'placement' => self::TOP_LEFT,
                        ],
                        'width' => '14%',
                        'dataType' => 'number',
                    ],
                ],
            ],
            [
                'name' => 'ntp_title',
                'title' => ['NTP'],
                'sortable' => true,
                'tooltip' => [
                    'title' => ['NTP'],
                    'placement' => self::TOP_LEFT,
                ],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '13%',
                'dataType' => 'text',
            ],
            [
                'name' => 'category_title',
                'title' => ['Category'],
                'sortable' => true,
                'tooltip' => [
                    'title' => ['Category'],
                    'placement' => self::TOP_LEFT,
                ],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '13%',
                'dataType' => 'text',
            ],
            [
                'name' => 'comment',
                'title' => ['Note'],
                'sortable' => true,
                'tooltip' => [
                    'title' => ['Note'],
                    'placement' => self::TOP_LEFT,
                ],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '13%',
                'dataType' => 'text',
            ],
        ];
    }

    private function getOwnershipContractColumns(): array
    {
        return [
            [
                'name' => null,
                'title' => [],
                'sortable' => false,
                'tooltip' => null,
                'rowspan' => 2,
                'colspan' => null,
                'width' => '3%',
                'dataType' => null,
            ],
            [
                'name' => 'kad_ident',
                'title' => ['Identifier'],
                'sortable' => true,
                'tooltip' => [
                    'title' => ['Identifier'],
                    'placement' => self::TOP_LEFT,
                ],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '15%',
                'dataType' => 'text',
            ],
            [
                'name' => 'ekatte_name',
                'title' => ['Land'],
                'sortable' => true,
                'tooltip' => [
                    'title' => ['Land'],
                    'placement' => self::TOP_LEFT,
                ],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '10%',
                'dataType' => 'text',
            ],
            [
                'name' => 'area_group',
                'title' => ['Area (dka)'],
                'sortable' => false,
                'tooltip' => [
                    'title' => ['Area (dka)'],
                    'placement' => self::TOP_LEFT,
                ],
                'rowspan' => null,
                'colspan' => 3,
                'width' => null,
                'dataType' => null,
                'children' => [
                    [
                        'name' => 'contract_area',
                        'title' => ['own'],
                        'sortable' => true,
                        'tooltip' => [
                            'title' => ['own'],
                            'placement' => self::TOP_LEFT,
                        ],
                        'width' => '9%',
                        'dataType' => 'number',
                    ],
                    [
                        'name' => 'allowable_area',
                        'title' => ['allowable'],
                        'sortable' => true,
                        'tooltip' => [
                            'title' => ['allowable'],
                            'placement' => self::TOP_LEFT,
                        ],
                        'width' => '9%',
                        'dataType' => 'number',
                    ],
                    [
                        'name' => 'document_area',
                        'title' => ['by document'],
                        'sortable' => true,
                        'tooltip' => [
                            'title' => ['by document'],
                            'placement' => self::TOP_LEFT,
                        ],
                        'width' => '9%',
                        'dataType' => 'number',
                    ],
                ],
            ],
            [
                'name' => 'price',
                'title' => ['Sold area (dka)'],
                'sortable' => true,
                'tooltip' => [
                    'title' => ['Sold area (dka)'],
                    'placement' => self::TOP_LEFT,
                ],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '9%',
                'dataType' => 'number',
            ],
            [
                'name' => 'plot_profit',
                'title' => ['Amount'],
                'sortable' => true,
                'tooltip' => [
                    'title' => ['Amount'],
                    'placement' => self::TOP_LEFT,
                ],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '10%',
                'dataType' => 'number',
            ],
            [
                'name' => 'ntp_title',
                'title' => ['NTP'],
                'sortable' => true,
                'tooltip' => [
                    'title' => ['NTP'],
                    'placement' => self::TOP_LEFT,
                ],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '9%',
                'dataType' => 'text',
            ],
            [
                'name' => 'category_title',
                'title' => ['Category'],
                'sortable' => true,
                'tooltip' => [
                    'title' => ['Category'],
                    'placement' => self::TOP_LEFT,
                ],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '9%',
                'dataType' => 'text',
            ],
            [
                'name' => 'comment',
                'title' => ['Note'],
                'sortable' => true,
                'tooltip' => [
                    'title' => ['Note'],
                    'placement' => self::TOP_LEFT,
                ],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '9%',
                'dataType' => 'text',
            ],
        ];
    }

    private function getSubleaseContractColumns(): array
    {
        return
            [
                [
                    'name' => null,
                    'title' => [],
                    'sortable' => false,
                    'tooltip' => null,
                    'rowspan' => 2,
                    'colspan' => null,
                    'width' => '3%',
                    'dataType' => null,
                ],
                [
                    'name' => 'kad_ident',
                    'title' => ['Identifier'],
                    'sortable' => true,
                    'tooltip' => [
                        'title' => ['Identifier'],
                        'placement' => self::TOP_LEFT,
                    ],
                    'rowspan' => 2,
                    'colspan' => null,
                    'width' => '15%',
                    'dataType' => 'text',
                ],
                [
                    'name' => 'ekatte_name',
                    'title' => ['Land'],
                    'sortable' => true,
                    'tooltip' => [
                        'title' => ['Land'],
                        'placement' => self::TOP_LEFT,
                    ],
                    'rowspan' => 2,
                    'colspan' => null,
                    'width' => '11.4%',
                    'dataType' => 'text',
                ],
                [
                    'name' => 'area_group',
                    'title' => ['Area (dka)'],
                    'sortable' => false,
                    'tooltip' => [
                        'title' => ['Area (dka)'],
                        'placement' => self::TOP_LEFT,
                    ],
                    'rowspan' => null,
                    'colspan' => 3,
                    'width' => null,
                    'dataType' => null,
                    'children' => [
                        [
                            'name' => 'contract_area',
                            'title' => ['sublease'],
                            'sortable' => true,
                            'tooltip' => [
                                'title' => ['sublease'],
                                'placement' => self::TOP_LEFT,
                            ],
                            'width' => '12%',
                            'dataType' => 'number',
                        ],
                        [
                            'name' => 'area_for_rent',
                            'title' => ['for rent'],
                            'sortable' => true,
                            'tooltip' => [
                                'title' => ['for rent'],
                                'placement' => self::TOP_LEFT,
                            ],
                            'width' => '12%',
                            'dataType' => 'number',
                        ],
                        [
                            'name' => 'document_area',
                            'title' => ['by document'],
                            'sortable' => true,
                            'tooltip' => [
                                'title' => ['by document'],
                                'placement' => self::TOP_LEFT,
                            ],
                            'width' => '12%',
                            'dataType' => 'number',
                        ],
                    ],
                ],
                [
                    'name' => 'ntp_title',
                    'title' => ['NTP'],
                    'sortable' => true,
                    'tooltip' => [
                        'title' => ['NTP'],
                        'placement' => self::TOP_LEFT,
                    ],
                    'rowspan' => 2,
                    'colspan' => null,
                    'width' => '11.4%',
                    'dataType' => 'text',
                ],
                [
                    'name' => 'category_title',
                    'title' => ['Category'],
                    'sortable' => true,
                    'tooltip' => [
                        'title' => ['Category'],
                        'placement' => self::TOP_LEFT,
                    ],
                    'rowspan' => 2,
                    'colspan' => null,
                    'width' => '11.4%',
                    'dataType' => 'text',
                ],
                [
                    'name' => 'comment',
                    'title' => ['Note'],
                    'sortable' => true,
                    'tooltip' => [
                        'title' => ['Note'],
                        'placement' => self::TOP_LEFT,
                    ],
                    'rowspan' => 2,
                    'colspan' => null,
                    'width' => '11.4%',
                    'dataType' => 'text',
                ],
            ];
    }
}
