<?php

declare(strict_types=1);

namespace App\Http\Resources\Plots;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AreaTypeResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'key' => $this->id,
            'value' => $this->title,
        ];
    }
}
