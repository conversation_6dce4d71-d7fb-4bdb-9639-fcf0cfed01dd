<?php

declare(strict_types=1);

namespace App\Http\Resources\Plots;

use App\Types\Enums\Plots\Article37vInclusionEnum;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PlotResource extends JsonResource
{
    /**
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'gid' => $this->resource->gid,
            'name' => [
                'kad_ident' => $this->resource->kad_ident,
                'document_area' => $this->resource->document_area,
                'ekatte_name' => $this->resource->virtual_ekatte_name,
            ],
            'type' => [
                'ntp_title' => $this->resource->virtual_ntp_title,
                'category_title' => $this->resource->virtual_category_title,
                'short_category_title' => trim($this->resource->virtual_category_title ?? '', 'Категория '),
            ],
            'document_area' => $this->resource->document_area,
            'area_type' => $this->resource->area_type,
            'allowable_area' => $this->resource->allowable_area,
            'contract_area' => $this->resource->contract_area,
            'rent_area' => $this->resource->rent_area,
            'rent_allowable_area' => $this->resource->rent_allowable_area,
            'irrigated_area' => $this->resource->irrigated_area,
            'include' => $this->resource->include,
            'participate' => $this->resource->participate,
            'white_spots' => $this->resource->white_spots,
            'include_in_article_37v' => Article37vInclusionEnum::fromDatabaseColumns(
                $this->resource->include,
                $this->resource->participate,
                $this->resource->white_spots
            )->value,            
            'virtual_ekatte_name' => $this->resource->virtual_ekatte_name,
            'masiv' => $this->resource->masiv,
            'number' => $this->resource->number,
            'block' => $this->resource->block,
            'allowable_type' => $this->resource->allowable_type,
            'comment' => $this->resource->comment,
            'rent_per_plot' => null,
            'actions' => null
        ];
    }

    public static function getPlotHeaders(): array
    {
        return [
            [
                'name' => null,
                'title' => [],
                'sortable' => false,
                'tooltip' => null,
                'rowspan' => 2,
                'colspan' => null,
                'width' => '3%',
                'dataType' => null,
            ],
            [
                'name' => 'name',
                'title' => ['Plot'],
                'sortable' => false,
                'tooltip' => [
                    'title' => ['Plot'],
                    'placement' => 'top-left',
                ],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '20%',
                'dataType' => null,
                'children' => [],
            ],
            [
                'name' => 'type',
                'title' => ['Type'],
                'sortable' => false,
                'tooltip' => [
                    'title' => ['Type'],
                    'placement' => 'top-left',
                ],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '15%',
                'dataType' => null,
                'children' => [],
            ],
            [
                'name' => 'area_group',
                'title' => ['Area (dka)'],
                'sortable' => false,
                'tooltip' => [
                    'title' => ['Area (dka)'],
                    'placement' => 'top-left',
                ],
                'rowspan' => null,
                'colspan' => 3,
                'width' => null,
                'dataType' => null,
                'children' => [
                    [
                        'name' => 'contract_area',
                        'title' => ['by contract'],
                        'sortable' => false,
                        'tooltip' => [
                            'title' => ['by contract'],
                            'placement' => 'top-left',
                        ],
                        'width' => '9%',
                        'dataType' => 'number',
                    ],
                    [
                        'name' => 'allowable_area',
                        'title' => ['allowable'],
                        'sortable' => false,
                        'tooltip' => [
                            'title' => ['allowable'],
                            'placement' => 'top-left',
                        ],
                        'width' => '9%',
                        'dataType' => 'number',
                    ],
                    [
                        'name' => 'rent_area',
                        'title' => ['for rent'],
                        'sortable' => false,
                        'tooltip' => [
                            'title' => ['for rent'],
                            'placement' => 'top-left',
                        ],
                        'width' => '9%',
                        'dataType' => 'number',
                    ],
                ],
            ],
            [
                'name' => 'include_in_article_37v',
                'title' => ['Article 37v'],
                'sortable' => false,
                'tooltip' => [
                    'title' => ['Article 37v'],
                    'placement' => 'top-left',
                ],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '8%',
                'dataType' => 'text',
            ],
            [
                'name' => 'rent_per_plot',
                'title' => ['Rent per plot'],
                'sortable' => false,
                'tooltip' => [
                    'title' => ['Rent per plot'],
                    'placement' => 'top-left',
                ],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '8%',
                'dataType' => 'number',
            ],
            [
                'name' => 'comment',
                'title' => ['Comment'],
                'sortable' => false,
                'tooltip' => [
                    'title' => ['Plot Comment'],
                    'placement' => 'top-left',
                ],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '10%',
                'dataType' => 'text',
            ],
            [
                'name' => 'actions',
                'title' => ['Actions'],
                'sortable' => false,
                'tooltip' => [
                    'title' => ['Available Actions'],
                    'placement' => 'top-left',
                ],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '8%',
                'dataType' => null,
            ],
        ];
    }
}
