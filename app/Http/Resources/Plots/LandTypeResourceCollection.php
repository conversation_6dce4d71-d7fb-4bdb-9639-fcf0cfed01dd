<?php

declare(strict_types=1);

namespace App\Http\Resources\Plots;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class LandTypeResourceCollection extends ResourceCollection
{
    public $collects = LandTypeResource::class;

    public function toArray(Request $request): array
    {
        return [
            'rows' => $this->collection,
            'total' => $this->collection->count(),
        ];
    }
}
