<?php

declare(strict_types=1);

namespace App\Http\Resources\Rent;

use App\Models\UserDb\Rent\RentInKind;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class RentInKindResourceCollection extends ResourceCollection
{
    /**
     * The resource that this resource collects.
     *
     * @var string
     */
    public $collects = RentInKindResource::class;

    /**
     * Transform the resource collection into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'rows' => $this->collection,
            'total' => $this->collection->count(),
        ];
    }
}
