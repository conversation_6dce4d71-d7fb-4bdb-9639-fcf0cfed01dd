<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Auth;
use Closure;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;

class SetUserDatabase
{
    public function handle(Request $request, Closure $next): mixed
    {
        /** @var \App\Models\Main\User $user */
        $user = Auth::user();

        $userDbName = $user->database;
        // Dynamically set the database name for the user_db connection
        Config::set('database.connections.user_db.database', $userDbName);

        // Set the default connection to user_db
        DB::setDefaultConnection('user_db');

        return $next($request);
    }
}
