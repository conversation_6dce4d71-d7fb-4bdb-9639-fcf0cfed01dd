<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Auth;
use Sentry\State\Scope;
use Illuminate\Http\Response;

class SentryUserContext
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return Response
     */
    public function handle($request, Closure $next)
    {
        if (Auth::check() && app()->bound('sentry')) {
            \Sentry\configureScope(function (Scope $scope): void {
                $scope->setUser([
                    'id' => Auth::user()->getAuthIdentifier(),
                    'email' => Auth::user()->email ?? '',
                    'name' => Auth::user()->name ?? '',
                ]);
            });
        }

        return $next($request);
    }
}
