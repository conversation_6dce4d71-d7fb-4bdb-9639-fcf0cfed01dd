# #!/usr/bin/env sh
# . "$(dirname -- "$0")/_/husky.sh"

# # get the staged .php files
# staged_php_files="$(git diff-index --cached --name-only --diff-filter=ACMR HEAD | grep '\.php$')" || exit 0
# for staged in ${staged_php_files}; do
#     # run pint fix for each of the staged .php files
#     ./vendor/bin/pint ${staged}
    
#     pint_result=$?
#     # the 'pint' command returns 0 for success
#     if [ ${pint_result} -eq 0 ]; then
#         git add ${staged} # stage formatted file
#     fi
# done