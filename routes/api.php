<?php

declare(strict_types=1);

use App\Http\Controllers\Contracts\ContractGroupController;
use App\Http\Controllers\Documents\Contracts\ContractsController;
use App\Http\Controllers\Documents\Contracts\ContractsRenewController;
use App\Http\Controllers\Documents\DocumentPlotsController;
use App\Http\Controllers\Owners\OwnersController;
use App\Http\Controllers\Owners\OwnerRepsController;
use App\Http\Controllers\Payments\PaymentsController;
use App\Http\Controllers\Rent\RentInKindController;
use App\Http\Controllers\Rent\RentTypeOptionsController;
use App\Http\Controllers\Plots\PlotCategoriesController;
use App\Http\Controllers\Plots\AreaTypesController;
use App\Http\Controllers\Plots\LandTypesController;
use App\Types\Enums\Documents\DocumentTypeEnum;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::prefix('/documents')->group(function () {
    Route::prefix('/{type}')
        ->whereIn('type', DocumentTypeEnum::values()->toArray())
        ->group(function () {
            Route::controller(ContractsController::class)
                ->group(function () {
                    Route::get('/', 'index');
                    Route::get('/filter-items', 'getFilterItems');
                    Route::get('/{id}', 'show')->where('id', '[0-9]+');
                });
        });

    // Renew endpoints with restricted type parameter (only 'contracts' and 'subleases')
    Route::prefix('/{type}')
        ->whereIn('type', [DocumentTypeEnum::Contracts->value, DocumentTypeEnum::Subleases->value])
        ->group(function () {
            Route::prefix('/renew')
                ->controller(ContractsRenewController::class)
                ->group(function () {
                    Route::get('/', 'index');
                    Route::get('/filter-items', 'getFilterItems');
                    Route::post('/', 'renewContracts');
                });
        });
});

// Owners search endpoints for contract creation
Route::prefix('/owners')->group(function () {
    Route::controller(OwnersController::class)
        ->group(function () {
            Route::get('/', 'index');
            Route::get('/filter-items', 'getFilterItems');
            Route::get('/tree', 'tree');
        });
});

// Owner representatives endpoints
Route::prefix('/owner-reps')->group(function () {
    Route::controller(OwnerRepsController::class)
        ->group(function () {
            Route::get('/', 'index');
        });
});

// Document-specific plot endpoints
Route::prefix('/documents/{document_type}')
    ->whereIn('document_type', [DocumentTypeEnum::Contracts->value, DocumentTypeEnum::Subleases->value])
    ->controller(DocumentPlotsController::class)
    ->group(function () {
        Route::get('/available-plots', 'available');
    });
// Contract renewal routes
Route::prefix('/documents')->group(function () {
    Route::controller(ContractsRenewController::class)->group(function () {
        Route::post('/renew-contracts', 'renewContracts');
    });
});

// Payments
Route::prefix('/payments')->group(function () {
    Route::controller(PaymentsController::class)
        ->group(function () {
            Route::get('/owners-payroll', 'getOwnersPayroll');
            Route::get('/owner-payroll', 'getOwnerPayroll');
            Route::get('/contract-payments', 'getContractPayments');
        });
});

// Rent-in-kind types API resource
Route::apiResource('rent-in-kind', RentInKindController::class)->only(['index']);
Route::apiResource('rent-type-options', RentTypeOptionsController::class)->only(['index']);
Route::apiResource('plot-categories', PlotCategoriesController::class)->only(['index']);
Route::apiResource('area-types', AreaTypesController::class)->only(['index']);
Route::apiResource('land-types', LandTypesController::class)->only(['index']);
Route::apiResource('contract-groups', ContractGroupController::class)->only(['index', 'store', 'update']);
