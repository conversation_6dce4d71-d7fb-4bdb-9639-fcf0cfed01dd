flowchart TD
    A["getOwnerPayroll()<br/>📁 PaymentsController<br/>📂 engine/Plugins/Core/Payments/PaymentsController.php<br/>🔧 $this->method"] --> B["getPaymentsPlots()<br/>📁 PaymentsController<br/>📂 engine/Plugins/Core/Payments/PaymentsController.php<br/>🔧 $this->method"]
    A --> C["processOwnerPersonalUse()<br/>📁 PaymentsController<br/>📂 engine/Plugins/Core/Payments/PaymentsController.php<br/>🔧 $this->method"]
    A --> D["aggPlots(rent_type)<br/>📁 PaymentsController<br/>📂 engine/Plugins/Core/Payments/PaymentsController.php<br/>🔧 $this->method"]
    A --> E["aggPlots(contract)<br/>📁 PaymentsController<br/>📂 engine/Plugins/Core/Payments/PaymentsController.php<br/>🔧 $this->method"]
    A --> F["contractsPaymentsMapping()<br/>📁 PaymentsController<br/>📂 engine/Plugins/Core/Payments/PaymentsController.php<br/>🔧 $this->method"]
    A --> G["formattingOwnersData()<br/>📁 PaymentsController<br/>📂 engine/Plugins/Core/Payments/PaymentsController.php<br/>🔧 $this->method"]

    %% getPaymentsPlots branch
    B --> B1["getPaymentPlots()<br/>📁 PaymentsModel<br/>📂 engine/Plugins/Core/Payments/PaymentsModel.php<br/>🔧 $this->DbHandler->method"]
    B --> B2["getNatRents()<br/>📁 PaymentsModel<br/>📂 engine/Plugins/Core/Payments/PaymentsModel.php<br/>🔧 $this->DbHandler->method"]
    B --> B3["getPersonalUseForOwners()<br/>📁 PaymentsController<br/>📂 engine/Plugins/Core/Payments/PaymentsController.php<br/>🔧 $this->method"]
    B --> B4["getPersonalUseArea()<br/>📁 PaymentsController<br/>📂 engine/Plugins/Core/Payments/PaymentsController.php<br/>🔧 $this->method"]
    B --> B5["getFarmngYearFromDate()<br/>📁 Global Function<br/>📂 engine/Kernel/functions.php<br/>🔧 global function"]

    %% getPersonalUseForOwners branch
    B3 --> B3A["getPersonalUseForOwners()<br/>📁 PaymentsModel<br/>📂 engine/Plugins/Core/Payments/PaymentsModel.php<br/>🔧 $this->DbHandler->method"]

    %% getPersonalUseArea branch
    B4 --> B4A["Math Operations<br/>📁 Math<br/>📂 engine/Kernel/Math.php<br/>🔧 Math::static methods"]

    %% processOwnerPersonalUse branch
    C --> C1["Math Operations<br/>📁 Math<br/>📂 engine/Kernel/Math.php<br/>🔧 Math::static methods"]

    %% aggPlots branch
    D --> D1["FarmingController<br/>📁 FarmingController<br/>📂 engine/Plugins/Core/Farming/FarmingController.php<br/>🔧 new instance"]
    D --> D2["sumPayments()<br/>📁 PaymentsController<br/>📂 engine/Plugins/Core/Payments/PaymentsController.php<br/>🔧 $this->method"]
    E --> E1["FarmingController<br/>📁 FarmingController<br/>📂 engine/Plugins/Core/Farming/FarmingController.php<br/>🔧 new instance"]
    E --> E2["sumPayments()<br/>📁 PaymentsController<br/>📂 engine/Plugins/Core/Payments/PaymentsController.php<br/>🔧 $this->method"]

    %% sumPayments branch
    D2 --> D2A["Math Operations<br/>📁 Math<br/>📂 engine/Kernel/Math.php<br/>🔧 Math::static methods"]
    E2 --> E2A["Math Operations<br/>📁 Math<br/>📂 engine/Kernel/Math.php<br/>🔧 Math::static methods"]

    %% contractsPaymentsMapping branch
    F --> F1["getPayments()<br/>📁 PaymentsController<br/>📂 engine/Plugins/Core/Payments/PaymentsController.php<br/>🔧 $this->method"]
    F --> F2["getOwnerDataFromContract()<br/>📁 PaymentsController<br/>📂 engine/Plugins/Core/Payments/PaymentsController.php<br/>🔧 $this->method (private)"]

    %% getPayments branch
    F1 --> F1A["getPaymentsPlots()<br/>📁 PaymentsController<br/>📂 engine/Plugins/Core/Payments/PaymentsController.php<br/>🔧 $this->method"]
    F1 --> F1B["aggPlots()<br/>📁 PaymentsController<br/>📂 engine/Plugins/Core/Payments/PaymentsController.php<br/>🔧 $this->method"]
    F1 --> F1C["processOwnerPersonalUse()<br/>📁 PaymentsController<br/>📂 engine/Plugins/Core/Payments/PaymentsController.php<br/>🔧 $this->method"]
    F1 --> F1D["processOwnerPayments()<br/>📁 PaymentsController<br/>📂 engine/Plugins/Core/Payments/PaymentsController.php<br/>🔧 $this->method"]
    F1 --> F1E["fixRounding()<br/>📁 PaymentsController<br/>📂 engine/Plugins/Core/Payments/PaymentsController.php<br/>🔧 $this->method"]
    F1 --> F1F["filterChildrenByParentDeadStatus()<br/>📁 PaymentsController<br/>📂 engine/Plugins/Core/Payments/PaymentsController.php<br/>🔧 $this->method (private)"]
    F1 --> F1G["buildOwnersTree()<br/>📁 PaymentsController<br/>📂 engine/Plugins/Core/Payments/PaymentsController.php<br/>🔧 $this->method"]
    F1 --> F1H["calculateTreeRents()<br/>📁 PaymentsController<br/>📂 engine/Plugins/Core/Payments/PaymentsController.php<br/>🔧 $this->method (private)"]

    %% processOwnerPayments branch
    F1D --> F1D1["UserDbController<br/>📁 UserDbController<br/>📂 engine/Plugins/Core/UserDb/UserDbController.php<br/>🔧 new instance"]
    F1D --> F1D2["getContractOwnerPayments()<br/>📁 PaymentsController<br/>📂 engine/Plugins/Core/Payments/PaymentsController.php<br/>🔧 $this->method"]
    F1D --> F1D3["getItemsByParams()<br/>📁 UserDbController<br/>📂 engine/Plugins/Core/UserDb/UserDbController.php<br/>🔧 instance->method"]

    %% getContractOwnerPayments branch
    F1D2 --> F1D2A["UserDbPaymentsController<br/>📁 UserDbPaymentsController<br/>📂 engine/Plugins/Core/UserDbPayments/UserDbPaymentsController.php<br/>🔧 new instance"]
    F1D2 --> F1D2B["getPaidData()<br/>📁 UserDbPaymentsController<br/>📂 engine/Plugins/Core/UserDbPayments/UserDbPaymentsController.php<br/>🔧 instance->method"]

    %% fixRounding branch
    F1E --> F1E1["Math Operations<br/>📁 Math<br/>📂 engine/Kernel/Math.php<br/>🔧 Math::static methods"]

    %% buildOwnersTree branch
    F1G --> F1G1["Recursive Processing<br/>📁 PaymentsController<br/>📂 engine/Plugins/Core/Payments/PaymentsController.php<br/>🔧 $this->method (recursive)"]

    %% calculateTreeRents branch
    F1H --> F1H1["calculateChildrenRentsBasedOfParentPayments()<br/>📁 PaymentsController<br/>📂 engine/Plugins/Core/Payments/PaymentsController.php<br/>🔧 $this->method (private)"]
    F1H --> F1H2["calculateParentRentsBasedOfChildrenPayments()<br/>📁 PaymentsController<br/>📂 engine/Plugins/Core/Payments/PaymentsController.php<br/>🔧 $this->method (private)"]

    %% calculateChildrenRentsBasedOfParentPayments branch
    F1H1 --> F1H1A["Math Operations<br/>📁 Math<br/>📂 engine/Kernel/Math.php<br/>🔧 Math::static methods"]

    %% calculateParentRentsBasedOfChildrenPayments branch
    F1H2 --> F1H2A["Math Operations<br/>📁 Math<br/>📂 engine/Kernel/Math.php<br/>🔧 Math::static methods"]

    %% getOwnerDataFromContract branch
    F2 --> F2A["Recursive Tree Traversal<br/>📁 PaymentsController<br/>📂 engine/Plugins/Core/Payments/PaymentsController.php<br/>🔧 $this->method (private, recursive)"]

    %% formattingOwnersData branch
    G --> G1["UserDbAreaTypesController<br/>📁 UserDbAreaTypesController<br/>📂 engine/Plugins/Core/UserDbAreaTypes/UserDbAreaTypesController.php<br/>🔧 new instance"]
    G --> G2["BGNtoEURO()<br/>📁 Global Function<br/>📂 engine/Kernel/functions.php<br/>🔧 global function"]
    G --> G3["Recursive Children Processing<br/>📁 PaymentsController<br/>📂 engine/Plugins/Core/Payments/PaymentsController.php<br/>🔧 $this->method (recursive)"]

    %% Math Operations Detail Box
    MATH["📊 Math Class Operations:<br/>📁 Math<br/>📂 engine/Kernel/Math.php<br/>🔧 Static Methods:<br/>• Math::add() - Addition<br/>• Math::sub() - Subtraction<br/>• Math::mul() - Multiplication<br/>• Math::div() - Division<br/>• Math::round() - Rounding<br/>• Math::compare() - Comparison"]

    %% Connect Math detail to math operation nodes
    B4A -.-> MATH
    C1 -.-> MATH
    D2A -.-> MATH
    E2A -.-> MATH
    F1E1 -.-> MATH
    F1H1A -.-> MATH
    F1H2A -.-> MATH

    %% Styling
    classDef mainMethod fill:#e1f5fe,stroke:#01579b,stroke-width:3px
    classDef dbMethod fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef mathMethod fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef controllerMethod fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef recursiveMethod fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef mathDetail fill:#f1f8e9,stroke:#33691e,stroke-width:2px

    class A mainMethod
    class B1,B2,B3A,F1D2B dbMethod
    class B4A,C1,D2A,E2A,F1E1,F1H1A,F1H2A,MATH mathMethod
    class D1,E1,F1D1,F1D2A,G1 controllerMethod
    class F1G1,F2A,G3,F1H1,F1H2 recursiveMethod
    class MATH mathDetail