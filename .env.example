APP_NAME=technofarm-laravel
APP_ENV=dev
APP_KEY=base64:isS2geZqDl6Fe7EQaPIYtW5WiaO+CnPSbW/MO7oqEMQ=
APP_DEBUG=true
APP_TIMEZONE=UTC
APP_URL=http://localhost

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stderr
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DEFAULT_DB="main"

MAIN_DB_HOST="************"
MAIN_DB_DATABASE="susi_main"
MAIN_DB_USERNAME="postgres"
MAIN_DB_PASSWORD="6nuk23"
MAIN_DB_PORT="5432"

USER_DB_HOST="************"
USER_DB_USERNAME="postgres"
USER_DB_PASSWORD="6nuk23"
USER_DB_PORT="5432"

SESSION_DRIVER=file
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=redis
REDIS_QUEUE_CONNECTION=default

CACHE_STORE=redis
CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=redis
REDIS_PASSWORD=null
REDIS_PORT=6379
REDIS_QUEUE=default
REDIS_CLUSTER_MODE=false

MAIL_MAILER=log
MAIL_SCHEME=null
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"

DOCS_PORT=8080

KEYCLOAK_CLIENT_ID=technofarm-laravel-backend
KEYCLOAK_CLIENT_SECRET=ZlamaNWggDr0IpXvS11c4PVT4hjobljB
KEYCLOAK_REDIRECT_URI=
KEYCLOAK_BASE_URL=http://keycloak.geoscan.info:8081/
KEYCLOAK_REALM=geotech
KEYCLOAK_ALG=RS256
KEYCLOAK_IGNORE_RESOURCES_VALIDATION=true

CONTAINER_NAME=technofarm-laravel-api
USER_ID=1001
GROUP_ID=1001
USER_NAME=appuser
USER_GROUP=appgroup
APP_DIR=/var/www/html/app

SENTRY_LARAVEL_DSN=