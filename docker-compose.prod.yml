version: "3.7"
services:
  php-laravel:
    user: ${USER_NAME:-appuser}:${USER_GROUP:-appgroup}
    image: technofarm/technofarm-laravel-api:${IMAGE_TAG_NAME}
    container_name: ${CONTAINER_NAME}
    environment:
      - APP_NAME
      - APP_ENV
      - APP_DEBUG
      - APP_TIMEZONE
      - APP_KEY
      - LOG_CHANNEL
      - LOG_DEPRECATIONS_CHANNEL
      - LOG_LEVEL
      - DB_CONNECTION
      - MAIN_DB_HOST
      - MAIN_DB_PORT
      - MAIN_DB_DATABASE
      - MAIN_DB_USERNAME
      - MAIN_DB_PASSWORD
      - DBL<PERSON>K_DRIVER
      - DB<PERSON>INK_HOST
      - DBLINK_PORT
      - DBLINK_DATABASE
      - DBLINK_USERNAME
      - DBLINK_PASSWORD
      - BROADCAST_DRIVER
      - CACHE_STORE
      - FILESYSTEM_DISK
      - QUEUE_CONNECTION
      - SESSION_DRIVER
      - SESSION_LIFETIME
      - MEMCACHED_HOST
      - REDIS_CLIENT
      - REDIS_HOST
      - REDIS_PASSWORD
      - REDIS_PORT
      - MAIL_MAILER
      - MAIL_HOST
      - MAIL_PORT
      - MAIL_USERNAME
      - MAIL_PASSWORD
      - MAIL_ENCRYPTION
      - MAIL_FROM_ADDRESS
      - MAIL_FROM_NAME
      - AWS_ACCESS_KEY_ID
      - AWS_SECRET_ACCESS_KEY
      - AWS_DEFAULT_REGION
      - AWS_BUCKET
      - AWS_USE_PATH_STYLE_ENDPOINT
      - PUSHER_APP_ID
      - PUSHER_APP_KEY
      - PUSHER_APP_SECRET
      - PUSHER_HOST
      - PUSHER_PORT
      - PUSHER_SCHEME
      - PUSHER_APP_CLUSTER
      - CONTAINER_NAME
      - USER_ID
      - GROUP_ID
      - USER_NAME
      - USER_GROUP
      - APP_DIR
      - KEYCLOAK_CLIENT_ID
      - KEYCLOAK_CLIENT_SECRET
      - KEYCLOAK_BASE_URL
      - KEYCLOAK_REALM
      - DOCS_PORT
      - TECHNO_FARM_BASE_URI
      - SENTRY_LARAVEL_DSN
    depends_on:
      - redis
    volumes:
      - app:/var/www/html/app:cached
      - tmp:/tmp/
    networks:
      - geoscan-net
    restart: always
    healthcheck:
      test: "exit 0"

  web:
    container_name: ${CONTAINER_NAME}-nginx
    image: technofarm/technofarm-laravel-api-nginx:${IMAGE_TAG_NAME}
    environment:
      - CONTAINER_NAME=${CONTAINER_NAME}
      - APP_DIR=${APP_DIR}
    working_dir: /etc/nginx
    ports:
      - ${NGINX_PORT-8025}:80
    volumes:
      - app:/var/www/html/app
    networks:
      - geoscan-net
    depends_on:
      - php-laravel
    restart: always
    healthcheck:
      test: "exit 0"
  redis:
    image: "redis:latest"
    container_name: ${CONTAINER_NAME}-redis
    environment:
      - ALLOW_EMPTY_PASSWORD=yes
    restart: always
    networks:
      - geoscan-net
  # redoc:
  #   container_name: ${CONTAINER_NAME}-docs
  #   image: technofarm/technofarm-laravel-api-docs:${IMAGE_TAG_NAME}
  #   networks:
  #     - geoscan-net
  #   ports:
  #     - ${DOCS_PORT}:80
networks:
  geoscan-net:
    external: true
volumes:
  app:
    name: ${CONTAINER_NAME}
  tmp:
    name: ${CONTAINER_NAME}-tmp
