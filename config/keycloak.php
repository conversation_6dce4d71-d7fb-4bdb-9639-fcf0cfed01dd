<?php

declare(strict_types=1);

return [
    'keycloak_base_url' => env('KEYCLOAK_BASE_URL'),
    'realm' => env('KEYCLOAK_REALM'),
    'keycloak_client_id' => env('<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_CLIENT_ID', ''),
    'keycloak_client_secret' => env('KEYCLOAK_CLIENT_SECRET', ''),
    'user_provider_credential' => env('KEYCLOAK_USER_PROVIDER_CREDENTIAL', 'username'),
    'token_principal_attribute' => env('KEY<PERSON><PERSON><PERSON>_TOKEN_PRINCIPAL_ATTRIBUTE', 'preferred_username'),
];
