---
- hosts: "servers"
  environment:
    CONTAINER_NAME: "{{ lookup('env', 'CONTAINER_NAME') }}"
    IMAGE_TAG_NAME: "{{ lookup('env', 'IMAGE_TAG_NAME') }}"
    APP_ENV: "{{ lookup('env', 'APP_ENV') }}"
    APP_KEY: "{{ lookup('env', 'APP_KEY') }}"
    USER_ID: "{{ lookup('env', 'USER_ID') }}"
    GROUP_ID: "{{ lookup('env', 'GROUP_ID') }}"
    USER_NAME: "{{ lookup('env', 'USER_NAME') }}"
    USER_GROUP: "{{ lookup('env', 'USER_GROUP') }}"
    APP_DIR: "{{ lookup('env', 'APP_DIR') }}"
    MAIN_DB_HOST: "{{ lookup('env', 'MAIN_DB_HOST') }}"
    MAIN_DB_PORT: "{{ lookup('env', 'MAIN_DB_PORT') }}"
    MAIN_DB_DATABASE: "{{ lookup('env', 'MAIN_DB_DATABASE') }}"
    MAIN_DB_USERNAME: "{{ lookup('env', 'MAIN_DB_USERNAME') }}"
    MAIN_DB_PASSWORD: "{{ lookup('env', 'MAIN_DB_PASSWORD') }}"
    USER_DB_HOST: "{{ lookup('env', 'USER_DB_HOST') }}"
    USER_DB_PORT: "{{ lookup('env', 'USER_DB_PORT') }}"
    USER_DB_USERNAME: "{{ lookup('env', 'USER_DB_USERNAME') }}"
    USER_DB_PASSWORD: "{{ lookup('env', 'USER_DB_PASSWORD') }}"
    REDIS_HOST: "{{ lookup('env', 'REDIS_HOST') }}"
    REDIS_PORT: "{{ lookup('env', 'REDIS_PORT') }}"
    REDIS_PASSWORD: "{{ lookup('env', 'REDIS_PASSWORD') }}"
    REDIS_CLUSTER_MODE: "{{ lookup('env', 'REDIS_CLUSTER_MODE') }}"
    REDIS_CACHE_CONNECTION: "{{ lookup('env', 'REDIS_CACHE_CONNECTION') }}"
    LOG_CHANNEL: "{{ lookup('env', 'LOG_CHANNEL') }}"
    PLOTS_CHUNK_SIZE: "{{ lookup('env', 'PLOTS_CHUNK_SIZE') }}"
    KEYCLOAK_CLIENT_ID: "{{ lookup('env', 'KEYCLOAK_CLIENT_ID') }}"
    KEYCLOAK_CLIENT_SECRET: "{{ lookup('env', 'KEYCLOAK_CLIENT_SECRET') }}"
    KEYCLOAK_BASE_URL: "{{ lookup('env', 'KEYCLOAK_BASE_URL') }}"
    KEYCLOAK_REALM: "{{ lookup('env', 'KEYCLOAK_REALM') }}"
    CACHE_STORE: "{{ lookup('env', 'CACHE_STORE') }}"
    REDIS_CLUSTER: "{{ lookup('env', 'REDIS_CLUSTER') }}"
    QUEUE_CONNECTION: "{{ lookup('env', 'QUEUE_CONNECTION') }}"
    SESSION_DRIVER: "{{ lookup('env', 'SESSION_DRIVER') }}"
    SESSION_CONNECTION: "{{ lookup('env', 'SESSION_CONNECTION') }}"
    NGINX_PORT: "{{ lookup('env', 'NGINX_PORT') }}"
    SENTRY_LARAVEL_DSN: "{{ lookup('env', 'SENTRY_LARAVEL_DSN') }}"
    SENTRY_TRACES_SAMPLE_RATE: "{{ lookup('env', 'SENTRY_TRACES_SAMPLE_RATE') }}"
    SENTRY_PROFILES_SAMPLE_RATE: "{{ lookup('env', 'SENTRY_PROFILES_SAMPLE_RATE') }}"
    DB_CONNECTION: "{{ lookup('env', 'DB_CONNECTION') }}"

  tasks:
    - name: Log in
      community.docker.docker_login:
        username: "{{ lookup('env', 'DOCKER_REGISTRY_USERNAME') }}"
        password: "{{ lookup('env', 'DOCKER_REGISTRY_PASSWORD') }}"

    - name: Copy docker-compose.prod.yml
      copy:
        src: docker-compose.prod.yml
        dest: ~/docker-compose.prod.yml
        mode: 0644

    - name: Copy .env.example
      copy:
        src: .env.example
        dest: .env
        mode: 0644

    - name: DOWN the containers
      community.docker.docker_compose_v2:
        project_name: "{{ lookup('env', 'CONTAINER_NAME') }}"
        project_src: ~/
        state: absent
        files:
          - docker-compose.prod.yml

    - name: Clear app volume
      community.docker.docker_volume:
        name: "{{ lookup('env', 'CONTAINER_NAME') }}"
        state: absent

    - name: UP the containers
      community.docker.docker_compose_v2:
        project_name: "{{ lookup('env', 'CONTAINER_NAME') }}"
        project_src: ~/
        build: always
        recreate: always
        dependencies: no
        files:
          - docker-compose.prod.yml

    - name: Copy .env in "{{ lookup('env', 'CONTAINER_NAME') }}" container
      command: docker cp .env "{{ lookup('env', 'CONTAINER_NAME') }}":{{ lookup('env', 'APP_DIR')}}

    - name: Image Prune
      community.docker.docker_prune:
        images: yes

    - name: Delete docker-compose.prod.yml
      ansible.builtin.file:
        path: ~/docker-compose.prod.yml
        state: absent
