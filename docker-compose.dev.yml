version: "3.7"
services:
    php-laravel:
        env_file:
            - .env
        user: ${USER_NAME}:${USER_GROUP}
        build:
            context: .
            dockerfile: ./.docker/Dockerfile
            target: backend
            args:
                - APP_ENV=dev
                - MAIN_DB_HOST=${MAIN_DB_HOST}
                - MAIN_DB_DATABASE=${MAIN_DB_DATABASE}
                - MAIN_DB_PORT=${MAIN_DB_PORT}
                - MAIN_DB_USERNAME=${MAIN_DB_USERNAME}
                - MAIN_DB_PASSWORD=${MAIN_DB_PASSWORD}
                - GROUP_ID=${GROUP_ID}
                - USER_ID=${USER_ID}
                - USER_NAME=${USER_NAME}
                - USER_GROUP=${USER_GROUP}
                - APP_DIR=${APP_DIR}
        environment:
            - APP_ENV=dev
            - CONTAINER_ROLE=app
        image: ${CONTAINER_NAME}
        container_name: ${CONTAINER_NAME}
        depends_on:
            - redis
        volumes:
            - .:/var/www/html/app:cached
            - tmp:/tmp/
        networks:
            - geoscan-net
        restart: always
        healthcheck:
            test: "exit 0"
    web:
        container_name: ${CONTAINER_NAME}-nginx
        build:
            context: .
            dockerfile: ./.docker/nginx/Dockerfile
        environment:
            - CONTAINER_NAME=${CONTAINER_NAME}
            - APP_DIR=${APP_DIR}
        working_dir: /etc/nginx
        ports:
            - ${NGINX_PORT-8025}:80
        volumes:
            - .:/var/www/html/app
        networks:
            - geoscan-net
        depends_on:
            - php-laravel
        restart: always
        healthcheck:
            test: "exit 0"
    redis:
        image: 'redis:latest'
        container_name: ${CONTAINER_NAME}-redis
        ports:
            - '6383:6379'
        environment:
            - ALLOW_EMPTY_PASSWORD=yes
        restart: always
        networks:
            - geoscan-net
    # redoc:
    #     container_name: ${CONTAINER_NAME}-docs
    #     build:
    #         context: .
    #         dockerfile: .docker/redoc/Dockerfile 
    #         args:
    #             - RD_TITLE=Diary API
    #     networks:
    #         - geoscan-net
    #     ports:
    #         - ${DOCS_PORT}:80
networks:
    geoscan-net:
        external: true
volumes:
    app:
        name: ${CONTAINER_NAME}
    tmp:
        name: ${CONTAINER_NAME}-tmp