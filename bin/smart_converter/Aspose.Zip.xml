<?xml version="1.0"?>
<doc>
  <assembly>
    <name>Aspose.Zip</name>
  </assembly>
  <members>
    <member name="T:Aspose.Zip.Bzip2.Bzip2Archive">
      <summary>
            This class represents bzip2 archive file. Use it to compose or extract bzip2 archives.
            </summary>
      <remarks>
            bzip2 compresses files using the Burrows-Wheeler block sorting text compression algorithm, and <PERSON><PERSON><PERSON> coding. See more: https://en.wikipedia.org/wiki/Bzip2
            </remarks>
    </member>
    <member name="M:Aspose.Zip.Bzip2.Bzip2Archive.#ctor">
      <summary>
            Initializes a new instance of the <see cref="T:Aspose.Zip.Bzip2.Bzip2Archive" /> class prepared for compressing.
            </summary>
      <example>
        <para>
            The following example shows how to compress a file.
            </para>
        <code>
            using (Bzip2Archive archive = new Bzip2Archive()) 
            {
                archive.SetSource("data.bin");
                archive.Save("archive.bz2");
            }
            </code>
      </example>
    </member>
    <member name="M:Aspose.Zip.Bzip2.Bzip2Archive.#ctor(System.IO.Stream)">
      <summary>
            Initializes a new instance of the <see cref="T:Aspose.Zip.Bzip2.Bzip2Archive" /> class prepared for decompressing.
            </summary>
      <remarks>
            This constructor does not decompress. See <see cref="M:Aspose.Zip.Bzip2.Bzip2Archive.Open" /> method for decompressing.
            </remarks>
      <param name="sourceStream">The source of the archive.</param>
      <example>
        <para>Open an archive from a stream and extract it to a <c>MemoryStream</c></para>
        <code>
            var ms = new MemoryStream();
            using (Bzip2Archive archive = new Bzip2Archive(File.OpenRead("archive.bz2")))
              archive.Open().CopyTo(ms);
            </code>
      </example>
    </member>
    <member name="M:Aspose.Zip.Bzip2.Bzip2Archive.#ctor(System.String)">
      <summary>
            Initializes a new instance of the <see cref="T:Aspose.Zip.Bzip2.Bzip2Archive" /> class prepared for decompressing.
            </summary>
      <remarks>
            This constructor does not decompress. See <see cref="M:Aspose.Zip.Bzip2.Bzip2Archive.Open" /> method for decompressing.
            </remarks>
      <param name="path">The path to the archive file.</param>
      <example>
        <para>Open an archive from file by path and extract it to a <c>MemoryStream</c></para>
        <code>
            var ms = new MemoryStream();
            using (Bzip2Archive archive = new Bzip2Archive("archive.bz2"))
              archive.Open().CopyTo(ms);
            </code>
      </example>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission to access </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="path" /> is empty, contains only white spaces, or contains invalid characters.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access to file <paramref name="path" /> is denied.</exception>
      <exception cref="T:System.IO.PathTooLongException"> The specified <paramref name="path" />, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters.</exception>
      <exception cref="T:System.NotSupportedException">File at <paramref name="path" /> contains a colon (:) in the middle of the string.</exception>
    </member>
    <member name="M:Aspose.Zip.Bzip2.Bzip2Archive.SetSource(System.IO.Stream)">
      <summary>
            Sets the content to be compressed within the archive.
            </summary>
      <param name="source">The input stream for the archive.</param>
      <example>
        <code>
            using (Bzip2Archive archive = new Bzip2Archive()) 
            {
                archive.SetSource(new MemoryStream(new byte[] { 0x00,0xFF }));
                archive.Save("archive.bz2");
            }
            </code>
      </example>
    </member>
    <member name="M:Aspose.Zip.Bzip2.Bzip2Archive.SetSource(System.IO.FileInfo)">
      <summary>
            Sets the content to be compressed within the archive.
            </summary>
      <param name="fileInfo">The reference to a file to be compressed.</param>
      <example>
        <code>
            using (Bzip2Archive archive = new Bzip2Archive()) 
            {
                archive.SetSource(new FileInfo("data.bin"));
                archive.Save("archive.bz2");
            }
            </code>
      </example>
    </member>
    <member name="M:Aspose.Zip.Bzip2.Bzip2Archive.SetSource(System.String)">
      <summary>
            Sets the content to be compressed within the archive.
            </summary>
      <param name="path">Path to file to be compressed.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission to access </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="path" /> is empty, contains only white spaces, or contains invalid characters.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access to file <paramref name="path" /> is denied.</exception>
      <exception cref="T:System.IO.PathTooLongException"> The specified <paramref name="path" />, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters.</exception>
      <exception cref="T:System.NotSupportedException">File at <paramref name="path" /> contains a colon (:) in the middle of the string.</exception>
      <example>
        <code>
            using (Bzip2Archive archive = new Bzip2Archive()) 
            {
                archive.SetSource("data.bin");
                archive.Save("archive.bz2");
            }
            </code>
      </example>
    </member>
    <member name="M:Aspose.Zip.Bzip2.Bzip2Archive.SetSource(Aspose.Zip.Tar.TarArchive)">
      <summary>
            Sets the content to be compressed within the archive.
            </summary>
      <param name="tarArchive">Tar archive to be compressed.</param>
      <remarks>Use this method to compose joint tar.gz archive.</remarks>
      <example>
        <code>
            using (var tarArchive = new TarArchive())
            {
                tarArchive.CreateEntry("first.bin", "data1.bin");
                tarArchive.CreateEntry("second.bin", "data2.bin");
                using (var bzippedArchive = new Bzip2Archive())
                {
                       bzippedArchive.SetSource(tarArchive);
                       bzippedArchive.Save("archive.tar.bz2");
                }
            }
            </code>
      </example>
    </member>
    <member name="M:Aspose.Zip.Bzip2.Bzip2Archive.Dispose">
      <inheritdoc />
      <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            </summary>
    </member>
    <member name="M:Aspose.Zip.Bzip2.Bzip2Archive.Open">
      <summary>
            Opens the archive for extraction and provides a stream with archive content.
            </summary>
      <returns>The stream that represents the contents of the archive.</returns>
      <remarks>
            Read from the stream to get original content of file. See examples section.
            </remarks>
      <example>
            Usage:
            <code>Stream decompressed = archive.Open();</code><para>
            .NET 4.0 and higher - use Stream.CopyTo method:
            <code>
            decompressed.CopyTo(httpResponse.OutputStream)
            </code></para><para>
            .NET 3.5 and before - copy bytes manually:
            <code>
            byte[] buffer = new byte[8192];
            int bytesRead;
            while (0 &lt; (bytesRead = decompressed.Read(buffer, 0, buffer.Length)))
             fileStream.Write(buffer, 0, bytesRead);
            </code></para></example>
    </member>
    <member name="M:Aspose.Zip.Bzip2.Bzip2Archive.Save(System.IO.Stream,Aspose.Zip.Bzip2.Bzip2SaveOptions)">
      <summary>
            Saves archive to the stream provided.
            </summary>
      <param name="outputStream">Destination stream.</param>
      <param name="saveOptions">
      </param>
      <remarks>
        <para>
          <paramref name="outputStream" /> must be writable.</para>
      </remarks>
      <exception cref="T:System.InvalidOperationException">Source of data to be archived has not been provided.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="outputStream" /> is not writable.</exception>
      <exception cref="T:System.UnauthorizedAccessException">File source is read-only or is a directory.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified file source path is invalid, such as being on an unmapped drive.</exception>
      <exception cref="T:System.IO.IOException">The File source is already open.</exception>
      <example>
        <para>Writes compressed data to http response stream.</para>
        <code>
            using (var archive = new Bzip2Archive()) 
            {
                archive.SetSource(new FileInfo("data.bin"));
                archive.Save(httpResponse.OutputStream);
            }
            </code>
      </example>
    </member>
    <member name="M:Aspose.Zip.Bzip2.Bzip2Archive.Save(System.String)">
      <summary>
            Saves archive to destination file provided.
            </summary>
      <param name="destinationFileName">The path of the archive to be created. If the specified file name points to an existing file, it will be overwritten.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destinationFileName" /> is null.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission to access </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="destinationFileName" /> is empty, contains only white spaces, or contains invalid characters.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access to file <paramref name="destinationFileName" /> is denied.</exception>
      <exception cref="T:System.IO.PathTooLongException"> The specified <paramref name="destinationFileName" />, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters.</exception>
      <exception cref="T:System.NotSupportedException">File at <paramref name="destinationFileName" /> contains a colon (:) in the middle of the string.</exception>
      <example>
        <para>Writes compressed data to file.</para>
        <code>
            using (var archive = new Bzip2Archive()) 
            {
                archive.SetSource(new FileInfo("data.bin"));
                archive.Save("data.bz2");
            }
            </code>
      </example>
    </member>
    <member name="M:Aspose.Zip.Bzip2.Bzip2Archive.Dispose(System.Boolean)">
      <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            </summary>
      <param name="disposing">Whether managed resources should be disposed.</param>
    </member>
    <member name="T:Aspose.Zip.Bzip2.Bzip2SaveOptions">
      <summary>
            Options for saving a bzip2 archive.
            </summary>
    </member>
    <member name="P:Aspose.Zip.Bzip2.Bzip2SaveOptions.BlockSize">
      <summary>
            Block size in hundreds of kilobytes.
            </summary>
    </member>
    <member name="M:Aspose.Zip.Bzip2.Bzip2SaveOptions.#ctor(System.Int32)">
      <summary>
            Initializes a new instance of the <see cref="T:Aspose.Zip.Bzip2.Bzip2SaveOptions" /> class.
            </summary>
      <param name="blockSize">Block size in hundreds of kilobytes.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Block size is not in valid range.</exception>
      <example>
        <code>
            using (FileStream result = File.Open("archive.bz2"))
            {
                using (Bzip2Archive archive = new Bzip2Archive())
                {
                    archive.SetSource("data.bin");
                    archive.Save(result, new Bzip2SaveOptions(9));
                }
            }
            </code>
      </example>
    </member>
    <member name="M:Aspose.Zip.Bzip2.Bzip2SaveOptions.#ctor">
      <summary>
            Initializes a new instance of the <see cref="T:Aspose.Zip.Bzip2.Bzip2SaveOptions" /> class with default block size.
            </summary>
      <example>
        <code>
            using (FileStream result = File.Open("archive.bz2"))
            {
                using (Bzip2Archive archive = new Bzip2Archive())
                {
                    archive.SetSource("data.bin");
                    archive.Save(result, new Bzip2SaveOptions());
                }
            }
            </code>
      </example>
    </member>
    <member name="T:Aspose.Zip.Bzip2.NamespaceDoc">
      <summary>
            The <see cref="N:Aspose.Zip.Bzip2" /> namespace contains classes which represent bzip2 archive related entities.
            </summary>
    </member>
    <member name="T:Aspose.Zip.Saving.Bzip2CompressionSettings">
      <inheritdoc />
      <summary>
            Settings for Bzip2 compression method.
            </summary>
      <remarks>
            bzip2 compresses files using the Burrows-Wheeler block sorting text compression algorithm, and Huffman coding. See more: https://en.wikipedia.org/wiki/Bzip2
            </remarks>
    </member>
    <member name="P:Aspose.Zip.Saving.Bzip2CompressionSettings.BlockSize">
      <summary>
            Block size in hundreds of kilobytes.
            </summary>
    </member>
    <member name="M:Aspose.Zip.Saving.Bzip2CompressionSettings.#ctor(System.Int32)">
      <summary>
            Initializes a new instance of the <see cref="T:Aspose.Zip.Saving.Bzip2CompressionSettings" /> class.
            </summary>
      <param name="blockSize">Block size in hundreds of kilobytes.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Block size is not between 1 and 9.</exception>
      <example>
        <code>
            using (Archive archive = new Archive(new ArchiveEntrySettings(new Bzip2CompressionSettings(1))))
            {
                archive.CreateEntry("data.bin", "data.bin");
                archive.Save(zipFile);
            }
            </code>
      </example>
    </member>
    <member name="M:Aspose.Zip.Saving.Bzip2CompressionSettings.#ctor">
      <summary>
            Initializes a new instance of the <see cref="T:Aspose.Zip.Saving.Bzip2CompressionSettings" /> class with default block size.
            </summary>
      <example>
        <code>
            using (Archive archive = new Archive(new ArchiveEntrySettings(new Bzip2CompressionSettings())))
            {
                archive.CreateEntry("data.bin", "data.bin");
                archive.Save(zipFile);
            }
            </code>
      </example>
    </member>
    <member name="T:Aspose.Zip.Saving.DeflateCompressionSettings">
      <inheritdoc />
      <summary>
            Settings for Deflate compression method.
            </summary>
      <remarks>
        <para>
             Deflate is a lossless data compression algorithm that uses a combination of the LZ77 algorithm and Huffman coding.
            </para>
        <para>
            See standard here: https://tools.ietf.org/html/rfc1951
            </para>
      </remarks>
    </member>
    <member name="M:Aspose.Zip.Saving.DeflateCompressionSettings.#ctor">
      <summary>
            Initializes a new instance of the <see cref="T:Aspose.Zip.Saving.DeflateCompressionSettings" /> class.
            </summary>
      <example>
        <code>
            using (Archive archive = new Archive(new ArchiveEntrySettings(new DeflateCompressionSettings())))
            {
                archive.CreateEntry("data.bin", "data.bin");                   
                archive.Save(zipFile);
            }
            </code>
      </example>
    </member>
    <member name="T:Aspose.Zip.Saving.EncryptionSettings">
      <summary>
            Base class for settings for several zip encryption methods.
            </summary>
    </member>
    <member name="M:Aspose.Zip.Saving.EncryptionSettings.#ctor(System.String,Aspose.Zip.Saving.EncryptionMethod)">
      <summary>
            Initializes a new instance of the <see cref="T:Aspose.Zip.Saving.EncryptionSettings" /> class.
            </summary>
      <param name="password">Password for encryption or decryption.</param>
      <param name="method">Method to encrypt or decrypt with.</param>
    </member>
    <member name="P:Aspose.Zip.Saving.EncryptionSettings.Method">
      <summary>
            Gets the encryption algorithm.
            </summary>
    </member>
    <member name="P:Aspose.Zip.Saving.EncryptionSettings.Password">
      <summary>
            Gets or sets password for encryption or decryption.
            </summary>
    </member>
    <member name="T:Aspose.Zip.Saving.LzmaCompressionSettings">
      <inheritdoc />
      <summary>
            Settings for LZMA compression method.
            </summary>
      <remarks>
        <para>
            The Lempel–Ziv–Markov chain algorithm (LZMA) is an algorithm used to perform lossless data compression. This algorithm uses a dictionary compression scheme somewhat similar to the LZ77 algorithm and features a high compression ratio and a variable compression-dictionary size.
            </para>
        <para>
            See more: https://en.wikipedia.org/wiki/Lempel–Ziv–Markov_chain_algorithm </para>
      </remarks>
    </member>
    <member name="M:Aspose.Zip.Saving.LzmaCompressionSettings.#ctor">
      <summary>
            Initializes a new instance of the <see cref="T:Aspose.Zip.Saving.LzmaCompressionSettings" /> class with default dictionary size.
            </summary>
      <example>
        <code>
            using (Archive archive = new Archive(new ArchiveEntrySettings(new LzmaCompressionSettings())))
            {
                archive.CreateEntry("data.bin", "data.bin");
                archive.Save(zipFile);
            }
            </code>
      </example>
    </member>
    <member name="T:Aspose.Zip.Saving.NamespaceDoc">
      <summary>
            The <see cref="N:Aspose.Zip.Saving" /> namespace contains classes which are needed for operations entailing saving the archive.
            </summary>
    </member>
    <member name="T:Aspose.Zip.Saving.CompressionSettings">
      <summary>
            Settings needed for compressor or decompressor to work.
            </summary>
    </member>
    <member name="P:Aspose.Zip.Saving.CompressionSettings.Bzip2">
      <summary>
            An instance of <c>Bzip2CompressionSettings</c> with default parameters.
            </summary>
    </member>
    <member name="P:Aspose.Zip.Saving.CompressionSettings.Deflate">
      <summary>
            An instance of <c>DeflateCompressionSettings</c> with default parameters.
            </summary>
    </member>
    <member name="P:Aspose.Zip.Saving.CompressionSettings.Store">
      <summary>
            An instance of <c>StoreCompressionSettings</c> with default parameters.
            </summary>
    </member>
    <member name="P:Aspose.Zip.Saving.CompressionSettings.Lzma">
      <summary>
            An instance of <c>LzmaCompressionSettings</c> with default parameters.
            </summary>
    </member>
    <member name="P:Aspose.Zip.Saving.CompressionSettings.PPMd">
      <summary>
            An instance of <c>PPMdCompressionSettings</c> with default parameters.
            </summary>
    </member>
    <member name="T:Aspose.Zip.Saving.PPMdCompressionSettings">
      <inheritdoc />
      <summary>
            Settings for PPMd compression method.
            </summary>
      <remarks>
        <para>
            PPMd is a data compression algorithm developed by Dmitry Shkarin.
            This algorithm is based on predictive phrase matching on multiple order contexts.
            </para>
      </remarks>
    </member>
    <member name="P:Aspose.Zip.Saving.PPMdCompressionSettings.ModelOrder">
      <summary>
            Gets the order of the model.
            </summary>
    </member>
    <member name="P:Aspose.Zip.Saving.PPMdCompressionSettings.SuballocatorSize">
      <summary>
            Gets the sub-allocator size in MB.
            </summary>
    </member>
    <member name="M:Aspose.Zip.Saving.PPMdCompressionSettings.#ctor(System.Int32,System.Int32)">
      <summary>
            Initializes a new instance of the <see cref="T:Aspose.Zip.Saving.PPMdCompressionSettings" /> class.
            </summary>
      <param name="modelOrder">Order of the model.</param>
      <param name="suballocatorSize">Memory size in MB suballocator may consume.</param>
      <remarks>
        <para>Bigger model orders almost surely results in better compression and surely more memory and CPU usage.</para>
        <para>The PPMd algorithm might need a lot of memory, especially when used on large files and/or used with large model order.
            If ppmd  needs  more memory than you give it, the compression will be worse.</para>
      </remarks>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="modelOrder" /> is not between 2 and 16. - or - <paramref name="suballocatorSize" /> is not between 1 and 256.</exception>
      <example>
        <code>
            using (Archive archive = new Archive(new ArchiveEntrySettings(new PPMdCompressionSettings(4, 10))))
            {
                archive.CreateEntry("data.bin", "data.bin");                   
                archive.Save(zipFile);
            }
            </code>
      </example>
    </member>
    <member name="M:Aspose.Zip.Saving.PPMdCompressionSettings.#ctor">
      <summary>
            Initializes a new instance of the <see cref="T:Aspose.Zip.Saving.PPMdCompressionSettings" /> class with default model order and sub-allocator size.
            </summary>
      <remarks>
            Default model order is 8 and sub-allocator size is 50MB.
            </remarks>
      <example>
        <code>
            using (Archive archive = new Archive(new ArchiveEntrySettings(new PPMdCompressionSettings())))
            {
                archive.CreateEntry("data.bin", "data.bin");                   
                archive.Save(zipFile);
            }
            </code>
      </example>
    </member>
    <member name="T:Aspose.Zip.Saving.StoreCompressionSettings">
      <inheritdoc />
      <summary>
            Settings for Store compression method.
            </summary>
      <remarks>
            This method stores original data as it is.
            </remarks>
    </member>
    <member name="M:Aspose.Zip.Saving.StoreCompressionSettings.#ctor">
      <summary>
            Initializes a new instance of the <see cref="T:Aspose.Zip.Saving.StoreCompressionSettings" /> class.
            </summary>
      <example>
        <code>
            using (Archive archive = new Archive(new ArchiveEntrySettings(new StoreCompressionSettings())))
            {
                archive.CreateEntry("data.bin", "data.bin");                   
                archive.Save(zipFile);
            }
            </code>
      </example>
    </member>
    <member name="T:Aspose.Zip.Saving.EncryptionMethod">
      <summary>
            Encryption/decryption methods can be used with zip archive.
            </summary>
    </member>
    <member name="F:Aspose.Zip.Saving.EncryptionMethod.Traditional">
      <summary>
            Traditional PKWARE encryption.
            </summary>
    </member>
    <member name="F:Aspose.Zip.Saving.EncryptionMethod.AES128">
      <summary>
            Advanced Encryption Standard with key length 128 bits.
            </summary>
    </member>
    <member name="F:Aspose.Zip.Saving.EncryptionMethod.AES192">
      <summary>
            Advanced Encryption Standard with key length 192 bits.
            </summary>
    </member>
    <member name="F:Aspose.Zip.Saving.EncryptionMethod.AES256">
      <summary>
            Advanced Encryption Standard with key length 256 bits.
            </summary>
    </member>
    <member name="T:Aspose.Zip.Saving.SevenZipAESEncryptionSettings">
      <inheritdoc />
      <summary>
            Settings for AES encryption or decryption algorithm.
            </summary>
    </member>
    <member name="M:Aspose.Zip.Saving.SevenZipAESEncryptionSettings.#ctor(System.String)">
      <summary>
            Initializes a new instance of the <see cref="T:Aspose.Zip.Saving.SevenZipAESEncryptionSettings" /> class.
            </summary>
      <param name="password">Password for encryption or decryption.</param>
      <example>
        <code>
             using (var archive = new SevenZipArchive(new SevenZipEntrySettings(null, new SevenZipAESEncryptionSettings("p@s$"))))
             {
                archive.CreateEntry("data.bin", "data.bin");
                archive.Save("archive.7z");
             }
            </code>
      </example>
    </member>
    <member name="T:Aspose.Zip.Saving.SevenZipCompressionMethod">
      <summary>
            Methods of compression that 7Z format support.
            </summary>
    </member>
    <member name="F:Aspose.Zip.Saving.SevenZipCompressionMethod.Store">
      <summary>
            The file is stored (no compression).
            </summary>
    </member>
    <member name="F:Aspose.Zip.Saving.SevenZipCompressionMethod.LZMA">
      <summary>
            File is compressed using LZMA.
            </summary>
    </member>
    <member name="F:Aspose.Zip.Saving.SevenZipCompressionMethod.LZMA2">
      <summary>
            File is compressed using LZMA2.
            </summary>
    </member>
    <member name="F:Aspose.Zip.Saving.SevenZipCompressionMethod.PPMd">
      <summary>
            File is compressed using PPMd.
            </summary>
    </member>
    <member name="F:Aspose.Zip.Saving.SevenZipCompressionMethod.BZip2">
      <summary>
            File is compressed using Bzip2.
            </summary>
    </member>
    <member name="T:Aspose.Zip.Saving.SevenZipCompressionSettings">
      <summary>
            Settings needed for 7z compressor or decompressor to work.
            </summary>
    </member>
    <member name="P:Aspose.Zip.Saving.SevenZipCompressionSettings.Method">
      <summary>
            Gets compression or decompression method.
            </summary>
    </member>
    <member name="T:Aspose.Zip.Saving.SevenZipEncryptionSettings">
      <summary>
            Base class for settings for several 7z encryption methods.
            </summary>
      <remarks>
            The AES-256 is the only possible encryption method for 7z archive. So the <see cref="T:Aspose.Zip.Saving.SevenZipAESEncryptionSettings" /> is the only implementation.
            </remarks>
    </member>
    <member name="M:Aspose.Zip.Saving.SevenZipEncryptionSettings.#ctor(System.String)">
      <summary>
            Initializes a new instance of the <see cref="T:Aspose.Zip.Saving.SevenZipEncryptionSettings" /> class.
            </summary>
      <param name="password">Password for encryption or decryption.</param>
    </member>
    <member name="P:Aspose.Zip.Saving.SevenZipEncryptionSettings.Password">
      <summary>
            Gets or sets password for encryption or decryption.
            </summary>
    </member>
    <member name="T:Aspose.Zip.Saving.SevenZipEntrySettings">
      <summary>
            Settings used to compress or decompress 7Z entries.
            </summary>
    </member>
    <member name="P:Aspose.Zip.Saving.SevenZipEntrySettings.CompressionSettings">
      <summary>
            Gets settings for compression or decompression routine.
            </summary>
    </member>
    <member name="P:Aspose.Zip.Saving.SevenZipEntrySettings.EncryptionSettings">
      <summary>
            Gets settings for encryption or decryption. Settings of particular entry may vary.
            </summary>
      <remarks>
            The <see cref="T:Aspose.Zip.Saving.SevenZipAESEncryptionSettings" /> is only option of 7Z archives.
            </remarks>
    </member>
    <member name="M:Aspose.Zip.Saving.SevenZipEntrySettings.#ctor(Aspose.Zip.Saving.SevenZipCompressionSettings,Aspose.Zip.Saving.SevenZipEncryptionSettings)">
      <summary>
            Initializes a new instance of the <see cref="T:Aspose.Zip.Saving.SevenZipEntrySettings" /> class.
            </summary>
      <param name="compressionSettings">Settings for compression. Pass null for default LZMA settings.
            <para>
            Can be one of these:
            <list type="bullet"><item><term><see cref="T:Aspose.Zip.Saving.SevenZipLZMACompressionSettings" /></term></item><item><term><see cref="T:Aspose.Zip.Saving.SevenZipLZMA2CompressionSettings" /></term></item><item><term><see cref="T:Aspose.Zip.Saving.SevenZipStoreCompressionSettings" /></term></item></list></para></param>
      <param name="encryptionSettings">Settings for encryption. Pass null if no need to encrypt or decrypt.
            <para>Can be only one:
            <list type="bullet"><item><term><see cref="T:Aspose.Zip.Saving.SevenZipAESEncryptionSettings" /></term></item></list></para></param>
    </member>
    <member name="T:Aspose.Zip.Saving.SevenZipLZMA2CompressionSettings">
      <inheritdoc />
      <summary>
            Settings for LZMA2 compression method within 7z archive.
            </summary>
      <remarks>
        <para>
            LZMA2 supports multiple runs of compressed LZMA data and uncompressed data.
            </para>
        <para>
            See more: https://en.wikipedia.org/wiki/Lempel–Ziv–Markov_chain_algorithm </para>
      </remarks>
    </member>
    <member name="M:Aspose.Zip.Saving.SevenZipLZMA2CompressionSettings.#ctor(System.Int32)">
      <summary>
            Instantiates settings for LZMA2 compression method within 7z archvie.
            </summary>
      <param name="dictionarySize">Size of history buffer, must be between 4096 and 1073741824.</param>
      <remarks>The bigger the dictionary, the better the compression ratio usually is, but dictionaries bigger than the uncompressed data are waste of RAM. </remarks>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="dictionarySize" /> is too big or too small.</exception>
    </member>
    <member name="P:Aspose.Zip.Saving.SevenZipLZMA2CompressionSettings.Method">
      <inheritdoc />
      <summary>
            Gets compression or decompression method.
            </summary>
    </member>
    <member name="P:Aspose.Zip.Saving.SevenZipLZMA2CompressionSettings.DictionarySize">
      <summary>
            Dictionary (history buffer) size indicates how many bytes of the recently processed uncompressed data is kept in memory.
            </summary>
      <remarks>The bigger the dictionary, the better the compression ratio usually is, but dictionaries bigger than the uncompressed data are waste of RAM. </remarks>
    </member>
    <member name="T:Aspose.Zip.Saving.SevenZipLZMACompressionSettings">
      <inheritdoc />
      <summary>
            Settings for LZMA compression method within 7z archive.
            </summary>
      <remarks>
        <para>
            The Lempel–Ziv–Markov chain algorithm (LZMA) is an algorithm used to perform lossless data compression.
            This algorithm uses a dictionary compression scheme somewhat similar to the LZ77 algorithm and features a high compression ratio and a variable compression-dictionary size.
            </para>
        <para>
            See more: https://en.wikipedia.org/wiki/Lempel–Ziv–Markov_chain_algorithm </para>
      </remarks>
    </member>
    <member name="P:Aspose.Zip.Saving.SevenZipLZMACompressionSettings.Method">
      <inheritdoc />
      <summary>
            Gets compression or decompression method.
            </summary>
    </member>
    <member name="T:Aspose.Zip.Saving.SevenZipStoreCompressionSettings">
      <inheritdoc />
      <summary>
            Settings for Store compression method within 7z archive.
            </summary>
      <remarks>
            This method stores original data as it is.
            </remarks>
    </member>
    <member name="P:Aspose.Zip.Saving.SevenZipStoreCompressionSettings.Method">
      <inheritdoc />
      <summary>
            Gets compression or decompression method.
            </summary>
    </member>
    <member name="T:Aspose.Zip.Saving.AesEcryptionSettings">
      <inheritdoc />
      <summary>
            Settings for AES encryption or decryption algorithm.
            </summary>
      <remarks>
            See more at https://www.winzip.com/win/en/aes_info.html
            </remarks>
    </member>
    <member name="M:Aspose.Zip.Saving.AesEcryptionSettings.#ctor(System.String,Aspose.Zip.Saving.EncryptionMethod)">
      <summary>
            Initializes a new instance of the <see cref="T:Aspose.Zip.Saving.AesEcryptionSettings" /> class.
            </summary>
      <param name="password">Password for encryption or decryption.</param>
      <param name="method">Algorithm option indicating block size of cipher.</param>
      <exception cref="T:System.NotSupportedException">
        <paramref name="method" /> is not one of <see cref="F:Aspose.Zip.Saving.EncryptionMethod.AES128" />, <see cref="F:Aspose.Zip.Saving.EncryptionMethod.AES192" />, or <see cref="F:Aspose.Zip.Saving.EncryptionMethod.AES256" />.</exception>
      <example>
        <code>
             using (var archive = new Archive(new ArchiveEntrySettings(null, new AesEcryptionSettings("p@s$", EncryptionMethod.AES256))))
             {
                archive.CreateEntry("data.bin", "data.bin");
                archive.Save("archive.zip");
             }
            </code>
      </example>
    </member>
    <member name="M:Aspose.Zip.Saving.AesEcryptionSettings.#ctor(Aspose.Zip.Saving.EncryptionMethod)">
      <summary>
            Initializes a new instance of the <see cref="T:Aspose.Zip.Saving.AesEcryptionSettings" /> class without a password.
            </summary>
      <param name="method">Algorithm option indicating block size of cipher.</param>
    </member>
    <member name="T:Aspose.Zip.Saving.ArchiveEntrySettings">
      <summary>
            Settings used to compress or decompress entries.
            </summary>
    </member>
    <member name="M:Aspose.Zip.Saving.ArchiveEntrySettings.#ctor(Aspose.Zip.Saving.CompressionSettings,Aspose.Zip.Saving.EncryptionSettings)">
      <summary>
            Initializes a new instance of the <see cref="T:Aspose.Zip.Saving.ArchiveEntrySettings" /> class.
            </summary>
      <param name="compressionSettings">Settings for compression. Pass null for default deflate settings.
            <para>
            Can be one of these:
            <list type="bullet"><item><term><see cref="T:Aspose.Zip.Saving.DeflateCompressionSettings" /></term></item><item><term><see cref="T:Aspose.Zip.Saving.StoreCompressionSettings" /></term></item><item><term><see cref="T:Aspose.Zip.Saving.Bzip2CompressionSettings" /></term></item><item><term><see cref="T:Aspose.Zip.Saving.LzmaCompressionSettings" /></term></item><item><term><see cref="T:Aspose.Zip.Saving.PPMdCompressionSettings" /></term></item></list></para></param>
      <param name="encryptionSettings">Settings for encryption. Pass null if no need to encrypt or decrypt.
            <para>Can be one of these:
            <list type="bullet"><item><term><see cref="T:Aspose.Zip.Saving.TraditionalEncryptionSettings" /></term></item><item><term><see cref="T:Aspose.Zip.Saving.AesEcryptionSettings" /></term></item></list></para></param>
    </member>
    <member name="P:Aspose.Zip.Saving.ArchiveEntrySettings.CompressionSettings">
      <summary>
            Gets settings for compression or decompression routine.
            </summary>
      <remarks>
            Can be one of these:
            <list type="bullet"><item><term><see cref="T:Aspose.Zip.Saving.DeflateCompressionSettings" /></term></item><item><term><see cref="T:Aspose.Zip.Saving.StoreCompressionSettings" /></term></item><item><term><see cref="T:Aspose.Zip.Saving.Bzip2CompressionSettings" /></term></item><item><term><see cref="T:Aspose.Zip.Saving.LzmaCompressionSettings" /></term></item><item><term><see cref="T:Aspose.Zip.Saving.PPMdCompressionSettings" /></term></item></list></remarks>
    </member>
    <member name="P:Aspose.Zip.Saving.ArchiveEntrySettings.EncryptionSettings">
      <summary>
            Gets settings for encryption or decryption. Settings of particular entry may vary.
            </summary>
      <remarks>
        <list type="bullet">
          <item>
            <term>
              <see cref="T:Aspose.Zip.Saving.TraditionalEncryptionSettings" />
            </term>
          </item>
          <item>
            <term>
              <see cref="T:Aspose.Zip.Saving.AesEcryptionSettings" />
            </term>
          </item>
        </list>
      </remarks>
    </member>
    <member name="T:Aspose.Zip.Saving.ArchiveSaveOptions">
      <summary>
            Options for saving an archive.
            </summary>
    </member>
    <member name="P:Aspose.Zip.Saving.ArchiveSaveOptions.Encoding">
      <summary>
            Gets or sets encoding for converting file names and other strings to bytes.
            </summary>
      <remarks>
            If not set, code page 437 will be used.
            </remarks>
    </member>
    <member name="P:Aspose.Zip.Saving.ArchiveSaveOptions.ArchiveComment">
      <summary>
            Gets or sets optional comment for the Zip file.
            </summary>
    </member>
    <member name="P:Aspose.Zip.Saving.ArchiveSaveOptions.ParallelOptions">
      <summary>
            Gets or sets settings for parallel compression.
            </summary>
      <remarks>
            Assign it if you want to utilize several CPU cores while compressing several archive entries.
            </remarks>
    </member>
    <member name="P:Aspose.Zip.Saving.ArchiveSaveOptions.SelfExtractorOptions">
      <summary>
            Gets or sets settings for self extracted archive.
            </summary>
      <remarks>Assign it if you need to compose executable program to extract an archive without any software installed on the target computer.</remarks>
    </member>
    <member name="T:Aspose.Zip.Saving.ParallelCompressionMode">
      <summary>
            Options of usage parallel compression facility.
            </summary>
    </member>
    <member name="F:Aspose.Zip.Saving.ParallelCompressionMode.Never">
      <summary>
            Do not compress in parallel.
            </summary>
      <example>
        <code>
            using (Archive archive = new Archive())
            {
                archive.CreateEntry("filename.bin", "filename.bin");
                archive.CreateEntry("filename1.bin", "filename1.bin");
                archive.CreateEntry("filename2.bin", "filename2.bin");
                archive.Save(destination, new ArchiveSaveOptions() { ParallelOptions = new ParallelOptions { ParallelCompressInMemory = ParallelCompressionMode.Never }});
            }
            </code>
      </example>
    </member>
    <member name="F:Aspose.Zip.Saving.ParallelCompressionMode.Always">
      <summary>
            Do compress in parallel. Beware out of memory.
            </summary>
      <example>
        <code>
            using (Archive archive = new Archive())
            {
                archive.CreateEntry("filename.bin", "filename.bin");
                archive.CreateEntry("filename1.bin", "filename1.bin");
                archive.CreateEntry("filename2.bin", "filename2.bin");
                archive.Save(destination, new ArchiveSaveOptions() { ParallelOptions = new ParallelOptions { ParallelCompressInMemory = ParallelCompressionMode.Always }});
            }
            </code>
      </example>
    </member>
    <member name="F:Aspose.Zip.Saving.ParallelCompressionMode.Auto">
      <summary>
            Decide if use parallel compression or not upon the entries.
            This option may compress in parallel some entries only.
            </summary>
      <example>
        <code>
            using (Archive archive = new Archive())
            {
                archive.CreateEntry("filename.bin", "filename.bin");
                archive.CreateEntry("filename1.bin", "filename1.bin");
                archive.CreateEntry("filename2.bin", "filename2.bin");
                archive.Save(destination, new ArchiveSaveOptions() { ParallelOptions = new ParallelOptions { ParallelCompressInMemory = ParallelCompressionMode.Auto }});
            }
            </code>
      </example>
    </member>
    <member name="T:Aspose.Zip.Saving.ParallelOptions">
      <summary>
            Options for parallel compression.
            </summary>
      <remarks>
            These options manage simultaneous compression by several CPU cores.
            </remarks>
      <example>
        <code>
            using (var archive = new Archive())
            {
                archive.CreateEntries("DirToCompress");
                archive.Save("archive.zip", new ArchiveSaveOptions() { ParallelOptions = new ParallelOptions { ParallelCompressInMemory = mode, AvailableMemorySize = 4000 } });
            }
            </code>
      </example>
    </member>
    <member name="P:Aspose.Zip.Saving.ParallelOptions.ParallelCompressInMemory">
      <summary>
            Gets or sets value indicating how parallel approach to be used.
            </summary>
    </member>
    <member name="P:Aspose.Zip.Saving.ParallelOptions.AvailableMemorySize">
      <summary>
            Gets or sets memory estimate in megabytes available to accomodate compressed entries without swap to disk.
            This value only makes sense if <see cref="P:Aspose.Zip.Saving.ParallelOptions.ParallelCompressInMemory" /> setting is in <see cref="F:Aspose.Zip.Saving.ParallelCompressionMode.Auto" /> mode.
            </summary>
      <remarks>
            This value is used to calculate biggest size of entry that can be compressed in parallel with others. All entries above this limit will be compressed sequentially.
            It is safe to have <see cref="P:Aspose.Zip.Saving.ParallelOptions.AvailableMemorySize" /> property as big as free RAM and even bigger.
            </remarks>
    </member>
    <member name="T:Aspose.Zip.Saving.SelfExtractorOptions">
      <summary>
            Options for creation of self-extracted executable archive.
            </summary>
      <example>
        <code>
            using (FileStream zipFile = File.Open("archive.exe", FileMode.Create))
            {
                using (var archive = new Archive())
                {
                    archive.CreateEntry("entry.bin", "data.bin");
                    var sfxOptions = new SelfExtractorOptions() { ExtractorTitle = "Extractor", CloseWindowOnExtraction = true, TitleIcon = "C:\pictorgam.ico" };
                    archive.Save(zipFile, new ArchiveSaveOptions() { SelfExtractorOptions = sfxOptions });
                }
            }
            </code>
      </example>
    </member>
    <member name="P:Aspose.Zip.Saving.SelfExtractorOptions.ExtractorTitle">
      <summary>
            Gets or sets the title of extractor's window.
            </summary>
    </member>
    <member name="P:Aspose.Zip.Saving.SelfExtractorOptions.TitleIcon">
      <summary>
            Gets or sets path to title icon for main windows of extractor application.
            </summary>
    </member>
    <member name="P:Aspose.Zip.Saving.SelfExtractorOptions.CloseWindowOnExtraction">
      <summary>
            Gets or sets a value indicating whether extractor window must be closed upon extraction or not.
            </summary>
    </member>
    <member name="T:Aspose.Zip.Saving.TraditionalEncryptionSettings">
      <inheritdoc />
      <summary>
            Settings for traditional ZipCrypto algorithm.
            </summary>
      <remarks>
            See section 6.0 at ZIP format description: https://pkware.cachefly.net/webdocs/casestudies/APPNOTE.TXT
            </remarks>
    </member>
    <member name="M:Aspose.Zip.Saving.TraditionalEncryptionSettings.#ctor(System.String)">
      <summary>
            Initializes a new instance of the <see cref="T:Aspose.Zip.Saving.TraditionalEncryptionSettings" /> class.
            </summary>
      <param name="password">Password for encryption.</param>
      <example>
        <code>
            using (var archive = new Archive(new ArchiveEntrySettings(null, new TraditionalEncryptionSettings("p@s$"))))
            {
                archive.CreateEntry("data.bin", "data.bin");
                archive.Save(zipFile);
            }
            </code>
      </example>
    </member>
    <member name="M:Aspose.Zip.Saving.TraditionalEncryptionSettings.#ctor(System.String,System.Text.Encoding)">
      <summary>
            Initializes a new instance of the <see cref="T:Aspose.Zip.Saving.TraditionalEncryptionSettings" /> class with user defined encoding.
            </summary>
      <remarks>Usage of this constructor is discouraged. Setting the encoding may contradict the standard and produce incompatible archive.</remarks>
      <param name="password">Password for encryption.</param>
      <param name="encoding">Encoding for password chracters.</param>
      <example>
        <code>
            using (var archive = new Archive(new ArchiveEntrySettings(null, new TraditionalEncryptionSettings("p£s$", System.Text.Encoding.ASCII))))
            {
                archive.CreateEntry("data.bin", "data.bin");
                archive.Save(zipFile);
            }
            </code>
      </example>
    </member>
    <member name="M:Aspose.Zip.Saving.TraditionalEncryptionSettings.#ctor">
      <summary>
            Initializes a new instance of the <see cref="T:Aspose.Zip.Saving.TraditionalEncryptionSettings" /> class without a password.
            </summary>
    </member>
    <member name="T:Aspose.Zip.ComHelper">
      <summary>
            Provides methods for COM clients to load archives into Aspose.Zip.
            </summary>
      <remarks>
            Use the ComHelper class to load an archive from a file or stream.
            Particular classes provide a default constructor to create a new archive
            and also provides overloaded constructors to load an archive from a file or stream.
            If you are using Aspose.Zip from a .NET application, you can use all of the archives
            constructors directly, but if you are using Aspose.Zip from a COM application,
            only the default archive constructor is available.
            </remarks>
    </member>
    <member name="M:Aspose.Zip.ComHelper.#ctor">
      <summary>
            Initializes a new instance of this class.
            </summary>
    </member>
    <member name="M:Aspose.Zip.ComHelper.OpenGzip(System.IO.Stream)">
      <summary>
            Allows a COM application to load a gzip archive from a stream.
            </summary>
    </member>
    <member name="M:Aspose.Zip.ComHelper.OpenGzip(System.String)">
      <summary>
            Allows a COM application to load a gzip archive from a file.
            </summary>
    </member>
    <member name="M:Aspose.Zip.ComHelper.OpenZip(System.IO.Stream)">
      <summary>
            Allows a COM application to load a zip archive from a stream.
            </summary>
    </member>
    <member name="M:Aspose.Zip.ComHelper.OpenZip(System.String)">
      <summary>
            Allows a COM application to load a zip archive from a file.
            </summary>
    </member>
    <member name="M:Aspose.Zip.ComHelper.OpenRar(System.IO.Stream)">
      <summary>
            Allows a COM application to load a rar archive from a stream.
            </summary>
    </member>
    <member name="M:Aspose.Zip.ComHelper.OpenRar(System.String)">
      <summary>
            Allows a COM application to load a rar archive from a file.
            </summary>
    </member>
    <member name="M:Aspose.Zip.ComHelper.OpenBzip2(System.IO.Stream)">
      <summary>
            Allows a COM application to load a bzip2 archive from a stream.
            </summary>
    </member>
    <member name="M:Aspose.Zip.ComHelper.OpenBzip2(System.String)">
      <summary>
            Allows a COM application to load a bzip2 archive from a file.
            </summary>
    </member>
    <member name="T:Aspose.Zip.NamespaceDoc">
      <summary>
            The <see cref="N:Aspose.Zip" /> namespace contains classes which represent archive related entities.
            </summary>
    </member>
    <member name="T:Aspose.Zip.Gzip.NamespaceDoc">
      <summary>
            The <see cref="N:Aspose.Zip.Gzip" /> namespace contains classes which represent gzip archive.
            </summary>
    </member>
    <member name="T:Aspose.Zip.Gzip.GzipArchive">
      <summary>
            This class represents gzip archive file. Use it to compose or extract gzip archives.
            </summary>
      <remarks>Gzip compression algorithm is based on the DEFLATE algorithm, which is a combination of LZ77 and Huffman coding.</remarks>
    </member>
    <member name="M:Aspose.Zip.Gzip.GzipArchive.#ctor">
      <summary>
            Initializes a new instance of the <see cref="T:Aspose.Zip.Gzip.GzipArchive" /> class prepared for compressing.
            </summary>
      <example>
        <para>
            The following example shows how to compress a file.
            </para>
        <code>
            using (GzipArchive archive = new GzipArchive()) 
            {
                archive.SetSource("data.bin");
                archive.Save("archive.gz");
            }
            </code>
      </example>
    </member>
    <member name="M:Aspose.Zip.Gzip.GzipArchive.#ctor(System.IO.Stream,System.Boolean)">
      <summary>
            Initializes a new instance of the <see cref="T:Aspose.Zip.Gzip.GzipArchive" /> class prepared for decompressing.
            </summary>
      <remarks>
            This constructor does not decompress. See <see cref="M:Aspose.Zip.Gzip.GzipArchive.Open" /> method for decompressing.
            </remarks>
      <param name="sourceStream">The source of the archive.</param>
      <param name="parseHeader">Whether to parse stream header to figure out properties, including name. Makes sense for seekable stream only.</param>
      <example>
        <para>Open an archive from a stream and extract it to a <c>MemoryStream</c></para>
        <code>
            var ms = new MemoryStream();
            using (GzipArchive archive = new GzipArchive(File.OpenRead("archive.gz")))
              archive.Open().CopyTo(ms);
            </code>
      </example>
    </member>
    <member name="M:Aspose.Zip.Gzip.GzipArchive.#ctor(System.String,System.Boolean)">
      <summary>
            Initializes a new instance of the <see cref="T:Aspose.Zip.Gzip.GzipArchive" /> class.
            </summary>
      <remarks>
            This constructor does not decompress. See <see cref="M:Aspose.Zip.Gzip.GzipArchive.Open" /> method for decompressing.
            </remarks>
      <param name="path">The path to the archive file.</param>
      <param name="parseHeader">Whether to parse stream header to figure out properties, including name. Makes sense for seekable stream only.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission to access </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="path" /> is empty, contains only white spaces, or contains invalid characters.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access to file <paramref name="path" /> is denied.</exception>
      <exception cref="T:System.IO.PathTooLongException"> The specified <paramref name="path" />, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters.</exception>
      <exception cref="T:System.NotSupportedException">File at <paramref name="path" /> contains a colon (:) in the middle of the string.</exception>
      <example>
        <para>Open an archive from file by path and extract it to a <c>MemoryStream</c></para>
        <code>
            var ms = new MemoryStream();
            using (GzipArchive archive = new GzipArchive("archive.gz"))
              archive.Open().CopyTo(ms);
            </code>
      </example>
    </member>
    <member name="P:Aspose.Zip.Gzip.GzipArchive.Name">
      <summary>
            Name of original file.
            </summary>
    </member>
    <member name="M:Aspose.Zip.Gzip.GzipArchive.SetSource(System.IO.Stream)">
      <summary>
            Sets the content to be compressed within the archive.
            </summary>
      <param name="source">The input stream for the archive.</param>
      <example>
        <code>
            using (var archive = new GzipArchive())
            {
                archive.SetSource(new MemoryStream(new byte[] { 0x00, 0xFF }));
                archive.Save("archive.gz");
            }
            </code>
      </example>
    </member>
    <member name="M:Aspose.Zip.Gzip.GzipArchive.SetSource(System.IO.FileInfo)">
      <summary>
            Sets the content to be compressed within the archive.
            </summary>
      <param name="fileInfo">The reference to a file to be compressed.</param>
      <example>
        <para>Open an archive from a stream and extract it to a <c>MemoryStream</c></para>
        <code>
            using (var archive = new GzipArchive()) 
            {
                archive.SetSource(new FileInfo("data.bin"));
                archive.Save("archive.gz");
            }
            </code>
      </example>
    </member>
    <member name="M:Aspose.Zip.Gzip.GzipArchive.SetSource(System.String)">
      <summary>
            Sets the content to be compressed within the archive.
            </summary>
      <param name="path">Path to file to be compressed.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission to access </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="path" /> is empty, contains only white spaces, or contains invalid characters.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access to file <paramref name="path" /> is denied.</exception>
      <exception cref="T:System.IO.PathTooLongException"> The specified <paramref name="path" />, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters.</exception>
      <exception cref="T:System.NotSupportedException">File at <paramref name="path" /> contains a colon (:) in the middle of the string.</exception>
      <example>
        <para>Open an archive from file by path and extract it to a <c>MemoryStream</c></para>
        <code>
            using (var archive = new GzipArchive()) 
            {
                archive.SetSource("data.bin");
                archive.Save("archive.gz");
            }
            </code>
      </example>
    </member>
    <member name="M:Aspose.Zip.Gzip.GzipArchive.SetSource(Aspose.Zip.Tar.TarArchive)">
      <summary>
            Sets the content to be compressed within the archive.
            </summary>
      <param name="tarArchive">Tar archive to be compressed.</param>
      <remarks>Use this method to compose joint tar.gz archive.</remarks>
      <example>
        <code>
            using (var tarArchive = new TarArchive())
            {
                tarArchive.CreateEntry("first.bin", "data1.bin");
                tarArchive.CreateEntry("second.bin", "data2.bin");
                using (var gzippedArchive = new GzipArchive())
                {
                       gzippedArchive.SetSource(tarArchive);
                       gzippedArchive.Save("archive.tar.gz");
                }
            }
            </code>
      </example>
    </member>
    <member name="M:Aspose.Zip.Gzip.GzipArchive.Dispose">
      <inheritdoc />
      <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            </summary>
    </member>
    <member name="M:Aspose.Zip.Gzip.GzipArchive.Open">
      <summary>
            Opens the archive for extraction and provides a stream with archive content.
            </summary>
      <returns>The stream that represents the contents of the archive.</returns>
      <remarks>
            Read from the stream to get original content of file. See examples section.
            </remarks>
      <example>
        <para>Extracts the archive and copies extracted content to file stream.</para>
        <code>        
            using (var archive = new GzipArchive("archive.gz"))
            {
                using (var extracted = File.Create("data.bin"))
                {
                    var unpacked = archive.Open();
                    byte[] b = new byte[8192];
                    int bytesRead;
                    while (0 &lt; (bytesRead = unpacked.Read(b, 0, b.Length)))
                        extracted.Write(b, 0, bytesRead);
                }            
            }
            </code>
        <para>
            You may use Stream.CopyTo method for .NET 4.0 and higher:
            <code>
            unpacked.CopyTo(extracted);
            </code></para>
      </example>
    </member>
    <member name="M:Aspose.Zip.Gzip.GzipArchive.Save(System.IO.Stream)">
      <summary>
            Saves archive to the stream provided.
            </summary>
      <param name="outputStream">Destination stream.</param>
      <remarks>
        <para>
          <paramref name="outputStream" /> must be writable.</para>
      </remarks>
      <exception cref="T:System.ArgumentException">
        <paramref name="outputStream" /> is not writable.</exception>
      <exception cref="T:System.InvalidOperationException">Source has not been supplied.</exception>
      <example>
        <para>Writes compressed data to http response stream.</para>
        <code>
            using (var archive = new GzipArchive()) 
            {
                archive.SetSource(new FileInfo("data.bin"));
                archive.Save(httpResponse.OutputStream);
            }
            </code>
      </example>
    </member>
    <member name="M:Aspose.Zip.Gzip.GzipArchive.Save(System.String)">
      <summary>
            Saves archive to destination file provided.
            </summary>
      <param name="destinationFileName">The path of the archive to be created. If the specified file name points to an existing file, it will be overwritten.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destinationFileName" /> is null.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission to access </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="destinationFileName" /> is empty, contains only white spaces, or contains invalid characters.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access to file <paramref name="destinationFileName" /> is denied.</exception>
      <exception cref="T:System.IO.PathTooLongException"> The specified <paramref name="destinationFileName" />, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters.</exception>
      <exception cref="T:System.NotSupportedException">File at <paramref name="destinationFileName" /> contains a colon (:) in the middle of the string.</exception>
      <example>
        <code>
            using (var archive = new GzipArchive())
            {
                archive.SetSource("data.bin");
                archive.Save("archive.gz");
            }
            </code>
      </example>
    </member>
    <member name="M:Aspose.Zip.Gzip.GzipArchive.Dispose(System.Boolean)">
      <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            </summary>
      <param name="disposing">Whether managed resources should be disposed.</param>
    </member>
    <member name="T:Aspose.Zip.Tar.NamespaceDoc">
      <summary>
            The <see cref="N:Aspose.Zip.Tar" /> namespace contains classes which represent tar archive related entities.
            </summary>
    </member>
    <member name="T:Aspose.Zip.Tar.TarArchive">
      <summary>
            This class represents tar archive file. Use it to compose, extract, or update tar archives.
            </summary>
    </member>
    <member name="M:Aspose.Zip.Tar.TarArchive.#ctor">
      <summary>
            Initializes a new instance of the <see cref="T:Aspose.Zip.Tar.TarArchive" /> class.
            </summary>
      <example>
        <para>The following example shows how to compress a file.</para>
        <code>
            using (var archive = new TarArchive())
            {
                archive.CreateEntry("first.bin", "data.bin");
                archive.Save("archive.tar");
            }
            </code>
      </example>
    </member>
    <member name="M:Aspose.Zip.Tar.TarArchive.#ctor(System.IO.Stream)">
      <summary>
            Initializes a new instance of the <see cref="T:Aspose.Zip.Archive" /> class and composes entries list can be extracted from the archive.
            </summary>
      <remarks>
            This constructor does not unpack any entry. See <see cref="M:Aspose.Zip.Tar.TarEntry.Open" /> method for unpacking.
            </remarks>
      <param name="sourceStream">The source of the archive. It must be seekable.</param>
      <exception cref="T:System.IO.InvalidDataException">
        <paramref name="sourceStream" /> is not seekable.</exception>
      <example>
        <para>The following example shows how to extract all of the entries to a directory.</para>
        <code>
            using (var archive = new TarArchive(File.OpenRead("archive.tar")))
            { 
               archive.ExtractToDirectory("C:\extracted");
            }
            </code>
      </example>
    </member>
    <member name="M:Aspose.Zip.Tar.TarArchive.#ctor(System.String)">
      <summary>
            Initializes a new instance of the <see cref="T:Aspose.Zip.Tar.TarArchive" /> class and composes entries list can be extracted from the archive.
            </summary>
      <remarks>
            This constructor does not unpack any entry. See <see cref="M:Aspose.Zip.Tar.TarEntry.Open" /> method for unpacking.
            </remarks>
      <param name="path">The path to the archive file.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission to access </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="path" /> is empty, contains only white spaces, or contains invalid characters.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access to file <paramref name="path" /> is denied.</exception>
      <exception cref="T:System.IO.PathTooLongException"> The specified <paramref name="path" />, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters.</exception>
      <exception cref="T:System.NotSupportedException">File at <paramref name="path" /> contains a colon (:) in the middle of the string.</exception>
      <example>
        <para>The following example shows how to extract all of the entries to a directory.</para>
        <code>
            using (var archive = new TarArchive("archive.tar")) 
            { 
               archive.ExtractToDirectory("C:\extracted");
            }
            </code>
      </example>
    </member>
    <member name="P:Aspose.Zip.Tar.TarArchive.Entries">
      <summary>
            Gets entries of <see cref="T:Aspose.Zip.Tar.TarEntry" /> type constituting the archive.
            </summary>
    </member>
    <member name="M:Aspose.Zip.Tar.TarArchive.CreateEntry(System.String,System.IO.Stream,System.IO.FileSystemInfo)">
      <summary>
            Create single entry within the archive.
            </summary>
      <param name="name">The name of the entry.</param>
      <param name="source">The input stream for the entry.</param>
      <param name="fileInfo">The metadata of file or folder to be compressed.</param>
      <returns>Tar entry instance.</returns>
      <remarks>
        <para>The entry name is solely set within <paramref name="name" /> parameter. The file name provided in <paramref name="fileInfo" /> parameter does not affect the entry name.</para>
        <para>
          <paramref name="fileInfo" /> can refer to <see cref="T:System.IO.DirectoryInfo" /> if the entry is directory.</para>
      </remarks>
      <example>
        <code>        
             using (var archive = new TarArchive())
             {
                archive.CreateEntry("bytes", new MemoryStream(new byte[] {0x00, 0xFF}));
                archive.Save(tarFile);
             }
            </code>
      </example>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="name" /> is too long for tar as of IEEE 1003.1-1998 standard.</exception>
      <exception cref="T:System.ArgumentException">File name, as a part of <paramref name="name" />, exceeds 100 symbols.</exception>
    </member>
    <member name="M:Aspose.Zip.Tar.TarArchive.CreateEntry(System.String,System.IO.FileInfo,System.Boolean)">
      <summary>
            Create single entry within the archive.
            </summary>
      <param name="name">The name of the entry.</param>
      <param name="fileInfo">The metadata of file or folder to be compressed.</param>
      <param name="openImmediately">True if open the file immediately, otherwise open the file on archive saving.</param>
      <returns>Tar entry instance.</returns>
      <remarks>
        <para>The entry name is solely set within <paramref name="name" /> parameter. The file name provided in <paramref name="fileInfo" /> parameter does not affect the entry name.</para>
        <para>
          <paramref name="fileInfo" /> can refer to <see cref="T:System.IO.DirectoryInfo" /> if the entry is directory.</para>
        <para>If the file is opened immediately with <paramref name="openImmediately" /> parameter it becomes blocked until archive is disposed.</para>
      </remarks>
      <example>
        <code>
             FileInfo fi = new FileInfo("data.bin");
             using (var archive = new TarArchive())
             {
                archive.CreateEntry("data.bin", fi);
                archive.Save(tarFile);
             }
            </code>
      </example>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="name" /> is too long for tar as of IEEE 1003.1-1998 standard.</exception>
      <exception cref="T:System.ArgumentException">File name, as a part of <paramref name="name" />, exceeds 100 symbols.</exception>
    </member>
    <member name="M:Aspose.Zip.Tar.TarArchive.CreateEntry(System.String,System.String,System.Boolean)">
      <summary>
            Create single entry within the archive.
            </summary>
      <param name="name">The name of the entry.</param>
      <param name="path">Path to file to be compressed.</param>
      <param name="openImmediately">True if open the file immediately, otherwise open the file on archive saving.</param>
      <returns>Tar entry instance.</returns>
      <remarks>
        <para>The entry name is solely set within <paramref name="name" /> parameter. The file name provided in <paramref name="path" /> parameter does not affect the entry name.</para>
        <para>If the file is opened immediately with <paramref name="openImmediately" /> parameter it becomes blocked until archive is disposed.</para>
      </remarks>
      <example>
        <code>
            using (var archive = new TarArchive())
            {
                archive.CreateEntry("first.bin", "data.bin");
                archive.Save(outputTarFile);
            }
            </code>
      </example>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission to access </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="path" /> is empty, contains only white spaces, or contains invalid characters. - or - File name, as a part of <paramref name="name" />, exceeds 100 symbols.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access to file <paramref name="path" /> is denied.</exception>
      <exception cref="T:System.IO.PathTooLongException"> The specified <paramref name="path" />, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. - or - <paramref name="name" /> is too long for tar as of IEEE 1003.1-1998 standard.</exception>
      <exception cref="T:System.NotSupportedException">File at <paramref name="path" /> contains a colon (:) in the middle of the string.</exception>
    </member>
    <member name="M:Aspose.Zip.Tar.TarArchive.CreateEntries(System.IO.DirectoryInfo,System.Boolean)">
      <summary>
            Adds to the archive all the files and directories recursively in the directory given.
            </summary>
      <param name="directory">Directory to compress.</param>
      <param name="includeRootDirectory">Indicates whether to include the root directory itself or not.</param>
      <returns>The archive with entries composed.</returns>
      <example>
        <code>
            using (FileStream tarFile = File.Open("archive.tar", FileMode.Create))
            {
                using (var archive = new TarArchive())
                {
                    archive.CreateEntries(new DirectoryInfo("C:\folder"), false);
                    archive.Save(tarFile);
                }
            }
            </code>
      </example>
    </member>
    <member name="M:Aspose.Zip.Tar.TarArchive.CreateEntries(System.String,System.Boolean)">
      <summary>
            Adds to the archive all the files and directories recursively in the directory given.
            </summary>
      <param name="sourceDirectory">Directory to compress.</param>
      <param name="includeRootDirectory">Indicates whether to include the root directory itself or not.</param>
      <returns>The archive with entries composed.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceDirectory" /> is null.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission to access <paramref name="sourceDirectory" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceDirectory" /> contains invalid characters such as ", &lt;, &gt;, or |.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. The specified path, file name, or both are too long.</exception>
      <example>
        <code>
            using (FileStream tarFile = File.Open("archive.tar", FileMode.Create))
            {
                using (var archive = new TarArchive())
                {
                    archive.CreateEntries("C:\folder", false);
                    archive.Save(tarFile);
                }
            }
            </code>
      </example>
    </member>
    <member name="M:Aspose.Zip.Tar.TarArchive.DeleteEntry(Aspose.Zip.Tar.TarEntry)">
      <summary>
            Removes the first occurrence of a specific entry from the entries list.
            </summary>
      <param name="entry">The entry to remove from the entries list.</param>
      <returns>The archive with the entry deleted.</returns>
      <example>
        <para>Here is how you can remove all entries except the last one:</para>
        <code>
            using (var archive = new TarArchive("archive.tar"))
            {
                while (archive.Entries.Count &gt; 1)
                    archive.DeleteEntry(archive.Entries[0]);
                archive.Save(outputTarFile);
            }
            </code>
      </example>
    </member>
    <member name="M:Aspose.Zip.Tar.TarArchive.DeleteEntry(System.Int32)">
      <summary>
            Removes the entry from the entries list by index.
            </summary>
      <param name="entryIndex">The zero-based index of the entry to remove.</param>
      <returns>The archive with the entry deleted.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="entryIndex" /> is less than 0.-or- <paramref name="entryIndex" /> is equal to or greater than <c>Entries</c> count.</exception>
      <example>
        <code>
            using (var archive = new TarArchive("two_files.tar"))
            {
                archive.DeleteEntry(0);
                archive.Save("single_file.tar");
            }
            </code>
      </example>
    </member>
    <member name="M:Aspose.Zip.Tar.TarArchive.ExtractToDirectory(System.String)">
      <summary>
            Extracts all the files in the archive to the directory provided.
            </summary>
      <param name="destinationDirectory">The path to the directory to place the extracted files in.</param>
      <remarks>If the directory does not exist, it will be created.</remarks>
      <exception cref="T:System.ArgumentNullException">path is null</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters and file names must be less than 260 characters.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission to access existing directory.</exception>
      <exception cref="T:System.NotSupportedException">If directory does not exist, path contains a colon character (:) that is not part of a drive label ("C:\").</exception>
      <exception cref="T:System.ArgumentException">path is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters by using the System.IO.Path.GetInvalidPathChars method. -or- path is prefixed with, or contains, only a colon character (:).</exception>
      <exception cref="T:System.IO.IOException">The directory specified by path is a file. -or- The network name is not known.</exception>
      <example>
        <code>
            using (var archive = new TarArchive("archive.tar")) 
            { 
               archive.ExtractToDirectory("C:\extracted");
            }
            </code>
      </example>
    </member>
    <member name="M:Aspose.Zip.Tar.TarArchive.Save(System.IO.Stream)">
      <summary>
            Saves archive to the stream provided.
            </summary>
      <param name="outputStream">Destination stream.</param>
      <remarks>
        <para>
          <paramref name="outputStream" /> must be writable.</para>
      </remarks>
      <exception cref="T:System.ArgumentException">
        <paramref name="outputStream" /> is not writable. - or - <paramref name="outputStream" /> is the same stream we extract from.</exception>
      <example>
        <code>
            using (FileStream tarFile = File.Open("archive.tar", FileMode.Create))
            {
                using (var archive = new TarArchive())
                {
                    archive.CreateEntry("entry1", "data.bin");        
                    archive.Save(tarFile);
                }
            }       
            </code>
      </example>
    </member>
    <member name="M:Aspose.Zip.Tar.TarArchive.Save(System.String)">
      <summary>
            Saves archive to destination file provided.
            </summary>
      <param name="destinationFileName">The path of the archive to be created. If the specified file name points to an existing file, it will be overwritten.</param>
      <remarks>
        <para>It is possible to save an archive to the same path as it was loaded from.
            However, this is not recommended because this approach uses copying to temporary file.</para>
      </remarks>
      <example>
        <code>
            using (var archive = new TarArchive())
            {
                archive.CreateEntry("entry1", "data.bin");        
                archive.Save("myarchive.tar");
            }       
            </code>
      </example>
      <exception cref="T:System.ArgumentException">
        <paramref name="destinationFileName" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by System.IO.Path.InvalidPathChars.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destinationFileName" /> is null.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified <paramref name="destinationFileName" />, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified <paramref name="destinationFileName" /> is invalid, (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while opening the file.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="destinationFileName" /> specified a file that is read-only and access is not Read.-or- path specified a directory.-or- The caller does not have the required permission.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="destinationFileName" /> is in an invalid format.</exception>
    </member>
    <member name="M:Aspose.Zip.Tar.TarArchive.Dispose">
      <inheritdoc />
      <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            </summary>
    </member>
    <member name="M:Aspose.Zip.Tar.TarArchive.Dispose(System.Boolean)">
      <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            </summary>
      <param name="disposing">Whether managed resources should be disposed.</param>
    </member>
    <member name="T:Aspose.Zip.Tar.TarEntry">
      <summary>
             Represents single file within tar archive.
            </summary>
    </member>
    <member name="P:Aspose.Zip.Tar.TarEntry.Name">
      <summary>
            Gets or sets name of the entry within archive.
            </summary>
    </member>
    <member name="P:Aspose.Zip.Tar.TarEntry.IsDirectory">
      <summary>
            Gets a value indicating whether the entry represents directory.
            </summary>
    </member>
    <member name="M:Aspose.Zip.Tar.TarEntry.Open">
      <summary>
            Opens the entry for extraction and provides a stream with entry content.
            </summary>
      <returns>The stream that represents the contents of the entry.</returns>
      <remarks>
            Read from the stream to get original content of file. See examples section.
            </remarks>
      <example>
            Usage:
            <code>Stream decompressed = entry.Open();</code><para>
            .NET 4.0 and higher - use Stream.CopyTo method:
            <code>
            decompressed.CopyTo(httpResponse.OutputStream)
            </code></para><para>
            .NET 3.5 and before - copy bytes manually:
            <code>
            byte[] buffer = new byte[8192];
            int bytesRead;
            while (0 &lt; (bytesRead = decompressed.Read(buffer, 0, buffer.Length)))
             fileStream.Write(buffer, 0, bytesRead);
            </code></para></example>
    </member>
    <member name="M:Aspose.Zip.Tar.TarEntry.Extract(System.String)">
      <summary>
            Extracts the entry to the filesystem by the path provided.
            </summary>
      <param name="path">The path to destination file. If the file already exists, it will be overwritten.</param>
      <returns>The file info of composed file.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission to access.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="path" /> is empty, contains only white spaces, or contains invalid characters.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access to file <paramref name="path" /> is denied.</exception>
      <exception cref="T:System.IO.PathTooLongException"> The specified <paramref name="path" />, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters.</exception>
      <exception cref="T:System.NotSupportedException">File at <paramref name="path" /> contains a colon (:) in the middle of the string.</exception>
      <example>
        <code>
            using (var archive = new TarArchive("archive.tar"))
            {
                archive.Entries[0].Extract("data.bin");
            }
            </code>
      </example>
    </member>
    <member name="T:Aspose.Zip.SevenZip.NamespaceDoc">
      <summary>
            The <see cref="N:Aspose.Zip.SevenZip" /> namespace contains classes which represent 7z archive related entities.
            </summary>
    </member>
    <member name="T:Aspose.Zip.SevenZip.SevenZipArchive">
      <summary>
            This class represents 7z archive file. Use it to compose 7z archives.
            </summary>
    </member>
    <member name="M:Aspose.Zip.SevenZip.SevenZipArchive.#ctor(Aspose.Zip.Saving.SevenZipEntrySettings)">
      <summary>
            Initializes a new instance of the <see cref="T:Aspose.Zip.SevenZip.SevenZipArchive" /> class with optional settings for its entries.
            </summary>
      <param name="newEntrySettings">Compression and encryption settings used for newly added <see cref="T:Aspose.Zip.SevenZip.SevenZipArchiveEntry" /> items.</param>
      <example>
        <para>
            The following example shows how to compress a single file with default settings: LZMA compression without encryption.
            </para>
        <code>
            using (FileStream sevenZipFile = File.Open("archive.7z", FileMode.Create))
            {
                using (var archive = new SevenZipArchive())
                {
                    archive.CreateEntry("data.bin", "file.dat");
                    archive.Save(sevenZipFile);
                }
            }
            </code>
      </example>
    </member>
    <member name="P:Aspose.Zip.SevenZip.SevenZipArchive.NewEntrySettings">
      <summary>
            Compression and encryption settings used for newly added <see cref="T:Aspose.Zip.SevenZip.SevenZipArchiveEntry" /> items.
            </summary>
    </member>
    <member name="P:Aspose.Zip.SevenZip.SevenZipArchive.Entries">
      <summary>
            Gets entries of <see cref="T:Aspose.Zip.SevenZip.SevenZipArchiveEntry" /> type constituting the archive.
            </summary>
    </member>
    <member name="M:Aspose.Zip.SevenZip.SevenZipArchive.CreateEntry(System.String,System.IO.FileInfo,System.Boolean,Aspose.Zip.Saving.SevenZipEntrySettings)">
      <summary>
            Create single entry within the archive.
            </summary>
      <param name="name">The name of the entry.</param>
      <param name="fileInfo">The metadata of file to be compressed.</param>
      <param name="openImmediately">True if open the file immediately, otherwise open the file on archive saving.</param>
      <param name="newEntrySettings">Compression and encryption settings used for added <see cref="T:Aspose.Zip.SevenZip.SevenZipArchiveEntry" /> item.</param>
      <returns>Seven Zip entry instance.</returns>
      <remarks>
        <para>The entry name is solely set within <paramref name="name" /> parameter. The file name provided in <paramref name="fileInfo" /> parameter does not affect the entry name.</para>
        <para>If the file is opened immediately with <paramref name="openImmediately" /> parameter it becomes blocked until archive is saved.</para>
      </remarks>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="fileInfo" /> is read-only or is a directory.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid, such as being on an unmapped drive.</exception>
      <exception cref="T:System.IO.IOException">The file is already open.</exception>
      <example>
        <para>Compose archive with entries encrypted with different passwords each.</para>
        <code>
            using (FileStream sevenZipFile = File.Open("archive.7z", FileMode.Create))
            {
                FileInfo fi1 = new FileInfo("data1.bin");
                FileInfo fi2 = new FileInfo("data2.bin");
                FileInfo fi3 = new FileInfo("data3.bin");
                using (var archive = new SevenZipArchive())
                {
                    archive.CreateEntry("entry1.bin", fi1, false, new SevenZipEntrySettings(new SevenZipStoreCompressionSettings(), new SevenZipAESEncryptionSettings("test1")));
                    archive.CreateEntry("entry2.bin", fi2, false, new SevenZipEntrySettings(new SevenZipStoreCompressionSettings(), new SevenZipAESEncryptionSettings("test2")));
                    archive.CreateEntry("entry3.bin", fi3, false, new SevenZipEntrySettings(new SevenZipStoreCompressionSettings(), new SevenZipAESEncryptionSettings("test3")));
                    archive.Save(sevenZipFile);
                }
            }
            </code>
      </example>
    </member>
    <member name="M:Aspose.Zip.SevenZip.SevenZipArchive.CreateEntry(System.String,System.IO.Stream,Aspose.Zip.Saving.SevenZipEntrySettings,System.IO.FileSystemInfo)">
      <summary>
            Create single entry within the archive.
            </summary>
      <param name="name">The name of the entry.</param>
      <param name="source">The input stream for the entry.</param>
      <param name="newEntrySettings">Compression and encryption settings used for added <see cref="T:Aspose.Zip.SevenZip.SevenZipArchiveEntry" /> item.</param>
      <param name="fileInfo">The metadata of file or folder to be compressed.</param>
      <returns>SevenZip entry instance.</returns>
      <remarks>
        <para>The entry name is solely set within <paramref name="name" /> parameter. The file name provided in <paramref name="fileInfo" /> parameter does not affect the entry name.</para>
        <para>
          <paramref name="fileInfo" /> can refer to <see cref="T:System.IO.DirectoryInfo" /> if the entry is directory.</para>
      </remarks>
      <exception cref="T:System.InvalidOperationException">Both <paramref name="source" /> and <paramref name="fileInfo" /> are null or <paramref name="source" /> is null and <paramref name="fileInfo" /> stands for directory.</exception>
      <example>
        <para>Compose archive with LZMA2 compressed encrypted entry.</para>
        <code>
            using (FileStream sevenZipFile = File.Open("archive.7z", FileMode.Create))
            {
                using (var archive = new SevenZipArchive())
                {
                    archive.CreateEntry("entry1.bin", new MemoryStream(new byte[] {0x00, 0xFF}), new SevenZipEntrySettings(new SevenZipLZMA2CompressionSettings(), new SevenZipAESEncryptionSettings("test1")), new FileInfo("data1.bin")); 
                    archive.Save(sevenZipFile);
                }
            }
            </code>
      </example>
    </member>
    <member name="M:Aspose.Zip.SevenZip.SevenZipArchive.CreateEntry(System.String,System.IO.Stream,Aspose.Zip.Saving.SevenZipEntrySettings)">
      <summary>
            Create single entry within the archive.
            </summary>
      <param name="name">The name of the entry.</param>
      <param name="source">The input stream for the entry.</param>
      <param name="newEntrySettings">Compression and encryption settings used for added <see cref="T:Aspose.Zip.SevenZip.SevenZipArchiveEntry" /> item.</param>
      <returns>Zip entry instance.</returns>
      <example>
        <para>Compose 7z archive with LZMA2 compression and encryption of all entries.</para>
        <code>
            using (var archive = new SevenZipArchive(new SevenZipEntrySettings(new SevenZipLZMA2CompressionSettings(), new SevenZipAESEncryptionSettings("p@s$"))))
            {
                archive.CreateEntry("data.bin", new MemoryStream(new byte[] {0x00, 0xFF} ));
                archive.Save("archive.7z");
            }
            </code>
      </example>
    </member>
    <member name="M:Aspose.Zip.SevenZip.SevenZipArchive.CreateEntry(System.String,System.String,System.Boolean,Aspose.Zip.Saving.SevenZipEntrySettings)">
      <summary>
            Create single entry within the archive.
            </summary>
      <param name="name">The name of the entry.</param>
      <param name="path">The fully qualified name of the new file, or the relative file name to be compressed.</param>
      <param name="openImmediately">True if open the file immediately, otherwise open the file on archive saving.</param>
      <param name="newEntrySettings">Compression and encryption settings used for added <see cref="T:Aspose.Zip.SevenZip.SevenZipArchiveEntry" /> item.</param>
      <returns>Zip entry instance.</returns>
      <remarks>
        <para>The entry name is solely set within <paramref name="name" /> parameter. The file name provided in <paramref name="path" /> parameter does not affect the entry name.</para>
        <para>If the file is opened immediately with <paramref name="openImmediately" /> parameter it becomes blocked until archive is saved.</para>
      </remarks>
             /// <exception cref="T:System.ArgumentNullException"><paramref name="path" /> is null.</exception><exception cref="T:System.Security.SecurityException">The caller does not have the required permission to access </exception><exception cref="T:System.ArgumentException">The <paramref name="path" /> is empty, contains only white spaces, or contains invalid characters.</exception><exception cref="T:System.UnauthorizedAccessException">Access to file <paramref name="path" /> is denied.</exception><exception cref="T:System.IO.PathTooLongException"> The specified <paramref name="path" />, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters.</exception><exception cref="T:System.NotSupportedException">File at <paramref name="path" /> contains a colon (:) in the middle of the string.</exception><example><code>
            using (FileStream sevenZipFile = File.Open("archive.7z", FileMode.Create))
            {
                using (var archive = new SevenZipArchive(new SevenZipEntrySettings(new SevenZipLZMA2CompressionSettings())))
                {
                    archive.CreateEntry("data.bin", "file.dat");
                    archive.Save(sevenZipFile);
                }
            }
            </code></example></member>
    <member name="M:Aspose.Zip.SevenZip.SevenZipArchive.CreateEntries(System.IO.DirectoryInfo,System.Boolean)">
      <summary>
            Adds to the archive all files and directories recursively in the directory given.
            </summary>
      <param name="directory">Directory to compress.</param>
      <param name="includeRootDirectory">Indicates whether to include the root directory itself or not.</param>
      <returns>The archive with entries composed.</returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">The path to <paramref name="directory" /> is invalid, such as being on an unmapped drive.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission to access <paramref name="directory" />.</exception>
      <example>
        <code>
            using (SevenZipArchive archive = new SevenZipArchive())
            {
                DirectoryInfo folder = new DirectoryInfo("C:\folder");
                archive.CreateEntries(folder);
                archive.Save("folder.7z");
            }
            </code>
      </example>
    </member>
    <member name="M:Aspose.Zip.SevenZip.SevenZipArchive.CreateEntries(System.String,System.Boolean)">
      <summary>
            Adds to the archive all files and directories recursively in the directory given.
            </summary>
      <param name="sourceDirectory">Directory to compress.</param>
      <param name="includeRootDirectory">Indicates whether to include the root directory itself or not.</param>
      <returns>The archive with entries composed.</returns>
      <example>
        <para>Compose 7z archive with LZMA2 compression.</para>
        <code>
            using (SevenZipArchive archive = new SevenZipArchive(new SevenZipEntrySettings(new SevenZipLZMACompressionSettings())))
            {
                archive.CreateEntries("C:\folder");
                archive.Save("folder.7z");
            }
            </code>
      </example>
    </member>
    <member name="M:Aspose.Zip.SevenZip.SevenZipArchive.Dispose">
      <inheritdoc />
      <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            </summary>
    </member>
    <member name="M:Aspose.Zip.SevenZip.SevenZipArchive.Save(System.IO.Stream)">
      <summary>
            Saves 7z archive to the stream provided.
            </summary>
      <param name="outputStream">Destination stream.</param>
      <remarks>
        <para>
          <paramref name="outputStream" /> must be seekable.</para>
      </remarks>
      <exception cref="T:System.ArgumentException">
        <paramref name="outputStream" /> does not support seeking.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="outputStream" /> is null.</exception>
      <example>
        <code>
            using (FileStream sevenZipFile = File.Open("archive.7z", FileMode.Create))
            {
              using (FileStream source = File.Open("data.bin", FileMode.Open, FileAccess.Read))
              {
                using (var archive = new SevenZipArchive())
                {
                  archive.CreateEntry("data", source);
                  archive.Save(sevenZipFile);
                }
              }
            }
             </code>
      </example>
      <exception cref="T:System.ArgumentException">Stream is not seekable.</exception>
      <exception cref="T:System.InvalidOperationException">Encoder failed to compress data.</exception>
    </member>
    <member name="M:Aspose.Zip.SevenZip.SevenZipArchive.Save(System.String)">
      <summary>
            Saves archive to destination file provided.
            </summary>
      <param name="destinationFileName">The path of the archive to be created. If the specified file name points to an existing file, it will be overwritten.</param>
      <remarks>
        <para>It is possible to save an archive to the same path as it was loaded from.
            However, this is not recommended because this approach uses copying to temporary file.</para>
      </remarks>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destinationFileName" /> is null.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission to access </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="destinationFileName" /> is empty, contains only white spaces, or contains invalid characters.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access to file <paramref name="destinationFileName" /> is denied.</exception>
      <exception cref="T:System.IO.PathTooLongException"> The specified <paramref name="destinationFileName" />, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters.</exception>
      <exception cref="T:System.NotSupportedException">File at <paramref name="destinationFileName" /> contains a colon (:) in the middle of the string.</exception>
      <example>
        <code>
             using (FileStream source = File.Open("data.bin", FileMode.Open, FileAccess.Read))
             {
                using (var archive = new SevenZipArchive(new SevenZipEntrySettings(new SevenZipLZMACompressionSettings())))
                {
                   archive.CreateEntry("data", source);
                   archive.Save("archive.7z");
                }
             }
             </code>
      </example>
    </member>
    <member name="M:Aspose.Zip.SevenZip.SevenZipArchive.Dispose(System.Boolean)">
      <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            </summary>
      <param name="disposing">Whether managed resources should be disposed.</param>
    </member>
    <member name="T:Aspose.Zip.SevenZip.SevenZipArchiveEntry">
      <summary>
            Represents single file within 7z archive.
            </summary>
      <remarks>
            Cast an <see cref="T:Aspose.Zip.SevenZip.SevenZipArchiveEntry" /> instance to <see cref="T:Aspose.Zip.SevenZip.SevenZipArchiveEntryEncrypted" /> to determine whether the entry encrypted or not.
            </remarks>
    </member>
    <member name="P:Aspose.Zip.SevenZip.SevenZipArchiveEntry.Name">
      <summary>
            Gets name of the entry within archive.
            </summary>
    </member>
    <member name="P:Aspose.Zip.SevenZip.SevenZipArchiveEntry.UncompressedSize">
      <summary>
            Gets size of original file.
            </summary>
    </member>
    <member name="P:Aspose.Zip.SevenZip.SevenZipArchiveEntry.CompressedSize">
      <summary>
            Gets size of compressed file.
            </summary>
    </member>
    <member name="P:Aspose.Zip.SevenZip.SevenZipArchiveEntry.IsDirectory">
      <summary>
            Gets a value indicating whether the entry represents directory.
            </summary>
    </member>
    <member name="P:Aspose.Zip.SevenZip.SevenZipArchiveEntry.CompressionSettings">
      <summary>
            Gets settings for compression or decompression.
            </summary>
    </member>
    <member name="M:Aspose.Zip.SevenZip.SevenZipArchiveEntry.#ctor(Aspose.Zip.SevenZip.SevenZipArchive,System.String,Aspose.Zip.Saving.SevenZipCompressionSettings,System.IO.Stream,System.IO.FileAttributes,System.IO.FileSystemInfo)">
      <summary>
            Initializes a new instance of the <see cref="T:Aspose.Zip.SevenZip.SevenZipArchiveEntry" /> class.
            </summary>
      <param name="parent">
      </param>
      <param name="name">Entry name.</param>
      <param name="compressionSettings">Settings for compression or decompression.</param>
      <param name="source">Stream with entry data either to be compressed or to be decompressed.</param>
      <param name="fileAttributes">Attributes from file system.</param>
      <param name="fileInfo">File or directory info the entry based on.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="name" /> is null or empty.</exception>
    </member>
    <member name="P:Aspose.Zip.SevenZip.SevenZipArchiveEntry.Source">
      <summary>
            Gets the data source stream for the entry.
            </summary>
    </member>
    <member name="P:Aspose.Zip.SevenZip.SevenZipArchiveEntry.FileAttributes">
      <summary>
            Gets file attributes from host system.
            </summary>
    </member>
    <member name="M:Aspose.Zip.SevenZip.SevenZipArchiveEntry.GetDestinationStream(System.IO.Stream)">
      <summary>
            Destination stream for the entry, may be decorated.
            </summary>
      <param name="outputStream">Output stream for the entry.</param>
      <returns>The destination stream for entry compression.</returns>
    </member>
    <member name="M:Aspose.Zip.SevenZip.SevenZipArchiveEntry.FinalizeCompressedData(System.IO.Stream,System.Byte[])">
      <summary>
            Write to output stream any headers that follow compressed data.
            </summary>
      <param name="outputStream">Output stream for the entry.</param>
      <param name="encoderProperties">Properties of compressor.</param>
      <returns>Number of "technical" bytes that were added after entry significant data block.</returns>
    </member>
    <member name="T:Aspose.Zip.SevenZip.SevenZipArchiveEntryEncrypted">
      <inheritdoc />
      <summary>
            SevanZip entry that needs to be compressed with encryption or decompressed with decryption.
            </summary>
    </member>
    <member name="M:Aspose.Zip.SevenZip.SevenZipArchiveEntryEncrypted.FinalizeCompressedData(System.IO.Stream,System.Byte[])">
      <inheritdoc />
      <summary>
            Write to output stream any headers that follow compressed data.
            </summary>
      <param name="outputStream">Output stream for the entry.</param>
      <param name="encoderProperties">Properties of compressor.</param>
      <returns>Number of "technical" bytes that were added after entry significant data block.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The key is corrupt which can cause invalid padding to the stream.</exception>
      <exception cref="T:System.NotSupportedException">The final block has already been transformed.</exception>
    </member>
    <member name="M:Aspose.Zip.SevenZip.SevenZipArchiveEntryEncrypted.GetDestinationStream(System.IO.Stream)">
      <inheritdoc />
      <summary>
            Destination stream for the entry, decorated with crypto stream.
            </summary>
      <param name="outputStream">Output stream for the entry.</param>
      <returns>Encryption stream.</returns>
    </member>
    <member name="T:Aspose.Zip.SevenZip.SevenZipArchiveEntryPlain">
      <inheritdoc />
      <summary>
            SevenZip entry that needs to be compressed without encryption or decompressed without decryption.
            </summary>
    </member>
    <member name="M:Aspose.Zip.SevenZip.SevenZipArchiveEntryPlain.FinalizeCompressedData(System.IO.Stream,System.Byte[])">
      <summary>
            Write to output stream any headers that follow compressed data.
            </summary>
      <param name="outputStream">Output stream for the entry.</param>
      <param name="encoderProperties">Properties of compressor.</param>
      <returns>Number of "technical" bytes that were added after entry significant data block.</returns>
    </member>
    <member name="M:Aspose.Zip.SevenZip.SevenZipArchiveEntryPlain.GetDestinationStream(System.IO.Stream)">
      <inheritdoc />
      <summary>
            Destination stream for the entry, may be decorated.
            </summary>
      <param name="outputStream">Output stream for the entry.</param>
      <returns>The destination stream for entry compression.</returns>
    </member>
    <member name="T:Aspose.Zip.UnRAR.NamespaceDoc">
      <summary>
            The <see cref="N:Aspose.Zip.UnRAR" /> namespace contains classes which represent RAR archive related entities. Classes of this namespace is obsolete.
            </summary>
    </member>
    <member name="T:Aspose.Zip.UnRAR.RarArchive">
      <summary>
            This class represents RAR archive file. Use it to extract RAR archives.
            </summary>
    </member>
    <member name="P:Aspose.Zip.UnRAR.RarArchive.Entries">
      <summary>
            Gets entries of <see cref="T:Aspose.Zip.UnRAR.RarArchiveEntry" /> type constituting the rar archive.
            </summary>
    </member>
    <member name="M:Aspose.Zip.UnRAR.RarArchive.#ctor(System.String)">
      <summary>
            Initializes a new instance of the <see cref="T:Aspose.Zip.UnRAR.RarArchive" /> class and composes entries list can be extracted from the archive.
            </summary>
      <remarks>
            This constructor does not decompress any entry. See <see cref="M:Aspose.Zip.UnRAR.RarArchiveEntry.Open(System.String)" /> method for decompressing.
            </remarks>
      <param name="path">The fully qualified or the relative path to the archive file.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission to access </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="path" /> is empty, contains only white spaces, or contains invalid characters.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access to file <paramref name="path" /> is denied.</exception>
      <exception cref="T:System.IO.PathTooLongException"> The specified <paramref name="path" />, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters.</exception>
      <exception cref="T:System.NotSupportedException">File at <paramref name="path" /> contains a colon (:) in the middle of the string.</exception>
      <example>
        <para>The following example extract an archive, then decompress first entry to a <c>MemoryStream</c>.</para>
        <code>        
            var extracted = new MemoryStream();
            using (RarArchive archive = new RarArchive("data.rar"))
            {
                using (var decompressed = archive.Entries[0].Open())
                {
                    byte[] b = new byte[8192];
                    int bytesRead;
                    while (0 &lt; (bytesRead = decompressed.Read(b, 0, b.Length)))
                        extracted.Write(b, 0, bytesRead);
                }
            }
            </code>
      </example>
    </member>
    <member name="M:Aspose.Zip.UnRAR.RarArchive.#ctor(System.IO.Stream)">
      <summary>
            Initializes a new instance of the <see cref="T:Aspose.Zip.UnRAR.RarArchive" /> class and composes entries list can be extracted from the archive.
            </summary>
      <remarks>
            This constructor does not decompress any entry. See <see cref="M:Aspose.Zip.UnRAR.RarArchiveEntry.Open(System.String)" /> method for decompressing.
            </remarks>
      <param name="sourceStream">The source of the archive.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceStream" /> is not seekable.</exception>
      <exception cref="T:System.IO.InvalidDataException">Wrong signature for archive. - or - The file is not a RAR archive.</exception>
      <exception cref="T:System.InvalidOperationException">
      </exception>
      <example>
        <para>The following example decipher and decompress first entry to a <c>MemoryStream</c>.</para>
        <code>
            var fs = File.OpenRead("encrypted.rar");
            var extracted = new MemoryStream();
            using (RarArchive archive = new RarArchive(fs))
            {
                using (var decompressed = archive.Entries[0].Open("secret"))
                {
                    byte[] b = new byte[8192];
                    int bytesRead;
                    while (0 &lt; (bytesRead = decompressed.Read(b, 0, b.Length)))
                        extracted.Write(b, 0, bytesRead);
                }
            }
            </code>
      </example>
    </member>
    <member name="M:Aspose.Zip.UnRAR.RarArchive.ExtractToDirectory(System.String,System.String)">
      <summary>
            Extracts all the files in the archive to the directory provided.
            </summary>
      <param name="destinationDirectory">The path to the directory to place the extracted files in.</param>
      <param name="password">Optional password for decryption.</param>
      <remarks>If the directory does not exist, it will be created.</remarks>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destinationDirectory" /> is null.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters and file names must be less than 260 characters.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission to access existing directory.</exception>
      <exception cref="T:System.NotSupportedException">If directory does not exist, path contains a colon character (:) that is not part of a drive label ("C:\").</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="destinationDirectory" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters by using the System.IO.Path.GetInvalidPathChars method. -or- path is prefixed with, or contains, only a colon character (:).</exception>
      <exception cref="T:System.IO.IOException">The directory specified by path is a file. -or- The network name is not known.</exception>
      <example>
        <code>
            using (var archive = new RarArchive("archive.rar")) 
            { 
               archive.ExtractToDirectory("C:\extracted");
            }
            </code>
      </example>
    </member>
    <member name="M:Aspose.Zip.UnRAR.RarArchive.Dispose(System.Boolean)">
      <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            </summary>
      <param name="disposing">Whether managed resources should be disposed.</param>
    </member>
    <member name="M:Aspose.Zip.UnRAR.RarArchive.Dispose">
      <inheritdoc />
      <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            </summary>
    </member>
    <member name="T:Aspose.Zip.UnRAR.RarArchiveEntry">
      <summary>
            Represents single file within archive.
            </summary>
      <remarks>
            Cast a <see cref="T:Aspose.Zip.UnRAR.RarArchiveEntry" /> instance to <see cref="T:Aspose.Zip.UnRAR.RarArchiveEntryEncrypted" /> to determine whether the entry encrypted or not.
            </remarks>
    </member>
    <member name="P:Aspose.Zip.UnRAR.RarArchiveEntry.Name">
      <summary>
            Gets name of the entry within archive.
            </summary>
    </member>
    <member name="P:Aspose.Zip.UnRAR.RarArchiveEntry.CompressedSize">
      <summary>
            Gets size of compressed file.
            </summary>
    </member>
    <member name="P:Aspose.Zip.UnRAR.RarArchiveEntry.UncompressedSize">
      <summary>
            Gets size of original file.
            </summary>
    </member>
    <member name="P:Aspose.Zip.UnRAR.RarArchiveEntry.ModificationTime">
      <summary>
            Gets last modified date and time.
            </summary>
    </member>
    <member name="P:Aspose.Zip.UnRAR.RarArchiveEntry.CreationTime">
      <summary>
            Gets creation date and time.
            </summary>
    </member>
    <member name="P:Aspose.Zip.UnRAR.RarArchiveEntry.LastAccessTime">
      <summary>
            Gets last access date and time.
            </summary>
    </member>
    <member name="P:Aspose.Zip.UnRAR.RarArchiveEntry.IsDirectory">
      <summary>
            Gets a value indicating whether the entry represents directory.
            </summary>
    </member>
    <member name="P:Aspose.Zip.UnRAR.RarArchiveEntry.Source">
      <summary>
            Gets the data source stream for the entry.
            </summary>
    </member>
    <member name="M:Aspose.Zip.UnRAR.RarArchiveEntry.Open(System.String)">
      <summary>
            Opens the entry for extraction and provides a stream with decompressed entry content.
            </summary>
      <param name="password">Optional password for decryption.</param>
      <returns>The stream that represents the contents of the entry.</returns>
      <remarks>
        <para>Read from the stream to get original content of file. See examples section.</para>
      </remarks>
      <example>
            Usage:
            <code>Stream decompressed = entry.Open();</code><para>
            .NET 4.0 and higher - use Stream.CopyTo method:
            <code>
            decompressed.CopyTo(httpResponse.OutputStream)
            </code></para><para>
            .NET 3.5 and before - copy bytes manually:
            <code>
            byte[] buffer = new byte[8192];
            int bytesRead;
            while (0 &lt; (bytesRead = decompressed.Read(buffer, 0, buffer.Length)))
             fileStream.Write(buffer, 0, bytesRead);
            </code></para></example>
    </member>
    <member name="M:Aspose.Zip.UnRAR.RarArchiveEntry.Extract(System.String,System.String)">
      <summary>
            Extracts the entry to the filesystem by the path provided.
            </summary>
      <param name="path">The path to destitation file. If the file already exists, it will be overwritten.</param>
      <param name="password">Optional password for decryption.</param>
      <returns>The file info of composed file.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission to access </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="path" /> is empty, contains only white spaces, or contains invalid characters.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access to file <paramref name="path" /> is denied.</exception>
      <exception cref="T:System.IO.PathTooLongException"> The specified <paramref name="path" />, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters.</exception>
      <exception cref="T:System.NotSupportedException">File at <paramref name="path" /> contains a colon (:) in the middle of the string.</exception>
      <exception cref="T:System.IO.InvalidDataException">CRC or MAC verification failed for the entry.</exception>
      <example>
        <para>Extract two entries of rar archive.</para>
        <code>
            using (FileStream rarFile = File.Open("archive.rar", FileMode.Open))
            {
                using (RarArchive archive = new RarArchive(rarFile))
                {
                    archive.Entries[0].Extract("first.bin", "pass");
                    archive.Entries[1].Extract("second.bin", "pass");
                }
            }
            </code>
      </example>
    </member>
    <member name="T:Aspose.Zip.UnRAR.RarArchiveEntryEncrypted">
      <inheritdoc />
      <summary>
            Zip entry that needs to be decompressed with decryption.
            </summary>
    </member>
    <member name="T:Aspose.Zip.UnRAR.RarArchiveEntryPlain">
      <inheritdoc />
      <summary>
            Rar entry that needs to be decompressed without decryption.
            </summary>
    </member>
    <member name="T:Aspose.Zip.Rar.NamespaceDoc">
      <summary>
            The <see cref="N:Aspose.Zip.Rar" /> namespace contains classes which represent RAR archive related entities.
            </summary>
    </member>
    <member name="T:Aspose.Zip.Rar.RarArchive">
      <summary>
            This class represents RAR archive file. Use it to extract RAR archives.
            </summary>
    </member>
    <member name="P:Aspose.Zip.Rar.RarArchive.Entries">
      <summary>
            Gets entries of <see cref="T:Aspose.Zip.Rar.RarArchiveEntry" /> type constituting the rar archive.
            </summary>
    </member>
    <member name="M:Aspose.Zip.Rar.RarArchive.#ctor(System.String)">
      <summary>
            Initializes a new instance of the <see cref="T:Aspose.Zip.Rar.RarArchive" /> class and composes entries list can be extracted from the archive.
            </summary>
      <remarks>
            This constructor does not decompress any entry. See <see cref="M:Aspose.Zip.Rar.RarArchiveEntry.Open(System.String)" /> method for decompressing.
            </remarks>
      <param name="path">The fully qualified or the relative path to the archive file.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission to access </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="path" /> is empty, contains only white spaces, or contains invalid characters.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access to file <paramref name="path" /> is denied.</exception>
      <exception cref="T:System.IO.PathTooLongException"> The specified <paramref name="path" />, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters.</exception>
      <exception cref="T:System.NotSupportedException">File at <paramref name="path" /> contains a colon (:) in the middle of the string.</exception>
      <example>
        <para>The following example extract an archive, then decompress first entry to a <c>MemoryStream</c>.</para>
        <code>        
            var extracted = new MemoryStream();
            using (RarArchive archive = new RarArchive("data.rar"))
            {
                using (var decompressed = archive.Entries[0].Open())
                {
                    byte[] b = new byte[8192];
                    int bytesRead;
                    while (0 &lt; (bytesRead = decompressed.Read(b, 0, b.Length)))
                        extracted.Write(b, 0, bytesRead);
                }
            }
            </code>
      </example>
    </member>
    <member name="M:Aspose.Zip.Rar.RarArchive.#ctor(System.IO.Stream)">
      <summary>
            Initializes a new instance of the <see cref="T:Aspose.Zip.Rar.RarArchive" /> class and composes entries list can be extracted from the archive.
            </summary>
      <remarks>
            This constructor does not decompress any entry. See <see cref="M:Aspose.Zip.Rar.RarArchiveEntry.Open(System.String)" /> method for decompressing.
            </remarks>
      <param name="sourceStream">The source of the archive.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceStream" /> is not seekable.</exception>
      <exception cref="T:System.IO.InvalidDataException">Wrong signature for archive. - or - The file is not a RAR archive.</exception>
      <exception cref="T:System.InvalidOperationException">
      </exception>
      <example>
        <para>The following example decipher and decompress first entry to a <c>MemoryStream</c>.</para>
        <code>
            var fs = File.OpenRead("encrypted.rar");
            var extracted = new MemoryStream();
            using (RarArchive archive = new RarArchive(fs))
            {
                using (var decompressed = archive.Entries[0].Open("secret"))
                {
                    byte[] b = new byte[8192];
                    int bytesRead;
                    while (0 &lt; (bytesRead = decompressed.Read(b, 0, b.Length)))
                        extracted.Write(b, 0, bytesRead);
                }
            }
            </code>
      </example>
    </member>
    <member name="M:Aspose.Zip.Rar.RarArchive.ExtractToDirectory(System.String,System.String)">
      <summary>
            Extracts all the files in the archive to the directory provided.
            </summary>
      <param name="destinationDirectory">The path to the directory to place the extracted files in.</param>
      <param name="password">Optional password for decryption.</param>
      <remarks>If the directory does not exist, it will be created.</remarks>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destinationDirectory" /> is null.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters and file names must be less than 260 characters.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission to access existing directory.</exception>
      <exception cref="T:System.NotSupportedException">If directory does not exist, path contains a colon character (:) that is not part of a drive label ("C:\").</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="destinationDirectory" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters by using the System.IO.Path.GetInvalidPathChars method. -or- path is prefixed with, or contains, only a colon character (:).</exception>
      <exception cref="T:System.IO.IOException">The directory specified by path is a file. -or- The network name is not known.</exception>
      <example>
        <code>
            using (var archive = new RarArchive("archive.rar")) 
            { 
               archive.ExtractToDirectory("C:\extracted");
            }
            </code>
      </example>
    </member>
    <member name="M:Aspose.Zip.Rar.RarArchive.Dispose(System.Boolean)">
      <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            </summary>
      <param name="disposing">Whether managed resources should be disposed.</param>
    </member>
    <member name="M:Aspose.Zip.Rar.RarArchive.Dispose">
      <inheritdoc />
      <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            </summary>
    </member>
    <member name="T:Aspose.Zip.Rar.RarArchiveEntry">
      <summary>
            Represents single file within archive.
            </summary>
      <remarks>
            Cast a <see cref="T:Aspose.Zip.Rar.RarArchiveEntry" /> instance to <see cref="T:Aspose.Zip.Rar.RarArchiveEntryEncrypted" /> to determine whether the entry encrypted or not.
            </remarks>
    </member>
    <member name="P:Aspose.Zip.Rar.RarArchiveEntry.Name">
      <summary>
            Gets name of the entry within archive.
            </summary>
    </member>
    <member name="P:Aspose.Zip.Rar.RarArchiveEntry.CompressedSize">
      <summary>
            Gets size of compressed file.
            </summary>
    </member>
    <member name="P:Aspose.Zip.Rar.RarArchiveEntry.UncompressedSize">
      <summary>
            Gets size of original file.
            </summary>
    </member>
    <member name="P:Aspose.Zip.Rar.RarArchiveEntry.ModificationTime">
      <summary>
            Gets last modified date and time.
            </summary>
    </member>
    <member name="P:Aspose.Zip.Rar.RarArchiveEntry.CreationTime">
      <summary>
            Gets creation date and time.
            </summary>
    </member>
    <member name="P:Aspose.Zip.Rar.RarArchiveEntry.LastAccessTime">
      <summary>
            Gets last access date and time.
            </summary>
    </member>
    <member name="P:Aspose.Zip.Rar.RarArchiveEntry.IsDirectory">
      <summary>
            Gets a value indicating whether the entry represents directory.
            </summary>
    </member>
    <member name="P:Aspose.Zip.Rar.RarArchiveEntry.Source">
      <summary>
            Gets the data source stream for the entry.
            </summary>
    </member>
    <member name="M:Aspose.Zip.Rar.RarArchiveEntry.Open(System.String)">
      <summary>
            Opens the entry for extraction and provides a stream with decompressed entry content.
            </summary>
      <param name="password">Optional password for decryption.</param>
      <returns>The stream that represents the contents of the entry.</returns>
      <remarks>
        <para>Read from the stream to get original content of file. See examples section.</para>
      </remarks>
      <example>
            Usage:
            <code>Stream decompressed = entry.Open();</code><para>
            .NET 4.0 and higher - use Stream.CopyTo method:
            <code>
            decompressed.CopyTo(httpResponse.OutputStream)
            </code></para><para>
            .NET 3.5 and before - copy bytes manually:
            <code>
            byte[] buffer = new byte[8192];
            int bytesRead;
            while (0 &lt; (bytesRead = decompressed.Read(buffer, 0, buffer.Length)))
             fileStream.Write(buffer, 0, bytesRead);
            </code></para></example>
    </member>
    <member name="M:Aspose.Zip.Rar.RarArchiveEntry.Extract(System.String,System.String)">
      <summary>
            Extracts the entry to the filesystem by the path provided.
            </summary>
      <param name="path">The path to destitation file. If the file already exists, it will be overwritten.</param>
      <param name="password">Optional password for decryption.</param>
      <returns>The file info of composed file.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission to access </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="path" /> is empty, contains only white spaces, or contains invalid characters.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access to file <paramref name="path" /> is denied.</exception>
      <exception cref="T:System.IO.PathTooLongException"> The specified <paramref name="path" />, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters.</exception>
      <exception cref="T:System.NotSupportedException">File at <paramref name="path" /> contains a colon (:) in the middle of the string.</exception>
      <exception cref="T:System.IO.InvalidDataException">CRC or MAC verification failed for the entry.</exception>
      <example>
        <para>Extract two entries of rar archive.</para>
        <code>
            using (FileStream rarFile = File.Open("archive.rar", FileMode.Open))
            {
                using (RarArchive archive = new RarArchive(rarFile))
                {
                    archive.Entries[0].Extract("first.bin", "pass");
                    archive.Entries[1].Extract("second.bin", "pass");
                }
            }
            </code>
      </example>
    </member>
    <member name="T:Aspose.Zip.Rar.RarArchiveEntryEncrypted">
      <inheritdoc />
      <summary>
            Zip entry that needs to be decompressed with decryption.
            </summary>
    </member>
    <member name="T:Aspose.Zip.Rar.RarArchiveEntryPlain">
      <inheritdoc />
      <summary>
            Rar entry that needs to be decompressed without decryption.
            </summary>
    </member>
    <member name="T:Aspose.Zip.License">
      <summary>
             Provides methods to license the component.
             </summary>
      <example>
             In this example, an attempt will be made to find a license file named MyLicense.lic
             in the folder that contains 
             <ms>
             the component, in the folder that contains the calling assembly,
             in the folder of the entry assembly and then in the embedded resources of the calling assembly.
             <code>
             [C#]
             
             License license = new License();
             license.SetLicense("MyLicense.lic");
            
            
             [Visual Basic]
            
             Dim license As license = New license
             License.SetLicense("MyLicense.lic")
             </code></ms><java>
             the component jar file:
             <code>
             License license = new License();
             license.setLicense("MyLicense.lic");
             </code></java></example>
    </member>
    <member name="M:Aspose.Zip.License.#ctor">
      <summary>
            Initializes a new instance of the <see cref="T:Aspose.Zip.License" /> class. 
            </summary>
      <example>
            In this example, an attempt will be made to find a license file named MyLicense.lic
            in the folder that contains 
            <ms>
            the component, in the folder that contains the calling assembly,
            in the folder of the entry assembly and then in the embedded resources of the calling assembly.
            <code>
            [C#]
            
            License license = new License();
            license.SetLicense("MyLicense.lic");
            
            
            [Visual Basic]
            
            Dim license As license = New license
            License.SetLicense("MyLicense.lic")
            </code></ms><java>
            the component jar file:
            <code>
            License license = new License();
            license.setLicense("MyLicense.lic");
            </code></java></example>
    </member>
    <member name="M:Aspose.Zip.License.SetLicense(System.String)">
      <summary>
            Licenses the component.
            </summary>
      <param name="licenseName">Can be a full or short file name or name of an embedded resource.
            Use an empty string to switch to evaluation mode.</param>
      <remarks>
        <p>Tries to find the license in the following locations:</p>
        <p>1. Explicit path.</p>
        <ms>
          <p>2. The folder that contains the Aspose component assembly.</p>
          <p>3. The folder that contains the client's calling assembly.</p>
          <p>4. The folder that contains the entry (startup) assembly.</p>
          <p>5. An embedded resource in the client's calling assembly.</p>
          <p>
            <b>Note:</b>On the .NET Compact Framework, tries to find the license only in these locations:</p>
          <p>1. Explicit path.</p>
          <p>2. An embedded resource in the client's calling assembly.</p>
        </ms>
        <java>
          <p>2. The folder that contains the Aspose component JAR file.</p>
          <p>3. The folder that contains the client's calling JAR file.</p>
        </java>
      </remarks>
      <example>
            In this example, an attempt will be made to find a license file named MyLicense.lic
            in the folder that contains 
            <ms>
            the component, in the folder that contains the calling assembly,
            in the folder of the entry assembly and then in the embedded resources of the calling assembly.
            <code>
            [C#]
            
            License license = new License();
            license.SetLicense("MyLicense.lic");
            </code></ms><java>
            the component jar file:
            <code>
            License license = new License();
            license.setLicense("MyLicense.lic");
            </code></java></example>
    </member>
    <member name="M:Aspose.Zip.License.SetLicense(System.IO.Stream)">
      <summary>
            Licenses the component.
            </summary>
      <param name="stream">A stream that contains the license.</param>
      <remarks>
        <p>Use this method to load a license from a stream.</p>
      </remarks>
      <example>
        <code>
          <ms>
            [C#]
            
            License license = new License();
            license.SetLicense(myStream);
            
            
            [Visual Basic]
            
            Dim license as License = new License
            license.SetLicense(myStream)
            </ms>
          <java>
            License license = new License();
            license.setLicense(myStream);
            </java>
        </code>
      </example>
      <javaName>void setLicense(java.io.InputStream stream)</javaName>
    </member>
    <member name="T:Aspose.Zip.Archive">
      <summary>
            This class represents zip archive file. Use it to compose, extract, or update zip archives.
            </summary>
    </member>
    <member name="M:Aspose.Zip.Archive.#ctor(Aspose.Zip.Saving.ArchiveEntrySettings)">
      <summary>
            Initializes a new instance of the <see cref="T:Aspose.Zip.Archive" /> class with optional settings for its entries.
            </summary>
      <param name="newEntrySettings">Compression and encryption settings used for newly added <see cref="T:Aspose.Zip.ArchiveEntry" /> items.</param>
      <example>
        <para>
            The following example shows how to compress a single file with default settings.
            </para>
        <code>
            using (FileStream zipFile = File.Open("archive.zip", FileMode.Create))
            {
                using (var archive = new Archive())
                {
                    archive.CreateEntry("data.bin", "file.dat");
                    archive.Save(zipFile);
                }
            }
            </code>
      </example>
    </member>
    <member name="M:Aspose.Zip.Archive.#ctor(System.IO.Stream,Aspose.Zip.ArchiveLoadOptions,Aspose.Zip.Saving.ArchiveEntrySettings)">
      <summary>
            Initializes a new instance of the <see cref="T:Aspose.Zip.Archive" /> class and composes entries list can be extracted from the archive.
            </summary>
      <remarks>
            This constructor does not decompress any entry. See <see cref="M:Aspose.Zip.ArchiveEntry.Open(System.String)" /> method for decompressing.
            </remarks>
      <param name="sourceStream">The source of the archive.</param>
      <param name="loadOptions">Options to load existing archive with.</param>
      <param name="newEntrySettings">Compression and encryption settings used for newly added <see cref="T:Aspose.Zip.ArchiveEntry" /> items.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceStream" /> is not seekable.</exception>
      <exception cref="T:System.IO.InvalidDataException">Encryption header for AES contradicts WinZip compression method.</exception>
      <example>
        <para>The following example extract an encrypted archive, then decompress first entry to a <c>MemoryStream</c>.</para>
        <code>
            var fs = File.OpenRead("encrypted.zip");
            var extracted = new MemoryStream();
            using (Archive archive = new Archive(fs, new ArchiveLoadOptions() { DecryptionPassword = "p@s$" }))
            {
                using (var decompressed = archive.Entries[0].Open())
                {
                    byte[] b = new byte[8192];
                    int bytesRead;
                    while (0 &lt; (bytesRead = decompressed.Read(b, 0, b.Length)))
                        extracted.Write(b, 0, bytesRead);
                }
            }
            </code>
      </example>
    </member>
    <member name="M:Aspose.Zip.Archive.#ctor(System.String,Aspose.Zip.ArchiveLoadOptions,Aspose.Zip.Saving.ArchiveEntrySettings)">
      <summary>
            Initializes a new instance of the <see cref="T:Aspose.Zip.Archive" /> class and composes entries list can be extracted from the archive.
            </summary>
      <remarks>
            This constructor does not decompress any entry. See <see cref="M:Aspose.Zip.ArchiveEntry.Open(System.String)" /> method for decompressing.
            </remarks>
      <param name="path">The fully qualified or the relative path to the archive file.</param>
      <param name="loadOptions">Options to load existing archive with.</param>
      <param name="newEntrySettings">Compression and encryption settings used for newly added <see cref="T:Aspose.Zip.ArchiveEntry" /> items.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission to access </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="path" /> is empty, contains only white spaces, or contains invalid characters.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access to file <paramref name="path" /> is denied.</exception>
      <exception cref="T:System.IO.PathTooLongException"> The specified <paramref name="path" />, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters.</exception>
      <exception cref="T:System.NotSupportedException">File at <paramref name="path" /> contains a colon (:) in the middle of the string.</exception>
      <example>
        <para>The following example extract an encrypted archive, then decompress first entry to a <c>MemoryStream</c>.</para>
        <code>        
            var extracted = new MemoryStream();
            using (Archive archive = new Archive("encrypted.zip", new ArchiveLoadOptions() { DecryptionPassword = "p@s$" }))
            {
                using (var decompressed = archive.Entries[0].Open())
                {
                    byte[] b = new byte[8192];
                    int bytesRead;
                    while (0 &lt; (bytesRead = decompressed.Read(b, 0, b.Length)))
                        extracted.Write(b, 0, bytesRead);
                }
            }
            </code>
      </example>
    </member>
    <member name="P:Aspose.Zip.Archive.NewEntrySettings">
      <summary>
            Compression and encryption settings used for newly added <see cref="T:Aspose.Zip.ArchiveEntry" /> items.
            </summary>
    </member>
    <member name="P:Aspose.Zip.Archive.Entries">
      <summary>
            Gets entries of <see cref="T:Aspose.Zip.ArchiveEntry" /> type constituting the archive.
            </summary>
    </member>
    <member name="M:Aspose.Zip.Archive.CreateEntry(System.String,System.String,System.Boolean,Aspose.Zip.Saving.ArchiveEntrySettings)">
      <summary>
            Create single entry within the archive.
            </summary>
      <param name="name">The name of the entry.</param>
      <param name="path">The fully qualified name of the new file, or the relative file name to be compressed.</param>
      <param name="openImmediately">True if open the file immediately, otherwise open the file on archive saving.</param>
      <param name="newEntrySettings">Compression and encryption settings used for added <see cref="T:Aspose.Zip.ArchiveEntry" /> item.</param>
      <returns>Zip entry instance.</returns>
      <remarks>
        <para>The entry name is solely set within <paramref name="name" /> parameter. The file name provided in <paramref name="path" /> parameter does not affect the entry name.</para>
        <para>If the file is opened immediately with <paramref name="openImmediately" /> parameter it becomes blocked until archive is saved.</para>
      </remarks>
             /// <exception cref="T:System.ArgumentNullException"><paramref name="path" /> is null.</exception><exception cref="T:System.Security.SecurityException">The caller does not have the required permission to access </exception><exception cref="T:System.ArgumentException">The <paramref name="path" /> is empty, contains only white spaces, or contains invalid characters.</exception><exception cref="T:System.UnauthorizedAccessException">Access to file <paramref name="path" /> is denied.</exception><exception cref="T:System.IO.PathTooLongException"> The specified <paramref name="path" />, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters.</exception><exception cref="T:System.NotSupportedException">File at <paramref name="path" /> contains a colon (:) in the middle of the string.</exception><example><code>
            using (FileStream zipFile = File.Open("archive.zip", FileMode.Create))
            {
                using (var archive = new Archive())
                {
                    archive.CreateEntry("data.bin", "file.dat");
                    archive.Save(zipFile);
                }
            }
            </code></example></member>
    <member name="M:Aspose.Zip.Archive.CreateEntry(System.String,System.IO.Stream,Aspose.Zip.Saving.ArchiveEntrySettings)">
      <summary>
            Create single entry within the archive.
            </summary>
      <param name="name">The name of the entry.</param>
      <param name="source">The input stream for the entry.</param>
      <param name="newEntrySettings">Compression and encryption settings used for added <see cref="T:Aspose.Zip.ArchiveEntry" /> item.</param>
      <returns>Zip entry instance.</returns>
      <example>
        <code>
            using (var archive = new Archive(new ArchiveEntrySettings(null, new AesEcryptionSettings("p@s$", EncryptionMethod.AES256))))
            {
                archive.CreateEntry("data.bin", new MemoryStream(new byte[] {0x00, 0xFF} ));
                archive.Save("archive.zip");
            }
            </code>
      </example>
    </member>
    <member name="M:Aspose.Zip.Archive.CreateEntry(System.String,System.IO.FileInfo,System.Boolean,Aspose.Zip.Saving.ArchiveEntrySettings)">
      <summary>
            Create single entry within the archive.
            </summary>
      <param name="name">The name of the entry.</param>
      <param name="fileInfo">The metadata of file to be compressed.</param>
      <param name="openImmediately">True if open the file immediately, otherwise open the file on archive saving.</param>
      <param name="newEntrySettings">Compression and encryption settings used for added <see cref="T:Aspose.Zip.ArchiveEntry" /> item.</param>
      <returns>Zip entry instance.</returns>
      <remarks>
        <para>The entry name is solely set within <paramref name="name" /> parameter. The file name provided in <paramref name="fileInfo" /> parameter does not affect the entry name.</para>
        <para>If the file is opened immediately with <paramref name="openImmediately" /> parameter it becomes blocked until archive is saved.</para>
      </remarks>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="fileInfo" /> is read-only or is a directory.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid, such as being on an unmapped drive.</exception>
      <exception cref="T:System.IO.IOException">The file is already open.</exception>
      <example>
        <para>Compose archive with entries encrypted with different encryption methods and passwords each.</para>
        <code>
            using (FileStream zipFile = File.Open("archive.zip", FileMode.Create))
            {
                FileInfo fi1 = new FileInfo("data1.bin");
                FileInfo fi2 = new FileInfo("data2.bin");
                FileInfo fi3 = new FileInfo("data3.bin");
                using (var archive = new Archive())
                {
                    archive.CreateEntry("entry1.bin", fi1, false, new ArchiveEntrySettings(new DeflateCompressionSettings(), new TraditionalEncryptionSettings("pass1")));
                    archive.CreateEntry("entry2.bin", fi2, false, new ArchiveEntrySettings(new DeflateCompressionSettings(), new AesEcryptionSettings("pass2", EncryptionMethod.AES128)));
                    archive.CreateEntry("entry3.bin", fi3, false, new ArchiveEntrySettings(new DeflateCompressionSettings(), new AesEcryptionSettings("pass3", EncryptionMethod.AES256)));
                    archive.Save(zipFile);
                }
            }
            </code>
      </example>
    </member>
    <member name="M:Aspose.Zip.Archive.CreateEntry(System.String,System.IO.Stream,Aspose.Zip.Saving.ArchiveEntrySettings,System.IO.FileSystemInfo)">
      <summary>
            Create single entry within the archive.
            </summary>
      <param name="name">The name of the entry.</param>
      <param name="source">The input stream for the entry.</param>
      <param name="newEntrySettings">Compression and encryption settings used for added <see cref="T:Aspose.Zip.ArchiveEntry" /> item.</param>
      <param name="fileInfo">The metadata of file or folder to be compressed.</param>
      <returns>Zip entry instance.</returns>
      <remarks>
        <para>The entry name is solely set within <paramref name="name" /> parameter. The file name provided in <paramref name="fileInfo" /> parameter does not affect the entry name.</para>
        <para>
          <paramref name="fileInfo" /> can refer to <see cref="T:System.IO.DirectoryInfo" /> if the entry is directory.</para>
      </remarks>
      <exception cref="T:System.InvalidOperationException">Both <paramref name="source" /> and <paramref name="fileInfo" /> are null or <paramref name="source" /> is null and <paramref name="fileInfo" /> stands for directory.</exception>
      <example>
        <para>Compose archive with encrypted entry.</para>
        <code>
            using (FileStream zipFile = File.Open("archive.zip", FileMode.Create))
            {
                using (var archive = new Archive())
                {
                    archive.CreateEntry("entry1.bin", new MemoryStream(new byte[] {0x00, 0xFF} ), new ArchiveEntrySettings(new DeflateCompressionSettings(), new TraditionalEncryptionSettings("pass1")), new FileInfo("data1.bin")); 
                    archive.Save(zipFile);
                }
            }
            </code>
      </example>
    </member>
    <member name="M:Aspose.Zip.Archive.CreateEntries(System.IO.DirectoryInfo,System.Boolean)">
      <summary>
            Adds to the archive all files and directories recursively in the directory given.
            </summary>
      <param name="directory">Directory to compress.</param>
      <param name="includeRootDirectory">Indicates whether to include the root directory itself or not.</param>
      <returns>The archive with entries composed.</returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">The path to <paramref name="directory" /> is invalid, such as being on an unmapped drive.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission to access <paramref name="directory" />.</exception>
      <example>
        <code>
            using (Archive archive = new Archive())
            {
                DirectoryInfo folder = new DirectoryInfo("C:\folder");
                archive.CreateEntries(folder);
                archive.Save("folder.zip");
            }
            </code>
      </example>
    </member>
    <member name="M:Aspose.Zip.Archive.CreateEntries(System.String,System.Boolean)">
      <summary>
            Adds to the archive all files and directories recursively in the directory given.
            </summary>
      <param name="sourceDirectory">Directory to compress.</param>
      <param name="includeRootDirectory">Indicates whether to include the root directory itself or not.</param>
      <returns>The archive with entries composed.</returns>
      <example>
        <code>
            using (Archive archive = new Archive())
            {
                archive.CreateEntries("C:\folder");
                archive.Save("folder.zip");
            }
            </code>
      </example>
    </member>
    <member name="M:Aspose.Zip.Archive.DeleteEntry(Aspose.Zip.ArchiveEntry)">
      <summary>
            Removes the first occurrence of a specific entry from the entries list.
            </summary>
      <param name="entry">The entry to remove from the entries list.</param>
      <returns>The archive with the entry deleted.</returns>
      <example>
        <para>Here is how you can remove all entries except the last one:</para>
        <code>
            using (var archive = new Archive("archive.zip"))
            {
                while (archive.Entries.Count &gt; 1)
                    archive.DeleteEntry(archive.Entries[0]);
                archive.Save("last_entry.zip");
            }
            </code>
      </example>
    </member>
    <member name="M:Aspose.Zip.Archive.DeleteEntry(System.Int32)">
      <summary>
            Removes the entry from the entries list by index.
            </summary>
      <param name="entryIndex">The zero-based index of the entry to remove.</param>
      <returns>The archive with the entry deleted.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="entryIndex" /> is less than 0.-or- <paramref name="entryIndex" /> is equal to or greater than <c>Entries</c> count.</exception>
      <example>
        <code>
            using (var archive = new TarArchive("two_files.zip"))
            {
                archive.DeleteEntry(0);
                archive.Save("single_file.zip");
            }
            </code>
      </example>
    </member>
    <member name="M:Aspose.Zip.Archive.Save(System.IO.Stream,Aspose.Zip.Saving.ArchiveSaveOptions)">
      <summary>
            Saves archive to the stream provided.
            </summary>
      <param name="outputStream">Destination stream.</param>
      <param name="saveOptions">Options for archive saving.</param>
      <remarks>
        <para>
          <paramref name="outputStream" /> must be writable.</para>
      </remarks>
      <exception cref="T:System.ArgumentException">
        <paramref name="outputStream" /> is not writable.</exception>
      <example>
        <code>
             using (FileStream zipFile = File.Open("archive.zip", FileMode.Create))
             {
                 using (var archive = new Archive())
                 {
                     archive.CreateEntry("entry.bin", "data.bin");
                     archive.Save(zipFile);
                 }
             }
             </code>
      </example>
    </member>
    <member name="M:Aspose.Zip.Archive.ExtractToDirectory(System.String)">
      <summary>
            Extracts all the files in the archive to the directory provided.
            </summary>
      <param name="destinationDirectory">The path to the directory to place the extracted files in.</param>
      <remarks>If the directory does not exist, it will be created.</remarks>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destinationDirectory" /> is null.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters and file names must be less than 260 characters.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission to access existing directory.</exception>
      <exception cref="T:System.NotSupportedException">If directory does not exist, path contains a colon character (:) that is not part of a drive label ("C:\").</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="destinationDirectory" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters by using the System.IO.Path.GetInvalidPathChars method. -or- path is prefixed with, or contains, only a colon character (:).</exception>
      <exception cref="T:System.IO.IOException">The directory specified by path is a file. -or- The network name is not known.</exception>
      <example>
        <code>
            using (var archive = new Archive("archive.zip")) 
            { 
               archive.ExtractToDirectory("C:\extracted");
            }
            </code>
      </example>
    </member>
    <member name="M:Aspose.Zip.Archive.Save(System.String,Aspose.Zip.Saving.ArchiveSaveOptions)">
      <summary>
            Saves archive to destination file provided.
            </summary>
      <param name="destinationFileName">The path of the archive to be created. If the specified file name points to an existing file, it will be overwritten.</param>
      <param name="saveOptions">Options for archive saving.</param>
      <remarks>
        <para>It is possible to save an archive to the same path as it was loaded from.
            However, this is not recommended because this approach uses copying to temporary file.</para>
      </remarks>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destinationFileName" /> is null.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission to access </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="destinationFileName" /> is empty, contains only white spaces, or contains invalid characters.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access to file <paramref name="destinationFileName" /> is denied.</exception>
      <exception cref="T:System.IO.PathTooLongException"> The specified <paramref name="destinationFileName" />, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters.</exception>
      <exception cref="T:System.NotSupportedException">File at <paramref name="destinationFileName" /> contains a colon (:) in the middle of the string.</exception>
      <example>
        <code>        
            using (var archive = new Archive())
            {
                archive.CreateEntry("entry.bin", "data.bin");
                archive.Save("archive.zip",  new ArchiveSaveOptions() { Encoding = Encoding.ASCII });
            }        
             </code>
      </example>
    </member>
    <member name="M:Aspose.Zip.Archive.Dispose">
      <inheritdoc />
      <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            </summary>
    </member>
    <member name="M:Aspose.Zip.Archive.Dispose(System.Boolean)">
      <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            </summary>
      <param name="disposing">Whether managed resources should be disposed.</param>
    </member>
    <member name="T:Aspose.Zip.ArchiveEntry">
      <summary>
            Represents single file within archive.
            </summary>
      <remarks>
            Cast an <see cref="T:Aspose.Zip.ArchiveEntry" /> instance to <see cref="T:Aspose.Zip.ArchiveEntryEncrypted" /> to determine whether the entry encrypted or not.
            </remarks>
    </member>
    <member name="M:Aspose.Zip.ArchiveEntry.#ctor(System.String,Aspose.Zip.Saving.CompressionSettings,System.IO.Stream,System.IO.FileAttributes,System.IO.FileSystemInfo)">
      <summary>
            Initializes a new instance of the <see cref="T:Aspose.Zip.ArchiveEntry" /> class.
            </summary>
      <param name="name">Entry name.</param>
      <param name="compressionSettings">Settings for compression or decompression.</param>
      <param name="source">Stream with entry data either to be compressed or to be decompressed.</param>
      <param name="fileAttributes">Attributes from file system.</param>
      <param name="fileInfo">File or directory info the entry based on.</param>
    </member>
    <member name="E:Aspose.Zip.ArchiveEntry.CompressionProgressed">
      <summary>
            Raises when a portion of raw stream compressed.
            </summary>
      <remarks>Event sender is an <see cref="T:Aspose.Zip.ArchiveEntry" /> instance.</remarks>
      <example>
        <code>
            archive.Entries[0].CompressionProgressed += (s, e) =&gt; { int percent = (int)((100 * (long)e.ProceededBytes) / entrySourceStream.Length); };
            </code>
      </example>
    </member>
    <member name="E:Aspose.Zip.ArchiveEntry.ExtractionProgressed">
      <summary>
            Raises when a portion of raw stream extracted.
            </summary>
      <remarks>Event sender is an <see cref="T:Aspose.Zip.ArchiveEntry" /> instance.</remarks>
      <example>
        <code>
            archive.Entries[0].ExtractionProgressed += (s, e) =&gt; {  int percent = (int)((100 * e.ProceededBytes) / ((ArchiveEntry)s).UncompressedSize); };
            </code>
      </example>
    </member>
    <member name="P:Aspose.Zip.ArchiveEntry.CompressedSize">
      <summary>
            Gets size of compressed file.
            </summary>
    </member>
    <member name="P:Aspose.Zip.ArchiveEntry.Name">
      <summary>
            Gets name of the entry within archive.
            </summary>
    </member>
    <member name="P:Aspose.Zip.ArchiveEntry.UncompressedSize">
      <summary>
            Gets size of original file.
            </summary>
    </member>
    <member name="P:Aspose.Zip.ArchiveEntry.ModificationTime">
      <summary>
            Gets or sets last modified date and time.
            </summary>
    </member>
    <member name="P:Aspose.Zip.ArchiveEntry.IsDirectory">
      <summary>
            Gets a value indicating whether the entry represents directory.
            </summary>
    </member>
    <member name="P:Aspose.Zip.ArchiveEntry.IsZip64">
      <summary>
            Gets or sets a value indicating whether use Zip64 related headers or not.
            </summary>
    </member>
    <member name="P:Aspose.Zip.ArchiveEntry.Source">
      <summary>
            Gets the data source stream for the entry.
            </summary>
    </member>
    <member name="P:Aspose.Zip.ArchiveEntry.VersionNeeded">
      <summary>
            Gets or sets the minimum supported ZIP specification version needed to extract the file.
            </summary>
    </member>
    <member name="P:Aspose.Zip.ArchiveEntry.FileAttributes">
      <summary>
            Gets file attributes from host system.
            </summary>
    </member>
    <member name="P:Aspose.Zip.ArchiveEntry.HeaderCompressionMethod">
      <summary>
            Gets compression method that goes to the header. This may differ from actual compression method in case of encryption.
            </summary>
    </member>
    <member name="P:Aspose.Zip.ArchiveEntry.CompressionSettings">
      <summary>
            Gets settings for compression or decompression.
            </summary>
    </member>
    <member name="M:Aspose.Zip.ArchiveEntry.Open(System.String)">
      <summary>
            Opens the entry for extraction and provides a stream with decompressed entry content.
            </summary>
      <param name="password">Optional password for decryption.</param>
      <returns>The stream that represents the contents of the entry.</returns>
      <remarks>
        <para>Read from the stream to get original content of file. See examples section.</para>
      </remarks>
      <example>
            Usage:
            <code>Stream decompressed = entry.Open();</code><para>
            .NET 4.0 and higher - use Stream.CopyTo method:
            <code>
            decompressed.CopyTo(httpResponse.OutputStream)
            </code></para><para>
            .NET 3.5 and before - copy bytes manually:
            <code>
            byte[] buffer = new byte[8192];
            int bytesRead;
            while (0 &lt; (bytesRead = decompressed.Read(buffer, 0, buffer.Length)))
             fileStream.Write(buffer, 0, bytesRead);
            </code></para></example>
    </member>
    <member name="M:Aspose.Zip.ArchiveEntry.Extract(System.String,System.String)">
      <summary>
            Extracts the entry to the filesystem by the path provided.
            </summary>
      <param name="path">The path to destitation file. If the file already exists, it will be overwritten.</param>
      <param name="password">Optional password for decryption.</param>
      <returns>The file info of composed file.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission to access </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="path" /> is empty, contains only white spaces, or contains invalid characters.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access to file <paramref name="path" /> is denied.</exception>
      <exception cref="T:System.IO.PathTooLongException"> The specified <paramref name="path" />, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters.</exception>
      <exception cref="T:System.NotSupportedException">File at <paramref name="path" /> contains a colon (:) in the middle of the string.</exception>
      <exception cref="T:System.IO.InvalidDataException">CRC or MAC verification failed for the entry.</exception>
      <example>
        <para>Extract two entries of zip archive, each with own password</para>
        <code>
            using (FileStream zipFile = File.Open("archive.zip", FileMode.Open))
            {
                using (Archive archive = new Archive(zipFile))
                {
                    archive.Entries[0].Extract("first.bin", "first_pass");
                    archive.Entries[1].Extract("second.bin", "second_pass");
                }
            }
            </code>
      </example>
    </member>
    <member name="M:Aspose.Zip.ArchiveEntry.GetDestinationStream(System.IO.Stream)">
      <summary>
            Gets output stream for the entry.
            </summary>
      <param name="outputStream">Output stream supplied for the entry.</param>
      <returns>Output stream.</returns>
    </member>
    <member name="M:Aspose.Zip.ArchiveEntry.InitializeCompressedData(System.IO.BinaryWriter)">
      <summary>
            Write to output stream any headers that precede compressed data.
            </summary>
      <param name="writer">Writer to output stream.</param>
    </member>
    <member name="M:Aspose.Zip.ArchiveEntry.FinalizeCompressedData(System.IO.BinaryWriter)">
      <summary>
            Write to output stream any headers that follow compressed data.
            </summary>
      <param name="writer">Writer to output stream.</param>
    </member>
    <member name="T:Aspose.Zip.ArchiveEntryEncrypted">
      <inheritdoc />
      <summary>
            Zip entry that needs to be compressed with encryption or decompressed with decryption.
            </summary>
    </member>
    <member name="P:Aspose.Zip.ArchiveEntryEncrypted.HeaderCompressionMethod">
      <summary>
            Gets compression method that goes to the header. This may differ from actual compression method in case of encryption.
            </summary>
    </member>
    <member name="M:Aspose.Zip.ArchiveEntryEncrypted.GetDestinationStream(System.IO.Stream)">
      <summary>
            Destination stream for the entry, decorated with crypto stream.
            </summary>
      <param name="outputStream">Output stream for the entry.</param>
      <returns>Encryption stream.</returns>
    </member>
    <member name="M:Aspose.Zip.ArchiveEntryEncrypted.FinalizeCompressedData(System.IO.BinaryWriter)">
      <summary>
            Write to output srteam any headers that follow compressed data.
            </summary>
      <param name="writer">Writer to output stream.</param>
    </member>
    <member name="M:Aspose.Zip.ArchiveEntryEncrypted.InitializeCompressedData(System.IO.BinaryWriter)">
      <summary>
            Write to output stream any headers that precede compressed data.
            </summary>
      <param name="writer">Writer to output stream.</param>
    </member>
    <member name="P:Aspose.Zip.ArchiveEntryEncrypted.EncryptionSettings">
      <summary>
            Gets settings for encryption or decryption.
            </summary>
    </member>
    <member name="T:Aspose.Zip.ArchiveEntryPlain">
      <inheritdoc />
      <summary>
            Zip entry that needs to be compressed without encryption or decompressed without decryption.
            </summary>
    </member>
    <member name="M:Aspose.Zip.ArchiveEntryPlain.GetDestinationStream(System.IO.Stream)">
      <summary>
            Destination stream for the entry, may be decorated.
            </summary>
      <param name="outputStream">Output stream for the entry.</param>
      <returns>The destination stream for entry compression.</returns>
    </member>
    <member name="M:Aspose.Zip.ArchiveEntryPlain.FinalizeCompressedData(System.IO.BinaryWriter)">
      <summary>
            Write to output srteam any headers that follow compressed data.
            </summary>
      <param name="writer">Writer to output stream.</param>
    </member>
    <member name="M:Aspose.Zip.ArchiveEntryPlain.InitializeCompressedData(System.IO.BinaryWriter)">
      <summary>
            Write to output stream any headers that precede compressed data.
            </summary>
      <param name="writer">Writer to output stream.</param>
    </member>
    <member name="T:Aspose.Zip.ArchiveLoadOptions">
      <summary>
            Options with which archive is loaded from compressed file.
            </summary>
    </member>
    <member name="P:Aspose.Zip.ArchiveLoadOptions.DecryptionPassword">
      <summary>
            Gets or sets the password to decrypt entries.
            </summary>
      <example>
        <para>You can provide decryption password once on archive extraction.</para>
        <code>
            using (FileStream fs = File.OpenRead("encrypted_archive.zip"))
            {
                using (var extracted = File.Create("extracted.bin"))
                {
                    using (Archive archive = new Archive(fs, new ArchiveLoadOptions() { DecryptionPassword = "p@s$" }))
                    {
                        using (var decompressed = archive.Entries[0].Open())
                        {
                            byte[] b = new byte[8192];
                            int bytesRead;
                            while (0 &lt; (bytesRead = decompressed.Read(b, 0, b.Length)))
                                extracted.Write(b, 0, bytesRead);
                            
                        }
                    }
                }
            }
            </code>
      </example>
      <seealso cref="M:Aspose.Zip.ArchiveEntry.Open(System.String)" />
    </member>
    <member name="P:Aspose.Zip.ArchiveLoadOptions.EntryListed">
      <summary>
            Gets or sets the delegate invoked when an entry listed within table of content.
            </summary>
      <example>
        <code>
             Archive archive = new Archive("archive.zip", new ArchiveLoadOptions() { EntryListed = (s, e) =&gt; { Console.WriteLine(e.Entry.Name); } });
            </code>
      </example>
    </member>
    <member name="P:Aspose.Zip.ArchiveLoadOptions.EntryExtractionProgressed">
      <summary>
             Gets or sets the delegate invoked when some bytes have been extracted.
             </summary>
      <remarks>Event sender is the <see cref="T:Aspose.Zip.ArchiveEntry" /> instance whtich extraction is progressed.</remarks>
      <example>
        <code>
             Archive archive = new Archive("archive.zip", 
             new ArchiveLoadOptions() { EntryExtractionProgressed = (s, e) =&gt; { int percent = (int)((100 * e.ProceededBytes) / ((ArchiveEntry)s).UncompressedSize); } })                 
            </code>
      </example>
    </member>
    <member name="T:Aspose.Zip.EntryEventArgs">
      <summary>
            Event arguments for entry related events.
            </summary>
      <seealso cref="P:Aspose.Zip.ArchiveLoadOptions.EntryListed" />
    </member>
    <member name="M:Aspose.Zip.EntryEventArgs.#ctor(Aspose.Zip.ArchiveEntry)">
      <summary>
            Initializes a new instance of the <see cref="T:Aspose.Zip.EntryEventArgs" /> class.
            </summary>
      <param name="entry">Archive entry the event is raised for.</param>
    </member>
    <member name="P:Aspose.Zip.EntryEventArgs.Entry">
      <summary>
            Gets the archive entry the event is raised for.
            </summary>
    </member>
    <member name="T:Aspose.Zip.ProgressEventArgs">
      <summary>
            Class for event data containing the number of bytes proceeded.
            </summary>
    </member>
    <member name="M:Aspose.Zip.ProgressEventArgs.#ctor(System.UInt64)">
      <summary>
            Initializes a new instance of the <see cref="T:Aspose.Zip.ProgressEventArgs" /> class.
            </summary>
      <param name="proceededBytes">The number of bytes proceeded.</param>
    </member>
    <member name="P:Aspose.Zip.ProgressEventArgs.ProceededBytes">
      <summary>
            Gets the number of bytes proceeded.
            </summary>
    </member>
    <!-- Badly formed XML comment ignored for member "F:Aspose.SevenZip.Compression.CoderPropID.PosStateBits" -->
    <!-- Badly formed XML comment ignored for member "F:Aspose.SevenZip.Compression.CoderPropID.LitContextBits" -->
    <!-- Badly formed XML comment ignored for member "F:Aspose.SevenZip.Compression.CoderPropID.LitPosBits" -->
  </members>
</doc>