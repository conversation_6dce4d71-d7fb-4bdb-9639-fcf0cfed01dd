Snap-shot of the ISO 11783-11 online data base
@ Copyright International Organization for Standardization, see: www.iso.org/iso/copyright.htm. No reproduction on networking 
permitted without license from ISO. The export file from the online data base is supplied without liability. Hard and Saved 
copies of this document are considered uncontrolled and represents a snap-shot of the ISO11783-11 online data base.


DD Entity: 0 Data Dictionary Version
Definition: This DDE is used to specify which version of the Data Dictionary is being used.
Comment: This DDE was a result of Jan 2005 Task Controller meeting.
Typically used by Device Classes: 
Not Assigned
Unit: not defined - not defined
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: 11783-Part 10 Task Force
Submit Date: 2005-01-20
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 1 Setpoint Volume Per Area Application Rate
Definition: Setpoint Application Rate specified as volume per area
Comment:  
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
10 - Irrigation
Unit: mm³/m² - Capacity per area unit
Resolution: 0,01
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 2 Actual Volume Per Area Application Rate
Definition: Actual Application Rate specified as volume per area
Comment: 
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
10 - Irrigation
Unit: mm³/m² - Capacity per area unit
Resolution: 0,01
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 3 Default Volume Per Area Application Rate
Definition: Default Application Rate specified as volume per area
Comment: use when missing Position data or outside any Treatment Zone
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
10 - Irrigation
Unit: mm³/m² - Capacity per area unit
Resolution: 0,01
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 4 Minimum Volume Per Area Application Rate
Definition: Minimum Application Rate specified as volume per area
Comment: supplied by device as physical minimum
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
10 - Irrigation
Unit: mm³/m² - Capacity per area unit
Resolution: 0,01
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 5 Maximum Volume Per Area Application Rate
Definition: Maximum Application Rate specified as volume per area
Comment: supplied by device as physical maximum
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
10 - Irrigation
Unit: mm³/m² - Capacity per area unit
Resolution: 0,01
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 6 Setpoint Mass Per Area Application Rate
Definition: Setpoint Application Rate specified as mass per area
Comment: 
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
Unit: mg/m² - Mass per area unit
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 7 Actual Mass Per Area Application Rate
Definition: Actual Application Rate specified as mass per area
Comment: 
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
10 - Irrigation
Unit: mg/m² - Mass per area unit
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 8 Default Mass Per Area Application Rate
Definition: Default Application Rate specified as mass per area
Comment: use when missing Position data or outside any Treatment Zone
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
10 - Irrigation
Unit: mg/m² - Mass per area unit
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 9 Minimum Mass Per Area Application Rate
Definition: Minimum Application Rate specified as mass per area
Comment: supplied by device as physical minimum
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
10 - Irrigation
Unit: mg/m² - Mass per area unit
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 10 Maximum Mass Per Area Application Rate
Definition: Maximum Application Rate specified as mass per area
Comment: supplied by device as physical maximum
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
10 - Irrigation
Unit: mg/m² - Mass per area unit
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 11 Setpoint Count Per Area Application Rate
Definition: Setpoint Application Rate specified as count per area
Comment: 
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
Unit: /m² - Quantity per area unit
Resolution: 0,001
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 12 Actual Count Per Area Application Rate
Definition: Actual Application Rate specified as count per area
Comment: 
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
Unit: /m² - Quantity per area unit
Resolution: 0,001
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 13 Default Count Per Area Application Rate
Definition: Default Application Rate specified as count per area
Comment: use when missing Position data or outside any Treatment Zone
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
Unit: /m² - Quantity per area unit
Resolution: 0,001
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 14 Minimum CountPer Area Application Rate
Definition: Minimum Application Rate specified as count per area
Comment: supplied by device as physical minimum
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
Unit: /m² - Quantity per area unit
Resolution: 0,001
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 15 Maximum Count Per Area Application Rate
Definition: Maximum Application Rate specified as count per area
Comment: supplied by device as physical maximum
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
Unit: /m² - Quantity per area unit
Resolution: 0,001
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 16 Setpoint Spacing Application Rate
Definition: Setpoint Application Rate specified as distance: e.g. seed spacing of a precision seeder
Comment: 
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
Unit: mm - Length
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 17 Actual Spacing Application Rate
Definition: Actual Application Rate specified as distance: e.g. seed spacing of a precision seeder
Comment: 
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
Unit: mm - Length
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 18 Default Spacing Application Rate
Definition: Default Application Rate specified as distance: e.g. seed spacing of a precision seeder
Comment: use when missing Position data or outside any Treatment Zone
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
Unit: mm - Length
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 19 Minimum Spacing Application Rate
Definition: Minimum Application Rate specified as distance: e.g. seed spacing of a precision seeder
Comment: supplied by device as physical minimum
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
Unit: mm - Length
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 20 Maximum Spacing Application Rate
Definition: Maximum Application Rate specified as distance: e.g. seed spacing of a precision seeder
Comment: supplied by device as physical maximum
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
Unit: mm - Length
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 21 Setpoint Volume Per Volume Application Rate
Definition: Setpoint Application Rate specified as volume per volume
Comment: 
Typically used by Device Classes: 
5 - Fertilizer
6 - Sprayers
Unit: mm³/m³ - Capacity per capacity unit
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 22 Actual Volume Per Volume Application Rate
Definition: Actual Application Rate specified as volume per volume
Comment: 
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
10 - Irrigation
Unit: mm³/m³ - Capacity per capacity unit
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 23 Default Volume Per Volume Application Rate
Definition: Default Application Rate specified as volume per volume
Comment: use when missing Position data or outside any Treatment Zone
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
10 - Irrigation
Unit: mm³/m³ - Capacity per capacity unit
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 24 Minimum Volume Per Volume Application Rate
Definition: Minimum Application Rate specified as volume per volume
Comment: supplied by device as physical minimum
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
10 - Irrigation
Unit: mm³/m³ - Capacity per capacity unit
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 25 Maximum Volume Per Volume Application Rate
Definition: Maximum Application Rate specified as volume per volume
Comment: supplied by device as physical maximum
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
10 - Irrigation
Unit: mm³/m³ - Capacity per capacity unit
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 26 Setpoint Mass Per Mass Application Rate
Definition: Setpoint Application Rate specified as mass per mass
Comment: 
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
Unit: mg/kg - Mass per mass unit
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 27 Actual Mass Per Mass Application Rate
Definition: Actual Application Rate specified as mass per mass
Comment: 
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
Unit: mg/kg - Mass per mass unit
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 28 Default Mass Per Mass Application Rate
Definition: Default Application Rate specified as mass per mass
Comment: use when missing Position data or outside any Treatment Zone
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
10 - Irrigation
Unit: mg/kg - Mass per mass unit
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 29 Minimum Mass Per Mass Application Rate
Definition: Minimum Application Rate specified as mass per mass
Comment: supplied by device as physical minimum
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
10 - Irrigation
Unit: mg/kg - Mass per mass unit
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 30 MaximumMass Per Mass Application Rate
Definition: Maximum Application Rate specified as mass per mass
Comment: supplied by device as physical maximum
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
10 - Irrigation
Unit: mg/kg - Mass per mass unit
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 31 Setpoint Volume Per Mass Application Rate
Definition: Setpoint Application Rate specified as volume per mass
Comment: 
Typically used by Device Classes: 
5 - Fertilizer
6 - Sprayers
Unit: mm³/kg - Capacity per mass unit
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 32 Actual Volume Per Mass Application Rate
Definition: Actual Application Rate specified as volume per mass
Comment: 
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
9 - Forage harvester
Unit: mm³/kg - Capacity per mass unit
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2009-02-05
Status Comments: Added device class 9 - Forage Harvester
Attachments: 
none



DD Entity: 33 Default Volume Per Mass Application Rate
Definition: Default Application Rate specified as volume per mass
Comment: use when missing Position data or outside any Treatment Zone
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
10 - Irrigation
Unit: mm³/kg - Capacity per mass unit
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 34 Minimum Volume Per Mass Application Rate
Definition: Minimum Application Rate specified as volume per mass
Comment: supplied by device as physical minimum
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
10 - Irrigation
Unit: mm³/kg - Capacity per mass unit
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 35 Maximum Volume Per Mass Application Rate
Definition: Maximum Application Rate specified as volume per mass
Comment: supplied by device as physical maximum
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
10 - Irrigation
Unit: mm³/kg - Capacity per mass unit
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 36 Setpoint Volume Per Time Application Rate
Definition: Setpoint Application Rate specified as volume per time
Comment: 
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
10 - Irrigation
Unit: mm³/s - Flow
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 37 Actual Volume Per Time Application Rate
Definition: Actual Application Rate specified as volume per time
Comment: 
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
10 - Irrigation
Unit: mm³/s - Flow
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 38 Default Volume Per Time Application Rate
Definition: Default Application Rate specified as volume per time
Comment: use when missing Position data or outside any Treatment Zone
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
10 - Irrigation
Unit: mm³/s - Flow
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 39 Minimum Volume Per Time Application Rate
Definition: Minimum Application Rate specified as volume per time
Comment: supplied by device as physical minimum
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
10 - Irrigation
Unit: mm³/s - Flow
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 40 Maximum Volume Per Time Application Rate
Definition: Maximum Application Rate specified as volume per time
Comment: supplied by device as physical maximum
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
10 - Irrigation
Unit: mm³/s - Flow
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 41 Setpoint Mass Per Time Application Rate
Definition: Setpoint Application Rate specified as mass per time
Comment: 
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
10 - Irrigation
Unit: mg/s - Mass flow
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 42 Actual Mass Per Time Application Rate
Definition: Actual Application Rate specified as mass per time
Comment: 
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
10 - Irrigation
Unit: mg/s - Mass flow
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 43 Default Mass Per Time Application Rate
Definition: Default Application Rate specified as mass per time
Comment: use when missing Position data or outside any Treatment Zone
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
10 - Irrigation
Unit: mg/s - Mass flow
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 44 Minimum Mass Per Time Application Rate
Definition: Minimum Application Rate specified as mass per time
Comment: supplied by device as physical minimum
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
10 - Irrigation
Unit: mg/s - Mass flow
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 45 Maximum Mass Per Time Application Rate
Definition: Maximum Application Rate specified as mass per time
Comment: supplied by device as physical maximum
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
10 - Irrigation
Unit: mg/s - Mass flow
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 46 Setpoint Count Per Time Application Rate
Definition: Setpoint Application Rate specified as count per time
Comment: 
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
Unit: /s - Quantity per time unit
Resolution: 0,001
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 47 Actual Count Per Time Application Rate
Definition: Actual Application Rate specified as count per time
Comment: 
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
Unit: /s - Quantity per time unit
Resolution: 0,001
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 48 Default Count Per Time Application Rate
Definition: Default Application Rate specified as count per time
Comment: use when missing Position data or outside any Treatment Zone
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
Unit: /s - Quantity per time unit
Resolution: 0,001
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 49 Minimum Count Per Time Application Rate
Definition: Minimum Application Rate specified as count per time
Comment: supplied by device as physical minimum
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
Unit: /s - Quantity per time unit
Resolution: 0,001
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 50 Maximum Count Per Time Application Rate
Definition: Maximum Application Rate specified as count per time
Comment: supplied by device as physical maximum
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
Unit: /s - Quantity per time unit
Resolution: 0,001
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 51 Setpoint Tillage Depth
Definition: Setpoint Tillage Depth of Device Element below soil surface, value increases with depth.  In case of a negative value the system will indicate the distance above the ground.
Comment: 
Typically used by Device Classes: 
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
8 - Root Harvester
Unit: mm - Length
Resolution: 1
SAE SPN: not specified
Range: -2147483648 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-06-16
Status Comments: 
Attachments: 
none



DD Entity: 52 Actual Tillage Depth
Definition: Actual Tillage Depth of Device Element below soil surface, value increases with depth. In case of a negative value the system will indicate the distance above the ground.
Comment: 
Typically used by Device Classes: 
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
Unit: mm - Length
Resolution: 1
SAE SPN: not specified
Range: -2147483648 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-06-16
Status Comments: 
Attachments: 
none



DD Entity: 53 Default Tillage Depth
Definition: Default Tillage Depth of Device Element below soil surface, value increases with depth. In case of a negative value the system will indicate the distance above the ground.
Comment: Use when missing Position data or outside any Treatment Zone
Typically used by Device Classes: 
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
8 - Root Harvester
Unit: mm - Length
Resolution: 1
SAE SPN: not specified
Range: -2147483648 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-06-16
Status Comments: 
Attachments: 
none



DD Entity: 54 Minimum Tillage Depth
Definition: Minimum Tillage Depth of Device Element below soil surface, value increases with depth. In case of a negative value the system will indicate the distance above the ground.
Comment: Supplied by device as physical minimum
Typically used by Device Classes: 
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
8 - Root Harvester
Unit: mm - Length
Resolution: 1
SAE SPN: not specified
Range: -2147483648 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-06-16
Status Comments: 
Attachments: 
none



DD Entity: 55 Maximum Tillage Depth
Definition: Maximum Tillage Depth of Device Element below soil surface, value increases with depth. In case of a negative value the system will indicate the distance above the ground.
Comment: Supplied by device as physical maximum
Typically used by Device Classes: 
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
8 - Root Harvester
Unit: mm - Length
Resolution: 1
SAE SPN: not specified
Range: -2147483648 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-06-16
Status Comments: 
Attachments: 
none



DD Entity: 56 Setpoint Seeding Depth
Definition: Setpoint Seeding Depth of Device Element below soil surface, value increases with depth
Comment: 
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
Unit: mm - Length
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 57 Actual Seeding Depth
Definition: Actual Seeding Depth of Device Element below soil surface, value increases with depth
Comment: 
Typically used by Device Classes: 
4 - Planters /Seeders
Unit: mm - Length
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 58 Default Seeding Depth
Definition: Default Seeding Depth of Device Element below soil surface, value increases with depth
Comment: use when missing Position data or outside any Treatment Zone
Typically used by Device Classes: 
4 - Planters /Seeders
Unit: mm - Length
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 59 Minimum Seeding Depth
Definition: Minimum Seeding Depth of Device Element below soil surface, value increases with depth
Comment: supplied by device as physical minimum
Typically used by Device Classes: 
4 - Planters /Seeders
Unit: mm - Length
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 60 Maximum Seeding Depth
Definition: Maximum Seeding Depth of Device Element below soil surface, value increases with depth
Comment: supplied by device as physical maximum
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
Unit: mm - Length
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 61 Setpoint Working Height
Definition: Setpoint Working Height of Device Element above crop or soil
Comment: 
Typically used by Device Classes: 
0 - Non-specific system
5 - Fertilizer
6 - Sprayers
7 - Harvesters
9 - Forage harvester
10 - Irrigation
14 - Special Crops
Unit: mm - Length
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 62 Actual Working Height
Definition: Actual Working Height of Device Element above crop or soil
Comment: This is the height above the effective control surface. For sprayers this is the height above the crop canapé and for fertilizer spreaders, harvesters, etc it is the height above the ground.
Typically used by Device Classes: 
5 - Fertilizer
6 - Sprayers
7 - Harvesters
9 - Forage harvester
10 - Irrigation
Unit: mm - Length
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2009-02-05
Status Comments: Added comment for clarification.
Attachments: 
none



DD Entity: 63 Default Working Height
Definition: Default Working Height of Device Element above crop or soil
Comment: use when missing Position data or outside any Treatment Zone
Typically used by Device Classes: 
5 - Fertilizer
6 - Sprayers
7 - Harvesters
9 - Forage harvester
10 - Irrigation
Unit: mm - Length
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 64 Minimum Working Height
Definition: Minimum Working Height of Device Element above crop or soil
Comment: supplied by device as physical minimum
Typically used by Device Classes: 
5 - Fertilizer
6 - Sprayers
7 - Harvesters
9 - Forage harvester
10 - Irrigation
Unit: mm - Length
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 65 Maximum Working Height
Definition: Maximum Working Height of Device Element above crop or soil
Comment: supplied by device as physical maximum
Typically used by Device Classes: 
5 - Fertilizer
6 - Sprayers
7 - Harvesters
9 - Forage harvester
10 - Irrigation
Unit: mm - Length
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 66 Setpoint Working Width
Definition: Setpoint Working Width of Device Element
Comment: 
Typically used by Device Classes: 
0 - Non-specific system
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
14 - Special Crops
15 - Municipal Work
Unit: mm - Length
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 67 Actual Working Width
Definition: Actual Working Width of Device Element
Comment: This is the effective / active working width during operation.
Typically used by Device Classes: 
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
Unit: mm - Length
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2009-02-05
Status Comments: Added comment for clarification.
Attachments: 
none



DD Entity: 68 Default Working Width
Definition: Default Working Width of Device Element
Comment: 
Typically used by Device Classes: 
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
Unit: mm - Length
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 69 Minimum Working Width
Definition: Minimum Working Width of Device Element
Comment: 
Typically used by Device Classes: 
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
Unit: mm - Length
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 70 Maximum Working Width
Definition: Maximum Working Width of Device Element
Comment: 
Typically used by Device Classes: 
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
Unit: mm - Length
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 71 Setpoint Volume Content
Definition: Setpoint Device Element Content specified as volume
Comment: 
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
10 - Irrigation
11 - Transport / Trailers
Unit: ml - Capacity large
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 72 Actual Volume Content
Definition: Actual Device Element Content specified as volume
Comment: 
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
Unit: ml - Capacity large
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 73 Maximum Volume Content
Definition: Maximum Device Element Content specified as volume
Comment: is a minimum needed as well ??
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
Unit: ml - Capacity large
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 74 Setpoint Mass Content
Definition: Setpoint Machine Element Content specified as mass
Comment: 
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
Unit: g - Mass large
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 75 Actual Mass Content
Definition: Actual Device Element Content specified as mass
Comment: If the device is equipped with a weighing system which provides the possibility to tare the current load it is possible that the value has a negative sign in case of an unload operation.
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
Unit: g - Mass large
Resolution: 1
SAE SPN: not specified
Range: -2147483648 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 2
Current Status: ISO-Published
Status Date: 2011-04-04
Status Comments: 
Attachments: 
none



DD Entity: 76 Maximum Mass Content
Definition: Maximum Device Element Content specified as mass
Comment: is a minimum needed as well ??
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
Unit: g - Mass large
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 77 Setpoint Count Content
Definition: Setpoint Device Element Content specified as count
Comment: 
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
Unit: # - Quantity/Count
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 78 Actual Count Content
Definition: Actual Device Element Content specified as count
Comment: 
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
Unit: # - Quantity/Count
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 79 Maximum Count Content
Definition: Maximum Device Element Content specified as count
Comment: is a minimum needed as well ??
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
Unit: # - Quantity/Count
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 80 Application Total Volume in [L]
Definition: Accumulated Application specified as volume in liter [L]
Comment: is a counter of a device element
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
10 - Irrigation
Unit: L - Capacity count
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 81 Application Total Mass in [kg]
Definition: Accumulated Application specified as mass in kilogram [kg]
Comment: is a counter of a device element
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
10 - Irrigation
Unit: kg - Mass
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 82 Application Total Count
Definition: Accumulated Application specified as count
Comment: is a counter of a device element
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
Unit: # - Quantity/Count
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 83 Volume Per Area Yield
Definition: Yield as volume per area
Comment: 
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
Unit: ml/m² - Capacity per area large
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 84 Mass Per Area Yield
Definition: Yield as mass per area, not corrected for the reference moisture percentage DDI 184.
Comment: This Mass per Area yield is the mass that includes the actual percentage moisture (DDI 99) if this is measured on e.g. harvesting equipment. This comment is added to clarify and differentiate this DDI from the Dry Mass Per Area Yield (DDI 181).

Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
Unit: mg/m² - Mass per area unit
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 85 Count Per Area Yield
Definition: Yield as count per area
Comment: 
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
Unit: /m² - Quantity per area unit
Resolution: 0,001
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 86 Volume Per Time Yield
Definition: Yield as volume per time
Comment: 
Typically used by Device Classes: 
0 - Non-specific system
Unit: ml/s - Float large
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 87 Mass Per Time Yield
Definition: Yield as mass per time, not corrected for the reference moisture percentage DDI 184.
Comment: This Mass per Time yield is the mass that includes the actual percentage moisture (DDI 99) if this is measured on e.g. harvesting equipment. This comment is added to clarify and differentiate this DDI from the Dry Mass Per Time Yield (DDI 182).
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
Unit: mg/s - Mass flow
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 88 Count Per Time Yield
Definition: Yield as count per time
Comment: 
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
Unit: /s - Quantity per time unit
Resolution: 0,001
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 89 Yield Total Volume
Definition: Accumulated Yield specified as volume
Comment: is a counter of a machine element
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
Unit: L - Quantity per volume
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 90 Yield Total Mass
Definition: Accumulated Yield specified as mass, not corrected for the reference moisture percentage DDI 184.
Comment: This Yield Total Mass is the mass that includes the average percentage moisture (DDI 262) if this is measured on e.g. harvesting equipment. This comment is added to clarify and differentiate this DDI from the Yield Total Dry Mass (DDI 183).
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
Unit: kg - Mass
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 91 Yield Total Count
Definition: Accumulated Yield specified as count
Comment: is a counter of a machine element
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
Unit: # - Quantity/Count
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 92 Volume Per Area Crop Loss
Definition: Crop yield loss as volume per area
Comment: 
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
Unit: ml/m² - Capacity per area large
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 93 Mass Per Area Crop Loss
Definition: Crop yield loss as mass per area
Comment: 
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
Unit: mg/m² - Mass per area unit
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 94 Count Per Area Crop Loss
Definition: Crop yield loss as count per area
Comment: 
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
Unit: /m² - Quantity per area unit
Resolution: 0,001
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 95 Volume Per Time Crop Loss
Definition: Crop yield loss as volume per time
Comment: 
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
Unit: ml/s - Float large
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 96 Mass Per Time Crop Loss
Definition: Crop yield loss as mass per time
Comment: 
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
Unit: mg/s - Mass flow
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 97 Count Per Time Crop Loss
Definition: Crop yield loss as count per time
Comment: 
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
Unit: /s - Quantity per time unit
Resolution: 0,001
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 98 Percentage Crop Loss
Definition: Crop yield loss
Comment: 
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
11 - Transport / Trailers
Unit: ppm - Parts per million
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 99 Crop Moisture
Definition: Moisture in crop yield
Comment: This DDE defines the actual percentage moisture of the crop.

Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
Unit: ppm - Parts per million
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 100 Crop Contamination
Definition: Dirt or foreign material in crop yield
Comment: This DDE defines the contamination in ratio of the yield DDI units.
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
Unit: ppm - Parts per million
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 101 Setpoint Bale Width
Definition: Setpoint Bale Width for square baler or round baler
Comment: 
Typically used by Device Classes: 
9 - Forage harvester
Unit: mm - Length
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 102 Actual Bale Width
Definition: Actual Bale Width for square baler or round baler
Comment: 
Typically used by Device Classes: 
9 - Forage harvester
Unit: mm - Length
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 103 Default Bale Width
Definition: Default Bale Width for square baler or round baler
Comment: 
Typically used by Device Classes: 
9 - Forage harvester
Unit: mm - Length
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 104 Minimum Bale Width
Definition: Minimum Bale Width for square baler or round baler
Comment: 
Typically used by Device Classes: 
9 - Forage harvester
Unit: mm - Length
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 105 Maximum Bale Width
Definition: Maximum Bale Width for square baler or round baler
Comment: 
Typically used by Device Classes: 
9 - Forage harvester
Unit: mm - Length
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 106 Setpoint Bale Height
Definition: Setpoint Bale Height is only applicable to square baler
Comment: 
Typically used by Device Classes: 
9 - Forage harvester
Unit: mm - Length
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 107 ActualBaleHeight
Definition: Actual Bale Height is only applicable to square baler
Comment: 
Typically used by Device Classes: 
9 - Forage harvester
Unit: mm - Length
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 108 Default Bale Height
Definition: Default Bale Height is only applicable to square baler
Comment: 
Typically used by Device Classes: 
9 - Forage harvester
Unit: mm - Length
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 109 Minimum Bale Height
Definition: Minimum Bale Height is only applicable to square baler
Comment: 
Typically used by Device Classes: 
9 - Forage harvester
Unit: mm - Length
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 110 Maximum Bale Height
Definition: Maximum Bale Height is only applicable to square baler
Comment: 
Typically used by Device Classes: 
9 - Forage harvester
Unit: mm - Length
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 111 Setpoint Bale Size
Definition: Setpoint Bale Size as length for a square baler or diameter for a round baler
Comment: 
Typically used by Device Classes: 
9 - Forage harvester
Unit: mm - Length
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 112 Actual Bale Size
Definition: Actual Bale Size as length for a square baler or diameter for a round baler
Comment: 
Typically used by Device Classes: 
9 - Forage harvester
Unit: mm - Length
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 113 Default Bale Size
Definition: Default Bale Size as length for a square baler or diameter for a round baler
Comment: 
Typically used by Device Classes: 
9 - Forage harvester
Unit: mm - Length
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 114 Minimum Bale Size
Definition: Minimum Bale Size as length for a square baler or diameter for a round baler
Comment: 
Typically used by Device Classes: 
9 - Forage harvester
Unit: mm - Length
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 115 Maximum Bale Size
Definition: Maximum Bale Size as length for a square baler or diameter for a round baler
Comment: 
Typically used by Device Classes: 
9 - Forage harvester
Unit: mm - Length
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 116 Total Area
Definition: Accumulated Area
Comment: is a counter of a machine element
Typically used by Device Classes: 
0 - Non-specific system
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
14 - Special Crops
15 - Municipal Work
Unit: m² - Area
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 117 Effective Total Distance
Definition: Accumulated Distance in working position
Comment: is a counter of a machine element
Typically used by Device Classes: 
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
Unit: mm - Length
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 118 Ineffective Total Distance
Definition: Accumulated Distance out of working position
Comment: is a counter of a machine element
Typically used by Device Classes: 
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
Unit: mm - Length
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 119 Effective Total Time
Definition: Accumulated Time in working position
Comment: is a counter of a machine element
Typically used by Device Classes: 
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
17 - Sensor System
Unit: s - Time count
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 2
Current Status: ISO-Published
Status Date: 2011-12-21
Status Comments: 
Attachments: 
none



DD Entity: 120 Ineffective Total Time
Definition: Accumulated Time out of working position
Comment: is a counter of a machine element
Typically used by Device Classes: 
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
Unit: s - Time count
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 121 Product Density Mass Per Volume
Definition: Product Density as mass per volume
Comment: 
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
Unit: mg/l - Mass per capacity unit
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 122 Product Density Mass PerCount
Definition: Product Density as mass per count
Comment: 
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
Unit: mg/1000 - 1000 seed Mass
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 123 Product Density Volume Per Count
Definition: Product Density as volume per count
Comment: 
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
Unit: ml/1000 - Volume per quantity unit
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Part 10 Task Force
Submit Date: 2003-08-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 124 Auxiliary Valve Scaling Extend
Definition: Factor to apply to AuxValveCommand PortFlowCommand. The scaling of the port flow relates to flow, not to spool position, although the position of the spool is of course indirectly affected.
Comment: 
Typically used by Device Classes: 
Not Assigned
Unit: % - Percent
Resolution: 0,1
SAE SPN: not specified
Range: -2147483648 - **********
Submit by: Lars Althof
Submit Date: 2004-09-10
Submit Company: 57 - Sauer-Danfoss Co.
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 125 Auxiliary Valve Scaling Retract
Definition: Factor to apply to AuxValveCommand PortFlowCommand. The scaling of the port flow relates to flow, not to spool position, although the position of the spool is of course indirectly affected.
Comment: 
Typically used by Device Classes: 
Not Assigned
Unit: % - Percent
Resolution: 0,1
SAE SPN: not specified
Range: -2147483648 - **********
Submit by: Lars Althof
Submit Date: 2004-09-10
Submit Company: 57 - Sauer-Danfoss Co.
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 126 Auxiliary Valve Ramp Extend Up
Definition: The valve will apply a ramp to the Auxiliary ValveCommand PortFlowCommand, to limit the acceleration or deceleration of flow. The valve must apply the ramp to create a liniear increase/decrease of flow over time.
Comment: 
Typically used by Device Classes: 
Not Assigned
Unit: ms - Time
Resolution: 1
SAE SPN: not specified
Range: -2147483648 - **********
Submit by: Lars Althof
Submit Date: 2004-09-10
Submit Company: 57 - Sauer-Danfoss Co.
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 127 Auxiliary Valve Ramp Extend Down
Definition: The valve will apply a ramp to the Auxiliary ValveCommand PortFlowCommand, to limit the acceleration or deceleration of flow. The valve must apply the ramp to create a liniear increase/decrease of flow over time.
Comment: 
Typically used by Device Classes: 
Not Assigned
Unit: ms - Time
Resolution: 1
SAE SPN: not specified
Range: -2147483648 - **********
Submit by: Lars Althof
Submit Date: 2004-09-10
Submit Company: 57 - Sauer-Danfoss Co.
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 128 Auxiliary Valve Ramp Retract Up
Definition: The valve will apply a ramp to theAuxiliary ValveCommand PortFlowCommand, to limit the acceleration or deceleration of flow. The valve must apply the ramp to create a liniear increase/decrease of flow over time.
Comment: 
Typically used by Device Classes: 
Not Assigned
Unit: ms - Time
Resolution: 1
SAE SPN: not specified
Range: -2147483648 - **********
Submit by: Lars Althof
Submit Date: 2004-09-10
Submit Company: 57 - Sauer-Danfoss Co.
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 129 Auxiliary Valve Ramp Retract Down
Definition: The valve will apply a ramp to the Auxiliary ValveCommand PortFlowCommand, to limit the acceleration or deceleration of flow. The valve must apply the ramp to create a liniear increase/decrease of flow over time.
Comment: 
Typically used by Device Classes: 
Not Assigned
Unit: ms - Time
Resolution: 1
SAE SPN: not specified
Range: -2147483648 - **********
Submit by: Lars Althof
Submit Date: 2004-09-10
Submit Company: 57 - Sauer-Danfoss Co.
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 130 Auxiliary Valve Float Threshold
Definition: Safety function. Current output of valve must be above threshold before float command is allowed.
Comment: 
Typically used by Device Classes: 
Not Assigned
Unit: % - Percent
Resolution: 0,1
SAE SPN: not specified
Range: -2147483648 - **********
Submit by: Lars Althof
Submit Date: 2004-09-10
Submit Company: 57 - Sauer-Danfoss Co.
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 131 Auxiliary Valve Progressivity Extend
Definition: Define non-linear releationship between command and flow by 2nd degree polynomium. (I will get polynomium)
Comment: 
Typically used by Device Classes: 
Not Assigned
Unit: n.a. - 
Resolution: 1
SAE SPN: not specified
Range: -2147483648 - **********
Submit by: Lars Althof
Submit Date: 2004-09-10
Submit Company: 57 - Sauer-Danfoss Co.
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 132 Auxiliary Valve Progressivity Retract
Definition: Define non-linear releationship between command and flow by 2nd degree polynomium. (I will get polynomium)
Comment: 
Typically used by Device Classes: 
Not Assigned
Unit: n.a. - 
Resolution: 1
SAE SPN: not specified
Range: -2147483648 - **********
Submit by: Lars Althof
Submit Date: 2004-09-10
Submit Company: 57 - Sauer-Danfoss Co.
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 133 Auxiliary Valve Invert Ports
Definition: Tell valve to swap extend and retract ports, easier than redoing plumbing on valve
Comment: 
Typically used by Device Classes: 
Not Assigned
Unit: n.a. - 
Resolution: 1
SAE SPN: not specified
Range: -2147483648 - **********
Submit by: Lars Althof
Submit Date: 2004-09-10
Submit Company: 57 - Sauer-Danfoss Co.
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 134 Device Element Offset X
Definition: X direction offset of a DeviceElement relative to a Device.
Comment: This DDE was a result of Jan 2005 Task Controller meeting.
Typically used by Device Classes: 
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
Unit: mm - Length
Resolution: 1
SAE SPN: not specified
Range: -2147483648 - **********
Submit by: 11783-Part 10 Task Force
Submit Date: 2005-01-20
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 135 Device Element Offset Y
Definition: Y direction offset of a DeviceElement relative to a Device.
Comment: This DDE was a result of Jan 2005 Task Controller meeting.
Typically used by Device Classes: 
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
Unit: mm - Length
Resolution: 1
SAE SPN: not specified
Range: -2147483648 - **********
Submit by: 11783-Part 10 Task Force
Submit Date: 2005-01-20
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 136 Device Element Offset Z
Definition: Z direction offset of a DeviceElement relative to a Device.
Comment: This DDE was a result of Jan 2005 Task Controller meeting.
Typically used by Device Classes: 
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
Unit: mm - Length
Resolution: 1
SAE SPN: not specified
Range: -2147483648 - **********
Submit by: 11783-Part 10 Task Force
Submit Date: 2005-01-20
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 137 Device Volume Capacity
Definition: DeviceElement Volume Capacity, dimension of a DeviceElement
Comment: This DDE was a result of Jan 2005 Task Controller meeting.
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
Unit: ml - Capacity large
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: 11783-Part 10 Task Force
Submit Date: 2005-01-20
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 138 Device Mass Capacity
Definition: DeviceElement Mass Capacity, dimension of a DeviceElement
Comment: This DDE was a result of Jan 2005 Task Controller meeting.
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
Unit: g - Mass large
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: 11783-Part 10 Task Force
Submit Date: 2005-01-20
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 139 Device Count Capacity
Definition: DeviceElement Count Capacity, dimension of a DeviceElement
Comment: This DDE was a result of Jan 2005 Task Controller meeting.
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
Unit: # - Quantity/Count
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: 11783-Part 10 Task Force
Submit Date: 2005-01-20
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-02-02
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 140 Setpoint Percentage Application Rate
Definition: Application Rate expressed as percentage
Comment: This DDE was a result of Jan 2005 Task Controller meeting.
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
10 - Irrigation
Unit: ppm - Parts per million
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: 11783-Part 10 Task Force
Submit Date: 2005-01-20
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-01-26
Status Comments: 
Attachments: 
none



DD Entity: 141 Actual Work State
Definition: Actual Work State, 2 bits defined as 00=disabled/off, 01=enabled/on, 10=error, 11=undefined/not installed
Comment: See the DDI 289 attachment "ISO11783-11-DDI-289-SetpointWorkState" for implementation guidelines.

This DDE has been revised in 2012 to be used as the Actual Work State. A separate Setpoint Work State was added to the data dictionary at that time.

Original comment: this DDE was a result of March 2005 TF10 meeting.
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
17 - Sensor System
Unit: n.a. - 
Resolution: 1
SAE SPN: not specified
Range: 0 - 3
Submit by: 11783-Part 10 Task Force
Submit Date: 2005-03-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-04-02
Status Comments: 
Attachments: 
none



DD Entity: 142 Physical Setpoint Time Latency
Definition: The Setpoint Value Latency Time is the time lapse between the moment of receival of a setpoint value command by the working set and the moment this setpoint value is physically applied on the device. That means if the setpoint value is communicated on the network (CAN bus) but the system needs 2 seconds to adjust the value physically on the desired unit (device element) then the Setpoint Latency Time is 2 seconds. 
The setpoint time latency value can only be positive.
Comment: The use of this DDE is to inform the overall system (e.g. Dektop Software, Task Controller) how the system works. The TC shall not shift this information into log files nor shall the device do that when sending actual values.
Typically used by Device Classes: 
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
14 - Special Crops
Unit: ms - Time
Resolution: 1
SAE SPN: not specified
Range: -2147483648 - **********
Submit by: 11783-Part 10 Task Force
Submit Date: 2005-03-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 2
Current Status: ISO-Approved
Status Date: 2011-04-04
Status Comments: 
Attachments: 
none



DD Entity: 143 Physical Actual Value Time Latency
Definition: The Actual Value Latency Time is the time lapse between the moment this actual value is physically applied on the device, and the moment that this actual value is communicated to the Task Controller. That means if the system needs 2 seconds to calculate or measure a value before communicating it on the network, then the Actual Latency Time value is 2 seconds.

Depending of the system characteristics the latency time could be negative or positive.

In case where the system communicates an actual value before the actual value has been physically applied the latency value should be positive.

In case where the system communicates an actual value after the actual value has been physically applied the latency value should be negative.
Comment: The use of this DDE is to inform the overall system (e.g. Dektop Software, Task Controller) how the system works. The TC shall not shift this information into log files nor shall the device do that when sending actual values.

Example for a positive value:

A seed flow sensor is placed at the start of the seed tube. At the moment the sensor measures seed flow X, it takes Y seconds for this flow to reach the coulters. The measured value is ahead of the physical value of the unit (coulters). So the latency could be plus 2 seconds.

Example for a negative value:

A flow sensor has a delay in its response to a flow change that means it takes Y seconds to realize the change. At the moment the sensor measures flow X, the flow is already present for Y seconds on the physical unit. The actual value is ahead of measured value. So the latency could be minus 2 seconds.
Typically used by Device Classes: 
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
14 - Special Crops
Unit: ms - Time
Resolution: 1
SAE SPN: not specified
Range: -2147483648 - **********
Submit by: 11783-Part 10 Task Force
Submit Date: 2005-03-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 2
Current Status: ISO-Approved
Status Date: 2011-04-04
Status Comments: 
Attachments: 
none



DD Entity: 144 Yaw Angle
Definition: Pivot / Yaw Angle of a DeviceElement
Comment: This DDE was a result of March 2005 TF10 meeting
Typically used by Device Classes: 

Unit: ° - Angle
Resolution: 0,001
SAE SPN: not specified
Range: -180000 - 180000
Submit by: 11783-Part 10 Task Force
Submit Date: 2005-03-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-05-09
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 145 Roll Angle
Definition: Roll Angle of a DeviceElement
Comment: This DDE was a result of March 2005 TF10 meeting
Typically used by Device Classes: 

Unit: ° - Angle
Resolution: 0,001
SAE SPN: not specified
Range: -180000 - 180000
Submit by: 11783-Part 10 Task Force
Submit Date: 2005-03-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-05-09
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 146 Pitch Angle
Definition: Pitch Angle of a DeviceElement
Comment: This DDE was a result of March 2005 TF10 meeting
Typically used by Device Classes: 

Unit: ° - Angle
Resolution: 0,001
SAE SPN: not specified
Range: -180000 - 180000
Submit by: 11783-Part 10 Task Force
Submit Date: 2005-03-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-05-09
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 147 Log Count
Definition: Log Counter, may be used to control data log record generation on a Task Controller
Comment: This DDE was a result of March 2005 TF10 meeting
Typically used by Device Classes: 
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
Unit: n.a. - 
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: 11783-Part 10 Task Force
Submit Date: 2005-03-01
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-05-09
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 148 Total Fuel Consumption
Definition: Accumulated Fuel Consumption as Counter
Comment: 
Typically used by Device Classes: 
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
Unit: ml - Capacity large
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Stephan Zelleröhr
Submit Date: 2005-04-12
Submit Company: 103 - Agrocom GmbH & Co. Agrarsystem KG
Revision Number: 1
Current Status: ISO-Published
Status Date: 2005-05-09
Status Comments: DDEs have been moved to published for creating the new Annex A version.
Attachments: 
none



DD Entity: 149 Instantaneous Fuel Consumption per Time
Definition: Fuel consumption per time
Comment: 
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
Unit: mm³/s - Flow
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Stephan Zelleröhr
Submit Date: 2005-04-12
Submit Company: 103 - Agrocom GmbH & Co. Agrarsystem KG
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2009-02-05
Status Comments: Added "Instantaneous" for clarification
Attachments: 
none



DD Entity: 150 Instantaneous Fuel Consumption per Area
Definition: Fuel consumption per area
Comment: 
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
Unit: mm³/m² - Capacity per area unit
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Stephan Zelleröhr
Submit Date: 2005-04-12
Submit Company: 103 - Agrocom GmbH & Co. Agrarsystem KG
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2009-02-05
Status Comments: Added "Instantaneous" for clarification.
Attachments: 
none



DD Entity: 151 Instantaneous Area Per Time Capacity
Definition: Area per time capacity
Comment:  
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
Unit: mm²/s - Area per time unit
Resolution: 1
SAE SPN: not specified
Range: 0 - **********
Submit by: Stephan Zelleröhr
Submit Date: 2005-04-12
Submit Company: 103 - Agrocom GmbH & Co. Agrarsystem KG
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2009-02-05
Status Comments: Added "Instantaneous" for clarification.
Attachments: 
none



DD Entity: 153 Actual Normalized Difference Vegetative Index (NDVI)
Definition: The Normalized Difference Vegetative Index (NDVI) computed from crop reflectances as the difference between NIR reflectance in the 780 to 880 nm band and red reflectance in the 640 to 680 nm band divided by the sum of the NIR and red reflectance in the same bands.
Comment: Document attached.
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
10 - Irrigation
Unit: n.a. - 
Resolution: 0,001
SAE SPN: TBD
Range: -1 - 1
Submit by: Marvin Stone
Submit Date: 2008-04-28
Submit Company: 39 - Microfirm Inc.
Revision Number: 1
Current Status: ISO-Approved
Status Date: 
Status Comments: 
Attachments: 
http://dictionary.isobus.net/isobus/attachments/173/NDVI_definition_summary.pdf



DD Entity: 154 Physical Object Length
Definition: Length of device element (dimension along the X-axis)
Comment: The reference point of the device element shall be located in the center of the device element
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
Unit: mm - Length
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Hans Jürgen Nissen
Submit Date: 2008-12-03
Submit Company: 33 - John Deere
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2009-02-05
Status Comments: 
Attachments: 
none



DD Entity: 155 Physical Object Width
Definition: Width of device element (dimension along the Y-axis)
Comment: The reference point of the device element shall be located in the center of the device element
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
Unit: mm - Length
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Hans Jürgen Nissen
Submit Date: 2008-12-03
Submit Company: 33 - John Deere
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2009-02-05
Status Comments: 
Attachments: 
none



DD Entity: 156 Physical Object Height
Definition: Height of device element (dimension along the Z-axis)
Comment: The reference point of the device element shall be located in the center of the device element
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
Unit: mm - Length
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Hans Jürgen Nissen
Submit Date: 2008-12-03
Submit Company: 33 - John Deere
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2009-02-05
Status Comments: 
Attachments: 
none



DD Entity: 157 Connector Type
Definition: Specification of the type of coupler. The value definitions are:
0 = unknown (default),
1 = ISO 6489-3 Tractor drawbar,
2 = ISO 730 Three-point-hitch semi-mounted,
3 = ISO 730 Three-point-hitch mounted,
4 = ISO 6489-1 Hitch-hook,
5 = ISO 6489-2 Clevis coupling 40,
6 = ISO 6489-4 Piton type coupling,
7 = ISO 6489-5 CUNA hitch, or ISO 5692-2 Pivot wagon hitch
8 = ISO 24347 Ball type hitch
all other values are reserved for future assignments.
Comment: This DDE allows systems to automatically select the connection between devices. For instance, when 2 devices are on the network that declare device elements with the same connector type, the system can connect them accordingly.
This DDE shall be used with the Device Element of type "Connector" only.
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
Unit: n.a. - 
Resolution: 1
SAE SPN: 
Range: 0 - 8
Submit by: Hans Jürgen Nissen
Submit Date: 2008-12-03
Submit Company: 33 - John Deere
Revision Number: 2
Current Status: ISO-Published
Status Date: 2012-03-09
Status Comments: 
Attachments: 
none



DD Entity: 158 Prescription Control State
Definition: Defines and synchronise the actual state of the prescription system. The state is represented by the lowest significant 2 bits in the lowest significant byte of the process data value: Byte 1: bit 0-1: 00 = manual/off, 01 = auto/on, 10 = error indicator, 11 = undefined/not installed. bits 2-7: reserved set to 0. Byte 2-4: reserved set to 0.

The DDI shall support the On Change trigger so that the TC is able to get informed when the value gets changed by the Working Set Master. The TC shall active this trigger when using the DDI.

See attachment for more information
Comment: The prescription control master and its clients need to be synchronized in terms of their general state or activation by the user (System activated/deactivated in individual setups). This DDE serves 2 purposes, one is to synchronize the prescription control state and the other is to enable a TC client to announce the support and initial state of its prescription control capabilities. Synchronisation by the prescription master (TC) is done by setting the prescription state of connected TC clients with a process data set value message with this DDE. It is recommended that TC clients reply their state immediately (within 250 ms) when such a message is received. The property flag "setable" and the trigger method "on change" shall be used with this DDE. The state "manual/off" indicates that the device is in manual state and will ignore all prescription commands. The "auto/on" state indicates that the client accepts the prescription commands as far as its overall process state allows.
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
Unit: n.a. - 
Resolution: 1
SAE SPN: 
Range: 0 - 3
Submit by: Matthias Meyer
Submit Date: 2008-11-07
Submit Company: 33 - John Deere
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2009-02-05
Status Comments: 
Attachments: 
http://dictionary.isobus.net/isobus/attachments/203/PCS Implementation Example.pdf



DD Entity: 159 Number of Sub-Units per Section
Definition: Specifies the number of sub-units for a section (e.g. number of nozzles per sprayer section or number of planter row units per metering device).
Comment: This DDE is used for objects which have further sub-units per section, which are of interest to the operator but not needed for the Task Controller operation itself. With this information and the overall width of the section the system can calculate for instance the row spacing without having individual objects for each row in the DCD.
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
Unit: # - Quantity/Count
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Jason Walter
Submit Date: 2008-12-03
Submit Company: 33 - John Deere
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2009-02-05
Status Comments: 
Attachments: 
none



DD Entity: 160 Section Control State
Definition: Specifies the actual state of section control. The value definitions are: Byte 1 (bitfield) Bit 0-1: 00 = manual/off, 01 = auto/on, 10 = error indicator, 11 = undefined/not installed. Bits 2-7: reserved, set to 0. Bytes 2-4: reserved, set to 0.

The DDI shall support the On Change trigger so that the TC is able to get informed when the value gets changed by the Working Set Master. The TC shall active this trigger when using the DDI.

See attachment for more information
Comment: In section control systems, the section control master and its clients need to be synchronized in terms of their general state or activation by the user (System activated/deactivated in individual setups). This DDE serves 2 purposes, one is to synchronize the section control state and the other is to enable a TC client to announce the support and initial state of its section control capabilities. Synchronisation by the section control master (TC) is done by setting the section control state of connected TC clients with a process data set value message with this DDE. It is recommended that TC clients reply their state immediately (within 250 ms) when such a message is received. The property flag "setable" and the trigger method "on change" should be used with this DDE. The state "manual/off" indicates that the device is in manual state and will ignore all control commands for section control. The "auto/on" state indicates that the client accepts the section control commands as far as its overall process state allows.

Listed below are 4 example Use Cases for this DDE:

Use case "Start up operation":
1. During a start up the implement shall set the SCS to ‘manual mode’.

Use case "Auto request from TC":
1. The implement receives an ‘auto’ request from TC.
2. The implement shall check whether all setup conditions are fulfilled to allow section control.
3. If this check is ok: The implement may respond with ‘auto mode’ and set its internal SCS client to ‘auto mode’.
4. If this check not ok: The implement shall respond with ‘manual mode’. The internal state is still in manual mode. The TC may inform the operator accordingly.  

Use case "Manual request from TC":
1. The implement receives a manual request from TC.
2. If the implement is still in ‘auto mode’ the implement shall set its internal SCS client to ‘manual mode’. 

Use case "Loss of requirements for auto mode":
1. The implement internal setup conditions don’t allow for automatic section control anymore.
2. The implement shall set the internal SCS client to ‘manual mode’.
3. The implement shall send the SCS to inform the TC accordingly. On reception of this ‘manual mode’ the TC/Section Control Master may inform the operator accordingly. 

Typically used by Device Classes: 
0 - Non-specific system
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
Unit: n.a. - 
Resolution: 1
SAE SPN: 
Range: 0 - 3
Submit by: Matthias Meyer
Submit Date: 2008-12-03
Submit Company: 33 - John Deere
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2009-02-05
Status Comments: 
Attachments: 
http://dictionary.isobus.net/isobus/attachments/181/SCS Implementation Example.pdf



DD Entity: 161 Actual Condensed Work State (1-16)
Definition: Combination of the actual work states of individual sections or units (e.g. nozzles) number 1 to 16 into a single actual work state of their parent device element. The actual condensed work state contains the child element actual work states, in the driving direction from left to right, where the leftmost child element actual work state are the 2 lowest significant bits of the Process Data Value. Each child device elements actual work state is represented by 2 bits and defined as: 00 = disabled/off, 01 = enabled/on, 10 = error indicator, 11 = undefined/not installed. In total 16 child device element actual work states can be contained in one actual condensed work state of their parent device element. If less than 16 child device element actual work states are available, then the unused bits shall be set to value 11 (not installed). When the parent device element contains the Actual Condensed Work State DDE, then the device descriptor shall not contain the individual actual work state DDEs of the child device elements.
Comment: See attachment "ISO11783-11-DDI-289-SetpointWorkState" for implementation guidelines.

This DDE is used to reduce number of messages. Individual work state messages for a many-sectioned device can potentially cause performance issues due to overloading communication. With this DDE, 16 work states can be sent via a single message, resulting in less traffic to convey on/off status for an entire device.
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
Unit: n.a. - 
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Adam Bogenrief
Submit Date: 2008-01-14
Submit Company: 33 - John Deere
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-04-02
Status Comments: 
Attachments: 
http://dictionary.isobus.net/isobus/attachments/183/ISO11783-11-DDI-289-SetpointWorkState-v1.pdf



DD Entity: 162 Actual Condensed Work State (17-32)
Definition: Combination of the actual work states of individual sections or units (e.g. nozzles) number 17 to 32 into a single actual work state of their parent device element. The actual condensed work state contains the child element actual work states, in the driving direction from left to right, where the leftmost child element actual work state are the 2 lowest significant bits of the Process Data Value. Each child device elements actual work state is represented by 2 bits and defined as: 00 = disabled/off, 01 = enabled/on, 10 = error indicator, 11 = undefined/not installed. In total 16 child device element actual work states can be contained in one actual condensed work state of their parent device element. If less than 16 child device element actual work states are available, then the unused bits shall be set to value 11 (not installed). When the parent device element contains the Actual Condensed Work State DDE, then the device descriptor shall not contain the individual actual work state DDEs of the child device elements.
Comment: See the DDI 161 attachment "ISO11783-11-DDI-289-SetpointWorkState" for implementation guidelines.

This DDE is used to reduce number of messages. Individual work state messages for a many-sectioned device can potentially cause performance issues due to overloading communication. With this DDE, 16 work states can be sent via a single message, resulting in less traffic to convey on/off status for an entire device.
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
Unit: n.a. - 
Resolution: 1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Adam Bogenrief
Submit Date: 2008-01-14
Submit Company: 33 - John Deere
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-04-02
Status Comments: 
Attachments: 
none



DD Entity: 163 Actual Condensed Work State (33-48)
Definition: Combination of the actual work states of individual sections or units (e.g. nozzles) number 33 to 48 into a single actual work state of their parent device element. The actual condensed work state contains the child element actual work states, in the driving direction from left to right, where the leftmost child element actual work state are the 2 lowest significant bits of the Process Data Value. Each child device elements actual work state is represented by 2 bits and defined as: 00 = disabled/off, 01 = enabled/on, 10 = error indicator, 11 = undefined/not installed. In total 16 child device element actual work states can be contained in one actual condensed work state of their parent device element. If less than 16 child device element actual work states are available, then the unused bits shall be set to value 11 (not installed). When the parent device element contains the Actual Condensed Work State DDE, then the device descriptor shall not contain the individual actual work state DDEs of the child device elements.
Comment: See the DDI 161 attachment "ISO11783-11-DDI-289-SetpointWorkState" for implementation guidelines.

This DDE is used to reduce number of messages. Individual work state messages for a many-sectioned device can potentially cause performance issues due to overloading communication. With this DDE, 16 work states can be sent via a single message, resulting in less traffic to convey on/off status for an entire device.
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
Unit: n.a. - 
Resolution: 1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Adam Bogenrief
Submit Date: 2008-01-14
Submit Company: 33 - John Deere
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-04-02
Status Comments: 
Attachments: 
none



DD Entity: 164 Actual Condensed Work State (49-64)
Definition: Combination of the actual work states of individual sections or units (e.g. nozzles) number 49 to 64 into a single actual work state of their parent device element. The actual condensed work state contains the child element actual work states, in the driving direction from left to right, where the leftmost child element actual work state are the 2 lowest significant bits of the Process Data Value. Each child device elements actual work state is represented by 2 bits and defined as: 00 = disabled/off, 01 = enabled/on, 10 = error indicator, 11 = undefined/not installed. In total 16 child device element actual work states can be contained in one actual condensed work state of their parent device element. If less than 16 child device element actual work states are available, then the unused bits shall be set to value 11 (not installed). When the parent device element contains the Actual Condensed Work State DDE, then the device descriptor shall not contain the individual actual work state DDEs of the child device elements.
Comment: See the DDI 161 attachment "ISO11783-11-DDI-289-SetpointWorkState" for implementation guidelines.

This DDE is used to reduce number of messages. Individual work state messages for a many-sectioned device can potentially cause performance issues due to overloading communication. With this DDE, 16 work states can be sent via a single message, resulting in less traffic to convey on/off status for an entire device.
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
Unit: n.a. - 
Resolution: 1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Adam Bogenrief
Submit Date: 2008-01-14
Submit Company: 33 - John Deere
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-04-02
Status Comments: 
Attachments: 
none



DD Entity: 165 Actual Condensed Work State (65-80)
Definition: Combination of the actual work states of individual sections or units (e.g. nozzles) number 65 to 80 into a single actual work state of their parent device element. The actual condensed work state contains the child element actual work states, in the driving direction from left to right, where the leftmost child element actual work state are the 2 lowest significant bits of the Process Data Value. Each child device elements actual work state is represented by 2 bits and defined as: 00 = disabled/off, 01 = enabled/on, 10 = error indicator, 11 = undefined/not installed. In total 16 child device element actual work states can be contained in one actual condensed work state of their parent device element. If less than 16 child device element actual work states are available, then the unused bits shall be set to value 11 (not installed). When the parent device element contains the Actual Condensed Work State DDE, then the device descriptor shall not contain the individual actual work state DDEs of the child device elements.
Comment: See the DDI 161 attachment "ISO11783-11-DDI-289-SetpointWorkState" for implementation guidelines.

This DDE is used to reduce number of messages. Individual work state messages for a many-sectioned device can potentially cause performance issues due to overloading communication. With this DDE, 16 work states can be sent via a single message, resulting in less traffic to convey on/off status for an entire device.
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
Unit: n.a. - 
Resolution: 1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Adam Bogenrief
Submit Date: 2008-01-14
Submit Company: 33 - John Deere
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-04-02
Status Comments: 
Attachments: 
none



DD Entity: 166 Actual Condensed Work State (81-96)
Definition: Combination of the actual work states of individual sections or units (e.g. nozzles) number 81 to 96 into a single actual work state of their parent device element. The actual condensed work state contains the child element actual work states, in the driving direction from left to right, where the leftmost child element actual work state are the 2 lowest significant bits of the Process Data Value. Each child device elements actual work state is represented by 2 bits and defined as: 00 = disabled/off, 01 = enabled/on, 10 = error indicator, 11 = undefined/not installed. In total 16 child device element actual work states can be contained in one actual condensed work state of their parent device element. If less than 16 child device element actual work states are available, then the unused bits shall be set to value 11 (not installed). When the parent device element contains the Actual Condensed Work State DDE, then the device descriptor shall not contain the individual actual work state DDEs of the child device elements.
Comment: See the DDI 161 attachment "ISO11783-11-DDI-289-SetpointWorkState" for implementation guidelines.

This DDE is used to reduce number of messages. Individual work state messages for a many-sectioned device can potentially cause performance issues due to overloading communication. With this DDE, 16 work states can be sent via a single message, resulting in less traffic to convey on/off status for an entire device.
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
Unit: n.a. - 
Resolution: 1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Adam Bogenrief
Submit Date: 2008-01-14
Submit Company: 33 - John Deere
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-04-02
Status Comments: 
Attachments: 
none



DD Entity: 167 Actual Condensed Work State (97-112)
Definition: Combination of the actual work states of individual sections or units (e.g. nozzles) number 97 to 112 into a single actual work state of their parent device element. The actual condensed work state contains the child element actual work states, in the driving direction from left to right, where the leftmost child element actual work state are the 2 lowest significant bits of the Process Data Value. Each child device elements actual work state is represented by 2 bits and defined as: 00 = disabled/off, 01 = enabled/on, 10 = error indicator, 11 = undefined/not installed. In total 16 child device element actual work states can be contained in one actual condensed work state of their parent device element. If less than 16 child device element actual work states are available, then the unused bits shall be set to value 11 (not installed). When the parent device element contains the Actual Condensed Work State DDE, then the device descriptor shall not contain the individual actual work state DDEs of the child device elements.
Comment: See the DDI 161 attachment "ISO11783-11-DDI-289-SetpointWorkState" for implementation guidelines.

This DDE is used to reduce number of messages. Individual work state messages for a many-sectioned device can potentially cause performance issues due to overloading communication. With this DDE, 16 work states can be sent via a single message, resulting in less traffic to convey on/off status for an entire device.
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
Unit: n.a. - 
Resolution: 1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Adam Bogenrief
Submit Date: 2008-01-14
Submit Company: 33 - John Deere
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-04-02
Status Comments: 
Attachments: 
none



DD Entity: 168 Actual Condensed Work State (113-128)
Definition: Combination of the actual work states of individual sections or units (e.g. nozzles) number 113 to 128 into a single actual work state of their parent device element. The actual condensed work state contains the child element actual work states, in the driving direction from left to right, where the leftmost child element actual work state are the 2 lowest significant bits of the Process Data Value. Each child device elements actual work state is represented by 2 bits and defined as: 00 = disabled/off, 01 = enabled/on, 10 = error indicator, 11 = undefined/not installed. In total 16 child device element actual work states can be contained in one actual condensed work state of their parent device element. If less than 16 child device element actual work states are available, then the unused bits shall be set to value 11 (not installed). When the parent device element contains the Actual Condensed Work State DDE, then the device descriptor shall not contain the individual actual work state DDEs of the child device elements.
Comment: See the DDI 161 attachment "ISO11783-11-DDI-289-SetpointWorkState" for implementation guidelines.

This DDE is used to reduce number of messages. Individual work state messages for a many-sectioned device can potentially cause performance issues due to overloading communication. With this DDE, 16 work states can be sent via a single message, resulting in less traffic to convey on/off status for an entire device.
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
Unit: n.a. - 
Resolution: 1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Adam Bogenrief
Submit Date: 2008-01-14
Submit Company: 33 - John Deere
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-04-02
Status Comments: 
Attachments: 
none



DD Entity: 169 Actual Condensed Work State (129-144)
Definition: Combination of the actual work states of individual sections or units (e.g. nozzles) number 129 to 144 into a single actual work state of their parent device element. The actual condensed work state contains the child element actual work states, in the driving direction from left to right, where the leftmost child element actual work state are the 2 lowest significant bits of the Process Data Value. Each child device elements actual work state is represented by 2 bits and defined as: 00 = disabled/off, 01 = enabled/on, 10 = error indicator, 11 = undefined/not installed. In total 16 child device element actual work states can be contained in one actual condensed work state of their parent device element. If less than 16 child device element actual work states are available, then the unused bits shall be set to value 11 (not installed). When the parent device element contains the Actual Condensed Work State DDE, tthen he device descriptor shall not contain the individual actual work state DDEs of the child device elements.
Comment: See the DDI 161 attachment "ISO11783-11-DDI-289-SetpointWorkState" for implementation guidelines.

This DDE is used to reduce number of messages. Individual work state messages for a many-sectioned device can potentially cause performance issues due to overloading communication. With this DDE, 16 work states can be sent via a single message, resulting in less traffic to convey on/off status for an entire device.
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
Unit: n.a. - 
Resolution: 1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Adam Bogenrief
Submit Date: 2008-01-14
Submit Company: 33 - John Deere
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-04-02
Status Comments: 
Attachments: 
none



DD Entity: 170 Actual Condensed Work State (145-160)
Definition: Combination of the actual work states of individual sections or units (e.g. nozzles) number 145 to 160 into a single actual work state of their parent device element. The actual condensed work state contains the child element actual work states, in the driving direction from left to right, where the leftmost child element actual work state are the 2 lowest significant bits of the Process Data Value. Each child device elements actual work state is represented by 2 bits and defined as: 00 = disabled/off, 01 = enabled/on, 10 = error indicator, 11 = undefined/not installed. In total 16 child device element actual work states can be contained in one actual condensed work state of their parent device element. If less than 16 child device element actual work states are available, then the unused bits shall be set to value 11 (not installed). When the parent device element contains the Actual Condensed Work State DDE, then the device descriptor shall not contain the individual actual work state DDEs of the child device elements.
Comment: See the DDI 161 attachment "ISO11783-11-DDI-289-SetpointWorkState" for implementation guidelines.

This DDE is used to reduce number of messages. Individual work state messages for a many-sectioned device can potentially cause performance issues due to overloading communication. With this DDE, 16 work states can be sent via a single message, resulting in less traffic to convey on/off status for an entire device.
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
Unit: n.a. - 
Resolution: 1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Adam Bogenrief
Submit Date: 2008-01-14
Submit Company: 33 - John Deere
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-04-02
Status Comments: 
Attachments: 
none



DD Entity: 171 Actual Condensed Work State (161-176)
Definition: Combination of the actual work states of individual sections or units (e.g. nozzles) number 161 to 176 into a single actual work state of their parent device element. The actual condensed work state contains the child element actual work states, in the driving direction from left to right, where the leftmost child element actual work state are the 2 lowest significant bits of the Process Data Value. Each child device elements actual work state is represented by 2 bits and defined as: 00 = disabled/off, 01 = enabled/on, 10 = error indicator, 11 = undefined/not installed. In total 16 child device element actual work states can be contained in one actual condensed work state of their parent device element. If less than 16 child device element actual work states are available, then the unused bits shall be set to value 11 (not installed). When the parent device element contains the Actual Condensed Work State DDE, then the device descriptor shall not contain the individual actual work state DDEs of the child device elements.
Comment: See the DDI 161 attachment "ISO11783-11-DDI-289-SetpointWorkState" for implementation guidelines.

This DDE is used to reduce number of messages. Individual work state messages for a many-sectioned device can potentially cause performance issues due to overloading communication. With this DDE, 16 work states can be sent via a single message, resulting in less traffic to convey on/off status for an entire device.
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
Unit: n.a. - 
Resolution: 1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Adam Bogenrief
Submit Date: 2008-01-14
Submit Company: 33 - John Deere
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-04-02
Status Comments: 
Attachments: 
none



DD Entity: 172 Actual Condensed Work State (177-192)
Definition: Combination of the actual work states of individual sections or units (e.g. nozzles) number 177 to 192 into a single actual work state of their parent device element. The actual condensed work state contains the child element actual work states, in the driving direction from left to right, where the leftmost child element actual work state are the 2 lowest significant bits of the Process Data Value. Each child device elements actual work state is represented by 2 bits and defined as: 00 = disabled/off, 01 = enabled/on, 10 = error indicator, 11 = undefined/not installed. In total 16 child device element actual work states can be contained in one actual condensed work state of their parent device element. If less than 16 child device element actual work states are available, then the unused bits shall be set to value 11 (not installed). When the parent device element contains the Actual Condensed Work State DDE, then the device descriptor shall not contain the individual actual work state DDEs of the child device elements.
Comment: See the DDI 161 attachment "ISO11783-11-DDI-289-SetpointWorkState" for implementation guidelines.

This DDE is used to reduce number of messages. Individual work state messages for a many-sectioned device can potentially cause performance issues due to overloading communication. With this DDE, 16 work states can be sent via a single message, resulting in less traffic to convey on/off status for an entire device.
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
Unit: n.a. - 
Resolution: 1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Adam Bogenrief
Submit Date: 2008-01-14
Submit Company: 33 - John Deere
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-04-02
Status Comments: 
Attachments: 
none



DD Entity: 173 Actual Condensed Work State (193-208)
Definition: Combination of the actual work states of individual sections or units (e.g. nozzles) number 193 to 208 into a single actual work state of their parent device element. The actual condensed work state contains the child element actual work states, in the driving direction from left to right, where the leftmost child element actual work state are the 2 lowest significant bits of the Process Data Value. Each child device elements actual work state is represented by 2 bits and defined as: 00 = disabled/off, 01 = enabled/on, 10 = error indicator, 11 = undefined/not installed. In total 16 child device element actual work states can be contained in one actual condensed work state of their parent device element. If less than 16 child device element actual work states are available, then the unused bits shall be set to value 11 (not installed). When the parent device element contains the Actual Condensed Work State DDE, then the device descriptor shall not contain the individual actual work state DDEs of the child device elements.
Comment: See the DDI 161 attachment "ISO11783-11-DDI-289-SetpointWorkState" for implementation guidelines.

This DDE is used to reduce number of messages. Individual work state messages for a many-sectioned device can potentially cause performance issues due to overloading communication. With this DDE, 16 work states can be sent via a single message, resulting in less traffic to convey on/off status for an entire device.
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
Unit: n.a. - 
Resolution: 1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Adam Bogenrief
Submit Date: 2008-01-14
Submit Company: 33 - John Deere
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-04-02
Status Comments: 
Attachments: 
none



DD Entity: 174 Actual Condensed Work State (209-224)
Definition: Combination of the actual work states of individual sections or units (e.g. nozzles) number 209 to 224 into a single actual work state of their parent device element. The actual condensed work state contains the child element actual work states, in the driving direction from left to right, where the leftmost child element actual work state are the 2 lowest significant bits of the Process Data Value. Each child device elements actual work state is represented by 2 bits and defined as: 00 = disabled/off, 01 = enabled/on, 10 = error indicator, 11 = undefined/not installed. In total 16 child device element actual work states can be contained in one actual condensed work state of their parent device element. If less than 16 child device element actual work states are available, then the unused bits shall be set to value 11 (not installed). When the parent device element contains the Actual Condensed Work State DDE, then the device descriptor shall not contain the individual actual work state DDEs of the child device elements.
Comment: See the DDI 161 attachment "ISO11783-11-DDI-289-SetpointWorkState" for implementation guidelines.

This DDE is used to reduce number of messages. Individual work state messages for a many-sectioned device can potentially cause performance issues due to overloading communication. With this DDE, 16 work states can be sent via a single message, resulting in less traffic to convey on/off status for an entire device.
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
Unit: n.a. - 
Resolution: 1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Adam Bogenrief
Submit Date: 2008-01-14
Submit Company: 33 - John Deere
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-04-02
Status Comments: 
Attachments: 
none



DD Entity: 175 Actual Condensed Work State (225-240)
Definition: Combination of the actual work states of individual sections or units (e.g. nozzles) number 225 to 240 into a single actual work state of their parent device element. The actual condensed work state contains the child element actual work states, in the driving direction from left to right, where the leftmost child element actual work state are the 2 lowest significant bits of the Process Data Value. Each child device elements actual work state is represented by 2 bits and defined as: 00 = disabled/off, 01 = enabled/on, 10 = error indicator, 11 = undefined/not installed. In total 16 child device element actual work states can be contained in one actual condensed work state of their parent device element. If less than 16 child device element actual work states are available, then the unused bits shall be set to value 11 (not installed). When the parent device element contains the Actual Condensed Work State DDE, then the device descriptor shall not contain the individual actual work state DDEs of the child device elements.
Comment: See the DDI 161 attachment "ISO11783-11-DDI-289-SetpointWorkState" for implementation guidelines.

This DDE is used to reduce number of messages. Individual work state messages for a many-sectioned device can potentially cause performance issues due to overloading communication. With this DDE, 16 work states can be sent via a single message, resulting in less traffic to convey on/off status for an entire device.
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
Unit: n.a. - 
Resolution: 1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Adam Bogenrief
Submit Date: 2008-01-14
Submit Company: 33 - John Deere
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-04-02
Status Comments: 
Attachments: 
none



DD Entity: 176 Actual Condensed Work State (241-256)
Definition: Combination of the actual work states of individual sections or units (e.g. nozzles) number 241 to 256 into a single actual work state of their parent device element. The actual condensed work state contains the child element actual work states, in the driving direction from left to right, where the leftmost child element actual work state are the 2 lowest significant bits of the Process Data Value. Each child device elements actual work state is represented by 2 bits and defined as: 00 = disabled/off, 01 = enabled/on, 10 = error indicator, 11 = undefined/not installed. In total 16 child device element actual work states can be contained in one actual condensed work state of their parent device element. If less than 16 child device element actual work states are available, then the unused bits shall be set to value 11 (not installed). When the parent device element contains the Actual Condensed Work State DDE, then the device descriptor shall not contain the individual actual work state DDEs of the child device elements.
Comment: See the DDI 161 attachment "ISO11783-11-DDI-289-SetpointWorkState" for implementation guidelines.

This DDE is used to reduce number of messages. Individual work state messages for a many-sectioned device can potentially cause performance issues due to overloading communication. With this DDE, 16 work states can be sent via a single message, resulting in less traffic to convey on/off status for an entire device.
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
Unit: n.a. - 
Resolution: 1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Adam Bogenrief
Submit Date: 2008-01-14
Submit Company: 33 - John Deere
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-04-02
Status Comments: 
Attachments: 
none



DD Entity: 177 Actual length of cut
Definition: Actual length of cut for harvested material, e.g. Forage Harvester or Tree Harvester.
Comment: none.
Typically used by Device Classes: 
0 - Non-specific system
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
Unit: mm - Length
Resolution: 0,001
SAE SPN: 
Range: 0 - 2147483,647
Submit by: Hans Jürgen Nissen
Submit Date: 2008-09-22
Submit Company: 33 - John Deere
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2009-02-05
Status Comments: 
Attachments: 
none



DD Entity: 178 Element Type Instance
Definition: This DDI is used to enumerate and identify multiple device elements (DET) of the same type within one Device Description object pool. The value of this DDI is independent of the DET number. The combination of device element type and value of Element Type Instance ETI represents a unique object inside the device description object pool and therefore shall exist only once per object pool. Recommendation: The definition of the device elements should be made from left to right direction or from front to back direction.  When in a matrix, count left-to-right first, then front-to-back and at last top-to-bottom. See attachment for more information.
Comment: This DDE allows the system to communicate with a device element object independent of the device element number. The same tank of a seeder for instance could have various device element numbers based upon the DDD. The DDD structure may change during setup the implement. In this case a unique implement tank might has a different element number as before. Particular if the Task Controller (TC) use a user interface to display and change data by the operator. Therefore the TC needs clear or rather unique device element information. Assign this DDE for instance to a DET of type bin. This number can be displayed to the operator while it may be printed physically at the bin. The ETI number range from 0 to 65533 inside the object pool corresponds to a displayed value from 1 to 65534 on a user interface or physical device.
Typically used by Device Classes: 
Not Assigned
Unit: n.a. - 
Resolution: 1
SAE SPN: 
Range: 0 - 65533
Submit by: Matthias Meyer
Submit Date: 2010-01-15
Submit Company: 33 - John Deere
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2010-03-12
Status Comments: 
Attachments: 
http://dictionary.isobus.net/isobus/attachments/204/Mixed product implement DDD recommendation 20100118.pdf



DD Entity: 179 Actual Cultural Practice
Definition: This DDI is used to define the current cultural practice which is performed by an individual device operation. For instance a planter/seeder could provide a sowing and a fertilizing operation at the same time.
 
The cultural practice value definitions are: 0=Unknown, 1=Fertilizing, 2=Sowing and Planting, 3=Crop Protection, 4=Tillage, 5=Baling, 6=Mowing, 7=Wrapping, 8=Harvesting, 9=Forage Harvesting, 10=Transport, 11=Swathing, 12-255=Reserved for future Assignment

See attachment for more information.
Comment: Implements as Planter or Seeder which provides more than one product application need an option to sign the cultural practice that is performed by each operation. More then ever if the applied products have the same unit type. As for instance a seeder provides a sowing and fertilizing operation which have both mass per area as unit defined. In this case it is not clear to the TC that the second operation is a fertilizing operation. Particular if the TC owns a user interface to display these information to the operator. Through this DDE the TC user interface can display the appropriate information. Adding this DDI to the device element of type device the main cultural practice of the device could be defined. For instance a baler will claim on the bus as forage device class and have set the actual cultural practice as baling.
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
Unit: n.a. - 
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Matthias Meyer
Submit Date: 2010-01-15
Submit Company: 33 - John Deere
Revision Number: 2
Current Status: ISO-Published
Status Date: 2015-01-28
Status Comments: 
Attachments: 
http://dictionary.isobus.net/isobus/attachments/205/Multiple and Single Product Implement Description.pdf



DD Entity: 180 Device Reference Point (DRP) to Ground distance
Definition: This DDI is used to specify the distance from the Device Reference Point (DRP) down to the ground surface. The DRP to Ground DDI shall be attached only to the Device Element (DET) with element number zero.
Comment: Depending on the application it might be required to know the distance of a device element down to the ground. All device element offsets refer to the DRP which is the centre of the device coordinate system and usually not at the ground surface. For instance the DRP of a tractor is the centre of the rear axle. In this case the distance from a GPS receiver (DET of type navigation reference) attached on the roof of the cab is calculated through sum up the Z-offset of the DET and the distance of the DRP to ground. The value of the DRP in this case is equivalent to the radius of the rolling wheel which is attached on the tractor rear axle.
Typically used by Device Classes: 

Unit: mm - Length
Resolution: 1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Matthias Meyer
Submit Date: 2010-01-15
Submit Company: 33 - John Deere
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2010-03-12
Status Comments: 
Attachments: 
none



DD Entity: 181 Dry Mass Per Area Yield
Definition: Actual Dry Mass Per Area Yield. The definition of dry mass is the mass with a reference moisture specified by DDI 184.
Comment: The earlier defined DDI 84 is the mass per area that is measured on e.g. harvesting equipment as a mass including a possibly unknown moisture percentage. This DDI 181 is the mass per area yield, corrected to a reference moisture.
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
Unit: mg/m² - Mass per area unit
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Lyle Jensen
Submit Date: 2010-01-29
Submit Company: 102 - AGCO GmbH & Co
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2010-03-12
Status Comments: 
Attachments: 
none



DD Entity: 182 Dry Mass Per Time Yield
Definition: Actual Dry Mass Per Time Yield. The definition of dry mass is the mass with a reference moisture specified by DDI 184.
Comment: The earlier defined DDI 87 is the mass per time that is measured on e.g. harvesting equipment as a mass including a possibly unknown moisture percentage. This DDI 182 is the mass per time yield, corrected to a reference moisture.
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
Unit: mg/s - Mass flow
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Lyle Jensen
Submit Date: 2010-01-29
Submit Company: 102 - AGCO GmbH & Co
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2010-03-12
Status Comments: 
Attachments: 
none



DD Entity: 183 Yield Total Dry Mass
Definition: Accumulated Yield specified as dry mass. The definition of dry mass is the mass with a reference moisture specified by DDI 184.
Comment: The earlier defined DDI 90 is considered to be the total mass that is measured on e.g. harvesting equipment as a mass including a possibly unknown moisture percentage. This DDI 183 is the yield total mass, corrected to a reference moisture.
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
Unit: kg - Mass
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Lyle Jensen
Submit Date: 2010-01-29
Submit Company: 102 - AGCO GmbH & Co
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2010-03-12
Status Comments: 
Attachments: 
none



DD Entity: 184 Reference Moisture For Dry Mass
Definition: Moisture percentage used for the dry mass DDIs 181, 182 and 183.
Comment: Example: this definition is similar to the "Standard Payable Moisture" term used by farmers.
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
Unit: ppm - Parts per million
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Lyle Jensen
Submit Date: 2010-01-29
Submit Company: 102 - AGCO GmbH & Co
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2010-03-12
Status Comments: 
Attachments: 
none



DD Entity: 185 Seed Cotton Mass Per Area Yield
Definition: Seed cotton yield as mass per area, not corrected for a possibly included lint percantage.
Comment: This Seed Cotton Mass Per Area Yield is the mass of the raw harvested cotton product as it is measured on e.g. harvesting equipment.
Typically used by Device Classes: 
7 - Harvesters
14 - Special Crops
Unit: mg/m² - Mass per area unit
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Andy Beck
Submit Date: 2010-02-26
Submit Company: 33 - John Deere
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2010-03-12
Status Comments: 
Attachments: 
none



DD Entity: 186 Lint Cotton Mass Per Area Yield
Definition: Lint cotton yield as mass per area.
Comment: This Lint Cotton Mass Per Area Yield is the mass of the lint after it has been removed from the seed cotton at a cotton gin. Calculated by use of the Lint Turnout Percentage.
Typically used by Device Classes: 
7 - Harvesters
14 - Special Crops
Unit: mg/m² - Mass per area unit
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Andy Beck
Submit Date: 2010-02-26
Submit Company: 33 - John Deere
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2010-03-12
Status Comments: 
Attachments: 
none



DD Entity: 187 Seed Cotton Mass Per Time Yield
Definition: Seed cotton yield as mass per time, not corrected for a possibly included lint percantage.
Comment: This Seed Cotton Mass Per Time Yield is the mass of the raw harvested cotton product as it is measured on e.g. harvesting equipment.
Typically used by Device Classes: 
7 - Harvesters
14 - Special Crops
Unit: mg/s - Mass flow
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Andy Beck
Submit Date: 2010-02-26
Submit Company: 33 - John Deere
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2010-03-12
Status Comments: 
Attachments: 
none



DD Entity: 188 Lint Cotton Mass Per Time Yield
Definition: Lint cotton yield as mass per time.
Comment: This Lint Cotton Mass Per Time Yield is the mass of the lint after it has been removed from the seed cotton at a cotton gin. Calculated by use of the Lint Turnout Percentage.
Typically used by Device Classes: 
7 - Harvesters
14 - Special Crops
Unit: mg/s - Mass flow
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Andy Beck
Submit Date: 2010-02-26
Submit Company: 33 - John Deere
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2010-03-12
Status Comments: 
Attachments: 
none



DD Entity: 189 Yield Total Seed Cotton Mass
Definition: Accumulated yield specified as seed cotton mass, not corrected for a possibly included lint percantage.
Comment: This Yield Total Seed Cotton Mass is the total mass of the raw harvested cotton product as it is measured on e.g. harvesting equipment.
Typically used by Device Classes: 
7 - Harvesters
14 - Special Crops
Unit: kg - Mass
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Andy Beck
Submit Date: 2010-02-26
Submit Company: 33 - John Deere
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2010-03-12
Status Comments: 
Attachments: 
none



DD Entity: 190 Yield Total Lint Cotton Mass
Definition: Accumulated yield specified as lint cotton mass.
Comment: This Yield Total Lint Cotton Mass is the total lint cotton mass, after it has been removed from the total seed cotton at a cotton gin.
Typically used by Device Classes: 
7 - Harvesters
14 - Special Crops
Unit: kg - Mass
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Andy Beck
Submit Date: 2010-02-26
Submit Company: 33 - John Deere
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2010-03-12
Status Comments: 
Attachments: 
none



DD Entity: 191 Lint Turnout Percentage 
Definition: Percent of lint in the seed cotton.
Comment: 
Typically used by Device Classes: 
7 - Harvesters
14 - Special Crops
Unit: ppm - Parts per million
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Andy Beck
Submit Date: 2010-02-26
Submit Company: 33 - John Deere
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2010-03-12
Status Comments: 
Attachments: 
none



DD Entity: 192 Ambient temperature
Definition: Ambient temperature measured by a machine. Unit is milli-Kelvin (mK). 
Comment: 
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
Unit: mK - Temperature
Resolution: 1
SAE SPN: 
Range: 0 - 1000000
Submit by: Timo Oksanen
Submit Date: 2011-01-17
Submit Company: Aalto University
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2011-04-04
Status Comments: 
Attachments: 
none



DD Entity: 193 Setpoint Product Pressure
Definition: Setpoint Product Pressure to adjust the pressure of the product flow system at the point of dispensing.
Comment: On pressure-based control systems, it is important to be able to monitor and control the system pressure to ensure the proper flow rate and droplet size.  Being able to display and log pressure is important. On sprayers, this would be the boom pressure.
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
Unit: Pa - Pressure
Resolution: 0,1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Tony Woodcock
Submit Date: 2011-01-19
Submit Company: Ag Leader Technology
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2011-03-27
Status Comments: 
Attachments: 
none



DD Entity: 194 Actual Product Pressure
Definition: Actual Product Pressure is the measured pressure in the product flow system at the point of dispensing.
Comment: On pressure-based control systems, it is important to be able to monitor and conrol the system pressure to ensure the proper flow rate and droplet size.  Being able to display and log pressure is important. On sprayers, this would be the boom pressure.
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
Unit: Pa - Pressure
Resolution: 0,1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Tony Woodcock
Submit Date: 2011-03-09
Submit Company: Ag Leader Technology
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2011-04-04
Status Comments: 
Attachments: 
none



DD Entity: 195 Minimum Product Pressure
Definition: Minimun Product Pressure in the product flow system at the point of dispensing. 
Comment: Minimum system product pressure to ensure a consistent product flow.
See also "Setpoint Product Pressure"
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
Unit: Pa - Pressure
Resolution: 0,1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Tony Woodcock
Submit Date: 2011-03-09
Submit Company: Ag Leader Technology
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2011-04-04
Status Comments: 
Attachments: 
none



DD Entity: 196 Maximum Product Pressure
Definition: Maximum Product Pressure in the product flow system at the point of dispensing. 
Comment: Maximum system product to ensure a stable and safe product flow.
See also "Setpoint Product Pressure"
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
Unit: Pa - Pressure
Resolution: 0,1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Tony Woodcock
Submit Date: 2011-03-09
Submit Company: Ag Leader Technology
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2011-04-04
Status Comments: 
Attachments: 
none



DD Entity: 197 Setpoint Pump Output Pressure
Definition: Setpoint Pump Output Pressure to adjust the  pressure at the output of the solution pump.
Comment: 
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
Unit: Pa - Pressure
Resolution: 0,1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Tony Woodcock
Submit Date: 2011-01-19
Submit Company: Ag Leader Technology
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2011-03-27
Status Comments: 
Attachments: 
none



DD Entity: 198 Actual Pump Output Pressure
Definition: Actual Pump Output Pressure measured at the output of the solution pump.
Comment: 
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
Unit: Pa - Pressure
Resolution: 0,1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Tony Woodcock
Submit Date: 2011-03-09
Submit Company: Ag Leader Technology
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2011-04-04
Status Comments: 
Attachments: 
none



DD Entity: 199 Minimum Pump Output Pressure
Definition: Minimum Pump Output Pressure for the output pressure of the solution pump.
Comment: 
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
Unit: Pa - Pressure
Resolution: 0,1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Tony Woodcock
Submit Date: 2011-03-09
Submit Company: Ag Leader Technology
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2011-04-04
Status Comments: 
Attachments: 
none



DD Entity: 200 Maximum Pump Output Pressure
Definition: Maximum Pump Output Pressure for the output pressure of the solution pump.
Comment: 
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
Unit: Pa - Pressure
Resolution: 0,1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Tony Woodcock
Submit Date: 2011-03-09
Submit Company: Ag Leader Technology
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2011-04-04
Status Comments: 
Attachments: 
none



DD Entity: 201 Setpoint Tank Agitation Pressure
Definition: Setpoint Tank Agitation Pressure to adjust the pressure for a stir system in a tank.
Comment: In a liquid application system, this is the pressure used to stir the tank contents to prevent products in liquid suspension from settling in the tank.
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
Unit: Pa - Pressure
Resolution: 0,1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Tony Woodcock
Submit Date: 2011-01-19
Submit Company: Ag Leader Technology
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2011-03-27
Status Comments: 
Attachments: 
none



DD Entity: 202 Actual Tank Agitation Pressure
Definition: Actual Tank Agitation Pressure measured by the tank stir system.
Comment: In a liquid application system, this is the pressure used to stir the tank contents to prevent products in liquid suspension from settling in the tank. Typically measured at the agitation manifold.
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
Unit: Pa - Pressure
Resolution: 0,1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Tony Woodcock
Submit Date: 2011-03-09
Submit Company: Ag Leader Technology
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2011-04-04
Status Comments: 
Attachments: 
none



DD Entity: 203 Minimum Tank Agitation Pressure
Definition: Minimun Tank Agitation Pressure for a stir system in a tank.
Comment: Minimum tank agitation pressure to prevent products in liquid suspension from settling in the tank.
See also "Setpoint Tank Agitation Pressure".
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
Unit: Pa - Pressure
Resolution: 0,1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Tony Woodcock
Submit Date: 2011-03-09
Submit Company: Ag Leader Technology
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2011-04-04
Status Comments: 
Attachments: 
none



DD Entity: 204 Maximum Tank Agitation Pressure
Definition: Maximun Tank Agitation Pressure for a stir system in a tank.
Comment: Maximum tank agitation pressure to prevent products in liquid suspension from settling in the tank.
See also "Setpoint Tank Agitation Pressure"
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
Unit: Pa - Pressure
Resolution: 0,1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Tony Woodcock
Submit Date: 2011-03-09
Submit Company: Ag Leader Technology
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2011-04-05
Status Comments: 
Attachments: 
none



DD Entity: 205 SC Turn On Time
Definition: The Section Control Turn On Time defines the overall time lapse between the moment the TC sends a turn on section command to the working set and the moment this section is physically turned on and the product is applied.
The working set may support this DDE as an optional feature to provide the possibility to store the time settings direct on the device to make the settings available after a power cycle. Therefore this DDE needs always to be setable by the TC and shall not be used to change any working set system behavior. 

The DDI shall support the On Change trigger so that the TC is able to get informed when the value gets changed by the Working Set Master. The TC shall active this trigger when using the DDI.
Comment: The SC Turn On Time setting is used to compensate the average physical machine reaction time (Electrical & Mechanical) from the moment the Task Controller send the command and the Working Set applies the product. 
To find the right time setting for the used system combination of Task Controller and Working Set it could take awhile and therefore it is a big benefit to store the setting on the working set to make them again available after a power cycle. For working sets supporting Section Control it is recommended to add SC Turn On Time to its device description and make it setable. 
In case where the device description contains also Physical Setpoint Time Latency or Physical Actual Time Latency the TC Turn On Time will always supercede it.
Typically used by Device Classes: 
0 - Non-specific system
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
Unit: ms - Time
Resolution: 1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Matthias Meyer
Submit Date: 2011-03-09
Submit Company: John Deere
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2011-03-28
Status Comments: 
Attachments: 
none



DD Entity: 206 SC Turn Off Time
Definition: The Section Control Turn Off Time defines the overall time lapse between the moment the TC sends a turn off section command to the working set and the moment this section is physically turned off.
The working set may support this DDE as an optional feature to provide the possibility to store the time settings direct on the device to make the settings available after a power cycle. Therefore this DDE needs always to be setable by the TC and shall not be used to change any working set system behavior. 

The DDI shall support the On Change trigger so that the TC is able to get informed when the value gets changed by the Working Set Master. The TC shall active this trigger when using the DDI.
Comment: The SC Turn Off Time setting is used to compensate the average physical machine reaction time (Electrical & Mechanical) from the moment the Task Controller send the command and the Working Set turns off the sections. 
To find the right time setting for the used system combination of Task Controller and Working Set it could take awhile and therefore it is a big benefit to store the setting on the Working Set to make them again available after a power cycle. For Working Sets supporting Section Control it is recommended to add SC Turn Off Time to its device description and make it setable. 
Typically used by Device Classes: 
0 - Non-specific system
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
Unit: ms - Time
Resolution: 1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Matthias Meyer
Submit Date: 2011-03-09
Submit Company: John Deere
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2011-03-28
Status Comments: 
Attachments: 
none



DD Entity: 207 Wind speed
Definition: Wind speed measured in the treated field at the beginning of operations or on the application implement during operations. Measurements at to be made at 2m height or 1 m over the canopy in tree and bush crops.
On implements the wind speed needs to be compansated by implement true ground speed and heading.
Comment: Requested by TC23 SC6 WG15
Typically used by Device Classes: 
1 - Tractor
5 - Fertilizer
6 - Sprayers
10 - Irrigation
14 - Special Crops
17 - Sensor System
Unit: mm/s - Speed
Resolution: 1
SAE SPN: 
Range: 0 - ***********
Submit by: Bob Benneweis
Submit Date: 2011-03-16
Submit Company: Benneweis Consulting Ltd
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2011-04-04
Status Comments: 
Attachments: 
none



DD Entity: 208 Wind direction
Definition: Wind direction measured in the treated field at the beginning of operations or on the application implement during operations. Measurements at to be made at 2m height or 1 m over the canopy in tree and bush crops.
On implements the wind direction needs to be compansated by implement true ground speed and heading.

Comment: 
Typically used by Device Classes: 
1 - Tractor
5 - Fertilizer
6 - Sprayers
10 - Irrigation
14 - Special Crops
17 - Sensor System
Unit: ° - Angle
Resolution: 1
SAE SPN: 
Range: 0 - 359
Submit by: Bob Benneweis
Submit Date: 2011-03-16
Submit Company: Benneweis Consulting Ltd
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2011-04-04
Status Comments: 
Attachments: 
none



DD Entity: 209 Air Humidity
Definition: Ambient humidty measured by a weather station in a treated field or on the application implement.
Comment: 
Typically used by Device Classes: 
1 - Tractor
5 - Fertilizer
6 - Sprayers
7 - Harvesters
9 - Forage harvester
10 - Irrigation
14 - Special Crops
17 - Sensor System
Unit: % - Percent
Resolution: 1
SAE SPN: 
Range: 0 - 100
Submit by: Bob Benneweis
Submit Date: 2011-03-16
Submit Company: Benneweis Consulting Ltd
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2011-04-04
Status Comments: 
Attachments: 
none



DD Entity: 210 Sky conditions
Definition: This DDE is used to define the current sky conditions during operation. The METAR format and its abbrivations is used as follows to define the sky conditions:

CLR=Clear, NSC=Mostly Sunny, FEW=Partly Sunny, SCT=Partly cloud, BKN=Mostly cloudy, OVC=overcast/cloudy

1. Byte = first character
2. Byte = second character
3. Byte = third character
4. Byte = fourt character

Unused bytes shall be set to 0x20
Byte 1 to 4 set to 0x00 = error
Byte 1 to 4  set to 0xFF = not available
Comment: To setup the METAR abbrivations the IS0 8859-1 standard is used.
From the Latin-1 printable characters set the capitals from "A" (0x41) to "Z" (0x5A ) shall be used. The space "SP" (0x20) is used for unused bytes.

Example for Clear (CLR):
1. Byte = 0x43(C)
2. Byte = 0x4C(L)
3. Byte = 0x52 (R)
4. Byte = 0x20 (unused) 
Typically used by Device Classes: 
1 - Tractor
5 - Fertilizer
6 - Sprayers
7 - Harvesters
9 - Forage harvester
10 - Irrigation
14 - Special Crops
17 - Sensor System
Unit: n.a. - 
Resolution: 1
SAE SPN: 
Range: 0 - 4294967295
Submit by: Bob Benneweis
Submit Date: 2011-03-16
Submit Company: Benneweis Consulting Ltd
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2011-04-29
Status Comments: 
Attachments: 
none



DD Entity: 211 Last Bale Flakes per Bale
Definition: The number of flakes in the most recently produced bale.
Comment: The recommended use of this DDE is for a baler to report this once for every bale that is produced. A baler can add this to its default set of data, based on an internal on-change data trigger that causes the baler to report the value of this DDE after the bale is produced. The recommendation for data logging is that all "Last Bale" DDEs that are supported by a device are reported together at the moment that the bale is produced and leaves the machine.
Typically used by Device Classes: 
7 - Harvesters
9 - Forage harvester
Unit: # - Quantity/Count
Resolution: 1
SAE SPN: 
Range: 0 - 1000
Submit by: Jaap van Bergeijk
Submit Date: 2011-04-30
Submit Company: AGCO
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2011-04-30
Status Comments: 
Attachments: 
none



DD Entity: 212 Last Bale Average Moisture
Definition: The average moisture in the most recently produced bale.
Comment: The recommended use of this DDE is for a baler to report this once for every bale that is produced. A baler may add this to its default set of data, based on an internal on-change data trigger that causes the baler to report the value of this DDE after the bale is produced. The recommendation for data logging is that all "Last Bale" DDEs that are supported by a device are reported together at the moment that the bale is produced and leaves the machine.
Typically used by Device Classes: 
7 - Harvesters
9 - Forage harvester
Unit: ppm - Parts per million
Resolution: 1
SAE SPN: 
Range: 0 - *********
Submit by: Jaap van Bergeijk
Submit Date: 2011-04-30
Submit Company: AGCO
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2011-04-30
Status Comments: 
Attachments: 
none



DD Entity: 213 Last Bale Average Strokes per Flake
Definition: The number of baler plunger compression strokes per flake that has entered the bale compression chamber. This value is the average valid for the most recently produced bale.
Comment: The recommended use of this DDE is for a baler to report this once for every bale that is produced. A baler may add this to its default set of data, based on an internal on-change data trigger that causes the baler to report the value of this DDE after the bale is produced. The recommendation for data logging is that all "Last Bale" DDEs that are supported by a device are reported together at the moment that the bale is produced and leaves the machine.
Typically used by Device Classes: 
7 - Harvesters
9 - Forage harvester
Unit: # - Quantity/Count
Resolution: 1
SAE SPN: 
Range: 0 - 1000
Submit by: Jaap van Bergeijk
Submit Date: 2011-04-30
Submit Company: AGCO
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2011-04-30
Status Comments: 
Attachments: 
none



DD Entity: 214 Lifetime Bale Count
Definition: The number of bales produced by a machine over its entire lifetime. This DDE value can not be set through the process data interface but can be requested and added to a datalog. This DDE value is not affected by a task based total bales but will increment at the same rate as the task based total.
Comment: The recommended use of this DDE is for a baler to report this once for every bale that is produced. A baler may add this to its default set of data, based on an internal on-change data trigger that causes the baler to report the value of this DDE after the bale is produced.
Typically used by Device Classes: 
7 - Harvesters
9 - Forage harvester
Unit: # - Quantity/Count
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Jaap van Bergeijk
Submit Date: 2011-04-30
Submit Company: AGCO
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2011-04-30
Status Comments: 
Attachments: 
none



DD Entity: 215 Lifetime Working Hours
Definition: The number of working hours of a device element over its entire lifetime. This DDE value can not be set through the process data interface but can be requested and added to a datalog.
Comment: The recommended use of this DDE is to be transmitted on a request basis only.

The Lifetime Working Hours is the overall time when the device was turned on.

This total does not refer to an application controlled by a Task Controller. Therefore this DDE shall not be setable within the device description and neither shall the device reset the value when the task status changed. It is up to the device control system when to reset this value.

The Working Set Master shall support the total trigger method for this DDE but shall not support the setable property.

The Task Controller can request and store this DDE at the end of a task. But it shall not set this DDE when the task is resumed.

Note: unit is h and the bit resolution is 0.05 h/bit, this aligns the resolution and range with similar SPNs as defined in SAE J1939-71.

Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
17 - Sensor System
Unit: h - Hour
Resolution: 0,05
SAE SPN: 
Range: 0 - *********,75
Submit by: Jaap van Bergeijk
Submit Date: 2011-04-30
Submit Company: AGCO
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2011-04-30
Status Comments: 
Attachments: 
none



DD Entity: 216 Actual Bale Hydraulic Pressure
Definition: The actual value of the hydraulic pressure applied to the sides of the bale in the bale compression chamber.
Comment: The actual pressure is the resultant of the baler controller targeting a certain setpoint plunger load.
Typically used by Device Classes: 
7 - Harvesters
9 - Forage harvester
Unit: Pa - Pressure
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Jaap van Bergeijk
Submit Date: 2011-04-30
Submit Company: AGCO
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2011-04-30
Status Comments: 
Attachments: 
none



DD Entity: 217 Last Bale Average Hydraulic Pressure
Definition: The average actual value of the hydraulic pressure applied to the sides of the bale in the bale compression chamber. This average is calculated over the most recently produced bale.
Comment: The recommended use of this DDE is for a baler to report this once for every bale that is produced. A baler may add this to its default set of data, based on an internal on-change data trigger that causes the baler to report the value of this DDE after the bale is produced. The recommendation for data logging is that all "Last Bale" DDEs that are supported by a device are reported together at the moment that the bale is produced and leaves the machine.
Typically used by Device Classes: 
7 - Harvesters
9 - Forage harvester
Unit: Pa - Pressure
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Jaap van Bergeijk
Submit Date: 2011-04-30
Submit Company: AGCO
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2011-04-30
Status Comments: 
Attachments: 
none



DD Entity: 218 Setpoint Bale Compression Plunger Load
Definition: The setpoint bale compression plunger load as a unitless number.
Comment: This value is measured / controlled for each new flake that entered the baler chamber and obtained at the rear dead end of the plunger.
Typically used by Device Classes: 
7 - Harvesters
9 - Forage harvester
Unit: n.a. - 
Resolution: 1
SAE SPN: 
Range: 0 - 2000
Submit by: Jaap van Bergeijk
Submit Date: 2011-04-30
Submit Company: AGCO
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2011-04-30
Status Comments: 
Attachments: 
none



DD Entity: 219 Actual Bale Compression Plunger Load
Definition: The actual bale compression plunger load as a unitless number.
Comment: This is the plunger load measured at the rear dead end of the plunger cycle and only updated for each new flake that has entered the baler chamber.
Typically used by Device Classes: 
7 - Harvesters
9 - Forage harvester
Unit: n.a. - 
Resolution: 1
SAE SPN: 
Range: 0 - 2000
Submit by: Jaap van Bergeijk
Submit Date: 2011-04-30
Submit Company: AGCO
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2011-04-30
Status Comments: 
Attachments: 
none



DD Entity: 220 Last Bale Average Bale Compression Plunger Load
Definition: The average bale compression plunger load for the most recently produced bale.
Comment: The recommended use of this DDE is for a baler to report this once for every bale that is produced. A baler may add this to its default set of data, based on an internal on-change data trigger that causes the baler to report the value of this DDE after the bale is produced. The recommendation for data logging is that all "Last Bale" DDEs that are supported by a device are reported together at the moment that the bale is produced and leaves the machine.
Typically used by Device Classes: 
7 - Harvesters
9 - Forage harvester
Unit: n.a. - 
Resolution: 1
SAE SPN: 
Range: 0 - 2000
Submit by: Jaap van Bergeijk
Submit Date: 2011-04-30
Submit Company: AGCO
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2011-04-30
Status Comments: 
Attachments: 
none



DD Entity: 221 Last Bale Applied Preservative
Definition: The total preservative applied to the most recently produced bale.
Comment: The recommended use of this DDE is for a baler to report this once for every bale that is produced. A baler may add this to its default set of data, based on an internal on-change data trigger that causes the baler to report the value of this DDE after the bale is produced. The recommendation for data logging is that all "Last Bale" DDEs that are supported by a device are reported together at the moment that the bale is produced and leaves the machine.
Typically used by Device Classes: 
7 - Harvesters
9 - Forage harvester
Unit: ml - Capacity large
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Jaap van Bergeijk
Submit Date: 2011-04-30
Submit Company: AGCO
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2011-04-30
Status Comments: 
Attachments: 
none



DD Entity: 222 Last Bale Tag Number
Definition: The Last Bale Tag Number as a decimal number in the range of 0 to 4294967295. Note that the value of this DDI has the limitation of being an unsigned 32 bit number.
Comment: For balers: the recommended use of this DDE is for a baler to report this once for every bale that is produced. A baler may add this to its default set of data, based on an internal on-change data trigger that causes the baler to report the value of this DDE after the bale is produced. The recommendation for data logging is that all "Last Bale" DDEs that are supported by a device are reported together at the moment that the bale is produced and leaves the machine.
Typically used by Device Classes: 
7 - Harvesters
9 - Forage harvester
Unit: n.a. - 
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Jaap van Bergeijk
Submit Date: 2011-04-30
Submit Company: AGCO
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2011-04-30
Status Comments: 
Attachments: 
none



DD Entity: 223 Last Bale Mass
Definition: The mass of the bale that has most recently been produced.
Comment: The recommended use of this DDE is for a baler to report this once for every bale that is produced. A baler may add this to its default set of data, based on an internal on-change data trigger that causes the baler to report the value of this DDE after the bale is produced. The recommendation for data logging is that all "Last Bale" DDEs that are supported by a device are reported together at the moment that the bale is produced and leaves the machine.
Typically used by Device Classes: 
7 - Harvesters
9 - Forage harvester
Unit: g - Mass large
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Jaap van Bergeijk
Submit Date: 2011-04-30
Submit Company: AGCO
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2011-04-30
Status Comments: 
Attachments: 
none



DD Entity: 224 Delta T
Definition: The difference between dry bulb temperature and wet bulb temperature measured by a weather station in a treated field or on the application equipment.
Comment: This parameter is used to determine spray effectiveness in hot and dry environments. If the Delta T value is too high the effectiveness of the overall spray application does not match the requirement for this operation. This value can be used by the application to notify the operator about the effectiveness and whether he should continue with the application or not. It can also be used to document the application environment within the log files for the task. 
Typically used by Device Classes: 
5 - Fertilizer
6 - Sprayers
7 - Harvesters
9 - Forage harvester
17 - Sensor System
Unit: mK - Temperature
Resolution: 1
SAE SPN: 
Range: 0 - 1000000
Submit by: Matthias Meyer
Submit Date: 2011-05-25
Submit Company: John Deere
Revision Number: 1
Current Status: ISO-Published
Status Date: 2011-06-20
Status Comments: 
Attachments: 
none



DD Entity: 225 Setpoint Working Length
Definition: Setpoint Working Length of Device Element.
Comment: This is the desired working length of the device element during operation. For the geometry definition and example use, see the attachment of the Actual Working Length, DDI 226.
Typically used by Device Classes: 
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
17 - Sensor System
Unit: mm - Length
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Moritz Roeingh
Submit Date: 2011-07-12
Submit Company: Competence Center ISOBUS e.V.
Revision Number: 1
Current Status: ISO-Published
Status Date: 2011-08-31
Status Comments: 
Attachments: 
none



DD Entity: 226 Actual Working Length
Definition: Actual Working Length of a Device Element.
Comment: Used for Section Control. By using the Actual Working Length of a device element a rectangular area is defined. This area represents the current working area and defines offsets for turning sections on and off by Section Control. The Actual Working Length parameter is useful for fertilizer spreaders and similar implements.
Typically used by Device Classes: 
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
17 - Sensor System
Unit: mm - Length
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Moritz Roeingh
Submit Date: 2011-07-07
Submit Company: Competence Center ISOBUS e.V.
Revision Number: 1
Current Status: ISO-Published
Status Date: 2011-08-31
Status Comments: 
Attachments: 
http://dictionary.isobus.net/isobus/attachments/257/ISO11783-11-DDI-226-ActualWorkingLength-v1.pdf



DD Entity: 227 Minimum Working Length
Definition: Minimum Working Length of Device Element.
Comment: This is the minimum working length of the device element during operation. For the geometry definition and example use, see the attachment of the Actual Working Length, DDI 226.
Typically used by Device Classes: 
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
17 - Sensor System
Unit: mm - Length
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Moritz Roeingh
Submit Date: 2011-07-12
Submit Company: Competence Center ISOBUS e.V.
Revision Number: 1
Current Status: ISO-Published
Status Date: 2011-08-31
Status Comments: 
Attachments: 
none



DD Entity: 228 Maximum Working Length
Definition: Maximum Working Length of Device Element.
Comment: This is the maximum working length of the device element during operation. For the geometry definition and example use, see the attachment of the Actual Working Length, DDI 226.
Typically used by Device Classes: 
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
17 - Sensor System
Unit: mm - Length
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Moritz Roeingh
Submit Date: 2011-07-12
Submit Company: Competence Center ISOBUS e.V.
Revision Number: 1
Current Status: ISO-Published
Status Date: 2011-08-31
Status Comments: 
Attachments: 
none



DD Entity: 229 Actual Net Weight
Definition: Actual Net Weight value specified as mass
Comment: The Actual Net Weight is the current measured mass by a weighing system.
For more information see attachment located at Actual Net Weight DDE
Typically used by Device Classes: 
11 - Transport / Trailers
17 - Sensor System
Unit: g - Mass large
Resolution: 1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Matthias Meyer
Submit Date: 2011-09-13
Submit Company: John Deere
Revision Number: 1
Current Status: ISO-Published
Status Date: 2011-09-28
Status Comments: 
Attachments: 
http://dictionary.isobus.net/isobus/attachments/279/ISO11783-11-DDI-229-Weighing System Implementation.pdf



DD Entity: 230 Net Weight State
Definition: Net Weight State, 2 bits defined as:
00 = unstable measurement
01 = stable measurement
10 = error (measuring error)
Comment: The Net Weight State indicates whether the current Actual Net Weight value is a reliable value or not. 
Example: After a mass of grain is filled into a grain cart it takes a while until the weighing system is able to provide the valid value of the load.
Typically used by Device Classes: 
11 - Transport / Trailers
17 - Sensor System
Unit: n.a. - 
Resolution: 1
SAE SPN: 
Range: 0 - 3
Submit by: Matthias Meyer
Submit Date: 2011-09-13
Submit Company: John Deere
Revision Number: 1
Current Status: ISO-Published
Status Date: 2011-09-28
Status Comments: 
Attachments: 
none



DD Entity: 231 Setpoint Net Weight
Definition: Setpoint Net Weight value.
Comment: The Setpoint Net Weight value is used to prompt the weighing system to perform a tare procedure. For more information see attachment located at Actual Net Weight DDE.
Typically used by Device Classes: 
11 - Transport / Trailers
17 - Sensor System
Unit: g - Mass large
Resolution: 1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Matthias Meyer
Submit Date: 2011-09-13
Submit Company: John Deere
Revision Number: 1
Current Status: ISO-Published
Status Date: 2011-09-28
Status Comments: 
Attachments: 
none



DD Entity: 232 Actual Gross Weight
Definition: Actual Gross Weight value specified as mass
Comment: The Actual Gross Weight is the overall measured mass by a weighing system. For more information see attachment located at Actual Net Weight DDE.
Typically used by Device Classes: 
11 - Transport / Trailers
17 - Sensor System
Unit: g - Mass large
Resolution: 1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Matthias Meyer
Submit Date: 2011-09-13
Submit Company: John Deere
Revision Number: 1
Current Status: ISO-Published
Status Date: 2011-09-28
Status Comments: 
Attachments: 
none



DD Entity: 233 Gross Weight State
Definition: Gross Weight State, 2 bits defined as:
00 = unstable measurement
01 = stable measurement
10 = error (measuring error)
Comment: The Gross Weight State indicates whether the current Actual Gross Weight value is a reliable value or not. For more information see attachment located at Actual Net Weight DDE.

Example: After a mass of grain is filled into a grain cart it takes a while until the weighing system is able to provide the valid value of the load.
Typically used by Device Classes: 
11 - Transport / Trailers
17 - Sensor System
Unit: n.a. - 
Resolution: 1
SAE SPN: 
Range: 0 - 3
Submit by: Matthias Meyer
Submit Date: 2011-09-13
Submit Company: John Deere
Revision Number: 1
Current Status: ISO-Published
Status Date: 2011-09-28
Status Comments: 
Attachments: 
none



DD Entity: 234 Minimum Gross Weight
Definition: Minimum Gross Weight specified as mass.
Comment: The Minimum Gross Weight may represent the minimum value of the effective range of the weighing system. For more information see attachment located at Actual Net Weight DDE.
Typically used by Device Classes: 
11 - Transport / Trailers
17 - Sensor System
Unit: g - Mass large
Resolution: 1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Matthias Meyer
Submit Date: 2011-09-13
Submit Company: John Deere
Revision Number: 1
Current Status: ISO-Published
Status Date: 2011-09-28
Status Comments: 
Attachments: 
none



DD Entity: 235 Maximum Gross Weight
Definition: Maximum Gross Weight specified as mass.
Comment: Maximum Gross Weight may represent the maximum value of the effective range of the weighing system. For more information see attachment located at Actual Net Weight DDE.
Typically used by Device Classes: 
11 - Transport / Trailers
17 - Sensor System
Unit: g - Mass large
Resolution: 1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Matthias Meyer
Submit Date: 2011-09-13
Submit Company: John Deere
Revision Number: 1
Current Status: ISO-Published
Status Date: 2011-09-28
Status Comments: 
Attachments: 
none



DD Entity: 236 Thresher Engagement Total Time
Definition: Accumulated time while the threshing mechanism is engaged
Comment: This DDE represents the total engagement time of the threshing mechanism of the machine and is recommended to be used at maximum once within the device description in the device element that represents the machine.
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
Unit: s - Time count
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Robert Waggoner
Submit Date: 2011-07-08
Submit Company: AGCO
Revision Number: 1
Current Status: ISO-Published
Status Date: 2011-10-17
Status Comments: 
Attachments: 
none



DD Entity: 237 Actual Header Working Height Status
Definition: Actual status of the header being above or below the threshold height for the in-work state.
2 bit status indicator:
00=disabled/off/above threshold height
01=enabled/on/below threshold height
10=error
11=undefined/not installed
Comment: The DDE has been defined to be able to communicate a more detailed work state of a machine.
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
Unit: n.a. - 
Resolution: 1
SAE SPN: 
Range: 0 - 3
Submit by: Robert Waggoner
Submit Date: 2011-07-08
Submit Company: AGCO
Revision Number: 1
Current Status: ISO-Published
Status Date: 2011-11-17
Status Comments: 
Attachments: 
none



DD Entity: 238 Actual Header Rotational Speed Status
Definition: Actual status of the header rotational speed being above or below the threshold for in-work state.
2 bit status indicator:
00=disabled/off/below threshold speed
01=enabled/on/above threshold speed
10=error
11=undefined/not installed
Comment: The DDE has been defined to be able to communicate a more detailed work state of a machine.
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
Unit: n.a. - 
Resolution: 1
SAE SPN: 
Range: 0 - 3
Submit by: Robert Waggoner
Submit Date: 2011-07-08
Submit Company: AGCO
Revision Number: 1
Current Status: ISO-Published
Status Date: 2011-10-17
Status Comments: 
Attachments: 
none



DD Entity: 239 Yield Hold Status
Definition: Status indicator for the yield measurement system. When enabled/on, the measurements from the yield measurement system are ignored and the yield is held constant.
2 bit status indicator:
00=disabled/off
01=enabled/on
10=error
11=undefined/not installed
Comment: This status indicator can e.g. be set by the operator when entering an area of the field where the yield measurement system yield measurements should not be used.
This DDE shall not be setable by the TC.
The values of the following list of DDE's is are held constant when this DDE is enabled/on: DDI's 83 to 91, 181 to 183 and 185 to 190.
Typically used by Device Classes: 
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
Unit: n.a. - 
Resolution: 1
SAE SPN: 
Range: 0 - 3
Submit by: Robert Waggoner
Submit Date: 2011-07-08
Submit Company: AGCO
Revision Number: 1
Current Status: ISO-Published
Status Date: 2011-10-17
Status Comments: 
Attachments: 
none



DD Entity: 240 Actual (Un)Loading System Status
Definition: Actual status of the Unloading and/or Loading system.  This DDE covers both Unloading and Loading of the device element wherein it is listed.
Byte 1:
2 bit unloading status indicator:
00=disabled/off
01=enabled/on/unloading
10=error
11=undefined/not installed
Byte 2:
2 bit loading status indicator:
00=disabled/off
01=enabled/on/loading
10=error
11=undefined/not installed
Comment: 
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
Unit: n.a. - 
Resolution: 1
SAE SPN: 
Range: 0 - 3
Submit by: Robert Waggoner
Submit Date: 2011-07-08
Submit Company: AGCO
Revision Number: 1
Current Status: ISO-Published
Status Date: 2011-10-17
Status Comments: 
Attachments: 
none



DD Entity: 241 Crop Temperature
Definition: Temperature of harvested crop
Comment: 
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
Unit: mK - Temperature
Resolution: 1
SAE SPN: 
Range: 0 - 1000000
Submit by: Robert Waggoner
Submit Date: 2011-07-08
Submit Company: AGCO
Revision Number: 1
Current Status: ISO-Published
Status Date: 2011-10-17
Status Comments: 
Attachments: 
none



DD Entity: 242 Setpoint Sieve Clearance
Definition: Setpoint separation distance between Sieve elements
Comment: 
Typically used by Device Classes: 
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
Unit: mm - Length
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Jaap van Bergeijk
Submit Date: 2011-07-08
Submit Company: AGCO
Revision Number: 1
Current Status: ISO-Published
Status Date: 2011-10-17
Status Comments: 
Attachments: 
none



DD Entity: 243 Actual Sieve Clearance
Definition: Actual separation distance between Sieve elements
Comment: 
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
Unit: mm - Length
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Robert Waggoner
Submit Date: 2011-07-08
Submit Company: AGCO
Revision Number: 1
Current Status: ISO-Published
Status Date: 2011-10-17
Status Comments: 
Attachments: 
none



DD Entity: 244 Minimum Sieve Clearance
Definition: Minimal separation distance between Sieve elements
Comment: 
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
Unit: mm - Length
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Jaap van Bergeijk
Submit Date: 2011-07-08
Submit Company: AGCO
Revision Number: 1
Current Status: ISO-Published
Status Date: 2011-10-17
Status Comments: 
Attachments: 
none



DD Entity: 245 Maximum Sieve Clearance
Definition: Maximum separation distance between Sieve elements.
Comment: 
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
Unit: mm - Length
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Jaap van Bergeijk
Submit Date: 2011-10-17
Submit Company: AGCO
Revision Number: 1
Current Status: ISO-Published
Status Date: 2011-10-17
Status Comments: 
Attachments: 
none



DD Entity: 246 Setpoint Chaffer Clearance
Definition: Setpoint separation distance between Chaffer elements.
Comment: 
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
Unit: mm - Length
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Jaap van Bergeijk
Submit Date: 2011-10-17
Submit Company: AGCO
Revision Number: 1
Current Status: ISO-Published
Status Date: 2011-10-17
Status Comments: 
Attachments: 
none



DD Entity: 247 Actual Chaffer Clearance
Definition: Actual separation distance between Chaffer elements.
Comment: 
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
Unit: mm - Length
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Robert Waggoner
Submit Date: 2011-07-08
Submit Company: AGCO
Revision Number: 3
Current Status: ISO-Published
Status Date: 2011-10-17
Status Comments: 
Attachments: 
none



DD Entity: 248 Minimum Chaffer Clearance
Definition: Minimum separation distance between Chaffer elements.
Comment: 
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
Unit: mm - Length
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Jaap van Bergeijk
Submit Date: 2011-10-17
Submit Company: AGCO
Revision Number: 1
Current Status: ISO-Published
Status Date: 2011-10-17
Status Comments: 
Attachments: 
none



DD Entity: 249 Maximum Chaffer Clearance
Definition: Maximum separation distance between Chaffer elements.
Comment: 
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
Unit: mm - Length
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Jaap van Bergeijk
Submit Date: 2011-10-17
Submit Company: AGCO
Revision Number: 1
Current Status: ISO-Published
Status Date: 2011-10-17
Status Comments: 
Attachments: 
none



DD Entity: 250 Setpoint Concave Clearance
Definition: Setpoint separation distance between Concave elements.
Comment: 
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
Unit: mm - Length
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Jaap van Bergeijk
Submit Date: 2011-10-17
Submit Company: AGCO
Revision Number: 1
Current Status: ISO-Published
Status Date: 2011-10-17
Status Comments: 
Attachments: 
none



DD Entity: 251 Actual Concave Clearance
Definition: Actual separation distance between Concave elements.
Comment: 
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
Unit: mm - Length
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Robert Waggoner
Submit Date: 2011-07-08
Submit Company: AGCO
Revision Number: 3
Current Status: ISO-Published
Status Date: 2011-10-17
Status Comments: 
Attachments: 
none



DD Entity: 252 Minimum Concave Clearance
Definition: Minimum separation distance between Concave elements.
Comment: 
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
Unit: mm - Length
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Jaap van Bergeijk
Submit Date: 2011-10-17
Submit Company: AGCO
Revision Number: 1
Current Status: ISO-Published
Status Date: 2011-10-17
Status Comments: 
Attachments: 
none



DD Entity: 253 Maximum Concave Clearance
Definition: Maximum separation distance between Concave elements.
Comment: 
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
Unit: mm - Length
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Jaap van Bergeijk
Submit Date: 2011-10-17
Submit Company: AGCO
Revision Number: 1
Current Status: ISO-Published
Status Date: 2011-10-17
Status Comments: 
Attachments: 
none



DD Entity: 254 Setpoint Separation Fan Rotational Speed
Definition: Setpoint rotational speed of the fan used for separating product material from non product material.
Comment: 
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
Unit: /s - Quantity per time unit
Resolution: 0,001
SAE SPN: 
Range: 0 - **********
Submit by: Jaap van Bergeijk
Submit Date: 2011-10-17
Submit Company: AGCO
Revision Number: 1
Current Status: ISO-Published
Status Date: 2011-10-17
Status Comments: 
Attachments: 
none



DD Entity: 255 Actual Separation Fan Rotational Speed
Definition: Actual rotational speed of the fan used for separating product material from non product material.
Comment: 
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
Unit: /s - Quantity per time unit
Resolution: 0,001
SAE SPN: 
Range: 0 - **********
Submit by: Robert Waggoner
Submit Date: 2011-07-08
Submit Company: AGCO
Revision Number: 3
Current Status: ISO-Published
Status Date: 2011-10-17
Status Comments: 
Attachments: 
none



DD Entity: 256 Minimum Separation Fan Rotational Speed
Definition: Minimum rotational speed of the fan used for separating product material from non product material.
Comment: 
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
Unit: /s - Quantity per time unit
Resolution: 0,001
SAE SPN: 
Range: 0 - **********
Submit by: Jaap van Bergeijk
Submit Date: 2011-10-17
Submit Company: AGCO
Revision Number: 1
Current Status: ISO-Published
Status Date: 2011-10-17
Status Comments: 
Attachments: 
none



DD Entity: 257 Maximum Separation Fan Rotational Speed
Definition: Maximum rotational speed of the fan used for separating product material from non product material.
Comment: 
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
Unit: /s - Quantity per time unit
Resolution: 0,001
SAE SPN: 
Range: 0 - **********
Submit by: Jaap van Bergeijk
Submit Date: 2011-10-17
Submit Company: AGCO
Revision Number: 1
Current Status: ISO-Published
Status Date: 2011-10-17
Status Comments: 
Attachments: 
none



DD Entity: 258 Hydraulic Oil Temperature
Definition: Temperature of fluid in the hydraulic system.
Comment: 
Typically used by Device Classes: 
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
Unit: mK - Temperature
Resolution: 1
SAE SPN: 
Range: 0 - 2000000
Submit by: Robert Waggoner
Submit Date: 2011-07-08
Submit Company: AGCO
Revision Number: 3
Current Status: ISO-Published
Status Date: 2011-10-17
Status Comments: 
Attachments: 
none



DD Entity: 259 Yield Lag Ignore Time
Definition: Amount of time to ignore yield data, starting at the transition from the in-work to the out-of-work state. During this time, the yield sensor provides inconsistent or unreliable crop flow data.
Comment: This DDE can be used to filter the yield data when creating yield maps. The values of the following list of DDE's may be inconsistent or unreliable during this yield lag ignore time: DDI's 83 to 91, 181 to 183 and 185 to 190.
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
Unit: ms - Time
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Robert Waggoner
Submit Date: 2011-07-08
Submit Company: AGCO
Revision Number: 3
Current Status: ISO-Published
Status Date: 2011-10-17
Status Comments: 
Attachments: 
none



DD Entity: 260 Yield Lead Ignore Time
Definition: Amount of time to ignore yield data, starting at the transition from the out-of-work to the in-work state. During this time, the yield sensor provides inconsistent or unreliable crop flow data.
Comment: This DDE can be used to filter the yield data when creating yield maps. The values of the following list of DDE's may be inconsistent or unreliable during this yield lead ignore time: DDI's 83 to 91, 181 to 183 and 185 to 190.
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
Unit: ms - Time
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Robert Waggoner
Submit Date: 2011-07-08
Submit Company: AGCO
Revision Number: 3
Current Status: ISO-Published
Status Date: 2011-10-17
Status Comments: 
Attachments: 
none



DD Entity: 261 Average Yield Mass Per Time
Definition: Average Yield expressed as mass per unit time, not corrected for the reference moisture percentage DDI 184. This value is the average for a Task and may be reported as a total.
Comment: This Average Yield Mass Per Time is the mass that includes the average crop moisture (DDI 262) if this is measured on e.g. harvesting equipment.  This average yield mass per time is calculated as the yield total mass (DDI 90) divided by the effective total time (DDI 119) of the active task.
When a task is resumed and its previously recorded totals are sent by the task controller to the connected working set, a situation can occur where there is a discrepancy between the yield total mass, the effective total time and the average yield mass per time values. In case all three of these DDI's are present in the device description and all three values are set by the task controller upon resuming a task, the working set shall compute its average yield mass per time from the yield total mass divided by the effective total time and shall discard the average yield mass per time value that it received from the task controller.
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
Unit: mg/s - Mass flow
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Robert Waggoner
Submit Date: 2011-07-08
Submit Company: AGCO
Revision Number: 2
Current Status: ISO-Published
Status Date: 2011-10-17
Status Comments: 
Attachments: 
none



DD Entity: 262 Average Crop Moisture
Definition: Average Moisture of the harvested crop. This value is the average for a Task and may be reported as a total.
Comment: This is the average of the actual crop moisture (DDI 99) for the active task and is calculated as an average based upon the yield total mass (DDI 90).  In order to correctly calculate this value when a task is resumed, the yield total mass shall also be reported by the device as a total. When a task is resumed, the task controller sets both the yield total mass and the average crop moisture values. The device uses these values to derive the total moisture and calculate and report the new average crop moisture values for the resumed task.
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
Unit: ppm - Parts per million
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Robert Waggoner
Submit Date: 2011-07-08
Submit Company: AGCO
Revision Number: 3
Current Status: ISO-Published
Status Date: 2011-10-17
Status Comments: 
Attachments: 
none



DD Entity: 263 Average Yield Mass Per Area
Definition: Average Yield expressed as mass per unit area, not corrected for the reference moisture percentage DDI 184. This value is the average for a Task and may be reported as a total.
Comment: This Average Yield Mass Per Area is the mass that includes the average crop moisture (DDI 262) if this is measured on e.g. harvesting equipment.  This average yield mass per area is calculated as the yield total mass (DDI 90) divided by the total area (DDI 116) of the active task.
When a task is resumed and its previously recorded totals are sent by the task controller to the connected working set, a situation can occur where there is a discrepancy between the yield total mass, the total area and the average yield mass per area values. In case all three of these DDI's are present in the device description and all three values are set by the task controller upon resuming a task, the working set shall compute its average yield mass per area from the yield total mass divided by the total area and shall discard the average yield mass per area value that it received from the task controller.
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
Unit: mg/m² - Mass per area unit
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Robert Waggoner
Submit Date: 2011-07-08
Submit Company: AGCO
Revision Number: 2
Current Status: ISO-Published
Status Date: 2011-10-17
Status Comments: 
Attachments: 
none



DD Entity: 264 Connector Pivot X-Offset
Definition: X direction offset of a connector pivot point  relative to DRP.
This DDE shall be only attached to a DET element of type connector.
Comment: Some connector types are equipped with a pivot point which will influence the accuracy of applications as section control, prescription or sequence control. The Pivot X-Offset is used to define the distance from the device DRP to the connector pivot point in X direction.
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
Unit: mm - Length
Resolution: 1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Matthias Meyer
Submit Date: 2012-03-07
Submit Company: John Deere
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-04-02
Status Comments: 
Attachments: 
http://dictionary.isobus.net/isobus/attachments/367/ISO11783-11-DDI-264-Connector Pivot X-Offset-v1.pdf



DD Entity: 265 Remaining Area
Definition: Remaining Area of a field, which is calculated from the total area and the processed area.
Comment: See DDI attachment for further details.
Typically used by Device Classes: 
Not Assigned
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
14 - Special Crops
17 - Sensor System
Unit: m² - Area
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Markus Eikler
Submit Date: 2011-12-15
Submit Company: Mueller Elektronik GmbH & Co. KG
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-04-02
Status Comments: 
Attachments: 
http://dictionary.isobus.net/isobus/attachments/319/ISO11783-11-DDI-265-Remaining Area-v1.pdf



DD Entity: 266 Lifetime Application Total Mass
Definition: Entire Application Total Mass of the device lifetime.
Comment: This is the overall total of the device. This total does not refer to an application controlled by a Task Controller. Therefore this DDE shall not be setable within the device description and neither shall the device reset the value when the task status changes. It is up to the device control system when to reset this value.

The Working Set Master shall support the total trigger method for this DDE but shall not support the setable property.

The Task Controller can request and store this DDE at the end of a task. But it shall not set this DDE when the task is resumed.
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
Unit: kg - Mass
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Matthias Meyer 
Submit Date: 2012-01-09
Submit Company: John Deere
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2012-02-15
Status Comments: Status was approved
Attachments: 
none



DD Entity: 267 Lifetime Application Total Count
Definition: Entire Application Total Count of the device lifetime.
Comment: This is the overall total of the device. This total does not refer to an application controlled by a Task Controller. Therefore this DDE shall not be setable within the device description and neither shall the device reset the value when the task status changes. It is up to the device control system when to reset this value.

The Working Set Master shall support the total trigger method for this DDE but shall not support the setable property.

The Task Controller can request and store this DDE at the end of a task. But it shall not set this DDE when the task is resumed.
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
Unit: # - Quantity/Count
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Matthias Meyer
Submit Date: 2012-01-09
Submit Company: John Deere
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-02-15
Status Comments: 
Attachments: 
none



DD Entity: 268 Lifetime Yield Total Volume
Definition: Entire Yield Total Volume of the device lifetime.
Comment: This is the overall total of the device. This total does not refer to an application controlled by a Task Controller. Therefore this DDE shall not be setable within the device description and neither shall the device reset the value when the task status changes. It is up to the device control system when to reset this value.

The Working Set Master shall support the total trigger method for this DDE but shall not support the setable property.

The Task Controller can request and store this DDE at the end of a task. But it shall not set this DDE when the task is resumed.
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
Unit: L - Quantity per volume
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Matthias Meyer
Submit Date: 2012-01-09
Submit Company: John Deere
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-02-15
Status Comments: 
Attachments: 
none



DD Entity: 269 Lifetime Yield Total Mass
Definition: Entire Yield Total Mass of the device lifetime.
Comment: This is the overall total of the device. This total does not refer to an application controlled by a Task Controller. Therefore this DDE shall not be setable within the device description and neither shall the device reset the value when the task status changes. It is up to the device control system when to reset this value.

The Working Set Master shall support the total trigger method for this DDE but shall not support the setable property.

The Task Controller can request and store this DDE at the end of a task. But it shall not set this DDE when the task is resumed.
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
Unit: kg - Mass
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Matthias Meyer
Submit Date: 2012-01-09
Submit Company: John Deere
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-02-15
Status Comments: 
Attachments: 
none



DD Entity: 270 Lifetime Yield Total Count
Definition: Entire Yield Total Count of the device lifetime.
Comment: This is the overall total of the device. This total does not refer to an application controlled by a Task Controller. Therefore this DDE shall not be setable within the device description and neither shall the device reset the value when the task status changes. It is up to the device control system when to reset this value.

The Working Set Master shall support the total trigger method for this DDE but shall not support the setable property.

The Task Controller can request and store this DDE at the end of a task. But it shall not set this DDE when the task is resumed.
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
Unit: # - Quantity/Count
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Matthias Meyer
Submit Date: 2012-01-09
Submit Company: John Deere
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-02-15
Status Comments: 
Attachments: 
none



DD Entity: 271 Lifetime Total Area
Definition: Entire Total Area of the device lifetime.
Comment: This is the overall total of the device. This total does not refer to an application controlled by a Task Controller. Therefore this DDE shall not be setable within the device description and neither shall the device reset the value when the task status changes. It is up to the device control system when to reset this value.

The Working Set Master shall support the total trigger method for this DDE but shall not support the setable property.

The Task Controller can request and store this DDE at the end of a task. But it shall not set this DDE when the task is resumed.
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
14 - Special Crops
15 - Municipal Work
Unit: m² - Area
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Matthias Meyer
Submit Date: 2012-01-09
Submit Company: John Deere
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-02-15
Status Comments: 
Attachments: 
none



DD Entity: 272 Lifetime Total Distance
Definition: Entire Total Distance of the device lifetime.
Comment: This is the overall total of the device. This total does not refer to an application controlled by a Task Controller. Therefore this DDE shall not be setable within the device description and neither shall the device reset the value when the task status changed. It is up to the device control system when to reset this value.

The Working Set Master shall support the total trigger method for this DDE but shall not support the setable property.

The Task Controller can request and store this DDE at the end of a task. But it shall not set this DDE when the task is resumed.
Typically used by Device Classes: 
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
Unit: m - Distance
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Matthias Meyer
Submit Date: 2012-01-09
Submit Company: John Deere
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-03-09
Status Comments: 
Attachments: 
none



DD Entity: 273 Lifetime Ineffective Total Distance
Definition: Entire Ineffective Total Distance of the device lifetime.
Comment: This is the overall total of the device. This total does not refer to an application controlled by a Task Controller. Therefore this DDE shall not be setable within the device description and neither shall the device reset the value when the task status changed. It is up to the device control system when to reset this value.

The Working Set Master shall support the total trigger method for this DDE but shall not support the setable property.

The Task Controller can request and store this DDE at the end of a task. But it shall not set this DDE when the task is resumed.
Typically used by Device Classes: 
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
Unit: m - Distance
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Matthias Meyer
Submit Date: 2012-01-09
Submit Company: John Deere
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-03-09
Status Comments: 
Attachments: 
none



DD Entity: 274 Lifetime Effective Total Time
Definition: Entire Effective Total Time of the device lifetime.
Comment: This is the overall total of the device. This total does not refer to an application controlled by a Task Controller. Therefore this DDE shall not be setable within the device description and neither shall the device reset the value when the task status changed. It is up to the device control system when to reset this value.

The Working Set Master shall support the total trigger method for this DDE but shall not support the setable property.

The Task Controller can request and store this DDE at the end of a task. But it shall not set this DDE when the task is resumed.
Typically used by Device Classes: 
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
17 - Sensor System
Unit: s - Time count
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Matthias Meyer
Submit Date: 2012-01-09
Submit Company: John Deere
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-02-15
Status Comments: 
Attachments: 
none



DD Entity: 275 Lifetime Ineffective Total Time
Definition: Entire Ineffective Total Time of the device lifetime.
Comment: This is the overall total of the device. This total does not refer to an application controlled by a Task Controller. Therefore this DDE shall not be setable within the device description and neither shall the device reset the value when the task status changed. It is up to the device control system when to reset this value.

The Working Set Master shall support the total trigger method for this DDE but shall not support the setable property.

The Task Controller can request and store this DDE at the end of a task. But it shall not set this DDE when the task is resumed.
Typically used by Device Classes: 
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
17 - Sensor System
Unit: s - Time count
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Matthias Meyer
Submit Date: 2012-01-09
Submit Company: John Deere
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-02-15
Status Comments: 
Attachments: 
none



DD Entity: 276 Lifetime Fuel Consumption
Definition: Entire Fuel Consumption of the device lifetime.
Comment: This is the overall total of the device. This total does not refer to an application controlled by a Task Controller. Therefore this DDE shall not be setable within the device description and neither shall the device reset the value when the task status changed. It is up to the device control system when to reset this value.

The Working Set Master shall support the total trigger method for this DDE but shall not support the setable property.

The Task Controller can request and store this DDE at the end of a task. But it shall not set this DDE when the task is resumed.
Typically used by Device Classes: 
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
Unit: L - Capacity count
Resolution: 0,5
SAE SPN: 
Range: 0 - **********
Submit by: Matthias Meyer
Submit Date: 2012-01-09
Submit Company: John Deere
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-02-15
Status Comments: 
Attachments: 
none



DD Entity: 277 Lifetime Average Fuel Consumption per Time
Definition: Entire Average Fuel Consumption per Time of the device lifetime.
Comment: This is the overall average of the device. This average does not refer to an application controlled by a Task Controller. Therefore this DDE shall not be setable within the device description and neither shall the device reset the value when the task status changed. It is up to the device control system when to reset this value.

The Working Set Master shall support the total trigger method for this DDE but shall not support the setable property.

The Task Controller can request and store this DDE at the end of a task. But it shall not set this DDE when the task is resumed.
Typically used by Device Classes: 
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
Unit: mm³/s - Flow
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: 
Submit Date: 2012-01-09
Submit Company: 
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-02-15
Status Comments: 
Attachments: 
none



DD Entity: 278 Lifetime Average Fuel Consumption per Area
Definition: Entire Average Fuel Consumption per Area of the device lifetime.
Comment: This is the overall average of the device. This average does not refer to an application controlled by a Task Controller. Therefore this DDE shall not be setable within the device description and neither shall the device reset the value when the task status changed. It is up to the device control system when to reset this value.

The Working Set Master shall support the total trigger method for this DDE but shall not support the setable property.

The Task Controller can request and store this DDE at the end of a task. But it shall not set this DDE when the task is resumed.
Typically used by Device Classes: 
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
Unit: mm³/m² - Capacity per area unit
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Matthias Meyer
Submit Date: 2012-01-09
Submit Company: John Deere
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-02-15
Status Comments: 
Attachments: 
none



DD Entity: 279 Lifetime Yield Total Dry Mass
Definition: Entire Yield Total Dry Mass of the device lifetime.
Comment: This is the overall total of the device. This total does not refer to an application controlled by a Task Controller. Therefore this DDE shall not be setable within the device description and neither shall the device reset the value when the task status changed. It is up to the device control system when to reset this value.

The Working Set Master shall support the total trigger method for this DDE but shall not support the setable property.

The Task Controller can request and store this DDE at the end of a task. But it shall not set this DDE when the task is resumed.
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
Unit: kg - Mass
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Matthias Meyer
Submit Date: 2012-01-09
Submit Company: John Deere
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-02-15
Status Comments: 
Attachments: 
none



DD Entity: 280 Lifetime Yield Total Seed Cotton Mass
Definition: Entire Yield Total Seed Cotton Mass of the device lifetime.
Comment: This is the overall total of the device. This total does not refer to an application controlled by a Task Controller. Therefore this DDE shall not be setable within the device description and neither shall the device reset the value when the task status changed. It is up to the device control system when to reset this value.

The Working Set Master shall support the total trigger method for this DDE but shall not support the setable property.

The Task Controller can request and store this DDE at the end of a task. But it shall not set this DDE when the task is resumed.
Typically used by Device Classes: 
7 - Harvesters
14 - Special Crops
Unit: # - Quantity/Count
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Matthias Meyer
Submit Date: 2012-01-09
Submit Company: John Deere
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-02-15
Status Comments: 
Attachments: 
none



DD Entity: 281 Lifetime Yield Total Lint Cotton Mass
Definition: Entire Yield Total Lint Cotton Mass of the device lifetime.
Comment: This is the overall total of the device. This total does not refer to an application controlled by a Task Controller. Therefore this DDE shall not be setable within the device description and neither shall the device reset the value when the task status changed. It is up to the device control system when to reset this value.

The Working Set Master shall support the total trigger method for this DDE but shall not support the setable property.

The Task Controller can request and store this DDE at the end of a task. But it shall not set this DDE when the task is resumed.
Typically used by Device Classes: 
7 - Harvesters
14 - Special Crops
Unit: kg - Mass
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Matthias Meyer
Submit Date: 2012-01-09
Submit Company: John Deere
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-02-15
Status Comments: 
Attachments: 
none



DD Entity: 282 Lifetime Threshing Engagement Total Time
Definition: Entire Threshing Engagement Total Time of the device lifetime.
Comment: This is the overall total of the device. This total does not refer to an application controlled by a Task Controller. Therefore this DDE shall not be setable within the device description and neither shall the device reset the value when the task status changed. It is up to the device control system when to reset this value.

The Working Set Master shall support the total trigger method for this DDE but shall not support the setable property.

The Task Controller can request and store this DDE at the end of a task. But it shall not set this DDE when the task is resumed.
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
Unit: s - Time count
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Matthias Meyer
Submit Date: 2012-01-09
Submit Company: John Deere
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-02-15
Status Comments: 
Attachments: 
none



DD Entity: 283 Precut Total Count
Definition: The total number of pre-cutted product units produced by a device during an operation.
Comment: Precut Total Count is a total of a device element. It is intended to be used as a task based total value and therefore it is recommended to support the on-time and on-change trigger methods. The total trigger method and the setable property are required for this DDE.
Typically used by Device Classes: 
7 - Harvesters
9 - Forage harvester
Unit: # - Quantity/Count
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Matthias Meyer
Submit Date: 2012-01-12
Submit Company: John Deere
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-02-15
Status Comments: 
Attachments: 
none



DD Entity: 284 Uncut Total Count
Definition: The total number of un-cutted product units produced by a device during an operation.
Comment: Uncut Total Count is a total of a device element. It is intended to be used as a task based total value and therefore it is recommended to support the on-time and on-change trigger methods. The total trigger method and the setable property are required for this DDE.
Typically used by Device Classes: 
7 - Harvesters
9 - Forage harvester
Unit: # - Quantity/Count
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Matthias Meyer
Submit Date: 2012-01-12
Submit Company: John Deere
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-02-15
Status Comments: 
Attachments: 
none



DD Entity: 285 Lifetime Precut Total Count
Definition: Entire Precut Total Count of the device lifetime.
Comment: This is the overall total of the device. This total does not refer to an application controlled by a Task Controller. Therefore this DDE shall not be setable within the device description and neither shall the device reset the value when the task status changed. It is up to the device control system when to reset this value.

The Working Set Master shall support the total trigger method for this DDE but shall not support the setable property.

The Task Controller can request and store this DDE at the end of a task. But it shall not set this DDE when the task is resumed.
Typically used by Device Classes: 
7 - Harvesters
9 - Forage harvester
Unit: # - Quantity/Count
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Matthias Meyer
Submit Date: 2012-01-12
Submit Company: John Deere
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-02-15
Status Comments: 
Attachments: 
none



DD Entity: 286 Lifetime Uncut Total Count
Definition: Entire Uncut Total Count of the device lifetime.
Comment: This is the overall total of the device. This total does not refer to an application controlled by a Task Controller. Therefore this DDE shall not be setable within the device description and neither shall the device reset the value when the task status changed. It is up to the device control system when to reset this value.

The Working Set Master shall support the total trigger method for this DDE but shall not support the setable property.

The Task Controller can request and store this DDE at the end of a task. But it shall not set this DDE when the task is resumed.
Typically used by Device Classes: 
7 - Harvesters
9 - Forage harvester
Unit: # - Quantity/Count
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Matthias Meyer
Submit Date: 2012-01-12
Submit Company: John Deere
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-02-15
Status Comments: 
Attachments: 
none



DD Entity: 287 Setpoint Prescription Mode
Definition: This DDE defines the source of the Task Controller set point value sent to the Control Function. This DDI shall be defined as DPD in the DDOP and needs to be setable. The TC shall then set this DDI before starting a prescription operation. The WS (Working Set) shall set this value to zero (0) after system start.  
Comment: The Task Controller Prescription Mode shall have the following values:
  0 = Unknown / not defined
  1 = Prescription Rate
  2 = Prescription Default
  3 = Prescription GPS loss
  4 = Prescription Out Of Field
  5 = Manual Entry
  6 = Peer Control
  7 and higher are reserved for future assignments
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
Unit: n.a. - 
Resolution: 1
SAE SPN: 
Range: 0 - 6
Submit by: Joe Tevis
Submit Date: 2013-09-23
Submit Company: Topcon 
Revision Number: 2
Current Status: ISO-Submitted (Pending)
Status Date: 2015-07-30
Status Comments: 
Attachments: 
http://dictionary.isobus.net/isobus/attachments/401/Prescription Mode Supporting Doc-v1.ppt



DD Entity: 288 Actual Prescription Mode
Definition: This DDE defines the actual source of the set point value used by the Control Function. This DDI shall be defined as DPD in the DDOP and shall not be setable and need to support the on change trigger. The TC should request this DDI in case of an active prescription operation for documentation purpose. 
Comment: The Control Function Prescription Mode sahll have one of the following values:
  0 = Unknown / not defined
  1 = TC rate
  2 = Manual Entry
  3 = Peer Control
  4 = Max override
  5 = Min override
  6 and higher are reserved for future assignments
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
Unit: n.a. - 
Resolution: 1
SAE SPN: 
Range: 0 - 5
Submit by: Joe Tevis
Submit Date: 2013-09-23
Submit Company: Topcon
Revision Number: 2
Current Status: ISO-Submitted (Pending)
Status Date: 2015-07-30
Status Comments: 
Attachments: 
none



DD Entity: 289 Setpoint Work State
Definition: The Setpoint Work State DDI is the control command counterparts to the Work State DDI (141). The separation of the control commands through one DDI from the actual state communicated through another DDI enables verification of the transmission of the control commands independent from the effectuation of the requested control action.
Comment: See DDI definition attachment for backwards compatibility and implementation guidelines.
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
14 - Special Crops
Unit: n.a. - 
Resolution: 1
SAE SPN: 
Range: 0 - 3
Submit by: Jaap van Bergeijk
Submit Date: 2012-01-19
Submit Company: AGCO corporation
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-04-02
Status Comments: Status was published
Attachments: 
http://dictionary.isobus.net/isobus/attachments/344/ISO11783-11-DDI-289-SetpointWorkState-v1-v5.pdf



DD Entity: 290 Setpoint Condensed Work State (1-16)
Definition: The Setpoint Condensed Work State DDIs are the control command counterparts to the Condensed Work States DDIs (161 – 176).

The value is a combination of the setpoint work states of individual sections or units (e.g. nozzles) number 1 to 16 into a single setpoint work state of their parent device element. The setpoint condensed work state contains the child element setpoint work states, in the driving direction from left to right, where the leftmost child element setpoint work state are the 2 lowest significant bits of the Process Data Value. Each child device elements setpoint work state is represented by 2 bits and defined as: 00 = disable/off, 01 = enable/on, 10 = error indicator, 11 = no change. In total 16 child device element setpoint work states can be contained in one setpoint condensed work state of their parent device element. If less than 16 child device element setpoint work states are available, then the unused bits shall be set to value 11 (no change). When the parent device element contains the Setpoint Condensed Work State DDE, then the device descriptor shall not contain the individual setpoint work state DDEs in the child device elements.

Comment: See the Setpoint Work State DDI (289) attachment for backwards compatibility and implementation guidelines.
Typically used by Device Classes: 
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
14 - Special Crops
Unit: n.a. - 
Resolution: 1
SAE SPN: 
Range: 0 - 4294967295
Submit by: Jaap van Bergeijk
Submit Date: 2012-01-19
Submit Company: AGCO corporation
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-04-02
Status Comments: 
Attachments: 
http://dictionary.isobus.net/isobus/attachments/345/ISO11783-11-DDI-289-SetpointWorkState-v1.pdf



DD Entity: 291 Setpoint Condensed Work State (17-32)
Definition: The Setpoint Condensed Work State DDIs are the control command counterparts to the Condensed Work States DDIs (161 – 176).

The value is a combination of the setpoint work states of individual sections or units (e.g. nozzles) number 17 to 32 into a single setpoint work state of their parent device element. The setpoint condensed work state contains the child element setpoint work states, in the driving direction from left to right, where the leftmost child element setpoint work state are the 2 lowest significant bits of the Process Data Value. Each child device elements setpoint work state is represented by 2 bits and defined as: 00 = disable/off, 01 = enable/on, 10 = error indicator, 11 = no change. In total 16 child device element setpoint work states can be contained in one setpoint condensed work state of their parent device element. If less than 16 child device element setpoint work states are available, then the unused bits shall be set to value 11 (no change). When the parent device element contains the Setpoint Condensed Work State DDE, then the device descriptor shall not contain the individual setpoint work state DDEs in the child device elements.

Comment: See the Setpoint Work State DDI (289) attachment for backwards compatibility and implementation guidelines.

Typically used by Device Classes: 
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
14 - Special Crops
Unit: n.a. - 
Resolution: 1
SAE SPN: 
Range: 0 - 4294967295
Submit by: Jaap van Bergeijk
Submit Date: 2012-01-19
Submit Company: AGCO corporation
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-04-02
Status Comments: 
Attachments: 
none



DD Entity: 292 Setpoint Condensed Work State (33-48)
Definition: The Setpoint Condensed Work State DDIs are the control command counterparts to the Condensed Work States DDIs (161 – 176).

The value is a combination of the setpoint work states of individual sections or units (e.g. nozzles) number 33 to 48 into a single setpoint work state of their parent device element. The setpoint condensed work state contains the child element setpoint work states, in the driving direction from left to right, where the leftmost child element setpoint work state are the 2 lowest significant bits of the Process Data Value. Each child device elements setpoint work state is represented by 2 bits and defined as: 00 = disable/off, 01 = enable/on, 10 = error indicator, 11 = no change. In total 16 child device element setpoint work states can be contained in one setpoint condensed work state of their parent device element. If less than 16 child device element setpoint work states are available, then the unused bits shall be set to value 11 (no change). When the parent device element contains the Setpoint Condensed Work State DDE, then the device descriptor shall not contain the individual setpoint work state DDEs in the child device elements.

Comment: See the Setpoint Work State DDI (289) attachment for backwards compatibility and implementation guidelines.
Typically used by Device Classes: 
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
14 - Special Crops
Unit: n.a. - 
Resolution: 1
SAE SPN: 
Range: 0 - 4294967295
Submit by: Jaap van Bergeijk
Submit Date: 2012-01-19
Submit Company: AGCO corporation
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-04-02
Status Comments: 
Attachments: 
none



DD Entity: 293 Setpoint Condensed Work State (49-64)
Definition: The Setpoint Condensed Work State DDIs are the control command counterparts to the Condensed Work States DDIs (161 – 176).

The value is a combination of the setpoint work states of individual sections or units (e.g. nozzles) number 49 to 64 into a single setpoint work state of their parent device element. The setpoint condensed work state contains the child element setpoint work states, in the driving direction from left to right, where the leftmost child element setpoint work state are the 2 lowest significant bits of the Process Data Value. Each child device elements setpoint work state is represented by 2 bits and defined as: 00 = disable/off, 01 = enable/on, 10 = error indicator, 11 = no change. In total 16 child device element setpoint work states can be contained in one setpoint condensed work state of their parent device element. If less than 16 child device element setpoint work states are available, then the unused bits shall be set to value 11 (no change). When the parent device element contains the Setpoint Condensed Work State DDE, then the device descriptor shall not contain the individual setpoint work state DDEs in the child device elements.

Comment: See the Setpoint Work State DDI (289) attachment for backwards compatibility and implementation guidelines.

Typically used by Device Classes: 
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
14 - Special Crops
Unit: n.a. - 
Resolution: 1
SAE SPN: 
Range: 0 - 4294967295
Submit by: Jaap van Bergeijk
Submit Date: 2012-01-19
Submit Company: AGCO corporation
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-04-02
Status Comments: 
Attachments: 
none



DD Entity: 294 Setpoint Condensed Work State (65-80)
Definition: The Setpoint Condensed Work State DDIs are the control command counterparts to the Condensed Work States DDIs (161 – 176).

The value is a combination of the setpoint work states of individual sections or units (e.g. nozzles) number 65 to 80 into a single setpoint work state of their parent device element. The setpoint condensed work state contains the child element setpoint work states, in the driving direction from left to right, where the leftmost child element setpoint work state are the 2 lowest significant bits of the Process Data Value. Each child device elements setpoint work state is represented by 2 bits and defined as: 00 = disable/off, 01 = enable/on, 10 = error indicator, 11 = no change. In total 16 child device element setpoint work states can be contained in one setpoint condensed work state of their parent device element. If less than 16 child device element setpoint work states are available, then the unused bits shall be set to value 11 (no change). When the parent device element contains the Setpoint Condensed Work State DDE, then the device descriptor shall not contain the individual setpoint work state DDEs in the child device elements.

Comment: See the Setpoint Work State DDI (289) attachment for backwards compatibility and implementation guidelines.

Typically used by Device Classes: 
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
14 - Special Crops
Unit: n.a. - 
Resolution: 1
SAE SPN: 
Range: 0 - 4294967295
Submit by: Jaap van Bergeijk
Submit Date: 2012-01-19
Submit Company: AGCO corporation
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-04-02
Status Comments: 
Attachments: 
none



DD Entity: 295 Setpoint Condensed Work State (81-96)
Definition: The Setpoint Condensed Work State DDIs are the control command counterparts to the Condensed Work States DDIs (161 – 176).

The value is a combination of the setpoint work states of individual sections or units (e.g. nozzles) number 81 to 96 into a single setpoint work state of their parent device element. The setpoint condensed work state contains the child element setpoint work states, in the driving direction from left to right, where the leftmost child element setpoint work state are the 2 lowest significant bits of the Process Data Value. Each child device elements setpoint work state is represented by 2 bits and defined as: 00 = disable/off, 01 = enable/on, 10 = error indicator, 11 = no change. In total 16 child device element setpoint work states can be contained in one setpoint condensed work state of their parent device element. If less than 16 child device element setpoint work states are available, then the unused bits shall be set to value 11 (no change). When the parent device element contains the Setpoint Condensed Work State DDE, then the device descriptor shall not contain the individual setpoint work state DDEs in the child device elements.

Comment: See the Setpoint Work State DDI (289) attachment for backwards compatibility and implementation guidelines.

Typically used by Device Classes: 
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
14 - Special Crops
Unit: n.a. - 
Resolution: 1
SAE SPN: 
Range: 0 - 4294967295
Submit by: Jaap van Bergeijk
Submit Date: 2012-01-19
Submit Company: AGCO corporation
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-04-02
Status Comments: 
Attachments: 
none



DD Entity: 296 Setpoint Condensed Work State (97-112)
Definition: The Setpoint Condensed Work State DDIs are the control command counterparts to the Condensed Work States DDIs (161 – 176).

The value is a combination of the setpoint work states of individual sections or units (e.g. nozzles) number 97 to 112 into a single setpoint work state of their parent device element. The setpoint condensed work state contains the child element setpoint work states, in the driving direction from left to right, where the leftmost child element setpoint work state are the 2 lowest significant bits of the Process Data Value. Each child device elements setpoint work state is represented by 2 bits and defined as: 00 = disable/off, 01 = enable/on, 10 = error indicator, 11 = no change. In total 16 child device element setpoint work states can be contained in one setpoint condensed work state of their parent device element. If less than 16 child device element setpoint work states are available, then the unused bits shall be set to value 11 (no change). When the parent device element contains the Setpoint Condensed Work State DDE, then the device descriptor shall not contain the individual setpoint work state DDEs in the child device elements.

Comment: See the Setpoint Work State DDI (289) attachment for backwards compatibility and implementation guidelines.

Typically used by Device Classes: 
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
14 - Special Crops
Unit: n.a. - 
Resolution: 1
SAE SPN: 
Range: 0 - 4294967295
Submit by: Jaap van Bergeijk
Submit Date: 2012-01-19
Submit Company: AGCO corporation
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-04-02
Status Comments: 
Attachments: 
none



DD Entity: 297 Setpoint Condensed Work State (113-128)
Definition: The Setpoint Condensed Work State DDIs are the control command counterparts to the Condensed Work States DDIs (161 – 176).

The value is a combination of the setpoint work states of individual sections or units (e.g. nozzles) number 1 to 16 into a single setpoint work state of their parent device element. The setpoint condensed work state contains the child element setpoint work states, in the driving direction from left to right, where the leftmost child element setpoint work state are the 2 lowest significant bits of the Process Data Value. Each child device elements setpoint work state is represented by 2 bits and defined as: 00 = disable/off, 01 = enable/on, 10 = error indicator, 11 = no change. In total 16 child device element setpoint work states can be contained in one setpoint condensed work state of their parent device element. If less than 16 child device element setpoint work states are available, then the unused bits shall be set to value 11 (no change). When the parent device element contains the Setpoint Condensed Work State DDE, then the device descriptor shall not contain the individual setpoint work state DDEs in the child device elements.

Comment: See the Setpoint Work State DDI (289) attachment for backwards compatibility and implementation guidelines.

Typically used by Device Classes: 
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
14 - Special Crops
Unit: n.a. - 
Resolution: 1
SAE SPN: 
Range: 0 - 4294967295
Submit by: Jaap van Bergeijk
Submit Date: 2012-01-19
Submit Company: AGCO corporation
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-04-02
Status Comments: 
Attachments: 
none



DD Entity: 298 Setpoint Condensed Work State (129-144)
Definition: The Setpoint Condensed Work State DDIs are the control command counterparts to the Condensed Work States DDIs (161 – 176).

The value is a combination of the setpoint work states of individual sections or units (e.g. nozzles) number 129 to 144 into a single setpoint work state of their parent device element. The setpoint condensed work state contains the child element setpoint work states, in the driving direction from left to right, where the leftmost child element setpoint work state are the 2 lowest significant bits of the Process Data Value. Each child device elements setpoint work state is represented by 2 bits and defined as: 00 = disable/off, 01 = enable/on, 10 = error indicator, 11 = no change. In total 16 child device element setpoint work states can be contained in one setpoint condensed work state of their parent device element. If less than 16 child device element setpoint work states are available, then the unused bits shall be set to value 11 (no change). When the parent device element contains the Setpoint Condensed Work State DDE, then the device descriptor shall not contain the individual setpoint work state DDEs in the child device elements.

Comment: See the Setpoint Work State DDI (289) attachment for backwards compatibility and implementation guidelines.

Typically used by Device Classes: 
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
14 - Special Crops
Unit: n.a. - 
Resolution: 1
SAE SPN: 
Range: 0 - 4294967295
Submit by: Jaap van Bergeijk
Submit Date: 2012-01-19
Submit Company: AGCO corporation
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-04-02
Status Comments: 
Attachments: 
none



DD Entity: 299 Setpoint Condensed Work State (145-160)
Definition: The Setpoint Condensed Work State DDIs are the control command counterparts to the Condensed Work States DDIs (161 – 176).

The value is a combination of the setpoint work states of individual sections or units (e.g. nozzles) number 145 to 160 into a single setpoint work state of their parent device element. The setpoint condensed work state contains the child element setpoint work states, in the driving direction from left to right, where the leftmost child element setpoint work state are the 2 lowest significant bits of the Process Data Value. Each child device elements setpoint work state is represented by 2 bits and defined as: 00 = disable/off, 01 = enable/on, 10 = error indicator, 11 = no change. In total 16 child device element setpoint work states can be contained in one setpoint condensed work state of their parent device element. If less than 16 child device element setpoint work states are available, then the unused bits shall be set to value 11 (no change). When the parent device element contains the Setpoint Condensed Work State DDE, then the device descriptor shall not contain the individual setpoint work state DDEs in the child device elements.

Comment: See the Setpoint Work State DDI (289) attachment for backwards compatibility and implementation guidelines.

Typically used by Device Classes: 
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
14 - Special Crops
Unit: n.a. - 
Resolution: 1
SAE SPN: 
Range: 0 - 4294967295
Submit by: Jaap van Bergeijk
Submit Date: 2012-01-19
Submit Company: AGCO corporation
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-04-02
Status Comments: 
Attachments: 
none



DD Entity: 300 Setpoint Condensed Work State (161-176)
Definition: The Setpoint Condensed Work State DDIs are the control command counterparts to the Condensed Work States DDIs (161 – 176).

The value is a combination of the setpoint work states of individual sections or units (e.g. nozzles) number 161 to 176 into a single setpoint work state of their parent device element. The setpoint condensed work state contains the child element setpoint work states, in the driving direction from left to right, where the leftmost child element setpoint work state are the 2 lowest significant bits of the Process Data Value. Each child device elements setpoint work state is represented by 2 bits and defined as: 00 = disable/off, 01 = enable/on, 10 = error indicator, 11 = no change. In total 16 child device element setpoint work states can be contained in one setpoint condensed work state of their parent device element. If less than 16 child device element setpoint work states are available, then the unused bits shall be set to value 11 (no change). When the parent device element contains the Setpoint Condensed Work State DDE, then the device descriptor shall not contain the individual setpoint work state DDEs in the child device elements.

Comment: See the Setpoint Work State DDI (289) attachment for backwards compatibility and implementation guidelines.

Typically used by Device Classes: 
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
14 - Special Crops
Unit: n.a. - 
Resolution: 1
SAE SPN: 
Range: 0 - 4294967295
Submit by: Jaap van Bergeijk
Submit Date: 2012-01-19
Submit Company: AGCO corporation
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-04-02
Status Comments: 
Attachments: 
none



DD Entity: 301 Setpoint Condensed Work State (177-192)
Definition: The Setpoint Condensed Work State DDIs are the control command counterparts to the Condensed Work States DDIs (161 – 176).

The value is a combination of the setpoint work states of individual sections or units (e.g. nozzles) number 177 to 192 into a single setpoint work state of their parent device element. The setpoint condensed work state contains the child element setpoint work states, in the driving direction from left to right, where the leftmost child element setpoint work state are the 2 lowest significant bits of the Process Data Value. Each child device elements setpoint work state is represented by 2 bits and defined as: 00 = disable/off, 01 = enable/on, 10 = error indicator, 11 = no change. In total 16 child device element setpoint work states can be contained in one setpoint condensed work state of their parent device element. If less than 16 child device element setpoint work states are available, then the unused bits shall be set to value 11 (no change). When the parent device element contains the Setpoint Condensed Work State DDE, then the device descriptor shall not contain the individual setpoint work state DDEs in the child device elements.

Comment: See the Setpoint Work State DDI (289) attachment for backwards compatibility and implementation guidelines.

Typically used by Device Classes: 
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
14 - Special Crops
Unit: n.a. - 
Resolution: 1
SAE SPN: 
Range: 0 - 4294967295
Submit by: Jaap van Bergeijk
Submit Date: 2012-01-19
Submit Company: AGCO corporation
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-04-02
Status Comments: 
Attachments: 
none



DD Entity: 302 Setpoint Condensed Work State (193-208)
Definition: The Setpoint Condensed Work State DDIs are the control command counterparts to the Condensed Work States DDIs (161 – 176).

The value is a combination of the setpoint work states of individual sections or units (e.g. nozzles) number 193 to 208 into a single setpoint work state of their parent device element. The setpoint condensed work state contains the child element setpoint work states, in the driving direction from left to right, where the leftmost child element setpoint work state are the 2 lowest significant bits of the Process Data Value. Each child device elements setpoint work state is represented by 2 bits and defined as: 00 = disable/off, 01 = enable/on, 10 = error indicator, 11 = no change. In total 16 child device element setpoint work states can be contained in one setpoint condensed work state of their parent device element. If less than 16 child device element setpoint work states are available, then the unused bits shall be set to value 11 (no change). When the parent device element contains the Setpoint Condensed Work State DDE, then the device descriptor shall not contain the individual setpoint work state DDEs in the child device elements.

Comment: See the Setpoint Work State DDI (289) attachment for backwards compatibility and implementation guidelines.

Typically used by Device Classes: 
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
14 - Special Crops
Unit: n.a. - 
Resolution: 1
SAE SPN: 
Range: 0 - 4294967295
Submit by: Jaap van Bergeijk
Submit Date: 2012-01-19
Submit Company: AGCO corporation
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-04-02
Status Comments: 
Attachments: 
none



DD Entity: 303 Setpoint Condensed Work State (209-224)
Definition: The Setpoint Condensed Work State DDIs are the control command counterparts to the Condensed Work States DDIs (161 – 176).

The value is a combination of the setpoint work states of individual sections or units (e.g. nozzles) number 209 to 224 into a single setpoint work state of their parent device element. The setpoint condensed work state contains the child element setpoint work states, in the driving direction from left to right, where the leftmost child element setpoint work state are the 2 lowest significant bits of the Process Data Value. Each child device elements setpoint work state is represented by 2 bits and defined as: 00 = disable/off, 01 = enable/on, 10 = error indicator, 11 = no change. In total 16 child device element setpoint work states can be contained in one setpoint condensed work state of their parent device element. If less than 16 child device element setpoint work states are available, then the unused bits shall be set to value 11 (no change). When the parent device element contains the Setpoint Condensed Work State DDE, then the device descriptor shall not contain the individual setpoint work state DDEs in the child device elements.

Comment: See the Setpoint Work State DDI (289) attachment for backwards compatibility and implementation guidelines.

Typically used by Device Classes: 
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
14 - Special Crops
Unit: n.a. - 
Resolution: 1
SAE SPN: 
Range: 0 - 4294967295
Submit by: Jaap van Bergeijk
Submit Date: 2012-01-19
Submit Company: AGCO corporation
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-04-02
Status Comments: 
Attachments: 
none



DD Entity: 304 Setpoint Condensed Work State (225-240)
Definition: The Setpoint Condensed Work State DDIs are the control command counterparts to the Condensed Work States DDIs (161 – 176).

The value is a combination of the setpoint work states of individual sections or units (e.g. nozzles) number 225 to 240 into a single setpoint work state of their parent device element. The setpoint condensed work state contains the child element setpoint work states, in the driving direction from left to right, where the leftmost child element setpoint work state are the 2 lowest significant bits of the Process Data Value. Each child device elements setpoint work state is represented by 2 bits and defined as: 00 = disable/off, 01 = enable/on, 10 = error indicator, 11 = no change. In total 16 child device element setpoint work states can be contained in one setpoint condensed work state of their parent device element. If less than 16 child device element setpoint work states are available, then the unused bits shall be set to value 11 (no change). When the parent device element contains the Setpoint Condensed Work State DDE, then the device descriptor shall not contain the individual setpoint work state DDEs in the child device elements.

Comment: See the Setpoint Work State DDI (289) attachment for backwards compatibility and implementation guidelines.

Typically used by Device Classes: 
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
14 - Special Crops
Unit: n.a. - 
Resolution: 1
SAE SPN: 
Range: 0 - 4294967295
Submit by: Jaap van Bergeijk
Submit Date: 2012-01-19
Submit Company: AGCO corporation
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-04-02
Status Comments: 
Attachments: 
none



DD Entity: 305 Setpoint Condensed Work State (241-256)
Definition: The Setpoint Condensed Work State DDIs are the control command counterparts to the Condensed Work States DDIs (161 – 176).

The value is a combination of the setpoint work states of individual sections or units (e.g. nozzles) number 241 to 256 into a single setpoint work state of their parent device element. The setpoint condensed work state contains the child element setpoint work states, in the driving direction from left to right, where the leftmost child element setpoint work state are the 2 lowest significant bits of the Process Data Value. Each child device elements setpoint work state is represented by 2 bits and defined as: 00 = disable/off, 01 = enable/on, 10 = error indicator, 11 = no change. In total 16 child device element setpoint work states can be contained in one setpoint condensed work state of their parent device element. If less than 16 child device element setpoint work states are available, then the unused bits shall be set to value 11 (no change). When the parent device element contains the Setpoint Condensed Work State DDE, then the device descriptor shall not contain the individual setpoint work state DDEs in the child device elements.


Comment: See the Setpoint Work State DDI (289) attachment for backwards compatibility and implementation guidelines.

Typically used by Device Classes: 
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
14 - Special Crops
Unit: n.a. - 
Resolution: 1
SAE SPN: 
Range: 0 - 4294967295
Submit by: Jaap van Bergeijk
Submit Date: 2012-01-19
Submit Company: AGCO corporation
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-04-02
Status Comments: Status was published
Attachments: 
none



DD Entity: 306 True Rotation Point  X-Offset 
Definition: X direction offset of the device rotation point relative to the DRP.
Comment: For devices with more than one axle the rotation point can be located at another position within the device than the DRP. In this case, the True Rotation Point X and Y Offset DDIs shall be used to define the location of the rotation point on the device. Both DDI's shall be attached to the device element of type Device.
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
14 - Special Crops
15 - Municipal Work
Unit: mm - Length
Resolution: 1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Matthias Meyer
Submit Date: 2012-06-05
Submit Company: John Deere
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-07-03
Status Comments: 
Attachments: 
http://dictionary.isobus.net/isobus/attachments/370/ISO11783-11-DDI-306-True Rotation Point-v1.pdf



DD Entity: 307 True Rotation Point Y-Offset
Definition: Y direction offset of the device rotation point relative to the DRP.
Comment: For devices with more than one axle the rotation point can be located at another position within the device than the DRP. In this case, the True Rotation Point X and Y Offset DDIs shall be used to define the location of the rotation point on the device. Both DDI's shall be attached to the device element of type Device. See also attachment of True Rotation Point X-Offset, DDI 306.
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
14 - Special Crops
15 - Municipal Work
Unit: mm - Length
Resolution: 1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Matthias Meyer
Submit Date: 2012-06-05
Submit Company: John Deere
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-07-03
Status Comments: 
Attachments: 
none



DD Entity: 308 Actual Percentage Application Rate
Definition: Actual Application Rate expressed as percentage
Comment: Counterpart to DDI 140 (Percentage Application Rate Setpoint)
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
10 - Irrigation
Unit: ppm - Parts per million
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Jan Steenbock
Submit Date: 2012-06-05
Submit Company: 98 - Müller-Elektronik GmbH & Co. KG
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-07-03
Status Comments: 
Attachments: 
none



DD Entity: 309 Minimum Percentage Application Rate
Definition: Minimum Application Rate expressed as percentage
Comment: Supplied by device as physical minimum, see also DDI 140.
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
10 - Irrigation
Unit: ppm - Parts per million
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Matthias Meyer
Submit Date: 2012-07-03
Submit Company: John Deere
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-07-03
Status Comments: 
Attachments: 
none



DD Entity: 310 Maximum Percentage Application Rate
Definition: Maximum Application Rate expressed as percentage
Comment: Supplied by device as physical maximum, see also DDI 140.
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
10 - Irrigation
Unit: ppm - Parts per million
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Matthias Meyer
Submit Date: 2012-07-03
Submit Company: John Deere
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-07-03
Status Comments: 
Attachments: 
none



DD Entity: 311 Relative Yield Potential
Definition: Relative yield potential provided by a FMIS or a sensor or entered by the operator for a certain task expressed as percentage.
Comment: Relative yield potential could be used as input for an intelligent unit to calculate the appropriate amount of fertilizer / seed / etc. more accurate. Typical range is 80 to 120%. Expressed in ppm this is 800,000 to 1,200,000.
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
17 - Sensor System
Unit: ppm - Parts per million
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Martin Sperlich
Submit Date: 2012-07-26
Submit Company: CLAAS Agrosystems GmbH & Co. KG
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-08-27
Status Comments: 
Attachments: 
none



DD Entity: 312 Minimum Relative Yield Potential
Definition: Minimum potential yield expressed as percentage.
Comment: This DDIs is used by the system to provide information about its value range support for relative yield potential.
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
17 - Sensor System
Unit: ppm - Parts per million
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Martin Sperlich
Submit Date: 2012-07-29
Submit Company: CLAAS Agrosystems GmbH & Co. KG
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-08-27
Status Comments: 
Attachments: 
none



DD Entity: 313 Maximum Relative Yield Potential
Definition: Maximum potential yield expressed as percentage.
Comment: This DDIs is used by the system to provide information about its value range support for relative yield potential.
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
17 - Sensor System
Unit: ppm - Parts per million
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Martin Sperlich
Submit Date: 2012-08-27
Submit Company: CLAAS Agrosystems GmbH & Co. KG
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-08-27
Status Comments: 
Attachments: 
none



DD Entity: 314 Actual Percentage Crop Dry Matter
Definition: Actual Percentage Crop Dry Matter expressed as parts per million. 
Comment: This DDE defines the actual percentage of dry matter in the harvested crop.
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
17 - Sensor System
Unit: ppm - Parts per million
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Martin Sperlich
Submit Date: 2012-09-17
Submit Company: CLAAS Agrosystems GmbH & Co. KG
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-09-24
Status Comments: 
Attachments: 
none



DD Entity: 315 Average Percentage Crop Dry Matter 
Definition: Average Percentage Crop Dry Matter expressed as parts per million.
Comment: This DDE defines the average percentage of dry matter in the harvested crop.
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
17 - Sensor System
Unit: ppm - Parts per million
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Martin Sperlich
Submit Date: 2012-09-17
Submit Company: CLAAS Agrosystems GmbH & Co. KG
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-09-24
Status Comments: 
Attachments: 
none



DD Entity: 316 Effective Total Fuel Consumption
Definition: Accumulated total fuel consumption in working position.
Comment: 
Typically used by Device Classes: 
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
Unit: ml - Capacity large
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Martin Sperlich
Submit Date: 2012-09-17
Submit Company: CLAAS Agrosystems GmbH & Co. KG
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-09-24
Status Comments: 
Attachments: 
none



DD Entity: 317 Ineffective Total Fuel Consumption
Definition: Accumulated total fuel consumption in non working position.
Comment: 
Typically used by Device Classes: 
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
Unit: ml - Capacity large
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Martin Sperlich
Submit Date: 2012-09-17
Submit Company: CLAAS Agrosystems GmbH & Co. KG
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-09-24
Status Comments: 
Attachments: 
none



DD Entity: 318 Effective Total Diesel Exhaust Fluid Consumption
Definition: Accumulated total Diesel Exhaust Fluid consumption in working position.
Comment: Example: Diesel Exhaust Fluid as specified per ISO22241.
Typically used by Device Classes: 
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
Unit: ml - Capacity large
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Martin Sperlich
Submit Date: 2012-09-17
Submit Company: CLAAS Agrosystems GmbH & Co. KG
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-09-24
Status Comments: 
Attachments: 
none



DD Entity: 319 Ineffective Total Diesel Exhaust Fluid Consumption
Definition: Accumulated total Diesel Exhaust Fluid consumption in non working position.
Comment: Example: Diesel Exhaust Fluid as specified per ISO22241.
Typically used by Device Classes: 
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
Unit: ml - Capacity large
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Martin Sperlich
Submit Date: 2012-09-24
Submit Company: CLAAS Agrosystems GmbH & Co. KG
Revision Number: 1
Current Status: ISO-Published
Status Date: 2012-09-24
Status Comments: 
Attachments: 
none



DD Entity: 320 Last loaded Weight
Definition: Last loaded Weight value specified as mass
Comment: After a loading Procedure, this DDI sends the loaded Mass. 

For more information see attachment located at Last loaded Weight DDE
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
11 - Transport / Trailers
17 - Sensor System
Unit: g - Mass large
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Franz Hoepfinger
Submit Date: 2013-01-14
Submit Company: 367 - Fliegl Agrartechnik GmbH
Revision Number: 1
Current Status: ISO-Published
Status Date: 2013-02-04
Status Comments: 
Attachments: 
http://dictionary.isobus.net/isobus/attachments/388/ISO_11783-11-DDI_320-Weighing_Load_Unload-v3.pdf



DD Entity: 321 Last unloaded Weight
Definition: Last unloaded Weight value specified as mass
Comment: After a unloading Procedure, this DDI sends the unloaded Mass. 

For more information see attachment located at Last loaded Weight DDE
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
11 - Transport / Trailers
17 - Sensor System
Unit: g - Mass large
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Franz Hoepfinger
Submit Date: 2013-01-14
Submit Company: 367 - Fliegl Agrartechnik GmbH
Revision Number: 1
Current Status: ISO-Published
Status Date: 2013-02-04
Status Comments: 
Attachments: 
none



DD Entity: 322 Load Identification Number
Definition: The Load Identification Number as a decimal number in the range of 0 to 4294967295. Note that the value of this DDI has the limitation of being an unsigned 32 bit number.
Comment: The DDI Load Identification Number can be used together with the DDI “320 - Last loaded Weight” to document the loading of material on a weighing system. See also the attached document for more details. 
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
14 - Special Crops
17 - Sensor System
Unit: # - Quantity/Count
Resolution: 1
SAE SPN: 
Range: 0 - 4294967295
Submit by: Franz Hoepfinger
Submit Date: 2013-05-21
Submit Company: 367 - Fliegl Agrartechnik GmbH
Revision Number: 1
Current Status: ISO-Published
Status Date: 2013-07-15
Status Comments: 
Attachments: 
http://dictionary.isobus.net/isobus/attachments/393/ISO11783-11-DDI-322-Load_Identification_Number_v1-v2.pdf



DD Entity: 323 Unload Identification Number
Definition: The Unload Identification Number as a decimal number in the range of 0 to **********. Note that the value of this DDI has the limitation of being an unsigned 32 bit number.
Comment: The DDI Unload Identification Number can be used together with the DDI “321 - Last Unloaded Weight” to document the unloading of material on a weighing system. See also the attached document for more details.
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
14 - Special Crops
17 - Sensor System
Unit: # - Quantity/Count
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Franz Hoepfinger
Submit Date: 2013-07-15
Submit Company: 367 - Fliegl Agratechnik GmbH
Revision Number: 1
Current Status: ISO-Published
Status Date: 2013-07-15
Status Comments: 
Attachments: 
http://dictionary.isobus.net/isobus/attachments/399/ISO11783-11-DDI-323-Unload_Identification_Number_v1-v1.pdf



DD Entity: 324 Chopper Engagement Total Time
Definition: Accumulated time while the chopping mechanism is engaged

Comment: This DDE represents the total engagement time of the chopping mechanism of the machine and is recommended to be used at maximum once within the device description in the device element that represents the machine.
This DDE is designated for the chopping unit of a forage harvester. It could be also used for the straw chopper of a harvester. For combine harvesters please also see DDE 236 Threshing Engagement Total Time.

Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
Unit: s - Time count
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Martin Sperlich
Submit Date: 2013-06-17
Submit Company: CLAAS Agrosystems KGaA mbH & Co KG
Revision Number: 1
Current Status: ISO-Published
Status Date: 2013-07-15
Status Comments: 
Attachments: 
none



DD Entity: 325 Lifetime Application Total Volume
Definition: Entire Application Total Volume of the device lifetime.
Comment: This is the overall total of the device. This total does not refer to an
application controlled by a Task Controller. Therefore this DDE shall
not be setable within the device description and neither shall the
device reset the value when the task status changes. It is up to the
device control system when to reset this value.
The Working Set Master shall support the total trigger method for this
DDE but shall not support the setable property.
The Task Controller can request and store this DDE at the end of a
task. But it shall not set this DDE when the task is resumed.
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
9 - Forage harvester
10 - Irrigation
Unit: L - Capacity count
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Michael Köcher
Submit Date: 2013-09-02
Submit Company: AMAZONE
Revision Number: 1
Current Status: ISO-Published
Status Date: 2013-09-27
Status Comments: 
Attachments: 
none



DD Entity: 326 Setpoint Header Speed
Definition: The setpoint rotational speed of the header attachment of a chopper, mower or combine
Comment: 
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
15 - Municipal Work
Unit: /s - Quantity per time unit
Resolution: 0,001
SAE SPN: 
Range: 0 - **********
Submit by: Frank Wiebeler
Submit Date: 2015-11-27
Submit Company: Maschinenfabrik Bernard Krone GmbH
Revision Number: 1
Current Status: ISO-Published
Status Date: 2013-12-04
Status Comments: 
Attachments: 
none



DD Entity: 327 Actual Header Speed
Definition: The actual rotational speed of the header attachment of a chopper, mower or combine
Comment: 
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
15 - Municipal Work
Unit: /s - Quantity per time unit
Resolution: 0,001
SAE SPN: 
Range: 0 - **********
Submit by: Frank Wiebeler
Submit Date: 2013-12-04
Submit Company: Maschinenfabrik Bernard Krone GmbH
Revision Number: 1
Current Status: ISO-Published
Status Date: 2013-12-04
Status Comments: 
Attachments: 
none



DD Entity: 328 Minimum Header Speed
Definition: The minimum rotational speed of the header attachment of a chopper, mower or combine
Comment: This is a value recommented by the manufacturer of the machine as the minimum
speed (unlike 0) for a propper working
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
15 - Municipal Work
Unit: /s - Quantity per time unit
Resolution: 0,001
SAE SPN: 
Range: 0 - **********
Submit by: Frank Wiebeler
Submit Date: 2013-12-04
Submit Company: Maschinenfabrik Bernard Krone GmbH
Revision Number: 1
Current Status: ISO-Published
Status Date: 2014-01-17
Status Comments: 
Attachments: 
none



DD Entity: 329 Maximum Header Speed
Definition: The maximum rotational speed of the header attachment of a chopper, mower or combine
Comment: This is a value recommented by the manufacturer of the machine as the maximum speed the machine is able to offer
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
15 - Municipal Work
Unit: /s - Quantity per time unit
Resolution: 0,001
SAE SPN: 
Range: 0 - **********
Submit by: Frank Wiebeler
Submit Date: 2013-12-04
Submit Company: Maschinenfabrik Bernard Krone GmbH
Revision Number: 1
Current Status: ISO-Published
Status Date: 2014-01-17
Status Comments: 
Attachments: 
none



DD Entity: 330 Setpoint Cutting drum speed
Definition: The setpoint speed of the cutting drum of a chopper 
Comment: 
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
15 - Municipal Work
Unit: /s - Quantity per time unit
Resolution: 0,001
SAE SPN: 
Range: 0 - **********
Submit by: Frank Wiebeler
Submit Date: 2013-12-04
Submit Company: Maschinenfabrik Bernard Krone GmbH
Revision Number: 1
Current Status: ISO-Published
Status Date: 2014-01-17
Status Comments: 
Attachments: 
none



DD Entity: 331 Actual Cutting drum speed
Definition: The actual speed of the cutting drum of a chopper
Comment: 
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
15 - Municipal Work
Unit: /s - Quantity per time unit
Resolution: 0,001
SAE SPN: 
Range: 0 - **********
Submit by: Frank Wiebeler
Submit Date: 2013-12-04
Submit Company: Maschinenfabrik Bernard Krone GmbH
Revision Number: 1
Current Status: ISO-Published
Status Date: 2014-01-17
Status Comments: 
Attachments: 
none



DD Entity: 333 Maximum Cutting drum speed
Definition: The maximum speed of the cutting drum of a chopper
Comment: This is a value recommented by the manufacturer of the machine as the maximum speed the machine is able to offer
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
15 - Municipal Work
Unit: /s - Quantity per time unit
Resolution: 0,001
SAE SPN: 
Range: 0 - **********
Submit by: Frank Wiebeler
Submit Date: 2013-12-04
Submit Company: Maschinenfabrik Bernard Krone GmbH
Revision Number: 1
Current Status: ISO-Published
Status Date: 2014-01-17
Status Comments: 
Attachments: 
none



DD Entity: 334 Operating Hours Since Last Sharpening
Definition: This value describes the working hours since the last sharpening of the cutting device.
Comment: As the sharpness of the cutting drums cutters on a harvester is an important indicator for cutting quality and an important factor for the fuel usage, this value provides information about quality and effectivity of the harvesting process
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
12 - Farmyard Work
14 - Special Crops
15 - Municipal Work
Unit: s - Time count
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Frank Wiebeler
Submit Date: 2013-12-04
Submit Company: Maschinenfabrik Bernard Krone GmbH
Revision Number: 1
Current Status: ISO-Published
Status Date: 2014-01-17
Status Comments: 
Attachments: 
none



DD Entity: 335 Front PTO hours
Definition: The hours the Front PTO of the machine was running for the current Task
Comment: This value provides information of the active working time for example of the header attachment of a selfpropelled machine
Typically used by Device Classes: 
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
Unit: s - Time count
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Frank Wiebeler
Submit Date: 2013-12-04
Submit Company: Maschinenfabrik Bernard Krone GmbH
Revision Number: 1
Current Status: ISO-Published
Status Date: 2014-01-17
Status Comments: 
Attachments: 
none



DD Entity: 336 Rear PTO hours
Definition: The hours the Rear PTO of the machine was running for the current Task
Comment: This value provides information of the active working time for example of the header attachment of a selfpropelled machine
Typically used by Device Classes: 
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
Unit: s - Time count
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Matthias Meyer
Submit Date: 2014-01-17
Submit Company: John Deere
Revision Number: 1
Current Status: ISO-Published
Status Date: 2014-01-17
Status Comments: 
Attachments: 
none



DD Entity: 337 Lifetime Front PTO hours
Definition: The hours the Front PTO of the machine was running for the lifetime of the machine
Comment: This value provides information of the active working time for example of the header attachment of a selfpropelled machine
Typically used by Device Classes: 
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
11 - Transport / Trailers
12 - Farmyard Work
14 - Special Crops
15 - Municipal Work
Unit: h - Hour
Resolution: 0,1
SAE SPN: 
Range: 0 - **********
Submit by: Frank Wiebeler
Submit Date: 2013-12-04
Submit Company: Maschinenfabrik Bernard Krone GmbH
Revision Number: 1
Current Status: ISO-Published
Status Date: 2014-01-17
Status Comments: 
Attachments: 
none



DD Entity: 338 Lifetime Rear PTO Hours
Definition: The hours the Rear PTO of the machine was running for the lifetime of the machine
Comment: This value provides information of the active working time for example of the header attachment of a selfpropelled machine
Typically used by Device Classes: 
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
17 - Sensor System
Unit: h - Hour
Resolution: 0,1
SAE SPN: 
Range: 0 - **********
Submit by: Meyer Matthias
Submit Date: 2014-01-17
Submit Company: John Deere
Revision Number: 1
Current Status: ISO-Published
Status Date: 2014-01-17
Status Comments: 
Attachments: 
none



DD Entity: 339 Effective Loading Time
Definition: The total time needed in the current task to load a product such as crop.
Comment: 
Typically used by Device Classes: 
7 - Harvesters
9 - Forage harvester
11 - Transport / Trailers
12 - Farmyard Work
Unit: s - Time count
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Frank Wiebeler
Submit Date: 2013-12-04
Submit Company: Maschinenfabrik Bernard Krone GmbH
Revision Number: 1
Current Status: ISO-Published
Status Date: 2014-01-17
Status Comments: 
Attachments: 
none



DD Entity: 340 Effective Unloading Time
Definition: The total time needed in the current task to unload a product crop.
Comment: 
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
11 - Transport / Trailers
12 - Farmyard Work
Unit: s - Time count
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Frank Wiebeler
Submit Date: 2013-12-04
Submit Company: Maschinenfabrik Bernard Krone GmbH
Revision Number: 1
Current Status: ISO-Published
Status Date: 2014-01-17
Status Comments: 
Attachments: 
none



DD Entity: 341 Setpoint Grain Kernel Cracker Gap
Definition: The setpoint gap (distance) of the grain kernel cracker drums in a chopper.
Comment: The gap (distance) of the grain kernel cracker is an indicator to the quality of chopped corn.
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
Unit: mm - Length
Resolution: 0,001
SAE SPN: 
Range: 0 - **********
Submit by: Frank Wiebeler
Submit Date: 2013-12-04
Submit Company: Maschinenfabrik Bernard Krone GmbH
Revision Number: 2
Current Status: ISO-Published
Status Date: 2014-02-24
Status Comments: 
Attachments: 
none



DD Entity: 342 Actual Grain Kernel Cracker Gap
Definition: The actual gap (distance) of the grain kernel cracker drums in a chopper
Comment: The actual gap (distance) of the grain kernel cracker is an indicator to the quality of chopped corn.
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
Unit: mm - Length
Resolution: 0,001
SAE SPN: 
Range: 0 - **********
Submit by: Frank Wiebeler
Submit Date: 2014-02-25
Submit Company: Maschinenfabrik Bernard Krone GmbH
Revision Number: 1
Current Status: ISO-Published
Status Date: 2014-02-25
Status Comments: 
Attachments: 
none



DD Entity: 343 Minimum Grain Kernel Cracker Gap
Definition: The minimum gap (distance) of the grain kernel cracker drums in a chopper
Comment: The minimum gap (distance) of the grain kernel cracker that can be adjusted
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
Unit: mm - Length
Resolution: 0,001
SAE SPN: 
Range: 0 - **********
Submit by: Frank Wiebeler
Submit Date: 2014-02-25
Submit Company: Maschinenfabrik Bernard Krone GmbH
Revision Number: 1
Current Status: ISO-Published
Status Date: 2014-02-25
Status Comments: 
Attachments: 
none



DD Entity: 344 Maximum Grain Kernel Cracker Gap
Definition: The maximum gap (distance) of the grain kernel cracker drums in a chopper
Comment: The maximum gap (distance) of the grain kernel cracker that can be adjusted.
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
Unit: mm - Length
Resolution: 0,001
SAE SPN: 
Range: 0 - **********
Submit by: Frank Wiebeler
Submit Date: 2014-02-25
Submit Company: Maschinenfabrik Bernard Krone GmbH
Revision Number: 1
Current Status: ISO-Published
Status Date: 2014-02-25
Status Comments: 
Attachments: 
none



DD Entity: 345 Setpoint Swathing Width
Definition: This is the setpoint swathing width of the swath created by a raker.
Comment: For mowers the working width DDIs will represent the with of the mower whereas swathing with will represent the swath with created by the mover.
Typically used by Device Classes: 
0 - Non-specific system
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
Unit: mm - Length
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Frank Wiebeler
Submit Date: 2014-03-17
Submit Company: Maschinenfabrik Bernard Krone GmbH
Revision Number: 1
Current Status: ISO-Published
Status Date: 2014-03-25
Status Comments: 
Attachments: 
none



DD Entity: 346 Actual Swathing Width
Definition: This is the width of the swath currently created by a raker.
Comment: For mowers the working width DDIs will represent the width of the mower whereas swathing width will represent the swath width created by the mover.
Typically used by Device Classes: 
0 - Non-specific system
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
Unit: mm - Length
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Frank Wiebeler
Submit Date: 2014-03-17
Submit Company: Maschinenfabrik Bernard Krone GmbH
Revision Number: 1
Current Status: ISO-Published
Status Date: 2014-03-25
Status Comments: 
Attachments: 
none



DD Entity: 347 Minimum Swathing Width
Definition: This is the minimum swath width the raker can create.
Comment: For mowers the working width DDIs will represent the width of the mower whereas swathing with will represent the swath with created by the mover.
Typically used by Device Classes: 
0 - Non-specific system
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
Unit: mm - Length
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Frank Wiebeler
Submit Date: 2014-03-17
Submit Company: Maschinenfabrik Bernard Krone GmbH
Revision Number: 1
Current Status: ISO-Published
Status Date: 2014-03-25
Status Comments: 
Attachments: 
none



DD Entity: 348 Maximum Swathing Width
Definition: This is the maximum with of the swath the raker can create.
Comment: For mowers the working width DDIs will represent the width of the mower whereas swathing width will represent the swath width created by the mover.
Typically used by Device Classes: 
0 - Non-specific system
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
Unit: mm - Length
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Frank Wiebeler
Submit Date: 2014-03-17
Submit Company: Maschinenfabrik Bernard Krone GmbH
Revision Number: 1
Current Status: ISO-Published
Status Date: 2014-03-25
Status Comments: 
Attachments: 
none



DD Entity: 349 Nozzle Drift Reduction
Definition: The Nozzle Drift Reduction classification value of the spraying equipment as percentage 


Comment: The use of this DDE is to document the current used drift reducing classification of the  nozzles or combination of drift reducing technique as percentage value.
To record documentation obligation product during applying in adjacency of sensitive areas.

Typically used by Device Classes: 
6 - Sprayers
Unit: ppm - Parts per million
Resolution: 1
SAE SPN: 
Range: 0 - 100
Submit by: Matthias Meyer
Submit Date: 2014-06-18
Submit Company: John Deere
Revision Number: 1
Current Status: ISO-Published
Status Date: 2014-06-18
Status Comments: 
Attachments: 
none



DD Entity: 350 Function Type
Definition: The Function Type DDE can be used to define the operation or functionality performed by the device element of type Function defined within the DDOP. The values to be used are defined in the attached document.
Comment: In a DDOP (Device Description Object Pool) of an ISOBUS device there are different functionalities covered. The device element types in the Task Controller standard which are Device, Function, Bin, Section, Unit, Connector Type, and Navigation Reference do not last out for certain or more complex devices to describe all information in a unique way to the Task Controller Server. For more information see the attached document.
Typically used by Device Classes: 
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
17 - Sensor System
Unit: n.a. - 
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Matthias Meyer
Submit Date: 2014-07-01
Submit Company: John Deere
Revision Number: 1
Current Status: ISO-Published
Status Date: 2014-07-01
Status Comments: 
Attachments: 
http://dictionary.isobus.net/isobus/attachments/447/ISO11783-11-DDI-350-Function Type-v1.pdf



DD Entity: 351 Application Total Volume in [ml]
Definition: Accumulated Application specified as volume in milliliter [ml]
Comment: is a counter of a device element
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
10 - Irrigation
Unit: ml - Capacity large
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Matthias Meyer
Submit Date: 2014-07-02
Submit Company: John Deere
Revision Number: 1
Current Status: ISO-Published
Status Date: 2014-07-02
Status Comments: 
Attachments: 
none



DD Entity: 352 Application Total Mass in gram [g]
Definition: Accumulated Application specified as mass in gram [g]
Comment: is a counter of a device element
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
10 - Irrigation
Unit: g - Mass large
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Matthias Meyer
Submit Date: 2014-07-02
Submit Company: John Deere
Revision Number: 1
Current Status: ISO-Published
Status Date: 2014-07-02
Status Comments: 
Attachments: 
none



DD Entity: 353 Total Application of Nitrogen
Definition: Accumulated application of nitrogen [N2] specified as gram [g]
Comment: This total is a counter of a device element
Typically used by Device Classes: 
5 - Fertilizer
Unit: g - Mass large
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Christoph Staub
Submit Date: 2014-06-04
Submit Company: Zunhammer
Revision Number: 1
Current Status: ISO-Published
Status Date: 2014-08-26
Status Comments: Status was published
Attachments: 
none



DD Entity: 354 Total Application of Ammonium
Definition: Accumulated  application of ammonium [NH4] specified as gram [g]
Comment: This total is a counter of a device element
Typically used by Device Classes: 
5 - Fertilizer
Unit: g - Mass large
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Christoph Staub
Submit Date: 2014-06-04
Submit Company: Zunhammer GmbH
Revision Number: 1
Current Status: ISO-Published
Status Date: 2014-08-26
Status Comments: Status was published
Attachments: 
none



DD Entity: 355 Total Application of Phosphor
Definition: Accumulated application of phosphor (P2O5) specified as gram [g]
Comment: This total is a counter of a device element
Typically used by Device Classes: 
5 - Fertilizer
Unit: g - Mass large
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Christoph Staub
Submit Date: 2014-06-04
Submit Company: Zunhammer GmbH
Revision Number: 1
Current Status: ISO-Published
Status Date: 2014-08-26
Status Comments: Status was published
Attachments: 
none



DD Entity: 356 Total Application of Potassium
Definition: Accumulated application of potassium (K2) specified as gram [g]
Comment: This total is a counter of a device element
Typically used by Device Classes: 
5 - Fertilizer
Unit: g - Mass large
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Christoph Staub
Submit Date: 2014-06-04
Submit Company: Zunhammer GmbH
Revision Number: 1
Current Status: ISO-Published
Status Date: 2014-08-26
Status Comments: Status was published
Attachments: 
none



DD Entity: 357 Total Application of Dry Matter
Definition: Accumulated application of dry matter  in kilogram [kg]. Dry matter measured at zero percent of moisture
Comment: This total is a counter of a device element
Typically used by Device Classes: 
5 - Fertilizer
Unit: kg - Mass
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Christoph Staub
Submit Date: 2014-06-04
Submit Company: Zunhammer GmbH
Revision Number: 1
Current Status: ISO-Published
Status Date: 2014-08-26
Status Comments: Status was published
Attachments: 
none



DD Entity: 358 Average Dry Yield Mass Per Time
Definition: Average Yield expressed as mass per unit time, corrected for the reference moisture percentage DDI 184. This value is the average for a Task and may be reported as a total.
Comment: This Average Dry Yield Mass Per Time is the mass flow that has been corrected for the average crop moisture (DDI 262) based on the reference moisture for dry mass (DDI 184). This is the "dry" equivalent to DDI 261. This average yield mass per time is calculated as the yield total dry mass (DDI 183) divided by the effective total time (DDI 119) of the active task. When resuming a task, the working set shall compute its average dry yield mass per time from the yield total mass (DDI 90), average crop moisture (DDI 262), reference moisture percentage (DDI 184), and effective total time (119) assuming these DDI's are sent by the task controller.
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
Unit: mg/s - Mass flow
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Tony Woodcock
Submit Date: 2014-08-08
Submit Company: Ag Leader Technology
Revision Number: 1
Current Status: ISO-Published
Status Date: 2014-08-26
Status Comments: Status was published
Attachments: 
none



DD Entity: 359 Average Dry Yield Mass Per Area
Definition: Average Yield expressed as mass per unit area, corrected for the reference moisture percentage DDI 184. This value is the average for a Task and may be reported as a total.

Comment: This Average Dry Yield Mass Per Area is the mass flow that has been corrected for the average crop moisture (DDI 262) based on the reference moisture for dry mass (DDI 184). This is the "dry" equivalent to DDI 263. This average yield mass per area is calculated as the yield total dry mass (DDI 183) divided by the total area (DDI 116) of the active task. When resuming a task, the working set shall compute its average dry yield mass per area from the yield total mass (DDI 90), average crop moisture (DDI 262), reference moisture percentage (DDI 184), and total area (DDI 116) assuming these DDI's are sent by the task controller.
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
14 - Special Crops
Unit: mg/m² - Mass per area unit
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Tony Woodcock
Submit Date: 2014-08-08
Submit Company: Ag Leader Technology
Revision Number: 1
Current Status: ISO-Published
Status Date: 2014-08-26
Status Comments: 
Attachments: 
none



DD Entity: 360 Last Bale Size
Definition: The bale size of the most recently produced bale. Bale Size as length for a square baler or diameter for a round baler.
Comment: The recommended use of this DDE is for a baler to report this once for every bale that is produced. A baler can add this to its default set of data, based on an internal on-change data trigger that causes the baler to report the value of this DDE after the bale is produced. The recommendation for data logging is that all "Last Bale" DDEs that are supported by a device are reported together at the moment that the bale is produced and leaves the machine.
Typically used by Device Classes: 
7 - Harvesters
9 - Forage harvester
Unit: mm - Length
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Lynn Derynck
Submit Date: 2014-11-07
Submit Company: CNH Industrial N.V.
Revision Number: 1
Current Status: ISO-Published
Status Date: 2014-12-03
Status Comments: 
Attachments: 
none



DD Entity: 361 Last Bale Density
Definition: The bale density of the most recently produced bale. 


Unit: kg/m3 (mass per unit volume)
Comment: The recommended use of this DDE is for a baler to report this once for every bale that is produced. A baler can add this to its default set of data, based on an internal on-change data trigger that causes the baler to report the value of this DDE after the bale is produced. The recommendation for data logging is that all "Last Bale" DDEs that are supported by a device are reported together at the moment that the bale is produced and leaves the machine.
Typically used by Device Classes: 
7 - Harvesters
9 - Forage harvester
Unit: not defined - not defined
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Lynn Derynck
Submit Date: 2014-11-07
Submit Company: CNH Industrial N.V.
Revision Number: 1
Current Status: ISO-Published
Status Date: 2014-12-03
Status Comments: 
Attachments: 
none



DD Entity: 362 Total Bale Length
Definition: Gives the total baled meters during a task. This is calculated as the sum of the lengths of all knotted bales (square baler). 
Comment: 
Typically used by Device Classes: 
9 - Forage harvester
Unit: mm - Length
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Lynn Derynck
Submit Date: 2014-11-07
Submit Company: CNH Industrial N.V.
Revision Number: 1
Current Status: ISO-Published
Status Date: 2014-12-03
Status Comments: 
Attachments: 
http://dictionary.isobus.net/isobus/attachments/459/ISO_11783-11_DDIdentifier_Total Bale Length-v1.doc



DD Entity: 363 Last Bale Dry Mass
Definition: The dry mass of the bale that has most recently been produced. This is the bale mass corrected for the average moisture of this bale (DDI 212).
Comment: The recommended use of this DDE is for a baler to report this once for every bale that is produced. A baler may add this to its default set of data, based on an internal on-change data trigger that causes the baler to report the value of this DDE after the bale is produced. The recommendation for data logging is that all "Last Bale" DDEs that are supported by a device are reported together at the moment that the bale is produced and leaves the machine.
Typically used by Device Classes: 
7 - Harvesters
9 - Forage harvester
Unit: g - Mass large
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Lynn Derynck
Submit Date: 2014-11-07
Submit Company: CNH Industrial N.V.
Revision Number: 1
Current Status: ISO-Published
Status Date: 2014-12-03
Status Comments: 
Attachments: 
http://dictionary.isobus.net/isobus/attachments/460/ISO_11783-11_DDIdentifier Last Bale Mass Dry-v1.doc



DD Entity: 364 Actual Flake Size
Definition: Actual size of the flake that is currently produced by the chamber.
Comment: The recommended use of this DDE is for a baler to report this once for each new flake that entered
the baler chamber and obtained at the maximum compression of the plunger. A baler may add this to its default set of data, based on an internal on-change data trigger that causes the baler to report the value of this DDE at each new flake.
Typically used by Device Classes: 
7 - Harvesters
9 - Forage harvester
Unit: mm - Length
Resolution: 1
SAE SPN: 
Range: 0 - 1000
Submit by: Lynn Derynck
Submit Date: 2014-11-07
Submit Company: CNH Industrial N.V.
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-01-13
Status Comments: 
Attachments: 
http://dictionary.isobus.net/isobus/attachments/461/ISO_11783-11_DDIdentifier_ Flake Size-v1.doc



DD Entity: 365 Setpoint Downforce Pressure
Definition: Setpoint downforce pressure for an operation
Comment: This value represents the system pressure to produce the downforce (or upforce) for an operation messured in Pa (Pascal); In case of an negative value the system pressure would  produce Upforce.
Typically used by Device Classes: 
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
Unit: Pa - Pressure
Resolution: 1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Brandon McDonald
Submit Date: 2014-12-04
Submit Company: John Deere
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-01-13
Status Comments: 
Attachments: 
none



DD Entity: 366 Actual Downforce Pressure
Definition: Actual downforce pressure for an operation
Comment: This value represents the actual system pressure to produce the downforce (or upforce) for an operation messured in Pa (Pascal); In case of an negative value the system pressure would  produce Upforce.
Typically used by Device Classes: 
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
Unit: Pa - Pressure
Resolution: 1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Brandon McDonald
Submit Date: 2014-12-04
Submit Company: John Deere
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-01-13
Status Comments: 
Attachments: 
none



DD Entity: 367 Condensed Section Override State (1 – 16)
Definition: This DDE is used by the implement to communicate that a certain section is overridden and will not follow the section control commands. The value is a combination of the override states of individual sections number 1 to 16 into a single override state. The condensed section override state contains the child element override states, in the driving direction from left to right, where the leftmost child element override state are the 2 lowest significant bits of the Process Data Value. Each child device elements override state is represented by 2 bits and defined as: 00 = section is not overridden, 01 = section is overridden, 10 = reserved, 11 = undefined / not installed. In total 16 child device element override states can be contained in one condensed section override state. If less than 16 child device element override states are available, then the unused bits shall be set to value 11 (undefined / not installed). This DDE shall be placed in the same device element as the corresponding actual condensed work state.
Comment: It is common for SC servers to show the current state of the sections in a proprietary screen. As the implement is allowed to override the commanded state from the task controller it is impossible for the operator to predict what happens when driving into an unworked area. With this DDE it is possible for the SC server to show overridden sections in the proprietary screen.

This DDE shall be defined as DPD in the DDOP of the implement. The DPD shall at least support the datalog triggers “on change” and “time based”. The value shall only be sent by the implement if it was requested (single request or datalog trigger) by the TC-SC server.
Typically used by Device Classes: 
Not Assigned
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
17 - Sensor System
Unit: not defined - not defined
Resolution: 1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Michael Köcher
Submit Date: 2014-11-18
Submit Company:  AMAZONEN-Werke H. Dreyer GmbH & Co. KG
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-01-20
Status Comments: 
Attachments: 
none



DD Entity: 368 Condensed Section Override State (17–32) 
Definition: This DDE is used by the implement to communicate that a certain section is overridden and will not follow the section control commands. The value is a combination of the override states of individual sections number 17 to 32 into a single override state. The condensed section override state contains the child element override states, in the driving direction from left to right, where the leftmost child element override state are the 2 lowest significant bits of the Process Data Value. Each child device elements override state is represented by 2 bits and defined as: 00 = section is not overridden, 01 = section is overridden, 10 = reserved, 11 = undefined / not installed. In total 16 child device element override states can be contained in one condensed section override state. If less than 16 child device element override states are available, then the unused bits shall be set to value 11 (undefined / not installed). This DDE shall be placed in the same device element as the corresponding actual condensed work state.
Comment: It is common for SC servers to show the current state of the sections in a proprietary screen. As the implement is allowed to override the commanded state from the task controller it is impossible for the operator to predict what happens when driving into an unworked area. With this DDE it is possible for the SC server to show overridden sections in the proprietary screen.

This DDE shall be defined as DPD in the DDOP of the implement. The DPD shall at least support the datalog triggers “on change” and “time based”. The value shall only be sent by the implement if it was requested (single request or datalog trigger) by the TC-SC server.
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
17 - Sensor System
Unit: not defined - not defined
Resolution: 1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Michael Köcher
Submit Date: 2015-01-20
Submit Company:  AMAZONEN-Werke H. Dreyer GmbH & Co. KG
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-01-20
Status Comments: 
Attachments: 
none



DD Entity: 369 Condensed Section Override State (33–48) 
Definition: This DDE is used by the implement to communicate that a certain section is overridden and will not follow the section control commands. The value is a combination of the override states of individual sections number 33 to 48 into a single override state. The condensed section override state contains the child element override states, in the driving direction from left to right, where the leftmost child element override state are the 2 lowest significant bits of the Process Data Value. Each child device elements override state is represented by 2 bits and defined as: 00 = section is not overridden, 01 = section is overridden, 10 = reserved, 11 = undefined / not installed. In total 16 child device element override states can be contained in one condensed section override state. If less than 16 child device element override states are available, then the unused bits shall be set to value 11 (undefined / not installed). This DDE shall be placed in the same device element as the corresponding actual condensed work state.
Comment: It is common for SC servers to show the current state of the sections in a proprietary screen. As the implement is allowed to override the commanded state from the task controller it is impossible for the operator to predict what happens when driving into an unworked area. With this DDE it is possible for the SC server to show overridden sections in the proprietary screen.

This DDE shall be defined as DPD in the DDOP of the implement. The DPD shall at least support the datalog triggers “on change” and “time based”. The value shall only be sent by the implement if it was requested (single request or datalog trigger) by the TC-SC server.
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
17 - Sensor System
Unit: not defined - not defined
Resolution: 1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Michael Köcher
Submit Date: 2015-01-20
Submit Company:  AMAZONEN-Werke H. Dreyer GmbH & Co. KG
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-01-20
Status Comments: 
Attachments: 
none



DD Entity: 370 Condensed Section Override State (49–64) 
Definition: This DDE is used by the implement to communicate that a certain section is overridden and will not follow the section control commands. The value is a combination of the override states of individual sections number 49 to 64 into a single override state. The condensed section override state contains the child element override states, in the driving direction from left to right, where the leftmost child element override state are the 2 lowest significant bits of the Process Data Value. Each child device elements override state is represented by 2 bits and defined as: 00 = section is not overridden, 01 = section is overridden, 10 = reserved, 11 = undefined / not installed. In total 16 child device element override states can be contained in one condensed section override state. If less than 16 child device element override states are available, then the unused bits shall be set to value 11 (undefined / not installed). This DDE shall be placed in the same device element as the corresponding actual condensed work state.
Comment: It is common for SC servers to show the current state of the sections in a proprietary screen. As the implement is allowed to override the commanded state from the task controller it is impossible for the operator to predict what happens when driving into an unworked area. With this DDE it is possible for the SC server to show overridden sections in the proprietary screen.

This DDE shall be defined as DPD in the DDOP of the implement. The DPD shall at least support the datalog triggers “on change” and “time based”. The value shall only be sent by the implement if it was requested (single request or datalog trigger) by the TC-SC server.
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
17 - Sensor System
Unit: not defined - not defined
Resolution: 1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Michael Köcher
Submit Date: 2015-01-20
Submit Company:  AMAZONEN-Werke H. Dreyer GmbH & Co. KG
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-01-20
Status Comments: 
Attachments: 
none



DD Entity: 371 Condensed Section Override State (65–80) 
Definition: This DDE is used by the implement to communicate that a certain section is overridden and will not follow the section control commands. The value is a combination of the override states of individual sections number 65 to 80 into a single override state. The condensed section override state contains the child element override states, in the driving direction from left to right, where the leftmost child element override state are the 2 lowest significant bits of the Process Data Value. Each child device elements override state is represented by 2 bits and defined as: 00 = section is not overridden, 01 = section is overridden, 10 = reserved, 11 = undefined / not installed. In total 16 child device element override states can be contained in one condensed section override state. If less than 16 child device element override states are available, then the unused bits shall be set to value 11 (undefined / not installed). This DDE shall be placed in the same device element as the corresponding actual condensed work state.
Comment: It is common for SC servers to show the current state of the sections in a proprietary screen. As the implement is allowed to override the commanded state from the task controller it is impossible for the operator to predict what happens when driving into an unworked area. With this DDE it is possible for the SC server to show overridden sections in the proprietary screen.

This DDE shall be defined as DPD in the DDOP of the implement. The DPD shall at least support the datalog triggers “on change” and “time based”. The value shall only be sent by the implement if it was requested (single request or datalog trigger) by the TC-SC server.
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
17 - Sensor System
18 - Reserved for Future Assignment
19 - Timber Harvesters
20 - Forwarders
21 - Timber loaders
22 - Timber Processing Machines
23 - Mulchers
24 - Utility Vehicles
25 - Slurry Applicators
26 - Feeder / Mixer
Unit: not defined - not defined
Resolution: 1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Michael Köcher
Submit Date: 2015-01-21
Submit Company:  AMAZONEN-Werke H. Dreyer GmbH & Co. KG
Revision Number: 1
Current Status: ISO-Published
Status Date: 2016-02-16
Status Comments: 
Attachments: 
none



DD Entity: 372 Condensed Section Override State (81-96) 
Definition: This DDE is used by the implement to communicate that a certain section is overridden and will not follow the section control commands. The value is a combination of the override states of individual sections number 145 to 160 into a single override state. The condensed section override state contains the child element override states, in the driving direction from left to right, where the leftmost child element override state are the 2 lowest significant bits of the Process Data Value. Each child device elements override state is represented by 2 bits and defined as: 00 = section is not overridden, 01 = section is overridden, 10 = reserved, 11 = undefined / not installed. In total 16 child device element override states can be contained in one condensed section override state. If less than 16 child device element override states are available, then the unused bits shall be set to value 11 (undefined / not installed). This DDE shall be placed in the same device element as the corresponding actual condensed work state.
Comment: It is common for SC servers to show the current state of the sections in a proprietary screen. As the implement is allowed to override the commanded state from the task controller it is impossible for the operator to predict what happens when driving into an unworked area. With this DDE it is possible for the SC server to show overridden sections in the proprietary screen.

This DDE shall be defined as DPD in the DDOP of the implement. The DPD shall at least support the datalog triggers “on change” and “time based”. The value shall only be sent by the implement if it was requested (single request or datalog trigger) by the TC-SC server.
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
17 - Sensor System
18 - Reserved for Future Assignment
19 - Timber Harvesters
20 - Forwarders
21 - Timber loaders
22 - Timber Processing Machines
23 - Mulchers
24 - Utility Vehicles
25 - Slurry Applicators
26 - Feeder / Mixer
Unit: not defined - not defined
Resolution: 1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Michael Köcher
Submit Date: 2015-01-21
Submit Company:  AMAZONEN-Werke H. Dreyer GmbH & Co. KG
Revision Number: 1
Current Status: ISO-Published
Status Date: 2016-02-16
Status Comments: 
Attachments: 
none



DD Entity: 373 Condensed Section Override State (97–112) 
Definition: This DDE is used by the implement to communicate that a certain section is overridden and will not follow the section control commands. The value is a combination of the override states of individual sections number 145 to 160 into a single override state. The condensed section override state contains the child element override states, in the driving direction from left to right, where the leftmost child element override state are the 2 lowest significant bits of the Process Data Value. Each child device elements override state is represented by 2 bits and defined as: 00 = section is not overridden, 01 = section is overridden, 10 = reserved, 11 = undefined / not installed. In total 16 child device element override states can be contained in one condensed section override state. If less than 16 child device element override states are available, then the unused bits shall be set to value 11 (undefined / not installed). This DDE shall be placed in the same device element as the corresponding actual condensed work state.
Comment: It is common for SC servers to show the current state of the sections in a proprietary screen. As the implement is allowed to override the commanded state from the task controller it is impossible for the operator to predict what happens when driving into an unworked area. With this DDE it is possible for the SC server to show overridden sections in the proprietary screen.

This DDE shall be defined as DPD in the DDOP of the implement. The DPD shall at least support the datalog triggers “on change” and “time based”. The value shall only be sent by the implement if it was requested (single request or datalog trigger) by the TC-SC server.
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
17 - Sensor System
18 - Reserved for Future Assignment
19 - Timber Harvesters
20 - Forwarders
21 - Timber loaders
22 - Timber Processing Machines
23 - Mulchers
24 - Utility Vehicles
25 - Slurry Applicators
26 - Feeder / Mixer
Unit: not defined - not defined
Resolution: 1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Michael Köcher
Submit Date: 2015-01-21
Submit Company: AMAZONEN-Werke H. Dreyer GmbH & Co. KG
Revision Number: 1
Current Status: ISO-Published
Status Date: 2016-02-16
Status Comments: 
Attachments: 
none



DD Entity: 374 Condensed Section Override State (113–128) 
Definition: This DDE is used by the implement to communicate that a certain section is overridden and will not follow the section control commands. The value is a combination of the override states of individual sections number 145 to 160 into a single override state. The condensed section override state contains the child element override states, in the driving direction from left to right, where the leftmost child element override state are the 2 lowest significant bits of the Process Data Value. Each child device elements override state is represented by 2 bits and defined as: 00 = section is not overridden, 01 = section is overridden, 10 = reserved, 11 = undefined / not installed. In total 16 child device element override states can be contained in one condensed section override state. If less than 16 child device element override states are available, then the unused bits shall be set to value 11 (undefined / not installed). This DDE shall be placed in the same device element as the corresponding actual condensed work state.
Comment: It is common for SC servers to show the current state of the sections in a proprietary screen. As the implement is allowed to override the commanded state from the task controller it is impossible for the operator to predict what happens when driving into an unworked area. With this DDE it is possible for the SC server to show overridden sections in the proprietary screen.

This DDE shall be defined as DPD in the DDOP of the implement. The DPD shall at least support the datalog triggers “on change” and “time based”. The value shall only be sent by the implement if it was requested (single request or datalog trigger) by the TC-SC server.
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
17 - Sensor System
18 - Reserved for Future Assignment
19 - Timber Harvesters
20 - Forwarders
21 - Timber loaders
22 - Timber Processing Machines
23 - Mulchers
24 - Utility Vehicles
25 - Slurry Applicators
26 - Feeder / Mixer
Unit: not defined - not defined
Resolution: 1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Michael Köcher
Submit Date: 2015-01-21
Submit Company: AMAZONEN-Werke H. Dreyer GmbH & Co. KG
Revision Number: 1
Current Status: ISO-Published
Status Date: 2016-02-16
Status Comments: 
Attachments: 
none



DD Entity: 375 Condensed Section Override State (129–144) 
Definition: This DDE is used by the implement to communicate that a certain section is overridden and will not follow the section control commands. The value is a combination of the override states of individual sections number 145 to 160 into a single override state. The condensed section override state contains the child element override states, in the driving direction from left to right, where the leftmost child element override state are the 2 lowest significant bits of the Process Data Value. Each child device elements override state is represented by 2 bits and defined as: 00 = section is not overridden, 01 = section is overridden, 10 = reserved, 11 = undefined / not installed. In total 16 child device element override states can be contained in one condensed section override state. If less than 16 child device element override states are available, then the unused bits shall be set to value 11 (undefined / not installed). This DDE shall be placed in the same device element as the corresponding actual condensed work state.
Comment: It is common for SC servers to show the current state of the sections in a proprietary screen. As the implement is allowed to override the commanded state from the task controller it is impossible for the operator to predict what happens when driving into an unworked area. With this DDE it is possible for the SC server to show overridden sections in the proprietary screen.

This DDE shall be defined as DPD in the DDOP of the implement. The DPD shall at least support the datalog triggers “on change” and “time based”. The value shall only be sent by the implement if it was requested (single request or datalog trigger) by the TC-SC server.
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
17 - Sensor System
18 - Reserved for Future Assignment
19 - Timber Harvesters
20 - Forwarders
21 - Timber loaders
22 - Timber Processing Machines
23 - Mulchers
24 - Utility Vehicles
25 - Slurry Applicators
26 - Feeder / Mixer
Unit: not defined - not defined
Resolution: 1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Michael Köcher
Submit Date: 2015-01-21
Submit Company:  AMAZONEN-Werke H. Dreyer GmbH & Co. KG
Revision Number: 1
Current Status: ISO-Published
Status Date: 2016-02-16
Status Comments: 
Attachments: 
none



DD Entity: 376 Condensed Section Override State (145–160) 
Definition: This DDE is used by the implement to communicate that a certain section is overridden and will not follow the section control commands. The value is a combination of the override states of individual sections number 145 to 160 into a single override state. The condensed section override state contains the child element override states, in the driving direction from left to right, where the leftmost child element override state are the 2 lowest significant bits of the Process Data Value. Each child device elements override state is represented by 2 bits and defined as: 00 = section is not overridden, 01 = section is overridden, 10 = reserved, 11 = undefined / not installed. In total 16 child device element override states can be contained in one condensed section override state. If less than 16 child device element override states are available, then the unused bits shall be set to value 11 (undefined / not installed). This DDE shall be placed in the same device element as the corresponding actual condensed work state.
Comment: It is common for SC servers to show the current state of the sections in a proprietary screen. As the implement is allowed to override the commanded state from the task controller it is impossible for the operator to predict what happens when driving into an unworked area. With this DDE it is possible for the SC server to show overridden sections in the proprietary screen.

This DDE shall be defined as DPD in the DDOP of the implement. The DPD shall at least support the datalog triggers “on change” and “time based”. The value shall only be sent by the implement if it was requested (single request or datalog trigger) by the TC-SC server.
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
17 - Sensor System
Unit: not defined - not defined
Resolution: 1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Michael Köcher
Submit Date: 2015-01-21
Submit Company:  AMAZONEN-Werke H. Dreyer GmbH & Co. KG
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-01-21
Status Comments: 
Attachments: 
none



DD Entity: 377 Condensed Section Override State (161–176) 
Definition: This DDE is used by the implement to communicate that a certain section is overridden and will not follow the section control commands. The value is a combination of the override states of individual sections number 161 to 176 into a single override state. The condensed section override state contains the child element override states, in the driving direction from left to right, where the leftmost child element override state are the 2 lowest significant bits of the Process Data Value. Each child device elements override state is represented by 2 bits and defined as: 00 = section is not overridden, 01 = section is overridden, 10 = reserved, 11 = undefined / not installed. In total 16 child device element override states can be contained in one condensed section override state. If less than 16 child device element override states are available, then the unused bits shall be set to value 11 (undefined / not installed). This DDE shall be placed in the same device element as the corresponding actual condensed work state.
Comment: It is common for SC servers to show the current state of the sections in a proprietary screen. As the implement is allowed to override the commanded state from the task controller it is impossible for the operator to predict what happens when driving into an unworked area. With this DDE it is possible for the SC server to show overridden sections in the proprietary screen.

This DDE shall be defined as DPD in the DDOP of the implement. The DPD shall at least support the datalog triggers “on change” and “time based”. The value shall only be sent by the implement if it was requested (single request or datalog trigger) by the TC-SC server.
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
17 - Sensor System
Unit: not defined - not defined
Resolution: 1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Michael Köcher
Submit Date: 2015-01-21
Submit Company:  AMAZONEN-Werke H. Dreyer GmbH & Co. KG
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-01-21
Status Comments: 
Attachments: 
none



DD Entity: 378 Condensed Section Override State (177–192) 
Definition: This DDE is used by the implement to communicate that a certain section is overridden and will not follow the section control commands. The value is a combination of the override states of individual sections number 177 to 192 into a single override state. The condensed section override state contains the child element override states, in the driving direction from left to right, where the leftmost child element override state are the 2 lowest significant bits of the Process Data Value. Each child device elements override state is represented by 2 bits and defined as: 00 = section is not overridden, 01 = section is overridden, 10 = reserved, 11 = undefined / not installed. In total 16 child device element override states can be contained in one condensed section override state. If less than 16 child device element override states are available, then the unused bits shall be set to value 11 (undefined / not installed). This DDE shall be placed in the same device element as the corresponding actual condensed work state.
Comment: It is common for SC servers to show the current state of the sections in a proprietary screen. As the implement is allowed to override the commanded state from the task controller it is impossible for the operator to predict what happens when driving into an unworked area. With this DDE it is possible for the SC server to show overridden sections in the proprietary screen.

This DDE shall be defined as DPD in the DDOP of the implement. The DPD shall at least support the datalog triggers “on change” and “time based”. The value shall only be sent by the implement if it was requested (single request or datalog trigger) by the TC-SC server.
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
17 - Sensor System
Unit: not defined - not defined
Resolution: 1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Michael Köcher
Submit Date: 2015-01-21
Submit Company:  AMAZONEN-Werke H. Dreyer GmbH & Co. KG
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-01-21
Status Comments: 
Attachments: 
none



DD Entity: 379 Condensed Section Override State (193–208) 
Definition: This DDE is used by the implement to communicate that a certain section is overridden and will not follow the section control commands. The value is a combination of the override states of individual sections number 193 to 208 into a single override state. The condensed section override state contains the child element override states, in the driving direction from left to right, where the leftmost child element override state are the 2 lowest significant bits of the Process Data Value. Each child device elements override state is represented by 2 bits and defined as: 00 = section is not overridden, 01 = section is overridden, 10 = reserved, 11 = undefined / not installed. In total 16 child device element override states can be contained in one condensed section override state. If less than 16 child device element override states are available, then the unused bits shall be set to value 11 (undefined / not installed). This DDE shall be placed in the same device element as the corresponding actual condensed work state.
Comment: It is common for SC servers to show the current state of the sections in a proprietary screen. As the implement is allowed to override the commanded state from the task controller it is impossible for the operator to predict what happens when driving into an unworked area. With this DDE it is possible for the SC server to show overridden sections in the proprietary screen.

This DDE shall be defined as DPD in the DDOP of the implement. The DPD shall at least support the datalog triggers “on change” and “time based”. The value shall only be sent by the implement if it was requested (single request or datalog trigger) by the TC-SC server.
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
17 - Sensor System
Unit: not defined - not defined
Resolution: 1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Michael Köcher
Submit Date: 2015-01-21
Submit Company:  AMAZONEN-Werke H. Dreyer GmbH & Co. KG
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-01-21
Status Comments: 
Attachments: 
none



DD Entity: 380 Condensed Section Override State (209–224) 
Definition: This DDE is used by the implement to communicate that a certain section is overridden and will not follow the section control commands. The value is a combination of the override states of individual sections number 209 to 224 into a single override state. The condensed section override state contains the child element override states, in the driving direction from left to right, where the leftmost child element override state are the 2 lowest significant bits of the Process Data Value. Each child device elements override state is represented by 2 bits and defined as: 00 = section is not overridden, 01 = section is overridden, 10 = reserved, 11 = undefined / not installed. In total 16 child device element override states can be contained in one condensed section override state. If less than 16 child device element override states are available, then the unused bits shall be set to value 11 (undefined / not installed). This DDE shall be placed in the same device element as the corresponding actual condensed work state.
Comment: It is common for SC servers to show the current state of the sections in a proprietary screen. As the implement is allowed to override the commanded state from the task controller it is impossible for the operator to predict what happens when driving into an unworked area. With this DDE it is possible for the SC server to show overridden sections in the proprietary screen.

This DDE shall be defined as DPD in the DDOP of the implement. The DPD shall at least support the datalog triggers “on change” and “time based”. The value shall only be sent by the implement if it was requested (single request or datalog trigger) by the TC-SC server.
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
17 - Sensor System
Unit: not defined - not defined
Resolution: 1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Michael Köcher
Submit Date: 2015-01-21
Submit Company:  AMAZONEN-Werke H. Dreyer GmbH & Co. KG
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-01-21
Status Comments: 
Attachments: 
none



DD Entity: 381 Condensed Section Override State (225–240) 
Definition: This DDE is used by the implement to communicate that a certain section is overridden and will not follow the section control commands. The value is a combination of the override states of individual sections number 225 to 240 into a single override state. The condensed section override state contains the child element override states, in the driving direction from left to right, where the leftmost child element override state are the 2 lowest significant bits of the Process Data Value. Each child device elements override state is represented by 2 bits and defined as: 00 = section is not overridden, 01 = section is overridden, 10 = reserved, 11 = undefined / not installed. In total 16 child device element override states can be contained in one condensed section override state. If less than 16 child device element override states are available, then the unused bits shall be set to value 11 (undefined / not installed). This DDE shall be placed in the same device element as the corresponding actual condensed work state.
Comment: It is common for SC servers to show the current state of the sections in a proprietary screen. As the implement is allowed to override the commanded state from the task controller it is impossible for the operator to predict what happens when driving into an unworked area. With this DDE it is possible for the SC server to show overridden sections in the proprietary screen.

This DDE shall be defined as DPD in the DDOP of the implement. The DPD shall at least support the datalog triggers “on change” and “time based”. The value shall only be sent by the implement if it was requested (single request or datalog trigger) by the TC-SC server.
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
17 - Sensor System
Unit: not defined - not defined
Resolution: 1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Michael Köcher
Submit Date: 2015-01-21
Submit Company:  AMAZONEN-Werke H. Dreyer GmbH & Co. KG
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-01-21
Status Comments: 
Attachments: 
none



DD Entity: 382 Condensed Section Override State (241–256) 
Definition: This DDE is used by the implement to communicate that a certain section is overridden and will not follow the section control commands. The value is a combination of the override states of individual sections number 241 to 256 into a single override state. The condensed section override state contains the child element override states, in the driving direction from left to right, where the leftmost child element override state are the 2 lowest significant bits of the Process Data Value. Each child device elements override state is represented by 2 bits and defined as: 00 = section is not overridden, 01 = section is overridden, 10 = reserved, 11 = undefined / not installed. In total 16 child device element override states can be contained in one condensed section override state. If less than 16 child device element override states are available, then the unused bits shall be set to value 11 (undefined / not installed). This DDE shall be placed in the same device element as the corresponding actual condensed work state.
Comment: It is common for SC servers to show the current state of the sections in a proprietary screen. As the implement is allowed to override the commanded state from the task controller it is impossible for the operator to predict what happens when driving into an unworked area. With this DDE it is possible for the SC server to show overridden sections in the proprietary screen.

This DDE shall be defined as DPD in the DDOP of the implement. The DPD shall at least support the datalog triggers “on change” and “time based”. The value shall only be sent by the implement if it was requested (single request or datalog trigger) by the TC-SC server.
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
17 - Sensor System
Unit: not defined - not defined
Resolution: 1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Michael Köcher
Submit Date: 2015-01-21
Submit Company:  AMAZONEN-Werke H. Dreyer GmbH & Co. KG
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-01-21
Status Comments: 
Attachments: 
none



DD Entity: 383 Apparent Wind Direction
Definition: The apparent wind is the wind which is measured on a moving vehicle. It is the result of two motions: the actual true wind and the motion of the vehicle. The wind angle is referenced to the present heading of the vehicle (Zero degree refers to the vehicle driving direction).
Comment: DDI 207 defines the true wind.
DDI 208 defines the true wind angle.
Typically used by Device Classes: 
0 - Non-specific system
Unit: ° - Angle
Resolution: 1
SAE SPN: 
Range: 0 - 359
Submit by: Jan Steenbock
Submit Date: 2015-01-12
Submit Company: Müller-Elektronik GmbH & Co. KG
Revision Number: 2
Current Status: ISO-Published
Status Date: 2015-02-10
Status Comments: 
Attachments: 
none



DD Entity: 384 Apparent Wind Speed
Definition: The apparent wind is the wind which is measured on a moving vehicle. It is the result of two motions: the actual true wind and the motion of the vehicle.
Comment: DDI 207 defines the true wind.
DDI 208 defines the true wind angle.
Typically used by Device Classes: 
0 - Non-specific system
17 - Sensor System
Unit: mm/s - Speed
Resolution: 1
SAE SPN: 
Range: 0 - *********
Submit by: Jan Steenbock
Submit Date: 2015-01-12
Submit Company: Müller-Elektronik GmbH & Co. KG
Revision Number: 2
Current Status: ISO-Published
Status Date: 2015-02-10
Status Comments: 
Attachments: 
none



DD Entity: 385 MSL Atmospheric Pressure 
Definition: The atmospheric pressure MSL (Mean Sea Level) is the air pressure related to mean sea level.
Comment: In weather charts only the converted pressure to mean sea level is indicated. Only the pressure changes due to the weather has to be considered.
Typically used by Device Classes: 
0 - Non-specific system
17 - Sensor System
Unit: Pa - Pressure
Resolution: 0,1
SAE SPN: 
Range: 0 - 2000000
Submit by: Jan Steenbock
Submit Date: 2015-03-30
Submit Company: Müller-Elektronik GmbH & Co. KG
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-03-30
Status Comments: 
Attachments: 
none



DD Entity: 386 Actual Atmospheric Pressure
Definition: The Actual Atmospheric Pressure is the air pressure currently measured by the weather station.
Comment: This value does take the current altitude (field position) into count.
Typically used by Device Classes: 
0 - Non-specific system
17 - Sensor System
Unit: Pa - Pressure
Resolution: 0,1
SAE SPN: 
Range: 0 - 2000000
Submit by: Jan Steenbock
Submit Date: 2015-03-30
Submit Company: Müller-Elektronik GmbH & Co. KG
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-03-30
Status Comments: 
Attachments: 
none



DD Entity: 387 Total Revolutions in Fractional Revolutions
Definition: Accumulated Revolutions specified with fractional revolutions
Comment: Forward or Clockwise rotation represented as positive numbers and reverse or Counter-Clockwise rotation represented by negative numbers.To prevent rounding errors the basic unit r/min where chosen.
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
Unit: # - Quantity/Count
Resolution: 0,0001
SAE SPN: 
Range: -2147483648 - **********
Submit by: Mike Schmidt
Submit Date: 2015-04-24
Submit Company: AGCO Corporation
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-04-24
Status Comments: 
Attachments: 
none



DD Entity: 388 Total Revolutions in Complete Revolutions
Definition: Accumulated Revolutions specified as completed integer revolutions
Comment: Forward or Clockwise rotation represented as positive numbers and reverse or Counter-Clockwise rotation represented by negative numbers.
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
Unit: # - Quantity/Count
Resolution: 1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Mike Schmidt
Submit Date: 2015-04-24
Submit Company: AGCO Corporation
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-04-24
Status Comments: 
Attachments: 
none



DD Entity: 389 Setpoint Revolutions specified as count per time
Definition: Setpoint Revolutions specified as count per time
Comment: Forward or Clockwise rotation represented as positive numbers and reverse or Counter-Clockwise rotation represented by negative numbers. To prevent rounding errors the basic unit r/min where chosen. 
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
Unit: r/min - Revolutions per minute
Resolution: 0,0001
SAE SPN: 
Range: -2147483648 - **********
Submit by: David Kuhnel
Submit Date: 2015-04-24
Submit Company: DICKEY-john Corp
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-04-24
Status Comments: 
Attachments: 
none



DD Entity: 390 Actual Revolutions Per Time
Definition: Actual Revolutions specified as count per time
Comment: Forward or Clockwise rotation represented as positive numbers and reverse or Counter-Clockwise rotation represented by negative numbers. To prevent rounding errors the basic unit r/min where chosen. 
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
Unit: r/min - Revolutions per minute
Resolution: 0,0001
SAE SPN: 
Range: -2147483648 - **********
Submit by: David Kuhnel
Submit Date: 2015-03-23
Submit Company: DICKEY-john Corp
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-03-23
Status Comments: 
Attachments: 
none



DD Entity: 391 Default Revolutions Per Time
Definition: Default Revolutions specified as count per time
Comment: Forward or Clockwise rotation represented as positive numbers and reverse or Counter-Clockwise rotation represented by negative numbers. To prevent rounding errors the basic unit r/min where chosen.
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
Unit: r/min - Revolutions per minute
Resolution: 0,0001
SAE SPN: 
Range: -2147483648 - **********
Submit by: David Kuhnel
Submit Date: 2015-03-23
Submit Company: DICKEY-john Corp
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-03-23
Status Comments: 
Attachments: 
none



DD Entity: 392 Minimum Revolutions Per Time
Definition: Minimum Revolutions specified as count per time
Comment: Forward or Clockwise rotation represented as positive numbers and reverse or Counter-Clockwise rotation represented by negative numbers. To prevent rounding errors the basic unit r/min where chosen.
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
Unit: r/min - Revolutions per minute
Resolution: 0,0001
SAE SPN: 
Range: -2147483648 - **********
Submit by: David Kuhnel
Submit Date: 2015-03-23
Submit Company: DICKEY-john Corp
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-03-23
Status Comments: 
Attachments: 
none



DD Entity: 393 Maximum Revolutions Per Time
Definition: Maximum Revolutions specified as count per time
Comment: Forward or Clockwise rotation represented as positive numbers and reverse or Counter-Clockwise rotation represented by negative numbers. To prevent rounding errors the basic unit r/min where chosen.
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
Unit: r/min - Revolutions per minute
Resolution: 0,0001
SAE SPN: 
Range: -2147483648 - **********
Submit by: David Kuhnel
Submit Date: 2015-07-02
Submit Company: DICKEY-john Corp
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-07-02
Status Comments: 
Attachments: 
none



DD Entity: 394 Actual Fuel Tank Content
Definition: The actual content of the fuel tank 
Comment: This value can be used to see the refilling of the fuel tank or the theft of fuel.
Typically used by Device Classes: 
1 - Tractor
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
Unit: ml - Capacity large
Resolution: 1
SAE SPN: 
Range: 0 - 4294967295
Submit by: Hans van Zadelhoff
Submit Date: 2015-07-02
Submit Company: Grimme Landmaschinenfabrik GmbH & Co. KG
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-07-02
Status Comments: 
Attachments: 
none



DD Entity: 395 Actual Diesel Exhaust Fluid Tank Content
Definition: The actualcontent of the diesel exhaust fluid tank 
Comment: This value can be used to see the refilling of the diesel exhaust fluid tank or the theft of diesel exhaust fluid.
Typically used by Device Classes: 
1 - Tractor
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
Unit: ml - Capacity large
Resolution: 1
SAE SPN: 
Range: 0 - 4294967295
Submit by: Hans van Zadelhoff
Submit Date: 2015-07-02
Submit Company: Grimme Landmaschinenfabrik GmbH & Co. KG
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-07-02
Status Comments: 
Attachments: 
none



DD Entity: 396 Setpoint Speed
Definition: The setpoint speed that can be specified in a process data variable for communication between farm management information systems and mobile implement control systems. The setpoint speed DDI may also be used in a device description object pool to specify support for speed control by a device. A positive value will represent forward direction and a negative value will represent reverse direction.
Comment: The implementation of speed control on the mobile implement control system may use other ISO11783 network parameter groups (e.g. ISO11783-7 Commanded Vehicle Speed and Machine Selected Speed Setpoint) and may be subject to control request authentication requirements. The definition of this DDI has been added to the ISO 11783-11 data dictionary to facilitate the specification of a setpoint speed in a task data transfer file and to enable specification of the support of speed control in a device description object pool. 
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
Unit: mm/s - Speed
Resolution: 1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Hans van Zadelhoff
Submit Date: 2015-02-12
Submit Company: Grimme Landmaschinenfabrik GmbH & Co. KG
Revision Number: 3
Current Status: ISO-Published
Status Date: 2015-04-24
Status Comments: 
Attachments: 
none



DD Entity: 397 Actual Speed
Definition: The actual speed as measured on or used by a device for the execution of task based data, e.g. to convert a setpoint rate expressed per area to device specific control data that is expressed as a rate per time. The actual speed can be measured by the device itself or it can be a speed value that is obtained from one of the speed parameter groups that are broadcasted on the ISO11783 network and defined in ISO11783-7. Examples of broadcasted speed parameter groups are wheel based speed, ground based speed and machine selected speed. The source of the actual speed can be specified by a Speed Source DDI that is present in the same device element as the speed DDI.  A positive value will represent forward direction and a negative value will represent reverse direction.
Comment: This DDI has been added to the data dictionary to support logging of the speed that the device uses for processing and for generation of task data. The addition of a DDI for actual speed allows speed values to be added to the default data set that devices present to a task controller or a data logger.
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
Unit: mm/s - Speed
Resolution: 1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Hans van Zadelhoff
Submit Date: 2015-04-24
Submit Company: Grimme Landmaschinenfabrik GmbH & Co. KG
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-04-24
Status Comments: 
Attachments: 
none



DD Entity: 398 Minimum Speed
Definition: The minimum speed that can be specified in a process data variable for communication between farm management information systems and mobile implement control systems. A positive value will represent forward direction and a negative value will represent reverse direction.
Comment: This DDI has been added to the data dictionary to support the setting and logging of a minimum speed for a part of a device. See also the definitions of the Setpoint, Actual and Maximum Speed DDIs for additional definition and implementation information of Speed DDIs.
Typically used by Device Classes: 
Not Assigned
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
Unit: mm/s - Speed
Resolution: 1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Hans van Zadelhoff
Submit Date: 2015-09-02
Submit Company: Grimme Landmaschinenfabrik GmbH & Co. KG
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-09-02
Status Comments: 
Attachments: 
none



DD Entity: 399 Maximum Speed
Definition: The maximum speed that can be specified in a process data variable for communication between farm management information systems and mobile implement control systems.  A positive value will represent forward direction and a negative value will represent reverse direction.
Comment: This DDI has been added to the data dictionary to support the setting and logging of a maximum speed for a part of a device. See also the definitions of the Setpoint, Actual and Minimum Speed DDIs for additional definition and implementation information of Speed DDIs.
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
Unit: mm/s - Speed
Resolution: 1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Hans van Zadelhoff
Submit Date: 2015-09-02
Submit Company: Grimme Landmaschinenfabrik GmbH & Co. KG
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-09-02
Status Comments: 
Attachments: 
none



DD Entity: 400 Speed Source
Definition: The Speed Source that the device uses to report actual speed and to process the setpoint, minimum and maximum speeds. The Speed Source value is an enumeration with the following definitions:
    0 = Unknown
    1 = Wheel-based speed
    2 = Ground-based speed
    3 = Navigation-based speed
    4 = Blended speed
    5 = Simulated speed
    6 = Machine Selected speed
    7 = Machine measured speed (This option indicates the machine uses an own sensor to measures the actual speed, instead of the speed provided on the bus).
    8 to 100 = Reserved
Comment: The Speed Source DDI can be used in conjunction with the Actual Speed DDI to specify which speed measurement method is used to determine the value reported via the Actual, Setpoint, Minimum and Maximum Speed DDIs. When a device receives commands for Setpoint, Minimum or Maximum Speed then the Speed Source can be used to select a ISO 11783-7 command for speed control.
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
Unit: n.a. - 
Resolution: 1
SAE SPN: 
Range: 0 - 100
Submit by: Jaap van Bergeijk
Submit Date: 2015-09-02
Submit Company: AGCO corporation
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-09-02
Status Comments: 
Attachments: 
none



DD Entity: 401 Actual Application of Nitrogen
Definition: Actual application of Nitrogen [N2] specified as milligram per liter [mg/l]
Comment: Is the actual amount of Nitrogen [N2] in liquid manure ( see also DD Entity 353 )
Typically used by Device Classes: 
5 - Fertilizer
Unit: mg/l - Mass per capacity unit
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Christoph Staub
Submit Date: 2015-09-02
Submit Company: Zunhammer GmbH
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-03-11
Status Comments: 
Attachments: 
none



DD Entity: 402 Actual application of Ammonium
Definition: Actual application of Ammonium [NH4] specified as milligram per liter [mg/l]
Comment: Is the actual amount of Ammonium [NH4] in liquig manure ( see also DD Entity 354 )
Typically used by Device Classes: 
5 - Fertilizer
Unit: mg/l - Mass per capacity unit
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Christoph Staub
Submit Date: 2015-09-02
Submit Company: Zunhammer GmbH
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-03-11
Status Comments: 
Attachments: 
none



DD Entity: 403 Actual application of Phosphor
Definition: Actual application of Phosphor [P2O5] specified as milligram per liter [mg/l]
Comment: Is the actual amount of Phosphor [P2O5] in liquid manure ( see also DD Entity 355 )
Typically used by Device Classes: 
5 - Fertilizer
Unit: mg/l - Mass per capacity unit
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Christoph Staub
Submit Date: 2015-03-11
Submit Company: Zunhammer GmbH
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-07-30
Status Comments: 
Attachments: 
none



DD Entity: 404 Actual application of Potassium
Definition: Actual application of Potassium [K2] specified as gram [g]
Comment: is the actual amount of Potassium [K2] in liquid manure ( see also DD Entity 356 )
Typically used by Device Classes: 
5 - Fertilizer
Unit: g - Mass large
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Christoph Staub
Submit Date: 2015-09-02
Submit Company: Zunhammer GmbH
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-09-04
Status Comments: 
Attachments: 
none



DD Entity: 405 Actual application of Dry Matter
Definition: Actual application of Dry Matter in kilogram [kg]. Dry matter measured at Zero percent of moisture.
Comment: is the actual amount of Dry matter in liquid manure ( see also DD Entity 357 )
Typically used by Device Classes: 
5 - Fertilizer
Unit: kg - Mass
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Christoph Staub
Submit Date: 2015-09-02
Submit Company: Zunhammer GmbH
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-09-04
Status Comments: 
Attachments: 
none



DD Entity: 406 Actual Protein Content 
Definition: Actual Protein content of a harvested crops
Comment: Protein content of harvested crop expressed as a percent mass of the total crop.
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
Unit: ppm - Parts per million
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Joe Tevis
Submit Date: 2015-09-04
Submit Company: Topcon
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-09-04
Status Comments: 
Attachments: 
none



DD Entity: 407 Average Protein Content
Definition: Average protein content in a harvested crop
Comment: Average protein content of harvested crop.
Typically used by Device Classes: 
7 - Harvesters
8 - Root Harvester
Unit: ppm - Parts per million
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Joe Tevis
Submit Date: 2015-09-04
Submit Company: Topcon
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-09-04
Status Comments: 
Attachments: 
none



DD Entity: 408 Average Crop Contamination
Definition: Average amount of dirt or foreign  in a harvested crop
Comment: Average amount of dirt or foreign in a harvested crop
Typically used by Device Classes: 
7 - Harvesters
Unit: ppm - Parts per million
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Ben Craker
Submit Date: 2015-09-02
Submit Company: AGCO
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-06-15
Status Comments: 
Attachments: 
none



DD Entity: 409 Total Diesel Exhaust Fluid Consumption
Definition: Accumulated Diesel Exhaust Fluid Consumption as a Task Total.
Comment: This data definition is the Diesel Exhaust Fluid (DEF) consumption counterpart of the previously defined data dictionary entity 148 - Total Fuel Consumption. These data dictionary entities can be used by devices that support data logging of Fuel and Diesel Exhaust Fluid consumption.
Typically used by Device Classes: 
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
24 - Utility Vehicles
25 - Slurry Applicators
26 - Feeder / Mixer
Unit: ml - Capacity large
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Eric Bongaerts
Submit Date: 2015-06-01
Submit Company: AGCO
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-06-16
Status Comments: 
Attachments: 
http://dictionary.isobus.net/isobus/attachments/539/DEF_DDI_Requests-v1.pptx



DD Entity: 410 Instantaneous Diesel Exhaust Fluid Consumption per Time
Definition: Diesel Exhaust Fluid consumption per time
Comment: This data definition is the Diesel Exhaust Fluid (DEF) consumption counterpart of the previously defined data dictionary entity 149 - Instantaneous Fuel Consumption per Time. These data dictionary entities can be used by devices that support data logging of Fuel and Diesel Exhaust Fluid consumption.
Typically used by Device Classes: 
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
24 - Utility Vehicles
25 - Slurry Applicators
26 - Feeder / Mixer
Unit: mm³/s - Flow
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Eric Bongaerts
Submit Date: 2015-06-01
Submit Company: AGCO
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-06-16
Status Comments: 
Attachments: 
none



DD Entity: 411 Instantaneous Diesel Exhaust Fluid Consumption per Area
Definition: Diesel Exhaust Fluid consumption per area
Comment: This data definition is the Diesel Exhaust Fluid (DEF) consumption counterpart of the previously defined data dictionary entity 150 - Instantaneous Fuel Consumption per Area. These data dictionary entities can be used by devices that support data logging of Fuel and Diesel Exhaust Fluid consumption.
Typically used by Device Classes: 
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
24 - Utility Vehicles
25 - Slurry Applicators
26 - Feeder / Mixer
Unit: mm³/m² - Capacity per area unit
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Eric Bongaerts
Submit Date: 2015-06-01
Submit Company: AGCO
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-06-16
Status Comments: 
Attachments: 
none



DD Entity: 412 Lifetime Diesel Exhaust Fluid Consumption
Definition: Accumulated Diesel Exhaust Fluid Consumption over the entire lifetime of the device.
Comment: This is the overall total of the device. This total does not refer to an application controlled by a Task Controller. Therefore this DDE shall not be setable within the device description and neither shall the device reset the value when the task status changed. It is up to the device control system when to reset this value.

The Device shall support the total trigger method for this DDE but shall not support the setable property.

The Task Controller can request and store this DDE at the end of a task. But it shall not set this DDE when the task is resumed.

This data definition is the Diesel Exhaust Fluid (DEF) consumption counterpart of the previously defined data dictionary entity 276 - Lifetime Fuel Consumption. These data dictionary entities can be used by devices that support data logging of Fuel and Diesel Exhaust Fluid consumption.
Typically used by Device Classes: 
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
24 - Utility Vehicles
25 - Slurry Applicators
26 - Feeder / Mixer
Unit: L - Capacity count
Resolution: 0,5
SAE SPN: 5963
Range: 0 - **********
Submit by: Eric Bongaerts
Submit Date: 2015-06-01
Submit Company: AGCO
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-06-16
Status Comments: 
Attachments: 
none



DD Entity: 413 Lifetime Average Diesel Exhaust Fluid Consumption per Time
Definition: Average Diesel Exhaust Fluid Consumption per Time over the entire lifetime of the device.
Comment: This is the overall average of the device. This average does not refer to an application controlled by a Task Controller. Therefore this DDE shall not be setable within the device description and neither shall the device reset the value when the task status changes. It is up to the device control system when to reset this value.

The Device shall support the total trigger method for this DDE but shall not support the setable property.

The Task Controller can request and store this DDE at the end of a task. But it shall not set this DDE when the task is resumed.

This data definition is the Diesel Exhaust Fluid (DEF) consumption counterpart of the previously defined data dictionary entity 277 - Lifetime Average Fuel Consumption per Time. These data dictionary entities can be used by devices that support data logging of Fuel and Diesel Exhaust Fluid consumption.
Typically used by Device Classes: 
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
24 - Utility Vehicles
25 - Slurry Applicators
26 - Feeder / Mixer
Unit: mm³/s - Flow
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Eric Bongaerts
Submit Date: 2015-06-01
Submit Company: AGCO
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-06-16
Status Comments: Status was published
Attachments: 
none



DD Entity: 414 Lifetime Average Diesel Exhaust Fluid Consumption per Area
Definition: Average Diesel Exhaust Fluid Consumption per Area over the entire lifetime of the device.
Comment: This is the overall average of the device. This average does not refer to an application controlled by a Task Controller. Therefore this DDE shall not be setable within the device description and neither shall the device reset the value when the task status changes. It is up to the device control system when to reset this value.

The Device shall support the total trigger method for this DDE but shall not support the setable property.

The Task Controller can request and store this DDE at the end of a task. But it shall not set this DDE when the task is resumed.

This data definition is the Diesel Exhaust Fluid (DEF) consumption counterpart of the previously defined data dictionary entity 278 - Lifetime Average Fuel Consumption per Area. These data dictionary entities can be used by devices that support data logging of Fuel and Diesel Exhaust Fluid consumption.
Typically used by Device Classes: 
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
24 - Utility Vehicles
25 - Slurry Applicators
26 - Feeder / Mixer
Unit: mm³/m² - Capacity per area unit
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Eric Bongaerts
Submit Date: 2015-06-01
Submit Company: AGCO
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-06-16
Status Comments: 
Attachments: 
none



DD Entity: 415 Actual Seed Singulation Percentage
Definition: Actual Seed Singulation Percentage calculated from measured seed spacing using ISO 7256-1 "Quality of Feed Index" algorithm
Comment: Reference ISO 7256-1 "Quality of Feed Index" for details on the standardized method for calculating the seed singulation parameter.  The number of seed drops for calculating this real-time percentage is not specified due to the possible differences in measurement and performance of the equipment and seeding rates.
Typically used by Device Classes: 
4 - Planters /Seeders
Unit: ppm - Parts per million
Resolution: 1
SAE SPN: 
Range: 0 - 1000000
Submit by: Matthias Rothmund
Submit Date: 2015-03-25
Submit Company: HORSCH
Revision Number: 2
Current Status: ISO-Submitted (Pending)
Status Date: 2015-07-30
Status Comments: 
Attachments: 
none



DD Entity: 416 Average Seed Singulation Percentage
Definition: Average Seed Singulation Percentage calculated from measured seed spacing using ISO 7256-1 "Quality of Feed Index" algorithm. The value is the average for a Task.
Comment: Reference ISO 7256-1 "Quality of Feed Index" for details on the standardized method for calculating the seed singulation parameter.  The number of seed drops for calculating this real-time percentage is not specified due to the possible differences in measurement and performance of the equipment and seeding rates.
Typically used by Device Classes: 
4 - Planters /Seeders
Unit: ppm - Parts per million
Resolution: 1
SAE SPN: 
Range: 0 - 1000000
Submit by: Matthias Rothmund
Submit Date: 2015-03-25
Submit Company: HORSCH
Revision Number: 2
Current Status: ISO-Submitted (Pending)
Status Date: 2015-07-30
Status Comments: 
Attachments: 
none



DD Entity: 417 Actual Seed Skip Percentage
Definition: Actual Seed Skip Percentage calculated from measured seed spacing using ISO 7256-1 "Miss Index" algorithm
Comment: Reference ISO 7256-1 "Miss Index" for details on the standardized method for calculating a percentage.  The number of seed drops for calculating this real-time percentage is not specified due to the possible differences in measurement and performance of the equipment and seeding rates.
Typically used by Device Classes: 
4 - Planters /Seeders
Unit: ppm - Parts per million
Resolution: 1
SAE SPN: 
Range: 0 - 1000000
Submit by: Matthias Rothmund
Submit Date: 2015-03-25
Submit Company: HORSCH
Revision Number: 2
Current Status: ISO-Submitted (Pending)
Status Date: 2015-07-30
Status Comments: 
Attachments: 
none



DD Entity: 418 Average Seed Skip Percentage
Definition: Average Seed Skip Percentage calculated from measured seed spacing using ISO 7256-1 "Miss Index" algorithm. The value is the average for a Task.
Comment: Reference ISO 7256-1 "Miss Index" for details on the standardized method for calculating a percentage.  The number of seed drops for calculating this real-time percentage is not specified due to the possible differences in measurement and performance of the equipment and seeding rates.
Typically used by Device Classes: 
4 - Planters /Seeders
Unit: ppm - Parts per million
Resolution: 1
SAE SPN: 
Range: 0 - 1000000
Submit by: Matthias Rothmund
Submit Date: 2015-03-25
Submit Company: HORSCH
Revision Number: 3
Current Status: ISO-Submitted (Pending)
Status Date: 2015-07-30
Status Comments: 
Attachments: 
none



DD Entity: 419 Actual Seed Multiple Percentage
Definition: Actual Seed Multiple Percentage calculated from measured seed spacing using ISO 7256-1 "Multiples Index" algorithm.
Comment: Reference ISO 7256-1 "Multiples Index" for details on the standardized method for calculating a percentage.  The number of seed drops for calculating this real-time percentage is not specified due to the possible differences in measurement and performance of the equipment and seeding rates.
Typically used by Device Classes: 
4 - Planters /Seeders
Unit: ppm - Parts per million
Resolution: 1
SAE SPN: 
Range: 0 - 1000000
Submit by: Matthias Rothmund
Submit Date: 2015-03-25
Submit Company: HORSCH
Revision Number: 3
Current Status: ISO-Submitted (Pending)
Status Date: 2015-07-30
Status Comments: 
Attachments: 
none



DD Entity: 420 Average Seed Multiple Percentage
Definition: Average Seed Multiple Percentage calculated from measured seed spacing using ISO 7256-1 "Multiples Index" algorithm. The value is the average for a Task.
Comment: Reference ISO 7256-1 "Multiples Index" for details on the standardized method for calculating a percentage.  The number of seed drops for calculating this real-time percentage is not specified due to the possible differences in measurement and performance of the equipment and seeding rates.
Typically used by Device Classes: 
4 - Planters /Seeders
Unit: ppm - Parts per million
Resolution: 1
SAE SPN: 
Range: 0 - 1000000
Submit by: Matthias Rothmund
Submit Date: 2015-03-25
Submit Company: HORSCH
Revision Number: 3
Current Status: ISO-Submitted (Pending)
Status Date: 2015-07-30
Status Comments: 
Attachments: 
none



DD Entity: 421 Actual Seed Spacing Deviation
Definition: Actual Seed Spacing Deviation from setpoint seed spacing
Comment: Deviation is a positive value independently of if distance between seeds is smaller or larger than the setpoint value
Typically used by Device Classes: 
4 - Planters /Seeders
Unit: mm - Length
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Matthias Rothmund
Submit Date: 2015-03-25
Submit Company: HORSCH
Revision Number: 3
Current Status: ISO-Submitted (Pending)
Status Date: 2015-07-30
Status Comments: 
Attachments: 
none



DD Entity: 422 Average Seed Spacing Deviation
Definition: Average Seed Spacing Deviation from setpoint seed spacing. The value is the average for a Task.
Comment: Deviation is a positive value independently of if distance between seeds is smaller or larger than the setpoint value
Typically used by Device Classes: 
4 - Planters /Seeders
Unit: mm - Length
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Matthias Rothmund
Submit Date: 2015-03-25
Submit Company: HORSCH
Revision Number: 3
Current Status: ISO-Submitted (Pending)
Status Date: 2015-07-30
Status Comments: 
Attachments: 
none



DD Entity: 423 Actual Coefficient of Variation of Seed Spacing Percentage 
Definition: Actual Coefficient of Variation of Seed Spacing Percentage calculated from measured seed spacing using ISO 7256-1 algorithm
Comment: Reference ISO 7256-1 "Coefficient of Variation" for details on the standardized method for calculating a percentage.  The number of seed drops for calculating this real-time percentage is not specified due to the possible differences in measurement and performance of the equipment and seeding rates.
Typically used by Device Classes: 
4 - Planters /Seeders
Unit: ppm - Parts per million
Resolution: 1
SAE SPN: 
Range: 0 - 1000000
Submit by: Matthias Rothmund
Submit Date: 2015-03-25
Submit Company: HORSCH
Revision Number: 3
Current Status: ISO-Submitted (Pending)
Status Date: 2015-07-30
Status Comments: 
Attachments: 
none



DD Entity: 424 Average Coefficient of Variation of Seed Spacing Percentage
Definition: Average Coefficient of Variation of Seed Spacing Percentage calculated from measured seed spacing using ISO 7256-1 algorithm. The value is the average for a Task.
Comment: Reference ISO 7256-1 "Coefficient of Variation" for details on the standardized method for calculating a percentage.  The number of seed drops for calculating this real-time percentage is not specified due to the possible differences in measurement and performance of the equipment and seeding rates.
Typically used by Device Classes: 
4 - Planters /Seeders
Unit: ppm - Parts per million
Resolution: 1
SAE SPN: 
Range: 0 - 1000000
Submit by: Matthias Rothmund
Submit Date: 2015-03-25
Submit Company: HORSCH
Revision Number: 5
Current Status: ISO-Submitted (Pending)
Status Date: 2015-07-30
Status Comments: 
Attachments: 
none



DD Entity: 425 Setpoint Maximum Allowed Seed Spacing Deviation
Definition: Setpoint Maximum Allowed Seed Spacing Deviation
Comment: Value is for TIM purposes. An acceptable seeding quality range can be defined in a task or prescription. Deviation is a positive value independently of if distance between seeds is smaller or larger than the setpoint value
Typically used by Device Classes: 
4 - Planters /Seeders
Unit: mm - Length
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Matthias Rothmund
Submit Date: 2015-03-25
Submit Company: HORSCH
Revision Number: 5
Current Status: ISO-Submitted (Pending)
Status Date: 2015-07-30
Status Comments: 
Attachments: 
none



DD Entity: 426 Setpoint Downforce as Force
Definition: Setpoint Downforce as Force
Comment: This value represents the system pressure to produce the downforce (or upforce) for an operation messured in newton; In case of an negative value the system pressure would produce Upforce.
Typically used by Device Classes: 
4 - Planters /Seeders
Unit: N - Newton
Resolution: 1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Matthias Rothmund
Submit Date: 2015-09-03
Submit Company: HORSCH
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-09-03
Status Comments: 
Attachments: 
none



DD Entity: 427 Actual Downforce as Force
Definition: Actual Downforce as Force
Comment: This value represents the actual downforce to produce the downforce (or upforce) for an operation messured in newton; In case of an negative value the system pressure would produce Upforce.
Typically used by Device Classes: 
4 - Planters /Seeders
Unit: N - Newton
Resolution: 1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Matthias Rothmund
Submit Date: 2015-09-03
Submit Company: HORSCH
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-09-03
Status Comments: 
Attachments: 
none



DD Entity: 428 Loaded Total Mass
Definition: Accumulated Loads specified as mass, not corrected for the reference moisture percentage DDI 184.
Comment: Is a counter of a machine element.

Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
11 - Transport / Trailers
17 - Sensor System
Unit: kg - Mass
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Franz Hoepfinger
Submit Date: 2015-07-24
Submit Company: 367 - Fliegl Agrartechnik GmbH
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-11-02
Status Comments: 
Attachments: 
none



DD Entity: 429 Unloaded Total Mass
Definition: Accumulated Unloads specified as mass, not corrected for the reference moisture percentage DDI 184.
Comment: Is a counter of a machine element.

Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
11 - Transport / Trailers
17 - Sensor System
Unit: kg - Mass
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Franz Hoepfinger
Submit Date: 2015-07-24
Submit Company: 367 - Fliegl Agrartechnik GmbH
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-11-02
Status Comments: 
Attachments: 
none



DD Entity: 430 Lifetime Loaded Total Mass
Definition: Entire Yield Total Mass of the device lifetime.

Comment: This is the overall total of the device. This total does not refer to an application controlled by a Task Controller. Therefore this DDE shall not be setable within the device description and neither shall the device reset the value when the task status changes. It is up to the device control system when to reset this value.

The Working Set Master shall support the total trigger method for this DDE but shall not support the setable property.

The Task Controller can request and store this DDE at the end of a task. But it shall not set this DDE when the task is resumed.
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
11 - Transport / Trailers
17 - Sensor System
Unit: kg - Mass
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Franz Hoepfinger
Submit Date: 2015-07-24
Submit Company: 367 - Fliegl Agrartechnik GmbH
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-11-02
Status Comments: 
Attachments: 
none



DD Entity: 431 Lifetime Unloaded Total Mass
Definition: Entire Unloaded Total Mass of the device lifetime.

Comment: This is the overall total of the device. This total does not refer to an application controlled by a Task Controller. Therefore this DDE shall not be setable within the device description and neither shall the device reset the value when the task status changes. It is up to the device control system when to reset this value.

The Working Set Master shall support the total trigger method for this DDE but shall not support the setable property.

The Task Controller can request and store this DDE at the end of a task. But it shall not set this DDE when the task is resumed.
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
11 - Transport / Trailers
17 - Sensor System
Unit: kg - Mass
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Franz Hoepfinger
Submit Date: 2015-07-24
Submit Company: 367 - Fliegl Agrartechnik GmbH
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-11-02
Status Comments: 
Attachments: 
none



DD Entity: 447 Maximum Application Rate of Potassium
Definition: Maximum application rate of potassium specified as a mass per area
Comment: 
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
17 - Sensor System
25 - Slurry Applicators
Unit: mg/m² - Mass per area unit
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Jan Steenbock
Submit Date: 2015-11-03
Submit Company: Müller-Elektronik GmbH  Co. KG
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-11-03
Status Comments: 
Attachments: 
none



DD Entity: 448 Setpoint Application Rate of Dry Matter
Definition: Setpoint application rate of dry matter expressed as percentage
Comment: As a reference the toal amount of dry matter will be documented with DDE 357
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
17 - Sensor System
25 - Slurry Applicators
Unit: ppm (parts per million) - Quantity per quantity unit
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Jan Steebock
Submit Date: 2015-11-03
Submit Company: Müller-Elektronik GmbH  Co. KG
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-11-03
Status Comments: 
Attachments: 
none



DD Entity: 449 Actual  Application Rate of Dry Matter
Definition: Actual application rate of dry matter expressed as percentage
Comment: As a reference the toal amount of dry matter will be documented with DDE 357
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
17 - Sensor System
25 - Slurry Applicators
Unit: ppm (parts per million) - Quantity per quantity unit
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Jan Steenbock
Submit Date: 2015-09-03
Submit Company: Müller-Elektronik GmbH  Co. KG
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-11-03
Status Comments: 
Attachments: 
none



DD Entity: 450 Minimum Application Rate of Dry Matter
Definition: Minimum application rate of dry matter expressed as percentage
Comment: 
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
17 - Sensor System
25 - Slurry Applicators
Unit: ppm (parts per million) - Quantity per quantity unit
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Jan Steenbock
Submit Date: 2015-11-03
Submit Company: Müller-Elektronik GmbH  Co. KG
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-11-03
Status Comments: 
Attachments: 
none



DD Entity: 451 Maximum Application Rate of Dry Matter
Definition: Maximum application rate of dry matter expressed as percentage
Comment: 
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
17 - Sensor System
25 - Slurry Applicators
Unit: ppm (parts per million) - Quantity per quantity unit
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Jan Steenbock
Submit Date: 2015-11-03
Submit Company: Müller-Elektronik GmbH  Co. KG
Revision Number: 1
Current Status: ISO-Published
Status Date: 2015-11-03
Status Comments: 
Attachments: 
none



DD Entity: 452 Loaded Total Volume
Definition: Accumulated Loaded Volume specified as volume
Comment: Is a counter of a machine element

Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
17 - Sensor System
Unit: ml - Capacity large
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Franz Hoepfinger
Submit Date: 2015-07-24
Submit Company: 367 - Fliegl Agrartechnik GmbH
Revision Number: 2
Current Status: ISO-Published
Status Date: 2015-11-03
Status Comments: 
Attachments: 
none



DD Entity: 453 Unloaded Total Volume
Definition: Accumulated Unloaded Volume specified as volume
Comment: Is a counter of a machine element

Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
17 - Sensor System
Unit: ml - Capacity large
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Franz Hoepfinger
Submit Date: 2015-07-24
Submit Company: 367 - Fliegl Agrartechnik GmbH
Revision Number: 2
Current Status: ISO-Published
Status Date: 2015-11-03
Status Comments: 
Attachments: 
none



DD Entity: 454 Lifetime loaded Total Volume
Definition: Entire loaded Volume of the device lifetime.

Comment: This is the overall total of the device. This total does not refer to an application controlled by a Task Controller. Therefore this DDE shall not be setable within the device description and neither shall the device reset the value when the task status changes. It is up to the device control system when to reset this value.

The Working Set Master shall support the total trigger method for this DDE but shall not support the setable property.

The Task Controller can request and store this DDE at the end of a task. But it shall not set this DDE when the task is resumed.
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
17 - Sensor System
Unit: L - Capacity count
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Franz Hoepfinger
Submit Date: 2015-07-24
Submit Company: 367 - Fliegl Agrartechnik GmbH
Revision Number: 2
Current Status: ISO-Published
Status Date: 2015-11-03
Status Comments: 
Attachments: 
none



DD Entity: 455 Lifetime Unloaded Total Volume
Definition: Entire unloaded Volume of the device lifetime.
Comment: This is the overall total of the device. This total does not refer to an application controlled by a Task Controller. Therefore this DDE shall not be setable within the device description and neither shall the device reset the value when the task status changes. It is up to the device control system when to reset this value.

The Working Set Master shall support the total trigger method for this DDE but shall not support the setable property.

The Task Controller can request and store this DDE at the end of a task. But it shall not set this DDE when the task is resumed.
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
17 - Sensor System
Unit: L - Capacity count
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Franz Hoepfinger
Submit Date: 2015-07-24
Submit Company: 367 - Fliegl Agrartechnik GmbH
Revision Number: 2
Current Status: ISO-Published
Status Date: 2015-11-03
Status Comments: 
Attachments: 
none



DD Entity: 456 Last loaded Volume
Definition: Last loaded Volume value specified as volume

Comment: After a loading Procedure, this DDI sends the loaded Volume. 

For more information see attachment located at Last loaded Weight DDE320
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
17 - Sensor System
Unit: ml - Capacity large
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Franz Hoepfinger
Submit Date: 2015-07-24
Submit Company: 367 - Fliegl Agrartechnik GmbH
Revision Number: 2
Current Status: ISO-Published
Status Date: 2015-11-03
Status Comments: 
Attachments: 
none



DD Entity: 457 Last unloaded Volume
Definition: Last unloaded Volume value specified as volume

Comment: After a unloading Procedure, this DDI sends the uloaded Volume. 

For more information see attachment located at Last loaded Weight DDE320

Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
17 - Sensor System
Unit: ml - Capacity large
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Franz Hoepfinger
Submit Date: 2015-07-24
Submit Company: 367 - Fliegl Agrartechnik GmbH
Revision Number: 2
Current Status: ISO-Published
Status Date: 2015-11-03
Status Comments: 
Attachments: 
none



DD Entity: 458 Loaded Total Count
Definition: Accumulated Loads specified as count



Comment: Is a counter of a device element

Typically used by Device Classes: 
4 - Planters /Seeders
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
11 - Transport / Trailers
14 - Special Crops
17 - Sensor System
Unit: # - Quantity/Count
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Franz Hoepfinger
Submit Date: 2015-07-24
Submit Company: 367 - Fliegl Agrartechnik GmbH
Revision Number: 2
Current Status: ISO-Published
Status Date: 2015-11-03
Status Comments: 
Attachments: 
none



DD Entity: 459 Unloaded Total Count
Definition: Accumulated Unloaded specified as count
Comment: Is a counter of a device element

Typically used by Device Classes: 
4 - Planters /Seeders
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
11 - Transport / Trailers
14 - Special Crops
17 - Sensor System
Unit: # - Quantity/Count
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Franz Hoepfinger
Submit Date: 2015-07-24
Submit Company: 367 - Fliegl Agrartechnik GmbH
Revision Number: 2
Current Status: ISO-Published
Status Date: 2015-11-03
Status Comments: 
Attachments: 
none



DD Entity: 460 Lifetime Loaded Total Count
Definition: Entire Loaded Total Count of the device lifetime.
Comment: This is the overall total of the device. This total does not refer to an application controlled by a Task Controller. Therefore this DDE shall not be setable within the device description and neither shall the device reset the value when the task status changes. It is up to the device control system when to reset this value.

The Working Set Master shall support the total trigger method for this DDE but shall not support the setable property.

The Task Controller can request and store this DDE at the end of a task. But it shall not set this DDE when the task is resumed.
Typically used by Device Classes: 
4 - Planters /Seeders
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
11 - Transport / Trailers
14 - Special Crops
17 - Sensor System
Unit: # - Quantity/Count
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Franz Hoepfinger
Submit Date: 2015-07-24
Submit Company: 367 - Fliegl Agrartechnik GmbH
Revision Number: 2
Current Status: ISO-Published
Status Date: 2015-11-03
Status Comments: 
Attachments: 
none



DD Entity: 461 Lifetime Unloaded Total Count
Definition: Entire Unloaded Total Count of the device lifetime.

Comment: This is the overall total of the device. This total does not refer to an application controlled by a Task Controller. Therefore this DDE shall not be setable within the device description and neither shall the device reset the value when the task status changes. It is up to the device control system when to reset this value.

The Working Set Master shall support the total trigger method for this DDE but shall not support the setable property.

The Task Controller can request and store this DDE at the end of a task. But it shall not set this DDE when the task is resumed.
Typically used by Device Classes: 
4 - Planters /Seeders
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
11 - Transport / Trailers
14 - Special Crops
17 - Sensor System
Unit: # - Quantity/Count
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Franz Hoepfinger
Submit Date: 2015-07-24
Submit Company: 367 - Fliegl Agrartechnik GmbH
Revision Number: 2
Current Status: ISO-Published
Status Date: 2015-11-03
Status Comments: 
Attachments: 
none



DD Entity: 462 Last loaded Count
Definition: Last loaded Count value specified as count

Comment: After a loading Procedure, this DDI sends the loaded Count. 

For more information see attachment located at Last loaded Weight DDE320

Typically used by Device Classes: 
4 - Planters /Seeders
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
11 - Transport / Trailers
17 - Sensor System
Unit: # - Quantity/Count
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Franz Hoepfinger
Submit Date: 2015-07-24
Submit Company: 367 - Fliegl Agrartechnik GmbH
Revision Number: 2
Current Status: ISO-Published
Status Date: 2015-11-03
Status Comments: 
Attachments: 
none



DD Entity: 463 Last unloaded Count
Definition: Last unloaded Count value specified as count
Comment: After a unloading Procedure, this DDI sends the loaded Count. 

For more information see attachment located at Last loaded Weight DDE 320

Typically used by Device Classes: 
4 - Planters /Seeders
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
11 - Transport / Trailers
14 - Special Crops
17 - Sensor System
Unit: # - Quantity/Count
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Franz Hoepfinger
Submit Date: 2015-07-24
Submit Company: 367 - Fliegl Agrartechnik GmbH
Revision Number: 2
Current Status: ISO-Published
Status Date: 2015-11-03
Status Comments: 
Attachments: 
none



DD Entity: 464 Haul Counter
Definition: Each Time a Device Element is filled and emptied this is called a haul cycle. This counter counts the cycles
Comment: Is a counter of a device element. Can be used to count loads, fillings, tippings and such. 

Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
11 - Transport / Trailers
17 - Sensor System
Unit: # - Quantity/Count
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Franz Hoepfinger
Submit Date: 2015-07-24
Submit Company: 367 - Fliegl Agrartechnik GmbH
Revision Number: 2
Current Status: ISO-Published
Status Date: 2015-12-14
Status Comments: 
Attachments: 
none



DD Entity: 465 Lifetime Haul Counter
Definition: The number of haul cycles done by a machine over its entire lifetime. This DDE value can not be set through the process data interface but can be requested and added to a datalog. This DDE value is not affected by a task based total haul cycles but will increment at the same rate as the task based total.

Comment: This is the overall total of the device. This total does not refer to an application controlled by a Task Controller. Therefore this DDE shall not be setable within the device description and neither shall the device reset the value when the task status changes. It is up to the device control system when to reset this value.

The Working Set Master shall support the total trigger method for this DDE but shall not support the setable property.

The Task Controller can request and store this DDE at the end of a task. But it shall not set this DDE when the task is resumed.
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
17 - Sensor System
Unit: # - Quantity/Count
Resolution: 1
SAE SPN: 
Range: 0 - **********
Submit by: Franz Hoepfinger
Submit Date: 2015-07-24
Submit Company: 367 - Fliegl Agrartechnik GmbH
Revision Number: 2
Current Status: ISO-Published
Status Date: 2015-12-14
Status Comments: 
Attachments: 
none



DD Entity: 466 Actual relative connector angle
Definition: The DDI Actual relative connector angle shall be placed in the device element of type connector in the DDOP of the TC-SC Client. The value describes the actual angle of the longitudinal axis of the implement relative to the longitudinal axis of the tractor. This angle should be used by the TC-SC server to calculate the real position of implement. The TC-SC server may smooth the rendering in any proprietary screen. 
The reference system is the coordinate system of the tractor. This results in the angles from table 1 of the attachment. 
In case of for example a malfunction sensor the error value is set to 0xFExxxxxx.
Comment: When working with Section Control it is necessary that the TC-Server calculates the exact position of the implement and its boom and sections to mark the covered area on its section control screen properly. To calculate the positions the TC-SC server uses the x and y offsets of the DRP and CRP. This works well for mounted and for non-steered trailed implements but comes up against limits when implements do have a steering axle or even a steering drawbar because the TC-SC server can’t know the current steering angle and moving the DRP doesn’t fit in all means. But this could be solved when the TC-SC server would knew the exact angle of the implement related to the tractor. This information could be provided by the implement because when they have a steering mechanism they even have a sensor to measure the angle between tractor and implement.
Typically used by Device Classes: 
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
25 - Slurry Applicators
Unit: ° - Angle
Resolution: 0,001
SAE SPN: 
Range: -180000 - 180000
Submit by: Thomas Konermann
Submit Date: 2015-07-27
Submit Company: AMAZONEN-Werke H. Dreyer GmbH & Co. KG
Revision Number: 2
Current Status: ISO-Published
Status Date: 2016-01-25
Status Comments: 
Attachments: 
http://dictionary.isobus.net/isobus/attachments/577/Actual relative connector angle-v1.pdf



DD Entity: 467 Actual  Application Rate of Nitrogen
Definition: Actual application rate of nitrogen specified as a mass per area
Comment: Accumulated application of nitrogen DDE 353
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
10 - Irrigation
25 - Slurry Applicators
Unit: mg/m² - Mass per area unit
Resolution: 1
SAE SPN: 
Range: 0 - 4294967295
Submit by: Jan Steenbock
Submit Date: 2016-01-20
Submit Company: Müller-Elektronik GmbH & Co. KG
Revision Number: 1
Current Status: ISO-Published
Status Date: 2016-02-16
Status Comments: 
Attachments: 
none



DD Entity: 468 Actual  Application Rate of Ammonium
Definition: Actual application rate of Ammonium specified as a mass per area
Comment: Accumulated application of ammonium DDE 354
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
10 - Irrigation
25 - Slurry Applicators
Unit: mg/m² - Mass per area unit
Resolution: 1
SAE SPN: 
Range: 0 - 4294967295
Submit by: Jan Steenbock
Submit Date: 2016-01-20
Submit Company: Müller-Elektronik GmbH & Co. KG
Revision Number: 1
Current Status: ISO-Published
Status Date: 2016-02-16
Status Comments: 
Attachments: 
none



DD Entity: 469 Actual  Application Rate of Phosphor
Definition: Actual application rate of phosphor specified as a mass per area
Comment: Accumulated application of phosphor DDE 355
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
10 - Irrigation
25 - Slurry Applicators
Unit: mg/m² - Mass per area unit
Resolution: 1
SAE SPN: 
Range: 0 - 4294967295
Submit by: Jan Steenbock
Submit Date: 2016-01-20
Submit Company: Müller-Elektronik GmbH & Co. KG
Revision Number: 1
Current Status: ISO-Published
Status Date: 2016-02-16
Status Comments: 
Attachments: 
none



DD Entity: 470 Actual  Application Rate of Potassium
Definition: Actual application rate of potassium specified as a mass per area
Comment: Accumulated application of potassium DDE 356
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
10 - Irrigation
25 - Slurry Applicators
Unit: mg/m² - Mass per area unit
Resolution: 1
SAE SPN: 
Range: 0 - 4294967295
Submit by: Jan Steenbock
Submit Date: 2016-01-20
Submit Company: Müller-Elektronik GmbH & Co. KG
Revision Number: 1
Current Status: ISO-Published
Status Date: 2016-02-16
Status Comments: 
Attachments: 
none



DD Entity: 471 Actual  Application Rate of Dry Matter
Definition: Actual application rate of dry matter expressed as percentage
Comment: Accumulated application of dry matter DDE 357
Typically used by Device Classes: 
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
10 - Irrigation
25 - Slurry Applicators
Unit: ppm - Parts per million
Resolution: 1
SAE SPN: 
Range: 0 - 4294967295
Submit by: Jan Steenbock
Submit Date: 2016-01-20
Submit Company: Müller-Elektronik GmbH & Co. KG
Revision Number: 1
Current Status: ISO-Published
Status Date: 2016-02-16
Status Comments: 
Attachments: 
none



DD Entity: 57342 PGN Based Data
Definition: This DDI is used in the XML files to identify PGN based data.
Comment: This DDI is specified in ISO 11783-10 IS paragraph 6.3 Logging parameters from parameter groups.
Typically used by Device Classes: 
0 - Non-specific system
1 - Tractor
2 - Primary Soil Tillage
3 - Secondary Soil Tillage
4 - Planters /Seeders
5 - Fertilizer
6 - Sprayers
7 - Harvesters
8 - Root Harvester
9 - Forage harvester
10 - Irrigation
11 - Transport / Trailers
12 - Farmyard Work
13 - Powered Auxilary Units
14 - Special Crops
15 - Municipal Work
Unit: n.a. - 
Resolution: 1
SAE SPN: 
Range: -2147483648 - **********
Submit by: Part 10 Task Force
Submit Date: 2005-01-25
Submit Company: 0 - Reserved
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2009-02-05
Status Comments: Added to the on-line database
Attachments: 
none



DD Entity: 57343 Request Default Process Data
Definition: Request Default Process Data. This DDE is the highest ISO assigned entity.  The range above this number is reserved for manufacture specific DDE's.
Comment: This DDE was a result of Jan 2005 Task Controller meeting.
Typically used by Device Classes: 
Not Assigned
Unit: n.a. - 
Resolution: 1
SAE SPN: not specified
Range: 0 - 0
Submit by: 11783-Part 10 Task Force
Submit Date: 2005-01-20
Submit Company: 89 - Kverneland Group, Electronics Division
Revision Number: 1
Current Status: ISO-Approved
Status Date: 2009-02-05
Status Comments: Updated description, added reference to ISO11783-10
Attachments: 
none



DD Entity: 57344 65534 Proprietary DDI Range
Definition: Manufacturer proprietary definitions
Comment: It is not recommended to process proprietary DDEs from other manufacturers
Typically used by Device Classes: 
Not Assigned
Unit: n.a. - 
Resolution: 0
SAE SPN: 
Range:  - 
Submit by: Part 10 Task Force
Submit Date: 
Submit Company: 0 - Reserved
Revision Number: 1
Current Status: 
Status Date: 
Status Comments: 
Attachments: 
none



DD Entity: 65535 Reserved
Definition: Reserved
Comment: 
Typically used by Device Classes: 
Not Assigned
Unit: n.a. - 
Resolution: 0
SAE SPN: 
Range:  - 
Submit by: Part 10 Task Force
Submit Date: 
Submit Company: 0 - Reserved
Revision Number: 1
Current Status: 
Status Date: 
Status Comments: 
Attachments: 
none


