﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <appSettings>
    <add key="Trimble AgData" value="{074cd482-0001-0000-0000-71281012f037}" />
    <add key="Trimble AgGPS" value="{074cd482-0001-0000-0000-71281012f037}" />
    <add key="GS3_2630" value="{66f62b80-626f-496c-9353-89cb16fd247d}" />
    <add key="GS4_4600Plugin" value="{66f62b80-626f-496c-9353-89cb16fd247d}" />
    <add key="ISOv4Plugin" value="" />
    <add key="ADMPlugin" value="" />
    <add key="TF-Plugin" value="" />
    <add key="GS2_1800" value="{66f62b80-626f-496c-9353-89cb16fd247d}" />
    <add key="GS2_CommandCenter" value="{66f62b80-626f-496c-9353-89cb16fd247d}" />
    <add key="GS2_2600" value="{66f62b80-626f-496c-9353-89cb16fd247d}" />
    <add key="ClientSettingsProvider.ServiceUri" value="" />
  </appSettings>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.5.2" />
  </startup>
  <runtime>
    <loadFromRemoteSources enabled="true" />
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="protobuf-net" publicKeyToken="257b51d87d2e4d67" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  <system.web>
    <membership defaultProvider="ClientAuthenticationMembershipProvider">
      <providers>
        <add name="ClientAuthenticationMembershipProvider" type="System.Web.ClientServices.Providers.ClientFormsAuthenticationMembershipProvider, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" serviceUri="" />
      </providers>
    </membership>
    <roleManager defaultProvider="ClientRoleProvider" enabled="true">
      <providers>
        <add name="ClientRoleProvider" type="System.Web.ClientServices.Providers.ClientRoleProvider, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" serviceUri="" cacheTimeout="86400" />
      </providers>
    </roleManager>
  </system.web>
</configuration>