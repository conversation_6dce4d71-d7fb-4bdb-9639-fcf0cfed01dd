<logEntry>
  <Date>03/29/2019 10:03:12</Date>
  <Exception>
    <Source>ADAPTConverter</Source>
    <Message>Object reference not set to an instance of an object</Message>
    <Stack>  at john_deere_export.ADAPTFactory.GetPolygonList (GeoJSON.Net.Feature.FeatureCollection featureCollection, System.Boolean loadInner) [0x00028] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.ADAPTFactory.Generate (GeoJSON.Net.Feature.FeatureCollection featureCollection, john_deere_export.ExportParameter param) [0x00189] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.Program.ProccessExportData (john_deere_export.ExportParameter param) [0x00044] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 </Stack>
  </Exception>
</logEntry>
<logEntry>
  <Date>03/29/2019 10:04:08</Date>
  <Exception>
    <Source>ADAPTConverter</Source>
    <Message>Object reference not set to an instance of an object</Message>
    <Stack>  at john_deere_export.ADAPTFactory.GetPolygonList (GeoJSON.Net.Feature.FeatureCollection featureCollection, System.Boolean loadInner) [0x00028] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.ADAPTFactory.Generate (GeoJSON.Net.Feature.FeatureCollection featureCollection, john_deere_export.ExportParameter param) [0x00189] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.Program.ProccessExportData (john_deere_export.ExportParameter param) [0x00044] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 </Stack>
  </Exception>
</logEntry>
<logEntry>
  <Date>04/05/2019 14:13:17</Date>
  <Exception>
    <Source>ADAPTConverter</Source>
    <Message>Object reference not set to an instance of an object</Message>
    <Stack>  at john_deere_export.ADAPTFactory.GetPolygonList (GeoJSON.Net.Feature.FeatureCollection featureCollection, System.Boolean loadInner) [0x00028] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.ADAPTFactory.Generate (GeoJSON.Net.Feature.FeatureCollection featureCollection, john_deere_export.ExportParameter param) [0x00189] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.Program.ProccessExportData (john_deere_export.ExportParameter param) [0x00044] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 </Stack>
  </Exception>
</logEntry>
<logEntry>
  <Date>04/18/2019 17:05:50</Date>
  <Exception>
    <Source>ADAPTConverter</Source>
    <Message>Object reference not set to an instance of an object</Message>
    <Stack>  at john_deere_export.ADAPTFactory.GetPolygonList (GeoJSON.Net.Feature.FeatureCollection featureCollection, System.Boolean loadInner) [0x00028] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.ADAPTFactory.Generate (GeoJSON.Net.Feature.FeatureCollection featureCollection, john_deere_export.ExportParameter param) [0x00189] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.Program.ProccessExportData (john_deere_export.ExportParameter param) [0x00044] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 </Stack>
  </Exception>
</logEntry>
<logEntry>
  <Date>04/18/2019 17:06:23</Date>
  <Exception>
    <Source>ADAPTConverter</Source>
    <Message>Object reference not set to an instance of an object</Message>
    <Stack>  at john_deere_export.ADAPTFactory.GetPolygonList (GeoJSON.Net.Feature.FeatureCollection featureCollection, System.Boolean loadInner) [0x00028] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.ADAPTFactory.Generate (GeoJSON.Net.Feature.FeatureCollection featureCollection, john_deere_export.ExportParameter param) [0x00189] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.Program.ProccessExportData (john_deere_export.ExportParameter param) [0x00044] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 </Stack>
  </Exception>
</logEntry>
<logEntry>
  <Date>04/18/2019 17:06:51</Date>
  <Exception>
    <Source>ADAPTConverter</Source>
    <Message>Object reference not set to an instance of an object</Message>
    <Stack>  at john_deere_export.ADAPTFactory.GetPolygonList (GeoJSON.Net.Feature.FeatureCollection featureCollection, System.Boolean loadInner) [0x00028] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.ADAPTFactory.Generate (GeoJSON.Net.Feature.FeatureCollection featureCollection, john_deere_export.ExportParameter param) [0x00189] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.Program.ProccessExportData (john_deere_export.ExportParameter param) [0x00044] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 </Stack>
  </Exception>
</logEntry>
<logEntry>
  <Date>05/08/2019 10:43:43</Date>
  <Exception>
    <Source>ADAPTConverter</Source>
    <Message>Object reference not set to an instance of an object</Message>
    <Stack>  at john_deere_export.ADAPTFactory.GetPolygonList (GeoJSON.Net.Feature.FeatureCollection featureCollection, System.Boolean loadInner) [0x00028] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.ADAPTFactory.Generate (GeoJSON.Net.Feature.FeatureCollection featureCollection, john_deere_export.ExportParameter param) [0x00189] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.Program.ProccessExportData (john_deere_export.ExportParameter param) [0x00044] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 </Stack>
  </Exception>
</logEntry>
<logEntry>
  <Date>05/08/2019 10:44:17</Date>
  <Exception>
    <Source>ADAPTConverter</Source>
    <Message>Object reference not set to an instance of an object</Message>
    <Stack>  at john_deere_export.ADAPTFactory.GetPolygonList (GeoJSON.Net.Feature.FeatureCollection featureCollection, System.Boolean loadInner) [0x00028] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.ADAPTFactory.Generate (GeoJSON.Net.Feature.FeatureCollection featureCollection, john_deere_export.ExportParameter param) [0x00189] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.Program.ProccessExportData (john_deere_export.ExportParameter param) [0x00044] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 </Stack>
  </Exception>
</logEntry>
<logEntry>
  <Date>05/08/2019 10:45:09</Date>
  <Exception>
    <Source>ADAPTConverter</Source>
    <Message>Object reference not set to an instance of an object</Message>
    <Stack>  at john_deere_export.ADAPTFactory.GetPolygonList (GeoJSON.Net.Feature.FeatureCollection featureCollection, System.Boolean loadInner) [0x00028] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.ADAPTFactory.Generate (GeoJSON.Net.Feature.FeatureCollection featureCollection, john_deere_export.ExportParameter param) [0x00189] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.Program.ProccessExportData (john_deere_export.ExportParameter param) [0x00044] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 </Stack>
  </Exception>
</logEntry>
<logEntry>
  <Date>05/08/2019 11:00:35</Date>
  <Exception>
    <Source>ADAPTConverter</Source>
    <Message>Object reference not set to an instance of an object</Message>
    <Stack>  at john_deere_export.ADAPTFactory.GetPolygonList (GeoJSON.Net.Feature.FeatureCollection featureCollection, System.Boolean loadInner) [0x00028] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.ADAPTFactory.Generate (GeoJSON.Net.Feature.FeatureCollection featureCollection, john_deere_export.ExportParameter param) [0x00189] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.Program.ProccessExportData (john_deere_export.ExportParameter param) [0x00044] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 </Stack>
  </Exception>
</logEntry>
<logEntry>
  <Date>05/08/2019 11:05:37</Date>
  <Exception>
    <Source>ADAPTConverter</Source>
    <Message>Object reference not set to an instance of an object</Message>
    <Stack>  at john_deere_export.ADAPTFactory.GetPolygonList (GeoJSON.Net.Feature.FeatureCollection featureCollection, System.Boolean loadInner) [0x00028] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.ADAPTFactory.Generate (GeoJSON.Net.Feature.FeatureCollection featureCollection, john_deere_export.ExportParameter param) [0x00189] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.Program.ProccessExportData (john_deere_export.ExportParameter param) [0x00044] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 </Stack>
  </Exception>
</logEntry>
<logEntry>
  <Date>05/08/2019 11:06:00</Date>
  <Exception>
    <Source>ADAPTConverter</Source>
    <Message>Object reference not set to an instance of an object</Message>
    <Stack>  at john_deere_export.ADAPTFactory.GetPolygonList (GeoJSON.Net.Feature.FeatureCollection featureCollection, System.Boolean loadInner) [0x00028] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.ADAPTFactory.Generate (GeoJSON.Net.Feature.FeatureCollection featureCollection, john_deere_export.ExportParameter param) [0x00189] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.Program.ProccessExportData (john_deere_export.ExportParameter param) [0x00044] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 </Stack>
  </Exception>
</logEntry>
<logEntry>
  <Date>05/08/2019 11:19:30</Date>
  <Exception>
    <Source>ADAPTConverter</Source>
    <Message>Object reference not set to an instance of an object</Message>
    <Stack>  at john_deere_export.ADAPTFactory.GetPolygonList (GeoJSON.Net.Feature.FeatureCollection featureCollection, System.Boolean loadInner) [0x00028] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.ADAPTFactory.Generate (GeoJSON.Net.Feature.FeatureCollection featureCollection, john_deere_export.ExportParameter param) [0x00189] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.Program.ProccessExportData (john_deere_export.ExportParameter param) [0x00044] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 </Stack>
  </Exception>
</logEntry>
<logEntry>
  <Date>05/08/2019 11:27:09</Date>
  <Exception>
    <Source>ADAPTConverter</Source>
    <Message>Object reference not set to an instance of an object</Message>
    <Stack>  at john_deere_export.ADAPTFactory.GetPolygonList (GeoJSON.Net.Feature.FeatureCollection featureCollection, System.Boolean loadInner) [0x00028] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.ADAPTFactory.Generate (GeoJSON.Net.Feature.FeatureCollection featureCollection, john_deere_export.ExportParameter param) [0x00189] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.Program.ProccessExportData (john_deere_export.ExportParameter param) [0x00044] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 </Stack>
  </Exception>
</logEntry>
<logEntry>
  <Date>10/15/2019 11:19:19</Date>
  <Exception>
    <Source>ADAPTConverter</Source>
    <Message>Object reference not set to an instance of an object</Message>
    <Stack>  at john_deere_export.ADAPTFactory.GetPolygonList (GeoJSON.Net.Feature.FeatureCollection featureCollection, System.Boolean loadInner) [0x00028] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.ADAPTFactory.Generate (GeoJSON.Net.Feature.FeatureCollection featureCollection, john_deere_export.ExportParameter param) [0x00189] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.Program.ProccessExportData (john_deere_export.ExportParameter param) [0x00044] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 </Stack>
  </Exception>
</logEntry>
<logEntry>
  <Date>10/15/2019 11:20:45</Date>
  <Exception>
    <Source>ADAPTConverter</Source>
    <Message>Object reference not set to an instance of an object</Message>
    <Stack>  at john_deere_export.ADAPTFactory.GetPolygonList (GeoJSON.Net.Feature.FeatureCollection featureCollection, System.Boolean loadInner) [0x00028] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.ADAPTFactory.Generate (GeoJSON.Net.Feature.FeatureCollection featureCollection, john_deere_export.ExportParameter param) [0x00189] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.Program.ProccessExportData (john_deere_export.ExportParameter param) [0x00044] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 </Stack>
  </Exception>
</logEntry>
<logEntry>
  <Date>10/15/2019 11:23:33</Date>
  <Exception>
    <Source>ADAPTConverter</Source>
    <Message>Object reference not set to an instance of an object</Message>
    <Stack>  at john_deere_export.ADAPTFactory.GetPolygonList (GeoJSON.Net.Feature.FeatureCollection featureCollection, System.Boolean loadInner) [0x00028] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.ADAPTFactory.Generate (GeoJSON.Net.Feature.FeatureCollection featureCollection, john_deere_export.ExportParameter param) [0x00189] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.Program.ProccessExportData (john_deere_export.ExportParameter param) [0x00044] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 </Stack>
  </Exception>
</logEntry>
<logEntry>
  <Date>10/15/2019 11:25:42</Date>
  <Exception>
    <Source>ADAPTConverter</Source>
    <Message>Object reference not set to an instance of an object</Message>
    <Stack>  at john_deere_export.ADAPTFactory.GetPolygonList (GeoJSON.Net.Feature.FeatureCollection featureCollection, System.Boolean loadInner) [0x00028] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.ADAPTFactory.Generate (GeoJSON.Net.Feature.FeatureCollection featureCollection, john_deere_export.ExportParameter param) [0x00189] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.Program.ProccessExportData (john_deere_export.ExportParameter param) [0x00044] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 </Stack>
  </Exception>
</logEntry>
<logEntry>
  <Date>10/15/2019 11:44:49</Date>
  <Exception>
    <Source>ADAPTConverter</Source>
    <Message>Object reference not set to an instance of an object</Message>
    <Stack>  at john_deere_export.ADAPTFactory.GetPolygonList (GeoJSON.Net.Feature.FeatureCollection featureCollection, System.Boolean loadInner) [0x00028] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.ADAPTFactory.Generate (GeoJSON.Net.Feature.FeatureCollection featureCollection, john_deere_export.ExportParameter param) [0x00189] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.Program.ProccessExportData (john_deere_export.ExportParameter param) [0x00044] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 </Stack>
  </Exception>
</logEntry>
<logEntry>
  <Date>11/06/2019 18:36:10</Date>
  <Exception>
    <Source>ADAPTConverter</Source>
    <Message>Object reference not set to an instance of an object</Message>
    <Stack>  at john_deere_export.ADAPTFactory.GetPolygonList (GeoJSON.Net.Feature.FeatureCollection featureCollection, System.Boolean loadInner) [0x00028] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.ADAPTFactory.Generate (GeoJSON.Net.Feature.FeatureCollection featureCollection, john_deere_export.ExportParameter param) [0x00189] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.Program.ProccessExportData (john_deere_export.ExportParameter param) [0x00044] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 </Stack>
  </Exception>
</logEntry>
<logEntry>
  <Date>11/06/2019 18:36:41</Date>
  <Exception>
    <Source>ADAPTConverter</Source>
    <Message>Object reference not set to an instance of an object</Message>
    <Stack>  at john_deere_export.ADAPTFactory.GetPolygonList (GeoJSON.Net.Feature.FeatureCollection featureCollection, System.Boolean loadInner) [0x00028] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.ADAPTFactory.Generate (GeoJSON.Net.Feature.FeatureCollection featureCollection, john_deere_export.ExportParameter param) [0x00189] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.Program.ProccessExportData (john_deere_export.ExportParameter param) [0x00044] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 </Stack>
  </Exception>
</logEntry>
<logEntry>
  <Date>12/03/2019 13:03:35</Date>
  <Exception>
    <Source>ADAPTConverter</Source>
    <Message>Object reference not set to an instance of an object</Message>
    <Stack>  at john_deere_export.ADAPTFactory.GetPolygonList (GeoJSON.Net.Feature.FeatureCollection featureCollection, System.Boolean loadInner) [0x00028] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.ADAPTFactory.Generate (GeoJSON.Net.Feature.FeatureCollection featureCollection, john_deere_export.ExportParameter param) [0x00189] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.Program.ProccessExportData (john_deere_export.ExportParameter param) [0x00044] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 </Stack>
  </Exception>
</logEntry>
<logEntry>
  <Date>04/14/2020 10:35:26</Date>
  <Exception>
    <Source>ADAPTConverter</Source>
    <Message>Object reference not set to an instance of an object</Message>
    <Stack>  at john_deere_export.ADAPTFactory.GetPolygonList (GeoJSON.Net.Feature.FeatureCollection featureCollection, System.Boolean loadInner) [0x00028] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.ADAPTFactory.Generate (GeoJSON.Net.Feature.FeatureCollection featureCollection, john_deere_export.ExportParameter param) [0x00189] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.Program.ProccessExportData (john_deere_export.ExportParameter param) [0x00044] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 </Stack>
  </Exception>
</logEntry>
<logEntry>
  <Date>04/14/2020 10:36:02</Date>
  <Exception>
    <Source>ADAPTConverter</Source>
    <Message>Object reference not set to an instance of an object</Message>
    <Stack>  at john_deere_export.ADAPTFactory.GetPolygonList (GeoJSON.Net.Feature.FeatureCollection featureCollection, System.Boolean loadInner) [0x00028] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.ADAPTFactory.Generate (GeoJSON.Net.Feature.FeatureCollection featureCollection, john_deere_export.ExportParameter param) [0x00189] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.Program.ProccessExportData (john_deere_export.ExportParameter param) [0x00044] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 </Stack>
  </Exception>
</logEntry>
<logEntry>
  <Date>04/21/2020 15:07:02</Date>
  <Exception>
    <Source>ADAPTConverter</Source>
    <Message>Object reference not set to an instance of an object</Message>
    <Stack>  at john_deere_export.ADAPTFactory.GetPolygonList (GeoJSON.Net.Feature.FeatureCollection featureCollection, System.Boolean loadInner) [0x00028] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.ADAPTFactory.Generate (GeoJSON.Net.Feature.FeatureCollection featureCollection, john_deere_export.ExportParameter param) [0x00189] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.Program.ProccessExportData (john_deere_export.ExportParameter param) [0x00044] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 </Stack>
  </Exception>
</logEntry>
<logEntry>
  <Date>05/06/2021 16:28:17</Date>
  <Exception>
    <Source>mscorlib</Source>
    <Message>Could not find a part of the path '/home/<USER>/adapt_converter_212/Output/Trimble AgGPS/AgGPS/Data/zp_tgenov/Osnovno_stopanstvo/1132_17960_26_20'.</Message>
    <Stack>  at System.IO.__Error.WinIOError (System.Int32 errorCode, System.String maybeFullPath) [0x000f7] in &lt;8fbafb724c144c9dad69bccfec38ae40&gt;:0 
  at System.IO.FileSystemEnumerableIterator`1[TSource].HandleError (System.Int32 hr, System.String path) [0x00006] in &lt;8fbafb724c144c9dad69bccfec38ae40&gt;:0 
  at System.IO.FileSystemEnumerableIterator`1[TSource].CommonInit () [0x00054] in &lt;8fbafb724c144c9dad69bccfec38ae40&gt;:0 
  at System.IO.FileSystemEnumerableIterator`1[TSource]..ctor (System.String path, System.String originalUserPath, System.String searchPattern, System.IO.SearchOption searchOption, System.IO.SearchResultHandler`1[TSource] resultHandler, System.Boolean checkHost) [0x000d6] in &lt;8fbafb724c144c9dad69bccfec38ae40&gt;:0 
  at System.IO.FileSystemEnumerableFactory.CreateFileNameIterator (System.String path, System.String originalUserPath, System.String searchPattern, System.Boolean includeFiles, System.Boolean includeDirs, System.IO.SearchOption searchOption, System.Boolean checkHost) [0x00009] in &lt;8fbafb724c144c9dad69bccfec38ae40&gt;:0 
  at System.IO.Directory.EnumerateFileSystemNames (System.String path, System.String searchPattern, System.IO.SearchOption searchOption, System.Boolean includeFiles, System.Boolean includeDirs) [0x00000] in &lt;8fbafb724c144c9dad69bccfec38ae40&gt;:0 
  at System.IO.Directory.InternalEnumerateFileSystemEntries (System.String path, System.String searchPattern, System.IO.SearchOption searchOption) [0x00000] in &lt;8fbafb724c144c9dad69bccfec38ae40&gt;:0 
  at System.IO.Directory.EnumerateFileSystemEntries (System.String path) [0x0000e] in &lt;8fbafb724c144c9dad69bccfec38ae40&gt;:0 
  at System.IO.Compression.ZipFile.IsDirEmpty (System.IO.DirectoryInfo possiblyEmptyDir) [0x00006] in &lt;4343a68aeec24c1dbfcd73ac706cc779&gt;:0 
  at System.IO.Compression.ZipFile.DoCreateFromDirectory (System.String sourceDirectoryName, System.String destinationArchiveFileName, System.Nullable`1[T] compressionLevel, System.Boolean includeBaseDirectory, System.Text.Encoding entryNameEncoding) [0x000cc] in &lt;4343a68aeec24c1dbfcd73ac706cc779&gt;:0 
  at System.IO.Compression.ZipFile.CreateFromDirectory (System.String sourceDirectoryName, System.String destinationArchiveFileName) [0x00000] in &lt;4343a68aeec24c1dbfcd73ac706cc779&gt;:0 
  at john_deere_export.Program.ExportAndZip (AgGateway.ADAPT.ApplicationDataModel.ADM.ApplicationDataModel adm, john_deere_export.ExportParameter param, System.String fileName) [0x0012d] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.Program.ProccessExportData (john_deere_export.ExportParameter param) [0x00058] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 </Stack>
  </Exception>
</logEntry>
<logEntry>
  <Date>05/06/2021 16:28:25</Date>
  <Exception>
    <Source>mscorlib</Source>
    <Message>Directory /home/<USER>/adapt_converter_212/Output/Trimble AgGPS/AgGPS/Data/zp_tgenov/Osnovno_stopanstvo is not empty</Message>
    <Stack>  at System.IO.Directory.Delete (System.String path) [0x0006f] in &lt;8fbafb724c144c9dad69bccfec38ae40&gt;:0 
  at System.IO.Directory.RecursiveDelete (System.String path) [0x00051] in &lt;8fbafb724c144c9dad69bccfec38ae40&gt;:0 
  at System.IO.Directory.RecursiveDelete (System.String path) [0x00024] in &lt;8fbafb724c144c9dad69bccfec38ae40&gt;:0 
  at System.IO.Directory.RecursiveDelete (System.String path) [0x00024] in &lt;8fbafb724c144c9dad69bccfec38ae40&gt;:0 
  at System.IO.Directory.RecursiveDelete (System.String path) [0x00024] in &lt;8fbafb724c144c9dad69bccfec38ae40&gt;:0 
  at System.IO.Directory.RecursiveDelete (System.String path) [0x00024] in &lt;8fbafb724c144c9dad69bccfec38ae40&gt;:0 
  at System.IO.Directory.RecursiveDelete (System.String path) [0x00024] in &lt;8fbafb724c144c9dad69bccfec38ae40&gt;:0 
  at System.IO.Directory.Delete (System.String path, System.Boolean recursive) [0x00009] in &lt;8fbafb724c144c9dad69bccfec38ae40&gt;:0 
  at john_deere_export.Program.ExportAndZip (AgGateway.ADAPT.ApplicationDataModel.ADM.ApplicationDataModel adm, john_deere_export.ExportParameter param, System.String fileName) [0x0008f] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.Program.ProccessExportData (john_deere_export.ExportParameter param) [0x00058] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 </Stack>
  </Exception>
</logEntry>
<logEntry>
  <Date>5/26/2021 7:22:04 PM</Date>
  <Exception>
    <Source>mscorlib</Source>
    <Message>Could not find a part of the path "/var/www/techno/trunk/public/files/coverage_files/2139/888/converted/AgData_Field-529.json".</Message>
    <Stack>  at System.IO.FileStream..ctor (System.String path, System.IO.FileMode mode, System.IO.FileAccess access, System.IO.FileShare share, System.Int32 bufferSize, System.Boolean anonymous, System.IO.FileOptions options) [0x00164] in &lt;8fbafb724c144c9dad69bccfec38ae40&gt;:0 
  at System.IO.FileStream..ctor (System.String path, System.IO.FileMode mode, System.IO.FileAccess access, System.IO.FileShare share, System.Int32 bufferSize, System.IO.FileOptions options, System.String msgPath, System.Boolean bFromProxy, System.Boolean useLongPath, System.Boolean checkHost) [0x00000] in &lt;8fbafb724c144c9dad69bccfec38ae40&gt;:0 
  at (wrapper remoting-invoke-with-check) System.IO.FileStream..ctor(string,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare,int,System.IO.FileOptions,string,bool,bool,bool)
  at System.IO.StreamWriter.CreateFile (System.String path, System.Boolean append, System.Boolean checkHost) [0x0001c] in &lt;8fbafb724c144c9dad69bccfec38ae40&gt;:0 
  at System.IO.StreamWriter..ctor (System.String path, System.Boolean append, System.Text.Encoding encoding, System.Int32 bufferSize, System.Boolean checkHost) [0x00055] in &lt;8fbafb724c144c9dad69bccfec38ae40&gt;:0 
  at System.IO.StreamWriter..ctor (System.String path, System.Boolean append, System.Text.Encoding encoding, System.Int32 bufferSize) [0x00000] in &lt;8fbafb724c144c9dad69bccfec38ae40&gt;:0 
  at System.IO.StreamWriter..ctor (System.String path, System.Boolean append, System.Text.Encoding encoding) [0x00000] in &lt;8fbafb724c144c9dad69bccfec38ae40&gt;:0 
  at (wrapper remoting-invoke-with-check) System.IO.StreamWriter..ctor(string,bool,System.Text.Encoding)
  at System.IO.File.WriteAllText (System.String path, System.String contents, System.Text.Encoding encoding) [0x00000] in &lt;8fbafb724c144c9dad69bccfec38ae40&gt;:0 
  at System.IO.File.WriteAllText (System.String path, System.String contents) [0x00007] in &lt;8fbafb724c144c9dad69bccfec38ae40&gt;:0 
  at ADAPTConverter.ImportLogic.ImportToJSONFragmentedByCriteria (System.String output, System.String fileName) [0x00129] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.Program.ProcessImportData (john_deere_export.ImportParameter param) [0x000c7] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 </Stack>
  </Exception>
</logEntry>
<logEntry>
  <Date>06/30/2021 13:41:30</Date>
  <Exception>
    <Source>mscorlib</Source>
    <Message>Directory /home/<USER>/adapt_converter_212/Output/Trimble AgGPS/AgGPS/Data/m_marinov/Alfa is not empty</Message>
    <Stack>  at System.IO.Directory.Delete (System.String path) [0x0006f] in &lt;8fbafb724c144c9dad69bccfec38ae40&gt;:0 
  at System.IO.Directory.RecursiveDelete (System.String path) [0x00051] in &lt;8fbafb724c144c9dad69bccfec38ae40&gt;:0 
  at System.IO.Directory.RecursiveDelete (System.String path) [0x00024] in &lt;8fbafb724c144c9dad69bccfec38ae40&gt;:0 
  at System.IO.Directory.RecursiveDelete (System.String path) [0x00024] in &lt;8fbafb724c144c9dad69bccfec38ae40&gt;:0 
  at System.IO.Directory.RecursiveDelete (System.String path) [0x00024] in &lt;8fbafb724c144c9dad69bccfec38ae40&gt;:0 
  at System.IO.Directory.RecursiveDelete (System.String path) [0x00024] in &lt;8fbafb724c144c9dad69bccfec38ae40&gt;:0 
  at System.IO.Directory.Delete (System.String path, System.Boolean recursive) [0x00009] in &lt;8fbafb724c144c9dad69bccfec38ae40&gt;:0 
  at john_deere_export.Program.ExportAndZip (AgGateway.ADAPT.ApplicationDataModel.ADM.ApplicationDataModel adm, john_deere_export.ExportParameter param, System.String fileName) [0x00144] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.Program.ProccessExportData (john_deere_export.ExportParameter param) [0x00058] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 </Stack>
  </Exception>
</logEntry>
<logEntry>
  <Date>09/16/2021 15:47:32</Date>
  <Exception>
    <Source>ADAPTConverter</Source>
    <Message>Object reference not set to an instance of an object</Message>
    <Stack>  at john_deere_export.ADAPTFactory.GetPolygonList (GeoJSON.Net.Feature.FeatureCollection featureCollection, System.Boolean loadInner) [0x00028] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.ADAPTFactory.Generate (GeoJSON.Net.Feature.FeatureCollection featureCollection, john_deere_export.ExportParameter param) [0x00189] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.Program.ProccessExportData (john_deere_export.ExportParameter param) [0x00044] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 </Stack>
  </Exception>
</logEntry>
<logEntry>
  <Date>09/16/2021 15:47:38</Date>
  <Exception>
    <Source>ADAPTConverter</Source>
    <Message>Object reference not set to an instance of an object</Message>
    <Stack>  at john_deere_export.ADAPTFactory.GetPolygonList (GeoJSON.Net.Feature.FeatureCollection featureCollection, System.Boolean loadInner) [0x00028] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.ADAPTFactory.Generate (GeoJSON.Net.Feature.FeatureCollection featureCollection, john_deere_export.ExportParameter param) [0x00189] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.Program.ProccessExportData (john_deere_export.ExportParameter param) [0x00044] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 </Stack>
  </Exception>
</logEntry>
<logEntry>
  <Date>09/16/2021 15:48:01</Date>
  <Exception>
    <Source>ADAPTConverter</Source>
    <Message>Object reference not set to an instance of an object</Message>
    <Stack>  at john_deere_export.ADAPTFactory.GetPolygonList (GeoJSON.Net.Feature.FeatureCollection featureCollection, System.Boolean loadInner) [0x00028] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.ADAPTFactory.Generate (GeoJSON.Net.Feature.FeatureCollection featureCollection, john_deere_export.ExportParameter param) [0x00189] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.Program.ProccessExportData (john_deere_export.ExportParameter param) [0x00044] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 </Stack>
  </Exception>
</logEntry>
<logEntry>
  <Date>09/16/2021 15:48:47</Date>
  <Exception>
    <Source>ADAPTConverter</Source>
    <Message>Object reference not set to an instance of an object</Message>
    <Stack>  at john_deere_export.ADAPTFactory.GetPolygonList (GeoJSON.Net.Feature.FeatureCollection featureCollection, System.Boolean loadInner) [0x00028] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.ADAPTFactory.Generate (GeoJSON.Net.Feature.FeatureCollection featureCollection, john_deere_export.ExportParameter param) [0x00189] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.Program.ProccessExportData (john_deere_export.ExportParameter param) [0x00044] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 </Stack>
  </Exception>
</logEntry>
<logEntry>
  <Date>09/16/2021 16:15:06</Date>
  <Exception>
    <Source>ADAPTConverter</Source>
    <Message>Object reference not set to an instance of an object</Message>
    <Stack>  at john_deere_export.ADAPTFactory.GetPolygonList (GeoJSON.Net.Feature.FeatureCollection featureCollection, System.Boolean loadInner) [0x00028] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.ADAPTFactory.Generate (GeoJSON.Net.Feature.FeatureCollection featureCollection, john_deere_export.ExportParameter param) [0x00189] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.Program.ProccessExportData (john_deere_export.ExportParameter param) [0x00044] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 </Stack>
  </Exception>
</logEntry>
<logEntry>
  <Date>09/16/2021 16:15:11</Date>
  <Exception>
    <Source>ADAPTConverter</Source>
    <Message>Object reference not set to an instance of an object</Message>
    <Stack>  at john_deere_export.ADAPTFactory.GetPolygonList (GeoJSON.Net.Feature.FeatureCollection featureCollection, System.Boolean loadInner) [0x00028] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.ADAPTFactory.Generate (GeoJSON.Net.Feature.FeatureCollection featureCollection, john_deere_export.ExportParameter param) [0x00189] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.Program.ProccessExportData (john_deere_export.ExportParameter param) [0x00044] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 </Stack>
  </Exception>
</logEntry>
<logEntry>
  <Date>09/16/2021 16:22:28</Date>
  <Exception>
    <Source>ADAPTConverter</Source>
    <Message>Object reference not set to an instance of an object</Message>
    <Stack>  at john_deere_export.ADAPTFactory.GetPolygonList (GeoJSON.Net.Feature.FeatureCollection featureCollection, System.Boolean loadInner) [0x00028] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.ADAPTFactory.Generate (GeoJSON.Net.Feature.FeatureCollection featureCollection, john_deere_export.ExportParameter param) [0x00189] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 
  at john_deere_export.Program.ProccessExportData (john_deere_export.ExportParameter param) [0x00044] in &lt;360791bed08f477a9dcbb38253177ee5&gt;:0 </Stack>
  </Exception>
</logEntry>
