[servers]
migrator    ansible_user=root   ansible_host=************ ansible_port=22 ansible_ssh_transfer_method=scp
staging ansible_user=root   ansible_host=************ ansible_port=22 ansible_ssh_transfer_method=scp
atridi  ansible_user=root   ansible_host=************* ansible_ssh_user=root ansible_port=22222 ansible_ssh_transfer_method=scp ansible_python_interpreter=/usr/bin/python3
pavlovi ansible_user=root   ansible_host=************** ansible_port=2221 ansible_ssh_transfer_method=scp ansible_python_interpreter=/usr/bin/python3
kronos  ansible_user=root   ansible_host=************* ansible_port=4022 ansible_ssh_transfer_method=scp ansible_python_interpreter=/usr/bin/python3
karadzha    ansible_user=root   ansible_host=*************** ansible_port=22 ansible_ssh_transfer_method=scp ansible_python_interpreter=/usr/bin/python3
ilchovski   ansible_user=root   ansible_host=************* ansible_port=9022 ansible_ssh_transfer_method=scp ansible_python_interpreter=/usr/bin/python3
fpi ansible_user=root   ansible_host=************* ansible_port=2222 ansible_ssh_transfer_method=scp ansible_python_interpreter=/usr/bin/python3
bim ansible_user=root   ansible_host=bim.technofarm.bg ansible_port=222 ansible_ssh_transfer_method=scp ansible_python_interpreter=/usr/bin/python3
agrobel ansible_user=root   ansible_host=************* ansible_port=22 ansible_ssh_transfer_method=scp ansible_python_interpreter=/usr/bin/python3
kamenshishkov ansible_user=root   ansible_host=************ ansible_port=22 ansible_ssh_transfer_method=scp ansible_python_interpreter=/usr/bin/python3