<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Mathematical Precision Settings
    |--------------------------------------------------------------------------
    |
    | These settings control the precision for various mathematical operations
    | in the payroll system, matching the legacy system's precision requirements.
    |
    */
    'precision' => [
        'area' => env('MATH_PRECISION_AREA', 3),
        'money' => env('MATH_PRECISION_MONEY', 2),
        'rent_nat' => env('MATH_PRECISION_RENT_NAT', 3),
        'infinity' => env('MATH_PRECISION_INFINITY', 16),
        'fix_money_rounding_value' => env('MATH_FIX_MONEY_ROUNDING_VALUE', 0.1),
    ],

    /*
    |--------------------------------------------------------------------------
    | Payroll System Settings
    |--------------------------------------------------------------------------
    |
    | Configuration settings specific to the payroll calculation system.
    |
    */
    'payroll' => [
        'default_page_size' => 50,
        'max_page_size' => 1000,
        'farming_year_start_month' => 10, // October
        'farming_year_start_day' => 1,
        'farming_year_end_month' => 9, // September
        'farming_year_end_day' => 30,
    ],

    /*
    |--------------------------------------------------------------------------
    | Currency Settings
    |--------------------------------------------------------------------------
    |
    | Settings for currency conversion and display.
    |
    */
    'currency' => [
        'default' => 'BGN',
        'supported' => ['BGN', 'EUR'],
        'conversion_rates' => [
            'BGN_TO_EUR' => 0.511292, // Fixed rate as per EU regulations
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Rent Types Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for different types of rent calculations.
    |
    */
    'rent_types' => [
        'money' => 'money',
        'natural' => 'natural',
        'mixed' => 'mixed',
    ],
];
