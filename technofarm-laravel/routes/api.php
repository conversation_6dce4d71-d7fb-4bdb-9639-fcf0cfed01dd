<?php

use App\Http\Controllers\PayrollController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

/*
|--------------------------------------------------------------------------
| Payroll API Routes
|--------------------------------------------------------------------------
|
| Routes for the payroll system functionality, migrated from the legacy
| PaymentsController.
|
*/

Route::prefix('payroll')->group(function () {
    // Get owner payroll data - main endpoint migrated from getOwnerPayroll()
    Route::get('/owner/{ownerId}', [PayrollController::class, 'getOwnerPayroll'])
        ->name('payroll.owner');
    
    // Get owners payroll with pagination - migrated from getOwnersPayroll()
    Route::get('/owners', [PayrollController::class, 'getOwnersPayroll'])
        ->name('payroll.owners');
    
    // Get owner payments - migrated from getOwnerPayments()
    Route::get('/owner/{ownerId}/payments', [PayrollController::class, 'getOwnerPayments'])
        ->name('payroll.owner.payments');
    
    // Get contract payments - migrated from getContractPayments()
    Route::get('/contract/{contractId}/payments', [PayrollController::class, 'getContractPayments'])
        ->name('payroll.contract.payments');
    
    // Get contract payments with annex
    Route::get('/contract/{contractId}/annex/{annexId}/payments', [PayrollController::class, 'getContractPayments'])
        ->name('payroll.contract.annex.payments');
});
