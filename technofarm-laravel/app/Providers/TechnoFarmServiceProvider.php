<?php

namespace App\Providers;

use App\Services\AggregationService;
use App\Services\MathService;
use App\Services\PaymentService;
use App\Services\PayrollService;
use App\Services\PersonalUseService;
use App\Services\PlotService;
use Illuminate\Support\ServiceProvider;

class TechnoFarmServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register MathService as singleton to maintain precision settings
        $this->app->singleton(MathService::class, function ($app) {
            return new MathService(
                config('technofarm.precision.area'),
                config('technofarm.precision.money'),
                config('technofarm.precision.rent_nat'),
                config('technofarm.precision.infinity'),
                config('technofarm.precision.fix_money_rounding_value')
            );
        });

        // Register other services
        $this->app->bind(PlotService::class);
        $this->app->bind(PersonalUseService::class);
        $this->app->bind(PaymentService::class);
        $this->app->bind(AggregationService::class);
        
        // PayrollService depends on other services
        $this->app->bind(PayrollService::class, function ($app) {
            return new PayrollService(
                $app->make(MathService::class),
                $app->make(PlotService::class),
                $app->make(PersonalUseService::class),
                $app->make(PaymentService::class),
                $app->make(AggregationService::class)
            );
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Publish configuration
        $this->publishes([
            __DIR__.'/../../config/technofarm.php' => config_path('technofarm.php'),
        ], 'technofarm-config');
    }
}
