<?php

namespace App\Services;

use App\Models\Payment;
use App\Models\Owner;
use App\Models\Contract;

/**
 * Service for payment processing and calculations.
 * Handles payment data retrieval and processing logic.
 */
class PaymentService
{
    public function __construct(
        private MathService $mathService,
        private PlotService $plotService,
        private PersonalUseService $personalUseService,
        private AggregationService $aggregationService
    ) {}

    /**
     * Get payments for a contract with full calculations.
     * Migrated from PaymentsController::getPayments().
     */
    public function getPayments(
        int $year,
        ?int $contractId = null,
        ?int $annexId = null,
        ?string $gridType = null,
        array $filterParams = []
    ): array {
        // Step 1: Get all plots for the contract with their rents
        $plots = $this->plotService->getPaymentsPlots($year, $contractId, $annexId, null, null, $filterParams);

        // Step 2: Aggregate data by owner
        $aggPlots = $this->aggregationService->aggPlots($plots);

        // Step 3: Process personal use data
        $aggPlots = $this->personalUseService->processOwnerPersonalUse($aggPlots, $gridType);

        // Step 4: Process owner payments
        $contractAnnexId = [$contractId];
        if (!empty($annexId)) {
            $contractAnnexId[] = $annexId;
        }
        $aggPlots = $this->processOwnerPayments($year, $aggPlots, $contractAnnexId);

        // Step 5: Fix rounding errors
        $aggPlots = $this->fixRounding($aggPlots);

        // Step 6: Filter children based on parent's dead status
        $aggPlots = $this->filterChildrenByParentDeadStatus($aggPlots);

        // Step 7: Build owners tree
        $ownersTree = $this->buildOwnersTree($aggPlots);

        // Step 8: Calculate tree rents (parent-child relationships)
        $this->calculateTreeRents($ownersTree);

        return $ownersTree;
    }

    /**
     * Process owner payments and calculate remaining rents.
     */
    public function processOwnerPayments(
        int $year,
        array $owners,
        ?array $contractAnnexIds = null,
        ?int $ownerId = null,
        string $aggKey = 'owner_path_key'
    ): array {
        // Get payments for owners
        $paidResults = $this->getContractOwnerPayments($year, $contractAnnexIds, $ownerId);

        foreach ($owners as $ownerKey => $owner) {
            $ownerPayments = $this->findOwnerPayments($paidResults, $owner, $aggKey);
            
            if ($ownerPayments) {
                $owners[$ownerKey] = $this->applyPaymentsToOwner($owners[$ownerKey], $ownerPayments);
            }
        }

        return $owners;
    }

    /**
     * Get contract owner payments.
     */
    public function getContractOwnerPayments(int $year, ?array $contractAnnexIds = null, ?int $ownerId = null): array
    {
        $query = Payment::query()
            ->with(['owner', 'contract', 'rentType', 'paidByOwner'])
            ->where('year', $year);

        if ($contractAnnexIds) {
            $query->whereIn('contract_id', $contractAnnexIds);
        }

        if ($ownerId) {
            $query->where('owner_id', $ownerId);
        }

        return $query->get()->toArray();
    }

    /**
     * Fix rounding errors in payments.
     * Migrated from PaymentsController::fixRounding().
     */
    public function fixRounding(array $payments): array
    {
        $fixMoneyRoundingValue = $this->mathService->getFixMoneyRoundingValue();

        foreach ($payments as $paymentKey => $payment) {
            $chargedRenta = $payment['charged_renta'] ?? 0;
            $renta = $payment['renta'] ?? 0;
            $paidRenta = $payment['paid_renta'] ?? 0;
            
            $sumRenta = $this->mathService->addMoney($chargedRenta, $renta);
            $rentCal = $this->mathService->subMoney($sumRenta, $paidRenta);

            if ($rentCal > 0 && $rentCal <= $fixMoneyRoundingValue) {
                if ($chargedRenta > 0) {
                    $payments[$paymentKey]['charged_renta'] = $this->mathService->subMoney($chargedRenta, $rentCal);
                } else {
                    $payments[$paymentKey]['renta'] = $this->mathService->subMoney($renta, $rentCal);
                }
                $payments[$paymentKey]['unpaid_renta'] = $this->mathService->roundMoney(0);
            }

            if ($rentCal < 0 && $rentCal >= ($fixMoneyRoundingValue * -1)) {
                if ($chargedRenta > 0) {
                    $payments[$paymentKey]['charged_renta'] = $this->mathService->subMoney($chargedRenta, $rentCal);
                } else {
                    $payments[$paymentKey]['renta'] = $this->mathService->subMoney($renta, $rentCal);
                }
                $payments[$paymentKey]['unpaid_renta'] = $this->mathService->roundMoney(0);
            }
        }

        return $payments;
    }

    /**
     * Filter children based on parent's dead status and farm year logic.
     */
    private function filterChildrenByParentDeadStatus(array $aggPlots): array
    {
        // Implementation would filter out children based on parent death status
        // This is a complex business logic that needs to be implemented based on requirements
        return $aggPlots;
    }

    /**
     * Build owners tree structure.
     */
    private function buildOwnersTree(array $aggPlots): array
    {
        // Implementation would build hierarchical tree structure of owners and heirs
        // This involves recursive processing of owner relationships
        return $aggPlots;
    }

    /**
     * Calculate tree rents for parent-child relationships.
     */
    private function calculateTreeRents(array &$ownersTree): void
    {
        // Implementation would calculate rent distributions between parents and children
        // This involves complex inheritance calculations
        foreach ($ownersTree as &$owner) {
            $this->calculateChildrenRentsBasedOnParentPayments($owner);
            $this->calculateParentRentsBasedOnChildrenPayments($owner);
        }
    }

    /**
     * Calculate children rents based on parent payments.
     */
    private function calculateChildrenRentsBasedOnParentPayments(array &$owner): void
    {
        // Implementation for calculating children rents when parent has payments
        if (isset($owner['children']) && isset($owner['paid_renta']) && $owner['paid_renta'] > 0) {
            // Distribute parent payments to children based on their ownership percentages
            foreach ($owner['children'] as &$child) {
                $childPercent = $child['plots_percent'] ?? 0;
                $parentPayment = $this->mathService->mulMoney($owner['paid_renta'], $childPercent / 100);
                
                $child['paid_renta'] = $this->mathService->addMoney($child['paid_renta'] ?? 0, $parentPayment);
                $child['unpaid_renta'] = $this->mathService->subMoney($child['unpaid_renta'] ?? 0, $parentPayment);
            }
        }
    }

    /**
     * Calculate parent rents based on children payments.
     */
    private function calculateParentRentsBasedOnChildrenPayments(array &$owner): void
    {
        // Implementation for calculating parent rents when children have payments
        if (isset($owner['children'])) {
            $totalChildrenPayments = 0;
            
            foreach ($owner['children'] as $child) {
                $totalChildrenPayments = $this->mathService->addMoney(
                    $totalChildrenPayments,
                    $child['paid_renta'] ?? 0
                );
            }
            
            if ($totalChildrenPayments > 0) {
                $owner['paid_renta'] = $this->mathService->addMoney($owner['paid_renta'] ?? 0, $totalChildrenPayments);
                $owner['unpaid_renta'] = $this->mathService->subMoney($owner['unpaid_renta'] ?? 0, $totalChildrenPayments);
            }
        }
    }

    /**
     * Find owner payments in the paid results.
     */
    private function findOwnerPayments(array $paidResults, array $owner, string $aggKey): ?array
    {
        foreach ($paidResults as $payment) {
            if ($payment['owner_id'] == $owner['owner_id']) {
                return $payment;
            }
        }
        
        return null;
    }

    /**
     * Apply payments to owner data.
     */
    private function applyPaymentsToOwner(array $owner, array $payments): array
    {
        $owner['paid_renta'] = $this->mathService->addMoney($owner['paid_renta'] ?? 0, $payments['amount'] ?? 0);
        $owner['unpaid_renta'] = $this->mathService->subMoney($owner['unpaid_renta'] ?? 0, $payments['amount'] ?? 0);
        
        // Handle overpayments
        if ($owner['unpaid_renta'] < 0) {
            $owner['overpaid_renta'] = $this->mathService->abs($owner['unpaid_renta']);
            $owner['unpaid_renta'] = $this->mathService->roundMoney(0);
        }

        return $owner;
    }
}
