<?php

namespace App\Services;

use App\Models\PersonalUse;
use App\Models\Owner;

/**
 * Service for personal use calculations and processing.
 * Handles personal use area calculations and data processing.
 */
class PersonalUseService
{
    public function __construct(
        private MathService $mathService
    ) {}

    /**
     * Get personal use data for owners based on criteria.
     */
    public function getPersonalUseForOwners(array $criteria, bool $includeInactive = false, bool $includeDeleted = false): array
    {
        $query = PersonalUse::query()
            ->with(['owner', 'plot', 'contract', 'rentType']);

        if (isset($criteria['contract_id'])) {
            $query->where('contract_id', $criteria['contract_id']);
        }

        if (isset($criteria['year'])) {
            $query->where('year', $criteria['year']);
        }

        if (isset($criteria['chosen_years'])) {
            $query->where('year', $criteria['chosen_years']);
        }

        return $query->get()->toArray();
    }

    /**
     * Process owner personal use data.
     * Migrated from PaymentsController::processOwnerPersonalUse().
     */
    public function processOwnerPersonalUse(array $owners, ?string $gridType = null): array
    {
        $partCoef = 1;

        foreach ($owners as $ownerKey => $owner) {
            if (in_array($gridType, ['contract_payments'])) {
                $partCoef = $this->mathService->div(
                    $owner['plot_owned_area'] ?? 0,
                    $owner['plot_owned_area_total'] ?? 1
                );
            }

            // Initialize personal use arrays
            $owners[$ownerKey]['personal_use'] = [];
            $owners[$ownerKey]['personal_use_nat_type_id'] = [];
            $owners[$ownerKey]['personal_use_nat_types_names_arr'] = [];
            $owners[$ownerKey]['personal_use_amount_arr'] = [];
            $owners[$ownerKey]['personal_use_price_sum'] = [];
            $owners[$ownerKey]['personal_use_paid_arr'] = [];
            $owners[$ownerKey]['personal_use_unpaid_arr'] = [];
            $owners[$ownerKey]['personal_use_total'] = [];

            // Get personal use data for this owner
            $personalUseData = $this->getPersonalUseForOwner($owner['owner_id']);

            foreach ($personalUseData as $personalUseValue) {
                $owners[$ownerKey]['personal_use_nat_type_id'][] = $personalUseValue['rent_type_id'];
                $owners[$ownerKey]['personal_use_unit_value'][] = $personalUseValue['personal_use_unit_value'];
                $owners[$ownerKey]['personal_use_nat_types_names_arr'][] = $personalUseValue['rent_type_name'];
                
                $owners[$ownerKey]['personal_use_renta_arr'][] = $this->mathService->mulMoney(
                    $personalUseValue['personal_use_renta'],
                    $partCoef
                );
                
                $owners[$ownerKey]['personal_use_paid_renta_arr'][] = $this->mathService->mulMoney(
                    $personalUseValue['personal_use_paid_renta'],
                    $partCoef
                );
                
                $owners[$ownerKey]['personal_use_unpaid_renta_arr'][] = $this->mathService->mulMoney(
                    $personalUseValue['personal_use_unpaid_renta'],
                    $partCoef
                );
                
                $owners[$ownerKey]['personal_use_treatments_sum_arr'][] = $this->mathService->mulMoney(
                    $personalUseValue['personal_use_treatments_sum'],
                    $partCoef
                );
                
                $owners[$ownerKey]['personal_use_paid_treatments_arr'][] = $this->mathService->mulMoney(
                    $personalUseValue['personal_use_paid_treatments'],
                    $partCoef
                );
                
                $owners[$ownerKey]['personal_use_unpaid_treatments_arr'][] = $this->mathService->mulMoney(
                    $personalUseValue['personal_use_unpaid_treatments'],
                    $partCoef
                );
            }
        }

        return $owners;
    }

    /**
     * Get personal use area for a specific owner and plot.
     */
    public function getPersonalUseArea(array $personalUse, ?int $ownerId, array $plotData): string
    {
        if (!$ownerId) {
            return $this->mathService->roundArea(0);
        }

        $totalPersonalUseArea = 0;

        foreach ($personalUse as $personalUseRecord) {
            if ($personalUseRecord['owner_id'] === $ownerId) {
                $totalPersonalUseArea = $this->mathService->addArea(
                    $totalPersonalUseArea,
                    $personalUseRecord['personal_use_area'] ?? 0
                );
            }
        }

        return $this->mathService->roundArea($totalPersonalUseArea);
    }

    /**
     * Get personal use data for a specific owner.
     */
    private function getPersonalUseForOwner(int $ownerId): array
    {
        return PersonalUse::where('owner_id', $ownerId)
            ->with(['rentType'])
            ->get()
            ->map(function ($personalUse) {
                return [
                    'owner_id' => $personalUse->owner_id,
                    'rent_type_id' => $personalUse->rent_type_id,
                    'rent_type_name' => $personalUse->rentType->name ?? '',
                    'personal_use_unit_value' => $personalUse->personal_use_unit_value,
                    'personal_use_renta' => $personalUse->personal_use_renta,
                    'personal_use_paid_renta' => $personalUse->personal_use_paid_renta,
                    'personal_use_unpaid_renta' => $personalUse->personal_use_unpaid_renta,
                    'personal_use_treatments_sum' => $personalUse->personal_use_treatments_sum,
                    'personal_use_paid_treatments' => $personalUse->personal_use_paid_treatments,
                    'personal_use_unpaid_treatments' => $personalUse->personal_use_unpaid_treatments,
                    'personal_use_area' => $personalUse->personal_use_area,
                ];
            })
            ->toArray();
    }

    /**
     * Process personal use for deceased owners.
     * Handles inheritance and distribution of personal use among heirs.
     */
    public function processDeceasedOwnerPersonalUse(array $plots, int $year): array
    {
        $personalUseFromParent = [];

        foreach ($plots as $plotKey => $plot) {
            if ($plot['is_dead']) {
                $deadDate = $plot['dead_date'] ?? '2023-01-01';
                $deadFarmingYear = $this->getFarmingYearFromDate($deadDate);

                if ($deadFarmingYear['id'] == $year) {
                    // Owner died in current farming year - distribute personal use to heirs
                    $plots[$plotKey]['pu_area'] = $this->getPersonalUseArea(
                        $this->getPersonalUseForOwner($plot['owner_id']),
                        $plot['owner_id'],
                        $plot
                    );

                    // Distribute to heirs
                    foreach ($plots as $herPlotKey => $herPlotData) {
                        if ($herPlotData['path']) {
                            $path = explode('.', $herPlotData['path']);
                            if (in_array($plot['owner_id'], $path) && $herPlotData['pc_rel_id'] == $plot['pc_rel_id']) {
                                $herPercent = $this->mathService->div($herPlotData['plots_percent'], 100);
                                $personalUseFromParent[] = $herPlotData['owner_path_key'];
                                $plots[$herPlotKey]['pu_area'] = $this->mathService->mulArea(
                                    $plots[$plotKey]['pu_area'],
                                    $herPercent
                                );
                            }
                        }
                    }
                } else {
                    // Owner died in previous years - aggregate heirs' personal use
                    foreach ($plots as $PUplot) {
                        if ($PUplot['path']) {
                            $path = explode('.', $PUplot['path']);
                            if (in_array($plot['owner_id'], $path) && $PUplot['pc_rel_id'] == $plot['pc_rel_id']) {
                                $heritorPuArea = $this->getPersonalUseArea(
                                    $this->getPersonalUseForOwner($PUplot['owner_id']),
                                    $PUplot['owner_id'],
                                    $PUplot
                                );
                                if ($heritorPuArea) {
                                    $plots[$plotKey]['pu_area'] = $this->mathService->addArea(
                                        $plots[$plotKey]['pu_area'],
                                        $heritorPuArea
                                    );
                                }
                            }
                        }
                    }
                }
            } else {
                // Living owner - calculate personal use if not inherited from parent
                if (!in_array($plot['owner_path_key'], $personalUseFromParent)) {
                    $plots[$plotKey]['pu_area'] = $this->getPersonalUseArea(
                        $this->getPersonalUseForOwner($plot['owner_id']),
                        $plot['owner_id'],
                        $plot
                    );
                }
            }
        }

        return $plots;
    }

    /**
     * Get farming year from date.
     * Helper method to determine farming year based on date.
     */
    private function getFarmingYearFromDate(string $date): array
    {
        $dateObj = new \DateTime($date);
        $year = (int) $dateObj->format('Y');
        $month = (int) $dateObj->format('n');

        // Farming year starts in October
        if ($month >= 10) {
            $farmingYear = $year + 1;
        } else {
            $farmingYear = $year;
        }

        return [
            'id' => $farmingYear,
            'year' => $farmingYear,
            'start_date' => ($farmingYear - 1) . '-10-01',
            'end_date' => $farmingYear . '-09-30',
        ];
    }
}
