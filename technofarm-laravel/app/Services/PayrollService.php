<?php

namespace App\Services;

use App\Models\Owner;
use App\Models\Contract;
use Illuminate\Support\Collection;

/**
 * Main service for payroll calculations.
 * Orchestrates the getOwnerPayroll functionality from the legacy system.
 */
class PayrollService
{
    public function __construct(
        private MathService $mathService,
        private PlotService $plotService,
        private PersonalUseService $personalUseService,
        private PaymentService $paymentService,
        private AggregationService $aggregationService
    ) {}

    /**
     * Get owner payroll data - main method migrated from getOwnerPayroll().
     */
    public function getOwnerPayroll(
        int $year,
        ?int $ownerId = null,
        ?string $path = null,
        array $filterParams = []
    ): array {
        // Step 1: Get all plots for the specified criteria with their rents
        $plots = $this->plotService->getPaymentsPlots($year, null, null, $ownerId, $path, $filterParams);

        // Step 2: Process personal use data
        $plots = $this->personalUseService->processOwnerPersonalUse($plots);

        // Step 3: Aggregate plots by rent type and contract
        $aggPlots = $this->aggregationService->aggPlots($plots, 'rent_type');
        $aggPlotsByContract = $this->aggregationService->aggPlots($plots, 'contract');

        // Step 4: Map contract payments if not disabled
        if (!isset($filterParams['no_contract_payments'])) {
            $aggPlots = $this->contractsPaymentsMapping($aggPlots, $aggPlotsByContract, $year, $ownerId, $path);
        }

        // Step 5: Format data for output
        return $this->formatOwnersData($aggPlots);
    }

    /**
     * Get owners payroll with pagination - migrated from getOwnersPayroll().
     */
    public function getOwnersPayroll(
        int $year,
        array $filterParams = [],
        ?int $rows = null,
        ?int $page = null,
        bool $returnFlatOwners = false
    ): array {
        $result = [];

        // Add filter by allowed farmings (this would come from user permissions)
        $allowedFarmingIds = $this->getAllowedFarmingIds();

        if (empty($allowedFarmingIds)) {
            throw new \Exception('User has no allowed farmings');
        }

        $filterParams['payroll_farming'] = !empty($filterParams['payroll_farming']) && '' != $filterParams['payroll_farming'][0]
            ? array_intersect($filterParams['payroll_farming'], $allowedFarmingIds)
            : $allowedFarmingIds;

        // Get all owner contracts based on filter parameters
        $ownerContracts = $this->getOwnerContracts($year, $filterParams);

        // Extract all owner IDs for pagination
        $allOwnerIdsString = implode(',', array_column($ownerContracts, 'owner_ids'));
        $allOwnerIdsArray = array_unique(explode(',', $allOwnerIdsString));

        // Apply pagination to owners
        if ($page && $rows) {
            $start = ($page - 1) * $rows;
            $limitedOwners = array_slice($allOwnerIdsArray, $start, $rows);

            // Filter contracts that contain owners from the current page
            $filteredContracts = array_filter($ownerContracts, function ($item) use ($limitedOwners) {
                $itemOwnerIds = explode(',', $item['owner_ids']);
                return count(array_intersect($itemOwnerIds, $limitedOwners)) > 0;
            });
        } else {
            $filteredContracts = $ownerContracts;
        }

        $flatOwners = [];

        // Process each contract
        foreach ($filteredContracts as $contract) {
            $contractId = $contract['contract_id'];
            $contract['parent_ids'] = explode(',', $contract['parent_ids']) ?? null;
            $contract['owner_ids'] = explode(',', $contract['owner_ids']) ?? null;
            $annexId = null;

            if ($contract['parent_id']) {
                $contractId = $contract['parent_id'];
                $annexId = $contract['contract_id'];
            }

            // Build owner tree for the contract with all calculations
            $contractsPayments = $this->paymentService->getPayments($year, $contractId, $annexId, null, $filterParams);

            // Filter owners and heirs by owner_ids of the respective contract
            $filterParams['owner_ids'] = $contract['owner_ids'];
            $filteredOwners = $this->filterOwnersTree($contractsPayments, $filterParams);

            // Transform calculated data into a two-dimensional array
            $flatOwners[$contractId] = $this->flattenOwnersTree($filteredOwners);
        }

        // Return flat owners if requested
        if ($returnFlatOwners) {
            return [
                'rows' => $this->formatOwnersData($flatOwners),
                'total' => count($flatOwners),
                'footer' => [],
            ];
        }

        // Aggregate data for all owners and their heirs
        foreach ($flatOwners as $contractId => $contractOwners) {
            foreach ($contractOwners as $ownerPath => $owner) {
                $result[$ownerPath] = $this->calculateOwnerData($result[$ownerPath] ?? [], $owner);
            }
        }

        // Build owners tree again for display
        $ownersTree = $this->buildOwnersTree($result);

        return [
            'rows' => $this->formatOwnersData($ownersTree),
            'total' => count($allOwnerIdsArray),
            'footer' => [$this->generateFooter($ownersTree, ['timespan'])],
        ];
    }

    /**
     * Get owner payments - migrated from getOwnerPayments().
     */
    public function getOwnerPayments(int $year, int $ownerId): array
    {
        if (empty($ownerId)) {
            return [
                'rows' => [],
                'total' => 0,
                'footer' => [],
            ];
        }

        $ownersPayments = [];
        $allowedFarmingIds = $this->getAllowedFarmingIds();

        if (empty($allowedFarmingIds)) {
            throw new \Exception('User has no allowed farmings');
        }

        // Get all owner contracts for the specified year
        $ownerContracts = $this->getOwnerContracts($year, [
            'owner_id' => $ownerId,
            'payroll_farming' => $allowedFarmingIds
        ]);

        foreach ($ownerContracts as $contract) {
            $contractId = $contract['contract_id'];
            $annexId = null;
            
            if ($contract['parent_id']) {
                $contractId = $contract['parent_id'];
                $annexId = $contract['contract_id'];
            }

            $contractsPayments = $this->paymentService->getPayments($year, $contractId, $annexId);

            if ($contractsPayments) {
                $ownersPayments[] = $this->sumOwnerDataInContract($contractsPayments, $ownerId);
            }
        }

        return [
            'rows' => $this->formatOwnersData($ownersPayments),
            'total' => count($ownersPayments),
            'footer' => [$this->generateFooter($ownersPayments, ['timespan'])],
        ];
    }

    /**
     * Get contract payments - migrated from getContractPayments().
     */
    public function getContractPayments(
        int $year,
        ?int $contractId = null,
        ?int $annexId = null,
        ?int $page = null,
        ?int $rows = null,
        ?string $sort = null,
        ?string $order = null
    ): array {
        $payments = $this->paymentService->getPayments($year, $contractId, $annexId, 'contract_payments');

        return [
            'rows' => $this->formatOwnersData($payments),
            'total' => count($payments),
            'footer' => [$this->generateFooter($payments)],
        ];
    }

    /**
     * Map contract payments to aggregated plots.
     */
    private function contractsPaymentsMapping(
        array $aggPlots,
        array $aggPlotsByContract,
        int $year,
        ?int $ownerId = null,
        ?string $path = null
    ): array {
        foreach ($aggPlotsByContract as $contract) {
            $contractId = $contract['contract_id'];
            $annexId = null;
            $ownerPayment = null;
            
            if ($contract['parent_id']) {
                $contractId = $contract['parent_id'];
                $annexId = $contract['contract_id'];
            }

            $contractPayments = $this->paymentService->getPayments($year, $contractId, $annexId);
            if ($contractPayments) {
                $ownerPayment = $this->getOwnerDataFromContract($contractPayments, $path ?? $ownerId);
            }
            
            foreach ($aggPlots as $plotKey => $plot) {
                if ($plot['contract_id'] == $contract['contract_id'] && $plot['parent_id'] == $contract['parent_id']) {
                    $aggPlots[$plotKey] = array_merge($aggPlots[$plotKey], [
                        'paid_renta' => $ownerPayment['paid_renta'] ?? 0,
                        'unpaid_renta' => $ownerPayment['unpaid_renta'] ?? 0,
                        'overpaid_renta' => $ownerPayment['overpaid_renta'] ?? 0,
                        'paid_renta_by' => $ownerPayment['paid_renta_by'] ?? null,
                        'paid_nat_via_money' => $ownerPayment['paid_nat_via_money'] ?? 0,
                        'paid_nat_via_nat' => $ownerPayment['paid_nat_via_nat'] ?? 0,
                        'paid_renta_nat_sum' => $ownerPayment['paid_renta_nat_sum'] ?? 0,
                        'overpaid_renta_nat_money_arr' => $ownerPayment['overpaid_renta_nat_money_arr'] ?? [],
                        'unpaid_renta_nat_money_arr' => $ownerPayment['unpaid_renta_nat_money_arr'] ?? [],
                        'overpaid_renta_nat_arr' => $ownerPayment['overpaid_renta_nat_arr'] ?? [],
                        'unpaid_renta_nat_arr' => $ownerPayment['unpaid_renta_nat_arr'] ?? [],
                        'paid_via_nat' => $ownerPayment['paid_via_nat'] ?? 0,
                        'paid_via_money' => $ownerPayment['paid_via_money'] ?? 0,
                        'rent_place' => (strlen($aggPlots[$plotKey]['rent_place'] ?? '') > 0 && '-' !== $aggPlots[$plotKey]['rent_place'])
                            ? $aggPlots[$plotKey]['rent_place']
                            : ($ownerPayment['rent_place_name'] ?? ''),
                    ]);
                }
            }
        }

        return $aggPlots;
    }

    // Placeholder methods that would need full implementation
    private function getAllowedFarmingIds(): array
    {
        // This would typically come from user permissions/authentication
        return [1, 2, 3]; // Placeholder
    }

    private function getOwnerContracts(int $year, array $filterParams): array
    {
        // This would query the database for owner contracts
        return []; // Placeholder
    }

    private function filterOwnersTree(array $contractsPayments, array $filterParams): array
    {
        // Implementation for filtering owners tree
        return $contractsPayments; // Placeholder
    }

    private function flattenOwnersTree(array $ownersTree): array
    {
        // Implementation for flattening owners tree
        return $ownersTree; // Placeholder
    }

    private function calculateOwnerData(array $existing, array $new): array
    {
        // Implementation for calculating/merging owner data
        return array_merge($existing, $new); // Placeholder
    }

    private function buildOwnersTree(array $flatOwners): array
    {
        // Implementation for building owners tree
        return $flatOwners; // Placeholder
    }

    private function formatOwnersData(array $data): array
    {
        // Implementation for formatting owners data
        return $data; // Placeholder
    }

    private function generateFooter(array $data, array $excludeFields = []): array
    {
        // Implementation for generating footer data
        return []; // Placeholder
    }

    private function getOwnerDataFromContract(array $contractPayments, string|int $ownerIdentifier): array
    {
        // Implementation for extracting owner data from contract
        return []; // Placeholder
    }

    private function sumOwnerDataInContract(array $contractsPayments, int $ownerId): array
    {
        // Implementation for summing owner data in contract
        return []; // Placeholder
    }
}
