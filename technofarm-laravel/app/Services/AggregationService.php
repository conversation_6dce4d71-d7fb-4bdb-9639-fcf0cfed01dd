<?php

namespace App\Services;

use App\Models\Farming;

/**
 * Service for data aggregation operations.
 * Handles aggregation of plot and payment data by various criteria.
 */
class AggregationService
{
    public function __construct(
        private MathService $mathService
    ) {}

    /**
     * Aggregate plots by specified criteria.
     * Migrated from PaymentsController::aggPlots().
     */
    public function aggPlots(array $plots, string $aggBy = 'owner'): array
    {
        $aggPlots = [];
        $aggPlotsByPlot = [];

        foreach ($plots as $key => $plot) {
            $aggKeyByPlot = null;
            $aggKey = $this->generateAggregationKey($plot, $aggBy);

            // Handle plot-specific aggregation key for cultivated area deduplication
            if ($aggBy === 'owner') {
                $aggKeyByPlot = $aggKey . '_' . $plot['pc_rel_id'];
            }

            if (!isset($aggPlots[$aggKey])) {
                $aggPlots[$aggKey] = $this->initializeAggregatedPlot($plot, $key);
                
                if ($aggKeyByPlot && !in_array($aggKeyByPlot, $aggPlotsByPlot)) {
                    $aggPlotsByPlot[] = $aggKeyByPlot;
                }
            } else {
                $aggPlots[$aggKey] = $this->sumPlotData($aggPlots[$aggKey], $plot, $aggKeyByPlot, $aggPlotsByPlot);
            }

            // Handle arrays for plot-specific properties
            $aggPlots[$aggKey] = $this->updatePlotArrays($aggPlots[$aggKey], $plot);
        }

        return $aggPlots;
    }

    /**
     * Sum payments data.
     * Migrated from PaymentsController::sumPayments().
     */
    public function sumPayments(array $payment1, array $payment2, bool $duplicatedPlot = false): array
    {
        // Add plot IDs
        $payment1['plot_id'][] = $payment2['plot_id'];
        $payment1['pc_rel_id'][] = $payment2['pc_rel_id'];

        // Sum area values
        $payment1['all_owner_area'] = $this->mathService->addArea($payment1['all_owner_area'], $payment2['all_owner_area']);
        $payment1['all_owner_contract_area'] = $this->mathService->addArea($payment1['all_owner_contract_area'], $payment2['all_owner_contract_area']);
        $payment1['all_owner_no_rounded'] = $this->mathService->add($payment1['all_owner_no_rounded'], $payment2['all_owner_no_rounded']);
        $payment1['all_owner_no_rounded_contract'] = $this->mathService->add($payment1['all_owner_no_rounded_contract'], $payment2['all_owner_no_rounded_contract']);
        $payment1['pu_area'] = $this->mathService->addArea($payment1['pu_area'], $payment2['pu_area']);
        $payment1['owner_area'] = $this->mathService->addArea($payment1['owner_area'], $payment2['calculation_area']);

        // Sum rent values
        $payment1['renta'] = $this->mathService->addMoney($payment1['renta'], $payment2['renta']);
        $payment1['charged_renta'] = $this->mathService->addMoney($payment1['charged_renta'], $payment2['charged_renta']);
        $payment1['unpaid_renta'] = $this->mathService->addMoney($payment1['unpaid_renta'], $payment2['unpaid_renta']);

        // Store plot-specific data
        $payment1['charged_renta_nat_values'][$payment2['plot_id']] = $payment2['charged_renta_nat'];
        $payment1['plots_percent'][$payment2['plot_id']] = $payment2['plots_percent'];

        // Sum rent in nature values
        foreach ($payment2['renta_nat_info'] as $rentaNatInfo) {
            $rentaNatId = $rentaNatInfo['renta_nat_id'];
            
            $payment1['renta_nat'][$rentaNatId] = $this->mathService->addRentNat(
                $payment1['renta_nat'][$rentaNatId] ?? 0,
                $rentaNatInfo['contract_renta_nat'] ?? 0
            );
            
            $payment1['charged_renta_nat'][$rentaNatId] = $this->mathService->addRentNat(
                $payment1['charged_renta_nat'][$rentaNatId] ?? 0,
                $rentaNatInfo['charged_renta_nat'] ?? 0
            );
        }

        // Sum unpaid rent in nature arrays
        foreach ($payment2['unpaid_renta_nat_arr'] as $rentaNatId => $unpaidRentaNatValue) {
            $payment1['unpaid_renta_nat_arr'][$rentaNatId] = $this->mathService->addRentNat(
                $payment1['unpaid_renta_nat_arr'][$rentaNatId] ?? 0,
                $unpaidRentaNatValue
            );
        }

        foreach ($payment2['unpaid_renta_nat_money_arr'] as $rentaNatId => $unpaidRentaNatMoneyValue) {
            $payment1['unpaid_renta_nat_money_arr'][$rentaNatId] = $this->mathService->addMoney(
                $payment1['unpaid_renta_nat_money_arr'][$rentaNatId] ?? 0,
                $unpaidRentaNatMoneyValue
            );
        }

        // Sum cultivated area only if not duplicated plot
        if (!$duplicatedPlot) {
            $payment1['cultivated_area'] = $this->mathService->addArea(
                $payment1['cultivated_area'],
                $payment2['cultivated_area']
            );
        }

        return $payment1;
    }

    /**
     * Generate aggregation key based on criteria.
     */
    private function generateAggregationKey(array $plot, string $aggBy): string
    {
        return match ($aggBy) {
            'owner' => $plot['owner_id'] . '_' . ($plot['path'] ?? '0'),
            'plot' => $plot['pc_rel_id'],
            'contract' => $plot['contract_id'],
            'rent_type' => $plot['owner_id'] . '_' . ($plot['plot_rent_type_id'] . '_' . $plot['pc_rel_id'] ?? '0'),
            default => $plot['owner_id'] . '_' . ($plot['path'] ?? '0'),
        };
    }

    /**
     * Initialize aggregated plot data structure.
     */
    private function initializeAggregatedPlot(array $plot, int $key): array
    {
        $farmingData = $this->getFarmingData($plot['farming_id']);

        return [
            'uuid' => substr(md5($plot['owner_id'] . ($plot['path'] ?? '')), 0, 6),
            'contract_id' => $plot['contract_id'],
            'parent_id' => $plot['parent_id'],
            'c_num' => $plot['c_num'],
            'c_num_with_group_name' => !empty($plot['contract_group_name']) 
                ? $plot['c_num'] . ' (' . $plot['contract_group_name'] . ')' 
                : $plot['c_num'],
            'contract_group_name' => $plot['contract_group_name'],
            'egn_eik' => $plot['egn_eik'],
            'farming_id' => $plot['farming_id'],
            'farming_name' => $plot['farming_name'],
            'contract_type' => $plot['contract_type'],
            'contract_start_date' => $plot['contract_start_date'],
            'contract_due_date' => $plot['contract_due_date'],
            'osz_date' => $plot['osz_date'],
            'osz_num' => $plot['osz_num'],
            'sv_num' => $plot['sv_num'],
            'sv_date' => $plot['sv_date'],
            'annex_id' => $plot['annex_id'],
            'id' => $key + 1,
            'owner_id' => $plot['owner_id'],
            'owner_names' => $plot['owner_names'],
            'owner_parent_names' => $plot['owner_parent_names'],
            'owner_parent_id' => $plot['owner_parent_id'],
            'phone' => $plot['phone'],
            'mobile' => $plot['mobile'],
            'rep_names' => $plot['rep_names'],
            'rep_iban' => $plot['rep_iban'],
            'rep_egn' => $plot['rep_egn'],
            'rep_address' => $plot['rep_address'],
            'rep_lk' => $plot['rep_lk'],
            'rep_lk_izdavane' => $plot['rep_lk_izdavane'],
            'address' => $plot['address'],
            'lk_nomer' => $plot['lk_nomer'],
            'lk_izdavane' => $plot['lk_izdavane'],
            'owner_post_payment_fields' => $plot['owner_post_payment_fields'],
            'iban' => $plot['iban'],
            'rent_place_ekatte' => $plot['rent_place_ekatte'],
            'rent_place_name' => $plot['rent_place_name'],
            'is_dead' => $plot['is_dead'],
            'dead_date' => $plot['dead_date'],
            'allow_owner_payment' => $plot['allow_owner_payment'],
            'dead_date_in_current_farm_year' => $plot['dead_date_in_current_farm_year'],
            'dont_show_heritoris' => $plot['dont_show_heritoris'],
            'is_heritor' => $plot['is_heritor'],
            'plot_id' => $plot['plot_id'],
            'kad_ident' => $plot['kad_ident'],
            'ekatte_name' => $plot['ekatte_name'],
            'area_type' => $plot['area_type'],
            'mestnost' => $plot['mestnost'],
            'category' => $plot['category'],
            'pc_rel_id' => $plot['pc_rel_id'],
            'plot_rent_type_id' => $plot['plot_rent_type_id'],
            'plot_rent_type_category' => $plot['plot_rent_type_category'],
            'plot_rent_type_value' => $plot['plot_rent_type_value'],
            'plot_rent_type_title' => $plot['plot_rent_type_title'],
            'path' => $plot['path'],
            'owner_path_key' => $plot['owner_path_key'],
            'all_owner_area' => $plot['all_owner_area'],
            'all_owner_contract_area' => $plot['all_owner_contract_area'],
            'all_owner_no_rounded' => $plot['all_owner_no_rounded'],
            'all_owner_no_rounded_contract' => $plot['all_owner_no_rounded_contract'],
            'plot_owned_area_total' => $plot['plot_owned_area_total'],
            'plot_owned_area' => $plot['plot_owned_area'],
            'pu_area' => $plot['pu_area'],
            'owner_area' => $plot['calculation_area'],
            'cultivated_area' => $plot['cultivated_area'],
            'renta' => $plot['renta'],
            'charged_renta' => $plot['charged_renta'],
            'paid_renta' => $this->mathService->roundMoney(0),
            'paid_via_money' => $this->mathService->roundMoney(0),
            'unpaid_renta' => $plot['unpaid_renta'],
            'overpaid_renta' => $this->mathService->roundMoney(0),
            'rent_per_plot_value' => $plot['rent_per_plot_value'],
            'plot_rent' => $plot['plot_rent'],
            'rent_money_value' => $plot['rent_money_value'],
            'charged_renta_value' => $plot['charged_renta_value'],
            'contracts_owned_area' => [
                $plot['contract_id'] => $plot['contract_owned_area'],
            ],
            'renta_nat' => $plot['renta_nat'] ?? [],
            'charged_renta_nat' => $plot['charged_renta_nat'] ?? [],
            'renta_nat_info' => $plot['renta_nat_info'] ?? [],
            'unpaid_renta_nat_arr' => $plot['unpaid_renta_nat_arr'] ?? [],
            'overpaid_renta_nat_arr' => $plot['overpaid_renta_nat_arr'] ?? [],
            'unpaid_renta_nat_money_arr' => $plot['unpaid_renta_nat_money_arr'] ?? [],
            'overpaid_renta_nat_money_arr' => $plot['overpaid_renta_nat_money_arr'] ?? [],
            'farm_post_payment_fields' => $farmingData['post_payment_fields'] ?? [],
            'kad_idents_array' => [],
            'rep_names_array' => [],
            'rep_ibans_array' => [],
        ];
    }

    /**
     * Sum plot data into aggregated structure.
     */
    private function sumPlotData(array $aggPlot, array $plot, ?string $aggKeyByPlot, array &$aggPlotsByPlot): array
    {
        $aggPlot['plot_id'][] = $plot['plot_id'];
        $aggPlot['pc_rel_id'][] = $plot['pc_rel_id'];

        // Add rent nature info if not already present
        foreach ($plot['renta_nat_info'] as $rentaNatInfo) {
            if (!isset($aggPlot['renta_nat_info'][$rentaNatInfo['renta_nat_id']])) {
                $aggPlot['renta_nat_info'][$rentaNatInfo['renta_nat_id']] = $rentaNatInfo;
            }
        }

        $duplicatedPlot = $aggKeyByPlot && in_array($aggKeyByPlot, $aggPlotsByPlot);
        $aggPlot = $this->sumPayments($aggPlot, $plot, $duplicatedPlot);

        if ($aggKeyByPlot && !in_array($aggKeyByPlot, $aggPlotsByPlot)) {
            $aggPlotsByPlot[] = $aggKeyByPlot;
        }

        return $aggPlot;
    }

    /**
     * Update plot-specific arrays.
     */
    private function updatePlotArrays(array $aggPlot, array $plot): array
    {
        // Update cadastral identifiers array
        if (!in_array($plot['kad_ident'], $aggPlot['kad_idents_array'])) {
            $aggPlot['kad_idents_array'][] = $plot['kad_ident'];
        }

        // Update representative names array
        if (!empty($plot['rep_names']) && !in_array($plot['rep_names'], $aggPlot['rep_names_array'])) {
            $aggPlot['rep_names_array'][] = $plot['rep_names'];
        }

        // Update representative IBANs array
        if (!empty($plot['rep_iban']) && !in_array($plot['rep_iban'], $aggPlot['rep_ibans_array'])) {
            $aggPlot['rep_ibans_array'][] = $plot['rep_iban'];
        }

        return $aggPlot;
    }

    /**
     * Get farming data.
     */
    private function getFarmingData(int $farmingId): array
    {
        $farming = Farming::find($farmingId);
        
        return [
            'id' => $farming?->id,
            'name' => $farming?->name,
            'post_payment_fields' => $farming?->post_payment_fields ?? [],
        ];
    }
}
