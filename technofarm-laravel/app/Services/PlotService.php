<?php

namespace App\Services;

use App\Models\Plot;
use App\Models\RentType;
use Illuminate\Support\Collection;

/**
 * Service for plot-related operations and calculations.
 * Handles plot data retrieval and rent calculations.
 */
class PlotService
{
    public function __construct(
        private MathService $mathService,
        private PersonalUseService $personalUseService
    ) {}

    /**
     * Get payment plots with their owners, rents, charged rents, contracts and annexes.
     * Migrated from PaymentsModel::getPaymentPlots().
     */
    public function getPaymentsPlots(
        int $year,
        ?int $contractAnnexId = null,
        ?int $annexId = null,
        ?int $ownerId = null,
        ?string $path = null,
        array $filterParams = []
    ): array {
        $farmYearStart = ($year - 1) . '-10-01';
        $farmYearEnd = $year . '-09-30';

        // Build query for plots with all related data
        $query = Plot::query()
            ->with([
                'owners' => function ($query) use ($ownerId, $path) {
                    if ($ownerId) {
                        $query->where('owners.id', $ownerId);
                        if (!$path) {
                            $query->whereNull('plot_owners.path');
                        }
                    }
                    if ($path) {
                        $query->where('plot_owners.path', $path);
                    }
                },
                'contracts' => function ($query) use ($contractAnnexId) {
                    if ($contractAnnexId) {
                        $query->where('contracts.id', $contractAnnexId);
                    }
                },
                'rentType'
            ]);

        // Apply filters
        if (!empty($filterParams['payroll_ekate']) && $filterParams['payroll_ekate'][0] !== '') {
            $query->whereIn('ekatte', $filterParams['payroll_ekate']);
        }

        if (!empty($filterParams['payroll_farming']) && $filterParams['payroll_farming'][0] !== '') {
            $hasNullValue = in_array(null, $filterParams['payroll_farming']);
            
            if ($hasNullValue) {
                $filterParams['payroll_farming'] = array_filter($filterParams['payroll_farming'], function ($value) {
                    return $value !== null;
                });
            }
            
            $query->whereHas('contracts', function ($q) use ($filterParams, $hasNullValue) {
                $q->whereIn('farming_id', $filterParams['payroll_farming']);
                if ($hasNullValue) {
                    $q->orWhereNull('farming_id');
                }
            });
        }

        $plots = $query->get();

        // Get rent nature data
        $rentaNatArr = RentType::active()->get()->keyBy('id')->toArray();

        // Get personal use data for the criteria
        $personalUse = $this->personalUseService->getPersonalUseForOwners([
            'contract_id' => $contractAnnexId,
            'year' => $year,
            'chosen_years' => $year,
        ]);

        $processedPlots = [];

        foreach ($plots as $plot) {
            $plotData = $this->processPlotData($plot, $year, $personalUse, $rentaNatArr);
            $processedPlots[] = $plotData;
        }

        return $processedPlots;
    }

    /**
     * Process individual plot data with rent calculations.
     */
    private function processPlotData(Plot $plot, int $year, array $personalUse, array $rentaNatArr): array
    {
        $plotData = $plot->toArray();

        // Calculate personal use area
        $plotData['pu_area'] = $this->personalUseService->getPersonalUseArea($personalUse, $plot->owners->first()?->id, $plotData);

        // Calculate calculation area (total area minus personal use area)
        $plotData['calculation_area'] = $this->mathService->subArea($plotData['total_area'], $plotData['pu_area']);

        // Set area values
        $plotData['all_owner_area'] = $this->mathService->round($plotData['total_area'], $this->mathService->getAreaPrecision());
        $plotData['owner_area'] = $this->mathService->round($plotData['calculation_area'], $this->mathService->getAreaPrecision());

        // Process rent in nature
        $plotData = $this->processRentInNature($plotData, $rentaNatArr);

        // Calculate rent amounts
        $plotData = $this->calculateRentAmounts($plotData);

        // Initialize payment-related fields
        $plotData['paid_renta'] = $this->mathService->roundMoney(0);
        $plotData['unpaid_renta'] = $this->mathService->addMoney($plotData['contract_renta'] ?? 0, $plotData['charged_renta'] ?? 0);
        $plotData['overpaid_renta'] = $this->mathService->roundMoney(0);

        return $plotData;
    }

    /**
     * Process rent in nature data for a plot.
     */
    private function processRentInNature(array $plotData, array $rentaNatArr): array
    {
        $plotData['renta_nat'] = [];
        $plotData['renta_nat_info'] = [];
        $plotData['charged_renta_nat'] = [];
        $plotData['unpaid_renta_nat_arr'] = [];
        $plotData['overpaid_renta_nat_arr'] = [];
        $plotData['unpaid_renta_nat_money_arr'] = [];
        $plotData['overpaid_renta_nat_money_arr'] = [];
        $plotData['paid_nat_via_nat'] = [];
        $plotData['paid_renta_nat_sum'] = [];

        // Parse charged rent in nature
        $chargedRentaNatArr = $plotData['charged_renta_nat_json'] ?? [];
        $rentaNatJsonArr = $plotData['renta_nat_json'] ?? [];

        foreach ($rentaNatJsonArr as $rentaNat) {
            $rentaNatId = $rentaNat['renta_nat_id'];
            
            $plotData['renta_nat'][$rentaNatId] = 0;
            $plotData['renta_nat_info'][$rentaNatId] = [
                'renta_nat_id' => $rentaNatId,
                'renta_nat_name' => $rentaNat['renta_nat_name'],
                'unit_id' => $rentaNat['unit_id'],
                'unit_name' => $rentaNatArr[$rentaNatId]['unit_name'] ?? '',
                'nat_value' => $rentaNat['nat_value'],
                'unit_value' => $rentaNat['unit_value'],
                'charged_renta_nat' => null,
                'contract_renta_nat' => null,
            ];

            // Process charged rent in nature if exists
            if (!empty($chargedRentaNatArr)) {
                foreach ($chargedRentaNatArr as $chargedRentaNat) {
                    if ($chargedRentaNat['charged_renta_nat_id'] == $rentaNat['id']) {
                        if ($chargedRentaNat['nat_is_converted']) {
                            // Converted to money
                            $chargedRentNatValue = $this->mathService->mul($chargedRentaNat['amount'], $chargedRentaNat['unit_value']);
                            $chargedRentaNatConverted = $this->mathService->mul($chargedRentNatValue, $plotData['calculation_area']);
                            $plotData['charged_renta_nat_converted'] = $this->mathService->add(
                                $plotData['charged_renta_nat_converted'] ?? 0,
                                $chargedRentaNatConverted
                            );
                        } else {
                            // Charged in nature
                            $plotData['charged_renta_nat'][$rentaNatId] = $this->mathService->mul(
                                $chargedRentaNat['amount'],
                                $plotData['calculation_area']
                            );
                            $plotData['renta_nat_info'][$rentaNatId]['charged_renta_nat'] = $plotData['charged_renta_nat'][$rentaNatId];
                        }
                    } else {
                        // Contract rent in nature
                        $rentNatValue = ($plotData['rent_per_plot_value'] > 0) ? 0 : $rentaNat['nat_value'];
                        $plotData['renta_nat_info'][$rentaNatId]['contract_renta_nat'] = $this->mathService->mul(
                            $rentNatValue,
                            $plotData['calculation_area']
                        );
                        $plotData['renta_nat'][$rentaNatId] = $this->mathService->add(
                            $plotData['renta_nat'][$rentaNatId],
                            $plotData['renta_nat_info'][$rentaNatId]['contract_renta_nat']
                        );
                    }
                }
            } else {
                // No charged rent, use contract rent
                $rentNatValue = ($plotData['rent_per_plot_value'] > 0) ? 0 : $rentaNat['nat_value'];
                $plotData['renta_nat_info'][$rentaNatId]['contract_renta_nat'] = $this->mathService->mul(
                    $rentNatValue,
                    $plotData['calculation_area']
                );
                $plotData['renta_nat'][$rentaNatId] = $this->mathService->add(
                    $plotData['renta_nat'][$rentaNatId],
                    $plotData['renta_nat_info'][$rentaNatId]['contract_renta_nat']
                );
            }

            // Calculate unpaid rent in nature
            $plotCharedRentaNat = $plotData['renta_nat_info'][$rentaNatId]['charged_renta_nat'] ?? 0;
            $plotContractRentaNat = $plotData['renta_nat_info'][$rentaNatId]['contract_renta_nat'] ?? 0;
            $unpaidRentaNat = $this->mathService->add($plotCharedRentaNat, $plotContractRentaNat);
            
            $plotData['unpaid_renta_nat_arr'][$rentaNatId] = $unpaidRentaNat;
            $plotData['unpaid_renta_nat_money_arr'][$rentaNatId] = $unpaidRentaNat * $rentaNat['unit_value'];

            // Initialize other arrays
            $plotData['overpaid_renta_nat_arr'][$rentaNatId] = $this->mathService->roundRentNat(0);
            $plotData['overpaid_renta_nat_money_arr'][$rentaNatId] = $this->mathService->roundMoney(0);
            $plotData['paid_nat_via_nat'][$rentaNatId] = $this->mathService->roundRentNat(0);
            $plotData['paid_renta_nat_sum'][$rentaNatId] = $this->mathService->roundRentNat(0);
        }

        return $plotData;
    }

    /**
     * Calculate rent amounts for a plot.
     */
    private function calculateRentAmounts(array $plotData): array
    {
        // Determine rent amount based on priority:
        // 1. Individual rent per plot (highest priority)
        // 2. Charged rent (if exists)
        // 3. Contract rent (default)

        if (!empty($plotData['rent_per_plot_value'])) {
            // Individual rent per plot
            $plotData['contract_renta'] = $this->mathService->mul($plotData['rent_per_plot_value'], $plotData['calculation_area']);
            $plotData['renta'] = $plotData['contract_renta'];
        } elseif (!empty($plotData['charged_renta_value']) || !empty($plotData['charged_renta_nat_converted'])) {
            // Charged rent
            $chargedRenta = $this->mathService->mul($plotData['charged_renta_value'] ?? 0, $plotData['calculation_area']);
            $plotData['charged_renta'] = $this->mathService->add($chargedRenta, $plotData['charged_renta_nat_converted'] ?? 0);
        } else {
            // Contract rent
            $plotData['contract_renta'] = $this->mathService->mul($plotData['rent_money_value'] ?? 0, $plotData['calculation_area']);
            $plotData['renta'] = $plotData['contract_renta'];
        }

        return $plotData;
    }
}
