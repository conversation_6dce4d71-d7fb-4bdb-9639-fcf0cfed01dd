<?php

namespace App\Services;

/**
 * Service for precision mathematical operations using bcmath.
 * Mirrors the functionality of the legacy Math class.
 */
class MathService
{
    private int $areaPrecision;
    private int $moneyPrecision;
    private int $rentaNatPrecision;
    private int $infinityPrecision;
    private float $fixMoneyRoundingValue;
    private int $scale = 15;

    public function __construct(
        int $areaPrecision = 3,
        int $moneyPrecision = 2,
        int $rentaNatPrecision = 3,
        int $infinityPrecision = 16,
        float $fixMoneyRoundingValue = 0.1
    ) {
        $this->areaPrecision = $areaPrecision;
        $this->moneyPrecision = $moneyPrecision;
        $this->rentaNatPrecision = $rentaNatPrecision;
        $this->infinityPrecision = $infinityPrecision;
        $this->fixMoneyRoundingValue = $fixMoneyRoundingValue;
    }

    /**
     * Add two arbitrary precision numbers.
     */
    public function add(string|float|int $addend1, string|float|int $addend2, ?int $precision = null): string
    {
        $result = bcadd((string)$addend1, (string)$addend2, $this->scale);
        
        if ($precision !== null) {
            return $this->round($result, $precision);
        }
        
        return $result;
    }

    /**
     * Subtract one arbitrary precision number from another.
     */
    public function sub(string|float|int $minuend, string|float|int $subtrahend, ?int $precision = null): string
    {
        $result = bcsub((string)$minuend, (string)$subtrahend, $this->scale);
        
        if ($precision !== null) {
            return $this->round($result, $precision);
        }
        
        return $result;
    }

    /**
     * Multiply two arbitrary precision numbers.
     */
    public function mul(string|float|int $multiplicand, string|float|int $multiplier, ?int $precision = null): string
    {
        $result = bcmul((string)$multiplicand, (string)$multiplier, $this->scale);
        
        if ($precision !== null) {
            return $this->round($result, $precision);
        }
        
        return $result;
    }

    /**
     * Divide two arbitrary precision numbers.
     */
    public function div(string|float|int $dividend, string|float|int $divisor, ?int $precision = null): string
    {
        if (bccomp((string)$divisor, '0', $this->scale) === 0) {
            throw new \InvalidArgumentException('Division by zero');
        }
        
        $result = bcdiv((string)$dividend, (string)$divisor, $this->scale);
        
        if ($precision !== null) {
            return $this->round($result, $precision);
        }
        
        return $result;
    }

    /**
     * Compare two arbitrary precision numbers.
     * Returns 0 if equal, 1 if num1 > num2, -1 if num1 < num2.
     */
    public function compare(string|float|int $num1, string|float|int $num2): int
    {
        return bccomp((string)$num1, (string)$num2, $this->scale);
    }

    /**
     * Round a number to specified precision.
     */
    public function round(string|float|int $number, int $precision): string
    {
        $factor = bcpow('10', (string)$precision, $this->scale);
        $multiplied = bcmul((string)$number, $factor, $this->scale);
        
        // Add 0.5 for positive numbers, subtract 0.5 for negative numbers
        if (bccomp($multiplied, '0', $this->scale) >= 0) {
            $adjusted = bcadd($multiplied, '0.5', $this->scale);
        } else {
            $adjusted = bcsub($multiplied, '0.5', $this->scale);
        }
        
        // Truncate to integer
        $truncated = bcadd($adjusted, '0', 0);
        
        // Divide back
        return bcdiv($truncated, $factor, $precision);
    }

    /**
     * Get the absolute value of a number.
     */
    public function abs(string|float|int $number): string
    {
        if (bccomp((string)$number, '0', $this->scale) < 0) {
            return bcmul((string)$number, '-1', $this->scale);
        }
        
        return (string)$number;
    }

    /**
     * Raise a number to a power.
     */
    public function pow(string|float|int $base, string|float|int $exponent): string
    {
        return bcpow((string)$base, (string)$exponent, $this->scale);
    }

    /**
     * Get the square root of a number.
     */
    public function sqrt(string|float|int $number): string
    {
        if (bccomp((string)$number, '0', $this->scale) < 0) {
            throw new \InvalidArgumentException('Cannot calculate square root of negative number');
        }
        
        return bcsqrt((string)$number, $this->scale);
    }

    // Convenience methods with predefined precision

    /**
     * Add with area precision.
     */
    public function addArea(string|float|int $addend1, string|float|int $addend2): string
    {
        return $this->add($addend1, $addend2, $this->areaPrecision);
    }

    /**
     * Subtract with area precision.
     */
    public function subArea(string|float|int $minuend, string|float|int $subtrahend): string
    {
        return $this->sub($minuend, $subtrahend, $this->areaPrecision);
    }

    /**
     * Multiply with area precision.
     */
    public function mulArea(string|float|int $multiplicand, string|float|int $multiplier): string
    {
        return $this->mul($multiplicand, $multiplier, $this->areaPrecision);
    }

    /**
     * Divide with area precision.
     */
    public function divArea(string|float|int $dividend, string|float|int $divisor): string
    {
        return $this->div($dividend, $divisor, $this->areaPrecision);
    }

    /**
     * Add with money precision.
     */
    public function addMoney(string|float|int $addend1, string|float|int $addend2): string
    {
        return $this->add($addend1, $addend2, $this->moneyPrecision);
    }

    /**
     * Subtract with money precision.
     */
    public function subMoney(string|float|int $minuend, string|float|int $subtrahend): string
    {
        return $this->sub($minuend, $subtrahend, $this->moneyPrecision);
    }

    /**
     * Multiply with money precision.
     */
    public function mulMoney(string|float|int $multiplicand, string|float|int $multiplier): string
    {
        return $this->mul($multiplicand, $multiplier, $this->moneyPrecision);
    }

    /**
     * Divide with money precision.
     */
    public function divMoney(string|float|int $dividend, string|float|int $divisor): string
    {
        return $this->div($dividend, $divisor, $this->moneyPrecision);
    }

    /**
     * Round with money precision.
     */
    public function roundMoney(string|float|int $number): string
    {
        return $this->round($number, $this->moneyPrecision);
    }

    /**
     * Add with rent nature precision.
     */
    public function addRentNat(string|float|int $addend1, string|float|int $addend2): string
    {
        return $this->add($addend1, $addend2, $this->rentaNatPrecision);
    }

    /**
     * Subtract with rent nature precision.
     */
    public function subRentNat(string|float|int $minuend, string|float|int $subtrahend): string
    {
        return $this->sub($minuend, $subtrahend, $this->rentaNatPrecision);
    }

    /**
     * Multiply with rent nature precision.
     */
    public function mulRentNat(string|float|int $multiplicand, string|float|int $multiplier): string
    {
        return $this->mul($multiplicand, $multiplier, $this->rentaNatPrecision);
    }

    /**
     * Divide with rent nature precision.
     */
    public function divRentNat(string|float|int $dividend, string|float|int $divisor): string
    {
        return $this->div($dividend, $divisor, $this->rentaNatPrecision);
    }

    /**
     * Round with rent nature precision.
     */
    public function roundRentNat(string|float|int $number): string
    {
        return $this->round($number, $this->rentaNatPrecision);
    }

    /**
     * Get precision values.
     */
    public function getAreaPrecision(): int
    {
        return $this->areaPrecision;
    }

    public function getMoneyPrecision(): int
    {
        return $this->moneyPrecision;
    }

    public function getRentNatPrecision(): int
    {
        return $this->rentaNatPrecision;
    }

    public function getInfinityPrecision(): int
    {
        return $this->infinityPrecision;
    }

    public function getFixMoneyRoundingValue(): float
    {
        return $this->fixMoneyRoundingValue;
    }
}
