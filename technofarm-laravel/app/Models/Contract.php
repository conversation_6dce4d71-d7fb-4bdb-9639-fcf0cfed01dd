<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Contract extends Model
{
    use HasFactory;

    protected $fillable = [
        'parent_id',
        'c_num',
        'farming_id',
        'farming_name',
        'nm_usage_rights',
        'virtual_contract_type',
        'start_date',
        'due_date',
        'osz_num',
        'osz_date',
        'sv_num',
        'sv_date',
        'rent_money_value',
        'rent_nature_json',
        'contract_group_id',
    ];

    protected $casts = [
        'start_date' => 'date',
        'due_date' => 'date',
        'osz_date' => 'date',
        'sv_date' => 'date',
        'rent_money_value' => 'decimal:6',
        'rent_nature_json' => 'array',
    ];

    /**
     * Get the parent contract (for annexes).
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(Contract::class, 'parent_id');
    }

    /**
     * Get the child contracts (annexes).
     */
    public function annexes(): HasMany
    {
        return $this->hasMany(Contract::class, 'parent_id');
    }

    /**
     * Get the owners for this contract.
     */
    public function owners(): BelongsToMany
    {
        return $this->belongsToMany(Owner::class, 'contract_owners')
            ->withPivot(['path', 'plots_percent', 'owner_path_key'])
            ->withTimestamps();
    }

    /**
     * Get the plots for this contract.
     */
    public function plots(): BelongsToMany
    {
        return $this->belongsToMany(Plot::class, 'plot_contracts')
            ->withPivot(['owned_area', 'cultivated_area'])
            ->withTimestamps();
    }

    /**
     * Get the payments for this contract.
     */
    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * Get the contract group.
     */
    public function contractGroup(): BelongsTo
    {
        return $this->belongsTo(ContractGroup::class, 'contract_group_id');
    }

    /**
     * Get the farming entity.
     */
    public function farming(): BelongsTo
    {
        return $this->belongsTo(Farming::class, 'farming_id');
    }

    /**
     * Scope to filter main contracts (not annexes).
     */
    public function scopeMainContracts($query)
    {
        return $query->whereNull('parent_id');
    }

    /**
     * Scope to filter annexes.
     */
    public function scopeAnnexes($query)
    {
        return $query->whereNotNull('parent_id');
    }

    /**
     * Scope to filter by farming year.
     */
    public function scopeForFarmingYear($query, $year)
    {
        $farmYearStart = ($year - 1) . '-10-01';
        $farmYearEnd = $year . '-09-30';
        
        return $query->where(function ($q) use ($farmYearStart, $farmYearEnd) {
            $q->whereBetween('start_date', [$farmYearStart, $farmYearEnd])
              ->orWhereBetween('due_date', [$farmYearStart, $farmYearEnd])
              ->orWhere(function ($q2) use ($farmYearStart, $farmYearEnd) {
                  $q2->where('start_date', '<=', $farmYearStart)
                     ->where('due_date', '>=', $farmYearEnd);
              });
        });
    }

    /**
     * Get the contract number with group name if available.
     */
    public function getCNumWithGroupNameAttribute()
    {
        if ($this->contractGroup && $this->contractGroup->name) {
            return $this->c_num . ' (' . $this->contractGroup->name . ')';
        }
        
        return $this->c_num;
    }

    /**
     * Check if this is an annex.
     */
    public function getIsAnnexAttribute()
    {
        return !is_null($this->parent_id);
    }
}
