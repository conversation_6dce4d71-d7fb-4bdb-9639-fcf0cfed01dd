<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Farming extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'group_id',
        'post_payment_fields',
        'is_active',
    ];

    protected $casts = [
        'post_payment_fields' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Get the contracts for this farming entity.
     */
    public function contracts(): HasMany
    {
        return $this->hasMany(Contract::class, 'farming_id');
    }

    /**
     * Scope to filter active farming entities.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to filter by group.
     */
    public function scopeByGroup($query, $groupId)
    {
        return $query->where('group_id', $groupId);
    }
}
