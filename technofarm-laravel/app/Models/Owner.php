<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Owner extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'owner_type',
        'egn',
        'eik',
        'names',
        'parent_names',
        'address',
        'company_address',
        'phone',
        'mobile',
        'lk_nomer',
        'lk_izdavane',
        'iban',
        'rent_place',
        'is_dead',
        'dead_date',
        'allow_owner_payment',
        'post_payment_fields',
        'rep_names',
        'rep_iban',
        'rep_egn',
        'rep_address',
        'rep_lk',
        'rep_lk_izdavane',
    ];

    protected $casts = [
        'is_dead' => 'boolean',
        'allow_owner_payment' => 'boolean',
        'dead_date' => 'date',
        'lk_izdavane' => 'date',
        'rep_lk_izdavane' => 'date',
        'post_payment_fields' => 'array',
    ];

    /**
     * Get the contracts for this owner.
     */
    public function contracts(): BelongsToMany
    {
        return $this->belongsToMany(Contract::class, 'contract_owners')
            ->withPivot(['path', 'plots_percent', 'owner_path_key'])
            ->withTimestamps();
    }

    /**
     * Get the plots owned by this owner.
     */
    public function plots(): BelongsToMany
    {
        return $this->belongsToMany(Plot::class, 'plot_owners')
            ->withPivot(['owned_area', 'owned_contract_area', 'plots_percent'])
            ->withTimestamps();
    }

    /**
     * Get the payments for this owner.
     */
    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * Get the personal use records for this owner.
     */
    public function personalUse(): HasMany
    {
        return $this->hasMany(PersonalUse::class);
    }

    /**
     * Get the parent owner (for inheritance relationships).
     */
    public function parent()
    {
        return $this->belongsTo(Owner::class, 'parent_id');
    }

    /**
     * Get the child owners (heirs).
     */
    public function children(): HasMany
    {
        return $this->hasMany(Owner::class, 'parent_id');
    }

    /**
     * Scope to filter by alive owners.
     */
    public function scopeAlive($query)
    {
        return $query->where('is_dead', false);
    }

    /**
     * Scope to filter by dead owners.
     */
    public function scopeDead($query)
    {
        return $query->where('is_dead', true);
    }

    /**
     * Scope to check if owner died in a specific farming year.
     */
    public function scopeDeadInFarmingYear($query, $year)
    {
        $farmYearStart = ($year - 1) . '-10-01';
        $farmYearEnd = $year . '-09-30';
        
        return $query->where('is_dead', true)
            ->whereBetween('dead_date', [$farmYearStart, $farmYearEnd]);
    }

    /**
     * Get the EGN or EIK based on owner type.
     */
    public function getEgnEikAttribute()
    {
        return $this->owner_type == 1 ? $this->egn : $this->eik;
    }

    /**
     * Get the appropriate address based on owner type.
     */
    public function getAddressAttribute()
    {
        return $this->owner_type == 1 ? $this->attributes['address'] : $this->company_address;
    }
}
