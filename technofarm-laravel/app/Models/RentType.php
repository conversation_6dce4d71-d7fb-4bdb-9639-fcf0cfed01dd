<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class RentType extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'unit_id',
        'unit_name',
        'unit_value',
        'nat_value',
        'is_active',
        'category',
        'description',
    ];

    protected $casts = [
        'unit_value' => 'decimal:6',
        'nat_value' => 'decimal:6',
        'is_active' => 'boolean',
    ];

    /**
     * Get the plots using this rent type.
     */
    public function plots(): HasMany
    {
        return $this->hasMany(Plot::class);
    }

    /**
     * Get the payments using this rent type.
     */
    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * Get the personal use records using this rent type.
     */
    public function personalUse(): HasMany
    {
        return $this->hasMany(PersonalUse::class);
    }

    /**
     * Scope to filter active rent types.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to filter by category.
     */
    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Scope to filter money rent types.
     */
    public function scopeMoneyRents($query)
    {
        return $query->where('category', 'money');
    }

    /**
     * Scope to filter nature rent types.
     */
    public function scopeNatureRents($query)
    {
        return $query->where('category', 'nature');
    }

    /**
     * Get the full unit name with rent type name.
     */
    public function getFullUnitNameAttribute()
    {
        return $this->name . ' (' . $this->unit_name . ')';
    }

    /**
     * Check if this is a money rent type.
     */
    public function isMoneyRent()
    {
        return $this->category === 'money';
    }

    /**
     * Check if this is a nature rent type.
     */
    public function isNatureRent()
    {
        return $this->category === 'nature';
    }

    /**
     * Calculate the money value for a given amount.
     */
    public function calculateMoneyValue($amount)
    {
        if ($this->isMoneyRent()) {
            return $amount;
        }

        return $amount * $this->unit_value;
    }
}
