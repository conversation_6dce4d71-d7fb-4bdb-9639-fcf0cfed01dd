<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PersonalUse extends Model
{
    use HasFactory;

    protected $table = 'personal_use';

    protected $fillable = [
        'owner_id',
        'plot_id',
        'contract_id',
        'year',
        'rent_type_id',
        'personal_use_area',
        'personal_use_unit_value',
        'personal_use_renta',
        'personal_use_paid_renta',
        'personal_use_unpaid_renta',
        'personal_use_treatments_sum',
        'personal_use_paid_treatments',
        'personal_use_unpaid_treatments',
    ];

    protected $casts = [
        'personal_use_area' => 'decimal:6',
        'personal_use_unit_value' => 'decimal:6',
        'personal_use_renta' => 'decimal:6',
        'personal_use_paid_renta' => 'decimal:6',
        'personal_use_unpaid_renta' => 'decimal:6',
        'personal_use_treatments_sum' => 'decimal:6',
        'personal_use_paid_treatments' => 'decimal:6',
        'personal_use_unpaid_treatments' => 'decimal:6',
    ];

    /**
     * Get the owner for this personal use record.
     */
    public function owner(): BelongsTo
    {
        return $this->belongsTo(Owner::class);
    }

    /**
     * Get the plot for this personal use record.
     */
    public function plot(): BelongsTo
    {
        return $this->belongsTo(Plot::class);
    }

    /**
     * Get the contract for this personal use record.
     */
    public function contract(): BelongsTo
    {
        return $this->belongsTo(Contract::class);
    }

    /**
     * Get the rent type for this personal use record.
     */
    public function rentType(): BelongsTo
    {
        return $this->belongsTo(RentType::class);
    }

    /**
     * Scope to filter by owner.
     */
    public function scopeForOwner($query, $ownerId)
    {
        return $query->where('owner_id', $ownerId);
    }

    /**
     * Scope to filter by contract.
     */
    public function scopeForContract($query, $contractId)
    {
        return $query->where('contract_id', $contractId);
    }

    /**
     * Scope to filter by farming year.
     */
    public function scopeForYear($query, $year)
    {
        return $query->where('year', $year);
    }

    /**
     * Scope to filter by rent type.
     */
    public function scopeForRentType($query, $rentTypeId)
    {
        return $query->where('rent_type_id', $rentTypeId);
    }

    /**
     * Get the total personal use value (renta + treatments).
     */
    public function getTotalPersonalUseValueAttribute()
    {
        return $this->personal_use_renta + $this->personal_use_treatments_sum;
    }

    /**
     * Get the total paid personal use value.
     */
    public function getTotalPaidPersonalUseValueAttribute()
    {
        return $this->personal_use_paid_renta + $this->personal_use_paid_treatments;
    }

    /**
     * Get the total unpaid personal use value.
     */
    public function getTotalUnpaidPersonalUseValueAttribute()
    {
        return $this->personal_use_unpaid_renta + $this->personal_use_unpaid_treatments;
    }

    /**
     * Check if this personal use record has any unpaid amounts.
     */
    public function hasUnpaidAmounts()
    {
        return $this->personal_use_unpaid_renta > 0 || $this->personal_use_unpaid_treatments > 0;
    }

    /**
     * Check if this personal use record is fully paid.
     */
    public function isFullyPaid()
    {
        return !$this->hasUnpaidAmounts();
    }
}
