<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ContractGroup extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get the contracts in this group.
     */
    public function contracts(): HasMany
    {
        return $this->hasMany(Contract::class, 'contract_group_id');
    }

    /**
     * Scope to filter active contract groups.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}
