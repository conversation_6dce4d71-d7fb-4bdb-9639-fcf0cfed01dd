<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Plot extends Model
{
    use HasFactory;

    protected $fillable = [
        'gid',
        'kad_ident',
        'mestnost',
        'category',
        'ekatte',
        'ekatte_name',
        'area_type',
        'total_area',
        'cultivated_area',
        'rent_per_plot_value',
        'charged_renta_value',
        'charged_renta_nat_json',
        'renta_nat_json',
    ];

    protected $casts = [
        'total_area' => 'decimal:6',
        'cultivated_area' => 'decimal:6',
        'rent_per_plot_value' => 'decimal:6',
        'charged_renta_value' => 'decimal:6',
        'charged_renta_nat_json' => 'array',
        'renta_nat_json' => 'array',
    ];

    /**
     * Get the owners for this plot.
     */
    public function owners(): BelongsToMany
    {
        return $this->belongsToMany(Owner::class, 'plot_owners')
            ->withPivot(['owned_area', 'owned_contract_area', 'plots_percent'])
            ->withTimestamps();
    }

    /**
     * Get the contracts for this plot.
     */
    public function contracts(): BelongsToMany
    {
        return $this->belongsToMany(Contract::class, 'plot_contracts')
            ->withPivot(['owned_area', 'cultivated_area'])
            ->withTimestamps();
    }

    /**
     * Get the rent type for this plot.
     */
    public function rentType(): BelongsTo
    {
        return $this->belongsTo(RentType::class, 'rent_type_id');
    }

    /**
     * Get the personal use records for this plot.
     */
    public function personalUse(): HasMany
    {
        return $this->hasMany(PersonalUse::class);
    }

    /**
     * Get the area type entity.
     */
    public function areaType(): BelongsTo
    {
        return $this->belongsTo(AreaType::class, 'area_type_id');
    }

    /**
     * Scope to filter by EKATTE code.
     */
    public function scopeByEkatte($query, $ekatte)
    {
        if (is_array($ekatte)) {
            return $query->whereIn('ekatte', $ekatte);
        }
        
        return $query->where('ekatte', $ekatte);
    }

    /**
     * Scope to filter by cadastral identifier.
     */
    public function scopeByKadIdent($query, $kadIdent)
    {
        return $query->where('kad_ident', $kadIdent);
    }

    /**
     * Scope to filter plots with individual rent.
     */
    public function scopeWithIndividualRent($query)
    {
        return $query->whereNotNull('rent_per_plot_value')
            ->where('rent_per_plot_value', '>', 0);
    }

    /**
     * Scope to filter plots without individual rent.
     */
    public function scopeWithoutIndividualRent($query)
    {
        return $query->where(function ($q) {
            $q->whereNull('rent_per_plot_value')
              ->orWhere('rent_per_plot_value', '<=', 0);
        });
    }

    /**
     * Get the virtual category title.
     */
    public function getVirtualCategoryTitleAttribute()
    {
        // This would typically come from a lookup table or configuration
        return $this->category;
    }

    /**
     * Get the virtual EKATTE name.
     */
    public function getVirtualEkatteNameAttribute()
    {
        return $this->ekatte_name;
    }

    /**
     * Get the virtual area type title.
     */
    public function getVirtualAreaTypeTitleAttribute()
    {
        return $this->area_type;
    }

    /**
     * Check if plot has charged rent in nature.
     */
    public function hasChargedRentNature()
    {
        return !empty($this->charged_renta_nat_json);
    }

    /**
     * Check if plot has rent in nature.
     */
    public function hasRentNature()
    {
        return !empty($this->renta_nat_json);
    }
}
