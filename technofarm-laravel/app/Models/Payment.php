<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Payment extends Model
{
    use HasFactory;

    protected $fillable = [
        'owner_id',
        'contract_id',
        'year',
        'payment_type',
        'amount',
        'currency',
        'payment_date',
        'rent_type_id',
        'rent_amount',
        'rent_unit',
        'is_converted_to_money',
        'conversion_rate',
        'notes',
        'paid_by_owner_id',
    ];

    protected $casts = [
        'amount' => 'decimal:6',
        'rent_amount' => 'decimal:6',
        'conversion_rate' => 'decimal:6',
        'payment_date' => 'date',
        'is_converted_to_money' => 'boolean',
    ];

    /**
     * Get the owner who made this payment.
     */
    public function owner(): BelongsTo
    {
        return $this->belongsTo(Owner::class);
    }

    /**
     * Get the owner who this payment was made for (in case of inheritance).
     */
    public function paidByOwner(): BelongsTo
    {
        return $this->belongsTo(Owner::class, 'paid_by_owner_id');
    }

    /**
     * Get the contract this payment is for.
     */
    public function contract(): BelongsTo
    {
        return $this->belongsTo(Contract::class);
    }

    /**
     * Get the rent type for this payment.
     */
    public function rentType(): BelongsTo
    {
        return $this->belongsTo(RentType::class);
    }

    /**
     * Scope to filter by payment type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('payment_type', $type);
    }

    /**
     * Scope to filter by farming year.
     */
    public function scopeForFarmingYear($query, $year)
    {
        return $query->where('year', $year);
    }

    /**
     * Scope to filter money payments.
     */
    public function scopeMoneyPayments($query)
    {
        return $query->where('payment_type', 'money');
    }

    /**
     * Scope to filter nature payments.
     */
    public function scopeNaturePayments($query)
    {
        return $query->where('payment_type', 'nature');
    }

    /**
     * Scope to filter converted nature payments.
     */
    public function scopeConvertedNaturePayments($query)
    {
        return $query->where('payment_type', 'nature')
            ->where('is_converted_to_money', true);
    }

    /**
     * Get the payment amount in money (converted if necessary).
     */
    public function getMoneyAmountAttribute()
    {
        if ($this->payment_type === 'money') {
            return $this->amount;
        }

        if ($this->payment_type === 'nature' && $this->is_converted_to_money) {
            return $this->rent_amount * $this->conversion_rate;
        }

        return 0;
    }

    /**
     * Get the payment amount in nature.
     */
    public function getNatureAmountAttribute()
    {
        if ($this->payment_type === 'nature' && !$this->is_converted_to_money) {
            return $this->rent_amount;
        }

        return 0;
    }

    /**
     * Check if this is a money payment.
     */
    public function isMoneyPayment()
    {
        return $this->payment_type === 'money';
    }

    /**
     * Check if this is a nature payment.
     */
    public function isNaturePayment()
    {
        return $this->payment_type === 'nature';
    }

    /**
     * Check if this nature payment is converted to money.
     */
    public function isConvertedToMoney()
    {
        return $this->payment_type === 'nature' && $this->is_converted_to_money;
    }
}
