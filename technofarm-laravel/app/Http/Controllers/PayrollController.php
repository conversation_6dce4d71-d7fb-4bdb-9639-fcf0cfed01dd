<?php

namespace App\Http\Controllers;

use App\Http\Requests\GetOwnerPayrollRequest;
use App\Http\Requests\GetOwnersPayrollRequest;
use App\Http\Requests\GetOwnerPaymentsRequest;
use App\Http\Requests\GetContractPaymentsRequest;
use App\Http\Resources\PayrollResource;
use App\Http\Resources\PayrollCollection;
use App\Services\PayrollService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Controller for payroll functionality.
 * Provides API endpoints for the migrated payroll system.
 */
class PayrollController extends Controller
{
    public function __construct(
        private PayrollService $payrollService
    ) {}

    /**
     * Get owner payroll data.
     * Migrated from PaymentsController::getOwnerPayroll().
     * 
     * @param GetOwnerPayrollRequest $request
     * @param int $ownerId
     * @return JsonResponse
     */
    public function getOwnerPayroll(GetOwnerPayrollRequest $request, int $ownerId): JsonResponse
    {
        try {
            $validated = $request->validated();
            
            $result = $this->payrollService->getOwnerPayroll(
                year: $validated['year'],
                ownerId: $ownerId,
                path: $validated['path'] ?? null,
                filterParams: $validated['filters'] ?? []
            );

            return response()->json([
                'success' => true,
                'data' => new PayrollCollection($result),
                'message' => 'Owner payroll data retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve owner payroll data',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get owners payroll with pagination.
     * Migrated from PaymentsController::getOwnersPayroll().
     * 
     * @param GetOwnersPayrollRequest $request
     * @return JsonResponse
     */
    public function getOwnersPayroll(GetOwnersPayrollRequest $request): JsonResponse
    {
        try {
            $validated = $request->validated();
            
            $result = $this->payrollService->getOwnersPayroll(
                year: $validated['year'],
                filterParams: $validated['filters'] ?? [],
                rows: $validated['rows'] ?? null,
                page: $validated['page'] ?? null,
                returnFlatOwners: $validated['return_flat_owners'] ?? false
            );

            return response()->json([
                'success' => true,
                'data' => [
                    'rows' => new PayrollCollection($result['rows']),
                    'total' => $result['total'],
                    'footer' => $result['footer']
                ],
                'message' => 'Owners payroll data retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve owners payroll data',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get owner payments.
     * Migrated from PaymentsController::getOwnerPayments().
     * 
     * @param GetOwnerPaymentsRequest $request
     * @param int $ownerId
     * @return JsonResponse
     */
    public function getOwnerPayments(GetOwnerPaymentsRequest $request, int $ownerId): JsonResponse
    {
        try {
            $validated = $request->validated();
            
            $result = $this->payrollService->getOwnerPayments(
                year: $validated['year'],
                ownerId: $ownerId
            );

            return response()->json([
                'success' => true,
                'data' => [
                    'rows' => new PayrollCollection($result['rows']),
                    'total' => $result['total'],
                    'footer' => $result['footer']
                ],
                'message' => 'Owner payments retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve owner payments',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get contract payments.
     * Migrated from PaymentsController::getContractPayments().
     * 
     * @param GetContractPaymentsRequest $request
     * @param int $contractId
     * @param int|null $annexId
     * @return JsonResponse
     */
    public function getContractPayments(
        GetContractPaymentsRequest $request, 
        int $contractId, 
        ?int $annexId = null
    ): JsonResponse {
        try {
            $validated = $request->validated();
            
            $result = $this->payrollService->getContractPayments(
                year: $validated['year'],
                contractId: $contractId,
                annexId: $annexId,
                page: $validated['page'] ?? null,
                rows: $validated['rows'] ?? null,
                sort: $validated['sort'] ?? null,
                order: $validated['order'] ?? null
            );

            return response()->json([
                'success' => true,
                'data' => [
                    'rows' => new PayrollCollection($result['rows']),
                    'total' => $result['total'],
                    'footer' => $result['footer']
                ],
                'message' => 'Contract payments retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve contract payments',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get payroll statistics.
     * Additional endpoint for dashboard/reporting purposes.
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getPayrollStatistics(Request $request): JsonResponse
    {
        try {
            $year = $request->input('year', date('Y'));
            $filterParams = $request->input('filters', []);

            // This would be implemented to provide summary statistics
            $statistics = [
                'total_owners' => 0,
                'total_contracts' => 0,
                'total_plots' => 0,
                'total_rent_amount' => 0,
                'total_paid_amount' => 0,
                'total_unpaid_amount' => 0,
                'payment_percentage' => 0,
            ];

            return response()->json([
                'success' => true,
                'data' => $statistics,
                'message' => 'Payroll statistics retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve payroll statistics',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export payroll data.
     * Additional endpoint for data export functionality.
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function exportPayrollData(Request $request): JsonResponse
    {
        try {
            $year = $request->input('year', date('Y'));
            $format = $request->input('format', 'excel'); // excel, csv, pdf
            $filterParams = $request->input('filters', []);

            // This would be implemented to export data in various formats
            $exportUrl = '/exports/payroll_' . $year . '_' . time() . '.' . $format;

            return response()->json([
                'success' => true,
                'data' => [
                    'export_url' => $exportUrl,
                    'format' => $format,
                    'year' => $year
                ],
                'message' => 'Payroll data export initiated successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to export payroll data',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
