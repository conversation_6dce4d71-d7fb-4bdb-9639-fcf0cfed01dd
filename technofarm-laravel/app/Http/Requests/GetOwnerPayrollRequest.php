<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class GetOwnerPayrollRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Authorization would be handled by middleware
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'year' => ['required', 'integer', 'min:2000', 'max:' . (date('Y') + 10)],
            'path' => ['nullable', 'string', 'max:255'],
            'filters' => ['nullable', 'array'],
            'filters.no_contract_payments' => ['nullable', 'boolean'],
            'filters.payroll_ekate' => ['nullable', 'array'],
            'filters.payroll_ekate.*' => ['string', 'max:10'],
            'filters.payroll_farming' => ['nullable', 'array'],
            'filters.payroll_farming.*' => ['integer', 'min:1'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'year.required' => 'The year field is required.',
            'year.integer' => 'The year must be an integer.',
            'year.min' => 'The year must be at least 2000.',
            'year.max' => 'The year cannot be more than ' . (date('Y') + 10) . '.',
            'path.string' => 'The path must be a string.',
            'path.max' => 'The path cannot exceed 255 characters.',
            'filters.array' => 'The filters must be an array.',
            'filters.payroll_ekate.array' => 'The EKATTE filter must be an array.',
            'filters.payroll_ekate.*.string' => 'Each EKATTE code must be a string.',
            'filters.payroll_ekate.*.max' => 'Each EKATTE code cannot exceed 10 characters.',
            'filters.payroll_farming.array' => 'The farming filter must be an array.',
            'filters.payroll_farming.*.integer' => 'Each farming ID must be an integer.',
            'filters.payroll_farming.*.min' => 'Each farming ID must be at least 1.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Convert string 'true'/'false' to boolean for no_contract_payments
        if ($this->has('filters.no_contract_payments')) {
            $this->merge([
                'filters' => array_merge($this->input('filters', []), [
                    'no_contract_payments' => filter_var(
                        $this->input('filters.no_contract_payments'),
                        FILTER_VALIDATE_BOOLEAN,
                        FILTER_NULL_ON_FAILURE
                    )
                ])
            ]);
        }
    }
}
