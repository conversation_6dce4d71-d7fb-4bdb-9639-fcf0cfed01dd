<?php
require_once('../config/global.config.php');
require_once('../engine/Plugins/Core/Farming/conf.php');

//user data
$user_id = 778;
//$user_id = 10;
$user_database = 'db_et_elia';
$stub_table = 'import_data_elia_preotdadeni';

$contract_types = array(
    'собственост' => 1,
    'Собствен' => 1,
    'аренда' => 2,
    'аренд' => 2,
    'Аренден договор' => 2,
    'наем' => 3,
    'споразумение' => 4,
    'съвместна обработка' => 5
);

//technofarm settings (login 3)
$dbhDevMain = new PDO("pgsql:host=" . DBLINK_HOST . ";port=" . DBLINK_PORT . ";dbname=".DBLINK_DATABASE.";", DBLINK_USERNAME, DBLINK_PASSWORD);
$dbhDev = new PDO("pgsql:host=" . DBLINK_HOST . ";port=" . DBLINK_PORT . ";dbname=".$user_database.";", DBLINK_USERNAME, DBLINK_PASSWORD);

//techno settings (4 testing)
//$dbhDevMain = new PDO("pgsql:host=" . DEFAULT_DB_HOST . ";port=" . DEFAULT_DB_PORT . ";dbname=".DEFAULT_DB_DATABASE.";", DEFAULT_DB_USERNAME, DBLINK_PASSWORD);
//$dbhDev = new PDO("pgsql:host=" . DEFAULT_DB_HOST . ";port=" . DEFAULT_DB_PORT . ";dbname=".$user_database.";", DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

//technofarm settings (login 1)
// $dbhDevMain = new PDO("pgsql:host=***************;port=" . DEFAULT_DB_PORT . ";dbname=".DEFAULT_DB_DATABASE.";", DEFAULT_DB_USERNAME, 'hyxQ9SUIWwG1');
// $dbhDev = new PDO("pgsql:host=***************;port=" . DEFAULT_DB_PORT . ";dbname=".$user_database.";", DEFAULT_DB_USERNAME, 'hyxQ9SUIWwG1');

$sql = $dbhDevMain->prepare(
    "SELECT id, name from su_users_farming where user_id = {$user_id}"
);

$sql->execute();
$farmings = $sql->fetchAll();

$sql = $dbhDev->prepare(
    "SELECT DISTINCT ON (f2.c_num) f2.*
         from {$stub_table} f2"
);

if(!$sql->execute())
{
    $error = $sql->errorInfo();
    var_export($error[2]);
    error_import_log('ERROR: ' . $error[2] .' : ' . date("Y-m-d h:i:s",time()));
}

$results = $sql->fetchAll();

error_import_log('Process started on user ' . $user_database .' : ' . date("Y-m-d h:i:s",time()));

foreach ($results as $contract)
{
    if($contract['c_num'] == NULL || $contract['c_num'] == '')
    {
        continue;
    }
    
    foreach ($contract_types as $key => $type)
    {
        if(strstr(strtolower($contract['nm_usage_rights']), $key) || $contract['nm_usage_rights'] == $key)
        {
            $contract['nm_usage_rights'] = $type;
            break;
        }    
    }

    if(!$contract['nm_usage_rights'])
    {
        $contract['nm_usage_rights'] = 1;
    }

    foreach ($farmings as $farmKey => $farmVal)
    {
        if(strtolower($farmVal['name']) == strtolower($contract['owner_farming']))
        {
           $contract['owner_farming_id'] =  $farmVal['id'];
        }
    }
    
    //insert data into contract table
    insertContract($dbhDev, $contract);

    //get the contract id
    $lastInsertedContractId = $dbhDev->lastInsertId('su_contracts_id_seq');

    $contract_plots = $dbhDev->prepare(
        "SELECT f2.*, kvs.gid from {$stub_table} f2 LEFT JOIN layer_kvs kvs on (f2.katident = kvs.kad_ident) where f2.c_num='{$contract['c_num']}'"
    );

    $contract_plots->execute();
    $plots = $contract_plots->fetchAll();
    $isValid = true;

    foreach ($plots as $plotKey => $plot)
    {
        if($plot['gid'] == NULL)
        {
            //log bad plot  
            error_import_log("ERR - Plot not existing in kvs_layer; kadident:" . $plot['katident'] . ' contract_num: ' . $plot['c_num']);
            continue;
        }
        
        $farming_id = getFarming($dbhDevMain, $plot['owner_farming']);

        if(!$farming_id)
        {
            continue;
        }

        $check_subleases_contract_plot_rel_exists = $dbhDev->prepare(
            " SELECT c_p_r.*
            FROM su_contracts_plots_rel c_p_r
            INNER JOIN su_plots_farming_rel p_f_r on (p_f_r.pc_rel_id = c_p_r.id) 
            INNER JOIN layer_kvs kvs on (c_p_r.plot_id = kvs.gid)
            WHERE kvs.kad_ident = :katident
            AND p_f_r.farming_id = :farming_id ");

        $check_subleases_contract_plot_rel_exists->bindValue(':katident', $plot['katident']);
        $check_subleases_contract_plot_rel_exists->bindValue(':farming_id', $farming_id);
        $check_subleases_contract_plot_rel_exists->execute();
        
        $check_subleases_contract_plot_rel_exists_result = $check_subleases_contract_plot_rel_exists->fetchAll();

        $pc_rel_id = $check_subleases_contract_plot_rel_exists_result[0]['id'];

        if(empty($pc_rel_id))
        {
        	error_import_log("ERR - pc_rel_id is NULL! katident: " . $plot['katident'] . " , farming_id: " . $farming_id);
        	continue;
        }

        //insert data into subleases_contracts_plots_rel table
        insertSubleasesPlotsContractsRel($dbhDev, $lastInsertedContractId, $pc_rel_id);

        //insert data into subleases_plots_area table
        insertSubleasesPlotsArea($dbhDev, $lastInsertedContractId, $plot['gid'], $plot['document_area']);

        if($isValid)
        {
        	$farming_id = getFarming($dbhDevMain, $plot['owner_name']);
    		$isValid = false;

		    if($farming_id)
		    {
		        //insert data into contracts_farming_contragent table
		        insertContractsFarmingContragent($dbhDev, $lastInsertedContractId, $farming_id);
		    }
		    else
		    {
		        //insert data into contracts_contragent table
		        $plot['egn_eik_farming'] = NULL;
		        $contragent_id = getOwnerId($dbhDev, $plot);

		        insertContractsContragent($dbhDev, $lastInsertedContractId, $contragent_id);
		    }
        } 
    }
}

error_import_log('Process ednded: ' . date("Y-m-d h:i:s",time()));
echo 'Process ended';

function insertContract($dbhDev, $contract)
{
    $insrt_sql = $dbhDev->prepare(
        "INSERT INTO su_contracts (
            c_num,
            c_date,
            nm_usage_rights,
            sv_num,
            sv_date,
            start_date,
            renta,
            due_date ,
            renta_nat,
            farming_id,
            comment,
            active,
            parent_id,
            is_annex,
            renta_nat_type_id,
            is_sublease
        )
        VALUES (
            :c_num,
            :c_date,
            :nm_usage_rights,
            :sv_num ,
            :sv_date,
            :start_date,
            :renta,
            :due_date,
            :renta_nat,
            :farming_id,
            :comment,
            :active,
            :parent_id,
            :is_annex,
            :renta_nat_type_id,
            :is_sublease
        )
    ");

    $insrt_sql->bindValue(':c_num', $contract['c_num']);
    $insrt_sql->bindValue(':c_date', $contract['c_date']);

    $insrt_sql->bindValue(':nm_usage_rights', $contract['nm_usage_rights']);
    $insrt_sql->bindValue(':sv_num', $contract['sv_num']);
    $insrt_sql->bindValue(':sv_date', $contract['sv_date']);

    $insrt_sql->bindValue(':start_date', $contract['start_date']);

    $insrt_sql->bindValue(':renta', $contract['renta']);
    $insrt_sql->bindValue(':due_date', $contract['due_date']);

    $insrt_sql->bindValue(':renta_nat', $contract['renta_nat']);
    $insrt_sql->bindValue(':farming_id', $contract['owner_farming_id']);
    $insrt_sql->bindValue(':comment', $contract['comment']);

    $insrt_sql->bindValue(':active', TRUE, PDO::PARAM_BOOL);
    $insrt_sql->bindValue(':parent_id', 0);
    $insrt_sql->bindValue(':is_annex', FALSE, PDO::PARAM_BOOL);
    $insrt_sql->bindValue(':renta_nat_type_id', 0);
    $insrt_sql->bindValue(':is_sublease', TRUE, PDO::PARAM_BOOL);

    if(!$insrt_sql->execute())
    { 
        $error = $insrt_sql->errorInfo();
        var_export($error[2]);
        error_import_log('ERR failed to insert contract with c_num: ' . $contract['c_num']);
    }
}

function createOwner($dbhDev, $owner_type, $plot)
{
    if($plot['egn_eik_farming'])
    {
        $plot['egn_eik'] = $plot['egn_eik_farming'];
    }

    $owner_sql = "INSERT INTO su_owners (
                    name,
                    surname,
                    lastname,
                    egn,
                    company_name,
                    eik,
                    owner_type,
                    is_dead
                )";
    if($owner_type == 1)
    {
        $owner_sql = $owner_sql . "
        VALUES (
        :owner_name,
        :owner_surname,
        :familly,
        :egn_eik,
        NULL,
        NULL,
        :owner_type,
        :is_dead
        )";
    }else
    {
        $owner_sql = $owner_sql . "
        VALUES (
        NULL,
        NULL,
        NULL,
        NULL,
        :owner_name,
        :egn_eik,
        :owner_type,
        :is_dead
        )";
    }

    $create_owner_sql = $dbhDev->prepare($owner_sql);

    if(is_array($plot['owner_name']))
    {
    	$create_owner_sql->bindValue(':owner_name', $plot['owner_name'][0] ?: NULL);
	    $create_owner_sql->bindValue(':owner_surname', $plot['owner_name'][1] ?: NULL);
	    $create_owner_sql->bindValue(':familly', $plot['owner_name'][2] ?: NULL);
    }
    else
    {
    	$create_owner_sql->bindValue(':owner_name', $plot['owner_name'] ?: NULL);
    }

    $create_owner_sql->bindValue(':owner_type', $owner_type);
    $create_owner_sql->bindValue(':egn_eik', $plot['egn_eik']);
    $create_owner_sql->bindValue(':is_dead', FALSE, PDO::PARAM_BOOL);

    if(!$create_owner_sql->execute())
    {
        $error = $create_owner_sql->errorInfo();
        var_export($error[2]);
        error_import_log('ERR failed owner creation with egn/eik: ' . $params['egn_eik'] . ' ,Name: ' . $params['owner_name']);
    }
}

function createFarming($dbhDevMain, $plot, $user_id)
{
    $ownerFarming = $plot['owner_farming'];
    $isSystem = false;

    if($ownerFarming == 'Основно стопанство')
    {
        $isSystem = true;
    }

    $insrt_sql = $dbhDevMain->prepare(
        "INSERT INTO su_users_farming (
            user_id,
            name,
            is_system,
            group_id
        )
        VALUES (
            :user_id,
            :name,
            :is_system,
            :group_id
        )
    ");

    $insrt_sql->bindValue(':user_id', $user_id);
    $insrt_sql->bindValue(':name', $plot['name']);
    $insrt_sql->bindValue(':is_system', $isSystem, PDO::PARAM_BOOL);
    $insrt_sql->bindValue(':group_id', $user_id);

    if(!$insrt_sql->execute())
    {    
        $error = $insrt_sql->errorInfo();
        var_export($error[2]);
        error_import_log('ERROR: dont add farming ' . $plot['name']);
    }
}

function insertSubleasesPlotsContractsRel($dbhDev, $lastInsertedContractId, $pc_rel_id)
{
    $insrt_subleases_plots_contracts_rel_sql = $dbhDev->prepare(
        "INSERT INTO su_subleases_plots_contracts_rel (
        sublease_id,    
        pc_rel_id
    )
        VALUES (
        :sublease_id,
        :pc_rel_id
    )");

    $insrt_subleases_plots_contracts_rel_sql->bindValue(':sublease_id', $lastInsertedContractId);
    $insrt_subleases_plots_contracts_rel_sql->bindValue(':pc_rel_id', $pc_rel_id);

    if(!$insrt_subleases_plots_contracts_rel_sql->execute())
    {    
        $error = $insrt_subleases_plots_contracts_rel_sql->errorInfo();
        var_export($error[2]);
        error_import_log('ERR failed insert in table su_subleases_plots_contracts_rel pc_rel_id: ' . $pc_rel_id . ', subleases_id: ' . $lastInsertedContractId);
    }
}

function insertSubleasesPlotsArea($dbhDev, $lastInsertedContractId, $plot_id, $document_area)
{
    $document_area = str_replace(",",".", $document_area);
    $document_area = (float)$document_area;

    $insrt_subleases_plots_area_sql = $dbhDev->prepare(
        "INSERT INTO su_subleases_plots_area (
        sublease_id,    
        plot_id,
        contract_area
    )
        VALUES (
        :sublease_id,
        :plot_id,
        :contract_area
    )");

    $insrt_subleases_plots_area_sql->bindValue(':sublease_id', $lastInsertedContractId);
    $insrt_subleases_plots_area_sql->bindValue(':plot_id', $plot_id);
    $insrt_subleases_plots_area_sql->bindValue(':contract_area', $document_area);

    if(!$insrt_subleases_plots_area_sql->execute())
    {          
        $error = $insrt_subleases_plots_area_sql->errorInfo();
        var_export($error[2]);
        error_import_log('ERR failed insert in table su_subleases_plots_area subleases_id: ' . $lastInsertedContractId . ' plot_id: ' . $plot_id);
    }
}

function getOwnerId($dbhDev, $plot)
{
    if(strtolower($plot['owner_type']) == 'юл' || strlen($plot['egn_eik']) == 9 || strlen($plot['egn_eik_farming']) == 9)
    {
        $egn_eik_column = 'eik';
        $owner_type = 0;
 	}
 	else
 	{
	    $egn_eik_column = 'egn';
	    $owner_type = 1;

	    $plot['owner_name'] = explode(' ', $plot['owner_name']);
        $ownerName = strtolower($plot['owner_name'][0]);
        $ownerSurname = strtolower($plot['owner_name'][1]);
        $ownerFamilly = strtolower($plot['owner_name'][2]);
	} 

    if($plot['egn_eik'] == 0 || $plot['egn_eik'] == '0' || !$plot['egn_eik'])
    {
        $check_owner_exists = $dbhDev->prepare(
        " select * from su_owners where
         lower(name) = '{$ownerName}'
            AND lower(surname) = '{$ownerSurname}'
                AND lower(lastname) = '{$ownerFamilly}' ");
    }else
    {
        $check_owner_exists = $dbhDev->prepare(
        " select * from su_owners where {$egn_eik_column} = '{$plot['egn_eik']}' ");
    }
    
    $check_owner_exists->execute();
    $check_owner_exists_result = $check_owner_exists->fetchAll();

    if(empty($check_owner_exists_result))
    {
        createOwner($dbhDev, $owner_type, $plot);
        $owner_id = $dbhDev->lastInsertId('su_owners_id_seq');
    }else{
        $owner_id = $check_owner_exists_result[0]['id'];
    }

    return $owner_id;
}

function insertContractsContragent($dbhDev, $lastInsertedContractId, $contragent_id)
{
    $insrt_contracts_contragent_sql = $dbhDev->prepare(
        "INSERT INTO su_contracts_contragents (
        contract_id,    
        owner_id,
        rep_id
    )
        VALUES (
        :contract_id,
        :owner_id,
        :rep_id
    )");

    $insrt_contracts_contragent_sql->bindValue(':contract_id', $lastInsertedContractId);
    $insrt_contracts_contragent_sql->bindValue(':owner_id', $contragent_id);
    $insrt_contracts_contragent_sql->bindValue(':rep_id', 1);

    if(!$insrt_contracts_contragent_sql->execute())
    {    
        $error = $insrt_contracts_contragent_sql->errorInfo();
        var_export($error[2]);
        error_import_log('ERR failed insert in table su_contracts_contragents contract_id: ' . $lastInsertedContractId . ' owner_id: ' . $contragent_id);
    }
}

function insertContractsFarmingContragent($dbhDev, $lastInsertedContractId, $farming_id)
{
    $insrt_contracts_farming_contragent_sql = $dbhDev->prepare(
        "INSERT INTO su_contracts_farming_contragents (
        contract_id,    
        farming_id
    )
        VALUES (
        :contract_id,
        :farming_id
    )");

    $insrt_contracts_farming_contragent_sql->bindValue(':contract_id', $lastInsertedContractId);
    $insrt_contracts_farming_contragent_sql->bindValue(':farming_id', $farming_id);

    if(!$insrt_contracts_farming_contragent_sql->execute())
    {    
        $error = $insrt_contracts_farming_contragent_sql->errorInfo();
        var_export($error[2]);
        error_import_log('ERR failed insert in table su_contracts_farming_contragents contract_id: ' . $lastInsertedContractId . ' farming_id: ' . $farming_id);
    }
}

function getFarming($dbhDevMain, $farming)
{
    $farming = trim($farming);

    $check_farming_exists = $dbhDevMain->prepare(
                 " select * from su_users_farming where name = :farming");

    $check_farming_exists->bindValue(':farming', $farming);
    $check_farming_exists->execute();
    $check_farming_exists_result = $check_farming_exists->fetchAll();

    if(empty($check_farming_exists_result))
    {
         error_import_log("Missing farming: " .  $farming);
         $farming_id = NULL;
        //createFarming($dbhDevMain, $plot, $user_id);
        //$farming_id = $dbhDevMain->lastInsertId('su_users_farming_id_seq');
    }else{
        $farming_id = $check_farming_exists_result[0]['id'];
    }

    return $farming_id;
}

function error_import_log($error)
{
    $filename = 'contract_import_log.txt';
    $fd = fopen($filename, "a");
    
    fwrite($fd, $error . "\n");
    
    fclose($fd);
}

?>
