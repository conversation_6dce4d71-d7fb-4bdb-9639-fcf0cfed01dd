<?php 
namespace TF\Crons;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface; 
use Prado;

class CronDeleteDownloadedFilesCommand extends BaseCommand {

    protected function configure()
    {
        $this
            ->setName('crons:DeleteDownloadedFiles')
            ->setDescription('Delete Downloaded Files');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $allowedFormats = array('pdf', 'doc','csv', 'xls', 'zip');
        $allowedPaths = [
                PUBLIC_UPLOAD_EXPORT,
                PUBLIC_UPLOAD_BLANK,
                PUBLIC_UPLOAD_COOPERATOR,
                PUBLIC_UPLOAD_HISTORY
        ];
        $tmp_path = SITE_PATH . 'public/' . PUBLIC_CONTRACTS_RELATIVE_PATH . '/blanks/';
        $results = scandir($tmp_path);

        foreach ($results as $result) {
            if ($result === '.' or $result === '..') {
                continue;
            }

            if (is_dir($tmp_path . '/' . $result)) {
                $allowedPaths[] = $tmp_path . '/' . $result;
            }
        }

        $tmp_path = PUBLIC_UPLOAD_EXPORT . '/farmings/';
        $results = scandir($tmp_path);

        foreach ($results as $result) {
            if ($result === '.' or $result === '..') {
                continue;
            }

            if (is_dir($tmp_path . '/' . $result)) {
                $allowedPaths[] = $tmp_path . '/' . $result;
            }
        }

        foreach ($allowedPaths as $path) {
            if (file_exists($path)) {
                if ($handle = opendir($path)) {
                    while (($file = readdir($handle)) !== false ) {
                        if ((time()-filectime($path.'/'.$file)) >= 3600) {  
                            $fileInfo = pathinfo($file);
                            if (in_array($fileInfo['extension'], $allowedFormats))
                            {
                                @unlink($path.'/'.$file);
                            }
                        }
                    }
                }
            }
        }    
                
    }
}
