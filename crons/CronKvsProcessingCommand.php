<?php 
 namespace TF\Crons;
 
 use Prado\Prado;
 use Symfony\Component\Console\Command\Command as BaseCommand;
 use Symfony\Component\Console\Input\InputArgument;
 use Symfony\Component\Console\Input\InputInterface;
 use Symfony\Component\Console\Input\InputOption;
 use Symfony\Component\Console\Output\OutputInterface;
 use TF\Engine\Kernel\CronsLoadData\KvsOszProcessingClass;
use TF\Engine\Kernel\CronsLoadData\KvsProcessingClass;

class CronKvsProcessingCommand extends BaseCommand {

    protected function configure()
    {
        $this
            ->setName('crons:KvsProcessing')
            ->setDescription('Zarejdane na KVS');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        /** @var KvsProcessingClass $objKvsProcessing */
        $objKvsProcessing = Prado::getApplication()->getModule('KvsProcessingClass');

        $objKvsProcessing->startProcessing();
    }
}
