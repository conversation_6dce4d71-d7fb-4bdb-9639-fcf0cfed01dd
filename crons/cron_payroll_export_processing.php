<?php

use Prado\Prado;
use Prado\Web\Services\TJsonRpcProtocol;
use TF\Engine\APIClasses\Payroll\PayrollGridExportAndPrint;
use TF\Engine\Plugins\Core\Users\UsersController;

include 'shell.php';
include '../vendor/pradosoft/prado/framework/Web/Services/TRpcService.php';
Prado::using('APIClasses.Payroll.*');

//$UsersController = getPluginInstance('Plugins.Core.Users');
$UsersController = new UsersController();


$data = $UsersController->getPayrollExprotsForProcessing();

if (count($data) == 0) { //No agreements for processing are found. Terminate the script
    die;
}
$application = Prado::getApplication();
$auth = $application->getModule('auth');

for($i = 0; $i < count($data); $i++) {

    $auth->switchUser($data[$i]['username']);
    $UsersController->setPayrollExportInProcess($data[$i]['id']);
    $server = new TRpcServer(new TJsonRpcProtocol);
    $payrollGrid = new PayrollGridExportAndPrint($server);
    $startTime = time();
    $rpcParams = json_decode($data[$i]['filter_params'], true);
    $result = $payrollGrid->exportToExcelPayrollGrid($rpcParams, $data[$i]['database'], $data[$i]['filename']);

    $filename = substr($result, -23);
    $path = PAYROLL_EXPORTS_PATH . '/' . $application->User->UserID . '/';
    rename($path.$filename, $path.$data[$i]['filename']);
    $endTime = time();
    $execution_time = $endTime - $startTime;
    $UsersController->setPayrollExportAsProcessed($data[$i]['id'], $execution_time);
}