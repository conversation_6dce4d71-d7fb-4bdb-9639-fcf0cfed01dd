<?php

namespace TF\Crons;

use Exception;
use Symfony\Component\Console\Command\Command as Command;
use Symfony\Component\Console\Input\ArrayInput;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\BufferedOutput;
use Symfony\Component\Console\Output\OutputInterface;
use TF\Engine\Kernel\Mail;

class CheckDBIntegrityCommand extends Command
{
    private $startTimestamp;
    private $endTimestamp;

    private $reportTypes = [
        1 => 'Missing columns',
        2 => 'Missing functions',
    ];

    protected function configure()
    {
        $this
            ->setName('crons:CheckDatabasesIntegrityCommand')
            ->setDescription('Check DB Integrity and send email if any missing columns or function found');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        try {
            $this->startTimestamp = time();

            $command = $this->getApplication()->find('tf:check_db_integrity');

            foreach ($this->reportTypes as $type => $reportType) {
                $commandInput = new ArrayInput(
                    [
                        'user_databases' => [],
                        '--type' => $type,
                        '--mail_format' => 1,
                    ]
                );

                $bufferedOutput = new BufferedOutput();
                $command->run($commandInput, $bufferedOutput);

                $this->endTimestamp = time();

                $response = $bufferedOutput->fetch();
                $resposneLen = strlen($response);
                if ($resposneLen > 0) {
                    $executionsInfo = '
                    Start time: ' . date('Y-m-d H:i:s', $this->startTimestamp) . '<br>
                    End time: ' . date('Y-m-d H:i:s', $this->endTimestamp) . '<br>
                    Execution time: ' . ($this->endTimestamp - $this->startTimestamp) . ' seconds<br><br>';

                    $this->sendEmail($executionsInfo . $response, 'Scheme differences in client databases:' . $reportType);
                }
            }
        } catch (Exception $e) {
            echo print_r($e->getMessage(), true) . "\n";
        }
    }

    private function sendEmail($message, $subject)
    {
        $mail = new Mail();
        if (getenv('ALARMS_MAIL')) {
            $mail->sendMail(getenv('ALARMS_MAIL'), $subject, $message, 'TechnoFarm');
        }
    }
}
