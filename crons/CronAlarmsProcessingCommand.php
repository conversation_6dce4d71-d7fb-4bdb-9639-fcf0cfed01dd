<?php 
 namespace TF\Crons;
 use Symfony\Component\Console\Command\Command as BaseCommand;
 use Symfony\Component\Console\Input\InputArgument;
 use Symfony\Component\Console\Input\InputInterface;
 use Symfony\Component\Console\Input\InputOption;
 use Symfony\Component\Console\Output\OutputInterface; 
 use Prado;

 class CronAlarmsProcessingCommand extends BaseCommand {

    protected function configure()
    {
        $this
            ->setName('crons:AlarmProcessing')
            ->setDescription('Get notifications from alarms');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {

    }
}
