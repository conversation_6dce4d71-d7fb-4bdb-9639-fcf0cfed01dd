<?php 
namespace TF\Crons;

use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface; 
use Prado;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\Users\UsersController;

class CronCroplayerProcessingCommand extends BaseCommand {

    protected function configure()
    {
        $this
            ->setName('crons:CroplayerProcessing')
            ->setDescription('Croplayer processing');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $UsersController = new UsersController('Users');
        $LayersController = new LayersController('Layers');

        $data = $UsersController->getCropLayerForProcessing();

        for ($i = 0; $i < count($data); $i++)
        {
            try
            {
                $layerData = $data[$i];
                $UsersDbController = new UserDbController($layerData['database']);
                $filePath = LAYERS_QUEUE_PATH . 'crop_layer_' . $layerData['id'] . '.csv';
                $status = 1;

                if (is_file($filePath))
                {
                    $rows = array_map('str_getcsv', file($filePath));
                    $header = array_shift($rows);
                    $csv = array();
                    foreach ($rows as $row)
                    {
                        $csv[] = explode(";", $row[0]);
                    }
                } else
                {
                    $status = 2;
                }

                if (count($csv[0]) != 8)
                    $status = 2;
                else
                {

                    $options = array(
                        'tablename' => $UsersDbController->DbHandler->cropLayersTable,
                        'where' => array(
                            'id' => array('column' => 'id', 'compare' => '=', 'value' => $layerData['item_id'])
                        )
                    );

                    $cropLayerData = $UsersDbController->getItemsByParams($options);
                    $farming = $cropLayerData[0]['farming'];
                    $year = $cropLayerData[0]['year'];
                    $user_id = $layerData['group_id'];
                    $layer_type = 6;

                    $options = array();
                    $options['farming'] = $farming;
                    $options['year'] = $year;
                    $options['user_id'] = $user_id;
                    $options['layer_type'] = $layer_type;
                    $options['return'] = array('table_name');
                    $ldata = $LayersController->getLayersIdByParams($options);

                    $LayerIsakTableName = $ldata['table_name'];

                    if (!$LayerIsakTableName)
                    {
                        $status = 2;
                    } else
                    {
                        for ($i = 0; $i < count($csv); $i++)
                        {
                            $tableExists = $UsersDbController->getTableNameExist($LayerIsakTableName);

                            if ($tableExists)
                            {
                                $hasNumber = $UsersDbController->getIsakLayerNumberExist($LayerIsakTableName, $csv[$i][1]);
                            } else
                            {
                                $hasNumber = false;
                            }

                            $options['tablename'] = $UsersDbController->DbHandler->cropLayersDataTable;
                            $options['mainData'] = array(
                                'crop_layer_id' => $layerData['item_id'],
                                'plot' => (int) $csv[$i][0],
                                'isak_number' => $csv[$i][1],
                                'area' => (float) $csv[$i][2],
                                'culture1' => (int) $csv[$i][3],
                                'culture2' => (int) $csv[$i][4],
                                'culture3' => (int) $csv[$i][5],
                                'culture4' => (int) $csv[$i][6],
                                'culture5' => (int) $csv[$i][7],
                                'has_number' => (int) $hasNumber
                            );
                            $UsersDbController->addItem($options);

                            if (!$hasNumber)
                                $status = 3;
                        }
                    }
                }

                $options['mainData'] = array('status' => $status);
                $options['where'] = array('id' => $layerData['item_id']);
                $options['tablename'] = $UsersDbController->DbHandler->cropLayersTable;
                $UsersDbController->editItem($options);

                $UsersController->setCropLayerProcessed($layerData['id']);

                //catch
            } catch (\Exception $e)
            {
                echo($e);
            }
        }
        
    }
}
