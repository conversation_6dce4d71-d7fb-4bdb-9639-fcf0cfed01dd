<?php

use TF\Engine\Plugins\Core\UserDb\UserDbController;

include 'shell.php';

$database = $argv[1];
$fileId = $argv[2];
$filePath = $argv[3];
$allowedFields = array(
    'ID_IMOT',
    'KVS_NO',
    'KAD_NO',
    'KOD_SUBEKT',
    'TXT_SUBEKT',
    'IME_SUBEKT',
    'EGN_SUBEKT',
    'KOD_PR_OSN',
    'TXT_PR_OSN',
    'PL_DKA',
    'PL_DKA_PO',
    'KATEGORIA',
    'KOD_NTP',
    'TXT_NTP',
    'KOD_SOBSTV',
    'TXT_SOBSTV',
    'KOD_IMOT',
    'TXT_IMOT',
    'EKATTE',
    'VER_NO',
    'DATA',
    'VREME');


if(!$database || !$fileId || !$filePath) {
    die;
}


// open in read-only mode
$db = dbase_open($filePath, 0);
if (!$db) {
    die;
}
$UsersDbController = new UserDbController($database);
//$UsersDbController = getPluginInstanceUserDb($database);
$record_numbers = dbase_numrecords($db);
$dbRows = array();
$ekatte = null;

for ($i = 1; $i <= $record_numbers; $i++) {
    $row = dbase_get_record_with_names($db, $i);
    if ($ekatte == null) {
        $ekatte = $row['EKATTE'];
    }

    $dbRow = getRow($row, $allowedFields);
    $dbRow['file_id'] = $fileId;
    $dbRows[] = $dbRow;
}

$usedColumns = array_keys(array_merge(array(), $dbRows[0], array("file_id" => $fileId)));
$usedColumnsStr = implode(', ', $usedColumns);
$options = array(
    'tablename' => $UsersDbController->DbHandler->tableOSZFilesPlots,
    'columns' => $usedColumnsStr,
    'values' => $dbRows
);
$UsersDbController->addItems($options);

$UsersDbController->deleteDuplicatedRecordsFromOSZ($ekatte);
$UsersDbController->refreshTopicLayerKVSViews();
$UsersDbController->refreshOszEkateCombobox();
@unlink($filePath);


function getRow($data, $allowedFields)
{
    $row = array();
    foreach ($data as $key => $value) {
        if (!in_array($key, $allowedFields)) {
            continue;
        }
        $columnName = strtolower($key);
        $row[$columnName] = trim(mb_convert_encoding($value, 'UTF-8', 'CP1251'));
    }

    return $row;
}