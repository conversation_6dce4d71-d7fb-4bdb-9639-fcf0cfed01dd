<?php 
 namespace TF\Crons;
 
 use Prado\Prado;
 use Symfony\Component\Console\Command\Command as BaseCommand;
 use Symfony\Component\Console\Input\InputArgument;
 use Symfony\Component\Console\Input\InputInterface;
 use Symfony\Component\Console\Input\InputOption;
 use Symfony\Component\Console\Output\OutputInterface;
 use TF\Engine\Kernel\CronsLoadData\KvsProcessingClass;
 use TF\Engine\Kernel\CronsLoadData\LayerProcessingClass;

class CronLayerProcessingCommand extends BaseCommand {

    protected function configure()
    {
        $this
            ->setName('crons:LayerProcessing')
            ->setDescription('Zarejdane na sloevete: Danni ot komasaciq, Ot <PERSON>k, Za <PERSON>k, <PERSON><PERSON><PERSON><PERSON> raster, Danni ot komasaciq ot drugi softueri');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        /** @var LayerProcessingClass $objLayerProcessing */
        $objLayerProcessing = Prado::getApplication()->getModule('LayerProcessingClass');

        $objLayerProcessing->startProcessing();
    }
}
