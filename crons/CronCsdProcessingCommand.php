<?php

namespace TF\Crons;

use Prado\Prado;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use TF\Engine\Kernel\CronsLoadData\CsdProcessingClass;

class CronCsdProcessingCommand extends BaseCommand
{
    protected function configure()
    {
        $this
            ->setName('crons:CsdProcessing')
            ->setDescription('Process uploaded files for consolidation source data.');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        /** @var CsdProcessingClass $csdProcessing  */
        $csdProcessing = Prado::getApplication()->getModule('CsdProcessingClass');

        $csdProcessing->startProcessing();
    }
}
