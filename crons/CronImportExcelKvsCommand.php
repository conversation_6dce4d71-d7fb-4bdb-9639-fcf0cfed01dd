<?php

namespace TF\Crons;

use Prado\Prado;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use TF\Engine\Kernel\CronsLoadData\ImportExcelKvsClass;

/**
 * Class CronImportExcelKvsCommand
 * @package TF\Crons
 *
 * @property ImportExcelKvsClass $serviceClass
 */
class CronImportExcelKvsCommand extends BaseCommand
{

    protected $serviceClass;

    protected function configure()
    {
        $this
            ->setName('crons:ImportExcelKvs')
            ->setDescription('Зареждане данните от Excel в един слой.');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        /** @var ImportExcelKvsClass $this->serviceClass */
        $this->serviceClass = Prado::getApplication()->getModule('ImportExcelKvsClass');

        $auth = Prado::getApplication()->getModule('auth');
        $mapTools = makeApiClass('map-rpc','map-tools');

        return $this->serviceClass->startProcessing($mapTools, $auth);
    }
}
