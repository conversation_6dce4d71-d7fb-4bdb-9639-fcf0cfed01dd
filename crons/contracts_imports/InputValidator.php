<?php

/**
 * Class InputValidator
 * 
 * @property PDO $mainConnection
 * @property PDO $userConnection
 * @property string $stubTable
 * @property int $userId
 * @property string $logFileName
 */
class InputValidator
{
    private $mainConnection;
    private $userConnection;
    private $stubTable;
    private $userId;
    private $logFileName;

    /**
     * InputValidator constructor.
     * @param array $param
     */
    public function __construct(array $param)
    {
        $this->mainConnection = new PDO('pgsql:host=' . $param['databaseCredentials']['dbHost'] . ';port=' . $param['databaseCredentials']['dbPort'] . ';dbname=' . $param['databaseCredentials']['mainDatabase'] . ';', $param['databaseCredentials']['dbUsername'], $param['databaseCredentials']['dbPassword']);
        $this->userConnection = new PDO('pgsql:host=' . $param['databaseCredentials']['dbHost'] . ';port=' . $param['databaseCredentials']['dbPort'] . ';dbname=' . $param['databaseCredentials']['userDatabase'] . ';', $param['databaseCredentials']['dbUsername'], $param['databaseCredentials']['dbPassword']);
        $this->stubTable      = $param['databaseCredentials']['stubTable'];
        $this->userId         = $param['user_id'];
        $this->logFileName    = $param['logFile'];
    }

    /**
     * @return bool
     */
    public function validateContractData()
    {
        $command = $this->userConnection->prepare(
            "SELECT c_num, nm_usage_rights, start_date, katident, c_date, owner_farming, contract_area, document_area from {$this->stubTable} as st WHERE 
         c_num is null OR nm_usage_rights is null OR start_date is null OR katident is null OR 
         c_date is null OR owner_farming is null OR contract_area is null;"
        );
        $command->execute();
        $invalidData = $command->fetchAll(PDO::FETCH_ASSOC);
        if (!empty($invalidData)) {
            //$this->errorImportLog('Записи с липсващи данни за номер на имот, тип договор, начална дата, дата на сключване, стопанство, площ по договор или площ по документ:');
            $this->errorImportLog('Records with missing data for property number, contract type, start date, date of conclusion, farm, area under contract or area per document:');
            foreach ($invalidData as $row) {
                foreach ($row as $field => $value) {
                    if ($value == null) {
                        $this->errorImportLog('WARNING plot with кадидент: ' . $row['katident'] . ' with contract number ' . $row['c_num'] . ' has this field null ' . $field);
                    }
                }
            }
            return false;
        }
        $this->errorImportLog('contracts data: ok');
        return true;
    }

    /**
     * @return bool
     */
    public function validateContractDates()
    {
        $command = $this->userConnection->prepare("SELECT katident from {$this->stubTable} as st WHERE due_date < start_date;");
        $command->execute();
        $incorrectDatesData = $command->fetchAll(PDO::FETCH_ASSOC);
        //check where end / due_date start before start_date
        if (!empty($incorrectDatesData)) {
            //$this->errorImportLog('Записи с крайна дата преди началната:');
            $this->errorImportLog('End date entries before start:');
            foreach ($incorrectDatesData as $value) {
                $this->errorImportLog('WARNING Invalid due_date/start_date for plot: ' . $value['katident']);
            }
            return false;
        }
        $this->errorImportLog('contracts dates: ok');
        return true;
    }

    /**
     * @return bool
     */
    public function validateClientName()
    {
        //stopanstwa, sashtestvuvashti v akaunta;
        $command = $this->mainConnection->prepare('SELECT DISTINCT(name) FROM su_users_farming WHERE user_id = :user_id;');
        $command->bindValue('user_id', $this->userId);
        $command->execute();
        $userFarmings = $command->fetchAll(PDO::FETCH_ASSOC);
        $userFarmings = $this->arrayForQueryTransformFarming($userFarmings);
        //stopanstva v importvanata tablica / In the imported table
        $sql = $this->userConnection->prepare("SELECT * from {$this->stubTable} as st WHERE owner_farming NOT IN ({$userFarmings}) OR owner_farming is NULL;");
        $sql->execute();
        $missingFarmings = $sql->fetchAll(PDO::FETCH_ASSOC);
        $errors = array();
        if (!empty($missingFarmings)) {
            //$this->errorImportLog("Стопансва в акаунта: " . $UserFarmings);
            $this->errorImportLog('Holdings of the account: ' . $userFarmings);
            foreach ($missingFarmings as $value) {
                $errors[] = 'WARNING Липсващо стопанство: ' . $value['owner_farming'] . ' към имот: ' . $value['katident'];
            }
            $errors = array_unique($errors);
            asort($errors);
            foreach ($errors as $error) {
                $this->errorImportLog($error);
            }

            return false;
        }
        $this->errorImportLog('client farm name: ok');
        return true;
    }

    /**
     * @return bool
     */
    public function validatePlots()
    {
        $command = $this->userConnection->prepare(
            "SELECT DISTINCT(katident) from {$this->stubTable} as st WHERE katident NOT IN ( SELECT kad_ident from layer_kvs WHERE kad_ident IS NOT NULL ) OR katident is NULL;"
        );
        $command->execute();
        $missingPlots = $command->fetchAll(PDO::FETCH_ASSOC);
        if (!empty($missingPlots)) {
            asort($missingPlots);
            foreach ($missingPlots as $value) {
                if(empty($value['katident'])){
                    $this->errorImportLog('WARNING Null KadIdent for contract : ' . $value['c_num']);
                }else{
                    $this->errorImportLog('WARNING Missing plot in a KBC layer with katident : ' . $value['katident']);
                }
            }
            return false;
        }
        $this->errorImportLog('plots: ok');
        return true;
    }

    /**
     * @param array $contractTypes
     * @return bool
     */
    public function validateContractTypes($contractTypes)
    {
        //типове договори
        $contractTypes = array_keys($contractTypes);
        $typesSql = $this->userConnection->prepare("SELECT * from {$this->stubTable} as st WHERE nm_usage_rights NOT IN ({$contractTypes});");
        $typesSql->execute();
        $invalidContractTypes = $typesSql->fetchAll(PDO::FETCH_ASSOC);
        if (!empty($invalidContractTypes)) {
            //$this->errorImportLog("Записи с некоректни типове на договори:");
            $this->errorImportLog('Records with incorrect types of contracts:');
            foreach ($invalidContractTypes as $value) {
                $this->errorImportLog('WARNING Некоректент тип на договор: ' . $value['c_num'] . ' с тип: ' . $value['nm_usage_rights']);
            }
            return false;
        }
        $this->errorImportLog('contracts types ok.');
        return true;
    }

    public function validateOwnerTypes()
    {
        $typesSql = $this->userConnection->prepare("SELECT * from {$this->stubTable} as st;");
        $typesSql->execute();
        $data = $typesSql->fetchAll(PDO::FETCH_ASSOC);
        $errors = array();
        $this->errorImportLog(PHP_EOL . 'Records with incorrect owner types');
        foreach ($data as $row) {
            $owner_type = mb_strtoupper($row['owner_type'], 'UTF-8');
            $EGN_EIK = $row['egn_eik'];
            $length = strlen($EGN_EIK);
            if ($owner_type == 'ЮЛ' && $length !== 9) {
                $errors[] = $this->errorImportLog('WARNING wrong egn length (' . $length. ') for egn :' . $EGN_EIK . ' on plot: ' . $row['katident'] . ' or wrong owner_type: ' . $owner_type);
            } elseif ($owner_type == 'ФЛ' && $length !== 10) {
                $errors[] = $this->errorImportLog('WARNING wrong eik length (' . $length. ') for eik :' . $EGN_EIK . ' on plot: ' . $row['katident'] . ' or wrong owner_type: ' . $owner_type);
            }
        }
        if (empty($errors)) {
            $this->errorImportLog('contracts types ok.' . PHP_EOL);
            return true;
        }
        return false;
    }
    
    /**
     * @return bool
     */
    public function validateContractOwnersEgn()
    {
        $sql = $this->userConnection->prepare("select distinct owner_name, owner_surname, familly, egn_eik ,owner_type from {$this->stubTable};");
        $sql->execute();
        $owners     = $sql->fetchAll(PDO::FETCH_ASSOC);
        $valid      = true;
        $tempEgnEik = array();
        $errors = array();
        if (!empty($owners)) {
            foreach ($owners as $row) {
                if (!in_array($row['egn_eik'], $tempEgnEik, true)) {
                    $tempEgnEik[] = $row['egn_eik'];
                } else {
                    $valid = false;
                    if (empty($row['egn_eik'])) {
                        $errors[] = 'WARNING EMPTY EGN/EIK number for Owner: ' . $row['owner_name'] . ' ' . $row['owner_surname'] . ' ' . $row['familly'];
                    } else {
                        $errors[] = 'WARNING Duplicate / not unique EGN number: ' . $row['egn_eik'] . ' for Owner: ' . $row['owner_name'] . ' ' . $row['owner_surname'] . ' ' . $row['familly'];
                    }
                }
                $strlen = strlen($row['egn_eik']);
                if ($strlen != 10 && $strlen != 9) {
                    $valid = false;
                    $errors[] = 'WARNING invalid length (' . strlen($row['egn_eik']) . ') for owner: ' . $row[' owner_name'] .' '. $row[' owner_surname'].' '. $row[' familly'];
                }
            }
            if ($valid) {
                $this->errorImportLog('owners data ok.');
            }else{
                $errors = array_unique($errors);
                asort($errors);
                foreach ($errors as $error) {
                    $this->errorImportLog($error);
                }
            }
            return $valid;
        }
        $this->errorImportLog('no owners present!');
        return false;
    }

    /**
     * @param string $error
     */
    private function errorImportLog($error)
    {
        $handle = fopen($this->logFileName, 'ab');
        fwrite($handle, iconv(mb_detect_encoding($error), 'UTF-8', $error) . PHP_EOL );
        fclose($handle);
    }

    /**
     * @param array $array
     * @return string
     */
    private function arrayForQueryTransformFarming($array)
    {
        foreach ($array as $key => $value) {
            $array[$key] = '\'' . $value['name'] . '\'';
        }
        return implode(', ', $array);
    }
}