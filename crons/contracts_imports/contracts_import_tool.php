<?php
/**
 * USAGE:
 * Required values to be imported:.
 *
 * table in user database with following columns:
 *
 * 1) valid client farming name
 * 2) valid EGN  / EIK number for each owner (UNIQUE)
 * 3) plot with katident id present in table kvs_layer (ekatte . masiv . plot number)
 * 4) contract are skipped if c_num (contract number) is not present
 *
 * Data will be inserted userDb in following tables:
 *
 * 1) su_contracts
 * 2) su_contracts_rents
 * 3) su_contracts_plots_rel
 * 4) su_owners
 * 5) su_owners_reps
 * 6) su_plots_owners_rel
 *
 * STEPS: import and excel file into a table , change the column name before to english names looking at
 * the contract_table for names. assign this table name to the $stub_table variable in this file.
 * Change those $vars before run to target the correct user:
 *
 * 1) $userId
 * 2) $user_database
 * 3) $stub_table
 *
 * NOTES: SET $truncate_tables_before_run to TRUE if you want to truncate the tables when the script starts.
 * NOTES: if you import multiple tables and re-run the script don't forget to set $truncate_tables_before_run to FALSE !
 */
define('log_dir', __DIR__);
require_once log_dir . '/InputValidator.php';

// UPDATE import_table set nm_usage_rights = 'наем' where nm_usage_rights ilike '%наем%';
// UPDATE import_table set nm_usage_rights = 'аренда' where nm_usage_rights ilike '%аренда%';
// UPDATE import_table set renta_nat_type = 1 where renta_nat_type ilike '%кг%
// UPDATE update import_rents set owner_name = BTRIM (owner_name),owner_surname = BTRIM (owner_surname),familly = BTRIM (familly)

// change following parameters (the user data):

$userId = $user_id;
$user_database = $user_db;
$stub_table = $table;
// $stub_table = 'import_ownerships';

$logFile = 'contract_import_log.txt';
$truncate_tables_before_run = $truncateTables;
$die_on_Invalid_Data = !$forceRun;
$updateKvs = $updateKvsData;
const MAX_YEAR_DATE = '2037';

$insertedContracts = [];
$insertedContractsRents = [];
$insertedContractsPlotsRel = [];
$insertedOwners = [];
$insertedOwnersReps = [];
$insertedPlotsOwnersRel = [];

file_put_contents(log_dir . '/' . $logFile, ''); // clear log file

$dbCredentials = [
    'dbHost' => DEFAULT_DB_HOST,
    'dbPort' => DEFAULT_DB_PORT,
    'dbUsername' => getenv('DEFAULT_DB_USERNAME'),
    'dbPassword' => getenv('DEFAULT_DB_PASSWORD'),
    'mainDatabase' => DEFAULT_DB_DATABASE,
    'userDatabase' => $user_database,
    'stubTable' => $stub_table,
];

/** @var PDO $dbhDevMain */
/** @var PDO $dbhDev */
$dbhDevMain = new PDO('pgsql:host=' . $dbCredentials['dbHost'] . ';port=' . $dbCredentials['dbPort'] . ';dbname=' . $dbCredentials['mainDatabase'] . ';', $dbCredentials['dbUsername'], $dbCredentials['dbPassword']);
$dbhDev = new PDO('pgsql:host=' . $dbCredentials['dbHost'] . ';port=' . $dbCredentials['dbPort'] . ';dbname=' . $dbCredentials['userDatabase'] . ';', $dbCredentials['dbUsername'], $dbCredentials['dbPassword']);

$contract_types = [
    'собственост' => 1, 'Собствен' => 1,
    'аренда' => 2, 'аренд' => 2, 'Аренден договор' => 2,
    'наем' => 3, 'Наем' => 3,
    'споразумение' => 4,
    'съвместна обработка' => 5,
];

// ЮЛ = owner_type company 0 EIK length 9
// ФЛ = owner_type physical 1 EGN length 10

$initialMaxContractId = MaxID($dbhDev, 'SELECT max(id) FROM su_contracts');
$initialContractsRentsId = MaxID($dbhDev, 'SELECT max(id) FROM su_contracts_rents');
$initialContractPlotsId = MaxID($dbhDev, 'SELECT max(id) FROM su_contracts_plots_rel');
$initialOwnersId = MaxID($dbhDev, 'SELECT max(id) FROM su_owners');
$initialOwnersRepsId = MaxID($dbhDev, 'SELECT max(id) FROM su_owners_reps');
$initialPlotsOwnersRelId = MaxID($dbhDev, 'SELECT max(id) FROM su_plots_owners_rel');
$initialHypothecsId = MaxID($dbhDev, 'SELECT max(id) FROM su_hypothecs');
$initialHypothecsPlotRelId = MaxID($dbhDev, 'SELECT max(id) FROM su_hypothecs_plots_rel');

error_import_log('Import Contract Process started on user_db: ' . $user_database . ', table: ' . $stub_table . ', time: ' . date('Y-m-d h:i:s'));

error_import_log('Initial id on table su_contracts           : ' . $initialMaxContractId);
error_import_log('Initial id on table su_contracts_rents     : ' . $initialContractsRentsId);
error_import_log('Initial id on table su_contracts_plots_rel : ' . $initialContractPlotsId);
error_import_log('Initial id on table su_owners              : ' . $initialOwnersId);
error_import_log('Initial id on table su_owners_reps         : ' . $initialOwnersRepsId);
error_import_log('Initial id on table su_plots_owners_rel    : ' . $initialPlotsOwnersRelId);
error_import_log('Initial id on table su_hypothecs           : ' . $initialHypothecsId);
error_import_log('Initial id on table su_hypothecs_plots_rel : ' . $initialHypothecsPlotRelId);

// validation
$dbParams = ['databaseCredentials' => $dbCredentials, 'user_id' => $userId, 'logFile' => log_dir . '/' . $logFile];

// analyze table to see if we have all the required info
$validator = new InputValidator($dbParams);
$valid_contract = $validator->validateContractData();
$valid_contract_dates = $validator->validateContractDates();
$valid_farm_name = $validator->validateClientName();
$valid_plots = $validator->validatePlots();
$valid_ctype = $validator->validateContractTypes($contract_types);
$valid_owners_egns = $validator->validateContractOwnersEgn();
$valid_owners_types = $validator->validateOwnerTypes();

if (!$valid_contract || !$valid_contract_dates || !$valid_farm_name || !$valid_plots || !$valid_ctype || !$valid_owners_egns) {
    // @noinspection ForgottenDebugOutputInspection
    print_r(
        [
            'contract_data' => $valid_contract,
            'dates' => $valid_contract_dates,
            'farmer_name' => $valid_farm_name,
            'plots_data' => $valid_plots,
            'contracts_types' => $valid_ctype,
            'owners_egns' => $valid_owners_egns,
            'owners_types' => $valid_owners_types,
        ]
    );
    if ($die_on_Invalid_Data) {
        die('data in import table is not valid');
    }
}
unset($valid_contract, $valid_contract_dates, $valid_farm_name, $valid_plots, $valid_ctype, $valid_owners_egns, $valid_owners_types);
$initialMaxContractId = MaxID($dbhDev, 'SELECT max(id) FROM su_contracts');
$initialContractsRentsId = MaxID($dbhDev, 'SELECT max(id) FROM su_contracts_rents');
$initialContractPlotsId = MaxID($dbhDev, 'SELECT max(id) FROM su_contracts_plots_rel');
$initialOwnersId = MaxID($dbhDev, 'SELECT max(id) FROM su_owners');
$initialOwnersRepsId = MaxID($dbhDev, 'SELECT max(id) FROM su_owners_reps');
$initialPlotsOwnersRelId = MaxID($dbhDev, 'SELECT max(id) FROM su_plots_owners_rel');
$initialHypothecsId = MaxID($dbhDev, 'SELECT max(id) FROM su_hypothecs');
$initialHypothecsPlotRelId = MaxID($dbhDev, 'SELECT max(id) FROM su_hypothecs_plots_rel');

$farmings = getFarmingsUser($dbhDevMain, $userId);
if (!$farmings) {
    die('invalid user id');
}

$contracts_to_insert = getContractsToInsert($dbhDev, $stub_table);
if (!$contracts_to_insert) {
    die('no contracts to insert');
}
$contracts_count = count($contracts_to_insert);
error_import_log('UNIQUE CONTRACTS FOUND TO IMPORT: ' . $contracts_count . ' CONTRACTS ');

if ($truncate_tables_before_run) {
    $command = $dbhDev->prepare('
    TRUNCATE 
    su_contracts, 
    su_contracts_rents, 
    su_contracts_plots_rel, 
    su_owners, 
    su_owners_reps, 
    su_plots_owners_rel,
    su_hypothecs,
    su_hypothecs_plots_rel 
    RESTART IDENTITY CASCADE;');
    $command->execute();
}

// resetIDs($dbhDev);

foreach ($contracts_to_insert as $contractIndex => $contract) {
    $farm_id = setClientFarmingId($contract, $farmings);
    $contract['owner_farming_id'] = $farm_id;
    if (empty($contract['c_num']) || !isset($contract['c_num'])) {
        error_import_log('WARNING - Contract without c_num for plot : ' . $contract['katident']);
        logInvalidContractsData($contract);

        continue;
    }
    $contract['nm_usage_rights'] = setContractType($contract['c_num'], $contract['nm_usage_rights'], $contract_types);
    $contract = setContractDefaults($contract);
    if (!logInvalidContractsData($contract)) {
        continue;
    }
    $existingContract = selectContract($dbhDev, $contract);
    if ($existingContract) {
        $contract = $existingContract;
        $lastInsertedContractId = $contract['id'];
    } else {
        $lastInsertedContractId = insertContract($dbhDev, $contract); // insert data into contract table
    }
    $insertedContracts[] = $lastInsertedContractId;

    if (0 !== $contract['renta_nat_type_id']) {
        $renta_nat_id = insertContractRentaNat($dbhDev, $lastInsertedContractId, $contract); // insert rent natura in contracts_rents
        $insertedContractsRents[] = $renta_nat_id;
    }

    $plots = SelectContractsPlots($dbhDev, $contract, $stub_table);
    $plotsCount = count($plots);

    error_import_log('Contract: ' . $contract['c_num'] . ' has ' . count($plots) . ' plots: ' . implode(',', array_column($plots, 'katident')));

    foreach ($plots as $plotIndex => $plot) {
        error_import_log('status: Contract number: (' . ($contractIndex + 1) . ' of ' . $contracts_count . ') num: ' . $plot['c_num'] . ' on plot ' . $plot['katident'] . ' - (plot ' . ($plotIndex + 1) . ' of ' . $plotsCount . ')');

        if (null == $plot['gid'] || empty($plot['gid'])) {
            error_import_log('ERR - Plot not existing in kvs_layer; katident: ' . $plot['katident'] . ' contract_num: ' . $plot['c_num']);

            continue;
        }

        $plot['owner_farming_id'] = $farm_id;
        $plot['nm_usage_rights'] = setContractType($plot['c_num'], $plot['nm_usage_rights'], $contract_types);
        $plot = setContractDefaults($plot);
        $plot = selectSumContractsArea($dbhDev, $stub_table, $plot);
        // relation between plot and contract, one to one
        $contractsPlotsRelId = selectContractPlotRel($dbhDev, $plot, $lastInsertedContractId);
        if (null == $contractsPlotsRelId) {
            // no contract relation ,  insert data into su_contracts_plots_rel table
            if (Config::CONTRACT_TYPE_OWN == $plot['nm_usage_rights']) {
                // if owner contract type
                $contractsPlotsRelId = insertOwnershipContractsPlotsRel($dbhDev, $lastInsertedContractId, $plot);
            } else {
                // if rent contract type
                $contractsPlotsRelId = insertRentContractsPlotsRel($dbhDev, $lastInsertedContractId, $plot);
            }
        } else {
            updateContractPlotRel($dbhDev, $plot, $contractsPlotsRelId);
        }
        $insertedContractsPlotsRel[] = $contractsPlotsRelId;

        if (Config::CONTRACT_TYPE_OWN != $plot['nm_usage_rights']) {
            $ownerId = getOrCreateOwner($dbhDev, $plot, true);
            if (null !== $ownerId) {
                $repId = getOrCreateRep($dbhDev, $plot, $ownerId);
                $insertedOwners[] = $ownerId;
                $insertedOwnersReps[] = $repId;
                $plot = setOwnerPercent($plot);
                $plotsParams = [
                    'pc_rel_id' => $contractsPlotsRelId,
                    'numerator' => round($plot['numerator'], 0),
                    'denominator' => round($plot['denominator'], 0),
                    'owner_percent' => $plot['owner_percent'],
                    'owner_id' => $ownerId,
                    'rep_id' => (false == $repId) ? null : $repId,
                ];

                $plotOwnersRelId = selectPlotOwnersRelation($dbhDev, $plotsParams);
                if (null == $plotOwnersRelId) {
                    $plotOwnersRelId = insertPlotsOwnersRel($dbhDev, $plotsParams); // insert data into contract_plots table
                    $insertedPlotsOwnersRel[] = $plotOwnersRelId;
                }
            }
        } else {
            $hypothec_id1 = null;
            $hypothec_id2 = null;
            if (!empty($plot['first_hypothec'])) {
                $hypothec_id1 = selectHypothec($dbhDev, $plot['first_hypothec']);
                if (!$hypothec_id1) {
                    $hypothec_id1 = insertHypothec($dbhDev, $plot['first_hypothec']);
                }
            }
            if (!empty($plot['second_hypothec'])) {
                $hypothec_id2 = selectHypothec($dbhDev, $plot['second_hypothec']);
                if (!$hypothec_id2) {
                    $hypothec_id2 = insertHypothec($dbhDev, $plot['second_hypothec']);
                }
            }
            if (!empty($plot['first_hypothec_num'])) {
                $hypothecRelId = selectHypothecPlotRel($dbhDev, $plot['gid'], $hypothec_id1, $plot['first_hypothec_num']);
                if (null === $hypothecRelId) {
                    insertHypothecPlotRel(
                        $dbhDev,
                        [
                            'plot_id' => $plot['gid'],
                            'hypothec_id' => $hypothec_id1,
                            'hypothec_area' => $plot['contract_area'],
                        ]
                    );
                }
            }
            if (!empty($plot['second_hypothec_num'])) {
                $hypothecRelId = selectHypothecPlotRel($dbhDev, $plot['gid'], $hypothec_id2, $plot['second_hypothec_num']);
                if (null === $hypothecRelId) {
                    insertHypothecPlotRel(
                        $dbhDev,
                        [
                            'plot_id' => $plot['gid'],
                            'hypothec_id' => $hypothec_id2,
                            'hypothec_area' => $plot['contract_area'],
                        ]
                    );
                }
            }
        }

        if ($updateKvs) {
            if (empty($plot['kvs_cat']) && !empty($plot['category']) && is_int($plot['category'])) {
                updateKvs($dbhDev, ['gid' => $plot['gid'], 'field' => 'category', 'value' => $plot['category']]);
            }
            if (empty($plot['kvs_mest']) && !empty($plot['mestnost']) && is_int($plot['mestnost'])) {
                updateKvs($dbhDev, ['gid' => $plot['gid'], 'field' => 'mestnost', 'value' => $plot['mestnost']]);
            }
            if ((float)$plot['document_area'] > 0 && $plot['kvs_doc'] != $plot['document_area']) {
                updateKvs($dbhDev, ['gid' => $plot['gid'], 'field' => 'document_area', 'value' => $plot['document_area']]);
            }
            if (!$plot['kvs_has_contr']) {
                updateKvs($dbhDev, ['gid' => $plot['gid'], 'field' => 'has_contracts', 'value' => 't']);
            }
        }
    }
}

error_import_log('Process ended: ' . date('Y-m-d h:i:s'));
error_import_log('insertedContracts : ' . var_export($insertedContracts, true));
error_import_log('insertedContractsRents : ' . var_export($insertedContractsRents, true));
error_import_log('insertedContractsPlotsRel : ' . var_export($insertedContractsPlotsRel, true));
error_import_log('insertedOwners : ' . var_export($insertedOwners, true));
error_import_log('insertedOwnersReps : ' . var_export($insertedOwnersReps, true));
error_import_log('insertedPlotsOwnersRel : ' . var_export($insertedPlotsOwnersRel, true));

echo '---------------------------------------------Process ended';

/**
 * @param array $contract
 *
 * @return array|bool
 */
function setContractDefaults($contract)
{
    $contract['c_date'] = $contract['c_date'] ? createDate($contract['c_date']) : null;
    $contract['start_date'] = $contract['start_date'] ? createDate($contract['start_date']) : null;
    if (Config::CONTRACT_TYPE_OWN != $contract['nm_usage_rights']) {
        $contract['due_date'] = $contract['due_date'] ? createDate($contract['due_date']) : createDate('01.01.' . MAX_YEAR_DATE);
    } else {
        unset($contract['due_date']);
    }
    $contract['sv_num'] = $contract['sv_num'] ? $contract['sv_num'] : null;
    $contract['sv_date'] = $contract['sv_date'] ? createDate($contract['sv_date']) : null;
    $contract['active'] = true;
    $contract['parent_id'] = 0;
    $contract['is_annex'] = false;

    $contract['renta'] = ($contract['renta'] && Config::CONTRACT_TYPE_OWN != $contract['nm_usage_rights']) ? $contract['renta'] : null;
    $contract['renta_nat'] = $contract['renta_nat'] ? $contract['renta_nat'] : null;
    $contract['renta_nat_type'] = $contract['renta_nat_type'] ? $contract['renta_nat_type'] : 0;
    $contract['renta_nat_type_id'] = $contract['renta_nat_type'] ? $contract['renta_nat_type'] : 0;

    $contract['is_sublease'] = false;
    $contract['na_num'] = $contract['na_num'] ? $contract['na_num'] : null;
    $contract['tom'] = $contract['tom'] ? $contract['tom'] : null;

    $contract['delo'] = $contract['delo'] ? $contract['delo'] : null;
    $contract['paydate'] = $contract['paydate'] ? $contract['paydate'] : null;
    $contract['comment'] = $contract['comment'] ? trim($contract['comment']) : null;
    $contract['court'] = $contract['court'] ? $contract['court'] : null;

    if (null != $contract['first_hypothec_num'] || '' != $contract['first_hypothec_num']) {
        // todo set creditor id not to be a static value/record
        $contract['first_hypothec'] = [
            'num' => $contract['first_hypothec_num'],
            'date' => !empty($contract['first_hypothec_date']) ? $contract['first_hypothec_date'] : null,
            'due_date' => !empty($contract['first_hypothec_end_date']) ? $contract['first_hypothec_end_date'] : createDate('01.01.' . MAX_YEAR_DATE),
            'start_date' => !empty($contract['first_hypothec_date']) ? $contract['first_hypothec_date'] : null,
            'farming_id' => $contract['owner_farming_id'],
            'comment' => $contract['comment'],
            'tom' => $contract['tom'],
            'na_num' => $contract['na_num'],
            'delo' => $contract['delo'],
            'court' => $contract['court'],
            'is_active' => true,
            'deactivate_num' => null,
            'deactivate_date' => null,
            'creditor_id' => 1,
        ];
    }
    if (null != $contract['second_hypothec_num'] || '' != $contract['second_hypothec_num']) {
        $contract['second_hypothec'] = [
            'num' => $contract['second_hypothec_num'],
            'date' => !empty($contract['second_hypothec_date']) ? $contract['second_hypothec_date'] : null,
            'due_date' => !empty($contract['second_hypothec_end_date']) ? $contract['second_hypothec_end_date'] : createDate('01.01.' . MAX_YEAR_DATE),
            'start_date' => !empty($contract['second_hypothec_date']) ? $contract['second_hypothec_date'] : null,
            'farming_id' => $contract['owner_farming_id'],
            'comment' => $contract['comment'],
            'tom' => $contract['tom'],
            'na_num' => $contract['na_num'],
            'delo' => $contract['delo'],
            'court' => $contract['court'],
            'is_active' => true,
            'deactivate_num' => null,
            'deactivate_date' => null,
            'creditor_id' => 1,
        ];
    }

    return $contract;
}

/**
 * @param array $contract
 * @param array $farmings
 *
 * @throws Exception
 *
 * @return int
 */
function setClientFarmingId($contract, $farmings)
{
    $currentOwnerName = mb_strtolower(trim($contract['owner_farming']), 'UTF-8');
    $defaultFarmName = mb_strtolower('Основно стопанство', 'UTF-8');
    foreach ($farmings as $farmObj) {
        // look for the farm name and get the id
        $farmName = mb_strtolower($farmObj['name']);
        if ($farmName == $currentOwnerName || $farmName == $defaultFarmName) {
            return $farmObj['id'];
        }
    }
    $contract_data = print_r($contract, true);

    throw new Exception('Invalid Farming Id for contract : ' . $contract_data);
}

/**
 * @param PDO $dbhDev
 * @param array $contract
 *
 * @return null|int
 */
function selectContract($dbhDev, $contract)
{
    $sql = 'SELECT * FROM su_contracts WHERE c_num = :c_num 
        AND c_date = :c_date 
        AND nm_usage_rights = :nm_usage_rights 
        AND start_date = :start_date ';

    if (Config::CONTRACT_TYPE_OWN !== $contract['nm_usage_rights']) {
        $sql .= 'AND due_date = :due_date';
    }

    $selectSql = $dbhDev->prepare($sql);
    $selectSql->bindValue(':c_num', $contract['c_num']);
    $selectSql->bindValue(':c_date', $contract['c_date']);
    $selectSql->bindValue(':nm_usage_rights', $contract['nm_usage_rights']);
    $selectSql->bindValue(':start_date', $contract['start_date']);

    if (Config::CONTRACT_TYPE_OWN !== $contract['nm_usage_rights']) {
        $selectSql->bindValue(':due_date', $contract['due_date']);
    }

    $selectSql->execute();
    $existingContract = $selectSql->fetchAll(PDO::FETCH_ASSOC);
    if (1 === count($existingContract)) {
        error_import_log('INFO: contract found already with id: ' . $existingContract[0]['id'] . ' on table: su_contracts');

        return $existingContract[0];
    }

    return;
}

/**
 * @param PDO $dbhDev
 * @param array $contract
 *
 * @return bool
 */
function insertContract($dbhDev, $contract)
{
    $insertSql = $dbhDev->prepare(
        'INSERT INTO su_contracts (
            c_num, c_date, nm_usage_rights, sv_num, sv_date, start_date, renta, due_date ,renta_nat, farming_id,
            active, parent_id, is_annex, renta_nat_type_id, is_sublease, comment, na_num, tom, delo 
         ) VALUES (
            :c_num, :c_date, :nm_usage_rights, :sv_num , :sv_date, :start_date, :renta, :due_date, :renta_nat, :farming_id,
            :active, :parent_id, :is_annex, :renta_nat_type_id, :is_sublease, :comment, :na_num, :tom, :delo
         )'
    );

    $insertSql->bindValue(':c_num', $contract['c_num']);
    $insertSql->bindValue(':c_date', $contract['c_date']);
    $insertSql->bindValue(':nm_usage_rights', $contract['nm_usage_rights']);
    $insertSql->bindValue(':sv_num', $contract['sv_num']);
    $insertSql->bindValue(':sv_date', $contract['sv_date']);
    $insertSql->bindValue(':start_date', $contract['start_date']);
    $insertSql->bindValue(':renta', $contract['renta']);
    $insertSql->bindValue(':due_date', $contract['due_date']);
    $insertSql->bindValue(':renta_nat', $contract['renta_nat']);
    $insertSql->bindValue(':farming_id', $contract['owner_farming_id']);
    $insertSql->bindValue(':active', true, PDO::PARAM_BOOL);
    $insertSql->bindValue(':parent_id', $contract['parent_id']);
    $insertSql->bindValue(':is_annex', false, PDO::PARAM_BOOL);
    $insertSql->bindValue(':renta_nat_type_id', $contract['renta_nat_type_id']);
    $insertSql->bindValue(':is_sublease', false, PDO::PARAM_BOOL);
    $insertSql->bindValue(':comment', $contract['comment']);
    $insertSql->bindValue(':na_num', $contract['na_num']);
    $insertSql->bindValue(':tom', $contract['tom']);
    $insertSql->bindValue(':delo', $contract['delo']);

    return executeAndLog($dbhDev, 'su_contracts', $insertSql, 'ERR failed to insert contract with c_num: ' . $contract['c_num'] . ' and start_date:' . ($contract['start_date'] ? createDate($contract['start_date']) : $contract['start_date']));
}

/**
 * @param PDO $dbhDev
 * @param int $contractId
 * @param array $contract
 *
 * @return null|bool
 */
function insertContractRentaNat($dbhDev, $contractId, $contract)
{
    $sql = 'INSERT INTO su_contracts_rents ( contract_id, renta_id, renta_value ) VALUES ( :c_id, :renta_id, :renta_value )';
    $insertSql = $dbhDev->prepare($sql);
    $insertSql->bindValue(':c_id', $contractId);
    $insertSql->bindValue(':renta_id', ((null != $contract['renta_nat_type']) ? $contract['renta_nat_type'] : 0));
    $insertSql->bindValue(':renta_value', $contract['renta_nat']);
    if (null != $contract['renta_nat_type'] && 0 != $contract['renta_nat_type']) {
        return executeAndLog($dbhDev, 'su_contracts_rents', $insertSql, 'ERR failed to insert renta_nat in contract with c_num: ' . $contract['c_num']);
    }

    return;
}

/**
 * @param int $contractNum
 * @param string $c_type
 * @param string[] $contractTypes
 *
 * @throws Exception
 *
 * @return int $contract['nm_usage_rights']
 */
function setContractType($contractNum, $c_type, $contractTypes)
{
    $contractType = trim($c_type);
    foreach ($contractTypes as $key => $cType) {
        $contractType = mb_strtolower($contractType, 'UTF-8');
        if ($contractType == $key || false !== strpos($key, $contractType)) {
            return $cType;
        }
    }
    error_import_log('WARNING - Contract usage type not valid : ' . $c_type . ' for contract number: ' . $contractNum);

    throw new RuntimeException('invalid usage right: ' . $c_type);
}

/**
 * @param string $string
 *
 * @return false|string
 */
function createDate($string)
{
    $piece = explode('.', $string);
    $format = 'Y-m-d';
    if (isset($piece[0], $piece[1], $piece[2])) {
        list($day, $month, $year) = $piece;
        $string = $year . '-' . $month . '-' . $day;
    }
    $return = DateTime::createFromFormat('Y-m-d', $string);
    if (!$return) {
        $return = DateTime::createFromFormat('Y-m-d H:i:s', $string);
    }
    if (!$return) {
        error_import_log('WARNING!! invalid date: ' . $string);
    }

    return $return->format($format);
}

/**
 * @param PDO $dbhDev
 * @param array $contract
 * @param string $stubTable
 *
 * @return array
 */
function SelectContractsPlots($dbhDev, $contract, $stubTable)
{
    if (Config::CONTRACT_TYPE_OWN != $contract['nm_usage_rights']) {
        $contractPlots = $dbhDev->prepare(
            "SELECT f2.*, kvs.gid, kvs.document_area as kvs_doc, kvs.category as kvs_cat ,kvs.mestnost as kvs_mest , kvs.has_contracts as kvs_has_contr from {$stubTable} f2 LEFT JOIN layer_kvs kvs on (f2.katident = kvs.kad_ident) where 
                f2.c_num='{$contract['c_num']}'
            and f2.start_date = '{$contract['start_date']}'
            and f2.due_date = '{$contract['due_date']}'
            and f2.c_date = '{$contract['c_date']}'"
        );
    } else {
        $contractPlots = $dbhDev->prepare(
            "SELECT f2.*, kvs.gid, kvs.document_area as kvs_doc, kvs.category as kvs_cat ,kvs.mestnost as kvs_mest , kvs.has_contracts as kvs_has_contr from {$stubTable} f2 LEFT JOIN layer_kvs kvs on (f2.katident = kvs.kad_ident) where 
                f2.c_num='{$contract['c_num']}'
            and f2.start_date = '{$contract['start_date']}'
            and f2.c_date = '{$contract['c_date']}'"
        );
    }
    $contractPlots->execute();

    return $contractPlots->fetchAll(PDO::FETCH_ASSOC);
}

// Calculate Contract Area
// if contract area field is missing document area will be used
/**
 * @param PDO $dbhDev
 * @param string $stubTable
 * @param array $plot
 */
function selectSumContractsArea($dbhDev, $stubTable, $plot)
{
    if (Config::CONTRACT_TYPE_OWN != $plot['nm_usage_rights']) {
        $plot['area_for_rent'] = $plot['contract_area'];
        $katident = $plot['katident'];
        $c_num = $plot['c_num'];
        $sql = "SELECT sum(CAST(contract_area AS FLOAT)) as total_contract_area from {$stubTable} 
                WHERE katident = '{$katident}' and c_num = '{$c_num}' and start_date = '{$plot['start_date']}' and c_date = '{$plot['c_date']}'";
        if ($plot['due_date']) {
            $sql .= " and due_date = '{$plot['due_date']}'";
        } else {
            $sql .= ' and due_date is null';
        }

        $sql_a = $dbhDev->prepare($sql);
        $sql_a->execute();
        $result = $sql_a->fetchAll(PDO::FETCH_ASSOC);
        $plot['total_contract_area'] = $result[0]['total_contract_area'];
    }
    // todo check this if case
    if (null != $plot['percent']) {
        $plot['contract_area'] = $plot['contract_area'] * $plot['percent'] / 100;
    }

    return $plot;
}

/** plot data for su_plots_owners_rel table.
 * @param array $plot
 *
 * @return array
 */
function setOwnerPercent($plot)
{
    if (!$plot['owner_percent']) {
        if (0 == (int)ceil($plot['total_contract_area']) || 0 == (int)ceil($plot['contract_area'])) {
            $plot['owner_percent'] = 0;
        } else {
            $plot['owner_percent'] = ($plot['contract_area'] / $plot['total_contract_area']) * 100;
        }
    }
    if (0 == $plot['owner_percent']) {
        $plot['denominator'] = 0;
        $plot['numerator'] = 0;
    } else {
        $testFraction = dec2frac($plot['owner_percent']);
        if (is_array($testFraction)) {
            $plot['denominator'] = $testFraction['den'];
            $plot['numerator'] = $testFraction['num'];
        }
    }

    return $plot;
}

/**
 * @param PDO $dbhDev
 * @param array $plot
 * @param int $contractId
 *
 * @return null|bool
 */
function selectContractPlotRel($dbhDev, &$plot, $contractId)
{
    $id = null;
    $contractPlotRel = $dbhDev->prepare("select * from su_contracts_plots_rel 
    where contract_id = {$contractId} AND plot_id = {$plot['gid']}");
    $contractPlotRel->execute();
    $result = $contractPlotRel->fetchAll(PDO::FETCH_ASSOC);
    if (empty($result)) {
        return;
    }
    $id = $result[0]['id'];
    error_import_log('INFO - relation found between plot with gid : ' . $plot['gid'] . ', contract (id): ' . $contractId . ' and su_contracts_plots_rel with id ' . $id);

    return $id;
}

/**
 * @param PDO $dbhDev
 * @param int $contractId
 * @param array $plot
 *
 * @return bool
 */
function insertRentContractsPlotsRel($dbhDev, $contractId, $plot)
{
    $insertSql = $dbhDev->prepare('INSERT INTO su_contracts_plots_rel
   ( contract_id, plot_id, contract_area, annex_action, area_for_rent, fraction, percent, contract_end_date ) VALUES 
   ( :contract_id, :gid, :contract_area, :annex_action, :area_for_rent, :fraction, :percent, :contract_end_date )');
    $insertSql->bindValue(':contract_id', $contractId);
    $insertSql->bindValue(':gid', $plot['gid']);
    $insertSql->bindValue(':contract_area', (float)$plot['total_contract_area']);
    $insertSql->bindValue(':annex_action', 'added');
    $insertSql->bindValue(':fraction', null);
    $insertSql->bindValue(':percent', null);
    $insertSql->bindValue(':area_for_rent', $plot['total_contract_area']);
    $insertSql->bindValue(':contract_end_date', $plot['due_date']);

    return executeAndLog($dbhDev, 'su_contracts_plots_rel', $insertSql, 'ERR failed to insert contract-plot relation  with contract_id: ' . $contractId . ' and plot gid: ' . $plot['gid']);
}

/**
 * @param PDO $dbhDev
 * @param int $contractId
 * @param array $plot
 *
 * @return bool
 */
function insertOwnershipContractsPlotsRel($dbhDev, $contractId, $plot)
{
    $insertSql = $dbhDev->prepare('INSERT INTO su_contracts_plots_rel 
    ( contract_id, plot_id, contract_area, annex_action, area_for_rent, fraction, percent, contract_end_date ) VALUES 
    (:contract_id, :gid, :contract_area, :annex_action, :area_for_rent, :fraction, :percent, :contract_end_date )');
    $insertSql->bindValue(':contract_id', $contractId);
    $insertSql->bindValue(':gid', $plot['gid']);
    $insertSql->bindValue(':contract_area', (float)$plot['contract_area']);
    $insertSql->bindValue(':annex_action', 'added');
    $insertSql->bindValue(':fraction', 1);
    $insertSql->bindValue(':percent', 100);
    $insertSql->bindValue(':area_for_rent', null);
    $insertSql->bindValue(':contract_end_date', null);

    return executeAndLog($dbhDev, 'su_contracts_plots_rel', $insertSql, 'ERR failed to insert contract-plot relation  with contract_id: ' . $contractId . ' and plot gid: ' . $plot['gid']);
}

function updateContractPlotRel($dbhDev, $plot, $contractsPlotsRelId)
{
    $updateRel = $dbhDev->prepare("update su_contracts_plots_rel SET 
    contract_area = :contract_area, 
    area_for_rent = :area_for_rent, 
    fraction = :fraction, 
    percent = :percent, 
    contract_end_date = :contract_end_date 
    where id = {$contractsPlotsRelId};");
    if (Config::CONTRACT_TYPE_OWN === $plot['nm_usage_rights']) {
        $updateRel->bindValue(':contract_area', (float)$plot['contract_area']);
        $updateRel->bindValue(':area_for_rent', null);
        $updateRel->bindValue(':fraction', 1);
        $updateRel->bindValue(':percent', 100);
        $updateRel->bindValue(':contract_end_date', null);
    } else {
        $updateRel->bindValue(':contract_area', (float)$plot['total_contract_area']);
        $updateRel->bindValue(':area_for_rent', $plot['total_contract_area']);
        $updateRel->bindValue(':fraction', null);
        $updateRel->bindValue(':percent', null);
        $updateRel->bindValue(':contract_end_date', $plot['due_date']);
    }
    $updateRel->execute();
}

/**
 * /**
 * @param PDO $dbhDev
 * @param int $owner_type
 * @param array $plot
 *
 * @return bool
 */
function insertOwner($dbhDev, $owner_type, $plot)
{
    $plot['owner_address'] = (!empty($plot['address'])) ? $plot['address'] . ', ' . $plot['owner_address'] : null;
    $phone = !empty($plot['phone']) ? $plot['phone'] : null;
    if ($plot['egn_eik_farming']) {
        $plot['egn_eik'] = $plot['egn_eik_farming'];
    }
    $owner_sql = 'INSERT INTO su_owners ( name, surname, lastname, egn, lk_nomer, lk_izdavane, company_name, eik, phone, fax, mobile, email, address, owner_type, mol, company_address, is_dead, iban )';
    // owner_type == 1 : individual , 0 == company
    if (1 == $owner_type) {
        $owner_sql .= "
        VALUES (
        '{$plot['owner_name']}', '{$plot['owner_surname']}', '{$plot['familly']}', '{$plot['egn_eik']}', NULL, NULL, NULL, NULL, '{$phone}', NULL, NULL, NULL, '{$plot['owner_address']}', {$owner_type}, NULL, NULL, false, NULL )";
    } else {
        $owner_sql .= "
        VALUES (
        NULL, NULL, NULL, NULL, NULL, NULL, '{$plot['owner_name']}', '{$plot['egn_eik']}', '{$phone}', NULL, NULL, NULL, NULL, {$owner_type}, NULL, '{$plot['owner_address']}', false, NULL )";
    }
    $create_owner_sql = $dbhDev->prepare($owner_sql);

    return executeAndLog($dbhDev, 'su_owners', $create_owner_sql, 'ERR failed owner creation with egn/eik: ' . $plot['egn_eik']);
}

/**
 * @param PDO $dbhDev
 * @param array $plot
 * @param int $owner_id
 *
 * @return bool is_created
 */
function createRep($dbhDev, &$plot, $owner_id)
{
    if ('ЮЛ' == $plot['owner_type'] || 'юл' == mb_strtolower($plot['owner_type'], 'UTF-8') || 9 == strlen($plot['egn_eik']) || 9 == strlen($plot['egn_eik_farming'])) {
        $owner_type = 0;
    } else {
        $owner_type = 1;
    }
    $rep_sql = 'INSERT INTO su_owners_reps (rep_name, rep_surname, rep_lastname, rep_egn, rep_lk, rep_lk_izdavane, rep_address, owner_id, rent_place) ';
    if (null != $plot['rep_name'] && '' != $plot['rep_name']) {
        $rep_sql = $rep_sql . " VALUES ('{$plot['rep_name']}', '{$plot['rep_surname']}', '{$plot['rep_familly']}', 0, NULL, NULL, NULL, NULL, NULL )";
    } elseif (1 == $owner_type && (null == $plot['rep_name'] || '' == $plot['rep_name'])) {
        $rep_sql = $rep_sql . " VALUES ('{$plot['owner_name']}', '{$plot['owner_surname']}', '{$plot['familly']}', 0, NULL, NULL, NULL, '{$owner_id}', NULL )";
    } else {
        error_import_log('INFO skipped insert representative (no representative given) on owner with egn/eik: ' . $plot['egn_eik'] . ' owner_name : ' . $plot['owner_name'] . ', rep_name: ' . $plot['rep_name']);

        return false;
    }
    $create_rep_sql = $dbhDev->prepare($rep_sql);

    return executeAndLog($dbhDev, 'su_owners_reps', $create_rep_sql, 'ERR failed adding representative on owner with egn/eik: ' . $plot['egn_eik']);
}

/**
 * @param PDO $dbhDevMain
 * @param array $plot
 * @param int $userId
 *
 * @return bool
 */
function insertFarming($dbhDevMain, $plot, $userId)
{
    $ownerFarming = $plot['owner_farming'];
    $isSystem = 'Основно стопанство' == $ownerFarming ? true : false;
    $insertSql = $dbhDevMain->prepare('INSERT INTO su_users_farming ( user_id, name, is_system, group_id ) VALUES ( :user_id, :name, :is_system, :group_id )');
    $insertSql->bindValue(':user_id', $userId);
    $insertSql->bindValue(':name', $plot['name']);
    $insertSql->bindValue(':is_system', $isSystem, PDO::PARAM_BOOL);
    $insertSql->bindValue(':group_id', $userId);

    return executeAndLog($dbhDevMain, 'su_users_farming', $insertSql, 'ERROR: cannot add farming ' . $plot['name']);
}

/**
 * @param PDO $dbhDev
 * @param array $params
 *
 * @return null|int
 */
function selectPlotOwnersRelation($dbhDev, $params)
{
    $sql = $dbhDev->prepare('SELECT * FROM  su_plots_owners_rel  
      WHERE pc_rel_id = :pc_rel_id 
      AND owner_id = :owner_id 
      AND percent = :percent
      AND numerator = :numerator 
      AND denominator = :denominator ');
    $sql->bindValue(':pc_rel_id', $params['pc_rel_id']);
    $sql->bindValue(':owner_id', $params['owner_id']);
    $sql->bindValue(':percent', $params['owner_percent']);
    $sql->bindValue(':numerator', $params['numerator']);
    $sql->bindValue(':denominator', $params['denominator']);
    $sql->execute();
    $result = $sql->fetchAll(PDO::FETCH_ASSOC);

    return (count($result) >= 1) ? $result[0]['id'] : null;
}

/**
 * @param PDO $dbhDev
 *
 * @return bool
 */
function insertPlotsOwnersRel($dbhDev, $params)
{
    $insertPlotOwnerRelSql = $dbhDev->prepare(
        'INSERT INTO su_plots_owners_rel (
        pc_rel_id, owner_id, percent, owner_document_id, rep_id, proxy_num, proxy_date, path, is_heritor, numerator, denominator 
        ) VALUES (
        :pc_rel_id, :owner_id, :percent, NULL, :rep_id, NULL, NULL, NULL, FALSE, :numerator, :denominator)'
    );
    $insertPlotOwnerRelSql->bindValue(':pc_rel_id', $params['pc_rel_id']);
    $insertPlotOwnerRelSql->bindValue(':owner_id', $params['owner_id']);
    $insertPlotOwnerRelSql->bindValue(':percent', $params['owner_percent']);
    $insertPlotOwnerRelSql->bindValue(':rep_id', $params['rep_id']);
    $insertPlotOwnerRelSql->bindValue(':numerator', $params['numerator']);
    $insertPlotOwnerRelSql->bindValue(':denominator', $params['denominator']);
    if (!$insertPlotOwnerRelSql->execute()) {
        $error = $insertPlotOwnerRelSql->errorInfo();
        error_import_log($error . ' , error : ' . $error[2]);

        return false;
    }
    // get last inserted id
    return $dbhDev->lastInsertId('su_plots_owners_rel_id_seq');
}

/**
 * @param PDO $dbhDev
 * @param array $params
 *
 * @return bool
 */
// todo add PlotsFarmingRel in case of farm owner and contract ownership/sublease
function insertPlotsFarmingRel($dbhDev, $params)
{
    $plot_farming_rel_sql = $dbhDev->prepare("INSERT INTO su_plots_farming_rel ( pc_rel_id, farming_id, percent ) 
    VALUES ({$params['contracts_plots_rel_id']}, {$params['farming_id']}, {$params['farming_percent']} )");

    return executeAndLog($dbhDev, 'su_plots_farming_rel', $plot_farming_rel_sql, 'ERR failed insert in table su_plots_farming_rel pc_rel_id: ' . $params['contracts_plots_rel_id']);
}

function selectHypothec($dbhDev, $params)
{
    $selectHypotech = $dbhDev->prepare('
    SELECT id from su_hypothecs  
    where num = :num 
    AND start_date = :start_date 
    AND date = :date 
    AND due_date = :due_date 
    AND farming_id = :farming_id');
    $selectHypotech->bindValue(':num', $params['num']);
    $selectHypotech->bindValue(':start_date', $params['start_date']);
    $selectHypotech->bindValue(':date', $params['date']);
    $selectHypotech->bindValue(':due_date', $params['due_date']);
    $selectHypotech->bindValue(':farming_id', $params['farming_id']);
    $selectHypotech->execute();

    $existingHypotech = $selectHypotech->fetchAll(PDO::FETCH_ASSOC);
    if (empty($existingHypotech)) {
        return;
    }
    error_import_log('INFO: hypothec already found with id: ' . $existingHypotech[0]['id'] . ' on table: su_hypothecs for hypothec number: ' . $params['num']);

    return $existingHypotech[0]['id'];
}

/**
 * @return bool
 */
function insertHypothec($dbhDev, $params)
{
    $hypotech_sql = $dbhDev->prepare('INSERT INTO su_hypothecs (num, date, due_date, comment, creditor_id, start_date, farming_id, tom, na_num, delo, court, is_active,deactivate_num,deactivate_date) 
    VALUES (:num, :date, :due_date, :comment, :creditor_id, :start_date, :farming_id, :tom, :na_num, :delo, :court, :is_active, :deactivate_num, :deactivate_date)');
    $hypotech_sql->bindValue(':num', $params['num']);
    $hypotech_sql->bindValue(':date', $params['date']);
    $hypotech_sql->bindValue(':due_date', $params['due_date']);
    $hypotech_sql->bindValue(':start_date', $params['start_date']);
    $hypotech_sql->bindValue(':farming_id', $params['farming_id']);
    $hypotech_sql->bindValue(':comment', $params['comment']);
    $hypotech_sql->bindValue(':tom', $params['tom']);
    $hypotech_sql->bindValue(':na_num', $params['na_num']);
    $hypotech_sql->bindValue(':delo', $params['delo']);
    $hypotech_sql->bindValue(':court', $params['court']);
    $hypotech_sql->bindValue(':is_active', $params['is_active']);
    $hypotech_sql->bindValue(':deactivate_num', $params['deactivate_num']);
    $hypotech_sql->bindValue(':deactivate_date', $params['deactivate_date']);
    $hypotech_sql->bindValue(':creditor_id', $params['creditor_id']);

    return executeAndLog($dbhDev, 'su_hypothecs', $hypotech_sql, 'ERR failed insert in table su_hypothecs hypothecs with id: ' . $params['num']);
}

function selectHypothecPlotRel($dbhDev, $plotId, $hypothecId, $hypothecNum)
{
    $selectHypothecRel = $dbhDev->prepare(
        'SELECT * from su_hypothecs_plots_rel where 
    hypothec_id = :hypothec_id 
    AND plot_id = :plot_id'
    );

    $selectHypothecRel->bindValue(':hypothec_id', $hypothecId);
    $selectHypothecRel->bindValue(':plot_id', $plotId);

    $selectHypothecRel->execute();

    $existingHypothecRel = $selectHypothecRel->fetchAll(PDO::FETCH_ASSOC);
    if (empty($existingHypothecRel)) {
        return;
    }
    error_import_log('INFO: hypothec_plot_rel already found for : ' . $hypothecNum . ' and plot with id' . $plotId);

    return $existingHypothecRel[0]['id'];
}

function insertHypothecPlotRel($dbhDev, $params)
{
    $hypotech_sql = $dbhDev->prepare("INSERT INTO su_hypothecs_plots_rel ( hypothec_id, plot_id, hypothec_area ) 
    VALUES ({$params['hypothec_id']}, {$params['plot_id']}, {$params['hypothec_area']} )");

    return executeAndLog($dbhDev, 'su_hypothecs_plots_rel', $hypotech_sql, 'ERR failed insert in table su_hypothecs_plots_rel pc_rel_id: ' . print_r($params, true));
}

function updateKvs($dbhDev, $params)
{
    $kvs_sql = $dbhDev->prepare("UPDATE layer_kvs SET {$params['field']} = :value where gid = {$params['gid']};");
    $kvs_sql->bindValue(':value', $params['value']);
    $kvs_sql->execute();
    error_import_log("INFO: updated KVS plot with gid: {$params['gid']} on field: {$params['field']} with value: {$params['value']}");
}

/**
 * @param PDO $dbhDevMain
 * @param int $userId
 *
 * @return array|bool
 */
function getFarmingsUser($dbhDevMain, $userId)
{
    $sql = $dbhDevMain->prepare("SELECT id, name from su_users_farming where user_id = {$userId}");
    $sql->execute();

    return $sql->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * @param PDO $dbhDev
 * @param string $stubTable
 *
 * @return array|bool
 */
function getContractsToInsert($dbhDev, $stubTable)
{
    $sql = $dbhDev->prepare("SELECT DISTINCT ON (f2.c_num, f2.start_date, f2.due_date, f2.c_date) f2.* from {$stubTable} f2");
    $sql->execute();

    return $sql->fetchAll(PDO::FETCH_ASSOC);
}

function selectKvsByKatIdent($dbhDev, $kad_ident, $hypotech_num)
{
    $cmd = $dbhDev->prepare("select gid, kad_ident, document_area from layer_kvs where kad_ident = '{$kad_ident}' ");
    $cmd->execute();
    $result = $cmd->fetchAll(PDO::FETCH_ASSOC);
    if (empty($result)) {
        error_import_log('ERR - Missing plot with kad_ident: ' . $kad_ident . ' for hypothec number: ' . $hypotech_num);

        return;
    }

    return $result[0]['gid'];
}

/**
 * @param PDO $dbhDev
 * @param array $plot
 * @param bool $bypassEGN
 *
 * @return null|bool $owner_id
 */
function getOrCreateOwner($dbhDev, $plot, $bypassEGN)
{
    $ownerName = mb_strtolower(trim($plot['owner_name']), 'UTF-8');
    $ownerSurname = mb_strtolower(trim($plot['owner_surname']), 'UTF-8');
    $ownerFamily = mb_strtolower(trim($plot['familly']), 'UTF-8');
    if (empty($ownerName) && empty($ownerSurname) && empty($ownerFamily)) {
        return;
    }
    if ('юл' == mb_strtolower($plot['owner_type'], 'UTF-8') || 'ЮЛ' == $plot['owner_type'] || 9 == strlen($plot['egn_eik']) || 9 == strlen($plot['egn_eik_farming'])) {
        $egn_eik_column = 'eik';
        $owner_type = 0;
    } else {
        $egn_eik_column = 'egn';
        $owner_type = 1;
    }

    if ((0 == !$plot['egn_eik'] || '0' == !$plot['egn_eik'] || $plot['egn_eik']) && !$bypassEGN) {
        // search by EGN on the plot
        $checkOwnerExists = $dbhDev->prepare(" select * from su_owners where {$egn_eik_column} = '{$plot['egn_eik']}' ");
    } else {
        // search by the name
        if (1 == $owner_type) {
            $checkOwnerExists = $dbhDev->prepare("select * from su_owners where trim(lower(name)) = '{$ownerName}' AND trim(lower(surname)) = '{$ownerSurname}' AND trim(lower(lastname)) = '{$ownerFamily}' ");
        } else {
            $checkOwnerExists = $dbhDev->prepare("select * from su_owners where trim(lower(company_name)) = '{$ownerName}'");
        }
    }
    $checkOwnerExists->execute();
    $result = $checkOwnerExists->fetchAll(PDO::FETCH_ASSOC);
    if (empty($result)) {
        $owner_id = insertOwner($dbhDev, $owner_type, $plot);
    } else {
        $owner_id = $result[0]['id'];
    }

    return $owner_id;
}

/**
 * @param PDO $dbhDev
 * @param array $plot
 */
function getOrCreateRep($dbhDev, &$plot, $ownerId)
{
    $repId = null;
    $repName = $plot['rep_name'] ? $plot['rep_name'] : $plot['owner_name'];
    $repSurname = $plot['rep_surname'] ? $plot['rep_surname'] : $plot['owner_surname'];
    $repFamily = $plot['rep_familly'] ? $plot['rep_familly'] : $plot['familly'];
    $repEgn = $plot['rep_egn'] ? $plot['rep_egn'] : $plot['egn_eik'];
    $repName = trim($repName);
    $repSurname = trim($repSurname);
    $repFamily = trim($repFamily);
    $repEgn = trim($repEgn);
    if (empty($repName) && empty($repSurname) && empty($repFamily)) {
        return;
    }
    $plot['rep_name'] = $repName;
    $plot['rep_surname'] = $repSurname;
    $plot['rep_familly'] = $repFamily;
    $plot['rep_egn'] = $repEgn;
    $repExists = $dbhDev->prepare(" select * from su_owners_reps where (rep_name) = '{$repName}' AND (rep_surname) = '{$repSurname}' AND (rep_lastname) = '{$repFamily}' ");
    $repExists->execute();
    $rep = $repExists->fetchAll(PDO::FETCH_ASSOC);
    if (empty($rep)) {
        $repId = createRep($dbhDev, $plot, $ownerId);
    } else {
        $repId = $rep[0]['id'];
        if ($plot['egn_eik'] != $rep[0]['rep_egn'] && $plot['rep_name'] == $repName && $plot['rep_surname'] == $repSurname && $plot['rep_familly'] == $repFamily) {
            $updateRep = $dbhDev->prepare("update su_owners_reps SET rep_egn = :rep_egn where id = {$repId};");
            $updateRep->bindValue(':rep_egn', $plot['egn_eik']);
            $updateRep->execute();
        }
    }

    return $repId;
}

/** @deprecated
 */
function getFarming($dbhDevMain, $farming)
{
    $farming = trim($farming);
    $farmingExists = $dbhDevMain->prepare("select * from su_users_farming where name = '{$farming}'");
    $farmingExists->execute();
    $farmingExistsRes = $farmingExists->fetchAll(PDO::FETCH_ASSOC);
    if (empty($farmingExistsRes)) {
        $farming_id = null;
        error_import_log('ERR - Missing farming: ' . $farming);
    } else {
        $farming_id = $farmingExistsRes[0]['id'];
    }

    return $farming_id;
}

/**
 * @param array $contract
 *
 * @return bool
 */
function logInvalidContractsData($contract)
{
    $valid = true;
    if (null == $contract['c_num'] || '' == $contract['c_num']) {
        $valid = false;
    }
    if ('' == $contract['nm_usage_rights']) {
        $valid = false;
        error_import_log('WARNING empty nm_usage_rights on contract with c_num: ' . $contract['c_num'] . ' and id:' . $contract['id']);
    }
    if ('' == $contract['start_date']) {
        $valid = false;
        error_import_log('WARNING empty start_date on contract with c_num: ' . $contract['c_num'] . ' and id:' . $contract['id']);
    }
    if ('' == $contract['due_date'] && 1 != $contract['nm_usage_rights']) {
        $valid = false;
        error_import_log('WARNING empty due_date on contract with c_num: ' . $contract['c_num'] . ' and id:' . $contract['id']);
    }
    if ('' == $contract['owner_farming_id']) {
        $valid = false;
        error_import_log('WARNING empty owner_farming_id on contract with c_num: ' . $contract['c_num']);
    }

    return $valid;
}

/**
 * @param PDO $connection
 * @param string $table
 * @param PDOStatement $sql
 * @param string $errorMsg
 *
 * @return bool
 */
function executeAndLog($connection, $table, $sql, $errorMsg)
{
    if (!$sql->execute()) {
        $error = $sql->errorInfo();
        error_import_log(print_r($errorMsg, true) . ' , error : ' . $error[2]);

        return false;
    }
    // get last inserted id
    $lastInsertedId = $connection->lastInsertId($table . '_id_seq');
    if ($lastInsertedId > 0) {
        error_import_log('INFO: inserted record with id: ' . $lastInsertedId . ' on table: ' . $table);
    }

    return $lastInsertedId;
}

/**
 * @return array|int|string
 */
function dec2frac($decimal)
{
    $decimal = (string)$decimal;
    $num = '';
    $den = 1;
    $dec = false;

    // find least reduced fractional form of number
    for ($i = 0, $ix = strlen($decimal); $i < $ix; $i++) {
        // build the denominator as we 'shift' the decimal to the right
        if ($dec) {
            $den *= 10;
        }

        // find the decimal place/ build the numerator
        if ('.' == $decimal[$i]) {
            $dec = true;
        } else {
            $num .= $decimal[$i];
        }
    }
    $num = (int)$num;

    // whole number, just return it
    if (1 == $den) {
        return $num;
    }

    $num2 = $num;
    $den2 = $den;
    $rem = 1;
    // Euclid's Algorithm (to find the gcd)
    while ($num2 % $den2) {
        $rem = $num2 % $den2;
        $num2 = $den2;
        $den2 = $rem;
    }
    if ($den2 != $den) {
        $rem = $den2;
    }

    // now $rem holds the gcd of the numerator and denominator of our fraction
    return ['num' => ($num / $rem), 'den' => ($den / $rem)];
}

/**
 * @param string $error
 */
function error_import_log($error)
{
    $handle = fopen(log_dir . '/contract_import_log.txt', 'ab');
    fwrite($handle, $error . "\n");
    // @noinspection ForgottenDebugOutputInspection
    echo($error . "\n");
    fclose($handle);
}

/**
 * @param PDO $dbhDevMain
 * @param string $sql
 *
 * @return int
 */
function MaxID($dbhDevMain, $sql)
{
    $pdo = $dbhDevMain->prepare($sql);
    $pdo->execute();
    $result = $pdo->fetchAll();

    return (null == $result[0][0]) ? 0 : $result[0][0];
}

/** @deprecated user set identity instead when truncating
 */
function resetIDs($dbhDev)
{
    $sql = 'ALTER SEQUENCE su_contracts_id_seq RESTART WITH 1;
            ALTER SEQUENCE su_contracts_rents_id_seq RESTART WITH 1;
            ALTER SEQUENCE su_contracts_plots_rel_id_seq RESTART WITH 1;
            ALTER SEQUENCE su_owners_id_seq RESTART WITH 1;
            ALTER SEQUENCE su_owners_reps_id_seq RESTART WITH 1;
            ALTER SEQUENCE su_plots_owners_rel_id_seq RESTART WITH 1;
            ALTER SEQUENCE su_hypothecs_id_seq RESTART WITH 1;
            ALTER SEQUENCE su_hypothecs_plots_rel_id_seq RESTART WITH 1;';
    $cmd = $dbhDev->prepare($sql);

    return $cmd->execute();
}

function createImportTable($importTableName = 'import_ownerships', $dbhDev)
{
    $sql = "DROP TABLE IF EXISTS 'public'.'{$importTableName}';
            CREATE TABLE 'public'.'{$importTableName}' (
            'katident' varchar(255) COLLATE 'default',
            'c_num' varchar(255) COLLATE 'default',
            'c_date' date,
            'nm_usage_rights' varchar(255) COLLATE 'default',
            'owner_farming' varchar(255) COLLATE 'default',
            'contract_area' float8,
            'document_area' float8,
            'ekatte' varchar(255) COLLATE 'default',
            'mestnost' varchar(255) COLLATE 'default',
            'category' varchar(255) COLLATE 'default',
            'owner_name' varchar(255) COLLATE 'default',
            'owner_surname' varchar(255) COLLATE 'default',
            'familly' varchar(255) COLLATE 'default',
            'egn_eik' varchar(255) COLLATE 'default',
            'owner_type' varchar(255) COLLATE 'default',
            'address' varchar(255) COLLATE 'default',
            'owner_address' varchar(255) COLLATE 'default',
            'phone' varchar(255) COLLATE 'default',
            'rep_name' varchar(255) COLLATE 'default',
            'rep_surname' varchar(255) COLLATE 'default',
            'rep_familly' varchar(255) COLLATE 'default',
            'rep_egn' varchar(255) COLLATE 'default',
            'start_date' date,
            'due_date' date,
            'renta_nat' varchar(255) COLLATE 'default',
            'renta_nat_type' varchar(255) COLLATE 'default',
            'renta' float8,
            'payday' varchar(255) COLLATE 'default',
            'comment' varchar(255) COLLATE 'default',
            'na_num' varchar(255) COLLATE 'default',
            'sv_date' timestamp(6),
            'first_hypothec_num' varchar(255) COLLATE 'default',
            'second_hypothec_num' varchar(32) COLLATE 'default',
            'first_hypothec_date' date,
            'second_hypothec_date' date
            );";
    $cmd = $dbhDev->prepare($sql);

    return $cmd->execute();
}
