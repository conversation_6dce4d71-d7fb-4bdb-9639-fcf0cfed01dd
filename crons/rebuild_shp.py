# -*- coding: utf-8 -*-
# Build a new shx index file
import shapefile
import argparse
import os
# Explicitly name the shp and dbf file objects
# so pyshp ignores the missing/corrupt shx
# /var/www/techno/trunk/layers_queue/759_ЕТРЕНЕСАНС- КПДТ- КИРИЛ ЖЕНДОВ_2015_Dobrin/LU_BZZ.SHP
def rebuildSHP(shapeFile, shapeDBF):
	shpFileName = os.path.basename(shapeFile)
	print(shapeFile)
	print(shapeDBF)
	myshp = open(shapeFile, "rb")
	mydbf = open(shapeDBF, "rb")
	r = shapefile.Reader(shp=myshp, shx=None, dbf=mydbf)
	w = shapefile.Writer(r.shapeType)
	# Copy everything from reader object to writer object
	w._shapes = r.shapes()
	w.records = r.records()
	w.fields = list(r.fields)
	# saving will generate the shx
	shpFileName = os.path.basename(shapeFile)
	# print(shpFileName)
	dirName = os.path.dirname(shapeFile)
	# print (dirName)
	name, ext = shpFileName.split('.')

	w.save("%s/%s" % (dirName, name))
	print("REBUILDING DONE")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Rebuilds broken SHP files.');
    parser.add_argument('shape_name', metavar='shape_name', type=str, nargs=1, help='The path to the SHP file.')
    parser.add_argument('shape_dbf', metavar='shape_dbf', type=str, nargs=1, help='The path to the DBF file.')

    args = parser.parse_args()

    rebuildSHP(args.shape_name[0], args.shape_dbf[0])