#!/bin/sh
/usr/bin/flock -n /tmp/update_users_product_usage.lockfile /usr/local/bin/php /var/www/html/app/run.php crons:UpdateUsersProductUsage > /dev/stdout
/usr/bin/flock -n /tmp/cron_layers_que.lockfile /usr/bin/find /var/www/html/app/public/files/layers_queue/ -type f -mtime +5 -exec rm -r {} \; 2>&1
/usr/bin/flock -n /tmp/cron_delete_plot_images.lockfile /usr/local/bin/php /var/www/html/app/run.php crons:RegenerateHashes > /dev/stdout