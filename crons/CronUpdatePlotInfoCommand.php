<?php

namespace TF\Crons;

use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\Users\UsersController;

class CronUpdatePlotInfoCommand extends BaseCommand
{
    protected function configure()
    {
        $this
            ->setName('crons:UpdatePlotInfo')
            ->setDescription('Update Plot Info');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $UsersController = new UsersController();

        // get all users without admins and subusers
        $options = [
            'where' => [
                'level' => ['column' => 'level', 'compare' => '=', 'value' => 2],
            ],
        ];

        $user_results = $UsersController->getUsers($options);

        try {
            for ($i = 0; $i < count($user_results); $i++) {
                $userData = $user_results[$i];

                $UsersDbController = new UserDbController($userData['database']);

                $options = [
                    'tablename' => $UsersDbController->DbHandler->contractsPlotsRelTable,
                ];

                $pc_rel_results = $UsersDbController->getItemsByParams($options, false, false);

                // update all plots to has_contracts = false
                $options = [
                    'tablename' => $UsersDbController->DbHandler->tableKVS,
                    'mainData' => [
                        'has_contracts' => 0,
                    ],
                    'where' => [],
                ];
                $UsersDbController->editItem($options);

                for ($j = 0; $j < count($pc_rel_results); $j++) {
                    // update found plots - has_contracts = true
                    $options['mainData']['has_contracts'] = 1;
                    $options['where'] = [
                        'gid' => $pc_rel_results[$j]['plot_id'],
                    ];
                    $UsersDbController->editItem($options);
                }
            }
        } catch (Exception $e) {
            echo($e);
        }
    }
}
