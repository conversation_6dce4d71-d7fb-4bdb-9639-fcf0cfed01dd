<?php
namespace TF\Crons;

use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Prado;

class CronDeletePublicFilesUploadsFilesCommand extends BaseCommand
{
    protected function configure()
    {
        $this
            ->setName('crons:DeletePublicFilesUploads')
            ->setDescription('Delete all files from public/files/uploads');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        system("find ".PUBLIC_UPLOAD."/* -not -path '*/\.gitkeep' -type f -mmin +360 -exec rm {} \;");
    }
}