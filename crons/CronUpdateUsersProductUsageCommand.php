<?php

namespace TF\Crons;

use Exception;
use Prado\TShellApplication;
use Prado\Web\Services\TJsonRpcProtocol;
use Prado\Web\Services\TRpcServer;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use DateTime;
use TF\Application\Common\MTAuthManager;
use TF\Engine\APIClasses\Plots\UsedPlotsReportGrid;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\Users\UsersController;

class CronUpdateUsersProductUsageCommand extends BaseCommand {

    /** @var TShellApplication */
    protected $pradoApp = false;

    public function __construct()
    {
        parent::__construct('crons:UpdateUsersProductUsage');
    }

    protected function configure()
    {
        $this->setDescription('Updates susi_main.su_users data with every users product usage (total plot, modules and loaded KVSs');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $UsersController = new UsersController('Users');
        $server = new TRpcServer(new TJsonRpcProtocol);
        $usedPlotsReportGrid = new UsedPlotsReportGrid($server);
        $reportOptions = $this->getPlotReportOptions();

        try {
            /** @var MTAuthManager $auth */
            $auth = $this->pradoApp->getModule('auth');

            $users = $UsersController->getUsers(array(
                'where' => array(
                    'level' => array('column' => 'level', 'compare' => '=', 'value' => 2),
                    'active' => array('column' => 'active', 'compare' => '=', 'value' => true),
                ),
            ));

            foreach ($users as $user) {
                if ($user['database']) {
                    $auth->switchUser($user['username']);

                    $ekatteCount = $this->getEkatteCount($user['database'], $output);
                    $totalPlotArea = $this->getTotalPlotArea($usedPlotsReportGrid, $reportOptions, $output);

                    $output->writeln('');
                    $output->writeln('User: ' . $user['username']);
                    $output->writeln('Ekatte count: ' . $ekatteCount);
                    $output->writeln('Plot total: ' . $totalPlotArea);

                    $updatedData = array(
                        'mainData' => array(
                            'ekatte_count' => $ekatteCount,
                            'total_plot_area' => $totalPlotArea,
                        ),
                        'where' => array(
                            'id' => array('column' => 'id', 'compare' => '=', 'value' => $user['id']),
                        ),
                    );

                    $UsersController->updateUsersData($updatedData);
                }
            }
        } catch (Exception $e) {
            $output->writeln('Error: Message: ' . $e->getMessage());
        }
    }

    protected function getEkatteCount($userDb, OutputInterface $output)
    {
        try {
            $UsersDbController = new UserDbController($userDb);
            $UsersDbController->DbHandler->refreshEkateCombobox();
            $ekatteCombobox = $UsersDbController->DbHandler->getEkatteCombobox();

            return count($ekatteCombobox);
        } catch (Exception $e) {
            $output->writeln('Error: Message: ' . $e->getMessage());
            return null;
        }
    }

    protected function getTotalPlotArea($usedPlotsReportGrid, $options, OutputInterface $output)
    {
        try {
            $plotArea = $usedPlotsReportGrid->getUseedPlots($options);
            $total = str_replace(' ', '', $plotArea['footer'][1]['area']);

            return $total || is_float($total) ? $total : null;
        } catch (Exception $e) {
            $output->writeln('Error: Message: ' . $e->getMessage());
            return null;
        }
    }

    protected function getPlotReportOptions()
    {
        $today = new DateTime();
        $reportFrom = null;
        $reportTo = null;

        foreach ($GLOBALS['Farming']['years'] as $farmingYear) {
            $yearStart = new DateTime($farmingYear['start_date']);
            $yearEnd = new DateTime($farmingYear['end_date']);

            if ($today >= $yearStart && $today <= $yearEnd) {
                $reportFrom = $yearStart->format('Y-m-d');
                $reportTo = $yearEnd->format('Y-m-d');
                break;
            }
        }
        return array(
            'filters' => array(
                'report_arendator' => null,
                'report_category' => null,
                'report_choose_participation' => null,
                'report_choose_renewed' => null,
                'report_contract_date' => null,
                'report_contract_date_to' => null,
                'report_date' => $reportTo,
                'report_date_as_of' => null,
                'report_date_from' => $reportFrom,
                'report_ekate' => null,
                'report_exclude_inactive' => true,
                'report_farming' => null,
                'report_include_subleases' => false,
                'report_irrigation' => 'all',
                'report_mestnost' => null,
                'report_ntp' => null,
                'report_sublease_type' => null,
            )
        );
    }

    public function setPradoApp($pradoApp)
    {
        $this->pradoApp = $pradoApp;
    }

    public function getPradoApp()
    {
        return $this->pradoApp;
    }

}