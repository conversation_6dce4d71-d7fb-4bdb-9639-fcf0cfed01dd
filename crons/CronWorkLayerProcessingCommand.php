<?php 
 namespace TF\Crons;
 
 use Prado\Prado;
 use Symfony\Component\Console\Command\Command as BaseCommand;
 use Symfony\Component\Console\Input\InputArgument;
 use Symfony\Component\Console\Input\InputInterface;
 use Symfony\Component\Console\Input\InputOption;
 use Symfony\Component\Console\Output\OutputInterface;
 use TF\Engine\Kernel\CronsLoadData\WorkLayerProcessingClass;

class CronWorkLayerProcessingCommand extends BaseCommand {

    protected function configure()
    {
        $this
            ->setName('crons:WorkLayerProcessing')
            ->setDescription('Zarejdane na raboten sloi');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        /** @var WorkLayerProcessingClass $objKvsProcessing */
        $objKvsProcessing = Prado::getApplication()->getModule('WorkLayerProcessingClass');

        $objKvsProcessing->startProcessing();
    }
}
