<?php 
namespace TF\Crons;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface; 
use Prado;
use TF\Application\Common\Config;
use TF\Engine\Plugins\Core\Users\UsersController;

class CronRegenerateLoginHashCommand extends BaseCommand {

    protected function configure()
    {
        $this
            ->setName('crons:RegenerateHashes')
            ->setDescription('Regenerira login hash tokens');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $paid_support_date = Config::PAID_SUPPORT_DATE_LAST;
        $sql = "SELECT id 
                FROM su_users 
                WHERE paid_support = '{$paid_support_date}' 
                OR level in (1, 2, 5, 6) 
                AND active = 'true'";

        $UsersController = new UsersController('Users');
        $cmd = $UsersController->DbHandler->DbModule->createCommand($sql);

        $userIDS = $cmd->query()->readAll();

        foreach ($userIDS as $user) {
            $UsersController->updateUsersData(array(
                'mainData' => array(
                    'login_token' => sha1(uniqid(rand(), true))
                ),
                'where' => array(
                    'id' => array('column' => 'id', 'compare' => '=', 'value' => $user['id'])
                )
            ));
        }
    }
}
