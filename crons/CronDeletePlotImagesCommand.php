<?php
namespace TF\Crons;

use \RecursiveDirectoryIterator;
use \RecursiveIteratorIterator;
use \RegexIterator;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Prado;

class CronDeletePlotImagesCommand extends BaseCommand
{
    protected function configure()
    {
        $this
            ->setName('crons:DeletePlotImages')
            ->setDescription('Delete Plot Images');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        //http://stackoverflow.com/questions/3126191/php-script-to-delete-files-older-than-24-hrs-deletes-all-files
        //http://stackoverflow.com/questions/3338123/how-do-i-recursively-delete-a-directory-and-its-entire-contents-files-sub-dir
        //Set the file path
        $path = WMS_IMAGE_PATH;

        $files = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($path, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::CHILD_FIRST
        );
        $Regex = new RegexIterator($Iterator, '/^.+\.php$/i', RecursiveRegexIterator::GET_MATCH);
        foreach ($files as $fileinfo) {
            if ($fileinfo->getFilename() !== '.gitkeep') {
                ($fileinfo->isDir() ? $this->removeFilesFromDir($fileinfo->getRealPath().'/') : @unlink($fileinfo->getRealPath()));
            }
        }
    }

    private function removeFilesFromDir($path)
    {
        if ($handle = opendir($path)) {
            while (($file = readdir($handle)) !== false) {
                if ((time()-filectime($path.$file)) >= 3600) {
                    if (preg_match('/\.png$/i', $file)) {
                        @unlink($path.$file);
                    }
                }
            }
        }
    }
}
