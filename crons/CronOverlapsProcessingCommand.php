<?php

namespace TF\Crons;

use Prado;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController;
use TF\Engine\Plugins\Core\Users\UsersController;

Prado::using('Plugins.Core.UserDb.*');
Prado::using('Plugins.Core.Users.*');
Prado::using('Plugins.Core.Layers.conf');
Prado::using('Plugins.Core.Layers.*');

class CronOverlapsProcessingCommand extends BaseCommand
{
    protected function configure()
    {
        $this
            ->setName('crons:OverlapsProcessing')
            ->setDescription('Overlaps processing');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $UsersController = new UsersController('Users');

        $data = $UsersController->getOverlapsForProcessing();

        for ($i = 0; $i < count($data); $i++) {
            // making item data easier to use
            $overlapData = $data[$i];

            $filePath = OVERLAPS_QUEUE_PATH . 'overlap_' . $overlapData['user_id'] . '_' . $overlapData['id'] . '.csv';
            $status = 1;
            $csv = [];

            // controller init
            $UsersDbController = new UserDbController($overlapData['database']);
            $UsersDbPlotsController = new UserDbPlotsController($overlapData['database']); // getPluginInstanceModuleUserDb('Plots', $overlapData['database']);

            if (is_file($filePath)) {
                // taking file information
                $rows = array_map('str_getcsv', file($filePath));
                // taking and removing headers
                $header = array_shift($rows);
                // taking every row and putting it as new element in array
                $csv = [];
                foreach ($rows as $row) {
                    $csv[] = explode(';', $row[0]);
                }
            } else {
                // this status stands for processing error
                $status = 2;
            }

            // clear old values
            $itemData = [];
            $kad_ident = '';
            $processed = false;

            if (0 != count($csv)) {
                for ($j = 0; $j < count($csv); $j++) {
                    $itemData = [];
                    $itemData = $csv[$j];
                    // if column numbers are higher than 5 then invalid format

                    if (5 == count($itemData)) {
                        $kad_ident = substr($itemData[3], 1, -1);
                        $tmp_explode = explode('-', $kad_ident);
                        if (3 != count($tmp_explode)) {
                            $tmp_explode = explode('.', $kad_ident);
                        }

                        if (3 == count($tmp_explode)) {
                            // assigning the values
                            $ekate = $tmp_explode[0];
                            $masiv = $tmp_explode[1];
                            $imot = $tmp_explode[2];

                            // checking if values are correct
                            if (is_numeric($ekate) && is_numeric($masiv) && is_numeric($imot)
                                && 5 == strlen($ekate) && 0 != strlen($masiv) && 0 != strlen($imot)) {
                                // information is correct
                                $layer_options = [
                                    'tablename' => 'layer_kvs',
                                    'return' => ['gid'],
                                    'where' => [
                                        'ekate' => ['column' => 'ekate', 'compare' => '=', 'prefix' => 'p', 'value' => $ekate],
                                        'masiv' => ['column' => 'masiv', 'compare' => '=', 'prefix' => 'p', 'value' => $masiv],
                                        'number' => ['column' => 'number', 'compare' => '=', 'prefix' => 'p', 'value' => $imot],
                                    ],
                                ];
                                $layerData = $UsersDbPlotsController->getPlotData($layer_options, false, false);

                                // information is correct
                                $layer_options = [
                                    'tablename' => 'layer_kvs',
                                    'return' => ['gid'],
                                    'where' => [
                                        'ekate' => ['column' => 'ekate', 'compare' => '=', 'prefix' => 'p', 'value' => $ekate],
                                        'masiv' => ['column' => 'masiv', 'compare' => '=', 'prefix' => 'p', 'value' => ('000' == $masiv) ? $masiv = '0' : intval($masiv)],
                                        'number' => ['column' => 'number', 'compare' => '=', 'prefix' => 'p', 'value' => ('000' == $imot) ? $imot = '0' : intval($imot)],
                                    ],
                                ];
                                $layerData1 = $UsersDbPlotsController->getPlotData($layer_options, false, false);

                                if (0 != count($layerData[0])) {
                                    $layer_id = $layerData[0]['gid'];
                                    $overlap_status = 1;
                                } elseif (0 != count($layerData1[0])) {
                                    $layer_id = $layerData1[0]['gid'];
                                    $overlap_status = 1;
                                } else {
                                    $layer_id = 0;
                                    $overlap_status = 0;
                                }

                                $options = [
                                    'tablename' => $UsersDbController->DbHandler->tableOverlapsData,
                                    'mainData' => [
                                        'ekatte' => $itemData[0],
                                        'land' => iconv('cp1251', 'utf-8', substr($itemData[1], 1, -1)),
                                        'bzs' => substr($itemData[2], 1, -1),
                                        'kad_ident' => substr($itemData[3], 1, -1),
                                        'area' => iconv('cp1251', 'utf-8', substr($itemData[4], 1, -1)),
                                        'has_match' => $overlap_status,
                                        'gid' => $layer_id,
                                        'overlap_id' => $overlapData['item_id'],
                                    ],
                                ];
                                $UsersDbController->addItem($options);
                                $processed = true;
                            } else {
                                // kad ident information is incorrect
                                $status = 2;
                            }
                        } else {
                            // kad ident is not from the required type
                            $status = 2;
                        }
                    } else {
                        // column numbers are not matching
                        $status = 2;
                    }
                }
            } else {
                $status = 2;
            }

            if ($processed) {
                $UsersController->setOverlapProcessed($overlapData['item_id']);
            }

            $options = [
                'tablename' => $UsersDbController->DbHandler->tableOverlaps,
                'id' => $overlapData['item_id'],
                'status' => $status,
            ];

            $UsersDbController->updateItemStatus($options);
        }
    }

    private function error_layers_log($id, $error)
    {
        $LayersController = new LayersController('Layers');

        $LayersController->log(1, 'cron-daemon', $error, [$id]);

        $options = [];
        $options['mainData'] = [
            'status' => $error,
        ];

        $options['id'] = $id;
        $LayersController->editItemFiles($options);
    }
}
