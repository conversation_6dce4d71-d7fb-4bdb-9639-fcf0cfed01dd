<?php 
namespace TF\Crons;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface; 
use Prado;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\Users\UsersController;

class CronAgreementsProcessingCommand extends BaseCommand {

    protected function configure()
    {
        $this
            ->setName('crons:AgreementsProcessing')
            ->setDescription('Procesvane na Agreements');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $UsersController = new UsersController('Users');
        $FarmingController = new FarmingController('Farming');

        $data = $UsersController->getAgreementsForProcessing();

        if (count($data) == 0) { //No agreements for processing are found. Terminate the script
            die;
        }

        for($i = 0; $i < count($data); $i++) {

            //making item data easier to use
            $agreementData = $data[$i];

            $filePath = AGREEMENTS_QUEUE_PATH . 'agreement_' . $agreementData['user_id'] . '_' . $agreementData['id'] . '.csv';
            $status = 1;
            $csv = array();
            
            //controller init
            $UsersDbController = new UserDbController($agreementData['database']);
            
            //Check if the agreement still exists
            $options = array(
                'tablename' => $UsersDbController->DbHandler->tableAgreements,
                'return' => array('*'),
                'where' => array(
                    'id' => array('column' => 'id', 'compare' => '=', 'value' => $agreementData['item_id'])
                    )
                );

            $results = $UsersDbController->getItemsByParams($options, false, false);

            if (count($results) == 0) {
                continue;
            }

            //Get file data as array
            if(file_exists($filePath)) {
                $csvFile = fopen($filePath, 'r');
                $isHeaderSkiped = false;
                $header = fgets($csvFile);
                $delimiter = $this->getCSVDelimiter($header);
                while ($csvRow = fgetcsv($csvFile, 0, $delimiter)) {
                    $csv[] = $csvRow;
                }
                fclose($csvFile);
            } else {
                //if the file is not found in the agreement directory
                //this status stands for processing error
                $status = 2;
            }
            
            //Get the EKATTE for the new uploaded file
            $tmp_ekate = $csv[$i][2];

            //Check if there is already data for this EKATTE, year and farming
            $initialCheckQuery = "SELECT 
                ad.ekate AS ekate, a.farming AS farming, a.year AS year 
            FROM 
                su_agreements_data ad 
            LEFT JOIN 
                su_agreements a ON(ad.agreement_id = a.id) 
            WHERE 
                ekate = '{$tmp_ekate}' and year = {$results[0]['year']} and farming = {$results[0]['farming']}
            GROUP BY 
                ekate, farming, year";
            $loadedData = $UsersDbController->DbHandler->getDataByQuery($initialCheckQuery);
            
            if (count($loadedData) != 0) {
                continue;
            }

            //clear old values 
            $itemData = array();
            $processed = false;
            $iteration = 0;
            $contractID = 0;

            if(count($csv)) { //The file contains valid rows
                //Drop and create tmp_agreements
                $recreate_query = 
                "DROP TABLE IF EXISTS tmp_agreements;
                CREATE TABLE tmp_agreements (
                    id SERIAL,
                    masiv character varying(255) NOT NULL,
                    imot character varying(255) NOT NULL,
                    ekate character varying(255) NOT NULL,
                    has_match boolean,
                    agreement_id integer,
                    gid integer,
                    area FLOAT NOT NULL
                    );";
                $UsersDbController->DbHandler->getDataByQuery($recreate_query);


                //Prepare the initial insert query for all rows, accuired from the CSV file
                //It's important not to omit the '' around ekate variable to ensure proper inclusion if leading zeros are present
                $insert_query = 'INSERT INTO tmp_agreements (masiv, imot, ekate, has_match, agreement_id, gid, area ) VALUES '; 
                for ($i=0; $i < count($csv); $i++) { 

                    $masiv = $csv[$i][0];
                    $imot = $csv[$i][1];
                    $area = (float)$csv[$i][4];
                    $ekate = $csv[$i][2];
                    $ekate = strlen( $ekate == 5) ? $ekate : $this->addLeadingZerosToEkate($ekate);

                    if(is_numeric($ekate) && is_numeric($masiv) && is_numeric($imot) && strlen($ekate) == 5 && strlen($masiv) != 0 && strlen($imot) != 0) {
                        //csv information is correct
                        //append the next row to the insert query
                        $insert_query .= "(".$masiv.", ".$imot.", '".$ekate."', false, ".$agreementData["item_id"].", 0,".$area.")";
                        if ($i != count($csv)-1)
                            $insert_query .= ', '; //do not insert ', ' after the last row
                    } else {
                        //csv information is incorrect
                        //skip the invalid row
                        $status = 2;
                    }   
                }
                $UsersDbController->DbHandler->getDataByQuery($insert_query);
                

                //Check for matches between the agreement plots and the plots loaded in layer_kvs. 
                //If matches are then get the gid of the plot and set it for the plot in tmp_agreements
                $updateQuery = 
                "UPDATE
                    tmp_agreements tmp
                SET
                    gid = kvs.gid,
                    has_match = true
                FROM
                    layer_kvs kvs
                WHERE
                    tmp.ekate = kvs.ekate AND 
                    tmp.masiv = kvs.masiv AND 
                    tmp.imot = kvs.number";
                $UsersDbController->DbHandler->getDataByQuery($updateQuery);


                //Get all plots in the tmp data and remove the duplicates by grouping the results
                //and calculating the total contract area for each plot
                $resultsToCopyQuery = 
                "SELECT 
                    masiv, imot, ekate, has_match, agreement_id, gid, SUM(area) AS area 
                FROM 
                    tmp_agreements 
                GROUP BY 
                    masiv, imot, ekate, has_match,agreement_id, gid"; 
                $resultsToCopy = $UsersDbController->DbHandler->getDataByQuery($resultsToCopyQuery);


                //Prepare the insert statement in su_agreements_data, after the data has been grouped and the duplicates removed
                //It's important not to omit the '' around ekate variable to ensure proper inclusion if leading zeros are present
                $finalInsertQuery = 'INSERT INTO su_agreements_data (masiv, imot, ekate, has_match, agreement_id, gid, area ) VALUES '; 
                for ($i=0; $i < count($resultsToCopy); $i++) { 
                    $tmpHasMatch = $resultsToCopy[$i]['has_match'] ? 'true' : 'false';
                    $finalInsertQuery .= "("
                            .$resultsToCopy[$i]['masiv'].", "
                            .$resultsToCopy[$i]['imot'].", '"
                            .$resultsToCopy[$i]['ekate']."', "
                            .$tmpHasMatch.", "
                            .$resultsToCopy[$i]['agreement_id'].", "
                            .$resultsToCopy[$i]['gid'].", "
                            .$resultsToCopy[$i]['area']
                            .")";
                    if ($i != count($resultsToCopy)-1) {
                        $finalInsertQuery .= ', '; //do not insert ', ' after the last row
                    }           
                }
                $UsersDbController->DbHandler->getDataByQuery($finalInsertQuery);


                //Get the contract ID, so it can be used later to update the plot-contract relations
                $options = array(
                    'tablename' => $UsersDbController->DbHandler->tableAgreements,
                    'where' => array(
                        'id' => array('column' => 'id', 'compare' => '=', 'value' => $agreementData['item_id'])
                        )
                    );
                $agreementUserData = $UsersDbController->getItemsByParams($options, false, false);
                $contractID = $agreementUserData[0]['contract_id'];
                $dueDate = $GLOBALS['Farming']['years'][$agreementUserData[0]['year']]['year'].'-09-30';

                //Check if there are any plots to be updated with the new contract data
                $forPlotsContractsRelQuery = "SELECT 
                    masiv, imot, ekate, has_match, agreement_id, gid, SUM(area) AS area 
                FROM 
                    tmp_agreements 
                WHERE 
                    gid > 0
                GROUP BY 
                    masiv, imot, ekate, has_match,agreement_id, gid";
                $forPlotsContractsRel = $UsersDbController->DbHandler->getDataByQuery($forPlotsContractsRelQuery);

                if (count($forPlotsContractsRel) > 0) { //There are plot-contract relations to be updated
                    
                    //Prepare the insert statement for the new plot-contract relations
                    $plotsContractsRelInsertQuery = "INSERT INTO su_contracts_plots_rel (contract_id, plot_id, contract_area, price_per_acre, price_sum, annex_action, contract_end_date) VALUES "; 

                    for ($j=0; $j < count($forPlotsContractsRel); $j++) { 
                        //append the next result to the insert query
                        $plotsContractsRelInsertQuery .= "(".$contractID.', '.$forPlotsContractsRel[$j]['gid'].', '. $forPlotsContractsRel[$j]['area'].", null, null, 'added', '".$dueDate."')";
                        if ($j != count($forPlotsContractsRel) - 1 ) {
                            $plotsContractsRelInsertQuery .= ', '; //do not insert ', ' after the last row
                        }
                    }
                    $UsersDbController->DbHandler->getDataByQuery($plotsContractsRelInsertQuery);

                    //Update all the plots, that have now been included in the agreement contract - 
                    //set has_contracts property to true
                    $updateKvsQuery = 
                    "UPDATE
                        layer_kvs kvs
                    SET
                        has_contracts = true
                    FROM
                        su_agreements_data agr
                    WHERE
                        agr.gid = kvs.gid";
                    $UsersDbController->DbHandler->getDataByQuery($updateKvsQuery);

                } //if (count($forPlotsContractsRel) > 0)

                //Empty table tmp_agreements
                $truncate_query = "TRUNCATE tmp_agreements";
                $UsersDbController->DbHandler->getDataByQuery($truncate_query);

            } //if(count($csv))
            else 
            {
                //empty CSV file
                $status = 2;
            }


            //Update the current agreement status in su_users_agreements
            $UsersController->setAgreementProcessed($agreementData['id']);

            
            //Update the current agreement status in su_agreements
            $options = array(
                'tablename' => $UsersDbController->DbHandler->tableAgreements,
                'id' => $agreementData['item_id'],
                'status' => $status
            );  
            $UsersDbController->updateItemStatus($options);
            

            //remove the CSV file
            @unlink($filePath);
        }
    }

    private function getCSVDelimiter($row) 
    {
        $commas = substr_count($row, ',');
        $semiColumns = substr_count($row, ';');

        if ($commas > $semiColumns) {
            return ',';
        }
        return ';';
    }

    /**
     * If the EKATE param in the csv file is shorter than 5 characters
     * add the necessary leading zeros to extend the param to 5 characters
     * @param string $ekate
     * @return string
     */
    private function addLeadingZerosToEkate($ekate) 
    {
        $currentLength = strlen($ekate);

        $diff = 5 - $currentLength;

        for ($i=0; $i < $diff; $i++) { 
            $ekate = '0'.$ekate; 
        }

        return $ekate;
    }
}
