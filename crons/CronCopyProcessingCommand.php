<?php 
namespace TF\Crons;
use Exception;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface; 
use Prado;
use TF\Application\Common\Config;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

//\Prado::using('Plugins.Core.UserDb.*');
//\Prado::using('Plugins.Core.Layers.conf');
//\Prado::using('Plugins.Core.Layers.*');

class CronCopyProcessingCommand extends BaseCommand {

    protected function configure()
    {
        $this
            ->setName('crons:CopyProcessing')
            ->setDescription('Copy processing');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $LayersController = new LayersController('Layers');

        $options = array(
            'status' => '0',
            'sort' => 't.id',
            'users' => true,
            'order' => 'ASC',
            'return' => array('lt2.id as lt2_id', 'lt1.layer_type as lt1_type,lt2.layer_type as lt2_type, lt1.year as lt1_year, lt1.farming as lt1_farming, lt1.table_name as lt1_tablename, lt2.table_name as lt2_tablename, u.database, t.id, t.user_id, t.group_id')
        );

        $data = $LayersController->getCopyLayersData($options);

        try {
            for ($i = 0; $i < count($data); $i++) {
                $copyData = $data[$i];
                $UsersDbController = new UserDbController($copyData['database']);

                $lt1_exists = $UsersDbController->getTableNameExist($copyData['lt1_tablename']);
                $lt2_exists = $UsersDbController->getTableNameExist($copyData['lt2_tablename']);

                if (!$lt2_exists)
                {
                    switch ($copyData['lt2_type']) {
                        case Config::LAYER_TYPE_ZP:
                            $UsersDbController->createTableZP($copyData['lt2_tablename']);
                            break;
                        case Config::LAYER_TYPE_FOR_ISAK:
                            $UsersDbController->createTableForISAK($copyData['lt2_tablename']);
                            break;
                        default:
                            break;
                    }
                }
                $lt2_exists = $UsersDbController->getTableNameExist($copyData['lt2_tablename']);
                
                if (!$lt1_exists || !$lt2_exists) {
                    $this->error_layers_log($copyData['id'], ERROR_RUNTIME);
                    continue;
                }

                $result1 = $UsersDbController->updateLayerCRS(DEFAUL_DB_CRS, DEFAUL_DB_CRS, $copyData['lt2_tablename']);
                $result2 = $UsersDbController->updateLayerCRS(DEFAUL_DB_CRS, DEFAUL_DB_CRS, $copyData['lt1_tablename']);

                if (!$result1['value'] || !$result2['value']) {
                    $this->error_layers_log($copyData['id'], ERROR_INVALID_GEOMETRY);
                    continue;
                }

                $isValidGeom = $UsersDbController->getValidGeom($copyData['lt1_tablename']);
                if (!$isValidGeom)
                {
                    if (DEFAULT_DB_VERSION >= 9.3)
                    {
                        $UsersDbController->stMakeValid($copyData['lt1_tablename']);

                        $isValidGeom = $UsersDbController->getValidGeom($copyData['lt1_tablename']);
                        if (!$isValidGeom)
                        {
                            $this->error_layers_log($copyData['id'], ERROR_INVALID_GEOMETRY);
                            continue;
                        }
                    } else {
                        $this->error_layers_log($copyData['id'], ERROR_INVALID_GEOMETRY);
                        continue;
                    }
                }

                if ($copyData['lt2_type'] != 2) {
                    $isIntersect = $UsersDbController->getIsIntersect($copyData['lt1_tablename'], $copyData['lt2_tablename']);
                    if ($isIntersect) {
                        $this->error_layers_log($copyData['id'], ERROR_INVALID_FILE_DATA);
                        continue;
                    }
                }

                if ($copyData['lt1_tablename'] == Config::LAYER_TYPE_ZP || $copyData['lt1_tablename'] == Config::LAYER_TYPE_FOR_ISAK)
                {
                    $UsersDbController->copyDataFromToCustom($copyData['lt1_tablename'], $copyData['lt2_tablename'], $copyData['lt1_type'], $copyData['lt2_type']);
                } else {
                    $UsersDbController->copyDataFromTo($copyData['lt1_tablename'], $copyData['lt2_tablename'], $copyData['lt1_type'], $copyData['lt2_type']);
                }

                $maxExtent = $UsersDbController->getMaxExtent($copyData['lt2_tablename']);
                $maxExtent = str_replace("BOX(", "", $maxExtent);
                $maxExtent = str_replace(")", "", $maxExtent);
                $maxExtent = str_replace(",", " ", $maxExtent);

                $options = array();
                $options['mainData'] = array(
                    'extent' => $maxExtent
                );
                $options['id'] = $copyData['lt2_id'];
                $LayersController->editItem($options);

                $options = array();
                $options["database"] = $copyData['database'];
                $options["user_id"] = $copyData['group_id'];
                $LayersController->generateMapFile($options);
                
                // system("/bin/chown -R www-data:www-data " . WMS_MAP_PATH);

                $LayersController->setCopyProccessed($copyData['id']);
            }
        } catch (Exception $e) {
            $this->error_layers_log($layer['id'], ERROR_RUNTIME);
            echo($e);
        }        
        
    }

    private function error_layers_log($id, $error)
    {
        $LayersController = new LayersController('Layers');

        $LayersController->log(1, "cron-daemon", $error, array($id));

        $options = array();
        $options['mainData'] = array(
            'status' => $error
        );

        $options['id'] = $id;
        $LayersController->editItemCopyTable($options);
    }
}
