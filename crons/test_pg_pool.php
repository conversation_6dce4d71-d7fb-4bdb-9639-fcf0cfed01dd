<?php 
require_once('../../config/global.config.php'); 
require_once('../../crons/shell.php');
$host = '***********'; 
$port = '5433'; 
$un = 'postgres'; 
$pass = '6nuk23'; 

$dbName = 'db_tf_qa'; 

$dbhDev = new PDO("pgsql:host=" . $host . ";port=" . $port .";dbname=".$dbName.";", $un, $pass); echo "Database: {$dbName} \n"; 

$record_id = 251; 
for($i = 0; $i <= 30; $i++) {
	$sql = "update su_contracts set renta = {$i} where id ={$record_id}";
	$cmd = $dbhDev->prepare($sql);
	$cmd->execute();
	echo "inserted {$i}";
	$sql = "select renta FROM su_contracts where id = {$record_id};";
	$cmd = $dbhDev->prepare($sql);
	$cmd->execute();
	$resultData = $cmd->fetchAll();
	var_export($resultData[0]['renta']); 
if ($i == $resultData[0]['renta']) {
	echo " OK ";
}else{
	echo " PROBLEM!!! ";
}
	echo "\n";
}
?>