<?php

use Plugins\Core\UserDb\UserDbController;

/**
 * Shell index file.
 *
 * <AUTHOR> Ltd. <<EMAIL>>
 * @link http://www.devision.bg/
 * @copyright Copyright &copy; 2008 Devision Ltd.
 *
 *
 * @DEPRACATED
 * @DEPRECATION NOTE fuction should not be used. Plugin should be instantiated  with new $plugin();
 */

function getPluginInstance($plugin)
{
	preg_match('/^(.*)\.([^\.]+)$/', $plugin, $split);
	$pluginPath = $split[1];
	$pluginName = $split[2];
	$controllerName = $pluginName.'Controller';
	
	Prado::using($plugin.'.*');
	Prado::using($plugin.'.conf');
	return new $controllerName($pluginName);
}

/**
 * @param $database
 * @return UserDbController
 *
 * @DEPRACATED
 * @DEPRECATION NOTE fuction should not be used. Plugin should be instantiated  with new $plugin();
 */
function getPluginInstanceUserDb($database)
{
    Prado::using('Plugins.Core.UserDb.*');
    Prado::using('Plugins.Core.UserDb.conf');           
    return new UserDbController($database);
}

/**
 * @param $module
 * @param $database
 * @return mixed
 *
 * @DEPRACATED
 * @DEPRECATION NOTE fuction should not be used. Plugin should be instantiated  with new $plugin();
 */
function getPluginInstanceModuleUserDb($module, $database) {
	$controller = 'UserDb' . $module . 'Controller';
	Prado::using('Plugins.Core.UserDb' . $module . '.*');
	return new $controller($database);
}

$prefix = '';

mb_internal_encoding('utf-8');
mb_regex_encoding('utf-8');

include __DIR__.'/../vendor/autoload.php';

$dotenv = new \Dotenv\Dotenv(__DIR__.'/..');
$dotenv->load();

$dir = realpath(dirname(__FILE__)) . '/../';
require_once($dir.'config/global.config.php');
require_once($dir.'config/templates.config.php');

if (file_exists($prefix.FRAMEWORK_PATH)) require_once($prefix.FRAMEWORK_PATH);
elseif (file_exists(SITE_PATH.FRAMEWORK_PATH)) require_once(SITE_PATH.FRAMEWORK_PATH);
else require_once(FRAMEWORK_PATH);


Prado::setPathOfAlias('Common', realpath(dirname(__FILE__)) . '/../protected/Common');
Prado::using('Common.Config');

$application=new TShellApplication(SITE_PATH.'/protected/application.xml');
$application->run();

?>