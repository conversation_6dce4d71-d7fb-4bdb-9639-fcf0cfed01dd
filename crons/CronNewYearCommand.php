<?php 
namespace TF\Crons;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use TF\Application\Entity\LayerStyles;
use TF\Application\Entity\User;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\Users\UsersController;

//\Prado::using('Plugins.Core.Users.*');
//\Prado::using('Plugins.Core.Farming.conf');
//\Prado::using('Plugins.Core.Farming.*');
//\Prado::using('Plugins.Core.Layers.conf');
//\Prado::using('Plugins.Core.Layers.*');

class CronNewYearCommand extends BaseCommand {

    protected function configure()
    {
        $this
            ->setName('crons:NewYear')
            ->setDescription('New Year');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        define('NEW_YEAR', 8);
        define('DEFAULT_MAX_EXTENT','125190.6162 4573142.7188 631370.3273 4887149.5823');

        $UsersController = new UsersController('Users');
        $LayersController = new LayersController('Layers');
        $FarmingController = new FarmingController('Farming');

        $options = array(
            'return' => array('*'),
            'paid_support' => APP_SUPPORT_YEAR . '-01-01'
        );

        $data = $UsersController->getHomeItems($options);
        $data = $data['data'];
        try {
            for ($i = 0; $i < count($data); $i++) {
                if($data[$i]['level'] != 3) {
                    
                    $id = $data[$i]['id'];
                    $username = $data[$i]['username'];
                    $year = NEW_YEAR;
                    $group_id = $data[$i]['group_id'];
                    
                    $options = array(
                        'return' => array('t.id'),
                        'where' => array(
                            'group_id' => array('column' => 'group_id', 'compare' => '=', 'value' => $group_id)
                        )            
                    );
                    
                    $result = $FarmingController->getFarmings($options);
                    for ($j = 0; $j < count($result); $j++) {
                    
                        $farming = $result[$j]['id'];
                            //echo $username.", ".$year.", ".$farming."\n";
                        $options = array(
                            'farming' => $farming,
                            'year' => $year,
                            'group_id' => $group_id,
                            'layer_type' => 1
                        );
                        $tableName = $LayersController->getTableNameByParams($options);
                        //echo $tableName."\n";
                        if ($tableName) continue;
                            
                        //var_dump($farming);    
                        //dobavqne na sloi zemedelski parcel
                        $tableName = 'layer_zp_' . time();
                        $fields = array();
                        $fields['name'] = "Земеделски парцели";
                        $fields['user_id'] = $id;
                        $fields['farming'] = $farming;
                        $fields['year'] = $year;
                        $fields['extent'] = DEFAULT_MAX_EXTENT;
                        $fields['table_name'] = $tableName;
                        $fields['layer_type'] = 1;
                        $fields['color'] = $LayersController->StringHelper->randomColorCode();
                        $fields['group_id'] = $group_id;
                        $settings['mainData'] = $fields;
                        $lid = $LayersController->addLayerItem($settings);
                        echo "ZP: ".$lid."\n";
                        
                        //dobavqne na sloi dopustimost
                        $tableName = 'layer_dss_' . time();
                        $fields['name'] = "Слой за допустимост";
                        $fields['user_id'] = $id;
                        $fields['farming'] = $farming;
                        $fields['year'] = $year;
                        $fields['extent'] = DEFAULT_MAX_EXTENT;
                        $fields['table_name'] = $tableName;
                        $fields['layer_type'] = 3;
                        $fields['color'] = $LayersController->StringHelper->randomColorCode();
                        $fields['group_id'] = $group_id;
                        $settings['mainData'] = $fields;
                        $lid = $LayersController->addLayerItem($settings);
                        echo "DSS: ".$lid."\n";
                        
                        //dobavqne na sloi komasaciq
                        $fields = array();
                        $tableName = 'layer_kms_' . time();
                        $fields['name'] = "Данни от комасация";
                        $fields['user_id'] = $id;
                        $fields['farming'] = $farming;
                        $fields['year'] = $year;
                        $fields['extent'] = DEFAULT_MAX_EXTENT;
                        $fields['table_name'] = $tableName;
                        $fields['layer_type'] = 4;
                        $fields['color'] = $LayersController->StringHelper->randomColorCode();
                        $fields['group_id'] = $group_id;
                        $settings['mainData'] = $fields;
                        $lid = $LayersController->addLayerItem($settings);
                        echo "KMS: ".$lid."\n";
                
                        //dobavqne na ISAK
                        $fields = array();
                        $tableName = 'layer_isak_' . time();
                        $fields['name'] = "ИСАК";
                        $fields['user_id'] = $id;
                        $fields['farming'] = $farming;
                        $fields['year'] = $year;
                        $fields['extent'] = DEFAULT_MAX_EXTENT;
                        $fields['table_name'] = $tableName;
                        $fields['layer_type'] = 6;
                        $fields['color'] = $LayersController->StringHelper->randomColorCode();
                        $fields['group_id'] = $group_id;
                        $settings['mainData'] = $fields;
                        $lid = $LayersController->addLayerItem($settings);
                        echo "ISAK: ".$lid."\n";
                        sleep(2);
                    }

                    /**
                     * @var User
                     */
                    $organization = User::finder()->find('group_id = :group_id', [':group_id' => $group_id]);
                    [$subUser] = $organization->getSubUsers();
                    LayerStyles::addUserLayersStyles($organization->id, $subUser->getId());
                    
                    sleep(2);
                    echo "\n\n";
                    $options = array();
                    $options["database"] = "db_" . strtolower($username);
                    $options["user_id"] = $id;
                    $options["maxextent"] = DEFAULT_MAX_EXTENT;
                    $LayersController->generateMapFile($options);   
                    
                    // system("/bin/chown -R www-data:www-data " . WMS_MAP_PATH);
                }
            }
        } catch (Exception $e) {  
            echo($e);
        }

    }
}
