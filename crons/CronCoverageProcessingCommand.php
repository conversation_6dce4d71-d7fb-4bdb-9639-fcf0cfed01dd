<?php 
 namespace TF\Crons;
 use Prado\Prado;
 use Symfony\Component\Console\Command\Command as BaseCommand;
 use Symfony\Component\Console\Input\InputArgument;
 use Symfony\Component\Console\Input\InputInterface;
 use Symfony\Component\Console\Input\InputOption;
 use Symfony\Component\Console\Output\OutputInterface;
use TF\Engine\Kernel\CronsLoadData\CoverageProcessingClass;
 use TF\Engine\Kernel\CronsLoadData\KvsProcessingClass;

 class CronCoverageProcessingCommand extends BaseCommand {

    protected function configure()
    {
        $this
            ->setName('crons:CoverageProcessing')
            ->setDescription('Zarejdane na Agrotehnica danni.');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        /** @var CoverageProcessingClass $objCoverageProcessing */
        $objCoverageProcessing = Prado::getApplication()->getModule('CoverageProcessingClass');

        $objCoverageProcessing->startProcessing();
    }
}
