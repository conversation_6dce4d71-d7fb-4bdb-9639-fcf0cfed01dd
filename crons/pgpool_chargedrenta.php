<?php
require_once('../config/global.config.php');

//user data
$user_database = 'db_tf_qa';

//technofarm settings (login 3)
$dbLogin3 = new PDO("pgsql:host=" . DEFAULT_DB_HOST . ";port=" . DEFAULT_DB_PORT . ";dbname=".$user_database.";", DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
$dbLogin3->setAttribute(PDO::ATTR_EMULATE_PREPARES, true);
$dbLogin3->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_WARNING);

$sql = $dbLogin3->prepare(
    "SELECT
	  C . ID AS contract_id,
	  o. ID AS owner_id,
	  ARRAY_AGG (DISTINCT(kvs.gid)) AS plot_ids,
	  (
	    CASE
	    WHEN (
	      SELECT
	        ltree2text (subpath(PATH, 0, 1)) :: INTEGER
	      FROM
	        su_heritors
	      WHERE
	        PATH ~ ('*.' || o. ID) :: lquery
	      AND nlevel (PATH) = (
	        SELECT
	          MAX (nlevel(PATH))
	        FROM
	          su_heritors
	        WHERE
	          TRUE
	        AND PATH ~ ('*.' || o. ID) :: lquery
	      )
	    ) IS NOT NULL THEN
	      (
	        SELECT
	          ltree2text (subpath(PATH, 0, 1)) :: INTEGER
	        FROM
	          su_heritors
	        WHERE
	          PATH ~ ('*.' || o. ID) :: lquery
	        AND nlevel (PATH) = (
	          SELECT
	            MAX (nlevel(PATH))
	          FROM
	            su_heritors
	          WHERE
	            TRUE
	          AND PATH ~ ('*.' || o. ID) :: lquery
	        )
	      )
	    ELSE
	      o. ID
	    END
	  ) AS parent_id
	FROM
	  su_contracts C
	LEFT JOIN (
	  SELECT DISTINCT
	    ON (parent_id) *
	  FROM
	    su_contracts
	  WHERE
	    is_annex = TRUE
	  AND active = TRUE
	  AND start_date <= '2016-09-30'
	  AND due_date >= '2015-10-01'
	  ORDER BY
	    parent_id,
	    due_date DESC
	) A ON A .parent_id = C . ID
	INNER JOIN su_contracts_plots_rel pc ON (
	  pc.contract_id = (
	    CASE
	    WHEN A . ID IS NULL THEN
	      C . ID
	    ELSE
	      A . ID
	    END
	  )
	)
	INNER JOIN layer_kvs kvs ON (kvs.gid = pc.plot_id)
	INNER JOIN su_plots_owners_rel po ON (po.pc_rel_id = pc. ID)
	INNER JOIN su_owners o ON (o. ID = po.owner_id)
	LEFT JOIN su_owners_reps o_r ON (o_r. ID = po.rep_id)
	LEFT JOIN su_charged_renta cr ON (
	  cr.contract_id = C . ID
	  AND cr.plot_id = kvs.gid
	  AND cr. YEAR = '7'
	)
	WHERE
	  TRUE
	AND C .is_sublease = 'FALSE'
	AND C .is_annex = 'FALSE'
	AND C .active = 'TRUE'
	AND C .nm_usage_rights IN (2, 3, 5)
	AND po.is_heritor = 'FALSE'
	AND C .c_num = '777'
	AND (
	  C .start_date <= '2016-09-30'
	  OR A .start_date <= '2016-09-30'
	)
	AND (
	  C .due_date >= '2015-10-01'
	  OR A .due_date >= '2015-10-01'
	)
	GROUP BY
	  C . ID,
	  o. ID"
);
$sql->execute();
$contracts = $sql->fetchAll();

for($j = 0; $j < count($contracts); $j++)
{
	$contract = $contracts[$j];

	$contract['plot_ids'] = trim($contract['plot_ids'], '{}');
	$plot_id_array = explode(',', $contract['plot_ids']); 
	$plotIdsCount = count($plot_id_array);
	$owner_id = $contract['owner_id'];
	$contract_id = $contract['contract_id'];

	for($i = 0; $i < $plotIdsCount; $i++) 
	{
    	$plot_id = $plot_id_array[$i];

    	$sql = $dbLogin3->prepare(
		    "UPDATE su_charged_renta
				SET renta_nat = 1,
				  nat_is_converted = FALSE,
				  nat_unit_price = 0,
				  renta = 30
				WHERE
					contract_id = {$contract_id}
				AND plot_id = {$plot_id}
				AND YEAR = 7
				AND owner_id = {$owner_id}"
				);
		$sql->execute();

		$sql = $dbLogin3->prepare(
	    "SELECT * FROM su_charged_renta 
	    	WHERE true AND contract_id = {$contract_id}
	    	AND plot_id = {$plot_id} 
	    	AND year = '7' 
	    	AND owner_id = {$owner_id}"
		);
		$sql->execute();
		$chargedRentaArr[] = $sql->fetchAll();
    }
}

$sql = $dbLogin3->prepare(
    "INSERT INTO su_charged_renta_params (type, date, farming_year, c_num, natura, renta) VALUES ('icn_test', '2016-03-01', '7', '777', 'all', '30.00')
   		RETURNING id;"
);
$sql->execute();
$id = $sql->fetchColumn();

for ($i=0; $i < count($chargedRentaArr); $i++) 
{ 
	$chargedRenta = $chargedRentaArr[$i][0];
	$plot_id = $chargedRenta['plot_id'];
	$owner_id = $chargedRenta['owner_id'];
	$contract_id = $chargedRenta['contract_id'];

	$sql = $dbLogin3->prepare(
	    "SELECT
			SUM (
				CASE
				WHEN cr.owner_id = o. ID THEN
					cr.renta * contract_area * percent / 100
				ELSE
					0
				END
			) AS charged_renta,
			(
				pc.contract_area * po.percent / 100
			) AS owner_area
		FROM
			su_contracts C
		LEFT JOIN su_contracts A ON (
			A .parent_id = C . ID
			AND A .active = TRUE
			AND A .start_date <= '2016-09-30'
			AND A .due_date >= '2015-10-01'
		)
		INNER JOIN su_contracts_plots_rel pc ON (
			pc.contract_id = (
				CASE
				WHEN A . ID IS NULL THEN
					C . ID
				ELSE
					A . ID
				END
			)
		)
		INNER JOIN layer_kvs kvs ON (kvs.gid = pc.plot_id)
		INNER JOIN su_plots_owners_rel po ON (po.pc_rel_id = pc. ID)
		INNER JOIN su_owners o ON (o. ID = po.owner_id)
		LEFT JOIN su_charged_renta cr ON (
			cr.contract_id = C . ID
			AND cr.plot_id = kvs.gid
			AND cr. YEAR = '7'
			AND cr.owner_id = o. ID
		)
		LEFT JOIN su_owners_reps rep ON (rep. ID = po.rep_id)
		WHERE
			TRUE
		AND pc.plot_id = {$plot_id}
		AND po.owner_id = {$owner_id}
		AND C . ID IN ({$contract_id})
		AND (
			C .start_date <= '2016-09-30'
			OR A .start_date <= '2016-09-30'
		)
		AND (
			C .due_date >= '2015-10-01'
			OR A .due_date >= '2015-10-01'
		)
		GROUP BY pc.contract_area, po.percent"
	);
	$sql->execute();
	$resultsPayments = $sql->fetchAll();
	$resultPayment = $resultsPayments[0];

	$charged_renta = $resultPayment['charged_renta'];
	$sql = $dbLogin3->prepare(
	    "INSERT INTO su_charged_renta_history (params_id, contract_id, owner_id, charged_renta) VALUES ({$id}, {$contract_id}, {$owner_id}, {$charged_renta});"
	);
	$sql->execute();
}