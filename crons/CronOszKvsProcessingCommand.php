<?php

namespace TF\Crons;

use Prado\Prado;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use TF\Engine\Kernel\CronsLoadData\KvsOszProcessingClass;

class CronOszKvsProcessingCommand extends BaseCommand
{
    protected function configure()
    {
        $this
            ->setName('crons:OszKvsProcessing')
            ->setDescription('Zarejdane na KVS OSZ.');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        /** @var KvsOszProcessingClass $objOszKvsProcessing */
        $objOszKvsProcessing = Prado::getApplication()->getModule('KvsOszProcessingClass');

        $objOszKvsProcessing->startProcessing();
    }
}
