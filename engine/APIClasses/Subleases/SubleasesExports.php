<?php

namespace TF\Engine\APIClasses\Subleases;

use Exception;
use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\ExportWordDocClass;
use TF\Engine\Kernel\PrintPdf;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbContracts\UserDbContractsController;
use TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController;
use TF\Engine\Plugins\Core\UserDbSubleases\UserDbSubleasesController;
use TF\Engine\Plugins\Core\Users\UsersController;
use ZipArchive;

/**
 * Creates the export files for printing subleased contracts.
 *
 * @rpc-module Subleases
 *
 * @rpc-service-id subleases-exports
 *
 * @property UserDbSubleasesController $UserDbSubleasesController
 * @property UserDbController $UserDbController
 * @property UsersController $UsersController
 * @property FarmingController $FarmingController
 */
class SubleasesExports extends TRpcApiProvider
{
    /**
     * Initialize the required database controllers.
     */
    private $UserDbSubleasesController;
    private $UserDbController;
    private $UsersController;
    private $FarmingController;

    /**
     * Register all public rpc methods.
     */
    public function registerMethods()
    {
        return [
            'exportContractBlank' => ['method' => [$this, 'exportContractBlank']],
            'deleteFile' => ['method' => [$this, 'deleteFile']],
        ];
    }

    /**
     * Creates sublease export file for the selected sublease with the selected template.
     *
     * @api-method exportContractBlank
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer template_id
     *                         #item integer sublease_id
     *                         #item string  blank_type
     *                         }
     *
     * @return string
     */
    public function exportContractBlank($rpcParams)
    {
        $FarmingController = new FarmingController('Farming');
        $UsersController = new UsersController('Users');
        $UserDbSubleasesController = new UserDbSubleasesController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);
        // get template data
        $options = [
            'tablename' => $UserDbController->DbHandler->tableTemplates,
            'where' => [
                'template_id' => ['column' => 'id', 'compare' => '=', 'value' => $rpcParams['template_id']],
            ],
        ];

        $template = $UserDbController->getItemsByParams($options, false, false);

        if (0 == count($template)) {
            return;
        }

        if (!empty($rpcParams['contractsData'])) {
            return $this->generateArchiveWithContracts($template[0], $rpcParams['contractsData'], $rpcParams);
        }

        return $this->generateSingleContract($template[0], $rpcParams['sublease_id'], $rpcParams);
    }

    /**
     * Delete created file.
     *
     * @api-method deleteFile
     *
     * @param string $fileName
     */
    public function deleteFile($fileName)
    {
        unlink(PUBLIC_UPLOAD . 'blanks/' . $this->User->GroupID . '/' . $fileName);
    }

    private function generateSingleContract($template, $contract_id, $rpcParams)
    {
        $document = $this->getTemplate($template['html'], $contract_id);

        $blankName = $template['title'];
        $blankName = str_replace(';', '', $blankName);
        $blankName = preg_replace('/\s+/', '_', $blankName);
        $FarmingController = new FarmingController('Farming');
        $blankName = $FarmingController->StringHelper->transLitString($blankName);
        $date = date('Y-m-d-H-i-s');
        $filename = 'dogovor_' . $blankName . '_' . $this->User->UserID . '_' . $date;

        if ('pdf' == $rpcParams['blank_type']) {
            $headerTxt = $this->getHeaderFooterTag('page_header', $document);
            $footerTxt = $this->getHeaderFooterTag('page_footer', $document);
            $headerTxt = $this->formatPDFHeaderFooter('page_header', $headerTxt);
            $footerTxt = $this->formatPDFHeaderFooter('page_footer', $footerTxt);
            $document = $headerTxt . $document . $footerTxt;
            $document = '<page style="font-family: freeserif" backtop="50px" backbottom="50px" >' . $document . '</page>';

            if (!file_exists(PUBLIC_UPLOAD . '/blanks/' . $this->User->GroupID . '/')) {
                mkdir(PUBLIC_UPLOAD . '/blanks/' . $this->User->GroupID, 0775, true);
            }

            $newPdfFileName = $filename . '.pdf';
            $newPDFFilePath = 'files/uploads/blanks/' . $this->User->GroupID . '/' . $newPdfFileName;

            $printPdf = new PrintPdf();
            $printPdf->generateFromHtml($document, $newPDFFilePath, ['pageNumber' => $template['show_page_numbers']], true);

            $return['file_path'] = $newPDFFilePath;
            $return['file_name'] = $newPdfFileName;

            return $return;
        }

        if ('doc' == $rpcParams['blank_type']) {
            $docOpts = [
                'sections' => [
                    'WordSection' => [
                        'size' => '21cm 29.7cm',
                        'margin' => '1.1cm 2cm 1.5cm 2cm',
                        'mso-page-orientation' => 'portrait',
                    ],
                ],
            ];
            $exportWordDoc = new ExportWordDocClass();

            if (!empty($template['show_page_numbers'])) {
                $document = $exportWordDoc->addPageNumbers($document, $template['show_page_numbers']);
            }

            $newWordDoc = $exportWordDoc->export($filename, $document, true, $docOpts);
            $return['file_path'] = $newWordDoc;
            $return['file_name'] = $filename . '.doc';

            return $return;
        }
    }

    private function generateArchiveWithContracts($template, $contractsData, $rpcParams)
    {
        $FarmingController = new FarmingController('Farming');
        $zip = new ZipArchive();
        $dir = LAYERS_CONTRACTS_PATH . 'blanks/' . $this->User->GroupID;

        if (!is_dir($dir)) {
            mkdir($dir, 0755);
        }

        $archiveName = 'dogovori_' . date('Y-m-d-H-i-s');
        $archievePath = $dir . '/' . $archiveName;

        $zipFile = $archievePath . '.zip';
        $zip->open($zipFile, ZipArchive::CREATE);

        $return['file_path'] = str_replace(SITE_PATH . 'public/', '', $zipFile);
        $return['file_name'] = $archiveName . '.zip';
        $tempFiles = [];

        try {
            if ('pdf' === $rpcParams['blank_type']) {
                foreach ($contractsData as $contract) {
                    $generatedTemplate = $this->getTemplate($template['html'], $contract['id']);
                    $filename = 'dogovor_' . $FarmingController->StringHelper->transLitString($contract['c_num']) . '_' . $contract['id'] . '.pdf';
                    $newPDFFilePath = $dir . '/' . $filename;

                    $printPdf = new PrintPdf();
                    $printPdf->generateFromHtml($generatedTemplate, $newPDFFilePath);

                    $tempFiles[] = $newPDFFilePath;

                    $zip->addFile($newPDFFilePath, $filename);
                }
            }

            if ('doc' === $rpcParams['blank_type']) {
                $exportWordDoc = new ExportWordDocClass();
                foreach ($contractsData as $contract) {
                    $generatedTemplate = $this->getTemplate($template['html'], $contract['id']);
                    $filename = 'dogovor_' . $FarmingController->StringHelper->transLitString($contract['c_num']) . '_' . $contract['id'];
                    $newWordDoc = $exportWordDoc->export($filename, $generatedTemplate, true);
                    $tempFiles[] = $newWordDoc;

                    $zip->addFile($newWordDoc, $filename . '.doc');
                }
            }
        } catch (Exception $e) {
            $zip->close();
            deleteFiles($tempFiles);

            throw $e;
        }

        $zip->close();
        deleteFiles($tempFiles);

        return $return;
    }

    private function getTemplate($template, $contract_id)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        $FarmingController = new FarmingController('Farming');
        $UserDbSubleasesController = new UserDbSubleasesController($this->User->Database);
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);
        $UserDbContractsController = new UserDbContractsController($this->User->Database);
        // get contract data
        $options = [
            'tablename' => $UserDbController->DbHandler->tableContracts,
            'return' => ['su_contracts.*', 'cg.name as group_name'],
            'where' => [
                'contract_id' => ['column' => 'su_contracts.id', 'compare' => '=', 'value' => $contract_id],
            ],
        ];

        $options['leftjoin'] = [
            'table' => 'su_contract_group cg',
            'condition' => ' ON (cg.id = su_contracts.group)',
        ];

        $contract_results = $UserDbController->getItemsByParams($options, false, false);

        if (0 == count($contract_results)) {
            $this->Response->reload();
        }

        $contractData = $contract_results[0];

        // get renta_natura data
        if (strstr($template, '[[renta_v_natura')) {
            // get renta natura types and create predefined array
            $renta_types = [];
            // options for renta nat types
            $options = [
                'tablename' => $UserDbController->DbHandler->tableRentaTypes,
            ];
            $renta_results = $UserDbController->getItemsByParams($options, false, false);
            $rentaCount = count($renta_results);
            for ($i = 0; $i < $rentaCount; $i++) {
                $renta_types[$renta_results[$i]['id']]['name'] = $renta_results[$i]['name'];
                $renta_types[$renta_results[$i]['id']]['unit'] = $GLOBALS['Contracts']['renta_units'][$renta_results[$i]['unit']]['name'];
            }
            $options = [
                'tablename' => $UserDbController->DbHandler->contractsRentsRelTable,
                'where' => [
                    'contract_id' => ['column' => 'contract_id', 'compare' => '=', 'value' => $contract_id],
                ],
            ];

            $renta_results = $UserDbController->getItemsByParams($options, false, false);

            $renta_regex = "/\\[\\[renta_v_natura ?([0-9\s]*)\\]\\]/";
            preg_match_all($renta_regex, $template, $renta_matches);

            foreach ($renta_matches[1] as $key => $match) {
                // $match = id-ta na renti ot saotvetniq template
                $renta_nat_ids[$key] = explode(' ', $match);

                $contractData['renta_natura'][$key] = '';
                if ('' == $renta_nat_ids[$key][0]) {
                    // nqma rachno dobaveni renti
                    $contractData['renta_natura'][$key] = array_map(function ($contract_renta_natura) use ($renta_types) {
                        return $renta_types[$contract_renta_natura['renta_id']]['name'] . ' ' . $contract_renta_natura['renta_value'] . ' ' . $renta_types[$contract_renta_natura['renta_id']]['unit'] . '/дка';
                    }, $renta_results);
                } else {
                    $contractData['renta_natura'][$key] = array_map(function ($template_renta_id) use ($renta_types, $renta_results) {
                        $rentaCount = count($renta_results);
                        for ($i = 0; $i < $rentaCount; $i++) {
                            if ($renta_results[$i]['renta_id'] == $template_renta_id) {
                                $rentaValue = $renta_results[$i]['renta_value'];
                            }
                        }

                        if (null == $rentaValue) {
                            return;
                        }

                        return $renta_types[$template_renta_id]['name'] . ' ' . $rentaValue . ' ' . $renta_types[$template_renta_id]['unit'] . '/дка';
                    }, $renta_nat_ids[$key]);
                }

                $contractData['renta_natura'][$key] = array_filter($contractData['renta_natura'][$key], function ($value) {
                    return null != $value;
                });

                $contractData['renta_natura'][$key] = implode(', ', $contractData['renta_natura'][$key]);
            }
        }
        // get farming
        $options = [
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $contractData['farming_id']],
            ],
        ];

        $farming_results = $FarmingController->getFarmings($options, false, false);

        if (0 == count($farming_results)) {
            $this->Response->reload();
        }

        // get owners and owner_reps data
        if (false !== strpos($template, '[[kontragent]]') || false !== strpos($template, '[[sublease]]')) {
            // get all group farmings and create array like predefined config
            $final_farming = [];
            // options for farming query
            $options = [
                'where' => [
                    'group_id' => ['column' => 'group_id', 'compare' => '=', 'value' => $this->User->GroupID],
                ],
            ];
            $farming_array = $FarmingController->getFarmings($options);
            $farmingCount = count($farming_array);
            if (0 != $farmingCount) {
                for ($i = 0; $i < $farmingCount; $i++) {
                    $final_farming[$farming_array[$i]['id']] = $farming_array[$i];
                }
            }
            $contragentData = null;
            $options = [
                'return' => [
                    'DISTINCT(o.id) as owner_id', 'o.mol', 'cc.proxy_num',
                    "(CASE WHEN owner_type = 1 THEN name || ' ' || surname || ' ' || lastname ELSE company_name END) as owner_names",
                    "(CASE WHEN owner_type = 1 THEN 'ЕГН ' || egn ELSE 'ЕИК ' || eik END) as egn_eik",
                    '(CASE WHEN owner_type = 1 THEN o.address ELSE o.company_address END) as address',
                    "to_char(cc.proxy_date,'DD.MM.YYYY') as proxy_date",
                    "(CASE WHEN r.owner_id <> o.id OR r.owner_id IS NULL THEN r.rep_name || ' ' || r.rep_surname || ' ' || r.rep_lastname || ', с ЕГН ' || r.rep_egn END) as rep_names",
                    'notary_name', 'notary_number', 'notary_address',
                ],
                'where' => [
                    'sublease_id' => ['column' => 'contract_id', 'compare' => '=', 'prefix' => 'cc', 'value' => $contract_id],
                ],
            ];

            $owner_results = $UserDbSubleasesController->getSubleasesContragentsData($options, false, false);
            $ownerCount = count($owner_results);
            for ($i = 0; $i < $ownerCount; $i++) {
                if (1 == count($owner_results)) {
                    $contragentData .= $owner_results[$i]['owner_names'] . ', ' . $owner_results[$i]['egn_eik'];
                } else {
                    $contragentData .= ($i + 1) . '. ' . $owner_results[$i]['owner_names'] . ', ' . $owner_results[$i]['egn_eik'];
                }
                $contragentData .= $owner_results[$i]['mol'] ? ', с управител ' . $owner_results[$i]['mol'] : '';
                $contragentData .= $owner_results[$i]['address'] ? ', адрес ' . $owner_results[$i]['address'] : '';
                $contragentData .= $owner_results[$i]['rep_names'] ? ', представляван от ' . $owner_results[$i]['rep_names'] : '';
                $contragentData .= $owner_results[$i]['proxy_num'] ? ', съгласно пълномощно № ' . $owner_results[$i]['proxy_num'] . '/' . $owner_results[$i]['proxy_date'] : '';
                $contragentData .= $owner_results[$i]['notary_name'] ? ' Заверено от нотариус ' . $owner_results[$i]['notary_name'] : '';
                $contragentData .= $owner_results[$i]['notary_name'] ? ' вписан в регистъра на нотариалната камара под № ' . $owner_results[$i]['notary_number'] : '';
                $contragentData .= $owner_results[$i]['notary_address'] ? ' с адрес ' . $owner_results[$i]['notary_address'] : '';
                $contragentData .= ($i == count($owner_results) - 1) ? '' : ';<br>';
            }
            $options = [
                'return' => [
                    'cc.id', 'cc.farming_id', 'cc.proxy_num', "to_char(cc.proxy_date,'DD.MM.YYYY') as proxy_date",
                    "r.rep_name || ' ' || r.rep_surname || ' ' || r.rep_lastname as rep_names",
                ],
                'where' => [
                    'sublease_id' => ['column' => 'contract_id', 'compare' => '=', 'prefix' => 'cc', 'value' => $contract_id],
                ],
            ];

            $results = $UserDbSubleasesController->getSubleasesFarmingContragentsData($options);
            $resultsCount = count($results);
            for ($i = 0; $i < $resultsCount; $i++) {
                $results[$i]['farming'] = $final_farming[$results[$i]['farming_id']]['name'];
                $results[$i]['mol'] = $final_farming[$results[$i]['farming_id']]['mol'];
                $results[$i]['address'] = $final_farming[$results[$i]['farming_id']]['address'];
                $results[$i]['bulstat'] = $final_farming[$results[$i]['farming_id']]['bulstat'];
                $contragentData .= $results[$i]['farming'];
                $contragentData .= $results[$i]['mol'] ? ', с управител ' . $results[$i]['mol'] : '';
                $contragentData .= $results[$i]['address'] ? ', адрес ' . $results[$i]['address'] : '';
                $contragentData .= $results[$i]['bulstat'] ? ', ЕИК ' . $results[$i]['bulstat'] : '';
                $contragentData .= $results[$i]['rep_names'] ? ', представляван от ' . $results[$i]['rep_names'] : '';
                $contragentData .= $results[$i]['proxy_num'] ? ', съгласно пълномощно № ' . $results[$i]['proxy_num'] . '/' . $results[$i]['proxy_date'] : '';
                $contragentData .= ($i == count($results) - 1) ? '' : ';<br>';
            }

            $subleaseContragentData = '';
            $subleaseContragentData .= $farming_results[0]['name'];
            $subleaseContragentData .= $farming_results[0]['mol'] ? ', с управител ' . $farming_results[0]['mol'] : '';
            $subleaseContragentData .= $farming_results[0]['address'] ? ', адрес ' . $farming_results[$i]['address'] : '';
            $subleaseContragentData .= $farming_results[0]['bulstat'] ? ', ЕИК ' . $farming_results[$i]['bulstat'] : '';
            $subleaseContragentData .= $farming_results[0]['rep_names'] ? ', представляван от ' . $farming_results[0]['rep_names'] : '';
            $subleaseContragentData .= $farming_results[0]['proxy_num'] ? ', съгласно пълномощно № ' . $farming_results[0]['proxy_num'] . '/' . $farming_results[0]['proxy_date'] : '';
            $subleaseContragentData .= ';<br>';
        }

        if (strstr($template, '[[zemlishte_ekatte]]')) {
            $ekateNames = $UserDbSubleasesController->getEkateNamesForSubleaseContract($contract_id);
            $tmpEkateString = '';
            $ekateCount = count($ekateNames);
            for ($ekN = 0; $ekN < $ekateCount; $ekN++) {
                $tmpEkateString .= $ekateNames[$ekN]['zemlishte_ekatte'];

                if ($ekN < $ekateCount - 1) {
                    $tmpEkateString .= ', ';
                }
            }

            $template = str_replace('[[zemlishte_ekatte]]', $tmpEkateString, $template);
        }

        if (strstr($template, '[[total_subleases_renta]]')) {
            $subleaseRent = $UserDbSubleasesController->getSubleaseMoneyToCollect(['sublease_id' => $contract_id]);
            $subleaseRent = array_shift($subleaseRent);

            $template = str_replace('[[total_subleases_renta]]', BGNtoEURO($subleaseRent['money_to_collect']), $template);
        }

        // get plots data and create table
        if (strstr($template, '[[imoti]]') || strstr($template, '[[imoti_zemlishta]]') || strstr($template, '[[imoti_podrobno')) {
            $options = [
                'return' => [
                    'gid', 'kad_ident', 'old_kad_ident', 'virtual_ntp_title as area_type', 'virtual_category_title as category', 'mestnost', 'number', 'ekate', 'virtual_ekatte_name as land', 'kvs.allowable_area as allowable_area',
                    'spa.contract_area as contract_area',
                    'coalesce(spa.rent_area, spa.contract_area) as area_for_rent',
                    'document_area',
                    'array_agg(DISTINCT P.c_num) AS c_num',
                    'array_agg(DISTINCT TO_CHAR(P .c_date, \'dd.mm.yyyy\')) AS c_date',
                    "LPAD(masiv::text, 3, '0') || LPAD(number::text, 3, '0') as imot_number",
                    "LPAD(masiv::text, 3, '0') || LPAD(number::text, 3, '0') as imoten_nomer",
                ],
                'where' => [
                    'sublease_id' => ['column' => 'sublease_id', 'compare' => '=', 'prefix' => 'spc', 'value' => $contract_id],
                ],
                'sort' => 'kad_ident COLLATE "alpha_numeric_bg"',
                'order' => 'asc',
                'group' => 'gid,spa.contract_area,spa.rent_area',
                'having' => 'count(CASE WHEN c2.id IS NULL THEN 1 ELSE NULL::integer END) = 0',
            ];

            $plotsResults = $UserDbSubleasesController->getSubleasePlotsData($options, false, false);
            $plotsCount = count($plotsResults);
            $ekates = [];
            for ($i = 0; $i < $plotsCount; $i++) {
                if (!in_array($plotsResults[$i]['ekate'], $ekates)) {
                    $ekates[] = $plotsResults[$i]['ekate'];
                }
                $plotsResults[$i]['contract_area'] = number_format($plotsResults[$i]['contract_area'], 3, '.', '');
                $plotsResults[$i]['document_area'] = number_format($plotsResults[$i]['document_area'], 3, '.', '');
                $plotsResults[$i]['area_for_rent'] = number_format($plotsResults[$i]['area_for_rent'], 3, '.', '');
                $plotsResults[$i]['c_num'] = trim($plotsResults[$i]['c_num'], '{}"');
                $plotsResults[$i]['c_date'] = trim($plotsResults[$i]['c_date'], '{}"');
                if ('' == $plotsResults[$i]['kad_ident']) {
                    $plotsResults[$i]['kad_ident'] = '[Няма информация]';
                }
            }
            if (strstr($template, '[[imoti_zemlishta]]') && !empty($ekates)) {
                $plots_by_ekatte_string = '';
                $options = [
                    'return' => [
                        'obl.obl_name', 'obs.obsht_name', 'kmet.kmet_name', 'ekatte.ekatte_code',
                    ],
                    'where' => [
                        'ekate' => ['column' => 'ekatte_code', 'compare' => 'IN', 'prefix' => 'ekatte', 'value' => $ekates],
                    ],
                ];

                $plots_by_ekatte = $UsersController->getEkatteOnlyItems($options);
                foreach ($plots_by_ekatte as $key => $value) {
                    $plots_by_ekatte_string .= '<p>Изброените по долу имоти от землището на с./гр.' . $value['kmet_name'] . ', с ЕКАТТЕ ' . $value['ekatte_code'] . ', общ. ' . $value['obsht_name'] . ', обл. ' . $value['obl_name'] . ':<br/>';
                    for ($i = 0; $i < $plotsCount; $i++) {
                        if ($plotsResults[$i]['ekate'] == $value['ekatte_code']) {
                            $plots_by_ekatte_string .= 'Имот';
                            $plots_by_ekatte_string .= (isset($plotsResults[$i]['area_type'])) ? (' с НТП ' . $plotsResults[$i]['area_type']) : '';
                            $plots_by_ekatte_string .= (isset($plotsResults[$i]['contract_area'])) ? (' от ' . $plotsResults[$i]['contract_area'] . ' дка') : '';
                            $plots_by_ekatte_string .= (!empty($plotsResults[$i]['mestnost'])) ? (', находяща се в местността ' . $plotsResults[$i]['mestnost']) : '';
                            $plots_by_ekatte_string .= ' ,идентификатор:' . $plotsResults[$i]['imot_number'];
                            $plots_by_ekatte_string .= '<br/>';
                        }
                    }
                    $plots_by_ekatte_string .= '</p>';
                }
            }

            if (strstr($template, '[[imoti]]') && !empty($ekates)) {
                $plotsData = '<table align="center" cellspacing="0" cellpadding="3" border="1">';
                $plotsData .= '<thead>';
                $plotsData .= '<tr align="center">';
                $plotsData .= '<th>№</th>';
                $plotsData .= '<th>Идентификатор</th>';
                $plotsData .= '<th>Землище</th>';
                $plotsData .= '<th>Местност</th>';
                $plotsData .= '<th>НТП</th>';
                $plotsData .= '<th>Площ по <br/>договор(дка)</th>';
                $plotsData .= '<th>Площ по <br/>документ(дка)</th>';
                $plotsData .= '</tr>';
                $plotsData .= '</thead>';
                $plotsData .= '<tbody>';

                $total_area = 0;
                $total_document_area = 0;

                for ($i = 0; $i < $plotsCount; $i++) {
                    $plotsData .= '<tr>';
                    $plotsData .= '<td width="20" align="center">' . ($i + 1) . '</td>';
                    $plotsData .= '<td width="115" align="center">' . $plotsResults[$i]['kad_ident'] . '</td>';
                    $plotsData .= '<td width="120" align="center">' . $plotsResults[$i]['land'] . '</td>';
                    $plotsData .= '<td width="115" align="center">' . $plotsResults[$i]['mestnost'] . '</td>';
                    $plotsData .= '<td width="180" align="center">' . $plotsResults[$i]['area_type'] . '</td>';
                    $plotsData .= '<td width="70" align="center">' . $plotsResults[$i]['contract_area'] . '</td>';
                    $plotsData .= '<td width="70" align="center">' . $plotsResults[$i]['document_area'] . '</td>';
                    $plotsData .= '</tr>';

                    $total_area += $plotsResults[$i]['contract_area'];
                    $total_document_area += $plotsResults[$i]['document_area'];
                }

                $plotsData .= '<tr>';
                $plotsData .= '<td colspan="4"></td>';
                $plotsData .= '<td width="180" align="center"><b>Общо</b></td>';
                $plotsData .= '<td width="70" align="center"><b>' . number_format($total_area, 3, '.', '') . '</b></td>';
                $plotsData .= '<td width="70" align="center"><b>' . number_format($total_document_area, 3, '.', '') . '</b></td>';
                $plotsData .= '</tr>';

                $plotsData .= '</tbody>';
                $plotsData .= '</table>';
            }

            if (strstr($template, '[[imoti_podrobno') && !empty($ekates)) {
                $regex = "/\[\[imoti_podrobno (.*?)\]\]/";
                preg_match_all($regex, $template, $matches);

                $plotsDetailedDataArr = [];
                foreach ($matches[1] as $key => $match) {
                    $columns = explode(' ', $match);
                    $columnsCount = count($columns);
                    if (0 == $columnsCount) {
                        continue;
                    }
                    $plotsDetailedData = '<table cellspacing="0" cellpadding="3" border="1">';

                    // header
                    $plotsDetailedData .= '<thead>';
                    $plotsDetailedData .= '<tr align="center">';
                    $plotsDetailedData .= '<th>№</th>';

                    foreach ($columns as $keyC => $column) {
                        $plotsDetailedData .= '<th>' . $GLOBALS['Contracts']['variables_plots_detailed'][$column] . '</th>';
                    }

                    $plotsDetailedData .= '</tr>';
                    $plotsDetailedData .= '</thead>';

                    // body
                    $plotsDetailedData .= '<tbody>';

                    $total_area = 0;
                    $total_document_area = 0;
                    $totalAllowableArea = 0;
                    $totalRentArea = 0;
                    for ($i = 0; $i < $plotsCount; $i++) {
                        $plotsResults[$i]['zemlishte'] = $plotsResults[$i]['land'];
                        $plotsResults[$i]['ntp'] = $plotsResults[$i]['area_type'];

                        $plotsDetailedData .= '<tr>';
                        $plotsDetailedData .= '<td style="white-space: nowrap;" align="center">' . ($i + 1) . '</td>';
                        foreach ($columns as $keyCo => $column) {
                            if ('ntp' === $column) {
                                $plotsDetailedData .= '<td width="115" align="center">' . $plotsResults[$i][$column] . '</td>';

                                continue;
                            }
                            $plotsDetailedData .= '<td style="white-space: nowrap;" align="center">' . $plotsResults[$i][$column] . '</td>';
                        }

                        $plotsDetailedData .= '</tr>';

                        if (in_array('contract_area', $columns) || in_array('document_area', $columns) || in_array('allowable_area', $columns) || in_array('area_for_rent', $columns)) {
                            $total_area += $plotsResults[$i]['contract_area'];
                            $total_document_area += $plotsResults[$i]['document_area'];
                            $totalAllowableArea += $plotsResults[$i]['allowable_area'];
                            $totalRentArea += $plotsResults[$i]['area_for_rent'];
                        }
                    }

                    // footer
                    if ($total_area > 0 || $total_document_area > 0 || $totalAllowableArea > 0) {
                        $plotsDetailedData .= '<tr>';

                        // column for numeration
                        $plotsDetailedData .= '<td style="white-space: nowrap;">Общо</td>';

                        for ($m = 0; $m < $columnsCount; $m++) {
                            $column = $columns[$m];

                            if ('contract_area' === $column) {
                                $plotsDetailedData .= '<td style="white-space: nowrap;" align="center"><b>' . number_format($total_area, 3, '.', '') . '</b></td>';
                            } elseif ('document_area' === $column) {
                                $plotsDetailedData .= '<td style="white-space: nowrap;" align="center"><b>' . number_format($total_document_area, 3, '.', '') . '</b></td>';
                            } elseif ('allowable_area' === $column) {
                                $plotsDetailedData .= '<td style="white-space: nowrap;" align="center"><b>' . number_format($totalAllowableArea, 3, '.', '') . '</b></td>';
                            } elseif ('area_for_rent' === $column) {
                                $plotsDetailedData .= '<td style="white-space: nowrap;" align="center"><b>' . number_format($totalRentArea, 3, '.', '') . '</b></td>';
                            } else {
                                $plotsDetailedData .= '<td style="white-space: nowrap;"></td>';
                            }
                        }
                        $plotsDetailedData .= '</tr>';
                    }
                    $plotsDetailedData .= '</tbody>';
                    $plotsDetailedData .= '</table>';
                    $plotsDetailedDataArr[$key] = $plotsDetailedData;
                }
            }
        }

        if (strstr($template, '[[imoti_kategoriq]]')) {
            $plots_by_category_string = $this->plotsByCategoryData($contract_id);
        }

        if (strstr($template, '[[timespan_farming_years]]')) {
            $timespan_message = 'срок от ';
            if ('01' == strftime('%d', strtotime($contractData['start_date']))
                && '10' == strftime('%m', strtotime($contractData['start_date']))
                && '30' == strftime('%d', strtotime($contractData['due_date']))
                && '09' == strftime('%m', strtotime($contractData['due_date']))) {
                $farming_years = strftime('%Y', strtotime($contractData['due_date'])) - strftime('%Y', strtotime($contractData['start_date']));
                $timespan_message = $timespan_message . $farming_years . ((1 == $farming_years) ? ' стопанска година' : ' стопански години');
            } else {
                $timespan_message = $timespan_message . '...... стопански години';
            }
        }
        if (strstr($template, '[[padej]]')) {
            if ($contractData['payday']) {
                $payday = explode('-', $contractData['payday']);
                $contractData['payday'] = $payday[0] . ' ' . $GLOBALS['Months'][$payday[1]];
            } else {
                $contractData['payday'] = '-';
            }
        }

        // replace the data
        $template = str_replace('[[nomer_na_dogovor]]', $contractData['c_num'], $template);
        $template = str_replace('[[tip_na_dogovor]]', $GLOBALS['Contracts']['ContractTypes'][$contractData['nm_usage_rights']]['name'], $template);
        $template = str_replace('[[grupa_na_dogovor]]', $contractData['group_name'], $template);
        $template = str_replace('[[data_na_dogovor]]', date('d.m.Y', strtotime($contractData['c_date'])), $template);
        $template = str_replace('[[vlizane_v_sila]]', date('d.m.Y', strtotime($contractData['start_date'])), $template);
        $template = str_replace('[[kraina_data]]', date('d.m.Y', strtotime($contractData['due_date'])), $template);

        $template = $FarmingController->farmDetailedTemplate($template, $farming_results[0]);

        $template = str_replace('[[stopanstvo]]', $farming_results[0]['name'], $template);
        $template = str_replace('[[stopanstvo_name]]', $farming_results[0]['name'], $template);
        $template = str_replace('[[stopanstvo_address]]', $farming_results[0]['address'], $template);
        $template = str_replace('[[stopanstvo_firma]]', $farming_results[0]['company'], $template);
        $template = str_replace('[[stopanstvo_bulstat]]', $farming_results[0]['bulstat'], $template);
        $template = str_replace('[[stopanstvo_firma_address]]', $farming_results[0]['company_address'], $template);
        $template = str_replace('[[stopanstvo_mol]]', $farming_results[0]['mol'], $template);
        $template = str_replace('[[stopanstvo_mol_egn]]', $farming_results[0]['mol_egn'], $template);
        $iban_arr = json_decode($farming_results[0]['iban_arr'], true);
        $iban_arr = empty($iban_arr) ? '' : implode(',', $iban_arr);
        $template = str_replace('[[stopanstvo_iban_arr]]', $iban_arr, $template);
        $template = str_replace('[[nomer_na_vpisvane]]', $contractData['sv_num'], $template);

        if (!empty($contractData['sv_date'])) {
            $template = str_replace('[[data_na_vpisvane]]', date('d.m.Y', strtotime($contractData['sv_date'])) . 'г.', $template);
        } else {
            $template = str_replace('[[data_na_vpisvane]]', 'Няма въведена', $template);
        }

        $template = str_replace('[[renta]]', BGNtoEURO($contractData['overall_renta'] ?? $contractData['renta']), $template);

        if (
            strstr($template, '[[total_renta]]')
            || strstr($template, '[[obobshtena_rent_area]]')
            || strstr($template, '[[obobshtena_rent_area]]')
            || strstr($template, '[[obobshtena_obrabotvaema_area]]')
        ) {
            $options = [
                'return' => [
                    'SUM(spa.contract_area) as contract_area',
                    'SUM(spa.rent_area) as area_for_rent',
                    'SUM(kvs.allowable_area) as kvs_allowable_area',
                    'max(c.renta) renta',
                ],
                'where' => [
                    'sublease_id' => ['column' => 'sublease_id', 'compare' => '=', 'prefix' => 'spc', 'value' => $contract_id],
                    'active_contract' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'true'],
                ],
                'having' => 'count(CASE WHEN c2.id IS NULL THEN 1 ELSE NULL::integer END) = 0',
            ];

            $plotsResults = $UserDbSubleasesController->getSubleasePlotsData($options, false, false);

            $total_contract_area = floatval($plotsResults[0]['contract_area']);
            $total_rent_area = floatval($plotsResults[0]['area_for_rent']);
            $total_kvs_allowabla_area = floatval($plotsResults[0]['kvs_allowable_area']);
            $total_renta = floatval($plotsResults[0]['renta']);

            $template = str_replace('[[obobshtena_rent_area]]', number_format($total_rent_area, 3, '.', ''), $template);
            $template = str_replace('[[obobshtena_contract_area]]', number_format($total_contract_area, 3, '.', ''), $template);
            $template = str_replace('[[obobshtena_obrabotvaema_area]]', number_format($total_kvs_allowabla_area, 3, '.', ''), $template);
            $template = str_replace('[[total_renta]]', BGNtoEURO($total_renta), $template);
        }

        // renta_v_natura
        $rentCount = count($renta_matches[0]);
        for ($n = 0; $n < $rentCount; $n++) {
            $rentaNatData = $contractData['renta_natura'][$n];
            $allMatchesRentaNat = $renta_matches[0][$n];

            $template = str_replace($allMatchesRentaNat, $rentaNatData, $template);
        }
        $template = str_replace('[[kontragent]]', $subleaseContragentData, $template);
        $template = str_replace('[[sublease]]', $contragentData, $template);
        $template = str_replace('[[imoti]]', $plotsData, $template);
        if (strstr($template, '[[imoti_zemlishta_kadident]]') && !empty($ekates)) {
            $plots_by_ekatte_kadident_string = '';
            $options = [
                'return' => [
                    'obl.obl_name',
                    'obs.obsht_name',
                    'kmet.kmet_name',
                    'ekatte.ekatte_code',
                    'ekatte.ekatte_name',
                ],
                'where' => [
                    'ekate' => [
                        'column' => 'ekatte_code',
                        'compare' => 'IN',
                        'prefix' => 'ekatte',
                        'value' => $ekates,
                    ],
                ],
            ];

            $plots_by_ekatte = $UsersController->getEkatteOnlyItems($options);
            $plotsCount = count($plotsResults);
            foreach ($plots_by_ekatte as $key => $value) {
                $plots_by_ekatte_kadident_string .= '<p>Изброените по долу имоти от землището на с./гр.' . $value['ekatte_name'] . ', с ЕКАТТЕ ' . $value['ekatte_code'] . ', общ. ' . $value['obsht_name'] . ', обл. ' . $value['obl_name'] . ':<br/>';
                $plotsNumByEkate = 1;
                for ($i = 0; $i < $plotsCount; $i++) {
                    if ($plotsResults[$i]['ekate'] == $value['ekatte_code']) {
                        $neighbourOptions = [
                            'return' => [
                                'kvs2.kad_ident',
                                '(case WHEN pc.contract_area is NULL then (ST_Area(kvs2.geom)/1000) else pc.contract_area end) as area',
                                'kvs2.virtual_ntp_title as area_type',
                                "LPAD(kvs2.masiv::text, 3, '0') || LPAD(kvs2.number::text, 3, '0') as imot_number",
                            ],
                            'kad_ident' => $plotsResults[$i]['kad_ident'],
                            'contract_id' => $contract_id,
                        ];
                        $neighbours = $UserDbPlotsController->getPlotNeighbours(
                            $neighbourOptions,
                            false,
                            false
                        );
                        $neighbourString = '';
                        $neighboursCount = count($neighbours);
                        for ($j = 0; $j < $neighboursCount; $j++) {
                            $neighbourString .= '№ ' . $neighbours[$j]['kad_ident'];
                            $neighbourString .= isset($neighbours[$j]['area_type']) ? (' с НТП ' . $neighbours[$j]['area_type']) : '';
                            $neighbourString .= ' с площ ' . number_format(
                                $neighbours[$j]['area'],
                                3,
                                '.',
                                ''
                            ) . ' дка';
                            $neighbourString .= '<br/>';
                        }

                        $plots_by_ekatte_kadident_string .= $plotsNumByEkate . '. ';
                        $plots_by_ekatte_kadident_string .= 'Имот';
                        $plots_by_ekatte_kadident_string .= ', идентификатор ' . $plotsResults[$i]['kad_ident'];
                        $plots_by_ekatte_kadident_string .= isset($plotsResults[$i]['area_type']) ? (' с НТП ' . $plotsResults[$i]['area_type']) : '';
                        $plots_by_ekatte_kadident_string .= isset($plotsResults[$i]['document_area']) ? (' с площ по документ ' . $plotsResults[$i]['document_area'] . ' дка') : '';
                        $plots_by_ekatte_kadident_string .= isset($plotsResults[$i]['contract_area']) ? (', с площ по договор ' . $plotsResults[$i]['contract_area'] . ' дка') : '';
                        $plots_by_ekatte_kadident_string .= !empty($plotsResults[$i]['mestnost']) ? (', находяща се в местността ' . $plotsResults[$i]['mestnost']) : '';

                        $plots_by_ekatte_kadident_string .= !empty($neighbourString) ? ' ,при граници(съседи): <br/>' . $neighbourString : '';
                        $plots_by_ekatte_kadident_string .= '<br/>';
                        $plotsNumByEkate++;
                    }
                }
                $plots_by_ekatte_kadident_string .= '</p>';
            }
        }

        $template = str_replace('[[imoti_zemlishta_kadident]]', $plots_by_ekatte_kadident_string, $template);

        // imoti_podrobno
        $matchCount = count($matches[0]);
        for ($n = 0; $n < $matchCount; $n++) {
            $plotDetailedData = $plotsDetailedDataArr[$n];
            $allMatchesPlotDet = $matches[0][$n];

            $template = str_replace($allMatchesPlotDet, $plotDetailedData, $template);
        }

        if (strstr($template, '[[today')) {
            $template = str_replace('[[today]]', date('d.m.Y'), $template);
        }

        $template = str_replace('[[sv_num]]', $contractData['sv_num'], $template);

        if (!empty($contractData['sv_date'])) {
            $template = str_replace('[[sv_date]]', date('d.m.Y', strtotime($contractData['sv_date'])) . 'г.', $template);
        } else {
            $template = str_replace('[[sv_date]]', 'Няма въведена', $template);
        }

        if (!empty($contractData['osz_date'])) {
            $template = str_replace('[[osz_date]]', date('d.m.Y', strtotime($contractData['osz_date'])) . 'г.', $template);
        } else {
            $template = str_replace('[[osz_date]]', 'Няма въведена', $template);
        }
        $template = str_replace('[[osz_num]]', $contractData['osz_num'], $template);
        $template = str_replace('[[comment]]', $contractData['comment'], $template);

        $template = str_replace('[[imoti_kategoriq]]', $plots_by_category_string, $template);
        $template = str_replace('[[imoti_zemlishta]]', $plots_by_ekatte_string, $template);
        $template = str_replace('[[padej]]', $contractData['payday'], $template);
        $template = str_replace('[[timespan_farming_years]]', $timespan_message, $template);

        return $template;
    }

    private function getHeaderFooterTag($tagName, &$template)
    {
        $tagContentRe = "/\[\[{$tagName}_\]\](?P<content>.*?)\[\[_{$tagName}\]\]/s";
        $matches = [];

        if (!preg_match_all($tagContentRe, $template, $matches)) {
            return '';
        }
        $template = preg_replace($tagContentRe, '', $template);

        return $matches['content'][0];
    }

    private function formatPDFHeaderFooter($tagName, $tagCont)
    {
        $imageRe = "/<img\\s[^>]*?src\\s*=\\s*['\\\"](?:(?P<img_src>[^'\\\"]*?)['\\\"][^>]*?>)/s";
        $matches = [];
        $formatedTag = "<{$tagName}>{$tagCont}</{$tagName}>";

        if (preg_match_all($imageRe, $tagCont, $matches)) {
            $newImage = '<p style="text-align: center"><img src="' . SITE_URL . '\1" /></p>';
            $formatedTag = "<{$tagName}>" . preg_replace($imageRe, $newImage, $tagCont) . "</{$tagName}>";
        }

        return $formatedTag;
    }

    private function plotsByCategoryData($contractId)
    {
        $UserDbSubleasesController = new UserDbSubleasesController($this->User->Database);
        $UsersController = new UsersController('Users');

        $options = [
            'return' => [
                'gid',
                'COALESCE(kad_ident, \'[Няма информация]\') as kad_ident',
                'virtual_ntp_title as area_type',
                'category',
                'virtual_category_title as category_name',
                'COALESCE(mestnost, \'-\') as mestnost',
                'number',
                'ekate',
                'virtual_ekatte_name as land',
                'masiv',
                'round(spa.contract_area::numeric, 3) AS contract_area',
                'round(document_area::numeric, 3) AS document_area',
            ],
            'where' => [
                'contract_id' => ['column' => 'id', 'prefix' => 'c', 'compare' => '=', 'value' => $contractId],
            ],
            'sort' => 'kad_ident COLLATE "alpha_numeric_bg"',
            'order' => 'asc',
        ];

        $plotsResults = $UserDbSubleasesController->getSubleasePlotsData($options, false, false);

        $ekate = [];
        $masiv = [];
        $number = [];

        if (count($plotsResults) > 0) {
            foreach ($plotsResults as $key => $row) {
                $ekate[$key] = $row['ekate'];
                $masiv[$key] = $row['masiv'];
                $number[$key] = $row['number'];
            }

            array_multisort($ekate, SORT_ASC, $masiv, SORT_ASC, $number, SORT_ASC, $plotsResults);
        }

        return $this->plotsByCategoryTemplate($plotsResults);
    }

    private function plotsByCategoryTemplate($plotsResults)
    {
        $plotsData = '<table align="center" cellspacing="0" cellpadding="3" border="1">';
        $plotsData .= '<thead>';
        $plotsData .= '<tr align="center">';
        $plotsData .= '<th>№</th>';
        $plotsData .= '<th>Идентификатор</th>';
        $plotsData .= '<th>Землище</th>';
        $plotsData .= '<th>Категория</th>';
        $plotsData .= '<th>Местност</th>';
        $plotsData .= '<th>НТП</th>';
        $plotsData .= '<th>Площ по <br/>договор(дка)</th>';
        $plotsData .= '<th>Площ по <br/>док. (дка)</th>';
        $plotsData .= '</tr>';
        $plotsData .= '</thead>';
        $plotsData .= '<tbody>';

        $secondCatsTotalArea = 0;
        $secondCatsDocArea = 0;
        $firstCatsTotalArea = 0;
        $firstCatsDocArea = 0;
        $firsCatsPlotsData = '';
        $secondCatsPlotsData = '';

        $lowCategoryCounter = 0;
        $highCategoryCounter = 0;

        $plotsCount = count($plotsResults);
        for ($i = 0; $i < $plotsCount; $i++) {
            if ($plotsResults[$i]['category'] <= 5) {
                $lowCategoryCounter++;
                $firsCatsPlotsData .= '<tr>';
                $firsCatsPlotsData .= '<td width="20" align="center">' . $lowCategoryCounter . '</td>';
                $firsCatsPlotsData .= '<td width="100" align="center">' . $plotsResults[$i]['kad_ident'] . '</td>';
                $firsCatsPlotsData .= '<td width="100" align="center">' . $plotsResults[$i]['land'] . '</td>';
                $firsCatsPlotsData .= '<td width="90" align="center">' . $plotsResults[$i]['category_name'] . '</td>';
                $firsCatsPlotsData .= '<td width="100" align="center">' . $plotsResults[$i]['mestnost'] . '</td>';
                $firsCatsPlotsData .= '<td width="140" align="center">' . $plotsResults[$i]['area_type'] . '</td>';
                $firsCatsPlotsData .= '<td width="70" align="center">' . $plotsResults[$i]['contract_area'] . '</td>';
                $firsCatsPlotsData .= '<td width="60" align="center">' . $plotsResults[$i]['document_area'] . '</td>';
                $firsCatsPlotsData .= '</tr>';

                $firstCatsTotalArea += $plotsResults[$i]['contract_area'];
                $firstCatsDocArea += $plotsResults[$i]['document_area'];
            } elseif ($plotsResults[$i]['category'] > 5) {
                $highCategoryCounter++;
                $secondCatsPlotsData .= '<tr>';
                $secondCatsPlotsData .= '<td width="20" align="center">' . $highCategoryCounter . '</td>';
                $secondCatsPlotsData .= '<td width="100" align="center">' . $plotsResults[$i]['kad_ident'] . '</td>';
                $secondCatsPlotsData .= '<td width="100" align="center">' . $plotsResults[$i]['land'] . '</td>';
                $secondCatsPlotsData .= '<td width="90" align="center">' . $plotsResults[$i]['category_name'] . '</td>';
                $secondCatsPlotsData .= '<td width="100" align="center">' . $plotsResults[$i]['mestnost'] . '</td>';
                $secondCatsPlotsData .= '<td width="140" align="center">' . $plotsResults[$i]['area_type'] . '</td>';
                $secondCatsPlotsData .= '<td width="70" align="center">' . $plotsResults[$i]['contract_area'] . '</td>';
                $secondCatsPlotsData .= '<td width="60" align="center">' . $plotsResults[$i]['document_area'] . '</td>';
                $secondCatsPlotsData .= '</tr>';

                $secondCatsTotalArea += $plotsResults[$i]['contract_area'];
                $secondCatsDocArea += $plotsResults[$i]['document_area'];
            }
        }

        if (0 != $firstCatsTotalArea && 0 != $firstCatsDocArea) {
            $firsCatsPlotsData .= '<tr>';
            $firsCatsPlotsData .= '<td colspan="6"><b>Общо – I-V-та категория</b></td>';
            $firsCatsPlotsData .= '<td width="70" align="center"><b>' . number_format($firstCatsTotalArea, 3, '.', '') . '</b></td>';
            $firsCatsPlotsData .= '<td width="70" align="center"><b>' . number_format($firstCatsDocArea, 3, '.', '') . '</b></td>';
            $firsCatsPlotsData .= '</tr>';
        }

        if (0 != $secondCatsTotalArea && 0 != $secondCatsDocArea) {
            $secondCatsPlotsData .= '<tr>';
            $secondCatsPlotsData .= '<td colspan="6"><b>Общо – над VI-та категория</b></td>';
            $secondCatsPlotsData .= '<td width="70" align="center"><b>' . number_format($secondCatsTotalArea, 3, '.', '') . '</b></td>';
            $secondCatsPlotsData .= '<td width="70" align="center"><b>' . number_format($secondCatsDocArea, 3, '.', '') . '</b></td>';
            $secondCatsPlotsData .= '</tr>';
        }

        $total_area = $firstCatsTotalArea + $secondCatsTotalArea;
        $total_document_area = $firstCatsDocArea + $secondCatsDocArea;

        $plotsData .= $firsCatsPlotsData;
        $plotsData .= $secondCatsPlotsData;

        $plotsData .= '<tr>';
        $plotsData .= '<td colspan="5"></td>';
        $plotsData .= '<td width="140" align="center"><b>Общо</b></td>';
        $plotsData .= '<td width="70" align="center"><b>' . number_format($total_area, 3, '.', '') . '</b></td>';
        $plotsData .= '<td width="60" align="center"><b>' . number_format($total_document_area, 3, '.', '') . '</b></td>';
        $plotsData .= '</tr>';

        $plotsData .= '</tbody>';
        $plotsData .= '</table>';

        return $plotsData;
    }
}
