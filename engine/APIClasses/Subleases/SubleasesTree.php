<?php

namespace TF\Engine\APIClasses\Subleases;

use DateTime;
use Prado\Exceptions\TDbException;
use Prado\Web\Services\TRpcApiProvider;
use TF\Application\Common\Config;
use TF\Application\Entity\ObjectPermissions;
use TF\Application\Entity\UserFarmings;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbContracts\UserDbContractsController;
use TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController;
use TF\Engine\Plugins\Core\UserDbSubleases\UserDbSubleasesController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * @rpc-module Subleases
 *
 * @rpc-service-id subleases-tree
 */
class SubleasesTree extends TRpcApiProvider
{
    private $module = 'Subleases';
    private $service_id = 'subleases-tree';

    private $copy_sublease_id = 0;

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'read'],
                'validators' => [
                    'filterObj' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'deleteSublease' => ['method' => [$this, 'deleteSublease']],
            'changeActiveStatus' => ['method' => [$this, 'changeActiveStatus']],
            'markSubleaseForEdit' => ['method' => [$this, 'markSubleaseForEdit']],
            'checkForExistence' => ['method' => [$this, 'checkForExistence']],
            'saveSublease' => ['method' => [$this, 'saveSublease']],
            'preEdit' => ['method' => [$this, 'preEdit']],
            'initSubleaseCopy' => ['method' => [$this, 'initSubleaseCopy']],
            'hasSubleaseEditedPlots' => ['method' => [$this, 'hasSubleaseEditedPlots']],
            'loadSublease' => ['method' => [$this, 'loadSublease']],
            'copySublease' => ['method' => [$this, 'copySublease']],
        ];
    }

    /**
     * Displays the subleases tree, based on current filter criteria.
     *
     * @api-method read
     *
     * @param array $filterObj filter parameters (if any)
     *                         {
     *                         #item int sublease_id
     *                         #item string c_num
     *                         #item array renta_types
     *                         {
     *                         #items int renta types
     *                         }
     *                         #item date date_from
     *                         #item date date_to
     *                         #item date due_date_from
     *                         #item date due_date_to
     *                         #item int  farming
     *                         #item int  c_type
     *                         }
     * @param int|string $page pagination rpc parameter
     * @param int|string $rows pagination rpc parameter
     * @param string $sort pagination rpc parameter
     * @param string $order pagination rpc parameter
     *
     * @return array relevant results
     */
    public function read(?array $filterObj, ?int $page = null, ?int $rows = null, ?string $sort = '', ?string $order = '')
    {
        if ($this->User->isGuest || !$this->User->Database) {
            return [];
        }

        // init controllers
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbSubleasesController = new UserDbSubleasesController($this->User->Database);
        $FarmingController = new FarmingController('Farming');

        date_default_timezone_set('Europe/Sofia');
        $currentDate = date('Y-m-d', time());
        $arrayHelper = $FarmingController->ArrayHelper;
        $userFarmings = $FarmingController->getUserFarmings();
        $userFarmingIds = array_keys($userFarmings);
        $farmingIds = $arrayHelper->filterEmptyStringArr($filterObj['farming']);
        $farmingIds = isset($farmingIds) && count($farmingIds) > 0
            ? array_intersect($farmingIds, $userFarmingIds)
            : [...$userFarmingIds, null];

        // get renta natura types and create predefined array
        $renta_types = [];
        // options for renta nat types
        $options = [
            'tablename' => $UserDbController->DbHandler->tableRentaTypes,
        ];
        $renta_results = $UserDbController->getItemsByParams($options, false, false);
        $rentaCount = count($renta_results);
        for ($i = 0; $i < $rentaCount; $i++) {
            $renta_types[$renta_results[$i]['id']]['name'] = $renta_results[$i]['name'];
            $renta_types[$renta_results[$i]['id']]['unit'] = $GLOBALS['Contracts']['renta_units'][$renta_results[$i]['unit']]['name'];
        }

        $filterObj['subleaser_name'] = $filterObj['subleaser_name'] ? preg_replace('/\s+/', ' ', $filterObj['subleaser_name']) : '';
        $filterObj['rep_name'] = $filterObj['rep_name'] ? preg_replace('/\s+/', ' ', $filterObj['rep_name']) : '';
        $filterObj['company_name'] = $filterObj['company_name'] ? preg_replace('/\s+/', ' ', $filterObj['company_name']) : '';

        $subleasesCompareType = '=';
        $subleases = $filterObj['sublease_id'];
        if (false !== strpos($filterObj['sublease_id'], ',')) {
            $subleasesCompareType = 'IN';
            $subleases = explode(',', $filterObj['sublease_id']);
        }

        // prepare options for contract query
        $options = [
            'tablename' => $UserDbController->DbHandler->tableContracts,
            'return' => [
                'DISTINCT on(c.id) c.id as contract_id,
                        c1.id as main_contract_id,
                        c.comment, c.sv_num, c.sv_date, c.due_date, c.c_num, c.start_date,
                        c.c_date, c.renta, c.renta_nat, c.renta_nat_type_id, c.is_declaration_subleased,
                        c.nm_usage_rights, c.active,
                        c.farming_id, c.payday, o.name, o.surname, o.lastname, o.egn,
                        o.company_name, o.eik,
                        o_r.rep_name, o_r.rep_surname, o_r.rep_lastname, o_r.rep_egn,
                        kvs.kad_ident, kvs.area_type, kvs.ekate, kvs.masiv, kvs.number, kvs.category',
            ],
            'where' => [
                'sublease_id' => ['column' => 'id', 'compare' => $subleasesCompareType, 'prefix' => 'c', 'value' => $subleases],
                'c_num' => ['column' => 'c_num', 'compare' => 'ILIKE', 'prefix' => 'c', 'value' => $filterObj['c_num']],
                'date_from' => ['column' => 'start_date', 'compare' => '>=', 'prefix' => 'c', 'value' => $filterObj['date_from']],
                'date_to' => ['column' => 'start_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $filterObj['date_to']],
                'due_date_from' => ['column' => 'due_date', 'compare' => '>=', 'prefix' => 'c', 'value' => $filterObj['due_date_from']],
                'due_date_to' => ['column' => 'due_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $filterObj['due_date_to']],
                'farming' => ['column' => 'farming_id', 'compare' => 'IN', 'prefix' => 'c', 'value' => $farmingIds],
                'c_type' => ['column' => 'nm_usage_rights', 'compare' => 'IN', 'prefix' => 'c', 'value' => $arrayHelper->filterEmptyStringArr($filterObj['c_type'])],
                'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'prefix' => 'c', 'value' => 'TRUE'],
                'renta_types' => ['column' => 'renta_id', 'compare' => 'IN', 'prefix' => 'cr', 'value' => $arrayHelper->filterEmptyStringArr($filterObj['renta_types'])],
                'kad_ident' => ['column' => 'kad_ident', 'compare' => '=', 'prefix' => 'kvs', 'value' => $filterObj['kad_ident']],
                'ekate' => ['column' => 'ekate', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $arrayHelper->filterEmptyStringArr($filterObj['ekate'])],
                'masiv' => ['column' => 'masiv', 'compare' => '=', 'prefix' => 'kvs', 'value' => $filterObj['masiv']],
                'number' => ['column' => 'number', 'compare' => '=', 'prefix' => 'kvs', 'value' => $filterObj['number']],
                'category' => ['column' => 'category', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $arrayHelper->filterEmptyStringArr($filterObj['category'])],
                'area_type' => ['column' => 'area_type', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $arrayHelper->filterEmptyStringArr($filterObj['area_type'])],
                'subleaser_egn' => ['column' => 'egn', 'compare' => 'ILIKE', 'prefix' => 'o', 'value' => $filterObj['subleaser_egn']],
                'rep_egn' => ['column' => 'rep_egn', 'compare' => 'ILIKE', 'prefix' => 'r', 'value' => $filterObj['rep_egn']],
                'company_name' => ['column' => 'TRIM(company_name)', 'compare' => 'ILIKE', 'value' => $filterObj['company_name']],
                'company_eik' => ['column' => 'eik', 'compare' => '=', 'prefix' => 'o', 'value' => $filterObj['company_eik']],
            ],
        ];

        if ($filterObj['farming_year']) {
            $options['where']['farming_year_start_date'] = [
                'column' => 'start_date',
                'compare' => '<=',
                'prefix' => 'c',
                'value' => $GLOBALS['Farming']['years'][$filterObj['farming_year']]['year'] . '-09-30',
            ];
            $options['where']['farming_year_due_date'] = [
                'column' => 'due_date',
                'compare' => '>=',
                'prefix' => 'c',
                'value' => ($GLOBALS['Farming']['years'][$filterObj['farming_year']]['year'] - 1) . '-10-01',
            ];
        }

        if (1 == $filterObj['c_status']) {
            $options['where']['active'] = ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'];
        }

        if (2 == $filterObj['c_status']) {
            $options['where']['active'] = ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'TRUE'];
            $options['where']['due_date'] = ['column' => 'due_date', 'compare' => '>=', 'prefix' => 'c', 'value' => $currentDate];
        }
        if (3 == $filterObj['c_status']) {
            $options['where']['due_date'] = ['column' => 'due_date', 'compare' => '<', 'prefix' => 'c', 'value' => $currentDate];
        }

        if ('all' != $filterObj['irrigated_area']) {
            $options['where']['irrigated_area'] = ['column' => 'irrigated_area', 'compare' => '=', 'prefix' => 'kvs', 'value' => $filterObj['irrigated_area']];
        }

        if ($filterObj['person_name']) {
            $tmp_person_names = preg_replace('/\s+/', '.*', $filterObj['person_name']);
            $tmp_person_names = mb_strtolower($tmp_person_names, 'UTF-8');
            $options['whereOr']['company_name'] = ['column' => 'TRIM(company_name)', 'compare' => 'ILIKE', 'value' => $filterObj['person_name']];
            $options['whereOr']['rep_names'] = ['column' => "lower(TRIM (o_r.rep_name)) || ' ' || lower(TRIM (o_r.rep_surname)) || ' ' || lower(TRIM (o_r.rep_lastname))", 'compare' => '~', 'value' => $tmp_person_names];
        }

        if ($filterObj['person_egn']) {
            $options['whereOr']['eik'] = ['column' => 'eik', 'compare' => 'ILIKE', 'prefix' => 'o', 'value' => $filterObj['person_egn']];
            $options['whereOr']['rep_egn'] = ['column' => 'rep_egn', 'compare' => 'ILIKE', 'prefix' => 'o_r', 'value' => $filterObj['person_egn']];
        }

        if ($filterObj['rep_name']) {
            $tmp_rep_names = preg_replace('/\s+/', '.*', $filterObj['rep_name']);
            $tmp_rep_names = mb_strtolower($tmp_rep_names, 'UTF-8');
            if (!$options['return']) {
                $options['return'] = [];
            }
            array_push($options['return'], "regexp_matches(lower(TRIM (o_r.rep_name)) || ' ' || lower(TRIM (o_r.rep_surname)) || ' ' || lower(TRIM (o_r.rep_lastname)), '{$tmp_rep_names}','g')");
        }

        if ($filterObj['subleaser_name']) {
            $tmp_subleaser_names = preg_replace('/\s+/', '.*', $filterObj['subleaser_name']);
            $tmp_subleaser_names = mb_strtolower($tmp_subleaser_names, 'UTF-8');
            if (!$options['return']) {
                $options['return'] = [];
            }
            array_push($options['return'], "regexp_matches(lower(TRIM (name)) || ' ' || lower(TRIM (surname)) || ' ' || lower(TRIM (lastname)), '{$tmp_subleaser_names}','g')");
        }

        if (true == $filterObj['c_num_complete_match']) {
            $options['where']['c_num']['compare'] = '=';
        }

        // get all contracts for pagination total
        $allResults = $UserDbSubleasesController->getFullContractDataByFilter($options, false, false);
        $counter = count($allResults);
        if (0 === $counter) {
            return [];
        }

        $pagination = [];

        // adding limit for pagination
        // define page limit
        $options['sort'] = $sort;
        $options['order'] = $order;
        $options['offset'] = ($page - 1) * $rows < 0 ? 0 : ($page - 1) * $rows;
        $options['limit'] = $rows;

        $results = $UserDbSubleasesController->getFullContractDataByFilter($options, false, false);

        $resultsCount = count($results);
        // clear old data
        $return = [];

        // transform results into tree format
        for ($i = 0; $i < $resultsCount; $i++) {
            $contractData = $results[$i];
            $contractData['c_date'] = strftime('%d.%m.%Y', strtotime($contractData['c_date']));
            $contractData['start_date'] = strftime('%d.%m.%Y', strtotime($contractData['start_date']));
            if ($contractData['active']) {
                $contractData['active_text'] = (!$contractData['due_date'] || $contractData['due_date'] > $currentDate) ? 'Действащ' : 'Изтекъл';
            } else {
                $contractData['active_text'] = 'Анулиран';
            }

            if (!$contractData['comment']) {
                $contractData['comment'] = '-';
            }

            if (!$contractData['sv_num']) {
                $contractData['sv_num'] = '-';
            }

            if ('' != $contractData['due_date']) {
                $contractData['due_date'] = strftime('%d.%m.%Y', strtotime($contractData['due_date']));
                $text = $contractData['c_num'] . ' (' . $contractData['start_date'] . ' - ' . $contractData['due_date'] . ')';
            } else {
                $contractData['due_date'] = '-';
                $text = $contractData['c_num'] . ' (' . $contractData['c_date'] . ')';
            }

            if (!$contractData['renta']) {
                $contractData['renta_text'] = '-';
            } else {
                $contractData['renta'] = number_format($contractData['renta'], 2, '.', '');
                $contractData['renta_text'] = BGNtoEURO($contractData['renta']);
            }

            $contractData['c_type'] = $contractData['nm_usage_rights'];
            $contractData['nm_usage_rights'] = $GLOBALS['Contracts']['ContractTypes'][$contractData['nm_usage_rights']]['name'];
            $contractData['farming'] = $userFarmings[$contractData['farming_id']];

            if ('' == $contractData['sv_date']) {
                $contractData['sv_date'] = '-';
            } else {
                $contractData['sv_date'] = strftime('%d.%m.%Y', strtotime($contractData['sv_date']));
            }

            if ('' == $contractData['payday']) {
                $contractData['payday'] = '-';
                $contractData['paymonth'] = '';
            } else {
                $payday = explode('-', $contractData['payday']);
                $contractData['payday'] = $payday[0];
                $contractData['paymonth'] = $payday[1];
            }

            $rentaOptions = [
                'tablename' => $UserDbController->DbHandler->contractsRentsRelTable,
                'where' => [
                    'id' => ['column' => 'contract_id', 'compare' => '=', 'value' => $contractData['contract_id']],
                ],
            ];

            $tmpRenta = $UserDbController->getItemsByParams($rentaOptions);
            $tmpRentCount = count($tmpRenta);
            for ($j = 0; $j < $tmpRentCount; $j++) {
                $tmpRenta[$j]['renta_nat_type'] = $renta_types[$tmpRenta[$j]['renta_id']]['name'];
                $tmpRenta[$j]['renta_nat_text'] = $tmpRenta[$j]['renta_value'] . ' ' . $renta_types[$tmpRenta[$j]['renta_id']]['unit'];
            }
            $contractData['additionalRentas'] = $tmpRenta;

            $contractData['is_declaration_subleased_text'] = $contractData['is_declaration_subleased'] ? 'Да' : 'Не';

            $return[] = [
                'id' => $contractData['contract_id'],
                'text' => $text,
                'attributes' => $contractData,
                'iconCls' => $this->getContractIcon($contractData),
            ];
        }

        // add attribute to first listed element of three for custom pagination
        // information for total can not be sent otherwise
        $return[0]['attributes']['pagination']['total'] = $counter;
        $return[0]['attributes']['pagination']['limit'] = $rows;

        return $return;
    }

    /**
     * Changes the selected sublease status.
     *
     * @api-method changeActiveStatus
     *
     * @param array $rpcParam status change parameters
     *                        {
     *                        #item int id
     *                        #item bool status
     *                        #item string comment
     *                        }
     */
    public function changeActiveStatus($rpcParam)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbContractsController = new UserDbContractsController($this->User->Database);
        $UsersController = new UsersController('Users');

        $contract_id = $rpcParam['id'];

        // Вземане на старите данни
        $options = [
            'tablename' => $UserDbController->DbHandler->tableContracts,
            'where' => [
                'sublease_id' => ['column' => 'id', 'compare' => '=', 'value' => $contract_id],
            ],
        ];

        $oldInfo = $UserDbController->getItemsByParams($options);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableContracts,
            'mainData' => [
                'active' => $rpcParam['status'],
                'comment' => $rpcParam['comment'],
            ],
            'where' => [
                'id' => $contract_id,
            ],
        ];

        $UserDbController->editItem($options);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['request_params' => $rpcParam], ['old_data' => $oldInfo[0]], 'Changing contract status');

        $farming_contragent_options = [
            'tablename' => $UserDbController->DbHandler->tableContractsFarmingContragents,
            'where' => [
                'contract_id' => ['column' => 'contract_id', 'compare' => '=', 'value' => $contract_id],
            ],
        ];
        $farming_results = $UserDbController->getItemsByParams($farming_contragent_options);

        if ($farming_results[0]['farming_id']) {
            $contract_data_options = [
                'return' => ['c.*'],
                'where' => [
                    'from_sublease' => ['column' => 'from_sublease', 'compare' => '=', 'prefix' => 'c', 'value' => $contract_id],
                    'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'prefix' => 'c', 'value' => false],
                    'farming_id' => ['column' => 'farming_id', 'compare' => '=', 'prefix' => 'c', 'value' => $farming_results[0]['farming_id']],
                ],
            ];
            $auto_contract_results = $UserDbContractsController->getFullContractDataByFilter($contract_data_options, false, false);

            $options = [
                'tablename' => $UserDbController->DbHandler->tableContracts,
                'mainData' => [
                    'active' => $rpcParam['status'],
                    'comment' => $rpcParam['comment'],
                ],
                'where' => [
                    'id' => $auto_contract_results[0]['id'],
                ],
            ];

            $UserDbController->editItem($options);

            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['request_params' => $rpcParam], ['old_data' => $oldInfo[0]], 'Changing auto generated   contract status');
        }
    }

    /**
     * Deletes the selected sublease.
     *
     * @api-method deleteSublease
     *
     * @param int $rpcParam selected annex ID
     *
     * @return array
     */
    public function deleteSublease($rpcParam)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        $UserDbContractsController = new UserDbContractsController($this->User->Database);

        $sublease_id = $rpcParam;
        $contract_tablename = $UserDbController->DbHandler->tableContracts;

        // Вземане на старите данни
        $options = [
            'tablename' => $UserDbController->DbHandler->tableContracts,
            'where' => [
                'sublease_id' => ['column' => 'id', 'compare' => '=', 'value' => $sublease_id],
            ],
        ];

        $oldInfo = $UserDbController->getItemsByParams($options);

        if (!$sublease_id || !(int) $sublease_id) {
            return [];
        }

        $UserDbController->disableRentaMatViewTriggers();

        $transaction = $UserDbController->DbHandler->DbModule->beginTransaction();

        try {
            // delete natural rentas
            $deleteOptions = [
                'tablename' => $UserDbController->DbHandler->contractsRentsRelTable,
                'id_name' => 'contract_id',
                'id_string' => $sublease_id,
            ];
            $UserDbController->deleteItemsByParams($deleteOptions);

            // delete annex
            $options = [
                'tablename' => $contract_tablename,
                'id_string' => $sublease_id,
            ];

            $farming_contragent_options = [
                'tablename' => $UserDbController->DbHandler->tableContractsFarmingContragents,
                'where' => [
                    'contract_id' => ['column' => 'contract_id', 'compare' => '=', 'value' => $sublease_id],
                ],
            ];
            $farming_results = $UserDbController->getItemsByParams($farming_contragent_options);

            $UserDbController->deleteItemsByParams($options);

            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['deleted_sublease_id' => $rpcParam], ['old_data' => $oldInfo[0]], 'Deleting sublease');

            // Изтриване на автоматично създаден договор, когато е добавено стопанство като контрагент);
            if ($farming_results[0]['farming_id']) {
                $contract_data_options = [
                    'return' => ['c.*'],
                    'where' => [
                        'from_sublease' => ['column' => 'from_sublease', 'compare' => '=', 'prefix' => 'c', 'value' => $sublease_id],
                        'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'prefix' => 'c', 'value' => false],
                        'farming_id' => ['column' => 'farming_id', 'compare' => '=', 'prefix' => 'c', 'value' => $farming_results[0]['farming_id']],
                    ],
                ];
                $auto_contract_results = $UserDbContractsController->getFullContractDataByFilter($contract_data_options, false, false);

                $rentaOptions = [
                    'tablename' => $UserDbController->DbHandler->contractsRentsRelTable,
                    'id_string' => $auto_contract_results[0]['id'],
                    'id_name' => 'contract_id',
                ];
                $UserDbController->deleteItemsByParams($rentaOptions);

                $contract_delete_options = [
                    'tablename' => $UserDbController->DbHandler->tableContracts,
                    'id_string' => $auto_contract_results[0]['id'],
                ];
                $UserDbController->deleteItemsByParams($contract_delete_options);
                $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['deleted_auto_contract_id' => $auto_contract_results[0]['id']], ['old_data' => $auto_contract_results[0]], 'Deleting Auto created contract');
            }
            $transaction->commit();
        } catch (TDbException $e) {
            $transaction->rollback();
            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['sublease_id' => $sublease_id], [], 'Error when deleting sublease');

            throw $e;
        }

        $UserDbController->enableRentaMatViewTriggers();
        $UserDbController->refreshRentaViews();
    }

    /**
     * checks whether a contract with the same name already exists.
     *
     * @api-method checkForExistence
     *
     * @param array $params{
     *                       #item string c_num
     *                       #item integer farming
     *                       #item integer sublease_id
     *                       }
     *
     * @return bool
     */
    public function checkForExistence($params)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $data = (object)$params;
        $options = [
            'tablename' => $UserDbController->DbHandler->tableContracts,
            'where' => [
                'c_num' => ['column' => 'c_num', 'compare' => '=', 'value' => $data->c_num],
                'farming_id' => ['column' => 'farming_id', 'compare' => '=', 'value' => $data->farming],
                'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'value' => 'true'],
            ],
        ];

        $count = $UserDbController->getItemsByParams($options, true, false);

        if ($count[0]['count'] && 0 == strlen($data->sublease_id)) {
            return true;
        }

        $this->saveSublease($params);

        return false;
    }

    /**
     * saves the changes done to a sublease.
     *
     * @api-method saveSublease
     *
     * @param array $params{
     *                       #item integer sublease_id
     *                       #item string c_num
     *                       #item string c_date
     *                       #item integer nm_usage_rights
     *                       #item string start_date
     *                       #item string due_date
     *                       #item integer farming_id
     *                       #item double renta
     *                       #item string comment
     *                       }
     * @param int $forceSkipCopy
     */
    public function saveSublease($params, $forceSkipCopy = false)
    {
        $data = (object) $params;

        if (0 != strlen($data->sublease_id) && 0 != $data->sublease_id) {
            return $this->editSublease($data);
        }

        return $this->createSublease($data, $forceSkipCopy);
    }

    /**
     * returns the rents that the selected sublease has.
     *
     * @api-method preEdit
     *
     * @param int $sublease_id
     *
     * @return array {
     *               #item integer contract_id
     *               #item integer id
     *               #item integer renta_id
     *               #item string renta_value
     *               }
     */
    public function preEdit($sublease_id)
    {
        $UserDbController = new UserDbController($this->User->Database);

        $rentaOptions = [
            'tablename' => $UserDbController->DbHandler->contractsRentsRelTable,
            'where' => [
                'id' => ['column' => 'contract_id', 'compare' => '=', 'value' => $sublease_id],
            ],
        ];

        return $UserDbController->getItemsByParams($rentaOptions);
    }

    /**
     * Gets selected sublease information.
     *
     * @api-method markSubleaseForEdit
     *
     * @param int $sublease_id
     *
     * @return array {
     *               #item string c_num
     *               #item string c_date
     *               #item integer nm_usage_rights
     *               #item string sv_num
     *               #item string sv_date
     *               #item integer pd_day
     *               #item integer pd_month
     *               #item string start_date
     *               #item double renta
     *               #item integer farming_id
     *               #item string due_date
     *               #item integer renta_nat_type_id
     *               #item double renta_nat
     *               #item string comment
     *               }
     */
    public function markSubleaseForEdit($sublease_id)
    {
        $UserDbContractsController = new UserDbContractsController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);
        $options = [
            'tablename' => $UserDbController->DbHandler->tableContracts,
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $sublease_id],
            ],
        ];

        $results = $UserDbController->getItemsByParams($options);
        $contractData = $results[0];

        $hasFarmingPermission = $this->User->hasPermissionTo(ObjectPermissions::PERMISSION_WRITE, UserFarmings::class, $contractData['farming_id']);
        if (false === $hasFarmingPermission) {
            throw new MTRpcException('NO_CONTRACT_PERMISSION', -33657);
        }

        $sublease_data = [];

        // set fields with current value
        $sublease_data['c_num'] = $contractData['c_num'];
        $sublease_data['c_date'] = strftime('%Y-%m-%d', strtotime($contractData['c_date']));
        $sublease_data['nm_usage_rights'] = $contractData['nm_usage_rights'];
        $sublease_data['sv_num'] = $contractData['sv_num'];
        $sublease_data['sv_date'] = ($contractData['sv_date']) ? strftime('%Y-%m-%d', strtotime($contractData['sv_date'])) : '';
        if ($contractData['payday']) {
            $payday = explode('-', $contractData['payday']);
            $sublease_data['pd_day'] = $payday[0];
            $sublease_data['pd_month'] = $payday[1];
        } else {
            $sublease_data['pd_day'] = '';
            $sublease_data['pd_month'] = '';
        }
        $sublease_data['start_date'] = strftime('%Y-%m-%d', strtotime($contractData['start_date']));
        $sublease_data['renta'] = $contractData['renta'];
        $sublease_data['farming_id'] = $contractData['farming_id'];
        $sublease_data['due_date'] = strftime('%Y-%m-%d', strtotime($contractData['due_date']));
        $sublease_data['comment'] = $contractData['comment'];
        $sublease_data['isEdit'] = true;
        $sublease_data['is_declaration_subleased'] = $contractData['is_declaration_subleased'];

        $rentaOptions = [
            'where' => [
                'id' => ['column' => 'contract_id', 'compare' => '=', 'value' => $sublease_id],
            ],
        ];
        $rentaResult = $UserDbContractsController->getContractRentsData($rentaOptions);
        $rentaCount = count($rentaResult);
        if ($rentaCount > 0) {
            for ($i = 0; $i < $rentaCount; $i++) {
                $sublease_data['additionalRentas'][$i] = $rentaResult[$i];
            }
        }

        return $sublease_data;
    }

    /**
     * returns information about the sublease you want to copy.
     *
     * @api-method initSubleaseCopy
     *
     * @param int $sublease_id
     *
     * @return array {
     *               #item string c_num
     *               #item string c_date
     *               #item integer nm_usage_rights
     *               #item string sv_num
     *               #item string sv_date
     *               #item integer pd_day
     *               #item integer pd_month
     *               #item string start_date
     *               #item double renta
     *               #item integer farming_id
     *               #item string due_date
     *               #item integer renta_nat_type_id
     *               #item double renta_nat
     *               #item string comment
     *               }
     */
    public function initSubleaseCopy($sublease_id)
    {
        $UserDbContractsController = new UserDbContractsController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);
        $options = [
            'tablename' => $UserDbController->DbHandler->tableContracts,
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $sublease_id],
            ],
        ];

        $results = $UserDbController->getItemsByParams($options);
        $contractData = $results[0];

        $sublease_data = [];
        // set fields with current value

        if (null != $results[0]['due_date']) {
            $due_date_year = date('Y', strtotime($results[0]['due_date']));
            $due_date_month = date('m', strtotime($results[0]['due_date']));

            if ((int)$due_date_month < 10) {
                $new_due_date_year = $due_date_year;
            } else {
                $new_due_date_year = $due_date_year + 1;
            }
            $new_start_date = date('Y-m-d', strtotime($new_due_date_year . '-10-01'));
        } else {
            $new_start_date = date('Y-m-d', strtotime($results[0]['start_date']));
        }

        $sublease_data['c_num'] = '';
        $sublease_data['c_date'] = date('Y-m-d', strtotime($contractData['due_date'] . ' + 1 day'));
        $sublease_data['start_date'] = $new_start_date;
        $sublease_data['due_date'] = date('Y-m-d', strtotime(($new_due_date_year + 1) . '-09-30'));

        $sublease_data['nm_usage_rights'] = $contractData['nm_usage_rights'];
        $sublease_data['sv_num'] = $contractData['sv_num'];
        $sublease_data['sv_date'] = ($contractData['sv_date']) ? strftime('%Y-%m-%d', strtotime($contractData['sv_date'])) : '';
        if ($contractData['payday']) {
            $payday = explode('-', $contractData['payday']);
            $sublease_data['pd_day'] = $payday[0];
            $sublease_data['pd_month'] = $payday[1];
        } else {
            $sublease_data['pd_day'] = '';
            $sublease_data['pd_month'] = '';
        }
        $sublease_data['renta'] = $contractData['renta'];
        $sublease_data['farming_id'] = $contractData['farming_id'];
        $sublease_data['comment'] = $contractData['comment'];
        $sublease_data['is_copy'] = true;
        $this->copy_sublease_id = $sublease_id;

        $rentaOptions = [
            'where' => [
                'id' => ['column' => 'contract_id', 'compare' => '=', 'value' => $sublease_id],
            ],
        ];

        $rentaResult = $UserDbContractsController->getContractRentsData($rentaOptions);
        $rentaCount = count($rentaResult);

        $sublease_data['additionalRentas'] = [];
        if ($rentaCount > 0) {
            for ($i = 0; $i < $rentaCount; $i++) {
                $sublease_data['additionalRentas'][$i] = $rentaResult[$i];
            }
        }

        return $sublease_data;
    }

    /**
     * Returns if the sublease has historical plots.
     *
     * @api-method hasSubleaseEditedPlots
     *
     * @param int $sublease_id
     *
     * @return bool
     */
    public function hasSubleaseEditedPlots($sublease_id)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbSubleasesController = new UserDbSubleasesController($this->User->Database);
        $return = false;

        $options = [
            'return' => [
                'bool_or(kvs.is_edited)',
            ],
            'where' => [
                'sublease_id' => ['column' => 'id', 'compare' => '=', 'prefix' => 'c', 'value' => $sublease_id],
                'annex_action' => ['column' => 'annex_action', 'compare' => '=', 'prefix' => 'pc', 'value' => 'added'],
            ],
        ];

        $result = $UserDbSubleasesController->hasSubleaseEditedPlots($options, false, false);

        if ('boolean' == gettype($result[0]['bool_or'])) {
            $return = $result[0]['bool_or'];
        }

        return $return;
    }

    /**
     * Load subleased contract by provided sublease Id.
     *
     * @param ?int $page
     * @param ?int $rows
     * @param ?string $sort
     * @param ?string $order
     *
     * @return array
     */
    public function loadSublease(array $params, ?int $page = null, ?int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);
        $contractPlots = $UserDbPlotsController->getPlotsInSublease($params, $page, $rows, $sort, $order, false, false);
        $gids = array_column($contractPlots, 'gid');

        if (0 == count($gids)) {
            throw new MTRpcException('NO_SUBLEASE_PLOTS_FOUND', -33757);
        }

        $subleaseId = $params['sublease_id'];
        $contractId = $params['main_contrat_id'];
        $startDate = $params['start_date'];
        $dueDate = $params['due_date'];
        $farmingId = $params['farming_id'];

        $startDate = DateTime::createFromFormat('Y-m-d', $startDate);
        $dueDateReal = DateTime::createFromFormat('Y-m-d', $dueDate);
        $dueDate = DateTime::createFromFormat('Y-m-d', $dueDate);

        $options['start_date_on'] = $subleaseStartDate = $startDate->format('Y-m-d 00:00:00');
        $options['due_date_on'] = $subleaseDueDate = $dueDate->format('Y-m-d 23:59:59');
        $options['contract_due_date_on'] = $contractSubleaseDueDate = $dueDate->format('Y-m-d 00:00:00');
        $options['disable_annex_dates'] = true;

        $options['return'] = [
            'gid', 'kad_ident', 'COALESCE(virtual_category_title, \'-\') as category', 'area_type', 'ekate', 'kvs.is_edited',
            'ekattes.ekatte_name',
            'used_area', 'St_Area(geom) as area',
            'pc.plot_id',
            "(CASE WHEN a.id IS NULL THEN c.id ELSE
            (case when ( a.start_date <= '" . $subleaseStartDate . "' and a.due_date >= '" . $subleaseDueDate . "') then a.id else c.id END) END) as c_id",
            "(CASE WHEN a.c_num IS NULL THEN c.c_num ELSE
            (case when ( a.start_date <= '" . $subleaseStartDate . "' and a.due_date >= '" . $subleaseDueDate . "') then a.c_num else c.c_num END) END)",
            "(CASE WHEN a.c_date IS NULL THEN c.c_date ELSE
            (case when ( a.start_date <= '" . $subleaseStartDate . "' and a.due_date >= '" . $subleaseDueDate . "') then a.c_date else c.c_date END) END)",
            'pc.id as pc_rel_id',
            'round((CASE WHEN document_area IS NULL THEN St_Area(geom)/1000 ELSE document_area END)::numeric, 3) as document_area',
            "round( (case 
                        when a.id is null then pc.contract_area
                        else
                            case 
                                when apc.annex_action = 'removed' then 0
                                else apc.contract_area
                            end	
                    end)::numeric, 3)	as contract_area",
            'round((coalesce(sspa.contract_area,0))::numeric,3) as subleased_area',
            'round((coalesce(sales_c.contract_area_for_sale, 0))::numeric,3) AS contract_area_for_sale',
            'count(sc.id) as subleases_cnt',
        ];

        $options['where']['farming'] = ['column' => 'farming_id', 'compare' => '=', 'prefix' => 'c', 'value' => $farmingId];
        $options['where']['active'] = ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => true];
        $options['where']['annex_action'] = ['column' => 'annex_action', 'compare' => '=', 'prefix' => 'pc', 'value' => 'added'];
        $options['where']['is_sublease'] = ['column' => 'is_sublease', 'compare' => '=', 'prefix' => 'c', 'value' => false];
        $options['where']['is_annex'] = ['column' => 'is_annex', 'compare' => '=', 'prefix' => 'c', 'value' => false];
        $options['where']['plot_id'] = ['column' => 'plot_id', 'compare' => 'IN', 'prefix' => 'pc', 'value' => $gids];

        $options['caseWhere'] = " and case
                                     when 
                                        a.id is null 
                                     then 
                                        c.start_date <= '" . $subleaseStartDate . "' and (c.due_date >= '" . $contractSubleaseDueDate . "' OR c.due_date is null)
                                     else
                                        (( a.start_date <= '" . $subleaseStartDate . "' and a.due_date >= '" . $contractSubleaseDueDate . "') OR (c.start_date <= '" . $subleaseStartDate . "' and c.due_date >= '" . $subleaseDueDate . "') OR (c.start_date <= '" . $subleaseStartDate . "' and a.due_date >= '" . $contractSubleaseDueDate . "')) end";

        $options['group'] = 'gid,ekattes.ekatte_name, pc.plot_id, sspa.contract_area, a.id, c.id, pc.contract_area, apc.annex_action, apc.contract_area,pc.id, spcr.pc_rel_id, sales_c.contract_area_for_sale';

        $options['having'] = '
            ROUND(
                (
                    sum(contract_area) - sum(subleased_area) - sum(contract_area_for_sale)
                )::NUMERIC,
                3
            ) > 0
        ';

        $result = $UserDbPlotsController->getAvailablePlotsToSublease($options, false, false);

        $cnt = 0;
        foreach ($result as &$record) {
            $record['index'] = $cnt;
            $record['is_checkable'] = true;
            $record['iconCls'] = 'icon-tree-edit-geometry';
            $cnt++;
        }

        return [
            'rows' => $result,
        ];
    }

    /**
     * @param array $contractData
     */
    public function copySublease($contractData)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $copyFromContractId = $contractData['copy_contract_id'];

        $data = $this->initSubleaseCopy($copyFromContractId);
        $data['c_num'] = $contractData['c_num'];
        $data['start_date'] = $contractData['start_date'];
        $data['due_date'] = $contractData['due_date'];
        $data['c_date'] = $contractData['c_date'];
        $data['is_declaration_subleased'] = $contractData['is_declaration_subleased'];

        $transaction = $UserDbController->DbHandler->DbModule->beginTransaction();

        try {
            $newSubeaseId = $this->saveSublease($data, true);

            $this->copySubleasePlotsContractsRel($contractData['plots'], $newSubeaseId);
            $this->addSubleasePlotsArea($newSubeaseId, $contractData['plots']);
            $this->copySubleaseContragentsData($copyFromContractId, $newSubeaseId);
            $this->createContract($copyFromContractId, $newSubeaseId);

            $transaction->commit();

            return $newSubeaseId;
        } catch (TDbException $e) {
            $transaction->rollback();
            $UsersController = new UsersController('Users');
            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $contractData, [], 'Copy sublease contract rollback');

            throw new MTRpcException('COPY_SUBLEASE_ERROR', -33757);
        }
    }

    private function getContractIcon($contractInfo)
    {
        $contractType = (int) $contractInfo['c_type'];
        switch (true) {
            case (Config::CONTRACT_TYPE_OWN === $contractType):
                $ret = Config::$contractTypeIconsMap[Config::ICON_CONTRACT_TYPE_OWN];

                break;
            case (Config::CONTRACT_TYPE_LEASE === $contractType):
                $ret = Config::$contractTypeIconsMap[Config::ICON_CONTRACT_TYPE_LEASE];

                break;
            case (Config::CONTRACT_TYPE_RENT === $contractType):
                $ret = Config::$contractTypeIconsMap[Config::ICON_CONTRACT_TYPE_RENT];

                break;
            default:
                $ret = Config::$contractTypeIconsMap[Config::ICON_CONTRACT_DEFAULT];
        }

        return $ret;
    }

    private function createSublease($data, $forceSkipCopy)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $params = [
            'tablename' => $UserDbController->DbHandler->tableContracts,
        ];

        $params['mainData'] = [
            'c_num' => $data->c_num,
            'c_date' => $data->c_date,
            'nm_usage_rights' => $data->nm_usage_rights,
            'sv_num' => $data->sv_num,
            'start_date' => $data->start_date,
            'due_date' => $data->due_date,
            'farming_id' => $data->farming_id,
            'renta' => $data->renta,
            'comment' => $data->comment,
            'is_sublease' => 1,
            'is_declaration_subleased' => $data->is_declaration_subleased,
        ];

        if ('' != $data->sv_date) {
            $params['mainData']['sv_date'] = $data->sv_date;
        }

        if ('' != $data->pd_day && '' != $data->pd_month) {
            $params['mainData']['payday'] = $data->pd_day . '-' . $data->pd_month;
        }

        $recordID = $UserDbController->addItem($params);

        if (count($data->additionalRentas) > 0) {
            $rentaParams = [
                'tablename' => $UserDbController->DbHandler->contractsRentsRelTable,
                'mainData' => [
                    'contract_id' => $recordID,
                ],
            ];

            foreach ($data->additionalRentas as $renta) {
                $currentAdditionalRenta = (object) $renta;
                if ((null == $currentAdditionalRenta->type || 0 == $currentAdditionalRenta->type) && (0 == $currentAdditionalRenta->value || '' == $currentAdditionalRenta->value)) {
                    continue;
                }

                if (0 != $currentAdditionalRenta->value) {
                    $rentaParams['mainData']['renta_id'] = $currentAdditionalRenta->type;
                    $rentaParams['mainData']['renta_value'] = $currentAdditionalRenta->value;
                    $UserDbController->addItem($rentaParams);
                }
            }
        }
        if (0 == strlen($data->copy_sublease_id) || 0 == $data->copy_sublease_id) {
            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['new_data' => $params], ['created_id' => $recordID], 'Adding sublease');
        } else {
            if (false === $forceSkipCopy) {
                $this->copySubleaseRelData($data->copy_sublease_id, $recordID, $params);
            }
        }

        return $recordID;
    }

    private function editSublease($data)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        // Вземане на старите данни
        $options = [
            'tablename' => $UserDbController->DbHandler->tableContracts,
            'where' => [
                'sublease_id' => ['column' => 'id', 'compare' => '=', 'value' => $data->sublease_id],
            ],
        ];

        $oldInfo = $UserDbController->getItemsByParams($options);

        if (count($oldInfo) > 0) {
            $yearStart = new DateTime($oldInfo[0]['start_date']);
            $yearEnd = new DateTime($oldInfo[0]['due_date']);

            if ($yearStart->format('Y-m-d') != $data->start_date || $yearEnd->format('Y-m-d') != $data->due_date) {
                throw new MTRpcException('CANNOT_MODIFY_FROM_SUBLEASE_CONTRACT_DATES', -33755);
            }
        }

        $params = [
            'tablename' => $UserDbController->DbHandler->tableContracts,
            'mainData' => [
                'c_num' => $data->c_num,
                'c_date' => $data->c_date,
                'nm_usage_rights' => $data->nm_usage_rights,
                'sv_num' => $data->sv_num,
                'start_date' => $data->start_date,
                'due_date' => $data->due_date,
                'farming_id' => $data->farming_id,
                'renta' => $data->renta,
                'comment' => $data->comment,
                'is_declaration_subleased' => $data->is_declaration_subleased,
            ],
            'where' => [
                'id' => $data->sublease_id,
            ],
        ];

        if ('' != $data->sv_date) {
            $params['mainData']['sv_date'] = $data->sv_date;
        }

        if ('' != $data->pd_day && '' != $data->pd_month) {
            $params['mainData']['payday'] = $data->pd_day . '-' . $data->pd_month;
        }

        $UserDbController->editItem($params);
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['new_data' => $params], ['old_data' => $oldInfo[0]], 'Editing sublease');

        if (count($data->additionalRentas) > 0) {
            $deleteOptions = [
                'tablename' => $UserDbController->DbHandler->contractsRentsRelTable,
                'id_name' => 'contract_id',
                'id_string' => $data->sublease_id,
            ];
            $UserDbController->deleteItemsByParams($deleteOptions);
            $rentaParams = [
                'tablename' => $UserDbController->DbHandler->contractsRentsRelTable,
                'mainData' => [
                    'contract_id' => $data->sublease_id,
                ],
            ];

            foreach ($data->additionalRentas as $renta) {
                $currentAdditionalRenta = (object) $renta;
                if ((null == $currentAdditionalRenta->type || 0 == $currentAdditionalRenta->type) && (0 == $currentAdditionalRenta->value || '' == $currentAdditionalRenta->value)) {
                    continue;
                }

                if (0 != $currentAdditionalRenta->value) {
                    $rentaParams['mainData']['renta_id'] = $currentAdditionalRenta->type;
                    $rentaParams['mainData']['renta_value'] = $currentAdditionalRenta->value;
                    $UserDbController->addItem($rentaParams);
                }
            }

            $this->editCreatedContract($data);
        }
    }

    private function copySubleaseRelData($fromSubleaseID, $toSubleaseID, $newSubleaseData)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        // get sublease rel data
        $options = [
            'tablename' => $UserDbController->DbHandler->tableSubleasesPlotsContractsRel,
            'where' => [
                'sublease_id' => ['column' => 'sublease_id', 'compare' => '=', 'value' => $fromSubleaseID],
            ],
        ];

        $results = $UserDbController->getItemsByParams($options, false, false);
        $resultsCount = count($results);
        for ($i = 0; $i < $resultsCount; $i++) {
            $options = [
                'tablename' => $UserDbController->DbHandler->tableSubleasesPlotsContractsRel,
                'mainData' => [
                    'sublease_id' => $toSubleaseID,
                    'pc_rel_id' => $results[$i]['pc_rel_id'],
                ],
            ];

            $rel_id = $UserDbController->addItem($options);

            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['new_rel_id' => $rel_id], ['pc_rel_id' => $results[$i]['pc_rel_id']], 'Adding Sublease plot contract rel');
        }

        // get sublease contragents data
        $options = [
            'tablename' => $UserDbController->DbHandler->tableContractsContragents,
            'where' => [
                'contract_id' => ['column' => 'contract_id', 'compare' => '=', 'value' => $fromSubleaseID],
            ],
        ];

        $results = $UserDbController->getItemsByParams($options, false, false);
        $resultsCount = count($results);
        for ($i = 0; $i < $resultsCount; $i++) {
            $options = [
                'tablename' => $UserDbController->DbHandler->tableContractsContragents,
                'mainData' => [
                    'contract_id' => $toSubleaseID,
                    'owner_id' => $results[$i]['owner_id'],
                    'rep_id' => $results[$i]['rep_id'],
                    'proxy_date' => $results[$i]['proxy_date'],
                    'proxy_num' => $results[$i]['proxy_num'],
                ],
            ];

            $rel_id = $UserDbController->addItem($options);

            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['options' => $options], ['relation_id' => $rel_id], 'Adding Sublease contragent rel');
        }

        // get sublease farming contragents data
        $options = [
            'tablename' => $UserDbController->DbHandler->tableContractsFarmingContragents,
            'where' => [
                'contract_id' => ['column' => 'contract_id', 'compare' => '=', 'value' => $fromSubleaseID],
            ],
        ];

        $results = $UserDbController->getItemsByParams($options, false, false);
        $resultsCount = count($results);
        for ($i = 0; $i < $resultsCount; $i++) {
            $options = [
                'tablename' => $UserDbController->DbHandler->tableContractsFarmingContragents,
                'mainData' => [
                    'contract_id' => $toSubleaseID,
                    'farming_id' => $results[$i]['farming_id'],
                    'rep_id' => $results[$i]['rep_id'],
                    'proxy_date' => $results[$i]['proxy_date'],
                    'proxy_num' => $results[$i]['proxy_num'],
                ],
            ];

            $rel_id = $UserDbController->addItem($options);

            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['options' => $options], ['relation_id' => $rel_id], 'Adding Sublease farming contragents rel');

            $farmingID = $newSubleaseData['mainData']['farming_id'];

            // add new contract
            $newSubleaseData['mainData']['farming_id'] = $results[$i]['farming_id'];
            unset($newSubleaseData['mainData']['is_sublease']);
            $newSubleaseData['mainData']['from_sublease'] = $toSubleaseID;
            $recordID = $UserDbController->addItem($newSubleaseData);
            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['new_data' => $newSubleaseData], ['created_id' => $recordID], 'Adding sublease');
            // add plots to the new contract
            if ($recordID) {
                // calculate contract_area
                $options = [
                    'tablename' => $UserDbController->DbHandler->tableSubleasesPlotsContractsRel,
                    'where' => [
                        'sublease_id' => ['column' => 'sublease_id', 'compare' => '=', 'value' => $fromSubleaseID],
                    ],
                ];

                $spc_results = $UserDbController->getItemsByParams($options, false, false);

                foreach ($spc_results as $spc) {
                    $pc_rel_id_array[] = $spc['pc_rel_id'];
                }

                $options = [
                    'tablename' => $UserDbController->DbHandler->contractsPlotsRelTable,
                    'return' => [
                        'DISTINCT(plot_id)', 'SUM(contract_area) as contract_area',
                    ],
                    'where' => [
                        'pc_rel_id' => ['column' => 'id', 'compare' => 'IN', 'value' => $pc_rel_id_array],
                    ],
                    'group' => 'plot_id',
                ];

                $plot_data = $UserDbController->getItemsByParams($options, false, false);

                foreach ($plot_data as $plot) {
                    $options = [
                        'tablename' => $UserDbController->DbHandler->contractsPlotsRelTable,
                        'mainData' => [
                            'contract_id' => $recordID,
                            'plot_id' => $plot['plot_id'],
                            'contract_area' => $plot['contract_area'],
                        ],
                    ];

                    $pcRelID = $UserDbController->addItem($options);

                    $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['new_data' => $options], ['created_id' => $pcRelID], 'Adding contract plots rel');

                    // add plot_owners_rel
                    if ($pcRelID) {
                        $options = [
                            'tablename' => $UserDbController->DbHandler->plotsFarmingRelTable,
                            'mainData' => [
                                'pc_rel_id' => $pcRelID,
                                'farming_id' => $farmingID,
                                'percent' => 100,
                            ],
                        ];

                        $rel_id = $UserDbController->addItem($options);

                        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['new_data' => $options], ['created_id' => $rel_id], 'Adding plot owners rel');
                    }
                }
            }
        }

        // get contract_area for plots

        $options = [
            'tablename' => $UserDbController->DbHandler->tableSubleasesPlotsArea,
            'where' => [
                'sublease_id' => ['column' => 'sublease_id', 'compare' => '=', 'value' => $fromSubleaseID],
            ],
        ];

        $results = $UserDbController->getItemsByParams($options, false, false);
        $resultsCount = count($results);
        for ($i = 0; $i < $resultsCount; $i++) {
            $options = [
                'tablename' => $UserDbController->DbHandler->tableSubleasesPlotsArea,
                'mainData' => [
                    'sublease_id' => $toSubleaseID,
                    'plot_id' => $results[$i]['plot_id'],
                    'contract_area' => $results[$i]['contract_area'],
                ],
            ];

            $UserDbController->addItem($options);
        }
    }

    private function editCreatedContract($data)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $options = [
            'tablename' => $UserDbController->DbHandler->tableContracts,
            'return' => ['id', 'due_date'],
            'where' => [
                'from_sublease' => ['column' => 'from_sublease', 'compare' => '=', 'value' => $data->sublease_id],
            ],
        ];
        $contractData = $UserDbController->getItemsByParams($options);
        $contractID = $contractData[0]['id'];
        $dueDate = $contractData[0]['due_date'];

        if (!$contractID) {
            return;
        }
        $params = [
            'tablename' => $UserDbController->DbHandler->tableContracts,
            'mainData' => [
                'c_num' => $data->c_num,
                'c_date' => $data->c_date,
                'nm_usage_rights' => $data->nm_usage_rights,
                'sv_num' => $data->sv_num,
                'start_date' => $data->start_date,
                'due_date' => $data->due_date,
                'renta' => $data->renta,
                'comment' => $data->comment,
                'is_declaration_subleased' => $data->is_declaration_subleased,
            ],
            'where' => [
                'id' => $contractID,
            ],
        ];

        if ('' != $data->sv_date) {
            $params['mainData']['sv_date'] = $data->sv_date;
        }

        if ('' != $data->pd_day && '' != $data->pd_month) {
            $params['mainData']['payday'] = $data->pd_day . '-' . $data->pd_month;
        }

        $UserDbController->editItem($params);

        $params = [
            'tablename' => $UserDbController->DbHandler->contractsPlotsRelTable,
            'mainData' => [
                'contract_end_date' => $dueDate,
            ],
            'where' => [
                'contract_id' => $contractID,
            ],
        ];

        $UserDbController->editItem($params);

        if (count($data->additionalRentas) > 0) {
            $deleteOptions = [
                'tablename' => $UserDbController->DbHandler->contractsRentsRelTable,
                'id_name' => 'contract_id',
                'id_string' => $contractID,
            ];
            $UserDbController->deleteItemsByParams($deleteOptions);
            $rentaParams = [
                'tablename' => $UserDbController->DbHandler->contractsRentsRelTable,
                'mainData' => [
                    'contract_id' => $contractID,
                ],
            ];
            foreach ($data->additionalRentas as $renta) {
                $currentAdditionalRenta = (object)$renta;

                if ((null == $currentAdditionalRenta->type || 0 == $currentAdditionalRenta->type) && (0 == $currentAdditionalRenta->value || '' == $currentAdditionalRenta->value)) {
                    continue;
                }

                if (0 != $currentAdditionalRenta->value) {
                    $rentaParams['mainData']['renta_id'] = $currentAdditionalRenta->type;
                    $rentaParams['mainData']['renta_value'] = $currentAdditionalRenta->value;
                    $UserDbController->addItem($rentaParams);
                }
            }
        }
    }

    private function copySubleasePlotsContractsRel($plots, $toSubleaseID)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        foreach ($plots as $plot) {
            $pc_rel_id_aggs = explode(',', trim($plot['pc_rel_id_agg'], '{}'));
            foreach ($pc_rel_id_aggs as $pc_rel_id) {
                $options = [
                    'tablename' => $UserDbController->DbHandler->tableSubleasesPlotsContractsRel,
                    'mainData' => [
                        'sublease_id' => $toSubleaseID,
                        'pc_rel_id' => $pc_rel_id,
                    ],
                ];

                $rel_id = $UserDbController->addItem($options);

                $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['new_rel_id' => $rel_id], ['pc_rel_id' => $pc_rel_id], 'Adding Sublease plot contract rel');
            }
        }
    }

    private function addSubleasePlotsArea($toSubleaseId, $plots)
    {
        $UserDbController = new UserDbController($this->User->Database);

        foreach ($plots as $plot) {
            $options = [
                'tablename' => $UserDbController->DbHandler->tableSubleasesPlotsArea,
                'mainData' => [
                    'sublease_id' => $toSubleaseId,
                    'plot_id' => $plot['gid'],
                    'contract_area' => $plot['contract_area'],
                    'rent_area' => $plot['rent_area'],
                ],
            ];

            $UserDbController->addItem($options);
        }
    }

    private function copySubleaseContragentsData($fromSubleaseId, $toSubleaseId)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        // get sublease contragents data
        $options = [
            'tablename' => $UserDbController->DbHandler->tableContractsContragents,
            'where' => [
                'contract_id' => ['column' => 'contract_id', 'compare' => '=', 'value' => $fromSubleaseId],
            ],
        ];

        $results = $UserDbController->getItemsByParams($options, false, false);
        $resultsCount = count($results);
        for ($i = 0; $i < $resultsCount; $i++) {
            $options = [
                'tablename' => $UserDbController->DbHandler->tableContractsContragents,
                'mainData' => [
                    'contract_id' => $toSubleaseId,
                    'owner_id' => $results[$i]['owner_id'],
                    'rep_id' => $results[$i]['rep_id'],
                    'proxy_date' => $results[$i]['proxy_date'],
                    'proxy_num' => $results[$i]['proxy_num'],
                ],
            ];

            $rel_id = $UserDbController->addItem($options);

            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['options' => $options], ['relation_id' => $rel_id], 'Adding Sublease contragent rel');
        }
    }

    private function getContractFarmingContragents($subleaseId)
    {
        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableContractsFarmingContragents,
            'where' => [
                'contract_id' => ['column' => 'contract_id', 'compare' => '=', 'value' => $subleaseId],
                'limit' => 1,
                'offset' => 0,
            ],
        ];

        return $UserDbController->getItemsByParams($options, false, false);
    }

    private function createContract($fromSubleaseId, $toSubleaseId)
    {
        $results = $this->getContractFarmingContragents($fromSubleaseId);

        if (count($results)) {
            $farmingContragentsData = reset($results);
            $farmingContragentsData['sublease_id'] = $toSubleaseId;

            $subleaseContragentsData = new SubleaseContragentData($this->rpcServer);
            $subleaseContragentsData->createContractFromSublease($farmingContragentsData, $farmingContragentsData['rep_id']);
        }
    }
}
