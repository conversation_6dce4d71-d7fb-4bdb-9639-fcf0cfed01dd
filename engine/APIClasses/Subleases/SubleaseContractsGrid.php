<?php

namespace TF\Engine\APIClasses\Subleases;

use Prado;
use Prado\Web\Services\TRpcApiProvider;
use TF\Application\Common\Config;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbSubleases\UserDbSubleasesController;
use TF\Engine\Plugins\Core\Users\UsersController;

include_once __DIR__ . '/../../Plugins/Core/Contracts/conf.php';

// Prado::using('Plugins.Core.Users.conf');
// Prado::using('Plugins.Core.Users.*');
// Prado::using('Plugins.Core.Farming.conf');
// Prado::using('Plugins.Core.Farming.*');
// Prado::using('Plugins.Core.UserDbPlots.*');
// Prado::using('Plugins.Core.UserDb.*');
// Prado::using('Plugins.Core.UserDbSubleases.*');
// Prado::using('Plugins.Core.Contracts.conf');

/**
 * @rpc-module Subleases
 *
 * @rpc-service-id sublease-contracts-grid
 */
class SubleaseContractsGrid extends TRpcApiProvider
{
    /**
     * Registers the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'read']],
        ];
    }

    /**
     * reads the contracts that a plot is involved in.
     *
     * @api-method read
     *
     * @param array $params {
     *                      #item integer sublease_id    id of the sublease
     *                      #item integer plot_id        id of the plot
     *                      }
     * @param int $page pagination rpc parameter
     * @param int $rows pagination rpc parameter
     * @param string $sort pagination rpc parameter
     * @param string $order pagination rpc parameter
     *
     * @return array {
     *               #item array rows {
     *               #item array {
     *               #item string c_date
     *               #item string c_num
     *               #item integer c_type
     *               #item double contract_area
     *               #item string farming
     *               #item integer farming_id
     *               #item integer id
     *               #item integer nm_usage_rights
     *               #item integer pc_rel_id
     *               }
     *               }
     *               }
     */
    public function read($params = [], $page = null, $rows = null, $sort = null, $order = null)
    {
        $sublease_id = $params['sublease_id'];
        $plot_id = $params['plot_id'];

        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        $FarmingController = new FarmingController('Farming');
        $UserDbSubleasesController = new UserDbSubleasesController($this->User->Database);

        if ($this->User->isGuest) {
            return [];
        }
        // create default return
        $return = [
            'rows' => [],
            'total' => 0,
        ];

        if (!$sublease_id || !$plot_id) {
            return $return;
        }
        $options = [
            'return' => [
                'DISTINCT(c.id)', 'c.c_date', 'c.c_num', 'c.nm_usage_rights', 'pc.id as pc_rel_id',
                'farming_id', 'round(pc.contract_area::numeric, 3) AS contract_area', 'c.active',
            ],
            'where' => [
                'sublease_id' => ['column' => 'sublease_id', 'compare' => '=', 'predix' => 'spc', 'value' => $sublease_id],
                'plot_id' => ['column' => 'plot_id', 'compare' => '=', 'predix' => 'pc', 'value' => $plot_id],
            ],
            'custom_counter' => 'COUNT(DISTINCT(c.id))',
            'group' => 'c.id, pc.id',
        ];

        $counter = $UserDbSubleasesController->getSubleasePlotContracts($options, true, false);
        if (0 == $counter[0]['count']) {
            return $return;
        }

        $results = $UserDbSubleasesController->getSubleasePlotContracts($options, false, false);

        $resultsCount = count($results);
        // get all group farmings and create array like predefined config
        $final_farming = [];
        // options for farming query
        $options = [
            'where' => [
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'value' => $this->User->GroupID],
            ],
        ];
        $farming_array = $FarmingController->getFarmings($options);
        $farmingCount = count($farming_array);
        if (0 != $farmingCount) {
            for ($i = 0; $i < $farmingCount; $i++) {
                $final_farming[$farming_array[$i]['id']] = $farming_array[$i];
            }
        }

        for ($i = 0; $i < $resultsCount; $i++) {
            $results[$i]['c_date'] = strftime('%d.%m.%Y', strtotime($results[$i]['c_date']));
            $results[$i]['c_type'] = $GLOBALS['Contracts']['ContractTypes'][$results[$i]['nm_usage_rights']]['name'];
            $results[$i]['farming'] = $final_farming[$results[$i]['farming_id']]['name'];
        }

        return [
            'rows' => $results,
            'total' => $counter[0]['count'],
        ];
    }
}
