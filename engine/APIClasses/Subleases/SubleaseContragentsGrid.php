<?php

namespace TF\Engine\APIClasses\Subleases;

use Prado;
use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbOwners\UserDbOwnersController;
use TF\Engine\Plugins\Core\UserDbSubleases\UserDbSubleasesController;
use TF\Engine\Plugins\Core\Users\UsersController;

// Prado::using('Plugins.Core.Users.conf');
// Prado::using('Plugins.Core.Users.*');
// Prado::using('Plugins.Core.Farming.conf');
// Prado::using('Plugins.Core.Farming.*');
// Prado::using('Plugins.Core.Contracts.*');
// Prado::using('Plugins.Core.Contracts.conf');
// Prado::using('Plugins.Core.Layers.*');
// Prado::using('Plugins.Core.UserDb.*');
// Prado::using('Plugins.Core.UserDbOwners.*');
// Prado::using('Plugins.Core.UserDbContracts.*');
// Prado::using('Plugins.Core.UserDbSubleases.*');

/**
 * @rpc-module Subleases
 *
 * @rpc-service-id sublease-contragents-grid
 */
class SubleaseContragentsGrid extends TRpcApiProvider
{
    /**
     * Registers the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'readContragents' => ['method' => [$this, 'readContragents']],
        ];
    }

    /**
     * Reads the contragents to a sublreased conract.
     *
     * @api-method readContragents
     *
     * @param array $params {
     *                      #item integer sublease_id
     *                      #item string  type
     *                      #item array filters {
     *                      #item string contragent_name
     *                      #item string egn
     *                      #item string eik
     *                      #item string company_name
     *                      }
     *                      }
     * @param int $page,
     * @param int $rows,
     * @param string $sort,
     * @param string $order
     *
     * @return array result{
     *               #item integer total
     *               #item array rows{
     *               #item array {
     *               #item integer id
     *               #item integer owner_id
     *               #item string owner_names
     *               #item string rep_names
     *               }
     *               }
     *               }
     */
    public function readContragents($params = [], $page = null, $rows = null, $sort = null, $order = null)
    {
        $type = $params['type'];
        $sublease_id = $params['sublease_id'];
        $filters = $params['filters'];

        $UserDbController = new UserDbController($this->User->Database);
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);
        $UsersController = new UsersController('Users');
        $FarmingController = new FarmingController('Farming');
        $UserDbSubleasesController = new UserDbSubleasesController($this->User->Database);

        // create default return
        $return = [
            'rows' => [],
            'total' => 0,
        ];

        if (!$type) {
            return $return;
        }

        if (!$sublease_id || !(int) $sublease_id) {
            return $return;
        }

        $options = [
            'return' => [
                'cc.id as id', 'o.id as owner_id',
                "(CASE WHEN owner_type = 1 THEN TRIM(name) || ' ' || TRIM(surname) || ' ' || TRIM(lastname) ELSE company_name END) as owner_names",
                "TRIM(r.rep_name) || ' ' || TRIM(r.rep_surname) || ' ' || TRIM(r.rep_lastname) as rep_names",
            ],
            'where' => [
                'sublease_id' => ['column' => 'contract_id', 'compare' => '=', 'prefix' => 'cc', 'value' => $sublease_id],
            ],
        ];

        $results = $UserDbSubleasesController->getSubleasesContragentsData($options);

        switch ($type) {
            case 'view':
                $return = [
                    'rows' => $results,
                    'total' => count($results),
                ];

                break;
            case 'add':
                $return = $this->getAddTypeContragents($results, $params, $page, $rows, $sort, $order);

                break;
            default:
                return $return;

                break;
        }

        return $return;
    }

    /**
     * @param array $results
     *                       * @param array $params {
     *                       #item integer sublease_id
     *                       #item string  type
     *                       #item array filters {
     *                       #item string contragent_name
     *                       #item string egn
     *                       #item string eik
     *                       #item string company_name
     *                       }
     *                       }
     * @param int $page
     * @param int $rows
     * @param string $sort
     * @param string $order
     *
     * @return array result{
     *               #item integer total
     *               #item array rows {
     *               #item array {
     *               #item integer id
     *               #item integer owner_id
     *               #item string owner_names
     *               #item string rep_names
     *               }
     *               }
     *               }
     */
    private function getAddTypeContragents($results = [], $params, $page = null, $rows = null, $sort = null, $order = null)
    {
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);

        $type = $params['type'];
        $sublease_id = $params['sublease_id'];
        $filters = $params['filters'];
        $resultsCount = count($results);
        // create default ID string
        $id_string = '0';
        // create plot owners relation id array
        if ($resultsCount) {
            // clear old data
            $owner_id_array = [];

            for ($i = 0; $i < $resultsCount; $i++) {
                $owner_id_array[] = $results[$i]['owner_id'];
            }

            $id_string = implode(',', $owner_id_array);
        }

        $filters['contragent_name'] = $filters['contragent_name'] ? preg_replace('/\s+/', ' ', $filters['contragent_name']) : '';
        $filters['company_name'] = $filters['company_name'] ? preg_replace('/\s+/', ' ', $filters['company_name']) : '';

        $options = [
            'return' => [
                'o.id', 'o.owner_type', '(CASE WHEN o.owner_type = 1 THEN o.egn ELSE o.eik END) as egn_eik',
                "(CASE WHEN o.owner_type = 1 THEN o.name || ' ' || o.surname || ' ' || o.lastname ELSE o.company_name END) as owner_names",
            ],
            'custom_counter' => 'count(distinct(o.id))',
            'anti_id_string' => $id_string,
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
            'where' => [
                'egn' => ['column' => 'egn', 'prefix' => 'o',  'compare' => 'ILIKE', 'value' => $filters['egn']],
                'eik' => ['column' => 'eik', 'prefix' => 'o',  'compare' => 'ILIKE', 'value' => $filters['eik']],
                'company_name' => ['column' => 'company_name', 'prefix' => 'o',  'compare' => 'ILIKE', 'value' => $filters['company_name']],
            ],
            'group' => 'o.id',
        ];

        if (isset($filters['contragent_name']) && '' != $filters['contragent_name']) {
            $tmp_owner_names = preg_replace('/\s+/', '.*', $filters['contragent_name']);
            $tmp_owner_names = mb_strtolower($tmp_owner_names, 'UTF-8');
            $options['where']['contragent_name'] = ['column' => "lower(TRIM (o.NAME)) || ' ' || lower(TRIM (o.surname)) || ' ' || lower(TRIM (o.lastname))", 'compare' => '~', 'value' => $tmp_owner_names];
        }

        $owners_data = $UserDbOwnersController->getOwnersData($options, false);
        $counter = $UserDbOwnersController->getOwnersData($options, true);

        return [
            'rows' => $owners_data,
            'total' => $counter[0]['count'],
        ];
    }
}
