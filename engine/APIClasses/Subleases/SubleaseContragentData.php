<?php

namespace TF\Engine\APIClasses\Subleases;

use Prado;
use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbContracts\UserDbContractsController;
use TF\Engine\Plugins\Core\Users\UsersController;

// Prado::using('Plugins.Core.UserDbContracts.*');
/**
 * @rpc-module Subleases
 *
 * @rpc-service-id sublease-contragent-data
 */
class SubleaseContragentData extends TRpcApiProvider
{
    private $module = 'Subleases';
    private $service_id = 'sublease-contragent-data';

    /**
     * Registers the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'addSubleaseContragent' => ['method' => [$this, 'addSubleaseContragent']],
            'deleteSubleaseContragent' => ['method' => [$this, 'deleteSubleaseContragent']],
            'createContractFromSublease' => ['method' => [$this, 'createContractFromSublease']],
        ];
    }

    /**
     * Adds a contragent to a sublreased conract.
     *
     * @api-method addSubleaseContragent
     *
     * @param array $contragentData {
     *                              #item boolean self_rep
     *                              #item integer owner_id
     *                              #item integer rep_id
     *                              #item integer sublease_id
     *                              #item string  proxy_date
     *                              #item string  proxy_num
     *                              #item string  notary_name
     *                              #item string  notary_number
     *                              #item string  notary_address
     *                              #item string  farming_id
     *                              }
     *
     * @throws MTRpcException
     *
     * @return array
     */
    public function addSubleaseContragent($contragentData)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        $FarmingController = new FarmingController('Farming');

        // if owner represents himself a representative record should be added(if not exists)
        if ($contragentData['self_rep']) {
            // check if rep exists
            $options = [
                'tablename' => $UserDbController->DbHandler->tableOwnersReps,
                'where' => [
                    'owner_id' => ['column' => 'owner_id', 'compare' => '=', 'value' => $contragentData['owner_id']],
                ],
            ];

            $exist_results = $UserDbController->getItemsByParams($options, false, false);
            $repID = $exist_results[0]['id'];

            // if count is 0 then rep does not exist yet
            if (0 == count($exist_results)) {
                $this->CheckIfOwnerExists($contragentData['owner_id']);
                // get owners data
                $options = [
                    'tablename' => $UserDbController->DbHandler->tableOwners,
                    'where' => [
                        'owner_id' => ['column' => 'id', 'compare' => '=', 'value' => $contragentData['owner_id']],
                    ],
                ];

                $owner_results = $UserDbController->getItemsByParams($options);

                // adding owner
                $options = [
                    'tablename' => $UserDbController->DbHandler->tableOwnersReps,
                    'mainData' => [
                        'rep_name' => $owner_results[0]['name'],
                        'rep_surname' => $owner_results[0]['surname'],
                        'rep_lastname' => $owner_results[0]['lastname'],
                        'rep_egn' => $owner_results[0]['egn'],
                        'rep_lk' => $owner_results[0]['lk_nomer'],
                        'rep_lk_izdavane' => $owner_results[0]['lk_izdavane'],
                        'rep_address' => $owner_results[0]['address'],
                        'owner_id' => $owner_results[0]['id'],
                    ],
                ];

                $repID = $UserDbController->addItem($options);

                $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['new_data' => $options], ['created_id' => $repID], 'Adding owner representative');
            }
        } else {
            $repID = $contragentData['rep_id'];
        }

        if ($contragentData['owner_id']) {
            $options = [
                'tablename' => $UserDbController->DbHandler->tableContractsContragents,
                'mainData' => [
                    'owner_id' => $contragentData['owner_id'],
                    'rep_id' => $repID,
                    'contract_id' => $contragentData['sublease_id'],
                    'proxy_date' => strlen($contragentData['proxy_date']) ? $contragentData['proxy_date'] : 'now()',
                    'proxy_num' => $contragentData['proxy_num'],
                    'notary_name' => $contragentData['notary_name'],
                    'notary_number' => $contragentData['notary_number'],
                    'notary_address' => $contragentData['notary_address'],
                ],
            ];
            $rel_id = $UserDbController->addItem($options);

            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['new_data' => $options], ['created_id' => $rel_id], 'Adding contract contragents');
        } elseif ($contragentData['farming_id']) {
            $this->createContractFromSublease($contragentData, $repID);
        }
    }

    public function createContractFromSublease($contragentData, $repID)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        $FarmingController = new FarmingController('Farming');

        $options = [
            'tablename' => $UserDbController->DbHandler->tableContractsFarmingContragents,
            'where' => [
                'farming_id' => ['column' => 'farming_id', 'compare' => '=', 'value' => $contragentData['farming_id']],
                'contract_id' => ['column' => 'contract_id', 'compare' => '=', 'value' => $contragentData['sublease_id']],
            ],
        ];
        $farmingContragentResults = $UserDbController->getItemsByParams($options, false, false);
        if ($farmingContragentResults[0]['farming_id'] > 0) {
            throw new MTRpcException('CANNOT_ADD_MORE_FARMING_CONTRAGENTS', -33751);
        }

        if (!$repID) {
            // get the default farming representative
            $farming = $FarmingController->getFarmingItemsByIDString($contragentData['farming_id'], $this->User->GroupID);
            if (!empty($farming)) {
                if (empty($farming[0]['mol'])) {
                    throw new MTRpcException('EMPTY_MOL', -33753);
                }
                $repID = $farming[0]['representative_id'];
            }
        }

        $options = [
            'tablename' => $UserDbController->DbHandler->tableContractsFarmingContragents,
            'mainData' => [
                'farming_id' => $contragentData['farming_id'],
                'rep_id' => $repID,
                'contract_id' => $contragentData['sublease_id'],
                'proxy_date' => strlen($contragentData['proxy_date']) ? $contragentData['proxy_date'] : 'now()',
                'proxy_num' => $contragentData['proxy_num'],
                'notary_name' => $contragentData['notary_name'],
                'notary_number' => $contragentData['notary_number'],
                'notary_address' => $contragentData['notary_address'],
            ],
        ];
        $record = $UserDbController->addItem($options);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['new_data' => $options], ['created_id' => $record], 'Adding farming contragents');

        if (!$record) {
            return;
        }

        $options = [
            'tablename' => $UserDbController->DbHandler->tableContracts,
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $contragentData['sublease_id']],
            ],
        ];

        $result = $UserDbController->getItemsByParams($options, false, false);
        $sublease_data = $result[0];

        // add new contract
        $params = [
            'tablename' => $UserDbController->DbHandler->tableContracts,
        ];
        $params['mainData'] = [
            'c_num' => $sublease_data['c_num'],
            'c_date' => $sublease_data['c_date'],
            'nm_usage_rights' => $sublease_data['nm_usage_rights'],
            'start_date' => $sublease_data['start_date'],
            'due_date' => $sublease_data['due_date'],
            'farming_id' => $contragentData['farming_id'],
            'renta' => $sublease_data['renta'],
            'renta_nat' => $sublease_data['renta_nat'],
            'renta_nat_type_id' => $sublease_data['renta_nat_type_id'],
            'sv_num' => $sublease_data['sv_num'],
            'sv_date' => $sublease_data['sv_date'],
            'comment' => $sublease_data['comment'],
            'from_sublease' => $sublease_data['id'],
            'is_declaration_subleased' => $sublease_data['is_declaration_subleased'],
        ];

        $recordID = $UserDbController->addItem($params);
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['new_data' => $params], ['created_id' => $recordID], 'Auto adding contract');

        // add plots and rents to the new contract
        if ($recordID) {
            $UserDbController->disableRentaMatViewTriggers();

            $options = [
                'tablename' => $UserDbController->DbHandler->contractsRentsRelTable,
                'where' => [
                    'contract_id' => ['column' => 'contract_id', 'compare' => '=', 'value' => $contragentData['sublease_id']],
                ],
            ];

            $rentsData = $UserDbController->getItemsByParams($options, false, false);

            foreach ($rentsData as $currentRent) {
                $rentaParams = [
                    'tablename' => $UserDbController->DbHandler->contractsRentsRelTable,
                    'mainData' => [
                        'contract_id' => $recordID,
                        'renta_id' => $currentRent['renta_id'],
                        'renta_value' => $currentRent['renta_value'],
                    ],
                ];
                $UserDbController->addItem($rentaParams);
            }
            // calculate contract_area
            $options = [
                'tablename' => $UserDbController->DbHandler->tableSubleasesPlotsContractsRel,
                'where' => [
                    'sublease_id' => ['column' => 'sublease_id', 'compare' => '=', 'value' => $contragentData['sublease_id']],
                ],
            ];
            $spc_results = $UserDbController->getItemsByParams($options, false, false);
            $spc_count = count($spc_results);
            if (0 == count($spc_results)) {
                return [];
            }

            for ($i = 0; $i < $spc_count; $i++) {
                $pc_rel_id_array[] = $spc_results[$i]['pc_rel_id'];
            }

            $options = [
                'tablename' => $UserDbController->DbHandler->contractsPlotsRelTable,
                'return' => [
                    'DISTINCT(plot_id)', 'SUM(contract_area) as contract_area',
                ],
                'where' => [
                    'pc_rel_id' => ['column' => 'id', 'compare' => 'IN', 'value' => $pc_rel_id_array],
                ],
                'group' => 'plot_id',
            ];

            $plot_data = $UserDbController->getItemsByParams($options, false, false);

            // options for farming query
            $options = [
                'where' => [
                    'farming_id' => ['column' => 'id', 'compare' => '=', 'value' => $sublease_data['farming_id']],
                ],
            ];
            $result = $FarmingController->getFarmings($options);
            $farming_data = $result[0];
            $plot_ids = array_column($plot_data, 'plot_id');

            $options = [
                'tablename' => $UserDbController->DbHandler->tableSubleasesPlotsArea,
                'return' => [
                    'DISTINCT(plot_id)', 'SUM(contract_area) as contract_area',
                ],
                'where' => [
                    'plot_id' => ['column' => 'plot_id', 'compare' => 'IN', 'value' => $plot_ids],
                    'sublease_id' => ['column' => 'sublease_id', 'compare' => '=', 'value' => $contragentData['sublease_id']],
                ],
                'group' => 'plot_id',
            ];

            $sublease_plot_data = $UserDbController->getItemsByParams($options, false, false);
            $subleaseCount = count($sublease_plot_data);
            for ($i = 0; $i < $subleaseCount; $i++) {
                $options = [
                    'tablename' => $UserDbController->DbHandler->contractsPlotsRelTable,
                    'mainData' => [
                        'contract_id' => $recordID,
                        'plot_id' => $sublease_plot_data[$i]['plot_id'],
                        'contract_area' => $sublease_plot_data[$i]['contract_area'],
                        'contract_end_date' => $sublease_data['due_date'],
                        'area_for_rent' => $sublease_plot_data[$i]['contract_area'],
                    ],
                ];

                $pcRelID = $UserDbController->addItem($options);

                $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['new_data' => $options], ['created_id' => $pcRelID], 'Adding contract plot rel');

                // add plot_owners_rel
                if ($pcRelID) {
                    $options = [
                        'tablename' => $UserDbController->DbHandler->plotsFarmingRelTable,
                        'mainData' => [
                            'pc_rel_id' => $pcRelID,
                            'farming_id' => $farming_data['id'],
                            'percent' => 100,
                        ],
                    ];

                    $rel_id = $UserDbController->addItem($options);

                    $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['new_data' => $options], ['created_id' => $rel_id], 'Adding contract farming rel');
                }
            }

            $UserDbController->enableRentaMatViewTriggers();
            $UserDbController->refreshRentaViews();
        }
    }

    /**
     * Deletes a contragent from subleased contract.
     *
     * @api-method deleteSubleaseContragent
     *
     * @param int $contragentId
     * @param string $contragentType the type can be 'owner' or 'farming'
     *
     * @throws MTRpcException
     */
    public function deleteSubleaseContragent($contragentId, $contragentType)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        $UserDbContractsController = new UserDbContractsController($this->User->Database);

        if ('owner' !== $contragentType && 'farming' !== $contragentType) {
            throw new MTRpcException('NON_EXISTING_CONTRAGENT_TYPE', -33208);
        }
        $contract_data = [];
        $options = [];
        if ('owner' == $contragentType) {
            $options = [
                'tablename' => $UserDbController->DbHandler->tableContractsContragents,
                'id_string' => $contragentId,
            ];
        }
        if ('farming' == $contragentType) {
            $farming_contragent_options = [
                'tablename' => $UserDbController->DbHandler->tableContractsFarmingContragents,
                'where' => [
                    'id' => ['column' => 'id', 'compare' => '=', 'value' => $contragentId],
                ],
            ];
            $results = $UserDbController->getItemsByParams($farming_contragent_options);
            if (count($results)) {
                $UserDbController->disableRentaMatViewTriggers();

                $farming_data = $results[0];
                $contract_data_options = [
                    'return' => ['c.*'],
                    'where' => [
                        'from_sublease' => ['column' => 'from_sublease', 'compare' => '=', 'prefix' => 'c', 'value' => $farming_data['contract_id']],
                        'farming' => ['column' => 'farming_id', 'compare' => '=', 'prefix' => 'c', 'value' => $farming_data['farming_id']],
                    ],
                ];
                $results = $UserDbContractsController->getFullContractDataByFilter($contract_data_options, false, false);

                if (count($results) > 0) {
                    $contract_data = $results[0];

                    $rentaOptions = [
                        'tablename' => $UserDbController->DbHandler->contractsRentsRelTable,
                        'id_string' => $contract_data['id'],
                        'id_name' => 'contract_id',
                    ];
                    $UserDbController->deleteItemsByParams($rentaOptions);

                    $contract_delete_options = [
                        'tablename' => $UserDbController->DbHandler->tableContracts,
                        'id_string' => $contract_data['id'],
                    ];
                    $UserDbController->deleteItemsByParams($contract_delete_options);
                }
            }

            $UserDbController->enableRentaMatViewTriggers();
            $UserDbController->refreshRentaViews();

            $options = [
                'tablename' => $UserDbController->DbHandler->tableContractsFarmingContragents,
                'id_string' => $contragentId,
            ];
        }

        $UserDbController->deleteItemsByParams($options);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['request' => ['contragent_id' => $contragentId, 'contragent_type' => $contragentType]], ['deletedContractData' => $contract_data], 'Deleting sublease contragent');
    }

    /**
     * @throws MTRpcException
     */
    private function CheckIfOwnerExists($ownerId)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $options = [
            'tablename' => $UserDbController->DbHandler->tableOwners,
            'where' => [
                'owner_id' => ['column' => 'id', 'compare' => '=', 'value' => $ownerId],
            ],
        ];

        $owner_results = $UserDbController->getItemsByParams($options);

        if (empty($owner_results[0])) {
            throw new MTRpcException('non_existing_owner_id', -33206);
        }
    }
}
