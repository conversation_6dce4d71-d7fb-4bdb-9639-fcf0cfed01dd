<?php

namespace TF\Engine\APIClasses\Subleases;

use Prado;
use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbOwners\UserDbOwnersController;
use TF\Engine\Plugins\Core\UserDbSubleases\UserDbSubleasesController;
use TF\Engine\Plugins\Core\Users\UsersController;

// Prado::using('Plugins.Core.Users.conf');
// Prado::using('Plugins.Core.Users.*');
// Prado::using('Plugins.Core.Farming.conf');
// Prado::using('Plugins.Core.Farming.*');
// Prado::using('Plugins.Core.Contracts.*');
// Prado::using('Plugins.Core.Contracts.conf');
// Prado::using('Plugins.Core.Layers.*');
// Prado::using('Plugins.Core.UserDb.*');
// Prado::using('Plugins.Core.UserDbOwners.*');
// Prado::using('Plugins.Core.UserDbContracts.*');
// Prado::using('Plugins.Core.UserDbSubleases.*');

/**
 * @rpc-module Subleases
 *
 * @rpc-service-id sublease-farming-contragents-grid
 *
 * @property UserDbController $UserDbController
 * @property UserDbOwnersController $UserDbOwnersController
 * @property UsersController $UsersController
 * @property FarmingController $FarmingController
 * @property UserDbSubleasesController $UserDbSubleasesController
 */
class SubleaseFarmingContragentsGrid extends TRpcApiProvider
{
    /**
     * Class Controllers
     * Define all class controllers as properties.
     */
    private $UserDbController;
    private $UserDbOwnersController;
    private $UsersController;
    private $FarmingController;
    private $UserDbSubleasesController;

    /**
     * Registers the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'read']],
        ];
    }

    /**
     * Reads the farming contragents to a sublreased conract.
     *
     * @api-method read
     *
     * @param array $params {
     *                      #item integer sublease_id
     *                      #item string  type
     *                      }
     * @param int $page,
     * @param int $rows,
     * @param string $sort,
     * @param string $order
     *
     * @return array $result {
     *               #item integer total
     *               #item array rows {
     *               #item integer id
     *               #item integer farming_id
     *               #item string farming
     *               #item string rep_names
     *               }
     *               }
     */
    public function read($params = [], $page = null, $rows = null, $sort = null, $order = null)
    {
        $type = $params['type'];
        $sublease_id = $params['sublease_id'];

        $UserDbController = new UserDbController($this->User->Database);
        $FarmingController = new FarmingController('Farming');
        $UserDbSubleasesController = new UserDbSubleasesController($this->User->Database);

        // create default return
        $return = [
            'rows' => [],
            'total' => 0,
        ];

        if (!$type) {
            return $return;
        }

        if (!$sublease_id || !(int) $sublease_id) {
            return $return;
        }

        $userFarmings = $FarmingController->getUserFarmings();
        $farmingIds = array_keys($userFarmings);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableContracts,
            'return' => [
                'farming_id',
            ],
            'where' => [
                'sublease_id' => ['column' => 'id', 'compare' => '=', 'value' => $sublease_id],
                'farming_id' => ['column' => 'farming_id', 'compare' => 'IN', 'value' => $farmingIds],
            ],
        ];
        $results = $UserDbController->getItemsByParams($options);
        $contractFarming = $results[0]['farming_id'];
        $options = [
            'return' => [
                'cc.id', 'cc.farming_id',
                "r.rep_name || ' ' || r.rep_surname || ' ' || r.rep_lastname as rep_names",
            ],
            'where' => [
                'sublease_id' => ['column' => 'contract_id', 'compare' => '=', 'prefix' => 'cc', 'value' => $sublease_id],
                'farming_id' => ['column' => 'farming_id', 'prefix' => 'cc', 'compare' => 'IN', 'value' => $farmingIds],
            ],
        ];

        $results = $UserDbSubleasesController->getSubleasesFarmingContragentsData($options, false, false);
        $resultsCount = count($results);

        switch ($type) {
            case 'view':
                for ($i = 0; $i < $resultsCount; $i++) {
                    $results[$i]['farming'] = $userFarmings[$results[$i]['farming_id']];
                }
                $return = [
                    'rows' => $results,
                    'total' => $resultsCount,
                ];

                break;
            case 'add':
                $options = [
                    'return' => ['id', 'name', 'company', 'bulstat', 'is_system', 'address', 'company_address', 'mol'],
                    'offset' => ($_POST['pager'] - 1) * $_POST['rows'],
                    'limit' => $_POST['rows'],
                    'sort' => $_POST['sort'],
                    'order' => $_POST['order'],
                    'where' => [
                        'id' => ['column' => 'id', 'compare' => 'IN', 'value' => $farmingIds],
                    ],
                ];

                $farming_id_array = $resultsCount ? array_column($results, 'farming_id') : [];
                array_push($farming_id_array, $contractFarming);
                $options['where']['not_id'] = ['column' => 'id', 'compare' => 'NOT IN', 'value' => $farming_id_array];
                $counter = $FarmingController->getFarmings($options, true, false);
                $farming_data = $FarmingController->getFarmings($options, false, false);

                $return = [
                    'rows' => $farming_data,
                    'total' => $counter[0]['count'],
                ];

                break;
            default:
                return $return;

                break;
        }

        return $return;
    }
}
