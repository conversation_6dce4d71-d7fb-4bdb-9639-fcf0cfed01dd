<?php

namespace TF\Engine\APIClasses\Subleases;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbOwners\UserDbOwnersController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * @rpc-module Subleases
 *
 * @rpc-service-id sublease-plot-owners-grid
 *
 * @property UserDbController $UserDbController
 * @property UsersController $UsersController
 * @property UserDbOwnersController $UserDbOwnersController
 */
class SubleasePlotOwnersGrid extends TRpcApiProvider
{
    public $relation_id;

    /**
     * Class Controllers
     * Define all class controllers as properties.
     */
    private $UserDbController;
    private $UsersController;
    private $UserDbOwnersController;

    /**
     * Registers the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'read']],
        ];
    }

    /**
     * Reads the owners of a plot of a sublreased conract.
     *
     * @api-method read
     *
     * @param array $params {
     *                      #item integer pc_rel_id
     *                      #item string type
     *                      }
     * @param null|mixed $page
     * @param null|mixed $rows
     * @param null|mixed $sort
     * @param null|mixed $order
     *
     * @return array {
     *               #item array rows {
     *               #item array {
     *               #item integer denominator
     *               #item string egn_eik
     *               #item string iconCls
     *               #item integer id
     *               #item string is_dead
     *               #item boolean is_heritor
     *               #item integer level
     *               #item integer numerator
     *               #item integer owner_id
     *               #item string owner_names
     *               #item double percent
     *               #item string rep_names
     *               }
     *               }
     *               }
     */
    public function read($params = [], $page = null, $rows = null, $sort = null, $order = null)
    {
        $pc_rel_id = $params['pc_rel_id'];
        $type = $params['type'];
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);

        if ($this->User->isGuest) {
            return [];
        }

        // create default return
        $return = [
            'rows' => [],
            'total' => 0,
        ];

        if (!$pc_rel_id) {
            return $return;
        }

        $this->relation_id = (int) $pc_rel_id;

        $options = [
            'return' => [
                'rel.percent', 'numerator', 'denominator', '(CASE WHEN owner_type = 1 THEN egn ELSE eik END) as egn_eik', 'is_dead', 'owner_id', 't.id',
                "(CASE WHEN owner_type = 1 THEN name || ' ' || surname || ' ' || lastname ELSE company_name END) as owner_names",
                "rep_name || ' ' || rep_surname || ' ' || rep_lastname as rep_names",
            ],
            'where' => [
                'pc_rel_id' => ['column' => 'pc_rel_id', 'compare' => '=', 'value' => $pc_rel_id],
                'is_heritor' => ['column' => 'is_heritor', 'compare' => '=', 'prefix' => 'rel', 'value' => 'FALSE'],
            ],
        ];

        $counter = $UserDbOwnersController->getPlotOwnersData($options, true);
        if (0 == $counter[0]['count']) {
            return $return;
        }

        $results = $UserDbOwnersController->getPlotOwnersData($options, false);
        $resultsCount = count($results);
        for ($i = 0; $i < $resultsCount; $i++) {
            if ($results[$i]['is_dead']) {
                $results[$i]['children'] = $this->getOwnerHeritors($results[$i]['owner_id'] . '.*{1}', $results[$i]['percent'], $results[$i]['numerator'], $results[$i]['denominator'], 1);
                $results[$i]['iconCls'] = 'icon-tree-user-rip';
                $results[$i]['is_dead'] = 'Да';
            } else {
                $results[$i]['iconCls'] = 'icon-tree-user';
                $results[$i]['is_dead'] = 'Не';
            }
            $results[$i]['is_heritor'] = false;
            $results[$i]['level'] = 1;
        }

        return $results;
    }

    /**
     * Returns information about the children of an owner.
     *
     * @param string $path
     * @param float $rat_ownage
     * @param int $numerator
     * @param int $denominator
     * @param int $level
     *
     * @return array
     */
    private function getOwnerHeritors($path, $rat_ownage, $numerator, $denominator, $level)
    {
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);

        ++$level;
        $options = [
            'return' => [
                'h.id', "name || ' ' || surname || ' ' || lastname as owner_names", 'owner_id', 'is_dead', 'path',
                "(SELECT poi.percent FROM su_plots_owners_rel poi WHERE poi.path = h.path AND poi.is_heritor = true AND poi.pc_rel_id = {$this->relation_id}) as percent",
                "(SELECT poi.numerator FROM su_plots_owners_rel poi WHERE poi.path = h.path AND poi.is_heritor = true AND poi.pc_rel_id = {$this->relation_id}) as numerator",
                "(SELECT poi.denominator FROM su_plots_owners_rel poi WHERE poi.path = h.path AND poi.is_heritor = true AND poi.pc_rel_id = {$this->relation_id}) as denominator",
            ],
            'where' => [
                'path' => ['column' => 'path', 'compare' => '~', 'value' => $path],
            ],
        ];

        $counter = $UserDbOwnersController->getOwnersHeritors($options, true);

        if (0 == $counter[0]['count']) {
            return [];
        }

        $results = $UserDbOwnersController->getOwnersHeritors($options, false, false);
        $resultsCount = count($results);

        $sum_custom_ownage = 0;
        $heritors = 0;
        for ($i = 0; $i < $resultsCount; $i++) {
            if (null != $results[$i]['percent']) {
                $sum_custom_ownage += $results[$i]['percent'];
            } else {
                $heritors++;
            }
        }

        for ($i = 0; $i < $resultsCount; $i++) {
            if (null != $numerator && null != $denominator && 0 == $sum_custom_ownage && null == $results[$i]['percent']) {
                $results[$i]['numerator'] = $numerator;
                $results[$i]['denominator'] = $denominator * $heritors;
            }

            if (null == $results[$i]['percent']) {
                $results[$i]['percent'] = ($rat_ownage - $sum_custom_ownage) / $heritors;
            }

            if ($results[$i]['is_dead']) {
                $results[$i]['children'] = $this->getOwnerHeritors($results[$i]['path'] . '.*{1}', $results[$i]['percent'], $results[$i]['numerator'], $results[$i]['denominator'], $level);
                $results[$i]['iconCls'] = 'icon-tree-user-rip';
            } else {
                $results[$i]['iconCls'] = 'icon-tree-user';
            }

            $results[$i]['is_heritor'] = true;
            $results[$i]['level'] = $level;
        }

        return $results;
    }
}
