<?php

namespace TF\Engine\APIClasses\Uploads;

use TF\Engine\Plugins\Core\UserDb\UserDbController;

class HypothecsUpload extends UploadsTool
{
    public function getJsonContent()
    {
        $hypothecid = (int) $_POST['hypothec_id'];
        $groupid = $this->User->GroupID;
        $userid = $this->User->UserID;

        $UsersDbController = new UserDbController($_SESSION['database']);

        $fname = pathinfo($_FILES['file']['name']);

        $options = [
            'tablename' => $UsersDbController->DbHandler->tableHypothecsFiles,
            'mainData' => [
                'hypothec_id' => $hypothecid,
                'group_id' => $groupid,
                'user_id' => $userid,
                'date' => date('Y-m-d H:i:s'),
                'filename' => $_FILES['file']['name'],
            ],
        ];
        $fileID = $UsersDbController->addItem($options);

        @mkdir(HYPOTHECS_FILES_PATH);
        @mkdir(HYPOTHECS_FILES_PATH . $groupid);
        @mkdir(HYPOTHECS_FILES_PATH . $groupid . '/' . $userid);

        copy($this->getFilePath(), HYPOTHECS_FILES_PATH . $groupid . '/' . $userid . '/' . $fileID . '.' . $fname['extension']);

        @unlink($this->getFilePath());
        // Return JSON-RPC response
        return $this->response(true);
    }
}
