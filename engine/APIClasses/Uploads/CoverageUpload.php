<?php

namespace TF\Engine\APIClasses\Uploads;

use Exception;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\Users\UsersController;

class CoverageUpload extends UploadsTool
{
    public function getJsonContent()
    {
        // init controllers
        $UsersDbController = new UserDbController($_SESSION['database']);
        $UsersController = new UsersController();

        // creating the directory
        if (!file_exists(COVERAGE_QUEUE_PATH . $this->User->GroupID)) {
            mkdir(COVERAGE_QUEUE_PATH . $this->User->GroupID, 0777);
        }

        $fileinfo = pathinfo($_FILES['file']['name']);

        try {
            // add upload item to user database
            $options = [
                'tablename' => $UsersDbController->DbHandler->tableCoverageFiles,
                'mainData' => [
                    'user_id' => $this->User->UserID,
                    'filename' => $_FILES['file']['name'],
                ],
            ];
            $userDbItemID = $UsersDbController->addItem($options);

            // add upload item to main database for processing
            $options = [
                'tablename' => 'su_users_coverage',
                'mainData' => [
                    'filename' => $_FILES['file']['name'],
                    'group_id' => $this->User->GroupID,
                    'user_id' => $this->User->UserID,
                    'item_id' => $userDbItemID,
                    'database' => $_SESSION['database'],
                ],
            ];
            $mainDbItemID = $UsersController->addCustomItem($options);

            // copy item to group location
            copy($this->getFilePath(), COVERAGE_QUEUE_PATH . $this->User->GroupID . '/' . $mainDbItemID . '.' . $fileinfo['extension']);

            // log upload action
            $UsersController->groupLog($_SESSION['username'], $_SESSION['user_id'], $_SESSION['group_id'], 'Coverage', 'File uploaded.', 'su_users_coverage', [$mainDbItemID]);
        } catch (Exception $e) {
        }
        @unlink($this->getFilePath());

        return $this->response(true);
    }
}
