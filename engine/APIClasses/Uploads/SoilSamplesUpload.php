<?php

namespace TF\Engine\APIClasses\Uploads;

use Exception;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\Users\UsersController;

class SoilSamplesUpload extends UploadsTool
{
    public function getJsonContent()
    {
        $group_id = (int) $this->User->UserID;

        $UsersDbController = new UserDbController($_SESSION['database']);
        $UsersController = new UsersController();

        // creating the directory
        if (!file_exists(SOIL_SAMPLES_QUEUE_PATH . $group_id)) {
            mkdir(SOIL_SAMPLES_QUEUE_PATH . $group_id, 0777);
        }

        try {
            // add item options
            $options = [
                'tablename' => $UsersDbController->DbHandler->tableSoilSamplesFiles,
                'mainData' => [
                    'user_id' => $_SESSION['user_id'],
                    'uploaded' => date('Y-m-d H:i:s'),
                    'filename' => $_FILES['file']['name'],
                ],
            ];

            $nid = $UsersDbController->addItem($options);

            copy($this->getFilePath(), SOIL_SAMPLES_QUEUE_PATH . $group_id . '/' . $nid . '.csv');

            $UsersController->groupLog($_SESSION['username'], $_SESSION['user_id'], $_SESSION['group_id'], 'CropRotation(Soil Sample Files)', 'File uploaded.', $UsersDbController->DbHandler->tableSoilSamplesFiles, [$id]);
        } catch (Exception $e) {
            echo($e);
        }

        @unlink($this->getFilePath());
        // Return JSON-RPC response
        return $this->response(true);
    }
}
