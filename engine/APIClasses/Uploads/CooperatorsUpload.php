<?php

namespace TF\Engine\APIClasses\Uploads;

use TF\Engine\Plugins\Core\UserDb\UserDbController;

class CooperatorsUpload extends UploadsTool
{
    public function getJsonContent()
    {
        $UsersDbController = new UserDbController($_SESSION['database']);

        $userid = (int) $_SESSION['user_id'];
        $groupid = (int) $_SESSION['user_id'];
        $cooperator_id = (int) $_POST['cooperator_id'];
        $fname = pathinfo($_FILES['file']['name']);
        $fname['filename'] = str_replace(' ', '_', $fname['filename']);

        $options = [
            'tablename' => $UsersDbController->DbHandler->tableCooperatorsFiles,
            'mainData' => [
                'cooperator_id' => $cooperator_id,
                'group_id' => $groupid,
                'user_id' => $userid,
                'date' => date('Y-m-d H:i:s'),
                'filename' => $fname['filename'] . '.' . $fname['extension'],
            ],
        ];
        $fileID = $UsersDbController->addItem($options);

        @mkdir(COOPERATORS_DOCUMENTS_PATH . $groupid);
        @mkdir(COOPERATORS_DOCUMENTS_PATH . $groupid . '/' . $userid);

        copy($this->getFilePath(), COOPERATORS_DOCUMENTS_PATH . $groupid . '/' . $userid . '/' . $fileID . '_' . $fname['filename'] . '.' . $fname['extension']);
        @unlink($this->getFilePath());

        return $this->response(true);
    }
}
