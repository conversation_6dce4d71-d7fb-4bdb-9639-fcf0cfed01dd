<?php

namespace TF\Engine\APIClasses\Uploads;

use TF\Application\Common\Config;
use TF\Engine\Kernel\LoggerMessages;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\Users\UsersController;

class FileUpload extends UploadsTool
{
    public function getJsonContent()
    {
        $UsersController = new UsersController();
        $LayersController = new LayersController();

        $userId = $this->User->UserID;
        $groupId = $this->User->GroupID;
        $fileInfo = pathinfo($_FILES['file']['name']);
        $fileExtension = $fileInfo['extension'];

        if (in_array($fileExtension, ['xls', 'xlsx'])) {
            $shapeType = Config::LAYER_TYPE_EXCEL_IMPORT;
            $prefix = 'plot_info_';
        } elseif ('zip' == $fileExtension) {
            $extractPath = PUBLIC_UPLOAD_EXTRACT . DIRECTORY_SEPARATOR . $this->User->UserID;
            $this->unzipData($extractPath);

            $extractedFilePath = $extractPath;
            if (file_exists($extractPath . DIRECTORY_SEPARATOR . $fileInfo['filename'])) {
                $extractedFilePath = $extractPath . DIRECTORY_SEPARATOR . $fileInfo['filename'];
            }

            $files = $LayersController->File->getFilesFromDir($extractedFilePath);
            $LayersController->File->removeFolder($extractedFilePath);

            $fileDo = $this->filterFiles($files, 'DO_', 'DBF');
            $filesZd = $this->filterFiles($files, 'ZD_', 'DBF');

            if (!count($fileDo) || !count($filesZd)) {
                return $this->returnError('Invalid file for consolidation source data (CSD)');
            }

            $shapeType = Config::LAYER_TYPE_CSD;
            $prefix = 'csd_info_';
        } else {
            return $this->returnError('The file is not recognized');
        }

        $fileId = $this->insertFileRecord($userId, $groupId, $fileInfo, $LayersController, $shapeType);

        $newFileName = "{$prefix}{$groupId}_{$fileId}.{$fileExtension}";
        $destinationPath = PUBLIC_UPLOAD_PLOT_DATA . DIRECTORY_SEPARATOR . $groupId;

        if (!file_exists(PUBLIC_UPLOAD_PLOT_DATA)) {
            @mkdir(PUBLIC_UPLOAD_PLOT_DATA);
        }

        if (!file_exists($destinationPath)) {
            @mkdir($destinationPath);
        }

        copy($this->getFilePath(), $destinationPath . '/' . $newFileName);
        unlink($this->getFilePath());

        $fileInfo = pathinfo($destinationPath . '/' . $newFileName);
        $this->editFileRecord($fileId, $fileInfo['basename'], $LayersController);

        $UsersController->groupLog($_SESSION['username'], $_SESSION['user_id'], $_SESSION['group_id'], 'Files', LoggerMessages::ITEM_ADD, DEFAULT_DB_PREFIX . 'users_files', [$fileId]);

        return $this->response();
    }

    private function insertFileRecord($userId, $groupId, $fileInfo, LayersController $LayersController, int $shapeType): int
    {
        $fields = [];
        $fields['name'] = $fileInfo['filename'];
        $fields['user_id'] = $userId;
        $fields['group_id'] = $groupId;
        $fields['filename'] = $userId . '_' . $_FILES['file']['name'];
        $fields['farming'] = null;
        $fields['year'] = null;
        $fields['shape_type'] = $shapeType;
        $fields['device_type'] = null;
        $fields['add_to_existing'] = 'f';

        $settings = [];
        $settings['mainData'] = $fields;

        return $LayersController->addFilesItem($settings);
    }

    /**
     * @param LayersController $LayersController
     */
    private function editFileRecord($fileId, $fileName, $LayersController): int
    {
        $options = [];
        $options['id'] = $fileId;
        $options['mainData'] = ['filename' => $fileName];

        return $LayersController->editItemFiles($options);
    }

    private function returnError(string $message): array
    {
        $this->getApplication()->getResponse()->setStatusCode(400);

        return $this->response(false, $message);
    }
}
