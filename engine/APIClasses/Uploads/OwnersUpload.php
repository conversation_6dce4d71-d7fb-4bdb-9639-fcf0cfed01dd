<?php

namespace TF\Engine\APIClasses\Uploads;

use TF\Engine\Plugins\Core\UserDb\UserDbController;

class OwnersUpload extends UploadsTool
{
    public function getJsonContent()
    {
        $userid = (int) $this->User->UserID;
        $groupid = (int) $this->User->GroupID;
        $documentid = (int) $_POST['document_id'];

        if ($_SESSION['user_id'] == $userid || $_SESSION['group_id'] == $groupid) {
            $UsersDbController = new UserDbController($_SESSION['database']);

            $tableName = $UsersDbController->DbHandler->tableOwnersDocuments;
            $filesDirPath = OWNER_DOCUMENT_FILES;

            if (isset($_POST['is_owner_file']) && 1 == $_POST['is_owner_file']) {
                $tableName = $UsersDbController->DbHandler->tableOwnersFiles;
                $filesDirPath = OWNER_FILES;
            }

            $fname = pathinfo($_FILES['file']['name']);

            $options = [
                'tablename' => $tableName,
                'mainData' => [
                    'attachment' => $fname['filename'] . '.' . $fname['extension'],
                ],
                'where' => [
                    'id' => $documentid,
                ],
            ];

            $UsersDbController->editItem($options);

            @mkdir($filesDirPath);
            @mkdir($filesDirPath . $groupid);

            copy($this->getFilePath(), $filesDirPath . $groupid . '/' . $documentid . '_' . $fname['filename'] . '.' . $fname['extension']);
        }

        @unlink($this->getFilePath());
        // Return JSON-RPC response
        return $this->response(true);
    }
}
