<?php

namespace TF\Engine\APIClasses\Uploads;

use Exception;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbOverlaps\UserDbOverlapsController;
use TF\Engine\Plugins\Core\Users\UsersController;

class OverlapsUpload extends UploadsTool
{
    public function getJsonContent()
    {
        if (isset($_POST['overlap_id']) && (int)$_POST['overlap_id']) {
            $UsersDbController = new UserDbController($_SESSION['database']);
            $UsersDbOverlapsController = new UserDbOverlapsController($_SESSION['database']);
            $UsersController = new UsersController();

            $id = $_POST['overlap_id'];

            try {
                $nid = $UsersController->addOverlapItemForProcessing($_SESSION['user_id'], $_SESSION['database'], $id);
                $UsersDbOverlapsController->updateOverlapItemStatus($id, 3);
                copy($this->getFilePath(), OVERLAPS_QUEUE_PATH . 'overlap_' . $_SESSION['user_id'] . '_' . $nid . '.csv');

                $UsersController->groupLog($_SESSION['username'], $_SESSION['user_id'], $_SESSION['group_id'], 'Overlaps', 'File uploaded.', $UsersDbController->DbHandler->tableOverlaps, [$id]);
            } catch (Exception $e) {
                $options['mainData'] = ['status' => 0];
                $options['where'] = ['id' => $id];
                $options['tablename'] = $UsersDbController->DbHandler->tableOverlaps;
                $UsersDbController->editItem($options);
            }
        }

        @unlink($this->getFilePath());
        // Return JSON-RPC response
        return $this->response(true);
    }
}
