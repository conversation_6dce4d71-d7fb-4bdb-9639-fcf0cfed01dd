<?php

namespace TF\Engine\APIClasses\Uploads;

use Exception;
use Prado\Web\Services\TJsonResponse;
use ZipArchive;

abstract class UploadsTool extends TJsonResponse
{
    protected $fileName;
    private $filePath;

    public function __construct()
    {
        // HTTP headers for no cache etc
        header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');
        header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');
        header('Cache-Control: no-store, no-cache, must-revalidate');
        header('Cache-Control: post-check=0, pre-check=0', false);
        header('Pragma: no-cache');

        // Settings
        $targetDir = PUBLIC_UPLOAD; // ini_get("upload_tmp_dir") . DIRECTORY_SEPARATOR . "plupload";
        // $targetDir = 'uploads';
        $cleanupTargetDir = true; // Remove old files
        $maxFileAge = 5 * 3600; // Temp file age in seconds
        // 5 minutes execution time
        // @set_time_limit(5 * 60);

        // Uncomment this one to fake upload time
        // usleep(5000);
        // Get parameters
        $chunk = isset($_REQUEST['chunk']) ? intval($_REQUEST['chunk']) : 0;
        $chunks = isset($_REQUEST['chunks']) ? intval($_REQUEST['chunks']) : 0;
        $this->fileName = $_REQUEST['name'] ?? '';

        // Clean the fileName for security reasons
        $this->fileName = preg_replace('/[^\w\._]+/', '_', $this->fileName);

        // Make sure the fileName is unique but only if chunking is disabled
        if ($chunks < 2 && file_exists($targetDir . DIRECTORY_SEPARATOR . $this->fileName)) {
            $ext = strrpos($this->fileName, '.');
            $fileName_a = substr($this->fileName, 0, $ext);
            $fileName_b = substr($this->fileName, $ext);

            $count = 1;
            while (file_exists($targetDir . DIRECTORY_SEPARATOR . $fileName_a . '_' . $count . $fileName_b)) {
                $count++;
            }

            $this->fileName = $fileName_a . '_' . $count . $fileName_b;
        }

        $filePath = $targetDir . DIRECTORY_SEPARATOR . $this->fileName;
        $this->setFilePath($filePath);

        // Create target dir
        if (!file_exists($targetDir)) {
            @mkdir($targetDir);
        }

        // Remove old temp files
        if ($cleanupTargetDir && is_dir($targetDir) && ($dir = opendir($targetDir))) {
            while (($file = readdir($dir)) !== false) {
                $tmpfilePath = $targetDir . DIRECTORY_SEPARATOR . $file;

                // Remove temp file if it is older than the max age and is not the current file
                if (preg_match('/\.part$/', $file) && (filemtime($tmpfilePath) < time() - $maxFileAge) && ($tmpfilePath != "{$filePath}.part")) {
                    @unlink($tmpfilePath);
                }
            }

            closedir($dir);
        } else {
            return $this->response(false, 'Failed to open temp directory.');
        }

        // Look for the content type header
        if (isset($_SERVER['HTTP_CONTENT_TYPE'])) {
            $contentType = $_SERVER['HTTP_CONTENT_TYPE'];
        }

        if (isset($_SERVER['CONTENT_TYPE'])) {
            $contentType = $_SERVER['CONTENT_TYPE'];
        }

        // Handle non multipart uploads older WebKit versions didn't support multipart in HTML5
        if (false !== strpos($contentType, 'multipart')) {
            if (isset($_FILES['file']['tmp_name']) && is_uploaded_file($_FILES['file']['tmp_name'])) {
                // Open temp file
                $out = fopen("{$filePath}.part", 0 == $chunk ? 'wb' : 'ab');
                if ($out) {
                    // Read binary input stream and append it to temp file
                    $in = fopen($_FILES['file']['tmp_name'], 'rb');

                    if ($in) {
                        while ($buff = fread($in, 4096)) {
                            fwrite($out, $buff);
                        }
                    } else {
                        return $this->response(false, 'Failed to open input stream.');
                    }
                    fclose($in);
                    fclose($out);
                    @unlink($_FILES['file']['tmp_name']);
                } else {
                    return $this->response(false, 'Failed to open output stream.');
                }
            } else {
                return $this->response(false, 'Failed to move uploaded file');
            }
        } else {
            // Open temp file
            $out = fopen("{$filePath}.part", 0 == $chunk ? 'wb' : 'ab');
            if ($out) {
                // Read binary input stream and append it to temp file
                $in = fopen('php://input', 'rb');

                if ($in) {
                    while ($buff = fread($in, 4096)) {
                        fwrite($out, $buff);
                    }
                } else {
                    return $this->response(false, 'Failed to input output stream.');
                }
                fclose($in);
                fclose($out);
            } else {
                return $this->response(false, 'Failed to open output stream.');
            }
        }

        // Check if file has been uploaded
        if (!$chunks || $chunk == $chunks - 1) {
            // Strip the temp .part suffix off
            rename("{$filePath}.part", $filePath);
        }
    }

    protected function setFilePath(string $filePath)
    {
        $this->filePath = $filePath;
    }

    protected function getFilePath()
    {
        return $this->filePath;
    }

    protected function response($success = true, $message = 'uploaded', $result = '')
    {
        return [
            'jsonrpc' => '2.0',
            'success' => $success,
            'message' => $message,
            'result' => $result,
        ];
    }

    protected function unzipData($extractPath): bool
    {
        try {
            if (!is_dir($extractPath)) {
                mkdir($extractPath, 0755, true);
            }

            $archive = new ZipArchive();
            $archive->open($this->filePath);
            $archive->extractTo($extractPath);
            $archive->close();

            return true;
        } catch (Exception $e) {
            throw new Exception($e->getMessage() . '\n=====' . $e->getTraceAsString(), ERROR_INVALID_ARCHIVE);
        }
    }

    protected function filterFiles($files, $pattern, $fileExtension): array
    {
        $filteredFiles = array_filter($files, function ($file) use ($pattern, $fileExtension) {
            $fileInfo = pathinfo($file['name']);
            $pos = strpos($file['name'], $pattern);

            return false !== $pos && $fileInfo['extension'] == $fileExtension;
        });

        return array_values($filteredFiles);
    }
}
