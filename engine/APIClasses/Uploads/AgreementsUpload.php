<?php

namespace TF\Engine\APIClasses\Uploads;

use Exception;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\Users\UsersController;

class AgreementsUpload extends UploadsTool
{
    public function getJsonContent()
    {
        if (isset($_POST['agreement_id']) && (int)$_POST['agreement_id']) {
            $UsersDbController = new UserDbController($_SESSION['database']);
            $UsersController = new UsersController();

            $id = $_POST['agreement_id'];

            try {
                $options = [
                    'return' => ['farming'],
                    'where' => [
                        'id' => ['column' => 'id', 'compare' => '=', 'value' => $id],
                    ],
                ];

                $nid = $UsersController->addAgreementItemForProcessing($_SESSION['group_id'], $_SESSION['database'], $id);

                $options = [
                    'mainData' => ['status' => 3],
                    'where' => ['id' => $id],
                    'tablename' => $UsersDbController->DbHandler->tableAgreements,
                ];

                $UsersDbController->editItem($options);

                // copy file to the current location
                copy($this->getFilePath(), AGREEMENTS_QUEUE_PATH . 'agreement_' . $_SESSION['group_id'] . '_' . $nid . '.csv');

                $UsersController->groupLog($_SESSION['username'], $_SESSION['user_id'], $_SESSION['group_id'], 'Agreements', 'File uploaded.', $UsersDbController->DbHandler->tableAgreements, [$id]);
            } catch (Exception $e) {
                $options['mainData'] = ['status' => 0];
                $options['where'] = ['id' => $id];
                $options['tablename'] = $UsersDbController->DbHandler->tableAgreements;

                $UsersDbController->editItem($options);
            }

            @unlink($this->getFilePath());

            return $this->response(true);
        }
    }
}
