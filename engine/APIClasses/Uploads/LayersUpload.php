<?php

namespace TF\Engine\APIClasses\Uploads;

use TF\Application\Common\Config;
use TF\Engine\Kernel\LoggerMessages;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\Users\UsersController;

class LayersUpload extends UploadsTool
{
    public function getJsonContent()
    {
        $UsersController = new UsersController();
        $LayersController = new LayersController();

        $userid = $this->User->UserID;
        $farming = $_POST['farming'];
        $year = $_POST['year'];
        $idprojection = (int) $_POST['projection'];

        foreach ($GLOBALS['Layers']['srid'] as $item) {
            if ($item['id'] == $idprojection) {
                $type = $item['type'];
            }
        }

        $fname = pathinfo($_FILES['file']['name']);

        $fields['name'] = $fname['filename'];
        $fields['user_id'] = $userid;
        $fields['filename'] = $userid . '_' . $_FILES['file']['name'];
        $fields['farming'] = $farming;
        $fields['year'] = $year;
        $fields['shape_type'] = $type;
        $fields['group_id'] = $_SESSION['group_id'];
        $fields['add_to_existing'] = $_POST['add_to_existing'] ? 'true' : 'false';
        $fields['device_type'] = isset($_POST['device'])
            ? (int) $_POST['device']
            : $this->getDeviceTypeByFileStructure();

        $settings['mainData'] = $fields;

        $fileId = $LayersController->addFilesItem($settings);

        $ext_pos = strrpos($fields['filename'], '.');
        $extention = substr($fields['filename'], $ext_pos);

        copy($this->getFilePath(), LAYERS_QUEUE_PATH . $userid . '_' . $fileId . $extention);
        $options = [];
        $options['mainData'] = [
            'filename' => $userid . '_' . $fileId . $extention,
        ];

        $options['id'] = $fileId;
        $LayersController->editItemFiles($options);

        $UsersController->groupLog($_SESSION['username'], $_SESSION['user_id'], $_SESSION['group_id'], 'Files', LoggerMessages::ITEM_ADD, DEFAULT_DB_PREFIX . 'users_files', [$fileId]);
        @unlink($this->getFilePath());

        return $this->response(true);
    }

    private function error_layers_log($id, $error)
    {
        $LayersController = new LayersController();

        $LayersController->log(1, 'cron-daemon', $error, [$id]);

        $options = [];
        $options['mainData'] = [
            'status' => $error,
        ];

        $options['id'] = $id;
        $LayersController->editItemFiles($options);
    }

    private function getDeviceTypeByFileStructure()
    {
        $tmpPath = DIRECTORY_SEPARATOR . 'tmp' . DIRECTORY_SEPARATOR . uniqid('layer_upload_') . DIRECTORY_SEPARATOR;
        $this->unzipData($tmpPath);
        $deviceType = Config::DEVICE_UNKNOWN;

        if (
            is_dir($tmpPath . 'AgGPS' . DIRECTORY_SEPARATOR . 'Data')
            || is_dir($tmpPath . 'AgData' . DIRECTORY_SEPARATOR . 'Fields')
        ) {
            $deviceType = Config::DEVICE_TRIMBLE;
        } elseif (
            is_dir($tmpPath . 'NavGuideGisImport')
            || is_dir($tmpPath . 'NavGuideExport')
            || is_dir($tmpPath . 'GIS')
            || is_dir($tmpPath . 'SHP')
        ) {
            $deviceType = Config::DEVICE_MUELLER;
        } elseif (count(glob($tmpPath . '*.shp')) > 0) {
            $deviceType = Config::DEVICE_TOPCON;
        }

        $LayersController = new LayersController();
        $LayersController->File->removeFolder($tmpPath);

        return $deviceType;
    }
}
