<?php

namespace TF\Engine\APIClasses\Uploads;

use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\Users\UsersController;

class ContractUpload extends UploadsTool
{
    public function getJsonContent()
    {
        $userid = (int) $_SESSION['user_id'];
        $groupid = (int) $_SESSION['user_id'];
        $contractid = (int) $_POST['contract_id'];
        $type = $_POST['type'];

        if ($_SESSION['user_id'] == $userid || $_SESSION['group_id'] == $groupid) {
            $UsersDbController = new UserDbController($_SESSION['database']);
            $UsersController = new UsersController();

            $fname = pathinfo($_FILES['file']['name']);

            // add in userFiles
            $options = [
                'tablename' => $UsersDbController->DbHandler->userFilesTable,
                'mainData' => [
                    'group_id' => $groupid,
                    'user_id' => $userid,
                    'date' => date('Y-m-d H:i:s'),
                    'filename' => $_FILES['file']['name'],
                ],
            ];

            if ($type && 'sales' == $type) {
                $options = [
                    'tablename' => $UsersDbController->DbHandler->salesContractsFilesTable,
                    'mainData' => [
                        'sales_contract_id' => $contractid,
                        'group_id' => $groupid,
                        'user_id' => $userid,
                        'date' => date('Y-m-d H:i:s'),
                        'filename' => $_FILES['file']['name'],
                    ],
                ];
            }

            $fileID = $UsersDbController->addItem($options);

            if ($type && 'sales' == $type) {
                @mkdir(SALES_CONTRACTS_PATH);
                @mkdir(SALES_CONTRACTS_PATH . $groupid);
                @mkdir(SALES_CONTRACTS_PATH . $groupid . '/' . $userid);

                copy($this->getFilePath(), SALES_CONTRACTS_PATH . $groupid . '/' . $userid . '/' . $fileID . '.' . $fname['extension']);

                $UsersController->groupLog($_SESSION['username'], $_SESSION['user_id'], $_SESSION['group_id'], 'Sales contracts', 'filesupload_contract_files', 'fileupload_contracts_files', $options, [$fileID], 'successfully uploaded');
            } else {
                // add contract_plot_relation
                $options = [
                    'tablename' => $UsersDbController->DbHandler->contractsFilesRelTable,
                    'mainData' => [
                        'contract_id' => $contractid,
                        'file_id' => $fileID,
                    ],
                    'id_name' => 'file_id',
                ];
                $UsersDbController->addItem($options);

                @mkdir(LAYERS_CONTRACTS_PATH . $groupid);
                @mkdir(LAYERS_CONTRACTS_PATH . $groupid . '/' . $userid);

                copy($this->getFilePath(), LAYERS_CONTRACTS_PATH . $groupid . '/' . $userid . '/' . $fileID . '.' . $fname['extension']);

                $UsersController->groupLog($_SESSION['username'], $_SESSION['user_id'], $_SESSION['group_id'], 'Contracts', 'filesupload_contract_files', 'fileupload_contracts_files', $options, [$fileID], 'successfully uploaded');
            }

            @unlink($this->getFilePath());
        }

        return $this->response(true);
    }
}
