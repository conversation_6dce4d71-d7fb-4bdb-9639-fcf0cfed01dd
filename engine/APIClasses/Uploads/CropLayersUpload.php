<?php

namespace TF\Engine\APIClasses\Uploads;

use Exception;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\Users\UsersController;

class CropLayersUpload extends UploadsTool
{
    public function getJsonContent()
    {
        if (isset($_POST['layer_id']) && (int) $_POST['layer_id']) {
            $UsersDbController = new UserDbController($_SESSION['database']);
            $UsersController = new UsersController();

            $id = $_POST['layer_id'];

            try {
                $nid = $UsersController->addCropLayerProcessing($_SESSION['user_id'], $_SESSION['database'], $_SESSION['group_id'], $id);
                copy($this->getFilePath(), LAYERS_QUEUE_PATH . 'crop_layer_' . $nid . '.csv');

                $UsersController->groupLog($_SESSION['username'], $_SESSION['user_id'], $_SESSION['group_id'], 'CropRotation', 'File uploaded.', DEFAULT_DB_PREFIX . 'users_croplayers', [$nid]);

                // update status when file is uploaded
                $options['mainData'] = ['status' => 0];
                $options['where'] = ['id' => $nid];
                $options['tablename'] = $UsersDbController->DbHandler->cropLayersTable;
                $UsersDbController->editItem($options);
            } catch (Exception $e) {
                $options['mainData'] = ['status' => 2];
                $options['where'] = ['id' => $id];
                $options['tablename'] = $UsersDbController->DbHandler->cropLayersTable;
                $UsersDbController->editItem($options);
            }
        }

        @unlink($this->getFilePath());

        return $this->response(true);
    }
}
