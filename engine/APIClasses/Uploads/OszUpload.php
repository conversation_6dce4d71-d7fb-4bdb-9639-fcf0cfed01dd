<?php

namespace TF\Engine\APIClasses\Uploads;

use Com;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

class OszUpload extends UploadsTool
{
    public function getJsonContent()
    {
        $land = $_POST['land'];
        $ekatte = $_POST['ekatte'];

        $UsersDbController = new UserDbController($_SESSION['database']);

        $options = [
            'tablename' => $UsersDbController->DbHandler->tableOSZFiles,
            'mainData' => [
                'land' => $land,
                'ekatte' => $ekatte,
            ],
        ];
        $fileID = $UsersDbController->addItem($options);

        $database = $_SESSION['database'];

        if (0 == strncasecmp(PHP_OS, 'WIN', 3)) {
            $com = new Com('WScript.shell');
            $com->run('php ' . SITE_PATH . "crons/load_osz_plots.php {$database} {$fileID} {$this->getFilePath()}", 3, false);
        } else {
            exec('php ' . SITE_PATH . "crons/load_osz_plots.php {$database} {$fileID} {$this->getFilePath()} > " . SITE_PATH . 'logs/load_osz_plots.log 2>&1 &', $output, $code);
        }

        return $this->response(true);
    }
}
