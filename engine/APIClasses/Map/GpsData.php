<?php

namespace TF\Engine\APIClasses\Map;

use Prado\Web\Services\TJsonRpcProtocol;
use Prado\Web\Services\TRpcApiProvider;
use Prado\Web\Services\TRpcServer;
use TF\Application\Common\Config;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

/**
 * Извличане на данните то Layer_gps за Technofarm Mobile.
 *
 * @rpc-module Map
 *
 * @rpc-service-id gps-data
 */
class GpsData extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'getAllGpsData' => ['method' => [$this, 'getAllGpsData'],
                'validators' => [
                    'plotData' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * @api-method getAllGpsData
     *
     * @param string $page
     * @param string $rows
     * @param ?string $sort
     * @param ?string $order
     *
     * @return array [string geoJSONFeatures]
     */
    public function getAllGpsData(array $plotData, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $UserDbController = new UserDbController($this->User->Database);
        $server = new TRpcServer(new TJsonRpcProtocol());
        $LayerZpData = new LayerZpData($server);

        if (!$plotData) {
            $plotData = [];
        }

        if ($this->User->HasMapRightsRW) {
            // If there are plots for sync, insert into layer_gps
            foreach ($plotData as $plotId => $plot) {
                $plot_info = '' != $plot['properties']['plot_info'] ? $plot['properties']['plot_info'] : null;
                $plot_name = $plot['properties']['plot_name'];
                $geom = json_encode($plot['geometry']);

                $data = [
                    'geom' => $geom,
                    'plot_name' => $plot_name,
                    'plot_info' => $plot_info,
                ];

                $UserDbController->addPlotToLayerGps($data);
            }
            $this->updateExtentAndMapfile();
        }

        // get all features as geoJson format
        $geoJSONFeatures = $UserDbController->getGeoJsonFeatures($rows, $sort, $order);

        // get all zp layers
        $zpLayers = $LayerZpData->getAllZpLayersData();

        return [$geoJSONFeatures, $zpLayers];
    }

    private function updateExtentAndMapfile()
    {
        $LayersController = new LayersController('Layers');
        $options = [];
        $options['group_id'] = $this->User->groupID;
        $options['layer_type'] = Config::LAYER_TYPE_GPS;
        $layerID = $LayersController->getLayersIdByLayerType($options);

        $server = new TRpcServer(new TJsonRpcProtocol());
        $mapTools = new MapTools($server);
        $mapTools->commonUpdateLayerProjectionAndMapFile('layer_gps', $layerID);
    }
}
