<?php

namespace TF\Engine\APIClasses\Map;

use Exception;
use Prado\Web\Services\TJsonRpcProtocol;
use Prado\Web\Services\TRpcApiProvider;
use Prado\Web\Services\TRpcServer;
use TF\Application\Common\Config;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

/**
 * @rpc-module Map
 *
 * @rpc-service-id layer-surveys
 */
class LayerSurveys extends TRpcApiProvider
{
    public const PLOT_STATE_SYNCED = 'synced';
    public const PLOT_STATE_NOT_SYNCED = 'not_synced';
    public const PLOT_STATE_EDITED = 'edited';
    public const PLOT_STATE_DELETED = 'deleted';

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'sync' => [
                'method' => [$this, 'sync'],
                'validators' => [
                    'plots' => [
                        'validateComplexArray|plots' => [
                            'gid' => 'validateNumber',
                            'geom' => 'validateRequired, validateNotNull',
                            'state' => 'validateRequired,validateText',
                        ],
                    ],
                ],
            ],
        ];
    }

    public function sync(array $rpcParams = [])
    {
        $plots = $rpcParams['plots'] ?? [];
        $userDbController = new UserDbController($this->User->database);
        $tableLayerGps = $userDbController->DbHandler->tableLayerGps;
        $identifierColumn = 'gid';
        $geoJsonDefaultProj = 4326;

        if (!$this->User->HasMapRightsRW) {
            throw new MTRpcException('NO_RIGHTS', 499);
        }

        $transaction = $userDbController->startTransaction();

        try {
            foreach ($plots as $plot) {
                $plotFeature = $plot['geom'];
                $plotInfo = '' != $plotFeature['properties']['plot_info'] ? $plotFeature['properties']['plot_info'] : null;
                $plotName = $plotFeature['properties']['plot_name'];
                $plotGeom = json_encode($plotFeature['geometry']);

                if (self::PLOT_STATE_NOT_SYNCED === $plot['state']) {
                    $plotToCreate = [
                        'geom' => $plotGeom,
                        'plot_name' => $plotName,
                        'plot_info' => $plotInfo,
                    ];

                    $userDbController->addPlotToLayerGps($plotToCreate, $geoJsonDefaultProj);

                    continue;
                }

                if (self::PLOT_STATE_EDITED === $plot['state']) {
                    $userDbController->updateGeometryWithInfoForGpsFromGeoJson($tableLayerGps, $plotGeom, $plot[$identifierColumn], $plotName, $plotInfo, $geoJsonDefaultProj);

                    continue;
                }

                if (self::PLOT_STATE_DELETED === $plot['state']) {
                    $userDbController->removeGeometry($tableLayerGps, $plot[$identifierColumn], $identifierColumn);

                    continue;
                }
            }

            $results = $userDbController->getGeoJSON([
                'tablename' => $tableLayerGps,
                'keep_multipolygons' => true,
                'return' => [
                    $identifierColumn,
                    'plot_info',
                    'plot_name',
                    'round(st_area(lg.geom)::numeric/1000, 3)::text as plot_area',
                ],
            ]);
            $features = json_decode($results['geoJSON'], true)['features'] ?? [];
            $response = array_map(function ($feature) use ($identifierColumn) {
                $feature['properties']['layer_type'] = Config::LAYER_TYPE_GPS;
                $gid = $feature['properties'][$identifierColumn];
                unset($feature['properties'][$identifierColumn]);

                return [
                    'gid' => $gid,
                    'geom' => $feature,
                    'state' => self::PLOT_STATE_SYNCED,
                ];
            }, $features);

            $transaction->commit();
        } catch (Exception $e) {
            $transaction->rollback();

            throw $e;
        }

        $this->updateExtentAndMapfile($tableLayerGps);

        return $response;
    }

    private function updateExtentAndMapfile(string $tableLayerGps)
    {
        $layersController = new LayersController('Layers');
        $options = [
            'group_id' => $this->User->groupID,
            'layer_type' => Config::LAYER_TYPE_GPS,
        ];
        $layerId = $layersController->getLayersIdByLayerType($options);

        $server = new TRpcServer(new TJsonRpcProtocol());
        $mapTools = new MapTools($server);
        $mapTools->commonUpdateLayerProjectionAndMapFile($tableLayerGps, $layerId);
    }
}
