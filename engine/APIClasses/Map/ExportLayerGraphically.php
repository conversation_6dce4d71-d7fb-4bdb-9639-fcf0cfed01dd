<?php

namespace TF\Engine\APIClasses\Map;

use Exception;
use labelObj;
use Prado\Web\Services\TRpcApiProvider;
use TF\Application\Common\Config;
use TF\Engine\APIClasses\Plots\PlotsTree;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Exports layer graphics in pdf file.
 *
 * @rpc-module Map
 *
 * @rpc-service-id export-layer-graphically
 */
class ExportLayerGraphically extends TRpcApiProvider
{
    public const MAP_FORMAT_A0 = 'A0';
    public const MAP_FORMAT_A1 = 'A1';
    public const MAP_FORMAT_A2 = 'A2';
    public const MAP_FORMAT_A3 = 'A3';
    public const MAP_FORMAT_A4 = 'A4';

    private $exportableLayerTypes = [
        Config::LAYER_TYPE_FOR_ISAK => 'gid',
        Config::LAYER_TYPE_ZP => 'id',
        Config::LAYER_TYPE_KMS => 'gid',
        Config::LAYER_TYPE_KVS => 'gid',
        Config::LAYER_TYPE_ISAK => 'gid',
        Config::LAYER_TYPE_VPS_ORLI_LESHOYADI => 'gid',
        Config::LAYER_TYPE_PERMANETELY_GREEN_AREAS => 'gid',
        Config::LAYER_TYPE_GPS => 'gid',
        Config::LAYER_TYPE_ALLOWABLE => 'gid',
        Config::LAYER_TYPE_ALLOWABLE_FINAL => 'gid',
        Config::LAYER_TYPE_LFA => 'gid',
        Config::LAYER_TYPE_NATURA_2000 => 'gid',
        Config::LAYER_TYPE_VPS_PASISHTA => 'gid',
        Config::LAYER_TYPE_VPS_GASKI_CHERVENOGUSHI => 'gid',
        Config::LAYER_TYPE_VPS_GASKI_ZIMNI => 'gid',
        Config::LAYER_TYPE_VPS_LIVADEN_BLATAR => 'gid',
        Config::LAYER_TYPE_WORK_LAYER => 'gid',
    ];

    /**
     * Registers the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'export' => ['method' => [$this, 'getExportLayersData']],
            'exportTopicLayer' => ['method' => [$this, 'exportTopicLayer']],
        ];
    }

    /**
     * Exports image of filtered plots.
     *
     * @api-method export
     *
     * @param array $data {
     *                    #item boolean checked
     *                    #item boolean selected
     *                    #item string domId
     *                    #item string iconCls
     *                    #item integer id
     *                    #item string state
     *                    #item string target
     *                    #item string text
     *                    #item array attributes {
     *                    #item string extent
     *                    #item string fill_color
     *                    #item boolean is_system
     *                    #item string layer_name
     *                    #item integer layer_type
     *                    #item integer level
     *                    #item string name
     *                    }
     *                    #item array selected_plots {
     *                    }
     *                    }
     * @param string $format
     * @param null|mixed $extent
     *
     * @return array {
     *               #item string pdf_blank_file
     *               }
     */
    public function getExportLayersData($data, $format, $extent = null)
    {
        list($width, $height, $legendLabelSize, $labelSize) = $this->getMapSizes($format);
        if ($extent) {
            list($minX, $minY, $maxX, $maxY) = explode(',', $extent);
        } else {
            list($minX, $minY, $maxX, $maxY) = $this->getMapExtent($data);
        }

        $map = ms_newmapobj(WMS_MAP_PATH . $this->User->GroupID . '.map');
        $map->setSize($width, $height);

        $this->setMapLayers($map, $data, $labelSize);

        $map->setExtent($minX, $minY, $maxX, $maxY);
        $map->scalebar->set('status', MS_EMBED);
        $map->legend->set('status', MS_EMBED);
        $map->legend->set('position', MS_LR);
        $map->legend->label->color->setRGB(0, 0, 0);
        $map->legend->label->set('encoding', 'UTF-8');
        $map->legend->label->set('font', 'arial');
        $map->legend->label->set('size', $legendLabelSize);

        $image = $map->draw();
        $unique = uniqid();
        $scale = round($map->scaledenom);

        $imgFileName = "{$unique}.png";
        $mapImage = WMS_IMAGE_PATH . "{$imgFileName}";

        $image->saveImage($mapImage, $map);
        $exportedFile = $this->exportMap($mapImage, $format, $scale);
        unlink($mapImage);

        return $exportedFile;
    }

    /**
     * Exports image of topic layer.
     *
     * @api-method exportTopicLayer
     *
     * @param array $data {
     *                    #item string layer_name
     *                    #item string ekate
     *                    #item string extent
     *                    #item string legend
     *                    }
     * @param string $format
     *
     * @return array {
     *               #item string pdf_blank_file
     *               }
     */
    public function exportTopicLayer($data, $format)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        $LayersController = new LayersController('Layers');
        $arrayHelper = $LayersController->ArrayHelper;
        $data = (object)$data;

        switch ($format) {
            case 'A0':
                $width = 4450;
                $height = 3050;
                $labelSize = 12;

                break;
            case 'A1':
                $width = 3120;
                $height = 2120;
                $labelSize = 12;

                break;
            case 'A2':
                $width = 2200;
                $height = 1460;
                $labelSize = 12;

                break;
            case 'A3':
                $width = 1550;
                $height = 1000;
                $labelSize = 10;

                break;
            default:
                $format = 'A4';
                $width = 1080;
                $height = 660;
                $labelSize = 7;

                break;
        }

        if (!isset($data->extent)) {
            $options = [
                'return' => ['st_extent(geom) as extent'],
                'tablename' => $data->layer_name . '_mat_view',
                'where' => [
                    'ekate' => ['column' => 'ekate', 'compare' => '=', 'value' => $data->ekate],
                ],
            ];

            $result = $UserDbController->getItemsByParams($options, false, false);
            $defaultExtent = str_replace('BOX(', '', $result[0]['extent']);
            $defaultExtent = str_replace(')', '', $defaultExtent);
            $defaultExtent = str_replace(' ', ',', $defaultExtent);
            $defaultExtent = explode(',', $defaultExtent);

            $extent = $defaultExtent;
        } else {
            $extent = explode(',', $data->extent);
        }

        $text = '
        <div style="text-align: center;font-size: 16px;padding-bottom: 5px;"><b>Легенда</b></div>
        <table style="font-size: 14px; vertical-align: middle; margin: auto;" cellspacing=0>
            <tr style="border-bottom: 1px solid black;">
                <th style="border-bottom: 1px solid black; text-align: center;padding-bottom: 5px;">Цвят</th>
                <th style="border-bottom: 1px solid black; text-align: center"> Име субект</th>
            </tr>';
        $legendCount = count($data->legend);
        for ($i = 0; $i < $legendCount; $i++) {
            if ($i != $legendCount - 1) {
                $text .= '
                <tr style="text-align: center;page-break-before: always">
                    <td style="border-bottom: 1px solid black;width: 100px;"><div style="width: 15px; height: 15px;color: ' . $data->legend[$i]['color'] . '; background-color: ' . $data->legend[$i]['color'] . '"></div></td>
                    <td style="border-bottom: 1px solid black;width: 350px;padding: 5px;">' . $data->legend[$i]['ime_subekt'] . '</td>
                </tr>';
            } else {
                $text .= '
                <tr style="text-align: center;page-break-before: always">
                    <td style="width: 100px;"><div style="width: 15px; height: 15px;color: ' . $data->legend[$i]['color'] . '; background-color: ' . $data->legend[$i]['color'] . '"></div></td>
                    <td style="width: 350px;padding: 5px;">' . $data->legend[$i]['ime_subekt'] . '</td>
                </tr>';
            }
        }
        $text .= '</table>';

        $legend = '<page format="A4" orientation="P">
        ' . $text . '
        </page>';

        $map = ms_newmapobj(WMS_MAP_PATH . $this->User->GroupID . '.map');

        $map->setExtent((float)$extent[0], (float)$extent[1], (float)$extent[2], (float)$extent[3]);
        $map->setSize($width, $height);

        // Прочита се необходимата информация от map file на потребителя
        $thematicMapLayer = ms_newlayerobj($map, $map->getLayerByName($data->layer_name));
        $thematicMapLayer->set('status', MS_DEFAULT);

        $underlayerKvs = ms_newlayerobj($map, $map->getLayerByName($data->layer_name . '_outlines'));

        $underlayerKvs->set('status', MS_DEFAULT);
        $underlayerKvs->queryByRect($map->extent);

        if ($map->scaledenom <= 12500) {
            $underlayerKvs->set('status', MS_ON);
        }
        $scale = round($map->scaledenom);

        $map->scalebar->set('units', MS_METERS);
        $map->scalebar->outlinecolor->setRGB(0, 0, 0);
        $map->scalebar->set('status', MS_EMBED);

        $image = $map->draw();
        $image->set('imagepath', WMS_IMAGE_PATH);
        $url = $image->saveWebImage();

        $tmp = explode('/', $url);
        $img = '<img style="width:100%;" src="' . WMS_IMAGE_PATH . $tmp[2] . '" />';

        $ltext = '<page style="font-family: freeserif;" format="' . $format . '" orientation="L">
            <div style="text-align: center;"></div>
            <div style="border: 2px solid #000;">' . $img . '</div><br />
                <b>Мащаб 1:' . $scale . '</b>
                    </page>' . $legend;
        $pdfPath = 'files/uploads/export/' . $this->User->GroupID . 'map.pdf';

        $LayersController->HtmlToPdf->writeHTML($ltext);
        $LayersController->HtmlToPdf->Output($pdfPath, 'F');

        $return['pdf_blank_file'] = $pdfPath;

        return $return;
    }

    /**
     * Exports map filee to pdf.
     *
     * @param [type] $mapImage
     * @param [type] $format
     * @param [type] $scale
     */
    private function exportMap($mapImage, $format, $scale): iterable
    {
        $LayersController = new LayersController();

        $img = '<img style="width:100%;" src="' . $mapImage . '" />';
        $ltext = '<page style="font-family: freeserif;" format="' . $format . '" orientation="L">
            <div style="text-align: center;"></div>
            <div style="border: 2px solid #000;">' . $img . '</div><br />
                <b>Мащаб 1:' . $scale . '</b>
					</page>';
        $antiCache = time();
        $pdfPath = "files/uploads/export/{$this->User->GroupID}_map_{$antiCache}.pdf";

        $LayersController->HtmlToPdf->writeHTML($ltext);
        $LayersController->HtmlToPdf->Output($pdfPath, 'F');

        $ret['pdf_blank_file'] = $pdfPath;

        return $ret;
    }

    /**
     * Calc map sizes and props related to format.
     */
    private function getMapSizes(string $format): iterable
    {
        switch ($format) {
            case self::MAP_FORMAT_A0:
                $width = 4450;
                $height = 3050;
                $legendLabelSize = 21;
                $labelSize = 7;

                break;
            case self::MAP_FORMAT_A1:
                $width = 3120;
                $height = 2120;
                $legendLabelSize = 19;
                $labelSize = 6;

                break;
            case self::MAP_FORMAT_A2:
                $width = 2200;
                $height = 1460;
                $legendLabelSize = 16;
                $labelSize = 5;

                break;
            case self::MAP_FORMAT_A3:
                $width = 1550;
                $height = 1000;
                $legendLabelSize = 9;
                $labelSize = 6;

                break;
            case self::MAP_FORMAT_A4:
                $width = 1080;
                $height = 660;
                $legendLabelSize = 8;
                $labelSize = 5;

                break;
            default:
                throw new Exception('Unsuported map print format');
        }

        return [$width, $height, $legendLabelSize, $labelSize];
    }

    /**
     * Calc map extent.
     */
    private function getMapExtent(array $data): iterable
    {
        $UserDbController = new UserDbController($this->User->Database);
        $LayersController = new LayersController('Layers');
        $arrayHelper = $LayersController->ArrayHelper;

        $queryParams = [];
        $remoteQueryParams = [];
        $wholeLayerExtents = [];

        foreach ($data as &$layer) {
            $layerName = $layer['attributes']['layer_name'];
            if (!empty($layer['attributes']['parent_layer_name'])) {
                $layerName = $layer['attributes']['parent_layer_name'];
            }
            if (true == $layer['selected']) {
                if (empty($layer['selected_plots']) && isset($layer['id'])) {
                    $plotsTree = new PlotsTree($this->rpcServer);

                    $params = ['ekate' => [$layer['id']], 'filter_action' => 'exec_filter'];
                    $plots = $plotsTree->getPlots($params);
                    $layer['selected_plots'] = $plots['info']['filteredGids'];
                }
                $_SESSION['filtered_plots'][$layerName] = $layer['selected_plots'];
            }

            $filtered_ids = [];

            if (isset($_SESSION['filtered_plots'][$layerName])) {
                $filtered_ids = $_SESSION['filtered_plots'][$layerName];
                if (true == $layer['attributes']['is_system'] && Config::LAYER_TYPE_KVS != $layer['attributes']['layer_type'] && Config::LAYER_TYPE_GPS != $layer['attributes']['layer_type'] && Config::LAYER_TYPE_WORK_LAYER != $layer['attributes']['layer_type']) {
                    $remoteQueryParams[] = [
                        'layer_name' => $layerName,
                        'layer_type' => $layer['attributes']['layer_type'],
                        'filtered_plots' => implode(',', $arrayHelper->filterEmptyStringArr($filtered_ids)),
                        'id' => $this->exportableLayerTypes[$layer['attributes']['layer_type']],
                    ];
                    $lastParam = end($remoteQueryParams);
                    if (is_array($lastParam['filtered_plots']) && empty($lastParam['filtered_plots'])) {
                        unset($remoteQueryParams[key($remoteQueryParams)]['filtered_plots']);
                    }
                } else {
                    $queryParams[] = [
                        'layer_name' => $layerName,
                        'layer_type' => $layer['attributes']['layer_type'],
                        'filtered_plots' => implode(',', $arrayHelper->filterEmptyStringArr($filtered_ids)),
                        'id' => $this->exportableLayerTypes[$layer['attributes']['layer_type']],
                    ];

                    $lastParam = end($queryParams);

                    if (is_array($lastParam['filtered_plots']) && empty($lastParam['filtered_plots'])) {
                        unset($queryParams[key($queryParams)]['filtered_plots']);
                    }
                }
            } else {
                if (false == $layer['attributes']['is_system'] || Config::LAYER_TYPE_KVS == $layer['attributes']['layer_type'] || Config::LAYER_TYPE_GPS == $layer['attributes']['layer_type'] || Config::LAYER_TYPE_WORK_LAYER == $layer['attributes']['layer_type']) {
                    $tableExists = $UserDbController->getTableNameExist($layerName);
                    if (!$tableExists) {
                        continue;
                    }
                }

                $layer['attributes']['extent'] = str_replace(' ', '', $layer['attributes']['extent']);
                $wholeLayerExtents[$layer['id']] = explode(',', $layer['attributes']['extent']);
            }
        }

        if (!empty($queryParams)) {
            $extent = $UserDbController->getExtentOfMultipleLayers($queryParams, false, false);
            $maxExtent = $extent[0]['st_extent'];
            $maxExtent = str_replace('BOX(', '', $maxExtent);
            $maxExtent = str_replace(')', '', $maxExtent);
            $maxExtent = str_replace(',', ' ', $maxExtent);

            $extent = explode(' ', $maxExtent);
        }

        if (!empty($remoteQueryParams)) {
            $remoteExtent = $UserDbController->getExtentOfRemoteMultipleLayers($remoteQueryParams, false, false);
            $maxExtent = $remoteExtent[0]['st_extent'];
            $maxExtent = str_replace('BOX(', '', $maxExtent);
            $maxExtent = str_replace(')', '', $maxExtent);
            $maxExtent = str_replace(',', ' ', $maxExtent);

            $remoteExtent = explode(' ', $maxExtent);
        }

        if (!empty($remoteExtent)) {
            $extent[0] = min($extent[0], $remoteExtent[0]);
            $extent[1] = min($extent[1], $remoteExtent[1]);
            $extent[2] = max($extent[2], $remoteExtent[2]);
            $extent[3] = max($extent[3], $remoteExtent[3]);
        }
        if (!empty($wholeLayerExtents)) {
            foreach ($wholeLayerExtents as $layer_extents) {
                if (null == $extent) {
                    $extent = $layer_extents;
                } else {
                    $extent[0] = min($extent[0], $layer_extents[0]);
                    $extent[1] = min($extent[1], $layer_extents[1]);
                    $extent[2] = max($extent[2], $layer_extents[2]);
                    $extent[3] = max($extent[3], $layer_extents[3]);
                }
            }
        }

        if (null == $extent) {
            $data[0]['attributes']['extent'] = str_replace(' ', '', $data[0]['attributes']['extent']);
            $extent = explode(',', $data[0]['attributes']['extent']);
        }

        return $extent;
    }

    /**
     * Sets map layers to map object.
     *
     * @param Map $map
     */
    private function setMapLayers(&$map, array $data, int $labelSize): void
    {
        $UserDbController = new UserDbController($this->User->Database);
        $LayersController = new LayersController('Layers');
        $arrayHelper = $LayersController->ArrayHelper;

        $data = $this->orderMapLayes($data, 'z_index', SORT_DESC);

        $layerDistribution = [];
        $class = [];
        $style = [];
        $dataCount = count($data);
        $kvsClassIndent = 0;
        for ($i = 0; $i < $dataCount; $i++) {
            $layerId = $data[$i]['id'];
            if (!empty($data[$i]['parent_id'])) {
                $layerId = $data[$i]['parent_id'];
            }
            $layerData = $LayersController->getLayerData($layerId);

            $dataLayerName = $data[$i]['attributes']['layer_name'];
            if (!empty($data[$i]['attributes']['parent_layer_name'])) {
                $dataLayerName = $data[$i]['attributes']['parent_layer_name'];
            }

            if (true == $data[$i]['attributes']['is_system']
                && Config::LAYER_TYPE_KVS != $data[$i]['attributes']['layer_type']
                && Config::LAYER_TYPE_GPS != $data[$i]['attributes']['layer_type']
                && Config::LAYER_TYPE_WORK_LAYER != $data[$i]['attributes']['layer_type']) {
                $tableExists = true;
            } else {
                $tableExists = $UserDbController->getTableNameExist($dataLayerName);
                if (!$tableExists) {
                    continue;
                }
            }

            $jsonStyle = $this->updateWithDynamicStyles($layerData, $data[$i]);

            if (Config::LAYER_TYPE_ISAK == $layerData['layer_type']) {
                $tagLabel = 'label';
                $remoteJoin = '';
                $label = [];
                if (empty($jsonStyle->label_name)) {
                    $label[] = "' '";
                }
                foreach ($jsonStyle->label_name as $label_key => $labelValue) {
                    switch ($labelValue) {
                        case 'area':
                            $label[] = 'round(area::numeric, 4)';

                            break;
                        case 'culture':
                            $label[] = "(case when cropname is null then '' else cropname end)";

                            break;
                        case 'watering':
                            $label[] = "(CASE WHEN watering = true THEN 'Да' ELSE 'Не' END)";

                            break;
                        case null:
                            $label[] = "(case when prc_uin is null then '' else prc_uin end)";

                            break;
                        default:
                            $label[] = '(case when ' . $labelValue . " is null then '' else " . $labelValue . ' end)';

                            break;
                    }
                }
                $label = implode(' || \'' . $this->wrapCharacter . '\' || ', $label);
                $labelQuery = "({$label}) as label";
            } elseif (Config::LAYER_TYPE_FOR_ISAK == $layerData['layer_type']) {
                $tagLabel = 'label';

                $label = [];
                if (empty($jsonStyle->label_name)) {
                    $label[] = "' '";
                }
                foreach ($jsonStyle->label_name as $label_key => $labelValue) {
                    switch ($labelValue) {
                        case 'land':
                            $label[] = "(case when ekatte_name is null then '' else ekatte_name end)";

                            break;
                        case 'schema':
                            $label[] = "left((case WHEN pndp = true THEN 'ПНДП, ' ELSE '' END)||
                            (case WHEN sepp = true THEN 'СЕПП,' ELSE '' END) ||
                            (case WHEN zdp = true THEN 'ЗДП,' ELSE '' END) ||
                            (case WHEN nr1 = true THEN 'НР 1,' ELSE '' END) ||
                            (case WHEN nr2 = true THEN 'НР 2,' ELSE '' END), -1)";

                            break;
                        case 'area':
                            $label[] = 'round((ST_Area(geom) / 10000)::numeric, 4)';

                            break;
                        case 'edited':
                            $label[] = "(case WHEN edited = true THEN 'Да' ELSE 'Не' END)";

                            break;
                        case null:
                            $label[] = "(CASE WHEN char_length(prc_name) > 0 OR char_length(prc_name) IS NOT NULL THEN prc_name || ' - ' ELSE '' END) || round(ST_Area(geom)::numeric/10000, 4) || ' ха'";

                            break;
                        default:
                            $label[] = '(case when ' . $labelValue . " is null then '' else " . $labelValue . ' end)';

                            break;
                    }
                }

                $label = implode(' || \'' . $this->wrapCharacter . '\' || ', $label);
                $labelQuery = "({$label}) as label";
                $remoteJoin = " LEFT JOIN dblink (
                    'host=" . DEFAULT_DB_HOST . ' port=' . DEFAULT_DB_PORT . ' dbname=' . DEFAULT_DB_DATABASE . ' user=' . DEFAULT_DB_USERNAME . ' password=' . DEFAULT_DB_PASSWORD . "',
                    'SELECT ekatte_code, ekatte_name FROM su_ekatte WHERE true'
                    ) AS e (ekatte_code varchar, ekatte_name varchar) ON(e.ekatte_code = ekatte) where true";
            } elseif (Config::LAYER_TYPE_KVS == $layerData['layer_type']) {
                $tagLabel = 'label';
                foreach ($jsonStyle as $ekatte => $value) {
                    $ekatteLabel = [];
                    if (empty($value->label_name)) {
                        $ekatteLabel[] = "' '";
                    }
                    foreach ($value->label_name as $label_key => $labelValue) {
                        switch ($labelValue) {
                            case 'area_kvs':
                                $labelQuery = ' round((ST_Area(geom)/1000)::numeric, 3)';

                                break;
                            case 'used_area':
                                $labelQuery = " COALESCE(round(used_area::NUMERIC, 3)::text, '')";

                                break;
                            case 'area_type':
                                $labelQuery = "(case when title is null then '' else title end)";

                                break;
                            case 'document_area':
                                $labelQuery = ' (CASE WHEN document_area IS NULL THEN round((ST_Area(geom)/1000)::numeric, 3) ELSE round(document_area::numeric, 3) END)';

                                break;
                            case 'irrigated_area':
                                $labelQuery = ' (CASE WHEN irrigated_area = TRUE THEN document_area ELSE 0 END)';

                                break;
                            case 'masiv_imot':
                                $labelQuery = " (masiv || '.' || number) ";

                                break;
                            case null:
                                $labelQuery = ' kad_ident';

                                break;
                            default:
                                $labelQuery = '(case when ' . $labelValue . " is null then '' else " . $labelValue . ' end)';

                                break;
                        }
                        $ekatteLabel[] = $labelQuery;
                    }

                    $kvsLabels[$ekatte] = implode(' || \'' . $this->wrapCharacter . '\' || ', $ekatteLabel);
                    $kvsLabels[$ekatte] = '(' . $kvsLabels[$ekatte] . ')';
                    $kvsLabels[$ekatte] .= " as label_{$ekatte}";

                    $arrClass[$kvsClassIndent]['name'] = 'ЕКАТТЕ ' . $ekatte;
                    $arrClass[$kvsClassIndent]['expression'] = '\'' . $ekatte . '\'';
                    $arrClass[$kvsClassIndent]['color'] = $value->color;
                    $arrClass[$kvsClassIndent]['border_color'] = $value->border_color;
                    $arrClass[$kvsClassIndent]['tags'] = $value->tags;
                    $arrClass[$kvsClassIndent]['size'] = $labelSize;
                    $arrClass[$kvsClassIndent]['transparency'] = $value->transparency;
                    $arrClass[$kvsClassIndent]['label_text'] = '\'' . "[label_{$ekatte}]" . '\'';
                    $arrClass[$kvsClassIndent]['wrap_character'] = $this->wrapCharacter;
                    $arrClass[$kvsClassIndent]['border_only'] = $value->border_only;

                    if (true == $value->border_only) {
                        $arrClass[$kvsClassIndent]['transparency'] = 100;
                    }
                    $kvsClassIndent++;
                }
                $class_labels = implode(', ', $kvsLabels);
                $labelQuery = 'ekate, masiv, kad_ident as ' . $tagLabel;
                $labelQuery = $labelQuery . ', ' . $class_labels;
                $remoteJoin = " LEFT JOIN dblink (
                    'host=" . DEFAULT_DB_HOST . ' port=' . DEFAULT_DB_PORT . ' dbname=' . DEFAULT_DB_DATABASE . ' user=' . DEFAULT_DB_USERNAME . ' password=' . DEFAULT_DB_PASSWORD . "',
                    'SELECT id, title FROM su_area_types WHERE true'
                    ) AS e (area_type_id varchar, title varchar) ON(area_type_id = area_type) WHERE TRUE";
                $tagLabel = "label_{$ekatte}";
            } elseif (Config::LAYER_TYPE_KMS == $layerData['layer_type']) {
                $tagLabel = 'composed_label';
                $labelQuery = "CASE WHEN ekatte IS NULL THEN (E'-'||name||E'/'|| trunc(cast((st_area(geom)/1000) as numeric),3)||E' дка') ELSE
                         (ekatte||E'-'||name||E'/'|| trunc(cast((st_area(geom)/1000) as numeric),3)||E' дка') END as composed_label";

                $label = [];
                if (empty($jsonStyle->label_name)) {
                    $label[] = "' '";
                }
                foreach ($jsonStyle->label_name as $label_key => $labelValue) {
                    switch ($labelValue) {
                        case 'area':
                            $label[] = 'round((st_area(geom)/1000)::numeric, 3)';

                            break;
                        case 'crop_code':
                            $label[] = "(case when virtual_crop_name is null then '' else virtual_crop_name end)";

                            break;
                        case 'ekatte_code':
                            $label[] = "(case when ekatte_name is null then '' else ekatte_name end)";

                            break;
                        case null:
                            $label[] = "CASE WHEN ekatte IS NULL THEN (E'-'||name||E'/'|| trunc(cast((st_area(geom)/1000) as numeric),3)||E' дка') ELSE
                            (ekatte||E'-'||name||E'/'|| trunc(cast((st_area(geom)/1000) as numeric),3)||E' дка') END";

                            break;
                        default:
                            $label[] = '(case when ' . $labelValue . " is null then '' else " . $labelValue . ' end)';

                            break;
                    }
                }
                $labelQuery = implode(' || \'' . $this->wrapCharacter . '\' || ', $label);
                $labelQuery = '(' . $labelQuery . ') as composed_label ';
                $remoteJoin = " LEFT JOIN dblink (
                    'host=" . DEFAULT_DB_HOST . ' port=' . DEFAULT_DB_PORT . ' dbname=' . DEFAULT_DB_DATABASE . ' user=' . DEFAULT_DB_USERNAME . ' password=' . DEFAULT_DB_PASSWORD . "',
                    'SELECT ekatte_code, ekatte_name FROM su_ekatte WHERE true'
                    ) AS e (ekatte_code varchar, ekatte_name varchar) ON(e.ekatte_code = ekatte) where true";
            } elseif (Config::LAYER_TYPE_WORK_LAYER == $layerData['layer_type']) {
                $tagLabel = 'composed_label';
                $label = [];
                if (empty($jsonStyle->label_name)) {
                    $label[] = "' '";
                }
                foreach ($jsonStyle->label_name as $label_key => $labelValue) {
                    switch ($labelValue) {
                        case 'area':
                            $label[] = "round((st_area(geom)/10000)::numeric, 4) || ' ха'";

                            break;
                        default:
                            $label[] = '(case when ' . $labelValue . " is null then '' else " . $labelValue . ' end)';

                            break;
                    }
                }
                $labelQuery = implode(' || \'' . $this->wrapCharacter . '\' || ', $label);
                $labelQuery = '(' . $labelQuery . ') as composed_label ';
                $remoteJoin = '';
            } elseif (Config::LAYER_TYPE_NATURA_2000 == $layerData['layer_type']) {
                $tagLabel = 'label';
                $labelQuery = "name_bg ||'('||sitecode||')' as label";
                $remoteJoin = '';
            } elseif (Config::LAYER_TYPE_PERMANETELY_GREEN_AREAS == $layerData['layer_type']) {
                $tagLabel = 'label';
                $labelQuery = 'imotcode as label';
                $remoteJoin = '';
            } elseif (Config::LAYER_TYPE_LFA == $layerData['layer_type']) {
                $tagLabel = 'label';
                $labelQuery = "nm_lfa_e_1 ||'-'|| Nm_LFA_EKA ||'-'||'HP'||Nm_LFA_LFA as label";
                $remoteJoin = '';
            } elseif (Config::LAYER_TYPE_VPS_GASKI_CHERVENOGUSHI == $layerData['layer_type']) {
                $tagLabel = 'label';
                $labelQuery = 'name as label';
                $remoteJoin = '';
            } elseif (Config::LAYER_TYPE_VPS_GASKI_ZIMNI == $layerData['layer_type']) {
                $tagLabel = 'label';
                $labelQuery = 'name as label';
                $remoteJoin = '';
            } elseif (Config::LAYER_TYPE_VPS_LIVADEN_BLATAR == $layerData['layer_type']) {
                $tagLabel = 'label';
                $labelQuery = 'ime as label';
                $remoteJoin = '';
            } elseif (Config::LAYER_TYPE_VPS_ORLI_LESHOYADI == $layerData['layer_type']) {
                $tagLabel = 'label';
                $labelQuery = "ekatte ||'-'|| blockuin as label";
                $remoteJoin = '';
            } elseif (Config::LAYER_TYPE_ZP == $layerData['layer_type']) {
                $tagLabel = 'composed_label';
                $remoteJoin = '';
                $label = [];
                if (empty($jsonStyle->label_name)) {
                    $label[] = "' '";
                }
                foreach ($jsonStyle->label_name as $label_key => $labelValue) {
                    switch ($labelValue) {
                        case 'area_zp':
                            $label[] = 'round((st_area(geom)/1000)::numeric, 3)';

                            break;
                        case 'culture':
                            $label[] = "(case when crop_name is null then '' else crop_name end)";

                            break;
                        case null:
                            $label[] = "CASE WHEN c.crop_name IS NOT NULL THEN (c.crop_name || E' - ' || trunc(cast((st_area(geom)/1000) as numeric),3)||E' дка') ELSE
                                (trunc(cast((st_area(geom)/1000) as numeric),3)||E' дка') END";

                            break;
                        default:
                            $label[] = "(case when {$dataLayerName}.{$labelValue} is null then '' else {$dataLayerName}.{$labelValue} end)";

                            break;
                    }
                }

                $label = implode(' || \'' . $this->wrapCharacter . '\' || ', $label);
                $labelQuery = '(' . $label . ') as composed_label ';

                $secondLabelQuery = " LEFT JOIN dblink (
                            'host=" . DEFAULT_DB_HOST . ' port=' . DEFAULT_DB_PORT . ' dbname=' . DEFAULT_DB_DATABASE . ' user=' . DEFAULT_DB_USERNAME . ' password=' . DEFAULT_DB_PASSWORD . "',
                            'SELECT crop_code, crop_name FROM su_crop_codes WHERE true'
                        ) AS c (crop_code varchar, crop_name varchar) ON(c.crop_code = " . $dataLayerName . '.culture)  ';
            } elseif (Config::LAYER_TYPE_GPS == $layerData['layer_type']) {
                $tagLabel = 'label';

                $label = [];
                if (empty($jsonStyle->label_name)) {
                    $label[] = "' '";
                }
                foreach ($jsonStyle->label_name as $label_key => $labelValue) {
                    switch ($labelValue) {
                        case 'area':
                            $label[] = 'round((ST_Area(geom) / 10000)::numeric, 4)';

                            break;
                        case null:
                            $label[] = "(CASE WHEN char_length(plot_name) > 0 OR char_length(plot_name) IS NOT NULL THEN plot_name || ' - ' ELSE '' END) || round(ST_Area(geom)::numeric/10000, 4) || ' ха'";

                            break;
                        default:
                            $label[] = $labelValue;

                            break;
                    }
                }
                $label = implode(' || \'' . $this->wrapCharacter . '\' || ', $label);
                $labelQuery = '(' . $label . ') as label ';
                $remoteJoin = '';
            } else {
                $tagLabel = 'gid';
                $labelQuery = '';
                $remoteJoin = '';
            }

            $options = [
                'tablename' => $dataLayerName,
                'return' => [
                    'geom',
                    $this->exportableLayerTypes[$data[$i]['attributes']['layer_type']] . ' AS gid',
                    $labelQuery,
                ],
                'where' => [
                    'filtered_plots' => ['column' => $this->exportableLayerTypes[$data[$i]['attributes']['layer_type']], 'compare' => 'IN', 'value' => $arrayHelper->filterEmptyStringArr($_SESSION['filtered_plots'][$dataLayerName])],
                ],
            ];

            $masivOptions = [
                'tablename' => $dataLayerName,
                'return' => [
                    'st_centroid(st_union(geom)) as geom',
                    'masiv',
                ],
                'where' => [
                    'filtered_plots' => ['column' => $this->exportableLayerTypes[$data[$i]['attributes']['layer_type']], 'compare' => 'IN', 'value' => $arrayHelper->filterEmptyStringArr($_SESSION['filtered_plots'][$dataLayerName])],
                    'masiv' => ['column' => 'masiv', 'compare' => '!=', 'value' => '0'],
                ],
                'group' => 'masiv',
            ];
            if (empty($_SESSION['filtered_plots'][$dataLayerName])) {
                unset($options['where']);
            }
            if (true == $data[$i]['attributes']['is_system'] && Config::LAYER_TYPE_KVS != $data[$i]['attributes']['layer_type'] && Config::LAYER_TYPE_GPS != $data[$i]['attributes']['layer_type'] && Config::LAYER_TYPE_WORK_LAYER != $data[$i]['attributes']['layer_type']) {
                $layerQuery = $LayersController->getRemoteLayerData($options, false, true);
                $connection = 'host=' . DBLINK_HOST . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD . ' port=' . DBLINK_PORT;
            } else {
                if (Config::LAYER_TYPE_KVS == $data[$i]['attributes']['layer_type']) {
                    $masivLayerQuery = $UserDbController->getItemsByParams($masivOptions, false, true);

                    $legendOptions = $options;
                    $legendOptions['return'] = ['array_agg(distinct(ekate)) as printed_ekates'];
                    $legendEkates = $UserDbController->getItemsByParams($legendOptions, false, false);
                    $legendEkates = explode(',', trim($legendEkates[0]['printed_ekates'], '{}'));
                }
                $layerQuery = $UserDbController->getItemsByParams($options, false, true);
                $connection = 'host=' . DEFAULT_DB_HOST . ' dbname=' . $this->User->Database . ' user=' . DEFAULT_DB_USERNAME . ' password=' . DEFAULT_DB_PASSWORD . ' port=' . DEFAULT_DB_PORT;
            }

            if (Config::LAYER_TYPE_ZP == $layerData['layer_type']) {
                $layer_query_array = explode('FROM ' . $dataLayerName, $layerQuery);

                $layerQuery = $layer_query_array[0] . 'FROM ' . $dataLayerName . $secondLabelQuery . $layer_query_array[1];
            }

            if ('' != $remoteJoin) {
                $layerQuery = str_replace('WHERE true', $remoteJoin, $layerQuery);
            }

            $layerDistribution[$i] = ms_newlayerobj($map);
            $layerDistribution[$i]->setConnectionType(MS_POSTGIS);
            $layerDistribution[$i]->set('name', $dataLayerName);
            $layerDistribution[$i]->set('type', MS_LAYER_POLYGON);
            $layerDistribution[$i]->set('status', MS_DEFAULT);
            $layerDistribution[$i]->set('connection', $connection);
            $layerDistribution[$i]->set('data', "geom from ({$layerQuery}) as " . $dataLayerName . ' using unique gid');
            $layerDistribution[$i]->set('dump', 'true');
            $layerDistribution[$i]->set('template', 'map');
            $layerDistribution[$i]->set('opacity', (int) $layerData['transparency']);
            $layerDistribution[$i]->set('labelitem', $tagLabel);
            $label = new labelObj();
            $label->set('encoding', 'UTF-8');
            $label->set('font', 'arial');
            $label->set('force', true);
            $label->set('size', $labelSize);
            $label->set('wrap', ord($this->wrapCharacter));

            $class[$i] = ms_newclassobj($layerDistribution[$i]);
            $class[$i]->set('name', $dataLayerName);

            if (true == $layerData['tags']) {
                $class[$i]->addLabel($label);
            }

            $legendName = '';
            if (isset($data[$i]['attributes']['farming'])) {
                $legendName = $legendName . $data[$i]['attributes']['farming'];
            }
            if (isset($data[$i]['attributes']['year'])) {
                $legendName = $legendName . '/' . $data[$i]['attributes']['year'] . '/';
            }
            if (isset($data[$i]['attributes']['name'])) {
                $legendName = $legendName . $data[$i]['attributes']['name'];
            }

            $class[$i]->set('title', $legendName);
            $style[$i] = ms_newstyleobj($class[$i]);
            $map->scalebar->set('units', MS_METERS);

            if (Config::LAYER_TYPE_KVS == $data[$i]['attributes']['layer_type']) {
                if (false == $layerData['border_only']) {
                    $style[$i]->color->setRGB(hexdec(substr($data[$i]['attributes']['fill_color'], 0, 2)), hexdec(substr($data[$i]['attributes']['fill_color'], 2, 2)), hexdec(substr($data[$i]['attributes']['fill_color'], 4, 2)));
                }
                $style[$i]->outlinecolor->setRGB(hexdec(substr($data[$i]['attributes']['border_color'], 0, 2)), hexdec(substr($data[$i]['attributes']['border_color'], 2, 2)), hexdec(substr($data[$i]['attributes']['border_color'], 4, 2)));
            } else {
                if (false == $layerData['border_only']) {
                    $style[$i]->color->setRGB(hexdec(substr($layerData['color'], 0, 2)), hexdec(substr($layerData['color'], 2, 2)), hexdec(substr($layerData['color'], 4, 2)));
                }
                $style[$i]->outlinecolor->setRGB(hexdec(substr($layerData['border_color'], 0, 2)), hexdec(substr($layerData['border_color'], 2, 2)), hexdec(substr($layerData['border_color'], 4, 2)));
            }

            $map->scalebar->outlinecolor->setRGB(0, 0, 0);
            $style[$i]->set('width', 1);

            // set kvs masiv layer
            if (Config::LAYER_TYPE_KVS == $layerData['layer_type']) {
                $layer_kvs_masiv = ms_newlayerobj($map);
                $layer_kvs_masiv->setConnectionType(MS_POSTGIS);
                $layer_kvs_masiv->set('name', 'kvs_masiv');
                $layer_kvs_masiv->set('type', MS_LAYER_POINT);
                $layer_kvs_masiv->set('status', MS_DEFAULT);
                $layer_kvs_masiv->set('connection', $connection);
                $layer_kvs_masiv->set('data', "geom from ({$masivLayerQuery}) as " . $dataLayerName . ' using unique masiv');
                $layer_kvs_masiv->set('dump', 'true');
                $layer_kvs_masiv->set('template', 'map');
                $layer_kvs_masiv->set('labelitem', 'masiv');

                $masiv_label = new labelObj();
                $masiv_label->set('encoding', 'UTF-8');
                $masiv_label->set('font', 'arial');
                $masiv_label->set('force', true);
                $masiv_label->set('size', $labelSize);

                $masiv_class = ms_newclassobj($layer_kvs_masiv);
                $masiv_class->addLabel($masiv_label);

                $masiv_style = ms_newstyleobj($masiv_class);
                $masiv_style->outlinecolor->setRGB(hexdec(substr($layerData['border_color'], 0, 2)), hexdec(substr($layerData['border_color'], 2, 2)), hexdec(substr($layerData['border_color'], 4, 2)));
                $nSymbolId = ms_newsymbolobj($map, 'circle');
                $oSymbol = $map->getsymbolobjectbyid($nSymbolId);
                $oSymbol->set('type', MS_SYMBOL_ELLIPSE);
                $oSymbol->set('filled', MS_TRUE);
                $aPoints[0] = 1;
                $aPoints[1] = 1;
                $oSymbol->setpoints($aPoints);

                $masiv_style->set('symbolname', 'circle');
                $masiv_style->set('size', 3 * $labelSize);

                $map->scalebar->outlinecolor->setRGB(0, 0, 0);
                $masiv_style->set('width', 1);
            }
        }
    }

    /**
     * Orders map layers based on key.
     *
     * @param [type] $data
     * @param [type] $key
     * @param [type] $sort
     */
    private function orderMapLayes($data, $key, $sort = SORT_ASC): iterable
    {
        array_multisort(array_map(function ($element) use ($key) {
            return $element[$key];
        }, $data), SORT_ASC, $data);

        return $data;
    }

    /**
     * Replaces returned db data wiht preview map attributes.
     *
     * @param [type] $layerData
     * @param [type] $requestData
     */
    private function updateWithDynamicStyles(&$layerData, $requestData)
    {
        $dbStyles = json_decode($layerData['style'], true);
        $previewStyles = json_decode($requestData['style'], true);
        if (Config::LAYER_TYPE_KVS == $requestData['attributes']['layer_type']) {
            $key = array_key_first($dbStyles);
            $mergedData[$key] = (object) array_merge((array) current($dbStyles), (array) current($previewStyles));
            $jsonStyle = $mergedData[$key];
        } else {
            $mergedData = (object) array_merge((array) $dbStyles, (array) $previewStyles);
            $jsonStyle = $mergedData;
        }

        $layerData['transparency'] = (int) $jsonStyle->transparency;
        $layerData['tags'] = $jsonStyle->tags;
        $layerData['border_only'] = $jsonStyle->border_only;

        return $mergedData;
    }
}
