<?php

namespace TF\Engine\APIClasses\Map;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

// Prado::using('Plugins.Core.Layers.conf');
// Prado::using('Plugins.Core.Layers.*');

/**
 * Попълване на полетата при разделяне на КВС имоти.
 *
 * @rpc-module Map
 *
 * @rpc-service-id kvs-split-propertygrid
 */
class KVSSplitPrGrid extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getKVSSplitPrGrid']],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item string gid
     *                         #item string featuresIds
     *                         }
     *
     * @return array
     */
    public function getKVSSplitPrGrid($rpcParams)
    {
        if ($this->User->isGuest || !(int) $rpcParams['gid']) {
            return [];
        }

        $UserDbController = new UserDbController($this->User->Database);
        $LayersController = new LayersController('Layers');

        $options = [
            'tablename' => $UserDbController->DbHandler->tableKVS,
            'return' => ['*'],
            'where' => [
                'gid' => ['column' => 'gid', 'compare' => '=', 'value' => $rpcParams['gid']],
            ],
        ];

        $array = $UserDbController->getItemsByParams($options, false, false);

        $rows = [];
        $featureDocumentAreas = $rpcParams['featureDocumentAreas'];
        $featureNumbers = $rpcParams['featureNumbers'];

        $featuresIds = explode(',', $rpcParams['featuresIds']);
        $featuresIds = $LayersController->ArrayHelper->filterEmptyStringArr($featuresIds);
        $featuresIdsCount = count($featuresIds);
        for ($i = 0; $i < $featuresIdsCount; $i++) {
            $groupRows = [
                0 => [
                    'name' => 'ekate',
                    'text' => 'EKATTE',
                    'value' => $array[0]['ekate'],
                    'group' => 'Имот ' . ($i + 1),
                    'groupId' => $i,
                    'featureId' => $featuresIds[$i],
                    'editingGid' => $rpcParams['gid'],
                ],
                1 => [
                    'name' => 'masiv',
                    'text' => 'Масив',
                    'value' => $array[0]['masiv'],
                    'group' => 'Имот ' . ($i + 1),
                    'groupId' => $i,
                    'featureId' => $featuresIds[$i],
                    'editingGid' => $rpcParams['gid'],
                ],
                2 => [
                    'name' => 'number',
                    'text' => 'Номер на имот',
                    'value' => $featureNumbers[$i] ?? '',
                    'editor' => [
                        'type' => 'combobox',
                        'options' => [
                            'url' => 'index.php?common-rpc=kvs-free-numbers',
                            'rpcParams' => [
                                [
                                    'gid' => $rpcParams['gid']],
                            ],
                            'valueField' => 'id',
                            'textField' => 'name',
                            'editable' => true,
                            'required' => true]],
                    'group' => 'Имот ' . ($i + 1),
                    'groupId' => $i,
                    'featureId' => $featuresIds[$i],
                    'editingGid' => $rpcParams['gid'],
                ],
                3 => [
                    'name' => 'area_kvs',
                    'text' => 'Площ по док. (дка)',
                    'value' => $featureDocumentAreas[$i] ?? $array[0]['area_kvs'],
                    'editor' => ['type' => 'numberbox', 'options' => ['min' => 0.001, 'precision' => 3, 'required' => true]],
                    'group' => 'Имот ' . ($i + 1),
                    'groupId' => $i,
                    'featureId' => $featuresIds[$i],
                    'editingGid' => $rpcParams['gid'],
                ],
                4 => [
                    'name' => 'mestnost',
                    'text' => 'Местност',
                    'value' => $array[0]['mestnost'],
                    'editor' => 'text',
                    'group' => 'Имот ' . ($i + 1),
                    'groupId' => $i,
                    'featureId' => $featuresIds[$i],
                    'editingGid' => $rpcParams['gid'],
                ],
                5 => [
                    'name' => 'category',
                    'text' => 'Категория',
                    'value' => $array[0]['category'],
                    'editor' => [
                        'type' => 'combobox',
                        'options' => [
                            'url' => 'index.php?common-rpc=plot-category-combobox',
                            'valueField' => 'id',
                            'textField' => 'name',
                            'editable' => false]],
                    'group' => 'Имот ' . ($i + 1),
                    'groupId' => $i,
                    'featureId' => $featuresIds[$i],
                    'editingGid' => $rpcParams['gid'],
                ],
                6 => [
                    'name' => 'area_type',
                    'text' => 'НТП',
                    'value' => $array[0]['area_type'],
                    'editor' => [
                        'type' => 'combobox',
                        'options' => [
                            'url' => 'index.php?common-rpc=plot-ntp-combobox',
                            'valueField' => 'id',
                            'textField' => 'name',
                            'editable' => false]],
                    'group' => 'Имот ' . ($i + 1),
                    'groupId' => $i,
                    'featureId' => $featuresIds[$i],
                    'editingGid' => $rpcParams['gid'],
                ],
                7 => [
                    'name' => 'usable',
                    'text' => 'Обработваем',
                    'value' => 1,
                    'editor' => ['type' => 'combobox', 'options' => ['data' => [
                        ['id' => 1, 'name' => 'Да'],
                        ['id' => 0, 'name' => 'Не'],
                    ], 'valueField' => 'id', 'textField' => 'name', 'editable' => false]],
                    'group' => 'Имот ' . ($i + 1),
                    'groupId' => $i,
                    'featureId' => $featuresIds[$i],
                    'editingGid' => $rpcParams['gid'],
                ],
            ];

            $rows = array_merge($rows, $groupRows);
        }

        $return['total'] = count($rows);
        $return['rows'] = $rows;

        return $return;
    }
}
