<?php

namespace TF\Engine\APIClasses\Map;

use Exception;
use PHPExcel_Exception;
use PHPExcel_Reader_Exception;
use PHPExcel_Writer_Exception;
use Prado\Exceptions\TDbException;
use Prado\Exceptions\TException;
use Prado\Prado;
use Prado\Web\Services\TRpcApiProvider;
use RuntimeException;
use TF\Application\Common\Config;
use TF\Application\Entity\Layer;
use TF\Application\Entity\LayerStyles;
use TF\Application\Entity\UserLayers;
use TF\Engine\APIClasses\Map\MapIntegrations\DFZCadastralPlots;
use TF\Engine\APIClasses\Map\MapIntegrations\DFZSplit;
use TF\Engine\APIClasses\Map\MapIntegrations\KAISSplit;
use TF\Engine\APIClasses\Plots\PlotsTree;
use TF\Engine\Kernel\ExportData\LayerTypes\LayerWork;
use TF\Engine\Kernel\ExportToExcelClass;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Kernel\PolygonFromMultipleLayers\PolygonFromLayersFactory;
use TF\Engine\Kernel\Sentry\Sentry;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbForIsak\UserDbForIsakController;
use TF\Engine\Plugins\Core\UserDbIsak\UserDbIsakController;
use TF\Engine\Plugins\Core\UserDbMap\UserDbMapController;
use TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Инструменти за карта.
 *
 * @rpc-module Map
 *
 * @rpc-service-id map-tools
 */
class MapTools extends TRpcApiProvider
{
    public const SPLIT_TOOL_SERVICE_TYPE_KAIS = 'KAIS';
    public const SPLIT_TOOL_SERVICE_TYPE_DFZ = 'DFZ';
    private $module = 'Map';
    private $service_id = 'map-tools';

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'selectPolygon' => ['method' => [$this, 'selectPolygon']],
            'selectPolygonFromMultipleLayers' => ['method' => [$this, 'selectPolygonFromMultipleLayers']],
            'deleteAllItemsByLayer' => ['method' => [$this, 'deleteAllItemsByLayer']],
            'saveItem' => ['method' => [$this, 'saveItem']],
            'savePropertyGrid' => ['method' => [$this, 'savePropertyGrid']],
            'copyLayerItems' => ['method' => [$this, 'copyLayerItems']],
            'copyLayer' => [
                'method' => [$this, 'copyLayer'],
                'validators' => [
                    [
                        'src_layer' => [
                            'id' => 'validateRequired', // int or string ('d' and 'df' are the ids of allowable layers)
                            'filters' => [
                                'groups' => 'isAssocArray',
                                'full_text_search' => 'validateString',
                                'plot_ids' => 'validateIntegerArray',
                            ],
                        ],
                    ],
                    [
                        'dst_layer' => [
                        ],
                    ],
                ],
            ],
            'saveClipping' => ['method' => [$this, 'saveClipping']],
            'deleteItem' => ['method' => [$this, 'deleteItem']],
            'saveKVSSplit' => ['method' => [$this, 'saveKVSSplit']],
            'initTopicLayer' => ['method' => [$this, 'initTopicLayer'],
                'validators' => [
                    'rpcParams' => [
                        'ekate' => 'validateRequired, validateDigitsOnly',
                        'type' => 'validateRequired, validateInteger',
                    ],
                ],
            ],
            'refreshTopicLayerKVSViews' => ['method' => [$this, 'refreshTopicLayerKVSViews']],
            'getCombinedGeometry' => ['method' => [$this, 'getCombinedGeometry']],
            'clippingKVSByNTPAndUpdateAreas' => ['method' => [$this, 'clippingKVSByNTPAndUpdateAreas']],
            'clippingWithKVSAndExportExcel' => ['method' => [$this, 'clippingWithKVSAndExportExcel']],
            'splitPolygon' => ['method' => [$this, 'splitPolygon']],
            'calculateAvgSlopeByGeoJson' => [
                'method' => [$this, 'calculateAvgSlopeByGeoJson'],
                'validators' => [
                    'plots_geojson' => 'validateRequired,validateNotNull',
                ],
            ],
            'getReportForRequestedCadastralPlots' => [
                'method' => [$this, 'getReportForRequestedCadastralPlots'],
                'validators' => [
                    'kad_ident' => 'validateRequired,validateNotNull',
                ],
            ],
            'getLayerColoringLegend' => [
                'method' => [$this, 'getLayerColoringLegend'],
                'validators' => [
                    'layer_id' => 'validateRequired,validateNotNull',
                    'ekatte' => 'validateText',
                ],
            ],
        ];
    }

    /**
     * 'Избор на обект'.
     *
     * @api-method selectPolygon
     *
     * @param array $rpcParams
     *                         {
     *                         #item string table_name
     *                         #item float  lon
     *                         #item float  lat
     *                         #item array extent
     *                         {
     *                         #item float point1
     *                         #item float point2
     *                         #item float point3
     *                         #item float point4
     *                         }
     *                         }
     *
     * @return array
     */
    public function selectPolygon($rpcParams)
    {
        $this->selectPolygonFromMultipleLayers($rpcParams);
        $UserDbController = new UserDbController($this->User->Database);

        $idFieldName = 'gid';

        $geomData = $UserDbController->getPolygonDataByPointFromLogin3($rpcParams['table_name'], $rpcParams['lon'], $rpcParams['lat'], $rpcParams['extent'], $idFieldName);

        return $geomData[0];
    }

    /**
     * @api-method selectPolygonFromMultipleLayers
     *
     * @param array $rpcParams
     *                         {
     *                         #item array table_names
     *                         #item float  lon
     *                         #item float  lat
     *                         }
     *
     * @return array
     */
    public function selectPolygonFromMultipleLayers($rpcParams)
    {
        $result = [
            'type' => 'FeatureCollection',
            'features' => [],
        ];
        $groupId = $this->getUser()->GroupID;

        foreach ($rpcParams['table_names'] as $tableName) {
            $polygonFromLayersFactory = new PolygonFromLayersFactory();
            $polygonFromLayersClass = $polygonFromLayersFactory->getClass($tableName, $groupId, $rpcParams['geometry'], $rpcParams['filters'] ?? []);
            $plots = $polygonFromLayersClass->getGeoJson();
            $systemLayer = array_search($tableName, $GLOBALS['Layers']['remoteTables']);

            foreach ($plots as $plot) {
                $plot = json_decode($plot['geo_json'], true);
                $plotProperties = $plot['properties'];
                $layer = UserLayers::getLayerById($plotProperties['layer_id']);
                $definitions = $layer->getDefinitions();

                if (Config::LAYER_TYPE_KVS == $layer->layer_type) {
                    $styles = $layer->getStyles();
                    [$ekatteColumnDef] = UserLayers::filterDefinitions($definitions, [['col_category' => Config::LAYER_COLUMN_CATEGORY_EKATTE]]);
                    $filteredStyles = array_filter($styles, function ($style) use ($layer, $plotProperties, $ekatteColumnDef) {
                        return $style->layer_id === $layer->id . '_' . $plotProperties[$ekatteColumnDef['col_name']];
                    });

                    $plotProperties['layer_label'] = $plotProperties['virtual_ekatte_name'] . ' (' . $plotProperties['layer_label'] . ')';
                    $style = array_pop($filteredStyles);
                } else {
                    [$style] = $layer->getStyles();
                }

                $plotProperties['fill_column_name'] = LayerStyles::BY_ATTRIBUTE_COLORING_TYPE === $style->type && $style->fill_column_name ? $style->fill_column_name : null;
                $plotProperties['border_column_name'] = LayerStyles::BY_ATTRIBUTE_COLORING_TYPE === $style->type && $style->border_column_name ? $style->border_column_name : null;

                if ($systemLayer) {
                    $plot['layer_type'] = $systemLayer;
                    $plotProperties['layer_type'] = $plot['layer_type'];
                }
                if (array_key_exists($tableName, $GLOBALS['Layers']['customLayers'])) {
                    $plotProperties = array_merge($plotProperties, $GLOBALS['Layers']['customLayers'][$tableName]);
                }
                if (!$systemLayer) {
                    $plotProperties['farm_year'] = $GLOBALS['Farming']['years'][$plotProperties['farm_year']]['year'];
                }
                $slopeColumn = LayerWork::$slopeColumn;
                if (!$this->User->HasSlopeRights && array_key_exists($slopeColumn, $plotProperties)) {
                    unset($plotProperties[$slopeColumn]);
                }

                if (Config::LAYER_TYPE_ZP == $plotProperties['layer_type']) {
                    $culture = (int) $plotProperties['culture'];
                    $plotProperties['culture'] = $GLOBALS['Farming']['crops'][$culture]['crop_name'];
                }

                if (Config::LAYER_TYPE_WORK_LAYER == $plotProperties['layer_type']) {
                    $defKeys = array_column($definitions, 'col_name');
                    $definitions = array_combine($defKeys, $definitions);
                    $UserDbController = new UserDbController($this->User->Database);

                    $columnsList = $UserDbController->getTableColumnsList($tableName);
                    $slopeColumnIndex = array_search(LayerWork::$slopeColumn, $columnsList);

                    if (!$this->User->HasSlopeRights && false !== $slopeColumnIndex) {
                        unset($columnsList[$slopeColumnIndex]);
                    }

                    $columns = $this->formatAndTranslateColumns($columnsList, $definitions);
                    $plotProperties['translation'] = $columns;
                }

                $plot['properties'] = $plotProperties;
                $result['features'][] = $plot;
            }
        }

        return $result;
    }

    /**
     * 'Изтриване на всички обекти в избрания активен слой'.
     *
     * @api-method deleteAllItemsByLayer
     *
     * @param array $rpcParams
     *                         {
     *                         #item string layer_id
     *                         #item string tablename
     *                         }
     *
     * @throws MTRpcException
     *
     * @return array|bool
     */
    public function deleteAllItemsByLayer($rpcParams)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        $LayersController = new LayersController('Layers');

        if (Config::LAYER_TYPE_WORK_LAYER == $rpcParams['layer_type']) {
            $this->deleteWorkLayer($rpcParams);

            return true;
        }

        $deletableLayersTypes = [Config::LAYER_TYPE_ISAK, Config::LAYER_TYPE_FOR_ISAK, Config::LAYER_TYPE_KMS, Config::LAYER_TYPE_ZP];

        // check if table exists
        $tableExist = $UserDbController->getTableNameExist($rpcParams['tablename']);

        // server side check
        if (!$tableExist) {
            throw new MTRpcException('DATABASE_INVALID_TABLE_NAME', -33102);
        }

        if (!in_array($rpcParams['layer_type'], $deletableLayersTypes)) {
            throw new MTRpcException('LAYER_NOT_DELETABLE', -346063);
        }

        $UserDbController->removeAllGeometries($rpcParams['tablename']);

        // Delete related AB lines
        $options = [
            'tablename' => $UserDbController->DbHandler->tableAbLines,
            'where' => [
                'layer_table' => ['column' => 'layer_table', 'compare' => '=', 'value' => $rpcParams['tablename']],
            ],
        ];
        $UserDbController->deleteItemsByParams($options);

        $this->updateMapFile($rpcParams['tablename'], $rpcParams['layer_id']);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['request_params' => $rpcParams], [], 'Delete all items by layer');

        $options = [
            'return' => [
                'f.name as farming_name', 'f.*', 't.*',
            ],
            'where' => [
                'layer_id' => ['column' => 'id', 'compare' => '=', 'prefix' => 't', 'value' => $rpcParams['layer_id']],
            ],
        ];
        $layer_results = $LayersController->getLayers($options, false, false);

        if (0 == count($layer_results)) {
            $return['error'] = true;
        } else {
            $return = [
                'extent' => str_replace(' ', ', ', $layer_results[0]['extent']),
                'layer_name' => $rpcParams['tablename'],
            ];

            if (Config::LAYER_TYPE_FOR_ISAK == $rpcParams['layer_type']) {
                $UserDbController->dropMaterializedView('sepp_for_isak_' . $rpcParams['layer_id']);
                $UserDbController->dropMaterializedView('pzp_for_isak_' . $rpcParams['layer_id']);
            }
        }
        $this->updateIsExistEmptyTable($rpcParams['tablename'], $rpcParams['layer_id']);

        return $return;
    }

    /**
     * 'Изтриване на обекти'.
     *
     * @api-method deleteItem
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer layer_id
     *                         #item string  tablename
     *                         #item string  id_name
     *                         #item array   features
     *                         {
     *                         #item integer id/gid
     *                         }
     *                         }
     *
     * @return array
     */
    public function deleteItem($rpcParams)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        $LayersController = new LayersController('Layers');

        $layer = UserLayers::getLayerById($rpcParams['layer_id']);
        $tablename = $layer->table_name;
        $layerId = $layer->getId();

        /** @var array $srcLayerIds The ids of the source layer items that will be deleted */
        $srcLayerIds = $this->getLayerDataByFilters(
            [
                'layer_id' => $layerId,
                'layer_type' => $layer->layer_type,
                'gids' => $rpcParams['gids'] ?? ($rpcParams['features'] ?? []),
                'filter_params' => $rpcParams['filter_params'] ?? [],
            ],
            true
        );

        $layerIdColumnName = $layer->getDefinitionByCategory(Config::LAYER_COLUMN_CATEGORY_GID)['col_name'];
        $UserDbController->deleteItemsByParams([
            'tablename' => $tablename,
            'id_name' => $layerIdColumnName,
            'id_string' => implode(',', $srcLayerIds),
        ]);

        // Delete related AB lines
        $options = [
            'tablename' => $UserDbController->DbHandler->tableAbLines,
            'id_name' => 'plot_id',
            'id_string' => implode(',', $srcLayerIds),
            'where' => [
                'layer_table' => ['column' => 'layer_table', 'compare' => '=', 'value' => $tablename],
            ],
        ];
        $UserDbController->deleteItemsByParams($options);

        $this->updateMapFile($tablename, $layerId);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['request_params' => $rpcParams], [], 'Delete map single item');

        $options = [
            'return' => [
                'f.name as farming_name', 'f.*', 't.*',
            ],
            'where' => [
                'layer_id' => ['column' => 'id', 'compare' => '=', 'prefix' => 't', 'value' => $layerId],
            ],
        ];
        $layer_results = $LayersController->getLayers($options, false, false);

        if (0 == count($layer_results)) {
            $return['error'] = true;
        } else {
            $return = [
                'extent' => str_replace(' ', ', ', $layer_results[0]['extent']),
                'layer_name' => $tablename,
            ];
        }

        $this->updateIsExistEmptyTable($tablename, $layerId);

        return $return;
    }

    /**
     * Записване на 'Разцепване на обект' от слой КВС
     *
     * @api-method saveKVSSplit
     *
     * @param array $rpcParams
     *                         {
     *                         #item array newPlots
     *                         {
     *                         #item string    ekate
     *                         #item string    masiv
     *                         #item string    number
     *                         #item string    category
     *                         #item string    area_type
     *                         #item string    mestnost
     *                         #item string    usable
     *                         #item string    area_kvs
     *                         #item timestamp editActiveFrom
     *                         #item integer   featureId
     *                         }
     *                         #item array features
     *                         {
     *                         #item integer id
     *                         #item string  geometry
     *                         }
     *                         }
     *
     * @throws Exception
     *
     * @return array
     */
    public function saveKVSSplit($rpcParams)
    {
        $UsersController = new UsersController('Users');
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);

        $newPlots = $rpcParams['newPlots'];
        $newPlotsCount = count($newPlots);
        $existNums = $this->findExistingPlotNumbers($newPlots);
        $featuresCount = count($rpcParams['features']);
        if (count($existNums)) {
            return [
                'type' => 'existNums',
                'existNums' => implode(', ', $existNums),
            ];
        }

        for ($i = 0; $i < $newPlotsCount; $i++) {
            for ($j = 0; $j < $featuresCount; $j++) {
                if ($rpcParams['features'][$j]['id'] == $newPlots[$i]['featureId']) {
                    $newPlots[$i]['geom'] = $rpcParams['features'][$j]['geometry'];
                }
            }
        }

        $transaction = $UsersController->startTransaction();

        try {
            // edit original polygon
            $options = [
                'tablename' => $UserDbController->DbHandler->tableKVS,
                'mainData' => [
                    'is_edited' => true,
                    'edit_date' => date('Y-m-d'),
                    'edit_active_from' => $rpcParams['editActiveFrom'],
                ],
                'where' => [
                    'gid' => $rpcParams['editingGid'],
                ],
            ];

            $UserDbController->editItem($options);
            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['request_params' => $options], [], 'Save kvs split - original polygon');
            $ekattes = [];

            // add new polygons
            $newPlotIDs = [];
            for ($i = 0; $i < $newPlotsCount; $i++) {
                $plot = $newPlots[$i];
                $options = [
                    'tablename' => $UserDbController->DbHandler->tableKVS,
                    'mainData' => [
                        'kad_ident' => $plot['ekate'] . '.' . $plot['masiv'] . '.' . $plot['number'],
                        'ekate' => $plot['ekate'],
                        'masiv' => $plot['masiv'],
                        'number' => $plot['number'],
                        'category' => $plot['category'],
                        'area_type' => $plot['area_type'],
                        'mestnost' => $plot['mestnost'],
                        'usable' => $plot['usable'],
                        'document_area' => $plot['area_kvs'],
                        'used_area' => $plot['area_kvs'],
                        'edit_active_from' => $rpcParams['editActiveFrom'],
                        'has_contracts' => 'FALSE',
                        'used_area_by' => 1,
                        'area_farming' => 0,
                        'area_year' => 0,
                        'is_edited' => 'FALSE',
                    ],
                    'id_name' => 'gid',
                ];

                if (!in_array($plot['ekate'], $ekattes)) {
                    $ekattes[] = $plot['ekate'];
                }

                $newPlotIDs[$i] = $UserDbController->addItem($options);

                $array[0] = (object) [];
                $array[0]->id = $newPlotIDs[$i];
                $array[0]->geometry = $plot['geom'];

                $UserDbController->updateGeometry($UserDbController->DbHandler->tableKVS, $array);

                $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['params' => $options], ['created_gid' => $newPlotIDs[$i]], 'Save kvs split - new polygons');
            }

            $UserDbController->updateKvsBorders($ekattes);

            $UserDbController->updateAllowableArea($UserDbController->DbHandler->tableKVS, $newPlotIDs);
            $newPlotIDsCount = count($newPlotIDs);
            // add kvs edit log
            for ($i = 0; $i < $newPlotIDsCount; $i++) {
                $options = [
                    'tablename' => $UserDbController->DbHandler->tableKvsEditLog,
                    'mainData' => [
                        'new_gid' => $newPlotIDs[$i],
                        'old_gid' => $rpcParams['editingGid'],
                        'edit_type' => 'split',
                        'edit_date' => date('Y-m-d'),
                        'edit_active_from' => $rpcParams['editActiveFrom'],
                    ],
                ];

                $recordID = $UserDbController->addItem($options);

                $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['params' => $options], ['created_id' => $recordID], 'Save kvs split - edit log');
            }

            $transaction->commit();

            // get contracts for editing plot
            $options = [
                'return' => [
                    'kvs.gid', 'array_agg(c.id) as c_id', "string_agg(c.c_num, ', ') as c_num",
                ],
                'where' => [
                    'active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'true'],
                    'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'prefix' => 'c', 'value' => 'false'],
                    'is_annnex' => ['column' => 'is_annex', 'compare' => '=', 'prefix' => 'c', 'value' => 'false'],
                    'annex_action' => ['column' => 'annex_action', 'compare' => '=', 'prefix' => 'pc', 'value' => 'added'],
                    'gid' => ['column' => 'gid', 'compare' => '=', 'prefix' => 'kvs', 'value' => $rpcParams['editingGid']],
                ],
                'get_edited_plots' => true,
                'group' => 'kvs.gid',
                'farming_start_date' => $rpcParams['editActiveFrom'],
                'farming_due_date' => '9999-12-31',
            ];

            $results = $UserDbPlotsController->getFullPlotDataForDeclaration($options, false, false);
            if (count($results) > 0) {
                $results[0]['c_id'] = trim($results[0]['c_id'], '{}');

                return [
                    'type' => 'contracts',
                    'contracts' => $results[0],
                ];
            }
        } catch (Exception $e) {
            $transaction->rollBack();

            throw $e;
        }
    }

    /**
     * Запазване на обекти.
     *
     * @rpc-module Map
     *
     * @api-method saveItem
     *
     * @param array $rpcParams
     *                         {
     *                         #item string tablename
     *                         #item string layer_type
     *                         #item string tool_operation
     *                         #item array  features
     *                         {
     *                         #item integer id
     *                         #item string  geometry
     *                         }
     *                         }
     *
     * @return array|bool
     */
    public function saveItem($rpcParams)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        $deleteLayerAttributeData = false;

        if (0 === count($rpcParams['features'])) {
            return false;
        }

        if (Config::LAYER_TYPE_WORK_LAYER == $rpcParams['layer_type'] && 'new' === $rpcParams['layer_id']) {
            $workLayerDefaultDefinitions = UserLayers::getDefinitionsByType(Config::LAYER_TYPE_WORK_LAYER);
            $newWorkLayer = $this->createWorkLayer($workLayerDefaultDefinitions, null, $rpcParams, $UserDbController);
            $rpcParams['tablename'] = $newWorkLayer->table_name;
            $rpcParams['layer_id'] = $newWorkLayer->id;
        }

        if (!isset($rpcParams['tablename'])) {
            return false;
        }

        // create table FOR_ISAK if the table does not exist
        if (Config::LAYER_TYPE_FOR_ISAK == $rpcParams['layer_type'] && !$UserDbController->getTableNameExist($rpcParams['tablename'])) {
            $UserDbController->createTableForISAK($rpcParams['tablename']);
        }

        // only one action will be performed
        // only edit or only add
        // if first element has ID then all elements are for edit
        $featuresCount = count($rpcParams['features']);

        try {
            if ($rpcParams['features'][0]['gid'] || $rpcParams['features'][0]['id']) {
                $features = [];
                for ($i = 0; $i < $featuresCount; $i++) {
                    $features[$i] = (object) $rpcParams['features'][$i];
                }

                if (Config::LAYER_TYPE_GPS == $rpcParams['layer_type'] && isset($rpcParams['plot_name']) && Config::TOOL_OPERATION_SAVE_WITH_INFO == $rpcParams['tool_operation']) {
                    $UserDbController->updateGeometryWithInfoForGps($rpcParams['tablename'], $features, $rpcParams['plot_name'], $rpcParams['plot_info']);
                } else {
                    $UserDbController->updateGeometry($rpcParams['tablename'], $features);
                }

                if (Config::LAYER_TYPE_FOR_ISAK == $rpcParams['layer_type'] && isset($rpcParams['plot_name'], $rpcParams['ekatte'])
                             && Config::TOOL_OPERATION_SAVE_WITH_INFO == $rpcParams['tool_operation']) {
                    $features = [];
                    for ($i = 0; $i < $featuresCount; $i++) {
                        $features[$i] = (object) $rpcParams['features'][$i];
                    }

                    $UserDbController->updateGeometryWithInfoForIsak($rpcParams['tablename'], $features, $rpcParams['plot_name'], $rpcParams['ekatte']);
                }

                if (Config::LAYER_TYPE_FOR_ISAK == $rpcParams['layer_type'] && Config::TOOL_OPERATION_EDIT_GEOMETRY == $rpcParams['tool_operation']) {
                    $features = [];
                    for ($i = 0; $i < $featuresCount; $i++) {
                        $features[$i] = (object) $rpcParams['features'][$i];
                    }
                    $UserDbController->updateDataForIsak($rpcParams['tablename'], $features);
                }
            } else {
                // if split, merge or draw hole actions were performed
                // draw hole
                if (Config::TOOL_OPERATION_DRAW_HOLE == $rpcParams['tool_operation']
                    && $rpcParams['replacePolygonVariables']['replace_reqired']
                    && count($rpcParams['replacePolygonVariables']['id_array'])
                    && null != $rpcParams['replacePolygonVariables']['id_array'][0]) {
                    $feature = (object) $rpcParams['features'][0];
                    $UserDbController->updateGeometryUniversal($rpcParams['tablename'], $feature->geometry, $rpcParams['replacePolygonVariables']['id_array'][0], $rpcParams['replacePolygonVariables']['id_name']);

                    $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['request_params' => $rpcParams], [], 'Makes a hole');
                } else {
                    // insert info from popup
                    if (Config::LAYER_TYPE_FOR_ISAK == $rpcParams['layer_type'] && isset($rpcParams['plot_name'], $rpcParams['ekatte'])
                         && Config::TOOL_OPERATION_SAVE_WITH_INFO == $rpcParams['tool_operation']) {
                        $features = [];
                        for ($i = 0; $i < $featuresCount; $i++) {
                            $features[$i] = (object) $rpcParams['features'][$i];
                        }

                        $UserDbController->addGeometryWithInfoForIsak($rpcParams['tablename'], $features, $rpcParams['plot_name'], $rpcParams['ekatte']);
                    } elseif (Config::LAYER_TYPE_GPS == $rpcParams['layer_type'] && isset($rpcParams['plot_name']) && Config::TOOL_OPERATION_SAVE_WITH_INFO == $rpcParams['tool_operation']) {
                        $features = [];
                        for ($i = 0; $i < $featuresCount; $i++) {
                            $features[$i] = (object) $rpcParams['features'][$i];
                        }

                        $UserDbController->addGeometryWithInfoForGps($rpcParams['tablename'], $features, $rpcParams['plot_name'], $rpcParams['plot_info']);
                    } else {
                        $features = [];

                        if ($rpcParams['replacePolygonVariables']['replace_reqired'] && count($rpcParams['replacePolygonVariables']['id_array']) && null != $rpcParams['replacePolygonVariables']['id_array'][0]) {
                            $idImplodeString = implode(',', $rpcParams['replacePolygonVariables']['id_array']);
                            $layer = Layer::getInstance($rpcParams['tablename']);
                            $toolOperation = $rpcParams['tool_operation'];
                            $deleteLayerAttributeData = true;

                            switch ($toolOperation) {
                                case Config::TOOL_OPERATION_INTERSECT:
                                case Config::TOOL_OPERATION_SPLIT:
                                    $layerToSplit = $layer->find("gid = ({$idImplodeString})");
                                    $attributes = $layer->splitAttributes($layerToSplit, $featuresCount);

                                    break;

                                case Config::TOOL_OPERATION_MERGE:
                                    $layersToMerge = $layer->findAll("gid IN ({$idImplodeString})");
                                    $attributes = $layer->mergeAttributes($layersToMerge);

                                    break;

                                default:
                                    $attributes = [$rpcParams['properties'] ?? []];

                                    break;
                            }
                        } else {
                            $attributes = [$rpcParams['properties'] ?? []];
                        }

                        for ($i = 0; $i < $featuresCount; $i++) {
                            $features[$i] = (object) $rpcParams['features'][$i];
                            $fieldParams[$i] = $attributes[$i] ?? [];
                        }

                        $UserDbController->addGeometry($rpcParams['tablename'], $features, $fieldParams);
                    }

                    if (true === $deleteLayerAttributeData) {
                        $this->deleteLayerAttributeData(
                            $rpcParams['tablename'],
                            $rpcParams['replacePolygonVariables']['id_name'],
                            $idImplodeString
                        );
                    }
                }
            }

            $this->personalizeLayer($rpcParams['layer_id'], $rpcParams['layer_type']);
        } catch (TDbException $e) {
            // TDbException is never handled nor thrown
            $re = '/\\(prc_name\\)=\\((?P<prc_name>[^\\)]*)\\)/';

            preg_match_all($re, $e->getErrorMessage(), $matches);

            $prc_name = $matches['prc_name'][0];

            if (null != $prc_name) {
                return ['existing_prc_name' => $matches['prc_name'][0]];
            }

            return ['other_error' => $e->getErrorMessage()];
        }

        $return = $this->commonUpdateLayerProjectionAndMapFile($rpcParams['tablename'], $rpcParams['layer_id']);

        $this->onGeomUpdate($rpcParams);
        if (Config::LAYER_TYPE_FOR_ISAK == $rpcParams['layer_type']) {
            $forIsakOldExtent = $this->getExtentFromLayers($rpcParams['layer_id']);
            $this->updateSEPPView($rpcParams['layer_id'], $forIsakOldExtent, $return['extent']);
            $this->updatePZPView($rpcParams['layer_id'], $forIsakOldExtent, $return['extent']);
        }

        return $return;
    }

    /**
     * Запис на грид "Информация" за имот/обект
     *
     * @api-method savePropertyGrid
     *
     * @param array $rpcParams
     *                         {
     *                         #item array [0]
     *                         {
     *                         #item string tablename
     *                         #item string layer_type
     *                         #item integer gid
     *                         }
     *                         #item array [1] - area_name
     *                         {
     *                         #item string value
     *                         }
     *                         #item array [2] - ekatte
     *                         {
     *                         #item string value
     *                         }
     *                         #item array [3] - culture
     *                         {
     *                         #item string value
     *                         }
     *                         #item array [4] - obrabotki
     *                         {
     *                         #item string value
     *                         }
     *                         #item array [5] - dobivi
     *                         {
     *                         #item string value
     *                         }
     *                         #item array [6] - napoqvane
     *                         {
     *                         #item string value
     *                         }
     *                         #item array [7] - polivki
     *                         {
     *                         #item string value
     *                         }
     *                         #item array [8] - polzvatel
     *                         {
     *                         #item string value
     *                         }
     *                         #item array [9] - isak_prc_uin
     *                         {
     *                         #item string value
     *                         }
     *                         }
     */
    public function savePropertyGrid($rpcParams)
    {
        $UsersController = new UsersController('Users');
        $UserDbController = new UserDbController($this->User->Database);

        $layerType = $rpcParams['ident']['layer_type'];
        $layerId = $rpcParams['ident']['layer_id'];

        $options = [];
        $options['tablename'] = $rpcParams['ident']['tablename'];

        // JavaScript въща текста на полето, а не стойността
        // за това се прави това присвояване, защото в противен случай
        // SQL се опитва вместо true/false да запази Да/Не като стойности.
        $rpcParams['irrigation_area']['value'] = isset($rpcParams['irrigation_area']['value'])
            && ('Да' == $rpcParams['irrigation_area']['value'] || true == $rpcParams['irrigation_area']['value'])
            ? true
            : false;

        if (Config::LAYER_TYPE_ZP == $layerType) {
            $options['where']['id'] = $rpcParams['ident']['gid'];
            $options['mainData'] = [
                'area_name' => $rpcParams['plot_name']['value'],
                'obrabotki' => $rpcParams['tilth']['value'],
                'dobivi' => $rpcParams['yield']['value'],
                'napoqvane' => $rpcParams['irrigation']['value'],
                'polivki' => $rpcParams['watering']['value'],
                'polzvatel' => $rpcParams['user']['value'],
                'isak_prc_uin' => $rpcParams['isak_number']['value'],
            ];

            // this record holds the combobox data, if it's changed it is numeric
            if (is_numeric($rpcParams['culture']['value']) || is_null($rpcParams['culture']['value'])) {
                $options['mainData']['culture'] = $rpcParams['culture']['value'];
            }

            if (is_numeric($rpcParams['ekatte']['value']) || is_null($rpcParams['ekatte']['value'])) {
                $options['mainData']['ekatte'] = $rpcParams['ekatte']['value'];
            }
        } elseif (Config::LAYER_TYPE_GPS == $layerType) {
            $options['where']['gid'] = $rpcParams['ident']['gid'];
            $options['mainData'] = [
                'plot_name' => $rpcParams['plot_name']['value'],
                'plot_info' => $rpcParams['comment']['value'],
            ];
        } else {
            $options['where']['gid'] = $rpcParams['ident']['gid'];
            if (Config::LAYER_TYPE_ISAK == $rpcParams['ident']['layer_type']) {
                $options['mainData'] = [
                    'prc_uin' => $rpcParams['prk_number']['value'],
                    'bzs_id' => $rpcParams['bzs_number']['value'],
                    'area' => $rpcParams['area_ha']['value'],
                    'watering' => 'да' == mb_strtolower(trim($rpcParams['irrigation']['value']), 'UTF-8'),
                    'urn' => $rpcParams['urn']['value'],
                ];

                if (is_numeric($rpcParams['cropcode']['value']) || is_null($rpcParams['cropcode']['value'])) {
                    $options['mainData']['cropcode'] = $rpcParams['cropcode']['value'];
                }
                if (is_numeric($rpcParams['ekatte']['value']) || is_null($rpcParams['ekatte']['value'])) {
                    $options['mainData']['ekatte'] = $rpcParams['ekatte']['value'];
                }
            }
            if (Config::LAYER_TYPE_FOR_ISAK == $layerType) {
                $options['mainData'] = $UsersController->getMainDatOptionsForIsakPropertyGrid($rpcParams);
            }

            if (Config::LAYER_TYPE_KVS == $layerType) {
                $options['where']['gid'] = $rpcParams['ident']['gid'];

                list($ekate, $masiv, $imot) = explode('.', $rpcParams['kad_ident']['value']);
                $kadIdent = "{$ekate}.{$rpcParams['masiv']['value']}.{$rpcParams['plot_number']['value']}";

                $options['mainData'] = [
                    'kad_ident' => $kadIdent,
                    'ekate' => $rpcParams['ekatte']['value'],
                    'masiv' => $rpcParams['masiv']['value'],
                    'number' => $rpcParams['plot_number']['value'],
                    'irrigated_area' => $rpcParams['irrigation_area']['value'],
                    'mestnost' => $rpcParams['mestnost']['value'],
                ];

                if (0 == strlen($options['mainData']['ekate']) || 0 == strlen($options['mainData']['masiv']) || 0 == strlen($options['mainData']['number'])) {
                    throw new MTRpcException('MAP_KVS_EDIT_PLOT', -33067);
                }

                if (is_numeric($rpcParams['category']['value']) || is_null($rpcParams['category']['value'])) {
                    $options['mainData']['category'] = $rpcParams['category']['value'];
                }

                if (is_numeric($rpcParams['ntp']['value']) || is_null($rpcParams['ntp']['value'])) {
                    $options['mainData']['area_type'] = $rpcParams['ntp']['value'];
                }
            }
            if (Config::LAYER_TYPE_KMS == $layerType) {
                $options['mainData'] = [
                    'name' => $rpcParams['plot_name']['value'],
                ];

                if (is_numeric($rpcParams['crop_code']['value']) || is_null($rpcParams['crop_code']['value'])) {
                    $options['mainData']['crop_code'] = (int) $rpcParams['crop_code']['value'];
                }

                if (is_numeric($rpcParams['ekatte']['value']) || is_null($rpcParams['ekatte']['value'])) {
                    $options['mainData']['ekatte'] = $rpcParams['ekatte']['value'];
                }
            }
            if (Config::LAYER_TYPE_WORK_LAYER == $rpcParams['ident']['layer_type']) {
                $options['where']['gid'] = $rpcParams['ident']['gid'];

                $columnDefinitions = UserLayers::getLayerById($layerId)->getDefinitions();
                $virtualColumnDef = UserLayers::filterDefinitions($columnDefinitions, [['col_virtual' => true, 'col_personalizable' => true]]);
                $referenceColumns = array_map(function ($definition) {
                    return $definition['col_reference'];
                }, $virtualColumnDef);

                foreach ($rpcParams as $key => $value) {
                    if ($value['is_virtual']) {
                        continue;
                    }

                    if ('' == trim($value['value'])) {
                        $value['value'] = null;
                    }

                    // Validate if the value for reference columns is not changed, skip update.
                    // If it is changed in FE, it should be numeric.
                    if (in_array($value['prop_name'], $referenceColumns) && (!is_numeric($value['value']) && !is_null($value['value']))) {
                        continue;
                    }

                    $options['mainData'][$value['prop_name']] = $value['value'];
                }

                $optionsKeys = array_keys($options['mainData']);
                foreach ($optionsKeys as $key) {
                    if ('' == $key) {
                        unset($options['mainData'][$key]);
                    }
                }
            }
        }

        $UserDbController->editItem($options);
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options, [], 'Save property grid');

        $this->personalizeLayer($layerId, $layerType, ($rpcParams['ekatte']['value'] ?? null));
    }

    public function copyLayer(array $rpcParams)
    {
        $userDbController = new UserDbController($this->User->Database);

        $transaction = $userDbController->startTransaction();

        try {
            $createNewLayer = false;

            /** @var UserLayers $srcLayer */
            $srcLayer = UserLayers::getLayerById($rpcParams['src_layer']['id']);

            /** @var UserLayers $dstLayer */
            $dstLayer = 'new' === $rpcParams['dst_layer']['id'] || !isset($rpcParams['dst_layer']['id'])
                ? null
                : UserLayers::getLayerById($rpcParams['dst_layer']['id']);

            // Check if the destination layer is copyable
            if (
                $rpcParams['dst_layer']['layer_type']
                && !in_array($rpcParams['dst_layer']['layer_type'], $GLOBALS['Layers']['copyableLayers'])
            ) {
                throw new MTRpcException('LAYER_NOT_COPYABLE', -346061);
            }

            // Create new work layer table or add columns to an existing one if needed
            if (Config::LAYER_TYPE_WORK_LAYER == $rpcParams['dst_layer']['layer_type']) {
                $definitions = $srcLayer->getDefinitions();
                $virtualColumnDef = UserLayers::filterDefinitions($definitions, [['col_virtual' => true, 'col_personalizable' => true]]);
                $referenceColumnNames = array_map(function ($definition) {
                    return $definition['col_reference'];
                }, $virtualColumnDef);

                // Destination layer exists and there are specified columns to copy from source layer
                if ($dstLayer && isset($rpcParams['dst_layer']['columns'])) {
                    $columnsToAdd = $rpcParams['dst_layer']['columns'];
                    $srcLayerDefinitions = array_map(function ($definition) use ($referenceColumnNames) {
                        // Virtual columns and reference columns should not be manipulated.
                        if ($definition['col_virtual'] || in_array($definition['col_name'], $referenceColumnNames)) {
                            return $definition;
                        }

                        // Set options to true for the columns that are going to be copied
                        $definition['col_visible'] = true;
                        $definition['col_multiedit'] = true;
                        $definition['col_singleedit'] = true;
                        $definition['col_sortable'] = true;
                        $definition['col_exportable'] = true;
                        $definition['col_personalizable'] = true;

                        return $definition;
                    }, $definitions);

                    // Filter only the columns that are copyable
                    $srcLayerDefinitions = UserLayers::filterDefinitions($srcLayerDefinitions, [['col_copyable' => true]]);
                    $diffDefinitions = UserLayers::diffDefinitions($dstLayer->getDefinitions(), $srcLayerDefinitions);

                    /** @var array $aditionalColumnDefinitions The specified columns that exist in the source layer and don't exist in the desitnation */
                    $aditionalColumnDefinitions = array_filter($diffDefinitions, function ($definition) use ($columnsToAdd) {
                        return in_array($definition['col_name'], $columnsToAdd);
                    });
                    $aditionalColumnDefinitions = array_values($aditionalColumnDefinitions);

                    // Create the additional columns in the destination work layer
                    $userDbController->addColumnsToWorkLayer($dstLayer, $aditionalColumnDefinitions);
                }

                // Create new work layer if doesn't exist
                if (!$dstLayer) {
                    $createNewLayer = true;
                    $definitions = $this->generateWorkLayerDefinitions($srcLayer->table_name);
                    $dstLayer = $this->createWorkLayer($definitions, null, $rpcParams['dst_layer'], $userDbController);
                }
            }

            // Validate source and destination layers
            if (!$srcLayer || !$dstLayer) {
                throw new MTRpcException('LAYER_NOT_FOUND', -346059);
            }

            $dstLayerStyleParams = array_merge(LayerStyles::generateDefaultStyle($dstLayer->layer_type), $rpcParams['dst_layer']['style'] ?? []);

            /**
             * @var LayerStyles $dstLayerStyle The style of the destination layer
             */
            $dstLayerStyle = LayerStyles::finder()->find('layer_id = :layer_id', [':layer_id' => $dstLayer->id]);

            // Create new non-work layer if doesn't exist - create table, update styles and mark it as existing
            if (!($dstLayer->is_exist && $userDbController->getTableNameExist($dstLayer->table_name))) {
                $createNewLayer = true;
                $userDbController->createTableForLayer($dstLayer);
                $dstLayer->is_exist = true;
                // TODO:: GPS-4321 remove this logic when remove data from UserLayers
                $dstLayer->color = 'fe60c5';
                $dstLayer->label_name = null;
                $dstLayer->border_color = 'fe60c5';
                $dstLayer->transparency = null;
                $dstLayer->border_only = null;
                $dstLayer->style = '{}';
                $dstLayer->save();

                if ($dstLayerStyle) {
                    // If the dst layer already has a style, update it
                    $dstLayerStyle->fill_color = $dstLayerStyleParams['fill_color'];
                    $dstLayerStyle->border_color = $dstLayerStyleParams['border_color'];
                    $dstLayerStyle->transparency = $dstLayerStyleParams['transparency'];
                    $dstLayerStyle->border_only = $dstLayerStyleParams['border_only'];
                    $dstLayerStyle->labels = $dstLayerStyleParams['labels'];
                    $dstLayerStyle->label_size = $dstLayerStyleParams['label_size'];
                    $dstLayerStyle->tags = $dstLayerStyleParams['tags'];
                    $dstLayerStyle->save();
                }
            }

            /** @var array $srcLayerGidsOrData The ids of the source layer items that will be copied if the layer has a physical table, otherwise the data itself */
            $srcLayerGidsOrData = $this->getLayerDataByFilters(
                [
                    'layer_id' => $srcLayer->id,
                    'layer_type' => $srcLayer->layer_type,
                    'gids' => $rpcParams['src_layer']['filters']['plot_ids'] ?? [],
                    'filter_params' => $rpcParams['src_layer']['filters'] ?? [],
                ],
                $srcLayer->hasPhysicalTable()
            );

            // Copy the items from the source layer to the destination layer
            $userDbController->copyLayerItems(
                $srcLayer,
                $dstLayer,
                $srcLayerGidsOrData,
                $rpcParams['merge_neighbour_features'] ?? false,
                $srcLayer->hasPhysicalTable()
            );

            $maxExtent = $userDbController->getMaxExtent($dstLayer->table_name);
            $maxExtent = str_replace('BOX(', '', $maxExtent);
            $maxExtent = str_replace(')', '', $maxExtent);
            $maxExtent = str_replace(',', ' ', $maxExtent);
            $maxExtentCsv = str_replace(' ', ',', $maxExtent);

            $dstLayer->extent = $maxExtent;
            $dstLayer->save();

            if (!$dstLayerStyle) {
                // If the dst layer does not have any style, create a new one
                $dstLayerStyle = new LayerStyles($dstLayerStyleParams);
                $dstLayerStyle->layer_id = $dstLayer->id;
                $dstLayerStyle->table_name = $dstLayer->table_name;
                $dstLayerStyle->save();
            }

            $this->updateLayerViews($dstLayer, $maxExtent);

            $transaction->commit();
        } catch (Exception $e) {
            $transaction->rollback();
            Sentry::logException($e);

            if ($createNewLayer && $dstLayer) {
                // Delete layer if it was created and the copy operation failed
                if (Config::LAYER_TYPE_WORK_LAYER == $dstLayer->layer_type) {
                    $dstLayer->delete();
                } else {
                    $dstLayer->is_exist = false;
                    $dstLayer->save();
                }
            }

            throw new MTRpcException('FAILED_TO_COPY_LAYER', -346062);
        }

        $LayersController = new LayersController('Layers');
        $LayersController->saveLayerPersonalization($dstLayer, $dstLayerStyle->toArray(), $this->module, $this->service_id);

        return [
            'id' => (int) $dstLayer->id,
            'extent' => $maxExtentCsv,
        ];
    }

    /**
     * Копиране на избрани обекти.
     *
     * @api-method copyLayerItems
     *
     * @param array $rpcParams
     *                         {
     *                         #item array plot_ids
     *                         {
     *                         #item integer id
     *                         }
     *                         #item array filter_data
     *                         #item string layer_name
     *                         #item integer layer_type
     *                         #item integer layer_id
     *                         #item integer layertmp_id
     *                         #item integer layertmp_type
     *                         #item array layertmp_data
     *                         {
     *                         #item array
     *                         {
     *                         #item integer layer_id
     *                         #item string  layer_table
     *                         #item integer layer_type
     *                         #item string  layer_name
     *                         #item integer farming_year
     *                         #item array style {
     *                         #item string color,
     *                         #item string border_color,
     *                         #item integer transparency,
     *                         #item boolean border_only,
     *                         #item string[] label_name,
     *                         #item boolean tags,
     *                         #item integer label_size,
     *                         }
     *                         }
     *                         }
     *
     * @throws Exception
     *
     * @return array
     */
    public function copyLayerItems($rpcParams)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbMapController = new UserDbMapController($this->User->Database);
        $LayersController = new LayersController('Layers');

        $fromTableType = $rpcParams['layer_type'];
        $toTableType = $rpcParams['layertmp_type'];

        $tablenameTo = $rpcParams['layertmp_name'];
        $tablenameFrom = in_array($fromTableType, $GLOBALS['Layers']['systemTables'])
            ? $rpcParams['layer_type']
            : $rpcParams['layer_name'];

        $farmingYear = $rpcParams['farming_year'];

        if ('new' == $rpcParams['layertmp_id']) {
            $toTableType = Config::LAYER_TYPE_WORK_LAYER;
            $layerData = $rpcParams['layertmp_data'][0] ?? null;
            $definitions = $this->generateWorkLayerDefinitions($tablenameFrom);
            $layer = $this->createWorkLayer(
                $definitions,
                null,
                $layerData
            );
            $rpcParams['layertmp_id'] = $layer->id;
        }

        $index = null;
        $id_array = [];
        $ekate = null;
        $rpcParams['filter_data']['forKvsGrid'] = true;

        if (empty($rpcParams['plot_ids']) && $rpcParams['filter_data']) {
            $id_array = $this->getLayerDataByFilters($rpcParams['filter_data'], true);
        } else {
            $id_array = $rpcParams['plot_ids'];
        }

        if (Config::LAYER_TYPE_FOR_ISAK == $toTableType || Config::LAYER_TYPE_ZP == $toTableType) {
            $dataTo = array_filter($rpcParams['layertmp_data'], function ($k) use ($rpcParams) {
                return $k['layer_id'] == $rpcParams['layertmp_id'];
            });

            if (Config::LAYER_TYPE_WORK_LAYER == $fromTableType && '' == $farmingYear) {
                $farmingYear = $LayersController->getFarmingYearByLayerID($rpcParams['layertmp_id']);
            }

            // set the key to [0]
            foreach ($dataTo as $key => $value) {
                $dataTo[0] = $dataTo[$key];
            }

            $tablenameTo = $dataTo[0]['layer_table'];
            $toTableType = $dataTo[0]['layer_type'];
            $toLayerId = $dataTo[0]['layer_id'];

            if (Config::LAYER_TYPE_FOR_ISAK == $toTableType) {
                if (!$UserDbController->getTableNameExist($tablenameTo)) {
                    $UserDbController->createTableForISAK($tablenameTo);
                }
                $index = 'gid';
            } elseif (Config::LAYER_TYPE_ZP == $toTableType) {
                if (!$UserDbController->getTableNameExist($tablenameTo)) {
                    $UserDbController->createTableZP($tablenameTo);
                }
                $index = 'id';
            }
        } elseif (Config::LAYER_TYPE_WORK_LAYER == $toTableType) {
            $options = [
                'tablename' => 'su_users_layers',
                'return' => ['table_name'],
                'whereFields' => ['id'],
                'whereValues' => [$rpcParams['layertmp_id']],
            ];

            $tablenameTo = $LayersController->getItemByParams($options);
            $tablenameTo = $tablenameTo['table_name'];
        } else {
            if (Config::LAYER_TYPE_GPS == $toTableType && null == $tablenameTo) {
                $tablenameTo = $rpcParams['layertmp_data'][0]['layer_table'];
            }
        }

        $forIsakOldExtent = null;
        if (Config::LAYER_TYPE_FOR_ISAK == $toTableType) {
            $forIsakOldExtent = $this->getExtentFromLayers($rpcParams['layertmp_id']);
        }

        $definitions = $this->getTablesDefinitions($tablenameFrom, $tablenameTo, $fromTableType, $toTableType);

        if (Config::LAYER_TYPE_FOR_ISAK == $toTableType || Config::LAYER_TYPE_ZP == $toTableType) {
            try {
                $UserDbController->copyDataFromToCustom($tablenameFrom, $tablenameTo, $fromTableType, $toTableType, $id_array, $farmingYear, $ekate, $definitions);
                $UserDbMapController->processMultiPolygons($tablenameTo, $index, $toTableType, $toLayerId);
            } catch (TException $e) {
                $regex = '/\\(prc_name|plot_name\\)=\\((?P<name>[^\\)]*)\\)/';
                preg_match_all($regex, $e->getErrorMessage(), $matches);
                $prc_name = $matches['name'][0];
                if (null != $prc_name) {
                    throw new MTRpcException('MAP_EXISTING_NAME', -33055, $prc_name);
                }

                throw new MTRpcException('DATABASE_CONNECTION_ERROR', -33101, $e->getErrorMessage());
            }
        } elseif (Config::LAYER_TYPE_WORK_LAYER == $toTableType) {
            $farmingYear = null;
            $UserDbController->copyDataFromToCustom($tablenameFrom, $tablenameTo, $fromTableType, $toTableType, $id_array, $farmingYear, $ekate, $definitions);
        } else {
            $UserDbController->copyDataFromTo($tablenameFrom, $tablenameTo, $fromTableType, $toTableType, $id_array, $ekate, $definitions);
        }

        if (Config::LAYER_TYPE_FOR_ISAK == $toTableType) {
            try {
                $maxExtent = $UserDbController->getMaxExtent($tablenameTo);
                $maxExtent = str_replace('BOX(', '', $maxExtent);
                $maxExtent = str_replace(')', '', $maxExtent);
                $maxExtentCsv = str_replace(' ', ',', $maxExtent);
                $maxExtent = str_replace(',', ' ', $maxExtent);

                $this->updateSEPPView($rpcParams['layertmp_id'], $forIsakOldExtent, $maxExtent);
                $this->updatePZPView($rpcParams['layertmp_id'], $forIsakOldExtent, $maxExtent);
            } catch (Exception $e) {
                throw new MTRpcException('DATABASE_CONNECTION_ERROR', -33101);
            }
        }

        $this->updateMapFile($tablenameTo, $rpcParams['layertmp_id']);

        $options = [
            'return' => ['t.*'],
            'where' => [
                'layer_id' => ['column' => 'id', 'compare' => '=', 'prefix' => 't', 'value' => $rpcParams['layertmp_id']],
            ],
        ];
        $layer_results = $LayersController->getLayers($options, false, false);

        return [
            'extent' => $maxExtentCsv,
            'from_layer_name' => $tablenameFrom,
            'to_layer_name' => $tablenameTo,
            'layertmp_id' => $rpcParams['layertmp_id'],
            'layertmp_type' => $toTableType,
            'farming_id' => $layer_results[0]['farming'],
            'year_id' => $layer_results[0]['year'],
        ];
    }

    /**
     * @param $definitions array
     * @param null|string $tableName
     * @param null|mixed $layerData = array[
     *                              layer_type => string|int,
     *                              layer_name => string,
     *                              layer_farming => int,
     *                              farming_year => int
     *                              style => array[
     *                              ]
     *                              additional_columns: string[]
     *                              ]
     */
    public function createWorkLayer(array $definitions, $tableName = null, $layerData = null, UserDbController $UserDbController = null): UserLayers
    {
        foreach ($definitions as $definition) {
            UserLayers::validateDefinition($definition);
        }

        // Use the passed $UserDbController in case the method is called inside a transaction block or create new instance
        $UserDbController ??= new UserDbController($this->User->Database);
        $LayersController = new LayersController('Layers');

        if (!$tableName) {
            $tableName = 'layer_work_' . time();
        }

        if (isset($layerData['columns']) && count($layerData['columns']) > 0) {
            $defaultDefinitions = UserLayers::getDefinitionsByType(Config::LAYER_TYPE_WORK_LAYER);
            $defaultCategories = array_column($defaultDefinitions, 'col_category');

            // Filter columns that are going to be created - only the default columns and the specified ones
            foreach ($definitions as $key => $definition) {
                if (
                    in_array($definition['col_category'], $defaultCategories)
                    || in_array($definition['col_name'], $layerData['columns'])
                ) {
                    continue;
                }
                unset($definitions[$key]);
            }

            $definitions = array_values($definitions);
        }

        $layerName = isset($layerData['layer_name']) && count($layerData['layer_name']) > 0
            ? $layerData['layer_name']
            : $LayersController->getNextWorkLayerName($this->User->GroupID);

        try {
            $workLayer = new UserLayers();
            $workLayer->name = $layerName;
            $workLayer->user_id = $this->User->UserID;
            $workLayer->extent = Config::DEFAULT_MAX_EXTENT;
            $workLayer->table_name = $tableName;
            $workLayer->layer_type = Config::LAYER_TYPE_WORK_LAYER;
            $workLayer->group_id = $this->User->GroupID;
            $workLayer->is_exist = true;
            $workLayer->definitions = json_encode($definitions);
            $workLayer->date_created = 'now()';

            // TODO:: GPS-4321 remove this logic when remove data from UserLayers
            $workLayer->color = 'fe60c5';
            $workLayer->label_name = null;
            $workLayer->border_color = 'fe60c5';
            $workLayer->transparency = null;
            $workLayer->border_only = null;
            $workLayer->style = '{}';

            if (isset($layerData['layer_farming'], $layerData['farming_year'])) {
                $workLayer->farming = $layerData['layer_farming'];
                $workLayer->year = $layerData['farming_year'];
            }

            $UserDbController->createTableForLayer($workLayer);
            $workLayer->save();

            $styleData = array_merge(LayerStyles::generateDefaultStyle($workLayer->layer_type), $layerData['style'] ? $layerData['style'] : []);
            /**  @var LayerStyles $style  */
            $style = new LayerStyles($styleData);
            $style->layer_id = $workLayer->id;
            $style->table_name = $workLayer->table_name;
            $style->save();
        } catch (Exception $e) {
            if ($workLayer->id) {
                $workLayer->delete();
            }

            throw new Exception($e->getMessage(), $e->getCode());
        }

        return $workLayer;
    }

    /**
     * Пресичане на слоеве.
     *
     * @api-method saveClipping
     *
     * @param array $rpcParams
     *                         {
     *                         #item array layerDataA
     *                         {
     *                         #item string  id_name
     *                         #item integer layer_id
     *                         #item integer layer_type
     *                         #item string  name
     *                         #item boolean selected
     *                         #item string  value
     *                         }
     *                         #item array layerDataB
     *                         {
     *                         #item string  id_name
     *                         #item integer layer_id
     *                         #item integer layer_type
     *                         #item string  name
     *                         #item boolean selected
     *                         #item string  value
     *                         }
     *                         #item string getSelectedA
     *                         #item string getSelectedB
     *                         #item array featuresGidA
     *                         {
     *                         #item integer id
     *                         }
     *                         #item array featuresGidB
     *                         {
     *                         #item integer id
     *                         }
     *                         }
     *
     * @throws MTRpcException
     *
     * @return array|bool
     */
    public function saveClipping($rpcParams)
    {
        $UserDbMapController = new UserDbMapController($this->User->Database);
        $UsersController = new UsersController('Users');
        if (0 == count($rpcParams['layerDataA']) || 0 == count($rpcParams['layerDataB'])) {
            return false;
        }

        $aLayerDataA = [];
        $aLayerDataB = [];

        // array filter and to set the key [0]
        foreach ($rpcParams['layerDataA'] as $key => $value) {
            if ($value['value'] == $rpcParams['getSelectedA']) {
                $aLayerDataA[0] = $value;
            }
        }

        if (!count($rpcParams['featuresGidA'])) {
            $layerData = $rpcParams['layerDataA'][0];
            $rpcParams['featuresGidA'] = $this->getLayerDataByFilters(
                [
                    'layer_id' => $layerData['layer_id'],
                    'layer_type' => $layerData['layer_type'],
                    'filter_params' => $rpcParams['filter_params'] ?? [],
                ],
                true
            );
        }

        foreach ($rpcParams['layerDataB'] as $key => $value) {
            if ($value['value'] == $rpcParams['getSelectedB']) {
                $aLayerDataB[0] = $value;
            }
        }

        if (0 == count($aLayerDataA) || 0 == count($aLayerDataB)) {
            return false;
        }

        $tableA = $aLayerDataA[0]['value'];
        $tableB = $aLayerDataB[0]['value'];

        if ($tableB == $GLOBALS['Layers']['vpsTables'][Config::LAYER_TYPE_VPS_PASISHTA]) {
            // this table is used when selecting (or clipping) objects from the merg layer
            $tableB = 'layer_merg_selection';
        }

        $idNameA = $aLayerDataA[0]['id_name'];
        $idNameB = $aLayerDataB[0]['id_name'];

        $layerTypeA = $aLayerDataA[0]['layer_type'];
        $layerIdA = $rpcParams['layerDataA'][0]['layer_id'];

        if (Config::LAYER_TYPE_FOR_ISAK == $rpcParams['layerDataA']['layer_type']) {
            $forIsakOldExtent = $this->getExtentFromLayers($rpcParams['layerDataA']['layer_id']);
        }

        // Изрязване
        if (Config::OPERATION_CUT == $rpcParams['operation']) {
            $transaction = $UserDbMapController->startTransaction();

            try {
                $UserDbMapController->processCutClipping($tableA, $tableB, (object)$rpcParams, $idNameA, $idNameB, $layerTypeA, $layerIdA);

                $UserDbMapController->processMultiPolygons($tableA, $idNameA, $layerTypeA, $layerIdA);

                $UserDbMapController->removeSmallAndInvalidPolygons($tableA, $idNameA);
                $transaction->commit();

                $return = $this->commonUpdateLayerProjectionAndMapFile($tableA, $aLayerDataA[0]['layer_id']);
            } catch (Exception $e) {
                $transaction->rollBack();

                throw new MTRpcException('layer_clipping_unsuccessfull_operation_cut: ' . $e->getMessage(), -33057);
            }
        }

        // Разцепване
        if (Config::OPERATION_SPLIT == $rpcParams['operation']) {
            $transaction = $UserDbMapController->startTransaction();

            try {
                $UserDbMapController->processSplitClipping($tableA, $tableB, (object)$rpcParams, $idNameA, $idNameB, $layerTypeA, $layerIdA);

                $UserDbMapController->removeGeometryCollection($tableA);

                $UserDbMapController->processMultiPolygons($tableA, $idNameA, $layerTypeA, $layerIdA);

                $UserDbMapController->removeSmallAndInvalidPolygons($tableA, $idNameA);

                $transaction->commit();

                $return = $this->commonUpdateLayerProjectionAndMapFile($tableA, $aLayerDataA[0]['layer_id']);
            } catch (Exception $e) {
                $transaction->rollBack();

                throw new MTRpcException('layer_clipping_unsuccessfull_operation_split: ' . $e->getMessage(), -33058);
            }
        }

        // Отрязване
        if (Config::OPERATION_DELETE == $rpcParams['operation']) {
            $transaction = $UserDbMapController->startTransaction();

            try {
                $UserDbMapController->processDeleteClipping($tableA, $tableB, (object)$rpcParams, $idNameA, $idNameB, $layerTypeA, $layerIdA);

                $UserDbMapController->processMultiPolygons($tableA, $idNameA, $layerTypeA, $layerIdA);
                $UserDbMapController->removeSmallAndInvalidPolygons($tableA, $idNameA);
                $transaction->commit();

                $return = $this->commonUpdateLayerProjectionAndMapFile($tableA, $aLayerDataA[0]['layer_id']);
            } catch (Exception $e) {
                $transaction->rollBack();

                throw new MTRpcException('layer_clipping_unsuccessfull_operation_delete: ' . $e->getMessage(), -33059);
            }
        }

        if (Config::LAYER_TYPE_FOR_ISAK == $rpcParams['layerDataA']['layer_type']) {
            try {
                $transaction = $UsersController->startTransaction();
                $this->updateSEPPView($rpcParams['layerDataA']['layer_id'], $forIsakOldExtent, $return['extent']);
                $this->updatePZPView($rpcParams['layerDataA']['layer_id'], $forIsakOldExtent, $return['extent']);
                $transaction->commit();
            } catch (Exception $e) {
                $transaction->rollBack();

                throw new MTRpcException('layer_clipping_unsuccessfull_copy_for_isak', -33060);
            }
        }

        return $return;
    }

    public function commonUpdateLayerProjectionAndMapFile($table, $layer_id)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        $LayersController = new LayersController('Layers');
        $return = [];

        // update layer projection to database default 32635
        $UserDbController->updateLayerProjection($table);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['table' => $table, 'layer_id' => $layer_id], [], 'Update layer projection');

        $this->updateMapFile($table, $layer_id);

        $options = [
            'return' => [
                'f.name as farming_name', 'f.*', 't.*',
            ],
            'where' => [
                'layer_id' => ['column' => 'id', 'compare' => '=', 'prefix' => 't', 'value' => $layer_id],
            ],
        ];
        $layer_results = $LayersController->getLayers($options, false, false);

        if (0 == count($layer_results)) {
            $return['error'] = true;
        } else {
            $return = [
                'extent' => str_replace(' ', ', ', $layer_results[0]['extent']),
                'layer_name' => $table,
                'layer_id' => $layer_id,
            ];
        }

        return $return;
    }

    /**
     * Основен метод, който генерира тематичната карта, спрямо тип на карта и ЕКАТТЕ
     * Типове тематични карти:
     * 1 - по собственик
     * 2 - по арендатор
     * 3 - по споразумение
     * 4 - по категория
     * 5 - по НТП
     * 6 - по тип собственост
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer type
     *                         #item string ekate
     *                         }
     *
     * @return array {
     *               #item array rows
     *               {
     *               #item string  color
     *               #item double  area
     *               #item integer number
     *               #item string  ime_subekt
     *               }
     *               #item integer total
     *               }
     */
    public function initTopicLayer($rpcParams)
    {
        $type = (int)$rpcParams['type'];
        $return = null;
        switch ($type) {
            case LayersController::LAYER_COLORING_BY_OWNERS_NAME:
                $return = $this->initTopicLayerByOwnerName($rpcParams['ekate'], $rpcParams['selected']);

                break;
            case LayersController::LAYER_COLORING_BY_TENANTS_NAME:
                $return = $this->initTopicLayerByTenantName($rpcParams['ekate'], $rpcParams['selected']);

                break;
            case LayersController::LAYER_COLORING_BY_AGREEMENT:
                $return = $this->initTopicLayerByAgreement($rpcParams['ekate'], $rpcParams['selected']);

                break;
            case LayersController::LAYER_COLORING_BY_CATEGORY:
                $return = $this->initTopicLayerByCategory($rpcParams['ekate'], $rpcParams['selected']);

                break;
            case LayersController::LAYER_COLORING_BY_NTP:
                $return = $this->initTopicLayerByNTP($rpcParams['ekate'], $rpcParams['selected']);

                break;
            case LayersController::LAYER_COLORING_BY_OWNERSHIP:
                $return = $this->initTopicLayerByOwnership($rpcParams['ekate'], $rpcParams['selected']);

                break;
            default:
                $return = ['rows' => [], 'total' => 0];

                break;
        }

        return $return;
    }

    /**
     * @api-method calculateAvgSlopeByGeoJson
     * Calculates average slope for each plot in the plots_geojson FeatureCollection
     * and returns the same FeatureCollection with added new property 'slope' to each feature
     *
     * @param array $rpcParams
     *                         {
     *                         #item object plots_geojson (FeatureCollection)
     *                         }
     *
     * @return array
     */
    public function calculateAvgSlopeByGeoJson($rpcParams = [])
    {
        $plotsGeoJson = $rpcParams['plots_geojson'];
        $usersController = new UsersController('Users');

        if (!$this->User->HasSlopeRights) {
            throw new MTRpcException('NO_RIGHTS', 499);
        }

        if (empty($plotsGeoJson['features'])) {
            throw new MTRpcException('AVG_SLOPE_CALCULATION_ERROR', -33957);
        }

        $commonServicesModule = Prado::getApplication()->getModule('commonServicesModule');
        $slopeFile = getenv('SLOPE_FILE');
        $plotsAvgSlopeGeoJson = $commonServicesModule->calculateAvgSlope($plotsGeoJson, $slopeFile);
        $slopeColumn = LayerWork::$slopeColumn;

        /*
         * @var array $plotsAvgSlope
         * {
         *      #item float slope
         * }
         */
        $plotsAvgSlopeGeoJson['features'] = array_map(function ($feature) use ($slopeColumn) {
            if (!isset($feature['properties'][$slopeColumn])) {
                throw new MTRpcException('AVG_SLOPE_CALCULATION_ERROR', -33957);
            }

            $feature['properties'][$slopeColumn] = number_format($feature['properties'][$slopeColumn], 1);

            return $feature;
        }, $plotsAvgSlopeGeoJson['features'] ?? []);

        $usersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $rpcParams, $plotsAvgSlopeGeoJson, 'Calculating avg slope by geojson');

        return $plotsAvgSlopeGeoJson;
    }

    /**
     * Метод, който прави refresh на материализираните views на тематичните карти.
     */
    public function refreshTopicLayerKVSViews()
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbController->DbHandler->refreshTopicLayerKVSViews();
    }

    public function updateIsExistEmptyTable($tablename, $layer_id)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $LayersController = new LayersController('Layers');

        $options = [
            'tablename' => $tablename,
            'return' => ['count(*)'],
        ];

        $rowCount = $UserDbController->getItemsByParams($options);
        if (0 == $rowCount[0]['count']) {
            $optionsUp = [
                'tablename' => 'su_users_layers',
                'id' => $layer_id,
                'mainData' => [
                    'is_exist' => false,
                ],
            ];

            $LayersController->editItem($optionsUp);
        }
    }

    public function getCombinedGeometry($replacePolygonIdArray, $replacePolygonIdName, $layerName)
    {
        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'tablename' => $layerName,
            'return' => [
                "CASE st_geometrytype (
                            st_union (st_buffer(geom, 0.0009))
                        )
                    WHEN 'ST_Polygon' THEN
                        st_astext (
                            st_buffer (
                                st_union (st_buffer(geom, 0.0009)),
                                - 0.001
                            )
                        )
                    ELSE
                        st_astext (
                            st_union (st_buffer(geom, 0.0009))
                        )
                    END AS geometry",
            ],
            'where' => [
                'gid' => ['column' => $replacePolygonIdName, 'compare' => 'IN', 'value' => $replacePolygonIdArray],
            ],
        ];

        $results = $UserDbController->getItemsByParams($options, false, false);

        return $results[0]['geometry'];
    }

    /**
     * @return array
     */
    public function clippingKVSByNTPAndUpdateAreas($params)
    {
        if (empty($params['ekate']) || empty($params['ntp'])) {
            throw new RuntimeException('Missing mandatory parameter(s)');
        }
        $UserDbMapController = new UserDbMapController($this->User->Database);
        $updatedPlots = $UserDbMapController->clippingKVSByNTPAndUpdateAreas($params['ekate'], $params['ntp']);

        return [
            'updatedPlots' => $updatedPlots ? $updatedPlots : 0,
        ];
    }

    /**
     * @throws PHPExcel_Exception
     * @throws PHPExcel_Reader_Exception
     * @throws PHPExcel_Writer_Exception
     * @throws TDbException
     *
     * @return string
     */
    public function clippingWithKVSAndExportExcel($params)
    {
        if (empty($params['layerA']) || empty($params['layerType'])) {
            throw new RuntimeException('Missing mandatory parameter(s)');
        }
        $layerA = UserLayers::getLayerByTableName($params['layerA'], $this->getUser()->GroupID);
        if (empty($layerA)) {
            throw new RuntimeException('Layer not found');
        }
        $layerAColumnName = $layerA->getDefinitionByCategory(Config::LAYER_COLUMN_CATEGORY_NAME);
        $return = ['tb.ekate', 'tb.kad_ident', 'tb.masiv', 'tb.number', 'tb.virtual_category_title as category', 'tb.virtual_ntp_title as ntp', 'tb.document_area', "ta.{$layerAColumnName['col_name']} as plot_name"];
        $plotName = $GLOBALS['Layers']['labelNames'][$params['layerType']][$layerAColumnName['col_name']];

        $layerAcolumns = array_filter($layerA->getDefinitions(), function ($column) {
            return $column['col_virtual'] && in_array($column['col_name'], ['virtual_area_dka', 'virtual_crop_name', 'area']);
        });

        $return = array_merge($return, array_map(function ($column) {
            return "ta.{$column['col_name']}";
        }, $layerAcolumns));

        $UserDbMapController = new UserDbMapController($this->User->Database);
        $intersectedPlots = $UserDbMapController->clippingLayerAndGetIntersection($layerA, 'layer_kvs', $return);

        if (!$intersectedPlots) {
            return '';
        }

        $headers = array_merge([
            'ekate' => 'ЕКАТТЕ',
            'kad_ident' => 'Идентификатор',
            'masiv' => 'Масив',
            'number' => 'Имот',
            'category' => 'Категория',
            'ntp' => 'НТП',
            'document_area' => 'Обща площ на имота(дка)',
            'intersec_area' => 'Площ на сечение(дка)',
            'plot_name' => $plotName,
        ], ...array_map(function ($column) use ($params) {
            return [$column['col_name'] => $GLOBALS['Layers']['labelNames'][$params['layerType']][$column['col_name']]];
        }, $layerAcolumns));
        /** @var ExportToExcelClass $exportExcelDoc */
        $exportExcelDoc = new ExportToExcelClass();
        $exportExcelDoc->export($intersectedPlots, $headers, [], 0, []);

        $fileName = 'intersect_kvs_' . date('Y-m-d-H-i-s') . '.xlsx';
        $path = PUBLIC_UPLOAD_EXPORT . '/' . $this->User->GroupID . '/';

        if (!is_dir($path)) {
            mkdir($path);
        }
        $exportExcelDoc->saveFile($path . $fileName);

        return PUBLIC_UPLOAD_EXPORT_RELATIVE_PATH . '/' . $this->User->GroupID . '/' . $fileName;
    }

    /**
     * @throws MTRpcException
     * @throws Exception
     *
     * @return array
     */
    public function splitPolygon($params)
    {
        try {
            $spliTool = new KAISSplit();

            return $spliTool->getGeometryInfo($params['geoms'], $params['gid']);
        } catch (MTRpcException $ex) {
            $spliTool = new DFZSplit();

            return $spliTool->getGeometryInfo($params['geoms'], $params['gid']);
        } catch (Exception $ex) {
            throw new MTRpcException('SPLIT_TOOL_UNSUCCESSFUL_OPERATION_AUTOMATIC_SPLIT', -33064);
        }
    }

    public function getReportForRequestedCadastralPlots($params)
    {
        $dfz = new DFZCadastralPlots($params['kad_ident']);

        return $dfz->getPlotData();
    }

    /**
     * @param array $params
     *                      [
     *                      'layer_id' => int|string,
     *                      'layer_type' => int|string,
     *                      'gids' => int[],
     *                      'filter_params' => array
     *                      ]
     */
    public function getLayerDataByFilters(array $params, bool $returnOnlyIds = false): array
    {
        $layerType = $params['layer_type'];
        $result = [];
        switch ($layerType) {
            case Config::LAYER_TYPE_KVS :
                $plotsTree = new PlotsTree($this->rpcServer);
                $params['forKvsGrid'] = true;
                $result = $plotsTree->getPlots($params, null, null, '', '', false, $returnOnlyIds, true);

                break;

            case Config::LAYER_TYPE_ISAK :
                $isakGrid = new ISAKGrid($this->rpcServer);
                $result = $isakGrid->getISAKGrid($params);
                $result = $returnOnlyIds ? $result['filtered_gids'] : $result['rows'];

                break;
            case Config::LAYER_TYPE_FOR_ISAK :
                $forIsakGrid = new ForIsakGrid($this->rpcServer);
                $result = $forIsakGrid->getForIsakGrid($params);
                $result = $returnOnlyIds ? $result['filtered_gids'] : $result['rows'];

                break;
            case Config::LAYER_TYPE_ZP :
                $zpGrid = new ZPGrid($this->rpcServer);
                $result = $zpGrid->getZPGrid($params);
                $result = $returnOnlyIds ? $result['filtered_gids'] : $result['rows'];

                break;
            case Config::LAYER_TYPE_KMS :
                $kmsGrid = new KmsGrid($this->rpcServer);
                $result = $kmsGrid->getKmsGrid($params);
                $result = $returnOnlyIds ? $result['filtered_gids'] : $result['rows'];

                break;
            case Config::LAYER_TYPE_WORK_LAYER :
                $workLayerGrid = new WorkLayerGrid($this->rpcServer);
                $result = $workLayerGrid->getWorkLayerGrid($params);
                $result = $returnOnlyIds ? $result['filtered_gids'] : $result['rows'];

                break;
            case Config::LAYER_TYPE_GPS :
                $gpsGrid = new GpsGrid($this->rpcServer);
                $result = $gpsGrid->getGpsGrid($params);
                $result = $returnOnlyIds ? $result['filtered_gids'] : $result['rows'];

                break;
            case Config::LAYER_TYPE_ALLOWABLE_FINAL:
            case Config::LAYER_TYPE_PHYSICAL_BLOCKS :
                $allowableGrid = new AllowableGrid($this->rpcServer);
                $result = $allowableGrid->getAllowableGrid($params);
                $result = $returnOnlyIds ? $result['filtered_gids'] : $result['rows'];

                break;
            case Config::LAYER_TYPE_PERMANETELY_GREEN_AREAS:
            case Config::LAYER_TYPE_LFA:
            case Config::LAYER_TYPE_NATURA_2000:
            case Config::LAYER_TYPE_VPS_PASISHTA:
            case Config::LAYER_TYPE_VPS_GASKI_CHERVENOGUSHI:
            case Config::LAYER_TYPE_VPS_GASKI_ZIMNI:
            case Config::LAYER_TYPE_VPS_LIVADEN_BLATAR:
            case Config::LAYER_TYPE_VPS_ORLI_LESHOYADI:
            case Config::LAYER_TYPE_DS_PRC:
            case Config::LAYER_TYPE_LANDSCAPE_ELEMENTS_POLYGONS:
            case Config::LAYER_TYPE_PERMANENT_GRASSLAND_FOR_MOWING:
            case Config::LAYER_TYPE_PERMANENT_GRASSLAND_FOR_MOWING_PRELIMINARY:
                $remoteDataGrid = new RemoteLayerGrid($this->rpcServer);
                $result = $remoteDataGrid->getRemoteLayerGrid($params);
                $result = $returnOnlyIds ? $result['filtered_gids'] : $result['rows'];

                break;
            case Config::LAYER_TYPE_CSD:
                $csdGrid = new CsdGrid($this->rpcServer);
                $result = $csdGrid->getCsdGrid($params);
                $result = $returnOnlyIds ? $result['filtered_gids'] : $result['rows'];

                break;
            case Config::LAYER_TYPE_CADASTRE:
                $cadastreGrid = new LayerCadastre($this->rpcServer);
                $result = $cadastreGrid->getData($params);

                $result = $returnOnlyIds ? $result['filtered_gids'] : $result['rows'];

                break;
            default:
                $result = [];
        }

        return $result;
    }

    public function getLayerColoringLegend(array $rpcParams)
    {
        $layer = UserLayers::getLayerById($rpcParams['layer_id']);
        $ekatte = $rpcParams['ekatte'];

        $userDbController = new UserDbController($this->User->Database);

        return $userDbController->getLayerColoringLegend($layer, $ekatte);
    }

    public function formatAndTranslateColumns(array $inputArray, array $definitions): array
    {
        $defColumns = [
            'name' => 'Име',
            'area' => 'Площ (дка)',
        ];

        array_unshift($inputArray, 'name', 'area');
        $inputArray = array_unique($inputArray);

        $columns = [];
        foreach ($inputArray as $col) {
            if ('gid' != $col && 'st_astext' != $col && 'geom' != $col) {
                if (array_key_exists($col, $definitions) && !$definitions[$col]['col_visible']) {
                    continue;
                }

                $columns['fields'][] = $col;
                $columns['titles'][] = array_key_exists($col, $definitions)
                    ? $definitions[$col]['col_title']
                    : (array_key_exists($col, $defColumns) ? $defColumns[$col] : $col);
            }
        }

        return array_combine($columns['fields'], $columns['titles']);
    }

    private function deleteLayerAttributeData($tableName, $idName, $idString)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        if (0 == strlen($idString)) {
            throw new Exception('No id string provided for deleting layer attribute data');
        }

        $options = [
            'tablename' => $tableName,
            'id_name' => $idName,
            'id_string' => $idString,
        ];

        $UserDbController->deleteItemsByParams($options);
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['request_params' => $options], [], 'Replacing polygon variables');
    }

    private function findExistingPlotNumbers($plots)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $inputNumbers = [];
        $count = count($plots);
        for ($i = 0; $i < $count; $i++) {
            $inputNumbers[] = $plots[$i]['number'];
        }

        $options = [
            'tablename' => $UserDbController->DbHandler->tableKVS,
            'return' => ['number::numeric'],
            'where' => [
                'ekate' => ['column' => 'ekate', 'compare' => '=', 'value' => $plots[0]['ekate']],
                'masiv' => ['column' => 'masiv', 'compare' => '=', 'value' => $plots[0]['masiv']],
                'is_edited' => ['column' => 'is_edited', 'compare' => '=', 'value' => false],
                'number' => ['column' => 'number', 'compare' => 'IN', 'value' => $inputNumbers],
            ],
        ];

        $results = $UserDbController->getItemsByParams($options);
        $resultsCount = count($results);
        $existNums = [];
        for ($i = 0; $i < $resultsCount; $i++) {
            $existNums[] = $results[$i]['number'];
        }

        return $existNums;
    }

    private function updateMapFile($tablename, $layer_id)
    {
        $LayersController = new LayersController('Layers');

        $layer = UserLayers::getLayerById($layer_id);
        $LayersController->updateLayerExtent($layer);

        $options = [];
        $options['database'] = $this->User->Database;
        $options['user_id'] = $this->User->GroupID;
        $LayersController->generateMapFile($options);
    }

    private function updateSEPPView($forIsakLayerId, $forIsakOldExtent, $forIsakNewExtent)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $forIsakNewExtent = str_replace(',', '', $forIsakNewExtent);
        $seppView = 'sepp_for_isak_' . $forIsakLayerId;
        if (!$UserDbController->getViewNameExists($seppView)) {
            $UserDbController->createSEPPReportView($forIsakLayerId);
        } elseif ($forIsakOldExtent != $forIsakNewExtent) {
            if (DEFAULT_DB_VERSION >= 9.3) {
                $UserDbController->refreshView($seppView);
            } else {
                $UserDbController->refreshSEPPReportFakeView($forIsakLayerId);
            }
        }
    }

    private function updatePZPView($forIsakLayerId, $forIsakOldExtent, $forIsakNewExtent)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $forIsakNewExtent = str_replace(',', '', $forIsakNewExtent);
        $pzpView = 'pzp_for_isak_' . $forIsakLayerId;
        if (!$UserDbController->getViewNameExists($pzpView)) {
            $UserDbController->createPZPReportView($forIsakLayerId);
        } elseif ($forIsakOldExtent != $forIsakNewExtent) {
            if (DEFAULT_DB_VERSION >= 9.3) {
                $UserDbController->refreshView($pzpView);
            } else {
                $UserDbController->refreshPZPReportFakeView($forIsakLayerId);
            }
        }
    }

    private function getExtentFromLayers($layerId)
    {
        $LayersController = new LayersController('Layers');

        $data = $LayersController->getLayers([
            'return' => ['extent'],
            'where' => [
                'table_name' => ['column' => 't.id', 'compare' => '=', 'value' => $layerId],
            ],
        ]);

        return $data[0]['extent'];
    }

    private function onGeomUpdate($data)
    {
        if (Config::LAYER_TYPE_FOR_ISAK == $data['layer_type']) {
            $this->onLayerForIsakGeomUpdate($data);
        }
    }

    private function onLayerForIsakGeomUpdate($data)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbMapController = new UserDbIsakController($this->User->Database);
        $UserDbForIsakController = new UserDbForIsakController($this->User->Database);

        $options = [
            'tablename' => $data['tablename'],
            'return' => ['ST_Extent(geom) as extent'],
        ];

        $extent_result = $UserDbController->getItemsByParams($options);

        // get FOR ISAK extent and transform it
        $maxextent = $extent_result[0]['extent'];
        $maxextent = str_replace('BOX(', '', $maxextent);
        $maxextent = str_replace(')', '', $maxextent);
        $maxextent = str_replace(' ', ',', $maxextent);

        if (Config::TOOL_OPERATION_EDIT_GEOMETRY == $data['tool_operation']) {
            $options = [
                'tablename' => $data['tablename'],
                'return' => [
                    $data['features'][0]['id_name'],
                    'vps_type',
                ],
                'where' => [
                    $data['features'][0]['id_name'] => ['column' => 'gid', 'compare' => '=', 'value' => $data['features'][0]['id']],
                ],
                'extent' => $maxextent,
                'dblink_where' => "geom && ST_MakeEnvelope({$maxextent})",
            ];
            $results = $UserDbController->getItemsByParams($options, false);
            switch ($results[0]['vps_type']) {
                case 1:
                    $vps_table = $GLOBALS['Layers']['vpsTables'][Config::LAYER_TYPE_VPS_PASISHTA];

                    break;
                case 2:
                    $vps_table = $GLOBALS['Layers']['vpsTables'][Config::LAYER_TYPE_VPS_GASKI_CHERVENOGUSHI];

                    break;
                case 3:
                    $vps_table = $GLOBALS['Layers']['vpsTables'][Config::LAYER_TYPE_VPS_GASKI_ZIMNI];

                    break;
                case 4:
                    $vps_table = $GLOBALS['Layers']['vpsTables'][Config::LAYER_TYPE_VPS_LIVADEN_BLATAR];

                    break;
                case 5:
                    $vps_table = $GLOBALS['Layers']['vpsTables'][Config::LAYER_TYPE_VPS_ORLI_LESHOYADI];

                    break;
                default:
                    $vps_table = null;

                    break;
            }

            if (null != $vps_table) {
                $options['return'] = [
                    $data['features'][0]['id_name'],
                    'vps_type',
                    'round(area::numeric/1000, 3) as area',
                    'round(outside_area::numeric/1000, 3) as outside_area',
                    '(round(area::numeric/1000, 3) - round(outside_area::numeric/1000, 3)) AS inside_area',
                ];
                $results = $UserDbForIsakController->getForIsakDiffVPSData($options, $vps_table, false, false);
                $updateOptions = [
                    'tablename' => $options['tablename'],
                    'gid' => $data['features'][0]['id'],
                    'area' => $results[0]['area'],
                    'inside_area' => $results[0]['inside_area'],
                ];
                $update = $UserDbForIsakController->updateForIsakDiffVPSArea($updateOptions, false, false);
            }
        }
    }

    private function addWhere($ekate, $filter, $keyField = 'egn_subekt')
    {
        $egn_subekts = [];
        $where = "ekate = '{$ekate}'";

        if (!empty($filter)) {
            foreach ($filter as $ownerData) {
                $ownerData = json_decode($ownerData, true);
                $egn_subekts[] = "'" . strip_tags($ownerData[$keyField]) . "'";
            }

            $where .= ' AND ' . $keyField . ' IN (' . implode(',', $egn_subekts) . ')';
        }

        return $where;
    }

    private function addCategoryWhere($ekate, $filter, $keyField = 'ime_subekt')
    {
        $params = [];
        $where = "ekate = '{$ekate}'";
        if (empty($filter)) {
            return $where;
        }
        $emptyCategory = false;
        foreach ($filter as $data) {
            $data = json_decode($data, true);
            $value = $this->transformCategoryToNumeral($data[$keyField]);
            if (null == $value) {
                $where .= "AND ( {$keyField} IS NULL OR {$keyField} = '0' OR {$keyField} = '' ";
                $emptyCategory = true;

                continue;
            }
            $params[] = "'" . strip_tags($value) . "'";
        }
        if (!empty($params)) {
            $where .= " OR {$keyField} IN (" . implode(',', $params) . ')';
        }
        if ($emptyCategory) {
            $where .= ')';
        }

        return $where;
    }

    private function initTopicLayerByOwnerName($ekate, $filter)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $LayersController = new LayersController('Layers');
        $where = $this->addWhere($ekate, $filter);
        // Main grid query
        // Gets all 50 owners, ordered by total owned area
        $map_info_query = "SELECT gid, area,egn_subekt,ime_subekt,number,ekate from topic_layer_kvs_by_owner_name_mat_view where {$where} order by area desc limit 50";
        // Helper variables
        $totalArea = 0;
        $totalNumber = 0;
        // The results of the main grid query
        $results = $UserDbController->DbHandler->getDataByQuery($map_info_query);
        $resultsCount = count($results);
        // This variable $connection_string is used when creating the map files, so the
        // template could use the PHP constants for the DB Connection,
        // as well as the database name from the $User object property.
        $connection_string = "CONNECTION 'host=" . DEFAULT_DB_HOST . ' dbname=' . $this->User->Database . ' user=' . DEFAULT_DB_USERNAME . ' password=' . DEFAULT_DB_PASSWORD . ' port=' . DEFAULT_DB_PORT . "'";
        // The $map_results array will be passed to the map file template
        // it gets the results before the additional processing of the data
        // because the processing is done only for visual purposes
        // and the 'area' parameter should not be truncated to only 3 digits
        // when it is passed to the map template. This ensures the proper expressions
        // will be created and matched in the map file
        $map_results = ['results' => $results, 'connection_string' => $connection_string];
        // Main results processing, to get user friendly texts, numbers and colors for the grid cells
        for ($i = 0; $i < $resultsCount; $i++) {
            // Get the color from the predefined set inside the Layers config file
            $results[$i]['color'] = $GLOBALS['Layers']['colors'][$i]['hex'];
            $results[$i]['area'] = number_format($results[$i]['area'], 3, '.', ' ');
            $results[$i]['number'] = number_format($results[$i]['number'], 0, '.', ' ');
            $results[$i]['ime_subekt'] = null == $results[$i]['ime_subekt'] ? 'гражданин' : $results[$i]['ime_subekt'];
            $totalArea += $results[$i]['area'];
            $totalNumber += $results[$i]['number'];
        }
        // Replace the last result of the query with the total values of the rest of the results with an additional query
        if ($resultsCount > 49) {
            $map_info_query = "SELECT sum(area) as area, sum(number) as number from topic_layer_kvs_by_owner_name_mat_view where ekate = '{$ekate}' ";
            $rest_of_the_results = $UserDbController->DbHandler->getDataByQuery($map_info_query);
            $results[49]['area'] = number_format($rest_of_the_results[0]['area'] - $totalArea, 3, '.', ' ');
            $results[49]['ime_subekt'] = 'Останали собственици';
            $results[49]['number'] = number_format($rest_of_the_results[0]['number'] - $totalNumber, 0, '.', ' ');
        }
        // This method will get the map extent of the current topic map
        $extent = $UserDbController->getMaxExtentForEkate($UserDbController->DbHandler->tableKVS, $ekate);
        if (!$extent) {
            $extent = $UserDbController->getMaxExtent($UserDbController->DbHandler->tableKVS);
        }
        // Replace and remove all the unnecessary strings so to get only the map coordinates for the extent of the map
        $extent = str_replace('BOX(', '', $extent);
        $extent = str_replace(')', '', $extent);
        $extent = str_replace(',', ' ', $extent);
        // Set the extent as new parameter of the $map_resutls array and pass it to the map file template
        $map_results['extent'] = $extent;
        $map_results['ekate'] = $ekate;
        // This will create the map file with the new data and template and then return it as a text string
        $mapString = $LayersController->StringHelper->loadTemplate($GLOBALS['Templates'][31]['template'], $map_results);
        // Save the map file string to a specific map file.
        $mapFile = fopen(WMS_MAP_PATH . '/' . $this->User->GroupID . '_topic_layer_kvs.map', 'w');
        fwrite($mapFile, $mapString);
        fclose($mapFile);
        if ($resultsCount > 0) {
            $results[0]['extent'] = str_replace(' ', ', ', $extent);
        }

        // return the results for the grid
        return ['rows' => $results, 'total' => $resultsCount];
    }

    private function initTopicLayerByTenantName($ekate, $filter)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $LayersController = new LayersController('Layers');
        $where = $this->addWhere($ekate, $filter);
        // Main grid query
        // Gets all 50 tenants, ordered by total owned area
        $map_info_query = "SELECT gid, area,egn_subekt,ime_subekt,number,ekate from topic_layer_kvs_by_tenant_name_mat_view where {$where} order by area desc limit 50";
        // Helper variables
        $totalArea = 0;
        $totalNumber = 0;
        // The results of the main grid query
        $results = $UserDbController->DbHandler->getDataByQuery($map_info_query);
        $resultsCount = count($results);
        // This variable $connection_string is used when creating the map files, so the
        // template could use the PHP constants for the DB Connection,
        // as well as the database name from the $User object property.
        $connection_string = "CONNECTION 'host=" . DEFAULT_DB_HOST . ' dbname=' . $this->User->Database . ' user=' . DEFAULT_DB_USERNAME . ' password=' . DEFAULT_DB_PASSWORD . ' port=' . DEFAULT_DB_PORT . "'";
        // The $map_results array will be passed to the map file template
        // it gets the results before the additional processing of the data
        // because the processing is done only for visual purposes
        // and the 'area' parameter should not be truncated to only 3 digits
        // when it is passed to the map template. This ensures the proper expressions
        // will be created and matched in the map file
        $map_results = ['results' => $results, 'connection_string' => $connection_string];
        // Main results processing, to get user friendly texts, numbers and colors for the grid cells
        for ($i = 0; $i < $resultsCount; $i++) {
            // Get the color from the predefined set inside the Layers config file
            $results[$i]['color'] = $GLOBALS['Layers']['colors'][$i]['hex'];
            $results[$i]['area'] = number_format($results[$i]['area'], 3, '.', ' ');
            $results[$i]['number'] = number_format($results[$i]['number'], 0, '.', ' ');
            $results[$i]['ime_subekt'] = null == $results[$i]['ime_subekt'] ? 'гражданин' : $results[$i]['ime_subekt'];
            $totalArea += $results[$i]['area'];
            $totalNumber += $results[$i]['number'];
        }
        // Replace the last result of the query with the total values of the rest of the results with an additional query
        if ($resultsCount > 49) {
            $map_info_query = "SELECT sum(area) as area, sum(number) as number from topic_layer_kvs_by_tenant_name_mat_view where ekate = '{$ekate}' ";
            $rest_of_the_results = $UserDbController->DbHandler->getDataByQuery($map_info_query);
            $results[49]['area'] = number_format($rest_of_the_results[0]['area'] - $totalArea, 3, '.', ' ');
            $results[49]['ime_subekt'] = 'Останали арендатори';
            $results[49]['number'] = number_format($rest_of_the_results[0]['number'] - $totalNumber, 0, '.', ' ');
        }
        // This method will get the map extent of the current topic map
        $extent = $UserDbController->getMaxExtentForEkate($UserDbController->DbHandler->tableKVS, $ekate);
        if (!$extent) {
            $extent = $UserDbController->getMaxExtent($UserDbController->DbHandler->tableKVS);
        }
        // Replace and remove all the unnecessary strings so to get only the map coordinates for the extent of the map
        $extent = str_replace('BOX(', '', $extent);
        $extent = str_replace(')', '', $extent);
        $extent = str_replace(',', ' ', $extent);
        // Set the extent as new parameter of the $map_resutls array and pass it to the map file template
        $map_results['extent'] = $extent;
        $map_results['ekate'] = $ekate;
        // This will create the map file with the new data and template and then return it as a text string
        $mapString = $LayersController->StringHelper->loadTemplate($GLOBALS['Templates'][32]['template'], $map_results);
        // Save the map file string to a specific map file.
        $mapFile = fopen(WMS_MAP_PATH . '/' . $this->User->GroupID . '_topic_layer_kvs.map', 'w');
        fwrite($mapFile, $mapString);
        fclose($mapFile);
        if ($resultsCount > 0) {
            $results[0]['extent'] = str_replace(' ', ', ', $extent);
        }

        // return the results for the grid
        return ['rows' => $results, 'total' => $resultsCount];
    }

    private function initTopicLayerByAgreement($ekate, $filter)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $LayersController = new LayersController('Layers');
        $where = $this->addWhere($ekate, $filter);
        // Main grid query
        // Gets all 50 results, ordered by total owned area
        $map_info_query = "SELECT gid, area,egn_subekt,ime_subekt,number,ekate from topic_layer_kvs_by_agreement_mat_view where {$where} order by area desc limit 50";
        // Helper variables
        $totalArea = 0;
        $totalNumber = 0;
        // The results of the main grid query
        $results = $UserDbController->DbHandler->getDataByQuery($map_info_query);
        $resultsCount = count($results);
        // This variable $connection_string is used when creating the map files, so the
        // template could use the PHP constants for the DB Connection,
        // as well as the database name from the $User object property.
        $connection_string = "CONNECTION 'host=" . DEFAULT_DB_HOST . ' dbname=' . $this->User->Database . ' user=' . DEFAULT_DB_USERNAME . ' password=' . DEFAULT_DB_PASSWORD . ' port=' . DEFAULT_DB_PORT . "'";
        // The $map_results array will be passed to the map file template
        // it gets the results before the additional processing of the data
        // because the processing is done only for visual purposes
        // and the 'area' parameter should not be truncated to only 3 digits
        // when it is passed to the map template. This ensures the proper expressions
        // will be created and matched in the map file
        $map_results = ['results' => $results, 'connection_string' => $connection_string];
        // Main results processing, to get user friendly texts, numbers and colors for the grid cells
        for ($i = 0; $i < $resultsCount; $i++) {
            // Get the color from the predefined set inside the Layers config file
            $results[$i]['color'] = $GLOBALS['Layers']['colors'][$i]['hex'];
            $results[$i]['area'] = number_format($results[$i]['area'], 3, '.', ' ');
            $results[$i]['number'] = number_format($results[$i]['number'], 0, '.', ' ');
            $results[$i]['ime_subekt'] = null == $results[$i]['ime_subekt'] ? 'гражданин' : $results[$i]['ime_subekt'];
            $totalArea += $results[$i]['area'];
            $totalNumber += $results[$i]['number'];
        }
        // Replace the last result of the query with the total values of the rest of the results with an additional query
        if (count($results) > 49) {
            $map_info_query = "SELECT sum(area) as area, sum(number) as number from topic_layer_kvs_by_agreement_mat_view where ekate = '{$ekate}' ";
            $rest_of_the_results = $UserDbController->DbHandler->getDataByQuery($map_info_query);
            $results[49]['area'] = number_format($rest_of_the_results[0]['area'] - $totalArea, 3, '.', ' ');
            $results[49]['ime_subekt'] = 'Останали собственици';
            $results[49]['number'] = number_format($rest_of_the_results[0]['number'] - $totalNumber, 0, '.', ' ');
        }
        // This method will get the map extent of the current topic map
        $extent = $UserDbController->getMaxExtentForEkate($UserDbController->DbHandler->tableKVS, $ekate);
        if (!$extent) {
            $extent = $UserDbController->getMaxExtent($UserDbController->DbHandler->tableKVS);
        }
        // Replace and remove all the unnecessary strings so to get only the map coordinates for the extent of the map
        $extent = str_replace('BOX(', '', $extent);
        $extent = str_replace(')', '', $extent);
        $extent = str_replace(',', ' ', $extent);
        // Set the extent as new parameter of the $map_resutls array and pass it to the map file template
        $map_results['extent'] = $extent;
        $map_results['ekate'] = $ekate;
        // This will create the map file with the new data and template and then return it as a text string
        $mapString = $LayersController->StringHelper->loadTemplate($GLOBALS['Templates'][33]['template'], $map_results);
        // Save the map file string to a specific map file.
        $mapFile = fopen(WMS_MAP_PATH . '/' . $this->User->GroupID . '_topic_layer_kvs.map', 'w');
        fwrite($mapFile, $mapString);
        fclose($mapFile);
        if ($resultsCount > 0) {
            $results[0]['extent'] = str_replace(' ', ', ', $extent);
        }

        // return the results for the grid
        return ['rows' => $results, 'total' => $resultsCount];
    }

    private function initTopicLayerByCategory($ekate, $filter)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $LayersController = new LayersController('Layers');
        $where = $this->addCategoryWhere($ekate, $filter);
        // Main grid query
        // Gets all 50 results, ordered by total owned area
        $map_info_query = "SELECT gid, area, ime_subekt as egn_subekt,number, ekate from topic_layer_kvs_by_category_mat_view where {$where} order by area desc limit 50";
        // Helper variables
        $totalArea = 0;
        $totalNumber = 0;
        // The results of the main grid query
        $results = $UserDbController->DbHandler->getDataByQuery($map_info_query);
        $resultsCount = count($results);
        // This variable $connection_string is used when creating the map files, so the
        // template could use the PHP constants for the DB Connection,
        // as well as the database name from the $User object property.
        $connection_string = "CONNECTION 'host=" . DEFAULT_DB_HOST . ' dbname=' . $this->User->Database . ' user=' . DEFAULT_DB_USERNAME . ' password=' . DEFAULT_DB_PASSWORD . ' port=' . DEFAULT_DB_PORT . "'";
        // The $map_results array will be passed to the map file template
        // it gets the results before the additional processing of the data
        // because the processing is done only for visual purposes
        // and the 'area' parameter should not be truncated to only 3 digits
        // when it is passed to the map template. This ensures the proper expressions
        // will be created and matched in the map file
        $map_results = ['results' => $results, 'connection_string' => $connection_string];
        // Main results processing, to get user friendly texts, numbers and colors for the grid cells
        for ($i = 0; $i < $resultsCount; $i++) {
            // Get the color from the predefined set inside the Layers config file
            $results[$i]['color'] = $GLOBALS['Layers']['colors'][$i]['hex'];
            $results[$i]['area'] = number_format($results[$i]['area'], 3, '.', ' ');
            $results[$i]['number'] = number_format($results[$i]['number'], 0, '.', ' ');
            $results[$i]['ime_subekt'] = $this->transformToCategoryRomanNumeral($results[$i]['egn_subekt']);
            $totalArea += $results[$i]['area'];
            $totalNumber += $results[$i]['number'];
        }
        // This method will get the map extent of the current topic map
        $extent = $UserDbController->getMaxExtentForEkate($UserDbController->DbHandler->tableKVS, $ekate);
        if (!$extent) {
            $extent = $UserDbController->getMaxExtent($UserDbController->DbHandler->tableKVS);
        }
        // Replace and remove all the unnecessary strings so to get only the map coordinates for the extent of the map
        $extent = str_replace('BOX(', '', $extent);
        $extent = str_replace(')', '', $extent);
        $extent = str_replace(',', ' ', $extent);
        // Set the extent as new parameter of the $map_resutls array and pass it to the map file template
        $map_results['extent'] = $extent;
        $map_results['ekate'] = $ekate;
        // This will create the map file with the new data and template and then return it as a text string
        $mapString = $LayersController->StringHelper->loadTemplate($GLOBALS['Templates'][34]['template'], $map_results);
        // Save the map file string to a specific map file.
        $mapFile = fopen(WMS_MAP_PATH . '/' . $this->User->GroupID . '_topic_layer_kvs.map', 'w');
        fwrite($mapFile, $mapString);
        fclose($mapFile);
        if ($resultsCount > 0) {
            $results[0]['extent'] = str_replace(' ', ', ', $extent);
        }

        // return the results for the grid
        return ['rows' => $results, 'total' => $resultsCount];
    }

    private function initTopicLayerByNTP($ekate, $filter)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $LayersController = new LayersController('Layers');
        $where = $this->addWhere($ekate, $filter, 'txt_ntp');
        // Main grid query
        // Gets all 50 results, ordered by total owned area
        $map_info_query = "SELECT gid, area,txt_ntp,txt_ntp as ime_subekt,number,ekate from topic_layer_kvs_by_ntp_mat_view where {$where} order by area desc limit 50";
        // Helper variables
        $totalArea = 0;
        $totalNumber = 0;
        // The results of the main grid query
        $results = $UserDbController->DbHandler->getDataByQuery($map_info_query);
        $resultsCount = count($results);
        // This variable $connection_string is used when creating the map files, so the
        // template could use the PHP constants for the DB Connection,
        // as well as the database name from the $User object property.
        $connection_string = "CONNECTION 'host=" . DEFAULT_DB_HOST . ' dbname=' . $this->User->Database . ' user=' . DEFAULT_DB_USERNAME . ' password=' . DEFAULT_DB_PASSWORD . ' port=' . DEFAULT_DB_PORT . "'";
        // The $map_results array will be passed to the map file template
        // it gets the results before the additional processing of the data
        // because the processing is done only for visual purposes
        // and the 'area' parameter should not be truncated to only 3 digits
        // when it is passed to the map template. This ensures the proper expressions
        // will be created and matched in the map file
        $map_results = ['results' => $results, 'connection_string' => $connection_string];
        // Main results processing, to get user friendly texts, numbers and colors for the grid cells
        for ($i = 0; $i < $resultsCount; $i++) {
            // Get the color from the predefined set inside the Layers config file
            $results[$i]['color'] = $GLOBALS['Layers']['colors'][$i]['hex'];
            $results[$i]['area'] = number_format($results[$i]['area'], 3, '.', ' ');
            $results[$i]['number'] = number_format($results[$i]['number'], 0, '.', ' ');
            $totalArea += $results[$i]['area'];
            $totalNumber += $results[$i]['number'];
        }
        // Replace the last result of the query with the total values of the rest of the results with an additional query
        if ($resultsCount > 49) {
            $map_info_query = "SELECT sum(area) as area, sum(number) as number from topic_layer_kvs_by_ntp_mat_view where ekate = '{$ekate}' ";
            $rest_of_the_results = $UserDbController->DbHandler->getDataByQuery($map_info_query);
            $results[49]['area'] = number_format($rest_of_the_results[0]['area'] - $totalArea, 3, '.', ' ');
            $results[49]['kod_ntp'] = 'Останали НТП';
            $results[49]['number'] = number_format($rest_of_the_results[0]['number'] - $totalNumber, 0, '.', ' ');
        }
        // This method will get the map extent of the current topic map
        $extent = $UserDbController->getMaxExtentForEkate($UserDbController->DbHandler->tableKVS, $ekate);
        if (!$extent) {
            $extent = $UserDbController->getMaxExtent($UserDbController->DbHandler->tableKVS);
        }
        // Replace and remove all the unnecessary strings so to get only the map coordinates for the extent of the map
        $extent = str_replace('BOX(', '', $extent);
        $extent = str_replace(')', '', $extent);
        $extent = str_replace(',', ' ', $extent);
        // Set the extent as new parameter of the $map_resutls array and pass it to the map file template
        $map_results['extent'] = $extent;
        $map_results['ekate'] = $ekate;
        // This will create the map file with the new data and template and then return it as a text string
        $mapString = $LayersController->StringHelper->loadTemplate($GLOBALS['Templates'][35]['template'], $map_results);
        // Save the map file string to a specific map file.
        $mapFile = fopen(WMS_MAP_PATH . '/' . $this->User->GroupID . '_topic_layer_kvs.map', 'w');
        fwrite($mapFile, $mapString);
        fclose($mapFile);
        if ($resultsCount > 0) {
            $results[0]['extent'] = str_replace(' ', ', ', $extent);
        }

        // return the results for the grid
        return ['rows' => $results, 'total' => $resultsCount];
    }

    private function initTopicLayerByOwnership($ekate, $filter)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $LayersController = new LayersController('Layers');
        $where = $this->addWhere($ekate, $filter, 'ime_subekt');
        // Main grid query
        // Gets all 50 results, ordered by total owned area
        $map_info_query = "SELECT gid, area, ime_subekt,number,ekate from topic_layer_kvs_by_ownership_mat_view where {$where} order by area desc limit 50";
        // Helper variables
        $totalArea = 0;
        $totalNumber = 0;
        // The results of the main grid query
        $results = $UserDbController->DbHandler->getDataByQuery($map_info_query);
        $resultsCount = count($results);
        // This variable $connection_string is used when creating the map files, so the
        // template could use the PHP constants for the DB Connection,
        // as well as the database name from the $User object property.
        $connection_string = "CONNECTION 'host=" . DEFAULT_DB_HOST . ' dbname=' . $this->User->Database . ' user=' . DEFAULT_DB_USERNAME . ' password=' . DEFAULT_DB_PASSWORD . ' port=' . DEFAULT_DB_PORT . "'";
        // The $map_results array will be passed to the map file template
        // it gets the results before the additional processing of the data
        // because the processing is done only for visual purposes
        // and the 'area' parameter should not be truncated to only 3 digits
        // when it is passed to the map template. This ensures the proper expressions
        // will be created and matched in the map file
        $map_results = ['results' => $results, 'connection_string' => $connection_string];
        // Main results processing, to get user friendly texts, numbers and colors for the grid cells
        for ($i = 0; $i < $resultsCount; $i++) {
            // Get the color from the predefined set inside the Layers config file
            $results[$i]['color'] = $GLOBALS['Layers']['colors'][$i]['hex'];
            $results[$i]['area'] = number_format($results[$i]['area'], 3, '.', ' ');
            $results[$i]['number'] = number_format($results[$i]['number'], 0, '.', ' ');
            $results[$i]['egn_subekt'] = $results[$i]['ime_subekt'];
            $totalArea += $results[$i]['area'];
            $totalNumber += $results[$i]['number'];
        }
        // This method will get the map extent of the current topic map
        $extent = $UserDbController->getMaxExtentForEkate($UserDbController->DbHandler->tableKVS, $ekate);
        if (!$extent) {
            $extent = $UserDbController->getMaxExtent($UserDbController->DbHandler->tableKVS);
        }
        // Replace and remove all the unnecessary strings so to get only the map coordinates for the extent of the map
        $extent = str_replace('BOX(', '', $extent);
        $extent = str_replace(')', '', $extent);
        $extent = str_replace(',', ' ', $extent);
        // Set the extent as new parameter of the $map_resutls array and pass it to the map file template
        $map_results['extent'] = $extent;
        $map_results['ekate'] = $ekate;
        // This will create the map file with the new data and template and then return it as a text string
        $mapString = $LayersController->StringHelper->loadTemplate($GLOBALS['Templates'][36]['template'], $map_results);
        // Save the map file string to a specific map file.
        $mapFile = fopen(WMS_MAP_PATH . '/' . $this->User->GroupID . '_topic_layer_kvs.map', 'w');
        fwrite($mapFile, $mapString);
        fclose($mapFile);
        if ($resultsCount > 0) {
            $results[0]['extent'] = str_replace(' ', ', ', $extent);
        }

        // return the results for the grid
        return ['rows' => $results, 'total' => $resultsCount];
    }

    private function transformToCategoryRomanNumeral($number)
    {
        switch ($number) {
            case 1:
                $return = 'Категория I';

                break;

            case 2:
                $return = 'Категория II';

                break;

            case 3:
                $return = 'Категория III';

                break;

            case 4:
                $return = 'Категория IV';

                break;

            case 5:
                $return = 'Категория V';

                break;

            case 6:
                $return = 'Категория VI';

                break;

            case 7:
                $return = 'Категория VII';

                break;

            case 8:
                $return = 'Категория VIII';

                break;

            case 9:
                $return = 'Категория IX';

                break;

            case 10:
                $return = 'Категория X';

                break;

            default:
                $return = 'Без категория';

                break;
        }

        return $return;
    }

    private function transformCategoryToNumeral($category)
    {
        $categories = [
            'Категория I' => 1,
            'Категория II' => 2,
            'Категория III' => 3,
            'Категория IV' => 4,
            'Категория V' => 5,
            'Категория VI' => 6,
            'Категория VII' => 7,
            'Категория VIII' => 8,
            'Категория IX' => 9,
            'Категория X' => 10,
            'Без категория' => '',
        ];

        return $categories[$category];
    }

    /**
     * @param array $rpcParams
     *
     * @throws MTRpcException
     */
    private function deleteWorkLayer($rpcParams)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $LayersController = new LayersController('Layers');

        $re = "/layer_work_\d+/";

        preg_match($re, $rpcParams['tablename'], $matches);

        if (count($matches) < 1) {
            throw new MTRpcException('DATABASE_INVALID_TABLE_NAME', -33102);
        }

        // Throw an exception if the work layer table is not physically created.
        $isWorkLayerTableExist = $UserDbController->getTableNameExist($rpcParams['tablename']);
        if (!$isWorkLayerTableExist) {
            throw new MTRpcException('DATABASE_INVALID_TABLE_NAME', -33102);
        }
        $LayersController->deleteWorkLayer($this->User->GroupID, $rpcParams['layer_id'], $rpcParams['tablename']);
        $UserDbController->deleteTable($rpcParams['tablename']);
    }

    /**
     * @param string $tablenameFrom
     * @param string $tablenameTo
     * @param int $fromTableType
     * @param int $toTableType
     *
     * @return array
     */
    private function getTablesDefinitions($tablenameFrom, $tablenameTo, $fromTableType, $toTableType)
    {
        $userDbController = new UserDbController($this->User->Database);
        $layersController = new LayersController();

        $sql = "SELECT TABLE_NAME,
            array_agg(column_name::TEXT) as columns,
            array_agg(column_default::TEXT) as column_default,
            array_agg(ordinal_position::TEXT) as ordinal_position,
            array_agg(is_nullable::TEXT) as is_nullable ,
            array_agg(data_type::TEXT) as data_type,
            array_agg(udt_name::TEXT) as udt_name,
            array_agg(character_maximum_length::TEXT) as chr_max_len,
            array_agg(numeric_precision::TEXT) as num_precision
            FROM information_schema.columns
            WHERE table_schema = 'public'
            and table_name = :tableName
            group by TABLE_NAME order by table_name asc";

        $tablenameFromDefinitions = (in_array($tablenameFrom, $GLOBALS['Layers']['systemTables']))
            ? $layersController->getDataByQuery($sql, ['tableName' => $fromTableType])
            : $userDbController->getDataByQuery($sql, ['tableName' => $tablenameFrom]);

        $tablenameToDefinitions = $userDbController->getDataByQuery($sql, ['tableName' => $tablenameTo]);

        $definitions = array_merge($tablenameFromDefinitions, $tablenameToDefinitions);
        foreach ($definitions as $t => &$tables) {
            if ($tables['table_name'] == $tablenameFrom) {
                $tables['layer_type'] = $fromTableType;
            }
            if ($tables['table_name'] == $tablenameTo) {
                $tables['layer_type'] = $toTableType;
            }
            $tables['columns'] = explode(',', trim($tables['columns'], '{}'));
            $tables['columns'] = array_combine($tables['columns'], $tables['columns']);
            $tables['column_default'] = explode(',', trim($tables['column_default'], '{}'));
            $tables['ordinal_position'] = explode(',', trim($tables['ordinal_position'], '{}'));
            $tables['is_nullable'] = explode(',', trim($tables['is_nullable'], '{}'));
            $tables['data_type'] = explode(',', str_replace('"', '', trim($tables['data_type'], '{}')));
            $tables['udt_name'] = explode(',', trim($tables['udt_name'], '{}'));
            $tables['chr_max_len'] = explode(',', trim($tables['chr_max_len'], '{}'));
            $tables['num_precision'] = explode(',', trim($tables['num_precision'], '{}'));
        }
        $definitions['from'] = ($tablenameFrom == $definitions[0]['table_name']) ? $definitions[0] : $definitions[1];
        $definitions['to'] = ($tablenameTo == $definitions[0]['table_name']) ? $definitions[0] : $definitions[1];
        unset($definitions[0], $definitions[1]);

        return $definitions;
    }

    private function generateWorkLayerDefinitions($tableName)
    {
        /** @var array $tableDefinitions Source table definitions */
        $tableDefinitions = UserLayers::getDefinitionsByTableName($tableName, $this->User->GroupID);

        // Filter only the columns that are copyable
        $tableDefinitions = UserLayers::filterDefinitions($tableDefinitions, [['col_copyable' => true]]);
        $virtualColumnDef = UserLayers::filterDefinitions($tableDefinitions, [['col_virtual' => true, 'col_personalizable' => true]]);
        $referenceColumnNames = array_map(function ($definition) {
            return $definition['col_reference'];
        }, $virtualColumnDef);

        $tableDefinitions = array_map(function ($definition) use ($referenceColumnNames) {
            // Virtual columns and reference columns should not be manipulated.
            if ($definition['col_virtual'] || in_array($definition['col_name'], $referenceColumnNames)) {
                return $definition;
            }

            // Set options to true for the columns that are going to be copied
            $definition['col_multiedit'] = true;
            $definition['col_singleedit'] = true;
            $definition['col_sortable'] = true;
            $definition['col_exportable'] = true;
            $definition['col_personalizable'] = true;

            return $definition;
        }, $tableDefinitions);

        /** @var array $workLayerDefinitions Default definitions for layer of type work */
        $workLayerDefinitions = UserLayers::getDefinitionsByType(Config::LAYER_TYPE_WORK_LAYER);

        /** @var array $diffDefinitions All definitions from $tableDefinitions that does not exist in $workLayerDefinition */
        $diffDefinitions = UserLayers::diffDefinitions($workLayerDefinitions, $tableDefinitions);

        return array_merge($workLayerDefinitions, $diffDefinitions);
    }

    private function updateLayerViews(UserLayers $layer, string $newExtent)
    {
        if (Config::LAYER_TYPE_FOR_ISAK != $layer->layer_type) {
            return;
        }

        $forIsakOldExtent = $layer->extent;
        $this->updateSEPPView($layer->id, $forIsakOldExtent, $newExtent);
        $this->updatePZPView($layer->id, $forIsakOldExtent, $newExtent);
    }

    /*
     * @param int|string $page
     * @param int $sort
     * @param ?string $order
     */
    private function personalizeLayer($layerId, $layerType, $ekatte = null)
    {
        $layer = UserLayers::getLayerById($layerId);

        if (Config::LAYER_TYPE_KVS == $layerType) {
            $styleLayerId = $layerId . '_' . $ekatte;
            $styles = $layer->getStyles();
            $styles = array_combine(array_column($styles, 'layer_id'), $layer->getStyles());
            $style = $styles[$styleLayerId];
        } else {
            [$style] = $layer->getStyles();
        }

        $LayersController = new LayersController('Layers');
        $style = $style->toArray();
        $style['layer_farming'] = $layer->farming;
        $style['layer_year'] = $layer->year;
        $style['layer_name'] = $layer->name;

        if (Config::LAYER_TYPE_KVS == $layerType) {
            $style['ekatte'] = $ekatte;
        }

        $LayersController->saveLayerPersonalization($layer, $style, 'Map', 'datagrid');
    }
}
