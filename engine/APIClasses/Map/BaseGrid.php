<?php

namespace TF\Engine\APIClasses\Map;

use Exception;
use Prado\Web\Services\TRpcApiProvider;
use TF\Application\Common\Config;
use TF\Application\Entity\UserLayers;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

abstract class BaseGrid extends TRpcApiProvider
{
    public function withNuz(array &$options, string $ekatteColumn, array $rpcParams)
    {
        if (true == $rpcParams['with_nuz']) {
            array_push(
                $options['joins'],
                "
                   LEFT JOIN  dblink (
                        'host=" . DBLINK_HOST . ' port=' . DBLINK_PORT . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD . "'::text,
                        'SELECT
                        CASE WHEN su_nuz.ekatte IS NULL THEN FALSE ELSE TRUE END as nuz,
                        su_nuz.ekatte
                        FROM
                            su_nuz'
                    ) AS A (nuz bool, nuz_ekatte text) ON A.nuz_ekatte = {$options['tablename']}.{$ekatteColumn}
                
                "
            );

            array_push($options['return'], 'A.nuz');
        }
    }

    public function withPhysicalBlock(array &$options, string $ekatteColumn, array $rpcParams)
    {
        if (true == $rpcParams['with_physical_block']) {
            array_push(
                $options['joins'],
                "
                    LEFT JOIN dblink (
                        'host=" . DBLINK_HOST . ' port=' . DBLINK_PORT . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD . "'::text,
                        'SELECT
                            la.gid AS physical_block_gid,
                            la.fbident AS physical_block_name,
                            la.geom,
                            la.ekatte_
                        FROM
                            layer_allowable AS la'
                    ) AS la (physical_block_gid integer, physical_block_name text, geom geometry, ekatte_ text)
                    ON la.ekatte_ = {$options['tablename']}.{$ekatteColumn}
                    AND ST_Intersects(
                        ST_Centroid({$options['tablename']}.geom),
                        la.geom
                    )
                ",
            );

            array_push($options['return'], 'la.physical_block_gid', 'la.physical_block_name');
        }
    }

    public function multiEdit($rpcParams): bool
    {
        $UserDbController = new UserDbController($this->User->Database);
        $columnDefinitions = UserLayers::getDefinitionsByTableName($rpcParams['layer_name'], $this->User->GroupID);
        $editableColumns = UserLayers::filterDefinitions($columnDefinitions, [['col_multiedit' => true]]);
        $editableColumns = array_column($editableColumns, 'col_name');

        $options = [
            'tablename' => "{$rpcParams['layer_name']}",
            'update' => $this->prepareUpdateOptions($rpcParams, $editableColumns),
        ];

        if (!count($options['update'])) {
            throw new Exception('Incorrect update input data!');
        }

        $this->buildWhere($options, $rpcParams);

        $UserDbController->multiEdit($options);

        $LayersController = new LayersController('Layers');
        $layer = UserLayers::getLayerById($rpcParams['layer_id']);
        [$style] = $layer->getStyles();
        $style = $style->toArray();
        $style['layer_farming'] = $layer->farming;
        $style['layer_year'] = $layer->year;
        $style['layer_name'] = $layer->name;

        $LayersController->saveLayerPersonalization($layer, $style, 'Map', 'datagrid');

        return true;
    }

    protected function prepareUpdateOptions(array $rpcParams, array $editableColumns): array
    {
        $options = [];
        foreach ($rpcParams['edited_columns'] as $key => $prop) {
            if (in_array($key, $editableColumns)) {
                $options[$key] = $prop;
            }
        }

        return $options;
    }

    protected function buildWhereGroup(UserLayers $layer, array $filterGroups): array
    {
        $layerDefinitions = $layer->getDefinitions();
        $filterableColumnsDefinitions = UserLayers::filterDefinitions(
            $layerDefinitions,
            [
                ['col_visible' => true],
                ['col_personalizable' => true],
            ]
        );

        $whereGroup = [];
        foreach ($filterGroups as $groupKey => $group) {
            $where = [];

            foreach ($filterableColumnsDefinitions as $colDef) {
                $colName = $colDef['col_name'];

                if (!isset($group[$colName])) {
                    continue;
                }

                $compareOperator = '=';
                if (Config::LAYER_COLUMN_CATEGORY_TEXT === $colDef['col_category']) {
                    $compareOperator = 'ILIKE';
                }

                if (is_array($group[$colName])) {
                    $compareOperator = 'IN';
                }

                $where["{$colName}_{$groupKey}"] = [
                    'column' => $colName,
                    'compare' => $compareOperator,
                    'value' => $group[$colName],
                ];
            }

            if (!count($where)) {
                continue;
            }

            $whereGroup[] = ['where' => $where];
        }

        return $whereGroup;
    }

    protected function buildFullTextSearchFilterGroup(string $search, array $columns)
    {
        $options = [];
        foreach ($columns as $column) {
            [$colName, $columnText] = $this->getColumnDetails($column);
            $options["full_text_search_{$colName}"] = ['column' => $columnText, 'compare' => 'ILIKE', 'value' => $search];
        }

        return $options;
    }

    protected function buildWhere(array &$options, array $rpcParams)
    {
        $layer = UserLayers::getLayerById($rpcParams['layer_id']);

        if (!$layer) {
            throw new Exception('Layer not found');
        }

        $validGroups = array_filter($rpcParams['filter_params']['groups'], function ($groupOptions) {
            return count($groupOptions ?? []) > 0;
        });

        if (count($validGroups) > 0) {
            $options['whereOrGroup'] = $this->buildWhereGroup($layer, $rpcParams['filter_params']['groups']);
        }

        if (
            is_string($rpcParams['filter_params']['full_text_search'])
            && strlen($rpcParams['filter_params']['full_text_search'])
            && count($options['full_text_search_columns'] ?? []) > 0
        ) {
            $options['whereOr'] = array_merge(
                $options['whereOr'] ?? [],
                $this->buildFullTextSearchFilterGroup($rpcParams['filter_params']['full_text_search'], $options['full_text_search_columns'])
            );
        }

        if ($rpcParams['gids']) {
            $idColumn = Config::LAYER_TYPE_ZP == $rpcParams['layer_type'] ? 'id' : 'gid';
            $options['where']['gids'] = ['column' => $idColumn, 'compare' => 'IN', 'value' => $rpcParams['gids']];
        }
    }

    private function getColumnDetails(string $column): array
    {
        $exploded = explode('.', $column);
        $colName = end($exploded);
        $columnText = 2 === count($exploded) ? "{$exploded[0]}.\"{$colName}\"::TEXT" : "\"{$colName}\"::TEXT";

        return [$colName, $columnText];
    }
}
