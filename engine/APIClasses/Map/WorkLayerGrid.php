<?php

namespace TF\Engine\APIClasses\Map;

use TF\Application\Entity\UserLayers;
use TF\Engine\Kernel\ExportData\LayerTypes\LayerWork;
use TF\Engine\Kernel\ExportToExcelClass;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

/**
 * Атрибутна информация "Работен слой".
 *
 * @rpc-module Map
 *
 * @rpc-service-id work-layer-datagrid
 */
class WorkLayerGrid extends BaseGrid
{
    public const COL_TRANSLATIONS = [
        'name' => 'Име',
        'area' => 'Площ (дка)',
    ];
    private $headers = [];
    private $footer = [];

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getWorkLayerGrid'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'exportToExcel' => ['method' => [$this, 'exportToExcel']],
            'multiEdit' => ['method' => [$this, 'multiEdit']],
        ];
    }

    /**
     * @api-method exportToExcel
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer layer_id
     *                         #item string  table_name
     *                         #item boolean clear_filter
     *                         #item string  action
     *                         #item string  ekatte
     *                         #item integer crop_code
     *                         #item string  bzz
     *                         }
     *
     * @return string
     */
    public function exportToExcel(array $rpcParams)
    {
        $time = strtotime(date('Y-m-d H:i:s'));

        $filename = PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID . '/work_layer_table_' . $time . '.xlsx';

        if (!file_exists(PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID)) {
            mkdir(PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID, 0777);
        }

        $rpcParams['forExport'] = true;
        $data = $this->getWorkLayerGrid($rpcParams);

        $exportExcelDoc = new ExportToExcelClass();
        $exportExcelDoc->export($data['rows'], $this->headers, $this->footer);
        $exportExcelDoc->saveFile($filename);

        return 'files/uploads/export/' . $this->User->UserID . '/work_layer_table_' . $time . '.xlsx';
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer layer_id
     *                         #item string  table_name
     *                         #item boolean clear_filter
     *                         #item string  filtered_plots
     *                         #item string  plot_name
     *                         #item string  plot_info
     *                         #item string  action
     *                         #item array   gids
     *                         }
     * @param string $page pagination parameter
     * @param string $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array
     *{
     *    #item integer gid
     *    #item string plot_name
     *    #item string plot_info
     *    #item string area
     *    #item string st_astext
     *}
     */
    public function getWorkLayerGrid(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $UserDbController = new UserDbController($this->User->Database);
        $LayersController = new LayersController('Layers');

        $return = [];

        // creating a default empty return
        $empty_return = [
            'rows' => [],
            'total' => 0,
        ];

        if (!isset($rpcParams['layer_id']) || !(int)$rpcParams['layer_id']) {
            return $empty_return;
        }

        $layer_result = $LayersController->getLayerData($rpcParams['layer_id']);
        $tableName = $layer_result['table_name'];
        $tableExists = $UserDbController->getTableNameExist($tableName);

        if (!$tableExists) {
            return $empty_return;
        }

        $definitionsData = json_decode($layer_result['definitions'], true);
        $definitions = [];

        foreach ($definitionsData as $definition) {
            $definitions[$definition['col_name']] = [
                'col_title' => $definition['col_title'],
                'col_visible' => $definition['col_visible'],
            ];
        }

        $columnsList = $UserDbController->getTableColumnsList($tableName);

        $slopeColumnIndex = array_search(LayerWork::$slopeColumn, $columnsList);
        if (!$this->User->HasSlopeRights && false !== $slopeColumnIndex) {
            unset($columnsList[$slopeColumnIndex]);
        }

        $columns = [$this->formatColumns($columnsList, $definitions)];
        $empty_return['columns'] = $columns;

        $dbLink = "'host=" . DBLINK_HOST . ' port=' . DBLINK_PORT . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD . "'::text";
        $ekattesTable = "(SELECT * FROM dblink({$dbLink}, 'SELECT ekatte_code, ekatte_name FROM su_ekatte') AS ekattes(id VARCHAR, name VARCHAR(255))) as ekattes";

        if ('area' === $sort) {
            $sort = 'tf_geom_area';
        }

        $options = [
            'tablename' => "{$tableName}",
            'return' => [
                "{$tableName}.gid",
                "round((ST_Area({$tableName}.geom) / 1000)::numeric, 3) AS tf_geom_area",
                "ST_ASTEXT(ST_Multi({$tableName}.geom))",
                "{$tableName}.*",
                "ST_Centroid({$tableName}.geom) as centroid",
            ],
            'full_text_search_columns' => array_diff($columnsList, ['geom', 'gid']),
            'custom_counter' => "COUNT(DISTINCT({$tableName}.gid)), SUM(round((ST_Area({$tableName}.geom)/1000)::numeric, 3)) as total_area",
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
            'layer_id' => $rpcParams['layer_id'],
        ];

        if (isset($rpcParams['ekatte_column'])) {
            [$ekatteColumnDefinition] = UserLayers::filterDefinitions($definitionsData, [['col_name' => $rpcParams['ekatte_column']]]);

            $ekatteColumn = $ekatteColumnDefinition['col_virtual'] ? $ekatteColumnDefinition['col_reference'] : $rpcParams['ekatte_column'];

            $options['return'] = array_merge($options['return'], ['ekattes.name as ekatte_name']);
            $options['joins'][] = " left join {$ekattesTable} ON ekattes.id = {$tableName}.{$ekatteColumn}";
        }

        $this->withNuz($options, ($ekatteColumn ?? ''), $rpcParams);
        $this->withPhysicalBlock($options, ($ekatteColumn ?? ''), $rpcParams);
        $this->buildWhere($options, $rpcParams);

        $counter = $UserDbController->getLayersByParams($options, true);

        if (0 == $counter[0]['count']) {
            return $empty_return;
        }

        $results = $UserDbController->getLayersByParams($options, false);
        $resultsCount = count($results);

        for ($i = 0; $i < $resultsCount; $i++) {
            $results[$i]['area'] = $results[$i]['tf_geom_area'];
            unset($results[$i]['geom'], $results[$i]['tf_geom_area']);

            $slopeColumn = LayerWork::$slopeColumn;
            if (!$this->User->HasSlopeRights && array_key_exists($slopeColumn, $results[$i])) {
                unset($results[$i][$slopeColumn], $columns[$slopeColumn]);
            }
        }

        if ($rpcParams['forExport']) {
            $this->setHeaders($results, $definitions);
        }

        $filteredGidsFromResults = [];
        foreach ($results as $result) {
            $filteredGidsFromResults[] = $result['gid'];
        }

        $return['rows'] = $results;
        $return['total'] = $counter[0]['count'];
        $return['columns'] = $columns;
        $return['filtered_gids'] = $filteredGidsFromResults ? $filteredGidsFromResults : [];

        $total_area = $counter[0]['total_area'];

        $return['footer'] = [
            [
                'plot_name' => '<b>ОБЩО</b>',
                'area' => $total_area,
            ],
        ];
        $this->footer = $return['footer'];

        return $return;
    }

    /**
     * Undocumented function.
     *
     * @param [type] $results
     * @param [type] $definitions
     */
    private function setHeaders($results, $definitions)
    {
        if (array_key_exists('st_astext', $results[0])) {
            unset($results[0]['st_astext']);
        }

        if (array_key_exists('gid', $results[0])) {
            unset($results[0]['gid']);
        }

        if (array_key_exists('geom', $results[0])) {
            unset($results[0]['geom']);
        }

        $cols = array_keys($results[0]);
        array_unshift($cols, 'name');
        $arrangedCols = array_unique($cols);

        foreach ($arrangedCols as $col) {
            if (array_key_exists($col, $definitions) && !$definitions[$col]['col_visible']) {
                continue;
            }
            $this->headers[$col] = array_key_exists($col, $definitions)
                ? $definitions[$col]['col_title']
                : (
                    array_key_exists($col, self::COL_TRANSLATIONS)
                    ? self::COL_TRANSLATIONS[$col]
                    : $col
                );
        }
    }

    private function formatColumns($inputArray, $definitions)
    {
        array_unshift($inputArray, 'name');
        $inputArray = array_unique($inputArray);
        $columns = [];
        foreach ($inputArray as $col) {
            if ('gid' != $col && 'st_astext' != $col && 'geom' != $col) {
                if (array_key_exists($col, $definitions) && !$definitions[$col]['col_visible']) {
                    continue;
                }
                $columns[] = [
                    'field' => $col,
                    'title' => array_key_exists($col, $definitions) ? $definitions[$col]['col_title'] : (array_key_exists($col, self::COL_TRANSLATIONS) ? self::COL_TRANSLATIONS[$col] : $col),
                    'sortable' => true,
                ];
            }
        }

        return $columns;
    }
}
