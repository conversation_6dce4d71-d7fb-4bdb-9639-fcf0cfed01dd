<?php

namespace TF\Engine\APIClasses\Map;

use Prado\Web\Services\TRpcApiProvider;

/**
 * @rpc-module Map
 *
 * @rpc-service-id map-attributes
 */
class MapAttributes extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'getMapAttributes' => ['method' => [$this, 'getMapAttributes']],
        ];
    }

    /**
     * @api-method getUserMapFilePath
     * Get all user path to map file
     *
     * @return array string
     */
    public function getMapAttributes()
    {
        return [
            'map_path' => WMS_MAP_PATH . "{$this->User->GroupID}.map",
            'wms_url' => WMS_SERVER,
            'mapcache_url' => LOGIN3_WMS_SERVER,
        ];
    }
}
