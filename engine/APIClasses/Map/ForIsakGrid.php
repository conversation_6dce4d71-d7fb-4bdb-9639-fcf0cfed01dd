<?php

namespace TF\Engine\APIClasses\Map;

use TF\Engine\Kernel\ExportToExcelClass;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\Users\UsersController;

// Prado::using('Plugins.Core.Farming.conf');
// Prado::using('Plugins.Core.Farming.*');
// Prado::using('Plugins.Core.Layers.conf');
// Prado::using('Plugins.Core.Layers.*');

/**
 * Атрибутна информация "За ИСАК".
 *
 * @rpc-module Map
 *
 * @rpc-service-id for-isak-datagrid
 */
class ForIsakGrid extends BaseGrid
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getForIsakGrid'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'multiEdit' => ['method' => [$this, 'multiEdit']],
            'exportToExcel' => ['method' => [$this, 'exportToExcel'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer layer_id
     *                         #item string  table_name
     *                         #item boolean clear_filter
     *                         #item string  filtered_plots
     *                         #item string  action
     *                         #item string  ekatte
     *                         #item string  prc_name
     *                         #item string  edited
     *                         #item string  cropcode
     *                         }
     * @param string $page pagination parameter
     * @param string $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array
     */
    public function getForIsakGrid(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $UserDbController = new UserDbController($this->User->Database);
        $LayersController = new LayersController('Layers');
        $UsersController = new UsersController('Users');

        // clean old results
        $return = [];
        $filteredGidsFromResults = [];
        // creating a default empty return
        $empty_return = [
            'rows' => [],
            'total' => 0,
        ];

        if (isset($rpcParams['layer_id']) && (int)$rpcParams['layer_id']) {
            $layer_result = $LayersController->getLayerData($rpcParams['layer_id']);
        } else {
            return $empty_return;
        }

        $tableName = $layer_result['table_name'];
        $tableExists = $UserDbController->getTableNameExist($tableName);
        if (!$tableExists) {
            return $empty_return;
        }

        $dbLink = "'host=" . DBLINK_HOST . ' port=' . DBLINK_PORT . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD . "'::text";
        $ekattesTable = "(SELECT * FROM dblink({$dbLink}, 'SELECT ekatte_code, ekatte_name FROM su_ekatte') AS ekattes(id VARCHAR, name VARCHAR(255))) as ekattes";

        $geomTransformationCoef = (true == $rpcParams['with_nuz']) ? 1000 : 10000;

        $options = [
            'tablename' => "{$tableName}",
            'return' => [
                "{$tableName}.gid",
                "{$tableName}.prc_name",
                "round((ST_Area({$tableName}.geom) / {$geomTransformationCoef} )::numeric, 3) AS area",
                "{$tableName}.ekatte",
                'ekattes.name as ekatte_name',
                "{$tableName}.virtual_ekatte_name",
                "ST_ASTEXT({$tableName}.geom)",
                "{$tableName}.edited",
                "{$tableName}.schema",
                "{$tableName}.comment",
                "{$tableName}.sepp",
                "{$tableName}.zdp",
                "{$tableName}.pndp",
                "{$tableName}.nr1",
                "{$tableName}.nr2",
                "{$tableName}.cropname",
                "{$tableName}.cropcode",
                "{$tableName}.virtual_crop_name",
                "ST_Centroid({$tableName}.geom) as centroid",
            ],
            'full_text_search_columns' => ["{$tableName}.prc_name", "{$tableName}.ekatte", "{$tableName}.edited", ".{$tableName}.cropname"],
            'custom_counter' => "COUNT(DISTINCT({$tableName}.gid)), SUM(round((ST_Area({$tableName}.geom) / {$geomTransformationCoef})::numeric, 3)) as total_area",
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
            'layer_id' => $rpcParams['layer_id'],
        ];
        $options['joins'][] = " left join {$ekattesTable} ON ekattes.id = {$tableName}.ekatte";

        $this->withNuz($options, ($ekatteColumn = 'ekatte'), $rpcParams);
        $this->withPhysicalBlock($options, ($ekatteColumn), $rpcParams);

        $this->buildWhere($options, $rpcParams);

        $results = $UserDbController->getLayersByParams($options, false);
        $counter = $UserDbController->getLayersByParams($options, true);
        $resultsCount = count($results);

        for ($i = 0; $i < $resultsCount; $i++) {
            $results[$i]['is_active_culture'] = $GLOBALS['Farming']['crops'][$results[$i]['cropcode']]['is_active'];
            $filteredGidsFromResults[] = $results[$i]['gid'];
        }

        $return['rows'] = $results;
        $return['total'] = $counter[0]['count'];
        $total_area = $counter[0]['total_area'];

        $return['footer'] = [
            [
                'schema' => '<b>ОБЩО</b>',
                'area' => $total_area,
            ],
        ];

        $return['filtered_gids'] = $filteredGidsFromResults ? $filteredGidsFromResults : [];

        return $return;
    }

    public function exportToExcel(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $time = strtotime(date('Y-m-d H:i:s'));
        $file = '/for_isak_table_' . $time . '.xlsx';
        $filename = PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID . $file;

        if (!file_exists(PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID)) {
            mkdir(PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID, 0777);
        }

        $headers = [
            'ekatte_name' => 'Землище',
            'prc_name' => 'Име на парцел',
            'virtual_crop_name' => 'Култура',
            'schema' => 'Схема',
            'area' => 'Площ (ха)',
            'virtual_ekatte_name' => 'ЕКАТТЕ',
            'edited' => 'За редакция',
            'comment' => 'Коментар',
        ];

        $data = $this->getForIsakGrid($rpcParams, $page, $rows, $sort, $order);
        $totalArea = $data['footer'][0]['area'];
        $footerArray = [
            'ekatte_name' => '',
            'prc_name' => '',
            'virtual_crop_name' => '',
            'schema' => 'ОБЩО',
            'area' => $totalArea,
            'virtual_ekatte_name' => '',
            'edited' => '',
            'comment' => '',
        ];
        $data['rows'][] = $footerArray;

        $exportExcelDoc = new ExportToExcelClass();
        $exportExcelDoc->export($data['rows'], $headers, []);
        $exportExcelDoc->saveFile($filename);

        return 'files/uploads/export/' . $this->User->UserID . $file;
    }

    protected function prepareUpdateOptions(array $rpcParams, array $editableColumns): array
    {
        $options = [];
        foreach ($rpcParams['edited_columns'] as $key => $prop) {
            if ('cropcode' === $key && in_array($key, $editableColumns)) {
                $options['cropcode'] = $prop;
                $options['cropname'] = $GLOBALS['Farming']['crops'][$prop]['crop_name'];

                continue;
            }

            if (in_array($key, $editableColumns)) {
                $options[$key] = $prop;
            }
        }

        return $options;
    }
}
