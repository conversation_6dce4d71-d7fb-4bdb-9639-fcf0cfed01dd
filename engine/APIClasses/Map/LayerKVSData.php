<?php

namespace TF\Engine\APIClasses\Map;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Layers\LayersController;

/**
 * Извличане на данните за КВС ИМОТИ за Technofarm Mobile.
 *
 * @rpc-module Map
 *
 * @rpc-service-id layer-kvs-data
 */
class LayerKVSData extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'getKVSLayerData' => ['method' => [$this, 'getKVSLayerData']],
        ];
    }

    /**
     * @api-method getKVSLayerData
     * Get data for kvs layer
     *
     * @return array $layerKvsData
     *               {
     *               #item string layer_table    The name of table
     *               #item string extent    		The table extent
     *               }
     */
    public function getKVSLayerData()
    {
        $LayersController = new LayersController('Layers');

        // layer KVS plots
        $layer_type = $GLOBALS['Layers']['srid'][4]['type'];
        $user_id = $this->User->GroupID;
        $layerKvsData = [];

        $layers = $LayersController->getLayersByType($layer_type, $user_id);
        $layer = $layers[0];

        $layerKvsData['layer_table'] = $layer['layer_table'];
        $layerKvsData['extent'] = $layer['extent'];

        return $layerKvsData;
    }
}
