<?php

namespace TF\Engine\APIClasses\Map;

use Exception;
use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController;
use TF\Engine\Plugins\Core\Users\UsersController;

// Prado::using('Plugins.Core.Farming.conf');
// Prado::using('Plugins.Core.Farming.*');
// Prado::using('Plugins.Core.Layers.conf');
// Prado::using('Plugins.Core.Layers.*');

/**
 * Попълване на полетата при сливане на КВС Имоти.
 *
 * @rpc-module Map
 *
 * @rpc-service-id kvs-merge
 */
class KVSMerge extends TRpcApiProvider
{
    private $module = 'Map';
    private $service_id = 'kvs-merge';

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'setMergeFields' => ['method' => [$this, 'setMergeFields']],
            'saveKVSMerge' => ['method' => [$this, 'saveKVSMerge'],
                'validators' => [
                    'rpcParams' => [
                        'number' => 'validateText',
                        'active' => 'validateText',
                        'area' => 'validateText',
                        'category' => 'validateText',
                        'editingIds' => 'validateText',
                        'ekate' => 'validateText',
                        'masiv' => 'validateText',
                        'mergeGeom' => 'validateText',
                        'mestnost' => 'validateText',
                        'ntp' => 'validateText',
                        'usable' => 'validateText',
                    ],
                ]],
        ];
    }

    /**
     * @api-method setMergeFields
     *
     * @param string $rpcParam
     *
     * @return array
     */
    public function setMergeFields($rpcParam)
    {
        $UserDbController = new UserDbController($this->User->Database);

        $editingIds = explode(',', $rpcParam);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableKVS,
            'return' => ['*'],
            'where' => [
                'gid' => ['column' => 'gid', 'compare' => '=', 'value' => $editingIds[0]],
            ],
        ];
        $results = $UserDbController->getItemsByParams($options);

        return $results[0];
    }

    /**
     * Запазване на сливането на КВС имоти.
     *
     * @api-method saveKVSMerge
     *
     * @param array $rpcParams
     *                         {
     *                         #item string  number
     *                         #item string  masiv
     *                         #item string  ekate
     *                         #item string  editingIds
     *                         #item boolean active
     *                         #item string  category
     *                         #item string  ntp
     *                         #item string  mestnost
     *                         #item string  usable
     *                         #item string  area
     *                         #item string  active
     *                         #item string  mergeGeom
     *                         }
     *
     * @throws @MTRpcException
     *
     * @return array
     */
    public function saveKVSMerge($rpcParams)
    {
        $UsersController = new UsersController('Users');
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);

        if ($this->isPlotNumberExists($rpcParams['number'], $rpcParams['masiv'], $rpcParams['ekate'])) {
            return [
                'type' => 'existNum',
            ];
        }

        $transaction = $UsersController->startTransaction();

        try {
            $editingIds = explode(',', $rpcParams['editingIds']);
            $editingIds = $UsersController->ArrayHelper->filterEmptyStringArr($editingIds);
            $editingIdsCount = count($editingIds);
            // edit original polygons
            for ($i = 0; $i < $editingIdsCount; $i++) {
                $options = [
                    'tablename' => $UserDbController->DbHandler->tableKVS,
                    'mainData' => [
                        'is_edited' => true,
                        'edit_date' => date('Y-m-d'),
                        'edit_active_from' => $rpcParams['active'],
                    ],
                    'where' => [
                        'gid' => $editingIds[$i],
                    ],
                ];

                $UserDbController->editItem($options);
                $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['options' => $options], [], 'KVS Merge - edit original polygon');
            }

            // add new polygon
            $options = [
                'tablename' => $UserDbController->DbHandler->tableKVS,
                'mainData' => [
                    'kad_ident' => $rpcParams['ekate'] . '.' . $rpcParams['masiv'] . '.' . $rpcParams['number'],
                    'ekate' => $rpcParams['ekate'],
                    'masiv' => $rpcParams['masiv'],
                    'number' => $rpcParams['number'],
                    'category' => $rpcParams['category'],
                    'area_type' => $rpcParams['ntp'],
                    'mestnost' => $rpcParams['mestnost'],
                    'usable' => $rpcParams['usable'],
                    'document_area' => $rpcParams['area'],
                    'edit_active_from' => $rpcParams['active'],
                    'has_contracts' => 'FALSE',
                    'used_area_by' => 1,
                    'area_farming' => 0,
                    'area_year' => 0,
                    'is_edited' => 'FALSE',
                ],
                'id_name' => 'gid',
            ];

            $plotID = $UserDbController->addItem($options);

            $array[0] = (object) [];
            $array[0]->id = $plotID;
            $array[0]->geometry = $rpcParams['mergeGeom'];
            $UserDbController->updateGeometry($UserDbController->DbHandler->tableKVS, $array);
            $UserDbController->updateKvsBorders([$rpcParams['ekate']]);

            $UserDbController->updateAllowableArea($UserDbController->DbHandler->tableKVS, [$plotID]);

            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['options' => $options], ['created_gid' => $plotID], 'KVS Merge - add new polygon');

            // add kvs edit log
            for ($i = 0; $i < $editingIdsCount; $i++) {
                $options = [
                    'tablename' => $UserDbController->DbHandler->tableKvsEditLog,
                    'mainData' => [
                        'new_gid' => $plotID,
                        'old_gid' => $editingIds[$i],
                        'edit_type' => 'merge',
                        'edit_date' => date('Y-m-d'),
                        'edit_active_from' => $rpcParams['active'],
                    ],
                ];

                $recordID = $UserDbController->addItem($options);

                $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['options' => $options], ['created_id' => $recordID], 'KVS Merge - edit log');
            }

            $transaction->commit();

            // get contracts for editing plotс
            $options = [
                'return' => [
                    'DISTINCT ON(c.id) c.id as c_id', 'c.c_num',
                ],
                'where' => [
                    'active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'true'],
                    'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'prefix' => 'c', 'value' => 'false'],
                    'is_annnex' => ['column' => 'is_annex', 'compare' => '=', 'prefix' => 'c', 'value' => 'false'],
                    'annex_action' => ['column' => 'annex_action', 'compare' => '=', 'prefix' => 'pc', 'value' => 'added'],
                    'gid' => ['column' => 'gid', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $editingIds],
                    'archited_plot' => ['column' => '(kvs.is_edited = false or kvs.edit_active_from > \'' . $rpcParams['active'] . '\')', 'compare' => '=', 'value' => 'true'],
                ],
                'farming_start_date' => $rpcParams['active'],
                'farming_due_date' => '9999-12-31',
            ];

            $results = $UserDbPlotsController->getFullPlotDataForDeclaration($options, false, false);
            $resultsCount = count($results);
            $response = [];
            if ($resultsCount > 0) {
                for ($i = 0; $i < $resultsCount; $i++) {
                    $response['c_id'] .= $results[$i]['c_id'] . ',';
                    $response['c_num'] .= $results[$i]['c_num'] . ', ';
                }
                $response['c_id'] = trim($response['c_id'], ',');
                $response['c_num'] = trim($response['c_num'], ', ');

                return [
                    'type' => 'contracts',
                    'contracts' => $response,
                ];
            }
        } catch (Exception $e) {
            $transaction->rollBack();

            throw $e;
        }
    }

    private function isPlotNumberExists($plotNum, $plotMasiv, $plotEkate)
    {
        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableKVS,
            'return' => ['number::numeric'],
            'where' => [
                'ekate' => ['column' => 'ekate', 'compare' => '=', 'value' => $plotEkate],
                'masiv' => ['column' => 'masiv', 'compare' => '=', 'value' => $plotMasiv],
                'number' => ['column' => 'number', 'compare' => '=', 'value' => $plotNum],
            ],
        ];

        $results = $UserDbController->getItemsByParams($options);

        return (count($results) > 0);
    }
}
