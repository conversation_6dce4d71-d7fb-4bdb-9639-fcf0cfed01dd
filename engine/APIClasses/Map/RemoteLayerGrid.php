<?php

namespace TF\Engine\APIClasses\Map;

use TF\Application\Common\Config;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

/**
 * 'Необлагодетелствани райони'.
 *
 * @rpc-module Map
 *
 * @rpc-service-id remote-layer-datagrid
 */
class RemoteLayerGrid extends BaseGrid
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getRemoteLayerGrid'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer layer_id
     *                         #item boolean clear_filter
     *                         #item integer layer_type
     *                         #item string  sort
     *                         #item string  action
     *                         #item integer layer
     *                         #item array   gids
     *                         {
     *                         #item integer gid
     *                         }
     *                         }
     * @param int $page pagination parameter
     * @param int $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array
     *               {
     *               #item array rows
     *               #item integer total
     *               }
     */
    public function getRemoteLayerGrid(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $UserDbController = new UserDbController($this->User->Database);

        // clean old results
        $results = [];
        $return = [];

        // creating a default empty return
        $empty_return = [
            'rows' => [],
            'total' => 0,
        ];
        if (!isset($rpcParams['layer_id'])) {
            return $empty_return;
        }

        $options = [];
        switch ($rpcParams['layer_type']) {
            case Config::LAYER_TYPE_LFA:
                $options['options'] = $this->buildLayerLFA($rpcParams);

                break;
            case Config::LAYER_TYPE_NATURA_2000:
                $options['options'] = $this->buildLayerNatura_2000($rpcParams);

                break;
            case Config::LAYER_TYPE_DS_PRC:
                $options['options'] = $this->buildLayerDsPrc($rpcParams);

                break;
            case Config::LAYER_TYPE_PERMANENT_GRASSLAND_FOR_MOWING:
                $options['options'] = $this->buildLayerPermanentGrasslandForMowing($rpcParams);

                break;
            case Config::LAYER_TYPE_PERMANETELY_GREEN_AREAS:
                $options['options'] = $this->buildLayerPZP($rpcParams);

                break;
            default:
                $options['options'] = $this->buildLayerPZP($rpcParams);

                break;
        }

        $options['options']['offset'] = ($page - 1) * $rows;
        $options['options']['limit'] = $rows;
        $options['options']['sort'] = $sort;
        $options['options']['order'] = $order;

        if (Config::LAYER_TYPE_LFA == $rpcParams['layer_type']) {
            if ('ekate' == $rpcParams['sort']) {
                $options['options']['sort'] = 'nm_lfa_eka';
            } elseif ('lfa_area_type' == $rpcParams['sort']) {
                $options['options']['sort'] = 'nm_lfa_lfa';
            } elseif ('zemlishte' == $rpcParams['sort']) {
                $options['options']['sort'] = 'nm_lfa_e_1';
            } else {
                $options['options']['sort'] = 'gid';
            }
        }

        if (Config::LAYER_TYPE_NATURA_2000 == $rpcParams['layer_type']) {
            if ('zone_name' == $options['options']['sort']) {
                $options['options']['sort'] = 'name_bg';
            } elseif (0 == $UserDbController->getColumnNameExist('layer_natura_2000', $options['options']['sort'])) {
                $options['options']['sort'] = 'gid';
            }
        }

        if (Config::LAYER_TYPE_PERMANETELY_GREEN_AREAS == $rpcParams['layer_type']) {
            if ('zone_name' == $options['options']['sort']) {
                $options['options']['sort'] = 'zeml';
            } elseif (0 == $UserDbController->getColumnNameExist('layer_pzp', $options['options']['sort'])) {
                $options['options']['sort'] = 'gid';
            }
        }

        if ($rpcParams['layer'] == $rpcParams['layer_type']) {
            $layer_results = $this->getLayersCached($options, true);
            $layer_results = $layer_results['data'];
            $layer_resultsCount = count($layer_results);
        }

        $postResult = $this->getLayersCached($options, false);
        $counter = $postResult['count'];
        $layer_results = $postResult['data'];
        $layer_resultsCount = count($layer_results);
        $results = [];
        if (0 != count($layer_results)) {
            for ($i = 0; $i < $layer_resultsCount; $i++) {
                if (Config::LAYER_TYPE_LFA == $rpcParams['layer_type']) {
                    $results[$i]['id'] = $layer_results[$i]['id'];
                    $results[$i]['gid'] = $layer_results[$i]['gid'];
                    $results[$i]['zemlishte'] = $layer_results[$i]['nm_lfa_e_1'];
                    $results[$i]['lfa_area_type'] = ($layer_results[$i]['nm_lfa_lfa']) ? ('НР ' . $layer_results[$i]['nm_lfa_lfa']) : '';
                    $results[$i]['ekate'] = $layer_results[$i]['nm_lfa_eka'];
                    $results[$i]['st_astext'] = $layer_results[$i]['st_astext'];
                } elseif (Config::LAYER_TYPE_NATURA_2000 == $rpcParams['layer_type']) {
                    $results[$i]['gid'] = $layer_results[$i][array_key_first($layer_results[$i])];
                    $results[$i]['zone_name'] = $layer_results[$i]['name_bg'];
                    $results[$i]['area_z_dka'] = number_format($layer_results[$i]['area_z_dka'], 3);
                    $results[$i]['zapoved_no'] = $layer_results[$i]['zapoved_no'];
                    $results[$i]['dv'] = $layer_results[$i]['dv'];
                    $results[$i]['sitecode'] = $layer_results[$i]['sitecode'];
                    $results[$i]['st_astext'] = $layer_results[$i]['st_astext'];
                    $results[$i]['bans'] = $GLOBALS['Farming']['natura_zones'][$layer_results[$i]['sitecode']]['bans'];// natura_bans
                    foreach ($results[$i]['bans'] as $key => $ban) {
                        $results[$i]['bans'][$key] = $GLOBALS['Farming']['natura_bans'][$ban]['text'];
                    }
                    $results[$i]['bans'] = implode(',<br/>', $results[$i]['bans']);

                    $total_area_z_dka = $counter[0]['total_area_z_dka'];

                    $return['footer'] = [
                        [
                            'zone_name' => '<b>ОБЩО</b>',
                            'area_z_dka' => number_format($total_area_z_dka, 3),
                        ],
                    ];
                } elseif (Config::LAYER_TYPE_PERMANETELY_GREEN_AREAS == $rpcParams['layer_type']) {
                    $results[$i]['gid'] = $layer_results[$i]['gid'];
                    $results[$i]['imotcode'] = $layer_results[$i]['imotcode'];
                    $results[$i]['zeml'] = $layer_results[$i]['zeml'];
                    $results[$i]['sharea'] = number_format($layer_results[$i]['sharea'] / 1000, 3);
                    $results[$i]['pzp_area'] = number_format($layer_results[$i]['pzp_area'] / 1000, 3);
                    $results[$i]['st_astext'] = $layer_results[$i]['st_astext'];

                    $total_sharea = $counter[0]['total_sharea'];
                    $total_pzp_area = $counter[0]['total_pzp_area'];

                    $return['footer'] = [
                        [
                            'zeml' => '<b>ОБЩО</b>',
                            'sharea' => $total_sharea,
                            'pzp_area' => $total_pzp_area,
                        ],
                    ];
                } else {
                    $results[] = $layer_results[$i];
                }
            }
            $return['rows'] = $results;
        } else {
            $return['rows'] = false;
        }

        foreach ($results as $result) {
            $filteredGidsFromResults[] = (int) $result['gid'];
        }

        $return['rows'] = $results;
        $return['total'] = $counter[0]['count'];
        $return['filtered_gids'] = $filteredGidsFromResults ? $filteredGidsFromResults : [];

        return $return;
    }

    public function getLayersCached($options)
    {
        $LayersController = new LayersController('Layers');

        $options['options']['tablename'] . 'post';
        $options = $options['options'];
        $hash = md5(json_encode($options));

        if ($result = $LayersController->MemCache->get($hash)) {
            return $result;
        }

        $result = [];

        $result['data'] = $LayersController->getRemoteLayerData($options, false, false);
        $result['count'] = $LayersController->getRemoteLayerData($options, true, false);

        $LayersController->MemCache->add($hash, $result, $LayersController->default_memcache_expire);

        return $result;
    }

    private function buildLayerLFA(array $rpcParams)
    {
        $options = [
            'tablename' => 'layer_lfa',
            'return' => [
                'id', 'gid', 'nm_lfa_e_1', 'nm_lfa_eka', 'nm_lfa_lfa', 'ST_ASTEXT(geom)',
            ],
            'full_text_search_columns' => ['nm_lfa_eka', 'nm_lfa_lfa', 'nm_lfa_e_1'],
        ];

        $this->buildWhere($options, $rpcParams);

        return $options;
    }

    private function buildLayerPZP(array $rpcParams)
    {
        $options = [
            'tablename' => 'layer_pzp',
            'return' => [
                'gid', 'imotcode', 'zeml', 'sharea', 'pzp_area',  'ST_ASTEXT(geom)',
            ],
            'custom_counter' => 'COUNT(DISTINCT(gid)), SUM(round((sharea/1000)::numeric, 3)) as total_sharea, SUM(round((pzp_area/1000)::numeric, 3)) as total_pzp_area',
            'full_text_search_columns' => ['imekatte', 'imotcode'],
        ];

        $this->buildWhere($options, $rpcParams);

        return $options;
    }

    private function buildLayerNatura_2000(array $rpcParams)
    {
        $options = [
            'tablename' => 'layer_natura_2000',
            'return' => [
                'objectid_1', 'name_bg', 'area_z_dka', 'zapoved_no', 'dv', 'sitecode',
                'ST_ASTEXT(ST_SIMPLIFY(geom, 2.5))',
            ],
            'custom_counter' => 'COUNT(DISTINCT(gid)), SUM(area_z_dka) as total_area_z_dka',
            'full_text_search_columns' => ['name_bg'],
        ];

        $this->buildWhere($options, $rpcParams);

        return $options;
    }

    private function buildLayerDsPrc(array $rpcParams)
    {
        $options = [
            'tablename' => 'layer_ds_prc',
            'return' => [
                'gid',
                'claim_year',
                'crop_code',
                'crop_name',
                'is_pg',
                'is_bio', 'ST_ASTEXT(geom)',
            ],
            'full_text_search_columns' => ['crop_name'],
        ];

        $this->buildWhere($options, $rpcParams);

        return $options;
    }

    private function buildLayerPermanentGrasslandForMowing(array $rpcParams)
    {
        $options = [
            'tablename' => 'layer_permanent_grassland_for_mowing',
            'return' => [
                'gid',
                'ekatte',
                'phbident',
                'elgident',
                'usagecode',
                'usagebul',
                'usageeng',
                'ST_ASTEXT(geom)',
            ],
            'full_text_search_columns' => ['usagebul'],
        ];

        $this->buildWhere($options, $rpcParams);

        return $options;
    }
}
