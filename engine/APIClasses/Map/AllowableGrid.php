<?php

namespace TF\Engine\APIClasses\Map;

use Prado\Prado;
use TF\Engine\Plugins\Core\Layers\LayersController;

/**
 * Допустим слой.
 *
 * @rpc-module Map
 *
 * @rpc-service-id allowable-datagrid
 */
class AllowableGrid extends BaseGrid
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getAllowableGrid'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'exportToExcel' => ['method' => [$this, 'exportToExcel']],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer layer_id
     *                         #item boolean clear_filter
     *                         #item string  ident
     *                         #item string  ekate
     *                         #item string  fbident
     *                         #item string  action
     *                         }
     * @param ?string $sort
     * @param ?string $order
     *
     * @return array
     */
    public function getAllowableGrid(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $LayersController = new LayersController('Layers');

        // clean old results
        $return = [];

        // creating a default empty return
        $empty_return = [
            'rows' => [],
            'total' => 0,
        ];

        if (!isset($rpcParams['layer_id'])) {
            return $empty_return;
        }

        $options = [
            'return' => [
                'gid', 'elg_ident', 'elgarea', 'ekatte_', 'fbident', 'fb_area', 'ntp_k', 'ntp', 'zemlishte', 'ST_ASTEXT(geom)',
            ],
            'full_text_search_columns' => ['elg_ident', 'ekatte_', 'fbident', 'zemlishte', 'elgarea', 'fb_area'],
        ];
        if ('d' == $rpcParams['layer_id']) {
            $options['tablename'] = 'layer_allowable';
        } elseif ('df' == $rpcParams['layer_id']) {
            $options['tablename'] = 'layer_allowable_final';
        }

        $options['offset'] = ($page - 1) * $rows;
        $options['limit'] = $rows;
        $options['sort'] = $sort;
        $options['order'] = $order;

        $this->buildWhere($options, $rpcParams);
        $hash = md5(json_encode($options));
        $result = $LayersController->MemCache->get($hash);
        if (!$result) {
            $result['data'] = $LayersController->getAllowableLayerData($options, false, false);
            $result['count'] = $LayersController->getAllowableLayerData($options, true, false);
            $LayersController->MemCache->add($hash, $result, $LayersController->default_memcache_expire);
        }

        $layer_results = $result['data'];
        $layer_results_count = count($layer_results);
        $counter = $result['count'];

        $results = [];
        $filteredGidsFromResults = [];
        if (0 != $layer_results_count) {
            for ($i = 0; $i < $layer_results_count; $i++) {
                $results[$i]['gid'] = $layer_results[$i]['gid'];
                $results[$i]['ident'] = $layer_results[$i]['elg_ident'];
                $results[$i]['elgarea'] = number_format($layer_results[$i]['elgarea'], 2);
                $results[$i]['area_zp'] = $layer_results[$i]['zemlishte'];
                $results[$i]['zemlishte'] = $layer_results[$i]['zemlishte'];
                $results[$i]['fbident'] = $layer_results[$i]['fbident'];
                $results[$i]['ekate'] = $layer_results[$i]['eaktte_'];
                $results[$i]['ntp'] = $layer_results[$i]['ntp'];
                $results[$i]['fb_area'] = number_format($layer_results[$i]['fb_area'], 2);
                $results[$i]['st_astext'] = $layer_results[$i]['st_astext'];
                $filteredGidsFromResults[] = $results[$i]['gid'];
            }
        }

        $return['rows'] = $results;
        $return['total'] = $counter[0]['count'];
        $return['filtered_gids'] = $filteredGidsFromResults;

        return $return;
    }

    public function exportToExcel(array $rpcParams)
    {
        $time = strtotime(date('Y-m-d H:i:s'));
        $userPath = $this->User->UserID . '/allowable_layer_table_' . $time . '.xlsx';
        $filename = PUBLIC_UPLOAD_EXPORT . '/' . $userPath;
        $headers = [
            'ident' => 'Идентификатор',
            'elgarea' => 'Площ(ха)',
            'area_zp' => 'Землище',
            'fbident' => 'Физически блок',
            'fb_area' => 'Площ на физически блок (ха)',
        ];

        $data = $this->getAllowableGrid($rpcParams);

        $exportExcelDoc = Prado::getApplication()->getModule('ExportToExcel');
        $exportExcelDoc->export($data['rows'], $headers, []);
        $exportExcelDoc->saveFile($filename);

        return 'files/uploads/export/' . $userPath;
    }
}
