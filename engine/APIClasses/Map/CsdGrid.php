<?php

namespace TF\Engine\APIClasses\Map;

use TF\Engine\Kernel\ExportToExcelClass;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

/**
 * Атрибутна информация 'Заявления и декларации'.
 *
 * @rpc-module Map
 *
 * @rpc-service-id csd-datagrid
 */
class CsdGrid extends BaseGrid
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getCsdGrid'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'exportToExcel' => ['method' => [$this, 'exportToExcel'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * @api-method read
     *
     * @param int $page pagination parameter
     * @param int $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array
     */
    public function getCsdGrid(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $UserDbController = new UserDbController($this->User->Database);
        $LayersController = new LayersController('Layers');

        $empty_return = [
            'rows' => [],
            'total' => 0,
        ];

        if (isset($rpcParams['layer_id']) && (int) $rpcParams['layer_id']) {
            $layer_result = $LayersController->getLayerData($rpcParams['layer_id']);
        } else {
            return $empty_return;
        }

        if (!$layer_result['is_exist']) {
            return $empty_return;
        }

        $options = [
            'return' => [
                '*',
                'ST_ASTEXT(geom)',
                'round((st_area(geom)/1000)::numeric, 3) as area',
            ],
            'custom_counter' => 'COUNT(DISTINCT(gid)), SUM(round((st_area(geom)/1000)::numeric, 3)) as total_area',
            'tablename' => $layer_result['table_name'],
            'full_text_search_columns' => ['kad_ident', 'ekatte', 'ntp', 'applicant', 'declaration_type', 'locality'],
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
            'layer_id' => $rpcParams['layer_id'],
        ];

        $this->buildWhere($options, $rpcParams);

        $results = $UserDbController->getLayersByParams($options, false);
        [$counter] = $UserDbController->getLayersByParams($options, true);
        $filteredGidsFromResults = array_column($results, 'gid');

        $return['rows'] = $results;
        $return['total'] = $counter['count'];

        $return['filtered_gids'] = $filteredGidsFromResults ?? [];

        $total_area = $counter['total_area'];

        $return['footer'] = [
            [
                'prc_uin' => '<b>ОБЩО</b>',
                'area' => $total_area,
            ],
        ];

        return $return;
    }

    /**
     * @api-method exportToExcel
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer   layer_type
     *                         #item integer   layer_id
     *                         #item array   ids
     *                         }
     * @param ?string $sort
     * @param ?string $order
     *
     * @return string
     */
    public function exportToExcel(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $time = strtotime(date('Y-m-d H:i:s'));
        $file = '/applications_and_declarations_' . $time . '.xlsx';
        $filename = PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID . $file;

        if (!file_exists(PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID)) {
            mkdir(PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID, 0777);
        }

        $headers = [
            'kad_ident' => 'Идентификатор',
            'area' => 'Площ(ха)',
            'ntp' => 'Тип',
            'category' => 'Категория',
            'locality' => 'Населено място',
            'document_area' => 'Площ по документ (дка)',
            'declared_area_status' => 'Declared area status',
            'allowable_area' => 'Площ в допустим слой (дка)',
            'applicant' => 'Заявител',
            'declared_area' => 'Декларирана площ (дка)',
            'lessor' => 'Наемодател',
            'declaration_type' => 'Тип декларация',
            'wish' => 'Желание',
            'year' => 'Година',
        ];

        $data = $this->getCsdGrid($rpcParams, $page, $rows, $sort, $order);

        $totalArea = $data['footer'][0]['area'];
        $footerArray = [
            'kad_ident' => 'ОБЩО',
            'area' => $totalArea,
            'ntp' => '',
            'category' => '',
            'locality' => '',
            'document_area' => '',
            'declared_area_status' => '',
            'allowable_area' => '',
            'applicant' => '',
            'declared_area' => ')',
            'lessor' => '',
            'declaration_type' => '',
            'wish' => '',
            'year' => '',
        ];
        $data['rows'][] = $footerArray;

        $exportExcelDoc = new ExportToExcelClass();
        $exportExcelDoc->export($data['rows'], $headers, []);
        $exportExcelDoc->saveFile($filename);

        return 'files/uploads/export/' . $this->User->UserID . $file;
    }
}
