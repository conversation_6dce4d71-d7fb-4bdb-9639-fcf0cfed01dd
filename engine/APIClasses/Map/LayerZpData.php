<?php

namespace TF\Engine\APIClasses\Map;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Layers\LayersController;

// Prado::using('Plugins.Core.Layers.conf');
// Prado::using('Plugins.Core.Layers.*');

/**
 * Извличане на данните за земеделски парцела за Technofarm Mobile.
 *
 * @rpc-module Map
 *
 * @rpc-service-id layer-zp-data
 */
class LayerZpData extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'getAllZpLayersData' => ['method' => [$this, 'getAllZpLayersData']],
        ];
    }

    /**
     * @api-method getAllZpLayersData
     * Get all zp layers
     *
     * @return array all zp layers
     */
    public function getAllZpLayersData()
    {
        $LayersController = new LayersController('Layers');

        // layer Zemedelski parcel
        $layer_type = $GLOBALS['Layers']['srid'][0]['type'];
        $user_id = $this->User->GroupID;
        $layersByFarming = [];

        $layers = $LayersController->getLayersByType($layer_type, $user_id);
        $layersCount = count($layers);

        for ($i = 0; $i < $layersCount; $i++) {
            $layer = $layers[$i];

            $layersByFarming[$layer['farming_name']][] = $layer;
        }

        return $layersByFarming;
    }
}
