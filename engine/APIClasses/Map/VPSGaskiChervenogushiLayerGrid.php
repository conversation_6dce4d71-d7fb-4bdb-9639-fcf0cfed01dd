<?php

namespace TF\Engine\APIClasses\Map;

use TF\Engine\Plugins\Core\Layers\LayersController;

/**
 * 'ВПС Червеногуши гъски'.
 *
 * @rpc-module Map
 *
 * @rpc-service-id layer-vps-gaski-chervenogushi-datagrid
 */
class VPSGaskiChervenogushiLayerGrid extends BaseGrid
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getVPSGaskiChervenogushiLayerGrid'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer layer_id
     *                         #item boolean clear_filter
     *                         #item strin   ekate
     *                         #item string  action
     *                         #item integer layer_type
     *                         #item integer layer
     *                         #item array   gids
     *                         {
     *                         #item integer gid
     *                         }
     *                         }
     * @param int $page pagination parameter
     * @param int $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array
     */
    public function getVPSGaskiChervenogushiLayerGrid(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $LayersController = new LayersController('Layers');
        $return = [];

        // creating a default empty return
        $empty_return = [
            'rows' => [],
            'total' => 0,
        ];
        if (!isset($rpcParams['layer_id'])) {
            return $empty_return;
        }

        $options = [];

        // for search/sort actions */
        $options['options'] = [
            'tablename' => 'layer_vps_gaski_chervenogushi',
            'return' => [
                'gid', 'name', 'ekatte', 'ST_ASTEXT(geom)',
            ],
            'full_text_search_columns' => ['ekatte', 'name'],
            'layer_id' => $rpcParams['layer_id'],
        ];

        if (0 == $LayersController->getColumnNameExistInDefaultDatabase('layer_vps_gaski_chervenogushi', $options['options']['sort'])) {
            $options['options']['sort'] = 'gid';
        }

        $options['options']['offset'] = ($page - 1) * $rows;
        $options['options']['limit'] = $rows;
        $options['options']['sort'] = $sort;
        $options['options']['order'] = $order;

        $this->buildWhere($options['options'], $rpcParams);

        $postResult = $this->getLayersCached($options);

        $counter = $postResult['count'];
        $layer_results = $postResult['data'];
        $layersCount = count($layer_results);
        $results = [];

        if (0 != $layersCount) {
            for ($i = 0; $i < $layersCount; $i++) {
                $results[$i]['gid'] = $layer_results[$i]['gid'];
                $name = explode(',', $layer_results[$i]['name']);
                $results[$i]['name'] = $name[1];
                $results[$i]['vps_type'] = 'Земи с ВПС в местообитания на червеногушата гъска';
                $results[$i]['ekatte'] = $layer_results[$i]['ekatte'];
                $results[$i]['st_astext'] = $layer_results[$i]['st_astext'];
            }
        }

        $return['rows'] = $results;
        $return['total'] = $counter[0]['count'];

        return $return;
    }

    private function getLayersCached(array $options)
    {
        $LayersController = new LayersController('Layers');

        $options['options']['tablename'] . 'post';
        $options = $options['options'];
        $hash = md5(json_encode($options));
        $result = $LayersController->MemCache->get($hash);
        if ($result) {
            return $result;
        }

        $result = [];

        $result['data'] = $LayersController->getRemoteLayerData($options, false, false);
        $result['count'] = $LayersController->getRemoteLayerData($options, true, false);

        $LayersController->MemCache->add($hash, $result, $LayersController->default_memcache_expire);

        return $result;
    }
}
