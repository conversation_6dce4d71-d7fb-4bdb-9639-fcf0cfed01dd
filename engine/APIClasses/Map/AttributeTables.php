<?php

namespace TF\Engine\APIClasses\Map;

use Prado\Web\Services\TRpcApiProvider;
use TF\Application\Common\Config;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbForIsak\UserDbForIsakController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Информация за бутон "Мащабирай".
 *
 * @rpc-module Map
 *
 * @rpc-service-id attribute-tables
 */
class AttributeTables extends TRpcApiProvider
{
    private $module = 'Map';
    private $service_id = 'attribute-tables';

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'getMaxExtent' => ['method' => [$this, 'getMaxExtent']],
            'executeMultiEdit' => ['method' => [$this, 'executeMultiEdit']],
        ];
    }

    /**
     * @api-method getMaxExtent
     *
     * @param array $rpcParams
     *                         {
     *                         #item string tablename
     *                         #item string id_name
     *                         #item int[] gids (optional)
     *                         }
     *
     * @return array
     */
    public function getMaxExtent($rpcParams)
    {
        $LayersController = new LayersController('Layers');
        $UserDbController = new UserDbController($this->User->Database);

        if (!$rpcParams || !$rpcParams['tablename'] || !$rpcParams['id_name']) {
            return [];
        }

        $tablename = $rpcParams['tablename'];

        $options = [
            'tablename' => $tablename,
            'return' => [
                'St_Extent(geom) as extent',
            ],
        ];
        $filteredPlots = $_SESSION['filtered_plots'];

        if (count($rpcParams['gids'])) {
            if (null === $filteredPlots) {
                $filteredPlots = [];
            }
            if (!array_key_exists($tablename, $filteredPlots)) {
                $filteredPlots[$tablename] = [];
            }
            foreach ($rpcParams['gids'] as $index => $gid) {
                if (!in_array($gid, $filteredPlots[$tablename], false)) {
                    $filteredPlots[$tablename][] = $gid;
                }
            }
        }

        if (!empty($rpcParams['gids'])) {
            $options['where'] = [
                'id' => ['column' => $rpcParams['id_name'], 'compare' => 'IN', 'value' => $rpcParams['gids']],
            ];
        } elseif (count($filteredPlots[$tablename])) {
            $options['where'] = [
                'id' => ['column' => $rpcParams['id_name'], 'compare' => 'IN', 'value' => $filteredPlots[$tablename]],
            ];
        }

        if ('layer_allowable' == $rpcParams['layer_type']
            || Config::LAYER_TYPE_LFA == $rpcParams['layer_type']
            || Config::LAYER_TYPE_NATURA_2000 == $rpcParams['layer_type']
            || Config::LAYER_TYPE_PERMANETELY_GREEN_AREAS == $rpcParams['layer_type']
            || Config::LAYER_TYPE_VPS_PASISHTA == $rpcParams['layer_type']
            || Config::LAYER_TYPE_VPS_GASKI_CHERVENOGUSHI == $rpcParams['layer_type']
            || Config::LAYER_TYPE_VPS_GASKI_ZIMNI == $rpcParams['layer_type']
            || Config::LAYER_TYPE_VPS_LIVADEN_BLATAR == $rpcParams['layer_type']
            || Config::LAYER_TYPE_VPS_ORLI_LESHOYADI == $rpcParams['layer_type']) {
            $options['rowtypes'] = [
                'extent box2d',
            ];
            $results = $LayersController->getAllowableLayerData($options);
        } else {
            $results = $UserDbController->getItemsByParams($options);
        }

        $maxextent = $results[0]['extent'];
        $maxextent = str_replace(['BOX(', ')', ' '], ['', '', ','], $maxextent);

        return $maxextent;
    }

    /**
     * @api-method executeMultiEdit
     *
     * @param array $data
     *                    {
     *                    #item string  layer_name
     *                    #item string  ekatte
     *                    #item boolean common_cultures
     *                    #item string  culture
     *                    #item array   plotIds
     *                    {
     *                    #item integer ids
     *                    }
     *
     * @throws MTRpcException
     */
    public function executeMultiEdit($rpcParams)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbForIsakController = new UserDbForIsakController($this->User->Database);
        $UsersController = new UsersController('Users');

        $tableName = $rpcParams['layer_name'];
        $ekatte = $rpcParams['ekatte'];
        $as_intermediate_crop = $rpcParams['common_cultures'];
        $culture = $rpcParams['culture'];
        $plotIds = $rpcParams['plotIds'];

        if ('null' == $culture) {
            $culture = ' ';
        }

        $tableExists = $UserDbController->getTableNameExist($tableName);

        if (!$tableExists) {
            throw new MTRpcException('MAP_REQUESTED_LAYER_NOT_FOUND', -33053);
        }

        $id_array = [];

        $crop = $GLOBALS['Farming']['crops'][$culture];

        // set crop_short_type and green_area_factor
        $crop_short_type = '';
        $green_area_factor = 1;

        if ($crop['azot_fixed_crop']) {
            $crop_short_type .= 'АФК, ';
            $green_area_factor = $GLOBALS['Farming']['crops_short_type']['azot_fixed_crop']['factor_enp'];
        }
        if ($crop['is_tree_short_rotation']) {
            $crop_short_type .= 'ДВКЦР, ';
            $green_area_factor = $GLOBALS['Farming']['crops_short_type']['is_tree_short_rotation']['factor_enp'];
        }
        // Угар
        if ('190000' == $culture) {
            $crop_short_type .= 'УГАР, ';
            $green_area_factor = $GLOBALS['Farming']['crops_short_type']['190000']['factor_enp'];
        }

        if (strlen($crop_short_type)) {
            $crop_short_type = substr($crop_short_type, 0, -2);
        }

        if (!empty($_SESSION['filtered_plots']) && empty($plotIds)) {
            $plotIds = $_SESSION['filtered_plots'][$tableName];
        }
        if (empty($plotIds) && empty($_SESSION['filtered_plots'])) {
            $plotIds[] = 0;
        }

        $options = [
            'tablename' => $tableName,
            'id_string' => implode(', ', $plotIds),

            'update' => [
                'cropcode' => $culture,
                'ekatte' => 'null' == $ekatte ? ' ' : $ekatte,
                'crop_type' => $crop['crop_type'] ?? null,
                'crop_genus' => $crop['crop_genus'] ?? null,
                'azot_fixed_crop' => (($crop['azot_fixed_crop']) ? 'true' : 'false'),
                'is_intermediate_crop' => (($crop['is_intermediate_crop']) ? 'true' : 'false'),
                'is_intermediate_weat_crop' => (($crop['is_intermediate_weat_crop']) ? 'true' : 'false'),
                'is_tree_short_rotation' => (($crop['is_tree_short_rotation']) ? 'true' : 'false'),
                'no_pndn' => (($crop['no_pndn']) ? 'true' : 'false'),
                'cropname' => $crop['crop_name'] ?? null,
                'crop_short_type' => strlen($crop_short_type) ? $crop_short_type : ' ',
                'green_area_factor' => $green_area_factor,
                'common_cultures' => 'false',
            ],
        ];

        foreach ($options['update'] as $key => $option) {
            if (null == $option) {
                unset($options['update'][$key]);
            }
        }

        $UserDbForIsakController->ForIsakMultiEdit($options);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['rpcParams' => $rpcParams, 'affected_plots' => $plotIds], ['options_array' => $options], 'Executing multiedit');
    }
}
