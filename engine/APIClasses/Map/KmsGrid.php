<?php

namespace TF\Engine\APIClasses\Map;

use TF\Engine\Kernel\ExportToExcelClass;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Атрибутна информация "Данни от комасиация".
 *
 * @rpc-module Map
 *
 * @rpc-service-id kms-datagrid
 */
class KmsGrid extends BaseGrid
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getKmsGrid'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'exportToExcel' => ['method' => [$this, 'exportToExcel']],
            'multiEdit' => ['method' => [$this, 'multiEdit']],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer layer_id
     *                         #item string  table_name
     *                         #item boolean clear_filter
     *                         #item string  action
     *                         #item string  ekatte
     *                         #item integer crop_code
     *                         #item string  bzz
     *                         }
     * @param int $page pagination parameter
     * @param int $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array
     */
    public function getKmsGrid(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $UserDbController = new UserDbController($this->User->Database);
        $LayersController = new LayersController('Layers');

        $return = [];
        $filteredGidsFromResults = [];
        // creating a default empty return
        $empty_return = [
            'rows' => [],
            'total' => 0,
        ];

        if (isset($rpcParams['layer_id']) && (int)$rpcParams['layer_id']) {
            $layer_result = $LayersController->getLayerData($rpcParams['layer_id']);
        } else {
            return $empty_return;
        }

        $tableExists = $UserDbController->getTableNameExist($layer_result['table_name']);
        if (!$tableExists) {
            return $empty_return;
        }

        $ekatte = $this->getConsolidationEKATTE($layer_result['table_name']);

        $options = [
            'tablename' => $layer_result['table_name'],
            'return' => ['name', 'EKATTE', 'crop_code', 'round((st_area(geom)/1000)::numeric, 3) as area', 'ST_ASTEXT(geom), gid, virtual_crop_name', 'ST_Centroid(geom) as centroid'],
            'full_text_search_columns' => ['name', 'virtual_crop_name', 'ekatte'],
            'offset' => ($page - 1) * $rows,
            'custom_counter' => "COUNT(DISTINCT({$layer_result['table_name']}.gid)), SUM(round((ST_Area({$layer_result['table_name']}.geom) / 1000)::numeric, 3)) as total_area",
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
            'layer_id' => $rpcParams['layer_id'],
        ];

        $this->withNuz($options, ($ekatteColumn = 'ekatte'), $rpcParams);
        $this->withPhysicalBlock($options, ($ekatteColumn), $rpcParams);

        $this->buildWhere($options, $rpcParams);

        $counter = $UserDbController->getLayersByParams($options, true);
        $results = $UserDbController->getLayersByParams($options, false);
        $filteredGidsFromResults = [];

        $results = array_map(function ($item) use ($ekatte, &$filteredGidsFromResults) {
            $item['ekatte'] = $ekatte[$item['ekatte']];
            if ($item['crop_code']) {
                $item['crop_name'] = $item['virtual_crop_name'];
            }

            $filteredGidsFromResults[] = $item['gid'];

            return $item;
        }, $results);

        $return['rows'] = $results;
        $return['total'] = $counter[0]['count'];
        $return['filtered_gids'] = $filteredGidsFromResults ? $filteredGidsFromResults : [];

        $total_area = $counter[0]['total_area'];

        $return['footer'] = [
            [
                'crop_code' => '<b>ОБЩО</b>',
                'area' => $total_area,
            ],
        ];

        return $return;
    }

    /**
     * @api-method exportToExcel
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer layer_id
     *                         #item string  table_name
     *                         #item boolean clear_filter
     *                         #item string  action
     *                         #item string  ekatte
     *                         #item integer crop_code
     *                         #item string  bzz
     *                         }
     *
     * @return string
     */
    public function exportToExcel(array $rpcParams)
    {
        $time = strtotime(date('Y-m-d H:i:s'));

        $filename = PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID . '/kms_table_' . $time . '.xlsx';

        if (!file_exists(PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID)) {
            mkdir(PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID, 0777);
        }
        $headers = [
            'name' => 'Номер БЗЗ',
            'ekatte' => 'Землище',
            'crop_code' => 'Земеделска култура',
            'area' => 'Площ (дка)',
        ];

        $rpcParams['forExport'] = true;
        $data = $this->getKmsGrid($rpcParams);

        $exportExcelDoc = new ExportToExcelClass();
        $exportExcelDoc->export($data['rows'], $headers);
        $exportExcelDoc->saveFile($filename);

        return 'files/uploads/export/' . $this->User->UserID . '/kms_table_' . $time . '.xlsx';
    }

    protected function prepareUpdateOptions(array $rpcParams, array $editableColumns): array
    {
        $options = [];
        foreach ($rpcParams['edited_columns'] as $key => $prop) {
            if ('crop_code' === $key) {
                $options['crop_code'] = $prop;

                continue;
            }

            if (in_array($key, $editableColumns)) {
                $options[$key] = $prop;
            }
        }

        return $options;
    }

    private function getConsolidationEKATTE(string $tablename)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $options = [
            'tablename' => $tablename,
            'return' => ['ekatte'],
            'where' => [
                'ekatte' => ['column' => 'ekatte', 'compare' => 'IS NOT', 'value' => 'NULL'],
            ],
            'group' => 'ekatte',
        ];

        $results = $UserDbController->getItemsByParams($options);
        $results_count = count($results);
        if (0 === $results_count) {
            return;
        }
        $ekatteByCode = [];
        for ($i = 0; $i < $results_count; $i++) {
            $ekatteByCode[$results[$i]['ekatte']] = $UsersController->getEkatteName($results[$i]['ekatte']);
        }

        return $ekatteByCode;
    }
}
