<?php

namespace TF\Engine\APIClasses\Map;

use Exception;
use TF\Application\Common\Config;
use TF\Application\Entity\LayerStyles;
use TF\Application\Entity\UserLayers;
use TF\Engine\APIClasses\Plots\PlotsTree;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

/**
 * Атрибутна информация.
 *
 * @rpc-module Map
 *
 * @rpc-service-id layer-datagrid
 */
class LayerDatagrid extends BaseGrid
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getGrid'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * @api-method read
     *
     * @param ?string $sort
     * @param ?string $order
     *
     * @return array
     */
    public function getGrid(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $layersController = new LayersController('Layers');
        $userDbController = new UserDbController($this->User->Database);

        $layer = UserLayers::getLayerById($rpcParams['layer_id']);

        if (!$layer) {
            throw new Exception('Layer not found', 404);
        }

        if (Config::LAYER_TYPE_CADASTRE == $layer->layer_type) {
            return $this->getCadastreDataGrid($rpcParams);
        }
        /**
         * @var LayersController|UserDbController $controller
         */
        $controller = $layer->isRemote() ? $layersController : $userDbController;

        if (
            !$controller->getTableNameExist($layer->table_name)
            && !$controller->getViewNameExists($layer->table_name)
        ) {
            return [
                'total' => 0,
                'header' => [],
                'rows' => [],
                'footer' => [],
                'filtered_gids' => [],
            ];
        }

        $layerDefinitions = $layer->getDefinitions();
        [$gidColumnDef] = UserLayers::filterDefinitions($layerDefinitions, [['col_category' => Config::LAYER_COLUMN_CATEGORY_GID]]);

        if (Config::LAYER_TYPE_KVS == $rpcParams['layer_type']) {
            $rpcParams['forKvsGrid'] = true;
            $hasFilterGroups = count($rpcParams['filter_params']['groups'] ?? []) > 0;
            $plotsTree = new PlotsTree($this->rpcServer);

            return $plotsTree->getPlots(
                $rpcParams,
                $page,
                $rows,
                $sort,
                $order,
                false,
                $hasFilterGroups,
                false,
                $rpcParams['group_by'] ?? $gidColumnDef['col_name'],
            );
        }

        $personalizableOrVisibleColumns = UserLayers::getColumns(UserLayers::filterDefinitions(
            $layerDefinitions,
            [['col_visible' => true], ['col_personalizable' => true]]
        ));

        [$layerStyle] = $layer->getStyles();
        $groupByColumn = $this->getGroupByColumn($layerDefinitions, $rpcParams['group_by']);
        $columnsToSelect = $this->generateColumnsToSelect($layerDefinitions, $groupByColumn, $layerStyle, $layer);
        $customCounter = $this->generateCustomCounter($layerDefinitions, $groupByColumn);
        [$sort, $order] = $this->buildSortOptions($sort, $order, $layerDefinitions, $groupByColumn);

        $options = [
            'tablename' => $layer->table_name,
            'return' => $columnsToSelect,
            'custom_counter' => $customCounter,
            'limit' => $rows,
            'offset' => ($page - 1) * $rows,
            'sort' => $sort,
            'order' => $order,
            'where' => [],
            'whereOr' => [],
            'whereOrGroup' => [],
            'full_text_search_columns' => $personalizableOrVisibleColumns,
        ];

        if ($groupByColumn !== $gidColumnDef['col_name']) {
            $options['group'] = $groupByColumn;
        }

        $this->buildWhere($options, $rpcParams);

        $ekatteColumnDef = $layer->getDefinitionByCategory(Config::LAYER_COLUMN_CATEGORY_EKATTE);
        if ($ekatteColumnDef && $rpcParams['with_nuz']) {
            $this->withNuz($options, $ekatteColumnDef['col_name'], $rpcParams);
        }

        if ($ekatteColumnDef && $rpcParams['with_physical_block']) {
            $this->withPhysicalBlock($options, $ekatteColumnDef['col_name'], $rpcParams);
        }

        $header = $this->generateHeader($layerDefinitions, $groupByColumn);

        $rows = array_map(
            fn ($item) => json_decode($item['row'], true),
            $controller->getItemsByParams($options)
        );

        $totalOptions = $options;
        unset($totalOptions['group']);
        [$total] = $controller->getItemsByParams($totalOptions, true);

        return [
            'header' => $header,
            'total' => $total['count'],
            'rows' => $rows,
            'footer' => array_filter(
                $total ?? [],
                fn ($key) => 'count' !== $key,
                ARRAY_FILTER_USE_KEY
            ),
            'filtered_gids' => array_reduce($rows, function ($carry, $item) use ($gidColumnDef, $groupByColumn) {
                if ($groupByColumn === $gidColumnDef['col_name']) {
                    return [
                        ...$carry,
                        $item[$gidColumnDef['col_name']],
                    ];
                }

                $childrenGids = array_map(fn ($child) => $child[$gidColumnDef['col_name']], $item['children']);

                return [
                    ...$carry,
                    ...$childrenGids,
                ];
            }, []),
        ];
    }

    private function generateColumnsToSelect(array $layerDefinitions, string $groupByColumn, LayerStyles $layerStyle, UserLayers $layer): array
    {
        $personalizableOrVisibleColumnsDef = UserLayers::filterDefinitions(
            $layerDefinitions,
            [
                ['col_visible' => true],
                ['col_personalizable' => true],
            ]
        );

        [$gidColumnDef] = UserLayers::filterDefinitions($layerDefinitions, [['col_category' => Config::LAYER_COLUMN_CATEGORY_GID]]);
        [$geomColumnDef] = UserLayers::filterDefinitions($layerDefinitions, [['col_category' => Config::LAYER_COLUMN_CATEGORY_GEOM]]);
        $colorColumnsDefs = UserLayers::filterDefinitions($layerDefinitions, [['col_category' => Config::LAYER_COLUMN_CATEGORY_COLOR]]);

        $columnsToSelect = [
            $geomColumnDef,
            $gidColumnDef,
            ...$personalizableOrVisibleColumnsDef,
            ...$colorColumnsDefs,
        ];

        $chunkSize = 40; // Maximum number of columns to select in a single JSONB_BUILD_OBJECT call
        $columnsToSelectSql = array_reduce(
            array_chunk($columnsToSelect, $chunkSize),
            function ($carry, $chunk) {
                $chunkSql = 'JSONB_BUILD_OBJECT(' . implode(",\n", array_map(
                    function ($colDef) {
                        if (Config::LAYER_COLUMN_CATEGORY_BOOLEAN === $colDef['col_category']) {
                            return "'{$colDef['col_name']}', CASE WHEN {$colDef['col_name']} THEN 'Yes' ELSE 'No' END";
                        }

                        if (Config::LAYER_COLUMN_CATEGORY_DATE === $colDef['col_category']) {
                            return "'{$colDef['col_name']}', to_char({$colDef['col_name']}, 'DD.MM.YYYY')";
                        }

                        // TODO: Update after adding LAYER_COLUMN_CATEGORY_AREA
                        $columnValue = false !== strpos($colDef['col_name'], 'area') && Config::LAYER_COLUMN_CATEGORY_NUMBER === $colDef['col_category']
                            ? "ROUND(COALESCE(\"{$colDef['col_name']}\"::numeric, 0), 3)::TEXT"
                            : "\"{$colDef['col_name']}\"";

                        return "'{$colDef['col_name']}', {$columnValue}";
                    },
                    $chunk
                )) . ')';

                return strlen($carry) > 0 ? "{$carry} || {$chunkSql}" : $chunkSql;
            },
            ''
        );

        $virtualColumnDefinitions = UserLayers::filterDefinitions($layer->getDefinitions(), [['col_virtual' => true]]);
        $virtualColumnDefinitions = array_combine(array_column($virtualColumnDefinitions, 'col_reference'), $virtualColumnDefinitions);

        $fillVirtualColumnName = $virtualColumnDefinitions[$layerStyle->fill_column_name]['col_name'];
        $borderVirtualColumnName = $virtualColumnDefinitions[$layerStyle->border_column_name]['col_name'];

        $fillColumnName = LayerStyles::BY_ATTRIBUTE_COLORING_TYPE === $layerStyle->type && $layerStyle->fill_column_name
            ? ($fillVirtualColumnName ? "'{$fillVirtualColumnName}'" : "'{$layerStyle->fill_column_name}'")
            : 'NULL';
        $borderColumnName = LayerStyles::BY_ATTRIBUTE_COLORING_TYPE === $layerStyle->type && $layerStyle->border_column_name
            ? ($borderVirtualColumnName ? "'{$borderVirtualColumnName}'" : "'{$layerStyle->border_column_name}'")
            : 'NULL';

        if ($groupByColumn === $gidColumnDef['col_name']) {
            $showFillAndBorderColors = $layerStyle->type = LayerStyles::BY_ATTRIBUTE_COLORING_TYPE ? 'TRUE' : 'FALSE';

            // Grouping by GID means no grouping
            return ["
                {$columnsToSelectSql} ||
                JSONB_BUILD_OBJECT(
                    'key', uuid_generate_v4(),
                    '{$groupByColumn}', \"{$groupByColumn}\",
                    'fill_color', CASE WHEN {$showFillAndBorderColors} THEN \"fill_color\" ELSE NULL END,
                    'fill_column_name', {$fillColumnName},
                    'border_color', CASE WHEN {$showFillAndBorderColors} THEN \"border_color\" ELSE NULL END,
                    'border_column_name', {$borderColumnName},
                    'gid', \"{$groupByColumn}\", -- ensure there is key 'gid' and is equal to field's primary key column (needed in FE)
                    'children', '[]'::JSONB
                ) as row
            "];
        }

        // Grouped by $groupByColumn

        /**
         * @var array $parentColumns - Aggregated SUM for each personalizable or visible column (except $groupByColumn)
         *            of category 'number' that contains 'area' in its name
         * */
        $parentColumnsSql = array_reduce(
            array_chunk($columnsToSelect, $chunkSize),
            function ($carry, $chunk) use ($groupByColumn) {
                $chunkSql = 'JSONB_BUILD_OBJECT(' . implode(",\n", array_map(
                    function ($colDef) use ($groupByColumn) {
                        if ($colDef['col_name'] === $groupByColumn) {
                            // Return the group by column
                            return "'{$colDef['col_name']}', \"{$colDef['col_name']}\"";
                        }

                        // TODO: Update after adding LAYER_COLUMN_CATEGORY_AREA
                        if (false !== strpos($colDef['col_name'], 'area') && Config::LAYER_COLUMN_CATEGORY_NUMBER === $colDef['col_category']) {
                            // Return aggregated sum for each personalizable or visible column of category 'number' that contains 'area' in its name
                            return "'{$colDef['col_name']}', COALESCE(SUM(ROUND(\"{$colDef['col_name']}\"::numeric, 3)), 0)::TEXT";
                        }

                        // Return null for the rest columns
                        return "'{$colDef['col_name']}', null";
                    },
                    $chunk
                )) . ')';

                return strlen($carry) > 0 ? "{$carry} || {$chunkSql}" : $chunkSql;
            },
            ''
        );

        $showFillColor = $layerStyle->type = LayerStyles::BY_ATTRIBUTE_COLORING_TYPE && in_array($groupByColumn, [$fillVirtualColumnName, $layerStyle->fill_column_name]) ? 'TRUE' : 'FALSE';
        $showBorderColor = $layerStyle->type = LayerStyles::BY_ATTRIBUTE_COLORING_TYPE && in_array($groupByColumn, [$borderVirtualColumnName, $layerStyle->border_column_name]) ? 'TRUE' : 'FALSE';

        return ["
            {$parentColumnsSql} ||
            JSONB_BUILD_OBJECT(
                'key', uuid_generate_v4(),
                'fill_color', CASE WHEN {$showFillColor} THEN MAX(\"fill_color\") ELSE NULL END,
                'fill_column_name', {$fillColumnName},
                'border_color', CASE WHEN {$showBorderColor} THEN MAX(\"border_color\") ELSE NULL END,
                'border_column_name', {$borderColumnName},
                'gid', ROW_NUMBER() OVER(), -- ensure there is key 'gid' in the result (needed in FE)
                '{$groupByColumn}', CASE WHEN \"{$groupByColumn}\" IS NULL THEN 'No value' ELSE normalize_value(\"{$groupByColumn}\"::TEXT)  END, -- overwrite the value for the column we use for grouping
                'children', JSONB_AGG(DISTINCT
                    {$columnsToSelectSql} ||
                    JSONB_BUILD_OBJECT(
                        'key', uuid_generate_v4(),
                        'fill_column_name', {$fillColumnName},
                        'border_column_name', {$borderColumnName},
                        'gid', \"{$gidColumnDef['col_name']}\" -- ensure there is key 'gid' and is equal to field's primary key column
                    )
                )
            ) as row
        "];
    }

    private function generateHeader(array $layerDefinitions, string $groupByColumn): array
    {
        $columns = UserLayers::filterDefinitions(
            $layerDefinitions,
            [
                ['col_visible' => true],
                ['col_name' => $groupByColumn],
            ]
        );

        [$gidColumnDef] = UserLayers::filterDefinitions($layerDefinitions, [['col_category' => Config::LAYER_COLUMN_CATEGORY_GID]]);

        return array_map(function ($column) use ($groupByColumn, $gidColumnDef) {
            $sortable = true;

            if ($gidColumnDef['col_name'] !== $groupByColumn) {
                // Sort only by parent columns with value
                $sortable = (
                    (
                        false !== strpos($column['col_name'], 'area')
                        && Config::LAYER_COLUMN_CATEGORY_NUMBER === $column['col_category']
                    )
                    || $column['col_name'] === $groupByColumn
                );
            }

            return [
                'field' => $column['col_name'],
                'title' => $column['col_title'],
                'visible' => $column['col_visible'],
                'sortable' => $sortable,
            ];
        }, $columns);
    }

    private function getGroupByColumn(array $layerDefinitions, ?string $groupByColumn): string
    {
        $groupByDef = [];

        [$gidDef] = UserLayers::filterDefinitions($layerDefinitions, [['col_category' => Config::LAYER_COLUMN_CATEGORY_GID]]);
        [$groupByDef] = UserLayers::filterDefinitions(
            $layerDefinitions,
            [
                ['col_name' => $groupByColumn, 'col_visible' => true],
            ]
        );

        return $groupByDef['col_name'] ?? $gidDef['col_name'];
    }

    private function buildSortOptions(?string $sort, ?string $order, array $layerDefinitions, string $groupByColumn): array
    {
        $sortColumn = null;
        $orderDirection = null;
        [$gidDef] = UserLayers::filterDefinitions($layerDefinitions, [['col_category' => Config::LAYER_COLUMN_CATEGORY_GID]]);

        if (0 === strlen($sort ?? '') || 0 === strlen($order ?? '')) {
            // Sort or order not set => return null values
            return [$sortColumn, $orderDirection];
        }

        if ($sort === $groupByColumn) {
            // Sort is same as the grouping column
            return [$sort, $order];
        }

        $sortColumnIdx = array_search($sort, array_column($layerDefinitions, 'col_name'));
        $sortColumnDef = $layerDefinitions[$sortColumnIdx] ?? null;

        if (false === $sortColumnIdx || !$sortColumnDef) {
            // Column not found in layer definitions => return null values
            return [$sortColumn, $orderDirection];
        }

        if (strlen($groupByColumn) > 0 && $groupByColumn !== $gidDef['col_name']) {
            // The result is grouped => can sort only by parent columns
            if (
                false !== strpos($sortColumnDef['col_name'], 'area')
                && Config::LAYER_COLUMN_CATEGORY_NUMBER === $sortColumnDef['col_category']
            ) {
                // If group by column is set and different than 'gid', sort by parent (aggregated) columns
                $sortColumn = "SUM(ROUND(\"{$sortColumnDef['col_name']}\"::numeric, 3))";
            }
        } else {
            // The result is not grouped => can sort by any column
            $sortColumn = $sort;
        }

        if (isset($sortColumn)) {
            $orderDirection = $order;
        }

        return [$sortColumn, $orderDirection];
    }

    /**
     * This method generates the sql select statement for the custom counter.
     * It selects items count and the sum for each area column.
     */
    private function generateCustomCounter(array $layerDefinitions, string $groupByColumn)
    {
        $areaColumnsDefs = array_filter(
            $layerDefinitions,
            fn ($colDef) => false !== strpos($colDef['col_name'], 'area') && Config::LAYER_COLUMN_CATEGORY_NUMBER === $colDef['col_category'] // TODO: Update after adding LAYER_COLUMN_CATEGORY_AREA
        );

        $areaColumns = array_map(
            fn ($colDef) => "COALESCE(SUM(ROUND(\"{$colDef['col_name']}\"::numeric, 3)), 0)::TEXT as \"{$colDef['col_name']}\"",
            $areaColumnsDefs
        );

        // Note: Use array_length and array_agg instead of count in order to count the null values too
        return count($areaColumns) > 0
            ? "ARRAY_LENGTH(ARRAY_AGG(DISTINCT \"{$groupByColumn}\"), 1) AS count, " . implode(', ', $areaColumns)
            : "ARRAY_LENGTH(ARRAY_AGG(DISTINCT \"{$groupByColumn}\"), 1) AS count";
    }

    private function getCadastreDataGrid(array $rpcParams): array
    {
        $cadastreLayerConfig = $GLOBALS['Layers']['customLayers'][Config::LAYER_TYPE_CADASTRE];
        $cadastreLayer = UserLayers::getLayerById($cadastreLayerConfig['id']);
        $cadastreLayerDataGrid = new LayerCadastre($this->rpcServer);

        $data = $cadastreLayerDataGrid->getData($rpcParams);

        $header = $this->generateHeader($cadastreLayer->getDefinitions(), 'kad_ident');

        return [
            'header' => $header,
            'total' => $data['total'] ?? 0,
            'rows' => $data['total'] > 0 ? $data['rows'] : [],
            'footer' => [],
        ];
    }
}
