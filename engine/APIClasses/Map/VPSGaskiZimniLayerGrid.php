<?php

namespace TF\Engine\APIClasses\Map;

use TF\Engine\Plugins\Core\Layers\LayersController;

/**
 * Атрибутна информация "ВПС Зимуващи гъски".
 *
 * @rpc-module Map
 *
 * @rpc-service-id layer-vps-gaski-zimni-datagrid
 */
class VPSGaskiZimniLayerGrid extends BaseGrid
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getVPSGaskiZimniLayerGrid'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer layer_id
     *                         #item boolean clear_filter
     *                         #item string  ekate
     *                         #item integer layer
     *                         #item integer layer_type
     *                         #item array   gids
     *                         {
     *                         #item integer gid
     *                         }
     *                         }
     * @param int $page pagination parameter
     * @param int $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array
     */
    public function getVPSGaskiZimniLayerGrid(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $LayersController = new LayersController('Layers');

        $return = [];

        // creating a default empty return
        $empty_return = [
            'rows' => [],
            'total' => 0,
        ];
        if (!isset($rpcParams['layer_id'])) {
            return $empty_return;
        }

        $options = [];

        // for search/sort actions */

        $options['options'] = [
            'tablename' => 'layer_vps_gaski_zimni',
            'return' => [
                'gid', 'zem', 'ekatte', 'ST_ASTEXT(geom)',
            ],
            'full_text_search_columns' => ['ekatte', 'name'],
            'layer_id' => $rpcParams['layer_id'],
        ];

        $options['options']['offset'] = ($page - 1) * $rows;
        $options['options']['limit'] = $rows;
        $options['options']['sort'] = $sort;
        $options['options']['order'] = $order;

        if (0 == $LayersController->getColumnNameExistInDefaultDatabase('layer_vps_gaski_zimni', $options['options']['sort'])) {
            $options['options']['sort'] = 'gid';
        }
        $this->buildWhere($options['options'], $rpcParams);

        $postResult = $this->getLayersCached($options);
        $counter = $postResult['count'];
        $layer_results = $postResult['data'];
        $layersCount = count($layer_results);
        $results = [];
        if (0 != $layersCount) {
            for ($i = 0; $i < $layersCount; $i++) {
                $results[$i]['gid'] = $layer_results[$i]['gid'];
                $results[$i]['zem'] = $layer_results[$i]['zem'];
                $results[$i]['vps_type'] = 'Земи с ВПС в местообитания на зимуващите видове гъски';
                $results[$i]['ekatte'] = $layer_results[$i]['ekatte'];
                $results[$i]['st_astext'] = $layer_results[$i]['st_astext'];
            }
        }

        $return['rows'] = $results;
        $return['total'] = $counter[0]['count'];

        return $return;
    }

    private function getLayersCached(array $options)
    {
        $LayersController = new LayersController('Layers');
        $options['options']['tablename'] . 'post';
        $options = $options['options'];
        $hash = md5(json_encode($options));
        $result = $LayersController->MemCache->get($hash);
        if ($result) {
            return $result;
        }

        $result = [];
        $result['data'] = $LayersController->getRemoteLayerData($options, false, false);
        $result['count'] = $LayersController->getRemoteLayerData($options, true, false);

        $LayersController->MemCache->add($hash, $result, $LayersController->default_memcache_expire);

        return $result;
    }
}
