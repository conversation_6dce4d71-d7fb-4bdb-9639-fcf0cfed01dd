<?php

namespace TF\Engine\APIClasses\Map;

use TF\Engine\Kernel\ExportToExcelClass;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

/**
 * Атрибутна информация "Временни данни".
 *
 * @rpc-module Map
 *
 * @rpc-service-id gps-datagrid
 */
class GpsGrid extends BaseGrid
{
    private $headers = [];
    private $footer = [];

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getGpsGrid'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'exportToExcel' => ['method' => [$this, 'exportToExcel']],
            'multiEdit' => ['method' => [$this, 'multiEdit']],
        ];
    }

    /**
     * @api-method exportToExcel
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer layer_id
     *                         #item string  table_name
     *                         #item boolean clear_filter
     *                         #item string  action
     *                         #item string  ekatte
     *                         #item integer crop_code
     *                         #item string  bzz
     *                         }
     *
     * @return string
     */
    public function exportToExcel(array $rpcParams)
    {
        $time = strtotime(date('Y-m-d H:i:s'));

        $filename = PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID . '/gps_layer_table_' . $time . '.xlsx';

        if (!file_exists(PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID)) {
            mkdir(PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID, 0777);
        }

        $rpcParams['forExport'] = true;
        $data = $this->getGpsGrid($rpcParams);

        $exportExcelDoc = new ExportToExcelClass();
        $exportExcelDoc->export($data['rows'], $this->headers, $this->footer);
        $exportExcelDoc->saveFile($filename);

        return 'files/uploads/export/' . $this->User->UserID . '/gps_layer_table_' . $time . '.xlsx';
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer layer_id
     *                         #item string  table_name
     *                         #item boolean clear_filter
     *                         #item string  filtered_plots
     *                         #item string  plot_name
     *                         #item string  plot_info
     *                         #item string  action
     *                         #item array   gids
     *                         }
     * @param string $page pagination parameter
     * @param string $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array
     *{
     *    #item integer gid
     *    #item string plot_name
     *    #item string plot_info
     *    #item string area
     *    #item string st_astext
     *}
     */
    public function getGpsGrid(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $UserDbController = new UserDbController($this->User->Database);
        $LayersController = new LayersController('Layers');

        $return = [];

        // creating a default empty return
        $empty_return = [
            'rows' => [],
            'total' => 0,
        ];

        if (!isset($rpcParams['layer_id']) || !(int)$rpcParams['layer_id']) {
            return $empty_return;
        }

        $layer_result = $LayersController->getLayerData($rpcParams['layer_id']);
        $tableExists = $UserDbController->getTableNameExist($layer_result['table_name']);

        if (!$tableExists) {
            return $empty_return;
        }

        $options = [
            'tablename' => $layer_result['table_name'],
            'return' => [
                'gid', 'plot_name', 'round((ST_Area(geom) / 1000)::numeric, 3) AS area',
                'ST_ASTEXT(geom)', 'plot_info',
            ],
            'full_text_search_columns' => ['plot_name', 'plot_info'],
            'custom_counter' => 'COUNT(DISTINCT(gid)), SUM(round((ST_Area(geom)/1000)::numeric, 3)) as total_area',
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
            'layer_id' => $rpcParams['layer_id'],
        ];

        $this->buildWhere($options, $rpcParams);
        $results = $UserDbController->getLayersByParams($options, false);
        $counter = $UserDbController->getLayersByParams($options, true);
        $resultsCount = count($results);

        for ($i = 0; $i < $resultsCount; $i++) {
            if (!$rpcParams['forExport']) {
                $filteredGidsFromResults[] = $results[$i]['gid'];
            }
        }

        if ($rpcParams['forExport']) {
            $this->setHeaders($results);
        }

        $return['rows'] = $results;
        $return['total'] = $counter[0]['count'];

        $return['filtered_gids'] = $filteredGidsFromResults ? $filteredGidsFromResults : [];

        $total_area = $counter[0]['total_area'];

        $return['footer'] = [
            [
                'plot_name' => '<b>ОБЩО</b>',
                'area' => $total_area,
            ],
        ];
        $this->footer = $return['footer'];

        return $return;
    }

    private function setHeaders($results)
    {
        if (array_key_exists('st_astext', $results[0])) {
            unset($results[0]['st_astext']);
        }

        if (array_key_exists('gid', $results[0])) {
            unset($results[0]['gid']);
        }

        if (array_key_exists('geom', $results[0])) {
            unset($results[0]['geom']);
        }
        $this->headers = array_combine(array_keys($results[0]), array_keys($results[0]));
    }
}
