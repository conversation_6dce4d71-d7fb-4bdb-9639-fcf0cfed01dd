<?php

namespace TF\Engine\APIClasses\Map;

use Exception;
use Prado\Prado;
use Prado\Web\Services\TRpcApiProvider;
use TF\Application\Common\Config;
use TF\Application\Entity\UserLayers;
use TF\Engine\APIClasses\Plots\PlotsTree;
use TF\Engine\Kernel\ExportData\LayersExportFactory;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Exports layers into .zip with .shp files.
 *
 * @rpc-module Map
 *
 * @rpc-service-id export-layer
 */
class ExportLayerData extends TRpcApiProvider
{
    private $exportableLayerTypes = [
        Config::LAYER_TYPE_FOR_ISAK => 'TF\Engine\Kernel\ExportData\LayerTypes\LayerForISAK',
        Config::LAYER_TYPE_ZP => 'TF\Engine\Kernel\ExportData\LayerTypes\LayerZP',
        Config::LAYER_TYPE_KMS => 'TF\Engine\Kernel\ExportData\LayerTypes\LayerKMS',
        Config::LAYER_TYPE_KVS => 'TF\Engine\Kernel\ExportData\LayerTypes\LayerKVS',
        Config::LAYER_TYPE_ISAK => 'TF\Engine\Kernel\ExportData\LayerTypes\LayerISAK',
        Config::LAYER_TYPE_VPS_ORLI_LESHOYADI => 'TF\Engine\Kernel\ExportData\LayerTypes\LayerOrliLeshoyadi',
        Config::LAYER_TYPE_PERMANETELY_GREEN_AREAS => 'TF\Engine\Kernel\ExportData\LayerTypes\LayerGreenAreas',
        Config::LAYER_TYPE_GPS => 'TF\Engine\Kernel\ExportData\LayerTypes\LayerGPS',
        Config::LAYER_TYPE_WORK_LAYER => 'TF\Engine\Kernel\ExportData\LayerTypes\LayerWork',
        'layer_allowable' => 'TF\Engine\Kernel\ExportData\LayerTypes\LayerAllowable',
        'layer_allowable_final' => 'TF\Engine\Kernel\ExportData\LayerTypes\LayerAllowableFinal',
        Config::LAYER_TYPE_CADASTRE => 'TF\Engine\Kernel\ExportData\LayerTypes\LayerCadastre',
        Config::LAYER_TYPE_DS_PRC => 'TF\Engine\Kernel\ExportData\LayerTypes\LayerDsPrc',
    ];

    /**
     * Registers the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'export' => ['method' => [$this, 'exportLayer'],
                'validators' => [
                    'data' => [
                        'export_type' => 'validateRequired, validateNotNull',
                        'layer_name' => 'validateRequired, validateNotNull',
                        'layer_type' => 'validateRequired, validateNotNull',
                    ],
                ],
            ],
            'exporToModem' => ['method' => [$this, 'exportLayerForModem'],
                'validators' => [
                    'data' => [
                        'export_type' => 'validateRequired, validateNotNull',
                        'layer_name' => 'validateRequired, validateNotNull',
                        'layer_type' => 'validateRequired, validateNotNull',
                        'layer_id' => 'validateRequired, validateNotNull',
                        'device_id' => 'validateRequired, validateNotNull',
                    ],
                ],
            ],
        ];
    }

    /**
     * Exports layer in .shp and returns url to file.
     *
     * @api-method export
     *
     * @param array $data {
     *                    #item string export_type
     *                    #item string layer_name
     *                    #item string layer_type
     *                    #item string layer_id
     *                    #item boolean united
     *                    #item boolean export_old
     *                    #item boolean use_filter
     *                    #item array selected_ids
     *                    #item string output_device
     *                    }
     *
     * @throws MTRpcException
     *
     * @return string url for downloading the file
     */
    public function exportLayer($data)
    {
        try {
            $filePath = $this->prepareExport($data);

            if (file_exists($filePath)) {
                return 'files/uploads/export/' . $this->User->UserID . '/' . basename($filePath);
            }

            throw new Exception('Export error');
        } catch (MTRpcException $exception) {
            throw new MTRpcException($exception->getCustomErrorMessage(), $exception->getErrorCodeNumber());
        } catch (Exception $exception) {
            throw new MTRpcException('Export error', -33052);
        }
    }

    public function exportLayerForModem($data)
    {
        $filePath = $this->prepareExport($data);
        $data['export_type'] = 'exportTrimble';
        $data['united'] = false;

        $UsersController = new UsersController('Users');
        $userId = $this->User->UserID;
        $modems = $UsersController->getUserModems([
            'return' => ['serial'],
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $data['device_id']],
                'user_id' => ['column' => 'user_id', 'compare' => '=', 'value' => $userId],
            ],
        ]);
        $modemSerial = $modems[0]['serial'];
        $deviceModule = Prado::getApplication()->getModule('deviceModule');
        $uplResp = $deviceModule->uploadFile($modemSerial, $filePath);

        return $deviceModule->setFileStatus($modemSerial, $uplResp['file_id'], 'sending');
    }

    private function prepareExport($data)
    {
        if (!array_key_exists($data['layer_type'], $this->exportableLayerTypes)) {
            throw new MTRpcException('Unexportable layer', -33051);
        }

        $data['forKvsGrid'] = true;

        if (!empty($data['selected_ids'])) {
            $data['id_array'] = array_map('intval', $data['selected_ids']);
        }

        $layer = UserLayers::getLayerById($data['layer_id']);

        if (!$layer) {
            throw new MTRpcException('Layer not found', 404);
        }

        $layersExportFactory = new LayersExportFactory();

        if ('layer_allowable' == $data['layer_type'] && 'df' == $data['layer_id']) {
            $data['layer_type'] = 'layer_allowable_final';
        }

        if (Config::LAYER_TYPE_CADASTRE == $data['layer_type']) {
            $layerDataGrid = new LayerDatagrid($this->rpcServer);
            $data['filter_params']['plot_ids'] = $data['selected_ids'];
            $cadastreData = $layerDataGrid->getGrid($data);
            $rows = $cadastreData['rows'] ?? [];
            $data['from_values'] = UserLayers::generateValuesExprByDefinitions($rows, $layer->getDefinitions());
        }

        $layerClass = $this->exportableLayerTypes[$data['layer_type']];
        $exportClass = $layersExportFactory->getExportClass($data['export_type'], $layerClass, $data['layer_id']);

        if (empty($data['selected_ids']) && !empty($data['filter_params'])) {
            $data['id_array'] = $this->getFilteredIds($data);
            $data['selected_ids'] = $data['id_array'];
        }

        $exportClass->setData($data);

        if (isset($data['united']) && false == $data['united']) {
            $filePath = $exportClass->exportSeparateFiles();
        } else {
            $filePath = $exportClass->exportOneFile();
        }

        return $filePath;
    }

    private function getFilteredIds(array $rpcParams)
    {
        switch ($rpcParams['layer_type']) {
            case Config::LAYER_TYPE_KVS :
                $plotsTree = new PlotsTree($this->rpcServer);
                $id_array = $plotsTree->getPlots($rpcParams, null, null, '', '', false, $hasFilterGroups = true, true);

                break;
            case Config::LAYER_TYPE_ISAK :
                $grid = makeApiClass('map-rpc', 'isak-datagrid');
                $id_array = $grid->getISAKGrid($rpcParams)['filtered_gids'];

                break;
            case Config::LAYER_TYPE_FOR_ISAK :
                $grid = makeApiClass('map-rpc', 'for-isak-datagrid');
                $id_array = $grid->getForIsakGrid($rpcParams)['filtered_gids'];

                break;
            case Config::LAYER_TYPE_ZP :
                $grid = makeApiClass('map-rpc', 'zp-datagrid');
                $id_array = $grid->getZPGrid($rpcParams)['filtered_gids'];

                break;
            case Config::LAYER_TYPE_KMS :
                $grid = makeApiClass('map-rpc', 'kms-datagrid');
                $id_array = $grid->getKmsGrid($rpcParams)['filtered_gids'];

                break;
            case Config::LAYER_TYPE_WORK_LAYER :
                $grid = makeApiClass('map-rpc', 'work-layer-datagrid');
                $id_array = $grid->getWorkLayerGrid($rpcParams)['filtered_gids'];

                break;
            case Config::LAYER_TYPE_GPS :
                $grid = makeApiClass('map-rpc', 'gps-datagrid');
                $id_array = $grid->getGpsGrid($rpcParams)['filtered_gids'];

                break;
            case Config::LAYER_TYPE_PERMANETELY_GREEN_AREAS :
                $grid = makeApiClass('map-rpc', 'remote-layer-datagrid');
                $id_array = $grid->getRemoteLayerGrid($rpcParams)['filtered_gids'];

                break;
            default:
                $id_array = [];
        }

        return $id_array;
    }
}
