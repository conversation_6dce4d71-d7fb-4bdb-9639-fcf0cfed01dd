<?php

namespace TF\Engine\APIClasses\Map;

use Prado\Prado;
use Prado\Web\Services\TRpcApiProvider;
use TF\Application\Common\Config;
use TF\Application\Entity\UserLayers;
use TF\Engine\Kernel\ExportData\LayerTypes\LayerWork;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbOSZ\UserDbOSZController;
use TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * MTSoapService class file.
 *
 * <AUTHOR> Ltd. <<EMAIL>>
 *
 * @link http://www.devision.bg/
 *
 * @copyright Copyright &copy; 2008 Devision Ltd.
 */

/**
 * Попълва таблица с информация за избран обект от картата.
 *
 * @rpc-module Map
 *
 * @rpc-service-id layer-pg
 */
class LayersPropertyGrid extends TRpcApiProvider
{
    private $module = 'Map';
    private $service_id = 'layer-pg';

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getLayersPropertyGrid']],
            'getOSZPlotData' => ['method' => [$this, 'getOSZPlotData']],
            'calculateAvgSlopeByLayer' => [
                'method' => [$this, 'calculateAvgSlopeByLayer'],
                'validators' => [
                    'layer_id' => 'validateRequired,validateNotNull',
                    'plots_gids' => 'validateNumberArray',
                ],
            ],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item string bbox
     *                         #item string x
     *                         #item string y
     *                         #item string width
     *                         #item string height
     *                         }
     *
     * @return array
     */
    public function getLayersPropertyGrid($rpcParams = '')
    {
        $UserDbController = new UserDbController($this->User->Database);
        $layer = UserLayers::getLayerById($rpcParams['layer']);
        $return = ['rows' => [], 'total' => 0];

        if (!$layer) {
            return $return;
        }

        $rpcParams['width'] = $rpcParams['width'] > 2048 ? 2048 : $rpcParams['width'];
        $rpcParams['height'] = $rpcParams['height'] > 2048 ? 2048 : $rpcParams['height'];

        $rpcParams['width'] = $rpcParams['width'] < 1 ? 1 : $rpcParams['width'];
        $rpcParams['height'] = $rpcParams['height'] < 1 ? 1 : $rpcParams['height'];

        if (!isset($rpcParams['request'])) {
            $urlRequest = WMS_SERVER_INTERNAL
                . '?REQUEST=GetFeatureInfo'
                . '&EXCEPTIONS=application/vnd.ogc.se_xml'
                . '&VERSION=1.1.1'
                . '&MAP=' . WMS_MAP_PATH . $this->User->GroupID . '.map'
                . '&BBOX=' . $rpcParams['bbox']
                . '&X=' . $rpcParams['x']
                . '&Y=' . $rpcParams['y']
                . '&INFO_FORMAT=text/plain'
                . '&QUERY_LAYERS=' . $layer->table_name
                . '&LAYERS=' . $layer->table_name
                . '&FEATURE_COUNT=50'
                . '&SRS=EPSG:900913'
                . '&STYLES='
                . '&WIDTH=' . $rpcParams['width']
                . '&HEIGHT=' . $rpcParams['height'];
            $curl = curl_init($urlRequest);
            curl_setopt($curl, CURLOPT_FAILONERROR, true);
            curl_setopt($curl, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
            $result = curl_exec($curl);

            if (curl_errno($curl)) {
                $error_msg = curl_error($curl);
            }
        }

        $tablename = trim(str_replace("'", '', $layer->table_name));
        $layer_type = $layer->layer_type;

        if (!isset($rpcParams['request'])) {
            $re = '/Feature (?<plot_id>[0-9]+)/m';
            preg_match_all($re, $result, $matches, PREG_SET_ORDER, 0);
            $id = $matches[0]['plot_id'];
        } else {
            $id = $rpcParams['plot_id'];
        }

        $combineOszInfo = false;
        if (array_key_exists('combineOszInfo', $rpcParams)) {
            $combineOszInfo = true;
        }

        if (!is_numeric($id)) {
            return $return;
        }

        $options = [
            'tablename' => $tablename,
        ];

        if (Config::LAYER_TYPE_ZP == $layer_type) {
            $options['where'] = ['id' => $id];
        } else {
            $options['where'] = ['gid' => $id];
        }

        if (Config::LAYER_TYPE_GPS == $layer_type) {
            $options['return'] = [
                'gid', 'ST_ASTEXT(geom)', 'ST_AsGeoJSON(ST_Transform(geom, 3857)) AS geojson', 'plot_info', 'plot_name', 'round((ST_Area(geom) / 1000)::numeric, 3) as area_kvs',
            ];
        }

        if (Config::LAYER_TYPE_KMS == $layer_type) {
            $options['return'] = [
                'gid', 'ST_ASTEXT(geom)', 'ST_AsGeoJSON(ST_Transform(geom, 3857)) AS geojson', 'round((ST_Area(geom) / 1000)::numeric, 3) as area_kms', 'ekatte', 'name', 'crop_code', 'virtual_crop_name', 'virtual_ekatte_name',
            ];
        }

        if (Config::LAYER_TYPE_KVS == $layer_type) {
            $options['return'] = [
                'gid', 'ST_ASTEXT(geom)', 'round((ST_Area(geom) / 1000)::numeric, 3) as area_kvs',
                'ST_AsGeoJSON(ST_Transform(geom, 3857)) AS geojson',
                'round((coalesce(document_area, (ST_Area(geom) / 1000)))::numeric, 3) as document_area',
                'kad_ident', 'ekate', 'masiv', 'number', 'virtual_category_title as category', 'virtual_ntp_title area_type', 'has_contracts', 'mestnost',
                'include', 'participate', 'white_spots', 'irrigated_area',
                "allowable_area || ' (' || round(((allowable_area / (st_area(geom)/1000))*100)::numeric,3) || '%)' as allowable_area",
                'allowable_type',
            ];
        }

        if (Config::LAYER_TYPE_WORK_LAYER == $layer_type) {
            $options['return'] = [
                '*', 'ST_AsGeoJSON(ST_Transform(geom, 3857)) AS geojson', 'round((ST_Area(geom) / 1000)::numeric, 3) as area',
            ];
        }

        // ISAK areas should be in hectares in order to be same as information from them
        if (Config::LAYER_TYPE_FOR_ISAK == $layer_type || Config::LAYER_TYPE_ISAK == $layer_type) {
            $options['return'] = [
                '*', 'ST_AsGeoJSON(ST_Transform(geom, 3857)) AS geojson', 'round((ST_Area(geom) / 10000)::numeric, 3) as area',
            ];
        }

        if (!$tablename || !$id) {
            return $return;
        }

        [$plotData] = $UserDbController->getPolygonDataById($options);

        if (Config::LAYER_TYPE_ZP == $layer_type) {
            $rows = [
                0 => [
                    'name' => 'Идентификатор',
                    'value' => $id,
                    'group' => 'Системна информация',
                    'tablename' => $tablename,
                    'gid' => $id,
                    'layer_type' => $layer_type,
                    'layer_id' => $layer->id,
                    'prop_name' => 'ident',
                ],
                1 => [
                    'name' => 'Име на парцела',
                    'value' => $plotData['area_name'],
                    'editor' => 'text',
                    'group' => 'Обща информация',
                    'layer_type' => $layer_type,
                    'prop_name' => 'plot_name',
                ],
                2 => [
                    'name' => 'EKATTE',
                    'value' => $plotData['virtual_ekatte_name'],
                    'editor' => [
                        'type' => 'combobox',
                        'options' => [
                            'url' => 'index.php?common-rpc=ekate-combobox',
                            'valueField' => 'ekate',
                            'textField' => 'text',
                        ],
                    ],
                    'group' => 'Обща информация',
                    'layer_type' => $layer_type,
                    'prop_name' => 'ekatte',
                ],
                3 => [
                    'name' => 'Култура',
                    'value' => $plotData['virtual_crop_name'],
                    'editor' => [
                        'type' => 'combobox',
                        'options' => [
                            'url' => 'index.php?common-rpc=culture-combobox',
                            'valueField' => 'id',
                            'textField' => 'name',
                        ],
                    ],
                    'group' => 'Обща информация',
                    'layer_type' => $layer_type,
                    'prop_name' => 'culture',
                ],
                4 => [
                    'name' => 'Обработки',
                    'value' => $plotData['obrabotki'],
                    'editor' => 'text',
                    'group' => 'Обща информация',
                    'layer_type' => $layer_type,
                    'prop_name' => 'tilth',
                ],
                5 => [
                    'name' => 'Добиви',
                    'value' => $plotData['dobivi'],
                    'editor' => 'text',
                    'group' => 'Обща информация',
                    'layer_type' => $layer_type,
                    'prop_name' => 'yield',
                ],
                6 => [
                    'name' => 'Напояване',
                    'value' => $plotData['napoqvane'],
                    'editor' => 'text',
                    'group' => 'Обща информация',
                    'layer_type' => $layer_type,
                    'prop_name' => 'irrigation',
                ],
                7 => [
                    'name' => 'Поливки',
                    'value' => $plotData['polivki'],
                    'editor' => 'text',
                    'group' => 'Обща информация',
                    'layer_type' => $layer_type,
                    'prop_name' => 'watering',
                ],
                8 => [
                    'name' => 'Ползвател',
                    'value' => $plotData['polzvatel'],
                    'editor' => 'text',
                    'group' => 'Обща информация',
                    'layer_type' => $layer_type,
                    'prop_name' => 'user',
                ],
                9 => [
                    'name' => 'ИСАК номер',
                    'value' => $plotData['isak_prc_uin'],
                    'editor' => 'text',
                    'group' => 'Обща информация',
                    'layer_type' => $layer_type,
                    'prop_name' => 'isak_number',
                ],
            ];

            $return['total'] = 10;
            $return['rows'] = $rows;
        }

        if (Config::LAYER_TYPE_ISAK == $layer_type) {
            $rows = [
                0 => [
                    'name' => 'Идентификатор',
                    'value' => $id,
                    'group' => 'Системна информация',
                    'tablename' => $tablename,
                    'layer_type' => $layer_type,
                    'layer_id' => $layer->id,
                    'gid' => $id,
                    'prop_name' => 'ident',
                ],
                1 => [
                    'name' => 'ПРК номер',
                    'value' => $plotData['prc_uin'],
                    'group' => 'Обща информация',
                    'layer_type' => $layer_type,
                    'prop_name' => 'prk_number',
                ],
                2 => [
                    'name' => 'БЗС номер',
                    'value' => (int) $plotData['bzs_id'],
                    'editor' => 'text',
                    'group' => 'Обща информация',
                    'layer_type' => $layer_type,
                    'prop_name' => 'bzs_number',
                ],
                3 => [
                    'name' => 'Площ (ха)',
                    'value' => $plotData['area'],
                    'editor' => 'text',
                    'group' => 'Обща информация',
                    'layer_type' => $layer_type,
                    'prop_name' => 'area_ha',
                ],
                4 => [
                    'name' => 'Напояване',
                    'value' => $plotData['watering'],
                    'editor' => 'boolean',
                    'group' => 'Обща информация',
                    'layer_type' => $layer_type,
                    'prop_name' => 'irrigation',
                ],
                5 => [
                    'name' => 'Култура',
                    'value' => $plotData['virtual_crop_name'],
                    'editor' => [
                        'type' => 'combobox',
                        'options' => [
                            'url' => 'index.php?common-rpc=culture-combobox',
                            'valueField' => 'id',
                            'textField' => 'name',
                        ],
                    ],
                    'group' => 'Обща информация',
                    'layer_type' => $layer_type,
                    'prop_name' => 'cropcode',
                ],
                6 => [
                    'name' => 'ЕКАТТЕ',
                    'value' => $plotData['virtual_ekatte_name'],
                    'editor' => [
                        'type' => 'combobox',
                        'options' => [
                            'url' => 'index.php?common-rpc=ekate-combobox',
                            'valueField' => 'ekate',
                            'textField' => 'text',
                        ],
                    ],
                    'group' => 'Обща информация',
                    'layer_type' => $layer_type,
                    'prop_name' => 'ekatte',
                ],
                7 => [
                    'name' => 'УРН',
                    'value' => (int) $plotData['urn'],
                    'editor' => 'text',
                    'group' => 'Обща информация',
                    'layer_type' => $layer_type,
                    'prop_name' => 'urn',
                ],
            ];

            $return['total'] = 8;
            $return['rows'] = $rows;
        }

        if (Config::LAYER_TYPE_GPS == $layer_type) {
            $rows = [
                0 => [
                    'name' => 'Идентификатор',
                    'value' => $id,
                    'group' => 'Системна информация',
                    'tablename' => $tablename,
                    'layer_type' => $layer_type,
                    'layer_id' => $layer->id,
                    'gid' => $id,
                    'prop_name' => 'ident',
                ],
                1 => [
                    'name' => 'Площ (дка)',
                    'value' => $plotData['area_kvs'],
                    'group' => 'Системна информация',
                    'tablename' => $tablename,
                    'layer_type' => $layer_type,
                    'gid' => $id,
                    'prop_name' => 'area_dka',
                ],
                2 => [
                    'name' => 'Име на парцела',
                    'value' => $plotData['plot_name'],
                    'editor' => 'text',
                    'group' => 'Обща информация',
                    'layer_type' => $layer_type,
                    'prop_name' => 'plot_name',
                ],
                3 => [
                    'name' => 'Коментар',
                    'value' => $plotData['plot_info'],
                    'editor' => 'text',
                    'group' => 'Обща информация',
                    'layer_type' => $layer_type,
                    'prop_name' => 'comment',
                ],
            ];

            $return['total'] = 8;
            $return['rows'] = $rows;
        }

        if (Config::LAYER_TYPE_FOR_ISAK == $layer_type) {
            $schema = $UserDbController->getForIsakSchemas($plotData);

            $rows = [
                0 => [
                    'name' => 'Идентификатор',
                    'value' => $id,
                    'group' => 'Системна информация',
                    'tablename' => $tablename,
                    'layer_type' => $layer_type,
                    'layer_id' => $layer->id,
                    'gid' => $id,
                    'prop_name' => 'ident',
                ],
                1 => [
                    'name' => 'Име на парцел',
                    'value' => $plotData['prc_name'],
                    'editor' => 'text',
                    'group' => 'Обща информация',
                    'layer_type' => $layer_type,
                    'prop_name' => 'plot_name',
                ],
                2 => [
                    'name' => 'ЕКАТТЕ',
                    'value' => $plotData['virtual_ekatte_name'],
                    'editor' => [
                        'type' => 'combobox',
                        'options' => [
                            'url' => 'index.php?common-rpc=ekate-combobox',
                            'valueField' => 'ekate',
                            'textField' => 'text',
                        ],
                    ],
                    'group' => 'Обща информация',
                    'layer_type' => $layer_type,
                    'prop_name' => 'ekatte',
                ],
                3 => [
                    'name' => 'Култура',
                    'value' => $plotData['virtual_crop_name'],
                    'cropcode' => $plotData['cropcode'],
                    'group' => 'Обща информация',
                    'layer_type' => $layer_type,
                    'editor' => [
                        'type' => 'combobox',
                        'options' => [
                            'url' => 'index.php?common-rpc=culture-combobox',
                            'valueField' => 'id',
                            'textField' => 'name',
                        ],
                    ],
                    'prop_name' => 'cropcode',
                ],
                4 => [
                    'name' => 'Схеми/Мерки',
                    'value' => $schema,
                    'group' => 'Обща информация',
                    'layer_type' => $layer_type,
                    'prop_name' => 'schemes_measures',
                ],
                5 => [
                    'name' => 'Площ (ха)',
                    'value' => $plotData['area'],
                    'group' => 'Обща информация',
                    'layer_type' => $layer_type,
                    'prop_name' => 'area_ha',
                ],
                6 => [
                    'name' => 'За редактиране',
                    'value' => $plotData['edited'],
                    'editor' => 'boolean',
                    'group' => 'Обща информация',
                    'layer_type' => $layer_type,
                    'prop_name' => 'to_edit',
                ],
                7 => [
                    'name' => 'Коментар',
                    'value' => $plotData['comment'],
                    'editor' => 'text',
                    'group' => 'Обща информация',
                    'layer_type' => $layer_type,
                    'prop_name' => 'comment',
                ],
            ];

            $return['total'] = 8;
            $return['rows'] = $rows;
        }

        if (Config::LAYER_TYPE_KVS == $layer_type) {
            if (true === $combineOszInfo) {
                $result = $this->getRowsCombinedWithOszInfo($plotData, $id, $layer_type, $tablename);

                $return['rows'] = $result['rows'];
                $return['total'] = $result['total'];
            } else {
                $oszTabRows = $this->getOSZPlotData($id);
                $owner = $this->getActiveContractsForPlot($id);
                $rows = [
                    0 => [
                        'name' => 'Идентификатор',
                        'value' => $id,
                        'group' => 'Системна информация',
                        'tablename' => $tablename,
                        'layer_type' => $layer_type,
                        'layer_id' => $layer->id,
                        'gid' => $id,
                        'attributes' => ['oszData' => $oszTabRows],
                        'prop_name' => 'ident',
                    ],
                    1 => [
                        'name' => 'Геогр. площ (дка)',
                        'value' => $plotData['area_kvs'],
                        'group' => 'Системна информация',
                        'tablename' => $tablename,
                        'layer_type' => $layer_type,
                        'prop_name' => 'area_kvs',
                    ],
                    2 => [
                        'name' => 'Идент. номер',
                        'value' => $plotData['kad_ident'],
                        'group' => 'Обща информация',
                        'layer_type' => $layer_type,
                        'prop_name' => 'kad_ident',
                    ],
                    3 => [
                        'name' => 'EKATTE',
                        'value' => $plotData['ekate'],
                        'group' => 'Обща информация',
                        'layer_type' => $layer_type,
                        'prop_name' => 'ekatte',
                    ],
                    4 => [
                        'name' => 'Масив',
                        'value' => $plotData['masiv'],
                        'editor' => 'text',
                        'group' => 'Обща информация',
                        'layer_type' => $layer_type,
                        'prop_name' => 'masiv',
                    ],
                    5 => [
                        'name' => 'Номер на имот',
                        'value' => $plotData['number'],
                        'editor' => 'text',
                        'group' => 'Обща информация',
                        'layer_type' => $layer_type,
                        'prop_name' => 'plot_number',
                    ],
                    6 => [
                        'name' => 'Категория',
                        'value' => $plotData['category'],
                        'editor' => [
                            'type' => 'combobox',
                            'options' => [
                                'url' => 'index.php?common-rpc=plot-category-combobox',
                                'valueField' => 'id',
                                'textField' => 'name',
                            ],
                        ],
                        'group' => 'Обща информация',
                        'layer_type' => $layer_type,
                        'prop_name' => 'category',
                    ],
                    7 => [
                        'name' => 'НТП',
                        'value' => $plotData['area_type'],
                        'editor' => [
                            'type' => 'combobox',
                            'options' => [
                                'url' => 'index.php?common-rpc=plot-ntp-combobox',
                                'valueField' => 'id',
                                'textField' => 'name',
                            ],
                        ],
                        'group' => 'Обща информация',
                        'layer_type' => $layer_type,
                        'prop_name' => 'ntp',
                    ],
                    8 => [
                        'name' => 'Поливна площ',
                        'value' => $plotData['irrigated_area'],
                        'editor' => 'boolean',
                        'group' => 'Обща информация',
                        'layer_type' => $layer_type,
                        'prop_name' => 'irrigation_area',
                    ],
                    9 => [
                        'name' => 'Местност',
                        'value' => $plotData['mestnost'],
                        'editor' => 'text',
                        'group' => 'Обща информация',
                        'layer_type' => $layer_type,
                        'prop_name' => 'mestnost',
                    ],
                    10 => [
                        'name' => 'Площ (дка) в "Допустим слой"',
                        'value' => (null !== $plotData['allowable_area']) ? $plotData['allowable_area'] : '0 (0%)',
                        'group' => 'Информация за "Допустим слой"',
                        'layer_type' => $layer_type,
                        'prop_name' => 'area_dka',
                    ],
                    11 => [
                        'name' => 'Площ по док. (дка)',
                        'value' => $plotData['document_area'],
                        'group' => 'Системна информация',
                        'tablename' => $tablename,
                        'layer_type' => $layer_type,
                        'prop_name' => 'document_area',
                    ],
                ];

                if ($owner) {
                    $rows[12] = [
                        'name' => $owner['c_num'],
                        'value' => $owner['owner'],
                        'group' => 'Собственик',
                        'layer_type' => $layer_type,
                        'prop_name' => 'c_num',
                    ];
                }
                $return['total'] = count($rows);
                $return['rows'] = $rows;
            }
        }

        if (Config::LAYER_TYPE_KMS == $layer_type) {
            $rows = [
                0 => [
                    'name' => 'Идентификатор',
                    'value' => $id,
                    'group' => 'Системна информация',
                    'tablename' => $tablename,
                    'layer_type' => $layer_type,
                    'layer_id' => $layer->id,
                    'gid' => $id,
                    'prop_name' => 'ident',
                ],
                1 => [
                    'name' => 'Име на парцел',
                    'value' => $plotData['name'],
                    'editor' => 'text',
                    'group' => 'Обща информация',
                    'layer_type' => $layer_type,
                    'prop_name' => 'plot_name',
                ],
                2 => [
                    'name' => 'ЕКАТТЕ',
                    'value' => $plotData['virtual_ekatte_name'],
                    'editor' => [
                        'type' => 'combobox',
                        'options' => [
                            'url' => 'index.php?common-rpc=ekate-combobox',
                            'valueField' => 'ekate',
                            'textField' => 'text',
                        ],
                    ],
                    'group' => 'Обща информация',
                    'layer_type' => $layer_type,
                    'prop_name' => 'ekatte',
                ],
                3 => [
                    'name' => 'Култура',
                    'value' => $plotData['virtual_crop_name'],
                    'editor' => [
                        'type' => 'combobox',
                        'options' => [
                            'url' => 'index.php?common-rpc=culture-combobox',
                            'valueField' => 'id',
                            'textField' => 'name',
                        ],
                    ],
                    'group' => 'Обща информация',
                    'layer_type' => $layer_type,
                    'prop_name' => 'crop_code',
                ],
                4 => [
                    'name' => 'Площ (дкa)',
                    'value' => $plotData['area_kms'],
                    'group' => 'Обща информация',
                    'layer_type' => $layer_type,
                    'prop_name' => 'area_dka',
                ],
            ];

            $return['total'] = 5;
            $return['rows'] = $rows;
        }

        if (Config::LAYER_TYPE_WORK_LAYER == $layer_type) {
            $definitions = UserLayers::filterDefinitions($layer->getDefinitions(), [
                ['col_category' => Config::LAYER_COLUMN_CATEGORY_GID],
                ['col_visible' => true, 'col_singleedit' => true],
                ['col_personalizable' => true, 'col_singleedit' => true],
                ['col_personalizable' => true, 'col_virtual' => true],
                ['col_category' => Config::LAYER_COLUMN_CATEGORY_CROP],
                ['col_category' => Config::LAYER_COLUMN_CATEGORY_EKATTE],
                ['col_category' => Config::LAYER_COLUMN_CATEGORY_NTP],
                ['col_category' => Config::LAYER_COLUMN_CATEGORY_CATEGORY],
            ]);

            $definitionsColumns = array_column($definitions, 'col_name');
            $definitionsAssocArray = array_combine($definitionsColumns, $definitions);
            $referenceColumns = array_column($definitions, 'col_reference');
            $referenceDefs = array_filter($layer->getDefinitions(), fn ($def) => in_array($def['col_name'], $referenceColumns));
            $referenceDefs = array_combine(array_column($referenceDefs, 'col_name'), $referenceDefs);

            foreach ($plotData as $key => $value) {
                if (!isset($definitionsAssocArray[$key])) {
                    continue;
                }

                $column = $definitionsAssocArray[$key];
                $columnValue = $value;

                if ($column['col_virtual'] && in_array($column['col_reference'], $referenceColumns)) {
                    /**
                     * If there is virtual column get its value and set it as value of the reference column.
                     * E.g. if we have column virtual_ekatte_name the column in the result will be 'ekate'
                     * and the value will be 'Бъргас (07979)' (the value of the virtual column).
                     *
                     * We use $columnName as key in the result to avoid duplicated values for virtual columns and their reference columns
                     */
                    $column = $referenceDefs[$column['col_reference']];
                }

                $columnName = $column['col_name'];
                $columnEditable = $definitionsAssocArray[$columnName]['col_singleedit'];

                if (Config::LAYER_COLUMN_CATEGORY_CROP == $definitionsAssocArray[$columnName]['col_category']) {
                    $rows[$columnName] = [
                        'name' => $definitionsAssocArray[$columnName]['col_title'],
                        'value' => $columnValue,
                        'cropcode' => $value,
                        'group' => 'Обща информация',
                        'layer_type' => $layer_type,
                        'tablename' => $tablename,
                        'editor' => [
                            'type' => 'combobox',
                            'options' => [
                                'url' => 'index.php?common-rpc=culture-combobox',
                                'valueField' => 'id',
                                'textField' => 'name',
                            ],
                        ],
                        'prop_name' => $columnName,
                    ];

                    continue;
                }

                if (Config::LAYER_COLUMN_CATEGORY_EKATTE == $definitionsAssocArray[$columnName]['col_category']) {
                    $rows[$columnName] = [
                        'name' => $definitionsAssocArray[$columnName]['col_title'],
                        'value' => $columnValue,
                        'editor' => [
                            'type' => 'combobox',
                            'options' => [
                                'url' => 'index.php?common-rpc=ekate-combobox',
                                'valueField' => 'ekate',
                                'textField' => 'text',
                            ],
                        ],
                        'group' => 'Обща информация',
                        'layer_type' => $layer_type,
                        'tablename' => $tablename,
                        'prop_name' => $columnName,
                    ];

                    continue;
                }

                if (Config::LAYER_COLUMN_CATEGORY_NTP == $definitionsAssocArray[$columnName]['col_category']) {
                    $rows[$columnName] = [
                        'name' => $definitionsAssocArray[$columnName]['col_title'],
                        'value' => $columnValue,
                        'editor' => [
                            'type' => 'combobox',
                            'options' => [
                                'url' => 'index.php?common-rpc=plot-ntp-combobox',
                                'valueField' => 'id',
                                'textField' => 'name',
                            ],
                        ],
                        'group' => 'Обща информация',
                        'layer_type' => $layer_type,
                        'tablename' => $tablename,
                        'prop_name' => $columnName,
                    ];

                    continue;
                }

                if (Config::LAYER_COLUMN_CATEGORY_CATEGORY == $definitionsAssocArray[$columnName]['col_category']) {
                    $rows[$columnName] = [
                        'name' => $definitionsAssocArray[$columnName]['col_title'],
                        'value' => $columnValue,
                        'cropcode' => $value,
                        'group' => 'Обща информация',
                        'layer_type' => $layer_type,
                        'tablename' => $tablename,
                        'editor' => [
                            'type' => 'combobox',
                            'options' => [
                                'url' => 'index.php?common-rpc=plot-category-combobox',
                                'valueField' => 'id',
                                'textField' => 'name',
                            ],
                        ],
                        'prop_name' => $columnName,
                    ];

                    continue;
                }

                if ('category' === $definitionsAssocArray[$columnName]['col_name'] && in_array($definitionsAssocArray[$columnName]['col_name'], $referenceColumns)) {
                    $rows[$columnName] = [
                        'name' => $definitionsAssocArray[$columnName]['col_title'],
                        'value' => $columnValue,
                        'editor' => [
                            'type' => 'combobox',
                            'options' => [
                                'url' => 'index.php?common-rpc=plot-category-combobox',
                                'valueField' => 'id',
                                'textField' => 'name',
                            ],
                        ],
                        'group' => 'Обща информация',
                        'layer_type' => $layer_type,
                        'tablename' => $tablename,
                        'prop_name' => $columnName,
                    ];

                    continue;
                }

                switch ($definitionsAssocArray[$columnName]['col_category']) {
                    case Config::LAYER_COLUMN_CATEGORY_BOOLEAN:
                        $editor = 'boolean';

                        break;
                    case Config::LAYER_COLUMN_CATEGORY_DATE:
                        $editor = 'date';

                        break;
                    default:
                        $editor = $columnEditable ? 'text' : null;

                        break;
                }

                $rows[$columnName] = [
                    'name' => $definitionsAssocArray[$columnName]['col_title'] ?? $columnName,
                    'value' => Config::LAYER_COLUMN_CATEGORY_GID === $definitionsAssocArray[$columnName]['col_category'] ? $id : $value,
                    'group' => $columnEditable ? 'Обща информация' : 'Системна информация',
                    'tablename' => $tablename,
                    'editor' => $editor, // This property is used in GS FE to show the columns without the ability to edit them
                    'layer_type' => $layer_type,
                    'layer_id' => $layer->id,
                    'gid' => $id,
                    'is_virtual' => !$columnEditable, // This property is used in TF FE to show the columns without the ability to edit them
                    'prop_name' => Config::LAYER_COLUMN_CATEGORY_GID === $definitionsAssocArray[$columnName]['col_category'] ? 'ident' : $columnName,
                ];
            }

            $return['rows'] = array_values($rows);
            $return['total'] = count($rows);
        }

        if (isset($plotData['geojson']) && strlen($plotData['geojson']) > 0) {
            $return['geojson'] = json_decode($plotData['geojson']);
        }

        return $return;
    }

    public function getRowsCombinedWithOszInfo($array, $id, $layer_type, $tablename)
    {
        $contractsData = $this->getOSZPlotData($id, true);
        $owner = $this->getActiveContractsForPlot($id);
        $rows = [
            0 => [
                'name' => 'Геогр. площ (дка)',
                'value' => $array['area_kvs'],
                'group' => 'Системна информация',
                'tablename' => $tablename,
                'layer_type' => $layer_type,
            ],
            1 => [
                'name' => 'Идент. номер',
                'value' => $array['kad_ident'],
                'group' => 'Обща информация',
                'layer_type' => $layer_type,
                'gid' => $id,
            ],
            2 => [
                'name' => 'EKATTE',
                'value' => $array['ekate'],
                'group' => 'Обща информация',
                'layer_type' => $layer_type,
            ],
            3 => [
                'name' => 'Масив',
                'value' => $array['masiv'],
                'editor' => 'text',
                'group' => 'Обща информация',
                'layer_type' => $layer_type,
            ],
            4 => [
                'name' => 'Номер на имот',
                'value' => $array['number'],
                'editor' => 'text',
                'group' => 'Обща информация',
                'layer_type' => $layer_type,
            ],
            5 => [
                'name' => 'Категория',
                'value' => $array['category'],
                'editor' => [
                    'type' => 'combobox',
                    'options' => [
                        'url' => 'index.php?common-rpc=plot-category-combobox',
                        'valueField' => 'id',
                        'textField' => 'name',
                    ],
                ],
                'group' => 'Обща информация',
                'layer_type' => $layer_type,
            ],
            6 => [
                'name' => 'НТП',
                'value' => $array['area_type'],
                'editor' => [
                    'type' => 'combobox',
                    'options' => [
                        'url' => 'index.php?common-rpc=plot-ntp-combobox',
                        'valueField' => 'id',
                        'textField' => 'name',
                    ],
                ],
                'group' => 'Обща информация',
                'layer_type' => $layer_type,
            ],
            7 => [
                'name' => 'Поливна площ',
                'value' => $array['irrigated_area'] ? 'Да' : 'Не',
                'editor' => [
                    'type' => 'combobox',
                    'options' => [
                        'url' => 'index.php?common-rpc=irrigated-area-combobox',
                        'valueField' => 'value',
                        'textField' => 'label',
                    ],
                ],
                'group' => 'Обща информация',
                'layer_type' => $layer_type,
            ],
            8 => [
                'name' => 'Местност',
                'value' => $array['mestnost'],
                'editor' => 'text',
                'group' => 'Обща информация',
                'layer_type' => $layer_type,
            ],
            9 => [
                'name' => 'Площ (дка) в "Допустим слой"',
                'value' => (null !== $array['allowable_area']) ? $array['allowable_area'] : '0 (0%)',
                'group' => 'Информация за "Допустим слой"',
                'layer_type' => $layer_type,
            ],
            10 => [
                'name' => 'Площ по док. (дка)',
                'value' => $array['document_area'],
                'group' => 'Системна информация',
                'tablename' => $tablename,
                'layer_type' => $layer_type,
            ],
        ];

        $this->setOszContractsInfo($rows, $contractsData['activeContracts'], 'Активни договори', $layer_type);
        $this->setOszContractsInfo($rows, $contractsData['contracts'], 'ОСЗ Данни', $layer_type);

        if ($owner) {
            $rows[] = [
                'name' => $owner['c_num'],
                'value' => $owner['owner'],
                'group' => 'Собственик',
                'layer_type' => $layer_type,
            ];
        }

        return ['rows' => $rows, 'total' => count($rows)];
    }

    public function getOSZPlotData($plot_id, $combineOszInfo = false)
    {
        $UserDbOSZController = new UserDbOSZController($this->User->Database);
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);

        $osz_results = $UserDbOSZController->getOszInfoForPlot($plot_id);
        $return = [];
        $oszCount = count($osz_results);
        for ($i = 0; $i < $oszCount; $i++) {
            $return[$i]['group'] = $osz_results[$i]['txt_pr_osn'];
            $return[$i]['name'] = wordwrap($osz_results[$i]['txt_pr_osn'], 20, '<br/>');
            $return[$i]['value'] = $osz_results[$i]['ime_subekt'];
            $return[$i]['value'] .= ' (' . $osz_results[$i]['egn_subekt'] . ')';
            $return[$i]['value'] = wordwrap($return[$i]['value'], 40, '<br/>');
        }

        $results = $UserDbPlotsController->getPlotsContractsRelations($plot_id);
        $resultsCount = count($results);
        for ($i = 0; $i < $resultsCount; $i++) {
            $id_array[] = $results[$i]['contract_id'];
        }

        if (0 == $resultsCount) {
            if (true === $combineOszInfo) {
                return ['activeContracts' => [], 'contracts' => $return];
            }

            return ['rows' => $return, 'total' => count($return)];
        }

        $options = [
            'return' => ['*'],
            'where' => [
                'contract_id' => ['column' => 'id', 'compare' => 'IN', 'value' => $id_array],
                'active' => ['column' => 'active', 'compare' => '=', 'value' => 'true'],
                'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'value' => 'FALSE'],
            ],
        ];

        $contract_results = $UserDbPlotsController->getContractsDataForPlots($options, false, false);
        $contractsCount = count($contract_results);
        $contracts = [];
        $activeContracts = [];
        for ($i = 0; $i < $contractsCount; $i++) {
            if (null === $contract_results[$i]['due_date'] || $contract_results[$i]['due_date'] >= date('Y-m-d')) {
                $contracts[$i]['group'] = 'Активни договори';
                $contracts[$i]['name'] = $GLOBALS['Contracts']['ContractTypes'][$contract_results[$i]['nm_usage_rights']]['name'];
                $contracts[$i]['value'] = $contract_results[$i]['c_num'];
                $contracts[$i]['id'] = $contract_results[$i]['id'];
                $contracts[$i]['is_annex'] = $contract_results[$i]['is_annex'];

                if (true === $combineOszInfo) {
                    array_push($activeContracts, $contracts[$i]);
                } else {
                    array_push($return, $contracts[$i]);
                }
            }
        }

        if (true === $combineOszInfo) {
            return ['activeContracts' => $activeContracts, 'contracts' => $return];
        }

        return ['rows' => $return, 'total' => count($return)];
    }

    /**
     * @api-method calculateAvgSlopeByLayer
     * Calculates average slope for each plot in the layer and updates the slope column in DB. (Only for layer_work_*)
     *
     * @param array $rpcParams
     *                         {
     *                         #item int layer_id
     *                         #item array plots_gids
     *                         }
     *
     * @return array
     */
    public function calculateAvgSlopeByLayer($rpcParams = [])
    {
        $layerId = $rpcParams['layer_id'];
        $plotsGids = $rpcParams['plots_gids'] ?? [];

        if (!$this->User->HasSlopeRights) {
            throw new MTRpcException('NO_RIGHTS', 499);
        }

        $userDbController = new UserDbController($this->User->database);
        $usersController = new UsersController('Users');
        $layerDbController = new LayersController('Layers');

        $layerData = $layerDbController->getLayerData($layerId);
        $isWorkLayer = Config::LAYER_TYPE_WORK_LAYER == $layerData['layer_type'];

        if (!$isWorkLayer) {
            throw new MTRpcException('AVG_SLOPE_ONLY_WORK_LAYER_ERROR', -33957);
        }

        $identifierColumn = 'gid';
        $slopeColumn = LayerWork::$slopeColumn;
        $tableName = $layerData['table_name'];

        $options = [
            'tablename' => $tableName,
            'return' => [$identifierColumn],
        ];

        if (count($plotsGids) > 0) {
            $options['where'] = [
                'id' => [
                    'column' => $identifierColumn,
                    'compare' => 'IN',
                    'value' => $plotsGids,
                ],
            ];
        }

        $result = $userDbController->getGeoJSON($options);

        if (empty($result['geoJSON'])) {
            throw new MTRpcException('AVG_SLOPE_CALCULATION_ERROR', -33957);
        }

        $plotsGeoJson = json_decode($result['geoJSON'], true);

        $commonServicesModule = Prado::getApplication()->getModule('commonServicesModule');
        $slopeFile = getenv('SLOPE_FILE');
        $plotsAvgSlopeGeoJSON = $commonServicesModule->calculateAvgSlope($plotsGeoJson, $slopeFile);

        /**
         * @var array $plotsAvgSlope
         *            {
         *            #item int   gid/id - $identifierColumn
         *            #item float slope
         *            }
         */
        $plotsAvgSlope = array_map(function ($feature) use ($identifierColumn, $slopeColumn) {
            $props = $feature['properties'];

            if (!isset($props[$identifierColumn]) || !isset($props[$slopeColumn])) {
                throw new MTRpcException('AVG_SLOPE_CALCULATION_ERROR', -33957);
            }

            return $feature['properties'] ?? null;
        }, $plotsAvgSlopeGeoJSON['features'] ?? []);

        $plotsAvgSlope = $userDbController->multiUpdate($tableName, $plotsAvgSlope, $identifierColumn, [$slopeColumn]);
        $usersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $rpcParams, $plotsAvgSlope, 'Calculating avg slope by layer');

        return $plotsAvgSlope;
    }

    private function setOszContractsInfo(&$rows, $contracts, $group, $layerType)
    {
        if (0 == count($contracts)) {
            $rows[] = [
                'name' => null,
                'value' => null,
                'group' => $group,
                'layer_type' => $layerType,
                'id' => null,
                'is_annex' => null,
            ];
        } else {
            foreach ($contracts as $data) {
                $rows[] = [
                    'name' => $data['name'],
                    'value' => $data['value'],
                    'group' => $group,
                    'layer_type' => $layerType,
                    'id' => $data['id'],
                    'is_annex' => $data['is_annex'],
                ];
            }
        }
    }

    private function getActiveContractsForPlot($plot_id)
    {
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);
        $FarmingController = new FarmingController('Farming');

        $options = [
            'return' => ['*'],
            'where' => [
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'value' => $this->User->GroupID],
            ],
            'sort' => 'id',
            'order' => 'ASC',
        ];

        $farming_results = $FarmingController->getFarmings($options, false, false);
        $farmingCount = count($farming_results);
        $finalFarmings = [];
        for ($i = 0; $i < $farmingCount; $i++) {
            $finalFarmings[$farming_results[$i]['id']]['name'] = $farming_results[$i]['name'];
            $finalFarmings[$farming_results[$i]['id']]['eik'] = $farming_results[$i]['bulstat'];
        }

        $results = $UserDbPlotsController->getPlotOwnerDataByPlotID($plot_id);

        $result = $results[0];

        $return = [];
        if (1 == $result['c_type']) {
            $return['c_num'] = $result['c_num'];
            $return['owner'] = $finalFarmings[$result['farming_id']]['name'] . '<br /> ЕИК: ' . $finalFarmings[$result['farming_id']]['eik'];

            return $return;
        }
        if (7 == $result['c_type']) {
            $return['c_num'] = '(Пр.) ' . $result['s_c_num'];
            $return['owner'] = $result['buyer'];

            return $return;
        }

        return false;
    }
}
