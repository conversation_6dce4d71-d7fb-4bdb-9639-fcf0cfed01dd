<?php

namespace TF\Engine\APIClasses\Map\MapIntegrations;

class KAISIdentifiers extends KAISIntegration
{
    public const SEARCH_BY_IDENTIFIER_URL = 'https://kais.cadastre.bg/bg/Map/SearchByIdentifier';

    public function getIdentifiers(string $ident): iterable
    {
        $content = $this->getWebContent();
        $tokens = $this->getTokens($content);

        $ch = curl_init();
        $url = $this->getRequestUrl() . '?' . http_build_query($this->getRequestBody(['ident' => $ident])) . '&LimitResultCount=false';
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $this->getRequestHeaders($tokens));
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_FAILONERROR, true);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $output = curl_exec($ch);

        if (curl_errno($ch)) {
            $errorMsg = curl_error($ch);
            curl_close($ch);
            $this->addIntegrationException($errorMsg);
        }
        curl_close($ch);

        $response = json_decode($output);

        return $this->handleResponse($response);
    }

    protected function getRequestUrl()
    {
        return self::SEARCH_BY_IDENTIFIER_URL;
    }

    protected function getRequestBody(array $params)
    {
        return [
            'provinceDropdownIdentSearch_input' => '',
            'provinceDropdownIdentSearch' => '',
            'CadastralIdentifier' => $params['ident'],
            'LimitSearchExtent' => 'false',
            'LimitResultCount' => 'true',
        ];
    }

    protected function buildResponse($response): iterable
    {
        return $this->getResponseIdentifiers($response);
    }

    private function getResponseIdentifiers($response): iterable
    {
        preg_match("/(?<=\"Data\":\[).*?(?=],\"Total)/", $response->content, $matches);

        $jsonObj = json_decode($matches[0]);

        return [
            'id' => $jsonObj->ID,
            'type' => $jsonObj->ServiceObjectType,
            'subType' => $jsonObj->SubType,
            'number' => $jsonObj->Number,
        ];
    }
}
