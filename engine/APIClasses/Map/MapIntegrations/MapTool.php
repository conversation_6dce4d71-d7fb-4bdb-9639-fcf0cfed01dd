<?php

namespace TF\Engine\APIClasses\Map\MapIntegrations;

use Prado\TModule;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\Users\UsersController;

abstract class MapTool extends TModule
{
    protected $geoms;
    protected $gid;
    private $module = 'MapTool';
    private $service_id = 'map-split-tool';

    public function __construct() {}

    public function setGeoms($geoms)
    {
        $this->geoms = $geoms;
    }

    public function setGid($gid)
    {
        $this->gid = $gid;
    }

    public function getWebContent()
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->getBaseUrl());
        curl_setopt($ch, CURLOPT_HEADER, 1);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($ch, CURLOPT_FAILONERROR, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
        $content = curl_exec($ch);

        if (curl_errno($ch)) {
            $errorMsg = curl_error($ch);
            curl_close($ch);
            $this->addIntegrationException($errorMsg);
        }

        curl_close($ch);

        return $content;
    }

    public function getGeometryInfo($geoms, $gids)
    {
        $content = $this->getWebContent();
        $tokens = $this->getTokens($content);

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->getGeomInfoUrl($tokens['token']));
        curl_setopt($ch, CURLOPT_POSTFIELDS, $this->getRequestBody(['geoms' => $geoms, 'gids' => $gids]));

        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $this->getRequestHeaders($tokens));
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_FAILONERROR, true);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $output = curl_exec($ch);

        if (curl_errno($ch)) {
            $errorMsg = curl_error($ch);
            curl_close($ch);
            $this->addIntegrationException($errorMsg);
        }
        curl_close($ch);

        $response = json_decode($output);

        return $this->handleResponse($response);
    }

    public function transformGeom($geom, $fromCrs, $toCrs)
    {
        $UserDbController = new UserDbController($this->User->Database);

        return $UserDbController->transformGeom($geom, $fromCrs, $toCrs);
    }

    /**
     * @param string $projection
     */
    public function filterGeom($projection = 'EPSG:32635')
    {
        $geom = reset(array_filter($this->geoms, function ($v, $k) use ($projection) {
            return ($v['projection'] == $projection);
        }, ARRAY_FILTER_USE_BOTH));

        return $geom['geom'];
    }

    public function addIntegrationException(string $errorMsg)
    {
        $UsersController = new UsersController('Users');
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, [], [], $errorMsg);

        throw new MTRpcException('SPLIT_TOOL_UNSUCCESSFUL_OPERATION_AUTOMATIC_SPLIT', -33064);
    }

    public function getBetween($content, $start, $end)
    {
        $r = explode($start, $content);
        if (isset($r[1])) {
            $r = explode($end, $r[1]);

            return trim($r[0]);
        }

        return '';
    }

    abstract protected function buildResponse($response);

    abstract protected function getTokens($content);

    abstract protected function getRequestBody(array $params);

    abstract protected function getRequestHeaders($tokens);

    abstract protected function getBaseUrl();

    abstract protected function getGeomInfoUrl($token = null);

    /**
     * @param [type] $response
     */
    protected function handleResponse($response)
    {
        switch (true) {
            case isset($response->errorCode):
                $errorMsg = 'ErrorCode: ' . $response->errorCode;
                $this->addIntegrationException($errorMsg);

                break;
            case (isset($response->status) && 'ERROR' == $response->status):
                // instead throw error we can make another request with dot polygon
                $errorMsg = 'Probably geometry must be simplified more than once. ';
                if (isset($response->value)) {
                    $errorMsg .= $response->value;
                }

                $this->addIntegrationException($errorMsg);

                break;
            default:
                $result = $this->buildResponse($response);

                if (empty($result)) {
                    throw new MTRpcException('SPLIT_TOOL_NO_SPLIT_PLOTS_FOUND', -33065);
                }

                return $result;
        }
    }

    protected function getKvsInfo()
    {
        $UserDbController = new UserDbController($this->User->Database);
        $options = [
            'tablename' => $UserDbController->DbHandler->tableKVS,
            'return' => ['kad_ident, ekate, masiv, number, ST_AsText(ST_Envelope(geom)) as extent'],
            'where' => [
                'gid' => ['column' => 'gid', 'compare' => '=', 'value' => $this->gid],
            ],
            'group' => 'kad_ident, ekate, masiv, number, geom',
        ];

        return $UserDbController->getItemsByParams($options, false, false);
    }
}
