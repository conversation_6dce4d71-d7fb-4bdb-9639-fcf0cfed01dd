<?php

namespace TF\Engine\APIClasses\Map\MapIntegrations;

class KAISGetGeometry extends KAISIntegration
{
    public const GET_OBJECT_GEOMETRY_BY_ID_AND_TYPE_URL = 'https://kais.cadastre.bg/bg/Map/GetObjectGeometryByIdAndType/';

    /**
     * Ident should be ekate or kadastral identifikator.
     */
    public function getObjectGeometryByIdAndType(string $ident)
    {
        $content = $this->getWebContent();
        $tokens = $this->getTokens($content);
        $identifiersIntegration = new KAISIdentifiers();

        [$id, $type, $subType] = array_values($identifiersIntegration->getIdentifiers($ident));

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->getRequestUrl() . '?' . http_build_query($this->getRequestBody(['id' => $id, 'type' => $type, 'subType' => $subType])));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $this->getRequestHeaders($tokens));
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_FAILONERROR, true);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $output = curl_exec($ch);

        if (curl_errno($ch)) {
            $errorMsg = curl_error($ch);
            curl_close($ch);
            $this->addIntegrationException($errorMsg);
        }
        curl_close($ch);

        $geoms = $this->transformGeom($output, '7801', 32635);

        return $this->handleResponse($geoms[0]['geom']);
    }

    protected function getRequestUrl(): string
    {
        return self::GET_OBJECT_GEOMETRY_BY_ID_AND_TYPE_URL;
    }

    protected function getRequestBody(array $params)
    {
        [$id, $type, $subType] = array_values($params);

        return [
            'id' => $id,
            'type' => $type,
            'subTypeId' => $subType,
        ];
    }

    /**
     * @param [string] $response Polygon
     */
    protected function buildResponse($response): string
    {
        return $response;
    }
}
