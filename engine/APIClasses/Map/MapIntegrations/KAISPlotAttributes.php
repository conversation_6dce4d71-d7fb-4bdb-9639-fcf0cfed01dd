<?php

namespace TF\Engine\APIClasses\Map\MapIntegrations;

use TF\Engine\Plugins\Core\UserDbPlotCategoriesType\UserDbPlotCategoriesTypeController;

class KAISPlotAttributes extends KAISIntegration
{
    public const GET_OBJECT_INFO_URL = 'https://kais.cadastre.bg/bg/Map/GetObjectInfo/';

    public function getObjectInfo(string $ident)
    {
        $content = $this->getWebContent();
        $tokens = $this->getTokens($content);
        $identifiersIntegration = new KAISIdentifiers();

        [$id, $type, $subType, $number] = array_values($identifiersIntegration->getIdentifiers($ident));

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->getRequestUrl() . '?' . http_build_query($this->getRequestBody(['id' => $id, 'type' => $type, 'subType' => $subType, 'number' => $number])));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $this->getRequestHeaders($tokens));
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_FAILONERROR, true);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $output = curl_exec($ch);

        if (curl_errno($ch)) {
            $errorMsg = curl_error($ch);
            curl_close($ch);
            $this->addIntegrationException($errorMsg);
        }
        curl_close($ch);

        return $this->handleResponse($output);
    }

    public function parseAttributeByLabel($label, $text, $regex = "([\w\s\d\.]+)")
    {
        $label = trim($label);
        $fullRegEx = "/(?>{$label} {$regex})/mu";
        $matches = [];
        preg_match($fullRegEx, $text, $matches);
        if (2 === count($matches)) {
            return $matches[1];
        }

        return;
    }

    protected function getRequestUrl()
    {
        return self::GET_OBJECT_INFO_URL;
    }

    protected function getRequestBody(array $params)
    {
        [$id, $type, $subType, $number] = array_values($params);
        $body = [
            'id' => $id,
            'type' => $type,
            'subTypeId' => $subType,
            'number' => ($pos = strpos($number, '.')) === false ? 'NaN' : $number,
        ];

        return $body;
    }

    protected function buildResponse($response): iterable
    {
        $UserDbPlotCategoriesTypeController = new UserDbPlotCategoriesTypeController($this->User->Database);

        $cad_ident = $this->parseAttributeByLabel('Поземлен имот', $response);
        $mestnost = $this->parseAttributeByLabel('м.', $response);
        $category = $this->parseAttributeByLabel('категория', $response);
        $ntp = $this->parseAttributeByLabel('НТП', $response);
        $area = $this->parseAttributeByLabel('площ', $response);
        $propertyType = $this->parseAttributeByLabel('вид собств.', $response);
        $purposeType = $this->parseAttributeByLabel('вид територия', $response);

        return [
            'cad_ident' => $cad_ident,
            'mestnost' => $mestnost,
            'category' => $UserDbPlotCategoriesTypeController->getPlotCategoryTitle($category),
            'ntp' => $ntp,
            'area' => $area / 1000,
            'propertytype' => $propertyType,
            'purposetype' => $purposeType,
        ];
    }
}
