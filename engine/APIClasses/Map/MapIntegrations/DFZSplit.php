<?php

namespace TF\Engine\APIClasses\Map\MapIntegrations;

use stdClass;

class DF<PERSON><PERSON><PERSON>lit extends MapTool
{
    public const CAMPAIGN_ID = 34;
    public const PREV_CAMPAIGN_ID = 301;
    public const LAYER_ID = 34;
    public const RESPONSE_SOURCE = 'DFZ';

    protected function getRequestBody(array $params)
    {
        [ $geoms, $gids ] = array_values($params);

        $this->setGeoms($geoms);
        $this->setGid($gids);

        $body = [
            'campaignId' => self::CAMPAIGN_ID,
            'previousCampaignId' => self::PREV_CAMPAIGN_ID,
            'farmerId' => null,
            'wkt' => $this->filterGeom('EPSG:32635'),
            'layerIds' => [self::LAYER_ID],
        ];

        return json_encode($body);
    }

    protected function getRequestHeaders($tokens)
    {
        return ['Content-Type:application/json'];
    }

    protected function getBaseUrl(): string
    {
        return DFZ_REQUEST_URL;
    }

    protected function getGeomInfoUrl($token = null)
    {
        return sprintf('https://seu.dfz.bg/dfz-gpwa/gsrv/%s/prx/rest/tlWktGeoms.json', $token);
    }

    protected function getTokens($content)
    {
        $cookies = $tokens = [];
        preg_match_all('/Set-Cookie:(?<cookie>\s{0,}.*)$/im', $content, $cookies);
        preg_match('/<input type="hidden" id="P8500_TOKEN" name="P8500_TOKEN" value=\"([^\">]+?)\"/', $content, $tokens);

        return [
            'cookies' => $cookies,
            'token' => $tokens[1],
        ];
    }

    protected function buildResponse($response)
    {
        $layerKvs = $this->getKvsInfo();
        $features = $response->all->{'iacsseu:V_GEO_KADASTRE_INFO'}->content->features;

        array_reduce($features, function ($carry, $o) {
            return max($carry->properties->KAD_YEAR, $o->properties->KAD_YEAR);
        });

        $result = [];
        foreach ($features as $key => $feature) {
            // filter by biggest cadastral year(removed) and same plot number
            if ($feature->properties->IMOT_UID == str_pad($layerKvs[0]['number'], 3, '0', STR_PAD_LEFT)
                || $feature->properties->IMOT_UID < $layerKvs[0]['number']
                || $feature->properties->SHORT_DESCR == $layerKvs[0]['kad_ident']
            ) {
                continue;
            }

            $tmpObject = new stdClass();
            $properties = new stdClass();

            $properties->document_area = $feature->properties->KAD_AREA;
            $properties->kad_year = $feature->properties->KAD_YEAR;
            $properties->ekate = $feature->properties->EKATTE_CODE;
            $properties->masiv = $feature->properties->MASIV_UID;
            $properties->number = $feature->properties->IMOT_UID;
            $properties->kad_ident = $feature->properties->SHORT_DESCR;
            $properties->mestnost = $feature->properties->EKATTE_NAME;

            $tmpObject->type = 'Feature';
            $tmpObject->responseSource = self::RESPONSE_SOURCE;
            $tmpObject->properties = $properties;
            $tmpObject->geometry = $feature->geometry;

            array_push($result, $tmpObject);
        }

        return $result;
    }
}
