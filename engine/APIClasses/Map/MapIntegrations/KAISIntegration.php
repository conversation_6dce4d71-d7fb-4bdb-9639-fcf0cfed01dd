<?php

namespace TF\Engine\APIClasses\Map\MapIntegrations;

abstract class KAISIntegration extends MapTool
{
    public const GEOM_INFO_URL = 'https://kais.cadastre.bg/bg/Map/SelectFeaturesWithPolygon/';
    public const RESPONSE_SOURCE = 'KAIS';

    abstract protected function getRequestBody(array $params);

    abstract protected function buildResponse($response);

    abstract protected function getRequestUrl();

    protected function getRequestHeaders($tokens)
    {
        return [
            'Content-Type:application/json',
            'accept: */*',
            'referer: https://kais.cadastre.bg/bg/Map',
            'x-requested-with: XMLHttpRequest',
            '__requestverificationtoken: ' . $tokens['token'] . '',
            $tokens['cookie'],
        ];
    }

    protected function getTokens($content)
    {
        $tokens = [];

        preg_match('/<form id="__AjaxAntiForgeryForm" action="#" method="post"><input name="__RequestVerificationToken" type="hidden" value=\"([^"]+)\"/', $content, $tokens);
        preg_match('/Set-Cookie: ASP.NET_SessionId=(?<cookie>\s{0,}.*)$/im', $content, $setSessions);
        preg_match('/Set-Cookie: __RequestVerificationToken=(?<cookie>\s{0,}.*)$/im', $content, $setTokens);

        $cookiesArr = explode(';', $setSessions['cookie']);
        $setCookiesArr = explode(';', $setTokens['cookie']);

        $sessionId = $cookiesArr[0];
        $token = $setCookiesArr[0];

        $cookie = 'cookie: ASP.NET_SessionId=' . KAIS_SESSION_ID . ';__RequestVerificationToken=' . $token . '; mycookies=s2;ca_token=' . KAIS_CA_TOKEN . ';cookies=true';

        return [
            'cookie' => $cookie,
            'token' => $tokens[1],
        ];
    }

    protected function getBaseUrl()
    {
        return KAIS_REQUEST_URL;
    }

    protected function getGeomInfoUrl($token = null)
    {
        return self::GEOM_INFO_URL;
    }
}
