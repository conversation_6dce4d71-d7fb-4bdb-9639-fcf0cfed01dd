<?php

namespace TF\Engine\APIClasses\Map\MapIntegrations;

class KAISGetGeometries extends KAISIntegration
{
    public function getObjectGeometryByIdentifier(string $ident)
    {
        $geomIntegration = new KAISGetGeometry();

        return $geomIntegration->getObjectGeometryByIdAndType($ident);
    }

    public function getEkatePlotsGeoms(string $ekatte)
    {
        $geomIntegration = new KAISGetGeometry();

        $wktGeometries = $geomIntegration->getObjectGeometryByIdAndType($ekatte);

        $content = $this->getWebContent();
        $tokens = $this->getTokens($content);

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->getRequestUrl());
        curl_setopt($ch, CURLOPT_POSTFIELDS, $this->getRequestBody(['wktGeometries' => $wktGeometries]));

        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $this->getRequestHeaders($tokens));
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_FAILONERROR, true);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $output = curl_exec($ch);

        if (curl_errno($ch)) {
            $errorMsg = curl_error($ch);
            curl_close($ch);
            $this->addIntegrationException($errorMsg);
        }
        curl_close($ch);

        $response = json_decode($output);

        return $this->handleResponse($response);
    }

    protected function getRequestUrl()
    {
        return $this->getGeomInfoUrl();
    }

    protected function getRequestBody(array $params)
    {
        [ $wktGeometries ] = array_values($params);
        $wktGeometriesTransformed = $this->transformGeom($wktGeometries, '7801', '32635');

        $body = [
            'wktGeometries' => [$wktGeometries],
            'wktGeometriesTransformed' => [$wktGeometriesTransformed[0]['geom']],
            'layerGroups' => [[
                'DbName' => 'iiscpr',
                'LayerNames' => ['CAD_IMMOVABLE_PGIS'],
            ]],
            'cqlFilter' => '1=0',
        ];

        return json_encode($body);
    }

    protected function buildResponse($response)
    {
        return $response;
    }
}
