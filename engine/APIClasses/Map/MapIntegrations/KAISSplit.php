<?php

namespace TF\Engine\APIClasses\Map\MapIntegrations;

use stdClass;

class KAISSplit extends KAISIntegration
{
    protected function getRequestUrl()
    {
        return $this->getGeomInfoUrl();
    }

    protected function getRequestBody(array $params)
    {
        [ $geoms, $gids ] = array_values($params);

        $this->setGeoms($geoms);
        $this->setGid($gids);

        $wktGeometries = $this->filterGeom('EPSG:8122');
        $wktGeometriesTransformed = $this->filterGeom('EPSG:32635');

        $body = [
            'wktGeometries' => [$wktGeometries],
            'wktGeometriesTransformed' => [$wktGeometriesTransformed],
            'layerGroups' => [[
                'DbName' => 'iiscpr',
                'LayerNames' => ['CAD_IMMOVABLE_PGIS'],
            ]],
            'cqlFilter' => '1=0',
        ];

        return json_encode($body);
    }

    protected function buildResponse($response)
    {
        $layerKvs = $this->getKvsInfo();
        $features = $response->data;

        $result = [];

        foreach ($features as $key => $feature) {
            // filter by biggest cadastral year(removed) and same plot number
            if ($feature->CadIdentifier->Immovable == $layerKvs[0]['number']
                || $feature->CadIdentifier->Immovable < $layerKvs[0]['number']
                || $feature->Number == $layerKvs[0]['kad_ident']
            ) {
                continue;
            }

            $tmpObject = new stdClass();
            $properties = new stdClass();

            $properties->document_area = '';
            $properties->kad_year = '';
            $properties->ekate = $feature->CadIdentifier->Ekatte;
            $properties->masiv = $feature->CadIdentifier->Region;
            $properties->number = $feature->CadIdentifier->Immovable;
            $properties->kad_ident = $feature->Number;
            $properties->mestnost = $feature->Description;

            $tmpObject->type = 'Feature';
            $tmpObject->responseSource = self::RESPONSE_SOURCE;
            $tmpObject->properties = $properties;
            $tmpObject->geometry = $feature->WktGeometry;

            array_push($result, $tmpObject);
        }

        return $result;
    }
}
