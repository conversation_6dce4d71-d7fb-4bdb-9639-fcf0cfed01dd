<?php

namespace TF\Engine\APIClasses\Map\MapIntegrations;

use GuzzleHttp\Cookie\CookieJar;
use Psr\Http\Message\ResponseInterface;
use Symfony\Component\DomCrawler\Crawler;
use TF\Engine\Kernel\MTRpcException;

class DFZCadastralPlots
{
    private const DFZ_MAIN_URL = 'https://seu.dfz.bg/seu';
    private const DFZ_PLOT_GRID_URL = 'https://seu.dfz.bg/seu/f?p=727:8145:::NO:::';
    private const DFZ_PLOT_ACCEPT_URL = 'https://seu.dfz.bg/seu/wwv_flow.accept';
    private const DFZ_COOKIE_DOMAIN = 'seu.dfz.bg';
    private $client;
    private $cookies;
    private $kadIdent;

    public function __construct(string $kadIdent)
    {
        $this->client = new \GuzzleHttp\Client();
        $this->kadIdent = $this->formatCadastralNumber($kadIdent);
    }

    public function getPlotData()
    {
        return $this->request('GET', self::DFZ_PLOT_GRID_URL, [], [$this, 'parse']);
    }

    private function parse(ResponseInterface $response)
    {
        $this->setCookies($response);

        $formParams = $this->createFormParams(
            new Crawler(
                $response->getBody()->getContents()
            )
        );

        $options = [
            'form_params' => $formParams,
            'cookies' => $this->cookies,
        ];

        return $this->request('POST', self::DFZ_PLOT_ACCEPT_URL, $options, [$this, 'handleRedirect']);
    }

    private function createFormParams(Crawler $response): array
    {
        $pFlowId = $response->filter('#pFlowId')->attr('value');
        $pFlowStepId = $response->filter('#pFlowStepId')->attr('value');
        $pInstance = $response->filter('#pInstance')->attr('value');
        $protected = $response->filter('#pPageItemsProtected')->attr('value');
        $pPageSubmissionId = $response->filter('#pPageSubmissionId')->attr('value');
        $salt = $response->filter('#pSalt')->attr('value');
        $ck = $response->filter('input[data-for="P8145_KAD_ID"]')->attr('value');

        return [
            'p_flow_id' => $pFlowId,
            'p_flow_step_id' => $pFlowStepId,
            'p_instance' => $pInstance,
            'p_debug' => '',
            'p_request' => 'GO',
            'p_reload_on_submit' => 'S',
            'p_page_submission_id' => $pPageSubmissionId,
            'p_json' => json_encode([
                'pageItems' => [
                    'itemsToSubmit' => [
                        ['n' => 'P8145_KAD_IDENT', 'v' => $this->kadIdent],
                        ['n' => 'P8145_KAD_ID', 'v' => '', 'ck' => $ck],
                    ],
                    'protected' => $protected,
                    'rowVersion' => '',
                ],
                'salt' => $salt,
            ]),
        ];
    }

    private function handleRedirect(ResponseInterface $response)
    {
        $redirDict = json_decode($response->getBody()->getContents(), true);

        if (!$redirDict) {
            throw new MTRpcException("No kad ident ({$this->kadIdent}) found!", 400, 'kad_ident');
        }

        $redirectUrl = $redirDict['redirectURL'];

        return $this->request('GET', self::DFZ_MAIN_URL . "/{$redirectUrl}", ['cookies' => $this->cookies], [$this, 'parsePlotData']);
    }

    private function parsePlotData(ResponseInterface $response)
    {
        $crawler = new Crawler($response->getBody()->getContents());

        $rows = $crawler->filter('table#84501753222788488 tr');

        $rowsArr = [];
        foreach ($rows as $row) {
            $rowCrawler = new \Symfony\Component\DomCrawler\Crawler($row);
            $rowData = $rowCrawler->filter('td')->each(function ($node) {
                return $node->text();
            });

            if (0 == count($rowData)) {
                continue;
            }

            $rowsArr[] = [
                'kad_imot' => $rowData[0],
                'area_intersection' => floatval($rowData[1]),
                'decl_plot' => $rowData[2],
                'decl_plot_area' => number_format(floatval($rowData[3]), 3, '.', ''),
                'campaign' => $rowData[4],
                'beneficient' => $rowData[5],
            ];
        }

        return $rowsArr;
    }

    private function request($method, $url, $options, callable $callback)
    {
        $response = $this->client->request($method, $url, $options);

        return call_user_func($callback, $response);
    }

    private function setCookies(ResponseInterface $response): void
    {
        $cookies = $response->getHeader('Set-Cookie');

        $cookieArray = [];
        foreach ($cookies as $cookie) {
            list($cookieName, $cookieValue) = explode('=', explode(';', $cookie)[0]);
            $cookieArray[$cookieName] = $cookieValue;
        }

        $this->cookies = CookieJar::fromArray($cookieArray, self::DFZ_COOKIE_DOMAIN);
    }

    private function formatCadastralNumber(string $cadastreNumber): string
    {
        $parts = explode('.', $cadastreNumber);

        return sprintf('%05d-%03d-%03d', $parts[0], $parts[1], $parts[2]);
    }
}
