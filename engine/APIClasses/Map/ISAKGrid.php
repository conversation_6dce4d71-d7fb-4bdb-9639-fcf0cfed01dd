<?php

namespace TF\Engine\APIClasses\Map;

use TF\Engine\Kernel\ExportToExcelClass;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

/**
 * Атрибутна информация 'ИСАК'.
 *
 * @rpc-module Map
 *
 * @rpc-service-id isak-datagrid
 */
class ISAKGrid extends BaseGrid
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getISAKGrid'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'exportToExcel' => ['method' => [$this, 'exportToExcel'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer layer_id
     *                         #item string  table_name
     *                         #item boolean clear_filter
     *                         #item string  action
     *                         #item string  ekatte
     *                         #item integer cropcode
     *                         #item string  watering
     *                         #item string  prc_uin
     *                         #item string  ek_name
     *                         #item string  schemata
     *                         #item string  campaign
     *                         #item string  urn
     *                         }
     * @param int $page pagination parameter
     * @param int $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array
     */
    public function getISAKGrid(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $UserDbController = new UserDbController($this->User->Database);
        $LayersController = new LayersController('Layers');

        $return = [];

        // creating a default empty return
        $empty_return = [
            'rows' => [],
            'total' => 0,
        ];

        if (isset($rpcParams['layer_id']) && (int) $rpcParams['layer_id']) {
            $layer_result = $LayersController->getLayerData($rpcParams['layer_id']);
        } else {
            return $empty_return;
        }

        $tableExists = $UserDbController->getTableNameExist($layer_result['table_name']);
        if (!$tableExists) {
            return $empty_return;
        }

        $geomTransformationCoef = (true == $rpcParams['with_nuz']) ? 10 : 1;

        $tableName = $layer_result['table_name'];
        $options = [
            'return' => [
                '*', "ST_ASTEXT({$tableName}.geom)",
                "round(({$tableName}.area * {$geomTransformationCoef})::numeric, 3) AS geom_area",
                'ek_name as ekatte_name',
                "ST_Centroid({$tableName}.geom) as centroid",
            ],
            'custom_counter' => "COUNT(DISTINCT({$tableName}.gid)), SUM(round(({$tableName}.area * {$geomTransformationCoef})::numeric, 3)) as total_area",
            'tablename' => $layer_result['table_name'],
            'full_text_search_columns' => ['prc_uin', 'cropname', 'ekatte', 'ek_name', 'urn'],
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
            'layer_id' => $rpcParams['layer_id'],
            'joins' => [],
        ];

        $this->withNuz($options, ($ekatteColumn = 'ekatte'), $rpcParams);
        $this->withPhysicalBlock($options, ($ekatteColumn), $rpcParams);
        $this->buildWhere($options, $rpcParams);

        $results = $UserDbController->getLayersByParams($options, false);
        $counter = $UserDbController->getLayersByParams($options, true);
        $resultsCount = count($results);

        for ($i = 0; $i < $resultsCount; $i++) {
            if (6 == $layer_result['layer_type']) {
                if (true == $results[$i]['watering']) {
                    $results[$i]['watering'] = 'Да';
                } else {
                    $results[$i]['watering'] = 'Не';
                }
                if ('' == $results[$i]['ekatte'] || null == $results[$i]['ekatte']) {
                    $results[$i]['ekatte'] = '-';
                }
                $culture = (int) $results[$i]['cropcode'];
                $results[$i]['culture'] = $GLOBALS['Farming']['crops'][$culture]['crop_name'];
                $results[$i]['area'] = $results[$i]['geom_area'];
            }
            $filteredGidsFromResults[] = $results[$i]['gid'];
        }

        $return['rows'] = $results;
        $return['rows'] = $results;
        $return['total'] = $counter[0]['count'];

        $return['filtered_gids'] = $filteredGidsFromResults ? $filteredGidsFromResults : [];

        $total_area = $counter[0]['total_area'];

        $return['footer'] = [
            [
                'prc_uin' => '<b>ОБЩО</b>',
                'area' => $total_area,
            ],
        ];

        return $return;
    }

    /**
     * @api-method exportToExcel
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer   layer_type
     *                         #item integer   layer_id
     *                         #item array   ids
     *                         }
     * @param ?string $sort
     * @param ?string $order
     *
     * @return string
     */
    public function exportToExcel(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $time = strtotime(date('Y-m-d H:i:s'));
        $file = '/isak_table_' . $time . '.xlsx';
        $filename = PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID . $file;

        if (!file_exists(PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID)) {
            mkdir(PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID, 0777);
        }

        $headers = [
            'prc_uin' => 'Идентификатор',
            'area' => 'Площ(ха)',
            'watering' => 'Напояване',
            'virtual_crop_name' => 'Култура',
            'virtual_ekatte_name' => 'ЕКАТТЕ',
        ];

        $data = $this->getISAKGrid($rpcParams, $page, $rows, $sort, $order);

        $totalArea = $data['footer'][0]['area'];
        $footerArray = [
            'prc_uin' => 'ОБЩО',
            'area' => $totalArea,
            'watering' => '',
            'virtual_crop_name' => '',
            'virtual_ekatte_name' => '',
            'dobivi' => '',
        ];
        $data['rows'][] = $footerArray;

        $exportExcelDoc = new ExportToExcelClass();
        $exportExcelDoc->export($data['rows'], $headers, []);
        $exportExcelDoc->saveFile($filename);

        return 'files/uploads/export/' . $this->User->UserID . $file;
    }
}
