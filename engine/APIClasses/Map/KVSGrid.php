<?php

namespace TF\Engine\APIClasses\Map;

use Exception;
use Prado\Exceptions\TException;
use Prado\Prado;
use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\APIClasses\Plots\PlotsTree;
use TF\Engine\Kernel\ExportToExcelClass;
use TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController;

/**
 * Атрибутна информация "КВС Имоти".
 *
 * @rpc-module Map
 *
 * @rpc-service-id kvs-datagrid
 */
class KVSGrid extends TRpcApiProvider
{
    private $filteredGidsFromResults = [];
    private $field;
    private $value;

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getKVSGrid'], 'validators' => [
                'rpcParams' => 'validateArray',
                'page' => 'validateInteger',
                'rows' => 'validateInteger',
                'sort' => 'validateSort',
                'order' => 'validateOrder',
            ],
            ],
            'exportToExcel' => ['method' => [$this, 'exportToExcel'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'exportToExcelCadastralMap' => ['method' => [$this, 'exportToExcelCadastralMap'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'getConsolidationSourceData' => ['method' => [$this, 'getConsolidationSourceData']],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item array area_type
     *                         {
     *                         #items string
     *                         }
     *                         #item string mestnost
     *                         #item array category {
     *                         #items string
     *                         }
     *                         #item string cnum
     *                         #item string company_eik
     *                         #item string company_name
     *                         #item string contract_status
     *                         #item array contract_type {
     *                         #items string
     *                         }
     *                         #item string date_from
     *                         #item string date_to
     *                         #item string due_date_from
     *                         #item string due_date_to
     *                         #item array ekate {
     *                         #items string
     *                         }
     *                         #item array farming {
     *                         #items string
     *                         }
     *                         #item bool is_edited
     *                         #item string kad_ident
     *                         #item string masiv
     *                         #item string number
     *                         #item string owner_egn
     *                         #item string owner_name
     *                         #item string rep_egn
     *                         #item string rep_name
     *                         }
     * @param int $page pagination parameter
     * @param int $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @throws TException
     *
     * @return array
     */
    public function getKVSGrid(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $plotsTree = new PlotsTree($this->rpcServer);

        $rpcParams['forKvsGrid'] = true;

        if (isset($rpcParams['plot_id']) && is_array($rpcParams['plot_id'])) {
            $rpcParams['gids'] = $rpcParams['plot_id'];
        }

        $plots = $plotsTree->getPlots($rpcParams, $page, $rows, $sort, $order, true, (bool)$rpcParams['filter_params']);

        $results = $plots['rows'];
        $counter = $plots['total'];
        $filteredGids = $plots['filteredGids'];
        $filteredGidsCount = count($filteredGids);

        for ($i = 0; $i < $filteredGidsCount; $i++) {
            $this->filteredGidsFromResults['filtered_plots']['layer_kvs'][] = $filteredGids[$i];
        }

        return $this->renderResults($results, $counter, $plots);
    }

    /**
     * @api-method exportToExcel
     *
     * @param array $rpcParams
     *                         {
     *                         #item boolean   has_contracts
     *                         #item boolean   clear_filter
     *                         #item integer   layer_id
     *                         #item string    action
     *                         #item string    ekate
     *                         #item string    masiv
     *                         #item string    number
     *                         #item integer   category
     *                         #item integer   area_type
     *                         #item timestamp contract_farming
     *                         #item timestamp contract_date
     *                         #item timestamp contract_date
     *                         }
     * @param ?string $sort
     * @param ?string $order
     *
     * @throws TException
     *
     * @return string
     */
    public function exportToExcel(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $time = strtotime(date('Y-m-d H:i:s'));
        $filename = PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID . '/kvs_table_' . $time . '.xlsx';

        if (!file_exists(PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID)) {
            mkdir(PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID, 0777);
        }
        $headers = [
            'ekate' => 'ЕКАТТЕ',
            'masiv' => 'Масив',
            'number' => 'Имот',
            'kad_ident' => 'Идентификатор',
            'kad_ident_kvs' => 'КВС номер',
            'mestnost' => 'Местност',
            'virtual_ntp_title' => 'НТП',
            'owners_osz' => 'Собственици от ОСЗ ',
            'category' => 'Категория',
            'document_area' => 'Площ по документ (дка)',
            'allowable_area' => 'Площ по сечение (дка)',
            'allow_prec' => 'Процент по сечение',
        ];

        $data = $this->getKVSGrid($rpcParams, $page, $rows, $sort, $order);

        $data['rows'] = array_map(function ($row) {
            $row['kad_ident_kvs'] = str_pad($row['ekate'], 5, '0', STR_PAD_LEFT)
                . '.' . str_pad($row['masiv'], 3, '0', STR_PAD_LEFT)
                . '.' . str_pad($row['number'], 3, '0', STR_PAD_LEFT);

            return $row;
        }, $data['rows']);

        $exportExcelDoc = Prado::getApplication()->getModule('ExportToExcel');
        $exportExcelDoc->export($data['rows'], $headers, []);
        $exportExcelDoc->saveFile($filename);

        return 'files/uploads/export/' . $this->User->UserID . '/kvs_table_' . $time . '.xlsx';
    }

    /**
     * @api-method exportToExcelCadastralMap
     *
     * @param array $rpcParams
     *                         {
     *                         #item boolean   has_contracts
     *                         #item boolean   clear_filter
     *                         #item integer   layer_id
     *                         #item string    action
     *                         #item string    ekate
     *                         #item string    masiv
     *                         #item string    number
     *                         #item integer   category
     *                         #item integer   area_type
     *                         #item timestamp contract_farming
     *                         #item timestamp contract_date
     *                         #item timestamp contract_date
     *                         }
     * @param ?string $sort
     * @param ?string $order
     *
     * @throws TException
     *
     * @return string
     */
    public function exportToExcelCadastralMap(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $time = strtotime(date('Y-m-d H:i:s'));
        $filename = PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID . '/kvs_table_cadastral_map_' . $time . '.xlsx';

        if (!file_exists(PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID)) {
            mkdir(PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID, 0777);
        }
        $headers = [
            'ekate' => 'ЕКАТТЕ',
            'masiv' => 'Масив',
            'number' => 'Имот',
            'kad_ident' => 'Идентификатор',
            'kad_ident_cadastral' => 'Кадастрален номер',
            'mestnost' => 'Местност',
            'area_type' => 'НТП',
            'category' => 'Категория',
            'used_area' => 'Използвана площ (дка)',
            'area_kvs' => 'Обща площ (дка)',
            'document_area' => 'Площ по документ (дка)',
            'irrigated_area' => 'Поливна площ (дка)',
            'allowable_area' => 'Площ по сечение (дка)',
            'allow_prec' => 'Процент по сечение',
        ];

        $data = $this->getKVSGrid($rpcParams, $page, $rows, $sort, $order);

        $data['rows'] = array_map(function ($row) {
            $row['kad_ident_cadastral'] = str_pad($row['ekate'], 5, '0', STR_PAD_LEFT)
                . '.' . str_pad($row['masiv'], 4, '0', STR_PAD_LEFT)
                . '.' . str_pad($row['number'], 4, '0', STR_PAD_LEFT);

            return $row;
        }, $data['rows']);

        $exportExcelDoc = new ExportToExcelClass();
        $exportExcelDoc->export($data['rows'], $headers, []);
        $exportExcelDoc->saveFile($filename);

        return 'files/uploads/export/' . $this->User->UserID . '/kvs_table_cadastral_map_' . $time . '.xlsx';
    }

    public function getConsolidationSourceData(array $rpcParams)
    {
        if (!isset($rpcParams['kad_ident'])) {
            throw new Exception('Parameter kad_ident is required)', 400);
        }

        $UserDbPlotController = new UserDbPlotsController($this->User->Database);

        return $UserDbPlotController->getConsolidationSourceData($rpcParams['kad_ident']);
    }

    private function renderResults(&$results, $counter, $plots)
    {
        $total_used_area = $plots['total_used_area'];
        $total_area_kvs = $plots['total_area_kvs'];
        $total_document_area = $plots['total_document_area'];
        $total_allowable_area = $plots['total_allowable_area'];
        $resultsCount = count($results);
        // converting data to user friendly format
        if (0 != $resultsCount) {
            for ($i = 0; $i < $resultsCount; $i++) {
                if (false == $results[$i]['irrigated_area']) {
                    $results[$i]['watering'] = 'Не';
                } else {
                    $results[$i]['watering'] = 'Да';
                }
                if (false == $results[$i]['has_contracts']) {
                    $results[$i]['has_contracts'] = 'Не';
                } else {
                    $results[$i]['has_contracts'] = 'Да';
                }
                if ('' == $results[$i]['ekate'] || null == $results[$i]['ekate']) {
                    $results[$i]['ekate'] = '-';
                }
                $results[$i]['category'] = $results[$i]['category'];
                $results[$i]['area_kvs'] = number_format($results[$i]['area_kvs'] / 1000, 3);
                $results[$i]['used_area'] = number_format($results[$i]['used_area'], 3);

                if ($results[$i]['document_area']) {
                    $results[$i]['document_area'] = number_format($results[$i]['document_area'], 3);
                } else {
                    $results[$i]['document_area'] = $results[$i]['area_kvs'];
                }

                $results[$i]['irrigated_area'] = $results[$i]['irrigated_area'] ? $results[$i]['document_area'] : 0;
            }

            $return['rows'] = $results;
            $return['footer'] = [
                [
                    'area_type' => '<b>ОБЩО</b>',
                    'used_area' => number_format($total_used_area, 3),
                    'area_kvs' => number_format($total_area_kvs, 3),
                    'document_area' => number_format($total_document_area, 3),
                    'allowable_area' => number_format($total_allowable_area, 3),
                ],
            ];
        } else {
            $return['rows'] = false;
            $return['footer'] = [];
        }

        $return['rows'] = $results ?? [];
        $return['total'] = $counter ?? 0;
        $return['filtered_gids'] = $this->filteredGidsFromResults ? $this->filteredGidsFromResults : [];

        return $return;
    }
}
