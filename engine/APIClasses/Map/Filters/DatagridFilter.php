<?php

namespace TF\Engine\APIClasses\Map\Filters;

use Exception;
use TF\Application\Common\Config;
use TF\Application\Entity\UserLayers;
use TF\Engine\APIClasses\Map\BaseGrid;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

/**
 * This class is responsible for returning the dropdown values used for filtering layers datagrid.
 *
 * @rpc-module Map
 *
 * @rpc-service-id datagrid-filter
 */
class DatagridFilter extends BaseGrid
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'getValuesByColumn' => [
                'method' => [$this, 'getValuesByColumn'],
                'validators' => [
                    [
                        'layer_id' => 'validateRequired',
                        'col_name' => 'validateRequired, validateString',
                        'search' => 'validateString',
                        'filter_params' => [
                            'groups' => 'isAssocArray',
                            'full_text_search' => 'validateString',
                            'plot_ids' => 'validateStrictIntegerArray',
                        ],
                    ],
                ],
            ],
        ];
    }

    public function getValuesByColumn(array $rpcParams, ?int $page, ?int $rows)
    {
        $layer = UserLayers::getLayerById($rpcParams['layer_id']);

        if (!$layer) {
            throw new Exception('Layer not found');
        }

        if (Config::LAYER_TYPE_KVS == $layer->layer_type) {
            $kvsGridFilter = new KVSGridFilter($this->rpcServer);

            return $kvsGridFilter->getValuesByColumn($rpcParams, $page, $rows);
        }

        $visibleOrPersonalizableColumnsDef = UserLayers::filterDefinitions(
            $layer->getDefinitions(),
            [
                ['col_visible' => true],
                ['col_personalizable' => true],
            ]
        );
        $visibleOrPersonalizableColumns = UserLayers::getColumns($visibleOrPersonalizableColumnsDef);

        if (!in_array($rpcParams['col_name'], $visibleOrPersonalizableColumns)) {
            throw new Exception('Invalid value for parameter col_name');
        }

        $options = [
            'tablename' => $layer->table_name,
            'return' => [
                'DISTINCT ' . $rpcParams['col_name'],
            ],
            'custom_counter' => "COUNT(DISTINCT COALESCE({$rpcParams['col_name']}::text, '{$rpcParams['col_name']}')) AS count",
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $rpcParams['col_name'],
            'full_text_search_columns' => $visibleOrPersonalizableColumns,
            'order' => 'ASC NULLS FIRST',
            'layer_id' => $rpcParams['layer_id'],
        ];

        $this->buildWhere($options, $rpcParams);

        if (isset($rpcParams['search']) && strlen($rpcParams['search']) > 0) {
            $options['where'] = [['column' => $rpcParams['col_name'] . '::text', 'compare' => 'ILIKE', 'value' => $rpcParams['search']]];
        }

        $rows = [];
        $total = 0;
        if ($layer->isRemote()) {
            $controller = new LayersController('Layers');
            $rows = $controller->getRemoteLayerData($options);
            $total = $controller->getRemoteLayerData($options, true)[0]['count'] ?? 0;
        } else {
            $controller = new UserDbController($this->User->Database);
            $rows = $controller->getItemsByParams($options);
            $total = $controller->getItemsByParams($options, true)[0]['count'] ?? 0;
        }

        return [
            'rows' => array_column($rows, $rpcParams['col_name']),
            'total' => $total,
        ];
    }
}
