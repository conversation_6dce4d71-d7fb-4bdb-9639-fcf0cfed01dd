<?php

namespace TF\Engine\APIClasses\Map\Filters;

use DateTime;
use Exception;
use Prado\Web\Services\TRpcApiProvider;
use TF\Application\Common\Config;
use TF\Application\Entity\UserLayers;
use TF\Engine\APIClasses\Plots\PlotsTree;
use TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController;

/**
 * This class is responsible for returning the dropdown values used for filtering the KVS datagrid.
 *
 * @rpc-module Map
 */
class KVSGridFilter extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'getValuesByColumn' => [
                'method' => [$this, 'getValuesByColumn'],
                'validators' => [
                    'layer_id' => 'validateRequired',
                    'col_name' => 'validateRequired, validateString',
                    'search' => 'validateString',
                    'filter_params' => [
                        'groups' => 'isAssocArray',
                        'full_text_search' => 'validateString',
                        'plot_ids' => 'validateStrictIntegerArray',
                    ],
                ],
            ],
        ];
    }

    public function getValuesByColumn($rpcParams, int $page = 1, int $rows = 10)
    {
        $farmYearFilters = ['farm_year_active_contract_plots', 'expiring_contracts_for_farm_year', 'for_sublease_farm_years'];
        $layer = UserLayers::getLayerById($rpcParams['layer_id']);
        $this->validateLayer($layer);

        $column = $rpcParams['col_name'];
        $this->validateColumn($column);
        $filterOptions = $this->getKvsFilterOptions()[$column];
        $filterGroups = $rpcParams['filter_params']['groups'] ?? [];
        $search = $rpcParams['search'] ?? '';

        $plotsTree = new PlotsTree($this->rpcServer);

        [$group] = $filterGroups;
        // Set empty string for farm year filter to take specific where conditions
        $group[$column] = '';
        $filterGroups = [$group];

        $groupOptions = $plotsTree->buildWhere($filterGroups);

        if ($rpcParams['filter_params']['plot_statuses']) {
            $plotStatusesFilter = PlotsTree::getPlotStatusesFilter($rpcParams['filter_params']['plot_statuses']);

            if (count($plotStatusesFilter) > 0) {
                if (!empty($groupOptions)) {
                    foreach ($groupOptions as &$filter) {
                        $filter['where'] = array_merge($filter['where'] ?? [], $plotStatusesFilter);
                    }
                } else {
                    $groupOptions[]['where'] = $plotStatusesFilter;
                }
            }
        }

        $groupBy = in_array($column, ['expiring_contracts_for_farm_year']) ? null : $filterOptions['groupBy'] ?? null;
        $options = [
            'return' => [
                "{$filterOptions['selectColumn']} AS {$column}",
            ],
            'whereOrGroup' => $groupOptions,
            'where' => $filterOptions['where'] ?? [],
            'whereOr' => $filterOptions['whereOr'] ?? [],
            'group' => $groupBy,
        ];

        if ($filterOptions['searchPriority']) {
            $options['return'][] = "{$filterOptions['searchPriority']} as search_priority";
        }

        $joins = $plotsTree->determineJoinsByFilters($filterGroups, !empty($rpcParams['filter_params']['full_text_search']));
        $options['joins'] = array_values($joins['readyForUse']);

        if (count($joins['buildJoins'])) {
            $options['joins'] = [...$options['joins'], ...$plotsTree->buildJoins($joins['buildJoins'], $filterGroups)];
        }

        if ($filterOptions['additionalSelectOptions']) {
            $options['return'][] = $filterOptions['additionalSelectOptions'];
        }

        if (in_array($column, $farmYearFilters)
        || array_intersect_key(array_flip($farmYearFilters), $rpcParams['filter_params']['groups'][0])
        ) {
            $options['cte'] = $plotsTree->getSubleasedPlotsCte($rpcParams['filter_params']['groups']);
        }

        if ($rpcParams['gids']) {
            $options['whereOr']['gids'] = ['column' => 'gid', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $rpcParams['gids']];
        }

        if (strlen($search) > 0) {
            $options['where']['search'] = ['column' => "({$filterOptions['searchColumn']})::TEXT", 'compare' => 'ILIKE', 'value' => $search];
        }

        $plotsTree->buildFullTextSearchFilterGroup($options, $rpcParams);

        $userDbPlotsController = new UserDbPlotsController($this->User->Database);

        $filteredDataSql = $userDbPlotsController->getPlotsData($options, false, true);

        $offset = ($page - 1) * $rows;

        if ('expiring_contracts_for_farm_year' === $column) {
            $filteredDataSql = "
            with filtered_data as ({$filteredDataSql})
                select
                    expiring_contracts_for_farm_year, search_priority
                from filtered_data    
                where
                    (due_date is not null and next_farm_year_start is null)
                    or (due_date is not null and due_date < next_farm_year_start and due_date + interval '1 year' not between next_farm_year_start and next_farm_year_end )
                    or (due_date is null and sale_start_date is not null and next_farm_year_start is null)
                    or (due_date is null and sale_start_date is not null and sale_start_date not between next_farm_year_start and next_farm_year_end)
                    group by expiring_contracts_for_farm_year, search_priority";
        }

        $orderBy = [];
        if ($filterOptions['searchPriority']) {
            $orderBy[] = 'search_priority DESC';
        }
        $orderBy[] = "{$column} ASC NULLS FIRST";

        $searchedDataSql = "
                        {$filteredDataSql}
                        ORDER BY
                            " . implode(', ', $orderBy) . "
                        LIMIT 
                            {$rows}
                        OFFSET 
                            {$offset}";
        $rowsCmd = $userDbPlotsController->DbHandler->DbModule->createCommand($searchedDataSql);
        $rows = $rowsCmd->queryColumn();

        $cte = "WITH count_data AS ({$filteredDataSql})";

        $searchedDataTotalSql = "{$cte}
            SELECT 
                COUNT(*)
            FROM 
                count_data
        ";
        $totalCmd = $userDbPlotsController->DbHandler->DbModule->createCommand($searchedDataTotalSql);
        $total = $totalCmd->queryScalar();

        return [
            'rows' => $rows,
            'total' => $total,
        ];
    }

    private function validateLayer(?UserLayers $layer): void
    {
        if (!$layer) {
            throw new Exception('Layer not found');
        }

        if (Config::LAYER_TYPE_KVS != $layer->layer_type) {
            throw new Exception('Unsupported layer type');
        }
    }

    private function validateColumn(string $column): void
    {
        if (!array_key_exists($column, $this->getKvsFilterOptions())) {
            throw new Exception('Invalid value for parameter col_name');
        }
    }

    private function getKvsFilterOptions(): array
    {
        $currentFarmYear = $this->getCurrentFarmYear();
        $farmYearQuery = "to_char(fy.farm_year_start, 'YYYY') || '/' || to_char(fy.farm_year_start + interval '1 year', 'YYYY')";

        return [
            // KVS
            'kad_ident' => [
                'selectColumn' => 'kvs.kad_ident',
                'searchColumn' => 'kvs.kad_ident',
                'where' => ['kad_ident' => ['column' => 'kvs.kad_ident notnull', 'compare' => '=', 'value' => true]],
                'groupBy' => 'kvs.kad_ident',
            ],
            'ekate' => [
                'selectColumn' => 'kvs.ekate',
                'searchColumn' => 'kvs.ekate',
                'where' => ['ekate' => ['column' => 'kvs.ekate notnull', 'compare' => '=', 'value' => true]],
                'groupBy' => 'kvs.ekate',
            ],
            'masiv' => [
                'selectColumn' => 'kvs.masiv',
                'searchColumn' => 'kvs.masiv',
                'where' => ['masiv' => ['column' => 'kvs.masiv notnull', 'compare' => '=', 'value' => true]],
                'groupBy' => 'kvs.masiv',
            ],
            'number' => [
                'selectColumn' => 'kvs.number',
                'searchColumn' => 'kvs.number',
                'where' => ['number' => ['column' => 'kvs.number notnull', 'compare' => '=', 'value' => true]],
                'groupBy' => 'kvs.number',
            ],
            'virtual_category_title' => [
                'selectColumn' => 'kvs.virtual_category_title',
                'searchColumn' => 'kvs.virtual_category_title',
                'where' => ['virtual_category_title' => ['column' => 'kvs.virtual_category_title notnull', 'compare' => '=', 'value' => true]],
                'groupBy' => 'kvs.virtual_category_title',
            ],
            'virtual_ntp_title' => [
                'selectColumn' => 'kvs.virtual_ntp_title',
                'searchColumn' => 'kvs.virtual_ntp_title',
                'where' => ['virtual_ntp_title' => ['column' => 'kvs.virtual_ntp_title notnull', 'compare' => '=', 'value' => true]],
                'groupBy' => 'kvs.virtual_ntp_title',
            ],
            'mestnost' => [
                'selectColumn' => 'kvs.mestnost',
                'searchColumn' => 'kvs.mestnost',
                'where' => ['mestnost' => ['column' => 'kvs.mestnost notnull', 'compare' => '=', 'value' => true]],
                'groupBy' => 'kvs.mestnost',
            ],
            'block' => [
                'selectColumn' => 'kvs.block',
                'searchColumn' => 'kvs.block',
                'where' => ['block' => ['column' => 'kvs.block notnull', 'compare' => '=', 'value' => true]],
                'groupBy' => 'kvs.block',
            ],
            'allowable_type' => [
                'selectColumn' => 'kvs.allowable_type',
                'searchColumn' => 'kvs.allowable_type',
                'groupBy' => 'kvs.allowable_type',
            ],
            'irrigated_area' => [
                'selectColumn' => 'kvs.irrigated_area',
                'searchColumn' => 'kvs.irrigated_area',
                'where' => ['irrigated_area' => ['column' => 'kvs.irrigated_area notnull', 'compare' => '=', 'value' => true]],
                'groupBy' => 'kvs.irrigated_area',
            ],

            // CONTRACT
            'cnum' => [
                'selectColumn' => 'c.c_num',
                'searchColumn' => 'c.c_num',
                'where' => ['c_num' => ['column' => 'c.c_num notnull', 'compare' => '=', 'value' => true]],
                'groupBy' => 'c.c_num',
            ],
            'contract_type' => [
                'selectColumn' => 'c.nm_usage_rights',
                'searchColumn' => 'c.nm_usage_rights',
                'where' => ['nm_usage_rights' => ['column' => 'c.nm_usage_rights notnull', 'compare' => '=', 'value' => true]],
                'groupBy' => 'c.nm_usage_rights',
            ],
            'virtual_contract_type' => [
                'selectColumn' => 'c.virtual_contract_type',
                'searchColumn' => 'c.virtual_contract_type',
                'where' => ['virtual_contract_type' => ['column' => 'c.virtual_contract_type notnull', 'compare' => '=', 'value' => true]],
                'groupBy' => 'c.virtual_contract_type',
            ],
            'contract_status_text' => [
                'selectColumn' => 'get_contract_status(c.id, c.active, c.start_date, c.due_date)',
                'searchColumn' => 'get_contract_status(c.id, c.active, c.start_date, c.due_date)',
                'where' => [
                    'annex_action' => ['column' => 'annex_action', 'compare' => '=', 'prefix' => 'pc', 'value' => 'added'],
                    'contract_status_text' => ['column' => 'get_contract_status(c.id, c.active, c.start_date, c.due_date) notnull', 'compare' => '=', 'value' => true],
                ],
                'groupBy' => 'get_contract_status(c.id, c.active, c.start_date, c.due_date)',
            ],
            'cnum_exact' => [
                'selectColumn' => 'c.c_num',
                'searchColumn' => 'c.c_num',
                'where' => ['c_num' => ['column' => 'c.c_num notnull', 'compare' => '=', 'value' => true]],
                'groupBy' => 'c.c_num',
            ],
            'farming_name' => [
                'selectColumn' => 'c.farming_name',
                'searchColumn' => 'c.farming_name',
                'where' => ['farming_name' => ['column' => 'c.farming_name notnull', 'compare' => '=', 'value' => true]],
                'groupBy' => 'c.farming_name',
            ],
            'participation' => [
                'selectColumn' => "CASE
                WHEN kvs.participate = TRUE and kvs.include = FALSE and kvs.white_spots = FALSE
                    THEN 'participate'
                WHEN kvs.participate = FALSE and kvs.include = TRUE and kvs.white_spots = FALSE
                    THEN 'no_participate'
                WHEN kvs.participate = FALSE and kvs.include = FALSE and kvs.white_spots = TRUE
                    THEN 'white_spots'
                ELSE 
                    'without'
                END
            ",
                'searchColumn' => "CASE
                WHEN kvs.participate = TRUE and kvs.include = FALSE and kvs.white_spots = FALSE
                    THEN 'participate'
                WHEN kvs.participate = FALSE and kvs.include = TRUE and kvs.white_spots = FALSE
                    THEN 'no_participate'
                WHEN kvs.participate = FALSE and kvs.include = FALSE and kvs.white_spots = TRUE
                    THEN 'white_spots'
                ELSE 
                    'without'
                END
            ",
                'groupBy' => 'kvs.participate, kvs.include, kvs.white_spots',
            ],
            'comment' => [
                'selectColumn' => 'kvs.comment',
                'searchColumn' => 'kvs.comment',
                'where' => ['comment' => ['column' => 'kvs.comment notnull', 'compare' => '=', 'value' => true]],
                'groupBy' => 'kvs.comment',
            ],

            // OWNERSHIP
            'person_name' => [
                'selectColumn' => "
                unnest(
                    array_remove(
                        ARRAY[
                            trim(o.name) || ' ' || trim(o.surname) || ' ' || trim(o.lastname),
                            trim(o_r.rep_name) || ' ' || trim(o_r.rep_surname) || ' ' || trim(o_r.rep_lastname)
                        ],
                        NULL
                    )
                )
            ",
                'searchColumn' => "
                    trim(o.name) || ' ' || trim(o.surname) || ' ' || trim(o.lastname),
                    trim(o_r.rep_name) || ' ' || trim(o_r.rep_surname) || ' ' || trim(o_r.rep_lastname)
                ",
                'groupBy' => 'person_name',
            ],
            'person_egn' => [
                'selectColumn' => "
                unnest(
                    array_remove(
                        array[
                            CASE WHEN o.egn IS NOT NULL AND o.egn <> '' THEN o.egn ELSE NULL END,
                            CASE WHEN o_r.rep_egn IS NOT NULL AND o_r.rep_egn <> '' THEN o_r.rep_egn ELSE NULL END
                        ],
                        NULL
                    )
                )
            ",
                'searchColumn' => '
                    o.egn,
                     o_r.rep_egn
                ',
                'groupBy' => 'person_egn',
            ],
            'owner_name' => [
                'selectColumn' => "trim(o.name) || ' ' || trim(o.surname) || ' ' || trim(o.lastname)",
                'searchColumn' => "trim(o.name) || ' ' || trim(o.surname) || ' ' || trim(o.lastname)",
                'where' => [
                    'owner_name' => ['column' => "trim(o.name) || ' ' || trim(o.surname) || ' ' || trim(o.lastname) notnull", 'compare' => '=', 'value' => true],
                ],
                'groupBy' => "trim(o.name) || ' ' || trim(o.surname) || ' ' || trim(o.lastname)",
            ],
            'owner_egn' => [
                'selectColumn' => 'o.egn',
                'searchColumn' => 'o.egn',
                'where' => ['egn' => ['column' => 'o.egn notnull', 'compare' => '=', 'value' => true]],
                'groupBy' => 'o.egn',
            ],
            'osz_ime_subekt' => [
                'selectColumn' => 'tkvs.owner_name',
                'searchColumn' => 'tkvs.owner_name',
                'where' => ['owner_name' => ['column' => 'tkvs.owner_name notnull', 'compare' => '=', 'value' => true]],
                'groupBy' => 'tkvs.owner_name',
            ],
            'egn_subekt' => [
                'selectColumn' => 'tkvs.egn_subekt',
                'searchColumn' => 'tkvs.egn_subekt',
                'where' => ['egn_subekt' => ['column' => 'tkvs.egn_subekt notnull', 'compare' => '=', 'value' => true]],
                'groupBy' => 'tkvs.egn_subekt',
            ],
            'rep_name' => [
                'selectColumn' => "trim(o_r.rep_name) || ' ' || trim(o_r.rep_surname) || ' ' || trim(o_r.rep_lastname)",
                'searchColumn' => "trim(o_r.rep_name) || ' ' || trim(o_r.rep_surname) || ' ' || trim(o_r.rep_lastname)",
                'where' => [
                    'owner_name' => ['column' => "trim(o_r.rep_name) || ' ' || trim(o_r.rep_surname) || ' ' || trim(o_r.rep_lastname) notnull", 'compare' => '=', 'value' => true],
                ],
                'groupBy' => "trim(o_r.rep_name) || ' ' || trim(o_r.rep_surname) || ' ' || trim(o_r.rep_lastname)",
            ],
            'rep_egn' => [
                'selectColumn' => 'o_r.rep_egn',
                'searchColumn' => 'o_r.rep_egn',
                'where' => ['rep_egn' => ['column' => 'o_r.rep_egn notnull', 'compare' => '=', 'value' => true]],
                'groupBy' => 'o_r.rep_egn',
            ],
            'company_name' => [
                'selectColumn' => 'o.company_name',
                'searchColumn' => 'o.company_name',
                'where' => ['company_name' => ['column' => 'o.company_name notnull', 'compare' => '=', 'value' => true]],
                'groupBy' => 'o.company_name',
            ],
            'company_eik' => [
                'selectColumn' => 'o.eik',
                'searchColumn' => 'o.eik',
                'where' => ['company_eik' => ['column' => 'o.eik notnull', 'compare' => '=', 'value' => true]],
                'groupBy' => 'o.eik',
            ],
            'heritor_name' => [
                'selectColumn' => "trim(o.name) || ' ' || trim(o.surname) || ' ' || trim(o.lastname)",
                'searchColumn' => "trim(o.name) || ' ' || trim(o.surname) || ' ' || trim(o.lastname)",
                'where' => [
                    'is_heritor' => ['column' => 'is_heritor', 'compare' => '=', 'prefix' => 'po', 'value' => true],
                    'heritor_name' => ['column' => "trim(o.name) || ' ' || trim(o.surname) || ' ' || trim(o.lastname) notnull", 'compare' => '=', 'value' => true],
                ],
                'groupBy' => "trim(o.name) || ' ' || trim(o.surname) || ' ' || trim(o.lastname)",
            ],
            'heritor_egn' => [
                'selectColumn' => 'o.egn',
                'searchColumn' => 'o.egn',
                'where' => [
                    'is_heritor' => ['column' => 'is_heritor', 'compare' => '=', 'prefix' => 'po', 'value' => true],
                    'heritor_egn' => ['column' => 'o.egn notnull', 'compare' => '=', 'value' => true],
                ],
                'groupBy' => 'o.egn',
            ],
            'owner_multi_field_search' => [
                'selectColumn' => "
                        unnest(
                            array_remove(
                                array[
                                    trim(tkvs.owner_name),
                                    trim(o.name) || ' ' || trim(o.surname) || ' ' || trim(o.lastname),
                                    trim(o_r.rep_name) || ' ' || trim(o_r.rep_surname) || ' ' || trim(o_r.rep_lastname)
                                ],
                                NULL
                            )
                        )",
                'searchColumn' => "
                    trim(tkvs.owner_name), 
                    trim(o.name) || ' ' || trim(o.surname) || ' ' || trim(o.lastname),
                    trim(o_r.rep_name) || ' ' || trim(o_r.rep_surname) || ' ' || trim(o_r.rep_lastname)",
                'groupBy' => 'owner_multi_field_search',
            ],

            // Farming years
            'farm_year_active_contract_plots' => [
                'selectColumn' => $farmYearQuery,
                'searchColumn' => $farmYearQuery,
                'searchPriority' => "case 
                                        when  {$farmYearQuery} = '" . $this->formatFarmYear($currentFarmYear) . "'::text then 1 
                                        when  {$farmYearQuery} = '" . $this->formatFarmYear($currentFarmYear + 1) . "'::text then 0.9 
                                        else 0 
                                        end",
                'groupBy' => 'farm_year_active_contract_plots',
            ],
            'expiring_contracts_for_farm_year' => [
                'selectColumn' => $farmYearQuery,
                'additionalSelectOptions' => "
                    fy.farm_year_start,
                    (fy.farm_year_start + INTERVAL '11 months' + INTERVAL '29 days')::date as farm_year_end,
                    c.start_date,
                    c.due_date,
                    sp.sale_start_date,
                    LEAD(fy.farm_year_start) OVER (PARTITION BY kvs.gid ORDER BY fy.farm_year_start) AS next_farm_year_start,
                    LEAD(fy.farm_year_start + interval '1 year') OVER (PARTITION BY kvs.gid ORDER BY fy.farm_year_start) AS next_farm_year_end",
                'searchColumn' => $farmYearQuery,
                'searchPriority' => "case 
                                        when  {$farmYearQuery} = '" . $this->formatFarmYear($currentFarmYear) . "'::text then 1 
                                        when  {$farmYearQuery} = '" . $this->formatFarmYear($currentFarmYear + 1) . "'::text then 0.9 
                                        else 0 
                                        end",
                'groupBy' => 'expiring_contracts_for_farm_year',
            ],
            'for_sublease_farm_years' => [
                'selectColumn' => $farmYearQuery,
                'searchColumn' => $farmYearQuery,
                'searchPriority' => "case 
                                        when  {$farmYearQuery} = '" . $this->formatFarmYear($currentFarmYear) . "'::text then 1 
                                        when  {$farmYearQuery} = '" . $this->formatFarmYear($currentFarmYear + 1) . "'::text then 0.9 
                                        else 0 
                                        end",
                'groupBy' => 'for_sublease_farm_years',
            ],
        ];
    }

    private function getCurrentFarmYear(): int
    {
        $currentDate = new DateTime();
        $currentYear = (int) $currentDate->format('Y');

        $farmYearStart = new DateTime("{$currentYear}-10-01");
        $farmYearEnd = new DateTime(($currentYear + 1) . '-09-30');

        if ($currentDate >= $farmYearStart && $currentDate <= $farmYearEnd) {
            $farmYear = ($currentYear + 1);
        } else {
            $farmYear = $currentYear;
        }

        return $farmYear;
    }

    private function formatFarmYear($year): string
    {
        return ($year - 1) . "/{$year}";
    }
}
