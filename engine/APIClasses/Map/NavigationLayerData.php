<?php

namespace TF\Engine\APIClasses\Map;

use Exception;
use Prado\Web\Services\TRpcApiProvider;
use TF\Application\Common\Config;
use TF\Application\Entity\UserLayers;
use TF\Engine\Kernel\ExportData\LayerTypes\LayerZP;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

/**
 * <AUTHOR> Atanasov <<EMAIL>>
 *
 * @link http://www.technofarm.bg/
 *
 * @copyright Copyright &copy; 2015 TechnoFarm Ltd.
 *
 * @rpc-module Map
 *
 * @rpc-service-id navigation-layer-data
 */
class NavigationLayerData extends TRpcApiProvider
{
    /**
     * Registers the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'getLineFeature' => ['method' => [$this, 'getLineFeature'],
                'validators' => [
                    'data' => [
                        'layer_name' => 'validateRequired, validateNotNull',
                        'layer_type' => 'validateRequired, validateNotNull',
                        'layer_id' => 'validateRequired, validateNotNull',
                    ],
                ],
            ],
        ];
    }

    public function getLineFeature($data)
    {
        $userDbController = new UserDbController($this->User->database);

        $options = [
            'tablename' => $data['layer_name'],
            'return' => LayerZP::$dbfColumns['exportTrimble'],
        ];
        $offset = $data['line_feature_offset'];
        $layer = UserLayers::getLayerById($data['layer_id']);

        if (!$layer) {
            throw new Exception('Invalid layer');
        }

        if (isset($data['selected_ids']) && count($data['selected_ids']) > 0) {
            $gidColumn = $layer->getDefinitionByCategory(Config::LAYER_COLUMN_CATEGORY_GID)['col_name'];
            $options['where']['id'] = [
                'column' => $gidColumn,
                'compare' => 'IN',
                'value' => $data['selected_ids'],
            ];
        }

        $result = $userDbController->getLineFeatureGeoJson($options, $offset);
        if (empty($result['geoJSON'])) {
            throw new Exception('Invalid data');
        }

        return $result;
    }
}
