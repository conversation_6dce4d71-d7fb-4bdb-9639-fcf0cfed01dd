<?php

namespace TF\Engine\APIClasses\Map;

use TF\Application\Common\Config;
use TF\Application\Entity\UserLayers;
use TF\Engine\Kernel\ExportToExcelClass;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

/**
 * Атрибутна информация "Земеделски парцели".
 *
 * @rpc-module Map
 *
 * @rpc-service-id zp-datagrid
 */
class ZPGrid extends BaseGrid
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getZPGrid'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'exportToExcel' => ['method' => [$this, 'exportToExcel'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'multiEdit' => ['method' => [$this, 'multiEdit']],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer layer_id
     *                         #item boolean clear_filter
     *                         #item string  action
     *                         #item string  ekatte
     *                         #item string  culture
     *                         #item string  obrabotki
     *                         #item string  dobivi
     *                         #item string  napoqvane
     *                         #item string  polivki
     *                         #item string  polzvatel
     *                         #item string  isak_prc_uin
     *                         #item array   ids
     *                         {
     *                         #item integer id
     *                         }
     *                         }
     * @param string $page pagination parameter
     * @param string $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array
     */
    public function getZPGrid(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $UserDbController = new UserDbController($this->User->Database);
        $return = [];
        $filteredGidsFromResults = [];
        // creating a default empty return
        $empty_return = [
            'rows' => [],
            'total' => 0,
        ];

        $layer = UserLayers::getLayerById($rpcParams['layer_id']);

        if (!$layer) {
            return $empty_return;
        }

        $tableExists = $UserDbController->getTableNameExist($layer->table_name);
        if (!$tableExists) {
            return $empty_return;
        }

        $options = [
            'tablename' => "{$layer->table_name} as zp",
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
            'custom_counter' => 'COUNT(DISTINCT(zp.id)), SUM(round((ST_Area(geom)/1000)::numeric, 3)) as total_area_zp',
            'return' => [
                'zp.culture',
                'ST_Area(zp.geom) as area_zp',
                'virtual_ekatte_name as ekatte_name',
                'zp.dobivi', 'zp.ekatte',
                'zp.id as gid',
                'zp.isak_prc_uin',
                'zp.napoqvane',
                'zp.obrabotki',
                'zp.polivki',
                'polzvatel',
                'ST_ASTEXT(geom)',
                'area_name',
            ],
            'full_text_search_columns' => [
                'zp.dobivi',
                'zp.ekatte',
                'zp.isak_prc_uin',
                'zp.napoqvane',
                'zp.obrabotki',
                'zp.polivki',
                'zp.polzvatel',
                'zp.area_name',
            ],
            'layer_id' => $rpcParams['layer_id'],
        ];

        $this->buildWhere($options, $rpcParams);
        $results = $UserDbController->getLayersByParams($options, false);
        $counter = $UserDbController->getLayersByParams($options, true);

        $results_count = count($results);

        $filteredGidsFromResults = [];
        for ($i = 0; $i < $results_count; $i++) {
            if (Config::LAYER_TYPE_ZP == $layer->layer_type) {
                if ('' == $results[$i]['ekatte'] || null == $results[$i]['ekatte']) {
                    $results[$i]['ekatte'] = '-';
                }
                $results[$i]['cropcode'] = (int) $results[$i]['culture'];
                $culture = (int) $results[$i]['culture'];
                $results[$i]['culture'] = $GLOBALS['Farming']['crops'][$culture]['crop_name'];
                $results[$i]['area_zp'] = number_format($results[$i]['area_zp'] / 1000, 3, '.', '');
            }

            $filteredGidsFromResults[] = $results[$i]['gid'];
        }

        $return['rows'] = $results;
        $return['total'] = $counter[0]['count'];
        $return['filtered_gids'] = $filteredGidsFromResults ? $filteredGidsFromResults : [];

        $total_area_zp = $counter[0]['total_area_zp'];

        $return['footer'] = [
            [
                'polzvatel' => '<b>ОБЩО</b>',
                'area_zp' => $total_area_zp,
            ],
        ];

        return $return;
    }

    /**
     * @api-method exportToExcel
     *
     * @param array|string $rpcParams
     *                                {
     *                                #item integer   layer_type
     *                                #item integer   layer_id
     *                                #item array   ids
     *                                }
     * @param string $page
     * @param string $rows
     * @param ?string $sort
     * @param ?string $order
     *
     * @return string
     */
    public function exportToExcel(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $time = strtotime(date('Y-m-d H:i:s'));
        $filename = PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID . '/zp_table_' . $time . '.xlsx';

        if (!file_exists(PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID)) {
            mkdir(PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID, 0777);
        }

        $headers = [
            'area_name' => 'Име на парцела',
            'isak_prc_uin' => 'ИСАК номер',
            'ekatte' => 'ЕКАТТЕ',
            'culture' => 'Култура',
            'obrabotki' => 'Обработки',
            'dobivi' => 'Добиви',
            'napoqvane' => 'Напояване',
            'polivki' => 'Поливки',
            'polzvatel' => 'Ползвател',
            'area_zp' => 'Площ(дка)',
        ];

        $data = $this->getZPGrid($rpcParams, $page, $rows, $sort, $order);

        $totalAreaZp = $data['footer'][0]['area_zp'];
        $footerArray = [
            'area_name' => '',
            'isak_prc_uin' => '',
            'ekate' => '',
            'culture' => '',
            'obrabotki' => '',
            'dobivi' => '',
            'napoqvane' => '',
            'polivki' => '',
            'polzvatel' => 'ОБЩО',
            'area_zp' => $totalAreaZp,
        ];
        array_push($data['rows'], $footerArray);

        $exportExcelDoc = new ExportToExcelClass();
        $exportExcelDoc->export($data['rows'], $headers, []);
        $exportExcelDoc->saveFile($filename);

        return 'files/uploads/export/' . $this->User->UserID . '/zp_table_' . $time . '.xlsx';
    }
}
