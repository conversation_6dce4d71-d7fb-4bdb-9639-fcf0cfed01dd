<?php

namespace TF\Engine\APIClasses\Map;

use Exception;
use Prado\Web\Services\TRpcApiProvider;
use TF\Application\Common\Config;
use TF\Application\Entity\UserLayers;
use TF\Engine\Kernel\ExportToExcelClass;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Kernel\Sentry\Sentry;

/**
 * @rpc-module Map
 *
 * @rpc-service-id layer-cadastre
 */
class LayerCadastre extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getData']],
            'exportToExcel' => ['method' => [$this, 'exportToExcel']],
        ];
    }

    /**
     * @param array $filter
     */
    public function getData(array $rpcParams = []): array
    {
        $ident = $rpcParams['filter_params']['full_text_search'] ?? '';
        $plotIds = $rpcParams['filter_params']['plot_ids'] ?? [];

        if (!$ident && !$plotIds) {
            return ['rows' => [], 'total' => 0];
        }

        $params = [];

        if (strlen($ident) > 0) {
            $params['kad_ident'] = $ident;
        }

        if (count($plotIds) > 0) {
            $params['plot_ids'] = json_encode($plotIds);
        }

        try {
            $storeModule = $this->getApplication()->getModule('kvsStoreModule');
            $data = $storeModule->getPlotAttributes($params);

            return [
                'rows' => [
                    $data,
                ],
                'filtered_gids' => $data['gid'] ? [$data['gid']] : [],
                'total' => count($data) ? 1 : 0,
            ];
        } catch (MTRpcException $ex) {
            if (404 === $ex->getErrorCodeNumber()) {
                throw new MTRpcException('PLOT_NOT_FOUND', -33356);
            }

            throw $ex;
        } catch (Exception $ex) {
            throw new MTRpcException('MAP_LAYER_CADASTRE_ERROR', -33069);
        }
    }

    public function exportToExcel(array $rpcParams = [])
    {
        // Get the cadastre layer to retrieve its definitions
        $cadastreLayer = $GLOBALS['Layers']['customLayers'][Config::LAYER_TYPE_CADASTRE];
        $layer = UserLayers::getLayerById($cadastreLayer['id']);

        if (!$layer) {
            throw new MTRpcException('LAYER_NOT_FOUND', -346059);
        }

        // Get layer definitions and filter for visible/exportable columns
        $layerDefinitions = $layer->getDefinitions();
        $exportableDefinitions = UserLayers::filterDefinitions(
            $layerDefinitions,
            [
                ['col_visible' => true],
                ['col_exportable' => true],
            ]
        );

        // Build headers array (col_name => col_title)
        $headersTranslation = [
            'kad_ident' => 'Идентификатор',
            'mestnost' => 'Населено място',
            'virtual_category_title' => 'Категория',
            'virtual_ntp_title' => 'НТП',
            'area' => 'Площ (дка)',
            'virtual_ekatte_name' => 'ЕКАТТЕ',
        ];

        $headers = [];
        foreach ($exportableDefinitions as $definition) {
            $headers[$definition['col_name']] = $headersTranslation[$definition['col_name']] ?? $definition['col_title'];
        }

        // Get the data to export using the existing getData method
        $dataResult = $this->getData($rpcParams);
        $rows = $dataResult['rows'] ?? [];

        // Filter each row to include only the exportable columns
        $exportData = [];
        foreach ($rows as $row) {
            $filteredRow = [];
            foreach (array_keys($headers) as $colName) {
                $filteredRow[$colName] = $row[$colName] ?? '';
            }
            $exportData[] = $filteredRow;
        }

        // Generate unique filename with timestamp
        $timestamp = date('Y-m-d_H-i-s');
        $filename = "cadastre_export_{$timestamp}.xlsx";

        // Create user-specific export directory path
        $userExportDir = PUBLIC_UPLOAD_EXPORT . DIRECTORY_SEPARATOR . $this->User->UserID;
        if (!is_dir($userExportDir)) {
            mkdir($userExportDir, 0755, true);
        }

        $filePath = $userExportDir . DIRECTORY_SEPARATOR . $filename;

        // Create Excel file using ExportToExcelClass
        $excelExporter = new ExportToExcelClass();

        try {
            $excelExporter->export($exportData, $headers);
            $excelExporter->saveFile($filePath);
        } catch (Exception $ex) {
            Sentry::logException($ex);

            throw new MTRpcException('EXCEL_EXPORT_ERROR', -346060);
        }

        // Return relative path for download
        return 'files/uploads/export/' . $this->User->UserID . '/' . $filename;
    }
}
