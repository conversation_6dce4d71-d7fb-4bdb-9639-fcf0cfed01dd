<?php

namespace TF\Engine\APIClasses\Map;

use TF\Engine\Plugins\Core\Layers\LayersController;

/**
 * Атрибутна информация "ВВПС Царски орел и Египетски лешояд".
 *
 * @rpc-module Map
 *
 * @rpc-service-id layer-vps-orli-leshoyadi-datagrid
 */
class VPSOrliLeshoyadiLayerGrid extends BaseGrid
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getVPSOrliLeshoyadiLayerGrid'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer layer_id
     *                         #item boolean clear_filter
     *                         #item string  ekatte
     *                         #item string  blockuin
     *                         #item string  action
     *                         #item integer layer
     *                         #item integer layer_type
     *                         #item array   gids
     *                         {
     *                         #item integer gid
     *                         }
     *                         }
     * @param string $page pagination parameter
     * @param string $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array
     */
    public function getVPSOrliLeshoyadiLayerGrid(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $LayersController = new LayersController('Layers');

        $return = [];

        // creating a default empty return
        $empty_return = [
            'rows' => [],
            'total' => 0,
        ];

        if (!isset($rpcParams['layer_id'])) {
            return $empty_return;
        }

        $options = [];

        // for search/sort actions */

        $options['options'] = [
            'tablename' => 'layer_vps_orli_leshoyadi',
            'return' => [
                'gid', 'blockuin', 'ST_ASTEXT(geom)', 'ekatte', 'ntp', 'ekatte_name',
            ],
            'full_text_search_columns' => ['ekatte', 'blockuin', 'ntp'],
            'layer_id' => $rpcParams['layer_id'],
        ];

        if (0 == $LayersController->getColumnNameExistInDefaultDatabase('layer_vps_orli_leshoyadi', $options['options']['sort'])
            && 0 == $LayersController->getColumnNameExistInDefaultDatabase('su_ekatte', $options['options']['sort'])) {
            $options['options']['sort'] = 'gid';
        }

        $options['options']['offset'] = ($page - 1) * $rows;
        $options['options']['limit'] = $rows;
        $options['options']['sort'] = $sort;
        $options['options']['order'] = $order;

        $this->buildWhere($options['options'], $rpcParams);
        $postResult = $this->getLayersCached($options);
        $counter = $postResult['count'];
        $layer_results = $postResult['data'];
        $layersCount = count($layer_results);
        $results = [];

        for ($i = 0; $i < $layersCount; $i++) {
            $results[$i]['gid'] = $layer_results[$i]['gid'];
            $results[$i]['blockuin'] = $layer_results[$i]['blockuin'];
            $results[$i]['vps_type'] = 'Земи с висока природна стойност в местообитанията на <br/>Царския орел и Египетския лешояд';
            $results[$i]['st_astext'] = $layer_results[$i]['st_astext'];
            $results[$i]['ekatte_name'] = $layer_results[$i]['ekatte_name'] . '(' . $layer_results[$i]['ekatte'] . ')';
            $results[$i]['ntp'] = $layer_results[$i]['ntp'];

            $filteredGidsFromResults[] = $layer_results[$i]['gid'];
        }

        $return['rows'] = $results;
        $return['total'] = $counter[0]['count'];
        $return['filtered_gids'] = $filteredGidsFromResults ? $filteredGidsFromResults : [];

        return $return;
    }

    public function getLayersCached(array $options)
    {
        $LayersController = new LayersController('Layers');

        $options['options']['tablename'] . 'post';
        $options = $options['options'];
        $hash = md5(json_encode($options));
        $result = $LayersController->MemCache->get($hash);
        if ($result) {
            return $result;
        }

        $result = [];

        $result['data'] = $LayersController->getRemoteLayerOrliLeshoyadiData($options, false, false);
        $result['count'] = $LayersController->getRemoteLayerOrliLeshoyadiData($options, true, false);

        $LayersController->MemCache->add($hash, $result, $LayersController->default_memcache_expire);

        return $result;
    }
}
