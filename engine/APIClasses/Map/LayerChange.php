<?php

namespace TF\Engine\APIClasses\Map;

use Exception;
use Prado\Web\Services\TRpcApiProvider;
use TF\Application\Common\Config;
use TF\Application\Entity\UserLayers;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Попълва текущите настройки за активния слой.
 *
 * @rpc-module Map
 *
 * @rpc-service-id layer-change
 */
class LayerChange extends TRpcApiProvider
{
    private $module = 'Map';
    private $service_id = 'layer-change';

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'markForEdit' => ['method' => [$this, 'markForEdit']],
            'save' => ['method' => [$this, 'saveLayerPersonalization'],
                'validators' => [
                    'rpcParams' => [
                        'id' => 'validateInteger',
                        'fill_color' => 'validateText',
                        'border_color' => 'validateText',
                        'border_width' => 'validateNumber',
                        'transparency' => 'validateText',
                        'tags' => 'validateText',
                        'border_only' => 'validateText',
                    ],
                ],
            ],
            'updateWorkLayerAttributes' => ['method' => [$this, 'updateWorkLayerAttributes'],
                'validators' => [
                    'rpcParams' => [
                        'farming' => 'validateInteger',
                        'year' => 'validateInteger',
                        'layerID' => 'validateInteger',
                    ],
                ],
            ],
            'updateWorkLayerColumnsDefinitions' => ['method' => [$this, 'updateWorkLayerColumnsDefinitions']],
            'setWorkLayerColumnsByDefinitions' => ['method' => [$this, 'setWorkLayerColumnsByDefinitions']],
            'getWorkLayerColumnsDefinitions' => ['method' => [$this, 'getWorkLayerColumnsDefinitions']],
            'getColoringAttributeData' => ['method' => [$this, 'getColoringAttributeData'], 'validators' => [
                'rpcParams' => [
                    'layer_id' => 'validateInteger',
                    'fill_column_name' => 'validateText',
                    'border_column_name' => 'validateText',
                    'ekatte' => 'validateText',
                ],
            ], ],
        ];
    }

    /**
     * @api-method markForEdit
     *
     * @param int $rpcParam
     *
     * @throws MTRpcException
     *
     * @return array
     */
    public function markForEdit($rpcParam)
    {
        $layer_id = $rpcParam;
        $layer = UserLayers::getLayerById($layer_id);

        if (Config::LAYER_TYPE_KVS == $layer->layer_type) {
            $style = $layer->getStyles();
            $keys = array_column($style, 'layer_id');
            $ekates = array_map(function ($key) {
                [$kvsId, $ekate] = explode('_', $key);

                return $ekate;
            }, $keys);
            $style = array_combine($ekates, $style);
        } else {
            [$style] = $layer->getStyles();
        }

        $layer->style = json_encode($style);
        $layer = $layer->toArray();

        if (!$layer) {
            throw new MTRpcException('MAP_REQUESTED_LAYER_NOT_FOUND', -33053);
        }
        unset($layer['table_name'], $layer['date_created']);

        return $layer;
    }

    /**
     * Записва направените промени.
     *
     * @api-method save
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer     id
     *                         #item hexidecimal color
     *                         #item hexidecimal border_color
     *                         #item integer     transparency
     *                         #item boolean     tags
     *                         #item boolean     border_only
     *                         #item string      border_width
     *                         }
     *
     * @throws MTRpcException
     * @throws Exception
     *
     * @return array
     */
    public function saveLayerPersonalization($rpcParams)
    {
        $UsersDbController = new UserDbController($this->User->Database);
        $LayersController = new LayersController('Layers');

        $layer = UserLayers::getLayerById($rpcParams['id']);

        if (isset($rpcParams['label_name']) && !empty($rpcParams['label_name'])) {
            $rpcParams['label_name'] = array_values(array_filter($rpcParams['label_name']));
        }

        if (empty($rpcParams['ekatte']) && Config::LAYER_TYPE_KVS == $layer->layer_type) {
            $ekatteCombobox = $UsersDbController->DbHandler->getEkatteCombobox();
            $rpcParams['ekatte'] = array_column($ekatteCombobox, 'ekate');
        }

        $LayersController->saveLayerPersonalization($layer, $rpcParams, $this->module, $this->service_id);

        return [
            'extent' => str_replace(' ', ', ', $layer->extent),
            'layer_name' => $layer->table_name,
        ];
    }

    /**
     * @api-method getWorkLayerColumnsDefinitions
     *
     * @param int $rpcParam The layer id
     *
     * @throws MTRpcException
     *
     * @deprecated Remove this method when the definitions in FE are get from the layers tree and when the functionality is removed from TF FE
     *
     * @return array
     */
    public function getWorkLayerColumnsDefinitions($rpcParam)
    {
        $layer = UserLayers::getLayerById($rpcParam);

        if (!$layer) {
            throw new MTRpcException('MAP_REQUESTED_LAYER_NOT_FOUND', -33053);
        }

        $definitions = $layer->getDefinitions();

        return UserLayers::filterDefinitions($definitions, [['col_personalizable' => true]]);
    }

    /**
     * @api-method setWorkLayerColumnsByDefinitions
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer     layer_id
     *                         #item hexidecimal definitions
     *                         }
     *
     * @throws MTRpcException
     * @throws Exception
     *
     * @return array
     */
    public function setWorkLayerColumnsByDefinitions(array $rpcParams)
    {
        $layer = UserLayers::getLayerById($rpcParams['layer_id']);

        if (!$layer) {
            throw new MTRpcException('MAP_REQUESTED_LAYER_NOT_FOUND', -33053);
        }

        $userDbController = new UserDbController($this->User->Database);
        $userDbController->setWorkLayerColumnsByDefinitions($layer, $rpcParams['definitions']);

        return $layer;
    }

    /**
     * @api-method updateWorkLayerColumnsDefinitions
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer     layer_id
     *                         #item hexidecimal definitions
     *                         }
     *
     * @throws MTRpcException
     * @throws Exception
     *
     * @return array
     */
    public function updateWorkLayerColumnsDefinitions($rpcParams)
    {
        $UsersController = new UsersController('Users');
        $LayersController = new LayersController('Layers');

        $layer = UserLayers::getLayerById($rpcParams['layer_id']);

        if (!$layer) {
            throw new MTRpcException('MAP_REQUESTED_LAYER_NOT_FOUND', -33053);
        }

        $oldDefinitions = $layer->getDefinitions();
        $newDefinitions = $rpcParams['definitions'];

        // Update only the col_visible and col_title properties of the definitions that are personalizable
        $newDefinitions = array_map(function ($oldDefinition) use ($newDefinitions) {
            if (!$oldDefinition['col_personalizable']) {
                return $oldDefinition;
            }

            $newDefinitionIdx = array_search($oldDefinition['col_name'], array_column($newDefinitions, 'col_name'));
            if (false === $newDefinitionIdx) {
                return $oldDefinition;
            }

            $oldDefinition['col_visible'] = $newDefinitions[$newDefinitionIdx]['col_visible'];
            $oldDefinition['col_title'] = $newDefinitions[$newDefinitionIdx]['col_title'];

            return $oldDefinition;
        }, $oldDefinitions);

        $options = [
            'id' => $rpcParams['layer_id'],
            'mainData' => [
                'definitions' => json_encode($newDefinitions),
            ],
        ];

        $LayersController->editItem($options);
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['new_data' => $options], ['old_data' => json_encode($oldDefinitions)], 'Update Layer definitions');

        return true;
    }

    public function updateWorkLayerAttributes($rpcParams)
    {
        $UsersController = new UsersController('Users');
        $LayersController = new LayersController('Layers');
        if (null === $rpcParams['farming']) {
            $rpcParams['year'] = null;
        }
        $options = [
            'id' => $rpcParams['layerID'],
            'mainData' => [
                'farming' => '' == $rpcParams['farming'] ? null : $rpcParams['farming'],
                'year' => '' == $rpcParams['year'] ? null : $rpcParams['year'],
            ],
        ];

        $LayersController->editItem($options);
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['new_data' => $options], [], 'Save Layer personalization');
    }

    public function getColoringAttributeData(array $rpcParams): array
    {
        if (null === $rpcParams['fill_column_name'] && null === $rpcParams['border_column_name']) {
            throw new Exception('Either the fill_column_name or border_column_name parameter must have a non-null value');
        }

        if (null !== $rpcParams['fill_column_name'] && null !== $rpcParams['border_column_name']) {
            throw new Exception('Only one of the two parameters fill_column_name or border_column_name can be passed to the function.');
        }

        $layer = UserLayers::getLayerById($rpcParams['layer_id']);

        if (!$layer) {
            throw new Exception('Layer not found');
        }

        $UserDbController = new UserDbController($this->User->Database);

        $columnName = $rpcParams['fill_column_name'] ?? $rpcParams['border_column_name'];
        $colorColumn = $rpcParams['fill_column_name'] ? 'fill_color' : 'border_color';
        $options = [
            'tablename' => $layer->table_name,
            'return' => ["distinct on(\"{$columnName}\") \"{$columnName}\" as value", "{$colorColumn} as color"],
            'where' => [
                'id' => ['column' => "\"{$columnName}\" notnull", 'compare' => '=', 'value' => true],
            ],
            'sort' => "\"{$columnName}\"",
            'order' => 'ASC',
        ];

        if (!empty($rpcParams['ekatte'])) {
            $ekatteDef = $layer->getDefinitionByCategory(Config::LAYER_COLUMN_CATEGORY_EKATTE);
            $options['where']['ekatte'] = ['column' => $ekatteDef['col_name'], 'compare' => '=', 'value' => $rpcParams['ekatte']];
        }

        return $UserDbController->getItemsByParams($options);
    }
}
