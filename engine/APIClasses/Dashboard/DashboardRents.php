<?php

namespace TF\Engine\APIClasses\Dashboard;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Dashboard\DashboardController;

/**
 * Dashboard.
 *
 * @rpc-module Dashboard
 *
 * @rpc-service-id dashboard-rents
 */
class DashboardRents extends TRpcApiProvider
{
    private $module = 'Dashboard';
    private $service_id = 'dashboard-rents';

    /**
     * Must return an array of the available methods.
     */
    public function registerMethods()
    {
        return [
            'getNaturaPaymentsByFarm' => ['method' => [$this, 'getNaturaPaymentsByFarm']],
            'getNaturaPayments' => ['method' => [$this, 'getNaturaPayments']],
            'getPaymentsPerDayByNatura' => ['method' => [$this, 'getPaymentsPerDayByNatura']],
            'getNaturaContracts' => ['method' => [$this, 'getNaturaContracts']],
            'read' => ['method' => [$this, 'read']],
        ];
    }

    public function getNaturaPaymentsByFarm($params)
    {
        $DashboardController = new DashboardController($this->User->Database);
        $farmYearId = $params['filters']['farm_year']['value'];
        $farmYearsConfig = $GLOBALS['Farming']['years'];

        return $DashboardController->getNaturaPaymentsByFarm([
            'farm_years' => [$params['filters']['farm_year']['value']],
            'start_date' => $farmYearsConfig[$farmYearId]['end_date'],
            'due_date' => $farmYearsConfig[$farmYearId]['start_date'],
            'farm_id' => $params['filters']['farm']['value'],
            'natura_id' => $params['filters']['natura']['value'],
        ]);
    }

    /*
            $params['start_date'] = $GLOBALS['Farming']['years'][$params['farm_years'][0]]['end_date'];
            $params['due_date'] = $GLOBALS['Farming']['years'][$params['farm_years'][0]]['start_date'];
            $params['natura_id'] = 1;

    */
    public function getNaturaPayments($params)
    {
        $DashboardController = new DashboardController($this->User->Database);
        $farmYearId = $params['filters']['farm_year']['value'];
        $farmYearsConfig = $GLOBALS['Farming']['years'];

        return $DashboardController->getNaturaPayments([
            'farm_years' => [$params['filters']['farm_year']['value']],
            'start_date' => $farmYearsConfig[$farmYearId]['end_date'],
            'due_date' => $farmYearsConfig[$farmYearId]['start_date'],
            'farm_id' => $params['filters']['farm']['value'],
        ]);
    }

    public function getPaymentsPerDayByNatura($params)
    {
        $DashboardController = new DashboardController($this->User->Database);
        $farmYearId = $params['filters']['farm_year']['value'];
        $farmYearsConfig = $GLOBALS['Farming']['years'];

        return $DashboardController->getPaymentsPerDayByNatura([
            'start_date' => $farmYearsConfig[$farmYearId]['end_date'],
            'due_date' => $farmYearsConfig[$farmYearId]['start_date'],
            'farm_years' => [$farmYearId],
            'farm_id' => $params['filters']['farm']['value'],
            'natura_id' => $params['filters']['natura']['value'],
        ]);
    }

    public function getNaturaContracts($params)
    {
        $DashboardController = new DashboardController($this->User->Database);
        $farmYearId = $params['filters']['farm_year']['value'];
        $farmYearsConfig = $GLOBALS['Farming']['years'];

        return $DashboardController->getNaturaContracts([
            'farm_years' => [$farmYearId],
            'start_date' => $farmYearsConfig[$farmYearId]['end_date'],
            'due_date' => $farmYearsConfig[$farmYearId]['start_date'],
            'farm_id' => $params['filters']['farm']['value'],
            'natura_id' => $params['filters']['natura_id']['value'],
        ], $params['page'], $params['page_count']);
    }

    public function read($params)
    {
        return [];
    }
}
