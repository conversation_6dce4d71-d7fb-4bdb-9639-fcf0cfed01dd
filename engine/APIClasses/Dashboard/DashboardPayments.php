<?php

namespace TF\Engine\APIClasses\Dashboard;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Dashboard\DashboardController;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController;

/**
 * Dashboard.
 *
 * @rpc-module Dashboard
 *
 * @rpc-service-id dashboard-payments
 *
 * @property LayersController $LayersController
 * @property UserDbController $UserDbController
 * @property UserDbPlotsController $UserDbPlotsController
 */
class DashboardPayments extends TRpcApiProvider
{
    private $module = 'Dashboard';
    private $service_id = 'dashboard-payments';

    /**
     * Must return an array of the available methods.
     */
    public function registerMethods()
    {
        return [
            'getBgnPayments' => ['method' => [$this, 'getBgnPayments']],
            'getBgnPaymentsByFarm' => ['method' => [$this, 'getBgnPaymentsByFarm']],
            'getBgnPaymentsPerDay' => ['method' => [$this, 'getBgnPaymentsPerDay']],
            'getBgnPaymentsPerEkatte' => ['method' => [$this, 'getBgnPaymentsPerEkatte']],
            'getBgnPaymentsContracts' => ['method' => [$this, 'getBgnPaymentsContracts']],
        ];
    }

    public function getBgnPayments($params)
    {
        $DashboardController = new DashboardController($this->User->Database);
        $farmYearId = $params['filters']['farm_year']['value'];
        $farmYearsConfig = $GLOBALS['Farming']['years'];

        return $DashboardController->getBgnPayments([
            'farm_years' => [$params['filters']['farm_year']['value']],
            'start_date' => $farmYearsConfig[$farmYearId]['end_date'],
            'due_date' => $farmYearsConfig[$farmYearId]['start_date'],
            'farm_id' => $params['filters']['farm']['value'],
            'ekatte' => $params['filters']['land']['value'],
        ]);
    }

    public function getBgnPaymentsByFarm($params)
    {
        $DashboardController = new DashboardController($this->User->Database);
        $farmYearId = $params['filters']['farm_year']['value'];
        $farmYearsConfig = $GLOBALS['Farming']['years'];

        return $DashboardController->getBgnPaymentsByFarm([
            'farm_years' => [$params['filters']['farm_year']['value']],
            'start_date' => $farmYearsConfig[$farmYearId]['end_date'],
            'due_date' => $farmYearsConfig[$farmYearId]['start_date'],
            'ekatte' => $params['filters']['land']['value'],
        ]);
    }

    public function getBgnPaymentsPerDay($params)
    {
        $DashboardController = new DashboardController($this->User->Database);
        $farmYearId = $params['filters']['farm_year']['value'];
        $farmYearsConfig = $GLOBALS['Farming']['years'];

        return $DashboardController->getBgnPaymentsPerDay([
            'start_date' => $farmYearsConfig[$farmYearId]['end_date'],
            'due_date' => $farmYearsConfig[$farmYearId]['start_date'],
            'farm_years' => [$farmYearId],
            'farm_id' => $params['filters']['farm']['value'],
            'ekatte' => $params['filters']['land']['value'],
        ]);
    }

    public function getBgnPaymentsPerEkatte($params)
    {
        $DashboardController = new DashboardController($this->User->Database);
        $farmYearId = $params['filters']['farm_year']['value'];
        $farmYearsConfig = $GLOBALS['Farming']['years'];

        return $DashboardController->getBgnPaymentsPerEkatte([
            'start_date' => $farmYearsConfig[$farmYearId]['end_date'],
            'due_date' => $farmYearsConfig[$farmYearId]['start_date'],
            'farm_years' => [$farmYearId],
            'farm_id' => $params['filters']['farm']['value'],
        ]);
    }

    public function getBgnPaymentsContracts($params)
    {
        $DashboardController = new DashboardController($this->User->Database);
        $farmYearId = $params['filters']['farm_year']['value'];
        $farmYearsConfig = $GLOBALS['Farming']['years'];

        return $DashboardController->getBgnPaymentsContracts([
            'start_date' => $farmYearsConfig[$farmYearId]['end_date'],
            'due_date' => $farmYearsConfig[$farmYearId]['start_date'],
            'farm_years' => [$farmYearId],
            'farm_id' => $params['filters']['farm']['value'],
            'ekatte' => $params['filters']['land']['value'],
        ]);
    }
}
