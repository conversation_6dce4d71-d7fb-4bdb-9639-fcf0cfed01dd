<?php

namespace TF\Engine\APIClasses\Dashboard;

use Prado\Web\Services\TJsonRpcProtocol;
use Prado\Web\Services\TRpcApiProvider;
use Prado\Web\Services\TRpcServer;
use TF\Engine\APIClasses\Common\EkateCombobox;
use TF\Engine\APIClasses\Common\FarmNameCombobox;
use TF\Engine\Plugins\Core\Dashboard\DashboardController;

/**
 * Dashboard.
 *
 * @rpc-module Dashboard
 *
 * @rpc-service-id dashboard-owncontracts
 */
class DashboardOwnContracts extends TRpcApiProvider
{
    /**
     * Must return an array of the available methods.
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getDashboardDataOwnContracts']],
        ];
    }

    public function getDashboardDataOwnContracts($params)
    {
        return $this->runReport(
            $params['type'],
            $params['filters'],
            $params['sort'],
            $params['page'],
            $params['page_count']
        );
    }

    private function runReport($type, $filters, $order, $page, $page_count)
    {
        $server = new TRpcServer(new TJsonRpcProtocol());
        $farmYearId = $filters['farm_year']['value'];
        $farmYearsConfig = $GLOBALS['Farming']['years'];

        switch ($type) {
            case 'farming_bar':
                $chartData = [];
                $filters['farm']['label'] = null;
                $reportFilter = $this->reportFilters($filters);
                $filter = [
                    'payroll_ekate' => $reportFilter['filterEkate']['ekate'] ? $reportFilter['filterEkate']['ekate'] : '',
                    'payroll_farming' => '',
                    'rights' => $reportFilter['filterType'] ? $reportFilter['filterType'] : '',
                    'report_date_from' => $farmYearsConfig[$farmYearId]['start_date'],
                    'report_date_to' => $farmYearsConfig[$farmYearId]['end_date'],
                    'farming_year' => $farmYearId,
                    'ownership' => ' = 1',
                ];
                $filter['col'] = [
                    'farming_id',
                    'sum( contract_area ) as area',
                    'c_type',
                ];
                $filter['group'] = ['farming_id', 'c_type'];
                $report = $this->getContracts($filter);
                $chart = [];
                $chart['xAxis'] = [];
                $label['data'] = [];
                $chart['series'] = [];

                foreach ($reportFilter['farmingList'] as $farming) {
                    array_push($label['data'], $farming);
                }

                array_push($chart['xAxis'], $label);
                $data = [];
                $table = [];
                foreach ($reportFilter['listType'] as $type_id => $c_type) {
                    $data['name'] = $c_type;
                    $data['type'] = 'bar';
                    $value = [];
                    foreach ($reportFilter['farmingList'] as $key => $farming) {
                        $data['stack'] = $key;
                        $area = 0;
                        foreach ($report['data'] as $result) {
                            if ($result['farming_id'] == $key && $result['c_type'] == $type_id) {
                                $area += $result['area'];
                            }
                        }
                        array_push(
                            $value,
                            ['label' => 'farm', 'value' => $area, 'name' => $farming]
                        );
                        if (!array_key_exists($key, $table)) {
                            $table[$key] = ['land' => $farming];
                        }
                        if ('Наета' === $c_type) {
                            $table[$key]['rent'] = $area;
                        }
                        if ('Арендована' === $c_type) {
                            $table[$key]['leased'] = $area;
                        }
                    }
                    $data['data'] = $value;
                    array_push($chart['series'], $data);
                }

                $chartData['chart_data'] = $chart;
                $chartData['table_data'] = array_values($table);

                break;
            case 'ekate_bar':
                $filters['land']['label'] = null;
                $reportFilter = $this->reportFilters($filters);
                $filter = [
                    'payroll_ekate' => '',
                    'payroll_farming' => $reportFilter['filterFarming']['id'] ? $reportFilter['filterFarming']['id'] : '',
                    'rights' => $reportFilter['filterType'] ? $reportFilter['filterType'] : '',
                    'report_date_from' => $farmYearsConfig[$farmYearId]['start_date'],
                    'report_date_to' => $farmYearsConfig[$farmYearId]['end_date'],
                    'farming_year' => $farmYearId,
                    'ownership' => ' = 1',
                ];
                $filter['col'] = [
                    'ekate',
                    'sum( contract_area ) as area',
                    'c_type',
                ];

                $filter['group'] = ['ekate', 'c_type'];
                $report = $this->getContracts($filter);
                $chart = [];
                $chart['xAxis'] = [];
                $label['data'] = [];
                $chart['series'] = [];

                foreach ($reportFilter['ekateList'] as $ekate) {
                    array_push($label['data'], $ekate);
                }

                array_push($chart['xAxis'], $label);
                $data = [];
                $table = [];
                foreach ($reportFilter['listType'] as $type_id => $c_type) {
                    $data['name'] = $c_type;
                    $data['type'] = 'bar';
                    $data['stack'] = 'type';
                    $value = [];
                    foreach ($reportFilter['ekateList'] as $key => $ekate) {
                        $area = 0;
                        foreach ($report['data'] as $result) {
                            if ($result['ekate'] == $key && $result['c_type'] == $type_id) {
                                $area += $result['area'];
                            }
                        }

                        array_push(
                            $value,
                            ['label' => 'land', 'value' => $area, 'name' => $ekate]
                        );
                        if (!array_key_exists($key, $table)) {
                            $table[$key] = ['land' => $ekate];
                        }
                        if ('Наета' === $c_type) {
                            $table[$key]['rent'] = $area;
                        }
                        if ('Арендована' === $c_type) {
                            $table[$key]['leased'] = $area;
                        }
                    }
                    $data['data'] = $value;
                    array_push($chart['series'], $data);
                }

                $chartData['chart_data'] = $chart;
                $chartData['table_data'] = array_values($table);

                break;
            case 'contract_pay':
                $chartData = [];
                $reportFilter = $this->reportFilters($filters);
                $filter = [
                    'payroll_ekate' => $reportFilter['filterEkate']['ekate'] ? $reportFilter['filterEkate']['ekate'] : '',
                    'payroll_farming' => $reportFilter['filterFarming']['id'] ? $reportFilter['filterFarming']['id'] : '',
                    'rights' => $reportFilter['filterType'] ? $reportFilter['filterType'] : '',
                    'report_date_from' => $farmYearsConfig[$farmYearId]['start_date'],
                    'report_date_to' => $farmYearsConfig[$farmYearId]['end_date'],
                    'farming_year' => $farmYearId,
                    'ownership' => ' = 1',
                ];

                $type = ['3' => 'Наета', '2' => 'Арендована'];
                $chart = [];
                $chart['series'] = [];
                $data['name'] = 'Пренаета/Преарендована';
                $data['type'] = 'pie';
                $data['data'] = [];

                $filter['col'] = ['sum(contract_area) as area', 's_rights'];
                $filter['group'] = ['s_rights'];
                $report = $this->getContracts($filter);
                $value = [];
                foreach ($reportFilter['listType'] as $key => $name) {
                    $value['label'] = 'rent_type';
                    $value['name'] = $name;

                    $tmpData = array_filter($report['data'], function ($element) use ($key) {
                        return $element['s_rights'] === $key;
                    });
                    $tmpData = reset($tmpData);
                    array_push($data['data'], ['label' => 'rent_type', 'name' => $name, 'value' => $tmpData['area']]);
                }
                array_push($chart['series'], $data);
                $chartData['chart_data'] = $chart;

                break;
            default:
                $chartData = [];
                $reportFilter = $this->reportFilters($filters);
                $filter = [
                    'payroll_ekate' => $reportFilter['filterEkate']['ekate'] ? $reportFilter['filterEkate']['ekate'] : '',
                    'payroll_farming' => $reportFilter['filterFarming']['id'] ? $reportFilter['filterFarming']['id'] : '',
                    'rights' => $reportFilter['filterType'] ? $reportFilter['filterType'] : '',
                    'report_date_from' => $farmYearsConfig[$farmYearId]['start_date'],
                    'report_date_to' => $farmYearsConfig[$farmYearId]['end_date'],
                    'farming_year' => $farmYearId,
                    'ownership' => ' = 1',
                ];
                $filter['col'] = [
                    'c_id',
                    'c_num',
                    'array_agg(farming_id) as farming',
                    'max(start_date) as start_date',
                    'max(due_date) as due_date',
                    'sum( contract_area ) as area',
                    'array_agg(kad_ident) as plots',
                    'array_agg(distinct(name)) as owner_names',
                    'array_agg(distinct(farming)) as farming',
                ];
                $filter['group'] = ['c_id', 'c_num', 'name'];
                $filter['page'] = ($page * $page_count) - $page_count + 1;
                $filter['page_count'] = $page_count;
                $filter['order'] = $order;
                $report = $this->getContracts($filter);
                $data = [];
                foreach ($report['data'] as $info) {
                    $grid['c_id'] = $info['c_id'];
                    $grid['c_num'] = $info['c_num'];
                    $grid['start_date'] = $info['start_date'];
                    $grid['due_date'] = $info['due_date'];
                    $grid['plots'] = str_replace(['{', '}', '"'], '', $info['plots']);
                    $grid['owner_names'] = str_replace(['{', '}', '"'], '', $info['owner_names']);
                    $grid['area'] = $info['area'];
                    array_push($data, $grid);
                }

                $chartData['contracts'] = $data;
                $chartData['count'] = $report['count'];
        }

        return $chartData;
    }

    private function reportFilters($filters)
    {
        $server = new TRpcServer(new TJsonRpcProtocol());
        // Farming
        $commonFarming = new FarmNameCombobox($server);
        $farmingListCombo = $commonFarming->getFarmNameCombobox([]);

        $farmingList = [];
        $filterFarming = [];

        foreach ($farmingListCombo as $f) {
            $farmingList[$f['id']] = $f['title'];
        }

        if ($filters['farm']['label'] || '' === !$filters['farm']['label']) {
            $filterFarming = array_filter($farmingListCombo, function ($element) use ($filters) {
                return $element['title'] === $filters['farm']['label'];
            });
            $filterFarming = reset($filterFarming);
            $farmingList = [$filterFarming['id'] => $filterFarming['title']];
        }

        // EKATE
        $ekateList = [];
        $filterEkate = [];

        $commonEkate = new EkateCombobox($server);
        $ekateListCombo = $commonEkate->getEKATTECombox([]);
        foreach ($ekateListCombo as $e) {
            $ekateList[$e['ekate']] = $e['text'];
        }
        // FARMINGS
        if ($filters['land']['label'] || '' === !$filters['land']['label']) {
            $filterEkate = array_filter($ekateListCombo, function ($element) use ($filters) {
                return $element['text'] === $filters['land']['label'];
            });
            $filterEkate = reset($filterEkate);
            $ekateList = [$filterEkate['ekate'] => $filterEkate['text']];
        }
        // Contract type
        $filterType = [];
        $listType = ['3' => 'Наета', '2' => 'Арендована'];
        if ($filters['rent_type'] || '' === !$filters['rent_type']) {
            foreach ($listType as $key => $row) {
                if ($row == $filters['rent_type']) {
                    $listType = [$key => $row];
                    $filterType = $key;
                }
            }
        }

        return [
            'farmingList' => $farmingList,
            'filterFarming' => $filterFarming,
            'ekateList' => $ekateList,
            'filterEkate' => $filterEkate,
            'listType' => $listType,
            'filterType' => $filterType,
        ];
    }

    private function getContracts($filter)
    {
        $this->DashboardController = new DashboardController($this->User->Database);

        return $this->DashboardController->getSubleasedContracts($filter);
    }
}
