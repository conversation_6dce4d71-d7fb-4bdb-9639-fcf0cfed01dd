<?php

namespace TF\Engine\APIClasses\Dashboard;

use Prado\Web\Services\TJsonRpcProtocol;
use Prado\Web\Services\TRpcApiProvider;
use Prado\Web\Services\TRpcServer;
use TF\Engine\APIClasses\Common\EkateCombobox;
use TF\Engine\APIClasses\Common\FarmNameCombobox;
use TF\Engine\Plugins\Core\Dashboard\DashboardController;

/**
 * Dashboard.
 *
 * @rpc-module Dashboard
 *
 * @rpc-service-id dashboard-contracts
 */
class DashboardContracts extends TRpcApiProvider
{
    /**
     * Must return an array of the available methods.
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getDashboardDataContracts']],
        ];
    }

    public function getDashboardDataContracts($params)
    {
        return $this->runReport(
            $params['type'],
            $params['filters'],
            $params['sort'],
            $params['page'],
            $params['page_count']
        );
    }

    private function runReport($type, $filters, $order, $page, $page_count)
    {
        $server = new TRpcServer(new TJsonRpcProtocol());

        $farmYearId = $filters['farm_year']['value'];
        $farmYearsConfig = $GLOBALS['Farming']['years'];

        switch ($type) {
            case 'farming_bar':
                $chartData = [];
                $filters['farm']['label'] = null;
                $reportFilter = $this->reportFilters($filters);
                $filter = [
                    'payroll_ekate' => $reportFilter['filterEkate']['ekate'] ? $reportFilter['filterEkate']['ekate'] : '',
                    'payroll_farming' => '',
                    'rights' => $reportFilter['filterType'] ? $reportFilter['filterType'] : '',
                    'payroll_from_date' => $farmYearsConfig[$farmYearId]['end_date'],
                    'payroll_to_date' => $farmYearsConfig[$farmYearId]['start_date'],
                    'farming_year' => $farmYearId,
                ];
                $filter['col'] = ['c_type', 'farming_id', 'sum(area) as area'];
                $filter['group_by'] = ['farming_id', 'c_type'];

                $report = $this->getRentContracts($filter);

                $chart = [];
                $chart['xAxis'] = [];
                $label['data'] = [];
                $chart['series'] = [];
                foreach ($reportFilter['farmingList'] as $farming) {
                    array_push($label['data'], $farming);
                }
                array_push($chart['xAxis'], $label);
                $series = [];
                $table = [];
                foreach ($reportFilter['listType'] as $type_id => $c_type) {
                    $series['name'] = $c_type;
                    $series['stack'] = 'payment';
                    $series['type'] = 'bar';
                    $series['data'] = [];
                    $value = [];
                    foreach ($reportFilter['farmingList'] as $farming_id => $farming) {
                        $area = 0;
                        foreach ($report['data'] as $result) {
                            if ($result['farming_id'] == $farming_id && $result['c_type'] == $type_id) {
                                $area += $result['area'];
                            }
                        }
                        array_push(
                            $value,
                            ['label' => 'farm', 'value' => $area, 'name' => $farming]
                        );
                        if (!array_key_exists($farming_id, $table)) {
                            $table[$farming_id] = ['land' => $farming];
                        }
                        if ('Наета' === $c_type) {
                            $table[$farming_id]['rent'] = $area;
                        }
                        if ('Арендована' === $c_type) {
                            $table[$farming_id]['leased'] = $area;
                        }
                    }
                    $series['data'] = $value;
                    array_push($chart['series'], $series);
                }
                $table = array_values($table);
                $chartData['chart_data'] = $chart;
                $chartData['table_data'] = $table;

                break;
            case 'ekate_bar':
                $chartData = [];
                $filters['land']['label'] = null;
                $reportFilter = $this->reportFilters($filters);
                $filter = [
                    'payroll_ekate' => '',
                    'payroll_farming' => $reportFilter['filterFarming']['id'] ? $reportFilter['filterFarming']['id'] : '',
                    'rights' => $reportFilter['filterType'] ? $reportFilter['filterType'] : '',
                    'payroll_from_date' => $farmYearsConfig[$farmYearId]['end_date'],
                    'payroll_to_date' => $farmYearsConfig[$farmYearId]['start_date'],
                    'farming_year' => $farmYearId,
                ];
                $filter['col'] = ['c_type', 'ekate', 'sum(area) as area'];
                $filter['group_by'] = ['ekate', 'c_type'];

                $report = $this->getRentContracts($filter);

                $chart = [];
                $chart['xAxis'] = [];
                $label['data'] = [];
                $chart['series'] = [];
                foreach ($reportFilter['ekateList'] as $ekate) {
                    array_push($label['data'], $ekate);
                }
                array_push($chart['xAxis'], $label);
                $series = [];
                $table = [];
                foreach ($reportFilter['listType'] as $type_id => $c_type) {
                    $series['name'] = $c_type;
                    $series['type'] = 'bar';
                    $series['stack'] = 'payment';
                    $series['data'] = [];
                    $value = [];
                    foreach ($reportFilter['ekateList'] as $key => $ekate) {
                        $area = 0;

                        foreach ($report['data'] as $result) {
                            if ($result['ekate'] == $key && $result['c_type'] == $type_id) {
                                $area += $result['area'];
                            }
                        }
                        array_push(
                            $value,
                            ['label' => 'land', 'value' => $area, 'name' => $ekate]
                        );
                        if (!array_key_exists($key, $table)) {
                            $table[$key] = ['land' => $ekate];
                        }
                        if ('Наета' === $c_type) {
                            $table[$key]['rent'] = $area;
                        }
                        if ('Арендована' === $c_type) {
                            $table[$key]['leased'] = $area;
                        }
                    }
                    $series['data'] = $value;
                    array_push($chart['series'], $series);
                }
                // TODO: Completely refactore this function.
                $table = array_values($table);
                $chartData['chart_data'] = $chart;
                $chartData['table_data'] = $table;

                break;
            case 'contract_bar':
                $chartData = [];
                $reportFilter = $this->reportFilters($filters);
                $filter = [
                    'payroll_ekate' => $reportFilter['filterEkate']['ekate'] ? $reportFilter['filterEkate']['ekate'] : '',
                    'payroll_farming' => $reportFilter['filterFarming']['id'] ? $reportFilter['filterFarming']['id'] : '',
                    'rights' => $reportFilter['filterType'] ? $reportFilter['filterType'] : '',
                    'payroll_from_date' => $farmYearsConfig[$farmYearId]['end_date'],
                    'payroll_to_date' => $farmYearsConfig[$farmYearId]['start_date'],
                    'farming_year' => $farmYearId,
                ];

                $filter['col'] = ['ekate', 'farming_id', 'sum(area) as area'];
                $filter['group_by'] = ['ekate', 'farming_id'];

                $report = $this->getRentContracts($filter);
                $chart = [];
                $chart['xAxis'] = [];
                $label['data'] = [];
                $chart['series'] = [];

                foreach ($reportFilter['farmingList'] as $farming) {
                    array_push($label['data'], $farming);
                }
                array_push($chart['xAxis'], $label);

                $data = [];
                foreach ($reportFilter['ekateList'] as $ekate) {
                    if (!empty($reportFilter['filterEkate']) && $ekate['ekate'] !== $reportFilter['filterEkate']) {
                        continue;
                    }
                    $data['name'] = $ekate['text'];
                    $data['type'] = 'bar';
                    $value = [];
                    foreach ($reportFilter['farmingList'] as $key => $farming) {
                        $data['stack'] = $key;
                        $tmpData = array_filter($report['data'], function ($element) use ($ekate, $key) {
                            return $element['ekate'] === $ekate['ekate'] && $element['farming_id'] === $key;
                        });

                        $area = 0;
                        if (!empty($tmpData)) {
                            $tmpData = reset($tmpData);
                            $area = $tmpData['area'];
                        }

                        array_push(
                            $value,
                            ['label' => 'farming', 'value' => $area, 'name' => $farming]
                        );
                    }

                    $data['data'] = $value;
                    array_push($chart['series'], $data);
                }
                $chartData['chart_data'] = $chart;

                break;
            case 'contract_pay':
                $chartData = [];
                $reportFilter = $this->reportFilters($filters);
                $filter = [
                    'payroll_ekate' => $reportFilter['filterEkate']['ekate'] ? $reportFilter['filterEkate']['ekate'] : '',
                    'payroll_farming' => $reportFilter['filterFarming']['id'] ? $reportFilter['filterFarming']['id'] : '',
                    'rights' => $reportFilter['filterType'] ? $reportFilter['filterType'] : '',
                    'payroll_from_date' => $farmYearsConfig[$farmYearId]['end_date'],
                    'payroll_to_date' => $farmYearsConfig[$farmYearId]['start_date'],
                    'farming_year' => $farmYearId,
                ];

                $type = ['3' => 'Наета', '2' => 'Арендована'];
                $chart = [];
                $chart['series'] = [];
                $data['name'] = 'Наета/Арендована';
                $data['type'] = 'pie';
                $data['data'] = [];

                $filter['col'] = ['sum(area) as area', 'c_type'];
                $filter['group_by'] = ['c_type'];
                $report = $this->getRentContracts($filter);

                $value = [];
                foreach ($reportFilter['listType'] as $key => $name) {
                    $value['label'] = 'rent_type';
                    $value['name'] = $name;

                    $tmpData = array_filter($report['data'], function ($element) use ($key) {
                        return $element['c_type'] === $key;
                    });

                    $tmpData = reset($tmpData);

                    array_push($data['data'], ['label' => 'rent_type', 'name' => $name, 'value' => $tmpData['area']]);
                }
                array_push($chart['series'], $data);
                $chartData['chart_data'] = $chart;

                break;
            default:
                $chartData = [];
                $reportFilter = $this->reportFilters($filters);
                $filter = [
                    'payroll_ekate' => $reportFilter['filterEkate']['ekate'] ? $reportFilter['filterEkate']['ekate'] : '',
                    'payroll_farming' => $reportFilter['filterFarming']['id'] ? $reportFilter['filterFarming']['id'] : '',
                    'rights' => $reportFilter['filterType'] ? $reportFilter['filterType'] : '',
                    'payroll_from_date' => $farmYearsConfig[$farmYearId]['end_date'],
                    'payroll_to_date' => $farmYearsConfig[$farmYearId]['start_date'],
                    'farming_year' => $farmYearId,
                ];
                $filter['col'] = [
                    'c_id',
                    'c_num',
                    'max(subl_start_date) as start_date',
                    'max(subl_due_date)as due_date',
                    'array_agg(distinct owner_names) as names',
                    'sum(area) as area',
                ];
                $filter['group_by'] = ['c_id', 'c_num'];
                $filter['page'] = ($page * $page_count) - $page_count + 1;
                $filter['page_count'] = $page_count;
                $filter['order'] = $order;
                $params['grid'] = true;
                $report = $this->getRentContracts($filter);

                $data = [];
                foreach ($report['data'] as $info) {
                    $grid['c_id'] = $info['c_id'];
                    $grid['c_num'] = $info['c_num'];
                    $grid['start_date'] = $info['start_date'];
                    $grid['due_date'] = $info['due_date'];
                    $grid['owner_names'] = str_replace(['{', '}', '"', 'NULL', ''], '', $info['names']);

                    $grid['area'] = $info['area'];
                    array_push($data, $grid);
                }
                $chartData['contracts'] = $data;
                $chartData['count'] = $report['count'];
        }

        return $chartData;
    }

    private function reportFilters($filters)
    {
        $server = new TRpcServer(new TJsonRpcProtocol());
        // Farming
        $commonFarming = new FarmNameCombobox($server);
        $farmingListCombo = $commonFarming->getFarmNameCombobox([]);

        $farmingList = [];
        $filterFarming = [];

        foreach ($farmingListCombo as $f) {
            $farmingList[$f['id']] = $f['title'];
        }

        if ($filters['farm']['label'] || '' === !$filters['farm']['label']) {
            $filterFarming = array_filter($farmingListCombo, function ($element) use ($filters) {
                return $element['title'] === $filters['farm']['label'];
            });
            $filterFarming = reset($filterFarming);
            $farmingList = [$filterFarming['id'] => $filterFarming['title']];
        }

        // EKATE
        $ekateList = [];
        $filterEkate = [];

        $commonEkate = new EkateCombobox($server);
        $ekateListCombo = $commonEkate->getEKATTECombox([]);
        foreach ($ekateListCombo as $e) {
            $ekateList[$e['ekate']] = $e['text'];
        }

        if ($filters['land']['label'] || '' === !$filters['land']['label']) {
            $filterEkate = array_filter($ekateListCombo, function ($element) use ($filters) {
                return $element['text'] === $filters['land']['label'];
            });
            $filterEkate = reset($filterEkate);
            $ekateList = [$filterEkate['ekate'] => $filterEkate['text']];
        }
        // Contract type
        $filterType = [];
        $listType = ['3' => 'Наета', '2' => 'Арендована'];
        if ($filters['rent_type'] || '' === !$filters['rent_type']) {
            foreach ($listType as $key => $row) {
                if ($row == $filters['rent_type']) {
                    $listType = [$key => $row];
                    $filterType = $key;
                }
            }
        }

        return [
            'farmingList' => $farmingList,
            'filterFarming' => $filterFarming,
            'ekateList' => $ekateList,
            'filterEkate' => $filterEkate,
            'listType' => $listType,
            'filterType' => $filterType,
        ];
    }

    private function getRentContracts($filter)
    {
        $this->DashboardController = new DashboardController($this->User->Database);

        return $this->DashboardController->getRentContracts($filter);
    }
}
