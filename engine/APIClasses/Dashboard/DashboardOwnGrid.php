<?php

namespace TF\Engine\APIClasses\Dashboard;

use Prado\Web\Services\TJsonRpcProtocol;
use Prado\Web\Services\TRpcApiProvider;
use Prado\Web\Services\TRpcServer;
use TF\Engine\APIClasses\Common\EkateCombobox;
use TF\Engine\APIClasses\Common\FarmNameCombobox;
use TF\Engine\Plugins\Core\Dashboard\DashboardController;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController;

/**
 * Dashboard.
 *
 * @rpc-module Dashboard
 *
 * @rpc-service-id dashboard-owngrid
 *
 * @property LayersController $LayersController
 * @property UserDbController $UserDbController
 * @property UserDbPlotsController $UserDbPlotsController
 */
class DashboardOwnGrid extends TRpcApiProvider
{
    /**
     * Must return an array of the available methods.
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getDashboardDataOwnGrid']],
        ];
    }

    public function getDashboardDataOwnGrid($params)
    {
        return $this->runReport($params['type'], $params['filters']);
    }

    private function runReport($type, $filters)
    {
        $server = new TRpcServer(new TJsonRpcProtocol());

        $farmYearId = $filters['farm_year']['value'];
        $farmYearsConfig = $GLOBALS['Farming']['years'];

        $commonFarming = new FarmNameCombobox($server);
        $farmingList = $commonFarming->getFarmNameCombobox([]);
        foreach ($farmingList as $f) {
            $farmingList[$f['id']] = $f['title'];
        }
        $filterFarming = array_filter($farmingList, function ($element) use ($filters) {
            return $element === $filters['farming'];
        });

        $commonEkate = new EkateCombobox($server);
        $ekateList = $commonEkate->getEKATTECombox([]);
        $filterEkate = array_filter($ekateList, function ($element) use ($filters) {
            return $element['text'] === $filters['land'];
        });
        $filterEkate = reset($filterEkate);

        $reportFilter = $this->reportFilters($filters);
        $settings = [
            'payroll_ekate' => $reportFilter['filterEkate']['ekate'] ? $reportFilter['filterEkate']['ekate'] : '',
            'payroll_farming' => $reportFilter['filterFarming']['id'] ? $reportFilter['filterFarming']['id'] : '',
            'payroll_from_date' => $farmYearsConfig[$farmYearId]['start_date'],
            'payroll_to_date' => $farmYearsConfig[$farmYearId]['end_date'],
            'type' => 'owners',
            'group' => $type,
        ];

        switch ($type) {
            case 'farming_bar':
                $chartData = [];
                $filters['farm']['label'] = null;
                $reportFilter = $this->reportFilters($filters);
                $settings = [
                    'payroll_ekate' => $reportFilter['filterEkate']['ekate'] ? $reportFilter['filterEkate']['ekate'] : '',
                    'payroll_farming' => '',
                    'payroll_from_date' => $farmYearsConfig[$farmYearId]['start_date'],
                    'payroll_to_date' => $farmYearsConfig[$farmYearId]['end_date'],
                    'type' => 'owners',
                    'group' => $type,
                ];

                $settings['col'] = ['DISTINCT(own_grid.farming_id) as farming_id'];
                $farmList = $this->getOwnReportPlotGrid($settings);
                $settings['col'] = ['own_grid.ekate', 'own_grid.farming_id as farming', 'own_grid.area'];
                $settings['query_type'] = 'bar_chart';
                $report = $this->getOwnReportPlotGrid($settings);
                $chart = [];
                $chart['xAxis'] = [];
                $label['data'] = [];
                $chart['series'] = [];
                foreach ($reportFilter['farmingList'] as $farming) {
                    array_push($label['data'], $farming);
                }
                array_push($chart['xAxis'], $label);

                $data = [];
                $data['type'] = 'bar';
                $value = [];
                $table = [];
                foreach ($reportFilter['farmingList'] as $key => $farming) {
                    $data['stack'] = $key;
                    $area = 0;
                    foreach ($report as $result) {
                        if ($result['farming'] == $key) {
                            $area += $result['suma'];
                        }
                    }

                    array_push(
                        $value,
                        ['label' => 'farm', 'value' => $area, 'name' => $farming]
                    );
                    if (!array_key_exists($key, $table)) {
                        $table[$key] = ['land' => $farming];
                    }
                    if ('Наета' === $c_type) {
                        $table[$key]['rent'] = $area;
                    }
                    if ('Арендована' === $c_type) {
                        $table[$key]['leased'] = $area;
                    }
                }

                $data['data'] = $value;
                $data['name'] = '';
                array_push($chart['series'], $data);

                $chartData['chart_data'] = $chart;
                $chartData['table_data'] = array_values($table);

                break;
            case 'ekate_bar':
                $chartData = [];
                $filters['land']['label'] = null;
                $reportFilter = $this->reportFilters($filters);
                $settings = [
                    'payroll_ekate' => '',
                    'payroll_farming' => $reportFilter['filterFarming']['id'] ? $reportFilter['filterFarming']['id'] : '',
                    'payroll_from_date' => $farmYearsConfig[$farmYearId]['start_date'],
                    'payroll_to_date' => $farmYearsConfig[$farmYearId]['end_date'],
                    'type' => 'owners',
                    'group' => $type,
                ];

                $settings['col'] = ['own_grid.ekate', 'own_grid.farming_id as farming', 'own_grid.area'];
                $settings['query_type'] = 'bar_chart';
                $report = $this->getOwnReportPlotGrid($settings);
                $chart = [];
                $chart['xAxis'] = [];
                $label['data'] = [];
                $chart['series'] = [];

                foreach ($reportFilter['ekateList'] as $ekate) {
                    array_push($label['data'], $ekate);
                }
                array_push($chart['xAxis'], $label);
                $data = [];
                $data['type'] = 'bar';
                $value = [];
                $table = [];

                foreach ($reportFilter['ekateList'] as $key => $ekate) {
                    $data['stack'] = $ekate;
                    $area = 0;
                    foreach ($report as $result) {
                        if ($result['ekate'] == $key) {
                            $area += $result['suma'];
                        }
                    }

                    array_push(
                        $value,
                        ['label' => 'land', 'value' => $area, 'name' => $ekate]
                    );
                    if (!array_key_exists($key, $table)) {
                        $table[$key] = ['land' => $ekate];
                    }
                    if ('Наета' === $c_type) {
                        $table[$key]['rent'] = $area;
                    }
                    if ('Арендована' === $c_type) {
                        $table[$key]['leased'] = $area;
                    }
                }

                $data['data'] = $value;
                $data['name'] = '';
                array_push($chart['series'], $data);

                $chartData['chart_data'] = $chart;
                $chartData['table_data'] = array_values($table);

                break;
            case 'own_line':
                $chartData = [];
                $reportFilter = $this->reportFilters($filters);
                $settings = [
                    'payroll_ekate' => $reportFilter['filterEkate']['ekate'] ? $reportFilter['filterEkate']['ekate'] : '',
                    'payroll_farming' => $reportFilter['filterFarming']['id'] ? $reportFilter['filterFarming']['id'] : '',
                    'payroll_from_date' => $farmYearsConfig[$farmYearId]['start_date'],
                    'payroll_to_date' => $farmYearsConfig[$farmYearId]['end_date'],
                    'type' => 'owners',
                    'group' => $type,
                ];

                $settings['col'] = ['own_grid.start_date'];
                $settings['group_by'] = ['own_grid.start_date'];
                $settings['order_by'] = ['start_date ASC'];
                $date_label = $this->getOwnReportPlotGrid($settings);
                $settings['query_type'] = 'line_chart';
                $settings['col'] = ['own_grid.start_date', 'sum(own_grid.area) as area'];
                $settings['group_by'] = ['own_grid.start_date'];
                $report = $this->getOwnReportPlotGrid($settings);

                $chart = [];
                $chart['xAxis'] = [];
                $label['data'] = [];
                foreach ($date_label as $date) {
                    array_push($label['data'], $date['start_date']);
                }
                $chart['xAxis'][] = $label;
                $chart['series'] = [];
                $series['data'] = [];
                $series['type'] = 'line';
                $series['name'] = '';
                foreach ($report as $info) {
                    array_push($series['data'], $info['sum']);
                }
                array_push($chart['series'], $series);
                $chartData['chart_data'] = $chart;

                break;
            default:
                break;
        }

        return $chartData;
    }

    private function reportFilters($filters)
    {
        $server = new TRpcServer(new TJsonRpcProtocol());
        // Farming
        $commonFarming = new FarmNameCombobox($server);
        $farmingListCombo = $commonFarming->getFarmNameCombobox([]);

        $farmingList = [];
        $filterFarming = [];

        foreach ($farmingListCombo as $f) {
            $farmingList[$f['id']] = $f['title'];
        }

        if ($filters['farm']['label'] || '' === !$filters['farm']['label']) {
            $filterFarming = array_filter($farmingListCombo, function ($element) use ($filters) {
                return $element['title'] === $filters['farm']['label'];
            });
            $filterFarming = reset($filterFarming);
            $farmingList = [$filterFarming['id'] => $filterFarming['title']];
        }

        // EKATE
        $ekateList = [];
        $filterEkate = [];

        $commonEkate = new EkateCombobox($server);
        $ekateListCombo = $commonEkate->getEKATTECombox([]);
        foreach ($ekateListCombo as $e) {
            $ekateList[$e['ekate']] = $e['text'];
        }

        if ($filters['land']['label'] || '' === !$filters['land']['label']) {
            $filterEkate = array_filter($ekateListCombo, function ($element) use ($filters) {
                return $element['text'] === $filters['land']['label'];
            });
            $filterEkate = reset($filterEkate);
            $ekateList = [$filterEkate['ekate'] => $filterEkate['text']];
        }

        return [
            'farmingList' => $farmingList,
            'filterFarming' => $filterFarming,
            'ekateList' => $ekateList,
            'filterEkate' => $filterEkate,
        ];
    }

    private function getOwnReportPlotGrid($filter)
    {
        $this->DashboardController = new DashboardController($this->User->Database);

        return $this->DashboardController->getOwnGrid($filter);
    }
}
