<?php

namespace TF\Engine\APIClasses\KVSContractsUpdate;

use Prado\Prado;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

/**
 * Adds created at and updated at timestamps to entities.
 */
trait KVSContractsUpdate
{
    public function tryResolveContracts(int $fileId, string $ekatte, array $plotsData): void
    {
        $UserDbController = new UserDbController(Prado::getApplication()->getUser()->Database);
        $kvsContractsUpdateView = "kvs_contracts_update_{$ekatte}";

        // @see KVSContractsPlotsForUpdateGrid
        if ($UserDbController->getViewNameExists($kvsContractsUpdateView)) {
            $kvsContractsUpdate = makeApiClass('kvs-contracts-update-rpc', 'kvs-contracts-plots-for-update-grid');
            $kvsContractsUpdate->updateKVSPlots([
                'file_id' => $fileId,
                'plots_data' => $plotsData,
            ]);
        }
    }
}
