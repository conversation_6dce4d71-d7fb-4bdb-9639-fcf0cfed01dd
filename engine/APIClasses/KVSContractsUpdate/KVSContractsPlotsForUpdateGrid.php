<?php

namespace TF\Engine\APIClasses\KVSContractsUpdate;

use DateTime;
use Exception;
use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\LoggerMessages;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * @rpc-module KVSContractsUpdate
 *
 * @rpc-service-id kvs-contracts-plots-for-update-grid
 *
 * @property UserDbController $UserDbController
 * @property LayersController $LayersController
 * @property UsersController $UsersController
 * @property FarmingController $FarmingController
 */
class KVSContractsPlotsForUpdateGrid extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'read']],
            'update' => ['method' => [$this, 'updateKVSPlots']],
        ];
    }

    public function read($rpcParams, $page = '', $pageSize = '', $sort = '', $order = '')
    {
        $UserDbController = new UserDbController($this->User->Database);
        $processedData = $gids = $invalidPlots = [];
        $ekate = $this->getEkatte($rpcParams);

        $kvsContractsUpdateView = 'kvs_contracts_update_' . $ekate;

        if (!$UserDbController->getViewNameExists($kvsContractsUpdateView)) {
            return [
                'total' => 0,
                'rows' => [],
            ];
        }

        if (array_key_exists('invalid_plots', $rpcParams)) {
            $invalidPlots = $rpcParams['invalid_plots'];
        }

        $options = [
            'tablename' => $kvsContractsUpdateView,
        ];

        $missingPlots = $UserDbController->getMissingKVSPlots($options, false, false);

        foreach ($missingPlots as $key => $plot) {
            $processedPlot = $this->processGeoms($plot);
            $processedPlot['kad_ident'] = $plot['kad_ident'];
            $processedPlot['area'] = $plot['area'];

            $invalidOnKvsUpdate = false;
            if (count($invalidPlots) && isset($invalidPlots[$plot['kad_ident']])) {
                $invalidOnKvsUpdate = in_array($plot['contract_id'], $invalidPlots[$plot['kad_ident']]);
            }

            if (in_array($plot['gid'], $gids)) {
                $controlKey = array_search($plot['gid'], $gids);
                $processedData[$controlKey]['contracts_data'][] = $this->processContract($plot, $invalidOnKvsUpdate);

                continue;
            }

            $processedData[$key] = $processedPlot;
            $processedData[$key]['contracts_data'][] = $this->processContract($plot, $invalidOnKvsUpdate);
            $gids[$key] = $plot['gid'];
        }

        $this->sortInvalidOnUpdateFirst($processedData);

        return [
            'total' => count($processedData),
            'rows' => array_values($processedData),
        ];
    }

    /**
     * This method updates existing KVS plot or plots with new ones. The update can be performed using
     *  keep new boundaries action. The method is exposed as an API endpoint with the
     * 'update' method.
     *
     * @api-method update
     *
     * @param array $rpcParams An associative array containing the following keys:
     *                         "file_id": (int) The ID of the file that contains the plot data.
     *                         "plots_data": (array) An array of associative arrays, each containing the following keys:
     *                         {
     *                         "kad_ident": (string) The identifier of the plot in the cadastre.
     *                         "contract_ids": (array) An array of integers representing the IDs of the contracts associated with the plot.
     *                         "edit_active_from": (date) The date from which the edit becomes active.
     *                         }
     *
     * @throws Exception if an error occurs during the update process, an exception is thrown
     */
    public function updateKVSPlots($rpcParams): array
    {
        $UserDbController = new UserDbController($this->User->Database);
        $fileId = $rpcParams['file_id'];
        $plotData = $rpcParams['plots_data'];
        $ekatte = $this->getEkatte(['file_id' => $fileId]);
        $kvsContractsUpdateView = 'kvs_contracts_update_' . $ekatte;
        $invalidPlots = [];

        foreach ($plotData as $plot) {
            $editActiveFrom = (DateTime::createFromFormat('Y-m-d', $plot['edit_active_from']))->modify('+1 day');
            $kadIdent = $plot['kad_ident'];

            if (!isset($kadIdent) || 0 == strlen($kadIdent)) {
                throw new Exception("Invalid plod kad ident. Review kvs ekatte ({$ekatte}) data. Ident: {$kadIdent}");
            }

            $contracts = $this->getPlotGeomInvalidContracts($editActiveFrom, $kadIdent, $kvsContractsUpdateView);

            if (count($contracts)) {
                $invalidPlots[$kadIdent] = $contracts;

                continue;
            }

            $this->KVSKeepNew($editActiveFrom, $kadIdent, $ekatte);
        }

        // REFRESH VIEW
        // if sumbited plots count is equal to invalid plots count (it's possible new contracts to be created after view generation and we want to regenerate the view to include them in the response)
        $UserDbController->refreshView($kvsContractsUpdateView);
        $missingPlots = $UserDbController->getMissingKVSPlots([
            'tablename' => $kvsContractsUpdateView,
        ], false, false);

        // Ако няма невалидни геометрии и няма не актуализирани договори - Приклячваме актуализацията автоматично
        $this->checkToEndActualization($fileId, $ekatte, $missingPlots);

        return $this->read(['file_id' => $fileId, 'invalid_plots' => $invalidPlots]);
    }

    /**
     * Check if the submitted editActiveFrom is after the due date of all contracts with changed geometry.
     */
    public function getPlotGeomInvalidContracts(DateTime $editActiveFrom, string $kadIdent, string $kvsContractsUpdateView): array
    {
        $UserDbController = new UserDbController($this->User->Database);
        $plots = $UserDbController->getMissingKVSPlots([
            'tablename' => $kvsContractsUpdateView,
            'where' => [
                'kad_ident' => $kadIdent,
                'due_date' => $editActiveFrom->format('Y-m-d H:i:s'),
            ],
        ], false, false);

        return array_column($plots, 'contract_id');
    }

    private function sortInvalidOnUpdateFirst(&$processedData): bool
    {
        return uasort($processedData, function ($a, $b) {
            $aInvalid = array_reduce($a['contracts_data'], function ($carry, $item) {
                return $carry || ($item['invalid_on_kvs_update'] ?? false);
            }, false);

            $bInvalid = array_reduce($b['contracts_data'], function ($carry, $item) {
                return $carry || ($item['invalid_on_kvs_update'] ?? false);
            }, false);

            if ($aInvalid && !$bInvalid) {
                return -1; // $a comes first
            } elseif (!$aInvalid && $bInvalid) {
                return 1; // $b comes first
            }

            return 0; // they are equal
        });
    }

    private function processContract(array $plot, bool $invalidOnKvsUpdate): array
    {
        return [
            'contract_id' => $plot['contract_id'],
            'c_num' => $plot['c_num'],
            'due_date' => $plot['due_date'],
            'start_date' => $plot['start_date'],
            'active_status' => $plot['active_status'],
            'nm_usage_rights' => $GLOBALS['Contracts']['ContractTypes'][$plot['nm_usage_rights']]['name'],
            'invalid_on_kvs_update' => $invalidOnKvsUpdate,
        ];
    }

    private function processGeoms($plot)
    {
        $processedPlot = [];
        $processedPlot['ngeom'] = explode('","', trim($plot['ngeom'], '{}'));
        $processedPlot['ngeom'][0] = str_replace('"', '', $processedPlot['ngeom'][0]);
        $processedPlot['ngeom'][count($processedPlot['ngeom']) - 1] = str_replace('"', '', $processedPlot['ngeom'][count($processedPlot['ngeom']) - 1]);

        $ngeomCount = count($processedPlot['ngeom']);
        for ($j = 0; $j < $ngeomCount; $j++) {
            $geomAttributes = explode(':', $processedPlot['ngeom'][$j]);
            $processedPlot['ngeom'][$j] = [];
            $processedPlot['ngeom'][$j]['label'] = $geomAttributes[0];
            $processedPlot['ngeom'][$j]['geom'] = $geomAttributes[1];
        }

        $processedPlot['oldgeom'] = [
            [
                'label' => $plot['kad_ident'],
                'geom' => $plot['geom'],
            ],
        ];

        return $processedPlot;
    }

    private function checkToEndActualization($file_id, $ekate, $missingPlots)
    {
        $UserDbController = new UserDbController($this->User->Database);

        $tableExists = $UserDbController->getTableNameExist('layer_tmp_kvs_' . $ekate . '_invalid');

        if (!$tableExists && !count($missingPlots)) {
            // Status: Успешно обработен
            $UserDbController->endUpdate($file_id, $ekate);
        }
    }

    private function KVSKeepNew(DateTime $editActiveFrom, string $kadIdent, string $ekatte)
    {
        $UsersController = new UsersController('Users');
        $UserDbController = new UserDbController($this->User->Database);
        $kvsContractsUpdateView = 'kvs_contracts_update_' . $ekatte;
        $tmpKvsTable = 'layer_tmp_kvs_' . $ekatte;
        $editActiveFrom = $editActiveFrom->format('Y-m-d H:i:s');

        $transaction = $UsersController->startTransaction();

        try {
            $options = [
                'tablename' => $kvsContractsUpdateView,
                'return' => ['gid', 'ngeom'],
                'where' => [
                    'kad_ident' => ['column' => 'kad_ident', 'compare' => '=', 'value' => $kadIdent],
                ],
                'limit' => 1,
                'offset' => 0,
            ];
            // get by kad_ident old plot gid and all new geoms
            // we rely on the fact that an old geometry cannot intersect with more different new geometries
            $result = array_pop($UserDbController->getItemsByParams($options, false, false));

            if (is_null($result)) {
                // If we add a plot to a contract and this plot is not previously in the kvs_contracts_update_ekatte view, we should skip it.
                // We only need to refresh the view because the plot can create a new conflict to resolve.
                $transaction->rollBack();

                return;
            }

            // edit original polygon
            $editingGid = $result['gid'];
            $options = [
                'tablename' => $UserDbController->DbHandler->tableKVS,
                'mainData' => [
                    'is_edited' => true,
                    'edit_date' => date('Y-m-d'),
                    'edit_active_from' => $editActiveFrom,
                    'waiting_update' => false,
                ],
                'where' => [
                    'gid' => $editingGid,
                ],
            ];

            $UserDbController->editItem($options);
            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, 'Map', LoggerMessages::ITEM_EDIT, $UserDbController->DbHandler->tableKVS, [$editingGid]);

            $newKadIdents = [];
            foreach ($this->processGeoms($result)['ngeom'] as $geom) {
                $newKadIdents[] = $geom['label'];
            }

            $options = [
                'tablename' => $tmpKvsTable,
                'where' => [
                    'kad_no' => ['column' => 'kad_no', 'compare' => 'IN', 'value' => $newKadIdents],
                ],
            ];
            $newPlots = $UserDbController->getItemsByParams($options, false, false);

            // add kvs edit log
            foreach ($newPlots as $newPlot) {
                // Result can be only one value because kad_ident is unique value
                $newPlotData = array_pop($UserDbController->getItemsByParams($options = [
                    'tablename' => $UserDbController->DbHandler->tableKVS,
                    'where' => [
                        'kad_ident' => ['column' => 'kad_ident', 'compare' => '=', 'value' => $newPlot['kad_no']],
                    ],
                ]));

                if (empty($newPlotData)) {
                    throw new Exception(sprintf('Plot not found in KVS table %s', $newPlot['kad_no']));
                }

                // it is possible that the 2 or more NEW plot geometries intersect with the geometry of same OLD plot,
                // so the edit_active_from of the new plot should be the lowest possible date value if we dont want to have hole on the map
                // The editing of a new geometry can be done from several different save events
                if ($newPlotData['edit_active_from'] != $newPlotData['edit_date']) {
                    if ($newPlotData['edit_active_from'] < $editActiveFrom) {
                        $editActiveFrom = $newPlotData['edit_active_from'];
                    }
                }

                if (!isset($newPlotData['gid']) || '' === $newPlotData['gid']) {
                    throw new Exception(sprintf('Plot gid not found in KVS table %s', $newPlot['kad_no']));
                }

                $UserDbController->editItem([
                    'tablename' => $UserDbController->DbHandler->tableKVS,
                    'mainData' => [
                        'waiting_update' => false,
                        'is_edited' => false,
                        'edit_active_from' => null, // edit_active_from will be used only for the old plot, new plot will be always active
                    ],
                    'id_name' => 'gid',
                    'id_string' => $newPlotData['gid'],
                ]);
            }

            $transaction->commit();
        } catch (Exception $e) {
            $transaction->rollBack();

            throw $e;
        }
    }

    private function getEkatte($rpcParams): string
    {
        $UsersController = new UsersController('Users');
        $options = [
            'return' => [
                'ekate',
            ],
            'tablename' => 'su_users_files',
            'where' => [
                'file_id' => ['column' => 'id', 'compare' => '=', 'value' => $rpcParams['file_id']],
            ],
        ];
        $ekate = $UsersController->getItemsByParams($options, false, false);

        return $ekate[0]['ekate'];
    }
}
