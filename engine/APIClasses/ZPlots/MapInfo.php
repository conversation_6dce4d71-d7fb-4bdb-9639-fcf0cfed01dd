<?php

namespace TF\Engine\APIClasses\ZPlots;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController;

// Prado::using('Plugins.Core.Contracts.conf');
// Prado::using('Plugins.Core.Contracts.*');
// Prado::using('Plugins.Core.Layers.conf');
// Prado::using('Plugins.Core.Layers.*');

/**
 * Returns map info, based on selected filter criteria.
 *
 * @rpc-module ZPlots
 *
 * @rpc-service-id zp-map-info
 *
 * @property UserDbController $UserDbController
 * @property LayersController $LayersController
 * @property UserDbPlotsController $UserDbPlotsController
 */
class MapInfo extends TRpcApiProvider
{
    private $ContractsController = false;
    private $UserDbController = false;
    private $LayersController = false;
    private $UserDbPlotsController = false;

    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getMapInfo']],
            'initMap' => ['method' => [$this, 'initMap']],
            'initKvs' => ['method' => [$this, 'initKvs']],
            'initCulture' => ['method' => [$this, 'initCulture']],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item int year
     *                         #item int farming
     *                         #item string bbox
     *                         #item int x
     *                         #item int y
     *                         #item int width
     *                         #item int height
     *                         }
     *
     * @throws MTRpcException
     *
     * @return array|int
     */
    public function getMapInfo($rpcParams)
    {
        $return = [];
        $UserDbController = new UserDbController($this->User->Database);
        $LayersController = new LayersController('Layers');

        $options = [
            'return' => ['t.id', 'table_name'],
            'where' => [
                'year' => ['column' => 'year', 'compare' => '=', 'value' => (int)$rpcParams['year']],
                'farming' => ['column' => 'farming', 'compare' => '=', 'value' => (int)$rpcParams['farming']],
                'layer_type' => ['column' => 'layer_type', 'compare' => '=', 'value' => 1],
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'prefix' => 't', 'value' => $this->User->GroupID],
            ],
        ];

        $layerData = $LayersController->getLayers($options);
        $tableExists = $UserDbController->getTableNameExist($layerData[0]['table_name']);

        if (!$tableExists) {
            return [];
        }

        // getting the required plot ID
        $urlRequest = WMS_SERVER_INTERNAL
                . '?REQUEST=GetFeatureInfo'
                . '&EXCEPTIONS=application/vnd.ogc.se_xml'
                . '&VERSION=1.1.1'
                . '&MAP=' . WMS_MAP_PATH . $this->User->GroupID . '.map'
                . '&BBOX=' . $rpcParams['bbox']
                . '&X=' . $rpcParams['x']
                . '&Y=' . $rpcParams['y']
                . '&INFO_FORMAT=text/plain'
                . '&QUERY_LAYERS=' . $layerData[0]['table_name']
                . '&LAYERS=' . $layerData[0]['table_name']
                . '&FEATURE_COUNT=50'
                . '&SRS=EPSG:900913'
                . '&STYLES='
                . '&WIDTH=' . $rpcParams['width']
                . '&HEIGHT=' . $rpcParams['height'];

        $curl = curl_init($urlRequest);
        curl_setopt($curl, CURLOPT_FAILONERROR, true);
        curl_setopt($curl, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        $result = curl_exec($curl);

        $explode_result = explode(' ', $result);

        $plot_id = rtrim($explode_result[5], ':');
        if (!is_numeric($plot_id)) {
            throw new MTRpcException('MAP_EMPTY_AREA', -33056);
        }
        // returning the plot ID
        return $plot_id;
    }

    /**
     * initMap Субсидии>Парцели>Карта (Инструменти>По филтър).
     *
     * @api-method initMap
     *
     * @param array $rpcParams
     *                         {
     *                         #item int farming
     *                         #item int year
     *                         #item string ekatte
     *                         #item string culture
     *                         #item string isak_prc_uin
     *                         }
     *
     * @return array
     */
    public function initMap($rpcParams)
    {
        $LayersController = new LayersController('Layers');
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);
        // creating the default return array
        $return = [
            'colorarray' => [
                0 => ['color' => 'ff0000', 'name' => 'Филтрирани', 'iconCls' => 'no-background no-padding'],
            ],
        ];

        $options = [
            'farming' => $rpcParams['farming'],
            'year' => $rpcParams['year'],
            'group_id' => $this->User->GroupID,
            'layer_type' => 1,
        ];
        $tableName = $LayersController->getTableNameByParams($options);

        if (!$tableName) {
            return $return;
        }

        $tableExists = $UserDbController->getTableNameExist($tableName);
        if (!$tableExists) {
            return $return;
        }

        $options = [
            'return' => [
                'p.geom', 'p.id',
            ],
            'tablename' => $tableName,
            'where' => [
                'ekatte' => ['column' => 'ekatte', 'compare' => '=', 'prefix' => 'p', 'value' => $rpcParams['ekatte']],
                'culture' => ['column' => 'culture', 'compare' => '=', 'prefix' => 'p', 'value' => $rpcParams['culture']],
                'isak_prc_uin' => ['column' => 'isak_prc_uin', 'compare' => '=', 'prefix' => 'p', 'value' => $rpcParams['isak_prc_uin']],
            ],
        ];

        $sqlString = $UserDbPlotsController->getPlotData($options, false, true);

        $options['return'] = ['ST_Extent(geom) as extent'];
        $maxextent = $UserDbPlotsController->getPlotData($options, false);

        $query = "({$sqlString}) as subquery using unique id";

        $options = [
            'return' => ['t.id', 't.table_name', 't.extent', 't.layer_type', 't.border_color', 't.color'],
            'sort' => 'id',
            'order' => 'DESC',
            'where' => [
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'prefix' => 't', 'value' => $this->User->GroupID],
                'layer_type' => ['column' => 'layer_type', 'compare' => '=', 'prefix' => 't', 'value' => 1],
                'farming' => ['column' => 'farming', 'compare' => '=', 'prefix' => 't', 'value' => $rpcParams['farming']],
                'year' => ['column' => 'year', 'compare' => '=', 'prefix' => 't', 'value' => $rpcParams['year']],
            ],
        ];

        $result = $LayersController->getLayers($options);

        $data = [];

        $color1 = '000000';
        $color2 = 'ff0000';
        $data['layername'] = 'topic_zp_layer';
        $data['maxextent'] = $result[0]['extent'];
        $data['host'] = DEFAULT_DB_HOST;
        $data['dbname'] = $this->User->Database;
        $data['username'] = DEFAULT_DB_USERNAME;
        $data['password'] = DEFAULT_DB_PASSWORD;
        $data['port'] = DEFAULT_DB_PORT;
        $data['query'] = $query;
        $data['gid'] = 'gid';
        $data['transparency'] = '100';
        $data['classes'][0]['name'] = 'topic_zp_layer';
        $data['classes'][0]['border_color'] = hexdec(substr($color1, 0, 2)) . ' ' . hexdec(substr($color1, 2, 2)) . ' ' . hexdec(substr($color1, 4, 2));
        $data['classes'][0]['color'] = hexdec(substr($color2, 0, 2)) . ' ' . hexdec(substr($color2, 2, 2)) . ' ' . hexdec(substr($color2, 4, 2));

        $hexColorArray[] = ['color' => $color2, 'name' => 'Филтрирани', 'iconCls' => 'no-background no-padding'];
        $ltext = $LayersController->StringHelper->loadTemplate($GLOBALS['Templates'][2]['template'], $data);

        @unlink(WMS_MAP_PATH . $this->User->GroupID . '_layer_zp.map');
        $fp = fopen(WMS_MAP_PATH . $this->User->GroupID . '_layer_zp.map', 'w');
        $res = fwrite($fp, $ltext);
        fclose($fp);

        // converting max extent
        $maxExtent = $maxextent[0]['extent'];
        $maxExtent = str_replace('BOX(', '', $maxExtent);
        $maxExtent = str_replace(')', '', $maxExtent);
        $maxExtent = str_replace(',', ' ', $maxExtent);
        $maxExtent = str_replace(' ', ', ', $maxExtent);

        $return = [
            'colorarray' => $hexColorArray,
            'layers' => [
                0 => ['extent' => $maxExtent, 'name' => $tableName],
                1 => ['extent' => $maxExtent, 'name' => 'topic_zp_layer'],
            ],
        ];

        return $return;
    }

    /**
     * initMap Субсидии>Парцели>Карта (Инструменти>По култура).
     *
     * @api-method initCulture
     *
     * @param array $rpcParams
     *                         {
     *                         #item int farming
     *                         #item int year
     *                         #item string ekatte
     *                         #item string culture
     *                         #item string isak_prc_uin
     *                         }
     *
     * @return array
     */
    public function initCulture($rpcParams)
    {
        $LayersController = new LayersController('Layers');
        $UserDbController = new UserDbController($this->User->Database);
        // creating the default return array
        $return = [
            'colorarray' => [
                0 => ['color' => 'ff0000', 'name' => 'Филтрирани', 'iconCls' => 'no-background no-padding'],
            ],
        ];

        $options = [];
        $options['farming'] = $rpcParams['farming'];
        $options['year'] = $rpcParams['year'];
        $options['group_id'] = $this->User->GroupID;
        $options['layer_type'] = 1;
        $tableName = $LayersController->getTableNameByParams($options);

        if (!$tableName) {
            return $return;
        }
        $tableExists = $UserDbController->getTableNameExist($tableName);

        if (!$tableExists) {
            return $return;
        }

        $options = [
            'return' => ['t.id', 't.table_name', 't.extent', 't.layer_type', 't.border_color', 't.color'],
            'sort' => 'id',
            'order' => 'DESC',
            'where' => [
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'prefix' => 't', 'value' => $this->User->GroupID],
                'layer_type' => ['column' => 'layer_type', 'compare' => '=', 'prefix' => 't', 'value' => 1],
                'farming' => ['column' => 'farming', 'compare' => '=', 'prefix' => 't', 'value' => $rpcParams['farming']],
                'year' => ['column' => 'year', 'compare' => '=', 'prefix' => 't', 'value' => $rpcParams['year']],
            ],
        ];

        $result = $LayersController->getLayers($options);

        $hexColorArray = [];
        $data = $UserDbController->getLayerColumnValues($tableName, 'culture');
        $dataCount = count($data);
        for ($j = 0; $j < $dataCount; $j++) {
            $color1 = '000000';
            $color2 = $LayersController->StringHelper->randomColorCode();

            $arrClass[$j]['display_label'] = 0;
            $arrClass[$j]['name'] = trim($data[$j]['culture']);
            $arrClass[$j]['expression'] = trim($data[$j]['culture']);
            $arrClass[$j]['border_color'] = hexdec(substr($color1, 0, 2)) . ' ' . hexdec(substr($color1, 2, 2)) . ' ' . hexdec(substr($color1, 4, 2));
            $arrClass[$j]['color'] = hexdec(substr($color2, 0, 2)) . ' ' . hexdec(substr($color2, 2, 2)) . ' ' . hexdec(substr($color2, 4, 2));
            $cult = (int) $data[$j]['culture'];
            $hexColorArray[] = ['color' => $color2, 'name' => $GLOBALS['Farming']['crops'][$cult]['crop_name'] ? $GLOBALS['Farming']['crops'][$cult]['crop_name'] : 'Неизвестна', 'iconCls' => 'no-background no-padding'];
        }

        $color1 = '000000';
        $color2 = 'ff0000';
        $data['layername'] = 'topic_zp_layer';
        $data['maxextent'] = $result[0]['extent'];
        $data['host'] = DEFAULT_DB_HOST;
        $data['dbname'] = $this->User->Database;
        $data['username'] = DEFAULT_DB_USERNAME;
        $data['password'] = DEFAULT_DB_PASSWORD;
        $data['port'] = DEFAULT_DB_PORT;
        $data['query'] = $tableName;
        $data['gid'] = 'id';
        $data['transparency'] = '100';
        $data['classitem'] = 'culture';
        $data['classes'] = $arrClass;

        $ltext = $LayersController->StringHelper->loadTemplate($GLOBALS['Templates'][3]['template'], $data);

        @unlink(WMS_MAP_PATH . $this->User->GroupID . '_layer_zp.map');
        $fp = fopen(WMS_MAP_PATH . $this->User->GroupID . '_layer_zp.map', 'w');
        $res = fwrite($fp, $ltext);
        fclose($fp);

        $return = [
            'colorarray' => $hexColorArray,
            'layers' => [
                0 => [
                    'extent' => str_replace(' ', ', ', $result[0]['extent']),
                    'name' => 'topic_zp_layer',
                ],
            ],
        ];

        return $return;
    }

    /**
     * initKvs Субсидии>Парцели>Пресичане с имоти.
     *
     * @api-method initKvs
     *
     * @param array $rpcParams
     *                         {
     *                         #item int farming
     *                         #item int year
     *                         #item string ekatte
     *                         #item string culture
     *                         #item string isak_prc_uin
     *                         }
     *
     * @return array
     */
    public function initKvs($rpcParams)
    {
        $LayersController = new LayersController('Layers');
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);

        $isak_prc_uin = $rpcParams['isak_prc_uin'];
        $culture = $rpcParams['culture'];

        // get ZP tablename
        $options = [
            'farming' => $rpcParams['farming'],
            'year' => $rpcParams['year'],
            'group_id' => $this->User->GroupID,
            'layer_type' => 1,
        ];
        $tableName = $LayersController->getTableNameByParams($options);

        if (!$tableName) {
            return [];
        }

        // get ZP - KVS intersection
        $options = [
            'return' => ['a.*'],
            'where' => [
                'ekatte' => ['column' => 'ekatte', 'compare' => '=', 'value' => $rpcParams['ekatte']],
                'isak' => ['column' => 'isak_prc_uin', 'compare' => '=', 'value' => $isak_prc_uin],
                'culture' => ['column' => 'culture', 'compare' => '=', 'value' => $culture],
            ],
        ];

        $sqlString = $UserDbController->getZPIntersectKvs($options['where'], $tableName);

        $query = "({$sqlString}) as subquery using unique gid";

        // get KVS data
        $options = [
            'return' => ['t.id', 't.table_name', 't.extent', 't.layer_type', 't.border_color', 't.color'],
            'sort' => 'id',
            'order' => 'DESC',
            'where' => [
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'prefix' => 't', 'value' => $this->User->GroupID],
                'layer_type' => ['column' => 'layer_type', 'compare' => '=', 'prefix' => 't', 'value' => 5],
            ],
        ];

        $result = $LayersController->getLayers($options);

        $data = [];

        $layersArray = [];
        $kvsExtent = str_replace(' ', ', ', $result[0]['extent']);

        // add the map pad
        $layersArray[] = ['name' => 'layer_kvs', 'extent' => $kvsExtent];

        $color1 = '000000';
        $color2 = 'ff0000';
        $data['layername'] = 'topic_kvs_layer';
        $data['maxextent'] = $result[0]['extent'];
        $data['host'] = DEFAULT_DB_HOST;
        $data['dbname'] = $this->User->Database;
        $data['username'] = DEFAULT_DB_USERNAME;
        $data['password'] = DEFAULT_DB_PASSWORD;
        $data['port'] = DEFAULT_DB_PORT;
        $data['query'] = $query;
        $data['gid'] = 'gid';
        $data['classitem'] = 'nm_usage_rights';
        $data['transparency'] = '60';
        $layersArray[] = ['name' => 'topic_kvs_layer', 'extent' => $kvsExtent];

        $color1 = '000000';
        $color2 = $LayersController->StringHelper->randomColorCode();

        $data['classes'][0]['name'] = 'topic_kvs_layer_1';
        $data['classes'][0]['border_color'] = hexdec(substr($color1, 0, 2)) . ' ' . hexdec(substr($color1, 2, 2)) . ' ' . hexdec(substr($color1, 4, 2));
        $data['classes'][0]['color'] = hexdec(substr($color2, 0, 2)) . ' ' . hexdec(substr($color2, 2, 2)) . ' ' . hexdec(substr($color2, 4, 2));
        $data['classes'][0]['expression'] = 1;
        $hexColorArray[] = ['color' => $color2, 'name' => 'Собствени', 'iconCls' => 'no-background no-padding'];
        $layersArray[] = ['name' => 'topic_kvs_layer_1', 'extent' => $kvsExtent];

        $color1 = '000000';
        $color2 = $LayersController->StringHelper->randomColorCode();

        $data['classes'][1]['name'] = 'topic_kvs_layer_2';
        $data['classes'][1]['border_color'] = hexdec(substr($color1, 0, 2)) . ' ' . hexdec(substr($color1, 2, 2)) . ' ' . hexdec(substr($color1, 4, 2));
        $data['classes'][1]['color'] = hexdec(substr($color2, 0, 2)) . ' ' . hexdec(substr($color2, 2, 2)) . ' ' . hexdec(substr($color2, 4, 2));
        $data['classes'][1]['expression'] = 2;
        $hexColorArray[] = ['color' => $color2, 'name' => 'Аренда', 'iconCls' => 'no-background no-padding'];
        $layersArray[] = ['name' => 'topic_kvs_layer_2', 'extent' => $kvsExtent];

        $color1 = '000000';
        $color2 = $LayersController->StringHelper->randomColorCode();

        $data['classes'][2]['name'] = 'topic_kvs_layer_3';
        $data['classes'][2]['border_color'] = hexdec(substr($color1, 0, 2)) . ' ' . hexdec(substr($color1, 2, 2)) . ' ' . hexdec(substr($color1, 4, 2));
        $data['classes'][2]['color'] = hexdec(substr($color2, 0, 2)) . ' ' . hexdec(substr($color2, 2, 2)) . ' ' . hexdec(substr($color2, 4, 2));
        $data['classes'][2]['expression'] = 3;
        $hexColorArray[] = ['color' => $color2, 'name' => 'Наем', 'iconCls' => 'no-background no-padding'];
        $layersArray[] = ['name' => 'topic_kvs_layer_3', 'extent' => $kvsExtent];

        $color1 = '000000';
        $color2 = $LayersController->StringHelper->randomColorCode();

        $data['classes'][3]['name'] = 'topic_kvs_layer_4';
        $data['classes'][3]['border_color'] = hexdec(substr($color1, 0, 2)) . ' ' . hexdec(substr($color1, 2, 2)) . ' ' . hexdec(substr($color1, 4, 2));
        $data['classes'][3]['color'] = hexdec(substr($color2, 0, 2)) . ' ' . hexdec(substr($color2, 2, 2)) . ' ' . hexdec(substr($color2, 4, 2));
        $data['classes'][3]['expression'] = 4;
        $hexColorArray[] = ['color' => $color2, 'name' => 'Споразумение', 'iconCls' => 'no-background no-padding'];
        $layersArray[] = ['name' => 'topic_kvs_layer_4', 'extent' => $kvsExtent];

        $color1 = '000000';
        $color2 = $LayersController->StringHelper->randomColorCode();

        $data['classes'][4]['name'] = 'topic_kvs_layer_5';
        $data['classes'][4]['border_color'] = hexdec(substr($color1, 0, 2)) . ' ' . hexdec(substr($color1, 2, 2)) . ' ' . hexdec(substr($color1, 4, 2));
        $data['classes'][4]['color'] = hexdec(substr($color2, 0, 2)) . ' ' . hexdec(substr($color2, 2, 2)) . ' ' . hexdec(substr($color2, 4, 2));
        $data['classes'][4]['expression'] = 5;
        $hexColorArray[] = ['color' => $color2, 'name' => 'Съвместна обработка', 'iconCls' => 'no-background no-padding'];
        $layersArray[] = ['name' => 'topic_kvs_layer_5', 'extent' => $kvsExtent];

        $color1 = '000000';
        $color2 = 'ff0000';
        $data['classes'][5]['name'] = 'topic_kvs_layer_6';
        $data['classes'][5]['border_color'] = hexdec(substr($color1, 0, 2)) . ' ' . hexdec(substr($color1, 2, 2)) . ' ' . hexdec(substr($color1, 4, 2));
        $data['classes'][5]['color'] = hexdec(substr($color2, 0, 2)) . ' ' . hexdec(substr($color2, 2, 2)) . ' ' . hexdec(substr($color2, 4, 2));
        $hexColorArray[] = ['color' => $color2, 'name' => 'Без договор', 'iconCls' => 'no-background no-padding'];
        $layersArray[] = ['name' => 'topic_kvs_layer_6', 'extent' => $kvsExtent];

        $ltext = $LayersController->StringHelper->loadTemplate($GLOBALS['Templates'][3]['template'], $data);

        // creating ZP border layer
        $options = [
            'return' => ['p.*'],
            'tablename' => $tableName,
            'where' => [
                'ekatte' => ['column' => 'ekatte', 'compare' => '=', 'prefix' => 'p', 'value' => $rpcParams['ekatte']],
                'culture' => ['column' => 'culture', 'compare' => '=', 'prefix' => 'p', 'value' => $culture],
                'isak_prc_uin' => ['column' => 'isak_prc_uin', 'compare' => '=', 'prefix' => 'p', 'value' => $isak_prc_uin],
            ],
        ];
        $sqlString = $UserDbPlotsController->getPlotData($options, false, true);

        $options['return'] = ['ST_Extent(geom) as extent'];
        $maxextent = $UserDbPlotsController->getPlotData($options, false);

        $query = "({$sqlString}) as subquery using unique id";
        unset($data['classes']);
        $color1 = '00ff00';
        $data['layername'] = 'topic_kvs_zp_layer';
        $data['maxextent'] = $result[0]['extent'];
        $data['host'] = DEFAULT_DB_HOST;
        $data['dbname'] = $this->User->Database;
        $data['username'] = DEFAULT_DB_USERNAME;
        $data['password'] = DEFAULT_DB_PASSWORD;
        $data['port'] = DEFAULT_DB_PORT;
        $data['query'] = $query;
        $data['gid'] = 'gid';
        $data['transparency'] = '100';
        $data['classes'][0]['name'] = 'topic_kvs_zp_layer';
        $data['classes'][0]['border_color'] = hexdec(substr($color1, 0, 2)) . ' ' . hexdec(substr($color1, 2, 2)) . ' ' . hexdec(substr($color1, 4, 2));
        $data['classes'][0]['color'] = false;
        $data['classes'][0]['width'] = 3;
        $data['classes'][0]['dashed'] = false;

        $ltext .= $LayersController->StringHelper->loadTemplate($GLOBALS['Templates'][2]['template'], $data);

        @unlink(WMS_MAP_PATH . $this->User->GroupID . '_layer_kvs.map');
        $fp = fopen(WMS_MAP_PATH . $this->User->GroupID . '_layer_kvs.map', 'w');
        $res = fwrite($fp, $ltext);
        fclose($fp);

        // get ZP extent and transform it
        $maxextent = $maxextent[0]['extent'];
        $maxextent = str_replace('BOX(', '', $maxextent);
        $maxextent = str_replace(')', '', $maxextent);
        $maxextent = str_replace(',', ' ', $maxextent);
        $maxextent = str_replace(' ', ',', $maxextent);

        // add the ZP border layer
        $layersArray[] = ['name' => 'topic_kvs_zp_layer', 'extent' => $maxextent];

        $return = [
            'colorarray' => $hexColorArray,
            'layers' => $layersArray,
            // will be used for tree
        ];

        return $return;
    }
}
