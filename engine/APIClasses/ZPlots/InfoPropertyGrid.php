<?php

namespace TF\Engine\APIClasses\ZPlots;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

/**
 *  Панел "Информация".
 *
 * @rpc-module ZPlots
 *
 * @rpc-service-id zp-info-pg
 *
 * @property LayersController $LayersController
 * @property UserDbController $UserDbController
 */
class InfoPropertyGrid extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getInfoPropertyGrid']],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item int year
     *                         #item int farming
     *                         #item int plot_id
     *                         }
     *
     * @return array
     */
    public function getInfoPropertyGrid($rpcParams)
    {
        if (0 == count($rpcParams['plot_id'])) {
            return ['rows' => [], 'total' => 0];
        }

        $LayersController = new LayersController('Layers');
        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'return' => ['table_name'],
            'where' => [
                'year' => ['column' => 'year', 'compare' => '=', 'value' => (int) $rpcParams['year']],
                'farming' => ['column' => 'farming', 'compare' => '=', 'value' => (int) $rpcParams['farming']],
                'layer_type' => ['column' => 'layer_type', 'compare' => '=', 'value' => 1],
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'prefix' => 't', 'value' => $this->User->GroupID],
            ],
        ];

        $layerData = $LayersController->getLayers($options);

        // creating the new option array for plot item data
        $options = [
            'return' => [
                'ST_Area(geom) as area_kvs', 'ekatte', 'culture', 'obrabotki', 'dobivi', 'napoqvane', 'polivki', 'polzvatel', 'isak_prc_uin',
            ],
            'tablename' => $layerData[0]['table_name'],
            'where' => [
                'id' => $rpcParams['plot_id'],
            ],
        ];

        $array = $UserDbController->getPolygonDataById($options);
        $return = [];

        $rows = [
            0 => [
                'name' => 'Идентификатор',
                'value' => $rpcParams['plot_id'],
                'group' => 'Системна информация',
            ],
            1 => [
                'name' => 'EKATTE',
                'value' => $array[0]['ekatte'],
                'group' => 'Обща информация',
            ],
            2 => [
                'name' => 'Култура',
                'value' => $GLOBALS['Farming']['crops'][$array[0]['culture']]['crop_name'],
                'group' => 'Обща информация',
            ],
            3 => [
                'name' => 'Обработки',
                'value' => $array[0]['obrabotki'],
                'group' => 'Обща информация',
            ],
            4 => [
                'name' => 'Добиви',
                'value' => $array[0]['dobivi'],
                'group' => 'Обща информация',
            ],
            5 => [
                'name' => 'Напояване',
                'value' => $array[0]['napoqvane'],
                'group' => 'Обща информация',
            ],
            6 => [
                'name' => 'Поливки',
                'value' => $array[0]['polivki'],
                'group' => 'Обща информация',
            ],
            7 => [
                'name' => 'Ползвател',
                'value' => $array[0]['polzvatel'],
                'group' => 'Обща информация',
            ],
            8 => [
                'name' => 'ИСАК номер',
                'value' => $array[0]['isak_prc_uin'],
                'group' => 'Обща информация',
            ],
            9 => [
                'name' => 'Площ (дка)',
                'value' => number_format($array[0]['area_kvs'] / 1000, 3),
                'group' => 'Системна информация',
            ],
        ];

        $return['total'] = 10;
        $return['rows'] = $rows;

        return $return;
    }
}
