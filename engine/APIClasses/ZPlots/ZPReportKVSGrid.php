<?php

namespace TF\Engine\APIClasses\ZPlots;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbZPlots\UserDbZPlotsController;

// Prado::using('Plugins.Core.Layers.conf');
// Prado::using('Plugins.Core.Layers.*');
// Prado::using('Plugins.Core.Farming.conf');

/**
 * Returns report information.
 *
 * @rpc-module ZPlots
 *
 * @rpc-service-id zp-kvs-report
 *
 * @property UserDbController $UserDbController
 * @property UserDbZPlotsController $UserDbZPlotsController
 * @property LayersController $LayersController
 */
class ZPReportKVSGrid extends TRpcApiProvider
{
    private $UserDbController = false;
    private $UserDbZPlotsController = false;
    private $LayersController = false;

    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readZPReportKVSGrid'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item int    farming
     *                         #item int    year
     *                         #item string ekatte
     *                         #item string isak
     *                         #item string culture
     *                         }
     * @param int $page pagination parameter
     * @param int $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array
     */
    public function readZPReportKVSGrid(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $return = [
            'total' => 0,
            'rows' => [],
            'footer' => [],
        ];

        // init all needed controllers
        $UserDbZPlotsController = new UserDbZPlotsController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);
        $LayersController = new LayersController('Layers');

        // $cropLayerid = (int)$rpcParams['layer_id'];
        $farming = (int) $rpcParams['farming'];
        $year = (int) $rpcParams['year'];
        $year_start_date = ($GLOBALS['Farming']['years'][$year]['year'] - 1) . '-10-01';
        $year_due_date = $GLOBALS['Farming']['years'][$year]['year'] . '-09-30';

        $options = [];
        $options['farming'] = $farming;
        $options['year'] = $year;
        $options['group_id'] = $this->User->GroupID;
        $options['layer_type'] = 1;
        $tableName = $LayersController->getTableNameByParams($options);

        $tableExists = $UserDbController->getTableNameExist($tableName);

        if (!$tableExists) {
            return $return;
        }

        $gidOptions = [
            'joinTable' => $tableName,
            'return' => [
                'array_agg(DISTINCT gid) as gids',
            ],
            'where' => [
                'is_edited' => ['column' => 'is_edited', 'compare' => '=', 'value' => 'FALSE'],
                'ekatte' => ['column' => 'ekatte', 'compare' => '=', 'value' => $rpcParams['ekatte']],
                'isak' => ['column' => 'isak_prc_uin', 'compare' => '=', 'value' => $rpcParams['isak_prc_uin']],
                'culture' => ['column' => 'culture', 'compare' => '=', 'value' => $rpcParams['culture']],
            ],
            'minimalIntersection' => 10,
        ];

        $gidsArray = $UserDbController->intersectKvsWithCustomTable($gidOptions);

        $gidsArray = trim($gidsArray[0]['gids'], '{}');
        $gidsArray = array_map('intval', explode(',', $gidsArray));
        if (!count($gidsArray)) {
            return $return;
        }
        $hasContractsQuery = '(CASE WHEN (SELECT count(plot_id) '
                            . 'FROM su_contracts_plots_rel ir '
                            . 'INNER JOIN su_contracts ic ON(ir.contract_id = ic.id) '
                            . 'WHERE true '
                            . 'AND ir.plot_id = a.gid '
                            . "AND (CASE WHEN nm_usage_rights = 1 THEN '9999-12-31 00:00:00' ELSE due_date END) >= '{$year_start_date}' "
                            . "AND start_date <= '{$year_due_date}' "
                            . "AND ic.active = 'TRUE' "
                            . "AND ic.is_sublease = 'FALSE') = 0 THEN false ELSE true END) as has_contracts";

        $contractName = "(SELECT array_to_string(array_agg(concat(c.c_num, ' (' , to_char(c.start_date, 'DD.MM.YYYY'), 'г.',
                            CASE
                            WHEN b.due_date NOTNULL THEN
                                concat(' - ', to_char( b.due_date, 'DD.MM.YYYY'), 'г.')
                            ELSE
                                CASE
                                WHEN c.due_date NOTNULL THEN
                                    concat(' - ', to_char(c.due_date, 'DD.MM.YYYY'), 'г.')
                                ELSE
                                ''
                                END
                            END
                         , ')' )), '<br/>')
                FROM su_contracts c
                LEFT JOIN su_contracts b ON (c.ID = b.parent_id)
                LEFT JOIN su_contracts_plots_rel cpr ON(cpr.contract_id = c.id) where true "
                . 'AND cpr.plot_id = a.gid '
                . "AND (CASE WHEN c.nm_usage_rights = 1 THEN '9999-12-31 00:00:00' ELSE (CASE
                            WHEN b.due_date NOTNULL THEN
                                b.due_date
                            ELSE
                                c.due_date
                            END) END) >= '{$year_start_date}' "
                . "AND c.start_date <= '{$year_due_date}' "
                . "AND c.active = 'TRUE' "
                . "AND c.is_sublease = 'FALSE' "
                . "AND c.is_annex = 'FALSE') as contract_name";

        $optionsAnex = [
            'return' => [
                'c.parent_id AS contract_id',
                'a.gid as plot_id',
                'round((ST_Area(a.geom) /1000)::numeric, 3) as area_kvs',
                'CASE 
                    WHEN 
                        sum(cpr.contract_area) > round((ST_Area(a.geom) /1000)::numeric, 3) 
                    THEN
                        round((ST_Area(a.geom) /1000)::numeric, 3)
                    ELSE
                        sum(cpr.contract_area)
                    END AS contract_area',
            ],
            'where' => [
                'is_edited' => ['column' => 'is_edited', 'compare' => '=', 'value' => 'FALSE'],
                'ekate' => ['column' => 'ekate', 'compare' => '=', 'value' => $rpcParams['ekatte']],
                'start_date' => ['column' => 'c.start_date', 'compare' => '<=', 'value' => $year_due_date],
                'active' => ['column' => 'c.active', 'compare' => '=', 'value' => 'TRUE'],
                'is_sublease' => ['column' => 'c.is_sublease', 'compare' => '=', 'value' => 'FALSE'],
                'start_due_date' => ['column' => "(CASE WHEN c.nm_usage_rights = 1 THEN '9999-12-31 00:00:00' ELSE c.due_date END)", 'compare' => '>=', 'value' => $year_start_date],
                'is_annex' => ['column' => 'is_annex', 'compare' => '=', 'value' => 'TRUE'],
                'gids' => ['column' => 'gid', 'compare' => 'IN', 'value' => $gidsArray],
            ],
            'group' => 'a.geom, c.id, a.gid',
        ];

        // get only annexes
        $annexes = $UserDbZPlotsController->getContractsZPlotData($optionsAnex, false, false);

        // get formatted annexes by contract_id and plot_id = contract_area
        $annexesById = $this->getAnnexesByContractPlotId($annexes);

        $optionsAnex['where']['is_annex']['value'] = 'FALSE';
        $optionsAnex['return'][0] = 'c.id AS contract_id';

        // get all contracts
        $contracts = $UserDbZPlotsController->getContractsZPlotData($optionsAnex, false, false);

        // get contracts area and if have annexes get annex contract area and check the total area for plot
        $contractsArea = $this->compareContractsAndAnnexes($annexesById, $contracts);

        $options = [
            'return' => [
                'a.gid',
                'ST_Area(
                    ST_Intersection(
                        ST_SetSRID(a.geom,32635),
                        ST_SetSRID(b.geom,32635)
                    )
                ) as area_intersect',
                'ST_Area(a.geom) as area_kvs',
                'a.used_area as used_area',
                'a.kad_ident',
                'a.ekate',
                'a.masiv',
                'a.number',
                'a.virtual_category_title as category',
                'a.virtual_ntp_title as area_type',
                'a.mestnost',
                'a.include',
                'a.participate',
                'a.white_spots',
                'b.isak_prc_uin',
                $hasContractsQuery,
                $contractName,
                'count(*) OVER () as total_count',
                'sum(
                    ST_Area (
                        ST_Intersection (
                            ST_SetSRID (A .geom, 32635),
                            ST_SetSRID (b.geom, 32635)
                        )
                    ) /1000
                ) OVER () as total_area_intersects',
            ],
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
            'where' => [
                'is_edited' => ['column' => 'is_edited', 'compare' => '=', 'value' => 'FALSE'],
                'ekatte' => ['column' => 'ekatte', 'compare' => '=', 'value' => $rpcParams['ekatte']],
                'isak' => ['column' => 'isak_prc_uin', 'compare' => '=', 'value' => $rpcParams['isak_prc_uin']],
                'culture' => ['column' => 'culture', 'compare' => '=', 'value' => $rpcParams['culture']],
                'gids' => ['column' => 'gid', 'compare' => 'IN', 'value' => $gidsArray],
            ],
        ];

        $data = $UserDbZPlotsController->getKVSReportZP($options, $tableName, false, false);
        $dataCount = count($data);

        if (0 == count($data[0]['total_count'])) {
            return $return;
        }

        // total variable
        $total_contract_area = 0;
        $gidsCount = count($gidsArray);
        // calculate total contract area
        for ($i = 0; $i < $gidsCount; $i++) {
            $total_contract_area += $contractsArea[$gidsArray[$i]];
        }

        // total variables
        $totalPage_area_intersect = 0;
        $totalPage_area_kvs = 0;
        $totalPage_contract_area = 0;

        $kadIdentExist = [];

        // iterate contractsAreas
        for ($i = 0; $i < $dataCount; $i++) {
            if ($data[$i]['has_contracts']) {
                $data[$i]['has_contracts'] = 'Да';
            } else {
                $data[$i]['has_contracts'] = 'Не';
            }

            // add to total
            $totalPage_area_intersect += $data[$i]['area_intersect'];
            if (!in_array($data[$i]['kad_ident'], $kadIdentExist)) {
                $totalPage_contract_area += $contractsArea[$data[$i]['gid']];
                $totalPage_area_kvs += $data[$i]['area_kvs'];
            }

            $data[$i]['area_intersect'] = number_format($data[$i]['area_intersect'] / 1000, 3, '.', '');
            $data[$i]['area_kvs'] = number_format($data[$i]['area_kvs'] / 1000, 3, '.', '');

            if (0 != $contractsArea[$data[$i]['gid']]) {
                $data[$i]['contract_area'] = number_format($contractsArea[$data[$i]['gid']], 3, '.', '');
            } else {
                $data[$i]['contract_area'] = $contractsArea[$data[$i]['gid']];
            }

            // add kad ident
            $kadIdentExist[] = $data[$i]['kad_ident'];
        }

        // change options cause sum KVC
        $optionsSum['return'][0] = 'sum(round((ST_Area(geom)/1000)::numeric, 3)) as total_area_kvs';
        $optionsSum['tablename'] = 'layer_kvs';
        $optionsSum['where']['gid'] = ['column' => 'gid', 'compare' => 'IN', 'value' => $gidsArray];

        $dataKvc = $UserDbZPlotsController->getItemsByParams($optionsSum, false, false);

        $totalPage_area_intersect = number_format($totalPage_area_intersect / 1000, 3, '.', '');
        $totalPage_area_kvs = number_format($totalPage_area_kvs / 1000, 3, '.', '');
        $totalPage_contract_area = number_format($totalPage_contract_area, 3, '.', '');

        $total_area_intersect = $data[0]['total_area_intersects'];

        $total_area_kvs = number_format($dataKvc[0]['total_area_kvs'], 3, '.', '');
        $total_contract_area = number_format($total_contract_area, 3, '.', '');
        $total_area_intersect = number_format($total_area_intersect, 3, '.', '');

        $return = [
            'total' => $data[0]['total_count'],
            'rows' => $data,
            'footer' => [
                0 => [
                    'contract_name' => '<div style="font-weight:bold;text-align:right">Общо за страница:</div>',
                    'area_kvs' => '<b>' . $totalPage_area_kvs . '</b>',
                    'contract_area' => '<b>' . $totalPage_contract_area . '</b>',
                    'area_intersect' => '<b>' . $totalPage_area_intersect . '</b>',
                ],
                1 => [
                    'contract_name' => '<div style="font-weight:bold;text-align:right">Общо:</div>',
                    'area_kvs' => '<b>' . $total_area_kvs . '</b>',
                    'contract_area' => '<b>' . $total_contract_area . '</b>',
                    'area_intersect' => '<b>' . $total_area_intersect . '</b>',
                ],
            ],
        ];
        $return['rows'] = $data;

        return $return;
    }

    private function getAnnexesByContractPlotId($annexes)
    {
        $annexById = [];
        $annexesCount = count($annexes);
        for ($i = 0; $i < $annexesCount; $i++) {
            $annex = $annexes[$i];

            $annexById[$annex['plot_id']][$annex['contract_id']] = $annex['contract_area'];
        }

        return $annexById;
    }

    private function compareContractsAndAnnexes($annexes, $contracts)
    {
        // get areas by plot id
        $plotsAreaByContr = [];
        $contractsCount = count($contracts);
        for ($i = 0; $i < $contractsCount; $i++) {
            $contr = $contracts[$i];
            $cntrId = $contr['contract_id'];
            $plotId = $contr['plot_id'];

            if (array_key_exists($plotId, $annexes)) {
                if (array_key_exists($cntrId, $annexes[$plotId])) {
                    $plotsAreaByContr[$plotId][] = $annexes[$plotId][$cntrId];

                    continue;
                }
            }
            $plotsAreaByContr[$plotId][] = $contr['contract_area'];
        }

        // check areas by plot id and get min from sum(plot area by contracts) and contract area
        $plotsContrArea = [];
        for ($j = 0; $j < $contractsCount; $j++) {
            $contr = $contracts[$j];
            $plotId = $contr['plot_id'];
            $plotArea = array_sum($plotsAreaByContr[$plotId]);

            $plotsContrArea[$plotId] = min($contr['area_kvs'], $plotArea);
        }

        return $plotsContrArea;
    }
}
