<?php

namespace TF\Engine\APIClasses\ZPlots;

use Prado\Web\Services\TRpcApiProvider;
use TF\Application\Common\Config;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController;
use TF\Engine\Plugins\Core\UserDbZPlots\UserDbZPlotsController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Субсидии>Парцели>Парцели.
 *
 * @rpc-module ZPlots
 *
 * @rpc-service-id zplots-maingrid
 *
 * @property LayersController $LayersController
 * @property UserDbController $UserDbController
 * @property UserDbPlotsController $UserDbPlotsController
 * @property UserDbZPlotsController $UserDbZPlotsController
 * @property UsersController $UsersController
 */
class PlotsGrid extends TRpcApiProvider
{
    private $LayersController = false;
    private $UserDbController = false;
    private $UserDbPlotsController = false;
    private $UserDbZPlotsController = false;
    private $UsersController = false;

    private $module = 'ZPlots';
    private $service_id = 'zplots-maingrid';

    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getPlotsGrid'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'markForEdit' => ['method' => [$this, 'markForEdit']],
            'saveEdit' => ['method' => [$this, 'saveEdit'],
                'validators' => [
                    'rpcParams' => [
                        'ekatte' => 'validateText',
                        'culture' => 'validateText',
                        'obrabotki' => 'validateText',
                        'dobivi' => 'validateText',
                        'napoqvane' => 'validateText',
                        'polivki' => 'validateText',
                        'polzvatel' => 'validateText',
                        'isak_prc_uin' => 'validateText',
                    ],
                ],
            ],
            'saveMultiEdit' => ['method' => [$this, 'saveMultiEdit'],
                'validators' => [
                    'rpcParams' => [
                        'newCulture' => 'validateText',
                    ],
                ]],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item id farming
     *                         #item id year
     *                         #item string ekatte
     *                         #item string culture
     *                         #item string obrabotki
     *                         #item string dobivi
     *                         #item string napoqvane
     *                         #item string polivki
     *                         #item string polzvatel
     *                         #item string isak_prc_uin
     *                         }
     * @param int $page pagination parameter
     * @param int $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array
     */
    public function getPlotsGrid(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);
        $LayersController = new LayersController('Layers');

        // clean old results
        $result = [];
        $return = [];

        if (isset($rpcParams['farming'], $rpcParams['year'])) {
            $options = [
                'farming' => $rpcParams['farming'],
                'year' => $rpcParams['year'],
                'group_id' => $this->User->GroupID,
                'layer_type' => 1,
            ];
            $tableName = $LayersController->getTableNameByParams($options);

            // check if table exists
            $tableExist = $UserDbController->getTableNameExist($tableName);
            if (!$tableExist) {
                return [
                    'rows' => [],
                    'total' => 0,
                ];
            }
        } else {
            return [
                'rows' => [],
                'total' => 0,
            ];
        }

        $options = [
            'tablename' => $tableName,
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
            'return' => [
                'culture', 'ST_ASTEXT(geom)', 'ST_Area(geom) as area_zp', 'dobivi', 'ekatte', 'id', 'isak_prc_uin', 'napoqvane', 'obrabotki', 'polivki', 'polzvatel', 'area_name', 'mestnost',
            ],
            'where' => [
                'ekatte' => ['column' => 'ekatte', 'compare' => '=', 'prefix' => 'p', 'value' => $rpcParams['ekatte']],
                'culture' => ['column' => 'culture', 'compare' => '=', 'prefix' => 'p', 'value' => $rpcParams['culture']],
                'obrabotki' => ['column' => 'obrabotki', 'compare' => '=', 'prefix' => 'p', 'value' => $rpcParams['obrabotki']],
                'dobivi' => ['column' => 'dobivi', 'compare' => '=', 'prefix' => 'p', 'value' => $rpcParams['dobivi']],
                'napoqvane' => ['column' => 'napoqvane', 'compare' => '=', 'prefix' => 'p', 'value' => $rpcParams['napoqvane']],
                'polivki' => ['column' => 'polivki', 'compare' => '=', 'prefix' => 'p', 'value' => $rpcParams['polivki']],
                'polzvatel' => ['column' => 'polzvatel', 'compare' => '=', 'prefix' => 'p', 'value' => $rpcParams['polzvatel']],
                'isak_prc_uin' => ['column' => 'isak_prc_uin', 'compare' => 'ILIKE', 'prefix' => 'p', 'value' => $rpcParams['isak_prc_uin']],
            ],
        ];
        $result = $UserDbPlotsController->getPlotData($options, false);
        $counter = $UserDbPlotsController->getPlotData($options, true);
        $resultCount = count($result);
        if (0 != $resultCount) {
            for ($i = 0; $i < $resultCount; $i++) {
                $result[$i]['cropcode'] = $result[$i]['culture'];
                $culture = (int) $result[$i]['culture'];
                $result[$i]['culture'] = $GLOBALS['Farming']['crops'][$culture]['crop_name'];
                $result[$i]['area_zp'] = number_format($result[$i]['area_zp'] / 1000, 3);
            }

            $return['rows'] = $result;
        } else {
            $return['rows'] = [];
        }
        $return['rows'] = $result;

        $return['total'] = $counter[0]['count'];

        return $return;
    }

    /**
     * markForEdit Субсидии>Парцели>Парцели>Редактиране.
     *
     * @api-method markForEdit
     *
     * @param array $rpcParams
     *                         {
     *                         #item int farming
     *                         #item int year
     *                         #item int id
     *                         }
     *
     * @return array
     */
    public function markForEdit($rpcParams)
    {
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);
        $LayersController = new LayersController('Layers');

        if (isset($rpcParams['farming'], $rpcParams['year'])) {
            $options = [
                'farming' => $rpcParams['farming'],
                'year' => $rpcParams['year'],
                'group_id' => $this->User->GroupID,
                'layer_type' => 1,
            ];
            $tableName = $LayersController->getTableNameByParams($options);

            // check if table exists
            $tableExist = $UserDbController->getTableNameExist($tableName);
            if (!$tableExist) {
                return [
                    'rows' => [],
                    'total' => 0,
                ];
            }
        } else {
            return [
                'rows' => [],
                'total' => 0,
            ];
        }
        $options = [
            'tablename' => $tableName,
            'return' => [
                'culture', 'ST_ASTEXT(geom)', 'ST_Area(geom) as area_zp', 'dobivi', 'ekatte', 'id', 'isak_prc_uin', 'napoqvane', 'obrabotki', 'polivki', 'polzvatel', 'area_name', 'mestnost',
            ],
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $rpcParams['id']],
            ],
        ];
        $result = $UserDbPlotsController->getPlotData($options, false);

        return $result[0];
    }

    /**
     * saveEdit Субсидии>Парцели>Парцели>Редактиране>Запис
     *
     * @api-method saveEdit
     *
     * @param array $rpcParams
     *                         {
     *                         #item int    farming
     *                         #item int    year
     *                         #item int    id
     *                         #item string ekatte
     *                         #item string obrabotki
     *                         #item string dobivi
     *                         #item string napoqvane
     *                         #item string polivki
     *                         #item string polzvatel
     *                         #item string isak_prc_uin
     *                         #item string culture
     *                         #item string area_name
     *                         }
     *
     * @throws MTRpcException
     */
    public function saveEdit($rpcParams)
    {
        // init controllers
        $UserDbController = new UserDbController($this->User->Database);
        $LayersController = new LayersController('Layers');
        $UsersController = new UsersController('Users');

        $options['farming'] = $rpcParams['farming'];
        $options['year'] = $rpcParams['year'];
        $options['group_id'] = $this->User->GroupID;
        $options['layer_type'] = Config::LAYER_TYPE_ZP;

        if (0 == strlen($options['farming']) || 0 == strlen($options['year'])) {
            die();
        }

        $tableName = $LayersController->getTableNameByParams($options);
        if (!$tableName) {
            throw new MTRpcException('invalid_table_name', -33102);
        }

        $options['tablename'] = $tableName;
        $options['where']['id'] = $rpcParams['id'];
        $options['mainData'] = [
            'ekatte' => $rpcParams['ekatte'],
            'obrabotki' => $rpcParams['obrabotki'],
            'dobivi' => $rpcParams['dobivi'],
            'napoqvane' => $rpcParams['napoqvane'],
            'polivki' => $rpcParams['polivki'],
            'polzvatel' => $rpcParams['polzvatel'],
            'isak_prc_uin' => $rpcParams['isak_prc_uin'],
            'culture' => $rpcParams['culture'],
            'area_name' => $rpcParams['area_name'],
            'mestnost' => $rpcParams['mestnost'],
        ];

        $UserDbController->editItem($options);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options, [], 'Save Edit');
    }

    /**
     * Субсидии>Парцели>Парцели>Мултиредакция.
     *
     * @api-method saveMultiEdit
     *
     * @param array $rpcParams
     *                         {
     *                         #item int    farming
     *                         #item int    year
     *                         #item string ekatte
     *                         #item string culture
     *                         #item string isak_prc_uin
     *                         #item string newCulture
     *                         }
     *
     * @throws MTRpcException invalid_table_name (-33102)
     * @throws MTRpcException NO_ZPLOTS_FOUND (-33253)
     */
    public function saveMultiEdit($rpcParams)
    {
        $LayersController = new LayersController('Layers');
        $UsersController = new UsersController('Users');
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);
        $UserDbZPlotsController = new UserDbZPlotsController($this->User->Database);

        $options = [
            'farming' => $rpcParams['farming'],
            'year' => $rpcParams['year'],
            'group_id' => $this->User->GroupID,
            'layer_type' => Config::LAYER_TYPE_ZP,
        ];

        $tableName = $LayersController->getTableNameByParams($options);
        $tableExists = $UserDbController->getTableNameExist($tableName);

        if (!$tableExists) {
            throw new MTRpcException('invalid_table_name', -33102);
        }

        $options = [
            'tablename' => $tableName,
            'where' => [
                'ekatte' => ['column' => 'ekatte', 'compare' => '=', 'prefix' => 'p', 'value' => $rpcParams['ekatte']],
                'culture' => ['column' => 'culture', 'compare' => '=', 'prefix' => 'p', 'value' => $rpcParams['culture']],
                'isak_prc_uin' => ['column' => 'isak_prc_uin', 'compare' => '=', 'prefix' => 'p', 'value' => $rpcParams['isak_prc_uin']],
            ],
        ];
        $result = $UserDbPlotsController->getPlotData($options, false);
        $resultCount = count($result);
        if (empty($result)) {
            throw new MTRpcException('NO_ZPLOTS_FOUND', -33253);
        }

        $id_array = [];

        for ($i = 0; $i < $resultCount; $i++) {
            $id_array[] = $result[$i]['id'];
        }

        $options = [
            'tablename' => $tableName,
            'id_string' => implode(', ', $id_array),
            'where' => [
                'culture' => $rpcParams['culture'],
                'isak_prc_uin' => $rpcParams['isak_prc_uin'],
            ],
            'update' => [],
        ];

        if ('' != $rpcParams['culture']) {
            $options['update']['culture'] = $rpcParams['newCulture'];
        }

        if ('' != $rpcParams['mestnost']) {
            $options['update']['mestnost'] = $rpcParams['mestnost'];
        }

        $UserDbZPlotsController->ZPMultiEdit($options);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options, [], 'Multiedit performed');
    }
}
