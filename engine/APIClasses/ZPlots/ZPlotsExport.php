<?php

namespace TF\Engine\APIClasses\ZPlots;

use Prado\Prado;
use Prado\Web\Services\TRpcApiProvider;
use TF\Application\Common\Config;
use TF\Engine\Kernel\Export2XlsClass;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController;
use TF\Engine\Plugins\Core\UserDbZPlots\UserDbZPlotsController;

// Prado::using('Plugins.Core.Contracts.conf');
// Prado::using('Plugins.Core.Contracts.*');
// Prado::using('Plugins.Core.Layers.conf');
// Prado::using('Plugins.Core.Layers.*');
// Prado::using('Plugins.Core.Farming.conf');
// Prado::using('Plugins.Core.Farming.*');
// Prado::using('APIClasses.ZPlots.ZPReportKVSGrid');

/**
 * Creates files for export.
 *
 * @rpc-module ZPlots
 *
 * @rpc-service-id zplots-export
 *
 * @property UserDbController $UserDbController
 * @property LayersController $LayersController
 * @property UserDbPlotsController $UserDbPlotsController
 */
class ZPlotsExport extends TRpcApiProvider
{
    private $UserDbController = false;
    private $LayersController = false;
    private $UserDbPlotsController = false;
    private $UserDbZPlotsController = false;

    public function registerMethods()
    {
        return [
            'exportExcel' => ['method' => [$this, 'exportExcel'],
                'validators' => [
                    'rpcParams' => [
                        'farming' => 'validateInteger, validateRequired',
                        'year' => 'validateInteger, validateRequired',
                    ]]],
            'exportIntersectExcel' => ['method' => [$this, 'exportIntersectExcel'],
                'validators' => [
                    'rpcParams' => [
                        'farming' => 'validateInteger, validateRequired',
                        'year' => 'validateInteger, validateRequired',
                    ]]],
            'removeFile' => ['method' => [$this, 'removeFile']],
        ];
    }

    /**
     * exportExcel Субсидии>Парцели>Парцели>Експорт
     *
     * @api-method exportExcel
     *
     * @param array $rpcParams
     *                         {
     *                         #item int    farming
     *                         #item int    year
     *                         #item string ekatte
     *                         #item string culture
     *                         #item string isak_prc_uin
     *                         }
     *
     * @throws MTRPcException DATABASE_INVALID_TABLE_NAME -33102
     *
     * @return array|string excel file path
     */
    public function exportExcel($rpcParams)
    {
        $LayersController = new LayersController('Layers');
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);

        $options = [
            'farming' => $rpcParams['farming'],
            'year' => $rpcParams['year'],
            'group_id' => $this->User->GroupID,
            'layer_type' => Config::LAYER_TYPE_ZP,
        ];
        $tableName = $LayersController->getTableNameByParams($options);

        // check if table exists
        $tableExist = $UserDbController->getTableNameExist($tableName);
        if (!$tableExist) {
            throw new MTRPcException('DATABASE_INVALID_TABLE_NAME', -33102);
        }

        $options = [
            'tablename' => $tableName,
            'return' => [
                'isak_prc_uin', 'ekatte', 'culture', 'obrabotki', 'dobivi', 'napoqvane', 'polivki', 'polzvatel', 'polzvatel', 'ST_Area(geom) as area_zp, area_name',
            ],
            'where' => [
                'ekatte' => ['column' => 'ekatte', 'compare' => '=', 'prefix' => 'p', 'value' => $rpcParams['ekatte']],
                'culture' => ['column' => 'culture', 'compare' => '=', 'prefix' => 'p', 'value' => $rpcParams['culture']],
                'isak_prc_uin' => ['column' => 'isak_prc_uin', 'compare' => 'ILIKE', 'prefix' => 'p', 'value' => $rpcParams['isak_prc_uin']],
            ],
        ];

        $result = $UserDbPlotsController->getPlotData($options, false, false);
        $resultCount = count($result);
        if (0 != $resultCount) {
            for ($i = 0; $i < $resultCount; $i++) {
                $result[$i]['culture'] = trim($result[$i]['culture']);
                if (strlen($result[$i]['culture']) > 0) {
                    $result[$i]['culture'] = $GLOBALS['Farming']['crops'][$result[$i]['culture']]['crop_name'];
                }
                $result[$i]['area_zp'] = round($result[$i]['area_zp'] / 1000, 2);
            }
        } else {
            return [];
        }

        $date = date('Y-m-d-H-i-s');
        $name = 'parceli_' . $this->User->GroupID . '_' . $date;

        $path = PUBLIC_UPLOAD_RELATIVE_PATH . '/export/' . $name . '.xlsx';

        $return = [];
        $headers = [
            'isak_prc_uin' => 'ИСАК Номер',
            'ekatte' => 'ЕКАТТЕ',
            'culture' => 'Култура',
            'obrabotki' => 'Обработки',
            'dobivi' => 'Добиви',
            'napoqvane' => 'Напояване',
            'polivki' => 'Поливки',
            'polzvatel' => 'Ползвател',
            'area_zp' => 'Площ (дка)',
            'area_name' => 'Име на парцел',
        ];
        array_unshift($result, $headers);

        $export2Xls = new Export2XlsClass();
        $export2Xls->exportUrlPath($path, $result);
        $return['path'] = $path;
        $return['name'] = $name . '.xlsx';

        return $return;
    }

    /**
     * exportIntersectExcel Субсидии>Парцели>Парцели>Пресичане с имоти>Експорт
     *
     * @api-method exportIntersectExcel
     *
     * @param array $rpcParams
     *                         {
     *                         #item int    farming
     *                         #item int    year
     *                         #item string ekatte
     *                         #item string culture
     *                         #item string isak_prc_uin
     *                         }
     *
     * @return string excel file path
     */
    public function exportIntersectExcel($rpcParams)
    {
        $LayersController = new LayersController('Layers');
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbZPlotsController = new UserDbZPlotsController($this->User->Database);
        $ZPReportKVSGrid = new ZPReportKVSGrid($this->rpcServer);

        $result = $ZPReportKVSGrid->readZPReportKVSGrid($rpcParams);
        $resultsToExport = [];
        $rowsCount = count($result['rows']);
        if (0 != $rowsCount) {
            for ($i = 0; $i < $rowsCount; $i++) {
                $resultsToExport[$i] = [
                    'ekate' => $result['rows'][$i]['ekate'],
                    'kad_ident' => $result['rows'][$i]['kad_ident'],
                    'masiv' => $result['rows'][$i]['masiv'],
                    'number' => $result['rows'][$i]['number'],
                    'category' => $result['rows'][$i]['category'],
                    'area_type' => $result['rows'][$i]['area_type'],
                    'has_contracts' => $result['rows'][$i]['has_contracts'],
                    'area_kvs' => $result['rows'][$i]['area_kvs'],
                    'used_area' => $result['rows'][$i]['used_area'],
                    'area_intersect' => $result['rows'][$i]['area_intersect'],
                    'isak_prc_uin' => $result['rows'][$i]['isak_prc_uin'],
                ];
            }
        }

        $date = date('Y-m-d-H-i-s');
        $name = 'presichane_' . $this->User->GroupID . '_' . $date;

        $path = PUBLIC_UPLOAD_RELATIVE_PATH . '/export//' . $name . '.xlsx';

        if (!is_dir(PUBLIC_UPLOAD_EXPORT)) {
            mkdir(PUBLIC_UPLOAD_EXPORT, 0775);
        }

        $return = [];

        $headers = [
            'ekate' => 'ЕКАТТЕ',
            'kad_ident' => 'Идентификатор',
            'masiv' => 'Масив',
            'number' => 'Имот',
            'category' => 'Категория',
            'area_type' => 'НТП',
            'has_contracts' => 'Има договори',
            'area_kvs' => 'Обща площ(дка)',
            'used_area' => 'Използвана площ(дка)',
            'area_intersect' => 'Припокриване(дка)',
            'isak_prc_uin' => 'ИСАК номер',
        ];
        $export2Xls = new Export2XlsClass();
        $export2Xls->exportUrlPath($path, $resultsToExport, $headers);
        $return['path'] = $path;
        $return['name'] = $name . '.xlsx';

        return $return;
    }

    /**
     * @api-method removeFile
     *
     * @param array $rpcParams
     *                         {
     *                         #item string fileName
     *                         }
     */
    public function removeFile($fileName)
    {
        unlink(PUBLIC_UPLOAD_EXPORT . '/' . $fileName);
    }
}
