<?php

namespace TF\Engine\APIClasses\ZPlots;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbZPlots\UserDbZPlotsController;

/**
 * Returns report information.
 *
 * @rpc-module ZPlots
 *
 * @rpc-service-id zplots-report-grid
 *
 * @property LayersController $LayersController
 * @property UserDbController $UserDbController
 * @property UserDbZPlotsController $UserDbZPlotsController
 */
class ZPlotsReportGrid extends TRpcApiProvider
{
    private $LayersController;
    private $UserDbController = false;
    private $UserDbZPlotsController = false;

    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getZPlotsReportGrid'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * getZPlotsReportGrid Обща площ по култури.
     *
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item int    farming
     *                         #item int    year
     *                         #item string ekatte
     *                         #item string culture
     *                         #item string isak_prc_uin
     *                         }
     * @param string $page pagination parameter
     * @param string $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array
     */
    public function getZPlotsReportGrid(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $return = [
            'total' => 0,
            'rows' => [],
            'footer' => [],
        ];

        $LayersController = new LayersController('Layers');
        $UserDbZPlotsController = new UserDbZPlotsController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'farming' => $rpcParams['farming'],
            'year' => $rpcParams['year'],
            'group_id' => $this->User->GroupID,
            'layer_type' => 1,
        ];
        $tableName = $LayersController->getTableNameByParams($options);

        // check if table exists
        $tableExist = $UserDbController->getTableNameExist($tableName);
        if (!$tableExist) {
            return $return;
        }

        $options = [
            'tablename' => $tableName,
            'group' => 'culture',
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
            'return' => [
                'DISTINCT(culture) ,SUM(ST_Area(geom)) as area',
            ],
            'where' => [
                'ekatte' => ['column' => 'ekatte', 'compare' => '=', 'prefix' => 'p', 'value' => $rpcParams['ekatte']],
                'culture' => ['column' => 'culture', 'compare' => '=', 'prefix' => 'p', 'value' => $rpcParams['culture']],
                'isak_prc_uin' => ['column' => 'isak_prc_uin', 'compare' => 'ILIKE', 'prefix' => 'p', 'value' => $rpcParams['isak_prc_uin']],
            ],
        ];

        $counter = $UserDbZPlotsController->getZPlotAreaReport($options, true, false);
        if (0 == $counter[0]['count']) {
            return $return;
        }

        $results = $UserDbZPlotsController->getZPlotAreaReport($options, false, false);
        $resultsCount = count($results);

        // total variable
        $total_area = 0;

        for ($i = 0; $i < $resultsCount; $i++) {
            $results[$i]['culture'] = trim($results[$i]['culture']);
            // if culture is defined
            if (strlen($results[$i]['culture']) > 0) {
                $results[$i]['culture'] = $GLOBALS['Farming']['crops'][$results[$i]['culture']]['crop_name'];
            } else {
                $results[$i]['culture'] = '[Парцели с незададена култура]';
            }

            $results[$i]['area'] = number_format($results[$i]['area'] / 1000, 3, '.', '');
            $total_area += $results[$i]['area'];
        }

        $total_area = number_format($total_area, 3, '.', '');

        return [
            'total' => $counter[0]['count'],
            'rows' => $results,
            'footer' => [
                0 => [
                    'culture' => '<b>Общо:</b>',
                    'area' => '<b>' . $total_area . '</b>',
                ],
            ],
        ];
    }
}
