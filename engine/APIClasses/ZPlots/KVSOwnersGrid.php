<?php

namespace TF\Engine\APIClasses\ZPlots;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbContracts\UserDbContractsController;
use TF\Engine\Plugins\Core\UserDbOwners\UserDbOwnersController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Пресичане с имоти > Подробна информация > Собственици.
 *
 * @rpc-module ZPlots
 *
 * @rpc-service-id kvs-owners-datagrid
 *
 * @property UserDbOwnersController $UserDbOwnersController
 * @property UserDbContractsController $UserDbContractsController
 * @property UsersController $UsersController
 */
class KVSOwnersGrid extends TRpcApiProvider
{
    private $UserDbController;
    private $UserDbOwnersController = false;
    private $UserDbContractsController = false;
    private $UsersController = false;

    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getKVSOwnersGrid'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item int plot_id
     *                         #item int contract_id
     *                         #item string rep_egn
     *                         #item string eik
     *                         #item string company_name
     *                         #item string name
     *                         }
     * @param int|string $page pagination parameter
     * @param int|string $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array
     */
    public function getKVSOwnersGrid(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        // init all needed controllers
        $UserDbContractsController = new UserDbContractsController($this->User->Database);
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        if ($rpcParams['contract_id'] && (int)$rpcParams['contract_id'] && $rpcParams['plot_id'] && (int)$rpcParams['plot_id']) {
            $relation_id = $UserDbContractsController->getContractPlotRelationID($rpcParams['contract_id'], $rpcParams['plot_id']);

            if (!$relation_id) {
                return [
                    'rows' => [],
                    'total' => 0,
                ];
            }
            $options = [
                'offset' => ($page - 1) * $rows,
                'limit' => $rows,
                'sort' => $sort,
                'order' => $order,
                'return' => [
                    't.id', 'percent', 'company_name',
                    "name || ' ' || surname || ' ' || lastname as owner_names",
                    "rep_name || ' ' || rep_surname || ' ' || rep_lastname as rep_names",
                ],
                'where' => [
                    'rel_id' => ['column' => 'pc_rel_id', 'compare' => '=', 'prefix' => 'rel', 'value' => $relation_id],
                    'rep_name' => ['column' => 'rep_name', 'compare' => 'ILIKE', 'prefix' => 't', 'value' => $rpcParams['rep_name']],
                    'rep_egn' => ['column' => 'rep_egn', 'compare' => '=', 'prefix' => 't', 'value' => $rpcParams['rep_egn']],
                    'eik' => ['column' => 'eik', 'compare' => '=', 'prefix' => 't', 'value' => $rpcParams['eik']],
                    'company_name' => ['column' => 'company_name', 'compare' => 'ILIKE', 'prefix' => 't', 'value' => $rpcParams['company_name']],
                    'name' => ['column' => 'name', 'compare' => 'ILIKE', 'prefix' => 't', 'value' => $rpcParams['name']],
                ],
            ];
            $results = $UserDbOwnersController->getPlotOwnersData($options, false);
            $resultsCount = count($results);
            $return = [
                'rows' => [],
                'total' => 0,
            ];
            if (0 != $resultsCount) {
                for ($i = 0; $i < $resultsCount; $i++) {
                    if ('' == trim($results[$i]['owner_names'])) {
                        $results[$i]['owner_names'] = '-';
                    }
                    if ('' == trim($results[$i]['company_name'])) {
                        $results[$i]['company_name'] = '-';
                    }

                    $results[$i]['rat_ownage'] = $results[$i]['percent'] . '% (' . $UsersController->StringHelper->float2rat($results[$i]['percent'] / 100) . ')';
                }

                $return['rows'] = $results;
                $counter = $UserDbOwnersController->getPlotOwnersData($options, true);
                $return['total'] = $counter[0]['count'];

                return $return;
            }

            return [
                'rows' => [],
                'total' => 0,
            ];
        }

        return [
            'rows' => [],
            'total' => 0,
        ];
    }
}
