<?php

namespace TF\Engine\APIClasses\ZPlots;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController;

/**
 * Пресичане с имоти > Подробна информация > Договори.
 *
 * @rpc-module ZPlots
 *
 * @rpc-service-id kvs-contracts-datagrid
 *
 * @property FarmingController $FarmingController
 * @property UserDbPlotsController $UserDbPlotsController
 */
class KVSContractsGrid extends TRpcApiProvider
{
    private $FarmingController = false;
    private $UserDbController = false;
    private $UserDbPlotsController = false;

    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getKVSContractsGrid'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item int plot_id
     *                         #item string contract
     *                         #item timestamp date_from
     *                         #item timestamp date_to
     *                         }
     * @param int $page pagination parameter
     * @param int $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array
     */
    public function getKVSContractsGrid(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        if ($this->User->isGuest) {
            return [];
        }

        $FarmingController = new FarmingController('Farming');
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);

        $return = [];
        $id_array = [];

        $results = $UserDbPlotsController->getPlotsContractsRelations($rpcParams['plot_id']);
        $resultsCount = count($results);
        $options = [
            'return' => ['*', 'date(c_date) as c_converted_date', 'date(start_date) as converted_start_date', 'date(due_date) as converted_due_date'],
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
            'where' => [
                'contract' => ['column' => 'c_num', 'compare' => '=', 'value' => $rpcParams['contract']],
                'date_from' => ['column' => 'c_date', 'compare' => '>=', 'value' => $rpcParams['date_from']],
                'date_to' => ['column' => 'c_date', 'compare' => '>=', 'value' => $rpcParams['date_to']],
                'parent' => ['column' => 'parent_id', 'compare' => '=', 'value' => '0'],
            ],
        ];

        // forming the id array. it's required for IN
        if (0 != $resultsCount) {
            for ($i = 0; $i < $resultsCount; $i++) {
                $id_array[] = $results[$i]['contract_id'];
            }
            $id_string = implode(', ', $id_array);
        } else {
            $id_string = '0';
        }

        // changing the ID string to anti string if add grid is required
        if (isset($rpcParams['type']) && 'view' == $rpcParams['type']) {
            $options['id_string'] = $id_string;
        } elseif (isset($rpcParams['type']) && 'add' == $rpcParams['type']) {
            $options['anti_id_string'] = $id_string;
        }

        $returns = $UserDbPlotsController->getContractsDataForPlots($options);
        $counter = $UserDbPlotsController->getContractsDataForPlots($options, true);
        $returnsCount = count($returns);
        $id_array = [];
        $farming_array = [];
        $farming_data = [];
        if (0 == count($returns)) {
            return [
                'rows' => [],
                'total' => 0,
            ];
        }

        for ($i = 0; $i < $returnsCount; $i++) {
            if ($returns[$i]['is_annex']) {
                $returns[$i]['c_num'] .= ' (анекс)';
            }
            $returns[$i]['c_type'] = $returns[$i]['nm_usage_rights'];
            $returns[$i]['nm_usage_rights'] = $GLOBALS['Contracts']['ContractTypes'][$returns[$i]['nm_usage_rights']]['name'];
            $returns[$i]['start_date'] = $returns[$i]['converted_start_date'];
            $returns[$i]['c_date'] = $returns[$i]['c_converted_date'];
            if ('' == $returns[$i]['due_date']) {
                $returns[$i]['due_date'] = '-';
            } else {
                $returns[$i]['due_date'] = $returns[$i]['converted_due_date'];
            }
            $id_array[] = $returns[$i]['farming_id'];
        }

        $id_string = implode(', ', $id_array);
        $farming_data = $FarmingController->getFarmingItemsByIDString($id_string, $this->User->GroupID);
        $farmingCount = count($farming_data);
        if (0 != $farmingCount) {
            for ($i = 0; $i < $farmingCount; $i++) {
                $farming_array[$farming_data[$i]['id']] = $farming_data[$i];
            }
        }

        for ($i = 0; $i < $returnsCount; $i++) {
            $returns[$i]['farming'] = $farming_array[$returns[$i]['farming_id']]['name'];
        }

        $result['rows'] = $returns;
        $result['total'] = $counter[0]['count'];

        return $result;
    }
}
