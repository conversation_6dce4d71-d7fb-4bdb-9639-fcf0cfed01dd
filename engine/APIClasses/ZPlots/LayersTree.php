<?php

namespace TF\Engine\APIClasses\ZPlots;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

/**
 * Returns all layers separated by farmings.
 *
 * @rpc-module ZPlots
 *
 * @rpc-service-id zplots-layers-tree
 *
 * @property FarmingController $FarmingController
 */
class LayersTree extends TRpcApiProvider
{
    private $UserDbController = false;
    private $FarmingController = false;

    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getLayersTree']],
        ];
    }

    /**
     * @api-method read
     *
     * @return array
     */
    public function getLayersTree()
    {
        // init all needed controllers
        $UserDbController = new UserDbController($this->User->Database);
        $FarmingController = new FarmingController('Farming');

        // get all farmings
        $options = [
            'return' => ['id', 'name'],
            'where' => [
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'value' => $this->User->GroupID],
            ],
        ];
        $farming_results = $FarmingController->getFarmings($options);
        $farmingCount = count($farming_results);
        // create children array
        // this array will be the same for every farming
        $children_array = [];
        // add new data
        foreach ($GLOBALS['Farming']['years'] as $yearData) {
            $children_array[] = [
                'text' => $yearData['title'],
                'id' => $yearData['id'],
                'iconCls' => 'icon-tree-calendar',
            ];
        }

        $return = [];
        for ($i = 0; $i < $farmingCount; $i++) {
            $return[] = [
                'id' => $farming_results[$i]['id'],
                'text' => $farming_results[$i]['name'],
                'children' => $children_array,
                'state' => 'open',
            ];
        }

        return $return;
    }
}
