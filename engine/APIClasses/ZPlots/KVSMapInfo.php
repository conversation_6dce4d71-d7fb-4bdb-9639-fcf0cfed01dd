<?php

namespace TF\Engine\APIClasses\ZPlots;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\MTRpcException;

/**
 * Returns Plot ID of selected map area.
 *
 * @rpc-module ZPlots
 *
 * @rpc-service-id zp-map-info
 */
class KVSMapInfo extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getKVSMapInfo']],
        ];
    }

    /**
     * @api-method read
     *
     * @throws MTRpcException
     *
     * @return int id
     */
    public function getKVSMapInfo($rpcParams)
    {
        // getting the required plot ID
        $urlRequest = WMS_SERVER_INTERNAL
            . '?REQUEST=GetFeatureInfo'
            . '&EXCEPTIONS=application/vnd.ogc.se_xml'
            . '&VERSION=1.1.1'
            . '&MAP=' . WMS_MAP_PATH . $this->User->GroupID . '.map'
            . '&BBOX=' . $rpcParams['bbox']
            . '&X=' . $rpcParams['x']
            . '&Y=' . $rpcParams['y']
            . '&INFO_FORMAT=text/plain'
            . '&QUERY_LAYERS=layer_kvs'
            . '&LAYERS=layer_kvs'
            . '&FEATURE_COUNT=50'
            . '&SRS=EPSG:900913'
            . '&STYLES='
            . '&WIDTH=' . $rpcParams['width']
            . '&HEIGHT=' . $rpcParams['height'];

        $curl = curl_init($urlRequest);
        curl_setopt($curl, CURLOPT_FAILONERROR, true);
        curl_setopt($curl, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        $result = curl_exec($curl);

        $explode_result = explode(' ', $result);

        $plot_id = rtrim($explode_result[5], ':');
        if (!is_numeric($plot_id)) {
            throw new MTRpcException('MAP_EMPTY_AREA', -33056);
        }
        // returning the plot ID
        return $plot_id;
    }
}
