<?php

namespace TF\Engine\APIClasses\Plots;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController;

/**
 * Карта в подмодул Имоти.
 *
 * @rpc-module Plots
 *
 * @rpc-service-id plot-map
 */
class PlotMap extends TRpcApiProvider
{
    /**
     * Class Controllers
     * Define all class controllers as properties, which will be set, depending on the required method.
     */
    private $LayersController;
    private $UserDbController;
    private $UserDbPlotsController;
    private $FarmingController;

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readPlotsMapContent']],
            'initMapLayers' => ['method' => [$this, 'initMapLayers']],
            'initMap' => ['method' => [$this, 'initMap']],
            'contractInit' => ['method' => [$this, 'contractInit']],
            'masivInit' => ['method' => [$this, 'masivInit']],
            'getKvsExtent' => ['method' => [$this, 'getKvsExtent']],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item string bbox
     *                         #item double x
     *                         #item double y
     *                         #item int width
     *                         #item int height
     *                         }
     *
     * @throws MTRpcException
     *
     * @return array
     */
    public function readPlotsMapContent($rpcParams)
    {
        $return = [];

        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);

        if (!isset($rpcParams['request'])) {
            $urlRequest = WMS_SERVER_INTERNAL
                . '?REQUEST=GetFeatureInfo'
                . '&EXCEPTIONS=application/vnd.ogc.se_xml'
                . '&VERSION=1.1.1'
                . '&MAP=' . WMS_MAP_PATH . $this->User->GroupID . '.map'
                . '&BBOX=' . $rpcParams['bbox']
                . '&X=' . $rpcParams['x']
                . '&Y=' . $rpcParams['y']
                . '&INFO_FORMAT=text/plain'
                . '&QUERY_LAYERS=layer_kvs'
                . '&LAYERS=layer_kvs'
                . '&FEATURE_COUNT=50'
                . '&SRS=EPSG:900913'
                . '&STYLES='
                . '&WIDTH=' . $rpcParams['width']
                . '&HEIGHT=' . $rpcParams['height'];

            $curl = curl_init($urlRequest);
            curl_setopt($curl, CURLOPT_FAILONERROR, true);
            curl_setopt($curl, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
            $result = curl_exec($curl);
        }

        if (!isset($rpcParams['request'])) {
            $data = @explode(' ', $result);
            $return['id'] = str_replace(':', '', $data[5]);

            if (!is_numeric($return['id'])) {
                throw new MTRpcException('MAP_EMPTY_AREA', -33056);
            }

            if ($rpcParams['plot_info']) {
                $options['tablename'] = 'layer_kvs';
                $options['where'] = [
                    'gid' => ['column' => 'gid', 'compare' => '=', 'prefix' => 'p', 'value' => $return['id']],
                ];
                $result = $UserDbPlotsController->getPlotData($options, false);

                if (0 != count($result)) {
                    $result[0]['category'] = $result[0]['virtual_category_title'];
                    $result[0]['area_type'] = $result[0]['virtual_ntp_title'];
                    $return['plot_info'] = $result[0];
                }
            }
        }

        return $return;
    }

    /**
     * @api-method initMapLayers
     *
     * @return array
     */
    public function initMapLayers()
    {
        $LayersController = new LayersController('Layers');

        $options = [
            'return' => ['t.id', 't.table_name as name', 't.extent', 't.layer_type'],
            'sort' => 'id',
            'order' => 'DESC',
            'where' => [
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'prefix' => 't', 'value' => $this->User->GroupID],
                'layer_type' => ['column' => 'layer_type', 'compare' => '=', 'prefix' => 't', 'value' => 5],
            ],
        ];

        $result = $LayersController->getLayers($options);

        $result[0]['extent'] = str_replace(' ', ', ', $result[0]['extent']);

        $result[1]['extent'] = $result[0]['extent'];
        $result[1]['name'] = 'topic_kvs_layer';
        $result[1]['id'] = 1;

        return $result;
    }

    /**
     * [initMap description].
     *
     * @api-method initMap
     *
     * @param array $rpcParams
     *                         {
     *                         #item string    kad_ident
     *                         #item string    ekate
     *                         #item string    masiv
     *                         #item string    number
     *                         #item int       category
     *                         #item int       area_type
     *                         #item bool      is_edited
     *                         #item string    cnum
     *                         #item int       contract_type
     *                         #item int       farming
     *                         #item timestamp date_from
     *                         #item timestamp date_to
     *                         #item timestamp due_date_from
     *                         #item timestamp due_date_to
     *                         #item string    owner_name
     *                         #item string    owner_egn
     *                         #item string    rep_name
     *                         #item string    rep_egn
     *                         #item string    company_name
     *                         #item string    company_eik
     *                         #item int       contract_status
     *                         }
     *
     * @return array
     */
    public function initMap($rpcParams)
    {
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);
        $LayersController = new LayersController('Layers');
        $FarmingController = new FarmingController('Farming');

        date_default_timezone_set('Europe/Sofia');
        $currentDate = date('Y-m-d', time());

        $arrayHelper = $FarmingController->ArrayHelper;

        $options = [
            'tablename' => 'layer_kvs',
            'return' => [
                'kvs.geom', 'kvs.gid', 'kad_ident',
            ],
            'where' => [
                'kad_ident' => ['column' => 'kad_ident', 'compare' => '=', 'prefix' => 'kvs', 'value' => $rpcParams['kad_ident']],
                'ekate' => ['column' => 'ekate', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $arrayHelper->mapArrayElToStr($arrayHelper->filterEmptyStringArr($rpcParams['ekate']))],
                'masiv' => ['column' => 'masiv', 'compare' => '=', 'prefix' => 'kvs', 'value' => $rpcParams['masiv']],
                'number' => ['column' => 'number', 'compare' => '=', 'prefix' => 'kvs', 'value' => $rpcParams['number']],
                'category' => ['column' => 'category', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $arrayHelper->mapArrayElToStr($arrayHelper->filterEmptyStringArr($rpcParams['category']))],
                'area_type' => ['column' => 'area_type', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $arrayHelper->mapArrayElToStr($arrayHelper->filterEmptyStringArr($rpcParams['area_type']))],
                'is_edited' => ['column' => 'is_edited', 'compare' => '=', 'prefix' => 'kvs', 'value' => "{$rpcParams['is_edited']}"],

                // contracts filter
                'cnum' => ['column' => 'c_num', 'compare' => 'LIKE', 'prefix' => 'c', 'value' => $rpcParams['cnum']],
                'contract_type' => ['column' => 'nm_usage_rights', 'compare' => 'IN', 'prefix' => 'c', 'value' => $arrayHelper->filterEmptyStringArr($rpcParams['contract_type'])],
                'farming' => ['column' => 'farming_id', 'compare' => 'IN', 'prefix' => 'c', 'value' => $arrayHelper->filterEmptyStringArr($rpcParams['farming'])],
                'date_from' => ['column' => 'c_date', 'compare' => '>=', 'prefix' => 'c', 'value' => $rpcParams['date_from']],
                'date_to' => ['column' => 'c_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $rpcParams['date_to']],
                'due_date_from' => ['column' => 'due_date', 'compare' => '>=', 'prefix' => 'c', 'value' => $rpcParams['due_date_from']],
                'due_date_to' => ['column' => 'due_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $rpcParams['due_date_to']],

                // owners filter
                'owner_name' => ['column' => 'name', 'compare' => 'ILIKE', 'prefix' => 'o', 'value' => $rpcParams['owner_name']],
                'owner_egn' => ['column' => 'egn', 'compare' => '=', 'prefix' => 'o', 'value' => $rpcParams['owner_egn']],
                'rep_name' => ['column' => 'rep_name', 'compare' => 'ILIKE', 'prefix' => 'o_r', 'value' => $rpcParams['rep_name']],
                'rep_egn' => ['column' => 'rep_egn', 'compare' => '=', 'prefix' => 'o_r', 'value' => $rpcParams['rep_egn']],
                'company_name' => ['column' => 'company_name', 'compare' => 'ILIKE', 'prefix' => 'o', 'value' => $rpcParams['company_name']],
                'company_eik' => ['column' => 'eik', 'compare' => '=', 'prefix' => 'o', 'value' => $rpcParams['company_eik']],
            ],
        ];

        if (1 == $rpcParams['contract_status']) {
            $options['where']['active'] = ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'];
        }

        if (2 == $rpcParams['contract_status']) {
            $options['where']['due_date'] = ['column' => 'due_date', 'compare' => '>=', 'prefix' => 'c', 'value' => $currentDate];
        }

        if (3 == $rpcParams['contract_status']) {
            $options['where']['due_date'] = ['column' => 'due_date', 'compare' => '<', 'prefix' => 'c', 'value' => $currentDate];
        }

        $sqlString = $UserDbPlotsController->getDataForPlotsTree($options, false, true);

        $options['return'] = ['ST_Extent(kvs.geom) as extent'];
        $options['where']['ekate'] = ['column' => 'ekate', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $arrayHelper->filterEmptyStringArr($rpcParams['ekate'])];
        $options['where']['category'] = ['column' => 'category', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $arrayHelper->filterEmptyStringArr($rpcParams['category'])];
        $options['where']['area_type'] = ['column' => 'area_type', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $arrayHelper->filterEmptyStringArr($rpcParams['area_type'])];

        $maxextent = $UserDbPlotsController->getDataForPlotsTree($options, false, false);

        $query = "({$sqlString}) as subquery using unique gid using srid=32635";

        $options = [
            'return' => ['t.id', 't.table_name', 't.extent', 't.layer_type', 't.border_color', 't.color'],
            'sort' => 'id',
            'order' => 'DESC',
            'where' => [
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'prefix' => 't', 'value' => $this->User->GroupID],
                'layer_type' => ['column' => 'layer_type', 'compare' => '=', 'prefix' => 't', 'value' => 5],
            ],
        ];

        $result = $LayersController->getLayers($options);

        $data = [];

        $color1 = '000000';
        $color2 = 'ff0000';
        $data['layername'] = 'topic_kvs_layer';
        $data['maxextent'] = $result[0]['extent'];
        $data['host'] = DEFAULT_DB_HOST;
        $data['dbname'] = $this->User->Database;
        $data['username'] = DEFAULT_DB_USERNAME;
        $data['password'] = DEFAULT_DB_PASSWORD;
        $data['port'] = DEFAULT_DB_PORT;
        $data['query'] = $query;
        $data['gid'] = 'gid';
        $data['transparency'] = '100';
        $data['display_label'] = 1;
        $data['classes'][0]['name'] = $layers[$j]['table_name'];
        $data['classes'][0]['border_color'] = hexdec(substr($color1, 0, 2)) . ' ' . hexdec(substr($color1, 2, 2)) . ' ' . hexdec(substr($color1, 4, 2));
        $data['classes'][0]['color'] = hexdec(substr($color2, 0, 2)) . ' ' . hexdec(substr($color2, 2, 2)) . ' ' . hexdec(substr($color2, 4, 2));

        $hexColorArray[] = ['color' => $color2, 'name' => 'Филтрирани', 'iconCls' => 'no-background no-padding'];
        $ltext = $LayersController->StringHelper->loadTemplate($GLOBALS['Templates'][3]['template'], $data);

        @unlink(WMS_MAP_PATH . $this->User->GroupID . '_layer_kvs.map');
        $fp = fopen(WMS_MAP_PATH . $this->User->GroupID . '_layer_kvs.map', 'w');
        $res = fwrite($fp, $ltext);
        fclose($fp);

        $maxextent = $maxextent[0]['extent'];
        $maxextent = str_replace('BOX(', '', $maxextent);
        $maxextent = str_replace(')', '', $maxextent);
        $maxextent = str_replace(' ', ',', $maxextent);

        $returnData['colorarray'] = $hexColorArray;
        $returnData['extent'] = $maxextent;

        return $returnData;
    }

    /**
     * Returns the map extent for the selected EKATTE.
     *
     * @api-method getKvsExtent
     *
     * @param array $rpcParams
     *                         {
     *                         #item string ekate
     *                         }
     *
     * @return array
     */
    public function getKvsExtent($rpcParams)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $options = [
            'tablename' => $UserDbController->DbHandler->tableKVS,
            'return' => ['St_Extent(geom) as extent'],
            'where' => [
                'ekate' => ['column' => 'ekate', 'compare' => '=', 'value' => $rpcParams['ekate']],
            ],
        ];

        $results = $UserDbController->getItemsByParams($options, false, false);

        if (0 == count($results)) {
            return [];
        }

        $maxExtent = $results[0]['extent'];
        $maxExtent = str_replace('BOX(', '', $maxExtent);
        $maxExtent = str_replace(')', '', $maxExtent);
        $maxExtent = str_replace(',', ' ', $maxExtent);
        $maxExtent = str_replace(' ', ', ', $maxExtent);

        $return['extent'] = $maxExtent;

        return $return;
    }

    /**
     * [contractInit description].
     *
     * @api-method contractInit
     *
     * @return array
     */
    public function contractInit($rpcParams)
    {
        $LayersController = new LayersController('Layers');

        $options = [
            'return' => ['t.id', 't.table_name', 't.extent', 't.layer_type', 't.border_color', 't.color'],
            'sort' => 'id',
            'order' => 'DESC',
            'where' => [
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'prefix' => 't', 'value' => $this->User->GroupID],
                'layer_type' => ['column' => 'layer_type', 'compare' => '=', 'prefix' => 't', 'value' => 5],
            ],
        ];

        $result = $LayersController->getLayers($options);

        $color2 = '000000';
        $color1 = 'E94909';
        $arrClass[0]['name'] = 'contract_1';
        $arrClass[0]['expression'] = '1';
        $arrClass[0]['color'] = hexdec(substr($color1, 0, 2)) . ' ' . hexdec(substr($color1, 2, 2)) . ' ' . hexdec(substr($color1, 4, 2));
        $arrClass[0]['border_color'] = hexdec(substr($color2, 0, 2)) . ' ' . hexdec(substr($color2, 2, 2)) . ' ' . hexdec(substr($color2, 4, 2));
        $hexColorArray[] = ['color' => $color1, 'name' => 'Собствени', 'iconCls' => 'no-background no-padding'];

        $color2 = '000000';
        $color1 = '2B81E0';
        $arrClass[1]['name'] = 'contract_2';
        $arrClass[1]['expression'] = '2';
        $arrClass[1]['color'] = hexdec(substr($color1, 0, 2)) . ' ' . hexdec(substr($color1, 2, 2)) . ' ' . hexdec(substr($color1, 4, 2));
        $arrClass[1]['border_color'] = hexdec(substr($color2, 0, 2)) . ' ' . hexdec(substr($color2, 2, 2)) . ' ' . hexdec(substr($color2, 4, 2));
        $hexColorArray[] = ['color' => $color1, 'name' => 'Аренда', 'iconCls' => 'no-background no-padding'];

        $color2 = '000000';
        $color1 = '2EBE37';
        $arrClass[2]['name'] = 'contract_3';
        $arrClass[2]['expression'] = '3';
        $arrClass[2]['color'] = hexdec(substr($color1, 0, 2)) . ' ' . hexdec(substr($color1, 2, 2)) . ' ' . hexdec(substr($color1, 4, 2));
        $arrClass[2]['border_color'] = hexdec(substr($color2, 0, 2)) . ' ' . hexdec(substr($color2, 2, 2)) . ' ' . hexdec(substr($color2, 4, 2));
        $hexColorArray[] = ['color' => $color1, 'name' => 'Наем', 'iconCls' => 'no-background no-padding'];

        $color2 = '000000';
        $color1 = '9E207C';
        $arrClass[3]['name'] = 'contract_4';
        $arrClass[3]['expression'] = '4';
        $arrClass[3]['color'] = hexdec(substr($color1, 0, 2)) . ' ' . hexdec(substr($color1, 2, 2)) . ' ' . hexdec(substr($color1, 4, 2));
        $arrClass[3]['border_color'] = hexdec(substr($color2, 0, 2)) . ' ' . hexdec(substr($color2, 2, 2)) . ' ' . hexdec(substr($color2, 4, 2));
        $hexColorArray[] = ['color' => $color1, 'name' => 'Споразумение', 'iconCls' => 'no-background no-padding'];

        $color2 = '000000';
        $color1 = 'E07A86';
        $arrClass[4]['name'] = 'contract_5';
        $arrClass[4]['expression'] = '5';
        $arrClass[4]['color'] = hexdec(substr($color1, 0, 2)) . ' ' . hexdec(substr($color1, 2, 2)) . ' ' . hexdec(substr($color1, 4, 2));
        $arrClass[4]['border_color'] = hexdec(substr($color2, 0, 2)) . ' ' . hexdec(substr($color2, 2, 2)) . ' ' . hexdec(substr($color2, 4, 2));
        $hexColorArray[] = ['color' => $color1, 'name' => 'Съвместна обработка', 'iconCls' => 'no-background no-padding'];

        $color2 = '000000';
        $color1 = 'FFFF00';
        $arrClass[5]['name'] = 'contract_6';
        $arrClass[5]['expression'] = '6';
        $arrClass[5]['color'] = hexdec(substr($color1, 0, 2)) . ' ' . hexdec(substr($color1, 2, 2)) . ' ' . hexdec(substr($color1, 4, 2));
        $arrClass[5]['border_color'] = hexdec(substr($color2, 0, 2)) . ' ' . hexdec(substr($color2, 2, 2)) . ' ' . hexdec(substr($color2, 4, 2));
        $hexColorArray[] = ['color' => $color1, 'name' => 'Преотдадени', 'iconCls' => 'no-background no-padding'];

        $color2 = '000000';
        $arrClass[6]['name'] = 'contract_7';
        $arrClass[6]['expression'] = '7';
        $arrClass[6]['border_color'] = hexdec(substr($color2, 0, 2)) . ' ' . hexdec(substr($color2, 2, 2)) . ' ' . hexdec(substr($color2, 4, 2));
        $arrClass[6]['color'] = false;

        $color2 = '000000';
        $color1 = 'F1F1F1';
        $arrClass[7]['name'] = 'contract_10';
        $arrClass[7]['expression'] = '10';
        $arrClass[7]['color'] = hexdec(substr($color1, 0, 2)) . ' ' . hexdec(substr($color1, 2, 2)) . ' ' . hexdec(substr($color1, 4, 2));
        $arrClass[7]['border_color'] = hexdec(substr($color2, 0, 2)) . ' ' . hexdec(substr($color2, 2, 2)) . ' ' . hexdec(substr($color2, 4, 2));
        $hexColorArray[] = ['color' => $color1, 'name' => 'Идеални части', 'iconCls' => 'no-background no-padding', 'ownership_field' => true];

        $data['layername'] = 'topic_kvs_layer';
        $data['maxextent'] = $result[0]['extent'];
        $data['host'] = DEFAULT_DB_HOST;
        $data['dbname'] = $this->User->Database;
        $data['username'] = DEFAULT_DB_USERNAME;
        $data['password'] = DEFAULT_DB_PASSWORD;
        $data['port'] = DEFAULT_DB_PORT;

        $filtered_gids = $this->getFilteredPlots($rpcParams);

        $date = date('Y-m-d');
        $rpcParams['date_from'] = $rpcParams['date_from'] ? $rpcParams['date_from'] : '1900-01-01';
        $rpcParams['date_to'] = $rpcParams['date_to'] ? $rpcParams['date_to'] : '9999-12-31';
        $rpcParams['due_date_from'] = $rpcParams['start_date'] ? $rpcParams['start_date'] : $date;
        $rpcParams['due_date_to'] = $rpcParams['due_date'] ? $rpcParams['due_date'] : $date;

        $active = '';
        if (1 == $rpcParams['contract_status']) {
            $active = 'FALSE';
        }

        $status_condition = '';
        if (2 == $rpcParams['contract_status']) {
            $status_condition = ".due_date >= '{$date}'";
        }

        if (3 == $rpcParams['contract_status']) {
            $status_condition = ".due_date < '{$date}'";
        }

        $data['query'] = '
  			(SELECT
				kvs.gid,
				kvs.geom,
				(
					CASE
					WHEN kvs.gid IN (
						SELECT DISTINCT
							ON (pcr.plot_id) pcr.plot_id
						FROM
							su_subleases_plots_contracts_rel spcr
						JOIN su_contracts_plots_rel pcr ON (pcr. ID = spcr.pc_rel_id)
						JOIN su_contracts superC ON (superC. ID = spcr.sublease_id)
						WHERE TRUE';

        if ('FALSE' == $active) {
            $data['query'] .= ' AND superC.active = FALSE';
        } else {
            $data['query'] .= ' AND superC.active = TRUE';
        }

        $data['query'] .= " AND superC.c_date >= '{$rpcParams['date_from']}'
						AND superC.c_date <= '{$rpcParams['date_to']}'
						AND superC.due_date >= '{$rpcParams['due_date_from']}'
                        AND superC.start_date <= '{$rpcParams['due_date_to']}'
                        ";
        if ('' != $status_condition) {
            $data['query'] .= ' AND superC' . $status_condition;
        }

        $data['query'] .= '
					) THEN
						6
					ELSE
						C .nm_usage_rights
					END
				) AS nm_usage_rights,
				kvs.kad_ident
			FROM
				su_contracts C
			LEFT JOIN su_contracts A ON (
				A .parent_id = C . ID';

        if ('FALSE' == $active) {
            $data['query'] .= ' AND A.active = FALSE';
        }
        $data['query'] .= " AND A .c_date >= '{$rpcParams['date_from']}'
				AND A .c_date <= '{$rpcParams['date_to']}'
				AND A .due_date >= '{$rpcParams['due_date_from']}'
                AND A .start_date <= '{$rpcParams['due_date_to']}'
                ";
        if ('' != $status_condition) {
            $data['query'] .= ' AND A' . $status_condition;
        }
        $data['query'] .= "
			)
			INNER JOIN su_contracts_plots_rel cpr ON (
				cpr.contract_id = (
					CASE
					WHEN A . ID IS NULL THEN
						C . ID
					ELSE
						A . ID
					END
				)
			)
			LEFT JOIN layer_kvs kvs ON (cpr.plot_id = kvs.gid)
			WHERE
				(kvs.is_edited = FALSE)
			AND C .nm_usage_rights <> '1'
			AND C .nm_usage_rights <> '4'
			AND kvs.gid NOT IN (
				SELECT
					plot_id
				FROM
					su_sales_contracts sc
				LEFT JOIN su_sales_contracts_plots_rel scp ON (sc. ID = sales_contract_id)
				WHERE
					TRUE
				AND plot_id IS NOT NULL
			)
            AND C .c_date >= '{$rpcParams['date_from']}'
			AND C .c_date <= '{$rpcParams['date_to']}'
			AND C .due_date >= '{$rpcParams['due_date_from']}'
            AND C .start_date <= '{$rpcParams['due_date_to']}'";
        if ('' != $status_condition) {
            $data['query'] .= ' AND C' . $status_condition;
        }
        if ('FALSE' == $active) {
            $data['query'] .= ' AND (
                    C .active = FALSE
                    OR A .active = FALSE
                )';
        }

        $data['query'] .= " AND kvs.gid in ({$filtered_gids})
			UNION
			/*QUERY for ownership contracts 1*/
			SELECT
				kvs.gid,
				kvs.geom,
				(
					CASE
					WHEN kvs.gid IN (
						SELECT DISTINCT
							ON (pcr.plot_id) pcr.plot_id
						FROM
							su_subleases_plots_contracts_rel spcr
						JOIN su_contracts_plots_rel pcr ON (pcr. ID = spcr.pc_rel_id)
						JOIN su_contracts superC ON (superC. ID = spcr.sublease_id)
						WHERE TRUE";

        if ('FALSE' == $active) {
            $data['query'] .= ' AND superC.active = FALSE';
        } else {
            $data['query'] .= ' AND superC.active = TRUE';
        }

        $data['query'] .= " AND superC.c_date >= '{$rpcParams['date_from']}'
						AND superC.c_date <= '{$rpcParams['date_to']}'
						AND superC.due_date >= '{$rpcParams['due_date_from']}'
                        AND superC.start_date <= '{$rpcParams['due_date_to']}'
					) THEN
						6
					ELSE
						C .nm_usage_rights
					END
				) AS nm_usage_rights,
				kvs.kad_ident
			FROM
				su_contracts_plots_rel cpr
			LEFT JOIN su_contracts C ON (C . ID = cpr.contract_id)
			LEFT JOIN layer_kvs kvs ON (kvs.gid = cpr.plot_id)
			WHERE
				C .nm_usage_rights = 1
			AND C .c_date >= '{$rpcParams['date_from']}'
            AND C .c_date <= '{$rpcParams['date_to']}'
            AND C .start_date <= '{$rpcParams['due_date_to']}'
			AND cpr.annex_action = 'added'
			AND C .is_annex = 'false'
			AND cpr.plot_id NOT IN (
				SELECT
					plot_id
				FROM
					su_sales_contracts sc
				LEFT JOIN su_sales_contracts_plots_rel scpr ON (
					sc. ID = scpr.sales_contract_id
				)
				WHERE
					plot_id IS NOT NULL
                AND sc .start_date <= '{$rpcParams['due_date_to']}'
			)
            AND kvs.gid in ({$filtered_gids})
			UNION
		
			/* query for agreement contracts 4 */
			SELECT
				kvs.gid,
				kvs.geom,
				C .nm_usage_rights,
				kvs.kad_ident
			FROM
				su_contracts_plots_rel cpr
			LEFT JOIN su_contracts C ON (C . ID = cpr.contract_id)
			LEFT JOIN layer_kvs kvs ON (kvs.gid = cpr.plot_id)
			WHERE
				C .nm_usage_rights = 4
            AND C .due_date >= '{$rpcParams['due_date_from']}'
            AND C .start_date <= '{$rpcParams['due_date_to']}'";
        if ('' != $status_condition) {
            $data['query'] .= ' AND C' . $status_condition;
        }
        $data['query'] .= "
			AND cpr.annex_action = 'added'
			AND cpr.plot_id NOT IN (
				SELECT
					plot_id
				FROM
					su_sales_contracts sc
				LEFT JOIN su_sales_contracts_plots_rel scpr ON (
					sc. ID = scpr.sales_contract_id
				)
				WHERE
					plot_id IS NOT NULL
				AND sc.start_date >= '{$rpcParams['due_date_from']}'
                AND sc.start_date <= '{$rpcParams['due_date_to']}'
			)
            AND kvs.gid in ({$filtered_gids})";
        if ($rpcParams['include_ownership']) {
            $data['query'] .= " 
            /* query for plots with less than 100% owership */
                UNION
                (SELECT
                        kvs.gid,
                        kvs.geom,
                        10 as nm_usage_rights,
                        kvs.kad_ident
                    FROM
                        layer_kvs kvs
                    INNER JOIN
                      su_contracts_plots_rel cpr on kvs.gid = cpr.plot_id
                    LEFT JOIN su_contracts c on c.id = cpr.contract_id
                    where kvs.gid in ({$filtered_gids})";

            if ('FALSE' == $active) {
                $data['query'] .= ' AND c.active = FALSE';
            } else {
                $data['query'] .= ' AND c.active = TRUE';
            }

            $data['query'] .= " AND c.c_date >= '{$rpcParams['date_from']}'
					AND c.c_date <= '{$rpcParams['date_to']}'
					AND c.due_date >= '{$rpcParams['due_date_from']}'
                    AND c.start_date <= '{$rpcParams['due_date_to']}'
                    GROUP BY
                        kvs.gid
                    HAVING
                        round(sum(cpr.contract_area)::numeric,3) < round((case when document_area is null then ST_AREA(geom)/1000 else document_area end)::numeric,3)
                )";
        }
        $data['query'] .= ' ) as subquery using unique gid using srid=32635;';
        $data['gid'] = 'gid';
        $data['transparency'] = '100';
        $data['display_label'] = 1;
        $data['classitem'] = 'nm_usage_rights';
        $data['classes'] = $arrClass;

        $ltext = $LayersController->StringHelper->loadTemplate($GLOBALS['Templates'][3]['template'], $data);

        @unlink(WMS_MAP_PATH . $this->User->GroupID . '_layer_kvs.map');
        $fp = fopen(WMS_MAP_PATH . $this->User->GroupID . '_layer_kvs.map', 'w');
        $res = fwrite($fp, $ltext);
        fclose($fp);

        $returnData['colorarray'] = $hexColorArray;
        $returnData['extent'] = $this->getExtentForFilteredPlots($filtered_gids);

        return $returnData;
    }

    /**
     * [masivInit description].
     *
     * @api-method masivInit
     *
     * @return array
     */
    public function masivInit($rpcParams)
    {
        $LayersController = new LayersController('Layers');
        $UserDbController = new UserDbController($this->User->Database);
        $options = [
            'return' => ['t.id', 't.table_name', 't.extent', 't.layer_type', 't.border_color', 't.color'],
            'sort' => 'id',
            'order' => 'DESC',
            'where' => [
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'prefix' => 't', 'value' => $this->User->GroupID],
                'layer_type' => ['column' => 'layer_type', 'compare' => '=', 'prefix' => 't', 'value' => 5],
            ],
        ];

        $result = $LayersController->getLayers($options);

        $data = $UserDbController->getLayerColumnValues('layer_kvs', 'masiv');

        $filtered_gids = $this->getFilteredPlots($rpcParams);
        $exploded_gids = explode(',', $filtered_gids);
        $options = [
            'tablename' => $UserDbController->DbHandler->tableKVS,
            'return' => ['distinct(masiv) as masiv'],
            'where' => [
                'ekate' => ['column' => 'gid', 'compare' => 'IN', 'value' => $exploded_gids],
            ],
        ];

        $data = $UserDbController->getItemsByParams($options, false, false);
        $dataCount = count($data);
        $hexColorArray = [];
        if ($dataCount > 0) {
            for ($j = 0; $j < $dataCount; $j++) {
                if (null != $data[$j]['masiv']) {
                    $color1 = '000000';
                    $color2 = $LayersController->StringHelper->randomColorCode();
                    $arrClass[$j]['name'] = $data[$j]['masiv'];
                    $arrClass[$j]['expression'] = $data[$j]['masiv'];
                    $arrClass[$j]['border_color'] = hexdec(substr($color1, 0, 2)) . ' ' . hexdec(substr($color1, 2, 2)) . ' ' . hexdec(substr($color1, 4, 2));
                    $arrClass[$j]['color'] = hexdec(substr($color2, 0, 2)) . ' ' . hexdec(substr($color2, 2, 2)) . ' ' . hexdec(substr($color2, 4, 2));
                    $hexColorArray[] = ['color' => $color2, 'name' => $data[$j]['masiv'] . ' масив', 'iconCls' => 'no-background no-padding'];
                }
            }
        } else {
            $arrClass[0]['name'] = $result[0]['table_name'];
            $arrClass[0]['border_color'] = hexdec(substr($result[0]['border_color'], 0, 2)) . ' ' . hexdec(substr($result[0]['border_color'], 2, 2)) . ' ' . hexdec(substr($result[0]['border_color'], 4, 2));
            $arrClass[0]['color'] = hexdec(substr($result[0]['color'], 0, 2)) . ' ' . hexdec(substr($result[0]['color'], 2, 2)) . ' ' . hexdec(substr($result[0]['color'], 4, 2));
            $arrClass[0]['color_hex'] = $result[0]['color'];
        }

        $color1 = '000000';
        $color2 = 'ff0000';
        $data['layername'] = 'topic_kvs_layer';
        $data['maxextent'] = $result[0]['extent'];
        $data['host'] = DEFAULT_DB_HOST;
        $data['dbname'] = $this->User->Database;
        $data['username'] = DEFAULT_DB_USERNAME;
        $data['password'] = DEFAULT_DB_PASSWORD;
        $data['port'] = DEFAULT_DB_PORT;
        $data['query'] = "(select geom, masiv, gid, kad_ident from layer_kvs where gid in ({$filtered_gids})) as subquery using unique gid using srid=32635;";
        $data['gid'] = 'gid';
        $data['transparency'] = '100';
        $data['display_label'] = 1;
        $data['classitem'] = 'masiv';
        $data['classes'] = $arrClass;
        $ltext = $LayersController->StringHelper->loadTemplate($GLOBALS['Templates'][3]['template'], $data);

        @unlink(WMS_MAP_PATH . $this->User->GroupID . '_layer_kvs.map');
        $fp = fopen(WMS_MAP_PATH . $this->User->GroupID . '_layer_kvs.map', 'w');
        $res = fwrite($fp, $ltext);
        fclose($fp);

        $returnData['colorarray'] = $hexColorArray;
        $returnData['extent'] = $this->getExtentForFilteredPlots($filtered_gids);

        return $returnData;
    }

    private function getFilteredPlots($rpcParams)
    {
        $this->UserDbPlotsController = new UserDbPlotsController($this->User->Database);
        $this->FarmingController = new FarmingController('Farming');

        date_default_timezone_set('Europe/Sofia');
        $currentDate = date('Y-m-d', time());

        $arrayHelper = $this->FarmingController->ArrayHelper;

        $options = [
            'tablename' => 'layer_kvs',
            'return' => [
                'array_agg(DISTINCT(kvs.gid)) as gids',
            ],
            'where' => [
                'kad_ident' => ['column' => 'kad_ident', 'compare' => '=', 'prefix' => 'kvs', 'value' => $rpcParams['kad_ident']],
                'ekate' => ['column' => 'ekate', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $arrayHelper->filterEmptyStringArr($rpcParams['ekate'])],
                'masiv' => ['column' => 'masiv', 'compare' => '=', 'prefix' => 'kvs', 'value' => $rpcParams['masiv']],
                'number' => ['column' => 'number', 'compare' => '=', 'prefix' => 'kvs', 'value' => $rpcParams['number']],
                'category' => ['column' => 'category', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $arrayHelper->filterEmptyStringArr($rpcParams['category'])],
                'area_type' => ['column' => 'area_type', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $arrayHelper->filterEmptyStringArr($rpcParams['area_type'])],
                'is_edited' => ['column' => 'is_edited', 'compare' => '=', 'prefix' => 'kvs', 'value' => "{$rpcParams['is_edited']}"],

                // contracts filter
                'cnum' => ['column' => 'c_num', 'compare' => 'LIKE', 'prefix' => 'c', 'value' => $rpcParams['cnum']],
                'contract_type' => ['column' => 'nm_usage_rights', 'compare' => 'IN', 'prefix' => 'c', 'value' => $arrayHelper->filterEmptyStringArr($rpcParams['contract_type'])],
                'farming' => ['column' => 'farming_id', 'compare' => 'IN', 'prefix' => 'c', 'value' => $arrayHelper->filterEmptyStringArr($rpcParams['farming'])],
                'date_from' => ['column' => 'c_date', 'compare' => '>=', 'prefix' => 'c', 'value' => $rpcParams['date_from']],
                'date_to' => ['column' => 'c_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $rpcParams['date_to']],
                'due_date_from' => ['column' => 'due_date', 'compare' => '>=', 'prefix' => 'c', 'value' => $rpcParams['due_date_from']],
                'due_date_to' => ['column' => 'due_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $rpcParams['due_date_to']],

                // owners filter
                'owner_name' => ['column' => 'name', 'compare' => 'ILIKE', 'prefix' => 'o', 'value' => $rpcParams['owner_name']],
                'owner_egn' => ['column' => 'egn', 'compare' => '=', 'prefix' => 'o', 'value' => $rpcParams['owner_egn']],
                'rep_name' => ['column' => 'rep_name', 'compare' => 'ILIKE', 'prefix' => 'o_r', 'value' => $rpcParams['rep_name']],
                'rep_egn' => ['column' => 'rep_egn', 'compare' => '=', 'prefix' => 'o_r', 'value' => $rpcParams['rep_egn']],
                'company_name' => ['column' => 'company_name', 'compare' => 'ILIKE', 'prefix' => 'o', 'value' => $rpcParams['company_name']],
                'company_eik' => ['column' => 'eik', 'compare' => '=', 'prefix' => 'o', 'value' => $rpcParams['company_eik']],
            ],
        ];

        if (1 == $rpcParams['contract_status']) {
            $options['where']['active'] = ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'];
        }

        if (2 == $rpcParams['contract_status']) {
            $options['where']['due_date'] = ['column' => 'due_date', 'compare' => '>=', 'prefix' => 'c', 'value' => $currentDate];
        }

        if (3 == $rpcParams['contract_status']) {
            $options['where']['due_date'] = ['column' => 'due_date', 'compare' => '<', 'prefix' => 'c', 'value' => $currentDate];
        }

        $sqlString = $this->UserDbPlotsController->getDataForPlotsTree($options, false, false);

        return str_replace(['{', '}'], ['', ''], $sqlString[0]['gids']);
    }

    private function getExtentForFilteredPlots($filtered_gids)
    {
        $this->UserDbPlotsController = new UserDbPlotsController($this->User->Database);

        if (!is_array($filtered_gids)) {
            $filtered_gids = explode(',', $filtered_gids);
        }
        $options = [
            'tablename' => $this->UserDbPlotsController->DbHandler->tableKVS,
            'return' => ['st_extent(geom) as extent'],
            'where' => [
                'gid' => ['column' => 'gid', 'compare' => 'IN', 'value' => $filtered_gids],
            ],
        ];

        $extent = $this->UserDbPlotsController->getItemsByParams($options, false, false);

        $extent = $extent[0]['extent'];
        $extent = str_replace('BOX(', '', $extent);
        $extent = str_replace(')', '', $extent);

        return str_replace(',', ' ', $extent);
    }
}
