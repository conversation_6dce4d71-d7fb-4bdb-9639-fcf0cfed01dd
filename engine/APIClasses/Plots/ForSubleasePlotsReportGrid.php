<?php

namespace TF\Engine\APIClasses\Plots;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\Export2XlsClass;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDbAreaTypes\UserDbAreaTypesController;
use TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController;

/**
 *Справка "Свободна земя за отдаване под наем/аренда".
 *
 * @rpc-module Plots
 *
 * @rpc-service-id for-sublease-plots-report-grid
 */
class ForSubleasePlotsReportGrid extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getPlotsForSublease'],
                'validators' => [
                    'params' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'exportToExcelForSubleasePlotsReportData' => ['method' => [$this, 'exportToExcelForSubleasePlotsReportData'],
                'validators' => [
                    'data' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * Export To Excel For Sublease Plots Report Data.
     *
     * @param array $data
     *                    [
     *                    #items array filters
     *                    [
     *                    #item  timestamp report_date
     *                    #item  timestamp report_date_from
     *                    #item  timestamp report_date_as_of
     *                    #item  timestamp report_contract_date
     *                    #item  timestamp report_contract_date_to
     *                    #item  integer report_farming
     *                    #item  integer report_ekate
     *                    #item  integer report_ntp
     *                    #item  integer report_category
     *                    #item  string report_mestnost
     *                    #item  string report_irrigation
     *                    ]
     *                    ]
     * @param int $page
     * @param int $rows
     * @param ?string $sort
     * @param ?string $order
     *
     * @return array
     */
    public function exportToExcelForSubleasePlotsReportData(array $data, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        if (!$page && !$rows) {
            $data['export_all_rows'] = true;
        }

        $results = $this->getPlotsForSublease($data, $page, $rows, $sort, $order);

        $result = $results['rows'];

        unset($result[0]['attributes']);

        $result = $this->formatRowsForExport($result);

        $columns = [
            'Землище',
            'Идентификатор',
            'Местност',
            'Категория',
            'НТП',
            'Площ(дка)',
            'Обработваема площ (дка)',
            'Собственик',
            'Отдаден на',
            'ЕГН/ЕИК',
            'Договор №',
            'Нотар. акт №',
            'Том',
            'Дело',
            'Районен съд',
        ];

        $this->addTotals($result);

        $time = strtotime(date('Y-m-d H:i:s'));
        $fileName = 'sobstvena_zemq_svobodna_za_otdavane_' . $this->User->GroupID . '_' . $time . '.xlsx';
        $path = PUBLIC_UPLOAD_EXPORT . '/' . $this->User->GroupID . '/' . $fileName;

        $export2Xls = new Export2XlsClass();
        $filePath = $export2Xls->exportUrlPath($path, $result, $columns);

        return ['file_path' => $filePath, 'file_name' => $fileName];
    }

    /**
     * @api-method read
     *
     * @param array $params
     *                      [
     *                      #items array filters
     *                      [
     *                      #item  timestamp report_date
     *                      #item  timestamp report_date_from
     *                      #item  timestamp report_date_as_of
     *                      #item  timestamp report_contract_date
     *                      #item  timestamp report_contract_date_to
     *                      #item  integer report_farming
     *                      #item  integer report_ekate
     *                      #item  integer report_ntp
     *                      #item  integer report_category
     *                      #item  string report_mestnost
     *                      #item  string report_irrigation
     *                      ]
     *                      ]
     * @param int|string $page
     * @param int|string $rows
     * @param ?string $sort
     * @param ?string $order
     *
     * @return array
     */
    public function getPlotsForSublease(array $params, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $reportDateFrom = $params['filters']['report_date_from'];
        $reportDate = $params['filters']['report_date'];
        $reportDateAsOf = $params['filters']['report_date_as_of'];
        $reportContractDate = $params['filters']['report_contract_date'];
        $reportContractDateTo = $params['filters']['report_contract_date_to'];
        $reportFarming = $params['filters']['report_farming'];
        $reportEkate = $params['filters']['report_ekate'];
        $reportMasiv = $params['filters']['report_masiv'];
        $reportPlotNumber = $params['filters']['report_plot_number'];
        $reportKadIdent = $params['filters']['report_kad_ident'];
        $reportNtp = $params['filters']['report_ntp'];
        $reportCategory = $params['filters']['report_category'];
        $reportMestnost = $params['filters']['report_mestnost'];
        $reportIrrigation = null;
        if ('all' != $params['filters']['report_irrigation']) {
            $reportIrrigation = $params['filters']['report_irrigation'];
        }

        $FarmingController = new FarmingController('Farming');
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);

        $userFarmings = $FarmingController->getUserFarmings(true);
        $farmingIds = array_keys($userFarmings);

        if ($reportFarming && in_array((int) $reportFarming, $farmingIds)) {
            $farmingIds = [(int) $reportFarming];
        }

        // create the default return
        $return = [
            'rows' => [],
            'total' => 0,
            'footer' => [],
        ];

        // Вземане на всички contract-plot-relation id-та, за които периода на активност на договора за преотдаване обхваща периода на справката
        // също така началната дата на договора за собственост е преди началната дата на справката.
        $options = [
            'return' => ['DISTINCT(spc.pc_rel_id)'],
            'where' => [
                'contract_type' => ['column' => 'nm_usage_rights', 'compare' => '=', 'prefix' => 'c1', 'value' => 1],
                'active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c1', 'value' => 'true'],
                'sublease_active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'true'],
                'start_date' => ['column' => 'start_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $reportDate],
                'due_date' => ['column' => 'due_date', 'compare' => '>=', 'prefix' => 'c', 'value' => $reportDateFrom],
                'start_date1' => ['column' => 'start_date', 'compare' => '<=', 'prefix' => 'c1', 'value' => $reportDateFrom],
                'c_date' => ['column' => 'c_date', 'compare' => '>=', 'prefix' => 'c', 'value' => $reportContractDate],
                'c_date_to' => ['column' => 'c_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $reportContractDateTo],
                'subleased_all' => ['column' => 'pc.contract_area - spa.contract_area', 'compare' => '<=', 'value' => 0],
            ],
        ];

        if ($reportDateAsOf) {
            $options['where']['start_date'] = ['column' => 'start_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $reportDateAsOf];
            $options['where']['due_date'] = ['column' => 'due_date', 'compare' => '>=', 'prefix' => 'c', 'value' => $reportDateAsOf];
            $options['where']['start_date1'] = ['column' => 'start_date', 'compare' => '<=', 'prefix' => 'c1', 'value' => $reportDateAsOf];
        }

        $results = $UserDbPlotsController->getSubleasedPlotsReport($options, false, false);
        $gid_array = [];
        $resultsCount = count($results);
        for ($i = 0; $i < $resultsCount; $i++) {
            $gid_array[] = $results[$i]['pc_rel_id'];
        }

        // Вземане на всички contract-plot-relation id-та, които са продадени преди началото на справката
        $options = [
            'return' => ['DISTINCT(cpr.id) as pc_rel_id'],
            'where' => [
                'start_date' => ['column' => 'start_date', 'compare' => '<=', 'prefix' => 'sc', 'value' => $reportDate],
                'sold_all' => ['column' => 'scpr.contract_area - scpr.contract_area_for_sale', 'compare' => '<=', 'value' => 0],
            ],
        ];

        if ($reportDateAsOf) {
            $options['where']['start_date'] = ['column' => 'start_date', 'compare' => '<=', 'prefix' => 'sc', 'value' => $reportDateAsOf];
        }

        $results = $UserDbPlotsController->getSoldPlotsData($options, false, false);
        $resultsCount = count($results);
        for ($i = 0; $i < $resultsCount; $i++) {
            $gid_array[] = $results[$i]['pc_rel_id'];
        }

        $gid_array = array_filter(array_unique($gid_array));

        $options = [
            'custom_counter' => 'COUNT(DISTINCT(cpr_id))',
            'sort' => $sort,
            'order' => $order,
            'return' => [
                'kvs_gid as gid', 'virtual_ekatte_name as land', 'kad_ident', 'kvs_masiv as masiv',
                'COALESCE(mestnost, \'-\') as mestnost',
                'COALESCE(virtual_category_title, \'-\') as category',
                'virtual_ntp_title as area_type',
                '(data.contract_area - coalesce (sscpr_1.contract_area_for_sale, 0) - coalesce(sum(sspa.contract_area), 0)) as area',
                'data.c_id', 'data.c_num', "to_char(data.start_date,'DD.MM.YYYY') as start_date",
                'COALESCE(data.na_num, \'-\') as na_num',
                'COALESCE(data.tom, \'-\') as tom',
                'COALESCE(data.delo, \'-\') as delo',
                'COALESCE(data.court, \'-\') as court',
                'data.farming_id',
                'round(sum(COALESCE(kvs_allowable_area, allowable_area, 0))::numeric,3) as kvs_allowable_area',
            ],
            'where' => [
                'contract_type' => ['column' => 'nm_usage_rights', 'prefix' => 'data', 'compare' => '=', 'value' => 1],
                'is_sublease' => ['column' => 'is_sublease', 'prefix' => 'data', 'compare' => '=', 'value' => 'false'],
                'is_annnex' => ['column' => 'is_annex', 'prefix' => 'data',  'compare' => '=', 'value' => 'false'],
                'active' => ['column' => 'active', 'prefix' => 'data', 'compare' => '=', 'value' => 'true'],
                'start_date' => ['column' => 'start_date', 'prefix' => 'data', 'compare' => '<=', 'value' => $reportDate],
                'c_date' => ['column' => 'c_date', 'prefix' => 'data', 'compare' => '>=', 'value' => $reportContractDate],
                'c_date_to' => ['column' => 'c_date', 'prefix' => 'data', 'compare' => '<=', 'value' => $reportContractDateTo],
                'farming_id' => ['column' => 'farming_id', 'prefix' => 'data', 'compare' => 'IN', 'value' => $farmingIds],
            ],
            'pc_rel_anti_id_string' => implode(',', $gid_array),
            'group' => 'c_id,kvs_gid, virtual_ekatte_name, kvs_masiv, kad_ident, mestnost, virtual_category_title, virtual_ntp_title, data.contract_area, 
                        sscpr.contract_area_for_sale, sscpr_1.contract_area_for_sale, data.c_num, to_char(data.start_date, \'DD.MM.YYYY\'), 
                        data.na_num, data.tom, data.delo, data.court, data.farming_id',
            'having' => '(data.contract_area - coalesce (sscpr_1.contract_area_for_sale, 0) - coalesce(sum(sspa.contract_area), 0))::numeric(9,3) > 0',
            'joins' => [
                'left join su_sales_contracts_plots_rel sscpr on sscpr.pc_rel_id = data.cpr_id',
                'left join su_sales_contracts ssc on ssc.id = sscpr.sales_contract_id and ssc.start_date < data.start_date',
                'left join su_sales_contracts_plots_rel sscpr_1 on sscpr_1.sales_contract_id = ssc.id ' . ($reportDateAsOf ? ' and ssc.start_date <= \'' . $reportDateAsOf . '\' ' : '') . '',
                'left join su_subleases_plots_contracts_rel sspcr on sspcr.pc_rel_id = data.cpr_id',
            ],
        ];

        if ($reportDateAsOf) {
            $options['joins'][] = "left join su_contracts sc on sc.id = sspcr.sublease_id and sc.start_date <= '" . $reportDateAsOf . "' and sc.due_date >= '" . $reportDateAsOf . "'";
        } else {
            $options['joins'][] = 'left join su_contracts sc 
                on sc.id = sspcr.sublease_id 
                ' . ($reportDateFrom ? 'and sc.due_date >=\'' . $reportDateFrom . '\'' : '') . '
                ' . ($reportDate ? 'and sc.start_date <=\'' . $reportDate . '\'' : '');
        }
        $options['joins'][] = 'left join su_subleases_plots_area sspa on sspa.sublease_id = sc.id and sspa.plot_id = data.kvs_gid';

        if ($reportEkate) {
            $options['where']['ekate'] = ['column' => 'kvs_ekate', 'compare' => '=', 'value' => $reportEkate];
        }

        if ($reportNtp) {
            $UserDbAreaTypesController = new UserDbAreaTypesController($this->User->Database);
            $ntpFilter = $UserDbAreaTypesController->getNtpCodesWithAdditionalCodes([$reportNtp]);
            $options['where']['ntp'] = ['column' => 'area_type', 'compare' => 'IN', 'value' => $ntpFilter];
        }
        if ($reportCategory) {
            $categoryFilter = in_array($reportCategory, ['-1', '0', null]) ? ['-1', '0', null] : [$reportCategory];
            $options['where']['category'] = ['column' => 'category', 'compare' => 'IN', 'value' => $categoryFilter];
        }
        if ($reportMestnost) {
            $options['where']['mestnost'] = ['column' => 'mestnost', 'compare' => '=', 'value' => $reportMestnost];
        }

        if ($reportIrrigation) {
            $options['where']['irrigated_area'] = ['column' => 'irrigated_area', 'compare' => '=', 'value' => $reportIrrigation];
        }

        if ($reportDateAsOf) {
            $options['where']['start_date'] = ['column' => 'start_date', 'prefix' => 'data', 'compare' => '<=', 'value' => $reportDateAsOf];
            // HACK
            // Договорите за собственост имат due_date null, и ако се остави null договорите за собственост няма да се включат
            $options['where']['due_date'] = ['column' => "(CASE WHEN data.due_date IS NULL THEN '9999-12-31' ELSE data.due_date END)", 'compare' => '>=', 'value' => $reportDateAsOf];
        }

        if ($reportMasiv) {
            $options['where']['masiv'] = ['column' => 'masiv', 'compare' => '=', 'value' => $reportMasiv];
        }
        if ($reportPlotNumber) {
            $options['where']['number'] = ['column' => 'number', 'compare' => '=', 'value' => $reportPlotNumber];
        }
        if ($reportKadIdent) {
            $options['where']['kad_ident'] = ['column' => 'kad_ident', 'compare' => '=', 'value' => $reportKadIdent];
        }

        $results_total = $UserDbPlotsController->getFullPlotData($options, false, false);
        $totalCounter = count($results_total);

        if (0 == $totalCounter) {
            return $return;
        }

        $total_area = array_sum(array_column($results_total, 'area'));
        $kvsAllowableArea = array_sum(array_column($results_total, 'kvs_allowable_area'));

        $page = $page ?: 1;
        $rows = $rows ?: 30;

        if (array_key_exists('export_all_rows', $params) && true == $params['export_all_rows']) {
            // export all rows
        } else {
            $options['offset'] = ($page - 1) * $rows;
            $options['limit'] = $rows;
        }

        $results = $UserDbPlotsController->getFullPlotData($options, false, false);
        $resultsCount = count($results);
        $total_area_for_page = 0;
        $kvsAllowableAreaPerPage = 0;
        for ($i = 0; $i < $resultsCount; $i++) {
            $results[$i]['farming'] = $userFarmings[$results[$i]['farming_id']]['name'];
            $results[$i]['eik'] = $userFarmings[$results[$i]['farming_id']]['bulstat'];
            $total_area_for_page += $results[$i]['area'];
            $kvsAllowableAreaPerPage += $results[$i]['kvs_allowable_area'];
            $results[$i]['area'] = number_format($results[$i]['area'], 3, '.', '');

            if ($results[$i]['usable']) {
                $results[$i]['usable'] = 'Да';
            } else {
                $results[$i]['usable'] = 'Не';
            }
        }

        $return['rows'] = $results;
        $return['total'] = $totalCounter;
        $return['footer'] = [
            [
                'area_type' => '<b>Общо за стр.</b>',
                'area' => number_format($total_area_for_page, 3, '.', ''),
                'kvs_allowable_area' => number_format($kvsAllowableAreaPerPage, 3, '.', ''),
            ],
            [
                'area_type' => '<b>Общо</b>',
                'area' => number_format($total_area, 3, '.', ''),
                'kvs_allowable_area' => number_format($kvsAllowableArea, 3, '.', ''),
            ],
        ];

        return $return;
    }

    private function formatRowsForExport($rows)
    {
        $results = [];

        $rowsCount = count($rows);
        for ($i = 0; $i < $rowsCount; $i++) {
            $results[$i]['land'] = $rows[$i]['land'];
            $results[$i]['kad_ident'] = $rows[$i]['kad_ident'];
            $results[$i]['mestnost'] = $rows[$i]['mestnost'];
            $results[$i]['category'] = $rows[$i]['category'];
            $results[$i]['area_type'] = $rows[$i]['area_type'];
            $results[$i]['area'] = $rows[$i]['area'];
            $results[$i]['kvs_allowable_area'] = $rows[$i]['kvs_allowable_area'];
            $results[$i]['farming'] = $rows[$i]['farming'];
            $results[$i]['egn'] = $rows[$i]['egn'];
            $results[$i]['c_num'] = $rows[$i]['c_num'];
            $results[$i]['start_date'] = $rows[$i]['start_date'];
            $results[$i]['na_num'] = $rows[$i]['na_num'];
            $results[$i]['tom'] = $rows[$i]['tom'];
            $results[$i]['delo'] = $rows[$i]['delo'];
            $results[$i]['court'] = $rows[$i]['court'];
        }

        return $results;
    }

    private function addTotals(&$rows)
    {
        $i = count($rows);
        $rows[$i]['land'] = '';
        $rows[$i]['kad_ident'] = '';
        $rows[$i]['mestnost'] = '';
        $rows[$i]['category'] = '';
        $rows[$i]['area_type'] = '';
        $rows[$i]['area'] = array_sum(array_column($rows, 'area'));
        $rows[$i]['kvs_allowable_area'] = number_format(array_sum(array_column($rows, 'kvs_allowable_area')), 3);
        $rows[$i]['farming'] = '';
        $rows[$i]['egn'] = '';
        $rows[$i]['c_num'] = '';
        $rows[$i]['start_date'] = '';
        $rows[$i]['na_num'] = '';
        $rows[$i]['tom'] = '';
        $rows[$i]['delo'] = '';
        $rows[$i]['court'] = '';
    }
}
