<?php

namespace TF\Engine\APIClasses\Plots;

use PHPExcel_IOFactory;
use PHPExcel_Style_Alignment;
use PHPExcel_Style_Border;
use PHPExcel_Style_NumberFormat;
use Prado\Prado;
use Prado\Web\Services\TRpcApiProvider;
use SimpleXMLElement;
use TF\Application\Common\Config;
use TF\Engine\Kernel\Export2XlsClass;
use TF\Engine\Kernel\ExportToExcelClass;
use TF\Engine\Kernel\ExportWordDocClass;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbAgreements\UserDbAgreementsController;
use TF\Engine\Plugins\Core\UserDbAreaTypes\UserDbAreaTypesController;
use TF\Engine\Plugins\Core\UserDbPayments\UserDbPaymentsController;
use TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController;
use TF\Engine\Plugins\Core\UserDbSubleases\UserDbSubleasesController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Експорт на декларация.
 *
 * @rpc-module Plots
 *
 * @rpc-service-id plots-declaration-export
 *
 * @property UserDbController $UserDbController
 * @property UserDbPlotsController $UserDbPlotsController
 * @property UserDbAgreementsController $UserDbAgreementsController
 * @property UserDbSubleasesController $UserDbSubleasesController
 * @property LayersController $LayersController
 * @property FarmingController $FarmingController
 */
class DeclarationExport extends TRpcApiProvider
{
    /**
     * Class Controllers
     * Define all class controllers as properties, which will be set, depending on the required method.
     */
    private $UserDbController = false;
    private $UserDbPlotsController = false;
    private $UserDbAgreementsController = false;
    private $UserDbSubleasesController = false;
    private $LayersController = false;
    private $FarmingController = false;
    private $UsersController = false;
    private $isXlsOrCsvExport = false;

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'createDecl69' => ['method' => [$this, 'createDecl69'],
                'validators' => [
                    'data' => [
                        'year' => 'validateFarmingYear, validateRequired, validateNotNull',
                        'farming' => 'validateInteger, validateRequired, validateNotNull',
                        'ekate' => 'validateText, validateRequired, validateNotNull',
                    ],
                ]],
            'createDecl70' => ['method' => [$this, 'createDecl70'],
                'validators' => [
                    'data' => [
                        'year' => 'validateFarmingYear, validateRequired, validateNotNull',
                        'farming' => 'validateInteger, validateRequired, validateNotNull',
                    ],
                ]],
            'createAnketnaKarta' => ['method' => [$this, 'createAnketnaKarta'],
                'validators' => [
                    'data' => [
                        'year' => 'validateFarmingYear, validateRequired, validateNotNull',
                        'farming' => 'validateInteger, validateRequired, validateNotNull',
                        'ekate' => 'validateText, validateRequired, validateNotNull',
                    ],
                ]],
            'createCSVDecl69' => ['method' => [$this, 'createCSVDecl69'],
                'validators' => [
                    'data' => [
                        'year' => 'validateFarmingYear, validateRequired, validateNotNull',
                        'farming' => 'validateInteger, validateRequired, validateNotNull',
                        'ekate' => 'validateText, validateRequired, validateNotNull',
                    ],
                ]],
            'createCSVDecl70' => ['method' => [$this, 'createCSVDecl70'],
                'validators' => [
                    'data' => [
                        'year' => 'validateFarmingYear, validateRequired, validateNotNull',
                        'farming' => 'validateInteger, validateRequired, validateNotNull',
                        'ekate' => 'validateText, validateRequired, validateNotNull',
                    ],
                ]],
            'createCSVDeclPML' => ['method' => [$this, 'createCSVDeclPML'],
                'validators' => [
                    'data' => [
                        'year' => 'validateFarmingYear, validateRequired, validateNotNull',
                        'farming' => 'validateInteger, validateRequired, validateNotNull',
                    ],
                ]],
            'checkKMSAgreements' => ['method' => [$this, 'checkKMSAgreements']],
            'removeFile' => ['method' => [$this, 'removeFile']],
            'createDecl73XML' => ['method' => [$this, 'createDecl73XML'],
                'validators' => [
                    'data' => [
                        'year' => 'validateFarmingYear, validateRequired, validateNotNull',
                        'farming' => 'validateInteger, validateRequired, validateNotNull',
                        'with_dividends' => 'validateRequired',
                    ],
                ]],
            'createDecl73XLS' => ['method' => [$this, 'createDecl73XLS'],
                'validators' => [
                    'data' => [
                        'year' => 'validateFarmingYear, validateRequired, validateNotNull',
                        'farming' => 'validateInteger, validateRequired, validateNotNull',
                        'with_dividends' => 'validateRequired',
                    ],
                ]],
        ];
    }

    /**
     * Creates "Декларация 69" Word file for download.
     *
     * @api-method createDecl69
     *
     * @param array $data declaration data
     *                    {
     *                    #item int year
     *                    #item string ekate
     *                    #item int farming
     *                    #item string ntp
     *                    #item string from_date - c_date filter parameter
     *                    #item string to_date   - c_date filter parameter
     *                    #item number export_type - 8 for KK and 6 for KVS.
     *                    }
     * @param int $page rpc parameters
     * @param int $rows rpc parameters
     * @param string $sort rpc parameters
     * @param string $order rpc parameters
     *
     * @throws MTRpcException CANNOT_EXPORT_EMPTY_REPORT -33212
     * @throws MTRpcException VALIDATION_INVALID_SORTING_RULE -33013
     * @throws MTRpcException VALIDATION_INVALID_SORTING_ORDER -33014
     *
     * @return array link to the temp word file
     */
    public function createDecl69($data, $page = null, $rows = null, $sort = null, $order = null)
    {
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);
        $FarmingController = new FarmingController('Farming');

        $this->declarationValidators($sort, $order);

        $farming_year_text = $GLOBALS['Farming']['years'][$data['year']]['farming_year_short'];
        $results = $UserDbPlotsController->getChosenPlotsForDecl69($data);

        $document = [];

        if (0 == count($results)) {
            throw new MTRpcException('CANNOT_EXPORT_EMPTY_REPORT', -33212);
        }

        $resultsCount = count($results);
        $totalArea = 0;

        for ($i = 0; $i < $resultsCount; $i++) {
            $row = [];
            $plot_gid = $results[$i]['gid'];
            $row['masiv'] = $results[$i]['masiv'];
            $row['number'] = $results[$i]['number'];

            // create the kad number
            $row['kad_ident'] = $this->kadIdentFormatting($results[$i]['masiv'], $results[$i]['number'], $data['plot_number_format'], $data['has_zerro_lpad']);

            // create the area column
            $row['total_area'] = number_format($results[$i]['area'], 3, '.', '');
            $totalArea += (float)$row['total_area'];
            // create contract data field
            $docNum = $results[$i]['c_num'];
            if (1 == $results[$i]['nm_usage_rights']) {
                if ($results[$i]['na_num']) {
                    $docNum = $results[$i]['na_num'];
                }

                $row['contract_data'] = $docNum . ' / ' . strftime('%d.%m.%Y', strtotime($results[$i]['c_date']));
            } elseif (array_search($results[$i]['nm_usage_rights'], [2, 3, 5])) {
                if ($results[$i]['sv_num']) {
                    $docNum = $results[$i]['sv_num'];
                }

                $row['contract_data'] = $docNum . ' / ' . strftime('%d.%m.%Y', strtotime($results[$i]['c_date']));
            } else {
                $row['contract_data'] = '-';
            }

            $row['area_type'] = $results[$i]['area_type_code'];
            $row['include'] = $results[$i]['include'] ? 'X' : '-';
            $row['participate'] = $results[$i]['participate'] ? 'X' : '-';
            $row['white_spots'] = $results[$i]['white_spots'] ? 'X' : '-';

            $document[] = $row;
        }

        switch ($order) {
            case 'asc':
                $orderType = SORT_ASC;

                break;
            case 'desc':
                $orderType = SORT_DESC;

                break;
            default:
                $orderType = SORT_ASC;

                break;
        }

        if ('kad_ident' == $sort) {
            if (count($document) > 0) {
                foreach ($results as $key => $row) {
                    $ekate[$key] = $row['ekate'];
                    $masiv[$key] = $row['masiv'];
                    $number[$key] = $row['number'];
                }

                array_multisort($ekate, $orderType, $masiv, $orderType, $number, $orderType, $document);
            }
        } else {
            switch ($sort) {
                case 'subleases':
                    $sort = 'sublease_data';

                    break;
                case 'c_num':
                    $sort = 'contract_data';

                    break;
            }
            if (count($document) > 0) {
                $document = $FarmingController->ArrayHelper->sortResultArray($document, $sort, $order);
                foreach ($document as $key => $row) {
                    $mainSorter[$key] = $row[$sort];
                    $masiv[$key] = $row['masiv'];
                    $number[$key] = $row['number'];
                }
                array_multisort($mainSorter, $orderType, $masiv, $orderType, $number, $orderType, $document);
            }
        }

        $document[] = ['kad_ident' => 'Общо/дка', 'total_area' => number_format($totalArea, 3, '.', '')];

        $dataForExport = [];
        $dataForExport['plots'] = $document;
        $dataForExport['farming_year_text'] = $farming_year_text;

        $farming = $this->getFarmingData($data['farming']);

        $data['titlePageInfo']['owner_names'] = $data['titlePageInfo']['owner_names'] ? $data['titlePageInfo']['owner_names'] : $farming[0]['mol'];
        $data['titlePageInfo']['owner_egn'] = $data['titlePageInfo']['owner_egn'] ? $data['titlePageInfo']['owner_egn'] : $farming[0]['mol_egn'];
        $data['titlePageInfo']['company_name'] = $data['titlePageInfo']['company_name'] ? $data['titlePageInfo']['company_name'] : $farming[0]['company'];
        $data['titlePageInfo']['company_bulstat'] = $data['titlePageInfo']['company_bulstat'] ? $data['titlePageInfo']['company_bulstat'] : $farming[0]['bulstat'];

        $ekatteData = $this->getEkatteData($data['ekate']);

        $dataForExport['obshtina'] = $ekatteData[0]['obsht_name'];
        $dataForExport['ekatte_name'] = $ekatteData[0]['ekatte_name'];

        $ltext = $FarmingController->StringHelper->loadTemplate($GLOBALS['Templates'][43]['template'], $data);
        $ltext .= $FarmingController->StringHelper->loadTemplate($GLOBALS['Templates'][5]['template'], $dataForExport);

        $return = [];

        $date = date('Y-m-d-H-i-s');
        $fileName = 'decl69_' . $this->User->GroupID . '_' . $date;

        $exportWordDoc = new ExportWordDocClass();
        $return['word_file_location'] = $exportWordDoc->export($fileName, $ltext, true);
        $return['file_name'] = $fileName . '.doc';

        return $return;
    }

    /**
     * Creates "Декларация 70" Word file for download.
     *
     * @api-method createDecl70
     *
     * @param array $data declaration data
     *                    {
     *                    #item int year
     *                    #item string ekate
     *                    #item int farming
     *                    #item string ntp
     *                    #item string from_date - c_date filter parameter
     *                    #item string to_date   - c_date filter parameter
     *                    }
     * @param int $page rpc parameters
     * @param int $rows rpc parameters
     * @param string $sort rpc parameters
     * @param string $order rpc parameters
     *
     * @throws MTRpcException VALIDATION_INVALID_SORTING_RULE -33013
     * @throws MTRpcException VALIDATION_INVALID_SORTING_ORDER -33014
     * @throws MTRpcException CANNOT_EXPORT_EMPTY_REPORT -33212
     *
     * @return array link to the temp word file
     */
    public function createDecl70($data, $page = null, $rows = null, $sort = null, $order = null)
    {
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);
        $FarmingController = new FarmingController('Farming');
        $UsersController = new UsersController('Users');

        $this->declarationValidators($sort, $order);

        // options for plots-contracts query
        $year = $GLOBALS['Farming']['years'][$data['year']]['year'];
        $farming_year_text = $GLOBALS['Farming']['years'][$data['year']]['farming_year_short'];

        $data['excelExport'] = true;
        $results = $UserDbPlotsController->getChosenPlotsForDecl70($data);

        $plotsDa = [];
        $pda = 0;
        $plotsNe = [];
        $pne = 0;

        if (empty($results)) {
            throw new MTRpcException('CANNOT_EXPORT_EMPTY_REPORT', -33212);
        }

        $plotsDaTotalArea = 0;
        $plotsNeTotalArea = 0;

        for ($i = 0; $i < $resultsCount = count($results); $i++) {
            if ($results[$i]['sv_date']) {
                $results[$i]['sv_date'] = strftime('%d.%m.%Y', strtotime($results[$i]['sv_date']));
            }

            $period = (strtotime($results[$i]['due_date']) - strtotime($results[$i]['start_date']));
            if ($period < 0) {
                $period = 0;
            }

            // The area can't be bigger than document area
            if ($results[$i]['area'] > $results[$i]['document_area']) {
                $results[$i]['area'] = $results[$i]['document_area'];
            }

            if (false == $results[$i]['include']) {
                // create the kad number
                $plotsDa[$pda]['kad_ident'] = $this->kadIdentFormatting($results[$i]['masiv'], $results[$i]['number'], $data['plot_number_format'], $data['has_zerro_lpad']);

                // create the area column
                $plotsDa[$pda]['total_area'] = number_format($results[$i]['area'], 3, '.', '');
                $plotsDaTotalArea += $plotsDa[$pda]['total_area'];

                $plotsDa[$pda]['date_vp'] = implode('/', array_filter([$results[$i]['sv_num'], $results[$i]['sv_date']]));

                $plotsDa[$pda]['area_type'] = $results[$i]['area_type_code'];
                $plotsDa[$pda]['nm_usage_rights'] = $results[$i]['c_type'];
                if ($results[$i]['from_sublease'] && 3 == $results[$i]['nm_usage_rights']) {
                    $plotsDa[$pda]['nm_usage_rights'] = 'Пренаем';
                }
                if ($results[$i]['from_sublease'] && 2 == $results[$i]['nm_usage_rights']) {
                    $plotsDa[$pda]['nm_usage_rights'] = 'Преаренда';
                }
                $plotsDa[$pda]['date'] = $results[$i]['c_num'] . ' / ' . $results[$i]['c_date'];
                $plotsDa[$pda]['date_vs'] = $results[$i]['start_date'];
                $plotsDa[$pda]['period'] = round($period / 31104000, 0);
                $pda++;
            } else {
                // create the kad number
                $plotsNe[$pne]['kad_ident'] = $this->kadIdentFormatting($results[$i]['masiv'], $results[$i]['number'], $data['plot_number_format'], $data['has_zerro_lpad']);

                // create the area column
                $plotsNe[$pne]['total_area'] = number_format($results[$i]['area'], 3, '.', '');
                $plotsNeTotalArea += $plotsNe[$pne]['total_area'];

                $plotsNe[$pne]['date_vp'] = implode('/', array_filter([$results[$i]['sv_num'], $results[$i]['sv_date']]));

                $plotsNe[$pne]['area_type'] = $results[$i]['area_type_code'];
                $plotsNe[$pne]['nm_usage_rights'] = $results[$i]['c_type'];
                if ($results[$i]['from_sublease'] && 3 == $results[$i]['nm_usage_rights']) {
                    $plotsNe[$pda]['nm_usage_rights'] = 'Пренаем';
                }
                if ($results[$i]['from_sublease'] && 2 == $results[$i]['nm_usage_rights']) {
                    $plotsNe[$pda]['nm_usage_rights'] = 'Преаренда';
                }
                $plotsNe[$pne]['date'] = $results[$i]['c_num'] . ' / ' . $results[$i]['c_date'];
                $plotsNe[$pne]['date_vs'] = $results[$i]['start_date'];
                $plotsNe[$pne]['period'] = round($period / 31104000, 0);
                $pne++;
            } // end of else
        } // end of for

        switch ($order) {
            case 'asc':
                $orderType = SORT_ASC;

                break;
            case 'desc':
                $orderType = SORT_DESC;

                break;
            default:
                $orderType = SORT_ASC;

                break;
        }

        if ('kad_ident' == $sort) {
            if (count($plotsDa)) {
                foreach ($plotsDa as $key => $row) {
                    $ekateDa[$key] = $row['ekate'];
                    $masivDa[$key] = $row['masiv'];
                    $numberDa[$key] = $row['number'];
                }

                array_multisort($ekateDa, $orderType, $masivDa, $orderType, $numberDa, $orderType, $plotsDa);
            }

            if (count($plotsNe)) {
                foreach ($plotsNe as $key => $row) {
                    $ekateNe[$key] = $row['ekate'];
                    $masivNe[$key] = $row['masiv'];
                    $numberNe[$key] = $row['number'];
                }

                array_multisort($ekateNe, $orderType, $masivNe, $orderType, $numberNe, $orderType, $plotsNe);
            }
        } else {
            switch ($sort) {
                case 'c_num':
                    $sort = 'date';

                    break;
                case 'c.id':
                    $sort = 'id';

                    break;
            }

            if (count($plotsDa)) {
                $plotsDa = $FarmingController->ArrayHelper->sortResultArray($plotsDa, $sort, $order);
                foreach ($plotsDa as $key => $row) {
                    $mainSorterDa[$key] = $row[$sort];
                    $masivDa[$key] = $row['masiv'];
                    $numberDa[$key] = $row['number'];
                }
                array_multisort($mainSorterDa, $orderType, $masivDa, $orderType, $numberDa, $orderType, $plotsDa);
            }

            if (count($plotsNe)) {
                $plotsNe = $FarmingController->ArrayHelper->sortResultArray($plotsNe, $sort, $order);
                foreach ($plotsNe as $key => $row) {
                    $mainSorterNe[$key] = $row[$sort];
                    $masivNe[$key] = $row['masiv'];
                    $numberNe[$key] = $row['number'];
                }
                array_multisort($mainSorterNe, $orderType, $masivNe, $orderType, $numberNe, $orderType, $plotsNe);
            }
        }

        $exportData = [];
        $exportData['plotsNe'] = $plotsNe;
        $exportData['plotsNe'][] = ['kad_ident' => 'Общо/дка', 'total_area' => number_format($plotsNeTotalArea, 3, '.', '')];
        $exportData['plotsDa'] = $plotsDa;
        $exportData['plotsDa'][] = ['kad_ident' => 'Общо/дка', 'total_area' => number_format($plotsDaTotalArea, 3, '.', '')];

        $farming = $this->getFarmingData($data['farming']);

        $data['titlePageInfo']['owner_names'] = $data['titlePageInfo']['owner_names'] ? $data['titlePageInfo']['owner_names'] : $farming[0]['mol'];
        $data['titlePageInfo']['owner_egn'] = $data['titlePageInfo']['owner_egn'] ? $data['titlePageInfo']['owner_egn'] : $farming[0]['mol_egn'];
        $data['titlePageInfo']['company_name'] = $data['titlePageInfo']['company_name'] ? $data['titlePageInfo']['company_name'] : $farming[0]['company'];
        $data['titlePageInfo']['company_bulstat'] = $data['titlePageInfo']['company_bulstat'] ? $data['titlePageInfo']['company_bulstat'] : $farming[0]['bulstat'];

        $ekatteData = $this->getEkatteData($data['ekate']);

        $exportData['obshtina'] = $ekatteData[0]['obsht_name'];
        $exportData['ekatte_name'] = $ekatteData[0]['ekatte_name'];

        $exportData['farming_year_text'] = $farming_year_text;
        $ltext = $FarmingController->StringHelper->loadTemplate($GLOBALS['Templates'][44]['template'], $data);

        $ltext .= $FarmingController->StringHelper->loadTemplate($GLOBALS['Templates'][6]['template'], $exportData);
        $date = date('Y-m-d-H-i-s');
        $return = [];
        $fileName = 'zaiavlenie_70_' . $this->User->GroupID . '_' . $date;

        if ('declPML' === $data['exportType']) {
            $fileName = 'zaiavlenie_pml_' . $this->User->GroupID . '_' . $date;
        }

        $exportWordDoc = new ExportWordDocClass();
        $return['word_file_location'] = $exportWordDoc->export($fileName, $ltext, true);
        $return['file_name'] = $fileName . '.doc';

        return $return;
    }

    /**
     * Creates "Анкетна карта" xls file for download.
     *
     * @api-method createAnketnaKarta
     *
     * @param array $data declaration data
     *                    {
     *                    #item int year
     *                    #item string ekate
     *                    #item int farming
     *                    #item string ntp
     *                    #item string from_date - c_date filter parameter
     *                    #item string to_date   - c_date filter parameter
     *                    }
     * @param string $sort
     * @param string $order
     *
     * @throws MTRpcException VALIDATION_INVALID_SORTING_RULE -33013
     * @throws MTRpcException VALIDATION_INVALID_SORTING_ORDER -33014
     * @throws MTRpcException CANNOT_EXPORT_EMPTY_REPORT -33212
     *
     * @return array|void
     */
    public function createAnketnaKarta($data, $sort = null, $order = null)
    {
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);
        $UserDbAgreementsController = new UserDbAgreementsController($this->User->Database);
        $FarmingController = new FarmingController('Farming');

        $results = $UserDbPlotsController->getChosenPlotsForAnketnaKarta($data);

        /*
        Вземат се всички резултати, и се прехвърлят в нов масив където всеки имот фигурира по един път
        Ако имотът участва в повече договори, то след първоначалното добавяне на имота в крайния масив
        то само към площта по договор се добавя площта от договора, както и към правното основание на
        вече добавените договори се добавя и новия. Това се прави с цел да се сумира площта, с която
        участва даден имот във всички договори, както и да се вземе типът на всеки един от договорите,
        в които участва имотът. В случай, че сумарната площ по договор надвишава площта по документ
        на имота от системното КВС, то за площ на имота се взема площта по документ. В случай, че
        всички правни основания на всички договори, с които участва имотът са от един и същи тип
        напр. Наем, то в споразумението се записва правно основание - Наем, но ако има повече от един
        тип правно основание, то в споразумението се записва - Друго.
         */
        $resultGidArray = [];
        foreach ($results as $result) {
            if (!array_key_exists($result['gid'], $resultGidArray)) {
                $resultGidArray[$result['gid']]['area'] = $result['area'];
                $resultGidArray[$result['gid']]['document_area'] = $result['document_area'];
                $resultGidArray[$result['gid']]['number'] = $this->formatNumberForKKOrKVS($result['number'], $data['export_type']);
                $resultGidArray[$result['gid']]['masiv'] = $this->formatNumberForKKOrKVS($result['masiv'], $data['export_type']);
                $resultGidArray[$result['gid']]['area_type'] = $result['area_type'];
                $resultGidArray[$result['gid']]['used_area'] = $result['used_area'];
                $resultGidArray[$result['gid']]['nm_usage_rights'] = $result['nm_usage_rights'];
                $resultGidArray[$result['gid']]['irrigated_area'] = $result['irrigated_area'];
                $resultGidArray[$result['gid']]['agg_type'] = $result['agg_type'];
                $resultGidArray[$result['gid']]['area_type_category'] = $result['area_type_category'];
            } else {
                $resultGidArray[$result['gid']]['area'] += $result['area'];
                $resultGidArray[$result['gid']]['nm_usage_rights'] .= ', ' . $result['nm_usage_rights'];
            }
        }

        $gid_array = [];
        $karta = [];
        // creating an array that holds plot gid
        foreach ($resultGidArray as $key => $value) {
            // adding the element to the array
            $gid_array[] = $key;
            $plot_gid = $key;

            // filling the csv data
            if (4 == strlen($value['masiv'])) {
                $item['kad_ident'] = "'" . sprintf('%04d', $value['masiv']) . sprintf('%04d', $value['number']);
            } else {
                $item['kad_ident'] = "'" . sprintf('%03d', $value['masiv']) . sprintf('%03d', $value['number']);
            }

            $item['area_type'] = $value['area_type_category'] ? $value['area_type_category'] : $value['area_type'];

            if (4 == $value['nm_usage_rights']) {
                $item['nm_usage_rights'] = (1 == $value['agg_type']) ? 7 : 6;
            } elseif (5 == $value['nm_usage_rights']) {
                $item['nm_usage_rights'] = 4;
            } elseif (null != $value['nm_usage_rights'] && '' != $value['nm_usage_rights']) {
                $item['nm_usage_rights'] = $value['nm_usage_rights'];
            } else {
                $item['nm_usage_rights'] = 4;
            }

            /*
            Ако имотът участва в повече от един договор, то правните основания за всеки един договор се запазват
            като стринг, разделени със ", ", а ако участва само в един договор, то правното му основание е стринг
            с дължина 1 символ - напр. Собственост -> '1'
             */
            if (strlen($value['nm_usage_rights']) > 1) {
                $rights = array_unique(array_filter(explode(', ', $value['nm_usage_rights'])));

                // Ако уникалните правни основания са повече от 1, то се записва за правно основание "4" - Друго
                if (count($rights) > 1) {
                    $item['nm_usage_rights'] = 4;
                } else {
                    // Ако всички договори са от един тип, то се запазва тяхното правно основание и в анкетната карта
                    $item['nm_usage_rights'] = $rights[0];
                }
            }

            $item['area'] = number_format($value['area'] / 10, 4, '.', '');

            $item['irrigated_area'] = $value['irrigated_area'] ? $item['area'] : 0;
            $karta[] = $item;
        }

        switch ($order) {
            case 'asc':
                $orderType = SORT_ASC;

                break;
            case 'desc':
                $orderType = SORT_DESC;

                break;
            default:
                $orderType = SORT_ASC;

                break;
        }

        if ('kad_ident' == $sort) {
            if (count($karta) > 0) {
                foreach ($karta as $key => $row) {
                    $kad_ident[$key] = $row['kad_ident'];
                }

                $karta = $FarmingController->ArrayHelper->sortResultArray($karta, $orderType, $kad_ident);
            }
        } else {
            switch ($sort) {
                case 'c_type':
                    $sort = 'nm_usage_rights';

                    break;
                case 'c_num':
                    $sort = 'nm_usage_rights';

                    break;
            }
            if (count($karta) > 0) {
                foreach ($karta as $key => $row) {
                    $mainSorter[$key] = $row[$sort];
                }
                $karta = $FarmingController->ArrayHelper->sortResultArray($karta, $orderType, $mainSorter);
            }
        }

        $date = date('Y-m-d-H-i-s');
        $fileNameBase = str_replace('/', '_', 'anketna_karta_' . $this->User->UserID . '_' . $date);
        $fileNameXlsx = $fileNameBase . '.xlsx';
        $fileNameXls = $fileNameBase . '.xls';

        $path = PUBLIC_UPLOAD_BLANK . DIRECTORY_SEPARATOR . $this->User->GroupID;
        $filePathXlsx = $path . DIRECTORY_SEPARATOR . $fileNameXlsx;
        $filePathXls = $path . DIRECTORY_SEPARATOR . $fileNameXls;

        $exporter = new Export2XlsClass();
        $exporter->exportUrlPath($filePathXlsx, $karta, []);

        // Convert .xlsx to .xls
        $spreadsheet = PHPExcel_IOFactory::load($filePathXlsx);
        $writer = PHPExcel_IOFactory::createWriter($spreadsheet, 'Excel5');
        $writer->save($filePathXls);

        return [
            'file_path' => PUBLIC_UPLOAD_BLANKS_RELATIVE_PATH . $this->User->GroupID . DIRECTORY_SEPARATOR . $fileNameXls,
            'file_name' => $fileNameXls,
        ];
    }

    /**
     * Creates "Декларация 69" CSV file for download.
     *
     * @api-method createCSVDecl69
     *
     * @param array $data declaration data
     *                    {
     *                    #item int year
     *                    #item string ekate
     *                    #item int farming
     *                    #item string names
     *                    #item string egn
     *                    #item string address_ekate
     *                    #item string address
     *                    #item string names
     *                    #item string phone
     *                    #item string names
     *                    #item string bulstat
     *                    #item string ekate
     *                    #item string ntp
     *                    #item string from_date - c_date filter parameter
     *                    #item string to_date   - c_date filter parameter
     *                    #item boolean is_export_type_xls
     *                    }
     * @param int $page rpc parameters
     * @param int $rows rpc parameters
     * @param string $sort rpc parameters
     * @param string $order rpc parameters
     *
     * @throws MTRpcException VALIDATION_INVALID_SORTING_RULE -33013
     * @throws MTRpcException VALIDATION_INVALID_SORTING_ORDER -33014
     * @throws MTRpcException CANNOT_EXPORT_EMPTY_REPORT -33212
     *
     * @return array link to the temp word file
     */
    public function createCSVDecl69($data, $page = null, $rows = null, $sort = null, $order = null)
    {
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);
        $FarmingController = new FarmingController('Farming');
        $UserDbSubleasesController = new UserDbSubleasesController($this->User->Database);
        $this->cleanInputData($data);
        $this->isXlsOrCsvExport = true;
        // get year from config

        $results = $UserDbPlotsController->getChosenPlotsForDecl69($data);
        $resultsCount = count($results);
        if (0 == $resultsCount) {
            throw new MTRpcException('CANNOT_EXPORT_EMPTY_REPORT', -33212);
        }

        $document = [];

        // heading
        if (false == $data['is_export_type_xls']) {
            $document[0] = [
                'number' => iconv(mb_detect_encoding('No по ред'), 'cp1251', 'No по ред'),
                'kad_ident' => iconv(mb_detect_encoding('Имот No в КВС или КК'), 'cp1251', 'Имот No в КВС или КК'),
                'area' => iconv(mb_detect_encoding('Площ дка'), 'cp1251', 'Площ дка'),
                'area_type' => iconv(mb_detect_encoding('НТП код'), 'cp1251', 'НТП код'),
                'c_num' => iconv(mb_detect_encoding('Документ собственост No'), 'cp1251', 'Документ собственост No'),
                'c_date' => iconv(mb_detect_encoding('Документ собственост дата'), 'cp1251', 'Документ собственост дата'),
                'sub_type' => iconv(mb_detect_encoding('Вид договор код'), 'cp1251', 'Вид договор код'),
                'sub_num' => iconv(mb_detect_encoding('Договор No'), 'cp1251', 'Договор No'),
                'sub_date' => iconv(mb_detect_encoding('Договор дата'), 'cp1251', 'Договор дата'),
                'wishes' => iconv(mb_detect_encoding('Желание за участие код'), 'cp1251', 'Желание за участие код'),
                'names' => iconv(mb_detect_encoding('Декларатор три имена'), 'cp1251', 'Декларатор три имена'),
                'egn' => iconv(mb_detect_encoding('ЕГН/ЛНЧ'), 'cp1251', 'ЕГН/ЛНЧ'),
                'address_ekate' => iconv(mb_detect_encoding('Адрес гр.(с.)'), 'cp1251', 'Адрес гр.(с.)'),
                'address' => iconv(mb_detect_encoding('Адрес ж.к./ул. No и др.'), 'cp1251', 'Адрес ж.к./ул. No и др.'),
                'phone' => iconv(mb_detect_encoding('Телефон'), 'cp1251', 'Телефон'),
                'company' => iconv(mb_detect_encoding('Име на юридическо лице'), 'cp1251', 'Име на юридическо лице'),
                'bulstat' => iconv(mb_detect_encoding('ЕИК'), 'cp1251', 'ЕИК'),
                'ekate' => iconv(mb_detect_encoding('Землище EKATTE код'), 'cp1251', 'Землище EKATTE код'),
                'year' => iconv(mb_detect_encoding('Стопанска год.'), 'cp1251', 'Стопанска год.'),
                'date' => iconv(mb_detect_encoding('Декларация дата'), 'cp1251', 'Декларация дата'),
            ];
        } else {
            $document[0] = [
                'number' => 'No по ред',
                'kad_ident' => 'Имот No в КВС или КК',
                'area' => 'Площ дка',
                'area_type' => 'НТП код',
                'c_num' => 'Документ собственост No',
                'c_date' => 'Документ собственост дата',
                'sub_type' => 'Вид договор код',
                'sub_num' => 'Договор No',
                'sub_date' => 'Договор дата',
                'wishes' => 'Желание за участие код',
                'names' => 'Декларатор три имена',
                'egn' => 'ЕГН/ЛНЧ',
                'address_ekate' => 'Адрес гр.(с.)',
                'address' => 'Адрес ж.к./ул. No и др.',
                'phone' => 'Телефон',
                'company' => 'Име на юридическо лице',
                'bulstat' => 'ЕИК',
                'ekate' => 'Землище EKATTE код',
                'year' => 'Стопанска год.',
                'date' => 'Декларация дата',
            ];
        }

        $sortedResults = [];

        $resultsCount = count($results);
        for ($i = 0; $i < $resultsCount; $i++) {
            $row = [];
            // create the kad number
            $row['kad_ident'] = $this->kadIdentFormatting($results[$i]['masiv'], $results[$i]['number'], $data['plot_number_format'], $data['has_zerro_lpad']);
            // create the area column
            $row['area'] = number_format($results[$i]['area'], 3, '.', '');
            $row['area_type'] = $results[$i]['area_type_code'];

            if (1 == $results[$i]['nm_usage_rights']) {
                $docNum = $results[$i]['c_num'];
                if ($results[$i]['na_num']) {
                    $docNum = $results[$i]['na_num'];
                }
                $docDate = $results[$i]['c_date'];
            } elseif (in_array($results[$i]['nm_usage_rights'], [2, 3, 5], true)) {
                $docNum = $results[$i]['c_num'];
                if ($results[$i]['sv_num']) {
                    $docNum = $results[$i]['sv_num'];
                }
                $docDate = $results[$i]['c_date'];
            } else {
                $docNum = '';
                $docDate = '';
            }

            if (false == $data['is_export_type_xls']) {
                $row['c_num'] = iconv(mb_detect_encoding($docNum), 'cp1251', $docNum);
            } else {
                $row['c_num'] = $docNum;
            }
            $row['c_date'] = '' != $docDate ? strftime('%d.%m.%Y', strtotime($docDate)) : '';
            if (5 == $results[$i]['nm_usage_rights']) {
                $row['sub_type'] = 3;
            }
            // create the wishes column
            if (6 == $results[$i]['nm_usage_rights'] || 5 == $results[$i]['nm_usage_rights']) {
                $row['wishes'] = 4;
            } elseif ($results[$i]['include']) {
                $row['wishes'] = 1;
            } elseif ($results[$i]['participate']) {
                $row['wishes'] = 2;
            } elseif ($results[$i]['white_spots']) {
                $row['wishes'] = 3;
            } else {
                $row['wishes'] = '';
            }

            $row['names'] = '';
            $row['egn'] = '';
            $row['address_ekate'] = '';
            $row['address'] = '';
            $row['phone'] = '';
            $row['company'] = '';
            $row['bulstat'] = '';
            $row['ekate'] = '';
            $row['year'] = '';
            $row['date'] = '';

            $sortedResults[] = $row;
        }

        switch ($order) {
            case 'asc':
                $orderType = SORT_ASC;

                break;
            case 'desc':
                $orderType = SORT_DESC;

                break;
            default:
                $orderType = SORT_ASC;

                break;
        }

        if ('kad_ident' == $sort) {
            if (count($sortedResults) > 0) {
                foreach ($results as $key => $row) {
                    $ekate[$key] = $row['ekate'];
                    $masiv[$key] = $row['masiv'];
                    $number[$key] = $row['number'];
                }
                array_multisort($ekate, $orderType, $masiv, $orderType, $number, $orderType, $sortedResults);
            }
        } else {
            switch ($sort) {
                case 'subleases':
                    $sort = 'sublease_data';

                    break;
                case 'c_num':
                    $sort = 'contract_data';

                    break;
                case 'c.id':
                    $sort = 'id';

                    break;
            }
            if (count($sortedResults) > 0) {
                $sortedResults = $FarmingController->ArrayHelper->sortResultArray($sortedResults, $sort, $order);
                foreach ($sortedResults as $key => $row) {
                    $mainSorter[$key] = $row[$sort];
                    $masiv[$key] = $row['masiv'];
                    $number[$key] = $row['number'];
                }
                array_multisort($mainSorter, $orderType, $masiv, $orderType, $number, $orderType, $sortedResults);
            }
        }

        $farming = $this->getFarmingData($data['farming']);

        $name = 'D69_' . $data['ekate'] . '_' . (empty($data['bulstat']) ? $data['egn'] : $data['bulstat']);

        $data['names'] = $data['names'] ? $data['names'] : $farming[0]['mol'];
        $data['egn'] = $data['egn'] ? $data['egn'] : $farming[0]['mol_egn'];
        $data['company'] = $data['company'] ? $data['company'] : $farming[0]['company'];
        $data['bulstat'] = $data['bulstat'] ? $data['bulstat'] : $farming[0]['bulstat'];

        $row_count = 1;
        foreach ($sortedResults as $row) {
            $document[] = ['number' => $row_count] + $row;
            $row_count++;
        }
        if (false == $data['is_export_type_xls']) {
            $document[1]['names'] = iconv(mb_detect_encoding($data['names']), 'cp1251', $data['names']);
            $document[1]['egn'] = iconv(mb_detect_encoding($data['names']), 'cp1251', $data['egn']);
            $document[1]['address_ekate'] = iconv(mb_detect_encoding($data['names']), 'cp1251', $data['address_ekate']);
            $document[1]['address'] = iconv(mb_detect_encoding($data['names']), 'cp1251', $data['address']);
            $document[1]['phone'] = iconv(mb_detect_encoding($data['names']), 'cp1251', $data['phone']);
            $document[1]['company'] = iconv(mb_detect_encoding($data['names']), 'cp1251', $data['company']);
            $document[1]['bulstat'] = iconv(mb_detect_encoding($data['names']), 'cp1251', $data['bulstat']);
            $document[1]['ekate'] = iconv(mb_detect_encoding($data['names']), 'cp1251', $data['ekate']);
        } else {
            $document[1]['names'] = $data['names'];
            $document[1]['egn'] = $data['egn'];
            $document[1]['address_ekate'] = $data['address_ekate'];
            $document[1]['address'] = $data['address'];
            $document[1]['phone'] = $data['phone'];
            $document[1]['company'] = $data['company'];
            $document[1]['bulstat'] = $data['bulstat'];
            $document[1]['ekate'] = $data['ekate'];
        }

        $document[1]['year'] = $GLOBALS['Farming']['years'][$data['year']]['year'];
        $document[1]['date'] = date('d.m.Y');

        if (false == $data['is_export_type_xls']) {
            $name .= '.csv';

            $path = PUBLIC_UPLOAD_BLANK . '/' . $this->User->GroupID . '/';

            if (!is_dir($path)) {
                mkdir($path);
            }

            $fp = fopen($path . $name, 'wb');

            $lines_count = count($document);
            for ($i = 0; $i < $lines_count; $i++) {
                fputs($fp, implode(';', $document[$i]) . "\n");
            }

            fclose($fp);

            $return['file_path'] = PUBLIC_UPLOAD_RELATIVE_PATH . '/blanks/' . $this->User->GroupID . '/' . $name;
            $return['file_name'] = $name;
        } else {
            $headers = array_shift($document);

            $name .= '.xlsx';

            $path = PUBLIC_UPLOAD_EXPORT . '/' . $this->User->GroupID . '/';

            if (!is_dir($path)) {
                mkdir($path);
            }

            $excellExportOptions = [
                'noStyle' => true,
                'format' => [
                    'kad_ident' => [
                        'type' => PHPExcel_Style_NumberFormat::FORMAT_TEXT,
                    ],
                    'c_date' => [
                        'type' => 'date',
                        'value' => 'dd.mm.YYYY "г.";@',
                    ],
                    'date' => [
                        'type' => 'date',
                        'value' => 'dd.mm.YYYY "г.";@',
                    ],
                    'egn' => [
                        'type' => PHPExcel_Style_NumberFormat::FORMAT_TEXT,
                    ],
                    'address_ekate' => [
                        'type' => PHPExcel_Style_NumberFormat::FORMAT_TEXT,
                    ],
                    'phone' => [
                        'type' => PHPExcel_Style_NumberFormat::FORMAT_TEXT,
                    ],
                    'company' => [
                        'type' => PHPExcel_Style_NumberFormat::FORMAT_TEXT,
                    ],
                    'ekate' => [
                        'type' => PHPExcel_Style_NumberFormat::FORMAT_TEXT,
                    ],
                    'c_num' => [
                        'type' => PHPExcel_Style_NumberFormat::FORMAT_TEXT,
                    ],
                    'sub_num' => [
                        'type' => PHPExcel_Style_NumberFormat::FORMAT_TEXT,
                    ],
                    'names' => [
                        'type' => PHPExcel_Style_NumberFormat::FORMAT_TEXT,
                    ],
                    'address' => [
                        'type' => PHPExcel_Style_NumberFormat::FORMAT_TEXT,
                    ],
                    'bulstat' => [
                        'type' => PHPExcel_Style_NumberFormat::FORMAT_TEXT,
                    ],
                    'area' => [
                        'type' => 'number',
                        'value' => '#,##0.000',
                    ],
                    'number' => [
                        'type' => 'number',
                        'value' => PHPExcel_Style_NumberFormat::FORMAT_NUMBER,
                    ],
                    'wishes' => [
                        'type' => 'number',
                        'value' => PHPExcel_Style_NumberFormat::FORMAT_NUMBER,
                    ],
                    'area_type' => [
                        'type' => 'number',
                        'value' => PHPExcel_Style_NumberFormat::FORMAT_NUMBER,
                    ],
                    'sub_type' => [
                        'type' => 'number',
                        'value' => PHPExcel_Style_NumberFormat::FORMAT_NUMBER,
                    ],
                    'year' => [
                        'type' => 'number',
                        'value' => PHPExcel_Style_NumberFormat::FORMAT_NUMBER,
                    ],
                ],
            ];

            $exportExcel = new ExportToExcelClass();
            $exportExcel->export($document, $headers, [], 0, $excellExportOptions);
            $exportExcel->saveFile($path . $name);

            $return['file_path'] = PUBLIC_UPLOAD_EXPORT_RELATIVE_PATH . $this->User->GroupID . '/' . $name;
            $return['file_name'] = $name;
        }

        return $return;
    }

    /**
     * Creates "Декларация 70" CSV file for download.
     *
     * @api-method createCSVDecl70
     *
     * @param array $data declaration data
     *                    {
     *                    #item string names
     *                    #item string egn
     *                    #item string address_ekate
     *                    #item string names
     *                    #item string phone
     *                    #item string names
     *                    #item string bulstat
     *                    #item string ekate
     *                    #item array  ntp
     *                    #item string from_date - c_date filter parameter
     *                    #item string to_date   - c_date filter parameter
     *                    #item boolean is_export_type_xls
     *                    }
     * @param int $page rpc parameters
     * @param int $rows rpc parameters
     * @param string $sort rpc parameters
     * @param string $order rpc parameters
     *
     * @throws MTRpcException VALIDATION_INVALID_SORTING_RULE -33013
     * @throws MTRpcException VALIDATION_INVALID_SORTING_ORDER -33014
     * @throws MTRpcException CANNOT_EXPORT_EMPTY_REPORT -33212
     *
     * @return array link to the temp word file
     */
    public function createCSVDecl70($data, $page = null, $rows = null, $sort = null, $order = null)
    {
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);
        $FarmingController = new FarmingController('Farming');
        $this->cleanInputData($data);
        $this->isXlsOrCsvExport = true;

        $data['excelExport'] = true;
        $results = $UserDbPlotsController->getChosenPlotsForDecl70($data);

        if (0 == count($results)) {
            throw new MTRpcException('CANNOT_EXPORT_EMPTY_REPORT', -33212);
        }

        $document = [];

        // heading
        if (false == $data['is_export_type_xls']) {
            $document[0] = [
                'number' => iconv(mb_detect_encoding('No по ред'), 'cp1251', 'No по ред'),
                'wishes' => iconv(mb_detect_encoding('Желание за участие код'), 'cp1251', 'Желание за участие код'),
                'kad_ident' => iconv(mb_detect_encoding('Имот No в КВС или КК'), 'cp1251', 'Имот No в КВС или КК'),
                'plot_area' => iconv(mb_detect_encoding('Площ имот дка'), 'cp1251', 'Площ имот дка'),
                'area' => iconv(mb_detect_encoding('Ползвана площ дка'), 'cp1251', 'Ползвана площ дка'),
                'area_type' => iconv(mb_detect_encoding('НТП код'), 'cp1251', 'НТП код'),
                'nm_usage_rights' => iconv(mb_detect_encoding('Вид договор код'), 'cp1251', 'Вид договор код'),
                'c_num' => iconv(mb_detect_encoding('Договор No'), 'cp1251', 'Договор No'),
                'c_date' => iconv(mb_detect_encoding('Договор дата'), 'cp1251', 'Договор дата'),
                'start_date' => iconv(mb_detect_encoding('В сила от дата'), 'cp1251', 'В сила от дата'),
                'period' => iconv(mb_detect_encoding('Срок в бр. год.'), 'cp1251', 'Срок в бр. год.'),
                'names' => iconv(mb_detect_encoding('Заявител три имена'), 'cp1251', 'Заявител три имена'),
                'egn' => iconv(mb_detect_encoding('ЕГН/ЛНЧ'), 'cp1251', 'ЕГН/ЛНЧ'),
                'address_ekate' => iconv(mb_detect_encoding('Адрес гр.(с.)'), 'cp1251', 'Адрес гр.(с.)'),
                'address' => iconv(mb_detect_encoding('Адрес ж.к./ул. No и др.'), 'cp1251', 'Адрес ж.к./ул. No и др.'),
                'phone' => iconv(mb_detect_encoding('Телефон'), 'cp1251', 'Телефон'),
                'company' => iconv(mb_detect_encoding('Име на юридическо лице'), 'cp1251', 'Име на юридическо лице'),
                'bulstat' => iconv(mb_detect_encoding('ЕИК'), 'cp1251', 'ЕИК'),
                'ekate' => iconv(mb_detect_encoding('Землище EKATTE код'), 'cp1251', 'Землище EKATTE код'),
                'year' => iconv(mb_detect_encoding('Стопанска год.'), 'cp1251', 'Стопанска год.'),
                'date' => iconv(mb_detect_encoding('Дата заявление'), 'cp1251', 'Дата заявление'),
            ];
        } else {
            $document[0] = [
                'number' => 'No по ред',
                'wishes' => 'Желание за участие код',
                'kad_ident' => 'Имот No в КВС или КК',
                'plot_area' => 'Площ имот дка',
                'area' => 'Ползвана площ дка',
                'area_type' => 'НТП код',
                'nm_usage_rights' => 'Вид договор код',
                'c_num' => 'Договор No',
                'c_date' => 'Договор дата',
                'start_date' => 'В сила от дата',
                'period' => 'Срок в бр. год.',
                'names' => 'Заявител три имена',
                'egn' => 'ЕГН/ЛНЧ',
                'address_ekate' => 'Адрес гр.(с.)',
                'address' => 'Адрес ж.к./ул. No и др.',
                'phone' => 'Телефон',
                'company' => 'Име на юридическо лице',
                'bulstat' => 'ЕИК',
                'ekate' => 'Землище EKATTE код',
                'year' => 'Стопанска год.',
                'date' => 'Дата заявление',
            ];
        }

        $sortedResults = [];
        $results_count = count($results);

        for ($i = 0; $i < $results_count; $i++) {
            $row = [];
            if ($results[$i]['participate']) {
                $row['wishes'] = 1;
            } elseif ($results[$i]['include']) {
                $row['wishes'] = 2;
            } else {
                $row['wishes'] = '';
            }

            // create the kad number
            $row['kad_ident'] = $this->kadIdentFormatting($results[$i]['masiv'], $results[$i]['number'], $data['plot_number_format'], $data['has_zerro_lpad']);

            // The area can't be bigger than document area
            if ($results[$i]['area'] > $results[$i]['plot_area']) {
                $results[$i]['area'] = $results[$i]['plot_area'];
            }

            $row['plot_area'] = $results[$i]['plot_area'];
            $row['area'] = $results[$i]['area'];

            if ($row['contract_area'] == $row['document_area']) {
                $row['contract_area'] = '';
            }

            $row['area_type'] = $results[$i]['area_type_code'];
            $isPlotSubleased = $results[$i]['from_sublease'];

            if (Config::CONTRACT_TYPE_JOINT_PROCESSING == $results[$i]['nm_usage_rights']) {
                $row['nm_usage_rights'] = Config::CONTRACT_TYPE_RENT;
            } elseif ((int)$results[$i]['nm_usage_rights'] > 1) {
                if (Config::CONTRACT_TYPE_LEASE == (int)$results[$i]['nm_usage_rights'] && ($results[$i]['is_declaration_subleased'] || $isPlotSubleased)) {
                    $row['nm_usage_rights'] = Config::CONTRACT_TYPE_AGREEMENT;
                } elseif (Config::CONTRACT_TYPE_RENT == (int)$results[$i]['nm_usage_rights'] && ($results[$i]['is_declaration_subleased'] || $isPlotSubleased)) {
                    $row['nm_usage_rights'] = Config::CONTRACT_TYPE_JOINT_PROCESSING;
                } else {
                    $row['nm_usage_rights'] = $results[$i]['nm_usage_rights'] - 1;
                }
            } else {
                $row['nm_usage_rights'] = '';
            }
            if (false == $data['is_export_type_xls']) {
                $row['c_num'] = iconv(mb_detect_encoding($results[$i]['c_num']), 'cp1251', $results[$i]['c_num']);
            } else {
                $row['c_num'] = $results[$i]['c_num'];
            }
            $row['c_date'] = $results[$i]['c_date'];

            if ($results[$i]['start_date']) {
                $row['start_date'] = $results[$i]['start_date'];
            } else {
                $row['start_date'] = '';
            }

            if ($results[$i]['start_date'] && $results[$i]['due_date']) {
                $results[$i]['due_date'] = $results[$i]['annex_due_date'] ? $results[$i]['annex_due_date'] : $results[$i]['due_date'];
                $period = (strtotime($results[$i]['due_date']) - strtotime($results[$i]['start_date']));
                $row['period'] = round($period / 31104000, 0);
            } else {
                $row['period'] = 0;
            }

            $row['names'] = '';
            $row['egn'] = '';
            $row['address_ekate'] = '';
            $row['address'] = '';
            $row['phone'] = '';
            $row['company'] = '';
            $row['bulstat'] = '';
            $row['ekate'] = '';
            $row['year'] = '';
            $row['date'] = '';

            $sortedResults[] = $row;
        }

        switch ($order) {
            case 'asc':
                $orderType = SORT_ASC;

                break;
            case 'desc':
                $orderType = SORT_DESC;

                break;
            default:
                $orderType = SORT_ASC;

                break;
        }

        if ('kad_ident' == $sort) {
            if (count($sortedResults) > 0) {
                foreach ($results as $key => $row) {
                    $ekate[$key] = $row['ekate'];
                    $masiv[$key] = $row['masiv'];
                    $number[$key] = $row['number'];
                }
                array_multisort($ekate, $orderType, $masiv, $orderType, $number, $orderType, $sortedResults);
            }
        } else {
            switch ($sort) {
                case 'subleases':
                    $sort = 'sublease_data';

                    break;
                case 'c_num':
                    $sort = 'contract_data';

                    break;
                case 'c.id':
                    $sort = 'id';

                    break;
            }
            if (count($sortedResults) > 0) {
                $sortedResults = $FarmingController->ArrayHelper->sortResultArray($sortedResults, $sort, $order);
                foreach ($sortedResults as $key => $row) {
                    $mainSorter[$key] = $row[$sort];
                    $masiv[$key] = $row['masiv'];
                    $number[$key] = $row['number'];
                }
                array_multisort($mainSorter, $orderType, $masiv, $orderType, $number, $orderType, $sortedResults);
            }
        }

        $farming = $this->getFarmingData($data['farming']);

        $name = 'Z70_' . $data['ekate'] . '_' . (empty($data['bulstat']) ? $data['egn'] : $data['bulstat']);

        $data['names'] = $data['names'] ? $data['names'] : $farming[0]['mol'];
        $data['egn'] = $data['egn'] ? $data['egn'] : $farming[0]['mol_egn'];
        $data['company'] = $data['company'] ? $data['company'] : $farming[0]['company'];
        $data['bulstat'] = $data['bulstat'] ? $data['bulstat'] : $farming[0]['bulstat'];
        $row_count = 1;
        foreach ($sortedResults as $row) {
            $document[] = ['number' => $row_count] + $row;
            $row_count++;
        }

        // The columns should be displayed in one row as per https://technofarm.atlassian.net/browse/TS-6422
        if (false == $data['is_export_type_xls']) {
            $document[1]['names'] = iconv(mb_detect_encoding($data['names']), 'cp1251', $data['names']);
            $document[1]['egn'] = iconv(mb_detect_encoding($data['names']), 'cp1251', $data['egn']);
            $document[1]['address_ekate'] = iconv(mb_detect_encoding($data['names']), 'cp1251', $data['address_ekate']);
            $document[1]['address'] = iconv(mb_detect_encoding($data['names']), 'cp1251', $data['address']);
            $document[1]['phone'] = iconv(mb_detect_encoding($data['names']), 'cp1251', $data['phone']);
            $document[1]['company'] = iconv(mb_detect_encoding($data['names']), 'cp1251', $data['company']);
            $document[1]['bulstat'] = iconv(mb_detect_encoding($data['names']), 'cp1251', $data['bulstat']);
            $document[1]['ekate'] = iconv(mb_detect_encoding($data['names']), 'cp1251', $data['ekate']);
        } else {
            $document[1]['names'] = $data['names'];
            $document[1]['egn'] = $data['egn'];
            $document[1]['address_ekate'] = $data['address_ekate'];
            $document[1]['address'] = $data['address'];
            $document[1]['phone'] = $data['phone'];
            $document[1]['company'] = $data['company'];
            $document[1]['bulstat'] = $data['bulstat'];
            $document[1]['ekate'] = $data['ekate'];
        }

        $document[1]['year'] = $GLOBALS['Farming']['years'][$data['year']]['year'];
        $document[1]['date'] = date('d.m.Y');

        if (false == $data['is_export_type_xls']) {
            $name .= '.csv';

            $path = PUBLIC_UPLOAD_BLANK . '/' . $this->User->GroupID . '/';

            if (!is_dir($path)) {
                mkdir($path);
            }

            $fp = fopen($path . $name, 'w');
            $lines_count = count($document);
            for ($i = 0; $i < $lines_count; $i++) {
                fputs($fp, implode(';', $document[$i]) . "\n");
            }

            fclose($fp);

            $return['file_path'] = PUBLIC_UPLOAD_RELATIVE_PATH . '/blanks/' . $this->User->GroupID . '/' . $name;
            $return['file_name'] = $name;
        } else {
            $headers = array_shift($document);

            $name .= '.xlsx';

            $path = PUBLIC_UPLOAD_EXPORT . '/' . $this->User->GroupID . '/';

            $excellExportOptions = [
                'noStyle' => true,
                'format' => [
                    'kad_ident' => [
                        'type' => PHPExcel_Style_NumberFormat::FORMAT_TEXT,
                    ],
                    'c_date' => [
                        'type' => 'date',
                        'value' => 'dd.mm.yyyy "г.";@',
                    ],
                    'start_date' => [
                        'type' => 'date',
                        'value' => 'dd.mm.yyyy "г.";@',
                    ],
                    'date' => [
                        'type' => 'date',
                        'value' => 'dd.mm.yyyy "г.";@',
                    ],
                    'egn' => [
                        'type' => PHPExcel_Style_NumberFormat::FORMAT_TEXT,
                    ],
                    'address_ekate' => [
                        'type' => PHPExcel_Style_NumberFormat::FORMAT_TEXT,
                    ],
                    'phone' => [
                        'type' => PHPExcel_Style_NumberFormat::FORMAT_TEXT,
                    ],
                    'bulstat' => [
                        'type' => PHPExcel_Style_NumberFormat::FORMAT_TEXT,
                    ],
                    'ekate' => [
                        'type' => PHPExcel_Style_NumberFormat::FORMAT_TEXT,
                    ],
                    'c_num' => [
                        'type' => PHPExcel_Style_NumberFormat::FORMAT_TEXT,
                    ],
                    'names' => [
                        'type' => PHPExcel_Style_NumberFormat::FORMAT_TEXT,
                    ],
                    'company' => [
                        'type' => PHPExcel_Style_NumberFormat::FORMAT_TEXT,
                    ],
                    'address' => [
                        'type' => PHPExcel_Style_NumberFormat::FORMAT_TEXT,
                    ],
                    'document_area' => [
                        'type' => 'number',
                        'value' => '#,##0.000',
                    ],
                    'area' => [
                        'type' => 'number',
                        'value' => '#,##0.000',
                    ],
                    'number' => [
                        'type' => 'number',
                        'value' => PHPExcel_Style_NumberFormat::FORMAT_NUMBER,
                    ],
                    'wishes' => [
                        'type' => 'number',
                        'value' => PHPExcel_Style_NumberFormat::FORMAT_NUMBER,
                    ],
                    'area_type' => [
                        'type' => 'number',
                        'value' => PHPExcel_Style_NumberFormat::FORMAT_NUMBER,
                    ],
                    'sub_type' => [
                        'type' => 'number',
                        'value' => PHPExcel_Style_NumberFormat::FORMAT_NUMBER,
                    ],
                    'year' => [
                        'type' => 'number',
                        'value' => PHPExcel_Style_NumberFormat::FORMAT_NUMBER,
                    ],
                    'nm_usage_rights' => [
                        'type' => 'number',
                        'value' => PHPExcel_Style_NumberFormat::FORMAT_NUMBER,
                    ],
                    'period' => [
                        'type' => 'number',
                        'value' => PHPExcel_Style_NumberFormat::FORMAT_NUMBER,
                    ],
                ],
            ];

            if (!is_dir($path)) {
                mkdir($path);
            }

            $exportExcel = new ExportToExcelClass();
            $exportExcel->export($document, $headers, [], 0, $excellExportOptions);
            $exportExcel->saveFile($path . $name);

            $return['file_path'] = PUBLIC_UPLOAD_EXPORT_RELATIVE_PATH . $this->User->GroupID . '/' . $name;
            $return['file_name'] = $name;
        }

        return $return;
    }

    /**
     * Creates "Заявление ПМЛ" CSV file for download.
     *
     * @api-method createCSVDeclPML
     *
     * @param array $data declaration data
     *                    {
     *                    #item string names
     *                    #item string egn
     *                    #item string address_ekate
     *                    #item string names
     *                    #item string phone
     *                    #item string names
     *                    #item string bulstat
     *                    #item string ekate
     *                    #item array  ntp
     *                    #item string from_date - c_date filter parameter
     *                    #item string to_date   - c_date filter parameter
     *                    #item boolean is_export_type_xls
     *                    }
     * @param int $page rpc parameters
     * @param int $rows rpc parameters
     * @param string $sort rpc parameters
     * @param string $order rpc parameters
     *
     * @throws MTRpcException VALIDATION_INVALID_SORTING_RULE -33013
     * @throws MTRpcException VALIDATION_INVALID_SORTING_ORDER -33014
     * @throws MTRpcException CANNOT_EXPORT_EMPTY_REPORT -33212
     *
     * @return array link to the temp word file
     */
    public function createCSVDeclPML($data, $page = null, $rows = null, $sort = null, $order = null)
    {
        $this->UserDbPlotsController = new UserDbPlotsController($this->User->Database);
        $this->FarmingController = new FarmingController('Farming');
        $UserDbAreaTypesController = new UserDbAreaTypesController($this->User->Database);

        $this->cleanInputData($data);
        $this->isXlsOrCsvExport = true;
        // options for plots-contracts query
        $year = $GLOBALS['Farming']['years'][$data['year']]['year'];

        // ###
        // GET SUBLEASES
        // ###
        $params = [
            'ekate' => $data['ekate'],
            'farming' => $data['farming'],
            'year' => $year,
        ];

        $subleased_plots = $this->getSubleasesByParams($params);
        // create variable/clear old data
        $subleased_gid_array = [];
        foreach ($subleased_plots as $key => $value) {
            $subleased_gid_array[] = $key;
        }

        $farming_start_date = ($year - 1) . '-10-01';
        $farming_due_date = $year . '-09-30';

        // options for plots-contracts query
        $options = [
            'return' => [
                'gid',
                'ROUND(pc.contract_area::numeric, 3) as contract_area',
                'kvs.include',
                'kvs.participate',
                'ROUND((CASE WHEN document_area IS NULL THEN St_Area(geom)/1000 ELSE document_area END)::numeric, 3) as document_area',
                'kvs.masiv',
                'kvs.number',
                'e.ekatte_name',
                'e.obsht_name',
                'e.obl_name',
                'TO_CHAR((CASE WHEN A.ID IS NULL THEN C.due_date ELSE A.due_date END), \'DD.MM.YYYY\') as due_date',
                'c.nm_usage_rights',
            ],
            'where' => [
                'farming' => ['column' => 'farming_id', 'compare' => '=', 'prefix' => 'c', 'value' => $data['farming']],
                'active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'TRUE'],
                'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
                'is_annex' => ['column' => 'is_annex', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
                'annex_action' => ['column' => 'annex_action', 'compare' => '=', 'prefix' => 'pc', 'value' => 'added'],
                'c_date_from' => ['column' => 'c_date', 'compare' => '>=', 'prefix' => 'c', 'value' => $data['from_date']],
                'c_date_to' => ['column' => 'c_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $data['to_date']],
                'archited_plot' => ['column' => '(kvs.is_edited = false or kvs.edit_active_from > \'' . $farming_due_date . '\')', 'compare' => '=', 'value' => 'true'],
            ],
            'farming_start_date' => $farming_start_date,
            'farming_due_date' => $farming_due_date,
            'joins' => [
                " LEFT JOIN dblink (
                    'host=" . DEFAULT_DB_HOST . ' port=' . DEFAULT_DB_PORT . ' dbname=' . DEFAULT_DB_DATABASE . ' user=' . DEFAULT_DB_USERNAME . ' password=' . DEFAULT_DB_PASSWORD . "',
                    'SELECT 
                        ekatte.ekatte_code as ekatte_code
                        , ekatte.ekatte_name as ekatte_name
                        , ob.obsht_name as obsht_name
                        , obl.obl_name as obl_name
                    FROM su_ekatte as ekatte 
                    LEFT JOIN su_kmetstva km on km.id = ekatte.nm_kmetst_id
                    LEFT JOIN su_obshtini ob on ob.id = km.nm_obst_id
                    LEFT JOIN su_oblasti obl on obl.id = ob.nm_obl_id
                    WHERE true'
                ) AS e (ekatte_code varchar, ekatte_name varchar, obsht_name varchar, obl_name varchar) ON(ekatte_code = ekate)",
            ],
        ];

        if (count($_SESSION['decl_array'])) {
            unset($options['where']['contract_type']);

            $ntpFilter = [];
            if (!empty($data['ntp'])) {
                $ntpFilter = $UserDbAreaTypesController->getNtpCodesWithAdditionalCodes($data['ntp']);

                $options['where']['pc_rel_id_string'] = [
                    'column' => 'id', 'compare' => 'IN', 'prefix' => 'pc',
                    'value' => $_SESSION['decl_array'] ? array_values($_SESSION['decl_array']) : [0],
                ];
                $options['where']['area_type'] = ['column' => 'area_type', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $ntpFilter];
            } else {
                $options['whereOr']['pc_rel_id_string'] = [
                    'column' => 'id', 'compare' => 'IN', 'prefix' => 'pc',
                    'value' => $_SESSION['decl_array'] ? array_values($_SESSION['decl_array']) : [0],
                ];
                $options['whereOr']['area_type'] = ['column' => 'area_type', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $ntpFilter];
            }
        } else {
            $ntpFilter = $GLOBALS['Plots']['pmlNtpCodes'];
            $options['where']['area_type'] = ['column' => 'area_type', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $ntpFilter];

            if (count($subleased_gid_array)) {
                $options['plots_anti_id_string'] = implode(', ', $subleased_gid_array);
            } else {
                $options['plots_anti_id_string'] = '0';
            }
        }

        // get all subleased plot GIDs for
        $results = $this->UserDbPlotsController->getFullPlotDataForDeclaration($options, false, false);

        if (0 == count($results)) {
            throw new MTRpcException('CANNOT_EXPORT_EMPTY_REPORT', -33212);
        }

        // heading
        $headers = [
            [
                'number' => '',
                'plot_number' => '',
                'document_area' => '',
                'ekatte_name' => '',
                'obsht_name' => '',
                'obl_name' => '',
                'contract_type' => '',
                'due_date' => '',
                'include_agreement' => '',
                'using_in_real_borders' => '',
            ],
            [
                'number' => '',
                'plot_number' => '',
                'document_area' => '',
                'ekatte_name' => '',
                'obsht_name' => '',
                'obl_name' => '',
                'contract_type' => '',
                'due_date' => '',
                'include_agreement' => '',
                'using_in_real_borders' => '',
            ],
            [
                'number' => '',
                'plot_number' => '',
                'document_area' => '',
                'ekatte_name' => '',
                'obsht_name' => '',
                'obl_name' => '',
                'contract_type' => '',
                'due_date' => '',
                'include_agreement' => '',
                'using_in_real_borders' => '',
            ],
            [
                'number' => 'ОПИС',
                'plot_number' => '',
                'document_area' => '',
                'ekatte_name' => '',
                'obsht_name' => '',
                'obl_name' => '',
                'contract_type' => '',
                'due_date' => '',
                'include_agreement' => '',
                'using_in_real_borders' => '',
            ],
            [
                'number' => 'на собствени и ползвани имоти  с НТП-пасища, мери и ливади',
                'plot_number' => '',
                'document_area' => '',
                'ekatte_name' => '',
                'obsht_name' => '',
                'obl_name' => '',
                'contract_type' => '',
                'due_date' => '',
                'include_agreement' => '',
                'using_in_real_borders' => '',
            ],
            [],
            [],
            [
                'number' => 'No по ред',
                'plot_number' => 'Имот № (по КВС  или КК )',
                'document_area' => 'Площ',
                'ekatte_name' => 'Землище',
                'obsht_name' => 'Община',
                'obl_name' => 'Област',
                'contract_type' => 'Основание за ползване',
                'due_date' => 'Краен срок за действие на договора',
                'include_agreement' => 'за включване в споразумение',
                'using_in_real_borders' => 'за ползване в реални граници',
            ],
            [
                'number' => '',
                'plot_number' => '',
                'document_area' => 'дка',
                'ekatte_name' => '',
                'obsht_name' => '',
                'obl_name' => '',
                'contract_type' => 'собствен или съсобствен или договор',
                'due_date' => 'дата (за имоти, ползвани с договор)',
                'include_agreement' => 'площ/дка',
                'using_in_real_borders' => 'площ/дка',
            ],
            [
                'number' => '1',
                'plot_number' => '2',
                'document_area' => '3',
                'ekatte_name' => '4',
                'obsht_name' => '5',
                'obl_name' => '6',
                'contract_type' => '7',
                'due_date' => '8',
                'include_agreement' => '9',
                'using_in_real_borders' => '10',
            ],
        ];

        /*
        Вземат се всички резултати, и се прехвърлят в нов масив където всеки имот фигурира по един път
        Ако имотът участва в повече договори, то след първоначалното добавяне на имота в крайния масив
        то само към площта по договор се добавя площта от договора, както и към правното основание на
        вече добавените договори се добавя и новия. Това се прави с цел да се сумира площта, с която
        участва даден имот във всички договори, както и да се вземе типът на всеки един от договорите,
        в които участва имотът. В случай, че сумарната площ по договор надвишава площта по документ
        на имота от системното КВС, то за площ на имота се взема площта по документ. В случай, че
        всички правни основания на всички договори, с които участва имотът са от един и същи тип
        напр. Наем, то в споразумението се записва правно основание - Наем, но ако има повече от един
        тип правно основание, то в споразумението се записва - Друго.
         */
        $formattedResults = [];
        $number = 1;
        $totalArea = 0;
        $totalAreaIncludeAgreement = 0;
        $totalAreaUsingInRealBorders = 0;
        foreach ($results as $result) {
            $index = $result['gid'] . $result['nm_usage_rights'];
            $formattedResults[$index]['number'] = $number;
            $formattedResults[$index]['plot_number'] = $this->kadIdentFormatting($result['masiv'], $result['number'], $data['plot_number_format'], $data['has_zerro_lpad']);
            $formattedResults[$index]['document_area'] = $result['document_area'];
            $formattedResults[$index]['ekatte_name'] = trim($result['ekatte_name']);
            $formattedResults[$index]['obsht_name'] = trim($result['obsht_name']);
            $formattedResults[$index]['obl_name'] = trim($result['obl_name']);
            $formattedResults[$index]['contract_type'] = 'собственост';
            $formattedResults[$index]['due_date'] = $result['due_date'];
            $formattedResults[$index]['include_agreement'] = '';
            $formattedResults[$index]['using_in_real_borders'] = '';

            $totalArea += $formattedResults[$index]['document_area'];
            if (1 !== $result['nm_usage_rights']) {
                $formattedResults[$index]['contract_type'] = 'договор';
            }

            if ($result['participate']) {
                $formattedResults[$index]['include_agreement'] = $result['contract_area'];
                $totalAreaIncludeAgreement += $result['contract_area'];
            }
            if ($result['include']) {
                $formattedResults[$index]['using_in_real_borders'] = $result['contract_area'];
                $totalAreaUsingInRealBorders += $result['contract_area'];
            }

            $number++;
        }

        $name = 'Z_PML_' . $data['ekate'] . '_' . time() . '.xlsx';
        $path = PUBLIC_UPLOAD_EXPORT . '/' . $this->User->GroupID . '/';

        $footers = [
            [
                'number' => '',
                'plot_number' => 'ОБЩО',
                'document_area' => $totalArea ?: '',
                'ekatte_name' => '',
                'obsht_name' => '',
                'obl_name' => '',
                'contract_type' => '',
                'due_date' => '',
                'include_agreement' => $totalAreaIncludeAgreement ?: '',
                'using_in_real_borders' => $totalAreaUsingInRealBorders ?: '',
            ],
            [
                'number' => '',
                'plot_number' => 'Известно ми  е че за неверни данни нося наказателна отговорност по чл. 313 от Наказателния кодекс.',
                'document_area' => '',
                'ekatte_name' => '',
                'obsht_name' => '',
                'obl_name' => '',
                'contract_type' => '',
                'due_date' => '',
                'include_agreement' => '',
                'using_in_real_borders' => '',
            ],
            [
                'number' => '',
                'plot_number' => '',
                'document_area' => '',
                'ekatte_name' => '',
                'obsht_name' => '',
                'obl_name' => '',
                'contract_type' => '',
                'due_date' => '',
                'include_agreement' => '',
                'using_in_real_borders' => '',
            ],
            [
                'number' => '',
                'plot_number' => '',
                'document_area' => '',
                'ekatte_name' => '',
                'obsht_name' => '',
                'obl_name' => '',
                'contract_type' => 'ЗАЯВИТЕЛ:',
                'due_date' => '',
                'include_agreement' => '',
                'using_in_real_borders' => '',
            ],
            [
                'number' => '',
                'plot_number' => '',
                'document_area' => '',
                'ekatte_name' => '',
                'obsht_name' => '',
                'obl_name' => '',
                'contract_type' => '',
                'due_date' => '/подпис/',
                'include_agreement' => '',
                'using_in_real_borders' => '',
            ],
        ];

        $footerTextRow = count($headers) + count($formattedResults) + 1;

        $excellExportOptions = [
            'tableBorders' => [
                [
                    'borderPosition' => 'allborders',
                    'style' => PHPExcel_Style_Border::BORDER_THIN,
                    'color' => ['argb' => 'FF000000'],
                    'startCol' => 'A',
                    'endCol' => 'J',
                    'startRow' => 7,
                    'endRow' => '',
                ],
            ],
            'bold' => [
                [
                    'startCol' => 'A',
                    'startRow' => '3',
                    'endCol' => 'J',
                    'endRow' => '4',
                ],
                [
                    'startCol' => 'A',
                    'startRow' => '7',
                    'endCol' => 'J',
                    'endRow' => '9',
                ],
            ],
            'horizontalAlign' => [
                [
                    'align' => PHPExcel_Style_Alignment::HORIZONTAL_CENTER,
                    'startCol' => 'A',
                    'startRow' => '3',
                    'endCol' => 'J',
                    'endRow' => '4',
                ],
                [
                    'align' => PHPExcel_Style_Alignment::HORIZONTAL_CENTER,
                    'startCol' => 'A',
                    'startRow' => '7',
                    'endCol' => 'J',
                    'endRow' => '9',
                ],
            ],
            'bgColor' => [
                [
                    'align' => PHPExcel_Style_Alignment::HORIZONTAL_CENTER,
                    'startCol' => 'A',
                    'startRow' => '7',
                    'endCol' => 'J',
                    'endRow' => '9',
                ],
            ],
            'noStyle' => true,
            'mergeCells' => [
                [
                    'startCol' => 0,
                    'startRow' => 3,
                    'endCol' => 9,
                    'endRow' => 3,
                ],
                [
                    'startCol' => 0,
                    'startRow' => 4,
                    'endCol' => 9,
                    'endRow' => 4,
                ],
                [
                    'startCol' => 1,
                    'startRow' => $footerTextRow,
                    'endCol' => 7,
                    'endRow' => $footerTextRow,
                ],
            ],
            'format' => [
                'plot_number' => [
                    'type' => PHPExcel_Style_NumberFormat::FORMAT_TEXT,
                ],
                'ekatte_name' => [
                    'type' => PHPExcel_Style_NumberFormat::FORMAT_TEXT,
                ],
                'obsht_name' => [
                    'type' => PHPExcel_Style_NumberFormat::FORMAT_TEXT,
                ],
                'obl_name' => [
                    'type' => PHPExcel_Style_NumberFormat::FORMAT_TEXT,
                ],
                'due_date' => [
                    'type' => 'date',
                    'value' => 'dd.mm.YYYY "г.";@',
                ],
                'contract_type' => [
                    'type' => PHPExcel_Style_NumberFormat::FORMAT_TEXT,
                ],
                'include_agreement' => [
                    'type' => PHPExcel_Style_NumberFormat::FORMAT_TEXT,
                ],
                'using_in_real_borders' => [
                    'type' => PHPExcel_Style_NumberFormat::FORMAT_TEXT,
                ],
                'document_area' => [
                    'type' => PHPExcel_Style_NumberFormat::FORMAT_TEXT,
                ],
            ],
        ];

        if (!is_dir($path)) {
            mkdir($path);
        }

        $exportExcel = new ExportToExcelClass();
        $exportExcel->export($formattedResults, $headers, $footers, 0, $excellExportOptions);
        $exportExcel->saveFile($path . $name);

        $return['file_path'] = PUBLIC_UPLOAD_EXPORT_RELATIVE_PATH . $this->User->GroupID . '/' . $name;
        $return['file_name'] = $name;

        return $return;
    }

    /**
     * Checks for existing KMS agreements.
     *
     * @api-method checkKMSAgreements
     *
     * @param array $data
     *                    {
     *                    #item int year
     *                    #item int farming
     *                    #item string ekate
     *                    }
     *
     * @return array
     */
    public function checkKMSAgreements($data)
    {
        $LayersController = new LayersController('Layers');
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);

        $return = [
            'year' => $GLOBALS['Farming']['years'][$data['year']]['farming_year'],
            'has_agreements' => false,
            'data' => $data,
        ];

        $options = [];
        $options['farming'] = $data['farming'];
        $options['year'] = $data['year'];
        $options['group_id'] = $this->User->GroupID;
        $options['layer_type'] = 4;

        $tableName = $LayersController->getTableNameByParams($options);

        $tableExists = $UserDbController->getTableNameExist($tableName);
        // there is loaded data for that year
        if ($tableExists) {
            $year = $GLOBALS['Farming']['years'][$data['year']]['year'];
            $farming_start_date = ($year - 1) . '-10-01';
            $farming_due_date = $year . '-09-30';

            $options = [
                'return' => [
                    'c.id',
                ],
                'where' => [
                    'ekate' => ['column' => 'ekatte', 'compare' => '=', 'prefix' => 'kms', 'value' => $data['ekate']],
                ],
                'kms_table' => $tableName,
            ];

            $contract_counter = $UserDbPlotsController->getExistingEkateInKMS($options, true, false);

            $options = [
                'return' => [
                    'kad_ident', 'area_type', 'include', 'participate', 'white_spots',
                    'contract_area as area', 'ekate', 'masiv', 'number',
                    'c.c_num', 'c.c_date', 'pc.id as pc_rel_id', 'c.id', 'c.start_date', 'c.nm_usage_rights',
                ],
                'where' => [
                    'ekate' => ['column' => 'ekate', 'compare' => '=', 'prefix' => 'kvs', 'value' => $data['ekate']],
                    'farming' => ['column' => 'farming_id', 'compare' => '=', 'prefix' => 'c', 'value' => $data['farming']],
                    'active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'TRUE'],
                    'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
                    'is_annex' => ['column' => 'is_annex', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
                    'c_type' => ['column' => 'nm_usage_rights', 'compare' => '=', 'prefix' => 'c', 'value' => '4'],
                    'annex_action' => ['column' => 'annex_action', 'compare' => '=', 'prefix' => 'pc', 'value' => 'added'],
                    'archited_plot' => ['column' => '(kvs.is_edited = false or kvs.edit_active_from > \'' . $farming_due_date . '\')', 'compare' => '=', 'value' => 'true'],
                ],
                'custom_counter' => 'COUNT(DISTINCT(gid))',
                'farming_start_date' => $farming_start_date,
                'farming_due_date' => $farming_due_date,
            ];
            $counter = $UserDbPlotsController->getFullPlotDataForDeclaration($options, true, false);
            if ($contract_counter[0]['count'] && 0 == $counter[0]['count']) {
                $return['has_agreements'] = true;
            }
        }
        $_SESSION['has_agreements_in_kms'] = $return['has_agreements'];

        return $return;
    }

    /**
     * removeFile remove File from the server.
     *
     * @api-method removeFile
     *
     * @param string $fileName
     */
    public function removeFile($fileName)
    {
        @unlink(PUBLIC_UPLOAD . '/blanks/' . $fileName);
    }

    public function createDecl73XML(array $rpcParams): array
    {
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);

        $farming = $rpcParams['farming'];
        $timePeriod = $UserDbPaymentsController->getDeclaration73TimePeriod($rpcParams['year']);
        $dateFrom = $timePeriod['dateFrom'];
        $dateTo = $timePeriod['dateTo'];
        $year = $GLOBALS['Farming']['years'][$rpcParams['year']]['year'];

        $options = [
            'return' => [
                'o.egn',
                'o.name',
                'o.surname',
                'o.lastname',
                '907 as reason_code',
                'round(sum(p.amount)::numeric, 2) as amount',
                '0.00 as tax',
                'o.is_foreigner',
                'to_char(o.birthday, \'YYYY/mm/dd\')  as birthday',
                'o.country',
            ],
            'where' => [
                'farm' => ['column' => 'farming_id', 'compare' => '=', 'prefix' => 'c', 'value' => $farming],
                'dateFrom' => ['column' => 'date', 'compare' => '>=', 'prefix' => 'p', 'value' => $dateFrom],
                'dateTo' => ['column' => 'date', 'compare' => '<=', 'prefix' => 'p', 'value' => $dateTo],
                'paid_in' => ['column' => 'paid_in', 'compare' => '=', 'prefix' => 'p', 'value' => 1],
                'paid_from' => ['column' => 'paid_from', 'compare' => 'BETWEEN', 'prefix' => 'p', 'value' => [1, 2]],
                'payments' => ['column' => "(
                                    select sum(p_inner.amount)
                                    from su_transactions t_inner
                                    inner join su_payments p_inner on t_inner.id = p_inner.transaction_id
                                    inner join su_owners o_inner on o_inner.id = p_inner.owner_id
                                    inner join su_contracts c_inner on c_inner.id = p_inner.contract_id
                                    where p_inner.owner_id = o.id
                                    and c_inner.farming_id = '{$farming}'
                                    and t_inner.status = true
                                    and p_inner.date >= '{$dateFrom}'
                                    and p_inner.date <= '{$dateTo}'
                                )", 'compare' => '>=', 'value' => 3000],
            ],
            'caseWhere' => ' and case 
                                when o.is_foreigner is false 
                                then LENGTH(o.egn) > 0
                                else o.is_foreigner is true
                            end
            ',
            'group' => 'o.egn, o.name, o.surname, o.lastname, o.is_foreigner, o.birthday, o.country',
            'annexes_no_join' => true,
            'rent_nature_no_join' => true,
            'union_dividends' => false,
        ];

        if ($rpcParams['with_dividends']) {
            $options['union_dividends'] = true;
            $options['return_dividends'] = [
                'c.egn',
                'c.name',
                'c.surname',
                'c.lastname',
                '814 as reason_code',
                'round(sum(dp.cashout::numeric)::numeric, 2) as amount',
                'round((sum(dp.cashout::numeric)::numeric * 0.05),2) as tax',
                'false as is_foreigner',
                'null as birthday',
                'null as country',
            ];
            $options['where_dividends'] = [
                'dateFrom' => ['column' => 'pay_date', 'compare' => '>=', 'prefix' => 'dp', 'value' => $dateFrom],
                'dateTo' => ['column' => 'pay_date', 'compare' => '<=', 'prefix' => 'dp', 'value' => $dateTo],
            ];
            $options['group_dividends'] = 'c.name, c.surname, c.lastname, c.egn';
        }

        $rowsenums = $UserDbPaymentsController->getPaymentsByParamsWithKvs($options, false, false);
        $farmingData = $this->getFarmingData($farming);

        $xml = new SimpleXMLElement('<?xml version="1.0" encoding="WINDOWS-1251" ?> <dec731 />');
        $xml->addChild('isterm', '0');
        $xml->addChild('year', $year);

        $part1 = $xml->addChild('part1');

        $part1->addChild('eik', $farmingData[0]['bulstat']);
        $part1->addChild('name', $farmingData[0]['name']); // тук да форамтраим !?!!?!?

        $part2 = $xml->addChild('part2');

        $wrongEgns = [];
        foreach ($rowsenums as $row) {
            if (!$row['is_foreigner'] && 10 != strlen($row['egn'])) {
                $wrongEgns[] = $row['egn'];

                continue;
            }

            $rowsenum = $part2->addChild('rowsenum');
            $rowsenum->addChild('correctioncode', '0');
            $rowsenum->addChild('foreign', $row['is_foreigner'] ? 1 : 0);
            $rowsenum->addChild('identtype', $row['is_foreigner'] ? 3 : 0);

            if (!$row['is_foreigner']) {
                $rowsenum->addChild('ident', $row['egn']);
            } else {
                $rowsenum->addChild('birthdate', $row['birthday']);
            }

            $rowsenum->addChild('firstname', $row['name']);
            $rowsenum->addChild('secondname', $row['surname']);
            $rowsenum->addChild('thirdname', $row['lastname']);

            if ($row['is_foreigner']) {
                $rowsenum->addChild('countrycode', $row['country']);
            }

            $rowsenum->addChild('incomecode', $row['reason_code']);
            $rowsenum->addChild('income', $row['amount']);
            $rowsenum->addChild('tax', $row['tax']);
        }

        if (count($wrongEgns)) {
            throw new MTRpcException('Експорта не може да бъде приключен. В списъка има невалидни данни за ЕГН - ' . implode(', ', $wrongEgns), -33236);
        }

        $filename = "SPR73_1_{$year}_{$farmingData[0]['bulstat']}";
        $filename = pathinfo($filename, PATHINFO_FILENAME);
        $groupId = Prado::getApplication()->getUser()->GroupID;
        $exportPath = PUBLIC_UPLOAD_EXPORT_RELATIVE_PATH . "{$groupId}";

        if (!file_exists($exportPath)) {
            mkdir($exportPath, 0755, true);
        }

        $path = "{$exportPath}/{$filename}.xml";
        $docXml = $xml->asXML();
        file_put_contents($path, $docXml);

        if (!file_exists($path)) {
            throw new MTRpcException('Mass export is not exists in: ' . $path, -33229);
        }

        return [
            'file_path' => $path,
            'file_name' => $filename,
        ];
    }

    public function createDecl73XLS($rpcParams)
    {
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);

        $year = $GLOBALS['Farming']['years'][$rpcParams['year']]['year'];
        $farming = $rpcParams['farming'];
        $farmingData = $this->getFarmingData($farming);
        $results = $UserDbPaymentsController->getDeclaration73Data($rpcParams, null, null, 'date', 'asc');
        $body = $results['rows'];

        $headers = [
            'transaction_id' => 'Номер на транзакция',
            'date' => 'Дата на плащане',
            'name' => 'Изплатено на (име)',
            'egn' => 'ЕГН/ЛНЧ',
            'amount' => 'Сума (лв.)',
            'tax' => 'Удържан данък (лв.)',
            'reason' => 'Основание (рента/дивидент)',
            'address' => 'Адрес',
            'is_foreigner' => 'Чуждестранно лице',
        ];

        $excellExportOptions = [
            'noStyle' => true,
            'format' => [
                'transaction_id' => [
                    'type' => 'number',
                    'value' => PHPExcel_Style_NumberFormat::FORMAT_NUMBER,
                ],
                'date' => [
                    'type' => PHPExcel_Style_NumberFormat::FORMAT_TEXT,
                ],
                'name' => [
                    'type' => PHPExcel_Style_NumberFormat::FORMAT_TEXT,
                ],
                'egn' => [
                    'type' => PHPExcel_Style_NumberFormat::FORMAT_TEXT,
                ],
                'amount' => [
                    'type' => PHPExcel_Style_NumberFormat::FORMAT_TEXT,
                ],
                'tax' => [
                    'type' => PHPExcel_Style_NumberFormat::FORMAT_TEXT,
                ],
                'reason_code' => [
                    'type' => PHPExcel_Style_NumberFormat::FORMAT_TEXT,
                ],
                'address' => [
                    'type' => PHPExcel_Style_NumberFormat::FORMAT_TEXT,
                ],
                'is_foreigner' => [
                    'type' => PHPExcel_Style_NumberFormat::FORMAT_TEXT,
                ],
            ],
        ];

        $name = "D73_{$year}_" . $farmingData[0]['bulstat'] . '.xlsx';

        $path = PUBLIC_UPLOAD_EXPORT . '/' . $this->User->GroupID . '/';

        if (!is_dir($path)) {
            mkdir($path);
        }

        $exportExcel = new ExportToExcelClass();
        $exportExcel->export($body, $headers, [], 0, $excellExportOptions);
        $exportExcel->saveFile($path . $name);

        return [
            'file_path' => PUBLIC_UPLOAD_EXPORT_RELATIVE_PATH . $this->User->GroupID . '/' . $name,
            'file_name' => $name,
        ];
    }

    private function getSubleasesByParams($params)
    {
        $UserDbSubleasesController = new UserDbSubleasesController($this->User->Database);

        $options = [
            'return' => [
                'DISTINCT(gid)', 'c.c_num', 'c.c_date',
            ],
            'where' => [
                'ekate' => ['column' => 'ekate', 'compare' => '=', 'prefix' => 'kvs', 'value' => $params['ekate']],
                'start_date' => ['column' => 'start_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $params['year'] . '-09-30'],
                'due_date' => ['column' => 'due_date', 'compare' => '>=', 'prefix' => 'c', 'value' => ($params['year'] - 1) . '-10-01'],
                'farming' => ['column' => 'farming_id', 'compare' => '=', 'prefix' => 'c', 'value' => $params['farming']],
                'active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'TRUE'],
            ],
            'group' => 'c.id, kvs.gid, pc.id',
        ];

        if ($params['plots_id_string']) {
            $options['plots_id_string'] = $params['plots_id_string'];
        }

        $subleased_results = $UserDbSubleasesController->getSubleaseFullData($options, false, false);
        $subleasedCount = count($subleased_results);

        if (0 == $subleasedCount) {
            return [];
        }

        // clear old subleased results
        $subleased_plots = [];

        for ($i = 0; $i < $subleasedCount; $i++) {
            $plot_gid = $subleased_results[$i]['gid'];
            $sublease_date = strftime('%d.%m.%Y', strtotime($subleased_results[$i]['c_date']));

            if ($subleased_plots[$plot_gid]) {
                $subleased_plots[$plot_gid] .= ', ' . $subleased_results[$i]['c_num'] . ' / ' . $sublease_date;
            } else {
                $subleased_plots[$plot_gid] = $subleased_results[$i]['c_num'] . ' / ' . $sublease_date;
            }
        }

        return $subleased_plots;
    }

    /** * @param string $sort * @param string $order * * @throws MTRpcException */
    private function declarationValidators($sort = '', $order = '')
    {
        $validSortColumns = ['kad_ident', 'area', 'area_type', 'c_num', 'c_type', 'subleases', 'c.id'];
        if (!$sort) {
            throw new MTRpcException('VALIDATION_INVALID_SORTING_RULE', -33013);
        }
        if (!in_array($sort, $validSortColumns)) {
            throw new MTRpcException('VALIDATION_INVALID_SORTING_RULE', -33013);
        }
        if ('asc' != $order && 'desc' != $order) {
            throw new MTRpcException('VALIDATION_INVALID_SORTING_ORDER', -33014);
        }
    }

    private function cleanInputData(&$data)
    {
        foreach ($data as &$input) {
            if (is_string($input)) {
                $input = str_replace(
                    ['.', ' ', "\n", "\t", "\r"],
                    ' ',
                    $input
                );
            }
        }
    }

    private function getEkatteData($ekatte)
    {
        $UsersController = new UsersController('Users');
        $options = ['return' => [
            'obl.obl_name',
            'obs.obsht_name',
            'kmet.kmet_name',
            'ekatte.ekatte_code',
            'ekatte.ekatte_name',
        ],
            'where' => [
                'ekate' => [
                    'column' => 'ekatte_code',
                    'compare' => '=',
                    'prefix' => 'ekatte',
                    'value' => $ekatte,
                ],
            ],
        ];

        return $UsersController->getEkatteOnlyItems($options);
    }

    private function getFarmingData($farmingId)
    {
        $FarmingController = new FarmingController('Farming');
        $options = [
            'where' => [
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'value' => $this->User->GroupID],
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $farmingId],
            ],
        ];

        return $FarmingController->getFarmings($options);
    }

    private function kadIdentFormatting($masivNum, $numberNum, $plotFormatNumber, $zerroLPad = false)
    {
        if ($zerroLPad) {
            $format = '%0' . $zerroLPad . 'd';
            $masivNum = sprintf($format, $masivNum);
            $numberNum = sprintf($format, $numberNum);
        }
        $separator = '';
        if ('dot_separator' == $plotFormatNumber) {
            $separator = '.';
        } elseif ('underscore_separator' == $plotFormatNumber) {
            $separator = '_';
        }

        return $masivNum . $separator . $numberNum;
    }

    private function formatNumberForKKOrKVS($number, $type)
    {
        $length = 3;
        if (Config::KK_NUMBER_FORMAT === $type) {
            $length = 4;
        }

        return str_pad($number, $length, 0, STR_PAD_LEFT);
    }
}
