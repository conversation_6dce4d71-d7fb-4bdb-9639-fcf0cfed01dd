<?php

namespace TF\Engine\APIClasses\Plots;

use Prado\Web\Services\TRpcApiProvider;
use Prado\Web\Services\TRpcException;
use TF\Application\Common\Config;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbContracts\UserDbContractsController;
use TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController;
use TF\Engine\Plugins\Core\UserDbSubleases\UserDbSubleasesController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Договори за продажба.
 *
 * @rpc-module Plots
 *
 * @rpc-service-id plots-sales-contracts-datagrid
 */
class PlotsSalesContractsGrid extends TRpcApiProvider
{
    private $module = 'Plots';
    private $service_id = 'plots-sales-contracts-datagrid';
    private $UserDbSubleasesController;
    private $FarmingController;
    private $UserDbPlotsController;
    private $UserDbController;
    private $UserDbContractsController;
    private $UsersController;

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readSalesContractsDatagrid'],
                'validators' => [
                    'filterObj' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'getSalesContractsAddGrid' => ['method' => [$this, 'getSalesContractsAddGrid'],
                'validators' => [
                    'filterObj' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'addSalesContractPlotRelation' => ['method' => [$this, 'addSalesContractPlotRelation'],
                'validators' => [
                    'sales_contract_id' => 'validateNumber, validateRequired',
                    'contract_id' => 'validateNumber, validateRequired',
                    'sublease_contract_id' => 'validateNumber',
                    'plot_id' => 'validateNumber, validateRequired',
                    'contract_area' => 'validateNumber',
                    'contract_area_for_sale' => 'validateNumber',
                    'price_per_acre' => 'validateNumber',
                    'price_sum' => 'validateNumber',
                ]],
            'deleteSalesContractPlotRelation' => ['method' => [$this, 'deleteSalesContractPlotRelation']],
        ];
    }

    /**
     * Gets all contracts, associated with current plot.
     *
     * @api plots-rpc
     *
     * @module-rpc plots-contracts-datagrid
     *
     * @param array|int $filterObj - the plot ID to filter the contracts
     * @param int|string $page - pagination parameters
     * @param int|string $rows - pagination parameters
     * @param string $sort - pagination parameters
     * @param string $order - pagination parameters
     *
     * @throws TRpcException
     *
     * @return array - an array of matching results
     */
    public function readSalesContractsDatagrid(array $filterObj, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $plot_id = is_array($filterObj) ? $filterObj['plot_id'] : $filterObj;
        $this->initializeControllers('readSalesContractsDatagrid');
        $results = $this->UserDbPlotsController->getPlotsContractsRelations($plot_id, $this->User);
        $resultsCount = count($results);
        $contract_results = [];
        if ($resultsCount > 0) {
            for ($i = 0; $i < $resultsCount; $i++) {
                $id_array[] = $results[$i]['contract_id'];
                $contract_area_array[$results[$i]['contract_id']] = ($results[$i]['contract_area']) ? $results[$i]['contract_area'] : $results[$i]['area'];
                $contract_plot_data[$results[$i]['contract_id']]['price_per_acre'] = $results[$i]['price_per_acre'];
                $contract_plot_data[$results[$i]['contract_id']]['price_sum'] = $results[$i]['price_sum'];
            }

            $options = [
                'return' => ['*'],
                'where' => [
                    'contract_id' => ['column' => 'id', 'compare' => 'IN', 'value' => $id_array],
                    'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'value' => 'FALSE'],
                ],
            ];

            $contract_results = $this->UserDbPlotsController->getContractsDataForPlots($options, false, false);
        }

        $contractsCount = count($contract_results);
        for ($i = 0; $i < $contractsCount; $i++) {
            $contract_results[$i]['contract_area'] = $contract_area_array[$contract_results[$i]['id']];
            $contract_results[$i]['contract_area'] = number_format($contract_results[$i]['contract_area'], 3, '.', '');
            $contract_results[$i]['price_per_acre'] = $contract_plot_data[$returns[$i]['id']]['price_per_acre'] ? $contract_plot_data[$returns[$i]['id']]['price_per_acre'] : '-';
            $contract_results[$i]['price_sum'] = $contract_plot_data[$returns[$i]['id']]['price_sum'] ? $contract_plot_data[$returns[$i]['id']]['price_sum'] : '-';
        }

        $options = [
            'return' => ['c.*'],
            'where' => [
                'plot_id' => ['column' => 'plot_id', 'compare' => '=', 'prefix' => 'pc', 'value' => $plot_id],
                'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'prefix' => 'c', 'value' => 'TRUE'],
            ],
        ];

        $sublease_results = $this->UserDbSubleasesController->getSubleasePlotsData($options, false, false);
        $subleaseCount = count($sublease_results);

        // get sublease contract_area data
        $options = [
            'tablename' => $UserDbController->DbHandler->tableSubleasesPlotsArea,
            'return' => [
                'sublease_id', 'plot_id', 'round(contract_area::numeric, 3) as contract_area',
            ],
            'where' => [
                'plot_id' => ['column' => 'plot_id', 'compare' => '=', 'value' => $plot_id],
            ],
        ];

        $sca_results = $this->UserDbController->getItemsByParams($options, false, false);
        $scaCount = count($sca_results);
        for ($i = 0; $i < $scaCount; $i++) {
            $contract_area_by_sublease_id[$sca_results[$i]['sublease_id']] = $sca_results[$i]['contract_area'];
        }

        for ($i = 0; $i < $subleaseCount; $i++) {
            $sublease_results[$i]['contract_area'] = $contract_area_by_sublease_id[$sublease_results[$i]['id']];
            $sublease_results[$i]['contract_area'] = number_format($sublease_results[$i]['contract_area'], 3, '.', '');
            $sublease_results[$i]['price_per_acre'] = '-';
            $sublease_results[$i]['price_sum'] = '-';
        }

        $returns = array_merge($contract_results, $sublease_results);
        $returnsCount = count($returns);
        if (0 == count($returns)) {
            return [
                'rows' => [],
                'total' => 0,
            ];
        }

        $id_array = [];
        $farming_array = [];

        date_default_timezone_set('Europe/Sofia');
        $currentDate = date('Y-m-d', time());

        for ($i = 0; $i < $returnsCount; $i++) {
            if ($returns[$i]['is_sublease']) {
                $returns[$i]['c_num'] = $returns[$i]['c_num'] . ' (преотдаден)';
            }

            if ($returns[$i]['is_annex']) {
                $returns[$i]['c_num'] = $returns[$i]['c_num'] . ' (анекс)';
            }

            if ($returns[$i]['active']) {
                $returns[$i]['active_text'] = (!$returns[$i]['due_date'] || $returns[$i]['due_date'] >= $currentDate) ? 'Действащ' : 'Изтекъл';
            } else {
                $returns[$i]['active_text'] = 'Анулиран';
            }
            $returns[$i]['c_type'] = $returns[$i]['nm_usage_rights'];
            $returns[$i]['nm_usage_rights'] = $GLOBALS['Contracts']['ContractTypes'][$returns[$i]['nm_usage_rights']]['name'];
            $returns[$i]['start_date'] = strftime('%d.%m.%Y', strtotime($returns[$i]['start_date']));
            $returns[$i]['c_date'] = strftime('%d.%m.%Y', strtotime($returns[$i]['c_date']));
            if ('' == $returns[$i]['due_date']) {
                $returns[$i]['due_date'] = '-';
            } else {
                $returns[$i]['due_date'] = strftime('%d.%m.%Y', strtotime($returns[$i]['due_date']));
            }
            $id_array[] = $returns[$i]['farming_id'];
        }

        $id_string = implode(', ', $id_array);

        $farming_data = $this->FarmingController->getFarmingItemsByIDString($id_string, $this->User->GroupID);
        $farmingCount = count($farming_data);
        if (0 != $farmingCount) {
            for ($i = 0; $i < $farmingCount; $i++) {
                $farming_array[$farming_data[$i]['id']] = $farming_data[$i];
            }
        }

        for ($i = 0; $i < $returnsCount; $i++) {
            $returns[$i]['farming'] = ($farming_array[$returns[$i]['farming_id']]['name']) ? $farming_array[$returns[$i]['farming_id']]['name'] : '-';
        }

        // sorting
        $returns = $this->FarmingController->ArrayHelper->sortResultArray($returns, $sort, $order);

        // pagination
        $page_returns = array_slice($returns, ($page - 1) * $rows, $rows);

        $return = [
            'total' => count($returns),
            'rows' => $page_returns,
        ];

        return $return;
    }

    /**
     * Gets all contracts, that are not currently associated with the selected plot.
     *
     * @api plots-rpc
     *
     * @module-rpc plots-contracts-datagrid
     *
     * @param array|int $filterObj - the plot ID to filter the contracts
     * @param int $page - pagination parameters
     * @param int $rows - pagination parameters
     * @param string $sort - pagination parameters
     * @param string $order - pagination parameters
     *
     * @throws TRpcException
     *
     * @return array - an array of matching results
     */
    public function getSalesContractsAddGrid(array $filterObj, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        if ($this->User->isGuest) {
            return [
                'rows' => [],
                'total' => 0,
            ];
        }

        $this->initializeControllers('getSalesContractsAddGrid');

        $plot_id = is_array($filterObj) ? $filterObj['plot_id'] : $filterObj;
        $results = $this->UserDbPlotsController->getPlotsContractsRelations($plot_id);
        $resultsCount = count($results);
        $id_array = [];
        for ($i = 0; $i < $resultsCount; $i++) {
            $id_array[] = $results[$i]['contract_id'];
        }

        $options = [
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
            'return' => ['*'],
            'where' => [
                'contract_id' => ['column' => 'id', 'compare' => 'NOT IN', 'value' => $id_array],
                'contract' => ['column' => 'c_num', 'compare' => 'ILIKE', 'value' => $filterObj['contract']],
                'date_from' => ['column' => 'c_date', 'compare' => '>=', 'value' => $filterObj['date_from']],
                'date_to' => ['column' => 'c_date', 'compare' => '>=', 'value' => $filterObj['date_to']],
                'is_annex' => ['column' => 'is_annex', 'compare' => '=', 'value' => 'FALSE'],
                'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'value' => 'FALSE'],
            ],
        ];

        $counter = $this->UserDbPlotsController->getContractsDataForPlots($options, true, false);

        if (0 == $counter[0]['count']) {
            return [
                'rows' => [],
                'total' => 0,
            ];
        }

        $returns = $this->UserDbPlotsController->getContractsDataForPlots($options, false, false);
        $returnsCount = count($returns);

        $plot_options = [
            'tablename' => $UserDbController->DbHandler->tableKVS,
            'return' => ['ST_Area(geom)/1000 as area'],
            'where' => [
                'gid' => ['column' => 'gid', 'compare' => '=', 'value' => $plot_id],
            ],
        ];

        $plot_result = $this->UserDbController->getItemsByParams($plot_options);

        $id_array = [];
        $farming_array = [];

        date_default_timezone_set('Europe/Sofia');
        $currentDate = date('Y-m-d', time());

        for ($i = 0; $i < $returnsCount; $i++) {
            if ($returns[$i]['active']) {
                $returns[$i]['active_text'] = (!$returns[$i]['due_date'] || $returns[$i]['due_date'] >= $currentDate) ? 'Действащ' : 'Изтекъл';
            } else {
                $returns[$i]['active_text'] = 'Анулиран';
            }
            $returns[$i]['c_type'] = $returns[$i]['nm_usage_rights'];
            $returns[$i]['nm_usage_rights'] = $GLOBALS['Contracts']['ContractTypes'][$returns[$i]['nm_usage_rights']]['name'];
            $returns[$i]['start_date'] = strftime('%d.%m.%Y', strtotime($returns[$i]['start_date']));
            $returns[$i]['c_date'] = strftime('%d.%m.%Y', strtotime($returns[$i]['c_date']));
            if ('' == $returns[$i]['due_date']) {
                $returns[$i]['due_date'] = '-';
            } else {
                $returns[$i]['due_date'] = strftime('%d.%m.%Y', strtotime($returns[$i]['due_date']));
            }
            $id_array[] = $returns[$i]['farming_id'];

            $returns[$i]['contract_area'] = $plot_result[0]['area'];
            $returns[$i]['contract_area'] = number_format($returns[$i]['contract_area'], 3, '.', '');
            $returns[$i]['price_per_acre'] = '';
            $returns[$i]['price_sum'] = '';
        }

        $id_string = implode(', ', $id_array);

        $farming_data = $this->FarmingController->getFarmingItemsByIDString($id_string, $this->User->GroupID);
        $farmingCount = count($farming_data);
        if (0 != count($farming_data)) {
            for ($i = 0; $i < $farmingCount; $i++) {
                $farming_array[$farming_data[$i]['id']] = $farming_data[$i];
            }
        }
        $returnsCount = count($returns);
        for ($i = 0; $i < $returnsCount; $i++) {
            $returns[$i]['farming'] = $farming_array[$returns[$i]['farming_id']]['name'];
        }

        $return = [
            'total' => $counter[0]['count'],
            'rows' => $returns,
        ];

        return $return;
    }

    /**
     * Creates a relation between selected contract ID and plot ID.
     *
     * @api plots-rpc
     *
     * @module-rpc plots-sales-contracts-datagrid
     *
     * @param array $data - RPC Parameters
     *                    {
     *
     *                    	@item int contract_id
     *                    	@item int plot_id
     *                    	@item int contract_area
     *                    	@item int contract_area_for_sale
     *                    	@item int price_per_acre
     *                    	@item int price_sum
     *                    }
     *
     * @throws TRpcException
     *
     * @return int returns status OK code
     */
    public function addSalesContractPlotRelation($data)
    {
        $this->initializeControllers('addSalesContractPlotRelation');

        $data['sublease_contract_id'] = $data['sublease_contract_id'] ? $data['sublease_contract_id'] : null;

        $contract_plot_rel = $this->getPlotContractRel($data['plot_id'], $data['contract_id']);

        $options = [
            'tablename' => $this->UserDbController->DbHandler->salesContractsPlotsRelTable,
            'mainData' => [
                'sales_contract_id' => $data['sales_contract_id'],
                'contract_id' => $data['contract_id'],
                'sublease_contract_id' => $data['sublease_contract_id'],
                'plot_id' => $data['plot_id'],
                'contract_area' => $data['contract_area'],
                'contract_area_for_sale' => $data['contract_area_for_sale'],
                'price_per_acre' => filter_var($data['price_per_acre'], FILTER_VALIDATE_FLOAT, FILTER_NULL_ON_FAILURE),
                'price_sum' => filter_var($data['price_sum'], FILTER_VALIDATE_FLOAT, FILTER_NULL_ON_FAILURE),
                'pc_rel_id' => $contract_plot_rel,
            ],
        ];

        $item_id = $this->UserDbController->addItem($options);

        $this->UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options, ['plot_sales_contract_relation' => $item_id], 'Add plot to sales-contract');

        return Config::STATUS_CODE_OK;
    }

    /**
     * Deletes the relation between the contract and the plot.
     *
     * @api plots-rpc
     *
     * @module-rpc plots-contracts-datagrid
     *
     * @param int $plotId - selected plot ID
     * @param int $salesContractPlotId - selected contract ID
     * @param int $originalContractId - the contract from wich the plot was sold
     *
     * @throws TRpcException
     *
     * @return array
     */
    public function deleteSalesContractPlotRelation($plotId, $salesContractPlotId, $originalContractId)
    {
        if ($this->User->isGuest) {
            return [];
        }

        $this->initializeControllers('deleteSalesContractPlotRelation');

        // Set plot added to Annex before delete from salesContractsPlotsRelTable
        // $this->setPlotAddedToAnnex($plotId, $salesContractId, $originalContractId);
        $options = [
            'tablename' => $this->UserDbController->DbHandler->salesContractsPlotsRelTable,
            'id_string' => implode(', ', [(int)$salesContractPlotId]),
        ];

        $before_delete = [];
        $after_delete = [];
        $plot_result_before = $this->UserDbContractsController->getPlotIDsForSalesContracts();
        $plot_before_count = count($plot_result_before);
        for ($i = 0; $i < $plot_before_count; $i++) {
            $before_delete[] = $plot_result_before[$i]['plot_id'];
        }
        $this->UserDbController->deleteItemsByParams($options);

        $plot_result_after = $this->UserDbContractsController->getPlotIDsForSalesContracts();
        $plot_after_count = count($plot_result_after);

        for ($i = 0; $i < $plot_after_count; $i++) {
            $after_delete[] = $plot_result_after[$i]['plot_id'];
        }

        $diff_array = array_diff($before_delete, $after_delete);
        if (0 != count($diff_array)) {
            $id_string = implode(', ', $diff_array);
            // update status contract to false;
            $this->UserDbContractsController->updatePlotContractStatus($id_string, false);
        }

        $this->UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, [], ['plot_id' => $plotId, 'sales_contract_id' => $salesContractPlotId], 'Delete plot-sales contract relation');
    }

    /**
     * @throws TRpcException
     */
    protected function initializeControllers($method)
    {
        switch ($method) {
            // Controllers for generating the plots tree
            case 'readSalesContractsDatagrid':
                $this->UserDbSubleasesController = new UserDbSubleasesController($this->User->Database);
                $this->FarmingController = new FarmingController();
                $this->UserDbPlotsController = new UserDbPlotsController($this->User->Database);
                $this->UserDbController = new UserDbController($this->User->Database);

                break;
            case 'getSalesContractsAddGrid':
                $this->FarmingController = new FarmingController('Farming');
                $this->UserDbPlotsController = new UserDbPlotsController($this->User->Database);
                $this->UserDbController = new UserDbController($this->User->Database);

                break;
            case 'addSalesContractPlotRelation':
                $this->UserDbContractsController = new UserDbContractsController($this->User->Database);
                $this->UserDbController = new UserDbController($this->User->Database);
                $this->UsersController = new UsersController('Users');

                break;
            case 'deleteSalesContractPlotRelation':
                $this->UserDbContractsController = new UserDbContractsController($this->User->Database);
                $this->UserDbController = new UserDbController($this->User->Database);
                $this->UsersController = new UsersController('Users');

                break;
            default:
                throw new TRpcException('Invalid method selected', 1);
        }
    }

    private function getAnnexIdByContractId($contractId)
    {
        $options = [
            'tablename' => $this->UserDbController->DbHandler->tableContracts,
            'return' => [
                'id',
            ],
            'where' => [
                'parent_id' => ['column' => 'parent_id', 'compare' => '=', 'value' => $contractId],
                'is_annex' => ['column' => 'is_annex', 'compare' => '=', 'value' => 'TRUE'],
                'active' => ['column' => 'active', 'compare' => '=', 'value' => 'TRUE'],
            ],
        ];
        $result = $this->UserDbController->getItemsByParams($options, false, false);

        return $result[0]['id'];
    }

    private function updateAnnexAction($plotId, $annexId, $text, $salesContractId = null)
    {
        $options = [
            'tablename' => $this->UserDbController->DbHandler->contractsPlotsRelTable,
            'mainData' => [
                'annex_action' => $text,
            ],
            'where' => [
                'plot_id' => $plotId,
                'contract_id' => $annexId,
            ],
        ];

        if ('removed' == $text) {
            $innerOption = [
                'tablename' => $this->UserDbController->DbHandler->tableSalesContracts,
                'where' => [
                    'contract_id' => ['column' => 'id', 'compare' => '=', 'value' => $salesContractId],
                ],
            ];

            $result = $this->UserDbController->getItemsByParams($innerOption);
            $contractData = $result[0];

            $options['mainData']['contract_end_date'] = $contractData['start_date'];
        } else {
            $options['mainData']['contract_end_date'] = null;
        }

        $this->UserDbController->editItem($options);
    }

    private function getSalesContractStartDate($id)
    {
        $options = [
            'tablename' => $this->UserDbController->DbHandler->tableSalesContracts,
            'return' => [
                'start_date',
            ],
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $id],
            ],
        ];
        $result = $this->UserDbController->getItemsByParams($options, false, false);

        return $result[0]['start_date'];
    }

    private function getContractInfo($id)
    {
        $options = [
            'tablename' => $this->UserDbController->DbHandler->tableContracts,
            'return' => [
                'nm_usage_rights', 'farming_id',
            ],
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $id],
            ],
        ];

        return $this->UserDbController->getItemsByParams($options, false, false);
    }

    private function createAnnex($startDate, $nmUsageRights, $farmingId, $contractId)
    {
        $options = [
            'tablename' => $this->UserDbController->DbHandler->tableContracts,
            'mainData' => [
                'c_num' => 'Служебен, ' . strftime('%d.%m.%Y', strtotime($startDate)),
                'c_date' => 'now()',
                'nm_usage_rights' => $nmUsageRights,
                'start_date' => $startDate,
                'farming_id' => $farmingId,
                'active' => true,
                'parent_id' => $contractId,
                'is_annex' => true,
            ],
        ];
        $itemId = $this->UserDbController->addItem($options);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableContracts,
            'mainData' => [
                'c_num' => '(' . $itemId . ')Служебен, ' . strftime('%d.%m.%Y', strtotime($startDate)),
            ],
            'where' => [
                'id' => $itemId,
            ],
        ];
        $this->UserDbController->editItem($options);

        return $itemId;
    }

    private function addAllPlots($annexId, $contractId)
    {
        // Get all plots by contract_id
        $options = [
            'tablename' => $this->UserDbController->DbHandler->contractsPlotsRelTable,
            'return' => [
                '*',
            ],
            'where' => [
                'contract_id' => ['column' => 'contract_id', 'compare' => '=', 'value' => $contractId],
            ],
        ];
        $result = $this->UserDbController->getItemsByParams($options, false, false);
        $resultCount = count($result);

        $options = [
            'tablename' => $this->UserDbController->DbHandler->tableContracts,
            'where' => [
                'contract_id' => ['column' => 'id', 'compare' => '=', 'value' => $contractId],
            ],
        ];

        $contractResult = $this->UserDbController->getItemsByParams($options);
        $contractData = $contractResult[0];

        // Add All Plot
        for ($i = 0; $i < $resultCount; $i++) {
            $options = [
                'tablename' => $this->UserDbController->DbHandler->contractsPlotsRelTable,
                'mainData' => [
                    'contract_id' => $annexId,
                    'plot_id' => $result[$i]['plot_id'],
                    'contract_area' => $result[$i]['contract_area'],
                    'price_per_acre' => $result[$i]['price_per_acre'],
                    'price_sum' => $result[$i]['price_sum'],
                    'annex_action' => 'added',
                    'contract_end_date' => null,
                ],
            ];

            $this->UserDbController->addItem($options);
        }
    }

    private function addAutoAnnexes($data)
    {
        // Check if Annex exist according to contract_id
        if ($this->annexNotExist($data['contract_id'])) {
            // create Annex Process
            $this->addAutoAnnexesProcess($data['sales_contract_id'], $data['contract_id']);
        }

        // Check if Annex exist according to sublease_contract_id
        if ($data['sublease_contract_id']) {
            if ($this->annexNotExist($data['sublease_contract_id'])) {
                // create Annex Process
                $this->addAutoAnnexesProcess($data['sales_contract_id'], $data['sublease_contract_id']);
            }
        }
    }

    private function addAutoAnnexesProcess($salesContractId, $contractId)
    {
        // Get sales_contract start_date
        $startDate = $this->getSalesContractStartDate($salesContractId);

        // Get nm_usage_rights, farming_id by contract_id
        $result = $this->getContractInfo($contractId);

        $nmUsageRights = $result[0]['nm_usage_rights'];
        $farmingId = $result[0]['farming_id'];

        // Create Annex
        $annexId = $this->createAnnex($startDate, $nmUsageRights, $farmingId, $contractId);

        // Add All Plots with annex_action => 'removed'
        $this->addAllPlots($annexId, $contractId);
    }

    private function annexNotExist($id)
    {
        $options = [
            'tablename' => $this->UserDbController->DbHandler->tableContracts,
            'where' => [
                'parent_id' => ['column' => 'parent_id', 'compare' => '=', 'value' => $id],
                'is_annex' => ['column' => 'is_annex', 'compare' => '=', 'value' => 'TRUE'],
                'active' => ['column' => 'active', 'compare' => '=', 'value' => 'TRUE'],
            ],
        ];
        $counter = $this->UserDbController->getItemsByParams($options, true, false);

        return (bool) (0 == $counter[0]['count']);
    }

    private function setPlotAddedToAnnex($plotId, $salesContractId, $originalContractId = '')
    {
        $contractId = 0;
        // Get contractId
        $options = [
            'tablename' => $this->UserDbController->DbHandler->salesContractsPlotsRelTable,
            'return' => [
                'contract_id', 'contract_area', 'contract_area_for_sale',
            ],
            'where' => [
                'plot_id' => ['column' => 'plot_id', 'compare' => '=', 'value' => $plotId],
                'sales_contract_id' => ['column' => 'sales_contract_id', 'compare' => '=', 'value' => $salesContractId],
            ],
        ];

        if ($originalContractId) {
            $options['where']['contract_id'] = ['column' => 'contract_id', 'compare' => '=', 'value' => $originalContractId];
        }
        $result = $this->UserDbController->getItemsByParams($options, false, false);

        $contractId = $result[0]['contract_id'];
        $contractArea = $result[0]['contract_area'];
        $contractAreaForSale = $result[0]['contract_area_for_sale'];

        if ($contractId) {
            $annexId = $this->getAnnexIdByContractId($contractId);
            if ($annexId) {
                if ($contractArea > $contractAreaForSale) {
                    // update annex contract area
                    $options = [
                        'tablename' => $this->UserDbController->DbHandler->contractsPlotsRelTable,
                        'mainData' => [
                            'annex_action' => 'added',
                            'contract_area' => $contractArea,
                        ],
                        'where' => [
                            'plot_id' => $plotId,
                            'contract_id' => $annexId,
                        ],
                    ];
                    $this->UserDbController->editItem($options);
                } else {
                    // Update annex_action to 'added' by $data['plot_id'] and $annexId
                    $this->updateAnnexAction($plotId, $annexId, 'added');
                }
            }
        }
    }

    private function getPlotContractRel($plotId, $contractId)
    {
        $options = [
            'tablename' => $this->UserDbController->DbHandler->contractsPlotsRelTable,
            'return' => [
                'id',
            ],
            'where' => [
                'plot_id' => ['column' => 'plot_id', 'compare' => '=', 'value' => $plotId],
                'contract_id' => ['column' => 'contract_id', 'compare' => '=', 'value' => $contractId],
            ],
        ];

        $result = $this->UserDbController->getItemsByParams($options, false, false);

        if (isset($result[0])) {
            return $result[0]['id'];
        }

        return;
    }
}
