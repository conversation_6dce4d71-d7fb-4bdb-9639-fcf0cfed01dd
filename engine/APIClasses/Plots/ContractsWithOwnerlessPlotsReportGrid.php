<?php

namespace TF\Engine\APIClasses\Plots;

use Prado\Prado;
use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\Export2XlsClass;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbAreaTypes\UserDbAreaTypesController;
use TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController;

include_once __DIR__ . '/../../Plugins/Core/Farming/conf.php';
include_once __DIR__ . '/../../Plugins/Core/Users/<USER>';
include_once __DIR__ . '/../../Plugins/Core/Contracts/conf.php';

// Prado::using('Plugins.Core.UserDbPlots.*');
// Prado::using('Plugins.Core.Users.*');
// Prado::using('Plugins.Core.Users.conf');
// Prado::using('Plugins.Core.Farming.*');
// Prado::using('Plugins.Core.Farming.conf');

/**
 *  Справка "Договори с имоти без собственици".
 *
 * @rpc-module Plots
 *
 * @rpc-service-id contracts-with-ownerless-plots-report-grid
 *
 * @property UserDbController $UserDbController
 */
class ContractsWithOwnerlessPlotsReportGrid extends TRpcApiProvider
{
    private $UserDbController;

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getReport']],
            'exportContractsWithOwnerlessPlots' => ['method' => [$this, 'exportContractsWithOwnerlessPlots'],
                'validators' => [
                    'data' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * Export Contracts With Ownerless Plots.
     *
     * @param array $data {
     *                    #items array filters {
     *                    #item  timestamp report_date
     *                    #item  timestamp report_date_from
     *                    #item  timestamp report_contract_date
     *                    #item  timestamp report_contract_date_to
     *                    #item  integer report_farming
     *                    #item  integer report_ekate
     *                    #item  integer report_ntp
     *                    #item  integer report_category
     *                    #item  string report_mestnost
     *                    #item  string report_irrigation
     *                    }
     *                    }
     * @param int $page
     * @param int $rows
     * @param ?string $sort
     * @param ?string $order
     *
     * @return array
     */
    public function exportContractsWithOwnerlessPlots(array $data, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $data['for_export'] = true;
        $results = $this->getReport($data, $page, $rows, $sort, $order);

        $data = $results['rows'];

        unset($data[0]['attributes']);

        $columns = [
            'Номер на договор/анекс',
            'Тип на договора',
            'Номер на имота',
            'Категория',
            'НТП',
            'Площ на имота по договор (дка)',
            'Начална дата на договора',
            'Крайна дата на договора',
        ];
        $this->addTotals($data);

        $time = strtotime(date('Y-m-d H:i:s'));
        $fileName = 'imoti_bez_sobstvenici_' . $this->User->GroupID . '_' . $time . '.xlsx';
        $path = PUBLIC_UPLOAD_EXPORT . '/' . $fileName;

        $export2Xls = new Export2XlsClass();
        $filePath = $export2Xls->exportUrlPath($path, $data, $columns);

        return ['file_path' => $filePath, 'file_name' => $fileName];
    }

    /**
     * getReport.
     *
     * @api-method read
     *
     * @param array $params {
     *                      #items array filters {
     *                      #item  timestamp report_date_from
     *                      #item  timestamp report_date
     *                      #item  timestamp report_date_as_of
     *                      #item  timestamp report_contract_date
     *                      #item  timestamp report_contract_date_to
     *                      #item  integer report_farming
     *                      #item  integer report_ekate
     *                      #item  integer report_ntp
     *                      #item  integer report_category
     *                      #item  string report_mestnost
     *                      #item  string report_irrigation
     *                      }
     *                      }
     * @param int $page
     * @param int $rows
     * @param string $sort
     * @param string $order
     *
     * @return array
     */
    public function getReport($params, $page = null, $rows = null, $sort = '', $order = '')
    {
        $reportDateFrom = $params['filters']['report_date_from'];
        $reportDate = $params['filters']['report_date'];
        $reportDateAsOf = $params['filters']['report_date_as_of'];
        $reportContractDate = $params['filters']['report_contract_date'];
        $reportContractDateTo = $params['filters']['report_contract_date_to'];
        $reportFarming = $params['filters']['report_farming'];
        $reportEkate = $params['filters']['report_ekate'];
        $reportMasiv = $params['filters']['report_masiv'];
        $reportPlotNumber = $params['filters']['report_plot_number'];
        $reportKadIdent = $params['filters']['report_kad_ident'];

        $reportNtp = $params['filters']['report_ntp'];
        $reportCategory = $params['filters']['report_category'];
        $reportMestnost = $params['filters']['report_mestnost'];

        if (!empty($params['filters']['report_irrigation']) && 'all' !== $params['filters']['report_irrigation']) {
            $reportIrrigation = $params['filters']['report_irrigation'];
        }

        $FarmingController = new FarmingController('Farming');
        $userFarmings = $FarmingController->getUserFarmings();
        $farmingIds = array_keys($userFarmings);

        if ($reportFarming && in_array((int) $reportFarming, $farmingIds)) {
            $farmingIds = [(int) $reportFarming];
        }

        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);

        // create the default return
        $return = [
            'rows' => [],
            'total' => 0,
            'footer' => [],
        ];

        $salesStartDate = $reportDateAsOf ? $reportDateAsOf : $reportDate;

        if ('' != $salesStartDate) {
            $soldPlots = $UserDbController->getSoldPlotsAfterDate($salesStartDate);
        }
        $options = [
            'custom_counter' => 'COUNT(DISTINCT(pc.id))',
            'sort' => $sort,
            'order' => $order,
            'return' => [
                'ROUND(pc.contract_area::numeric, 3) as contract_area', 'pc.plot_id',
                'c.c_num', 'c.virtual_contract_type as nm_usage_rights', 'c.id', 'c.is_annex',
                'kvs.kad_ident', 'COALESCE(kvs.virtual_category_title, \'-\') as category', 'kvs.virtual_ntp_title as area_type', 'kvs.gid',
                "to_char(c.start_date,'DD.MM.YYYY') as start_date", "to_char(c.due_date,'DD.MM.YYYY') as due_date",
            ],
            'where' => [
                'active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => true],
                'annex_action' => ['column' => 'annex_action', 'compare' => '=', 'prefix' => 'pc', 'value' => 'added'],
                'plot_id' => ['column' => 'gid', 'compare' => 'NOT IN', 'value' => $soldPlots],
                'start_date' => ['column' => 'start_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $reportDate],
                'due_date' => ['column' => 'due_date', 'compare' => '>=', 'prefix' => 'c', 'value' => $reportDateFrom],
                'c_date' => ['column' => 'c_date', 'compare' => '>=', 'value' => $reportContractDate],
                'c_date_to' => ['column' => 'c_date', 'compare' => '<=', 'value' => $reportContractDateTo],
                'nm_usage_rights' => ['column' => 'nm_usage_rights', 'compare' => '!=', 'prefix' => 'c', 'value' => 4],
                'is_edited' => ['column' => 'is_edited', 'compare' => '=', 'prefix' => 'kvs', 'value' => 'false'],
                'farming_id' => ['column' => 'farming_id', 'compare' => 'IN', 'prefix' => 'c', 'value' => $farmingIds],
            ],
        ];

        if (null !== $page && null !== $rows) {
            $options['offset'] = ($page - 1) * $rows;
        }

        if (null !== $rows) {
            $options['limit'] = $rows;
        }

        if ($reportEkate) {
            $options['where']['ekate'] = ['column' => 'ekate', 'compare' => '=', 'prefix' => 'kvs', 'value' => $reportEkate];
        }
        if ($reportMasiv) {
            $options['where']['masiv'] = ['column' => 'masiv', 'prefix' => 'kvs', 'compare' => '=', 'value' => $reportMasiv];
        }
        if ($reportPlotNumber) {
            $options['where']['number'] = ['column' => 'number', 'prefix' => 'kvs', 'compare' => '=', 'value' => $reportPlotNumber];
        }
        if ($reportKadIdent) {
            $options['where']['kad_ident'] = ['column' => 'kad_ident', 'prefix' => 'kvs', 'compare' => '=', 'value' => $reportKadIdent];
        }
        if ($reportNtp) {
            $UserDbAreaTypesController = new UserDbAreaTypesController($this->User->Database);
            $ntpFilter = $UserDbAreaTypesController->getNtpCodesWithAdditionalCodes([$reportNtp]);
            $options['where']['ntp'] = ['column' => 'area_type', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $ntpFilter];
        }
        if ($reportCategory) {
            $categoryFilter = in_array($reportCategory, ['-1', '0', null]) ? ['-1', '0', null] : [$reportCategory];
            $options['where']['category'] = ['column' => 'category', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $categoryFilter];
        }
        if ($reportMestnost) {
            $options['where']['mestnost'] = ['column' => 'mestnost', 'compare' => '=', 'prefix' => 'kvs', 'value' => $reportMestnost];
        }

        if (!empty($reportIrrigation)) {
            $options['where']['irrigated_area'] = ['column' => 'irrigated_area', 'compare' => '=', 'value' => $reportIrrigation];
        }

        if ($reportDateAsOf) {
            $options['where']['start_date'] = ['column' => 'start_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $reportDateAsOf];
            // HACK
            // Договорите за собственост имат due_date null, и ако се остави null договорите за собственост няма да се включат
            $options['where']['due_date'] = ['column' => "(CASE WHEN c.due_date IS NULL THEN '9999-12-31' ELSE c.due_date END)", 'compare' => '>=', 'value' => $reportDateAsOf];
        }

        $results = $UserDbPlotsController->getOwnerlessPlots($options, false, false);
        $counter = $UserDbPlotsController->getOwnerlessPlots($options, true, false);

        if (0 == $counter[0]['count']) {
            return $return;
        }

        if (isset($params['for_export']) && true == $params['for_export']) {
            $results = $this->formatRowsForExport($results);
        }

        $return['rows'] = $results;
        $return['total'] = $counter[0]['count'];

        return $return;
    }

    private function formatRowsForExport($rows)
    {
        $results = [];
        $rowCount = count($rows);
        for ($i = 0; $i < $rowCount; $i++) {
            $results[$i]['c_num'] = $rows[$i]['c_num'];
            $results[$i]['nm_usage_rights'] = $rows[$i]['nm_usage_rights'];
            $results[$i]['kad_ident'] = $rows[$i]['kad_ident'];
            $results[$i]['category'] = $rows[$i]['category'];
            $results[$i]['area_type'] = $rows[$i]['area_type'];
            $results[$i]['contract_area'] = $rows[$i]['contract_area'];
            $results[$i]['start_date'] = $rows[$i]['start_date'];
            $results[$i]['due_date'] = $rows[$i]['due_date'];
        }

        return $results;
    }

    private function addTotals(&$rows)
    {
        $i = count($rows);
        $rows[$i]['c_num'] = '';
        $rows[$i]['nm_usage_rights'] = '';
        $rows[$i]['kad_ident'] = '';
        $rows[$i]['category'] = '';
        $rows[$i]['area_type'] = '';
        $rows[$i]['contract_area'] = number_format(array_sum(array_column($rows, 'contract_area')), 3);
        $rows[$i]['start_date'] = '';
        $rows[$i]['due_date'] = '';
    }
}
