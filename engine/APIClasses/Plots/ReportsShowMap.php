<?php

namespace TF\Engine\APIClasses\Plots;

use DateTime;
use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbOwners\UserDbOwnersController;
use TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController;

/**
 * Показва справки за имоти на карта.
 *
 * @rpc-module Plots
 *
 * @rpc-service-id reports-show-map
 */
class ReportsShowMap extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'mapReports' => ['method' => [$this, 'mapReports']],
            'mapReportsByType' => ['method' => [$this, 'mapReportsByType']],
        ];
    }

    /**
     * Show map - type: reports.
     *
     * @api-method mapReports
     *
     * @param array $filterParams {
     *                            #item string kad_ident
     *                            #item array ekate
     *                            #item string masiv
     *                            #item string number
     *                            #item array category
     *                            #item array area_type
     *                            #item string mestnost
     *                            #item boolean is_edited
     *                            #item string cnum
     *                            #item array contract_type
     *                            #item array farming
     *                            #item string date_from
     *                            #item string date_to
     *                            #item string due_date_from
     *                            #item string due_date_to
     *                            #item string owner_name
     *                            #item string owner_egn
     *                            #item string rep_name
     *                            #item string rep_egn
     *                            #item string company_name
     *                            #item string company_eik
     *                            }
     *
     * @return array
     */
    public function mapReports($filterParams)
    {
        date_default_timezone_set('Europe/Sofia');
        $currentDate = date('Y-m-d', time());

        $filterParams['start_date'] = '' != $filterParams['start_date'] ? $filterParams['start_date'] : $currentDate;
        $filterParams['due_date'] = '' != $filterParams['due_date'] ? $filterParams['due_date'] : $currentDate;

        $FarmingController = new FarmingController('Farming');
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);

        $arrayHelper = $FarmingController->ArrayHelper;

        $ownPlotsRequested = false;
        foreach ($filterParams['contract_type'] as $filter) {
            if (1 == $filter) {
                $ownPlotsRequested = true;

                break;
            }
        }
        $options = [
            'return' => [
                'distinct(kvs.gid) as gid, kvs.geom as geom, kvs.kad_ident',
            ],
            'where' => [
                // Филтри свързани с имот
                'kad_ident' => ['column' => 'kad_ident', 'compare' => '=', 'prefix' => 'kvs', 'value' => $filterParams['kad_ident']],
                'ekate' => ['column' => 'ekate', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $arrayHelper->filterEmptyStringArr($filterParams['ekate'])],
                'masiv' => ['column' => 'masiv', 'compare' => '=', 'prefix' => 'kvs', 'value' => $filterParams['masiv']],
                'number' => ['column' => 'number', 'compare' => '=', 'prefix' => 'kvs', 'value' => $filterParams['number']],
                'category' => ['column' => 'category', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $arrayHelper->filterEmptyStringArr($filterParams['category'])],
                'area_type' => ['column' => 'area_type', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $arrayHelper->filterEmptyStringArr($filterParams['area_type'])],
                'mestnost' => ['column' => 'mestnost', 'compare' => '=', 'prefix' => 'kvs', 'value' => $filterParams['mestnost']],
                // Филтри свързани с договор
                'cnum' => ['column' => 'c_num', 'compare' => 'LIKE ', 'prefix' => 'c', 'value' => $filterParams['cnum']],
                'contract_type' => ['column' => 'nm_usage_rights', 'compare' => 'IN', 'prefix' => 'c', 'value' => $arrayHelper->filterEmptyStringArr($filterParams['contract_type'])],
                'farming' => ['column' => 'farming_id', 'compare' => 'IN', 'prefix' => 'c', 'value' => $arrayHelper->filterEmptyStringArr($filterParams['farming'])],
                'date_from' => ['column' => 'c_date', 'compare' => '>=   ', 'prefix' => 'c', 'value' => $filterParams['date_from']],
                'date_to' => ['column' => 'c_date', 'compare' => '<=   ', 'prefix' => 'c', 'value' => $filterParams['date_to']],
                // Филтри свързани със собственици
                'owner_egn' => ['column' => 'egn', 'compare' => '=', 'prefix' => 'o', 'value' => $filterParams['owner_egn']],
                'rep_egn' => ['column' => 'rep_egn', 'compare' => '=', 'prefix' => 'o_r', 'value' => $filterParams['rep_egn']],
                'company_name' => ['column' => 'company_name', 'compare' => 'ILIKE', 'prefix' => 'o', 'value' => $filterParams['company_name']],
                'company_eik' => ['column' => 'eik', 'compare' => '=', 'prefix' => 'o', 'value' => $filterParams['company_eik']],

                'is_annex' => ['column' => 'is_annex', 'compare' => '=', 'prefix' => 'c', 'value' => 'false'],
                'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'prefix' => 'c', 'value' => 'false'],
                'annex_action' => ['column' => 'annex_action', 'compare' => '=', 'prefix' => 'cpr', 'value' => 'added'],
            ],
            'start_date' => $filterParams['due_date'],
            'due_date' => $filterParams['start_date'],
            'include_subleases' => $filterParams['include_subleases'],
            'exclude_sold_plots' => $ownPlotsRequested,
        ];

        if (1 == $filterParams['contract_status']) {
            $options['where']['active'] = ['column' => 'active', 'compare' => '=', 'value' => 'FALSE', 'prefix' => 'c'];
        }

        if (2 == $filterParams['contract_status']) {
            $options['where']['due_date'] = ['column' => "(CASE WHEN c.due_date IS NULL THEN '9999-12-31' ELSE c.due_date END)", 'compare' => '>=', 'value' => $filterParams['due_date']];
            $options['where']['active'] = ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'TRUE'];
        }

        if (3 == $filterParams['contract_status']) {
            $options['where']['due_date'] = ['column' => "(CASE WHEN c.due_date IS NULL THEN '9999-12-31' ELSE c.due_date END)", 'compare' => '<', 'value' => $filterParams['due_date']];
            $options['where']['active'] = ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'TRUE'];
        }

        // check to see whether irrigation should be considered
        if ($filterParams['irrigated_area'] && 'all' != $filterParams['irrigated_area']) {
            $options['where']['irrigated_area'] = ['column' => 'irrigated_area', 'compare' => '=', 'prefix' => 'kvs', 'value' => ($filterParams['irrigated_area'])];
        }

        if ($filterParams['owner_name']) {
            $tmp_owner_names = preg_replace('/\s+/', '.*', $filterParams['owner_name']);
            $tmp_owner_names = mb_strtolower($tmp_owner_names, 'UTF-8');
            array_push($options['return'], "regexp_matches(lower(TRIM (o.NAME)) || ' ' || lower(TRIM (o.surname)) || ' ' || lower(TRIM (o.lastname)), '{$tmp_owner_names}','g')");
        }

        if ($filterParams['rep_name']) {
            $tmp_rep_names = preg_replace('/\s+/', '.*', $filterParams['rep_name']);
            $tmp_rep_names = mb_strtolower($tmp_rep_names, 'UTF-8');
            array_push($options['return'], "regexp_matches(lower(TRIM (o_r.name)) || ' ' || lower(TRIM (o_r.surname)) || ' ' || lower(TRIM (o_r.lastname)), '{$tmp_rep_names}','g')");
        }

        if ($filterParams['ime_subekt']) {
            $tmp_subekt_names = preg_replace('/\s+/', '.*', $filterParams['ime_subekt']);
            $tmp_subekt_names = mb_strtolower($tmp_subekt_names, 'UTF-8');
            array_push($options['return'], "regexp_matches(lower(TRIM (tkvs.ime_subekt)), '{$tmp_subekt_names}','g')");
        }

        if ($filterParams['egn_subekt']) {
            $tmp_subekt_egn = preg_replace('/\s+/', '.*', $filterParams['egn_subekt']);
            $tmp_subekt_egn = mb_strtolower($tmp_subekt_egn, 'UTF-8');
            array_push($options['return'], "regexp_matches(lower(TRIM (tkvs.egn_subekt)), '{$tmp_subekt_egn}','g')");
        }

        $sqlString = $UserDbPlotsController->getDataForTotalAreaReport($options, false, true);

        $extent_query = "SELECT st_extent(a.geom) as extent from ({$sqlString}) as a";
        $maxextent = $UserDbPlotsController->DbHandler->getDataByQuery($extent_query);

        return $this->getDataMap($maxextent, $sqlString);
    }

    /**
     * Show maps by type.
     *
     * @api-method mapReportsByType
     *
     * @param array $param
     *                     {
     *                     #item date report_date
     *                     #item date report_date_to
     *                     #item string type
     *                     #item integer report_farming
     *                     #item string report_ekate
     *                     }
     *
     * @throws MTRpcException
     *
     * @return array
     */
    public function mapReportsByType($param)
    {
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);

        // check if date is not valid
        if ($param['report_date'] && !DateTime::createFromFormat('Y-m-d', $param['report_date'])) {
            $redirect = 'index.php?page=Plots.Home';

            throw new MTRpcException('', '', $redirect);
        }

        switch ($param['type']) {
            case 'own_plots':
                $options = [
                    'return' => [
                        'DISTINCT(kvs_gid) as gid', 'kad_ident', 'kvs_geom as geom',
                    ],
                    'where' => [
                        'contract_type' => ['column' => 'nm_usage_rights', 'compare' => '=', 'value' => 1],
                        'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'value' => 'false'],
                        'is_annnex' => ['column' => 'is_annex', 'compare' => '=', 'value' => 'false'],
                        'active' => ['column' => 'active', 'compare' => '=', 'value' => 'true'],
                        'farming_id' => ['column' => 'farming_id', 'compare' => '=', 'value' => $param['report_farming']],
                        'ekate' => ['column' => 'kvs_ekate', 'compare' => '=', 'value' => $param['report_ekate']],
                        'start_date' => ['column' => 'start_date', 'compare' => '<=', 'value' => $param['report_date']],
                        'c_date' => ['column' => 'c_date', 'compare' => '>=', 'value' => $param['report_contract_date']],
                        'c_date_to' => ['column' => 'c_date', 'compare' => '<=', 'value' => $param['report_contract_date_to']],
                        'mestnost' => ['column' => 'mestnost', 'compare' => 'ILIKE', 'value' => $param['report_mestnost']],
                    ],
                ];
                if ($param['report_date_as_of']) {
                    $options['where']['start_date'] = ['column' => 'start_date', 'compare' => '<=', 'value' => $param['report_date_as_of']];
                }

                $sqlString = $UserDbPlotsController->getFullPlotData($options, false, true);
                $options['return'] = ['ST_Extent(kvs_geom) as extent'];
                $maxextent = $UserDbPlotsController->getFullPlotData($options, false, false);

                break;
            case 'subleased':
                $options = [
                    'return' => [
                        'DISTINCT(gid) as gid', 'kad_ident', 'geom as geom',
                    ],
                    'where' => [
                        'contract_type' => ['column' => 'nm_usage_rights', 'compare' => '=', 'prefix' => 'c1', 'value' => 1],
                        'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'prefix' => 'c', 'value' => 'true'],
                        'active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'true'],
                        'farming_id' => ['column' => 'farming_id', 'compare' => '=', 'prefix' => 'c', 'value' => $param['report_farming']],
                        'ekate' => ['column' => 'ekate', 'compare' => '=', 'prefix' => 'kvs', 'value' => $param['report_ekate']],
                        'start_date' => ['column' => 'start_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $param['report_date']],
                        'due_date' => ['column' => 'due_date', 'compare' => '>=', 'prefix' => 'c', 'value' => $param['report_date_to']],
                        'c_date' => ['column' => 'c_date', 'compare' => '>=', 'prefix' => 'c', 'value' => $param['report_contract_date']],
                        'c_date_to' => ['column' => 'c_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $param['report_contract_date_to']],
                        'mestnost' => ['column' => 'mestnost', 'compare' => 'ILIKE', 'value' => $param['report_mestnost']],
                    ],
                ];
                if ($param['report_date_as_of']) {
                    $options['where']['start_date'] = ['column' => 'start_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $param['report_date_as_of']];
                    $options['where']['due_date'] = ['column' => "(CASE WHEN c.due_date IS NULL THEN '9999-12-31' ELSE c.due_date END)", 'compare' => '>=', 'value' => $param['report_date_as_of']];
                }
                $sqlString = $UserDbPlotsController->getSubleasedPlotsReport($options, false, true);
                $options['return'] = ['geom'];
                $options['extent'] = 'ST_Extent(geom) as extent';
                $maxextent = $UserDbPlotsController->getSubleasedPlotsReport($options, false, false);

                break;
            case 'for_sublease':
                $options = [
                    'return' => ['DISTINCT(gid)'],
                    'where' => [
                        'contract_type' => ['column' => 'nm_usage_rights', 'compare' => '=', 'prefix' => 'c1', 'value' => 1],
                        'active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c1', 'value' => 'true'],
                        'sublease_active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'true'],
                    ],
                ];

                $results = $UserDbPlotsController->getSubleasedPlotsReport($options, false, false);
                $resultsCount = count($results);

                $gid_array = [];
                for ($i = 0; $i < $resultsCount; $i++) {
                    $gid_array[] = $results[$i]['gid'];
                }

                $options = [
                    'return' => [
                        'DISTINCT(kvs_gid) as gid', 'kad_ident', 'kvs_geom as geom',
                    ],
                    'where' => [
                        'contract_type' => ['column' => 'nm_usage_rights', 'compare' => '=', 'value' => 1],
                        'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'value' => 'false'],
                        'is_annnex' => ['column' => 'is_annex', 'compare' => '=', 'value' => 'false'],
                        'active' => ['column' => 'active', 'compare' => '=', 'value' => 'true'],
                        'farming_id' => ['column' => 'farming_id', 'compare' => '=', 'value' => $param['report_farming']],
                        'ekate' => ['column' => 'kvs_ekate', 'compare' => '=', 'value' => $param['report_ekate']],
                        'start_date' => ['column' => 'start_date', 'compare' => '<=', 'value' => $param['report_date_to']],
                        'due_date' => ['column' => "(CASE WHEN due_date IS NULL THEN '9999-12-31' ELSE due_date END)", 'compare' => '>=', 'value' => $param['report_date']],
                        'c_date' => ['column' => 'c_date', 'compare' => '>=', 'value' => $param['report_contract_date']],
                        'c_date_to' => ['column' => 'c_date', 'compare' => '<=', 'value' => $param['report_contract_date_to']],
                        'mestnost' => ['column' => 'mestnost', 'compare' => 'ILIKE', 'value' => $param['report_mestnost']],
                    ],
                    'plots_anti_id_string' => implode(',', $gid_array),
                ];
                if ($param['report_date_as_of']) {
                    $options['where']['start_date'] = ['column' => 'start_date', 'compare' => '<=', 'value' => $param['report_date_as_of']];
                    // HACK
                    // Договорите за собственост имат due_date null, и ако се остави null договорите за собственост няма да се включат
                    $options['where']['due_date'] = ['column' => "(CASE WHEN due_date IS NULL THEN '9999-12-31' ELSE due_date END)", 'compare' => '>=', 'value' => $param['report_date_as_of']];
                }

                $sqlString = $UserDbPlotsController->getFullPlotData($options, false, true);
                // var_export($sqlString);die();
                $options['return'] = ['ST_Extent(kvs_geom) as extent'];
                $maxextent = $UserDbPlotsController->getFullPlotData($options, false, false);

                break;
            case 'hypothecs':
                if ($param['report_date_as_of']) {
                    $param['report_date'] = $param['report_date_as_of'];
                    $param['report_date_to'] = $param['report_date_as_of'];
                }
                $options = [
                    'return' => [
                        'DISTINCT(kvs_gid) as gid', 'kad_ident', 'kvs_geom as geom',
                    ],
                    'where' => [
                        'h_start_date' => ['column' => 'start_date', 'compare' => '<=', 'prefix' => 'h', 'value' => $param['report_date']],
                        'h_due_date' => ['column' => 'due_date', 'compare' => '>=', 'prefix' => 'h', 'value' => $param['report_date_to']],
                        'h_date' => ['column' => 'date', 'compare' => '>=', 'prefix' => 'h', 'value' => $param['report_contract_date']],
                        'h_date_to' => ['column' => 'date', 'compare' => '<=', 'prefix' => 'h', 'value' => $param['report_contract_date_to']],
                        'farming_id' => ['column' => 'farming_id', 'compare' => '=', 'prefix' => 'h', 'value' => $param['report_farming']],
                        'ekate' => ['column' => 'ekate', 'compare' => '=', 'prefix' => 'kvs', 'value' => $param['report_ekate']],
                        'mestnost' => ['column' => 'mestnost', 'compare' => 'ILIKE', 'prefix' => 'kvs', 'value' => $param['report_mestnost']],
                    ],
                ];

                $sqlString = $UserDbPlotsController->getHypothecsPlotsReport($options, false, true);
                $options['return'] = ['geom'];
                $options['extent'] = 'ST_Extent(geom) as extent';
                $maxextent = $UserDbPlotsController->getHypothecsPlotsReport($options, false, false);

                break;
            case 'for_hypothec':
                $options = [
                    'return' => ['DISTINCT(gid)'],
                ];

                $results = $UserDbPlotsController->getHypothecsPlotsReport($options, false, false);
                $resultsCount = count($results);

                $gid_array = [];
                for ($i = 0; $i < $resultsCount; $i++) {
                    $gid_array[] = $results[$i]['gid'];
                }

                $options = [
                    'return' => [
                        'DISTINCT(kvs_gid) as gid', 'kad_ident', 'kvs_geom as geom',
                    ],
                    'where' => [
                        'contract_type' => ['column' => 'nm_usage_rights', 'compare' => '=', 'value' => 1],
                        'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'value' => 'false'],
                        'is_annnex' => ['column' => 'is_annex', 'compare' => '=', 'value' => 'false'],
                        'active' => ['column' => 'active', 'compare' => '=', 'value' => 'true'],
                        'farming_id' => ['column' => 'farming_id', 'compare' => '=', 'value' => $param['report_farming']],
                        'ekate' => ['column' => 'ekate', 'compare' => '=', 'value' => $param['report_ekate']],
                        'start_date' => ['column' => 'start_date', 'compare' => '<=', 'value' => $param['report_date_to']],
                        'due_date' => ['column' => "(CASE WHEN due_date IS NULL THEN '9999-12-31' ELSE due_date END)", 'compare' => '>=', 'value' => $param['report_date']],
                        'c_date' => ['column' => 'c_date', 'compare' => '>=', 'value' => $param['report_contract_date']],
                        'c_date_to' => ['column' => 'c_date', 'compare' => '<=', 'value' => $param['report_contract_date_to']],
                        'mestnost' => ['column' => 'mestnost', 'compare' => 'ILIKE', 'value' => $param['report_mestnost']],
                    ],
                    'plots_anti_id_string' => implode(',', $gid_array),
                ];

                if ($param['report_date_as_of']) {
                    $options['where']['start_date'] = ['column' => 'start_date', 'compare' => '<=', 'value' => $param['report_date_as_of']];
                    // HACK
                    // Договорите за собственост имат due_date null, и ако се остави null договорите за собственост няма да се включат
                    $options['where']['due_date'] = ['column' => "(CASE WHEN due_date IS NULL THEN '9999-12-31' ELSE due_date END)", 'compare' => '>=', 'value' => $param['report_date_as_of']];
                }

                $sqlString = $UserDbPlotsController->getFullPlotData($options, false, true);
                $options['return'] = ['ST_Extent(kvs_geom) as extent'];
                $maxextent = $UserDbPlotsController->getFullPlotData($options, false, false);

                break;
            case 'rented_plots':
                $reportDateFrom = $param['report_date'];
                $reportDate = $param['report_date_to'];
                $reportDateAsOf = $param['report_date_as_of'];
                $reportContractDate = $param['report_contract_date'];
                $reportContractDateTo = $param['report_contract_date_to'];
                $reportFarming = $param['report_farming'];
                $reportEkate = $param['report_ekate'];
                $reportNtp = $param['report_ntp'];
                $reportCategory = $param['report_category'];
                $reportMestnost = $param['report_mestnost'];
                $reportIncludeSubleased = $param['report_include_subleases'];
                $reportIrrigation = null;
                if ('all' != $param['report_irrigation']) {
                    $reportIrrigation = $param['report_irrigation'];
                }

                // Вземане на всички преотдадени pc_rel_id
                $pc_rel_ids = $UserDbPlotsController->getSubleasePcRelIds($reportDateFrom, $reportDate);

                $pc_rel_ids = array_map('intval', explode(',', trim($pc_rel_ids[0]['pc_rel_ids'], '{}')));

                if (!count($pc_rel_ids)) {
                    $pc_rel_ids[] = 0;
                }

                // Вземане на всички договори, от които е преотдавано
                $options = [
                    'tablename' => $UserDbPlotsController->DbHandler->contractsPlotsRelTable,
                    'return' => [
                        'array_agg(distinct (contract_id)) as contract_ids',
                    ],
                    'where' => [
                        'id' => ['column' => 'id', 'compare' => 'IN', 'value' => $pc_rel_ids],
                    ],
                ];

                $contract_ids = $UserDbPlotsController->getItemsByParams($options);

                $contract_ids = array_map('intval', explode(',', trim($contract_ids[0]['contract_ids'], '{}')));

                // Вземане на всички преотдадени имоти
                $options = [
                    'tablename' => $UserDbPlotsController->DbHandler->contractsPlotsRelTable,
                    'return' => [
                        'array_agg(distinct (plot_id)) as plot_ids',
                    ],
                    'where' => [
                        'id' => ['column' => 'id', 'compare' => 'IN', 'value' => $pc_rel_ids],
                    ],
                ];

                $plot_ids = $UserDbPlotsController->getItemsByParams($options);

                $plot_ids = array_map('intval', explode(',', trim($plot_ids[0]['plot_ids'], '{}')));

                // Вземане на всички анекси, от чийто оригинален договор е преотдавано
                $options = [
                    'tablename' => $UserDbPlotsController->DbHandler->tableContracts,
                    'return' => [
                        'array_agg(distinct (id)) as annex_ids',
                    ],
                    'where' => [
                        'parent_id' => ['column' => 'parent_id', 'compare' => 'IN', 'value' => $contract_ids],
                    ],
                ];

                $annex_ids = $UserDbPlotsController->getItemsByParams($options);

                $annex_ids = array_map('intval', explode(',', trim($annex_ids[0]['annex_ids'], '{}')));
                $contract_ids = array_merge($contract_ids, $annex_ids);
                if (!count($contract_ids)) {
                    $contract_ids[] = 0;
                }
                $contracts_ids_string = implode(',', $contract_ids);
                if (!count($plot_ids)) {
                    $plot_ids[] = 0;
                }
                $plot_ids_string = implode(',', $plot_ids);
                $options = [
                    'return' => [
                        'DISTINCT(kvs.gid) as gid', 'kad_ident', 'kvs.geom as geom',
                    ],
                    'where' => [
                        'active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'true'],
                        'annex_action' => ['column' => 'annex_action', 'compare' => '=', 'prefix' => 'cpr', 'value' => 'added'],
                        'c_date' => ['column' => 'c_date', 'compare' => '>=', 'prefix' => 'c', 'value' => $reportContractDate],
                        'c_date_to' => ['column' => 'c_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $reportContractDateTo],
                    ],
                    'group' => 'kvs.gid, cpr.id, c.id, pf.id, cpr.contract_area, cpr.price_per_acre, cpr.price_sum',
                    'start_date' => $reportDate,
                    'due_date' => $reportDateFrom,
                    'include_subleases' => $reportIncludeSubleased,
                    'contract_ids' => $contracts_ids_string,
                    'plot_ids' => $plot_ids_string,
                ];

                if ($reportFarming) {
                    $options['where']['farming_id'] = ['column' => 'farming_id', 'compare' => '=', 'prefix' => 'c', 'value' => $reportFarming];
                }
                if ($reportEkate) {
                    $options['where']['ekate'] = ['column' => 'ekate', 'compare' => '=', 'prefix' => 'kvs', 'value' => $reportEkate];
                }
                if ($reportNtp) {
                    $options['where']['ntp'] = ['column' => 'area_type', 'compare' => '=', 'prefix' => 'kvs', 'value' => $reportNtp];
                }
                if ($reportCategory) {
                    $options['where']['category'] = ['column' => 'category', 'compare' => '=', 'prefix' => 'kvs', 'value' => $reportCategory];
                }
                if ($reportMestnost) {
                    $options['where']['mestnost'] = ['column' => 'mestnost', 'compare' => '=', 'prefix' => 'kvs', 'value' => $reportMestnost];
                }

                if ($reportIrrigation) {
                    $options['where']['irrigated_area'] = ['column' => 'irrigated_area', 'compare' => '=', 'prefix' => 'kvs', 'value' => $reportIrrigation];
                }

                if ($reportDateAsOf) {
                    $options['start_date'] = $reportDateAsOf;
                    $options['due_date'] = $reportDateAsOf;
                }

                $sqlString = $UserDbPlotsController->getRentedPlotsForReport($options, false, true);
                $options['return'] = ['geom'];
                $options['extent'] = 'ST_Extent(geom) as extent';
                $maxextent = $UserDbPlotsController->getRentedPlotsForReport($options, false, false);

                break;
            case 'used_plots':
                $reportDateFrom = $param['report_date'];
                $reportDate = $param['report_date_to'];
                $reportDateAsOf = $param['report_date_as_of'];
                $reportContractDate = $param['report_contract_date'];
                $reportContractDateTo = $param['report_contract_date_to'];
                $reportFarming = $param['report_farming'];
                $reportEkate = $param['report_ekate'];
                $reportNtp = $param['report_ntp'];
                $reportCategory = $param['report_category'];
                $reportMestnost = $param['report_mestnost'];
                $reportIncludeSubleased = $param['report_include_subleases'];
                $reportParticipation = $param['report_choose_participation'];
                $reportIrrigation = null;
                if ('all' != $param['report_irrigation']) {
                    $reportIrrigation = $param['report_irrigation'];
                }

                // Вземане на всички преотдадени pc_rel_id
                $pc_rel_ids = $UserDbPlotsController->getSubleasePcRelIds($reportDateFrom, $reportDate);

                $pc_rel_ids = array_map('intval', explode(',', trim($pc_rel_ids[0]['pc_rel_ids'], '{}')));

                if (!count($pc_rel_ids)) {
                    $pc_rel_ids[] = 0;
                }

                // Вземане на всички договори, от които е преотдавано
                $options = [
                    'tablename' => $UserDbPlotsController->DbHandler->contractsPlotsRelTable,
                    'return' => [
                        'array_agg(distinct (contract_id)) as contract_ids',
                    ],
                    'where' => [
                        'id' => ['column' => 'id', 'compare' => 'IN', 'value' => $pc_rel_ids],
                    ],
                ];

                $contract_ids = $UserDbPlotsController->getItemsByParams($options);

                $contract_ids = array_map('intval', explode(',', trim($contract_ids[0]['contract_ids'], '{}')));

                // Вземане на всички преотдадени имоти
                $options = [
                    'tablename' => $UserDbPlotsController->DbHandler->contractsPlotsRelTable,
                    'return' => [
                        'array_agg(distinct (plot_id)) as plot_ids',
                    ],
                    'where' => [
                        'id' => ['column' => 'id', 'compare' => 'IN', 'value' => $pc_rel_ids],
                    ],
                ];

                $plot_ids = $UserDbPlotsController->getItemsByParams($options);

                $plot_ids = array_map('intval', explode(',', trim($plot_ids[0]['plot_ids'], '{}')));

                // Вземане на всички анекси, от чийто оригинален договор е преотдавано
                $options = [
                    'tablename' => $UserDbPlotsController->DbHandler->tableContracts,
                    'return' => [
                        'array_agg(distinct (id)) as annex_ids',
                    ],
                    'where' => [
                        'parent_id' => ['column' => 'parent_id', 'compare' => 'IN', 'value' => $contract_ids],
                    ],
                ];

                $annex_ids = $UserDbPlotsController->getItemsByParams($options);

                $annex_ids = array_map('intval', explode(',', trim($annex_ids[0]['annex_ids'], '{}')));
                $contract_ids = array_merge($contract_ids, $annex_ids);
                if (!count($contract_ids)) {
                    $contract_ids[] = 0;
                }
                $contracts_ids_string = implode(',', $contract_ids);
                if (!count($plot_ids)) {
                    $plot_ids[] = 0;
                }
                $plot_ids_string = implode(',', $plot_ids);

                $options = [
                    'return' => [
                        'DISTINCT(kvs.gid) as gid', 'kad_ident', 'kvs.geom as geom',
                    ],
                    'where' => [
                        'c_date' => ['column' => 'c_date', 'compare' => '>=', 'prefix' => 'c', 'value' => $reportContractDate],
                        'c_date_to' => ['column' => 'c_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $reportContractDateTo],
                    ],
                    'start_date' => $reportDate,
                    'due_date' => $reportDateFrom,
                    'include_subleases' => $reportIncludeSubleased,
                    'contract_ids' => $contracts_ids_string,
                    'plot_ids' => $plot_ids_string,
                    'group' => 'kvs.gid, cpr.id, c.id, a.id, pf.id, cpr.contract_area, cpr.price_per_acre, cpr.price_sum',
                ];

                if ($reportFarming) {
                    $options['where']['farming_id'] = ['column' => 'farming_id', 'compare' => '=', 'prefix' => 'c', 'value' => $reportFarming];
                }
                if ($reportEkate) {
                    $options['where']['ekate'] = ['column' => 'ekate', 'compare' => '=', 'prefix' => 'kvs', 'value' => $reportEkate];
                }
                if ($reportNtp) {
                    $options['where']['ntp'] = ['column' => 'area_type', 'compare' => '=', 'prefix' => 'kvs', 'value' => $reportNtp];
                }
                if ($reportCategory) {
                    $options['where']['category'] = ['column' => 'category', 'compare' => '=', 'prefix' => 'kvs', 'value' => $reportCategory];
                }
                if ($reportMestnost) {
                    $options['where']['mestnost'] = ['column' => 'mestnost', 'compare' => '=', 'prefix' => 'kvs', 'value' => $reportMestnost];
                }
                if ($reportIrrigation) {
                    $options['where']['irrigated_area'] = ['column' => 'irrigated_area', 'compare' => '=', 'prefix' => 'kvs', 'value' => $reportIrrigation];
                }
                if ($reportDateAsOf) {
                    $options['start_date'] = $reportDateAsOf;
                    $options['due_date'] = $reportDateAsOf;
                }

                if ($reportParticipation) {
                    switch ($reportParticipation) {
                        case 'participate':
                            $options['where']['participate'] = ['column' => 'participate', 'compare' => '=', 'prefix' => 'kvs', 'value' => 'true'];
                            $options['where']['include'] = ['column' => 'include', 'compare' => '=', 'prefix' => 'kvs', 'value' => 'false'];
                            $options['where']['white_spots'] = ['column' => 'white_spots', 'compare' => '=', 'prefix' => 'kvs', 'value' => 'false'];

                            break;
                        case 'no_participate':
                            $options['where']['participate'] = ['column' => 'participate', 'compare' => '=', 'prefix' => 'kvs', 'value' => 'false'];
                            $options['where']['include'] = ['column' => 'include', 'compare' => '=', 'prefix' => 'kvs', 'value' => 'true'];
                            $options['where']['white_spots'] = ['column' => 'white_spots', 'compare' => '=', 'prefix' => 'kvs', 'value' => 'false'];

                            break;
                        case 'without':
                            $options['where']['participate'] = ['column' => 'participate', 'compare' => '=', 'prefix' => 'kvs', 'value' => 'false'];
                            $options['where']['include'] = ['column' => 'include', 'compare' => '=', 'prefix' => 'kvs', 'value' => 'false'];
                            $options['where']['white_spots'] = ['column' => 'white_spots', 'compare' => '=', 'prefix' => 'kvs', 'value' => 'false'];

                            break;
                        default:
                            break;
                    }
                }

                $sqlString = $UserDbPlotsController->getUsedPlotsForReport($options, false, true);

                $options['return'] = ['geom'];
                $options['extent'] = 'ST_Extent(geom) as extent';

                $maxextent = $UserDbPlotsController->getUsedPlotsForReport($options, false, false);

                break;
            case 'subleased_rented_plots':
                $options = [
                    'return' => [
                        'DISTINCT(gid)', 'kad_ident', 'geom',
                    ],
                    'where' => [
                        'contract_type1' => ['column' => 'nm_usage_rights', 'compare' => '<>', 'prefix' => 'c1', 'value' => 1],
                        'contract_type2' => ['column' => 'nm_usage_rights', 'compare' => '<>', 'prefix' => 'c1', 'value' => 4],
                        'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'prefix' => 'c', 'value' => 'true'],
                        'active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'true'],
                        'ekate' => ['column' => 'ekate', 'compare' => '=', 'prefix' => 'kvs', 'value' => $param['report_ekate']],
                        'mestnost' => ['column' => 'mestnost', 'compare' => 'ILIKE', 'prefix' => 'kvs', 'value' => $param['report_mestnost']],
                        'c_date' => ['column' => 'c_date', 'compare' => '>=', 'prefix' => 'c', 'value' => $param['report_contract_date']],
                        'c_date_to' => ['column' => 'c_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $param['report_contract_date_to']],
                    ],
                    'start_date' => $param['report_date'],
                    'due_date' => $param['report_date'],
                ];

                $sqlString = $UserDbPlotsController->getSubleasedPlotsReport($options, false, true);
                $options['return'] = ['geom'];
                $options['extent'] = 'ST_Extent(geom) as extent';
                $maxextent = $UserDbPlotsController->getSubleasedPlotsReport($options, false, false);

                break;
            case 'rented_expiring_contracts':
                $reportDateFrom = $param['report_date'];
                $reportDate = $param['report_date_to'];
                $reportContractDate = $param['report_contract_date'];
                $reportContractDateTo = $param['report_contract_date_to'];
                $reportFarming = $param['report_farming'];
                $reportEkate = $param['report_ekate'];
                $reportNtp = $param['report_ntp'];
                $reportCategory = $param['report_category'];
                $reportMestnost = $param['report_mestnost'];
                $reportArendator = '' == $param['report_arendator'] ? null : $param['report_arendator'];
                $reportRenewed = $param['report_choose_renewed'];
                $reportIrrigation = null;
                if ('all' != $param['report_irrigation']) {
                    $reportIrrigation = $param['report_irrigation'];
                }

                $options = [
                    'return' => [
                        'gid', 'kad_ident', 'geom', 'new_c_info.c_info',
                    ],
                    'where' => [
                        'c_date' => ['column' => 'c_date', 'compare' => '>=', 'prefix' => 'c', 'value' => $reportContractDate],
                        'c_date_to' => ['column' => 'c_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $reportContractDateTo],
                    ],
                    'group' => 'kvs.gid, pc. ID, C . ID, pf. ID, new_c_info.c_info',
                ];

                $options['start_date'] = $reportDateFrom;
                $options['due_date'] = $reportDate;

                $tmpNewStartDate = date(date('Y', strtotime($options['due_date'])) . '-10-01');
                if ($tmpNewStartDate < $options['due_date']) {
                    $tmpNewStartDate = date('Y-m-d', strtotime($tmpNewStartDate . ' + 1 year'));
                }

                $tmpNewDueDate = date('Y-m-d', strtotime($tmpNewStartDate . ' + 1 year - 1 day'));

                $options['new_start_date'] = $tmpNewStartDate;
                $options['new_due_date'] = $tmpNewDueDate;
                $owner_ids = [];

                if ('' != $reportArendator) {
                    $owners_options = [
                        'return' => [
                            'DISTINCT id',
                        ],
                    ];

                    $tmp_arendator_names = preg_replace('/\s+/', '.*', $reportArendator);
                    $tmp_arendator_names = mb_strtolower($tmp_arendator_names, 'UTF-8');

                    $tmp_arendator_names_string = "regexp_matches(lower(TRIM (name)) || ' ' || lower(TRIM (surname)) || ' ' || lower(TRIM (lastname)),'{$tmp_arendator_names}','g')";
                    $tmp_arendator_company = "regexp_matches(lower(TRIM (company_name)),'{$tmp_arendator_names}','g')";
                    $tmp_names = "(CASE WHEN owner_type = 1 THEN {$tmp_arendator_names_string} ELSE {$tmp_arendator_company} END) as names";
                    array_push($owners_options['return'], $tmp_names);
                    $owners = $UserDbOwnersController->getOwnersData($owners_options, false, false);

                    foreach ($owners as $owner => $value) {
                        $owner_ids[] = $value['id'];
                    }
                }

                if ($reportFarming) {
                    $options['where']['farming_id'] = ['column' => 'farming_id', 'compare' => '=', 'prefix' => 'c', 'value' => $reportFarming];
                }
                if ($reportNtp) {
                    $options['where']['ntp'] = ['column' => 'area_type', 'compare' => '=', 'value' => $reportNtp];
                }
                if ($reportEkate) {
                    $options['where']['ekate'] = ['column' => 'ekate', 'compare' => '=', 'value' => $reportEkate];
                }
                if ($reportCategory) {
                    $options['where']['category'] = ['column' => 'category', 'compare' => '=', 'value' => $reportCategory];
                }
                if ($reportMestnost) {
                    $options['where']['mestnost'] = ['column' => 'mestnost', 'compare' => '=', 'value' => $reportMestnost];
                }

                if ($reportIrrigation) {
                    $options['where']['irrigated_area'] = ['column' => 'irrigated_area', 'compare' => '=', 'prefix' => 'kvs', 'value' => $reportIrrigation];
                }

                if (count($owner_ids) > 0) {
                    $options['where']['arendator'] = ['column' => 'owner_id', 'compare' => 'IN', 'prefix' => 'po', 'value' => $owner_ids];
                }

                if ('' != $reportRenewed) {
                    $tmpRenewed = 'true' === $reportRenewed ? true : false;
                    $options['renewed'] = $tmpRenewed;
                }

                $sqlString = $UserDbPlotsController->getPlotsInExpiringContractsForReport($options, false, true);
                $options['return'] = ['geom'];
                $options['extent'] = 'ST_Extent(geom) as extent';
                $maxextent = $UserDbPlotsController->getPlotsInExpiringContractsForReport($options, false, false);

                break;
            case 'plots_in_many_contracts':
                if ('' == $param['report_date'] && '' == $param['report_date_to']) {
                    $param['report_date_to'] = '9999-01-01';
                    $param['report_date'] = '1900-01-01';
                }

                $options = [
                    'return' => [
                        'gid', 'kad_ident', 'geom',
                    ],
                    'where' => [
                        'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'prefix' => 'c', 'value' => 'false'],
                        'is_annnex' => ['column' => 'is_annex', 'compare' => '=', 'prefix' => 'c', 'value' => 'false'],
                        'annex_action' => ['column' => 'annex_action', 'compare' => '=', 'prefix' => 'pc', 'value' => 'added'],
                        'farming_id' => ['column' => 'farming_id', 'compare' => '=', 'prefix' => 'c', 'value' => $param['report_farming']],
                        'ekate' => ['column' => 'ekate', 'compare' => '=', 'prefix' => 'kvs', 'value' => $param['report_ekate']],
                        'c_type' => ['column' => 'nm_usage_rights', 'compare' => '<>', 'prefix' => 'c', 'value' => 4],
                        'active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'true'],
                        'c_date' => ['column' => 'c_date', 'compare' => '>=', 'prefix' => 'c', 'value' => $param['report_contract_date']],
                        'c_date_to' => ['column' => 'c_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $param['report_contract_date_to']],
                        'mestnost' => ['column' => 'mestnost', 'compare' => 'ILIKE', 'prefix' => 'kvs', 'value' => $param['report_mestnost']],
                    ],
                    'group' => 'kvs.gid',
                    'having' => 'count(DISTINCT(c.id)) > 1',
                ];

                if ($param['report_date_as_of']) {
                    $options['start_date'] = $param['report_date_as_of'];
                    $options['due_date'] = $param['report_date_as_of'];
                }

                $sqlString = $UserDbPlotsController->getFullPlotDataForReport($options, false, true);
                // var_export($sqlString);die();
                $options['return'] = ['geom'];
                $options['extent'] = 'ST_Extent(geom) as extent';
                $maxextent = $UserDbPlotsController->getFullPlotDataForReport($options, false, false);

                break;
            case 'contracts_with_ownerless_plots':
                $UserDbController = new UserDbController($this->User->Database);
                $salesStartDate = $param['report_date_as_of'] ? $param['report_date_as_of'] : $param['report_date_to'];

                if ('' != $salesStartDate) {
                    $soldPlots = $UserDbController->getSoldPlotsAfterDate($salesStartDate);
                }

                $options = [
                    'return' => [
                        'kvs.kad_ident', 'kvs.geom', 'kvs.gid',
                    ],
                    'where' => [
                        'active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => true],
                        'annex_action' => ['column' => 'annex_action', 'compare' => '=', 'prefix' => 'pc', 'value' => 'added'],
                        'plot_id' => ['column' => 'gid', 'compare' => 'NOT IN', 'prefix' => 'kvs', 'value' => $soldPlots],
                        'farming_id' => ['column' => 'farming_id', 'compare' => '=', 'prefix' => 'c', 'value' => $param['report_farming']],
                        'ekate' => ['column' => 'ekate', 'compare' => '=', 'prefix' => 'kvs', 'value' => $param['report_ekate']],
                        'start_date' => ['column' => 'start_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $param['report_date']],
                        'due_date' => ['column' => 'due_date', 'compare' => '>=', 'prefix' => 'c', 'value' => $param['report_date_to']],
                        'c_date' => ['column' => 'c_date', 'compare' => '>=', 'prefix' => 'c', 'value' => $param['report_contract_date']],
                        'c_date_to' => ['column' => 'c_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $param['report_contract_date_to']],
                        'mestnost' => ['column' => 'mestnost', 'compare' => 'ILIKE', 'prefix' => 'kvs', 'value' => $param['report_mestnost']],
                    ],
                ];

                if ($param['report_date_as_of']) {
                    $options['where']['start_date'] = ['column' => 'start_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $param['report_date_as_of']];
                    // HACK
                    // Договорите за собственост имат due_date null, и ако се остави null договорите за собственост няма да се включат
                    $options['where']['due_date'] = ['column' => "(CASE WHEN c.due_date IS NULL THEN '9999-12-31' ELSE c.due_date END)", 'compare' => '>=', 'value' => $param['report_date_as_of']];
                }
                $sqlString = $UserDbPlotsController->getOwnerlessPlots($options, false, true);

                $options['return'] = ['ST_Extent(geom) as extent'];
                $maxextent = $UserDbPlotsController->getOwnerlessPlots($options, false, false);

                break;
            case 'historical_plots':
                $options = [
                    'return' => [
                        'kvs.kad_ident', 'kvs.geom', 'kvs.gid',
                    ],
                    'where' => [
                        'is_edited' => ['column' => 'is_edited', 'compare' => '=', 'prefix' => 'kvs', 'value' => 'true'],
                        'active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => true],
                        'annex_action' => ['column' => 'annex_action', 'compare' => '=', 'prefix' => 'pc', 'value' => 'added'],
                        'start_date' => ['column' => 'start_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $param['report_date_to']],
                        'farming_id' => ['column' => 'farming_id', 'compare' => '=', 'prefix' => 'c', 'value' => $param['report_farming']],
                        'ekate' => ['column' => 'ekate', 'compare' => '=', 'prefix' => 'kvs', 'value' => $param['report_ekate']],
                        'mestnost' => ['column' => 'mestnost', 'compare' => 'ILIKE', 'prefix' => 'kvs', 'value' => $param['report_mestnost']],
                    ],
                    'due_date' => $param['report_date'],
                ];

                $sqlString = $UserDbPlotsController->getHistoricalPlotsReportData($options, false, true);

                $options['return'] = ['ST_Extent(geom) as extent'];
                $maxextent = $UserDbPlotsController->getHistoricalPlotsReportData($options, false, false);

                break;
        }

        return $this->getDataMap($maxextent, $sqlString);
    }

    private function getDataMap($maxextent, $sqlString, $is_expiring = false)
    {
        $LayersController = new LayersController('Layers');

        $query = "({$sqlString}) as subquery using unique gid using srid=32635";

        $options = [
            'return' => ['t.id', 't.table_name', 't.extent', 't.layer_type', 't.border_color', 't.color'],
            'sort' => 'id',
            'order' => 'DESC',
            'where' => [
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'prefix' => 't', 'value' => $this->User->GroupID],
                'layer_type' => ['column' => 'layer_type', 'compare' => '=', 'prefix' => 't', 'value' => 5],
            ],
        ];

        $result = $LayersController->getLayers($options);

        $data = [];

        $color1 = '000000';
        $color2 = 'ff0000';
        $data['layername'] = 'topic_kvs_layer';
        $data['maxextent'] = $result[0]['extent'];
        $data['host'] = DEFAULT_DB_HOST;
        $data['dbname'] = $this->User->Database;
        $data['username'] = DEFAULT_DB_USERNAME;
        $data['password'] = DEFAULT_DB_PASSWORD;
        $data['port'] = DEFAULT_DB_PORT;
        $data['query'] = $query;
        $data['gid'] = 'gid';
        $data['transparency'] = '100';
        $data['display_label'] = 1;
        $data['classes'][0]['name'] = $layers[$j]['table_name'];
        $data['classes'][0]['border_color'] = hexdec(substr($color1, 0, 2)) . ' ' . hexdec(substr($color1, 2, 2)) . ' ' . hexdec(substr($color1, 4, 2));
        $data['classes'][0]['color'] = hexdec(substr($color2, 0, 2)) . ' ' . hexdec(substr($color2, 2, 2)) . ' ' . hexdec(substr($color2, 4, 2));

        $hexColorArray[] = ['color' => $color2, 'name' => 'Филтрирани', 'iconCls' => 'no-background no-padding'];
        $ltext = $LayersController->StringHelper->loadTemplate($GLOBALS['Templates'][3]['template'], $data);

        @unlink(WMS_MAP_PATH . $this->User->GroupID . '_layer_kvs.map');
        $fp = fopen(WMS_MAP_PATH . $this->User->GroupID . '_layer_kvs.map', 'w');
        $res = fwrite($fp, $ltext);
        fclose($fp);

        $maxextent = $maxextent[0]['extent'];
        $maxextent = str_replace('BOX(', '', $maxextent);
        $maxextent = str_replace(')', '', $maxextent);
        $maxextent = str_replace(' ', ',', $maxextent);
        $returnData['colorarray'] = $hexColorArray;
        $returnData['extent'] = $maxextent;

        return $returnData;
    }
}
