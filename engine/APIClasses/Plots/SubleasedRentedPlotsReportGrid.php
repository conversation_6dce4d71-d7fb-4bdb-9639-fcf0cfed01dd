<?php

namespace TF\Engine\APIClasses\Plots;

use Prado\Web\Services\TRpcApiProvider;
use TF\Application\Common\Config;
use TF\Engine\Kernel\Export2XlsClass;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDbAreaTypes\UserDbAreaTypesController;
use TF\Engine\Plugins\Core\UserDbOwners\UserDbOwnersController;
use TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Справка "Преотдадена/ пренаета земя".
 *
 * @rpc-module Plots
 *
 * @rpc-service-id subleased-rented-plots-report-grid
 */
class SubleasedRentedPlotsReportGrid extends TRpcApiProvider
{
    private $UserDbController;

    private $return = [
        'rows' => [],
        'total' => 0,
        'footer' => [],
    ];

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getSubleasedRentedPlots'],
                'validators' => [
                    'params' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'exportToExcelSubleasedRentedPlotsReportData' => ['method' => [$this, 'exportToExcelSubleasedRentedPlotsReportData'],
                'validators' => [
                    'data' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * Export To Excel Subleased Rented Plots Report Data.
     *
     * @param array $data
     *                    {
     *                    #items array filters
     *                    {
     *                    #item  timestamp report_date
     *                    #item  timestamp report_date_from
     *                    #item  timestamp report_date_as_of
     *                    #item  timestamp report_contract_date
     *                    #item  timestamp report_contract_date_to
     *                    #item  integer report_farming
     *                    #item  integer report_ekate
     *                    #item  integer report_ntp
     *                    #item  integer report_category
     *                    #item  string report_mestnost
     *                    #item  string report_irrigation
     *                    #item  string report_mestnost
     *                    #item  string report_arendator
     *                    }
     *                    }
     * @param int|string $page
     * @param int|string $rows
     * @param ?string $sort
     * @param ?string $order
     *
     * @return array
     */
    public function exportToExcelSubleasedRentedPlotsReportData(array $data, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $results = $this->getSubleasedRentedPlots($data, $page, $rows, $sort, $order);

        $result = $results['rows'];

        unset($result[0]['attributes']);

        $result = $this->formatRowsForExport($result);

        $columns = [
            'Землище',
            'Идентификатор',
            'Местност',
            'Категория',
            'НТП',
            'Площ(дка)',
            'Ползване',
            'Собственик',
            'ЕГН/ЕИК',
            'Арендодател/Наемодател',
            'Арендатор/Наемател',
            'Договор №',
            'Вписване №',
            'Дата на вписване',
            'Валиден до',
            'Рента (пари)',
        ];
        $this->addTotals($result);
        $time = strtotime(date('Y-m-d H:i:s'));
        $fileName = 'preotdadena_prenaeta_zemq_' . $this->User->GroupID . '_' . $time . '.xlsx';
        $path = PUBLIC_UPLOAD_EXPORT . '/' . $this->User->GroupID . '/' . $fileName;

        $export2Xls = new Export2XlsClass();
        $filePath = $export2Xls->exportUrlPath($path, $result, $columns);

        return ['file_path' => $filePath, 'file_name' => $fileName];
    }

    /**
     * getSubleasedRentedPlots.
     *
     * @api-method read
     *
     * @param array $params
     *                      {
     *                      #items array filters
     *                      {
     *                      #item  timestamp report_date
     *                      #item  timestamp report_date_from
     *                      #item  timestamp report_date_as_of
     *                      #item  timestamp report_contract_date
     *                      #item  timestamp report_contract_date_to
     *                      #item  integer report_farming
     *                      #item  integer report_ekate
     *                      #item  integer report_ntp
     *                      #item  integer report_category
     *                      #item  string report_mestnost
     *                      #item  string report_irrigation
     *                      #item  string report_mestnost
     *                      #item  string report_arendator
     *                      }
     *                      }
     * @param int|string $page
     * @param int|string $rows
     * @param ?string $sort
     * @param ?string $order
     *
     * @return array
     */
    public function getSubleasedRentedPlots(array $params, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $contractOperatingFrom = '' == $params['filters']['report_date_from'] ? null : $params['filters']['report_date_from'];
        $contractOperatingTo = '' == $params['filters']['report_date'] ? null : $params['filters']['report_date'];
        $reportDateAsOf = $params['filters']['report_date_as_of'];
        $contractDateConclusionFrom = $params['filters']['report_contract_date'];
        $contractDateConclusionTo = $params['filters']['report_contract_date_to'];
        $reportFarming = $params['filters']['report_farming'];
        $reportEkate = $params['filters']['report_ekate'];
        $reportMasiv = $params['filters']['report_masiv'];
        $reportPlotNumber = $params['filters']['report_plot_number'];
        $reportKadIdent = $params['filters']['report_kad_ident'];
        $reportNtp = $params['filters']['report_ntp'];
        $reportCategory = $params['filters']['report_category'];
        $reportMestnost = $params['filters']['report_mestnost'];
        $reportArendator = '' == $params['filters']['report_arendator'] ? null : $params['filters']['report_arendator'];
        $reportSubleaseType = '' == $params['filters']['report_sublease_type'] ? null : $params['filters']['report_sublease_type'];
        $reportIrrigation = null;
        if ('all' != $params['filters']['report_irrigation']) {
            $reportIrrigation = $params['filters']['report_irrigation'];
        }

        $FarmingController = new FarmingController('Farming');
        $UsersController = new UsersController('Users');
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);

        $farming_array = $FarmingController->getUserFarmings(true);
        $farmingIds = array_keys($farming_array);

        if ($reportFarming && in_array((int) $reportFarming, $farmingIds)) {
            $farmingIds = [(int) $reportFarming];
        }

        // get all group farmings and create array like predefined config
        $final_farming = [];
        $search_farming_ids = [];
        $farming_count = count($farming_array);
        if (0 != $farming_count) {
            foreach ($farming_array as $farming) {
                $currentFarming = mb_strtolower($farming['name'], mb_detect_encoding($farming['name']));
                $currentArendator = mb_strtolower($reportArendator, mb_detect_encoding($reportArendator));
                $currentArendator = '' == $currentArendator ? null : $currentArendator;

                $final_farming[$farming['id']] = $farming;
                if (false !== strpos($currentFarming, $currentArendator)) {
                    $search_farming_ids[] = $farming['id'];
                }
            }
        }

        $owners_options = [
            'return' => [
                'DISTINCT o.id',
            ],
        ];

        $tmp_arendator_names = preg_replace('/\s+/', '.*', $reportArendator);
        $tmp_arendator_names = mb_strtolower($tmp_arendator_names, 'UTF-8');

        $owner_ids = $this->getOwnersIds($UserDbOwnersController, $owners_options);
        // -----
        // get subleased plots by contract with nm_usage_rights = 1
        $options = [
            'return' => [
                'gid', 'c1.farming_id',
            ],
            'where' => [
                // 'contract_type' => array('column' => 'nm_usage_rights', 'compare' => '=', 'prefix' => 'c1', 'value' => 1),
                'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'prefix' => 'c', 'value' => 'true'],
                'active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'true'],
                'createdPlots' => 'true',
                'farming_id' => ['column' => 'farming_id', 'compare' => 'IN', 'prefix' => 'c1', 'value' => $farmingIds],
            ],
            'start_date' => $contractOperatingTo,
        ];

        $results = $UserDbPlotsController->getSubleasedPlotsReport($options);

        $results_count = count($results);
        if (0 == $results_count) {
            return $this->return;
        }

        $gidsArray = [];
        $ownersArray = [];

        foreach ($results as $result) {
            $gidsArray[] = $result['gid'];
            $ownersArray[$result['gid']] = $result['farming_id'];
        }
        $options = [
            'custom_counter' => 'COUNT(DISTINCT(pc.id))',
            'return' => [
                'gid', 'ekate', 'virtual_ekatte_name as land', 'kad_ident', 'mestnost', 'virtual_category_title as category', 'virtual_ntp_title as area_type', 'spa.contract_area as area',
                'c.id as sublease_id', 'c.c_num', 'c.sv_num', "to_char(c.sv_date,'DD.MM.YYYY') as sv_date", "to_char(c.due_date,'DD.MM.YYYY') as due_date", 'c.virtual_contract_type as c_type', 'c.farming_id',
                'pc.price_per_acre', 'pc.price_sum', 'c.renta',
                "string_agg(DISTINCT(CASE WHEN co.owner_type = 1 THEN co.name || ' ' || co.surname || ' ' || co.lastname ELSE co.company_name END), ',<br />') as owner_contragent",
                "string_agg((CASE WHEN o.owner_type = 1 THEN o.egn ELSE o.eik END), ',<br />' ORDER BY o.id) as egn",
                'array_agg(DISTINCT(fc.farming_id)) as farming_id_array',
                "COALESCE ( NULLIF ( trim(BOTH FROM string_agg ( concat_ws ( ' ', o. NAME, o.surname, o.lastname ), ',<br />' ORDER BY o.id)), '' ) , string_agg (o.company_name, ', ' ORDER BY o.id) ) owner_names",
            ],
            'where' => [
                'gid' => ['column' => 'gid', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $gidsArray],
                'contract_type1' => ['column' => 'nm_usage_rights', 'compare' => '<>', 'prefix' => 'c1', 'value' => 1],
                'contract_type2' => ['column' => 'nm_usage_rights', 'compare' => '<>', 'prefix' => 'c1', 'value' => 4],
                'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'prefix' => 'c', 'value' => 'true'],
                'active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'true'],
                'c_date' => ['column' => 'c_date', 'compare' => '>=', 'prefix' => 'c', 'value' => $contractDateConclusionFrom],
                'c_date_to' => ['column' => 'c_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $contractDateConclusionTo],
                'start_date' => ['column' => 'start_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $contractOperatingTo],
                'due_date' => ['column' => 'due_date', 'compare' => '>=', 'prefix' => 'c', 'value' => $contractOperatingFrom],
                'createdPlots' => 'true',
                'farming_id' => ['column' => 'farming_id', 'compare' => 'IN', 'prefix' => 'c1', 'value' => $farmingIds],
            ],
            'group' => 'kvs.gid, c.id, spa.contract_area, pc.price_per_acre, pc.price_sum',
        ];

        if ($reportEkate) {
            $options['where']['ekate'] = ['column' => 'ekate', 'compare' => '=', 'prefix' => 'kvs', 'value' => $reportEkate];
        }
        if ($reportMasiv) {
            $options['where']['masiv'] = ['column' => 'masiv', 'prefix' => 'kvs', 'compare' => '=', 'value' => $reportMasiv];
        }
        if ($reportPlotNumber) {
            $options['where']['number'] = ['column' => 'number', 'prefix' => 'kvs', 'compare' => '=', 'value' => $reportPlotNumber];
        }
        if ($reportKadIdent) {
            $options['where']['kad_ident'] = ['column' => 'kad_ident', 'prefix' => 'kvs', 'compare' => '=', 'value' => $reportKadIdent];
        }

        if ($reportNtp) {
            $UserDbAreaTypesController = new UserDbAreaTypesController($this->User->Database);
            $ntpFilter = $UserDbAreaTypesController->getNtpCodesWithAdditionalCodes([$reportNtp]);
            $options['where']['ntp'] = ['column' => 'area_type', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $ntpFilter];
        }
        if ($reportCategory) {
            $categoryFilter = in_array($reportCategory, ['-1', '0', null]) ? ['-1', '0', null] : [$reportCategory];
            $options['where']['category'] = ['column' => 'category', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $categoryFilter];
        }
        if ($reportMestnost) {
            $options['where']['mestnost'] = ['column' => 'mestnost', 'compare' => '=', 'prefix' => 'kvs', 'value' => $reportMestnost];
        }

        if ($reportIrrigation) {
            $options['where']['irrigated_area'] = ['column' => 'irrigated_area', 'compare' => '=', 'prefix' => 'kvs', 'value' => $reportIrrigation];
        }

        // преотдаден
        if (Config::CONTRACT_SUBLEASE_TYPE_SUBLET == $reportSubleaseType) {
            $options['where']['sublease_type'] = ['column' => 'farming_id', 'compare' => 'IS', 'prefix' => 'fc', 'value' => 'NULL'];
        }
        // пренает
        if (Config::CONTRACT_SUBLEASE_TYPE_SUBLEASE == $reportSubleaseType) {
            $options['where']['sublease_type'] = ['column' => 'farming_id', 'compare' => 'IS NOT', 'prefix' => 'fc', 'value' => 'NULL'];
        }

        if ($reportDateAsOf) {
            $options['where']['start_date'] = ['column' => 'start_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $reportDateAsOf];
            $options['where']['due_date'] = ['column' => 'due_date', 'compare' => '>=', 'prefix' => 'c', 'value' => $reportDateAsOf];
        }

        if ($reportArendator) {
            $options['whereOr']['arendator'] = ['column' => 'owner_id', 'compare' => 'IN', 'prefix' => 'cc', 'value' => $owner_ids];
            $options['whereOr']['farming_arendator'] = ['column' => 'farming_id', 'compare' => 'IN', 'prefix' => 'fc', 'value' => $search_farming_ids];
        }
        $counter_total = $UserDbPlotsController->getSubleasedPlotsReport($options, false, false);

        $counter = count($counter_total);

        if (0 == $counter) {
            return $this->return;
        }

        $options['offset'] = ($page - 1) * $rows;
        $options['limit'] = $rows;

        $results = $counter_total;
        $results_count = count($results);
        $ekatte_codes = array_filter(array_unique(array_column($results, 'ekate'), SORT_REGULAR));
        $ekatte = $UsersController->getAllEkateName($ekatte_codes);

        $total_price_sum = 0;
        $total_area = 0;
        $total_renta = 0;

        for ($i = 0; $i < $counter; $i++) {
            $total_area += $counter_total[$i]['area'];
            $total_renta += $counter_total[$i]['renta'];
        }

        for ($i = 0; $i < $results_count; $i++) {
            if (!$results[$i]['mestnost']) {
                $results[$i]['mestnost'] = '-';
            }

            if (!$results[$i]['category']) {
                $results[$i]['category'] = '-';
            }

            $total_price_sum += $results[$i]['price_sum'];
            $results[$i]['area'] = number_format($results[$i]['area'], 3, '.', '');
            $results[$i]['price_per_acre'] = BGNtoEURO($results[$i]['price_per_acre']);
            $results[$i]['price_sum'] = BGNtoEURO($results[$i]['price_sum']);
            $results[$i]['renta_val'] = $results[$i]['renta'];
            $results[$i]['renta'] = BGNtoEURO($results[$i]['renta']);

            if (!$results[$i]['sv_num']) {
                $results[$i]['sv_num'] = '-';
            }

            if (!$results[$i]['sv_date']) {
                $results[$i]['sv_date'] = '-';
            }

            $farmingID = $ownersArray[$results[$i]['gid']];
            // $results[$i]['owner_names'] = $final_farming[$farmingID]['name'];
            // $results[$i]['owner_names'] = str_replace(['"',"'"], '', $results[$i]['owner_names']);
            $results[$i]['farming'] = $final_farming[$results[$i]['farming_id']]['name'];
            $results[$i]['farming'] = str_replace(['"', "'"], '', $results[$i]['farming']);
            $results[$i]['farming_id_array'] = str_getcsv(trim($results[$i]['farming_id_array'], '{}'));

            foreach ($results[$i]['farming_id_array'] as $farming_id) {
                $results[$i]['farming_contragent'] .= $final_farming[$farming_id]['name'] . ',<br />';
            }
            $results[$i]['egn'] = trim($results[$i]['egn'], ',<br />');
            if (',<br />' != $results[$i]['farming_contragent']) {
                $results[$i]['contragent'] = $results[$i]['farming_contragent'];
            }

            $results[$i]['contragent'] .= $results[$i]['owner_contragent'];
            $results[$i]['contragent'] = trim($results[$i]['contragent'], ',<br />');
            $results[$i]['contragent'] = str_replace(['"', "'"], '', $results[$i]['contragent']);
        }

        $sort = empty($sort) ? 'c_num' : $sort;
        $sort_flag = 'asc' !== $order ? SORT_DESC : SORT_ASC;
        $column_to_sort = array_column($results, $sort);
        if (!empty($column_to_sort)) {
            array_multisort($column_to_sort, $sort_flag, $results);
        }
        if (!empty($page) && !empty($rows)) {
            $results = array_splice($results, $options['offset'], $options['limit']);
        }

        $total_area_for_page = array_sum(array_column($results, 'area'));
        $total_renta_for_page = array_sum(array_column($results, 'renta_val'));
        $total_price_per_acre = array_sum(array_column($results, 'price_per_acre'));

        $return['rows'] = $results;
        $return['total'] = $counter;
        $return['footer'] = [
            [
                'area_type' => '<b>Общо за стр.</b>',
                'area' => number_format($total_area_for_page, 3, '.', ''),
                'renta' => BGNtoEURO($total_renta_for_page),
            ],
            [
                'area_type' => '<b>Общо</b>',
                'area' => number_format($total_area, 3, '.', ''),
                'renta' => BGNtoEURO($total_renta),
            ],
        ];

        return $return;
    }

    /**
     * @param UserDbOwnersController $UserDbOwnersController
     * @param array $owners_options
     *
     * @return array
     */
    protected function getOwnersIds(&$UserDbOwnersController, $owners_options)
    {
        $owners = $UserDbOwnersController->getOwnersData($owners_options, false, false);

        $owner_ids = [];
        foreach ($owners as $owner => $value) {
            $owner_ids[] = $value['id'];
        }

        return $owner_ids;
    }

    private function formatRowsForExport($rows)
    {
        $results = [];

        $count = count($rows);
        for ($i = 0; $i < $count; $i++) {
            $results[$i]['land'] = $rows[$i]['land'];
            $results[$i]['kad_ident'] = $rows[$i]['kad_ident'];
            $results[$i]['mestnost'] = $rows[$i]['mestnost'];
            $results[$i]['category'] = $rows[$i]['category'];
            $results[$i]['area_type'] = $rows[$i]['area_type'];
            $results[$i]['area'] = $rows[$i]['area'];
            $results[$i]['c_type'] = $rows[$i]['c_type'];
            $results[$i]['owner_names'] = $rows[$i]['owner_names'];
            $results[$i]['egn'] = str_replace('<br />', "\n", $rows[$i]['egn']);
            $results[$i]['farming'] = $rows[$i]['farming'];
            $results[$i]['contragent'] = str_replace('<br />', "\n", $rows[$i]['contragent']);
            $results[$i]['c_num'] = $rows[$i]['c_num'];
            $results[$i]['sv_num'] = $rows[$i]['sv_num'];
            $results[$i]['sv_date'] = $rows[$i]['sv_date'];
            $results[$i]['due_date'] = $rows[$i]['due_date'];
            $results[$i]['renta'] = $rows[$i]['renta'];
        }

        return $results;
    }

    private function addTotals(&$rows)
    {
        $i = count($rows);
        $rows[$i]['land'] = '';
        $rows[$i]['kad_ident'] = '';
        $rows[$i]['mestnost'] = '';
        $rows[$i]['category'] = '';
        $rows[$i]['area_type'] = '';
        $rows[$i]['area'] = number_format(array_sum(array_column($rows, 'area')), 3);
        $rows[$i]['c_type'] = '';
        $rows[$i]['owner_names'] = '';
        $rows[$i]['egn'] = '';
        $rows[$i]['farming'] = '';
        $rows[$i]['contragent'] = '';
        $rows[$i]['c_num'] = '';
        $rows[$i]['sv_num'] = '';
        $rows[$i]['sv_date'] = '';
        $rows[$i]['due_date'] = '';
        $rows[$i]['renta'] = '';
    }
}
