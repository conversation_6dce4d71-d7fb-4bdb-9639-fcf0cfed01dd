<?php

namespace TF\Engine\APIClasses\Plots;

// Prado::using('Plugins.Core.UserDbPlots.*');
// Prado::using('Plugins.Core.Users.*');
// Prado::using('Plugins.Core.Users.conf');
// Prado::using('Plugins.Core.Farming.*');
// Prado::using('Plugins.Core.Farming.conf');

use Prado\Prado;
use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\Export2XlsClass;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDbAreaTypes\UserDbAreaTypesController;
use TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Справка "Собствена земя - по имоти".
 *
 * @rpc-module Plots
 *
 * @rpc-service-id detailed-own-plots-report-grid
 *
 * @property bool $excludeInactive
 */
class DetailedOwnPlotsReportGrid extends TRpcApiProvider
{
    protected $excludeInactive;

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getDetailedOwnPlots']],
            'plotDetails' => ['method' => [$this, 'plotDetails']],
            'exportToExcelDetailedOwnPlotsReportData' => ['method' => [$this, 'exportToExcelDetailedOwnPlotsReportData']],
        ];
    }

    /**
     * Export To Excel Own Plots Report Data.
     *
     * @param array $data
     *                    [
     *                    #items array filters
     *                    [
     *                    #item  timestamp report_contract_date
     *                    #item  timestamp report_contract_date_to
     *                    #item  integer report_farming
     *                    #item  integer report_ekate
     *                    #item  integer report_ntp
     *                    #item  integer report_category
     *                    #item  string report_mestnost
     *                    #item  string report_irrigation
     *                    ]
     *                    ]
     * @param int $page
     * @param int $rows
     * @param string $sort
     * @param string $order
     *
     * @return array
     */
    public function exportToExcelDetailedOwnPlotsReportData($data, $page = null, $rows = null, $sort = null, $order = null)
    {
        $results = $this->getDetailedOwnPlots($data, $page, $rows, $sort, $order);

        $result = $results['rows'];

        unset($result[0]['attributes']);

        $result = $this->formatRowsForExport($result);

        $columns = [
            'Землище',
            'Идентификатор',
            'Местност',
            'Категория',
            'НТП',
            'Площ по договор (дка)',
            'Обработваема площ (дка)',
        ];

        $this->addTotals($result);

        $fileName = 'sobstvena_zemq_det_' . $this->User->GroupID . '.xlsx';
        $path = PUBLIC_UPLOAD_EXPORT . '/' . $this->User->GroupID . '/' . $fileName;

        $export2Xls = new Export2XlsClass();
        $filePath = $export2Xls->exportUrlPath($path, $result, $columns);

        return ['file_path' => $filePath, 'file_name' => $fileName];
    }

    /**
     * @api-method read
     *
     * @param array $params
     *                      [
     *                      #items array filters
     *                      [
     *                      #item  timestamp report_contract_date
     *                      #item  timestamp report_contract_date_to
     *                      #item  integer report_farming
     *                      #item  integer report_ekate
     *                      #item  integer report_ntp
     *                      #item  integer report_category
     *                      #item  string report_mestnost
     *                      #item  string report_irrigation
     *                      ]
     *                      ]
     * @param int $page
     * @param int $rows
     * @param string $sort
     * @param string $order
     *
     * @return array
     *               #item array rows
     *               [
     *               #item string active
     *               #item date c_date
     *               #item date start_date
     *               #item date due_date
     *               #item string c_num
     *               #item float contract_area
     *               #item string farming_id
     *               #item integer id
     *               #item boolean is_sublease
     *               #item string nm_usage_rights
     *               #item float price_per_acre
     *               #item float price_sum
     *               #item float kvs_allowable_area
     *               ]
     *               #item ingeter total
     *               #item array footer
     *               [
     *               #item array 0
     *               [
     *               #item string area_type
     *               #item float area
     *               #item float price_per_acre
     *               #item float price_sum
     *               #item float price_sum
     *               #item float kvs_allowable_area_sum
     *               ]
     *               #item array 1
     *               [
     *               #item string area_type
     *               #item float area
     *               #item float price_per_acre
     *               #item float price_sum
     *               #item float kvs_allowable_area_sum
     *               ]
     *               ]
     */
    public function getDetailedOwnPlots($params, $page = null, $rows = null, $sort = null, $order = null)
    {
        // Присвояване на филтрите към променливи за по-лесна работа с тях
        $reportDateAsOf = $params['filters']['report_date_as_of'];
        $reportContractDate = $params['filters']['report_contract_date'];
        $reportContractDateTo = $params['filters']['report_contract_date_to'];
        $reportFarming = $params['filters']['report_farming'];
        $reportEkate = $params['filters']['report_ekate'];
        $reportMasiv = $params['filters']['report_masiv'];
        $reportPlotNumber = $params['filters']['report_plot_number'];
        $reportKadIdent = $params['filters']['report_kad_ident'];
        $reportNtp = $params['filters']['report_ntp'];
        $reportCategory = $params['filters']['report_category'];
        $reportMestnost = $params['filters']['report_mestnost'];
        $this->excludeInactive = $params['filters']['report_exclude_inactive'];

        if ('all' != $params['filters']['report_irrigation']) {
            $reportIrrigation = $params['filters']['report_irrigation'];
        } else {
            $reportIrrigation = null;
        }

        // Инициализиране на контролери
        $UsersController = new UsersController('Users');
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);
        $FarmingController = new FarmingController('Farming');

        $userFarmings = $FarmingController->getUserFarmings();
        $farmingIds = array_keys($userFarmings);

        if ($reportFarming && in_array((int) $reportFarming, $farmingIds)) {
            $farmingIds = [(int) $reportFarming];
        }
        // Създаване на стандартен отговор, който да бъде върнат, в случай, че няма резултати от зададените критерии
        $return = [
            'rows' => [],
            'total' => 0,
            'footer' => [],
        ];

        // Масив с настройки.
        // Единствената променлива, която се подава за дата е от филтърът "Към дата", за да може да се вземат правилно продадените и препродадените имоти
        // защото началната дата на договорите за собственост и продажба трябва да бъде обвъзрана с дата. Това позволява правилно филтриране на имотите,
        // които са били продадени от едно стопанство и после купени от друго стопанство на един и същи потребител.
        $options = [
            'custom_counter' => 'COUNT(DISTINCT(cpr_id))',
            'return' => [
                'DISTINCT (kvs. gid) as gid',
            ],
            'where' => [
                'c_date' => ['column' => 'c_date', 'prefix' => 'c', 'compare' => '>=', 'value' => $reportContractDate],
                'c_date_to' => ['column' => 'c_date', 'prefix' => 'c', 'compare' => '<=', 'value' => $reportContractDateTo],
                'farming_id' => ['column' => 'farming_id', 'prefix' => 'c', 'compare' => 'IN', 'value' => [...$farmingIds, null]],
            ],
            'start_date' => $reportDateAsOf,
            'due_date' => $reportDateAsOf,
            'group' => 'kvs.gid',
        ];

        // В зависимост от филтрите се добавят по отделно различни филтри
        if ($this->excludeInactive) {
            $options['where']['active'] = ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'true'];
        }
        if ($reportContractDate) {
            $options['where']['c_date'] = ['column' => 'c_date', 'prefix' => 'c', 'compare' => '>=', 'value' => $reportContractDate];
        }
        if ($reportEkate) {
            $options['where']['ekate'] = ['column' => 'ekate', 'prefix' => 'kvs', 'compare' => '=', 'value' => $reportEkate];
        }
        if ($reportMasiv) {
            $options['where']['masiv'] = ['column' => 'masiv', 'prefix' => 'kvs', 'compare' => '=', 'value' => $reportMasiv];
        }
        if ($reportPlotNumber) {
            $options['where']['number'] = ['column' => 'number', 'prefix' => 'kvs', 'compare' => '=', 'value' => $reportPlotNumber];
        }
        if ($reportKadIdent) {
            $options['where']['kad_ident'] = ['column' => 'kad_ident', 'prefix' => 'kvs', 'compare' => '=', 'value' => $reportKadIdent];
        }
        if ($reportNtp) {
            $UserDbAreaTypesController = new UserDbAreaTypesController($this->User->Database);
            $ntpFilter = $UserDbAreaTypesController->getNtpCodesWithAdditionalCodes([$reportNtp]);

            $options['where']['ntp'] = ['column' => 'area_type', 'compare' => 'IN', 'value' => $ntpFilter];
        }
        if ($reportCategory) {
            $categoryFilter = in_array($reportCategory, ['-1', '0', null]) ? ['-1', '0', null] : [$reportCategory];
            $options['where']['category'] = ['column' => 'category', 'compare' => 'IN', 'value' => $categoryFilter];
        }
        if ($reportMestnost) {
            $options['where']['mestnost'] = ['column' => 'mestnost', 'compare' => '=', 'value' => $reportMestnost];
        }

        if ($reportIrrigation) {
            $options['where']['irrigated_area'] = ['column' => 'irrigated_area', 'compare' => '=', 'value' => $reportIrrigation];
        }

        // Взимат се всички резултати, за да може да се пресметнат стойностите във footer-a
        // както и да се види дали изобщо има резултати, които да отговарят на зададените във филтъра критерии
        $results_total = $UserDbPlotsController->getOwnPlotsForReport($options, false, false);
        $rtCounter = count($results_total);

        if (0 == $rtCounter) {
            return $return;
        }

        $id_array = array_column($results_total, 'gid');

        $results = $this->getPlotDetailsForIDS($id_array);
        $rtCounter = count($results);

        // Добавят се pagination параметрите

        $total_area_for_page = 0;
        $total_price_per_acre_for_page = 0;
        $total_price_sum_for_page = 0;
        $total_area = 0;
        $total_price_per_acre = 0;
        $total_price_sum = 0;
        $totalWorkArea = 0;
        $totalWorkAreaPerPage = 0;

        // Обикаляне по всички резултати за текущата страница
        // за да се форматират резултатите в подходящ за визуализиране вид
        for ($i = 0; $i < $rtCounter; $i++) {
            // В случай, че някой от имотите има повече от един договор за собственост, то стопанствата към тези договори
            // ще са равни на броя на договорите, в които участва имотът. Вземат се само уникалните стопанства
            $results[$i]['farming_id'] = array_unique(explode(',', trim($results[$i]['farming_id'], '{}')));

            if (1 == count($results[$i]['farming_id'])) {
                // Ако стопанството е само едно се взема само неговото име.
                $results[$i]['farming_id'] = $userFarmings[$results[$i]['farming_id'][0]];
            } else {
                // В случай, че имотът е собственост на различни стопанства,
                // то имената на стопанствата се изреждат със запетая едно след друго
                $tmp_Farming = [];
                foreach ($results[$i]['farming_id'] as $farming) {
                    $tmp_Farming[] = $userFarmings[$farming];
                }
                $results[$i]['farming_id'] = implode(', ', $tmp_Farming);
            }

            $results[$i]['area'] = number_format($results[$i]['area'], 3, '.', '');

            // if all the plot's ownership contracts are inactive show the row in grey color
            $results[$i]['active'] = '{f}' == $results[$i]['active'] ? false : true;

            // TOTALS ALL RECORDS Пресмятания за footer-a на за всички страници (цялата справка)

            $total_area += number_format($results[$i]['area'], 3, '.', '');
            $total_price_per_acre += number_format($results[$i]['price_per_acre'], 3, '.', '');
            $total_price_sum += number_format($results[$i]['price_sum'], 3, '.', '');
            $totalWorkArea += (float)number_format($results[$i]['kvs_allowable_area'], 3, '.', '');
        }

        $sort_flag = 'asc' !== $order ? SORT_DESC : SORT_ASC;
        $values_to_sort = array_column($results, $sort);
        if (!empty($values_to_sort)) {
            array_multisort($values_to_sort, $sort_flag, $results);
        }
        if (!empty($page) && !empty($rows)) {
            $results = array_splice($results, ($page - 1) * $rows, $rows);
        }
        $rtCounterPage = count($results);
        for ($i = 0; $i < $rtCounterPage; $i++) {
            // Пресмятания за footer-a за текущата страница
            $total_area_for_page += $results[$i]['area'];
            $total_price_per_acre_for_page += $results[$i]['price_per_acre'];
            $total_price_sum_for_page += $results[$i]['price_sum'];
            $totalWorkAreaPerPage += $results[$i]['kvs_allowable_area'];
        }
        // Присвояване на различните елементи от response към една променлива
        $return['rows'] = $results;
        $return['total'] = $rtCounter;
        $return['footer'] = [
            [
                'area_type' => '<b>Общо за стр.</b>',
                'area' => number_format($total_area_for_page, 3, '.', ''),
                'price_per_acre' => number_format($total_price_per_acre_for_page, 2, '.', ''),
                'price_sum' => number_format($total_price_sum_for_page, 2, '.', ''),
                'kvs_allowable_area' => number_format($totalWorkAreaPerPage, 3, '.', ''),
            ],
            [
                'area_type' => '<b>Общо</b>',
                'area' => number_format($total_area, 3, '.', ''),
                'price_per_acre' => number_format($total_price_per_acre, 2, '.', ''),
                'price_sum' => number_format($total_price_sum, 2, '.', ''),
                'kvs_allowable_area' => number_format($totalWorkArea, 3, '.', ''),
            ],
        ];

        return $return;
    }

    /**
     * @api-method plotDetails
     *
     * @param int $plotID
     *
     * @return array
     *               #item array rows
     *               [
     *               #item integer id
     *               #item integer c_num
     *               #item integer farming_id
     *               #item float contract_area
     *               #item float price_per_acre
     *               #item float price_sum
     *               #item string nm_usage_rights
     *               #item boolean is_sublease
     *               #item boolean active
     *               #item date start_date
     *               #item date due_date
     *               #item date c_date
     *               ]
     *               #item integer total
     *               #item array footer
     *               [
     *               #item string farming_id
     *               #item float contract_area
     *               #item float price_per_acre
     *               #item float price_sum
     *               ]
     */
    public function plotDetails($plotID)
    {
        // Инициализиране на контролери
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);
        $FarmingController = new FarmingController('Farming');

        // Вземане на всички стопанства, за да се визуализират, ако са добавени като собственик по договор
        $options = [
            'return' => ['*'],
            'where' => [
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'value' => $this->User->GroupID],
            ],
            'sort' => 'id',
            'order' => 'ASC',
        ];

        $farming_results = $FarmingController->getFarmings($options, false, false);
        $farmingCount = count($farming_results);
        $finalFarmings = [];
        // Присвояване на имената им за по-лесна работа
        for ($i = 0; $i < $farmingCount; $i++) {
            $finalFarmings[$farming_results[$i]['id']]['name'] = $farming_results[$i]['name'];
        }

        // Създаване на масив с опции, за всички резултати
        $options = [
            'return' => [
                'c.id', 'C .c_num', "to_char(C .start_date,'DD.MM.YYYY') as start_date", "to_char(C .due_date,'DD.MM.YYYY') as due_date",
                "to_char(c.c_date,'DD.MM.YYYY') as c_date", 'C .farming_id',
                'cpr.price_per_acre', 'cpr.price_sum', 'C .active', 'C .is_sublease', 'c.is_annex', 'c.start_date as sdate',
            ],
            'where' => [
                'plot_id' => ['column' => 'plot_id', 'compare' => '=', 'prefix' => 'cpr',  'value' => $plotID],
                'id' => ['column' => 'id', 'compare' => 'IS NOT', 'prefix' => 'c',  'value' => 'null'],
            ],
            'sort' => 'sdate',
            'order' => 'asc',
        ];

        // Вземане на всички резултати
        $results = $UserDbPlotsController->getContractDetailsForPlotForReport($options, false, false);
        // Вземане на днешната дата като променлива, за да се пресметне дали договора е действащ или изтекъл
        $currentDate = date('Y-m-d', time());

        // Променливи за footer-a
        $contract_area = 0;
        $price_per_acre = 0;
        $price_sum = 0;

        // Итериране през резултатите, за да се визуализират по-добре данните за потребителя
        $resultsCount = count($results);
        for ($i = 0; $i < $resultsCount; $i++) {
            // Взема се името на землището, спрямо ЕКАТТЕ на имота
            $results[$i]['farming_id'] = $finalFarmings[$results[$i]['farming_id']]['name'];
            $results[$i]['contract_area'] = number_format($results[$i]['contract_area'], 3, '.', '');

            $pricePerAcre = $results[$i]['price_per_acre'];
            $results[$i]['price_per_acre'] = BGNtoEURO($pricePerAcre);

            $priceSum = $pricePerAcre ? $pricePerAcre * $results[$i]['contract_area'] : 0;
            $results[$i]['price_sum'] = $priceSum ? BGNtoEURO($priceSum) : '-';

            // Взема се името на типа договор
            if (7 == $results[$i]['nm_usage_rights']) {
                $results[$i]['nm_usage_rights'] = 'Продажба';
            } else {
                $results[$i]['nm_usage_rights'] = $GLOBALS['Contracts']['ContractTypes'][$results[$i]['nm_usage_rights']]['name'];
            }

            $results[$i]['is_sublease'] = $results[$i]['is_sublease'] ? 'Да' : 'Не';

            // Сравнява датите на договора спрямо днешната дата $currentDate и се слага име дали е действащ или изтекъл
            if ($results[$i]['active']) {
                $results[$i]['active'] = (!$results[$i]['due_date'] || $results[$i]['due_date'] >= $currentDate) ? 'Действащ' : 'Изтекъл';
            } else {
                $results[$i]['active'] = 'Анулиран';
            }

            $results[$i]['start_date'] = $results[$i]['start_date'] ? $results[$i]['start_date'] : '-';
            $results[$i]['due_date'] = $results[$i]['due_date'] ? $results[$i]['due_date'] : '-';
            $results[$i]['c_date'] = $results[$i]['c_date'] ? $results[$i]['c_date'] : '-';

            // Сумиране на резултати за footer-a
            $contract_area += $results[$i]['contract_area'];
            $price_per_acre += $pricePerAcre;
            $price_sum += $priceSum;
        }

        $return['rows'] = $results;
        $return['total'] = count($results);

        return $return;
    }

    private function formatRowsForExport($rows)
    {
        $results = [];

        $rowsCount = count($rows);
        for ($i = 0; $i < $rowsCount; $i++) {
            $results[$i]['land'] = $rows[$i]['land'];
            $results[$i]['kad_ident'] = $rows[$i]['kad_ident'];
            $results[$i]['mestnost'] = $rows[$i]['mestnost'];
            $results[$i]['category'] = $rows[$i]['category'];
            $results[$i]['area_type'] = $rows[$i]['area_type'];
            $results[$i]['area'] = $rows[$i]['area'];
            $results[$i]['kvs_allowable_area'] = $rows[$i]['kvs_allowable_area'];
        }

        return $results;
    }

    private function addTotals(&$rows)
    {
        $i = count($rows);
        $rows[$i]['land'] = '';
        $rows[$i]['kad_ident'] = '';
        $rows[$i]['mestnost'] = '';
        $rows[$i]['category'] = '';
        $rows[$i]['area_type'] = '';
        $rows[$i]['area'] = number_format(array_sum(array_column($rows, 'area')), 3);
        $rows[$i]['kvs_allowable_area'] = number_format(array_sum(array_column($rows, 'kvs_allowable_area')), 3);
    }

    private function getPlotDetailsForIDS($id_array)
    {
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);
        $options = [
            'return' => [
                'distinct(kvs.gid)',
                'kvs.ekate',
                'kvs.virtual_ekatte_name as land',
                'kad_ident',
                'COALESCE(mestnost, \'-\') as mestnost',
                'COALESCE(virtual_category_title, \'-\') as category',
                'virtual_ntp_title as area_type',
                'sum(scpr.contract_area_for_sale) as sold_area',
                'round(sum(CASE WHEN c.is_annex = true then 0 else cpr.contract_area end)::numeric - sum(COALESCE(scpr.contract_area_for_sale, 0))::numeric ,3) AS area',
                'array_agg(c.farming_id) as farming_id',
                'array_agg(distinct active) as active',
                'round(sum(COALESCE(cpr.kvs_allowable_area, kvs.allowable_area, 0))::numeric,3) as kvs_allowable_area',
            ],
            'where' => [
                'plot_id' => ['column' => 'gid', 'prefix' => 'kvs', 'compare' => 'IN', 'value' => $id_array],
                'nm_usage_rights' => ['column' => 'nm_usage_rights', 'prefix' => 'c', 'compare' => '=', 'value' => 1],
            ],
            'group' => 'kvs.gid',
            'joins' => [
                'left join su_sales_contracts_plots_rel scpr on scpr.pc_rel_id = cpr.id',
            ],
        ];

        if ($this->excludeInactive) {
            $options['where']['active'] = ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'true'];
        }

        return $UserDbPlotsController->getPlotDetailsFrom($options, false, false);
    }
}
