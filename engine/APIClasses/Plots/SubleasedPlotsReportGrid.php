<?php

namespace TF\Engine\APIClasses\Plots;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\Export2XlsClass;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDbAreaTypes\UserDbAreaTypesController;
use TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController;

/**
 * Справка "Отдадена собствена земя".
 *
 * @rpc-module Plots
 *
 * @rpc-service-id subleased-plots-report-grid
 */
class SubleasedPlotsReportGrid extends TRpcApiProvider
{
    private $UserDbController;

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getSubleasedPlots'],
                'validators' => [
                    'params' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'exportToExcelSubleasedPlotsReportData' => ['method' => [$this, 'exportToExcelSubleasedPlotsReportData'],
                'validators' => [
                    'data' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * Export To Excel Subleased Plots Report Data.
     *
     * @param array $data {
     *                    #items array filters {
     *                    #item  timestamp report_date
     *                    #item  timestamp report_date_from
     *                    #item  timestamp report_date_as_of
     *                    #item  timestamp report_contract_date
     *                    #item  timestamp report_contract_date_to
     *                    #item  integer report_farming
     *                    #item  integer report_ekate
     *                    #item  integer report_ntp
     *                    #item  integer report_category
     *                    #item  string report_mestnost
     *                    #item  string report_irrigation
     *                    }
     *                    }
     * @param int $page
     * @param int $rows
     * @param ?string $sort
     * @param ?string $order
     *
     * @return array
     */
    public function exportToExcelSubleasedPlotsReportData(array $data, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $results = $this->getSubleasedPlots($data, $page, $rows, $sort, $order);

        $result = $results['rows'];

        unset($result[0]['attributes']);

        $result = $this->formatRowsForExport($result);

        $columns = [
            'Землище',
            'Идентификатор',
            'Местност',
            'Категория',
            'НТП',
            'Площ(дка)',
            'Собственик',
            'Отдаден на',
            'ЕГН/ЕИК',
            'Договор',
            'Дата',
            'Валиден до',
            'Рента (пари)',
        ];

        $this->addTotals($result);

        $time = strtotime(date('Y-m-d H:i:s'));
        $fileName = 'otdadena_sobstvena_zemq_' . $this->User->GroupID . '_' . $time . '.xlsx';
        $path = PUBLIC_UPLOAD_EXPORT . '/' . $this->User->GroupID . '/' . $fileName;

        $export2Xls = new Export2XlsClass();
        $filePath = $export2Xls->exportUrlPath($path, $result, $columns);

        return ['file_path' => $filePath, 'file_name' => $fileName];
    }

    /**
     * getSubleasedPlots.
     *
     * @api-method read
     *
     * @param array $params {
     *                      #items array filters {
     *                      #item  timestamp report_date
     *                      #item  timestamp report_date_from
     *                      #item  timestamp report_date_as_of
     *                      #item  timestamp report_contract_date
     *                      #item  timestamp report_contract_date_to
     *                      #item  integer report_farming
     *                      #item  integer report_ekate
     *                      #item  integer report_ntp
     *                      #item  integer report_category
     *                      #item  string report_mestnost
     *                      #item  string report_irrigation
     *                      }
     *                      }
     * @param int|string $page
     * @param int|string $rows
     * @param ?string $sort
     * @param ?string $order
     *
     * @return array
     */
    public function getSubleasedPlots(array $params, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $reportDateFrom = $params['filters']['report_date_from'];
        $reportDate = $params['filters']['report_date'];
        $reportDateAsOf = $params['filters']['report_date_as_of'];
        $reportContractDate = $params['filters']['report_contract_date'];
        $reportContractDateTo = $params['filters']['report_contract_date_to'];
        $reportFarming = $params['filters']['report_farming'];
        $reportEkate = $params['filters']['report_ekate'];
        $reportMasiv = $params['filters']['report_masiv'];
        $reportPlotNumber = $params['filters']['report_plot_number'];
        $reportKadIdent = $params['filters']['report_kad_ident'];
        $reportNtp = $params['filters']['report_ntp'];
        $reportCategory = $params['filters']['report_category'];
        $reportMestnost = $params['filters']['report_mestnost'];
        $usage_rights = $params['filters']['usage_rights'];
        $reportIrrigation = null;
        if ('all' != $params['filters']['report_irrigation']) {
            $reportIrrigation = $params['filters']['report_irrigation'];
        }

        $FarmingController = new FarmingController('Farming');
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);

        $userFarmings = $FarmingController->getUserFarmings(true);
        $farmingIds = array_keys($userFarmings);

        if ($reportFarming && in_array((int) $reportFarming, $farmingIds)) {
            $farmingIds = [(int) $reportFarming];
        }

        // create the default return
        $return = [
            'rows' => [],
            'total' => 0,
            'footer' => [],
        ];

        $options = [
            'custom_counter' => 'COUNT(DISTINCT(spc.id))',
            'return' => [
                'gid',
                'ekate',
                'virtual_ekatte_name as land',
                'kad_ident',
                'COALESCE(mestnost, \'-\') as mestnost',
                'COALESCE(virtual_category_title, \'-\') as category',
                'virtual_ntp_title as area_type',
                '(
                    select 
                        sum(pa.contract_area) 
                    from su_subleases_plots_area pa 
                    where 
                        pa.plot_id = kvs.gid 
                        and pa.sublease_id = c.id
                ) as area',
                'c.id as sublease_id', 'c.c_num', "to_char(c.start_date,'DD.MM.YYYY') as start_date", "to_char(c.due_date,'DD.MM.YYYY') as due_date", 'c.farming_id',
                'SUM(DISTINCT(pc.price_per_acre)) as price_per_acre', 'SUM(DISTINCT(pc.price_sum)) as price_sum', 'c.renta',
                "string_agg(DISTINCT(CASE WHEN co.owner_type = 1 THEN co.name || ' ' || co.surname || ' ' || co.lastname ELSE co.company_name END), ',<br />') as contragent",
                "string_agg(DISTINCT(CASE WHEN co.owner_type = 1 THEN co.egn ELSE co.eik END), ',<br />') as egn",
                'array_agg(DISTINCT(fc.farming_id)) as farming_id_array',
            ],
            'where' => [
                'contract_type' => ['column' => 'nm_usage_rights', 'compare' => '=', 'prefix' => 'c1', 'value' => 1],
                'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'prefix' => 'c', 'value' => 'true'],
                'active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'true'],
                'farming_id' => ['column' => 'farming_id', 'prefix' => 'c', 'compare' => 'IN', 'value' => $farmingIds],
                'start_date' => ['column' => 'start_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $reportDate],
                'due_date' => ['column' => 'due_date', 'compare' => '>=', 'prefix' => 'c', 'value' => $reportDateFrom],
                'c_date' => ['column' => 'c_date', 'compare' => '>=', 'prefix' => 'c', 'value' => $reportContractDate],
                'c_date_to' => ['column' => 'c_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $reportContractDateTo],
                'nm_usage_rights' => ['column' => 'nm_usage_rights', 'compare' => '<=', 'prefix' => 'c', 'value' => $usage_rights],
            ],
            'group' => 'kvs.gid, c.id',
        ];

        if ($reportEkate) {
            $options['where']['ekate'] = ['column' => 'ekate', 'compare' => '=', 'prefix' => 'kvs', 'value' => $reportEkate];
        }
        if ($reportNtp) {
            $UserDbAreaTypesController = new UserDbAreaTypesController($this->User->Database);
            $ntpFilter = $UserDbAreaTypesController->getNtpCodesWithAdditionalCodes([$reportNtp]);
            $options['where']['ntp'] = ['column' => 'area_type', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $ntpFilter];
        }
        if ($reportCategory) {
            $categoryFilter = in_array($reportCategory, ['-1', '0', null]) ? ['-1', '0', null] : [$reportCategory];
            $options['where']['category'] = ['column' => 'category', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $categoryFilter];
        }
        if ($reportMestnost) {
            $options['where']['mestnost'] = ['column' => 'mestnost', 'compare' => '=', 'prefix' => 'kvs', 'value' => $reportMestnost];
        }
        if ($reportIrrigation) {
            $options['where']['irrigated_area'] = ['column' => 'irrigated_area', 'compare' => '=', 'prefix' => 'kvs', 'value' => $reportIrrigation];
        }
        if ($reportDateAsOf) {
            $options['where']['start_date'] = ['column' => 'start_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $reportDateAsOf];
            // HACK
            // Договорите за собственост имат due_date null, и ако се остави null договорите за собственост няма да се включат
            $options['where']['due_date'] = ['column' => "(CASE WHEN c.due_date IS NULL THEN '9999-12-31' ELSE c.due_date END)", 'compare' => '>=', 'value' => $reportDateAsOf];
        }
        if ($reportMasiv) {
            $options['where']['masiv'] = ['column' => 'masiv', 'prefix' => 'kvs', 'compare' => '=', 'value' => $reportMasiv];
        }
        if ($reportPlotNumber) {
            $options['where']['number'] = ['column' => 'number', 'prefix' => 'kvs', 'compare' => '=', 'value' => $reportPlotNumber];
        }
        if ($reportKadIdent) {
            $options['where']['kad_ident'] = ['column' => 'kad_ident', 'prefix' => 'kvs', 'compare' => '=', 'value' => $reportKadIdent];
        }

        $counter_total = $UserDbPlotsController->getSubleasedPlotsReport($options, false, false);

        $counter = count($counter_total);

        if (0 == $counter) {
            return $return;
        }

        if (!empty($page) && !empty($rows)) {
            $options['offset'] = ($page - 1) * $rows;
            $options['limit'] = $rows;
        }

        $total_area = 0;
        $total_renta = 0;
        for ($i = 0; $i < $counter; $i++) {
            $total_area += $counter_total[$i]['area'];
            $total_renta += $counter_total[$i]['renta'];
        }

        $results = $counter_total;
        $resultsCount = count($results);

        for ($i = 0; $i < $resultsCount; $i++) {
            $results[$i]['farming'] = $userFarmings[$results[$i]['farming_id']]['name'];
            $results[$i]['farming'] = str_replace(['"', "'"], '', $results[$i]['farming']);

            $results[$i]['area'] = number_format($results[$i]['area'], 3, '.', '');
            $results[$i]['price_per_acre'] = number_format($results[$i]['price_per_acre'], 2, '.', '');
            $results[$i]['price_sum'] = number_format($results[$i]['price_sum'], 2, '.', '');
            $results[$i]['renta_val'] = $results[$i]['renta'];
            $results[$i]['renta'] = BGNtoEURO($results[$i]['renta']);

            $results[$i]['farming_id_array'] = str_getcsv(trim($results[$i]['farming_id_array'], '{}'));
            $farmingIdCount = count($results[$i]['farming_id_array']);
            for ($j = 0; $j < $farmingIdCount; $j++) {
                $results[$i]['farming_contragent'] .= ($userFarmings[$results[$i]['farming_id_array'][$j]]['name']) ? $userFarmings[$results[$i]['farming_id_array'][$j]]['name'] . ',<br />' : '';
            }
            $results[$i]['contragent'] = $results[$i]['farming_contragent'] . $results[$i]['contragent'];
            $results[$i]['contragent'] = str_replace(['"', "'"], '', $results[$i]['contragent']);
            $results[$i]['farming_contragent'] = str_replace(['"', "'"], '', $results[$i]['farming_contragent']);
        }

        $sort = empty($sort) ? 'c_num' : $sort;
        $sort_flag = 'asc' !== $order ? SORT_DESC : SORT_ASC;
        $column_to_sort = array_column($results, $sort);
        if (!empty($column_to_sort)) {
            array_multisort($column_to_sort, $sort_flag, $results);
        }
        if (!empty($page) && !empty($rows)) {
            $results = array_splice($results, $options['offset'], $options['limit']);
        }

        $total_area_for_page = array_sum(array_column($results, 'area'));
        $total_renta_for_page = array_sum(array_column($results, 'renta_val'));

        $return['rows'] = $results;
        $return['total'] = $counter;
        $return['footer'] = [
            [
                'area_type' => '<b>Общо за стр.</b>',
                'area' => number_format($total_area_for_page, 3, '.', ''),
                'renta' => BGNtoEURO($total_renta_for_page),
            ],
            [
                'area_type' => '<b>Общо</b>',
                'area' => number_format($total_area, 3, '.', ''),
                'renta' => BGNtoEURO($total_renta),
            ],
        ];

        return $return;
    }

    private function formatRowsForExport($rows)
    {
        $results = [];

        $count = count($rows);
        for ($i = 0; $i < $count; $i++) {
            $results[$i]['land'] = $rows[$i]['land'];
            $results[$i]['kad_ident'] = $rows[$i]['kad_ident'];
            $results[$i]['mestnost'] = $rows[$i]['mestnost'];
            $results[$i]['category'] = $rows[$i]['category'];
            $results[$i]['area_type'] = $rows[$i]['area_type'];
            $results[$i]['area'] = $rows[$i]['area'];
            $results[$i]['farming'] = $rows[$i]['farming'];
            $results[$i]['contragent'] = str_replace('<br />', "\n", $rows[$i]['contragent']);
            $results[$i]['egn'] = str_replace('<br />', "\n", $rows[$i]['egn']);
            $results[$i]['c_num'] = $rows[$i]['c_num'];
            $results[$i]['start_date'] = $rows[$i]['start_date'];
            $results[$i]['due_date'] = $rows[$i]['due_date'];
            $results[$i]['renta'] = $rows[$i]['renta'];
        }

        return $results;
    }

    private function addTotals(&$rows)
    {
        $i = count($rows);
        $rows[$i]['land'] = '';
        $rows[$i]['kad_ident'] = '';
        $rows[$i]['mestnost'] = '';
        $rows[$i]['category'] = '';
        $rows[$i]['area_type'] = '';
        $rows[$i]['area'] = number_format(array_sum(array_column($rows, 'area')), 3);
        $rows[$i]['farming'] = '';
        $rows[$i]['contragent'] = '';
        $rows[$i]['egn'] = '';
        $rows[$i]['c_num'] = '';
        $rows[$i]['start_date'] = '';
        $rows[$i]['due_date'] = '';
        $rows[$i]['renta'] = '';
    }
}
