<?php

namespace TF\Engine\APIClasses\Plots;

use labelObj;
use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

// Prado::using('Plugins.Core.UserDbHypothecs.*');

/**
 * @rpc-module Plots
 *
 * @rpc-service-id plots-image
 */
class PlotsImage extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'generateImage' => ['method' => [$this, 'generateImage']],
        ];
    }

    /**
     * Generate image for the selected plot.
     *
     * @api-method generateImage
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer gid
     *                         #item integer width
     *                         #item integer height
     *                         #item integer user_id
     *                         }
     *
     * @return string url
     */
    public function generateImage($rpcParams)
    {
        if (!$rpcParams['gid'] || !$rpcParams['width'] || !$rpcParams['height'] || !$rpcParams['user_id']) {
            die();
        }

        if (!$_SESSION['user_id'] || $_SESSION['user_id'] != $rpcParams['user_id']) {
            die();
        }
        $map = null;
        $gid = $rpcParams['gid'];
        $width = $rpcParams['width'];
        $height = $rpcParams['height'];
        $forPrint = $rpcParams['forPrint'];
        $extension = '.png';
        if ($forPrint) {
            $extension = '_print.png';
        }

        $options = [
            'tablename' => 'layer_kvs',
            'return' => ['kad_ident', 'ekate'],
            'where' => [
                'gid' => ['column' => 'gid', 'compare' => '=', 'value' => $gid],
            ],
        ];

        $userDbController = new UserDbController($this->User->database);

        $plot_data = $userDbController->getItemsByParams($options, false, false);

        $plot_name = str_replace('.', '_', $plot_data[0]['kad_ident']);

        $ekate = $plot_data[0]['ekate'];

        if (!file_exists(WMS_IMAGE_PATH)) {
            mkdir(WMS_IMAGE_PATH, 0755, true);
        }

        if (file_exists(WMS_IMAGE_PATH . $plot_name . $extension)) {
            return 'files/img_plots/' . $plot_name . $extension;
        }

        $map = ms_newmapobj(WMS_MAP_PATH . $_SESSION['group_id'] . '.map');

        $maxextent = $userDbController->getMaxExtent('layer_kvs');
        // $maxextent = $userDbController->getKvsMaxExtent('layer_kvs',$gid);

        $maxextent = str_replace('BOX(', '', $maxextent);
        $maxextent = str_replace(')', '', $maxextent);
        $maxextent = str_replace(',', ' ', $maxextent);

        $extent = explode(' ', $maxextent);

        $map->setExtent((float)$extent[0], (float)$extent[1], (float)$extent[2], (float)$extent[3]);
        $map->setSize($width, $height);

        $layer1 = ms_newlayerobj($map);
        $layer1->setConnectionType(MS_POSTGIS);
        $layer1->set('name', 'layer_kvs');
        $layer1->set('type', MS_LAYER_POLYGON);
        $layer1->set('status', MS_DEFAULT);
        $layer1 = $this->removeLayerClasses($layer1, $plot_name);
        $query = "geom 
                    FROM 
                    (SELECT gid, geom, ekate, kad_ident as label
                        FROM
                     layer_kvs
                     LEFT JOIN dblink (
                        'host=" . DEFAULT_DB_HOST . ' port=' . DEFAULT_DB_PORT . ' dbname=' . DEFAULT_DB_DATABASE . ' user=' . DEFAULT_DB_USERNAME . ' password=' . DEFAULT_DB_PASSWORD . "',
                        'SELECT id, title FROM su_area_types WHERE true'
                    ) AS e (area_type_id varchar, title varchar) ON(area_type_id = area_type) WHERE true AND ekate = '" . $ekate . "' AND (is_edited = FALSE AND (edit_active_from <= now() OR edit_active_from IS NULL)) OR (is_edited = TRUE AND edit_active_from > now())) as subquery using unique gid using srid=32635";

        $layer1->set('connection', 'host=' . DEFAULT_DB_HOST . ' dbname=' . $_SESSION['database'] . ' user=' . DEFAULT_DB_USERNAME . ' password=' . DEFAULT_DB_PASSWORD . ' port=' . DEFAULT_DB_PORT);
        $layer1->set('data', $query);
        $layer1->set('dump', 'true');
        $layer1->set('template', 'map');
        $layer1->set('labelitem', 'label');
        $label = new labelObj();
        $label->set('encoding', 'UTF-8');
        $label->set('font', 'arial');
        $label->set('force', true);
        $label->set('size', 5);
        $label->set('wrap', 94);

        $class1 = ms_newclassobj($layer1);
        $class1->set('name', 'foo1');
        $class1->set('title', 'Граници');
        $class1->addLabel($label);
        $style = ms_newstyleobj($class1);
        $style->outlinecolor->setRGB(0, 0, 0);

        $layer2 = ms_newlayerobj($map);
        $layer2->setConnectionType(MS_POSTGIS);
        $layer2->set('name', 'layer_kvs_filter');
        $layer2->set('type', MS_LAYER_POLYGON);
        $layer2->set('status', MS_DEFAULT);
        $layer2->set('connection', 'host=' . DEFAULT_DB_HOST . ' dbname=' . $_SESSION['database'] . ' user=' . DEFAULT_DB_USERNAME . ' password=' . DEFAULT_DB_PASSWORD . ' port=' . DEFAULT_DB_PORT);
        $layer2->set('data', "geom from (select * from layer_kvs where gid = '" . $gid . "') as foo using unique gid");
        $layer2->set('dump', 'true');
        $layer2->set('template', 'layer_kvs_filter');
        $class2 = ms_newclassobj($layer2);
        $class2->set('name', 'foo');
        $class2->set('title', 'Избран имот');
        $style = ms_newstyleobj($class2);
        $style->color->setRGB(66, 167, 11);
        $style->outlinecolor->setRGB(0, 0, 0);

        $maxextent = $userDbController->getKvsMaxExtent('layer_kvs', $gid);
        $maxextent = str_replace('BOX(', '', $maxextent);
        $maxextent = str_replace(')', '', $maxextent);
        $maxextent = str_replace(',', ' ', $maxextent);
        $extent = explode(' ', $maxextent);

        $map->setExtent((float)$extent[0], (float)$extent[1], (float)$extent[2], (float)$extent[3]);

        $my_point = ms_newpointobj();
        $my_point->setXY($width / 2, $height / 2);

        $my_extent = ms_newrectobj();
        $my_extent->setextent($extent[0], $extent[1], $extent[2], $extent[3]);

        $map->zoompoint(-2, $my_point, $map->width, $map->height, $my_extent);

        $scale = round($map->scaledenom);
        $class3 = ms_newclassobj($layer1);
        $class3->set('name', 'scale');
        $class3->set('title', '1:' . $scale);

        $map->scalebar->set('units', MS_METERS);
        $map->scalebar->outlinecolor->setRGB(0, 0, 0);
        $map->scalebar->set('status', MS_EMBED);

        $map->legend->set('status', MS_EMBED);
        $map->legend->set('position', MS_LR);
        $map->legend->label->color->setRGB(0, 0, 0);
        $map->legend->label->set('encoding', 'UTF-8');
        $map->legend->label->set('font', 'arial');
        // type prop does not exit in newest version of mapscript
        // $map->legend->label->set("type", "truetype");
        $map->legend->label->set('size', 10);

        $image = $map->draw();

        if (!file_exists(WMS_IMAGE_PATH . $this->User->GroupID . '/' . $plot_name . $extension)) {
            if (!file_exists(WMS_IMAGE_PATH . $this->User->GroupID . '/')) {
                mkdir(WMS_IMAGE_PATH . $this->User->GroupID, 0755, true);
            }
            $url = $image->saveImage(WMS_IMAGE_PATH . $this->User->GroupID . '/' . $plot_name . $extension);
        }

        return 'files/img_plots/' . $this->User->GroupID . '/' . $plot_name . $extension;
    }

    private function removeLayerClasses($layer, $plot_name)
    {
        $ekate = explode('_', $plot_name)[0];
        $numclasses = $layer->numclasses;
        for ($i = 0; $i < $numclasses; $i++) {
            $class = $layer->getClass(0);
            $expression = trim($class->getExpressionString(), '"');
            if ($expression != $ekate) {
                $layer->removeClass(0);
            } elseif ($layer->numclasses > 1) {
                $layer->removeClass(1);
            }
        }

        return $layer;
    }
}
