<?php

namespace TF\Engine\APIClasses\Plots;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * История на имот
 *
 * @rpc-module Plots
 *
 * @rpc-service-id plots-history-grid
 *
 * @property UserDbPlotsController $UserDbPlotsController
 * @property UsersController $UsersController
 */
class PlotsHistoryGrid extends TRpcApiProvider
{
    private $UserDbController;
    private $UserDbPlotsController;
    private $UsersController = false;

    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getPlotsHistoryGrid']],
        ];
    }

    /**
     * Информация за грида.
     *
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer plot_id
     *                         }
     *
     * @return array
     */
    public function getPlotsHistoryGrid($rpcParams)
    {
        // create default empty return
        $empty_return = [
            'rows' => [],
            'total' => 0,
        ];

        if (!$rpcParams['plot_id'] || !(int) $rpcParams['plot_id']) {
            return $empty_return;
        }

        $splited = $this->getSplitedPlots($rpcParams);
        $merged = $this->getMergedPlots($rpcParams);

        $final = array_merge($splited, $merged);

        return [
            'rows' => $final,
            'total' => count($final),
        ];
    }

    private function getSplitedPlots($rpcParams)
    {
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);
        $UsersController = new UsersController('Users');

        $options = [
            'return' => [
                'kvs_new.ekate AS new_ekate',
                'kvs_new.kad_ident AS new_kad_ident',
                'kvs_log.old_gid as old_gid',
                'kvs_log.new_gid as new_gid',
                'kvs_log.id as child_operation_id',
                "kvs_log.old_gid::text || 1 || extract('epoch' from kvs_log.edit_date) AS parent_operation_id",
                "to_char(kvs_log.edit_date, 'dd-mm-YYYY') as edit_date",
                'kvs_old.ekate AS old_ekate',
                'kvs_old.kad_ident AS old_kad_ident',
            ],
            'where' => [
                'edit_type' => ['column' => 'edit_type', 'compare' => '=', 'prefix' => 'kvs_log', 'value' => 'split'],
                'old_gid' => ['column' => 'old_gid', 'compare' => '=', 'prefix' => 'kvs_log', 'value' => $rpcParams['plot_id']],
            ],
        ];
        $results = $UserDbPlotsController->getEditedPlots($options, false, false);
        $resultsCount = count($results);
        $final = [];
        for ($i = 0; $i < $resultsCount; $i++) {
            $ekateName = $UsersController->getEkatteName($results[$i]['old_ekate']);

            $final[$results[$i]['parent_operation_id']]['gid'] = $results[$i]['old_gid'];
            $final[$results[$i]['parent_operation_id']]['ekate'] = $results[$i]['old_ekate'];
            $final[$results[$i]['parent_operation_id']]['ekate_name'] = $ekateName;
            $final[$results[$i]['parent_operation_id']]['kad_ident'] = $results[$i]['old_kad_ident'];
            $final[$results[$i]['parent_operation_id']]['level'] = 1;
            $final[$results[$i]['parent_operation_id']]['iconCls'] = 'icon-tree-edit-geometry';
            $final[$results[$i]['parent_operation_id']]['status'] = 'Делба';
            $final[$results[$i]['parent_operation_id']]['operation_id'] = $results[$i]['parent_operation_id'];
            $final[$results[$i]['parent_operation_id']]['edit_date'] = $results[$i]['edit_date'];

            $final[$results[$i]['parent_operation_id']]['children'][] = [
                'gid' => $results[$i]['new_gid'],
                'ekate' => $results[$i]['new_ekate'],
                'ekate_name' => $ekateName,
                'kad_ident' => $results[$i]['new_kad_ident'],
                'level' => 2,
                'iconCls' => 'icon-tree-edit-geometry',
                'status' => 'Нов',
                'operation_id' => $results[$i]['child_operation_id'],
            ];
        }

        $final = array_merge($final);
        if (0 != count($final)) {
            $final = array_combine(range(0, count($final) - 1), array_values($final));
        }

        return $final;
    }

    private function getMergedPlots($rpcParams)
    {
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);
        $UsersController = new UsersController('Users');

        $options = [
            'return' => [
                'kvs_log.new_gid as new_gid',
            ],
            'where' => [
                'edit_type' => ['column' => 'edit_type', 'compare' => '=', 'prefix' => 'kvs_log', 'value' => 'merge'],
                'old_gid' => ['column' => 'old_gid', 'compare' => '=', 'prefix' => 'kvs_log', 'value' => $rpcParams['plot_id']],
            ],
        ];
        $results = $UserDbPlotsController->getEditedPlots($options, false, false);
        $resultsCount = count($results);
        $newGids = [];
        for ($i = 0; $i < $resultsCount; $i++) {
            $newGids[] = $results[$i]['new_gid'];
        }

        if (0 == count($newGids)) {
            return [];
        }

        $options = [
            'return' => [
                'kvs_new.ekate AS new_ekate',
                'kvs_new.kad_ident AS new_kad_ident',
                'kvs_log.old_gid as old_gid',
                'kvs_log.new_gid as new_gid',
                'kvs_log.id as child_operation_id',
                "kvs_log.new_gid::text || 2 || extract('epoch' from kvs_log.edit_date) AS parent_operation_id",
                "to_char(kvs_log.edit_date, 'dd-mm-YYYY') as edit_date",
                'kvs_old.ekate AS old_ekate',
                'kvs_old.kad_ident AS old_kad_ident',
            ],
            'where' => [
                'edit_type' => ['column' => 'edit_type', 'compare' => '=', 'prefix' => 'kvs_log', 'value' => 'merge'],
                'new_gid' => ['column' => 'new_gid', 'compare' => 'IN', 'prefix' => 'kvs_log', 'value' => $newGids],
            ],
        ];
        $results = $UserDbPlotsController->getEditedPlots($options, false, false);
        $resultsCount = count($results);
        $final = [];
        for ($i = 0; $i < $resultsCount; $i++) {
            $ekateName = $UsersController->getEkatteName($results[$i]['old_ekate']);

            $final[$results[$i]['parent_operation_id']]['gid'] .= ((0 == $i) ? '' : ',') . $results[$i]['old_gid'];
            $final[$results[$i]['parent_operation_id']]['ekate'] = $results[$i]['old_ekate'];
            $final[$results[$i]['parent_operation_id']]['ekate_name'] = $ekateName;
            $final[$results[$i]['parent_operation_id']]['kad_ident'] .= ((0 == $i) ? '' : ', ') . $results[$i]['old_kad_ident'];
            $final[$results[$i]['parent_operation_id']]['level'] = 1;
            $final[$results[$i]['parent_operation_id']]['iconCls'] = 'icon-tree-edit-geometry';
            $final[$results[$i]['parent_operation_id']]['status'] = 'Обединение';
            $final[$results[$i]['parent_operation_id']]['operation_id'] = $results[$i]['parent_operation_id'];
            $final[$results[$i]['parent_operation_id']]['edit_date'] = $results[$i]['edit_date'];

            $final[$results[$i]['parent_operation_id']]['children'][0] = [
                'gid' => $results[$i]['new_gid'],
                'ekate' => $results[$i]['new_ekate'],
                'ekate_name' => $ekateName,
                'kad_ident' => $results[$i]['new_kad_ident'],
                'level' => 2,
                'iconCls' => 'icon-tree-edit-geometry',
                'status' => 'Нов',
                'operation_id' => $results[$i]['child_operation_id'],
            ];
        }

        $final = array_merge($final);
        if (0 != count($final)) {
            $final = array_combine(range(0, count($final) - 1), array_values($final));
        }

        return $final;
    }
}
