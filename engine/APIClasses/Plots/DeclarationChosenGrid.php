<?php

namespace TF\Engine\APIClasses\Plots;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbAgreements\UserDbAgreementsController;
use TF\Engine\Plugins\Core\UserDbAreaTypes\UserDbAreaTypesController;
use TF\Engine\Plugins\Core\UserDbOwners\UserDbOwnersController;
use TF\Engine\Plugins\Core\UserDbPayments\UserDbPaymentsController;
use TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController;
use TF\Engine\Plugins\Core\UserDbSubleases\UserDbSubleasesController;

/**
 * Грид "Избрани имоти за декларация".
 *
 * @rpc-module Plots
 *
 * @rpc-service-id declaration-chosen-grid
 *
 * @property UserDbController $UserDbController
 * @property UserDbPlotsController $UserDbPlotsController
 * @property UserDbAgreementsController $UserDbAgreementsController
 * @property UserDbSubleasesController $UserDbSubleasesController
 * @property UserDbOwnersController $UserDbOwnersController
 * @property FarmingController $FarmingController
 */
class DeclarationChosenGrid extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'decl69' => ['method' => [$this, 'loadDecl69'],
                'validators' => [
                    'rpcParams' => [
                        'year' => 'validateFarmingYear, validateRequired, validateNotNull',
                        'farming' => 'validateInteger, validateRequired, validateNotNull',
                        'ekate' => 'validateText, validateRequired, validateNotNull',
                    ],
                ]],
            'decl70' => ['method' => [$this, 'loadDecl70'],
                'validators' => [
                    'rpcParams' => [
                        'year' => 'validateFarmingYear, validateRequired, validateNotNull',
                        'farming' => 'validateInteger, validateRequired, validateNotNull',
                        'ekate' => 'validateText, validateRequired, validateNotNull',
                    ],
                ]],
            'declPML' => ['method' => [$this, 'loadDeclPML'],
                'validators' => [
                    'rpcParams' => [
                        'year' => 'validateFarmingYear, validateRequired, validateNotNull',
                        'farming' => 'validateInteger, validateRequired, validateNotNull',
                    ],
                ]],
            'anketnaKarta' => ['method' => [$this, 'loadAnketnaKarta'],
                'validators' => [
                    'rpcParams' => [
                        'year' => 'validateFarmingYear, validateRequired, validateNotNull',
                        'farming' => 'validateInteger, validateRequired, validateNotNull',
                        'ekate' => 'validateText, validateRequired, validateNotNull',
                    ],
                ]],
            'decl73' => ['method' => [$this, 'loadDecl73'],
                'validators' => [
                    'rpcParams' => [
                        'year' => 'validateFarmingYear, validateRequired, validateNotNull',
                        'farming' => 'validateInteger, validateRequired, validateNotNull',
                        'with_dividends' => 'validateRequired',
                    ],
                ]],
            'addToDeclaration' => ['method' => [$this, 'addToDeclaration']],
            'deleteFromDeclaration' => ['method' => [$this, 'deleteFromDeclaration']],
            'clearDeclarations' => ['method' => [$this, 'clearDeclarations']],
            'areaReport' => ['method' => [$this, 'areaReport']],
        ];
    }

    /**
     * Loads information for 'Декларация №69'.
     *
     * @api-method decl69
     *
     * @param array $rpcParams
     *                         {
     *                         #item string ekate
     *                         #item int year
     *                         #item int farming
     *                         #item string ntp
     *                         #item string from_date - c_date filter parameter
     *                         #item string to_date   - c_date filter parameter
     *                         }
     * @param int $page pagination rpc parameter
     * @param int $rows pagination rpc parameter
     * @param string $sort pagination rpc parameter
     * @param string $order pagination rpc parameter
     *
     * @return array
     */
    public function loadDecl69($rpcParams, $page = null, $rows = null, $sort = null, $order = null)
    {
        if ($this->User->isGuest) {
            return [];
        }
        if (!$rpcParams['ekate'] || !$rpcParams['year'] || !$rpcParams['farming']) {
            return [];
        }

        if (!empty($rpcParams['action']) && 'open' === $rpcParams['action']) {
            $_SESSION['decl_array'] = [];
        }

        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);

        if (!empty($rpcParams['action']) && 'open' !== $rpcParams['action'] && empty($_SESSION['decl_array'])) {
            return [
                'rows' => [],
                'total' => 0,
                'footer' => [
                    [
                        'kad_ident' => '<b>ОБЩО</b>',
                        'area' => '0.000',
                    ],
                ],
            ];
        }

        $results = $UserDbPlotsController->getChosenPlotsForDecl69($rpcParams, $page, $rows, $sort, $order);

        if (!empty($rpcParams['action']) && 'open' === $rpcParams['action']) {
            $_SESSION['decl_array'] = array_column($results, 'pc_rel_id');
        }

        // convert results for datagrid
        $total_area = 0;
        foreach ($results as &$result) {
            if (!empty($result['subleases_c_nums'])) {
                $result['subleases'] = $result['subleases_c_nums'];
                $result['is_subleased'] = true;
            } else {
                $result['subleases'] = '-';
                $result['is_subleased'] = false;
            }

            $total_area += $result['area'];
        }
        unset($result);

        $return = [
            'rows' => $results,
            'total' => count($results),
            'footer' => [
                [
                    'kad_ident' => '<b>ОБЩО</b>',
                    'area' => number_format($total_area, 3, '.', ''),
                ],
            ],
        ];

        return $return;
    }

    /**
     * Loads information for 'Декларация №70'.
     *
     * @api-method decl70
     *
     * @param array $rpcParams
     *                         {
     *                         #item string ekate
     *                         #item int year
     *                         #item int farming
     *                         #item string ntp
     *                         #item string from_date - c_date filter parameter
     *                         #item string to_date   - c_date filter parameter *
     *                         }
     * @param int $page pagination rpc parameter
     * @param int $rows pagination rpc parameter
     * @param string $sort pagination rpc parameter
     * @param string $order pagination rpc parameter
     *
     * @return array
     */
    public function loadDecl70($rpcParams, $page = null, $rows = null, $sort = null, $order = null)
    {
        if ($this->User->isGuest) {
            return [];
        }

        if (!empty($rpcParams['action']) && 'open' === $rpcParams['action']) {
            $_SESSION['decl_array'] = [];
        }

        if (!empty($rpcParams['action']) && 'open' !== $rpcParams['action'] && empty($_SESSION['decl_array'])) {
            return [
                'rows' => [],
                'total' => 0,
                'footer' => [
                    [
                        'kad_ident' => '<b>ОБЩО</b>',
                        'area' => '0.000',
                    ],
                ],
            ];
        }

        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);

        // get all subleased plot GIDs for
        $results = $UserDbPlotsController->getChosenPlotsForDecl70($rpcParams, $page, $rows, $sort, $order);

        if (!empty($rpcParams['action']) && 'open' === $rpcParams['action']) {
            $_SESSION['decl_array'] = array_column($results, 'pc_rel_id');
        }

        // convert results for datagrid
        $total_area = 0;
        foreach ($results as &$result) {
            if (!empty($result['subleases_c_nums'])) {
                $result['subleases'] = $result['subleases_c_nums'];
                $result['is_subleased'] = true;
            } else {
                $result['subleases'] = '-';
                $result['is_subleased'] = false;
            }

            $total_area += $result['area'];
        }
        unset($result);

        switch ($order) {
            case 'asc':
                $orderType = SORT_ASC;

                break;
            case 'desc':
                $orderType = SORT_DESC;

                break;
            default:
                $orderType = SORT_ASC;

                break;
        }

        if ('kad_ident' == $sort) {
            foreach ($results as $key => $row) {
                $ekate[$key] = $row['ekate'];
                $masiv[$key] = $row['masiv'];
                $number[$key] = $row['number'];
            }

            array_multisort($ekate, $orderType, $masiv, $orderType, $number, $orderType, $results);
        } else {
            $farmingController = new FarmingController('Farming');
            $results = $farmingController->ArrayHelper->sortResultArray($results, $sort, $order);
            if (!empty($results)) {
                foreach ($results as $key => $row) {
                    $mainSorter[$key] = $row[$sort];
                    $masiv[$key] = $row['masiv'];
                    $number[$key] = $row['number'];
                }
                array_multisort($mainSorter, $orderType, $masiv, $orderType, $number, $orderType, $results);
            }
        }

        // return the final results
        return [
            'rows' => $results,
            'total' => count($results),
            'footer' => [
                [
                    'kad_ident' => '<b>ОБЩО</b>',
                    'area' => number_format($total_area, 3, '.', ''),
                ],
            ],
        ];
    }

    /**
     * Loads information for 'Декларация ПМЛ'.
     *
     * @api-method declPML
     *
     * @param array $rpcParams
     *                         {
     *                         #item int year
     *                         #item int farming
     *                         #item string ntp
     *                         #item string from_date - c_date filter parameter
     *                         #item string to_date   - c_date filter parameter *
     *                         }
     * @param int $page pagination rpc parameter
     * @param int $rows pagination rpc parameter
     * @param string $sort pagination rpc parameter
     * @param string $order pagination rpc parameter
     *
     * @return array
     */
    public function loadDeclPML($rpcParams, $page = null, $rows = null, $sort = null, $order = null)
    {
        if ($this->User->isGuest) {
            return [];
        }

        $FarmingController = new FarmingController('Farming');
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);
        $UserDbAreaTypesController = new UserDbAreaTypesController($this->User->Database);

        $return = [
            'rows' => [],
            'total' => 0,
            'footer' => [],
        ];

        $is_reload = (!$_SESSION['decl_array'] && !$_SESSION['decl_loaded']) ? false : true;

        $year = $GLOBALS['Farming']['years'][$rpcParams['year']]['year'];

        // ###
        // GET SUBLEASES
        // ###
        $params = [
            'ekate' => $rpcParams['ekate'],
            'farming' => $rpcParams['farming'],
            'year' => $year,
        ];

        $subleased_plots = $this->getSubleasesByParams($params);
        // create variable/clear old data
        $subleased_gid_array = [];
        foreach ($subleased_plots as $key => $value) {
            $subleased_gid_array[] = $key;
        }

        $farming_start_date = ($year - 1) . '-10-01';
        $farming_due_date = $year . '-09-30';

        // options for plots-contracts query
        $options = [
            'return' => [
                'gid', 'kad_ident', 'virtual_ntp_title as area_type', 'include', 'participate', 'white_spots',
                'ROUND(contract_area::numeric, 3) as area', 'ekate', 'masiv', 'number',
                'c.c_num', 'TO_CHAR(c.c_date, \'DD.MM.YYYY\') as c_date', 'pc.id as pc_rel_id', 'c.id', 'c.nm_usage_rights', 'c.virtual_contract_type as c_type',
            ],
            'where' => [
                'masiv' => ['column' => 'masiv', 'compare' => '=', 'prefix' => 'kvs', 'value' => $rpcParams['masiv']],
                'imot' => ['column' => 'number', 'compare' => '=', 'prefix' => 'kvs', 'value' => $rpcParams['imot']],
                'farming' => ['column' => 'farming_id', 'compare' => '=', 'prefix' => 'c', 'value' => $rpcParams['farming']],
                'active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'TRUE'],
                'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
                'is_annex' => ['column' => 'is_annex', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
                'annex_action' => ['column' => 'annex_action', 'compare' => '=', 'prefix' => 'pc', 'value' => 'added'],
                'c_date_from' => ['column' => 'c_date', 'compare' => '>=', 'prefix' => 'c', 'value' => $rpcParams['from_date']],
                'c_date_to' => ['column' => 'c_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $rpcParams['to_date']],
                'archited_plot' => ['column' => '(kvs.is_edited = false or kvs.edit_active_from > \'' . $farming_due_date . '\')', 'compare' => '=', 'value' => 'true'],
            ],
            'custom_counter' => 'COUNT(DISTINCT(pc.id))',
            'farming_start_date' => $farming_start_date,
            'farming_due_date' => $farming_due_date,
        ];

        if ('-1' == $options['where']['area_type']['value']) {
            $options['where']['area_type'] = ['column' => 'area_type', 'compare' => 'is', 'prefix' => 'kvs', 'value' => 'NULL'];
        }

        if ($is_reload) {
            unset($options['where']['contract_type']);

            $ntpFilter = [];
            if (!empty($rpcParams['ntp'])) {
                $ntpFilter = $UserDbAreaTypesController->getNtpCodesWithAdditionalCodes($rpcParams['ntp']);

                $options['where']['pc_rel_id_string'] = [
                    'column' => 'id', 'compare' => 'IN', 'prefix' => 'pc',
                    'value' => $_SESSION['decl_array'] ? array_values($_SESSION['decl_array']) : [0],
                ];
                $options['where']['area_type'] = ['column' => 'area_type', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $ntpFilter];
            } else {
                $options['whereOr']['pc_rel_id_string'] = [
                    'column' => 'id', 'compare' => 'IN', 'prefix' => 'pc',
                    'value' => $_SESSION['decl_array'] ? array_values($_SESSION['decl_array']) : [0],
                ];
                $options['whereOr']['area_type'] = ['column' => 'area_type', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $ntpFilter];
            }
        } else {
            $ntpFilter = $GLOBALS['Plots']['pmlNtpCodes'];
            $ntpFilter = array_map('strval', $ntpFilter);
            $options['where']['area_type'] = ['column' => 'area_type', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $ntpFilter];

            if (count($subleased_gid_array)) {
                $options['plots_anti_id_string'] = implode(', ', $subleased_gid_array);
            } else {
                $options['plots_anti_id_string'] = '0';
            }
        }

        $counter = $UserDbPlotsController->getFullPlotDataForDeclaration($options, true, false);

        if (0 == $counter[0]['count']) {
            $_SESSION['decl_loaded'] = true;

            return $return;
        }

        // get all subleased plot GIDs for
        $results = $UserDbPlotsController->getFullPlotDataForDeclaration($options, false, false);
        // convert results for datagrid
        $total_area = 0;
        $count_results = count($results);
        for ($i = 0; $i < $count_results; $i++) {
            // assign variables for easy use
            $plot_gid = $results[$i]['gid'];

            $total_area += $results[$i]['area'];

            if (false === $is_reload) {
                // case when first load is requested
                // add plot relations IDs in session for reload and set loaded flag to TRUE
                $_SESSION['decl_array'][] = $results[$i]['pc_rel_id'];
                $_SESSION['decl_loaded'] = true;
            }

            if ($subleased_plots[$plot_gid]) {
                $results[$i]['subleases'] = $subleased_plots[$plot_gid];
                $results[$i]['is_subleased'] = true;
            } else {
                $results[$i]['subleases'] = '-';
                $results[$i]['is_subleased'] = false;
            }
        }

        switch ($order) {
            case 'asc':
                $orderType = SORT_ASC;

                break;
            case 'desc':
                $orderType = SORT_DESC;

                break;
            default:
                $orderType = SORT_ASC;

                break;
        }

        if ('kad_ident' == $sort) {
            foreach ($results as $key => $row) {
                $ekate[$key] = $row['ekate'];
                $masiv[$key] = $row['masiv'];
                $number[$key] = $row['number'];
            }

            array_multisort($ekate, $orderType, $masiv, $orderType, $number, $orderType, $results);
        } else {
            $results = $FarmingController->ArrayHelper->sortResultArray($results, $sort, $order);
            foreach ($results as $key => $row) {
                $mainSorter[$key] = $row[$sort];
                $masiv[$key] = $row['masiv'];
                $number[$key] = $row['number'];
            }
            array_multisort($mainSorter, $orderType, $masiv, $orderType, $number, $orderType, $results);
        }

        // return the final results
        return [
            'rows' => $results,
            'total' => $counter[0]['count'],
            'footer' => [
                [
                    'kad_ident' => '<b>ОБЩО</b>',
                    'area' => number_format($total_area, 3, '.', ''),
                ],
            ],
        ];
    }

    /**
     * Loads information for 'Анкетна карта'.
     *
     * @api-method anketnaKarta
     *
     * @param array $rpcParams
     *                         {
     *                         #item string ekate
     *                         #item int year
     *                         #item int farming
     *                         #item string ntp
     *                         #item string from_date - c_date filter parameter
     *                         #item string to_date   - c_date filter parameter
     *                         #item string area_type_code - NTP code.
     *                         }
     * @param int $page pagination rpc parameter
     * @param int $rows pagination rpc parameter
     * @param string $sort pagination rpc parameter
     * @param string $order pagination rpc parameter
     *
     * @return array
     */
    /**
     * Loads information for 'Декларация №70'.
     *
     * @api-method decl70
     *
     * @param array $rpcParams
     *                         {
     *                         #item string ekate
     *                         #item int year
     *                         #item int farming
     *                         #item string ntp
     *                         #item string from_date - c_date filter parameter
     *                         #item string to_date   - c_date filter parameter *
     *                         }
     * @param int $page pagination rpc parameter
     * @param int $rows pagination rpc parameter
     * @param string $sort pagination rpc parameter
     * @param string $order pagination rpc parameter
     *
     * @return array
     */
    public function loadAnketnaKarta($rpcParams, $page = null, $rows = null, $sort = null, $order = null)
    {
        if ($this->User->isGuest) {
            return [];
        }

        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);

        if (!empty($rpcParams['action']) && 'open' === $rpcParams['action']) {
            $_SESSION['decl_array'] = [];
        }

        if (!empty($rpcParams['action']) && 'open' !== $rpcParams['action'] && empty($_SESSION['decl_array'])) {
            return [
                'rows' => [],
                'total' => 0,
                'footer' => [
                    [
                        'kad_ident' => '<b>ОБЩО</b>',
                        'area' => '0.000',
                    ],
                ],
            ];
        }

        $hasPlotsWithoutNTP = false;
        // get all subleased plot GIDs for
        $results = $UserDbPlotsController->getChosenPlotsForAnketnaKarta($rpcParams, $page, $rows, $sort, $order);
        if (!empty($rpcParams['action']) && 'open' === $rpcParams['action']) {
            $_SESSION['decl_array'] = array_column($results, 'pc_rel_id');
        }

        // convert results for datagrid
        $total_area = 0;
        foreach ($results as &$result) {
            if (!empty($result['subleases_c_nums'])) {
                $result['subleases'] = $result['subleases_c_nums'];
                $result['is_subleased'] = true;
            } else {
                $result['subleases'] = '-';
                $result['is_subleased'] = false;
            }

            $result['area'] = number_format($result['area'] / 10, 4, '.', '');

            if (!$result['area_type']) {
                $hasPlotsWithoutNTP = true;
            }
            $total_area += $result['area'];
        }
        unset($result);

        switch ($order) {
            case 'asc':
                $orderType = SORT_ASC;

                break;
            case 'desc':
                $orderType = SORT_DESC;

                break;
            default:
                $orderType = SORT_ASC;

                break;
        }

        if ('kad_ident' == $sort) {
            foreach ($results as $key => $row) {
                $ekate[$key] = $row['ekate'];
                $masiv[$key] = $row['masiv'];
                $number[$key] = $row['number'];
            }

            array_multisort($ekate, $orderType, $masiv, $orderType, $number, $orderType, $results);
        } else {
            $farmingController = new FarmingController('Farming');
            $results = $farmingController->ArrayHelper->sortResultArray($results, $sort, $order);
            if (!empty($results)) {
                foreach ($results as $key => $row) {
                    $mainSorter[$key] = $row[$sort];
                    $masiv[$key] = $row['masiv'];
                    $number[$key] = $row['number'];
                }
                array_multisort($mainSorter, $orderType, $masiv, $orderType, $number, $orderType, $results);
            }
        }

        // return the final results
        return [
            'rows' => $results,
            'total' => count($results),
            'footer' => [
                [
                    'kad_ident' => '<b>ОБЩО</b>',
                    'area' => number_format($total_area, 3, '.', ''),
                ],
            ],
            'hasPlotsWithoutNTP' => $hasPlotsWithoutNTP,
        ];
    }

    public function loadDecl73(array $rpcParams, ?int $page = null, ?int $rows = null, ?string $sort = null, ?string $order = null): array
    {
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);

        return $UserDbPaymentsController->getDeclaration73Data($rpcParams, $page, $rows, $sort, $order);
    }

    /**
     * Generates area report.
     *
     * @api-method areaReport
     *
     * @param array $data
     *                    {
     *                    #item string kad_ident
     *                    #item string ekate
     *                    #item string masiv
     *                    #item string number
     *                    #item int category
     *                    #item int area_type
     *                    #item string mestnost
     *                    #item string cnum
     *                    #item int contract_type
     *                    #item int farming
     *                    #item date date_from
     *                    #item date date_to
     *                    #item date due_date_from
     *                    #item date due_date_to
     *                    #item string owner_name
     *                    #item string owner_egn
     *                    #item string rep_name
     *                    #item string rep_egn
     *                    #item string company_name
     *                    #item string company_eik
     *                    }
     *
     * @return string formatted html paragraph with calculated area
     */
    public function areaReport($data)
    {
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);

        $return = '';

        $options = [
            'tablename' => 'layer_kvs',
            // filters
            'return' => ['SUM(ST_Area(geom)) as area'],
            'where' => [
                'kad_ident' => ['column' => 'kad_ident', 'compare' => '=', 'prefix' => 'p', 'value' => $data['kad_ident']],
                'ekate' => ['column' => 'ekate', 'compare' => '=', 'prefix' => 'p', 'value' => $data['ekate']],
                'masiv' => ['column' => 'masiv', 'compare' => '=', 'prefix' => 'p', 'value' => $data['masiv']],
                'number' => ['column' => 'number', 'compare' => '=', 'prefix' => 'p', 'value' => $data['number']],
                'category' => ['column' => 'category', 'compare' => '=', 'prefix' => 'p', 'value' => $data['category']],
                'area_type' => ['column' => 'area_type', 'compare' => '=', 'prefix' => 'p', 'value' => $data['area_type']],
                'mestnost' => ['column' => 'mestnost', 'compare' => '=', 'prefix' => 'p', 'value' => $data['mestnost']],
            ],
            'contract_data' => [
                'cnum' => ['column' => 'c_num', 'compare' => 'LIKE', 'prefix' => 'c', 'value' => $data['cnum']],
                'contract_type' => ['column' => 'nm_usage_rights', 'compare' => '=', 'prefix' => 'c', 'value' => $data['contract_type']],
                'farming' => ['column' => 'farming_id', 'compare' => '=', 'prefix' => 'c', 'value' => $data['farming']],
                'date_from' => ['column' => 'c_date', 'compare' => '>=', 'prefix' => 'c', 'value' => $data['date_from']],
                'date_to' => ['column' => 'c_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $data['date_to']],
                'due_date_from' => ['column' => 'due_date', 'compare' => '>=', 'prefix' => 'c', 'value' => $data['due_date_from']],
                'due_date_to' => ['column' => 'due_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $data['due_date_to']],
            ],
            'owner_data' => [
                'owner_name' => ['column' => 'name', 'compare' => 'ILIKE', 'prefix' => 'o', 'value' => $data['owner_name']],
                'owner_egn' => ['column' => 'egn', 'compare' => '=', 'prefix' => 'o', 'value' => $data['owner_egn']],
                'rep_name' => ['column' => 'rep_name', 'compare' => 'ILIKE', 'prefix' => 'o', 'value' => $data['rep_name']],
                'rep_egn' => ['column' => 'rep_egn', 'compare' => '=', 'prefix' => 'o', 'value' => $data['rep_egn']],
                'company_name' => ['column' => 'company_name', 'compare' => 'ILIKE', 'prefix' => 'o', 'value' => $data['company_name']],
                'company_eik' => ['column' => 'eik', 'compare' => '=', 'prefix' => 'o', 'value' => $data['company_eik']],
            ],
        ];
        // checking if the complex query is required
        if ('' != $data['owner_name'] || '' != $data['owner_egn'] || '' != $data['rep_name'] || '' != $data['rep_egn'] || '' != $data['company_name'] || '' != $data['company_eik']) {
            // if owner information is required
            $results = $UserDbOwnersController->getPlotDataByOwnerFilter($options, false, false);
        } elseif ('' != $data['contract_type'] || '' != $data['farming'] || '' != $data['date_from'] || '' != $data['date_to'] || '' != $data['due_date_to'] || '' != $data['due_date_to'] || '' != $data['cnum']) {
            // in that case contract info is required so we use the complex function
            $results = $UserDbPlotsController->getPlotDataByContractFilter($options, false, false);
        } else {
            // in that case only plot info is required so we can use the simple function
            $results = $UserDbPlotsController->getPlotData($options, false, false);
        }

        $count = count($results);
        if (0 != $count) {
            for ($i = 0; $i < $count; $i++) {
                $return .= number_format($results[$i]['area'] / 1000, 3);
            }
        }

        return '<span style="font-weight: bold; font-style: italic;">Обща площ:</span> ' . $return . ' дка.';
    }

    /**
     * Adds new plots to declaration.
     *
     * @api-method addToDeclaration
     *
     * @param array $data plot data
     *
     * @return array
     */
    public function addToDeclaration($data)
    {
        if (empty($data)) {
            return;
        }

        foreach ($data as $plots) {
            $_SESSION['decl_array'][] = $plots;
        }
    }

    /**
     * Deletes plots from declaration.
     *
     * @api-method deleteFromDeclaration
     *
     * @param array $data plot data
     */
    public function deleteFromDeclaration($data)
    {
        if (empty($data)) {
            return;
        }

        $removeArray = [];
        foreach ($data as $plot) {
            $removeArray[] = $plot;
        }

        $_SESSION['decl_array'] = array_diff($_SESSION['decl_array'], $removeArray);
    }

    /**
     * Clears session plots array.
     *
     * @api-method clearDeclarations
     */
    public function clearDeclarations()
    {
        $_SESSION['decl_array'] = [];
        $_SESSION['decl_loaded'] = false;
    }

    private function getSubleasesByParams($params)
    {
        if ($this->User->isGuest) {
            return [];
        }

        $UserDbSubleasesController = new UserDbSubleasesController($this->User->Database);

        $options = [
            'return' => [
                'DISTINCT(gid)', 'c.c_num', 'c.c_date', 'c.due_date',
            ],
            'where' => [
                'ekate' => ['column' => 'ekate', 'compare' => '=', 'prefix' => 'kvs', 'value' => $params['ekate']],
                'start_date' => ['column' => 'start_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $params['year'] . '-09-30'],
                'due_date' => ['column' => 'due_date', 'compare' => '>=', 'prefix' => 'c', 'value' => ($params['year'] - 1) . '-10-01'],
                'farming' => ['column' => 'farming_id', 'compare' => '=', 'prefix' => 'c', 'value' => $params['farming']],
                'active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'TRUE'],
                'active2' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c1', 'value' => 'TRUE'],
            ],
            'group' => 'c.id, kvs.gid, pc.id',
        ];

        if ($params['plots_id_string']) {
            $options['plots_id_string'] = $params['plots_id_string'];
        }
        $subleased_results = $UserDbSubleasesController->getSubleaseFullData($options, false, false);
        $subleaseCount = count($subleased_results);
        if (0 == $subleaseCount) {
            return [];
        }
        // clear old subleased results
        $subleased_plots = [];
        // assing due date to a variable
        // contracts with c_type 1(own contracts) do not have due date, and can not be given as query param
        $due_date = ($params['year'] - 1) . '-10-01';

        for ($i = 0; $i < $subleaseCount; $i++) {
            // flag hold if element should be added to return array or not
            $add_flag = false;
            if (false == strtotime($subleased_results['due_date'])) {
                $add_flag = true;
            } else {
                if (strtotime($subleased_results['due_date']) <= strtotime($due_date)) {
                    $add_flag = true;
                }
            }
            if ($add_flag) {
                $plot_gid = $subleased_results[$i]['gid'];

                if ($subleased_plots[$plot_gid]) {
                    $subleased_plots[$plot_gid] .= ', ' . $subleased_results[$i]['c_num'];
                } else {
                    $subleased_plots[$plot_gid] = $subleased_results[$i]['c_num'];
                }
            }
        }

        return $subleased_plots;
    }
}
