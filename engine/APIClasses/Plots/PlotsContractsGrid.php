<?php

namespace TF\Engine\APIClasses\Plots;

use Prado\Web\Services\TRpcApiProvider;
use Prado\Web\Services\TRpcException;
use TF\Application\Common\Config;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbContracts\UserDbContractsController;
use TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController;
use TF\Engine\Plugins\Core\UserDbSubleases\UserDbSubleasesController;
use TF\Engine\Plugins\Core\Users\UsersController;

include_once __DIR__ . '/../../Plugins/Core/Contracts/conf.php';

/**
 * Справка за имоти, участващи в договори.
 *
 * @rpc-module Plots
 *
 * @rpc-service-id plots-contracts-datagrid
 */
class PlotsContractsGrid extends TRpcApiProvider
{
    private $module = 'Plots';
    private $service_id = 'plots-contracts-datagrid';

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readContractsDatagrid'],
                'validators' => [
                    'filterObj' => 'validateInteger',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'getContractsAddGrid' => ['method' => [$this, 'getContractsAddGrid'],
                'validators' => [
                    'filterObj' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'addContractPlotRelation' => ['method' => [$this, 'addContractPlotRelation'],
                'validators' => [
                    'contract_id' => 'validateNumber, validateRequired',
                    'plot_id' => 'validateNumber, validateRequired',
                    'contract_area' => 'validateNumber',
                    'price_per_acre' => 'validateNumber',
                    'price_sum' => 'validateNumber',
                ]],
            'addConfirmedContractPlotRelation' => ['method' => [$this, 'addConfirmedContractPlotRelation']],
            'deleteContractPlotRelation' => ['method' => [$this, 'deleteContractPlotRelation']],
        ];
    }

    /**
     * Gets all contracts, associated with current plot.
     *
     * @api-method read
     *
     * @param array|int $filterObj - the plot ID to filter the contracts
     * @param int|string $page - pagination parameters
     * @param int|string $rows - pagination parameters
     * @param string $sort - pagination parameters
     * @param string $order - pagination parameters
     *
     * @throws TRpcException
     *
     * @return array - an array of matching results
     */
    public function readContractsDatagrid($filterObj, int $page = null, int $rows = null, string $sort = null, string $order = null)
    {
        $UserDbSubleasesController = new UserDbSubleasesController($this->User->Database);
        $FarmingController = new FarmingController('Farming');
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);

        $userFarmings = $FarmingController->getUserFarmings();
        $userFarmingIds = array_keys($userFarmings);
        $plotId = is_array($filterObj) ? $filterObj['plot_id'] : $filterObj;
        $includeRemovedPlots = isset($filterObj['include_removed_plots']) && false === $filterObj['include_removed_plots'] ? false : true;

        if (!$plotId) {
            return [
                'rows' => [],
                'total' => 0,
            ];
        }

        $results = $UserDbPlotsController->getPlotsContractsRelations($plotId, $includeRemovedPlots);
        $resultsCount = count($results);
        $contract_results = [];
        $id_array = [];
        $contract_plot_data = [];
        $contract_area_array = [];
        if ($resultsCount > 0) {
            for ($i = 0; $i < $resultsCount; $i++) {
                $id_array[] = $results[$i]['contract_id'];
                $contract_area_array[$results[$i]['contract_id']] = ($results[$i]['contract_area']) ? $results[$i]['contract_area'] : $results[$i]['area'];
                $contract_plot_data[$results[$i]['contract_id']]['price_per_acre'] = $results[$i]['price_per_acre'];
                $contract_plot_data[$results[$i]['contract_id']]['price_sum'] = $results[$i]['price_sum'];
                $contract_plot_data[$results[$i]['contract_id']]['annex_action'] = $results[$i]['annex_action'];
            }

            $options = [
                'return' => ['*', 'get_contract_status(id, active, start_date, due_date) AS contract_status_text'],
                'where' => [
                    'contract_id' => ['column' => 'id', 'compare' => 'IN', 'value' => $id_array],
                    'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'value' => 'FALSE'],
                    'skip_agreement' => ['column' => 'nm_usage_rights', 'compare' => '<>', 'value' => 4],
                ],
            ];

            $contract_results = $UserDbPlotsController->getContractsDataForPlots($options, false, false);
        }

        $contractsCount = count($contract_results);
        for ($i = 0; $i < $contractsCount; $i++) {
            $contract_results[$i]['annex_action'] = $contract_plot_data[$contract_results[$i]['id']]['annex_action'];
            $contract_results[$i]['contract_area'] = $contract_area_array[$contract_results[$i]['id']];
            $contract_results[$i]['contract_area'] = number_format($contract_results[$i]['contract_area'], 3, '.', '');
            $contract_results[$i]['price_per_acre'] = $contract_plot_data[$results[$i]['contract_id']]['price_per_acre'] ? $contract_plot_data[$results[$i]['contract_id']]['price_per_acre'] : '-';
            $contract_results[$i]['price_sum'] = $contract_plot_data[$results[$i]['contract_id']]['price_sum'] ? $contract_plot_data[$results[$i]['contract_id']]['price_sum'] : '-';

            $contract_results[$i]['price_per_acre'] = '-' != $contract_results[$i]['price_per_acre'] ? BGNtoEURO($contract_results[$i]['price_per_acre']) : '-';
            $contract_results[$i]['price_sum'] = '-' != $contract_results[$i]['price_per_acre'] ? BGNtoEURO($contract_results[$i]['price_per_acre'] * $contract_results[$i]['contract_area']) : '-';
        }

        $options = [
            'return' => ['distinct (C .*)', 'get_contract_status(c.id, c.active, c.start_date, c.due_date) AS contract_status_text'],
            'where' => [
                'plot_id' => ['column' => 'plot_id', 'compare' => '=', 'prefix' => 'pc', 'value' => $plotId],
                'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'prefix' => 'c', 'value' => 'TRUE'],
                'farming_id' => ['column' => 'farming_id', 'compare' => 'IN', 'prefix' => 'c', 'value' => $userFarmingIds],
            ],
        ];

        $sublease_results = $UserDbSubleasesController->getSubleasePlotsData($options, false, false);

        $subleaseCount = count($sublease_results);
        $subleaseIds = array_map(fn ($sublease) => $sublease['id'], $sublease_results);
        // get sublease contract_area data
        $options = [
            'tablename' => $UserDbController->DbHandler->tableSubleasesPlotsArea,
            'return' => [
                'sublease_id', 'plot_id', 'round(contract_area::numeric, 3) as contract_area',
            ],
            'where' => [
                'plot_id' => ['column' => 'plot_id', 'compare' => '=', 'value' => $plotId],
                'sublease_id' => ['column' => 'sublease_id', 'compare' => 'IN', 'value' => $subleaseIds],
            ],
        ];

        $sca_results = $UserDbController->getItemsByParams($options, false, false);
        $scaCount = count($sca_results);
        for ($i = 0; $i < $scaCount; $i++) {
            $contract_area_by_sublease_id[$sca_results[$i]['sublease_id']] = $sca_results[$i]['contract_area'];
        }

        for ($i = 0; $i < $subleaseCount; $i++) {
            $sublease_results[$i]['contract_area'] = $contract_area_by_sublease_id[$sublease_results[$i]['id']];
            $sublease_results[$i]['contract_area'] = number_format($sublease_results[$i]['contract_area'], 3, '.', '');
            $sublease_results[$i]['price_per_acre'] = '-';
            $sublease_results[$i]['price_sum'] = '-';
        }

        $options = [
            'return' => [
                'sum( scpr.contract_area_for_sale ) sold_area',
                'MAX(scpr.price_per_acre)',
                'MAX(scpr.price_sum)',
                'scpr.sales_contract_id sales_contract_id',
                'get_contract_status(sc.id, sc.active, sc.start_date, sc.due_date, false) AS contract_status_text',
                'sc.*',
            ],
            'where' => [
                'plot_id' => ['column' => 'plot_id', 'compare' => '=', 'prefix' => 'scpr', 'value' => $plotId],
                'farming_id' => ['column' => 'farming_id', 'compare' => 'IN', 'prefix' => 'sc', 'value' => $userFarmingIds],
            ],
            'group' => 'scpr.contract_id, sc.id, scpr.sales_contract_id',
        ];

        $sold_results = $UserDbSubleasesController->getSoldPlotsData($options, false, false);

        foreach ($sold_results as &$sold_result) {
            $sold_result['contract_area'] = number_format($sold_result['sold_area'], 3, '.', '');
            $sold_result['nm_usage_rights'] = 6;
            $sold_result['virtual_contract_type'] = 'Продажба';
        }

        $returns = array_merge($sold_results, $contract_results, $sublease_results);
        $returnsCount = count($returns);
        if (0 == $returnsCount) {
            return [
                'rows' => [],
                'total' => 0,
            ];
        }

        $id_array = [];
        $farming_array = [];
        date_default_timezone_set('Europe/Sofia');
        $currentDate = date('Y-m-d', time());

        for ($i = 0; $i < $returnsCount; $i++) {
            if ($returns[$i]['is_sublease']) {
                $returns[$i]['c_num'] = $returns[$i]['c_num'] . ' (преотдаден)';
            }

            if ($returns[$i]['is_annex']) {
                $returns[$i]['c_num'] = $returns[$i]['c_num'] . ' (анекс)';
            }

            if ($returns[$i]['active']) {
                $returns[$i]['active_text'] = (!$returns[$i]['due_date'] || $returns[$i]['due_date'] >= $currentDate) ? 'Действащ' : 'Изтекъл';
            } else {
                $returns[$i]['active_text'] = 'Анулиран';
            }
            $returns[$i]['c_type'] = $returns[$i]['nm_usage_rights'];

            if (6 == $returns[$i]['nm_usage_rights']) {
                $returns[$i]['nm_usage_rights'] = 'Продажба';
            } else {
                $returns[$i]['nm_usage_rights'] = $GLOBALS['Contracts']['ContractTypes'][$returns[$i]['nm_usage_rights']]['name'];
            }

            $returns[$i]['start_date'] = strftime('%d.%m.%Y', strtotime($returns[$i]['start_date']));
            $returns[$i]['c_date'] = strftime('%d.%m.%Y', strtotime($returns[$i]['c_date']));
            if ('' == $returns[$i]['due_date']) {
                $returns[$i]['due_date'] = '-';
            } else {
                $returns[$i]['due_date'] = strftime('%d.%m.%Y', strtotime($returns[$i]['due_date']));
            }
            $id_array[] = $returns[$i]['farming_id'];
        }

        for ($i = 0; $i < $returnsCount; $i++) {
            $returns[$i]['farming'] = !empty($userFarmings[$returns[$i]['farming_id']]) ? $userFarmings[$returns[$i]['farming_id']] : '-';
        }

        // sorting
        if (isset($sort, $order)) {
            $returns = $FarmingController->ArrayHelper->sortResultArray($returns, $sort, $order);
        }

        // pagination
        if (isset($page, $rows)) {
            $returns = array_slice($returns, ($page - 1) * $rows, $rows);
        }

        $return = [
            'total' => $returnsCount,
            'rows' => $returns,
        ];

        return $return;
    }

    /**
     * Gets all contracts, that are not currently associated with the selected plot.
     *
     * @api-method getContractsAddGrid
     *
     * @param array|int $filterObj - the plot ID to filter the contracts
     * @param int|string $page - pagination parameters
     * @param int|string $rows - pagination parameters
     * @param string $sort - pagination parameters
     * @param string $order - pagination parameters
     *
     * @throws MTRpcException|TRpcException
     *
     * @return array {
     *               #item array rows {
     *               #item array {
     *               #item int id
     *               #item string c_num
     *               #item string c_date
     *               #item string nm_usage_rights
     *               #item string sv_num
     *               #item string sv_date
     *               #item string start_date
     *               #item string renta
     *               #item string due_date
     *               #item string  renta_nat
     *               #item int farming_id
     *               #item string comment
     *               #item string agg_type
     *               #item boolean active
     *               #item int parent_id
     *               #item boolean is_annex
     *               #item int renta_nat_type_id
     *               #item boolean is_sublease
     *               #item string original_due_date
     *               #item string original_renta
     *               #item string original_renta_nat
     *               #item string original_renta_nat_type_id
     *               #item string na_num
     *               #item string tom
     *               #item string delo
     *               #item string court
     *               #item string payday
     *               #item boolean is_declaration_subleased
     *               #item string contract_area
     *               #item string price_per_acre
     *               #item string price_sum
     *               #item string active_text
     *               #item int c_type
     *               #item string farming
     *               }
     *               }
     *               #item int total    Total rows
     *               }
     */
    public function getContractsAddGrid(array $filterObj, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        if ($this->User->isGuest) {
            return [
                'rows' => [],
                'total' => 0,
            ];
        }

        $UserDbController = new UserDbController($this->User->Database);
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);
        $FarmingController = new FarmingController('Farming');

        $plot_id = is_array($filterObj) ? $filterObj['plot_id'] : $filterObj;
        $results = $UserDbPlotsController->getPlotsContractsRelations($plot_id);
        $resultsCount = count($results);
        $id_array = [];
        for ($i = 0; $i < $resultsCount; $i++) {
            $id_array[] = $results[$i]['contract_id'];
        }

        $options = [
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
            'return' => ['*'],
            'where' => [
                'contract_id' => ['column' => 'id', 'compare' => 'NOT IN', 'value' => $id_array],
                'contract' => ['column' => 'c_num', 'compare' => 'ILIKE', 'value' => $filterObj['contract'] ?? ''],
                'date_from' => ['column' => 'c_date', 'compare' => '>=', 'value' => $filterObj['date_from'] ?? ''],
                'date_to' => ['column' => 'c_date', 'compare' => '>=', 'value' => $filterObj['date_to'] ?? ''],
                'is_annex' => ['column' => 'is_annex', 'compare' => '=', 'value' => 'FALSE'],
                'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'value' => 'FALSE'],
                'from_sublease' => ['column' => 'from_sublease', 'compare' => 'IS', 'value' => 'NULL'],
            ],
        ];

        $counter = $UserDbPlotsController->getContractsDataForPlots($options, true, false);

        if (0 == $counter[0]['count']) {
            return [
                'rows' => [],
                'total' => 0,
            ];
        }

        $returns = $UserDbPlotsController->getContractsDataForPlots($options, false, false);
        $returnsCount = count($returns);

        $plot_options = [
            'tablename' => $UserDbController->DbHandler->tableKVS,
            'return' => ['ST_Area(geom)/1000 as area'],
            'where' => [
                'gid' => ['column' => 'gid', 'compare' => '=', 'value' => $plot_id],
            ],
        ];

        $plot_result = $UserDbController->getItemsByParams($plot_options);

        $id_array = [];
        $farming_array = [];
        $farming_data = [];

        date_default_timezone_set('Europe/Sofia');
        $currentDate = date('Y-m-d', time());

        for ($i = 0; $i < $returnsCount; $i++) {
            if ($returns[$i]['active']) {
                $returns[$i]['active_text'] = (!$returns[$i]['due_date'] || $returns[$i]['due_date'] >= $currentDate) ? 'Действащ' : 'Изтекъл';
            } else {
                $returns[$i]['active_text'] = 'Анулиран';
            }
            $returns[$i]['c_type'] = $returns[$i]['nm_usage_rights'];
            $returns[$i]['nm_usage_rights'] = $GLOBALS['Contracts']['ContractTypes'][$returns[$i]['nm_usage_rights']]['name'];
            $returns[$i]['start_date'] = strftime('%d.%m.%Y', strtotime($returns[$i]['start_date']));
            $returns[$i]['c_date'] = strftime('%d.%m.%Y', strtotime($returns[$i]['c_date']));
            if ('' == $returns[$i]['due_date']) {
                $returns[$i]['due_date'] = '-';
            } else {
                $returns[$i]['due_date'] = strftime('%d.%m.%Y', strtotime($returns[$i]['due_date']));
            }
            $id_array[] = $returns[$i]['farming_id'];

            $returns[$i]['contract_area'] = $plot_result[0]['area'];
            $returns[$i]['contract_area'] = number_format($returns[$i]['contract_area'], 3, '.', '');
            $returns[$i]['price_per_acre'] = '-';
            $returns[$i]['price_sum'] = '-';

            if (1 == $returns[$i]['c_type']) {
                $returns[$i]['price_per_acre'] = $UserDbController->getSinglePlotPricePerContract($returns[$i]['id']);
                $returns[$i]['price_per_acre'] = $returns[$i]['price_per_acre'] ? BGNtoEURO($returns[$i]['price_per_acre']) : '-';
                $returns[$i]['price_sum'] = '-' != $returns[$i]['price_per_acre'] ? BGNtoEURO($returns[$i]['price_per_acre'] * $returns[$i]['contract_area']) : '-';
            }
        }
        $id_array = array_filter($id_array);
        $id_string = implode(', ', $id_array);

        $farming_data = $FarmingController->getFarmingItemsByIDString($id_string, $this->User->GroupID);
        $farmingCount = count($farming_data);
        if (0 != count($farming_data)) {
            for ($i = 0; $i < $farmingCount; $i++) {
                $farming_array[$farming_data[$i]['id']] = $farming_data[$i];
            }
        }
        $returnsCount = count($returns);
        for ($i = 0; $i < $returnsCount; $i++) {
            $returns[$i]['farming'] = $farming_array[$returns[$i]['farming_id']]['name'];
        }

        $return = [
            'total' => $counter[0]['count'],
            'rows' => $returns,
        ];

        return $return;
    }

    /**
     * Creates a relation between selected contract ID and plot ID.
     *
     * @api-method addContractPlotRelation
     *
     * @param array $data - RPC Parameters
     *                    {
     *                    #item int plot_id
     *                    #item int contract_id
     *                    #item float contract_area
     *                    #item float price_per_acre
     *                    #item float price_sum
     *                    #item float document_area
     *                    }
     *
     * @throws MTRpcException|TRpcException if failed - returns MTRpcException and stores the plot relation data in the session
     *
     * @return array|bool boolean if successful - creates the relation and returns status OK code
     */
    public function addContractPlotRelation($data)
    {
        if ($this->User->isGuest) {
            return [];
        }

        $UserDbContractsController = new UserDbContractsController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $isFromSublease = $UserDbContractsController->isContractFromSublease($data['contract_id']);

        if ($isFromSublease) {
            throw new MTRpcException('CANNOT_MODIFY_FROM_SUBLEASE_CONTRACT', -33752);
        }
        $relations = $UserDbContractsController->getContractPlotRelationID($data['contract_id'], $data['plot_id']);
        if ($relations) {
            throw new MTRpcException('CONTRACT_PLOT_RELATION_ALREADY_EXISTS', -33207);
        }
        // free plots waiting confirmation data
        unset($_SESSION['contract_waiting_confirmation']);

        $_SESSION['contract_waiting_confirmation'] = [];

        $options = [
            'return' => [
                'c.start_date', 'c.nm_usage_rights as contract_type', 'c.farming_id',
                'MAX(CASE WHEN a.due_date IS NULL THEN c.due_date ELSE a.due_date END) as due_date',
                'c.overall_renta',
            ],
            'where' => [
                'contract_id' => ['column' => 'id', 'compare' => '=', 'prefix' => 'c', 'value' => $data['contract_id']],
            ],
            'group' => 'c.id, c.nm_usage_rights',
        ];

        $results = $UserDbContractsController->getContractDataByPCRel($options, false, false);

        if (!empty($results['contract_type']) && Config::CONTRACT_TYPE_OWN == $results['contract_type'] && !$this->User->HasContractsOwnWriteRights) {
            throw new MTRpcException('invalid_user_contracts_rights_own_contracts', -33200);
        }

        $originalContractType = $results[0]['contract_type'];
        $originalContractFarming = $results[0]['farming_id'];

        if (!$results[0]['due_date']) {
            $results[0]['due_date'] = '9999-12-31 00:00:00';
        }

        $options = [
            'return' => [
                'plot_id', 'array_agg(contract_area) as contract_area_array', 'array_agg(c.id) as contract_id_array',
            ],
            'where' => [
                'plot_id' => ['column' => 'plot_id', 'compare' => '=', 'prefix' => 'pc', 'value' => $data['plot_id']],
                'contract_id' => ['column' => 'id', 'compare' => '<>', 'prefix' => 'c', 'value' => $data['contract_id']],
                'active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'TRUE'],
                'is_annex' => ['column' => 'is_annex', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
                'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
                'start_date' => ['column' => 'start_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $results[0]['due_date']],
                'due_date' => ['column' => "CASE WHEN c.due_date IS NULL OR (CASE WHEN a.due_date IS NULL THEN c.due_date ELSE a.due_date END) >= '{$results[0]['start_date']}' THEN true ELSE false END", 'compare' => '=', 'value' => 'TRUE'],
            ],
            'group' => 'plot_id',
        ];

        $results = $UserDbContractsController->getContractDataByPCRel($options, false, false);

        $contract_id_array = explode(',', trim($results[0]['contract_id_array'], '{}'));
        $contract_id_count = count($contract_id_array);
        $contract_area_array = explode(',', trim($results[0]['contract_area_array'], '{}'));
        $tempArray = [];

        for ($i = 0; $i < $contract_id_count; $i++) {
            $tempArray[$contract_id_array[$i]] = $contract_area_array[$i];
        }
        $total_contract_area = 0;
        foreach ($tempArray as $value) {
            $total_contract_area += $value;
        }

        $allowed_contract_area = $data['document_area'] - $total_contract_area;

        if ((string) $data['contract_area'] <= (string) $allowed_contract_area) {
            $options = [
                'tablename' => $UserDbController->DbHandler->contractsPlotsRelTable,
                'mainData' => [
                    'contract_id' => $data['contract_id'],
                    'plot_id' => $data['plot_id'],
                    'contract_area' => $data['contract_area'],
                    'area_for_rent' => $data['contract_area'],
                    'price_per_acre' => filter_var($data['price_per_acre'], FILTER_VALIDATE_FLOAT, FILTER_NULL_ON_FAILURE),
                    'price_sum' => filter_var($data['price_sum'], FILTER_VALIDATE_FLOAT, FILTER_NULL_ON_FAILURE),
                ],
            ];

            $item_id = $UserDbController->addItem($options);
            if (1 == $originalContractType) {
                $UserDbController->addFarmingAsOwnerForPcRelId($item_id, $originalContractFarming);
            }
            $UserDbContractsController->manageOverallRenta($data['contract_id']);
            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $data, ['created_pc_rel_id' => $item_id], 'add contract plot rel');

            return Config::STATUS_CODE_OK;
        }
        $_SESSION['contract_waiting_confirmation'] = $data;

        throw new MTRpcException('contract_area_exceeds_plot_area', Config::CONTRACT_AREA_EXCEEDS_PLOT_AREA);
    }

    /**
     * When invoked creates the contract-to-plot relation from the data in the session.
     *
     * @api-method addContractPlotRelation
     *
     * @throws MTRpcException|TRpcException
     */
    public function addConfirmedContractPlotRelation()
    {
        $data = $_SESSION['contract_waiting_confirmation'];

        $UsersController = new UsersController('Users');
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbContractsController = new UserDbContractsController($this->User->Database);

        $isFromSublease = $UserDbContractsController->isContractFromSublease($data['contract_id']);
        if ($isFromSublease) {
            throw new MTRpcException('CANNOT_MODIFY_FROM_SUBLEASE_CONTRACT', -33752);
        }

        $options = [
            'return' => [
                'c.start_date', 'c.nm_usage_rights as contract_type', 'c.farming_id',
                'MAX(CASE WHEN a.due_date IS NULL THEN c.due_date ELSE a.due_date END) as due_date',
            ],
            'where' => [
                'contract_id' => ['column' => 'id', 'compare' => '=', 'prefix' => 'c', 'value' => $data['contract_id']],
            ],
            'group' => 'c.id, c.nm_usage_rights',
        ];

        $results = $UserDbContractsController->getContractDataByPCRel($options, false, false);

        if (!empty($results['contract_type']) && Config::CONTRACT_TYPE_OWN == $results['contract_type'] && !$this->User->HasContractsOwnWriteRights) {
            throw new MTRpcException('invalid_user_contracts_rights_own_contracts', -33200);
        }

        $originalContractType = $results[0]['contract_type'];
        $originalContractFarming = $results[0]['farming_id'];

        $options = [
            'tablename' => $UserDbController->DbHandler->contractsPlotsRelTable,
            'mainData' => [
                'contract_id' => $data['contract_id'],
                'plot_id' => $data['plot_id'],
                'contract_area' => $data['contract_area'],
                'price_per_acre' => filter_var($data['price_per_acre'], FILTER_VALIDATE_FLOAT, FILTER_NULL_ON_FAILURE),
                'price_sum' => filter_var($data['price_sum'], FILTER_VALIDATE_FLOAT, FILTER_NULL_ON_FAILURE),
            ],
        ];

        $item_id = $UserDbController->addItem($options);
        if (1 == $originalContractType) {
            $UserDbController->addFarmingAsOwnerForPcRelId($item_id, $originalContractFarming);
        }

        $UserDbContractsController->manageOverallRenta($data['contract_id']);
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $data, ['created_pc_rel_id' => $item_id], 'add confirmed contract plot rel');

        unset($_SESSION['contract_waiting_confirmation']);
    }

    /**
     * Deletes the relation between the contract and the plot.
     *
     * @api-method deleteContractPlotRelation
     *
     * @param int $plot_id - selected plot ID
     * @param array $checked - selected contract ID
     *                       {
     *                       #item int id
     *                       }
     *
     * @throws MTRpcException|TRpcException
     *
     * @return array
     */
    public function deleteContractPlotRelation($plot_id, $checked)
    {
        if ($this->User->isGuest) {
            return [];
        }

        $UserDbContractsController = new UserDbContractsController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        // get contractType
        $contractType = $UserDbContractsController->getContractType($checked[0]['id']);
        $isFromSublease = $UserDbContractsController->isContractFromSublease($checked[0]['id']);

        if ($isFromSublease) {
            throw new MTRpcException('CANNOT_MODIFY_FROM_SUBLEASE_CONTRACT', -33752);
        }
        // No Rights to operate with "Договори за собственост"
        if (Config::CONTRACT_TYPE_OWN == $contractType && !$this->User->HasContractsOwnWriteRights) {
            throw new MTRpcException('INVALID_USER_CONTRACTS_RIGHTS_OWN_CONTRACTS', -33200);
        }

        // getting parameters from callback
        $id_array = [];

        // getting all contract IDs
        if (is_array($checked)) {
            $checkedCount = count($checked);
            for ($i = 0; $i < $checkedCount; $i++) {
                $id_array[] = $checked[$i]['id'];
                $relations = $UserDbContractsController->getContractPlotRelationID($checked[$i]['id'], $plot_id);
                if (!$relations) {
                    throw new MTRpcException('NON_EXISTING_CONTRACT_PLOT_RELATION', -33204);
                }
            }
        } else {
            $id_array[0] = $checked;
        }
        /*
         * В случай, че имотът е преотдаден и е включен в автоматично създаден договор за
         * наем/аренда този имот да се изтрива и от новите автоматично създадени договори
         * */
        $UserDbContractsController->deleteCPRelForCPRel($relations);

        $options = [
            'tablename' => $UserDbController->DbHandler->contractsPlotsRelTable,
            'id_name' => 'contract_id',
            'id_string' => implode(', ', $id_array),
            'where' => [
                'plot_id' => ['column' => 'plot_id', 'compare' => '=', 'value' => $plot_id],
            ],
        ];
        $id_string = $options['id_string'];

        $before_delete = [];
        $after_delete = [];
        $plot_result_before = $UserDbContractsController->getPlotIDsForContracts();
        $plot_before_count = count($plot_result_before);
        for ($i = 0; $i < $plot_before_count; $i++) {
            $before_delete[] = $plot_result_before[$i]['plot_id'];
        }
        $UserDbController->deleteItemsByParams($options);

        $plot_result_after = $UserDbContractsController->getPlotIDsForContracts();
        $plot_after_count = count($plot_result_after);
        for ($i = 0; $i < $plot_after_count; $i++) {
            $after_delete[] = $plot_result_after[$i]['plot_id'];
        }

        $diff_array = array_diff($before_delete, $after_delete);
        if (0 != count($diff_array)) {
            $id_string = implode(', ', $diff_array);
            // update status contract to false;
            $UserDbContractsController->updatePlotContractStatus($id_string, false);
        }

        foreach ($id_array as $key => $contract_id) {
            $UserDbContractsController->manageOverallRenta($contract_id);
        }

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['plot_id' => $plot_id, 'contract_id' => $id_array], ['deleted-pc_rel_id' => $relations], 'delete contract plot rel');
    }
}
