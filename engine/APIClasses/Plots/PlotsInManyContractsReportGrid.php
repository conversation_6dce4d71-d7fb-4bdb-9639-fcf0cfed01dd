<?php

namespace TF\Engine\APIClasses\Plots;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\Export2XlsClass;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDbAreaTypes\UserDbAreaTypesController;
use TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController;

/**
 * Справка "Имоти в повече от един договор".
 *
 * @rpc-module Plots
 *
 * @rpc-service-id plots-in-many-contracts-report-grid
 */
class PlotsInManyContractsReportGrid extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getPlotsInManyContracts'],
                'validators' => [
                    'params' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'exportToExcelPlotsInManyContractsReportData' => ['method' => [$this, 'exportToExcelPlotsInManyContractsReportData'],
                'validators' => [
                    'data' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * Export To Excel Plots In Many Contracts Report Data.
     *
     * @param array $data {
     *                    #items array filters {
     *                    #item  timestamp report_date
     *                    #item  timestamp report_date_from
     *                    #item  timestamp report_date_as_of
     *                    #item  timestamp report_contract_date
     *                    #item  timestamp report_contract_date_to
     *                    #item  integer report_farming
     *                    #item  integer report_ekate
     *                    #item  integer report_ntp
     *                    #item  integer report_category
     *                    #item  string report_mestnost
     *                    #item  string report_irrigation
     *                    }
     *                    }
     * @param int|string $page
     * @param int|string $rows
     * @param ?string $sort
     * @param ?string $order
     *
     * @return array
     */
    public function exportToExcelPlotsInManyContractsReportData(array $data, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $data['for_export'] = true;
        $results = $this->getPlotsInManyContracts($data, $page, $rows, $sort, $order);

        $result = $results['rows'];

        unset($result[0]['attributes']);

        $columns = [
            'Землище',
            'Идентификатор',
            'Площ по документ (дка)',
            'Обща площ по договори (дка)',
            'Площ по договор (дка)',
            'Договор №',
            'Дата',
            'Тип',
            'Стопанство',
            'Влиза в сила',
            'Валиден до',
            'Статус',
        ];
        $this->addTotals($result);
        $time = strtotime(date('Y-m-d H:i:s'));
        $fileName = 'imoti_v_mnogo_dogovori_' . $this->User->GroupID . '_' . $time . '.xlsx';
        $path = PUBLIC_UPLOAD_EXPORT . '/' . $fileName;

        $export2Xls = new Export2XlsClass();
        $filePath = $export2Xls->exportUrlPath($path, $result, $columns);

        return ['file_path' => $filePath, 'file_name' => $fileName];
    }

    /**
     * getPlotsInManyContracts.
     *
     * @api-method read
     *
     * @param array $params {
     *                      #items array filters {
     *                      #item  timestamp report_date
     *                      #item  timestamp report_date_from
     *                      #item  timestamp report_date_as_of
     *                      #item  timestamp report_contract_date
     *                      #item  timestamp report_contract_date_to
     *                      #item  integer report_farming
     *                      #item  integer report_ekate
     *                      #item  integer report_ntp
     *                      #item  integer report_category
     *                      #item  string report_mestnost
     *                      #item  string report_irrigation
     *                      }
     *                      }
     * @param int|string $page
     * @param int|string $rows
     * @param ?string $sort
     * @param ?string $order
     *
     * @return array
     */
    public function getPlotsInManyContracts(array $params, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $reportDateFrom = $params['filters']['report_date_from'];
        $reportDate = $params['filters']['report_date'];
        $reportDateAsOf = $params['filters']['report_date_as_of'];
        $reportContractDate = $params['filters']['report_contract_date'];
        $reportContractDateTo = $params['filters']['report_contract_date_to'];
        $reportFarming = $params['filters']['report_farming'];
        $reportEkate = $params['filters']['report_ekate'];
        $reportMasiv = $params['filters']['report_masiv'];
        $reportPlotNumber = $params['filters']['report_plot_number'];
        $reportKadIdent = $params['filters']['report_kad_ident'];
        $reportNtp = $params['filters']['report_ntp'];
        $reportCategory = $params['filters']['report_category'];
        $reportMestnost = $params['filters']['report_mestnost'];
        $reportIrrigation = null;
        if ('all' != $params['filters']['report_irrigation']) {
            $reportIrrigation = $params['filters']['report_irrigation'];
        }

        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);
        $FarmingController = new FarmingController('Farming');

        $farmings = $FarmingController->getUserFarmings();
        $farmingIds = array_keys($farmings);

        if ($reportFarming && in_array((int) $reportFarming, $farmingIds)) {
            $farmingIds = [(int) $reportFarming];
        }

        date_default_timezone_set('Europe/Sofia');
        $currentDate = date('Y-m-d', time());

        if ('' == $reportDateFrom && '' == $reportDate && '' == $reportDateAsOf) {
            $reportDate = '9999-01-01';
            $reportDateFrom = '1900-01-01';
        }

        // create the default return
        $return = [
            'rows' => [],
            'total' => 0,
            'footer' => [],
        ];

        $options = [
            'sort' => $sort,
            'order' => $order,
            'return' => [
                'gid',
                'virtual_ekatte_name as land',
                'kad_ident',
                'COALESCE(mestnost, \'-\') as mestnost',
                'COALESCE(virtual_category_title, \'-\') as category',
                'virtual_ntp_title as area_type',
                '(CASE WHEN document_area IS NOT NULL THEN round(document_area::numeric, 3) ELSE round((ST_Area(geom)/1000)::numeric, 3) END) as document_area',
                'array_agg(c.id) as c_id',
                'array_agg(c.c_num) as c_num',
                'array_agg(c.virtual_contract_type) as c_type',
                "array_agg(to_char(c.c_date,'DD.MM.YYYY')) as c_date",
                "array_agg(to_char(c.start_date,'DD.MM.YYYY')) as start_date",
                "array_agg(to_char((CASE WHEN a.due_date IS NULL THEN c.due_date ELSE a.due_date END),'DD.MM.YYYY')) as due_date",
                'array_agg(c.farming_id) as farming_id',
                'array_agg(round(pc.contract_area::numeric, 3)) as contract_area',
                'array_agg(c.active) as active',
            ],
            'where' => [
                'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'prefix' => 'c', 'value' => 'false'],
                'is_annnex' => ['column' => 'is_annex', 'compare' => '=', 'prefix' => 'c', 'value' => 'false'],
                'annex_action' => ['column' => 'annex_action', 'compare' => '=', 'prefix' => 'pc', 'value' => 'added'],
                'active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'true'],
                'c_type' => ['column' => 'nm_usage_rights', 'compare' => '<>', 'prefix' => 'c', 'value' => 4],
                'c_date' => ['column' => 'c_date', 'compare' => '>=', 'prefix' => 'c', 'value' => $reportContractDate],
                'c_date_to' => ['column' => 'c_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $reportContractDateTo],
                'farming_id' => ['column' => 'farming_id', 'compare' => 'IN', 'prefix' => 'c', 'value' => $farmingIds],
            ],
            'start_date' => $reportDate,
            'due_date' => $reportDateFrom,
            'group' => 'kvs.gid',
            'having' => 'count(DISTINCT(c.id)) > 1',
        ];

        if ($reportEkate) {
            $options['where']['ekate'] = ['column' => 'ekate', 'compare' => '=', 'prefix' => 'kvs', 'value' => $reportEkate];
        }
        if ($reportMasiv) {
            $options['where']['masiv'] = ['column' => 'masiv', 'prefix' => 'kvs', 'compare' => '=', 'value' => $reportMasiv];
        }
        if ($reportPlotNumber) {
            $options['where']['number'] = ['column' => 'number', 'prefix' => 'kvs', 'compare' => '=', 'value' => $reportPlotNumber];
        }
        if ($reportKadIdent) {
            $options['where']['kad_ident'] = ['column' => 'kad_ident', 'prefix' => 'kvs', 'compare' => '=', 'value' => $reportKadIdent];
        }
        if ($reportNtp) {
            $UserDbAreaTypesController = new UserDbAreaTypesController($this->User->Database);
            $ntpFilter = $UserDbAreaTypesController->getNtpCodesWithAdditionalCodes([$reportNtp]);
            $options['where']['ntp'] = ['column' => 'area_type', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $ntpFilter];
        }
        if ($reportCategory) {
            $categoryFilter = in_array($reportCategory, ['-1', '0', null]) ? ['-1', '0', null] : [$reportCategory];
            $options['where']['category'] = ['column' => 'category', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $categoryFilter];
        }
        if ($reportMestnost) {
            $options['where']['mestnost'] = ['column' => 'mestnost', 'compare' => '=', 'prefix' => 'kvs', 'value' => $reportMestnost];
        }

        if ($reportIrrigation) {
            $options['where']['irrigated_area'] = ['column' => 'irrigated_area', 'compare' => '=', 'prefix' => 'kvs', 'value' => $reportIrrigation];
        }

        if ($reportDateAsOf) {
            $options['start_date'] = $reportDateAsOf;
            $options['due_date'] = $reportDateAsOf;
        }

        $results_total = $UserDbPlotsController->getFullPlotDataForReport($options, false, false);

        $totalCounter = count($results_total);

        if (0 == $totalCounter) {
            return $return;
        }

        $options['offset'] = ($page - 1) * $rows;
        $options['limit'] = $rows;

        $results = $UserDbPlotsController->getFullPlotDataForReport($options, false, false);
        $resultsCount = count($results);

        $total_document_area = 0;
        $total_sum_contract_area = 0;
        for ($i = 0; $i < $totalCounter; $i++) {
            $contracts = $this->getContractsTotal($results_total[$i], $farmings);

            $results_total[$i]['sum_contract_area'] = array_sum(array_map(function ($el) {return $el['contract_area'];}, $contracts));

            $total_document_area += $results_total[$i]['document_area'];
            $total_sum_contract_area += $results_total[$i]['sum_contract_area'];
        }

        $total_document_area_for_page = 0;
        $total_sum_contract_area_for_page = 0;
        for ($i = 0; $i < $resultsCount; $i++) {
            $c_ids = explode(',', trim($results[$i]['c_id'], '{}'));
            $c_nums = explode(',', trim($results[$i]['c_num'], '{}'));
            $c_dates = explode(',', trim($results[$i]['c_date'], '{}'));
            $start_dates = explode(',', trim($results[$i]['start_date'], '{}'));
            $due_dates = explode(',', trim($results[$i]['due_date'], '{}'));
            $c_types = explode(',', trim($results[$i]['c_type'], '{}'));
            $c_farmings = explode(',', trim($results[$i]['farming_id'], '{}'));
            $contract_areas = explode(',', trim($results[$i]['contract_area'], '{}'));
            $is_active = explode(',', trim($results[$i]['active'], '{}'));

            $contracts = [];
            $cIdsCount = count($c_ids);
            for ($j = 0; $j < $cIdsCount; $j++) {
                $contracts[$c_ids[$j]]['c_id'] = $c_ids[$j];
                $contracts[$c_ids[$j]]['c_num'] = $c_nums[$j];
                $contracts[$c_ids[$j]]['c_date'] = $c_dates[$j];
                $contracts[$c_ids[$j]]['start_date'] = $start_dates[$j];
                $contracts[$c_ids[$j]]['due_date'] = ('NULL' != $due_dates[$j]) ? $due_dates[$j] : '-';
                $contracts[$c_ids[$j]]['c_type'] = $c_types[$j];
                $contracts[$c_ids[$j]]['farming_id'] = $farmings[$c_farmings[$j]];
                $contracts[$c_ids[$j]]['contract_area'] = $contract_areas[$j];

                if ('t' == $is_active[$j]) {
                    $contracts[$c_ids[$j]]['status'] = ('NULL' == $due_dates[$j] || date('Y-m-d', strtotime($due_dates[$j])) >= date('Y-m-d', strtotime($currentDate))) ? 'Действащ' : 'Изтекъл';
                } else {
                    $contracts[$c_ids[$j]]['status'] = 'Анулиран';
                }
            }
            $results[$i]['c_id'] = implode(',', array_map(function ($el) {return $el['c_id'];}, $contracts));
            $results[$i]['c_num'] = implode('<br/>', array_map(function ($el) {return $el['c_num'];}, $contracts));
            $results[$i]['c_date'] = implode('<br/>', array_map(function ($el) {return $el['c_date'];}, $contracts));
            $results[$i]['start_date'] = implode('<br/>', array_map(function ($el) {return $el['start_date'];}, $contracts));
            $results[$i]['due_date'] = implode('<br/>', array_map(function ($el) {return $el['due_date'];}, $contracts));
            $results[$i]['c_type'] = implode('<br/>', array_map(function ($el) {return $el['c_type'];}, $contracts));
            $results[$i]['farming_id'] = implode('<br/>', array_map(function ($el) {return $el['farming_id'];}, $contracts));
            $results[$i]['contract_area'] = implode('<br/>', array_map(function ($el) {return number_format($el['contract_area'], 3, '.', ' ');}, $contracts));
            $results[$i]['document_area'] = number_format($results[$i]['document_area'], 3, '.', ' ');
            $results[$i]['status'] = implode('<br/>', array_map(function ($el) {return $el['status'];}, $contracts));
            $results[$i]['sum_contract_area'] = array_sum(array_map(function ($el) {return $el['contract_area'];}, $contracts));
            $results[$i]['sum_contract_area'] = number_format($results[$i]['sum_contract_area'], 3, '.', ' ');
            $total_document_area_for_page += $results[$i]['document_area'];
            $total_sum_contract_area_for_page += $results[$i]['sum_contract_area'];
        }

        if (isset($params['for_export']) && true == $params['for_export']) {
            $results = $this->formatRowsForExport($results);
        }

        $return['rows'] = $results;
        $return['total'] = $totalCounter;
        $return['footer'] = [
            [
                'kad_ident' => '<b>Общо за стр.</b>',
                'document_area' => number_format($total_document_area_for_page, 3, '.', ' '),
                'sum_contract_area' => number_format($total_sum_contract_area_for_page, 3, '.', ' '),
            ],
            [
                'kad_ident' => '<b>Общо</b>',
                'document_area' => number_format($total_document_area, 3, '.', ' '),
                'sum_contract_area' => number_format($total_sum_contract_area, 3, '.', ' '),
            ],
        ];

        return $return;
    }

    private function formatRowsForExport($rows)
    {
        $results = [];

        $count = count($rows);
        for ($i = 0; $i < $count; $i++) {
            $results[$i]['land'] = $rows[$i]['land'];
            $results[$i]['kad_ident'] = $rows[$i]['kad_ident'];
            $results[$i]['document_area'] = $rows[$i]['document_area'];
            $results[$i]['sum_contract_area'] = $rows[$i]['sum_contract_area'];
            $results[$i]['contract_area'] = str_replace('<br/>', ', ', $rows[$i]['contract_area']);
            $results[$i]['c_num'] = str_replace('<br/>', ', ', $rows[$i]['c_num']);
            $results[$i]['c_date'] = str_replace('<br/>', ', ', $rows[$i]['c_date']);
            $results[$i]['c_type'] = str_replace('<br/>', ', ', $rows[$i]['c_type']);
            $results[$i]['farming_id'] = str_replace('<br/>', ', ', $rows[$i]['farming_id']);
            $results[$i]['start_date'] = str_replace('<br/>', ', ', $rows[$i]['start_date']);
            $results[$i]['due_date'] = str_replace('<br/>', ', ', $rows[$i]['due_date']);
            $results[$i]['status'] = str_replace('<br/>', ', ', $rows[$i]['status']);
        }

        return $results;
    }

    private function addTotals(&$rows)
    {
        $i = count($rows);
        $rows[$i]['land'] = '';
        $rows[$i]['kad_ident'] = '';
        $rows[$i]['document_area'] = array_sum(array_column($rows, 'document_area'));
        $sum_contract_area = array_sum(array_column($rows, 'sum_contract_area'));
        $rows[$i]['sum_contract_area'] = $sum_contract_area;
        $rows[$i]['contract_area'] = $sum_contract_area;
        $rows[$i]['c_num'] = '';
        $rows[$i]['c_date'] = '';
        $rows[$i]['c_type'] = '';
        $rows[$i]['farming_id'] = '';
        $rows[$i]['start_date'] = '';
        $rows[$i]['due_date'] = '';
        $rows[$i]['status'] = '';
    }

    private function getContractsTotal($result, $farmings)
    {
        $c_ids = explode(',', trim($result['c_id'], '{}'));
        $c_nums = explode(',', trim($result['c_num'], '{}'));
        $c_dates = explode(',', trim($result['c_date'], '{}'));
        $start_dates = explode(',', trim($result['start_date'], '{}'));
        $due_dates = explode(',', trim($result['due_date'], '{}'));
        $c_types = explode(',', trim($result['c_type'], '{}'));
        $c_farmings = explode(',', trim($result['farming_id'], '{}'));
        $contract_areas = explode(',', trim($result['contract_area'], '{}'));
        $is_active = explode(',', trim($result['active'], '{}'));
        $cIdsCount = count($c_ids);

        $contracts = [];
        for ($j = 0; $j < $cIdsCount; $j++) {
            $contracts[$c_ids[$j]]['c_num'] = $c_nums[$j];
            $contracts[$c_ids[$j]]['c_date'] = $c_dates[$j];
            $contracts[$c_ids[$j]]['start_date'] = $start_dates[$j];
            $contracts[$c_ids[$j]]['due_date'] = ('NULL' != $due_dates[$j]) ? $due_dates[$j] : '-';
            $contracts[$c_ids[$j]]['c_type'] = $c_types[$j];
            $contracts[$c_ids[$j]]['farming_id'] = $farmings[$c_farmings[$j]];
            $contracts[$c_ids[$j]]['contract_area'] = $contract_areas[$j];
        }

        return $contracts;
    }
}
