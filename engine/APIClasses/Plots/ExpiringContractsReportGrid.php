<?php

namespace TF\Engine\APIClasses\Plots;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\ExportToExcelClass;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDbAreaTypes\UserDbAreaTypesController;
use TF\Engine\Plugins\Core\UserDbOwners\UserDbOwnersController;
use TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController;

/**
 * Изтичащи договори.
 *
 * @rpc-module Plots
 *
 * @rpc-service-id expiring-contracts-report-grid
 */
class ExpiringContractsReportGrid extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getExpiringContracts'],
                'validators' => [
                    'params' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'exportToExcelExpiringContractsReportData' => ['method' => [$this, 'exportToExcelExpiringContractsReportData'],
                'validators' => [
                    'data' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * Export To Excel Expiring Contracts Report Data.
     *
     * * @param  array $data
     * [
     *     #items array filters
     *     [
     *         #item  timestamp report_date        		    The start date of the report.
     *         #item  timestamp report_date_from   		    The end date of the report.
     *         #item  timestamp report_date_as_of  			The date of the report.
     *         #item  timestamp report_contract_date   		The lower end of the contract date interval.
     *         #item  timestamp report_contract_date_to   	The higher end of the contract date interval.
     *         #item  integer report_farming       			A farming 'id'.
     *         #item  integer report_ekate         			An EKATTE number.
     *         #item  integer report_ntp           			An area type id.
     *         #item  integer report_category      			A plot category number.
     *         #item  string report_mestnost       			Тhe name of the locality
     *         #item  string report_irrigation     			Is the plot irrigated area
     *     ]
     * ]
     * @param int $page the current page number
     * @param int $rows rows per page
     * @param string $sort a grid column by which the grid is sorted
     * @param string $order The sort order ASC/DESC
     *
     * @return array
     */
    public function exportToExcelExpiringContractsReportData(array $data, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $time = strtotime(date('Y-m-d H:i:s'));
        $fileName = 'imoti_s_iztichashti_dogovori_' . $this->User->GroupID . '_' . $time . '.xlsx';
        $path = PUBLIC_UPLOAD_EXPORT . '/' . $this->User->GroupID . '/' . $fileName;

        if (!file_exists(PUBLIC_UPLOAD_EXPORT . '/' . $this->User->GroupID)) {
            mkdir(PUBLIC_UPLOAD_EXPORT . '/' . $this->User->GroupID, 0777);
        }

        $results = $this->getExpiringContracts($data, $page, $rows, $sort, $order);

        $result = $results['rows'];

        $result = $this->formatRowsForExport($result);

        $columns = [
            'land' => 'Землище',
            'kad_ident' => 'Идентификатор',
            'document_area' => 'Пл. по док.(дка)',
            'mestnost' => 'Местност',
            'category' => 'Категория',
            'area_type' => 'НТП',
            'c_num' => 'Договор №',
            'due_date' => 'Валиден до',
            'c_type' => 'Ползване',
            'area' => 'Площ по дог.(дка)',
            'fraction' => 'Ид. част',
            'farming_id' => 'Стопанство',
            'owner_names' => 'Арендодател/Наемодател',
            'egn' => 'ЕГН/ЕИК',
            'phones' => 'Телефон',
            'sv_num' => 'Вписване №',
            'sv_date' => 'Дата на вписване',
            'renewed' => 'Подновен',
            'new_c_num' => 'Нов дог.',
            'new_c_period' => 'Период',
        ];

        $this->addTotals($result);

        $exportExcelDoc = new ExportToExcelClass();
        $exportExcelDoc->export($result, $columns, []);
        $exportExcelDoc->saveFile($path);
        $filePath = PUBLIC_UPLOAD_EXPORT_RELATIVE_PATH . $this->User->GroupID . '/' . $fileName;

        return ['file_path' => $filePath, 'file_name' => $fileName];
    }

    /**
     * Returns a report for all expiring contracts.
     *
     * @api-method read
     *
     * * @param  array $params
     * [
     *     #items array filters
     *     [
     *         #item  timestamp report_date        		    The start date of the report.
     *         #item  timestamp report_date_from   		    The end date of the report.
     *         #item  timestamp report_date_as_of  			The date of the report.
     *         #item  timestamp report_contract_date   		The lower end of the contract date interval.
     *         #item  timestamp report_contract_date_to   	The higher end of the contract date interval.
     *         #item  integer report_farming       			A farming 'id'.
     *         #item  integer report_ekate         			An EKATTE number.
     *         #item  integer report_ntp           			An area type id.
     *         #item  integer report_category      			A plot category number.
     *         #item  string report_mestnost       			Тhe name of the locality
     *         #item  string report_irrigation     			Is the plot irrigated area
     *     ]
     * ]
     * @param int $page the current page number
     * @param int $rows rows per page
     * @param string $sort a grid column by which the grid is sorted
     * @param string $order The sort order ASC/DESC
     *
     * @return array
     */
    public function getExpiringContracts(array $params, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $reportDate = $params['filters']['report_date'];
        $reportDateFrom = $params['filters']['report_date_from'];
        $reportContractDate = $params['filters']['report_contract_date'];
        $reportContractDateTo = $params['filters']['report_contract_date_to'];
        $reportFarming = $params['filters']['report_farming'];
        $reportEkate = $params['filters']['report_ekate'];
        $reportMasiv = $params['filters']['report_masiv'];
        $reportPlotNumber = $params['filters']['report_plot_number'];
        $reportKadIdent = $params['filters']['report_kad_ident'];
        $reportNtp = $params['filters']['report_ntp'];
        $reportCategory = $params['filters']['report_category'];
        $reportMestnost = $params['filters']['report_mestnost'];
        $reportArendator = '' == $params['filters']['report_arendator'] ? null : $params['filters']['report_arendator'];
        $reportRenewed = $params['filters']['report_choose_renewed'];
        if (!empty($params['filters']['report_irrigation']) && 'all' !== $params['filters']['report_irrigation']) {
            $reportIrrigation = $params['filters']['report_irrigation'];
        }

        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);
        $FarmingController = new FarmingController('Farming');

        $userFarmings = $FarmingController->getUserFarmings();
        $farmingIds = array_keys($userFarmings);

        if ($reportFarming && in_array((int) $reportFarming, $farmingIds)) {
            $farmingIds = [(int) $reportFarming];
        }

        // create the default return
        $return = [
            'rows' => [],
            'total' => 0,
            'footer' => [],
        ];

        $dates = $this->getReportDates($reportDateFrom, $reportDate);

        if ('' != $reportArendator) {
            $owners_options = [
                'return' => [
                    'DISTINCT o.id',
                ],
            ];

            $tmp_arendator_names = preg_replace('/\s+/', '.*', $reportArendator);
            $tmp_arendator_names = mb_strtolower($tmp_arendator_names, 'UTF-8');

            $tmp_arendator_names_string = "regexp_matches(lower(TRIM (o.name)) || ' ' || lower(TRIM (o.surname)) || ' ' || lower(TRIM (o.lastname)),'{$tmp_arendator_names}','g')";
            $tmp_arendator_company = "regexp_matches(lower(TRIM (o.company_name)),'{$tmp_arendator_names}','g')";
            $tmp_names = "(CASE WHEN o.owner_type = 1 THEN {$tmp_arendator_names_string} ELSE {$tmp_arendator_company} END) as names";
            array_push($owners_options['return'], $tmp_names);
            $owners = $UserDbOwnersController->getOwnersData($owners_options, false, false);

            $owner_ids = [];
            foreach ($owners as $owner => $value) {
                $owner_ids[] = $value['id'];
            }
        }

        $options = [
            'sort' => $sort,
            'order' => $order,
            'return' => [
                'DISTINCT (pc. ID)',
                'gid',
                'virtual_ekatte_name as land',
                'kad_ident',
                'COALESCE(mestnost, \'-\') as mestnost',
                'COALESCE(virtual_category_title, \'-\') as category',
                'virtual_ntp_title as area_type',
                'pc.contract_area AS area', 'C .c_num', 'C .is_annex', 'C .sv_num', "to_char(C .sv_date, 'DD.MM.YYYY') AS sv_date",
                'C.virtual_contract_type AS c_type', 'C .farming_id', "to_char(C .due_date, 'DD.MM.YYYY') AS due_date",
                'C .renta', 'pc.price_per_acre', 'pc.price_sum', 'pf.farming_id AS arendodatel', 'c.id as c_id', 'c_a.id as c_a_id', 'c_a.c_num c_a_c_num',
                "string_agg (DISTINCT(CASE WHEN owner_type = 1 THEN NAME || ' ' || surname || ' ' || lastname ELSE company_name END), ',<br />') AS owner_names",
                "string_agg (DISTINCT(CASE WHEN NULLIF(TRIM(phone), '') IS NOT NULL OR NULLIF(TRIM(mobile), '') IS NOT NULL THEN (CASE WHEN owner_type = 1 THEN concat_ws(', ', NULLIF(TRIM(phone), ''),NULLIF(TRIM(mobile), '')) || ': ' || NAME || ' ' || surname || ' ' || lastname ELSE concat_ws(', ', NULLIF(TRIM(phone), ''),NULLIF(TRIM(mobile), '')) || ': ' || company_name END) ELSE null END), ';<br />') AS phones",                "string_agg (DISTINCT(CASE WHEN o.owner_type = 1 THEN o.egn ELSE o.eik END),',<br />') AS egn",
                'round((pc.contract_area /case when document_area is null OR document_area = 0 then ST_AREA(geom)/1000 else document_area end)::numeric,3) as fraction',
                'round((case when document_area is null OR document_area = 0 then ST_AREA(geom)/1000 else document_area end)::numeric,3) as document_area',
                'new_c_info.c_info as new_c_info',
                "case when new_c_info.c_info is not null then 'Да' else 'Не' end as renewed",
            ],
            'where' => [
                'c_date' => ['column' => 'c_date', 'compare' => '>=', 'prefix' => 'c', 'value' => $reportContractDate],
                'c_date_to' => ['column' => 'c_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $reportContractDateTo],
                'annex_action' => ['column' => 'annex_action', 'compare' => '=', 'prefix' => 'pc', 'value' => 'added'],
                'nm_usage_rights' => ['column' => 'nm_usage_rights', 'compare' => '!=', 'prefix' => 'C', 'value' => 4],
                'farming_id' => ['column' => 'farming_id', 'compare' => 'IN', 'prefix' => 'c', 'value' => $farmingIds],
            ],
            'start_date' => $dates['start_date'],
            'due_date' => $dates['due_date'],
            'new_start_date' => $dates['new_start_date'],
            'new_due_date' => $dates['new_due_date'],
            'group' => 'kvs.gid, pc. ID, C . ID, pf. ID, new_c_info.c_info, c_a.id',
        ];

        if ($reportNtp) {
            $UserDbAreaTypesController = new UserDbAreaTypesController($this->User->Database);
            $ntpFilter = $UserDbAreaTypesController->getNtpCodesWithAdditionalCodes([$reportNtp]);
            $options['where']['ntp'] = ['column' => 'area_type', 'compare' => 'IN', 'value' => $ntpFilter];
        }
        if ($reportEkate) {
            $options['where']['ekate'] = ['column' => 'ekate', 'compare' => '=', 'value' => $reportEkate];
        }
        if ($reportMasiv) {
            $options['where']['masiv'] = ['column' => 'masiv', 'prefix' => 'kvs', 'compare' => '=', 'value' => $reportMasiv];
        }
        if ($reportPlotNumber) {
            $options['where']['number'] = ['column' => 'number', 'prefix' => 'kvs', 'compare' => '=', 'value' => $reportPlotNumber];
        }
        if ($reportKadIdent) {
            $options['where']['kad_ident'] = ['column' => 'kad_ident', 'prefix' => 'kvs', 'compare' => '=', 'value' => $reportKadIdent];
        }

        if ($reportCategory) {
            $categoryFilter = in_array($reportCategory, ['-1', '0', null]) ? ['-1', '0', null] : [$reportCategory];
            $options['where']['category'] = ['column' => 'category', 'compare' => 'IN', 'value' => $categoryFilter];
        }
        if ($reportMestnost) {
            $options['where']['mestnost'] = ['column' => 'mestnost', 'compare' => '=', 'value' => $reportMestnost];
        }

        if (!empty($reportIrrigation)) {
            $options['where']['irrigated_area'] = ['column' => 'irrigated_area', 'compare' => '=', 'prefix' => 'kvs', 'value' => $reportIrrigation];
        }

        if (!empty($owner_ids)) {
            $options['where']['arendator'] = ['column' => 'owner_id', 'compare' => 'IN', 'prefix' => 'po', 'value' => $owner_ids];
        }

        if ('' != $reportRenewed) {
            $tmpRenewed = 'true' === $reportRenewed ? true : false;
            $options['renewed'] = $tmpRenewed;
        }
        $counter = $UserDbPlotsController->getPlotsInExpiringContractsForReport($options, true, false);

        if (0 == $counter[0]['count']) {
            return $return;
        }

        if ($rows) {
            $options['offset'] = ($page - 1) * $rows;
            $options['limit'] = $rows;
        }

        $results = $UserDbPlotsController->getPlotsInExpiringContractsForReport($options, false, false);

        $total_area_for_page = 0;
        $total_renta_for_page = 0;
        $total_price_per_acre = 0;
        $total_price_sum = 0;

        $resCount = count($results);
        for ($i = 0; $i < $resCount; $i++) {
            if ($results[$i]['is_annex']) {
                $results[$i]['c_num'] = $results[$i]['c_num'] . ' (Анекс) от ' . $results[$i]['c_a_c_num'] . ' (Договор)';
            }
            $results[$i]['farming_id'] = $userFarmings[$results[$i]['farming_id']];
            $results[$i]['new_c_num'] = '';
            $results[$i]['new_c_period'] = '';
            $results[$i]['new_c_links'] = '';
            if ($results[$i]['new_c_info']) {
                $tmpNewContractData = $this->formatNewContractData($results[$i]['new_c_info']);
                $results[$i]['new_c_num'] = $tmpNewContractData['c_num'];
                $results[$i]['new_c_period'] = $tmpNewContractData['period'];
                $results[$i]['new_c_links'] = $tmpNewContractData['links'];
            }
            $total_area_for_page += $results[$i]['area'];
            $total_renta_for_page += $results[$i]['renta'];
            $total_price_per_acre += $results[$i]['price_per_acre'];
            $total_price_sum += $results[$i]['price_sum'];
            $results[$i]['area'] = number_format($results[$i]['area'], 3, '.', '');
            $results[$i]['price_per_acre'] = number_format($results[$i]['price_per_acre'], 2, '.', '');
            $results[$i]['price_sum'] = number_format($results[$i]['price_sum'], 2, '.', '');

            if (!$results[$i]['sv_num']) {
                $results[$i]['sv_num'] = '-';
            }

            if (!$results[$i]['sv_date']) {
                $results[$i]['sv_date'] = '-';
            }

            if (!$results[$i]['owner_names']) {
                $arendodatel = $userFarmings[$results[$i]['arendodatel']];

                if ($arendodatel) {
                    $results[$i]['owner_names'] = $arendodatel;
                } else {
                    $results[$i]['owner_names'] = '-';
                }
            }
        }

        $return['rows'] = $results;
        $return['total'] = $counter[0]['count'];
        $return['footer'] = [
            [
                'area_type' => '<b>Общо за стр.</b>',
                'area' => number_format($total_area_for_page, 3, '.', ''),
                'renta' => number_format($total_renta_for_page, 2, '.', ''),
            ],
            [
                'area_type' => '<b>Общо</b>',
                'area' => number_format($counter[0]['area'], 3, '.', ''),
                'renta' => number_format($counter[0]['renta'] ?? 0, 2, '.', ''),
            ],
        ];

        return $return;
    }

    private function formatRowsForExport($rows)
    {
        $results = [];

        $rowsCount = count($rows);
        for ($i = 0; $i < $rowsCount; $i++) {
            $results[$i]['land'] = $rows[$i]['land'];
            $results[$i]['kad_ident'] = $rows[$i]['kad_ident'];
            $results[$i]['document_area'] = $rows[$i]['document_area'];
            $results[$i]['c_num'] = $rows[$i]['c_num'];
            $results[$i]['due_date'] = $rows[$i]['due_date'];
            $results[$i]['renewed'] = $rows[$i]['new_c_num'] ? 'Да' : 'Не';
            $results[$i]['new_c_num'] = str_replace('<br />', "\n", $rows[$i]['new_c_num']);
            $results[$i]['new_c_period'] = str_replace('<br />', "\n", $rows[$i]['new_c_period']);
            $results[$i]['mestnost'] = $rows[$i]['mestnost'];
            $results[$i]['category'] = $rows[$i]['category'];
            $results[$i]['area_type'] = $rows[$i]['area_type'];
            $results[$i]['area'] = $rows[$i]['area'];
            $results[$i]['fraction'] = $this->dec2fraction((float)$rows[$i]['fraction']);
            $results[$i]['c_type'] = $rows[$i]['c_type'];
            $results[$i]['farming_id'] = $rows[$i]['farming_id'];
            $results[$i]['owner_names'] = str_replace('<br />', "\n", $rows[$i]['owner_names']);
            $results[$i]['egn'] = str_replace('<br />', "\n", $rows[$i]['egn']);
            $results[$i]['phones'] = str_replace('<br />', "\n", $rows[$i]['phones']);
            $results[$i]['sv_num'] = $rows[$i]['sv_num'];
            $results[$i]['sv_date'] = $rows[$i]['sv_date'];
        }

        return $results;
    }

    private function addTotals(&$rows)
    {
        $i = count($rows);
        $rows[$i]['land'] = '';
        $rows[$i]['kad_ident'] = '';
        $rows[$i]['document_area'] = number_format(array_sum(array_column($rows, 'document_area')), 3);
        $rows[$i]['c_num'] = '';
        $rows[$i]['due_date'] = '';
        $rows[$i]['renewed'] = '';
        $rows[$i]['new_c_num'] = '';
        $rows[$i]['new_c_period'] = '';
        $rows[$i]['mestnost'] = '';
        $rows[$i]['category'] = '';
        $rows[$i]['area_type'] = '';
        $rows[$i]['area'] = number_format(array_sum(array_column($rows, 'area')), 3);
        $rows[$i]['fraction'] = '';
        $rows[$i]['c_type'] = '';
        $rows[$i]['farming_id'] = '';
        $rows[$i]['owner_names'] = '';
        $rows[$i]['egn'] = '';
        $rows[$i]['phones'] = '';
        $rows[$i]['sv_num'] = '';
        $rows[$i]['sv_date'] = '';
    }

    private function getReportDates($start_date, $due_date)
    {
        $return['start_date'] = $start_date;
        $return['due_date'] = $due_date;

        $tmpNewStartDate = date(date('Y', strtotime($due_date)) . '-10-01');
        if ($tmpNewStartDate < $due_date) {
            $tmpNewStartDate = date('Y-m-d', strtotime($tmpNewStartDate . ' + 1 year'));
        }

        $tmpNewDueDate = date('Y-m-d', strtotime($tmpNewStartDate . ' + 1 year - 1 day'));

        $return['new_start_date'] = $tmpNewStartDate;
        $return['new_due_date'] = $tmpNewDueDate;

        return $return;
    }

    private function formatNewContractData($cData)
    {
        $return['c_num'] = [];
        $return['links'] = [];
        $return['period'] = [];

        $tmpContracts = explode('__', $cData);
        // "243~~01.10.2017-30.09.2018~~true~~aneks~~3"
        // [0] - 243                   - c.id
        // [1] - 01.10.2017-30.09.2018 - c.start_date - c.due_date
        // [2] - true/false            - is_annex
        // [3] - aneks                 - c_num
        // [4] - 2/3/5                 - c.nm_usage_rights
        foreach ($tmpContracts as $tmpContract) {
            $c_data = explode('~~', $tmpContract);
            $tmpCNum = $c_data[3];
            $tmpLink = "<a href='";
            $tmpNmUsageRights = $GLOBALS['Contracts']['ContractTypes'][$c_data[4]]['name'];

            if ('true' === $c_data[2]) {
                $tmpLink .= 'index.php?page=Contracts.Home&contract_id=' . $c_data[0];
                $tmpCNum .= '(' . $tmpNmUsageRights . ') - Анекс';
            } else {
                $tmpLink .= 'index.php?page=Contracts.Home&contract_id=' . $c_data[0];
                $tmpCNum .= '(' . $tmpNmUsageRights . ')';
            }
            $tmpLink .= "', target='new'>" . $tmpCNum . '</a>';
            array_push($return['c_num'], $tmpCNum);
            array_push($return['links'], $tmpLink);
            array_push($return['period'], $c_data[1]);
        }
        $return['c_num'] = implode('<br />', $return['c_num']);
        $return['links'] = implode('<br />', $return['links']);
        $return['period'] = implode('<br />', $return['period']);

        return $return;
    }

    // http://php.net/manual/en/ref.math.php
    private function dec2fraction($decimal)
    {
        $decimal = (string)$decimal;
        $num = '';
        $den = 1;
        $dec = false;

        // find least reduced fractional form of number
        for ($i = 0, $ix = strlen($decimal); $i < $ix; $i++) {
            // build the denominator as we 'shift' the decimal to the right
            if ($dec) {
                $den *= 10;
            }

            // find the decimal place/ build the numerator
            if ('.' == $decimal[$i]) {
                $dec = true;
            } else {
                $num .= $decimal[$i];
            }
        }
        $num = (int)$num;

        // whole number, just return it
        if (1 == $den) {
            return $num;
        }

        $num2 = $num;
        $den2 = $den;
        $rem = 1;
        // Euclid's Algorithm (to find the gcd)
        while ($num2 % $den2) {
            $rem = $num2 % $den2;
            $num2 = $den2;
            $den2 = $rem;
        }
        if ($den2 != $den) {
            $rem = $den2;
        }

        // now $rem holds the gcd of the numerator and denominator of our fraction
        return ($num / $rem) . '/' . ($den / $rem);
    }
}
