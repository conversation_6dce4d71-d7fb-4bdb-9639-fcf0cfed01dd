<?php

namespace TF\Engine\APIClasses\Plots;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\Export2XlsClass;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDbAreaTypes\UserDbAreaTypesController;
use TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController;

/**
 * Справка "Собствена земя".
 *
 * @rpc-module Plots
 *
 * @rpc-service-id own-plots-report-grid
 */
class OwnPlotsReportGrid extends TRpcApiProvider
{
    private $UserDbController;

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getOwnPlots']],
            'exportToExcelOwnPlotsReportData' => ['method' => [$this, 'exportToExcelOwnPlotsReportData'],
                'validators' => [
                    'data' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * Export To Excel Own Plots Report Data.
     *
     * @param array $data
     *                    [
     *                    #items array filters
     *                    [
     *                    #item  timestamp report_date
     *                    #item  timestamp report_date_from
     *                    #item  timestamp report_date_as_of
     *                    #item  timestamp report_contract_date
     *                    #item  timestamp report_contract_date_to
     *                    #item  integer report_farming
     *                    #item  integer report_ekate
     *                    #item  integer report_ntp
     *                    #item  integer report_category
     *                    #item  string report_mestnost
     *                    #item  string report_irrigation
     *                    ]
     *                    ]
     * @param int|string $page
     * @param int|string $rows
     * @param ?string $sort
     * @param ?string $order
     *
     * @return array
     */
    public function exportToExcelOwnPlotsReportData(array $data, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        if (!$page && !$rows) {
            $data['export_all_rows'] = true;
        }

        $results = $this->getOwnPlots($data, $page, $rows, $sort, $order);

        $data = $results['rows'];

        unset($data[0]['attributes']);

        $data = $this->formatRowsForExport($data);

        $columns = [
            'Землище',
            'Идентификатор',
            'Местност',
            'Категория',
            'НТП',
            'Площ(дка)',
            'Обработваем',
            'Обработваема площ(дка)',
            'Договор №',
            'Стопанство',
            'Дата',
            'Нотар. акт №',
            'Том',
            'Дело',
            'Районен съд',
            'Цена/дка',
            'Сума',
        ];

        $this->addTotals($data);

        $time = strtotime(date('Y-m-d H:i:s'));
        $fileName = 'sobstvena_zemq_' . $this->User->GroupID . '_' . $time . '.xlsx';
        $path = PUBLIC_UPLOAD_EXPORT . '/' . $this->User->GroupID . '/' . $fileName;

        $export2Xls = new Export2XlsClass();
        $filePath = $export2Xls->exportUrlPath($path, $data, $columns);

        return ['file_path' => $filePath, 'file_name' => $fileName];
    }

    /**
     * @api-method read
     *
     * @param array $params
     *                      [
     *                      #items array filters
     *                      [
     *                      #item  timestamp report_date
     *                      #item  timestamp report_date_from
     *                      #item  timestamp report_date_as_of
     *                      #item  timestamp report_contract_date
     *                      #item  timestamp report_contract_date_to
     *                      #item  integer report_farming
     *                      #item  integer report_ekate
     *                      #item  integer report_ntp
     *                      #item  integer report_category
     *                      #item  string report_mestnost
     *                      #item  string report_irrigation
     *                      ]
     *                      ]
     * @param int $page
     * @param int $rows
     * @param string $sort
     * @param string $order
     *
     * @return array
     */
    public function getOwnPlots($params, $page = 1, $rows = 30, $sort = '', $order = '')
    {
        $reportDateFrom = $params['filters']['report_date_from'];
        $reportDate = $params['filters']['report_date'];
        $reportDateAsOf = $params['filters']['report_date_as_of'];
        $reportContractDate = $params['filters']['report_contract_date'];
        $reportContractDateTo = $params['filters']['report_contract_date_to'];
        $reportFarming = $params['filters']['report_farming'];
        $reportEkate = $params['filters']['report_ekate'];
        $reportMasiv = $params['filters']['report_masiv'];
        $reportPlotNumber = $params['filters']['report_plot_number'];
        $reportKadIdent = $params['filters']['report_kad_ident'];
        $reportNtp = $params['filters']['report_ntp'];
        $reportCategory = $params['filters']['report_category'];
        $reportMestnost = $params['filters']['report_mestnost'];
        $reportExcludeInactive = $params['filters']['report_exclude_inactive'];
        $reportIrrigation = null;
        if ('all' != $params['filters']['report_irrigation']) {
            $reportIrrigation = $params['filters']['report_irrigation'];
        }

        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);
        $FarmingController = new FarmingController('Farming');
        $userFarmings = $FarmingController->getUserFarmings();
        $farmingIds = array_keys($userFarmings);

        if ($reportFarming && in_array((int) $reportFarming, $farmingIds)) {
            $farmingIds = [(int) $reportFarming];
        }

        // create the default return
        $return = [
            'rows' => [],
            'total' => 0,
            'footer' => [],
        ];

        $reportDateFrom = $reportDateAsOf ? $reportDateAsOf : $reportDateFrom;
        $reportDate = $reportDateAsOf ? $reportDateAsOf : $reportDate;

        if (!$reportDateFrom) {
            $reportDateFrom = date('Y-m-d');
        }
        if (!$reportDate) {
            $reportDate = date('Y-m-d');
        }

        $options = [
            'custom_counter' => 'COUNT(DISTINCT(cpr_id))',
            'sort' => $sort,
            'order' => $order,
            'return' => [
                'DISTINCT (cpr. ID)', 't1.*', 'kvs.gid', 'kvs.virtual_ekatte_name as land', 'kvs.kad_ident',
                'COALESCE(kvs.mestnost, \'-\') as mestnost',
                'COALESCE(kvs.virtual_category_title, \'-\') as category',
                'kvs.virtual_ntp_title as area_type',
                'round(cpr.contract_area::numeric,3) AS area', 'kvs.usable', 'c.c_num',
                "to_char(c.start_date, 'DD.MM.YYYY') AS start_date",
                'COALESCE(c.na_num, \'-\') as na_num',
                'COALESCE(c.tom, \'-\') as tom',
                'COALESCE(c.delo, \'-\') as delo',
                'COALESCE(c.court, \'-\') as court',
                'cpr.price_per_acre', 'cpr.price_sum', 'c.farming_id',
                'c.is_annex', 'cpr.contract_id', ' c.active as active',
                'COALESCE(cpr.kvs_allowable_area, kvs.allowable_area, 0) as kvs_allowable_area',
            ],
            'where' => [
                'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'prefix' => 'c', 'value' => 'false'],
                'c_date' => ['column' => 'c_date', 'compare' => '>=', 'prefix' => 'c', 'value' => $reportContractDate],
                'c_date_to' => ['column' => 'c_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $reportContractDateTo],
                'farming_id' => ['column' => 'farming_id', 'prefix' => 'c', 'compare' => 'IN', 'value' => [...$farmingIds, null]],
            ],
            'start_date' => $reportDateFrom,
            'due_date' => $reportDate,
        ];
        if ($reportExcludeInactive) {
            $options['where']['active'] = ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'true'];
        }
        if ($reportEkate) {
            $options['where']['ekate'] = ['column' => 'ekate', 'prefix' => 'kvs', 'compare' => '=', 'value' => $reportEkate];
        }

        if ($reportMasiv) {
            $options['where']['masiv'] = ['column' => 'masiv', 'prefix' => 'kvs', 'compare' => '=', 'value' => $reportMasiv];
        }
        if ($reportPlotNumber) {
            $options['where']['number'] = ['column' => 'number', 'prefix' => 'kvs', 'compare' => '=', 'value' => $reportPlotNumber];
        }
        if ($reportKadIdent) {
            $options['where']['kad_ident'] = ['column' => 'kad_ident', 'prefix' => 'kvs', 'compare' => '=', 'value' => $reportKadIdent];
        }

        if ($reportNtp) {
            $UserDbAreaTypesController = new UserDbAreaTypesController($this->User->Database);
            $ntpFilter = $UserDbAreaTypesController->getNtpCodesWithAdditionalCodes([$reportNtp]);
            $options['where']['ntp'] = ['column' => 'area_type', 'compare' => 'IN', 'value' => $ntpFilter];
        }
        if ($reportCategory) {
            $categoryFilter = in_array($reportCategory, ['-1', '0', null]) ? ['-1', '0', null] : [$reportCategory];
            $options['where']['category'] = ['column' => 'category', 'compare' => 'IN', 'value' => $categoryFilter];
        }
        if ($reportMestnost) {
            $options['where']['mestnost'] = ['column' => 'mestnost', 'compare' => '=', 'value' => $reportMestnost];
        }

        if ($reportIrrigation) {
            $options['where']['irrigated_area'] = ['column' => 'irrigated_area', 'compare' => '=', 'value' => $reportIrrigation];
        }

        $results_total = $UserDbPlotsController->getOwnPlotsForReport($options, false, false);
        $totalCounter = count($results_total);

        if (0 == $totalCounter) {
            return $return;
        }

        $page = $page ?: 1;
        $rows = $rows ?: 30;

        if (array_key_exists('export_all_rows', $params) && true == $params['export_all_rows']) {
            // export all rows
        } else {
            $options['offset'] = ($page - 1) * $rows;
            $options['limit'] = $rows;
        }

        $results = $UserDbPlotsController->getOwnPlotsForReport($options, false, false);
        $resultsCount = count($results);

        $total_area_for_page = 0;
        $kvsAllowableAreaPerPage = 0;
        $pricePerPage = 0;
        for ($i = 0; $i < $resultsCount; $i++) {
            $results[$i]['farming_id'] = $userFarmings[$results[$i]['farming_id']];
            $results[$i]['kvs_allowable_area'] = null !== $results[$i]['kvs_allowable_area'] ? number_format($results[$i]['kvs_allowable_area'], 3, '.', '') : null;
            $results[$i]['area'] = number_format($results[$i]['area'], 3, '.', '');

            if ($results[$i]['sold_area']) {
                $results[$i]['area'] = number_format($results[$i]['total_area'] - $results[$i]['sold_area'], 3, '.', '');
            }

            $pricePerAcre = $results[$i]['price_per_acre'];

            $results[$i]['price_per_acre'] = $pricePerAcre ? BGNtoEURO($pricePerAcre) : '-';

            $priceSum = $pricePerAcre * $results[$i]['area'];

            $results[$i]['price_sum'] = $pricePerAcre > 0 ? BGNtoEURO($priceSum) : '-';

            $results[$i]['usable'] = $results[$i]['usable'] ? 'Да' : 'Не';

            $total_area_for_page += $results[$i]['area'];
            $kvsAllowableAreaPerPage += $results[$i]['kvs_allowable_area'];
            $pricePerPage += $priceSum;
        }
        $total_area = 0;
        $totalKvsAllowableArea = 0;
        $totalPrice = 0;

        for ($i = 0; $i < $totalCounter; $i++) {
            $soldArea = !empty($results_total[$i]['sold_area']) ? $results_total[$i]['sold_area'] : 0;
            $total_area += (float)number_format($results_total[$i]['area'] - $soldArea, 3, '.', '');
            $totalKvsAllowableArea += (float)number_format($results_total[$i]['kvs_allowable_area'], 3, '.', '');
            $area = number_format($results_total[$i]['area'], 3, '.', '');
            $price_per_acre = number_format($results_total[$i]['price_per_acre'], 2, '.', '');
            $price = $price_per_acre * $area;
            $totalPrice += (float)number_format($price, 2, '.', '');
        }

        $return['rows'] = $results;
        $return['total'] = $totalCounter;
        $return['footer'] = [
            [
                'area_type' => '<b>Общо за стр.</b>',
                'area' => number_format($total_area_for_page, 3, '.', ''),
                'kvs_allowable_area' => number_format($kvsAllowableAreaPerPage, 3, '.', ''),
                'price_sum' => BGNtoEURO($pricePerPage),
            ],
            [
                'area_type' => '<b>Общо</b>',
                'area' => number_format($total_area, 3, '.', ''),
                'kvs_allowable_area' => number_format($totalKvsAllowableArea, 3, '.', ''),
                'price_sum' => BGNtoEURO($totalPrice),
            ],
        ];

        return $return;
    }

    private function formatRowsForExport($rows)
    {
        $results = [];

        $count = count($rows);
        for ($i = 0; $i < $count; $i++) {
            $results[$i]['land'] = $rows[$i]['land'];
            $results[$i]['kad_ident'] = $rows[$i]['kad_ident'];
            $results[$i]['mestnost'] = $rows[$i]['mestnost'];
            $results[$i]['category'] = $rows[$i]['category'];
            $results[$i]['area_type'] = $rows[$i]['area_type'];
            $results[$i]['area'] = $rows[$i]['area'];
            $results[$i]['usable'] = $rows[$i]['usable'];
            $results[$i]['kvs_allowable_area'] = $rows[$i]['kvs_allowable_area'];
            $results[$i]['c_num'] = $rows[$i]['c_num'];
            $results[$i]['farming_id'] = $rows[$i]['farming_id'];
            $results[$i]['start_date'] = $rows[$i]['start_date'];
            $results[$i]['na_num'] = $rows[$i]['na_num'];
            $results[$i]['tom'] = $rows[$i]['tom'];
            $results[$i]['delo'] = $rows[$i]['delo'];
            $results[$i]['court'] = $rows[$i]['court'];
            $results[$i]['price_per_acre'] = $rows[$i]['price_per_acre'];
            $results[$i]['price_sum'] = $rows[$i]['price_sum'];
        }

        return $results;
    }

    private function addTotals(&$rows)
    {
        $i = count($rows);
        $rows[$i]['land'] = '';
        $rows[$i]['kad_ident'] = '';
        $rows[$i]['mestnost'] = '';
        $rows[$i]['category'] = '';
        $rows[$i]['area_type'] = '';
        $rows[$i]['area'] = array_sum(array_column($rows, 'area'));
        $rows[$i]['usable'] = '';
        $rows[$i]['kvs_allowable_area'] = number_format(array_sum(array_column($rows, 'kvs_allowable_area')), 3);
        $rows[$i]['c_num'] = '';
        $rows[$i]['farming_id'] = '';
        $rows[$i]['start_date'] = '';
        $rows[$i]['na_num'] = '';
        $rows[$i]['tom'] = '';
        $rows[$i]['delo'] = '';
        $rows[$i]['court'] = '';
        $rows[$i]['price_per_acre'] = '';
        $rows[$i]['price_sum'] = '';
    }
}
