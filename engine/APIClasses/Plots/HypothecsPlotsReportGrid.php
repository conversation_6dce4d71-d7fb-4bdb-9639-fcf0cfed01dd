<?php

namespace TF\Engine\APIClasses\Plots;

use Prado\Prado;
use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\Export2XlsClass;
use TF\Engine\Plugins\Core\UserDbAreaTypes\UserDbAreaTypesController;
use TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController;
use TF\Engine\Plugins\Core\Users\UsersController;

// Prado::using('Plugins.Core.UserDbPlots.*');
// Prado::using('Plugins.Core.Users.*');
// Prado::using('Plugins.Core.Users.conf');
// Prado::using('Plugins.Core.Farming.*');
// Prado::using('Plugins.Core.Farming.conf');
/**
 * Справка "Ипотеки и тежести".
 *
 * @rpc-module Plots
 *
 * @rpc-service-id hypothecs-plots-report-grid
 */
class HypothecsPlotsReportGrid extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getPlotsHypothecs'],
                'validators' => [
                    'params' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'exportToExcelHypothecsPlotsReportData' => ['method' => [$this, 'exportToExcelHypothecsPlotsReportData'],
                'validators' => [
                    'data' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * Export To Excel Hypothecs Plots Report Data.
     *
     * @param array $data
     *                    [
     *                    #items array filters
     *                    [
     *                    #item  timestamp report_date
     *                    #item  timestamp report_date_from
     *                    #item  timestamp report_date_as_of
     *                    #item  timestamp report_contract_date
     *                    #item  timestamp report_contract_date_to
     *                    #item  integer report_farming
     *                    #item  integer report_ekate
     *                    #item  integer report_ntp
     *                    #item  integer report_category
     *                    #item  string report_mestnost
     *                    #item  string report_irrigation
     *                    ]
     *                    ]
     * @param int|string $page
     * @param int|string $rows
     * @param ?string $sort
     * @param ?string $order
     *
     * @return array
     */
    public function exportToExcelHypothecsPlotsReportData(array $data, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        if (!$page && !$rows) {
            $data['export_all_rows'] = true;
        }

        $results = $this->getPlotsHypothecs($data, $page, $rows, $sort, $order);

        $result = $results['rows'];

        unset($result[0]['attributes']);

        $result = $this->formatRowsForExport($result);

        $columns = [
            'Землище',
            'Идентификатор',
            'Местност',
            'Категория',
            'НТП',
            'Площ(дка)',
            'Ипотека №',
            'Ипотекирана площ (дка)',
            'Влиза в сила',
            'Падеж',
            'Кредитор',
        ];

        $this->addTotals($result);

        $time = strtotime(date('Y-m-d H:i:s'));
        $fileName = 'ipoteki_i_tejesti_' . $this->User->GroupID . '_' . $time . '.xlsx';
        $path = PUBLIC_UPLOAD_EXPORT . '/' . $fileName;

        $export2Xls = new Export2XlsClass();
        $filePath = $export2Xls->exportUrlPath($path, $result, $columns);

        return ['file_path' => $filePath, 'file_name' => $fileName];
    }

    /**
     * getPlotsHypothecs.
     *
     * @api-method read
     *
     * @param array $params
     *                      [
     *                      #items array filters
     *                      [
     *                      #item  timestamp report_date
     *                      #item  timestamp report_date_from
     *                      #item  timestamp report_date_as_of
     *                      #item  timestamp report_contract_date
     *                      #item  timestamp report_contract_date_to
     *                      #item  integer report_farming
     *                      #item  integer report_ekate
     *                      #item  integer report_ntp
     *                      #item  integer report_category
     *                      #item  string report_mestnost
     *                      #item  string report_irrigation
     *                      ]
     *                      ]
     * @param int|string $page
     * @param int|string $rows
     * @param ?string $sort
     * @param ?string $order
     *
     * @return array
     */
    public function getPlotsHypothecs(array $params, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $reportDateFrom = $params['filters']['report_date_from'];
        $reportDateTo = $params['filters']['report_date'];
        $reportDateAsOf = $params['filters']['report_date_as_of'];
        $reportContractDate = $params['filters']['report_contract_date'];
        $reportContractDateTo = $params['filters']['report_contract_date_to'];
        $reportFarming = $params['filters']['report_farming'];
        $reportEkate = $params['filters']['report_ekate'];
        $reportMasiv = $params['filters']['report_masiv'];
        $reportPlotNumber = $params['filters']['report_plot_number'];
        $reportKadIdent = $params['filters']['report_kad_ident'];
        $reportNtp = $params['filters']['report_ntp'];
        $reportCategory = $params['filters']['report_category'];
        $reportMestnost = $params['filters']['report_mestnost'];
        $reportIrrigation = null;
        if ('all' != $params['filters']['report_irrigation']) {
            $reportIrrigation = $params['filters']['report_irrigation'];
        }

        $UsersController = new UsersController('Users');
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);

        if ($reportDateAsOf) {
            $reportDateTo = $reportDateAsOf;
            $reportDateFrom = $reportDateAsOf;
        }

        // create the default return
        $return = [
            'rows' => [],
            'total' => 0,
            'footer' => [],
        ];

        $options = [
            'custom_counter' => 'COUNT(DISTINCT(h.id))',
            'sort' => $sort,
            'order' => $order,
            'return' => [
                'DISTINCT(gid)', 'virtual_ekatte_name as land', 'kad_ident',
                'COALESCE(mestnost, \'-\') as mestnost',
                'COALESCE(virtual_category_title, \'-\') as category', 'virtual_ntp_title as area_type',
                'ROUND((CASE WHEN document_area IS NULL THEN ST_Area(geom)/1000 ELSE document_area END)::numeric , 3) as area',
                'h.id as h_id', 'num', "to_char(h.start_date,'DD.MM.YYYY') as start_date", "to_char(h.due_date,'DD.MM.YYYY') as due_date", 'c.name as creditor',
                'ROUND(ph.hypothec_area::numeric, 3) as hypothec_area', 'h.is_active',
            ],
            'where' => [
                'h_start_date' => ['column' => 'start_date', 'compare' => '<=', 'prefix' => 'h', 'value' => $reportDateTo],
                'h_due_date' => ['column' => 'due_date', 'compare' => '>=', 'prefix' => 'h', 'value' => $reportDateFrom],
                'h_date' => ['column' => 'date', 'compare' => '>=', 'prefix' => 'h', 'value' => $reportContractDate],
                'h_date_to' => ['column' => 'date', 'compare' => '<=', 'prefix' => 'h', 'value' => $reportContractDateTo],
            ],
        ];

        if ($reportFarming) {
            $options['where']['farming_id'] = ['column' => 'farming_id', 'compare' => '=', 'prefix' => 'h', 'value' => $reportFarming];
        }
        if ($reportEkate) {
            $options['where']['ekate'] = ['column' => 'ekate', 'compare' => '=', 'prefix' => 'kvs', 'value' => $reportEkate];
        }
        if ($reportNtp) {
            $UserDbAreaTypesController = new UserDbAreaTypesController($this->User->Database);
            $ntpFilter = $UserDbAreaTypesController->getNtpCodesWithAdditionalCodes([$reportNtp]);
            $options['where']['ntp'] = ['column' => 'area_type', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $ntpFilter];
        }
        if ($reportCategory) {
            $categoryFilter = in_array($reportCategory, ['-1', '0', null]) ? ['-1', '0', null] : [$reportCategory];
            $options['where']['category'] = ['column' => 'category', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $categoryFilter];
        }
        if ($reportMestnost) {
            $options['where']['mestnost'] = ['column' => 'mestnost', 'compare' => '=', 'prefix' => 'kvs', 'value' => $reportMestnost];
        }
        if ($reportIrrigation) {
            $options['where']['irrigated_area'] = ['column' => 'irrigated_area', 'compare' => '=', 'prefix' => 'kvs', 'value' => $reportIrrigation];
        }
        if ($reportMasiv) {
            $options['where']['masiv'] = ['column' => 'masiv', 'prefix' => 'kvs', 'compare' => '=', 'value' => $reportMasiv];
        }
        if ($reportPlotNumber) {
            $options['where']['number'] = ['column' => 'number', 'prefix' => 'kvs', 'compare' => '=', 'value' => $reportPlotNumber];
        }
        if ($reportKadIdent) {
            $options['where']['kad_ident'] = ['column' => 'kad_ident', 'prefix' => 'kvs', 'compare' => '=', 'value' => $reportKadIdent];
        }

        $results_total = $UserDbPlotsController->getHypothecsPlotsReport($options, false, false);

        $totalCounter = count($results_total);

        if (0 == $totalCounter) {
            return $return;
        }

        $page = $page ?: 1;
        $rows = $rows ?: 30;

        if (array_key_exists('export_all_rows', $params) && true == $params['export_all_rows']) {
            // export all rows
        } else {
            $options['offset'] = ($page - 1) * $rows;
            $options['limit'] = $rows;
        }

        $results = $UserDbPlotsController->getHypothecsPlotsReport($options, false, false);
        $resultsCount = count($results);
        $total_area_for_page = 0;
        $total_hypothec_area_for_page = 0;
        for ($i = 0; $i < $resultsCount; $i++) {
            $total_area_for_page += $results[$i]['area'];
            $total_hypothec_area_for_page += $results[$i]['hypothec_area'];
        }
        $total_area = 0;
        $total_hypothec_area = 0;
        for ($i = 0; $i < $totalCounter; $i++) {
            $total_area += $results_total[$i]['area'];
            $total_hypothec_area += $results_total[$i]['hypothec_area'];
        }

        $return['rows'] = $results;
        $return['total'] = $totalCounter;
        $return['footer'] = [
            [
                'area_type' => '<b>Общо за стр.</b>',
                'area' => number_format($total_area_for_page, 3),
                'hypothec_area' => number_format($total_hypothec_area_for_page, 3),
            ],
            [
                'area_type' => '<b>Общо</b>',
                'area' => number_format($total_area, 3),
                'hypothec_area' => number_format($total_hypothec_area, 3),
            ],
        ];

        return $return;
    }

    private function formatRowsForExport($rows)
    {
        $results = [];

        $count = count($rows);
        for ($i = 0; $i < $count; $i++) {
            $results[$i]['land'] = $rows[$i]['land'];
            $results[$i]['kad_ident'] = $rows[$i]['kad_ident'];
            $results[$i]['mestnost'] = $rows[$i]['mestnost'];
            $results[$i]['category'] = $rows[$i]['category'];
            $results[$i]['area_type'] = $rows[$i]['area_type'];
            $results[$i]['area'] = $rows[$i]['area'];
            $results[$i]['num'] = $rows[$i]['num'];
            $results[$i]['hypothec_area'] = $rows[$i]['hypothec_area'];
            $results[$i]['start_date'] = $rows[$i]['start_date'];
            $results[$i]['due_date'] = $rows[$i]['due_date'];
            $results[$i]['creditor'] = $rows[$i]['creditor'];
        }

        return $results;
    }

    private function addTotals(&$rows)
    {
        $i = count($rows);
        $rows[$i]['land'] = '';
        $rows[$i]['kad_ident'] = '';
        $rows[$i]['mestnost'] = '';
        $rows[$i]['category'] = '';
        $rows[$i]['area_type'] = '';
        $rows[$i]['area'] = number_format(array_sum(array_column($rows, 'area')), 3);
        $rows[$i]['num'] = '';
        $rows[$i]['hypothec_area'] = number_format(array_sum(array_column($rows, 'hypothec_area')), 3);
        $rows[$i]['start_date'] = '';
        $rows[$i]['due_date'] = '';
        $rows[$i]['creditor'] = '';
    }
}
