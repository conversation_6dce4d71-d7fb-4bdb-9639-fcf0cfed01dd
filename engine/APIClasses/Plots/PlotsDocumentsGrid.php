<?php

namespace TF\Engine\APIClasses\Plots;

use Prado\Web\Services\TRpcApiProvider;
use Prado\Web\Services\TRpcException;
use TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController;

/**
 * Get information about official documents associated with the plots.
 *
 * @rpc-module Plots
 *
 * @rpc-service-id plots-documents-datagrid
 */
class PlotsDocumentsGrid extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readDocumentsDatagrid'],
                'validators' => [
                    'rpcParams' => [
                        'kad_ident' => 'validateRequired, validateKadIdent',
                        'year' => 'validateStrictInteger',
                    ],
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * Gets all documents, associated with current plot.
     *
     * @api-method read
     *
     * @param array|int $rpcParams - array containing kad_ident and year
     * @param int|string $page - pagination parameters
     * @param int|string $rows - pagination parameters
     * @param string $sort - pagination parameters
     * @param string $order - pagination parameters
     *
     * @throws TRpcException
     *
     * @return array - an array of matching results
     */
    public function readDocumentsDatagrid(array $rpcParams, int $page = null, int $rows = null, string $sort = null, string $order = null)
    {
        $userDbPlotsController = new UserDbPlotsController($this->User->Database);

        $kadIdent = $rpcParams['kad_ident'];
        $year = $rpcParams['year'];

        return $userDbPlotsController->getPlotDocuments($kadIdent, $year, $page, $rows, $sort, $order);
    }
}
