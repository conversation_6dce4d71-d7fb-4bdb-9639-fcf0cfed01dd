<?php

namespace TF\Engine\APIClasses\Plots;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDbAreaTypes\UserDbAreaTypesController;
use TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController;

/**
 * Грид "Имоти за декларация".
 *
 * @rpc-module Plots
 *
 * @rpc-service-id declaration-choose-grid
 */
class DeclarationChooseGrid extends TRpcApiProvider
{
    /**
     * All public methods.
     */
    public function registerMethods()
    {
        return [
            'decl69' => ['method' => [$this, 'loadDecl69'],
                'validators' => [
                    'rpcParams' => [
                        'year' => 'validateFarmingYear, validateRequired, validateNotNull',
                        'farming' => 'validateInteger, validateRequired, validateNotNull',
                        'ekate' => 'validateText, validateRequired, validateNotNull',
                        'c_type' => 'validateContractType',
                    ],
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ]],
            'decl70' => ['method' => [$this, 'loadDecl70'],
                'validators' => [
                    'rpcParams' => [
                        'year' => 'validateFarmingYear, validateRequired, validateNotNull',
                        'farming' => 'validateInteger, validateRequired, validateNotNull',
                        'ekate' => 'validateText, validateRequired, validateNotNull',
                        'c_type' => 'validateContractType',
                    ],
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ]],
            'declPML' => ['method' => [$this, 'loadDeclPML'],
                'validators' => [
                    'rpcParams' => [
                        'year' => 'validateFarmingYear, validateRequired, validateNotNull',
                        'farming' => 'validateInteger, validateRequired, validateNotNull',
                    ],
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ]],
            'anketnaKarta' => ['method' => [$this, 'loadAnketnaKarta'],
                'validators' => [
                    'rpcParams' => [
                        'year' => 'validateFarmingYear, validateRequired, validateNotNull',
                        'farming' => 'validateInteger, validateRequired, validateNotNull',
                        'ekate' => 'validateText, validateRequired, validateNotNull',
                    ],
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ]],
        ];
    }

    /**
     * Loads information for 'Декларация №69'.
     *
     * @api-method decl69
     *
     * @param array $rpcParams
     *                         {
     *                         #item string ekate
     *                         #item int farming
     *                         #item string masiv
     *                         #item string imot
     *                         #item int c_type
     *                         #item string ntp
     *                         #item string from_date - c_date filter parameter
     *                         #item string to_date   - c_date filter parameter
     *                         }
     * @param int $page pagination rpc parameter
     * @param int $rows pagination rpc parameter
     * @param string $sort pagination rpc parameter
     * @param string $order pagination rpc parameter
     *
     * @return array
     */
    public function loadDecl69(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        if ($this->User->isGuest) {
            return [];
        }
        $return = [
            'rows' => [],
            'total' => 0,
            'footer' => [],
        ];

        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);
        $UserDbAreaTypesController = new UserDbAreaTypesController($this->User->Database);

        $year = $GLOBALS['Farming']['years'][$rpcParams['year']]['year'];

        $farming_start_date = ($year - 1) . '-10-01';
        $farming_due_date = $year . '-09-30';

        $ntpFilter = [];
        if (!empty($rpcParams['ntp'])) {
            $ntpFilter = $UserDbAreaTypesController->getNtpCodesWithAdditionalCodes($rpcParams['ntp']);
        }

        $options = [
            'return' => [
                'gid',
                'kad_ident',
                'virtual_ntp_title as area_type',
                'include',
                'participate',
                'white_spots',
                'round((sum(coalesce(pc.contract_area, 0)) - sum(coalesce(sscpr.contract_area_for_sale, 0)))::numeric, 3) as area',
                'ekate',
                'virtual_ekatte_name as ekatte_name',
                'masiv',
                'number',
                'c.c_num',
                'TO_CHAR(c.c_date, \'DD.MM.YYYY\') as c_date',
                "concat('sub_', spcr.id) as sub_pc_rel_id",
                'pc.id::text as pc_rel_id',
                'c.id',
                'c.nm_usage_rights',
                'c.virtual_contract_type as c_type',
                'count(*) OVER () as total_count',
                "string_agg(scs.c_num::varchar, ',') as subleases_c_nums",
            ],
            'where' => [
                'ekate' => ['column' => 'ekate', 'compare' => '=', 'prefix' => 'kvs', 'value' => $rpcParams['ekate']],
                'farming' => ['column' => 'farming_id', 'compare' => '=', 'prefix' => 'c', 'value' => $rpcParams['farming']],
                'active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'TRUE'],
                'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
                'is_annex' => ['column' => 'is_annex', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
                'annex_action' => ['column' => 'annex_action', 'compare' => '=', 'prefix' => 'pc', 'value' => 'added'],
                // filters
                'masiv' => ['column' => 'masiv', 'compare' => '=', 'prefix' => 'kvs', 'value' => $rpcParams['masiv']],
                'imot' => ['column' => 'number', 'compare' => '=', 'prefix' => 'kvs', 'value' => $rpcParams['imot']],
                'c_type' => ['column' => 'nm_usage_rights', 'compare' => '=', 'prefix' => 'c', 'value' => $rpcParams['c_type']],
                'c_type_not_agg' => ['column' => 'nm_usage_rights', 'compare' => '!=', 'prefix' => 'c', 'value' => 4],
                'area_type' => ['column' => 'area_type', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $ntpFilter],
                'c_date_from' => ['column' => 'c_date', 'compare' => '>=', 'prefix' => 'c', 'value' => $rpcParams['from_date']],
                'c_date_to' => ['column' => 'c_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $rpcParams['to_date']],
                'cs_date_from' => ['column' => 'start_date', 'compare' => '<=', 'prefix' => 'scs', 'value' => $farming_due_date],
                'cs_date_to' => ['column' => 'due_date', 'compare' => '>=', 'prefix' => 'scs', 'value' => $farming_start_date],
                'archited_plot' => ['column' => '(kvs.is_edited = false or kvs.edit_active_from > \'' . $farming_due_date . '\')', 'compare' => '=', 'value' => 'true'],
            ],
            'joins' => [
                'left join su_contracts as scs on ( scs.id = spcr.sublease_id)',
                "left join su_subleases_plots_area as sspa on ( sspa.sublease_id = scs.id and sspa.plot_id = pc.plot_id and scs.active = true and scs.start_date <= '" . $farming_due_date . "' and scs.due_date >= '" . $farming_start_date . "' )",
                'left join su_sales_contracts_plots_rel sscprp on (sscprp.pc_rel_id = pc.id)',
                "left join su_sales_contracts as ssc on (ssc.id = sscprp.sales_contract_id and ssc.active = true and ssc.start_date <= '" . $farming_start_date . "')",
                'left join su_sales_contracts_plots_rel sscpr on (sscpr.pc_rel_id = pc.id and sscpr.sales_contract_id = ssc.id)',
            ],
            'group' => 'gid, pc.id, spcr.id, c.id',
            'having' => 'having sum(coalesce(sspa.contract_area, 0))::numeric(9, 3) > 0',
            'custom_counter' => 'COUNT(DISTINCT(pc.id))',
            'limit' => $rows,
            'offset' => ($page - 1) * $rows,
            'farming_start_date' => $farming_start_date,
            'farming_due_date' => $farming_due_date,
            'include_subleased_contracts' => true,
        ];

        if ('-1' == $options['where']['area_type']['value']) {
            $options['where']['area_type'] = ['column' => 'area_type', 'compare' => 'is', 'prefix' => 'kvs', 'value' => 'NULL'];
        }

        $subRels = [];
        $rels = [];
        if (!empty($_SESSION['decl_array'])) {
            foreach ($_SESSION['decl_array'] as $rel) {
                if (false !== strpos($rel, 'sub_')) {
                    $subRels[] = (int)str_replace('sub_', '', $rel);
                } else {
                    $rels[] = (int)$rel;
                }
            }
            if (!empty($subRels)) {
                $options['sub_pc_rel_anti_id_string'] = implode(', ', $subRels);
            }
        }

        $options['union']['return'] = [
            'gid', 'kad_ident', 'virtual_ntp_title as area_type', 'include', 'participate', 'white_spots',
            'round((pc.contract_area - sum(coalesce(sspa.contract_area, 0)) - sum(coalesce(sscpr.contract_area_for_sale, 0)))::numeric, 3) as area',
            'ekate', 'virtual_ekatte_name as ekatte_name', 'masiv', 'number',
            'c.c_num', 'TO_CHAR(c.c_date, \'DD.MM.YYYY\') as c_date',
            "concat('sub_', spcr.id) as sub_pc_rel_id",
            'pc.id::text as pc_rel_id',
            'c.id', 'c.nm_usage_rights', 'c.virtual_contract_type as c_type',
            'count(*) OVER () as total_count',
            'null as subleases_c_nums',
        ];
        $options['union']['having'] = 'having sum(coalesce(sspa.contract_area, 0))::numeric(9, 3) + sum(coalesce(sscpr.contract_area_for_sale, 0))::numeric(9, 3) < pc.contract_area::numeric(9, 3)';
        $options['union']['group'] = 'gid, pc.id, spcr.id, c.id';
        $options['union']['pc_rel_anti_id_string'] = implode(', ', $rels);

        switch ($sort) {
            case 'kad_ident':
                $options['sort'] = 'kad_ident COLLATE "alpha_numeric_bg"';
                $options['order'] = $order;

                break;
            case 'area':
                $options['sort'] = 'area';
                $options['order'] = $order;

                break;
            case 'area_type':
                $options['sort'] = 'area_type COLLATE "alpha_numeric_bg"';
                $options['order'] = $order;

                break;
            case 'c_num':
                $options['sort'] = 'c_num';
                $options['order'] = $order;

                break;
            case 'c_type':
                $options['sort'] = 'c_type COLLATE "alpha_numeric_bg"';
                $options['order'] = $order;

                break;
            default:
                break;
        }

        $results = $UserDbPlotsController->getFullPlotDataForDeclaration($options, false, false);

        if (0 === count($results)) {
            return $return;
        }

        // convert results for datagrid
        $total_area = 0;
        foreach ($results as &$result) {
            if (!empty($result['subleases_c_nums'])) {
                $result['subleases'] = $result['subleases_c_nums'];
                $result['is_subleased'] = true;
                $result['pc_rel_id'] = $result['sub_pc_rel_id'];
            } else {
                $result['subleases'] = '-';
                $result['is_subleased'] = false;
            }

            $total_area += $result['area'];
        }
        unset($result);
        $return = [
            'rows' => $results,
            'total' => $results[0]['total_count'],
            'footer' => [
                [
                    'kad_ident' => '<b>Общо за стр.</b>',
                    'area' => number_format($total_area, 3, '.', ''),
                ],
            ],
        ];

        return $return;
    }

    /**
     * Loads information for 'Декларация №70'.
     *
     * @api-method decl70
     *
     * @param array $rpcParams
     *                         {
     *                         #item string ekate
     *                         #item int farming
     *                         #item string masiv
     *                         #item string imot
     *                         #item int c_type
     *                         #item int year
     *                         #item string ntp
     *                         #item string from_date - c_date filter parameter
     *                         #item string to_date   - c_date filter parameter
     *                         }
     * @param int $page pagination rpc parameter
     * @param int $rows pagination rpc parameter
     * @param string $sort pagination rpc parameter
     * @param string $order pagination rpc parameter
     *
     * @return array
     */
    public function loadDecl70(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        if ($this->User->isGuest) {
            return [];
        }

        $return = [
            'rows' => [],
            'total' => 0,
            'footer' => [],
        ];

        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);
        $UserDbAreaTypesController = new UserDbAreaTypesController($this->User->Database);

        $year = $GLOBALS['Farming']['years'][$rpcParams['year']]['year'];

        $farming_start_date = ($year - 1) . '-10-01';
        $farming_due_date = $year . '-09-30';

        $ntpFilter = [];
        if (!empty($rpcParams['ntp'])) {
            $ntpFilter = $UserDbAreaTypesController->getNtpCodesWithAdditionalCodes($rpcParams['ntp']);
        }

        // options for plots-contracts query
        $options = [
            'return' => [
                'gid',
                'kad_ident',
                'virtual_ntp_title as area_type',
                'include',
                'participate',
                'white_spots',
                'round(sum(pc.contract_area)::numeric, 3) as plots_contracts_area',
                'round((sum(coalesce(pc.contract_area, 0)) - sum(coalesce(sscpr.contract_area_for_sale, 0)))::numeric, 3) as area',
                'coalesce(sspa.contract_area, 0) as subleased_area',
                'ekate',
                'virtual_ekatte_name as ekatte_name',
                'masiv',
                'number',
                'string_agg(c.c_num, \',\') as c_num',
                'string_agg(concat(\'sub_\', spcr.id), \',\') as sub_pc_rel_id',
                'null as pc_rel_id',
                'string_agg(c.nm_usage_rights::text, \',\') as nm_usage_rights',
                'string_agg(c.virtual_contract_type::text, \',\') as c_type',
                'scs.c_num::varchar as subleases_c_nums',
            ],
            'where' => [
                'ekate' => ['column' => 'ekate', 'compare' => '=', 'prefix' => 'kvs', 'value' => $rpcParams['ekate']],
                'farming' => ['column' => 'farming_id', 'compare' => '=', 'prefix' => 'c', 'value' => $rpcParams['farming']],
                'active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'TRUE'],
                'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
                'is_annex' => ['column' => 'is_annex', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
                'annex_action' => ['column' => 'annex_action', 'compare' => '=', 'prefix' => 'pc', 'value' => 'added'],
                // filters
                'masiv' => ['column' => 'masiv', 'compare' => '=', 'prefix' => 'kvs', 'value' => $rpcParams['masiv']],
                'imot' => ['column' => 'number', 'compare' => '=', 'prefix' => 'kvs', 'value' => $rpcParams['imot']],
                'c_type' => ['column' => 'nm_usage_rights', 'compare' => '=', 'prefix' => 'c', 'value' => $rpcParams['c_type']],
                'c_type_not_agg' => ['column' => 'nm_usage_rights', 'compare' => '!=', 'prefix' => 'c', 'value' => 4],
                'area_type' => ['column' => 'area_type', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $ntpFilter],
                'c_date_from' => ['column' => 'c_date', 'compare' => '>=', 'prefix' => 'c', 'value' => $rpcParams['from_date']],
                'c_date_to' => ['column' => 'c_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $rpcParams['to_date']],
                'archited_plot' => ['column' => '(kvs.is_edited = false or kvs.edit_active_from > \'' . $farming_due_date . '\')', 'compare' => '=', 'value' => 'true'],
            ],
            'joins' => [
                'scs' => "inner join su_contracts as scs on ( scs.id = spcr.sublease_id and scs.start_date <= '" . $farming_due_date . "' and scs.due_date >= '" . $farming_start_date . "')",
                'sspa' => "left join su_subleases_plots_area as sspa on ( sspa.sublease_id = scs.id and sspa.plot_id = pc.plot_id and scs.active = true and scs.start_date <= '" . $farming_due_date . "' and scs.due_date >= '" . $farming_start_date . "' )",
                'sscprp' => 'left join su_sales_contracts_plots_rel sscprp on (sscprp.pc_rel_id = pc.id)',
                'ssc' => "left join su_sales_contracts as ssc on (ssc.id = sscprp.sales_contract_id and ssc.active = true and ssc.start_date <= '" . $farming_start_date . "')",
                'sscpr' => 'left join su_sales_contracts_plots_rel sscpr on (sscpr.pc_rel_id = pc.id and sscpr.sales_contract_id = ssc.id)',
            ],
            'group' => 'gid, scs.id, sspa.id, pc.id',
            'having' => 'having round((coalesce(sspa.contract_area, 0) - sum(coalesce(sscpr.contract_area_for_sale, 0)))::numeric, 3) > 0',
            'custom_counter' => 'COUNT(DISTINCT(pc.id))',
            'limit' => $rows,
            'offset' => ($page - 1) * $rows,
            'farming_start_date' => $farming_start_date,
            'farming_due_date' => $farming_due_date,
            'include_subleased_contracts' => true,
            'today' => strftime('%Y-%m-%d', time()),
        ];

        if ('-1' == $options['where']['area_type']['value']) {
            $options['where']['area_type'] = ['column' => 'area_type', 'compare' => 'is', 'prefix' => 'kvs', 'value' => 'NULL'];
        }

        $subRels = [];
        $rels = [];
        if (!empty($_SESSION['decl_array'])) {
            foreach ($_SESSION['decl_array'] as $rel) {
                if (false !== strpos($rel, 'sub_')) {
                    $subRelArr = explode(',', $rel);
                    foreach ($subRelArr as $subRel) {
                        $subRels[] = (int)str_replace('sub_', '', $subRel);
                    }
                } else {
                    $rels[] = (int)$rel;
                }
            }
            if (!empty($subRels)) {
                $options['sub_pc_rel_anti_id_string'] = implode(', ', $subRels);
            }
        }

        $options['union']['return'] = [
            'gid',
            'kad_ident',
            'virtual_ntp_title as area_type',
            'include',
            'participate',
            'white_spots',
            'ROUND((pc.contract_area - sum(coalesce(sscpr.contract_area_for_sale, 0)))::numeric, 3) as plots_contracts_area',
            'round((pc.contract_area - sum(coalesce(sspa.contract_area, 0)) - sum(coalesce(sscpr.contract_area_for_sale, 0)))::numeric, 3) as area',
            'round(((pc.contract_area - sum(coalesce(sscpr.contract_area_for_sale, 0))) - sum(coalesce(sspa.contract_area, 0)))::numeric, 3) as subleased_area',
            'ekate',
            'virtual_ekatte_name as ekatte_name',
            'masiv',
            'number',
            'c.c_num',
            'null as sub_pc_rel_id',
            'pc.id::text as pc_rel_id',
            'c.nm_usage_rights::text',
            'c.virtual_contract_type::text',
            'null as subleases_c_nums',
        ];

        $options['union']['joins']['scs'] = "left join su_contracts as scs on ( scs.id = spcr.sublease_id and scs.start_date <= '" . $farming_due_date . "' and scs.due_date >= '" . $farming_start_date . "')";
        $options['union']['having'] = 'having sum(coalesce(sspa.contract_area, 0))::numeric(9, 3) + sum(coalesce(sscpr.contract_area_for_sale, 0))::numeric(9, 3) < pc.contract_area::numeric(9, 3)';
        $options['union']['group'] = 'gid, pc.id, c.id, scs.due_date, scs.start_date';
        $options['union']['pc_rel_anti_id_string'] = implode(', ', $rels);

        switch ($sort) {
            case 'kad_ident':
                $options['sort'] = 'kad_ident COLLATE "alpha_numeric_bg"';
                $options['order'] = $order;

                break;
            case 'plots_contracts_area':
                $options['sort'] = 'plots_contracts_area';
                $options['order'] = $order;

                break;
            case 'subleased_area':
                $options['sort'] = 'subleased_area';
                $options['order'] = $order;

                break;
            case 'area_type':
                $options['sort'] = 'area_type COLLATE "alpha_numeric_bg"';
                $options['order'] = $order;

                break;
            case 'c_num':
                $options['sort'] = 'c_num COLLATE "alpha_numeric_bg"';
                $options['order'] = $order;

                break;
            case 'c_type':
                $options['sort'] = 'nm_usage_rights';
                $options['order'] = $order;

                break;
            default:
                break;
        }

        $results = $UserDbPlotsController->getFullPlotDataForDeclaration($options, false, false);

        if (0 === count($results)) {
            return $return;
        }

        // convert results for datagrid
        $total_area = 0;
        $total_subleased_area = 0;
        foreach ($results as &$result) {
            if (null !== $result['subleases_c_nums']) {
                $result['subleases'] = $result['subleases_c_nums'];
                $result['is_subleased'] = true;
                $result['pc_rel_id'] = $result['sub_pc_rel_id'];
            } else {
                $result['subleases'] = '-';
                $result['is_subleased'] = false;
            }

            $result['c_type'] = explode(',', $result['c_type']);

            $total_area += $result['plots_contracts_area'];
            $total_subleased_area += $result['subleased_area'];
        }
        unset($result);

        return [
            'rows' => $results,
            'total' => $results[0]['total_count'],
            'footer' => [
                [
                    'kad_ident' => '<b>Общо за стр.</b>',
                    'plots_contracts_area' => number_format($total_area, 3, '.', ''),
                    'subleased_area' => number_format($total_subleased_area, 3, '.', ''),
                ],
            ],
        ];
    }

    /**
     * Loads information for 'Декларация ПМЛ'.
     *
     * @api-method declPML
     *
     * @param array $rpcParams
     *                         {
     *                         #item int farming
     *                         #item string masiv
     *                         #item string imot
     *                         #item int c_type
     *                         #item int year
     *                         #item string ntp
     *                         #item string from_date - c_date filter parameter
     *                         #item string to_date   - c_date filter parameter
     *                         }
     * @param int $page pagination rpc parameter
     * @param int $rows pagination rpc parameter
     * @param string $sort pagination rpc parameter
     * @param string $order pagination rpc parameter
     *
     * @return array
     */
    public function loadDeclPML(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        if ($this->User->isGuest) {
            return [];
        }

        $return = [
            'rows' => [],
            'total' => 0,
            'footer' => [],
        ];

        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);
        $UserDbAreaTypesController = new UserDbAreaTypesController($this->User->Database);
        $year = $GLOBALS['Farming']['years'][$rpcParams['year']]['year'];

        $export_type = $rpcParams['export_type'];

        $farming_start_date = ($year - 1) . '-10-01';
        $farming_due_date = $year . '-09-30';

        $ntpFilter = [];

        if (!empty($rpcParams['ntp'])) {
            $ntpFilter = $UserDbAreaTypesController->getNtpCodesWithAdditionalCodes($rpcParams['ntp']);
        }

        // options for plots-contracts query
        $options = [
            'return' => [
                'gid', 'kad_ident', 'virtual_ntp_title as area_type', 'include', 'participate', 'white_spots',
                'ROUND(contract_area::numeric, 3) as area', 'ekate', 'virtual_ekatte_name as ekatte_name', 'masiv', 'number',
                'c.c_num', 'TO_CHAR(c.c_date, \'DD.MM.YYYY\') as c_date', 'pc.id as pc_rel_id', 'c.id', 'c.nm_usage_rights', 'c.virtual_contract_type as c_type',
                'count(*) OVER () as total_count',
            ],
            'where' => [
                'farming' => ['column' => 'farming_id', 'compare' => '=', 'prefix' => 'c', 'value' => $rpcParams['farming']],
                'active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'TRUE'],
                'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
                'is_annex' => ['column' => 'is_annex', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
                'annex_action' => ['column' => 'annex_action', 'compare' => '=', 'prefix' => 'pc', 'value' => 'added'],
                // filters
                'masiv' => ['column' => 'masiv', 'compare' => '=', 'prefix' => 'kvs', 'value' => $rpcParams['masiv']],
                'imot' => ['column' => 'number', 'compare' => '=', 'prefix' => 'kvs', 'value' => $rpcParams['imot']],
                'area_type' => ['column' => 'area_type', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $ntpFilter],
                'c_date_from' => ['column' => 'c_date', 'compare' => '>=', 'prefix' => 'c', 'value' => $rpcParams['from_date']],
                'c_date_to' => ['column' => 'c_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $rpcParams['to_date']],
                'archited_plot' => ['column' => '(kvs.is_edited = false or kvs.edit_active_from > \'' . $farming_due_date . '\')', 'compare' => '=', 'value' => 'true'],
            ],
            'group' => 'gid, pc.id, c.id',
            'custom_counter' => 'COUNT(DISTINCT(pc.id))',
            'limit' => $rows,
            'offset' => ($page - 1) * $rows,
            'farming_start_date' => $farming_start_date,
            'farming_due_date' => $farming_due_date,
            'include_subleased_contracts' => true,
        ];

        if ('-1' == $options['where']['area_type']['value']) {
            $options['where']['area_type'] = ['column' => 'area_type', 'compare' => 'is', 'prefix' => 'kvs', 'value' => 'NULL'];
        }

        if ($_SESSION['decl_loaded'] && count($_SESSION['decl_array'])) {
            $options['where']['pc_rel_anti_id_string'] = ['column' => 'id', 'compare' => 'NOT IN', 'prefix' => 'pc', 'value' => array_values($_SESSION['decl_array'])];
        }

        switch ($sort) {
            case 'kad_ident':
                $options['sort'] = 'kad_ident COLLATE "alpha_numeric_bg"';
                $options['order'] = $order;

                break;
            case 'area':
                $options['sort'] = 'area';
                $options['order'] = $order;

                break;
            case 'area_type':
                $options['sort'] = 'area_type COLLATE "alpha_numeric_bg"';
                $options['order'] = $order;

                break;
            case 'c_num':
                $options['sort'] = 'c_num COLLATE "alpha_numeric_bg"';
                $options['order'] = $order;

                break;
            case 'c_type':
                $options['sort'] = 'nm_usage_rights';
                $options['order'] = $order;

                break;
            default:
                break;
        }

        $results = $UserDbPlotsController->getFullPlotDataForDeclaration($options, false, false);
        $counter[0]['count'] = $results[0]['total_count'];

        if (0 == $counter[0]['count']) {
            return $return;
        }

        // get all subleased plot GIDs for

        // array holds all plot GIDs from own contracts
        $plots_id_array = [];
        for ($i = 0; $i < count($results); $i++) {
            // create array with GIDs for sublease query
            $plots_id_array[] = $results[$i]['gid'];
        }

        // options for subleased query
        $options = [
            'return' => [
                'DISTINCT(gid)', 'kad_ident', 'virtual_ntp_title as area_type', 'include', 'participate', 'white_spots',
                '(CASE WHEN (SUM(St_Area(geom) * po.percent / 100)) IS NULL THEN ROUND(SUM(St_Area(geom))::numeric, 3) ELSE ROUND((SUM(St_Area(geom) * po.percent / 100))::numeric ,3) END) as area',
                'c_num', 'TO_CHAR(c.c_date, \'DD.MM.YYYY\') as c_date', 'pc.id as pc_rel_id', 'c.id',
            ],
            'where' => [
                'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'prefix' => 'c', 'value' => 'TRUE'],
                'ekate' => ['column' => 'ekate', 'compare' => '=', 'prefix' => 'kvs', 'value' => $rpcParams['ekate']],
                'start_date' => ['column' => 'start_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $year . '-09-30'],
                'farming' => ['column' => 'farming_id', 'compare' => '=', 'prefix' => 'c', 'value' => $rpcParams['farming']],
                'active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'TRUE'],
            ],
            'plots_id_string' => implode(', ', $plots_id_array),
            'group' => 'c.id, kvs.gid, pc.id',
        ];

        // get all subleased plot GIDs for
        $subleased_results = $UserDbPlotsController->getSubleasedPlotData($options, false, false);

        // create array with leading plot GIDs as keys
        // array holds plot_gid => array(sublease_num, ...)
        $subleased_plots = [];
        if (0 != count($subleased_results)) {
            for ($i = 0; $i < count($subleased_results); $i++) {
                // assign current result to variables for easy use
                $plot_gid = $subleased_results[$i]['gid'];
                $sublease_data = $subleased_results[$i];

                // check if sublease for this plot was already found
                if ('' != $subleased_plots[$plot_gid]) {
                    // if sublease already exists concat
                    $subleased_plots[$plot_gid] .= ', ' . $sublease_data['c_num'];
                } else { // if sublease was not found create record
                    $subleased_plots[$plot_gid] = $sublease_data['c_num'];
                }
            }
        }

        // convert results for datagrid
        $total_area = 0;
        for ($i = 0; $i < count($results); $i++) {
            // assing variables for easy use
            $plot_gid = $results[$i]['gid'];

            $total_area += $results[$i]['area'];

            if ($subleased_plots[$plot_gid]) {
                $results[$i]['subleases'] = $subleased_plots[$plot_gid];
                $results[$i]['is_subleased'] = true;
            } else {
                $results[$i]['subleases'] = '-';
                $results[$i]['is_subleased'] = false;
            }
        }

        $return = [
            'rows' => $results,
            'total' => $counter[0]['count'],
            'footer' => [
                [
                    'kad_ident' => '<b>Общо за стр.</b>',
                    'area' => number_format($total_area, 3, '.', ''),
                ],
            ],
        ];

        // return the final results
        return $return;
    }

    /**
     * Loads information for 'Анкетна карта'.
     *
     * @api-method anketnaKarta
     *
     * @param array $rpcParams
     *                         {
     *                         #item string ekate
     *                         #item int farming
     *                         #item int year
     *                         #item string ntp
     *                         #item string from_date - c_date filter parameter
     *                         #item string to_date   - c_date filter parameter
     *                         }
     * @param string $page pagination rpc parameter
     * @param string $rows pagination rpc parameter
     * @param string $sort pagination rpc parameter
     * @param string $order pagination rpc parameter
     *
     * @return array
     */
    public function loadAnketnaKarta(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        if ($this->User->isGuest) {
            return [];
        }
        $return = [
            'rows' => [],
            'total' => 0,
            'footer' => [],
        ];

        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);
        $UserDbAreaTypesController = new UserDbAreaTypesController($this->User->Database);

        $year = $GLOBALS['Farming']['years'][$rpcParams['year']]['year'];

        $farming_start_date = ($year - 1) . '-10-01';
        $farming_due_date = $year . '-09-30';

        $ntpFilter = [];
        if (!empty($rpcParams['ntp'])) {
            $ntpFilter = $UserDbAreaTypesController->getNtpCodesWithAdditionalCodes($rpcParams['ntp']);
        }
        // options for plots-contracts query
        $options = [
            'return' => [
                'kvs.gid',
                'kad_ident',
                'virtual_ntp_title as area_type',
                'include',
                'participate',
                'white_spots',
                'sum(coalesce(sspa.contract_area, 0))::numeric(9, 3) - sum(coalesce(sscpr.contract_area_for_sale, 0))::numeric(9, 3) as area',
                'kvs.ekate',
                'kvs.virtual_ekatte_name as ekatte_name',
                'kvs.masiv',
                'number',
                'c.c_num',
                'TO_CHAR(c.c_date, \'DD.MM.YYYY\') as c_date',
                "concat('sub_', spcr.id) as sub_pc_rel_id",
                'pc.id::text as pc_rel_id',
                'c.id',
                'c.nm_usage_rights',
                'c.virtual_contract_type as c_type',
                'count(*) OVER () as total_count',
                "string_agg(scs.c_num::varchar, ',') as subleases_c_nums",
            ],
            'where' => [
                'ekate' => ['column' => 'ekate', 'compare' => '=', 'prefix' => 'kvs', 'value' => $rpcParams['ekate']],
                'farming' => ['column' => 'farming_id', 'compare' => '=', 'prefix' => 'c', 'value' => $rpcParams['farming']],
                'active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'TRUE'],
                'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
                'is_annex' => ['column' => 'is_annex', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
                'annex_action' => ['column' => 'annex_action', 'compare' => '=', 'prefix' => 'pc', 'value' => 'added'],
                // filters
                'masiv' => ['column' => 'masiv', 'compare' => '=', 'prefix' => 'kvs', 'value' => $rpcParams['masiv']],
                'imot' => ['column' => 'number', 'compare' => '=', 'prefix' => 'kvs', 'value' => $rpcParams['imot']],
                'c_type' => ['column' => 'nm_usage_rights', 'compare' => '=', 'prefix' => 'c', 'value' => $rpcParams['c_type']],
                'area_type' => ['column' => 'area_type', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $ntpFilter],
                'c_date_from' => ['column' => 'c_date', 'compare' => '>=', 'prefix' => 'c', 'value' => $rpcParams['from_date']],
                'c_date_to' => ['column' => 'c_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $rpcParams['to_date']],
                'cs_date_from' => ['column' => '(case when scs.start_date is not null then scs.start_date <= \'' . $farming_start_date . '\' else true end)', 'compare' => '=', 'value' => true],
                'cs_date_to' => ['column' => '(case when scs.due_date is not null then scs.due_date >= \'' . $farming_due_date . '\' else true end)', 'compare' => '=', 'value' => true],
                'archited_plot' => ['column' => '(kvs.is_edited = false or kvs.edit_active_from > \'' . $farming_due_date . '\')', 'compare' => '=', 'value' => 'true'],
            ],
            'joins' => [
                'left join su_contracts as scs on (scs.id = spcr.sublease_id)',
                "left join su_subleases_plots_area as sspa on ( sspa.sublease_id = scs.id and sspa.plot_id = pc.plot_id and scs.active = true and scs.start_date <= '" . $farming_due_date . "' and scs.due_date >= '" . $farming_start_date . "' )",
                'left join su_sales_contracts_plots_rel sscprp on (sscprp.pc_rel_id = pc.id)',
                "left join su_sales_contracts as ssc on (ssc.id = sscprp.sales_contract_id and ssc.active = true and ssc.start_date <= '" . $farming_start_date . "')",
                'left join su_sales_contracts_plots_rel sscpr on (sscpr.pc_rel_id = pc.id and sscpr.sales_contract_id = ssc.id)',
                'left join su_agreements sa on sa.farming = c.farming_id and sa.year = ' . $rpcParams['year'],
                'left join su_agreements_data sad on sad.agreement_id = sa.id and sad.ekate = kvs.ekate and sad.masiv = kvs.masiv and sad.imot = kvs."number"',
            ],
            'group' => 'kvs.gid, pc.id, spcr.id, c.id',
            'having' => 'having (sum(coalesce(sspa.contract_area, 0))::numeric(9, 3) > 0) and (case when max(sa.id) is not null then (kvs.participate = false or c.nm_usage_rights = 4) else true end)',
            'custom_counter' => 'COUNT(DISTINCT(pc.id))',
            'limit' => $rows,
            'offset' => ($page - 1) * $rows,
            'farming_start_date' => $farming_start_date,
            'farming_due_date' => $farming_due_date,
            'include_subleased_contracts' => true,
        ];

        if ('-1' == $options['where']['area_type']['value']) {
            $options['where']['area_type'] = ['column' => 'area_type', 'compare' => 'is', 'prefix' => 'kvs', 'value' => 'NULL'];
        }
        $subRels = [];
        $rels = [];
        if (!empty($_SESSION['decl_array'])) {
            foreach ($_SESSION['decl_array'] as $rel) {
                if (false !== strpos($rel, 'sub_')) {
                    $subRels[] = (int)str_replace('sub_', '', $rel);
                } else {
                    $rels[] = (int)$rel;
                }
            }
            if (!empty($subRels)) {
                $options['sub_pc_rel_anti_id_string'] = implode(', ', $subRels);
            }
        }

        $options['union']['return'] = [
            'kvs.gid',
            'kad_ident',
            'virtual_ntp_title as area_type',
            'include',
            'participate',
            'white_spots',
            'coalesce(sum(sad.area), pc.contract_area)::numeric(9, 3) - sum(coalesce(sspa.contract_area, 0))::numeric(9, 3) - sum(coalesce(sscpr.contract_area_for_sale, 0))::numeric(9, 3) as area',
            'kvs.ekate',
            'kvs.virtual_ekatte_name as ekatte_name',
            'kvs.masiv',
            'number',
            'c.c_num',
            'TO_CHAR(c.c_date, \'DD.MM.YYYY\') as c_date',
            'null as sub_pc_rel_id',
            'pc.id::text as pc_rel_id',
            'c.id',
            'c.nm_usage_rights',
            'c.virtual_contract_type as c_type',
            'count(*) OVER () as total_count',
            'null as subleases_c_nums',
        ];
        $options['union']['having'] = 'having sum(coalesce(sspa.contract_area, 0))::numeric(9, 3) + sum(coalesce(sscpr.contract_area_for_sale, 0))::numeric(9, 3) < coalesce(sum(sad.area), pc.contract_area)::numeric(9, 3) and (case when max(sa.id) is not null then (kvs.participate = true or c.nm_usage_rights = 4) else true end)';

        $options['union']['group'] = 'kvs.gid, pc.id, c.id';
        $options['union']['pc_rel_anti_id_string'] = implode(', ', $rels);

        switch ($sort) {
            case 'kad_ident':
                $options['sort'] = 'kad_ident COLLATE "alpha_numeric_bg"';
                $options['order'] = $order;

                break;
            case 'area':
                $options['sort'] = 'area';
                $options['order'] = $order;

                break;
            case 'area_type':
                $options['sort'] = 'area_type COLLATE "alpha_numeric_bg"';
                $options['order'] = $order;

                break;
            case 'c_num':
                $options['sort'] = 'c_num COLLATE "alpha_numeric_bg"';
                $options['order'] = $order;

                break;
            case 'c_type':
                $options['sort'] = 'nm_usage_rights';
                $options['order'] = $order;

                break;
            default:
                break;
        }
        $counterOptions = $options;
        unset($counterOptions['limit'], $counterOptions['offset']);

        $resultsCount = $UserDbPlotsController->getFullPlotDataForDeclaration($counterOptions);
        $totalResults = count($resultsCount);
        if (0 === $totalResults) {
            return $return;
        }

        $results = $UserDbPlotsController->getFullPlotDataForDeclaration($options, false, false);

        // convert results for datagrid
        $total_area = 0;
        foreach ($results as &$result) {
            if (null !== $result['subleases_c_nums']) {
                $result['subleases'] = $result['subleases_c_nums'];
                $result['is_subleased'] = true;
                $result['pc_rel_id'] = $result['sub_pc_rel_id'];
            } else {
                $result['subleases'] = '-';
                $result['is_subleased'] = false;
            }
            $areaInHa = $result['area'] / 10;
            $result['area'] = number_format($areaInHa, 4);

            $total_area += $areaInHa;
        }
        unset($result);

        return [
            'rows' => $results,
            'total' => $totalResults,
            'footer' => [
                [
                    'kad_ident' => '<b>Общо за стр.</b>',
                    'area' => number_format($total_area, 3, '.', ''),
                    'irrigated_area' => number_format($results[0]['total_irrigated_area'], 4, '.', ''),
                ],
            ],
        ];
    }
}
