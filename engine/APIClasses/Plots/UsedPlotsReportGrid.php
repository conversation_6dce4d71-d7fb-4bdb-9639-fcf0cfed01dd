<?php

namespace TF\Engine\APIClasses\Plots;

use Prado\Web\Services\TRpcApiProvider;
use TF\Application\Common\Config;
use TF\Engine\Kernel\Export2XlsClass;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDbAreaTypes\UserDbAreaTypesController;
use TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Справка "Използвана земя".
 *
 * @rpc-module Plots
 *
 * @rpc-service-id used-plots-report-grid
 *
 * @property FarmingController $FarmingController
 * @property UsersController $UsersController
 * @property UserDbPlotsController $UserDbPlotsController
 */
class UsedPlotsReportGrid extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getUseedPlots']],
            'exportToExcelUsedPlotsReportData' => ['method' => [$this, 'exportToExcelUsedPlotsReportData']],
        ];
    }

    /**
     * Export To Excel Used Plots Report Data.
     *
     * @param array $data {
     *                    #items array filters {
     *                    #item  timestamp report_date
     *                    #item  timestamp report_date_from
     *                    #item  timestamp report_date_as_of
     *                    #item  timestamp report_contract_date
     *                    #item  timestamp report_contract_date_to
     *                    #item  integer report_farming
     *                    #item  integer report_ekate
     *                    #item  integer report_ntp
     *                    #item  integer report_category
     *                    #item  string report_mestnost
     *                    #item  string report_irrigation
     *                    #item  string report_include_subleases
     *                    }
     *                    }
     * @param int $page
     * @param int $rows
     * @param string $sort
     * @param string $order
     *
     * @return array
     */
    public function exportToExcelUsedPlotsReportData($data, $page = null, $rows = null, $sort = null, $order = null)
    {
        $results = $this->getUseedPlots($data, $page, $rows, $sort, $order);
        $result = $results['rows'];

        unset($result[0]['attributes']);

        $result = $this->formatRowsForExport($result);

        $columns = [
            'Землище',
            'Идентификатор',
            'Местност',
            'Категория',
            'НТП',
            'Площ(дка)',
            'Ид. част',
            'Пл. по док.(дка)',
            'Обработваема площ',
            'Ползване',
            'Тип',
            'Стопанство',
            'Арендодател/Наемодател',
            'ЕГН/ЕИК',
            'Договор №',
            'Преотдаден чрез',
            'Вписване №',
            'Дата на вписване',
            'Том',
            'Дело',
            'Нотар. акт №',
            'Районен съд',
            'Валиден до',
            'Рента (пари)',
        ];

        $this->addTotals($result);

        $time = strtotime(date('Y-m-d H:i:s'));
        $fileName = 'izpolzvana_zemq_' . $this->User->GroupID . '_' . $time . '.xlsx';
        $path = PUBLIC_UPLOAD_EXPORT . '/' . $this->User->GroupID . '/' . $fileName;

        $export2Xls = new Export2XlsClass();

        $filePath = $export2Xls->exportUrlPath($path, $result, $columns);

        return ['file_path' => $filePath, 'file_name' => $fileName];
    }

    /**
     * getUseedPlots.
     *
     * @api-method read
     *
     * @param array $params {
     *                      #items array filters {
     *                      #item  timestamp report_date
     *                      #item  timestamp report_date_from
     *                      #item  timestamp report_date_as_of
     *                      #item  timestamp report_contract_date
     *                      #item  timestamp report_contract_date_to
     *                      #item  integer report_farming
     *                      #item  integer report_ekate
     *                      #item  integer report_ntp
     *                      #item  integer report_category
     *                      #item  string report_mestnost
     *                      #item  string report_irrigation
     *                      #item  string report_include_subleases
     *                      }
     *                      }
     * @param int $page
     * @param int $rows
     * @param string $sort
     * @param string $order
     *
     * @return array
     */
    public function getUseedPlots($params, $page = null, $rows = null, $sort = null, $order = null)
    {
        $reportDateFrom = $params['filters']['report_date_from'];
        $reportDate = $params['filters']['report_date'];
        $reportDateAsOf = $params['filters']['report_date_as_of'];
        $reportContractDate = $params['filters']['report_contract_date'];
        $reportContractDateTo = $params['filters']['report_contract_date_to'];
        $reportFarming = $params['filters']['report_farming'];
        $reportEkate = $params['filters']['report_ekate'];
        $reportMasiv = $params['filters']['report_masiv'];
        $reportPlotNumber = $params['filters']['report_plot_number'];
        $reportKadIdent = $params['filters']['report_kad_ident'];
        $reportNtp = $params['filters']['report_ntp'];
        $reportCategory = $params['filters']['report_category'];
        $reportMestnost = $params['filters']['report_mestnost'];
        $reportIncludeSubleased = $params['filters']['report_include_subleases'];
        $reportParticipation = $params['filters']['report_choose_participation'];
        $reportIrrigation = null;
        if ('all' != $params['filters']['report_irrigation']) {
            $reportIrrigation = $params['filters']['report_irrigation'];
        }

        $FarmingController = new FarmingController('Farming');
        $userFarmings = $FarmingController->getUserFarmings(true);
        $farmingIds = array_keys($userFarmings);

        if ($reportFarming && in_array((int) $reportFarming, $farmingIds)) {
            $farmingIds = [(int) $reportFarming];
        }

        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);

        if ($reportDateAsOf) {
            $reportDate = $reportDateAsOf;
            $reportDateFrom = $reportDateAsOf;
        }

        // Вземане на всички преотдадени pc_rel_id
        $pc_rel_ids = $UserDbPlotsController->getSubleasePcRelIds($reportDateFrom, $reportDate);

        $pc_rel_ids = array_map('intval', explode(',', trim($pc_rel_ids[0]['pc_rel_ids'], '{}')));

        if (!count($pc_rel_ids)) {
            $pc_rel_ids[] = 0;
        }

        // Вземане на всички договори, от които е преотдавано
        $options = [
            'tablename' => $UserDbPlotsController->DbHandler->contractsPlotsRelTable,
            'return' => [
                'array_agg(distinct (contract_id)) as contract_ids',
            ],
            'where' => [
                'id' => ['column' => 'id', 'compare' => 'IN', 'value' => $pc_rel_ids],
            ],
        ];

        $contract_ids = $UserDbPlotsController->getItemsByParams($options);

        $contract_ids = array_map('intval', explode(',', trim($contract_ids[0]['contract_ids'], '{}')));

        // Вземане на всички преотдадени имоти
        $options = [
            'tablename' => $UserDbPlotsController->DbHandler->contractsPlotsRelTable,
            'return' => [
                'array_agg(distinct (plot_id)) as plot_ids',
            ],
            'where' => [
                'id' => ['column' => 'id', 'compare' => 'IN', 'value' => $pc_rel_ids],
            ],
        ];

        $plot_ids = $UserDbPlotsController->getItemsByParams($options);

        $plot_ids = array_map('intval', explode(',', trim($plot_ids[0]['plot_ids'], '{}')));

        // Вземане на всички анекси, от чийто оригинален договор е преотдавано
        $options = [
            'tablename' => $UserDbPlotsController->DbHandler->tableContracts,
            'return' => [
                'array_agg(distinct (id)) as annex_ids',
            ],
            'where' => [
                'parent_id' => ['column' => 'parent_id', 'compare' => 'IN', 'value' => $contract_ids],
            ],
        ];

        $annex_ids = $UserDbPlotsController->getItemsByParams($options);

        $annex_ids = array_map('intval', explode(',', trim($annex_ids[0]['annex_ids'], '{}')));

        $contract_ids = array_merge($contract_ids, $annex_ids);

        // от списъка с договори трябва да се изключат служебните договори (за да се показват в резултата)
        // (в основната заявка има NOT IN на този списък)
        $options = [
            'tablename' => $UserDbPlotsController->DbHandler->tableContracts,
            'return' => [
                'array_agg(distinct (id)) as service_contract_ids',
            ],
            'where' => [
                'from_sublease' => ['column' => 'from_sublease', 'compare' => 'IS NOT', 'value' => 'NULL'],
            ],
        ];

        if ($reportDateAsOf) {
            $options['where']['start_date'] = ['column' => 'start_date', 'compare' => '<=', 'value' => $reportDateAsOf];
            $options['where']['due_date'] = ['column' => 'due_date', 'compare' => '>=', 'value' => $reportDateAsOf];
        }

        if ('' == $options['start_date'] && '' == $options['due_date']) {
            $options['where']['start_date'] = ['column' => 'start_date', 'compare' => '<=', 'value' => '2050-01-01'];
            $options['where']['due_date'] = ['column' => 'due_date', 'compare' => '>=', 'value' => '1970-01-01'];
        }

        $service_ids = $UserDbPlotsController->getItemsByParams($options);
        $service_ids = array_map('intval', explode(',', trim($service_ids[0]['service_contract_ids'], '{}')));

        $contract_ids = array_diff($contract_ids, $service_ids);

        if (!count($contract_ids)) {
            $contract_ids[] = 0;
        }
        $contracts_ids_string = implode(',', $contract_ids);
        if (!count($plot_ids)) {
            $plot_ids[] = 0;
        }
        $plot_ids_string = implode(',', $plot_ids);

        // create the default return
        $return = [
            'rows' => [],
            'total' => 0,
            'footer' => [],
        ];

        $contractTypeLease = Config::CONTRACT_TYPE_LEASE;
        $contractTypeRent = Config::CONTRACT_TYPE_RENT;

        $options = [
            'custom_counter' => 'COUNT(DISTINCT(cpr.id))',
            'sort' => $sort,
            'order' => $order,
            'return' => [
                'DISTINCT(cpr.id)', 't2.sold_area', 't2.total_area', 'gid',
                'ekate',
                'virtual_ekatte_name as land',
                'kad_ident', 'masiv', 'number', 'participate', 'white_spots', 'include',
                'COALESCE(mestnost, \'-\') as mestnost',
                'category as category_id',
                'COALESCE(virtual_category_title, \'-\') as category',
                'area_type as ntp_code',
                'virtual_ntp_title as area_type',
                ' CASE WHEN t2.sold_area IS NULL THEN cpr.contract_area ELSE t2.total_area - t2.sold_area END as area',
                'c.sv_num', "to_char(c.sv_date,'DD.MM.YYYY') as sv_date", 'c.virtual_contract_type as c_type',  'c.farming_id', 'c.farming_name',
                'c.tom', 'c.delo', 'c.na_num', 'c.court',
                "CASE WHEN c.is_declaration_subleased
                    THEN 
                        CASE WHEN c.nm_usage_rights = {$contractTypeLease}
                            THEN 'Преарендован'
                        WHEN c.nm_usage_rights = {$contractTypeRent}
                            THEN 'Пренает'
                        END
                    ELSE
                        '-'
                END as lease_type",
                'c_a.id as c_a_id', 'c_a.c_num as c_a_c_num', 'c_a.is_sublease as c_a_is_sublease', 'max(a.id) as a_id',
                'max(c_a_sub_c.id) as c_a_sublease_id',
                'max(c_a_sub_c.id) as has_sublease',
                'max(c_a_sub_c.c_num) as has_sublease_num',
                'c.is_annex',
                "to_char((case when c.is_sublease then c.due_date else cpr.contract_end_date end),'DD.MM.YYYY') AS due_date",
                'coalesce(cpr.rent_per_plot, c1.renta) as renta',
                'cpr.price_per_acre', 'cpr.price_sum', 'pf.farming_id as arendodatel',
                'round(COALESCE(cpr.kvs_allowable_area, kvs.allowable_area, 0)::numeric, 3) as kvs_allowable_area',
                "string_agg(DISTINCT(CASE WHEN owner_type = 1 THEN name || ' ' || surname || ' ' || lastname ELSE company_name END), ',<br />') as owner_names",
                "string_agg(DISTINCT(CASE WHEN o.owner_type = 1 THEN o.egn ELSE o.eik END), ',<br />') as egn", 'c.is_sublease',
                'case when c.is_sublease then c.id else null end as sublease_id', 'case when c.is_sublease then c.c_num else null end as sublease_num',
                "to_char((c.start_date),'DD.MM.YYYY') AS subl_start_date",
                "to_char((c.due_date),'DD.MM.YYYY') AS subl_due_date",
                'round((cpr.contract_area / case when document_area is null OR document_area = 0 then ST_AREA(geom)/1000 else document_area end)::numeric,3) as fraction',
                'round((case when document_area is null OR document_area = 0 then ST_AREA(geom)/1000 else document_area end)::numeric,3) as document_area',
            ],
            'where' => [
                'c_date' => ['column' => 'c_date', 'compare' => '>=', 'prefix' => 'c', 'value' => $reportContractDate],
                'c_date_to' => ['column' => 'c_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $reportContractDateTo],
                'farming_id' => ['column' => 'farming_id', 'compare' => 'IN', 'value' => $farmingIds],
            ],
            'start_date' => $reportDate,
            'due_date' => $reportDateFrom,
            'include_subleases' => $reportIncludeSubleased,
            'contract_ids' => $contracts_ids_string,
            'plot_ids' => $plot_ids_string,
            'group' => 'kvs.gid, cpr.id, c.id, c1.id, pf.id, cpr.contract_area, cpr.price_per_acre, cpr.price_sum, t2.sold_area, t2.total_area, c_a.id',
        ];

        if (!$reportIncludeSubleased) {
            $options['where']['exclude_subleases'] = ['column' => 'c_a_sublease_id', 'compare' => 'is', 'prefix' => 'data', 'value' => 'NULL'];
        }
        if ($reportEkate) {
            $options['where']['ekate'] = ['column' => 'ekate', 'compare' => '=', 'value' => $reportEkate];
        }
        if ($reportMasiv) {
            $options['where']['masiv'] = ['column' => 'masiv',  'compare' => '=', 'value' => $reportMasiv];
        }
        if ($reportPlotNumber) {
            $options['where']['number'] = ['column' => 'number', 'compare' => '=', 'value' => $reportPlotNumber];
        }
        if ($reportKadIdent) {
            $options['where']['kad_ident'] = ['column' => 'kad_ident', 'compare' => '=', 'value' => $reportKadIdent];
        }
        if ($reportNtp) {
            $UserDbAreaTypesController = new UserDbAreaTypesController($this->User->Database);
            $ntpFilter = $UserDbAreaTypesController->getNtpCodesWithAdditionalCodes([$reportNtp]);
            $options['where']['ntp'] = ['column' => 'ntp_code', 'compare' => 'IN', 'value' => $ntpFilter];
        }
        if ($reportCategory) {
            $categoryFilter = in_array($reportCategory, ['-1', '0', null]) ? ['-1', '0', null] : [$reportCategory];
            $options['where']['category'] = ['column' => 'category_id', 'compare' => 'IN', 'value' => $categoryFilter];
        }
        if ($reportMestnost) {
            $options['where']['mestnost'] = ['column' => 'mestnost', 'compare' => '=', 'value' => $reportMestnost];
        }
        if ($reportIrrigation) {
            $options['where']['irrigated_area'] = ['column' => 'irrigated_area', 'compare' => '=', 'value' => $reportIrrigation];
        }
        if ($reportDateAsOf) {
            $options['start_date'] = $reportDateAsOf;
            $options['due_date'] = $reportDateAsOf;
        }

        if ('' == $options['start_date'] && '' == $options['due_date']) {
            $options['start_date'] = '2050-01-01';
            $options['due_date'] = '1970-01-01';
        }

        if (isset($params['filters']['report_owner_type']) && '' !== $params['filters']['report_owner_type']) {
            $options['where']['owner_type'] = ['column' => 'owner_type', 'compare' => '=', 'value' => $params['filters']['report_owner_type']];
            $options['where']['nm_usage_rights'] = ['column' => 'nm_usage_rights', 'compare' => 'IN', 'value' => [Config::CONTRACT_TYPE_LEASE, Config::CONTRACT_TYPE_RENT, Config::CONTRACT_TYPE_JOINT_PROCESSING]];
        }

        if ($reportParticipation) {
            switch ($reportParticipation) {
                case 'participate':
                    $options['where']['participate'] = ['column' => 'participate', 'compare' => '=', 'value' => 'true'];
                    $options['where']['include'] = ['column' => 'include', 'compare' => '=', 'value' => 'false'];
                    $options['where']['white_spots'] = ['column' => 'white_spots', 'compare' => '=', 'value' => 'false'];

                    break;
                case 'no_participate':
                    $options['where']['participate'] = ['column' => 'participate', 'compare' => '=', 'value' => 'false'];
                    $options['where']['include'] = ['column' => 'include', 'compare' => '=', 'value' => 'true'];
                    $options['where']['white_spots'] = ['column' => 'white_spots', 'compare' => '=', 'value' => 'false'];

                    break;
                case 'without':
                    $options['where']['participate'] = ['column' => 'participate', 'compare' => '=', 'value' => 'false'];
                    $options['where']['include'] = ['column' => 'include', 'compare' => '=', 'value' => 'false'];
                    $options['where']['white_spots'] = ['column' => 'white_spots', 'compare' => '=', 'value' => 'false'];

                    break;
                default:
                    break;
            }
        }

        $totals = $UserDbPlotsController->getUsedPlotsForReport($options, false, false);
        $totalCounter = count($totals);
        if (0 == $totalCounter) {
            return $return;
        }

        $options['offset'] = ($page - 1) * $rows;
        $options['limit'] = $rows;
        $results = $UserDbPlotsController->getUsedPlotsForReport($options, false, false);
        $resultsCount = count($results);

        $total_area_for_page = 0;
        $total_renta_for_page = 0;
        $total_price_per_acre = 0;
        $total_price_sum = 0;
        $total_kvs_area_for_page = 0;

        for ($i = 0; $i < $resultsCount; $i++) {
            $results[$i] = $this->reformatResult($results[$i], $userFarmings);

            $results[$i]['c_num_export'] = $results[$i]['c_num'];
            if ($results[$i]['is_annex']) {
                $results[$i]['c_num_export'] = $results[$i]['c_num'] . ' (Анекс) от ' . $results[$i]['c_a_c_num'] . ' (Договор)';
            }

            $total_area_for_page += $results[$i]['area'];
            $total_renta_for_page += $results[$i]['renta'];
            $total_price_per_acre += $results[$i]['price_per_acre'];
            $total_price_sum += $results[$i]['price_sum'];
            $total_kvs_area_for_page += $results[$i]['kvs_allowable_area'];

            $results[$i]['renta'] = BGNtoEURO($results[$i]['renta']);
            $results[$i]['price_per_acre'] = BGNtoEURO($results[$i]['price_per_acre']);
            $results[$i]['price_sum'] = BGNtoEURO($results[$i]['price_sum']);
        }

        $total_area = 0;
        $total_renta = 0;
        $total_kvs_area = 0;

        for ($i = 0; $i < $totalCounter; $i++) {
            $total_area += $totals[$i]['area'];
            $total_renta += $totals[$i]['renta'];
            $total_kvs_area += $totals[$i]['kvs_allowable_area'];
        }

        $return['rows'] = $results;
        $return['total'] = count($totals);
        $return['footer'] = [
            [
                'land' => '<b>Бр. имоти за стр.</b>',
                'kad_ident' => count(array_unique(array_column($results, 'kad_ident'))),
                'area_type' => '<b>Общо за стр.</b>',
                'area' => number_format($total_area_for_page, 3, '.', ' '),
                'renta' => BGNtoEURO($total_renta_for_page),
                'kvs_allowable_area' => number_format($total_kvs_area_for_page, 3, '.', ' '),
            ],
            [
                'land' => '<b>Бр. имоти</b>',
                'kad_ident' => count(array_unique(array_column($totals, 'kad_ident'))),
                'area_type' => '<b>Общо</b>',
                'area' => number_format($total_area, 3, '.', ' '),
                'renta' => BGNtoEURO($total_renta),
                'kvs_allowable_area' => number_format($total_kvs_area, 3, '.', ' '),
            ],
        ];

        return $return;
    }

    // http://php.net/manual/en/ref.math.php
    protected function dec2fraction($decimal)
    {
        $decimal = (string)$decimal;
        $num = '';
        $den = 1;
        $dec = false;

        // find least reduced fractional form of number
        for ($i = 0, $ix = strlen($decimal); $i < $ix; $i++) {
            // build the denominator as we 'shift' the decimal to the right
            if ($dec) {
                $den *= 10;
            }

            // find the decimal place/ build the numberator
            if ('.' == $decimal[$i]) {
                $dec = true;
            } else {
                $num .= $decimal[$i];
            }
        }
        $num = (int)$num;

        // whole number, just return it
        if (1 == $den) {
            return $num;
        }

        $num2 = $num;
        $den2 = $den;
        $rem = 1;
        // Euclid's Algorithm (to find the gcd)
        while ($num2 % $den2) {
            $rem = $num2 % $den2;
            $num2 = $den2;
            $den2 = $rem;
        }
        if ($den2 != $den) {
            $rem = $den2;
        }

        // now $rem holds the gcd of the numerator and denominator of our fraction
        return ($num / $rem) . '/' . ($den / $rem);
    }

    private function formatRowsForExport($rows)
    {
        $results = [];

        $count = count($rows);
        for ($i = 0; $i < $count; $i++) {
            $results[$i]['land'] = $rows[$i]['land'];
            $results[$i]['kad_ident'] = $rows[$i]['kad_ident'];
            $results[$i]['mestnost'] = $rows[$i]['mestnost'];
            $results[$i]['category'] = $rows[$i]['category'];
            $results[$i]['area_type'] = $rows[$i]['area_type'];
            $results[$i]['area'] = $rows[$i]['area'];
            $results[$i]['fraction'] = $this->dec2fraction((float)$rows[$i]['fraction']);
            $results[$i]['document_area'] = $rows[$i]['document_area'];
            $results[$i]['kvs_allowable_area'] = $rows[$i]['kvs_allowable_area'];
            $results[$i]['c_type'] = $rows[$i]['c_type'];
            $results[$i]['lease_type'] = $rows[$i]['lease_type'];
            $results[$i]['farming_name'] = $rows[$i]['farming_name'];
            $results[$i]['owner_names'] = str_replace('<br />', "\n", $rows[$i]['owner_names']);
            $results[$i]['egn'] = str_replace('<br />', "\n", $rows[$i]['egn']);
            $results[$i]['c_num_export'] = $rows[$i]['c_num_export'];
            $results[$i]['sublease_num'] = $rows[$i]['sublease_num'];
            $results[$i]['sv_num'] = $rows[$i]['sv_num'];
            $results[$i]['sv_date'] = $rows[$i]['sv_date'];
            $results[$i]['tom'] = $rows[$i]['tom'];
            $results[$i]['delo'] = $rows[$i]['delo'];
            $results[$i]['na_num'] = $rows[$i]['na_num'];
            $results[$i]['court'] = $rows[$i]['court'];
            $results[$i]['due_date'] = $rows[$i]['due_date'];
            $results[$i]['renta'] = $rows[$i]['renta'];
        }

        return $results;
    }

    private function addTotals(&$rows)
    {
        $i = count($rows);
        $rows[$i]['land'] = '';
        $rows[$i]['kad_ident'] = '';
        $rows[$i]['mestnost'] = '';
        $rows[$i]['category'] = '';
        $rows[$i]['area_type'] = '';
        $rows[$i]['area'] = number_format(array_sum(array_column($rows, 'area')), 3);
        $rows[$i]['fraction'] = '';
        $rows[$i]['document_area'] = number_format(array_sum(array_column($rows, 'document_area')), 3);
        $rows[$i]['kvs_allowable_area'] = number_format(array_sum(array_column($rows, 'kvs_allowable_area')), 3);
        $rows[$i]['c_type'] = '';
        $rows[$i]['lease_type'] = '';
        $rows[$i]['farming_id'] = '';
        $rows[$i]['owner_names'] = '';
        $rows[$i]['egn'] = '';
        $rows[$i]['c_num'] = '';
        $rows[$i]['sublease_num'] = '';
        $rows[$i]['sv_num'] = '';
        $rows[$i]['sv_date'] = '';
        $rows[$i]['tom'] = '';
        $rows[$i]['delo'] = '';
        $rows[$i]['na_num'] = '';
        $rows[$i]['court'] = '';
        $rows[$i]['due_date'] = '';
        $rows[$i]['renta'] = '';
    }

    private function reformatResult($result, $farmings)
    {
        $result['area'] = number_format($result['area'], 3, '.', '');
        $result['price_per_acre'] = number_format($result['price_per_acre'], 2, '.', '');
        $result['price_sum'] = number_format($result['price_sum'], 2, '.', '');

        if (!$result['sv_num']) {
            $result['sv_num'] = '-';
        }
        if (!$result['sv_date']) {
            $result['sv_date'] = '-';
        }
        if (!$result['owner_names']) {
            $result['owner_names'] = '-';
        }

        if ($result['renta']) {
            $result['renta'] = number_format($result['renta'], 2, '.', '');
        }

        if ($result['arendodatel']) {
            $arendodatel = $farmings[$result['arendodatel']]['name'];
            $arendodatelEIK = $farmings[$result['arendodatel']]['bulstat'];

            if ('-' != $result['owner_names']) {
                $result['owner_names'] .= '<br>' . $arendodatel;
                $result['egn'] .= '<br>' . $arendodatelEIK;
            } else {
                $result['owner_names'] = $arendodatel;
                $result['egn'] = $arendodatelEIK;
            }
        }

        return $result;
    }
}
