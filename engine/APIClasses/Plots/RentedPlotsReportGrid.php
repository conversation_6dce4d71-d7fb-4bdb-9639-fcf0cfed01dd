<?php

namespace TF\Engine\APIClasses\Plots;

// Prado::using('Plugins.Core.UserDbPlots.*');
// Prado::using('Plugins.Core.Users.*');
// Prado::using('Plugins.Core.Users.conf');
// Prado::using('Plugins.Core.Farming.*');
// Prado::using('Plugins.Core.Farming.conf');

use Prado\Prado;
use Prado\Web\Services\TRpcApiProvider;
use TF\Application\Common\Config;
use TF\Engine\Kernel\Export2XlsClass;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDbAreaTypes\UserDbAreaTypesController;
use TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController;

/**
 * Справка "Свободна земя за ипотека".
 *
 * @rpc-module Plots
 *
 * @rpc-service-id rented-plots-report-grid
 *
 * @property UserDbPlotsController $UserDbPlotsController
 */
class RentedPlotsReportGrid extends TRpcApiProvider
{
    private $UserDbController;
    private $UserDbPlotsController;

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getRentedPlots'],
                'validators' => [
                    'params' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'exportToExcelRentedPlotsReportData' => ['method' => [$this, 'exportToExcelRentedPlotsReportData'],
                'validators' => [
                    'data' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * Export To Excel Rented Plots Report Data.
     *
     * @param array $data {
     *                    #items array filters {
     *                    #item  timestamp report_date
     *                    #item  timestamp report_date_from
     *                    #item  timestamp report_date_as_of
     *                    #item  timestamp report_contract_date
     *                    #item  timestamp report_contract_date_to
     *                    #item  integer report_farming
     *                    #item  integer report_ekate
     *                    #item  integer report_ntp
     *                    #item  integer report_category
     *                    #item  string report_mestnost
     *                    #item  string report_irrigation
     *                    }
     *                    }
     * @param int|string $page
     * @param int|string $rows
     * @param ?string $sort
     * @param ?string $order
     *
     * @return array
     */
    public function exportToExcelRentedPlotsReportData(array $data, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $results = $this->getRentedPlots($data, $page, $rows, $sort, $order);

        $result = $results['rows'];

        unset($result[0]['attributes']);

        $result = $this->formatRowsForExport($result);

        $columns = [
            'Землище',
            'Идентификатор',
            'Местност',
            'Категория',
            'НТП',
            'Площ(дка)',
            'Обработваема площ',
            'Ползване',
            'Тип',
            'Стопанство',
            'Арендодател/Наемодател',
            'ЕГН/ЕИК',
            'Договор №',
            'Преотдаден чрез',
            'Вписване №',
            'Дата на вписване',
            'Том',
            'Дело',
            'Нотар. акт №',
            'Районен съд',
            'Валиден до',
            'Рента (пари)',
        ];

        $this->addTotals($result);

        $time = strtotime(date('Y-m-d H:i:s'));
        $fileName = 'naeta_arendovana_zemq_' . $this->User->GroupID . '_' . $time . '.xlsx';
        $path = PUBLIC_UPLOAD_EXPORT . '/' . $this->User->GroupID . '/' . $fileName;
        /** @var Export2XlsClass $export2Xls */
        $export2Xls = new Export2XlsClass();
        $filePath = $export2Xls->exportUrlPath($path, $result, $columns);

        return ['file_path' => $filePath, 'file_name' => $fileName];
    }

    /**
     * getRentedPlots.
     *
     * @api-method read
     *
     * @param array $params {
     *                      #items array filters {
     *                      #item  timestamp report_date
     *                      #item  timestamp report_date_from
     *                      #item  timestamp report_date_as_of
     *                      #item  timestamp report_contract_date
     *                      #item  timestamp report_contract_date_to
     *                      #item  integer report_farming
     *                      #item  integer report_ekate
     *                      #item  integer report_ntp
     *                      #item  integer report_category
     *                      #item  string report_mestnost
     *                      #item  string report_irrigation
     *                      #item  string report_include_subleases
     *                      }
     *                      }
     * @param int|string $page
     * @param int|string $rows
     * @param ?string $sort
     * @param ?string $order
     *
     * @return array
     */
    public function getRentedPlots(array $params, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $reportDateFrom = $params['filters']['report_date_from'];
        $reportDate = $params['filters']['report_date'];
        $reportDateAsOf = $params['filters']['report_date_as_of'];
        $reportContractDate = $params['filters']['report_contract_date'];
        $reportContractDateTo = $params['filters']['report_contract_date_to'];
        $reportFarming = $params['filters']['report_farming'];
        $reportEkate = $params['filters']['report_ekate'];
        $reportMasiv = $params['filters']['report_masiv'];
        $reportPlotNumber = $params['filters']['report_plot_number'];
        $reportKadIdent = $params['filters']['report_kad_ident'];
        $reportNtp = $params['filters']['report_ntp'];
        $reportCategory = $params['filters']['report_category'];
        $reportMestnost = $params['filters']['report_mestnost'];
        $reportIncludeSubleased = $params['filters']['report_include_subleases'];
        $reportIrrigation = null;
        if ('all' != $params['filters']['report_irrigation']) {
            $reportIrrigation = $params['filters']['report_irrigation'];
        }

        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);
        $UserDbAreaTypesController = new UserDbAreaTypesController($this->User->Database);
        $FarmingController = new FarmingController('Farming');

        $userFarmings = $FarmingController->getUserFarmings(true);
        $farmingIds = array_keys($userFarmings);

        if ($reportFarming && in_array((int) $reportFarming, $farmingIds)) {
            $farmingIds = [(int) $reportFarming];
        }

        // create the default return
        $return = [
            'rows' => [],
            'total' => 0,
            'footer' => [],
        ];

        $subleasesRelationStartDate = $reportDateFrom;
        $subleasesRelationDueDate = $reportDate;

        if ('' == $reportDateFrom && '' == $reportDate) {
            $subleasesRelationStartDate = '2050-01-01';
            $subleasesRelationDueDate = '1970-01-01';
        }

        if ($reportDateAsOf) {
            $subleasesRelationStartDate = $reportDateAsOf;
            $subleasesRelationDueDate = $reportDateAsOf;
        }
        // Вземане на всички преотдадени pc_rel_id
        $pc_rel_ids_string = $UserDbPlotsController->getSubleasePcRelIds($subleasesRelationStartDate, $subleasesRelationDueDate);

        $pc_rel_ids = array_map('intval', explode(',', trim($pc_rel_ids_string[0]['pc_rel_ids'], '{}')));
        $pc_rel_anti_id_string = trim($pc_rel_ids_string[0]['pc_rel_ids'], '{}');

        $contractTypeLease = Config::CONTRACT_TYPE_LEASE;
        $contractTypeRent = Config::CONTRACT_TYPE_RENT;

        $options = [
            'custom_counter' => 'COUNT(DISTINCT(cpr.id))',
            'sort' => $sort,
            'order' => $order,
            'return' => [
                'DISTINCT(cpr.id)',
                'gid',
                'virtual_ekatte_name as ekate',
                'kad_ident',
                'COALESCE(mestnost, \'-\') as mestnost',
                'COALESCE(virtual_category_title, \'-\') as category',
                'virtual_ntp_title as area_type',
                'c.is_annex',
                'round(cpr.contract_area::numeric, 3) AS area',
                'case when c.is_sublease then c1.c_num else c.c_num end as c_num',
                'case when c.is_sublease then c1.id else c.id end as c_id',
                "CASE WHEN c.is_declaration_subleased
                    THEN 
                        CASE WHEN c.nm_usage_rights = {$contractTypeLease}
                            THEN 'Преарендован'
                        WHEN c.nm_usage_rights = {$contractTypeRent}
                            THEN 'Пренает'
                        END
                    ELSE
                        '-'
                END as lease_type",
                'c_a.id as c_a_id', 'c_a.c_num as c_a_c_num', 'c_a.is_sublease as c_a_is_sublease',
                'max(c_a_sub_c.id) as has_sublease',
                'max(c_a_sub_c.c_num) as has_sublease_num',
                'case when c.is_sublease then c.id else null end as sublease_id', 'case when c.is_sublease then c.c_num else null end as sublease_num',
                'COALESCE(c.sv_num, \'-\') as sv_num',
                'COALESCE(to_char(c.sv_date,\'DD.MM.YYYY\'), \'-\') as sv_date',
                'c.virtual_contract_type as c_type',
                'c.farming_id as farming_id',
                'c.tom', 'c.delo', 'c.na_num', 'c.court',
                'coalesce(cpr.rent_per_plot, c1.renta) as renta',
                'c.is_sublease', "to_char((case when c.is_sublease then c.due_date else cpr.contract_end_date end),'DD.MM.YYYY') AS due_date",
                'cpr.price_per_acre', 'cpr.price_sum', 'pf.farming_id as arendodatel',
                'round(COALESCE(cpr.kvs_allowable_area, kvs.allowable_area, 0)::numeric, 3) as kvs_allowable_area',
                "string_agg(DISTINCT(CASE WHEN owner_type = 1 THEN name || ' ' || surname || ' ' || lastname ELSE company_name END), ',<br />') as owner_names",
                "string_agg(DISTINCT(CASE WHEN o.owner_type = 1 THEN o.egn ELSE o.eik END), ',<br />') as egn",
                "to_char((c.start_date),'DD.MM.YYYY') AS subl_start_date",
                "to_char((c.due_date),'DD.MM.YYYY') AS subl_due_date",
            ],
            'where' => [
                'annex_action' => ['column' => 'annex_action', 'compare' => '=', 'prefix' => 'cpr', 'value' => 'added'],
                'active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'true'],
                'c1_active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c1', 'value' => 'true'],
                'c_date' => ['column' => 'c_date', 'compare' => '>=', 'prefix' => 'c', 'value' => $reportContractDate],
                'c_date_to' => ['column' => 'c_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $reportContractDateTo],
                'farming_id' => ['column' => 'farming_id', 'compare' => 'IN', 'prefix' => 'c', 'value' => $farmingIds],
            ],
            'group' => 'kvs.gid, cpr.id, c.id, pf.id, cpr.contract_area, cpr.price_per_acre, cpr.price_sum, c1.id, c_a.id',
            'start_date' => $reportDateFrom,
            'due_date' => $reportDate,
            'include_subleases' => $reportIncludeSubleased,
            'pc_rel_anti_id_string' => $pc_rel_anti_id_string,
        ];

        if (!$reportIncludeSubleased) {
            $options['where']['exclude_subleases'] = ['column' => 'id', 'compare' => 'is', 'prefix' => 'c_a_sub_c', 'value' => 'NULL'];
        }
        if ($reportEkate) {
            $options['where']['ekate'] = ['column' => 'ekate', 'compare' => '=', 'prefix' => 'kvs', 'value' => $reportEkate];
        }
        if ($reportMasiv) {
            $options['where']['masiv'] = ['column' => 'masiv', 'prefix' => 'kvs', 'compare' => '=', 'value' => $reportMasiv];
        }
        if ($reportPlotNumber) {
            $options['where']['number'] = ['column' => 'number', 'prefix' => 'kvs', 'compare' => '=', 'value' => $reportPlotNumber];
        }
        if ($reportKadIdent) {
            $options['where']['kad_ident'] = ['column' => 'kad_ident', 'prefix' => 'kvs', 'compare' => '=', 'value' => $reportKadIdent];
        }
        if ($reportNtp) {
            $ntpCodes = $UserDbAreaTypesController->getNtpCodesWithAdditionalCodes([$reportNtp]);
            $options['where']['ntp'] = ['column' => 'area_type', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $ntpCodes];
        }
        if ($reportCategory) {
            $categoryFilter = in_array($reportCategory, ['-1', '0', null]) ? ['-1', '0', null] : [$reportCategory];
            $options['where']['category'] = ['column' => 'category', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $categoryFilter];
        }
        if ($reportMestnost) {
            $options['where']['mestnost'] = ['column' => 'mestnost', 'compare' => '=', 'prefix' => 'kvs', 'value' => $reportMestnost];
        }

        if ($reportIrrigation) {
            $options['where']['irrigated_area'] = ['column' => 'irrigated_area', 'compare' => '=', 'prefix' => 'kvs', 'value' => $reportIrrigation];
        }

        if ($reportDateAsOf) {
            $options['start_date'] = $reportDateAsOf;
            $options['due_date'] = $reportDateAsOf;
        }

        if ('' == $options['start_date'] && '' == $options['due_date']) {
            $options['due_date'] = '2050-01-01';
            $options['start_date'] = '1970-01-01';
        }

        $results_total = $UserDbPlotsController->getRentedPlotsForReport($options, false, false);

        $counter = count($results_total);

        if (0 == $counter) {
            return $return;
        }

        $options['offset'] = ($page - 1) * $rows;
        $options['limit'] = $rows;

        $results = $UserDbPlotsController->getRentedPlotsForReport($options, false, false);
        $resultsCount = count($results);

        $total_area_for_page = 0;
        $total_renta_for_page = 0;
        $total_price_per_acre = 0;
        $total_price_sum = 0;
        $total_kvs_area_for_page = 0;

        for ($i = 0; $i < $resultsCount; $i++) {
            if ($results[$i]['is_annex']) {
                $results[$i]['c_num'] = $results[$i]['c_num'] . ' (Анекс) от ' . $results[$i]['c_a_c_num'] . ' (Договор)';
            }

            $results[$i]['farming_id'] = $userFarmings[$results[$i]['farming_id']]['name'];

            $total_area_for_page += $results[$i]['area'];
            $total_renta_for_page += $results[$i]['renta'];
            $total_price_per_acre += $results[$i]['price_per_acre'];
            $total_price_sum += $results[$i]['price_sum'];
            $total_kvs_area_for_page += $results[$i]['kvs_allowable_area'];

            $results[$i]['area'] = number_format($results[$i]['area'], 3, '.', '');
            $results[$i]['price_per_acre'] = BGNtoEURO($results[$i]['price_per_acre']);
            $results[$i]['price_sum'] = BGNtoEURO($results[$i]['price_sum']);

            if (!$results[$i]['owner_names']) {
                $results[$i]['owner_names'] = '-';
            }

            if ($results[$i]['renta']) {
                $results[$i]['renta'] = BGNtoEURO($results[$i]['renta']);
            }

            if ($results[$i]['arendodatel']) {
                $arendodatel = $userFarmings[$results[$i]['arendodatel']]['name'];
                $arendodatelEIK = $userFarmings[$results[$i]['arendodatel']]['bulstat'];

                if ('-' != $results[$i]['owner_names']) {
                    $results[$i]['owner_names'] .= '<br>' . $arendodatel;
                    $results[$i]['egn'] .= '<br>' . $arendodatelEIK;
                } else {
                    $results[$i]['owner_names'] = $arendodatel;
                    $results[$i]['egn'] = $arendodatelEIK;
                }
            }
        }

        $total_area = 0;
        $total_renta = 0;
        $total_kvs_allowable_area = 0;

        for ($i = 0; $i < $counter; $i++) {
            $total_area += $results_total[$i]['area'];
            $total_renta += $results_total[$i]['renta'];
            $total_kvs_allowable_area += $results_total[$i]['kvs_allowable_area'];
        }

        $return['rows'] = $results;
        $return['total'] = $counter;
        $return['footer'] = [
            [
                'land' => '<b>Бр. имоти за стр.</b>',
                'kad_ident' => count(array_unique(array_column($results, 'kad_ident'))),
                'area_type' => '<b>Общо за стр.</b>',
                'area' => number_format($total_area_for_page, 3, '.', ''),
                'renta' => BGNtoEURO($total_renta_for_page),
                'kvs_allowable_area' => number_format($total_kvs_area_for_page, 3, '.', ''),
            ],
            [
                'land' => '<b>Бр. имоти</b>',
                'kad_ident' => count(array_unique(array_column($results_total, 'kad_ident'))),
                'area_type' => '<b>Общо</b>',
                'area' => number_format($total_area, 3, '.', ''),
                'renta' => BGNtoEURO($total_renta),
                'kvs_allowable_area' => number_format($total_kvs_allowable_area, 3, '.', ''),
            ],
        ];

        return $return;
    }

    private function formatRowsForExport($rows)
    {
        $results = [];
        $count = count($rows);
        for ($i = 0; $i < $count; $i++) {
            $results[$i]['ekate'] = $rows[$i]['ekate'];
            $results[$i]['kad_ident'] = $rows[$i]['kad_ident'];
            $results[$i]['mestnost'] = $rows[$i]['mestnost'];
            $results[$i]['category'] = $rows[$i]['category'];
            $results[$i]['area_type'] = $rows[$i]['area_type'];
            $results[$i]['area'] = $rows[$i]['area'];
            $results[$i]['kvs_allowable_area'] = $rows[$i]['kvs_allowable_area'];
            $results[$i]['c_type'] = $rows[$i]['c_type'];
            $results[$i]['lease_type'] = $rows[$i]['lease_type'];
            $results[$i]['farming_id'] = $rows[$i]['farming_id'];
            $results[$i]['owner_names'] = str_replace('<br />', "\n", $rows[$i]['owner_names']);
            $results[$i]['egn'] = str_replace('<br />', "\n", $rows[$i]['egn']);
            $results[$i]['c_num'] = $rows[$i]['c_num'];
            $results[$i]['sublease_num'] = $rows[$i]['sublease_num'];
            $results[$i]['sv_num'] = $rows[$i]['sv_num'];
            $results[$i]['sv_date'] = $rows[$i]['sv_date'];
            $results[$i]['tom'] = $rows[$i]['tom'];
            $results[$i]['delo'] = $rows[$i]['delo'];
            $results[$i]['na_num'] = $rows[$i]['na_num'];
            $results[$i]['court'] = $rows[$i]['court'];
            $results[$i]['due_date'] = $rows[$i]['due_date'];
            $results[$i]['renta'] = $rows[$i]['renta'];
        }

        return $results;
    }

    private function addTotals(&$rows)
    {
        $i = count($rows);
        $rows[$i]['ekate'] = '';
        $rows[$i]['kad_ident'] = '';
        $rows[$i]['mestnost'] = '';
        $rows[$i]['category'] = '';
        $rows[$i]['area_type'] = '';
        $rows[$i]['area'] = number_format(array_sum(array_column($rows, 'area')), 3);
        $rows[$i]['kvs_allowable_area'] = number_format(array_sum(array_column($rows, 'kvs_allowable_area')), 3);
        $rows[$i]['c_type'] = '';
        $rows[$i]['farming_id'] = '';
        $rows[$i]['owner_names'] = '';
        $rows[$i]['egn'] = '';
        $rows[$i]['c_num'] = '';
        $rows[$i]['lease_type'] = '';
        $rows[$i]['sublease_num'] = '';
        $rows[$i]['sv_num'] = '';
        $rows[$i]['sv_date'] = '';
        $rows[$i]['tom'] = '';
        $rows[$i]['delo'] = '';
        $rows[$i]['na_num'] = '';
        $rows[$i]['court'] = '';
        $rows[$i]['due_date'] = '';
        $rows[$i]['renta'] = '';
    }

    private function getFarmingName($farmings, $farming_id)
    {
        $count = count($farmings);
        for ($i = 0; $i < $count; $i++) {
            if ($farmings[$i]['id'] == $farming_id) {
                return $farmings[$i]['name'];
            }
        }
    }
}
