<?php

namespace TF\Engine\APIClasses\Plots;

use Prado\Prado;
use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\Export2XlsClass;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDbAreaTypes\UserDbAreaTypesController;
use TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController;

// Prado::using('Plugins.Core.UserDbPlots.*');
// Prado::using('Plugins.Core.Users.*');
// Prado::using('Plugins.Core.Users.conf');
// Prado::using('Plugins.Core.Farming.*');
// Prado::using('Plugins.Core.Farming.conf');
/**
 * Справка "Свободна земя за ипотека".
 *
 * @rpc-module Plots
 *
 * @rpc-service-id for-hypothec-plots-report-grid
 */
class ForHypothecPlotsReportGrid extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getPlotsForHypothec'],
                'validators' => [
                    'params' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'exportToExcelForHypothecPlotsReportData' => ['method' => [$this, 'exportToExcelForHypothecPlotsReportData'],
                'validators' => [
                    'params' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * Export To Excel For Hypothec Plots Report Data.
     *
     * @param array $data
     *                    [
     *                    #items array filters
     *                    [
     *                    #item  timestamp report_date
     *                    #item  timestamp report_date_from
     *                    #item  timestamp report_date_as_of
     *                    #item  timestamp report_contract_date
     *                    #item  timestamp report_contract_date_to
     *                    #item  integer report_farming
     *                    #item  integer report_ekate
     *                    #item  integer report_ntp
     *                    #item  integer report_category
     *                    #item  string report_mestnost
     *                    #item  string report_irrigation
     *                    ]
     *                    ]
     * @param int $page
     * @param int $rows
     * @param ?string $sort
     * @param ?string $order
     *
     * @return array
     */
    public function exportToExcelForHypothecPlotsReportData(array $data, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        if (!$page && !$rows) {
            $data['export_all_rows'] = true;
        }
        $results = $this->getPlotsForHypothec($data, $page, $rows, $sort, $order);

        $result = $results['rows'];

        unset($result[0]['attributes']);

        $result = $this->formatRowsForExport($result);

        $columns = [
            'Землище',
            'Идентификатор',
            'Местност',
            'Категория',
            'НТП',
            'Площ(дка)',
            'Договор №',
            'Дата',
            'Нотар. акт №',
            'Том',
            'Дело',
            'Районен съд',
        ];

        $this->addTotals($result);

        $time = strtotime(date('Y-m-d H:i:s'));
        $fileName = 'svobodna_zemq_za_ipoteki_' . $this->User->GroupID . '_' . $time . '.xlsx';
        $path = PUBLIC_UPLOAD_EXPORT . '/' . $fileName;

        $export2Xls = new Export2XlsClass();
        $filePath = $export2Xls->exportUrlPath($path, $result, $columns);

        return ['file_path' => $filePath, 'file_name' => $fileName];
    }

    /**
     * @api-method read
     *
     * @param array $params
     *                      [
     *                      #items array filters
     *                      [
     *                      #item  timestamp report_date
     *                      #item  timestamp report_date_from
     *                      #item  timestamp report_date_as_of
     *                      #item  timestamp report_contract_date
     *                      #item  timestamp report_contract_date_to
     *                      #item  integer report_farming
     *                      #item  integer report_ekate
     *                      #item  integer report_ntp
     *                      #item  integer report_category
     *                      #item  string report_mestnost
     *                      #item  string report_irrigation
     *                      ]
     *                      ]
     * @param int $page
     * @param int $rows
     * @param ?string $sort
     * @param ?string $order
     *
     * @return array
     */
    public function getPlotsForHypothec(array $params, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $reportDateFrom = $params['filters']['report_date_from'];
        $reportDate = $params['filters']['report_date'];
        $reportDateAsOf = $params['filters']['report_date_as_of'];
        $reportContractDate = $params['filters']['report_contract_date'];
        $reportContractDateTo = $params['filters']['report_contract_date_to'];
        $reportFarming = $params['filters']['report_farming'];
        $reportEkate = $params['filters']['report_ekate'];
        $reportMasiv = $params['filters']['report_masiv'];
        $reportPlotNumber = $params['filters']['report_plot_number'];
        $reportKadIdent = $params['filters']['report_kad_ident'];

        $reportNtp = $params['filters']['report_ntp'];
        $reportCategory = $params['filters']['report_category'];
        $reportMestnost = $params['filters']['report_mestnost'];

        if ('all' != $params['filters']['report_irrigation']) {
            $reportIrrigation = $params['filters']['report_irrigation'];
        }

        $reportDateFrom = $reportDateAsOf ? $reportDateAsOf : $reportDateFrom;
        $reportDate = $reportDateAsOf ? $reportDateAsOf : $reportDate;

        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);
        $FarmingController = new FarmingController('Farming');

        $userFarmings = $FarmingController->getUserFarmings();
        $farmingIds = array_keys($userFarmings);

        if ($reportFarming && in_array((int) $reportFarming, $farmingIds)) {
            $farmingIds = [(int) $reportFarming];
        }

        // create the default return
        $return = [
            'rows' => [],
            'total' => 0,
            'footer' => [],
        ];

        $options = [
            'return' => ['DISTINCT(gid)'],
        ];

        $results = $UserDbPlotsController->getHypothecsPlotsReport($options, false, false);
        $resultsCount = count($results);
        $gid_array = [];

        for ($i = 0; $i < $resultsCount; $i++) {
            $gid_array[] = $results[$i]['gid'];
        }

        $options = [
            'custom_counter' => 'COUNT(DISTINCT(cpr_id))',
            'sort' => $sort,
            'order' => $order,
            'return' => [
                'kvs.gid as gid', 'virtual_ekatte_name as land ', 'kad_ident',
                'COALESCE(mestnost, \'-\') as mestnost',
                'COALESCE(virtual_category_title, \'-\') as category',
                'virtual_ntp_title as area_type',
                'contract_area as area',
                'c.id as c_id', 'c.c_num', "to_char(c.start_date,'DD.MM.YYYY') as start_date",
                'COALESCE(c.na_num, \'-\') as na_num',
                'COALESCE(c.tom, \'-\') as tom',
                'COALESCE(c.delo, \'-\') as delo',
                'COALESCE(c.court, \'-\') as court',
            ],
            'where' => [
                'is_sublease' => ['column' => 'is_sublease', 'prefix' => 'c', 'compare' => '=', 'value' => 'false'],
                'is_annnex' => ['column' => 'is_annex', 'prefix' => 'c', 'compare' => '=', 'value' => 'false'],
                'active' => ['column' => 'active', 'prefix' => 'c', 'compare' => '=', 'value' => 'true'],
                'c_date' => ['column' => 'c_date', 'compare' => '>=', 'value' => $reportContractDate],
                'c_date_to' => ['column' => 'c_date', 'compare' => '<=', 'value' => $reportContractDateTo],
                'gid' => ['column' => 'gid', 'compare' => 'NOT IN', 'value' => $gid_array],
                'farming_id' => ['column' => 'farming_id', 'prefix' => 'c', 'compare' => 'IN', 'value' => $farmingIds],
            ],
            'start_date' => $reportDateFrom,
            'due_date' => $reportDate,
        ];

        if ($reportEkate) {
            $options['where']['ekate'] = ['column' => 'ekate', 'prefix' => 'kvs', 'compare' => '=', 'value' => $reportEkate];
        }
        if ($reportNtp) {
            $UserDbAreaTypesController = new UserDbAreaTypesController($this->User->Database);
            $ntpFilter = $UserDbAreaTypesController->getNtpCodesWithAdditionalCodes([$reportNtp]);
            $options['where']['ntp'] = ['column' => 'area_type', 'compare' => 'IN', 'value' => $ntpFilter];
        }
        if ($reportCategory) {
            $categoryFilter = in_array($reportCategory, ['-1', '0', null]) ? ['-1', '0', null] : [$reportCategory];
            $options['where']['category'] = ['column' => 'category', 'compare' => 'IN', 'value' => $categoryFilter];
        }
        if ($reportMestnost) {
            $options['where']['mestnost'] = ['column' => 'mestnost', 'compare' => '=', 'value' => $reportMestnost];
        }

        if ($reportIrrigation) {
            $options['where']['irrigated_area'] = ['column' => 'irrigated_area', 'compare' => '=', 'value' => $reportIrrigation];
        }

        if ($reportMasiv) {
            $options['where']['masiv'] = ['column' => 'masiv', 'prefix' => 'kvs', 'compare' => '=', 'value' => $reportMasiv];
        }
        if ($reportPlotNumber) {
            $options['where']['number'] = ['column' => 'number', 'prefix' => 'kvs', 'compare' => '=', 'value' => $reportPlotNumber];
        }
        if ($reportKadIdent) {
            $options['where']['kad_ident'] = ['column' => 'kad_ident', 'prefix' => 'kvs', 'compare' => '=', 'value' => $reportKadIdent];
        }

        $results_total = $UserDbPlotsController->getOwnPlotsForReport($options, false, false);

        $counter = count($results_total);

        if (0 == $counter) {
            return $return;
        }

        $page = $page ?: 1;
        $rows = $rows ?: 30;

        if (array_key_exists('export_all_rows', $params) && true == $params['export_all_rows']) {
            // export all rows
        } else {
            $options['offset'] = ($page - 1) * $rows;
            $options['limit'] = $rows;
        }

        $results = $UserDbPlotsController->getOwnPlotsForReport($options, false, false);
        $resultsCount = count($results);
        $total_area_for_page = 0;

        for ($i = 0; $i < $resultsCount; $i++) {
            $total_area_for_page += $results[$i]['area'];
            $results[$i]['area'] = number_format($results[$i]['area'], 3, '.', '');

            if ($results[$i]['usable']) {
                $results[$i]['usable'] = 'Да';
            } else {
                $results[$i]['usable'] = 'Не';
            }
        }
        $total_area = 0;
        for ($i = 0; $i < $counter; $i++) {
            $total_area += $results_total[$i]['area'];
        }

        $return['rows'] = $results;
        $return['total'] = $counter;
        $return['footer'] = [
            [
                'area_type' => '<b>Общо за стр.</b>',
                'area' => number_format($total_area_for_page, 3, '.', ''),
            ],
            [
                'area_type' => '<b>Общо.</b>',
                'area' => number_format($total_area, 3, '.', ''),
            ],
        ];

        return $return;
    }

    private function formatRowsForExport($rows)
    {
        $results = [];

        $rowsCount = count($rows);
        for ($i = 0; $i < $rowsCount; $i++) {
            $results[$i]['land'] = $rows[$i]['land'];
            $results[$i]['kad_ident'] = $rows[$i]['kad_ident'];
            $results[$i]['mestnost'] = $rows[$i]['mestnost'];
            $results[$i]['category'] = $rows[$i]['category'];
            $results[$i]['area_type'] = $rows[$i]['area_type'];
            $results[$i]['area'] = $rows[$i]['area'];
            $results[$i]['c_num'] = $rows[$i]['c_num'];
            $results[$i]['start_date'] = $rows[$i]['start_date'];
            $results[$i]['na_num'] = $rows[$i]['na_num'];
            $results[$i]['tom'] = $rows[$i]['tom'];
            $results[$i]['delo'] = $rows[$i]['delo'];
            $results[$i]['court'] = $rows[$i]['court'];
        }

        return $results;
    }

    private function addTotals(&$rows)
    {
        $i = count($rows);
        $rows[$i]['land'] = '';
        $rows[$i]['kad_ident'] = '';
        $rows[$i]['mestnost'] = '';
        $rows[$i]['category'] = '';
        $rows[$i]['area_type'] = '';
        $rows[$i]['area'] = number_format(array_sum(array_column($rows, 'area')), 3);
        $rows[$i]['c_num'] = '';
        $rows[$i]['start_date'] = '';
        $rows[$i]['na_num'] = '';
        $rows[$i]['tom'] = '';
        $rows[$i]['delo'] = '';
        $rows[$i]['court'] = '';
    }
}
