<?php

namespace TF\Engine\APIClasses\Plots;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\Export2XlsClass;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbAreaTypes\UserDbAreaTypesController;
use TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController;
use TF\Engine\Plugins\Core\Users\UsersController;

// Prado::using('Plugins.Core.Farming.conf');
// Prado::using('Plugins.Core.Farming.*');
// Prado::using('Plugins.Core.UserDbPlots.*');
// Prado::using('Plugins.Core.UserDb.*');
// Prado::using('Plugins.Core.UserDb.conf');
// Prado::using('Plugins.Core.Users.*');
// Prado::using('Plugins.Core.Users.conf');

/**
 * Имоти > Справки.
 *
 * @rpc-module Plots
 *
 * @rpc-service-id plots-report-grid
 *
 * @property UserDbController $UserDbController
 * @property UserDbPlotsController $UserDbPlotsController
 * @property FarmingController $FarmingController
 * @property UsersController $UsersController
 */
class PlotReportsGrid extends TRpcApiProvider
{
    private $UserDbController = false;
    private $UserDbPlotsController = false;
    private $FarmingController = false;
    private $UsersController = false;
    private $final_farming = [];
    private $ekatte_names = [];
    private $ekatte_numbers = [];

    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getPlotsReportData'],
                'validators' => [
                    'filterParam' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'exportToExcelPlotsReportData' => ['method' => [$this, 'exportToExcelPlotsReportData'],
                'validators' => [
                    'data' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * Export To Excel Plots Report Data.
     *
     * @param array $data
     *                    {
     *                    #item string kad_ident
     *                    #item string ekate
     *                    #item string masiv
     *                    #item string number
     *                    #item int category
     *                    #item int area_type
     *                    #item string mestnost
     *                    #item string cnum
     *                    #item int contract_type
     *                    #item int farming
     *                    #item date date_from
     *                    #item date date_to
     *                    #item date due_date_from
     *                    #item date due_date_to
     *                    #item string owner_name
     *                    #item string owner_egn
     *                    #item string rep_name
     *                    #item string rep_egn
     *                    #item string company_name
     *                    #item string company_eik
     *                    #item string contract_status
     *                    #item string group_by
     *                    #item string multi_group
     *                    }
     * @param int|string $page pagination parameters
     * @param  string"int    $rows         pagination parameters
     * @param string $sort pagination parameters
     * @param string $order pagination parameters
     *
     * @return array
     */
    public function exportToExcelPlotsReportData(array $data, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $results = $this->getPlotsReportData($data, $page, $rows, $sort, $order);

        $result = $results['rows'];

        unset($result[0]['attributes']);

        $info = $this->getColumns($data, $result);

        $fileName = 'obshta_plosht_' . time() . '.xlsx';
        $path = PUBLIC_UPLOAD_EXPORT . '/' . $this->User->GroupID . '/' . $fileName;

        // Map the $info['results'] to contain only keys that exist in $info['columns']
        $info['results'] = array_map(fn ($row) => array_intersect_key($row, $info['columns']), $info['results']);

        $export2Xls = new Export2XlsClass();

        $filePath = $export2Xls->exportUrlPath($path, $info['results'], $info['columns']);

        return ['file_path' => $filePath, 'file_name' => $fileName];
    }

    /**
     * Gets data for reports.
     *
     * @api-method read
     *
     * @param array $filterParams
     *                            {
     *                            #item string kad_ident
     *                            #item string ekate
     *                            #item string masiv
     *                            #item string number
     *                            #item int category
     *                            #item int area_type
     *                            #item string mestnost
     *                            #item string cnum
     *                            #item int contract_type
     *                            #item int farming
     *                            #item date date_from
     *                            #item date date_to
     *                            #item date due_date_from
     *                            #item date due_date_to
     *                            #item string owner_name
     *                            #item string owner_egn
     *                            #item string rep_name
     *                            #item string rep_egn
     *                            #item string company_name
     *                            #item string company_eik
     *                            #item string contract_status
     *                            #item string group_by
     *                            #item string multi_group
     *                            }
     * @param int|string $page pagination parameters
     * @param int|string $rows pagination parameters
     * @param string $sort pagination parameters
     * @param string $order pagination parameters
     *
     * @return array
     */
    public function getPlotsReportData(array $filterParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        if ($this->User->isGuest) {
            return [];
        }

        $UserDbController = new UserDbController($this->User->Database);
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);
        $FarmingController = new FarmingController('Farming');
        $arrayHelper = $FarmingController->ArrayHelper;

        $this->final_farming = $FarmingController->getUserFarmings(true);
        $userFarmingIds = array_keys($this->final_farming);

        $farmingIds = $arrayHelper->filterEmptyStringArr($filterParams['farming']);
        $farmingIds = isset($farmingIds) && count($farmingIds) > 0
            ? array_intersect($farmingIds, $userFarmingIds)
            : $userFarmingIds;

        date_default_timezone_set('Europe/Sofia');
        $currentDate = date('Y-m-d', time());

        $filterParams['start_date'] = '' != $filterParams['start_date'] ? $filterParams['start_date'] : $currentDate;
        $filterParams['due_date'] = '' != $filterParams['due_date'] ? $filterParams['due_date'] : $currentDate;

        $ekatte_options = [
            'tablename' => 'ekate_combobox',
            'return' => ['*'],
        ];

        $ekatte_results = $UserDbController->getItemsByParams($ekatte_options, false, false);
        foreach ($ekatte_results as $ekatte_result) {
            $this->ekatte_names[$ekatte_result['ekate']] = $ekatte_result;
        }
        $ownPlotsRequested = false;
        foreach ($filterParams['contract_type'] as $filter) {
            if (1 == $filter) {
                $ownPlotsRequested = true;

                break;
            }
        }
        if ('farming' == $sort) {
            $sort = 'farming_id';
        }

        if ('c_type' == $sort) {
            $sort = 'nm_usage_rights';
        }
        $UserDbAreaTypesController = new UserDbAreaTypesController($this->User->Database);
        $ntpFilter = $UserDbAreaTypesController->getNtpCodesWithAdditionalCodes($arrayHelper->filterEmptyStringArr($filterParams['area_type']));
        $categoryFilter = $arrayHelper->filterEmptyStringArr($filterParams['category']);
        $categoryFilter = in_array('0', $categoryFilter) || in_array('-1', $categoryFilter)
            ? array_merge($categoryFilter, [null])
            : $categoryFilter;

        $options = [
            'return' => [
                'array_agg(distinct(cpr.id)) as cpr_ids',
            ],
            'sort' => $sort,
            'order' => $order,
            'where' => [
                // Филтри свързани с имот
                'kad_ident' => ['column' => 'kad_ident', 'compare' => '=', 'prefix' => 'kvs', 'value' => $filterParams['kad_ident']],
                'ekate' => ['column' => 'ekate', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $arrayHelper->filterEmptyStringArr($filterParams['ekate'])],
                'masiv' => ['column' => 'masiv', 'compare' => '=', 'prefix' => 'kvs', 'value' => $filterParams['masiv']],
                'number' => ['column' => 'number', 'compare' => '=', 'prefix' => 'kvs', 'value' => $filterParams['number']],
                'category' => ['column' => 'category', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $categoryFilter],
                'area_type' => ['column' => 'area_type', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $ntpFilter],
                'mestnost' => ['column' => 'mestnost', 'compare' => '=', 'prefix' => 'kvs', 'value' => $filterParams['mestnost']],
                // Филтри свързани с договор
                'cnum' => ['column' => 'c_num', 'compare' => 'LIKE ', 'prefix' => 'c', 'value' => $filterParams['cnum']],
                'contract_type' => ['column' => 'nm_usage_rights', 'compare' => 'IN', 'prefix' => 'c', 'value' => $arrayHelper->filterEmptyStringArr($filterParams['contract_type'])],
                'farming' => ['column' => 'farming_id', 'compare' => 'IN', 'prefix' => 'c', 'value' => $farmingIds],
                'date_from' => ['column' => 'c_date', 'compare' => '>=', 'prefix' => 'c', 'value' => $filterParams['date_from']],
                'date_to' => ['column' => 'c_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $filterParams['date_to']],
                'start_date' => ['column' => 'start_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $filterParams['start_date']],
                'due_date' => ['column' => "(CASE WHEN c.due_date IS NULL THEN '9999-12-31' ELSE (CASE WHEN a.id IS NULL THEN c.due_date ELSE a.due_date END) END)", 'compare' => '>=', 'value' => $filterParams['start_date']],
                // Филтри свързани със собственици
                'owner_egn' => ['column' => 'egn', 'compare' => '=', 'prefix' => 'o', 'value' => $filterParams['owner_egn']],
                'rep_egn' => ['column' => 'rep_egn', 'compare' => '=', 'prefix' => 'o_r', 'value' => $filterParams['rep_egn']],
                'company_name' => ['column' => 'company_name', 'compare' => 'ILIKE', 'prefix' => 'o', 'value' => $filterParams['company_name']],
                'company_eik' => ['column' => 'eik', 'compare' => '=', 'prefix' => 'o', 'value' => $filterParams['company_eik']],

                'is_annex' => ['column' => 'is_annex', 'compare' => '=', 'prefix' => 'c', 'value' => 'false'],
                'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'prefix' => 'c', 'value' => 'false'],
                'annex_action' => ['column' => 'annex_action', 'compare' => '=', 'prefix' => 'cpr', 'value' => 'added'],
            ],
            'start_date' => $filterParams['due_date'],
            'due_date' => $filterParams['start_date'],
            'include_subleases' => $filterParams['include_subleases'],
            'exclude_sold_plots' => $ownPlotsRequested,
        ];
        $grouping = $this->getGrouping($filterParams);

        $options['group'] = $grouping['group'];

        array_push($options['return'], $grouping['return']);
        $options['return'] = array_filter($options['return']);
        if (1 == $filterParams['contract_status']) {
            $options['where']['active'] = ['column' => 'active', 'compare' => '=', 'value' => 'FALSE', 'prefix' => 'c'];
        }

        if (2 == $filterParams['contract_status']) {
            $options['where']['due_date'] = ['column' => "(CASE WHEN c.due_date IS NULL THEN '9999-12-31' ELSE (CASE WHEN a.id IS NULL THEN c.due_date ELSE a.due_date END) END)", 'compare' => '>=', 'value' => $filterParams['start_date']];
            $options['where']['active'] = ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'TRUE'];
        }

        if (3 == $filterParams['contract_status']) {
            $options['where']['due_date'] = ['column' => "(CASE WHEN c.due_date IS NULL THEN '9999-12-31' ELSE (CASE WHEN a.id IS NULL THEN c.due_date ELSE a.due_date END) END)", 'compare' => '<', 'value' => $filterParams['start_date']];
            unset($options['where']['start_date']);
            $options['where']['active'] = ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'TRUE'];
        }

        // check to see whether irrigation should be considered
        if ($filterParams['irrigated_area'] && 'all' != $filterParams['irrigated_area']) {
            $options['where']['irrigated_area'] = ['column' => 'irrigated_area', 'compare' => '=', 'prefix' => 'kvs', 'value' => ($filterParams['irrigated_area'])];
        }

        if ($filterParams['owner_name']) {
            $tmp_owner_names = preg_replace('/\s+/', '.*', $filterParams['owner_name']);
            $tmp_owner_names = mb_strtolower($tmp_owner_names, 'UTF-8');
            array_push($options['return'], "regexp_matches(lower(TRIM (o.NAME)) || ' ' || lower(TRIM (o.surname)) || ' ' || lower(TRIM (o.lastname)), '{$tmp_owner_names}','g')");
        }

        if ($filterParams['rep_name']) {
            $tmp_rep_names = preg_replace('/\s+/', '.*', $filterParams['rep_name']);
            $tmp_rep_names = mb_strtolower($tmp_rep_names, 'UTF-8');
            array_push($options['return'], "regexp_matches(lower(TRIM (o_r.name)) || ' ' || lower(TRIM (o_r.surname)) || ' ' || lower(TRIM (o_r.lastname)), '{$tmp_rep_names}','g')");
        }

        if ($filterParams['ime_subekt']) {
            $tmp_subekt_names = preg_replace('/\s+/', '.*', $filterParams['ime_subekt']);
            $tmp_subekt_names = mb_strtolower($tmp_subekt_names, 'UTF-8');
            array_push($options['return'], "regexp_matches(lower(TRIM (tkvs.ime_subekt)), '{$tmp_subekt_names}','g')");
        }

        if ($filterParams['egn_subekt']) {
            $tmp_subekt_egn = preg_replace('/\s+/', '.*', $filterParams['egn_subekt']);
            $tmp_subekt_egn = mb_strtolower($tmp_subekt_egn, 'UTF-8');
            array_push($options['return'], "regexp_matches(lower(TRIM (tkvs.egn_subekt)), '{$tmp_subekt_egn}','g')");
        }

        if ($filterParams['participation']) {
            switch ($filterParams['participation']) {
                case 'participate':
                    $options['where']['participate'] = ['column' => 'participate', 'compare' => '=', 'prefix' => 'kvs', 'value' => 'true'];
                    $options['where']['include'] = ['column' => 'include', 'compare' => '=', 'prefix' => 'kvs', 'value' => 'false'];
                    $options['where']['white_spots'] = ['column' => 'white_spots', 'compare' => '=', 'prefix' => 'kvs', 'value' => 'false'];

                    break;
                case 'no_participate':
                    $options['where']['participate'] = ['column' => 'participate', 'compare' => '=', 'prefix' => 'kvs', 'value' => 'false'];
                    $options['where']['include'] = ['column' => 'include', 'compare' => '=', 'prefix' => 'kvs', 'value' => 'true'];
                    $options['where']['white_spots'] = ['column' => 'white_spots', 'compare' => '=', 'prefix' => 'kvs', 'value' => 'false'];

                    break;
                case 'without':
                    $options['where']['participate'] = ['column' => 'participate', 'compare' => '=', 'prefix' => 'kvs', 'value' => 'false'];
                    $options['where']['include'] = ['column' => 'include', 'compare' => '=', 'prefix' => 'kvs', 'value' => 'false'];
                    $options['where']['white_spots'] = ['column' => 'white_spots', 'compare' => '=', 'prefix' => 'kvs', 'value' => 'false'];

                    break;
                default:
                    break;
            }
        }
        // total
        $results_total = $UserDbPlotsController->getDataForTotalAreaReport($options, false, false);

        $totalCount = count($results_total);
        $formattedResults = $this->formatResults($results_total, $grouping['groupingType']);
        $total_ids = $formattedResults['cpr_ids'];
        $total_ids = array_map('intval', $total_ids);

        if ($filterParams['forMap']) {
            return $total_ids;
        }

        if (!$totalCount) {
            return ['rows' => [], 'total' => 0, 'footer' => [['ekate' => '<b>ОБЩО</b>']]];
        }

        $options['offset'] = ($page - 1) * $rows;
        $options['limit'] = $rows;

        $results = $UserDbPlotsController->getDataForTotalAreaReport($options, false, false);

        $totalFormattedResults = $this->formatResults($results_total, $grouping['groupingType']);

        $formattedResults = $this->formatResults($results, $grouping['groupingType']);

        $results = $formattedResults['results'];
        $pageIDS = $formattedResults['cpr_ids'];
        $page_ekattes = [];
        $final = [];
        foreach ($results as $result) {
            array_push($final, $result);
            array_push($page_ekattes, $result['ekate_code']);
        }

        $pageContractArea = 0;
        $pageDocumentArea = 0;

        foreach ($final as $row) {
            $pageContractArea += $row['contract_area'];
            $pageDocumentArea += $row['document_area'];
        }

        $totalContractArea = 0;
        $totalDocumentArea = 0;
        foreach ($totalFormattedResults['results'] as $row) {
            $totalContractArea += $row['contract_area'];
            $totalDocumentArea += $row['document_area'];
        }

        if (!$filterParams['group_by_masiv']) {
            $totalDocumentArea = $this->getEkateAreaForEKATTE($this->ekatte_numbers);
            $totalDocumentArea = $totalDocumentArea[0]['document_area'];
            $pageDocumentArea = $this->getEkateAreaForEKATTE($page_ekattes);
            $pageDocumentArea = $pageDocumentArea[0]['document_area'];
        }

        $columns = $this->getColumnNamesForGrid($filterParams);

        $footer = [
            [
                'ekate' => '<b>ОБЩО за стр.</b>',
                'contract_area' => number_format($pageContractArea, 3, '.', ''),
                'document_area' => number_format($pageDocumentArea, 3, '.', ''),
                'unused_area' => number_format($pageDocumentArea - $pageContractArea, 3, '.', ''),
            ],
            [
                'ekate' => '<b>ОБЩО</b>',
                'contract_area' => number_format($totalContractArea, 3, '.', ''),
                'document_area' => number_format($totalDocumentArea, 3, '.', ''),
                'unused_area' => number_format($totalDocumentArea - $totalContractArea, 3, '.', ''),
            ],
        ];

        return ['rows' => $final, 'total' => $totalCount, 'footer' => $footer, 'columns' => $columns];
    }

    protected function getColumnNamesForGrid($filterParams)
    {
        $ekate = $filterParams['group_by_ekatte'];
        $farming = $filterParams['group_by_farming'];
        $masiv = $filterParams['group_by_maisv'];
        $contractType = $filterParams['group_by_contract_type'];
        $withContract = $filterParams['group_by_with_contract'];
        $columns = [[]];
        if ($ekate) {
            $tmpCol = [];
            $tmpCol['field'] = 'ekate';
            $tmpCol['title'] = '<b>ЕКАТТЕ</b>';
            $tmpCol['sortable'] = true;
            $tmpCol['width'] = 20;
            $columns[0][] = $tmpCol;
        }

        if ($masiv) {
            $tmpCol = [];
            $tmpCol['field'] = 'masiv';
            $tmpCol['title'] = '<b>Масив</b>';
            $tmpCol['sortable'] = true;
            $tmpCol['width'] = 20;
            $columns[0][] = $tmpCol;
        }
        if ($farming) {
            $tmpCol = [];
            $tmpCol['field'] = 'farming';
            $tmpCol['title'] = '<b>Стопанство</b>';
            $tmpCol['sortable'] = true;
            $tmpCol['width'] = 20;
            $columns[0][] = $tmpCol;
        }

        if ($contractType) {
            $tmpCol = [];
            $tmpCol['field'] = 'c_type';
            $tmpCol['title'] = '<b>Тип на дог.</b>';
            $tmpCol['sortable'] = true;
            $tmpCol['width'] = 20;
            $columns[0][] = $tmpCol;
        }
        unset($tmpCol);
        $tmpCol = [];
        $tmpCol['field'] = 'contract_area';
        $tmpCol['title'] = '<b>Площ по</br>дог. (дка)</b>';
        $tmpCol['sortable'] = false;
        $tmpCol['width'] = 20;
        $columns[0][] = $tmpCol;

        $tmpCol = [];
        $tmpCol['field'] = 'document_area';
        $tmpCol['title'] = '<b>Площ по</br>док. (дка)</b>';
        $tmpCol['sortable'] = false;
        $tmpCol['width'] = 20;
        $columns[0][] = $tmpCol;

        if ($withContract) {
            $tmpCol = [];
            $tmpCol['field'] = 'unused_area';
            $tmpCol['title'] = '<b>Свободна</br>площ (дка)</b>';
            $tmpCol['sortable'] = false;
            $tmpCol['width'] = 20;
            $columns[0][] = $tmpCol;
        }

        return $columns;
    }

    private function getColumns($filterParams, $results)
    {
        $reportType = $this->getGroupingType($filterParams);
        $resultsCount = count($results);

        $columns = [];
        switch ($reportType) {
            case 1:
                $columns['ekate'] = 'Землище';
                $columns['contract_area'] = 'Площ по дог.(дка)';
                $columns['document_area'] = 'Площ по док.(дка)';
                $columns['unused_area'] = 'Свободна площ(дка)';

                for ($i = 0; $i < $resultsCount; $i++) {
                    unset($results[$i]['ekate_code']);
                }

                break;
            case 2:
                $columns['ekate'] = 'Землище';
                $columns['c_type'] = 'Тип на дог.';
                $columns['contract_area'] = 'Площ по дог.(дка)';
                $columns['document_area'] = 'Площ по док.(дка)';

                for ($i = 0; $i < $resultsCount; $i++) {
                    unset($results[$i]['ekate_code']);
                }

                break;
            case 3:
                $columns['ekate'] = 'Землище';
                $columns['masiv'] = 'Масив';
                $columns['contract_area'] = 'Площ по дог.(дка)';
                $columns['document_area'] = 'Площ по док.(дка)';
                $columns['unused_area'] = 'Свободна площ(дка)';

                for ($i = 0; $i < $resultsCount; $i++) {
                    unset($results[$i]['ekate_code']);
                }

                break;
            case 4:
                $columns['ekate'] = 'Землище';
                $columns['masiv'] = 'Масив';
                $columns['c_type'] = 'Тип на дог.';
                $columns['contract_area'] = 'Площ по дог.(дка)';
                $columns['document_area'] = 'Площ по док.(дка)';

                for ($i = 0; $i < $resultsCount; $i++) {
                    unset($results[$i]['ekate_code']);
                }

                break;
            case 5:
                $columns['ekate'] = 'Землище';
                $columns['farming'] = 'Стопанство';
                $columns['contract_area'] = 'Площ по дог.(дка)';
                $columns['document_area'] = 'Площ по док.(дка)';
                $columns['unused_area'] = 'Свободна площ(дка)';

                for ($i = 0; $i < $resultsCount; $i++) {
                    unset($results[$i]['ekate_code'], $results[$i]['farming_id']);
                }

                break;
            case 6:
                $columns['ekate'] = 'Землище';
                $columns['farming'] = 'Стопанство';
                $columns['c_type'] = 'Тип на дог.';
                $columns['contract_area'] = 'Площ по дог.(дка)';
                $columns['document_area'] = 'Площ по док.(дка)';

                for ($i = 0; $i < $resultsCount; $i++) {
                    unset($results[$i]['ekate_code'], $results[$i]['farming_id']);
                }

                break;
            case 7:
                $columns['ekate'] = 'Землище';
                $columns['masiv'] = 'Масив';
                $columns['farming'] = 'Стопанство';
                $columns['contract_area'] = 'Площ по дог.(дка)';
                $columns['document_area'] = 'Площ по док.(дка)';
                $columns['unused_area'] = 'Свободна площ(дка)';

                for ($i = 0; $i < $resultsCount; $i++) {
                    unset($results[$i]['ekate_code'], $results[$i]['farming_id']);
                }

                break;
            case 8:
                $columns['ekate'] = 'Землище';
                $columns['masiv'] = 'Масив';
                $columns['farming'] = 'Стопанство';
                $columns['c_type'] = 'Тип на дог.';
                $columns['contract_area'] = 'Площ по дог.(дка)';
                $columns['document_area'] = 'Площ по док.(дка)';

                for ($i = 0; $i < $resultsCount; $i++) {
                    unset($results[$i]['ekate_code'], $results[$i]['farming_id']);
                }

                break;
            default:
                $columns['ekate'] = 'Землище';
                $columns['contract_area'] = 'Площ по дог.(дка)';
                $columns['document_area'] = 'Площ по док.(дка)';
                $columns['unused_area'] = 'Свободна площ(дка)';

                break;
        }

        return ['columns' => $columns, 'results' => $results];
    }

    private function getGrouping($filterParams)
    {
        $ekate = $filterParams['group_by_ekatte'];
        $farming = $filterParams['group_by_farming'];
        $masiv = $filterParams['group_by_maisv'];
        $contractType = $filterParams['group_by_contract_type'];
        $withContract = $filterParams['group_by_with_contract'];

        $groupBy = [];
        $return = [];
        $groupingType = $this->getGroupingType($filterParams);
        if ($ekate) {
            array_push($groupBy, 'kvs.ekate');
            array_push($return, 'kvs.ekate');
        }
        if ($farming) {
            array_push($groupBy, 'c.farming_id');
            array_push($return, 'c.farming_id');
        }
        if ($masiv) {
            array_push($groupBy, 'kvs.masiv');
            array_push($return, 'kvs.masiv');
        }
        if ($contractType) {
            array_push($groupBy, 'c.nm_usage_rights');
            array_push($return, 'c.nm_usage_rights');
        }
        $ownersRequired = $this->checkForOwnersReq($filterParams);
        if ($ownersRequired) {
            array_push($groupBy, 'o.id');
        }
        $repsRequired = $this->checkForRepsReq($filterParams);
        if ($repsRequired) {
            array_push($groupBy, 'o_r.id');
        }
        $tkvsRequired = $this->checkForTKVSReq($filterParams);
        if ($tkvsRequired) {
            array_push($groupBy, 'tkvs.egn_subekt');
        }

        return ['return' => implode(',', array_filter($return)), 'group' => implode(',', array_filter($groupBy)), 'groupingType' => $groupingType];
    }

    private function checkForOwnersReq($params)
    {
        if ($params['owner_name']) {
            return true;
        }
        if ($params['owner_egn']) {
            return true;
        }
        if ($params['company_name']) {
            return true;
        }

        return (bool) ($params['company_eik']);
    }

    private function checkForRepsReq($params)
    {
        if ($params['rep_name']) {
            return true;
        }

        return (bool) ($params['rep_egn']);
    }

    private function checkForTKVSReq($params)
    {
        if ($params['ime_subekt']) {
            return true;
        }

        return (bool) ($params['egn_subekt']);
    }

    private function getGroupingType($filterParams)
    {
        $ekate = $filterParams['group_by_ekatte'];
        $farming = $filterParams['group_by_farming'];
        $masiv = $filterParams['group_by_maisv'];
        $contractType = $filterParams['group_by_contract_type'];
        $withContract = $filterParams['group_by_with_contract'];

        if ($ekate && $withContract && !$farming && !$masiv && !$contractType) {
            return 1;
        }
        if ($ekate && $contractType && !$withContract && !$farming && !$masiv) {
            return 2;
        }
        if ($ekate && $masiv && $withContract && !$farming && !$contractType) {
            return 3;
        }
        if ($ekate && $masiv && $contractType && !$withContract && !$farming) {
            return 4;
        }
        if ($ekate && $withContract && $farming && !$masiv && !$contractType) {
            return 5;
        }
        if ($ekate && $farming && $contractType && !$withContract && !$masiv) {
            return 6;
        }
        if ($ekate && $withContract && $farming && $masiv && !$contractType) {
            return 7;
        }
        if ($ekate && !$withContract && $farming && $masiv && $contractType) {
            return 8;
        }

        return 1;
    }

    private function formatResults($results, $groupingType)
    {
        switch ($groupingType) {
            case 1:
                return $this->formatResultsEkateWithoutContract($results);

                break;
            case 2:
                return $this->formatResultsEkateContractType($results);

                break;
            case 3:
                return $this->formatResultsEkateMasivWithoutContract($results);

                break;
            case 4:
                return $this->formatResultsEkateMasivContractType($results);

                break;
            case 5:
                return $this->formatResultsEkateFarmingWithoutContract($results);

                break;
            case 6:
                return $this->formatResultsEkateFarmingContractType($results);

                break;
            case 7:
                return $this->formatResultsEkateMasivFarmingWithoutContract($results);

                break;
            case 8:
                return $this->formatResultsEkateMasivFarmingContractType($results);

                break;
            default:
                return $this->formatResultsEkateWithoutContract($results);

                break;
        }
    }

    private function formatResultsEkateWithoutContract($results)
    {
        $return = [];
        $total_ids = [];
        foreach ($results as $result) {
            $ids = explode(',', trim($result['cpr_ids'], '{}'));
            if (!$return[$result['ekate']]['cpr_ids']) {
                $return[$result['ekate']]['cpr_ids'] = [];
            }
            $return[$result['ekate']]['cpr_ids'] = array_merge($return[$result['ekate']]['cpr_ids'], $ids);
            $total_ids = array_merge($total_ids, $ids);
        }
        $total_ids = array_unique($total_ids);

        foreach ($return as $key => $value) {
            array_push($this->ekatte_numbers, $key);
            $ekateAreas = $this->getAreasForEkate($key);
            $return[$key]['ekate_code'] = (string) $key;
            $return[$key]['ekate'] = (string) $this->ekatte_names[$key]['ekatte_name'];
            $return[$key]['contract_area'] = $this->getContractAreaForCPRIds($value['cpr_ids']);
            $return[$key]['document_area'] = (float)$ekateAreas[0]['document_area'];
            $return[$key]['unused_area'] = number_format($return[$key]['document_area'] - $return[$key]['contract_area'], 3, '.', '');
            unset($return[$key]['cpr_ids']);
        }

        return ['results' => $return, 'cpr_ids' => $total_ids];
    }

    private function formatResultsEkateContractType($results)
    {
        $return = [];
        $total_ids = [];
        foreach ($results as $result) {
            $ids = explode(',', trim($result['cpr_ids'], '{}'));
            if (!$return[$result['ekate']][$result['nm_usage_rights']]['cpr_ids']) {
                $return[$result['ekate']][$result['nm_usage_rights']]['cpr_ids'] = [];
            }
            $return[$result['ekate']][$result['nm_usage_rights']]['cpr_ids'] = array_merge($return[$result['ekate']][$result['nm_usage_rights']]['cpr_ids'], $ids);
            $total_ids = array_merge($total_ids, $ids);
        }
        $total_ids = array_unique($total_ids);
        $finalResults = [];
        foreach ($return as $key => $value) {
            array_push($this->ekatte_numbers, $key);
            foreach ($value as $newKey => $newValue) {
                $ekateAreas = $this->getAreasForEkate($key);
                $return[$key][$newKey]['ekate_code'] = $key;
                $return[$key][$newKey]['ekate'] = $this->ekatte_names[$key]['ekatte_name'];
                $return[$key][$newKey]['c_type'] = $GLOBALS['Contracts']['ContractTypes'][(int)$newKey]['name'];
                $return[$key][$newKey]['contract_area'] = $this->getContractAreaForCPRIds($newValue['cpr_ids']);
                $return[$key][$newKey]['document_area'] = (float)$ekateAreas[0]['document_area'];
                $return[$key][$newKey]['unused_area'] = number_format($return[$key][$newKey]['document_area'] - $return[$key][$newKey]['contract_area'], 3, '.', '');
                unset($return[$key][$newKey]['cpr_ids']);
                array_push($finalResults, $return[$key][$newKey]);
            }
        }

        return ['results' => $finalResults, 'cpr_ids' => $total_ids];
    }

    private function formatResultsEkateMasivWithoutContract($results)
    {
        $return = [];
        $total_ids = [];
        foreach ($results as $result) {
            $ids = explode(',', trim($result['cpr_ids'], '{}'));

            if (!$return[$result['ekate']][$result['masiv']]['cpr_ids']) {
                $return[$result['ekate']][$result['masiv']]['cpr_ids'] = [];
            }
            $return[$result['ekate']][$result['masiv']]['cpr_ids'] = array_merge($return[$result['ekate']][$result['masiv']]['cpr_ids'], $ids);
            $total_ids = array_merge($total_ids, $ids);
        }
        $total_ids = array_unique($total_ids);
        $finalResults = [];
        foreach ($return as $ekate => $ekate_values) {
            foreach ($ekate_values as $masiv => $masiv_values) {
                array_push($this->ekatte_numbers, $ekate);
                $return[$ekate][$masiv]['ekate_code'] = $ekate;
                $return[$ekate][$masiv]['ekate'] = $this->ekatte_names[$ekate]['ekatte_name'];
                $return[$ekate][$masiv]['masiv'] = (int)$masiv;
                $return[$ekate][$masiv]['contract_area'] = $this->getContractAreaForCPRIds($masiv_values['cpr_ids']);
                $return[$ekate][$masiv]['document_area'] = $this->getMasivArea($ekate, $masiv);
                $return[$ekate][$masiv]['unused_area'] = number_format($return[$ekate][$masiv]['document_area'] - $return[$ekate][$masiv]['contract_area'], 3, '.', '');
                unset($return[$ekate][$masiv]['cpr_ids']);
                array_push($finalResults, $return[$ekate][$masiv]);
            }
        }

        return ['results' => $finalResults, 'cpr_ids' => $total_ids];
    }

    private function formatResultsEkateMasivContractType($results)
    {
        $return = [];
        $total_ids = [];
        foreach ($results as $result) {
            $ids = explode(',', trim($result['cpr_ids'], '{}'));
            if (!$return[$result['ekate']][$result['nm_usage_rights']]['cpr_ids']) {
                $return[$result['ekate']][$result['masiv']][$result['nm_usage_rights']]['cpr_ids'] = [];
            }
            $return[$result['ekate']][$result['masiv']][$result['nm_usage_rights']]['cpr_ids'] = array_merge($return[$result['ekate']][$result['masiv']][$result['nm_usage_rights']]['cpr_ids'], $ids);
            $total_ids = array_merge($total_ids, $ids);
        }
        $total_ids = array_unique($total_ids);
        $finalResults = [];

        foreach ($return as $ekate => $ekate_values) {
            array_push($this->ekatte_numbers, $ekate);
            foreach ($ekate_values as $masiv => $masiv_values) {
                foreach ($masiv_values as $c_type => $c_values) {
                    $return[$ekate][$masiv][$c_type]['ekate_code'] = $ekate;
                    $return[$ekate][$masiv][$c_type]['ekate'] = $this->ekatte_names[$ekate]['ekatte_name'];
                    $return[$ekate][$masiv][$c_type]['masiv'] = (int)$masiv;
                    $return[$ekate][$masiv][$c_type]['c_type'] = $GLOBALS['Contracts']['ContractTypes'][(int)$c_type]['name'];
                    $return[$ekate][$masiv][$c_type]['contract_area'] = $this->getContractAreaForCPRIds($c_values['cpr_ids']);
                    $return[$ekate][$masiv][$c_type]['document_area'] = $this->getMasivArea($ekate, $masiv);
                    $return[$ekate][$masiv][$c_type]['unused_area'] = number_format($return[$ekate][$masiv][$c_type]['document_area'] - $return[$ekate][$masiv][$c_type]['contract_area'], 3, '.', '');
                    unset($return[$ekate][$masiv][$c_type]['cpr_ids']);
                    array_push($finalResults, $return[$ekate][$masiv][$c_type]);
                }
            }
        }

        return ['results' => $finalResults, 'cpr_ids' => $total_ids];
    }

    private function formatResultsEkateFarmingWithoutContract($results)
    {
        $return = [];
        $total_ids = [];
        foreach ($results as $result) {
            $ids = explode(',', trim($result['cpr_ids'], '{}'));
            if (!$return[$result['ekate']][$result['farming_id']]['cpr_ids']) {
                $return[$result['ekate']][$result['farming_id']]['cpr_ids'] = [];
            }
            $return[$result['ekate']][$result['farming_id']]['cpr_ids'] = array_merge($return[$result['ekate']][$result['farming_id']]['cpr_ids'], $ids);
            $total_ids = array_merge($total_ids, $ids);
        }
        $total_ids = array_unique($total_ids);
        $finalResults = [];
        foreach ($return as $ekate => $ekate_values) {
            array_push($this->ekatte_numbers, $ekate);
            foreach ($ekate_values as $stop => $stop_values) {
                $ekateAreas = $this->getAreasForEkate($ekate);
                $return[$ekate][$stop]['ekate_code'] = $ekate;
                $return[$ekate][$stop]['ekate'] = $this->ekatte_names[$ekate]['ekatte_name'];
                $return[$ekate][$stop]['farming'] = $this->final_farming[(int)$stop]['name'];
                $return[$ekate][$stop]['contract_area'] = $this->getContractAreaForCPRIds($stop_values['cpr_ids']);
                $return[$ekate][$stop]['document_area'] = (float)$ekateAreas[0]['document_area'];
                $return[$ekate][$stop]['farming_id'] = (int)$stop;
                $return[$ekate][$stop]['unused_area'] = number_format($return[$ekate][$stop]['document_area'] - $return[$ekate][$stop]['contract_area'], 3, '.', '');
                unset($return[$ekate][$stop]['cpr_ids']);
                array_push($finalResults, $return[$ekate][$stop]);
            }
        }

        return ['results' => $finalResults, 'cpr_ids' => $total_ids];
    }

    private function formatResultsEkateFarmingContractType($results)
    {
        $return = [];
        $total_ids = [];
        foreach ($results as $result) {
            $ids = explode(',', trim($result['cpr_ids'], '{}'));
            if (!$return[$result['ekate']][$result['farming_id']][$result['nm_usage_rights']]['cpr_ids']) {
                $return[$result['ekate']][$result['farming_id']][$result['nm_usage_rights']]['cpr_ids'] = [];
            }
            $return[$result['ekate']][$result['farming_id']][$result['nm_usage_rights']]['cpr_ids'] = array_merge($return[$result['ekate']][$result['farming_id']][$result['nm_usage_rights']]['cpr_ids'], $ids);
            $total_ids = array_merge($total_ids, $ids);
        }
        $total_ids = array_unique($total_ids);
        $finalResults = [];
        foreach ($return as $ekate => $ekate_values) {
            array_push($this->ekatte_numbers, $ekate);
            foreach ($ekate_values as $farming_id => $farming_values) {
                foreach ($farming_values as $c_type => $c_values) {
                    $ekateAreas = $this->getAreasForEkate($ekate);
                    $return[$ekate][$farming_id][$c_type]['ekate_code'] = $ekate;
                    $return[$ekate][$farming_id][$c_type]['ekate'] = $this->ekatte_names[$ekate]['ekatte_name'];
                    $return[$ekate][$farming_id][$c_type]['contract_area'] = $this->getContractAreaForCPRIds($c_values['cpr_ids']);
                    $return[$ekate][$farming_id][$c_type]['document_area'] = (float)$ekateAreas[0]['document_area'];
                    $return[$ekate][$farming_id][$c_type]['farming_id'] = (int)$farming_id;
                    $return[$ekate][$farming_id][$c_type]['farming'] = $this->final_farming[(int)$farming_id]['name'];
                    $return[$ekate][$farming_id][$c_type]['c_type'] = $GLOBALS['Contracts']['ContractTypes'][(int)$c_type]['name'];
                    $return[$ekate][$farming_id][$c_type]['unused_area'] = number_format($return[$ekate][$farming_id][$c_type]['document_area'] - $return[$ekate][$farming_id][$c_type]['contract_area'], 3, '.', '');
                    unset($return[$ekate][$farming_id][$c_type]['cpr_ids']);
                    array_push($finalResults, $return[$ekate][$farming_id][$c_type]);
                }
            }
        }

        return ['results' => $finalResults, 'cpr_ids' => $total_ids];
    }

    private function formatResultsEkateMasivFarmingWithoutContract($results)
    {
        $return = [];
        $total_ids = [];
        foreach ($results as $result) {
            $ids = explode(',', trim($result['cpr_ids'], '{}'));
            if (!$return[$result['ekate']][$result['masiv']][$result['farming_id']]['cpr_ids']) {
                $return[$result['ekate']][$result['masiv']][$result['farming_id']]['cpr_ids'] = [];
            }
            $return[$result['ekate']][$result['masiv']][$result['farming_id']]['cpr_ids'] = array_merge($return[$result['ekate']][$result['masiv']][$result['farming_id']]['cpr_ids'], $ids);
            $total_ids = array_merge($total_ids, $ids);
        }

        $total_ids = array_unique($total_ids);
        $finalResults = [];
        foreach ($return as $ekate => $ekate_values) {
            array_push($this->ekatte_numbers, $ekate);
            foreach ($ekate_values as $masiv => $masiv_values) {
                foreach ($masiv_values as $farming_id => $farming_values) {
                    $return[$ekate][$masiv][$farming_id]['ekate_code'] = $ekate;
                    $return[$ekate][$masiv][$farming_id]['ekate'] = $this->ekatte_names[$ekate]['ekatte_name'];
                    $return[$ekate][$masiv][$farming_id]['masiv'] = $masiv;
                    $return[$ekate][$masiv][$farming_id]['farming'] = $this->final_farming[(int)$farming_id]['name'];
                    $return[$ekate][$masiv][$farming_id]['contract_area'] = $this->getContractAreaForCPRIds($farming_values['cpr_ids']);
                    $return[$ekate][$masiv][$farming_id]['document_area'] = $this->getMasivArea($ekate, $masiv);
                    $return[$ekate][$masiv][$farming_id]['farming_id'] = (int)$farming_id;
                    $return[$ekate][$masiv][$farming_id]['unused_area'] = number_format($return[$ekate][$masiv][$farming_id]['document_area'] - $return[$ekate][$masiv][$farming_id]['contract_area'], 3, '.', '');
                    unset($return[$ekate][$masiv][$farming_id]['cpr_ids']);
                    array_push($finalResults, $return[$ekate][$masiv][$farming_id]);
                }
            }
        }

        return ['results' => $finalResults, 'cpr_ids' => $total_ids];
    }

    private function formatResultsEkateMasivFarmingContractType($results)
    {
        $return = [];
        $total_ids = [];
        foreach ($results as $result) {
            $ids = explode(',', trim($result['cpr_ids'], '{}'));
            if (!$return[$result['ekate']][$result['masiv']][$result['farming_id']][$result['nm_usage_rights']]['cpr_ids']) {
                $return[$result['ekate']][$result['masiv']][$result['farming_id']][$result['nm_usage_rights']]['cpr_ids'] = [];
            }
            $return[$result['ekate']][$result['masiv']][$result['farming_id']][$result['nm_usage_rights']]['cpr_ids'] = array_merge($return[$result['ekate']][$result['masiv']][$result['farming_id']][$result['nm_usage_rights']]['cpr_ids'], $ids);
            $total_ids = array_merge($total_ids, $ids);
        }
        $total_ids = array_unique($total_ids);
        $finalResults = [];
        foreach ($return as $ekate => $ekate_values) {
            array_push($this->ekatte_numbers, $ekate);
            foreach ($ekate_values as $masiv => $masiv_values) {
                foreach ($masiv_values as $farming_id => $farming_values) {
                    foreach ($farming_values as $c_type => $c_values) {
                        $return[$ekate][$masiv][$farming_id][$c_type]['ekate_code'] = $ekate;
                        $return[$ekate][$masiv][$farming_id][$c_type]['ekate'] = $this->ekatte_names[$ekate]['ekatte_name'];
                        $return[$ekate][$masiv][$farming_id][$c_type]['masiv'] = $masiv;
                        $return[$ekate][$masiv][$farming_id][$c_type]['farming'] = $this->final_farming[(int)$farming_id]['name'];
                        $return[$ekate][$masiv][$farming_id][$c_type]['c_type'] = $GLOBALS['Contracts']['ContractTypes'][(int)$c_type]['name'];
                        $return[$ekate][$masiv][$farming_id][$c_type]['contract_area'] = $this->getContractAreaForCPRIds($c_values['cpr_ids']);
                        $return[$ekate][$masiv][$farming_id][$c_type]['document_area'] = $this->getMasivArea($ekate, $masiv);
                        $return[$ekate][$masiv][$farming_id][$c_type]['farming_id'] = (int)$farming_id;
                        $return[$ekate][$masiv][$farming_id][$c_type]['unused_area'] = number_format($return[$ekate][$masiv][$farming_id][$c_type]['document_area'] - $return[$ekate][$masiv][$farming_id][$c_type]['contract_area'], 3, '.', '');
                        unset($return[$ekate][$masiv][$farming_id][$c_type]['cpr_ids']);
                        array_push($finalResults, $return[$ekate][$masiv][$farming_id][$c_type]);
                    }
                }
            }
        }

        return ['results' => $finalResults, 'cpr_ids' => $total_ids];
    }

    private function getContractAreaForCPRIds($ids)
    {
        $UserDbController = new UserDbController($this->User->Database);

        $ids = array_map('intval', $ids);
        $options = [
            'tablename' => $UserDbController->DbHandler->contractsPlotsRelTable . ' as cpr',
            'return' => [
                'round(SUM(round(coalesce(cpr.contract_area, 0)::numeric, 3))::numeric - SUM(round(coalesce(scpr.contract_area_for_sale, 0)::numeric))::numeric, 3) as contract_area',
            ],
            'leftjoin' => [
                'table' => ' su_sales_contracts_plots_rel scpr ',
                'condition' => ' on scpr.pc_rel_id = cpr.id ',
            ],
            'where' => [
                'ids' => ['column' => 'cpr.id', 'compare' => 'IN', 'value' => $ids],
            ],
        ];
        $area_results = $UserDbController->getItemsByParams($options, false, false);
        if ($area_results['0']['contract_area']) {
            return $area_results['0']['contract_area'];
        }

        return 0;
    }

    private function getMasivArea($ekate, $masiv)
    {
        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableKVS,
            'return' => [
                'round(SUM(case when document_area is null OR document_area = 0 then ST_AREA(geom)/1000 else document_area end)::numeric,3) as document_area',
            ],
            'where' => [
                'ekate' => ['column' => 'ekate', 'compare' => '=', 'value' => $ekate],
                'masiv' => ['column' => 'masiv', 'compare' => '=', 'value' => $masiv],
                'is_edited' => ['column' => 'is_edited', 'compare' => '=', 'value' => 'false'],
            ],
        ];
        $area_results = $UserDbController->getItemsByParams($options, false, false);

        if ($area_results['0']['document_area']) {
            return $area_results['0']['document_area'];
        }

        return 0;
    }

    private function getAreasForEkate($ekate)
    {
        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableKVS,
            'return' => [
                'round(SUM(case when document_area is null OR document_area = 0 then ST_AREA(geom)/1000 else document_area end)::numeric,3) as document_area',
            ],
            'where' => [
                'ekate' => ['column' => 'ekate', 'compare' => '=', 'value' => (string) $ekate],
                'is_edited' => ['column' => 'is_edited', 'compare' => '=', 'value' => 'false'],
            ],
        ];

        return $UserDbController->getItemsByParams($options);
    }

    private function getEkateAreaForEKATTE($ekates)
    {
        $UserDbController = new UserDbController($this->User->Database);

        $ekateCount = count($ekates);
        for ($i = 0; $i < $ekateCount; $i++) {
            $ekates[$i] = $ekates[$i] . '';
        }
        $ekates = array_filter($ekates);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableKVS,
            'return' => [
                'round(SUM(case when document_area is null OR document_area = 0 then ST_AREA(geom)/1000 else document_area end)::numeric,3) as document_area',
            ],
            'where' => [
                'ekate' => ['column' => 'ekate', 'compare' => 'IN', 'value' => $ekates],
                'is_edited' => ['column' => 'is_edited', 'compare' => '=', 'value' => 'false'],
            ],
        ];

        return $UserDbController->getItemsByParams($options, false, false);
    }
}
