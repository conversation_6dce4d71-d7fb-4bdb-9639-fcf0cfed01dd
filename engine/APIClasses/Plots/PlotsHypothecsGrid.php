<?php

namespace TF\Engine\APIClasses\Plots;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDbHypothecs\UserDbHypothecsController;

// Prado::using('Plugins.Core.UserDbHypothecs.*');

/**
 * @rpc-module Plots
 *
 * @rpc-service-id plots-hypothecs-datagrid
 */
class PlotsHypothecsGrid extends TRpcApiProvider
{
    protected $return = [
        'rows' => [],
        'total' => 0,
    ];

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getPlotsHypothecs'],
                'validators' => [
                    'plotId' => 'validateInteger',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateText',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * getPlotsHypothecs Справка "Ипотеки и тежести".
     *
     * @param int $plotId
     *                    [
     *
     * @items array filters
     *     [
     *
     * @item  timestamp report_date
     * @item  integer report_farming
     * @item  integer report_ekate
     * @item  integer report_ntp
     * @item  integer report_category
     * @item  string report_mestnost
     *     ]
     * ]
     *
     * @param int|string $page
     * @param int|string $rows
     * @param ?string $sort
     * @param ?string $order
     *
     * @return array
     */
    public function getPlotsHypothecs(int $plotId, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $UserDbHypothecsController = new UserDbHypothecsController($this->User->Database);

        if (!$plotId) {
            return $this->return;
        }

        $options = [
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
            'return' => ['h.*', 'c.name as creditor', 'round(ph.hypothec_area::numeric,3) as hypothec_area'],
            'where' => [
                'plot_id' => ['prefix' => 'p', 'column' => 'gid', 'compare' => '=', 'value' => $plotId],
            ],
        ];

        $counter = $UserDbHypothecsController->getHypothecs($options, true, false);
        if (0 == $counter[0]['count']) {
            return $this->return;
        }

        $results = $UserDbHypothecsController->getHypothecs($options, false, false);

        return [
            'rows' => $results,
            'total' => $counter[0]['count'],
        ];
    }
}
