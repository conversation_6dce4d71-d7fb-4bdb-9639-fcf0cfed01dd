<?php

namespace TF\Engine\APIClasses\Plots;

use Exception;
use Prado\Exceptions\TException;
use Prado\Web\Services\TRpcApiProvider;
use TF\Application\Common\Config;
use TF\Application\Entity\LayerStyles;
use TF\Application\Entity\UserLayers;
use TF\Engine\Kernel\ArrayHelper;
use TF\Engine\Plugins\Core\Contracts\CollectionsController;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbAreaTypes\UserDbAreaTypesController;
use TF\Engine\Plugins\Core\UserDbContracts\UserDbContractsController;
use TF\Engine\Plugins\Core\UserDbOwners\UserDbOwnersController;
use TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController;
use TF\Engine\Plugins\Core\UserDbZPlots\UserDbZPlotsController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * The PlotsTreeClass is the class responsible for displaying all plots in the plots tree,
 * associated with the current user, editing single plot, editing multiple plots.
 * The class allows filtering the displayed plots, which meet different filter cireria.
 *
 * @rpc-module Plots
 *
 * @rpc-service-id plots-tree
 */

/**
 * Дърво с имоти.
 *
 * @rpc-module Plots
 *
 * @rpc-service-id plots-tree
 *
 * @property LayersController $LayersController
 * @property UserDbController $UserDbController
 * @property UserDbZPlotsController $UserDbZPlotsController
 * @property CollectionsController $CollectionsController
 * @property CollectionsController $CollectionsController
 * @property FarmingController $FarmingController
 * @property UsersController $UsersController
 * @property UserDbPlotsController $UserDbPlotsController
 * @property UserDbOwnersController $UserDbOwnersController
 */
class PlotsTree extends TRpcApiProvider
{
    public const CONTRACT_STATUS_CANCELLED = 1;
    public const CONTRACT_STATUS_ACTIVE = 2;
    public const CONTRACT_STATUS_EXPIRED = 3;

    public const PLOT_STATUS_ACTIVE = 'Active';
    public const PLOT_STATUS_ARCHIVED = 'Archived';
    public const KVS_MAX_FILTERED_GIDS_COUNT = 5000;
    /**
     * Class Controllers
     * Define all class controllers as properties, which will be set, depending on the required method.
     */
    private $LayersController;
    private $UserDbController;
    private $UserDbZPlotsController;
    private $ContractsController;
    private $FarmingController;
    private $UsersController;
    private $UserDbPlotsController;
    private $UserDbOwnersController;

    private $module = 'Plots';
    private $service_id = 'plots-tree';

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getPlots'],
                'validators' => [
                    'filterParam' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'getTreePlots' => ['method' => [$this, 'getTreePlots'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'load' => ['method' => [$this, 'loadPlotForEdit'],
                'validators' => [
                    'editPlotID' => 'validateInteger, validateRequired, validateNotNull',
                ],
            ],
            'update' => ['method' => [$this, 'updatePlotValues'],
                'validators' => [
                    'params' => [
                        'ekate' => 'validateDigitsOnly,validateRequired, validateNotNull',
                        'masiv' => 'validateDigitsOnly,validateRequired, validateNotNull',
                        'number' => 'validateDigitsOnly,validateRequired, validateNotNull',
                        'document_area' => 'validateNumber',
                        'mestnost' => 'validateText',
                        'category' => 'validateInteger',
                        'area_type' => 'validateInteger',
                        'area_farming' => 'validateInteger',
                        'area_year' => 'validateFarmingYear',
                    ],
                ],
            ],
            'multiEdit' => ['method' => [$this, 'executeMultiEdit'],
                'validators' => [
                    'params' => [
                        'mestnost' => 'validateText',
                    ],
                ],
            ],
        ];
    }

    /**
     * Make a database request to get the plots that meet the search criteria, set by the filter parameters ($filterParams).
     *
     * @api-method read
     *
     * @param array $filterParams {
     *                            #item array area_type
     *                            {
     *                            #items string
     *                            }
     *                            #item string mestnost
     *                            #item array category {
     *                            #items string
     *                            }
     *                            #item string cnum
     *                            #item string company_eik
     *                            #item string company_name
     *                            #item string contract_status
     *                            #item array contract_type {
     *                            #items string
     *                            }
     *                            #item string date_from
     *                            #item string date_to
     *                            #item string due_date_from
     *                            #item string due_date_to
     *                            #item array ekate {
     *                            #items string
     *                            }
     *                            #item array farming {
     *                            #items string
     *                            }
     *                            #item bool is_edited
     *                            #item string kad_ident
     *                            #item string masiv
     *                            #item string number
     *                            #item string owner_egn
     *                            #item string owner_name
     *                            #item string rep_egn
     *                            #item string rep_name
     *                            #item boolean forKvsGrid
     *                            }
     * @param string $page
     * @param string $rows
     * @param bool $getPointPolygon
     * @param ?string $sort
     * @param ?string $order
     *
     * @throws TException
     *
     * @return array
     */
    public function getPlots(array $filterParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '', $getPointPolygon = false, $hasFilterGroups = false, $returnFiltredGids = false, string $groupBy = null)
    {
        if ($this->User->isGuest) {
            return [];
        }

        $this->UserDbPlotsController = new UserDbPlotsController($this->User->Database);
        $layer = UserLayers::getLayerByTableName($this->UserDbPlotsController->DbHandler->tableKVS, $this->User->GroupID);

        $this->initializeControllers('getPlots');

        if (!empty($filterParams['is_edited'])) {
            $filterParams['is_edited'] = filter_var($filterParams['is_edited'], FILTER_VALIDATE_BOOLEAN);
        }

        if (isset($filterParams['from_alert'], $_SESSION['alert_filters']) && 1 == $filterParams['from_alert'] && array_key_exists('owner_less_contracts', $_SESSION['alert_filters']) && !empty($_SESSION['alert_filters']['owner_less_contracts'])) {
            $filterParams['plot_id'] = implode(',', $_SESSION['alert_filters']['owner_less_contracts']);
        }

        [$firstFilterGroup] = $filterParams['filter_params']['groups'] ?? [[]];
        [$ekatte] = $firstFilterGroup['ekate'] ?? [null];
        [$layerStyles] = $ekatte
            ? array_values(
                array_filter(
                    $layer->getStyles(),
                    function (LayerStyles $style) use ($layer, $ekatte) {
                        return $style->layer_id === $layer->id . '_' . $ekatte;
                    }
                )
            )
            : [null];

        $groupingOptions = $this->buildGroupingOptions($filterParams, $groupBy, $sort, $order);
        $select = $this->buildSelect($groupingOptions, $groupBy, $layerStyles, $layer);
        $sort = $this->buildSort($layer, $sort, $order, $groupBy);

        $options = [
            'tablename' => 'layer_kvs',
            'group' => $groupingOptions['group'],
            'sort' => $sort,
            'return' => $select,
            'group_id' => $this->User->GroupID,
        ];

        if ($getPointPolygon && !$groupBy) {
            $options['return'][] = 'ST_ASTEXT(kvs.geom)';
        }

        if ($filterParams['fromMobile']) {
            $pos = array_search('ST_ASTEXT(kvs.geom)', $options['return']);
            if ($pos > -1) {
                $options['return'][$pos] = 'ST_ASTEXT(ST_transform(kvs.geom,3857))';
            }
        }

        // adding limit for pagination
        // define page limit
        $page_limit = 50;

        $forKvsGrid = $filterParams['forKvsGrid'] ?? null;
        if ($forKvsGrid) {
            $page_limit = $rows;
            unset($filterParams['forKvsGrid']);
        }

        $joins = $this->determineJoinsByFilters($hasFilterGroups ? $filterParams['filter_params']['groups'] : [$filterParams], !empty($filterParams['filter_params']['full_text_search']));
        $options['joins'] = array_values($joins['readyForUse']);

        if (true === $hasFilterGroups) {
            $isForSubleaseFarmYearsExist = $this->checkFilterExist($filterParams['filter_params']['groups'], 'for_sublease_farm_years');
            $isExpiringContractsForFarmYearExist = $this->checkFilterExist($filterParams['filter_params']['groups'], 'expiring_contracts_for_farm_year');
            $isFarmYearActiveContractPlotsExist = $this->checkFilterExist($filterParams['filter_params']['groups'], 'farm_year_active_contract_plots');

            if ($isForSubleaseFarmYearsExist || $isExpiringContractsForFarmYearExist || $isFarmYearActiveContractPlotsExist) {
                $options['cte'] = $this->getSubleasedPlotsCte($filterParams['filter_params']['groups']);
            }

            if (count($joins['buildJoins'])) {
                $options['joins'] = [...$options['joins'], ...$this->buildJoins($joins['buildJoins'], $filterParams['filter_params']['groups'])];
            }

            $filterOptions = $this->buildWhere($filterParams['filter_params']['groups']);
        } else {
            $filterOptions = $this->buildWhere([$filterParams]);
        }

        if (count($filterOptions)) {
            if (true === $hasFilterGroups) {
                $options['whereOrGroup'] = $filterOptions;
            } else {
                $options['where'] = current($filterOptions)['where'];
            }
        }

        if ($filterParams['filter_params']['plot_statuses']) {
            $plotStatusesFilter = self::getPlotStatusesFilter($filterParams['filter_params']['plot_statuses']);

            if (count($plotStatusesFilter) > 0) {
                if (!empty($options['whereOrGroup'])) {
                    foreach ($options['whereOrGroup'] as &$filter) {
                        $filter['where'] = array_merge($filter['where'] ?? [], $plotStatusesFilter);
                    }
                } else {
                    $options['whereOrGroup'][]['where'] = $plotStatusesFilter;
                }
            }
        }

        if ($filterParams['gids']) {
            $options['where']['gids'] = ['column' => 'gid', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $filterParams['gids']];
        }

        $this->buildFullTextSearchFilterGroup($options, $filterParams);

        $options['offset'] = ($page - 1) * $page_limit;
        $options['offset'] = $options['offset'] >= 0 ? $options['offset'] : 0;
        $options['limit'] = $page_limit;

        if ($filterParams['getAllPlots']) {
            unset($options['offset'], $options['limit']);
        }

        $results = $this->UserDbPlotsController->getPlotsData($options, false);
        $totalData = $this->getPlotsTotalData($layer, $options, $groupingOptions);

        if ($groupBy) {
            return $this->getGroupedPlotsResult($layer, $results, $totalData, $groupBy);
        }

        $resultsCount = count($results);
        if (!$results[0]['full_count']) {
            return [];
        }

        if ($returnFiltredGids) {
            return $totalData['filtered_gids'];
        }

        if (!empty($forKvsGrid)) {
            return [
                'rows' => $results,
                'total' => $totalData['count'],
                'filteredGids' => $totalData['filtered_gids'],
                'filtered_gids' => $totalData['filtered_gids'],
                'total_used_area' => $totalData['used_area'],
                'total_area_kvs' => $totalData['area_kvs'],
                'total_document_area' => $totalData['document_area'],
                'total_allowable_area' => $totalData['allowable_area'],
            ];
        }

        // get all ekate data
        $ekateData = $this->UsersController->getAllEkatteData();
        $ekateNames = [];

        foreach ($ekateData as $ekate) {
            $ekateNames[$ekate['ekatte_code']] = $ekate['ekatte_name'];
        }

        $options = [
            'return' => ['*'],
            'where' => [
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'value' => $this->User->GroupID],
            ],
            'sort' => 'id',
            'order' => 'asc',
        ];

        for ($i = 0; $i < $resultsCount; $i++) {
            // Помощен метод, с който се премахва идентификатора от името на собственика,
            // което идва от материализираното view за етикети на собствениците от
            // тематичните карти по данни от ОСЗ. Там имената се пазят като стринг и имената на отделнтие
            // собственици са разделени със ',' и винаги като последен елемент стои идентификатора на имота,
            // за да бъде визуализиран и той, когато се гледа тематичната карта.
            $results[$i]['ime_subekt'] = $this->getOSZOwner($results[$i]['ime_subekt']);

            $results[$i]['has_contracts'] = (false == $results[$i]['has_contracts']) ? 'Не' : 'Да';
            $results[$i]['area_type_id'] = $results[$i]['area_type'];

            $results[$i]['land'] = $ekateNames[$results[$i]['ekate']];

            $results[$i]['area_type'] = $results[$i]['virtual_ntp_title'];
            $results[$i]['area_kvs'] = number_format($results[$i]['area_kvs'] / 1000, 3);
            $results[$i]['used_area'] = number_format($results[$i]['used_area'], 3);

            if (!$results[$i]['document_area']) {
                $results[$i]['document_area'] = $results[$i]['area_kvs'];
            } else {
                $results[$i]['document_area'] = number_format($results[$i]['document_area'], 3);
            }

            $return['data'][] = [
                'text' => $results[$i]['kad_ident'],
                'id' => $results[$i]['gid'],
                'attributes' => $results[$i],
                'iconCls' => 'icon-tree-edit-geometry',
            ];
        }
        // add attribute to first listed element of three for custom pagination
        // information for total can not be sent otherwise
        $return['data'][0]['attributes']['pagination']['total'] = $totalData['count'];
        $return['data'][0]['attributes']['pagination']['limit'] = (int) $page_limit;

        $return['info']['filteredGids'] = $totalData['filtered_gids'];

        return $return;
    }

    public function buildFullTextSearchFilterGroup(array &$options, array $filterParams)
    {
        $search = $filterParams['filter_params']['full_text_search'] ?? '';

        if (
            !strlen($search)
        ) {
            return;
        }

        $fullTextSearchOptions = [
            'full_text_search_kad_ident' => ['column' => '"kad_ident"::TEXT', 'compare' => 'ILIKE', 'prefix' => 'kvs', 'value' => $search],
            'full_text_search_masiv' => ['column' => '"masiv"::TEXT', 'compare' => 'ILIKE', 'prefix' => 'kvs', 'value' => $search],
            'full_text_search_number' => ['column' => '"number"::TEXT', 'compare' => 'ILIKE', 'prefix' => 'kvs', 'value' => $search],
            'full_text_search_mestnost' => ['column' => '"mestnost"::TEXT', 'compare' => 'ILIKE', 'prefix' => 'kvs', 'value' => $search],
            'full_text_search_is_edited' => ['column' => '"is_edited"::TEXT', 'compare' => 'ILIKE', 'prefix' => 'kvs', 'value' => $search],
            'full_text_search_block' => ['column' => '"block"::TEXT', 'compare' => 'ILIKE', 'prefix' => 'kvs', 'value' => $search],
            'full_text_search_allowable_type' => ['column' => '"allowable_type"::TEXT', 'compare' => 'ILIKE', 'prefix' => 'kvs', 'value' => $search],
            'full_text_search_c_num' => ['column' => '"c_num"::TEXT', 'compare' => 'ILIKE', 'prefix' => 'c', 'value' => $search],
            'full_text_search_c_date' => ['column' => '"c_date"::TEXT', 'compare' => 'ILIKE', 'prefix' => 'c', 'value' => $search],
            'full_text_search_due_date' => ['column' => '"due_date"::TEXT', 'compare' => 'ILIKE', 'prefix' => 'c', 'value' => $search],
            'full_text_search_start_date' => ['column' => '"start_date"::TEXT', 'compare' => 'ILIKE', 'prefix' => 'c', 'value' => $search],
        ];

        $options['whereOr'] = array_merge($options['whereOr'] ?? [], $fullTextSearchOptions);
    }

    public function buildWhere($groups)
    {
        $UserDbAreaTypesController = new UserDbAreaTypesController($this->User->Database);
        $FarmingController = new FarmingController('Farming');
        $arrayHelper = new ArrayHelper();
        $currentDate = date('Y-m-d', time());
        $groupOptions = [];

        $filtersRequiringFarmingPermissions = [
            'cnum',
            'cnum_exact',
            'nm_usage_rights',
            'virtual_contract_type',
            'farming_id',
            'farming',
            'farming_name',
            'c_date',
            'due_date',
            'start_date',
            'date_from',
            'date_to',
            'active',
            'egn',
            'owner_egn',
            'person_egn',
            'heritor_egn',
            'rep_egn',
            'company_name',
            'eik',
            'name',
            'person_name',
            'owner_name',
            'heritor_name',
            'surname',
            'lastname',
            'company_eik',
            'owner_multi_field_search',
            'rep_name',
            'contract_status',
            'contract_status_text',
            'for_sublease_farm_years',
            'farm_year_active_contract_plots',
            'expiring_contracts_for_farm_year',
        ];

        $userFarmings = $FarmingController->getUserFarmings();
        $allowedFarmingIds = array_keys($userFarmings);

        foreach ($groups as $key => $filterParams) {
            $applyFarmingPermissionsFilter = array_reduce(
                $filtersRequiringFarmingPermissions,
                fn ($carry, $filterKey) => $carry || isset($filterParams[$filterKey]),
                false
            );

            if ($applyFarmingPermissionsFilter) {
                // If any of the filters requiring farming permissions is set, filter the allowed farming ids
                $filterParams['farming'] = isset($filterParams['farming']) && count($filterParams['farming']) > 0
                    ? array_intersect($filterParams['farming'], $allowedFarmingIds)
                    : [...$allowedFarmingIds, null];
            }

            if (isset($filterParams['owner_multi_field_search'])) {
                $commonValue = $filterParams['owner_multi_field_search'];

                $filterParams['sobst_ime'] ??= $commonValue;
                $filterParams['person_name'] ??= $commonValue;
                $filterParams['osz_ime_subekt'] ??= $commonValue;
            }

            $ntpFilter = [];
            if (!empty($filterParams['area_type'])) {
                $ntpFilter = $UserDbAreaTypesController->getNtpCodesWithAdditionalCodes($filterParams['area_type']);
            }

            $options = [
                'where' => [
                    // plots filter
                    "ekate{$key}" => [
                        'column' => 'ekate',
                        'compare' => is_array($filterParams['ekate']) ? 'IN' : '=',
                        'prefix' => 'kvs',
                        'value' => $arrayHelper->filterEmptyStringArr($filterParams['ekate']) ?? '',
                    ],
                    "kad_ident{$key}" => [
                        'column' => 'kad_ident',
                        'compare' => is_array($filterParams['kad_ident']) ? 'IN' : '=',
                        'prefix' => 'kvs',
                        'value' => $filterParams['kad_ident'] ?? '',
                    ],
                    "masiv{$key}" => [
                        'column' => 'masiv',
                        'compare' => is_array($filterParams['masiv']) ? 'IN' : '=',
                        'prefix' => 'kvs',
                        'value' => $filterParams['masiv'] ?? '',
                    ],
                    "number{$key}" => [
                        'column' => 'number',
                        'compare' => is_array($filterParams['number']) ? 'IN' : '=',
                        'prefix' => 'kvs',
                        'value' => $filterParams['number'] ?? '',
                    ],
                    "area_type{$key}" => [
                        'column' => 'area_type',
                        'compare' => is_array($ntpFilter) ? 'IN' : '=',
                        'prefix' => 'kvs',
                        'value' => $ntpFilter,
                    ],
                    "virtual_ntp_title{$key}" => [
                        'column' => 'virtual_ntp_title',
                        'compare' => is_array($filterParams['virtual_ntp_title']) ? 'IN' : 'ILIKE',
                        'value' => $filterParams['virtual_ntp_title'] ?? '',
                    ],
                    "mestnost{$key}" => [
                        'column' => 'mestnost',
                        'compare' => is_array($filterParams['mestnost']) ? 'IN' : '=',
                        'prefix' => 'kvs',
                        'value' => $filterParams['mestnost'] ?? '',
                    ],
                    "block{$key}" => [
                        'column' => 'block',
                        'compare' => is_array($filterParams['block']) ? 'IN' : '=',
                        'prefix' => 'kvs',
                        'value' => $filterParams['block'] ?? '',
                    ],
                    "allowable_type{$key}" => [
                        'column' => 'allowable_type',
                        'compare' => is_array($filterParams['allowable_type']) ? 'IN' : '=',
                        'prefix' => 'kvs',
                        'value' => $filterParams['allowable_type'] ?? '',
                    ],
                    "virtual_category_title{$key}" => [
                        'column' => 'virtual_category_title',
                        'compare' => is_array($filterParams['virtual_category_title']) ? 'IN' : '=',
                        'prefix' => 'kvs',
                        'value' => $filterParams['virtual_category_title'] ?? '',
                    ],
                    "comment{$key}" => [
                        'column' => 'comment',
                        'compare' => is_array($filterParams['comment']) ? 'IN' : 'ILIKE',
                        'prefix' => 'kvs',
                        'value' => $filterParams['comment'] ?? '',
                    ],

                    // contracts filter
                    "cnum{$key}" => [
                        'column' => 'c_num',
                        'compare' => is_array($filterParams['cnum']) ? 'IN' : 'ILIKE',
                        'prefix' => 'c',
                        'value' => $filterParams['cnum'] ?? '',
                    ],
                    "contract_type{$key}" => [
                        'column' => 'nm_usage_rights',
                        'compare' => is_array($filterParams['contract_type']) ? 'IN' : 'ILIKE',
                        'prefix' => 'c',
                        'value' => $arrayHelper->filterEmptyStringArr($filterParams['contract_type']) ?? '',
                    ],
                    "virtual_contract_type{$key}" => [
                        'column' => 'virtual_contract_type',
                        'compare' => is_array($filterParams['virtual_contract_type']) ? 'IN' : 'ILIKE',
                        'prefix' => 'c',
                        'value' => $filterParams['virtual_contract_type'] ?? '',
                    ],
                    "contract_status_text{$key}" => [
                        'column' => 'get_contract_status(c.id, c.active, c.start_date, c.due_date)::VARCHAR',
                        'compare' => is_array($filterParams['contract_status_text']) ? 'IN' : '=',
                        'value' => $filterParams['contract_status_text'] ?? '',
                    ],
                    "farming{$key}" => [
                        'column' => 'farming_id',
                        'compare' => is_array($filterParams['farming']) ? 'IN' : 'ILIKE',
                        'prefix' => 'c',
                        'value' => $arrayHelper->filterEmptyStringArr($filterParams['farming']) ?? '',
                    ],
                    "farming_name{$key}" => [
                        'column' => 'farming_name',
                        'compare' => is_array($filterParams['farming_name']) ? 'IN' : 'ILIKE',
                        'prefix' => 'c',
                        'value' => $filterParams['farming_name'] ?? '',
                    ],
                    "date_from{$key}" => [
                        'column' => 'c_date',
                        'compare' => '>=',
                        'prefix' => 'c',
                        'value' => $filterParams['date_from'] ?? '',
                    ],
                    "date_to{$key}" => [
                        'column' => 'c_date',
                        'compare' => '<=',
                        'prefix' => 'c',
                        'value' => $filterParams['date_to'] ?? '',
                    ],
                    "due_date{$key}" => [
                        'column' => "(CASE WHEN c.due_date IS NULL THEN '9999-01-01' ELSE c.due_date END)",
                        'compare' => '<=',
                        'value' => $filterParams['due_date'] ?? '',
                    ],
                    "start_date{$key}" => [
                        'column' => 'start_date',
                        'compare' => '>=',
                        'prefix' => 'c',
                        'value' => $filterParams['start_date'] ?? '',
                    ],

                    // owners filter
                    "egn{$key}" => [
                        'column' => 'egn',
                        'compare' => is_array($filterParams['owner_egn']) ? 'IN' : '=',
                        'prefix' => 'o',
                        'value' => $filterParams['owner_egn'] ?? '',
                    ],
                    "rep_egn{$key}" => [
                        'column' => 'rep_egn',
                        'compare' => is_array($filterParams['rep_egn']) ? 'IN' : '=',
                        'prefix' => 'o_r',
                        'value' => $filterParams['rep_egn'] ?? '',
                    ],
                    "company_name{$key}" => [
                        'column' => 'company_name',
                        'compare' => is_array($filterParams['company_name']) ? 'IN' : 'ILIKE',
                        'prefix' => 'o',
                        'value' => $filterParams['company_name'] ?? '',
                    ],
                    "company_eik{$key}" => [
                        'column' => 'eik',
                        'compare' => is_array($filterParams['company_eik']) ? 'IN' : '=',
                        'prefix' => 'o',
                        'value' => $filterParams['company_eik'] ?? '',
                    ],
                ],
            ];

            if (array_key_exists('plot_statuses', $filterParams)) {
                $options['where']["is_edited{$key}"] = [
                    'column' => 'get_kvs_plot_status(kvs.is_edited, kvs.edit_active_from)',
                    'compare' => is_array($filterParams['plot_statuses']) ? 'IN' : '=',
                    'value' => $filterParams['plot_statuses'],
                ];
            }

            $categoris = $filterParams['category'] ?? [];
            if (count($categoris) && '' !== $categoris[0]) {
                if (in_array('-1', $categoris)) {
                    // Logic when selected category option is without category
                    if (count($categoris) > 1) {
                        $categoris = array_diff($categoris, ['-1']);
                        $categoris = "'" . implode("','", $categoris) . "'";

                        $options['where']["category{$key}"] = [
                            'column' => "(category in ({$categoris}) or category is null)",
                            'compare' => '=',
                            'value' => true,
                        ];
                    } else {
                        $options['where']["category{$key}"] = [
                            'column' => 'category',
                            'compare' => 'IS',
                            'value' => 'null',
                        ];
                    }
                } else {
                    $options['where']["category{$key}"] = [
                        'column' => 'category',
                        'compare' => 'IN',
                        'value' => $categoris,
                    ];
                }
            }

            if ($plots = $arrayHelper->filterEmptyStringArr(explode(',', $filterParams['plot_id']))) {
                $options['where']["plot_id{$key}"] = ['column' => 'gid', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $plots];

                if (1 === count($plots)) {
                    $options['where']["plot_id{$key}"] = ['column' => 'gid', 'compare' => '=', 'prefix' => 'kvs', 'value' => $plots[0]];
                }
            }

            if (!empty($filterParams['cnum_exact'])) {
                $options['where']["cnum{$key}"] = ['column' => 'c_num', 'compare' => '=', 'prefix' => 'c', 'value' => $filterParams['cnum'] ?? ''];
            }

            if ($filterParams['osz_ime_subekt']) {
                // OSZ files plots
                $options['whereOr']['osz_ime_subekt'] = [
                    'column' => 'lower(tkvs.owner_name)',
                    'compare' => is_array($filterParams['osz_ime_subekt']) ? 'IN' : '=',
                    'value' => array_map(fn ($item) => mb_strtolower($item, 'UTF-8'), $filterParams['osz_ime_subekt']) ?? '',
                ];
            }

            if ($filterParams['person_name']) {
                if (is_array($filterParams['person_name'])) {
                    $options['whereOr']["owner_name{$key}"] = [
                        'column' => "lower(TRIM (o.name)) || ' ' || lower(TRIM (o.surname)) || ' ' || lower(TRIM (o.lastname))",
                        'compare' => 'IN',
                        'value' => array_map(fn ($item) => mb_strtolower($item, 'UTF-8'), $filterParams['person_name']),
                    ];
                    $options['whereOr']["rep_names{$key}"] = [
                        'column' => "lower(TRIM (o_r.rep_name)) || ' ' || lower(TRIM (o_r.rep_surname)) || ' ' || lower(TRIM (o_r.rep_lastname))",
                        'compare' => 'IN',
                        'value' => array_map(fn ($item) => mb_strtolower($item, 'UTF-8'), $filterParams['person_name']),
                    ];
                } else {
                    $tmp_person_names = preg_replace('/\s+/', '.*', $filterParams['person_name']);
                    $tmp_person_names = mb_strtolower($tmp_person_names, 'UTF-8');
                    $options['whereOr']["owner_name{$key}"] = [
                        'column' => "lower(TRIM (o.name)) || ' ' || lower(TRIM (o.surname)) || ' ' || lower(TRIM (o.lastname))",
                        'compare' => '~',
                        'value' => $tmp_person_names,
                    ];
                    $options['whereOr']["rep_names{$key}"] = [
                        'column' => "lower(TRIM (o_r.rep_name)) || ' ' || lower(TRIM (o_r.rep_surname)) || ' ' || lower(TRIM (o_r.rep_lastname))",
                        'compare' => '~',
                        'value' => $tmp_person_names,
                    ];
                }
            }

            if ($filterParams['person_egn']) {
                $options['whereOr']["owner_egn{$key}"] = [
                    'column' => 'egn',
                    'compare' => is_array($filterParams['person_egn']) ? 'IN' : 'ILIKE',
                    'prefix' => 'o',
                    'value' => $filterParams['person_egn'] ?? '',
                ];
                $options['whereOr']["rep_egn{$key}"] = [
                    'column' => 'rep_egn',
                    'compare' => is_array($filterParams['person_egn']) ? 'IN' : 'ILIKE',
                    'prefix' => 'o_r',
                    'value' => $filterParams['person_egn'] ?? '',
                ];
            }

            if ($filterParams['owner_name']) {
                if (is_array($filterParams['owner_name'])) {
                    $options['where']["owner_names{$key}"] = [
                        'column' => "lower(TRIM (o.name)) || ' ' || lower(TRIM (o.surname)) || ' ' || lower(TRIM (o.lastname))",
                        'compare' => 'IN',
                        'value' => array_map(fn ($item) => mb_strtolower($item, 'UTF-8'), $filterParams['owner_name']),
                    ];
                } else {
                    $tmp_owner_names = preg_replace('/\s+/', '.*', $filterParams['owner_name']);
                    $tmp_owner_names = mb_strtolower($tmp_owner_names, 'UTF-8');
                    $options['where']["owner_names{$key}"] = [
                        'column' => "lower(TRIM (o.name)) || ' ' || lower(TRIM (o.surname)) || ' ' || lower(TRIM (o.lastname))",
                        'compare' => '~',
                        'value' => $tmp_owner_names,
                    ];
                }
            }

            if (isset($filterParams['heritor_name']) && '' != $filterParams['heritor_name']) {
                if (is_array($filterParams['heritor_name'])) {
                    $options['where']["owner_names{$key}"] = [
                        'column' => "lower(TRIM (o.name)) || ' ' || lower(TRIM (o.surname)) || ' ' || lower(TRIM (o.lastname))",
                        'compare' => 'IN',
                        'value' => array_map(fn ($item) => mb_strtolower($item, 'UTF-8'), $filterParams['owner_name']),
                    ];
                } else {
                    $tmp_heritor_name = trim(preg_replace('/\\s+/', ' ', $filterParams['heritor_name']));
                    $tmp_heritor_name = mb_strtolower($tmp_heritor_name, 'UTF-8');
                    $options['where']["owner_names{$key}"] = ['column' => "lower(TRIM (o.name)) || ' ' || lower(TRIM (o.surname)) || ' ' || lower(TRIM (o.lastname))", 'compare' => '~', 'value' => $tmp_heritor_name];
                }
                $options['where']["is_heritor{$key}"] = ['column' => 'is_heritor', 'compare' => '=', 'prefix' => 'po', 'value' => 'TRUE'];
            }

            if (isset($filterParams['heritor_egn']) && '' != $filterParams['heritor_egn']) {
                $options['where']["egn{$key}"] = [
                    'column' => 'egn',
                    'compare' => is_array($filterParams['heritor_egn']) ? 'IN' : 'ILIKE',
                    'prefix' => 'o',
                    'value' => $filterParams['heritor_egn'] ?? '',
                ];
                $options['where']["is_heritor{$key}"] = ['column' => 'is_heritor', 'compare' => '=', 'prefix' => 'po', 'value' => 'TRUE'];
            }

            if ($filterParams['rep_name']) {
                if (is_array($filterParams['rep_name'])) {
                    $options['where']["rep_names{$key}"] = [
                        'column' => "lower(TRIM (o_r.rep_name)) || ' ' || lower(TRIM (o_r.rep_surname)) || ' ' || lower(TRIM (o_r.rep_lastname))",
                        'compare' => 'IN',
                        'value' => array_map(fn ($item) => mb_strtolower($item, 'UTF-8'), $filterParams['rep_name']),
                    ];
                } else {
                    $tmp_rep_names = preg_replace('/\s+/', '.*', $filterParams['rep_name']);
                    $tmp_rep_names = mb_strtolower($tmp_rep_names, 'UTF-8');
                    $options['where']["rep_names{$key}"] = [
                        'column' => "lower(TRIM (o_r.rep_NAME)) || ' ' || lower(TRIM (o_r.rep_surname)) || ' ' || lower(TRIM (o_r.rep_lastname))",
                        'compare' => '~',
                        'value' => $tmp_rep_names,
                    ];
                }
            }

            if ($filterParams['ime_subekt']) {
                if (is_array($filterParams['ime_subekt'])) {
                    $options['where']["ime_subekt{$key}"] = [
                        'column' => 'lower(TRIM (tkvs.ime_subekt))',
                        'compare' => 'IN',
                        'value' => array_map(fn ($item) => mb_strtolower($item, 'UTF-8'), $filterParams['ime_subekt']),
                    ];
                } else {
                    $tmp_subekt_names = preg_replace('/\s+/', '.*', $filterParams['ime_subekt']);
                    $tmp_subekt_names = mb_strtolower($tmp_subekt_names, 'UTF-8');
                    $options['where']["ime_subekt{$key}"] = ['column' => 'lower(TRIM (tkvs.ime_subekt))', 'compare' => '~', 'value' => $tmp_subekt_names];
                }
            }

            if ($filterParams['egn_subekt']) {
                if (is_array($filterParams['egn_subekt'])) {
                    $options['where']["egn_subekt{$key}"] = [
                        'column' => 'lower(TRIM (tkvs.egn_subekt))',
                        'compare' => 'IN',
                        'value' => array_map(fn ($item) => mb_strtolower($item, 'UTF-8'), $filterParams['egn_subekt']),
                    ];
                } else {
                    $tmp_subekt_egn = preg_replace('/\s+/', '.*', $filterParams['egn_subekt']);
                    $tmp_subekt_egn = mb_strtolower($tmp_subekt_egn, 'UTF-8');
                    $options['where']["egn_subekt{$key}"] = [
                        'column' => 'lower(TRIM (tkvs.egn_subekt))',
                        'compare' => '~',
                        'value' => $tmp_subekt_egn,
                    ];
                }
            }

            if (
                (isset($filterParams['contract_status']) && '' != $filterParams['contract_status'])
                || (isset($filterParams['contract_status_text']) && '' != $filterParams['contract_status_text'])
            ) {
                $options['where']["annex_action{$key}"] = ['column' => 'annex_action', 'compare' => '=', 'prefix' => 'pc', 'value' => 'added'];
            }

            if (isset($filterParams['contract_status']) && self::CONTRACT_STATUS_CANCELLED == $filterParams['contract_status']) {
                $options['where']["active{$key}"] = ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'];
            }

            if (isset($filterParams['contract_status']) && self::CONTRACT_STATUS_ACTIVE == $filterParams['contract_status']) {
                $options['where']["active{$key}"] = ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'TRUE'];
                $options['where']["due_date{$key}"] = ['column' => "(CASE WHEN c.due_date IS NULL THEN '9999-01-01' ELSE c.due_date END)", 'compare' => '>=', 'value' => $currentDate];
            }

            if (isset($filterParams['contract_status']) && self::CONTRACT_STATUS_EXPIRED == $filterParams['contract_status']) {
                $options['where']["due_date{$key}"] = ['column' => 'due_date', 'compare' => '<', 'prefix' => 'c', 'value' => $currentDate];
            }

            if (
                isset($filterParams['contract_status'], $filterParams['contract_type'][0])
                && ($filterParams['contract_status'] || $filterParams['contract_type'][0])
            ) {
                $options['having'] = "length(string_agg(distinct(annex_action)::text, ', ')) = 5";
            }

            if (isset($filterParams['participation']) && '' != $filterParams['participation']) {
                $options['where']["participateion{$key}"] = [
                    'column' => "CASE
                            WHEN kvs.participate = TRUE and kvs.include = FALSE and kvs.white_spots = FALSE
                                THEN 'participate'
                            WHEN kvs.participate = FALSE and kvs.include = TRUE and kvs.white_spots = FALSE
                                THEN 'no_participate'
                            WHEN kvs.participate = FALSE and kvs.include = FALSE and kvs.white_spots = TRUE
                                THEN 'white_spots'
                            ELSE 
                                'without'
                            END
                        ",
                    'compare' => is_array($filterParams['participation']) ? 'IN' : '=',
                    'value' => $filterParams['participation'] ?? '',
                ];
            }

            // check to see whether irrigation should be considered
            if (isset($filterParams['irrigated_area']) && 'all' !== $filterParams['irrigated_area']) {
                $options['where']["irrigated_area{$key}"] = [
                    'column' => 'irrigated_area',
                    'compare' => is_array($filterParams['irrigated_area']) ? 'IN' : '=',
                    'prefix' => 'kvs',
                    'value' => $filterParams['irrigated_area'],
                ];
            }

            // HACK: Добавено е в случай, че заявката е от грид "Исторически имоти"
            // Защото идва като string и понякога проверката в $options->where
            // връща falsie true, когато всъщност се подава false като стойност
            if (isset($filterParams['is_edited']) && 'false' === $filterParams['is_edited']) {
                $options['where']["is_edited{$key}"] = false;
            }

            if (isset($filterParams['farm_year_active_contract_plots']) || isset($filterParams['expiring_contracts_for_farm_year']) || isset($filterParams['for_sublease_farm_years'])) {
                $options['where']["active_contract_{$key}"] = [
                    'column' => 'active',
                    'prefix' => 'c',
                    'compare' => '=',
                    'value' => true,
                ];
            }

            if (
                (isset($filterParams['for_sublease_farm_years']))
                || (isset($filterParams['farm_year_active_contract_plots']))
                || (isset($filterParams['expiring_contracts_for_farm_year']))
            ) {
                $options['where']["annex_with_removed_plots{$key}"] = [
                    'column' => "(case when a.id notnull
                                    then a_pc.annex_action <> 'removed'
                                    else pc.annex_action <> 'removed'
                                end
                            )",
                    'compare' => '=',
                    'value' => true,
                ];
            }
            if (
                (isset($filterParams['for_sublease_farm_years']) && empty($filterParams['for_sublease_farm_years']))
                || (isset($filterParams['farm_year_active_contract_plots']) && empty($filterParams['farm_year_active_contract_plots']))
            ) {
                $options['where']["contract_validation_{$key}"] = [
                    'column' => "
                            case
                                when c.start_date notnull 
                                    and c.due_date notnull 
                                    and fy.farm_year_start between c.start_date and c.due_date 
                                    then true
                                when c.due_date is null 
                                    and sp.sale_start_date is null 
                                    and c.start_date < (fy.farm_year_start + interval '11 months' + interval '29 days')::date 
                                    then true
                                when c.due_date is null
                                    and sp.sale_start_date notnull
                                    and c.start_date::date < sp.sale_start_date
                                    and (fy.farm_year_start + interval '11 months' + interval '29 days')::date  < sp.sale_start_date
                                    then true
                                when c.due_date is null
                                    and sp.sale_start_date notnull
                                    and c.start_date::date < sp.sale_start_date
                                    and sp.sale_start_date between fy.farm_year_start::date and (fy.farm_year_start + interval '11 months' + interval '29 days')::date 
                                    then sp.cumulative_sold_area < sp.total_area
                            end",
                    'compare' => '=',
                    'value' => 'true',
                ];
            }

            if (
                (isset($filterParams['expiring_contracts_for_farm_year']) && empty($filterParams['expiring_contracts_for_farm_year']))
            ) {
                $options['where']["sold_plots_{$key}"] = [
                    'column' => "(
                                    (c.due_date is not null 
                                    and c.due_date between fy.farm_year_start::date 
                                    and (fy.farm_year_start + interval '11 months' + interval '29 days')::date)
                                    or (c.due_date is null 
                                        and sp.sale_start_date is not null 
                                        and c.start_date::date < sp.sale_start_date 
                                        and sp.sale_start_date between fy.farm_year_start::date + interval '1 year'
                                        and (fy.farm_year_start + interval '1 year' + interval '11 months' + interval '29 days')::date)
                                )",
                    'compare' => '=',
                    'value' => 'true',
                ];
            }

            if (isset($filterParams['for_sublease_farm_years'])) {
                if (empty($filterParams['for_sublease_farm_years'])) {
                    $options['where']["for_sublease_farm_years{$key}"] = ['column' => "
                    (case 
                        when sub_p.farming_years notnull and to_char(fy.farm_year_start, 'YYYY') || '/' || to_char(fy.farm_year_start + interval '1 year', 'YYYY') = ANY (sub_p.farming_years) then false
                        else true
                    end)",
                        'compare' => '=',
                        'value' => true];
                }

                if (!empty($filterParams['for_sublease_farm_years'])) {
                    list($startYear, $endYear) = explode('/', $filterParams['for_sublease_farm_years']);
                    $startFarmingYear = "{$startYear}-10-01";
                    $endYearYear = "{$endYear}-09-30";

                    $options['where']["for_sublease_farm_years{$key}"] = ['column' => "
                                (case 
                                    when sub_p.farming_years is null then true
                                    when '{$startYear}/{$endYear}' = ANY (sub_p.farming_years) then false
                                    else true
                                end)",
                        'compare' => '=',
                        'value' => true,
                    ];

                    $options['whereOr']["farm_year_in_not_owned_contract_{$key}"] = [
                        'column' => "
                                (c.due_date notnull and (
                                    -- The contract starts before the beginning of the farming year and ends after it
                                    (c.start_date::date < '{$startFarmingYear}' and c.due_date::date > '{$endYearYear}')
                                        -- The contract starts before the beginning of the farming year and ends within it
                                    or (c.start_date::date < '{$startFarmingYear}'and c.due_date::date between '{$startFarmingYear}' and '{$endYearYear}') 
                                        --The contract starts in the farming year and ends after it
                                    or (c.start_date::date between '{$startFarmingYear}' and '{$endYearYear}' and c.due_date::date >'{$endYearYear}') 
                                        -- The contract starts and ends within the farming year
                                    or (c.start_date::date between '{$startFarmingYear}' and '{$endYearYear}' and c.due_date::date between '{$startFarmingYear}' and '{$endYearYear}') 
                                    )
                                )
                            ",
                        'compare' => '=',
                        'value' => true,
                    ];

                    $options['whereOr']["sold_plots_{$key}"] = [
                        'column' => "
                                    (c.due_date is null and c.start_date::date < '{$endYearYear}'
                                    and (case
                                        when sp.sale_start_date is null then true
                                        when sp.sale_start_date notnull
                                            and c.start_date < sp.sale_start_date 
                                            and c.start_date < '{$endYearYear}'
                                            and sp.sale_start_date > '{$endYearYear}'
                                            then true
                                        when sp.sale_start_date notnull
                                            and c.start_date < sp.sale_start_date
                                            and c.start_date < '{$endYearYear}'
                                            and sp.sale_start_date between '{$startFarmingYear}' and '{$endYearYear}'
                                            and sp.cumulative_sold_area < sp.total_area
                                            then true
                                    end))",
                        'compare' => '=',
                        'value' => true,
                    ];
                }
            }

            if (!empty($filterParams['farm_year_active_contract_plots'])) {
                list($startYear, $endYear) = explode('/', $filterParams['farm_year_active_contract_plots']);
                $startFarmingYear = "{$startYear}-10-01";
                $endYearYear = "{$endYear}-09-30";

                $options['where']["farm_year_active_contract_plots_{$key}"] = [
                    'column' => "
                        (
                            (
                                (c.due_date is null and c.start_date::date < '{$endYearYear}')
                                or(
                                    c.due_date notnull
                                    and (
                                        -- The contract starts before the beginning of the farming year and ends after it
                                        (c.start_date::date < '{$startFarmingYear}' and c.due_date::date > '{$endYearYear}')
                                            -- The contract starts before the beginning of the farming year and ends within it
                                        or (c.start_date::date < '{$startFarmingYear}'and c.due_date::date between '{$startFarmingYear}' and '{$endYearYear}')
                                            --The contract starts in the farming year and ends after it
                                        or (c.start_date::date between '{$startFarmingYear}' and '{$endYearYear}' and c.due_date::date >'{$endYearYear}')
                                            -- The contract starts and ends within the farming year
                                        or (c.start_date::date between '{$startFarmingYear}' and '{$endYearYear}' and c.due_date::date between '{$startFarmingYear}' and '{$endYearYear}')
                                    )
                                )
                            ) = true
                            and
                                (case
                                    when sp.sale_start_date notnull 
                                        and c.start_date::date < sp.sale_start_date 
                                        and sp.sale_start_date between '{$startFarmingYear}' and '{$endYearYear}' 
                                        then false
                                    when sp.sale_start_date notnull 
                                        and c.start_date::date < sp.sale_start_date 
                                        and '{$startFarmingYear}' > sp.sale_start_date::date 
                                        and sp.sale_start_date not between '{$startFarmingYear}' and '{$endYearYear}' 
                                        then false
                                    when sp.sale_start_date notnull 
                                        and c.start_date::date < sp.sale_start_date 
                                        and sp.sale_start_date not between '{$startFarmingYear}' and '{$endYearYear}' 
                                        then true
                                    else true
                                end)
                        )
                        ",
                    'compare' => '=',
                    'value' => true,
                ];
            }

            if (!empty($filterParams['expiring_contracts_for_farm_year'])) {
                list($startYear, $endYear) = explode('/', $filterParams['expiring_contracts_for_farm_year']);
                $startFarmingYear = "{$startYear}-10-01";
                $endYearYear = "{$endYear}-09-30";

                $nextStartFarmingYear = ++$startYear . '-10-01';
                $nextEndYearYear = ++$endYear . '-09-30';

                $options['where']["expiring_contracts_for_farm_year_{$key}"] = [
                    'column' => "(
                                case 
                                    when c.due_date is null 
                                        and sp.sale_start_date between '{$nextStartFarmingYear}' and '{$nextEndYearYear}' 
                                        then true
                                    when c.due_date between '{$startFarmingYear}' and '{$endYearYear}' 
                                        and next_contract.next_contract_start_date is null 
                                        then true
                                    else false
                                end
                        )",
                    'compare' => '=',
                    'value' => 'true',
                ];
            }

            $groupOptions[] = $options;
        }

        return $groupOptions;
    }

    public function buildJoins(array $joins, ?array $filterGroups = [])
    {
        $options = [];
        $whereAnexOverlapsClauses = [];
        $hasAnnexOverlapsContractPeriod = false;
        $hasAnnexOverlapsContractPeriodForFarmYears = false;
        $hasSubleasePlots = false;
        $hasSubleasePlotsByFarmYears = false;
        $subleaseYearsFilter = [];

        foreach ($joins as $join) {
            if ('sold_plots' === $join['name']) {
                $kvs = $join['arguments']['gid'];
                $contractId = $join['arguments']['contract_id'];

                $soldPlotQuerry = $this->getSoldPlotsIds(true);
                $options[] = "left join ({$soldPlotQuerry}) sp on sp.plot_id = {$kvs} and sp.contract_id = {$contractId}";
            }

            if ('next_contract' === $join['name']) {
                $where = '';
                foreach ($filterGroups as $filterParams) {
                    if (!empty($filterParams['expiring_contracts_for_farm_year'])) {
                        list($startYear, $endYear) = explode('/', $filterParams['expiring_contracts_for_farm_year']);

                        $nextStartFarmingYear = ++$startYear . '-10-01';
                        $nextEndYearYear = ++$endYear . '-09-30';

                        if (strlen($where) > 0) {
                            $where .= ' OR ';
                        }

                        $where .= "next_contract.next_contract_start_date BETWEEN '{$nextStartFarmingYear}' AND '{$nextEndYearYear}'";
                    }
                }

                $onClause = 'next_contract.next_plot_id = kvs.gid and next_contract.contract_id > c.id ';

                if (strlen($where) > 0) {
                    $onClause .= " AND ({$where})";
                }

                $options[] = "
                    LEFT JOIN (
                        SELECT
                            cpr.plot_id AS next_plot_id,
                            c.start_date AS next_contract_start_date,
                            c.id as contract_id,
                            c.start_date,
                            c.due_date 
                        FROM su_contracts c
                        JOIN su_contracts_plots_rel cpr ON c.id = cpr.contract_id
                        WHERE c.active = TRUE
                    ) next_contract ON {$onClause}
                ";
            }

            if ('farm_years' === $join['name']) {
                $options[] = "join lateral (
                                        select
                                            generate_series(
                                                date_trunc('year', c.start_date) + interval '9 months',
                                                coalesce(
                                                    date_trunc('year', c.due_date) + interval '8 months' + interval '29 days',
                                                    '2100-12-31'::date
                                                ),
                                                interval '1 year'
                                            ) as farm_year_start
                                    ) fy on true";
            }

            // Create where logic for sublease_plots
            if ('sublease_plots' === $join['name']) {
                $hasSubleasePlots = true;

                foreach ($filterGroups as $filterParams) {
                    if (!empty($filterParams['for_sublease_farm_years'])) {
                        $subleaseYearsFilter[] = "'" . $filterParams['for_sublease_farm_years'] . "'";
                    }
                }
            }

            if ('sublease_plots_by_farm_years' === $join['name']) {
                $hasSubleasePlotsByFarmYears = true;
            }

            // Create where logic for annex_overlaps_contract_period
            if ('annex_overlaps_contract_period' === $join['name']) {
                $hasAnnexOverlapsContractPeriod = true;
                $where = '';

                foreach ($filterGroups as $filterParams) {
                    if (!empty($filterParams['for_sublease_farm_years'])) {
                        list($startYear, $endYear) = explode('/', $filterParams['for_sublease_farm_years']);
                        $startFarmYear = $startYear . '-10-01';
                        $endFarmYear = $endYear . '-09-30';

                        if (strlen($where) > 0) {
                            $where .= ' OR ';
                        }

                        $where .= $this->generateDateOverlapCondition("'{$startFarmYear}'", "'{$endFarmYear}'");
                    }
                }

                if (strlen($where) > 0) {
                    $whereAnexOverlapsClauses[] = $where;
                }
            }

            // Create where logic for annex_overlaps_contract_period_for_farm_years
            if ('annex_overlaps_contract_period_for_farm_years' === $join['name']) {
                $hasAnnexOverlapsContractPeriodForFarmYears = true;
                $whereAnexOverlapsClauses[] = $this->generateDateOverlapCondition(
                    'fy.farm_year_start',
                    "fy.farm_year_start + interval '11 months' + interval '29 days'"
                );
            }
        }

        // If there is at least one of the two sublease joins, add only one LEFT JOIN
        if ($hasSubleasePlots || $hasSubleasePlotsByFarmYears) {
            $sublease_plotsJoin = "
            LEFT JOIN (
                SELECT
                    contract_ids,
                    gid,
                    array_agg(to_char(farming_year_start, 'YYYY') || '/' || to_char(farming_year_end, 'YYYY')) as farming_years
                FROM sublease_per_year
                WHERE ((total_area_for_rent - total_subleased_area) > 0.002) = false";

            // If 'sublease_plots' has a filter for years
            if ($hasSubleasePlots && !empty($subleaseYearsFilter)) {
                $sublease_plotsJoin .= " AND to_char(farming_year_start, 'YYYY') || '/' || to_char(farming_year_end, 'YYYY') IN (" . implode(',', $subleaseYearsFilter) . ')';
            }

            $sublease_plotsJoin .= '
                GROUP BY contract_ids, gid
            ) sub_p ON c.id = ANY (sub_p.contract_ids) AND sub_p.gid = kvs.gid
        ';

            // If there is 'sublease_plots_by_farm_years', add a link to farm_years
            if ($hasSubleasePlotsByFarmYears) {
                $sublease_plotsJoin .= " AND to_char(fy.farm_year_start,'YYYY') || '/' || to_char(fy.farm_year_start + interval '1 year','YYYY') = ANY(sub_p.farming_years)";
            }

            $options[] = $sublease_plotsJoin;
        }

        // If at least one of the two joins is available, we use only one LEFT JOIN with where clause
        if ($hasAnnexOverlapsContractPeriod || $hasAnnexOverlapsContractPeriodForFarmYears) {
            $onClause = 'a.parent_id = c.id and a.active = true';

            if (!empty($whereAnexOverlapsClauses)) {
                $onClause .= ' AND (' . implode(' OR ', $whereAnexOverlapsClauses) . ')';
            }

            $options[] = "left join su_contracts a on ({$onClause})
                left join su_contracts_plots_rel a_pc on
                (a_pc.plot_id = kvs.gid and a_pc.contract_id = a.id)
            ";
        }

        return $options;
    }

    public function checkFilterExist(?array $filtersGroup, $neededFilter): bool
    {
        foreach ($filtersGroup as $filters) {
            foreach ($filters as $filterName => $filterValue) {
                if ($filterName === $neededFilter) {
                    return true;
                }
            }
        }

        return false;
    }

    public function determineJoinsByFilters(?array $filtersGroup, $fullTextSearchExist = false): array
    {
        $joinsForBuild = [
            'sold_plots' => ['name' => 'sold_plots',
                'arguments' => [
                    'gid' => 'kvs.gid',
                    'contract_id' => 'c.id',
                ],
            ],
            'next_contract' => ['name' => 'next_contract'],
            'farm_years' => ['name' => 'farm_years'],
            'sublease_plots' => ['name' => 'sublease_plots'],
            'sublease_plots_by_farm_years' => ['name' => 'sublease_plots_by_farm_years'],
            'annex_overlaps_contract_period' => ['name' => 'annex_overlaps_contract_period'],
            'annex_overlaps_contract_period_for_farm_years' => ['name' => 'annex_overlaps_contract_period_for_farm_years'],
        ];

        $kcUid = isset($this->User) && isset($this->User->KeyCloakUID) ? "'{$this->User->KeyCloakUID}'" : 'NULL';

        $joins = [
            'readyForUse' => [
                'user_contracts_count_by_plot_and_status' => "LEFT JOIN user_contracts_count_by_plot_and_status({$kcUid}) uccps ON (uccps.plot_id = kvs.gid)",
            ],
            'buildJoins' => [],
        ];

        foreach ($filtersGroup as $filters) {
            foreach ($filters as $filter => $filterValue) {
                switch ($filter) {
                    case 'ekate':
                    case 'cnum':
                    case 'cnum_exact':
                    case 'nm_usage_rights':
                    case 'virtual_contract_type':
                    case 'contract_type':
                    case 'farming_id':
                    case 'farming':
                    case 'farming_name':
                    case 'c_date':
                    case 'due_date':
                    case 'start_date':
                    case 'date_from':
                    case 'date_to':
                    case 'active':
                        $joins['readyForUse']['su_contracts_plots_rel'] = 'LEFT JOIN su_contracts_plots_rel pc ON(pc.plot_id = kvs.gid)';
                        $joins['readyForUse']['su_contracts'] = 'LEFT JOIN su_contracts c ON(c.id = pc.contract_id)';

                        break;
                    case 'egn':
                    case 'owner_egn':
                    case 'person_egn':
                    case 'heritor_egn':
                    case 'rep_egn':
                    case 'company_name':
                    case 'eik':
                    case 'name':
                    case 'person_name':
                    case 'owner_name':
                    case 'heritor_name':
                    case 'surname':
                    case 'lastname':
                    case 'company_eik':
                        $joins['readyForUse']['su_contracts_plots_rel'] = 'LEFT JOIN su_contracts_plots_rel pc ON(pc.plot_id = kvs.gid)';
                        $joins['readyForUse']['su_plots_owners_rel'] = 'LEFT JOIN su_plots_owners_rel po ON(po.pc_rel_id = pc.id)';
                        $joins['readyForUse']['su_owners'] = 'LEFT JOIN su_owners o ON(o.id = po.owner_id)';
                        $joins['readyForUse']['su_owners_reps'] = 'LEFT JOIN su_owners_reps o_r on(o_r.id = po.rep_id)';
                        $joins['readyForUse']['su_contracts'] = 'LEFT JOIN su_contracts c ON(c.id = pc.contract_id)';

                        break;
                    case 'owner_multi_field_search':
                        $joins['readyForUse']['su_contracts_plots_rel'] = 'LEFT JOIN su_contracts_plots_rel pc ON(pc.plot_id = kvs.gid)';
                        $joins['readyForUse']['su_plots_owners_rel'] = 'LEFT JOIN su_plots_owners_rel po ON(po.pc_rel_id = pc.id)';
                        $joins['readyForUse']['su_owners'] = 'LEFT JOIN su_owners o ON(o.id = po.owner_id)';
                        $joins['readyForUse']['su_owners_reps'] = 'LEFT JOIN su_owners_reps o_r on(o_r.id = po.rep_id)';
                        $joins['readyForUse']['su_contracts'] = 'LEFT JOIN su_contracts c ON(c.id = pc.contract_id)';

                        break;
                    case 'rep_name':
                        $joins['readyForUse']['su_contracts_plots_rel'] = 'LEFT JOIN su_contracts_plots_rel pc ON(pc.plot_id = kvs.gid)';
                        $joins['readyForUse']['su_plots_owners_rel'] = 'LEFT JOIN su_plots_owners_rel po ON(po.pc_rel_id = pc.id)';
                        $joins['readyForUse']['su_owners_reps'] = 'LEFT JOIN su_owners_reps o_r on(o_r.id = po.rep_id)';
                        $joins['readyForUse']['su_contracts'] = 'LEFT JOIN su_contracts c ON(c.id = pc.contract_id)';

                        break;
                    case 'contract_status':
                    case 'contract_status_text':
                        $joins['readyForUse']['su_contracts_plots_rel'] = 'LEFT JOIN su_contracts_plots_rel pc ON(pc.plot_id = kvs.gid)';
                        $joins['readyForUse']['su_contracts'] = 'LEFT JOIN su_contracts c ON(c.id = pc.contract_id)';

                        break;
                    case 'for_sublease_farm_years':
                    case 'farm_year_active_contract_plots':
                        $joins['readyForUse']['su_contracts_plots_rel'] = 'LEFT JOIN su_contracts_plots_rel pc ON(pc.plot_id = kvs.gid)';
                        $joins['readyForUse']['su_contracts'] = 'LEFT JOIN su_contracts c ON(c.id = pc.contract_id)';
                        if (!empty($filterValue)) {
                            $joins['buildJoins']['sold_plots'] = $joinsForBuild['sold_plots'];
                            $joins['buildJoins']['sublease_plots'] = $joinsForBuild['sublease_plots'];
                            $joins['buildJoins']['annex_overlaps_contract_period'] = $joinsForBuild['annex_overlaps_contract_period'];
                        } else {
                            $joins['buildJoins']['sold_plots'] = $joinsForBuild['sold_plots'];
                            $joins['buildJoins']['farm_years'] = $joinsForBuild['farm_years'];
                            $joins['buildJoins']['sublease_plots_by_farm_years'] = $joinsForBuild['sublease_plots_by_farm_years'];
                            $joins['buildJoins']['annex_overlaps_contract_period_for_farm_years'] = $joinsForBuild['annex_overlaps_contract_period_for_farm_years'];
                        }

                        break;
                    case 'expiring_contracts_for_farm_year':
                        if (!empty($filterValue)) {
                            $joins['readyForUse']['su_contracts_plots_rel'] = 'LEFT JOIN su_contracts_plots_rel pc ON(pc.plot_id = kvs.gid)';
                            $joins['readyForUse']['su_contracts'] = 'LEFT JOIN su_contracts c ON(c.id = pc.contract_id)';
                            $joins['buildJoins']['sold_plots'] = $joinsForBuild['sold_plots'];
                            $joins['buildJoins']['next_contract'] = $joinsForBuild['next_contract'];
                            $joins['buildJoins']['farm_years'] = $joinsForBuild['farm_years'];
                            $joins['buildJoins']['annex_overlaps_contract_period'] = $joinsForBuild['annex_overlaps_contract_period'];
                        } else {
                            $joins['readyForUse']['su_contracts_plots_rel'] = 'LEFT JOIN su_contracts_plots_rel pc ON(pc.plot_id = kvs.gid)';
                            $joins['readyForUse']['su_contracts'] = 'LEFT JOIN su_contracts c ON(c.id = pc.contract_id)';
                            $joins['buildJoins']['sold_plots'] = $joinsForBuild['sold_plots'];
                            $joins['buildJoins']['next_contract'] = $joinsForBuild['next_contract'];
                            $joins['buildJoins']['farm_years'] = $joinsForBuild['farm_years'];
                            $joins['buildJoins']['annex_overlaps_contract_period_for_farm_years'] = $joinsForBuild['annex_overlaps_contract_period_for_farm_years'];
                        }

                        break;
                    default:
                        break;
                }
            }
        }

        if ($fullTextSearchExist) {
            $joins['readyForUse']['su_contracts_plots_rel'] = 'LEFT JOIN su_contracts_plots_rel pc ON(pc.plot_id = kvs.gid)';
            $joins['readyForUse']['su_contracts'] = 'LEFT JOIN su_contracts c ON(c.id = pc.contract_id)';
        }

        return $joins;
    }

    public static function getPlotStatusesFilter(array $statuses, string $tablename = 'kvs')
    {
        $validStatuses = [
            self::PLOT_STATUS_ACTIVE,
            self::PLOT_STATUS_ARCHIVED,
        ];

        $hasInvalidStatus = count(array_diff($statuses, $validStatuses)) > 0;

        if ($hasInvalidStatus) {
            throw new Exception('Invalid plot status. Valid statuses are: ' . implode(', ', $validStatuses));
        }

        if (count($statuses) === count($validStatuses) && !$hasInvalidStatus) {
            // Filter by all statuses means no filter
            return [];
        }

        $filter = [];
        foreach ($statuses as $status) {
            $key = "plot_status_{$status}";

            switch ($status) {
                case self::PLOT_STATUS_ACTIVE:
                    $filter[$key] = [
                        'column' => "(
                                {$tablename}.is_edited = FALSE
                                OR ({$tablename}.is_edited = TRUE AND {$tablename}.edit_active_from::date >= now())
                            )",
                        'compare' => '=',
                        'value' => true,
                    ];

                    break;
                case self::PLOT_STATUS_ARCHIVED:
                    $filter[$key] = [
                        'column' => "(
                                {$tablename}.is_edited = TRUE
                                AND {$tablename}.edit_active_from::date < now()
                            )",
                        'compare' => '=',
                        'value' => true,
                    ];

                    break;
            }
        }

        return $filter;
    }

    /**
     * @api-method getTreePlots
     *
     * @param array $rpcParams
     *                         {
     *                         #item array area_type
     *                         {
     *                         #items string
     *                         }
     *                         #item string mestnost
     *                         #item array category {
     *                         #items string
     *                         }
     *                         #item string cnum
     *                         #item string company_eik
     *                         #item string company_name
     *                         #item string contract_status
     *                         #item array contract_type {
     *                         #items string
     *                         }
     *                         #item string date_from
     *                         #item string date_to
     *                         #item string due_date_from
     *                         #item string due_date_to
     *                         #item array ekate {
     *                         #items string
     *                         }
     *                         #item array farming {
     *                         #items string
     *                         }
     *                         #item bool is_edited
     *                         #item string kad_ident
     *                         #item string masiv
     *                         #item string number
     *                         #item string owner_egn
     *                         #item string owner_name
     *                         #item string rep_egn
     *                         #item string rep_name
     *                         }
     * @param int $page pagination parameter
     * @param int $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @throws TException
     *
     * @return array
     */
    public function getTreePlots(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        switch ($rpcParams['filter_action']) {
            case 'exec_filter':
                $_SESSION['filtered_plots']['layer_kvs'] = [];
                $plots = $this->getPlots($rpcParams, $page, $rows, $sort, $order);

                if ([] === $plots['data']) {
                    return ['rows' => [], 'total' => 0, 'footer' => []];
                }

                $rpcParams['getAllPlots'] = true;
                $allPlots = $this->getPlots($rpcParams, $page, $rows, $sort, $order);
                $filteredGids = $allPlots['info']['filteredGids'];

                foreach ($filteredGids as $filteredGid) {
                    $_SESSION['filtered_plots']['layer_kvs'][] = $filteredGid;
                }

                break;
            case 'clear_filter':
                $plots = $this->getPlots($rpcParams, $page, $rows, $sort, $order);
                $_SESSION['filtered_plots']['layer_kvs'] = [];

                if ([] === $plots['data']) {
                    return ['rows' => [], 'total' => 0, 'footer' => []];
                }

                break;
            case 'add_to_filter':
                $rpcParams['getAllPlots'] = true;
                $filtredPlots = $this->getPlots($rpcParams, $page, $rows, $sort, $order);
                $filteredGids = $filtredPlots['info']['filteredGids'];

                foreach ($filteredGids as $filteredGid) {
                    if (!in_array($filteredGid, $_SESSION['filtered_plots']['layer_kvs'])) {
                        $_SESSION['filtered_plots']['layer_kvs'][] = $filteredGid;
                    }
                }

                $rpcParams = [];
                $rpcParams['plot_id'] = implode(',', $_SESSION['filtered_plots']['layer_kvs']);
                $plots = $this->getPlots($rpcParams, $page, $rows, $sort, $order);

                if (!empty($plots['data'][0]['attributes'])) {
                    $plots['data'][0]['attributes']['currentFilterCount'] = count($filteredGids);
                }

                break;
            case 'remove_from_filter':
                $field = $rpcParams['removedTarget']['field'];
                $value = $rpcParams['removedTarget']['value'];

                $requestParams = [];
                $requestParams['plot_id'] = implode(',', $_SESSION['filtered_plots']['layer_kvs']);
                $requestParams['getAllPlots'] = true;
                $plots = $this->getPlots($requestParams, $page, $rows, $sort, $order);
                foreach ($plots['data'] as $plot) {
                    if (false !== strpos(strtolower($plot['attributes'][$field]), strtolower($value))) {
                        $key = array_search($plot['id'], $_SESSION['filtered_plots']['layer_kvs']);
                        unset($_SESSION['filtered_plots']['layer_kvs'][$key]);
                    }
                }

                $requestParams = [];
                $requestParams['plot_id'] = implode(',', $_SESSION['filtered_plots']['layer_kvs']);

                $plots = $this->getPlots($requestParams, $page, $rows, $sort, $order);

                break;
            default:
                $_SESSION['filtered_plots']['layer_kvs'] = [];
                $plots = $this->getPlots($rpcParams, $page, $rows, $sort, $order);

                if (empty($plots['data'])) {
                    return ['rows' => [], 'total' => 0, 'footer' => []];
                }

                break;
        }

        return $plots['data'];
    }

    public function loadPlotForEdit($editPlotID)
    {
        $this->initializeControllers('loadPlotForEdit');

        $options = [
            'tablename' => $this->UserDbController->DbHandler->tableKVS,
            'return' => [
                '*', 'ST_Area(geom)/1000 as area',
            ],
            'where' => [
                'gid' => ['column' => 'gid', 'compare' => '=', 'value' => $editPlotID],
            ],
        ];

        $results = $this->UserDbController->getItemsByParams($options);

        return $results[0];
    }

    /**
     * Update the selected plot.
     *
     * @api-method update
     *
     * @param array $data {
     *                    #item string kad_ident
     *                    #item string ekate
     *                    #item string masiv
     *                    #item string number
     *                    #item string document_area
     *                    #item string mestnost
     *                    #item string category
     *                    #item string area_type
     *                    #item bool   usable
     *                    #item bool   area_by_geom
     *                    #item bool   area_by_zp
     *                    #item bool   area_by_ownage
     *                    #item string area_farming
     *                    #item string area_year
     *                    #item bool   include
     *                    #item bool   participate
     *                    #item bool   white_spots
     *                    #item int    gid
     *                    #item string    block
     *                    }
     *
     * @throws TException
     *
     * @return array
     */
    public function updatePlotValues($data)
    {
        // Initialize used database Controllers
        $this->initializeControllers('updatePlotValues');

        if (0 == $data['gid']) {
            return [];
        }

        $gid = $data['gid'];

        // Тази заявка се прави за да се вземат старите стойности на имота, преди редакцията и да се запишат в лога
        $options = [
            'tablename' => $this->UserDbController->DbHandler->tableKVS,
            'return' => [
                'gid, area_type, category, kad_ident, ekate, masiv, number, document_area, mestnost, usable, include, participate, white_spots, irrigated_area, used_area_by, area_farming, area_year, used_area, old_kad_ident'],
            'where' => [
                'gid' => ['column' => 'gid', 'compare' => '=', 'value' => $gid],
            ],
        ];

        $oldValues = $this->UserDbController->getItemsByParams($options);
        $oldValues = $oldValues[0];

        $options = [
            'tablename' => $this->UserDbController->DbHandler->tableKVS,
            'where' => [
                'gid' => $gid,
            ],
            'mainData' => [
                'area_type' => $data['area_type'],
                'category' => $data['category'],
                'kad_ident' => $data['kad_ident'],
                'old_kad_ident' => $data['old_kad_ident'],
                'ekate' => $data['ekate'],
                'masiv' => $data['masiv'],
                'number' => $data['number'],
                'mestnost' => $data['mestnost'],
                'usable' => $data['usable'],
                'include' => $data['include'],
                'participate' => $data['participate'],
                'white_spots' => $data['white_spots'],
                'irrigated_area' => $data['irrigated_area'],
                'comment' => $data['comment'],
                'block' => $data['block'],
            ],
        ];

        $this->UserDbController->editItem($options);

        $this->UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $data, $oldValues, 'Edited plot info in kvs');
    }

    /**
     * Edit multiple plots which meet the filter parameters.
     *
     * @api-method multiEdit
     *
     * @param array $data all of the infomation, that is submitted with the
     *                    multi edit plots form. The first array ($filterParams) - contains
     *                    all of the filtering criteria
     *                    {
     *                    #item array filterParams{ =>
     *                    #item string kad_ident
     *                    #item array  ekate {
     *                    #items string
     *                    }
     *                    #item string masiv
     *                    #item string number
     *                    #item array  category {
     *                    #items string
     *                    }
     *                    #item array  area_type {
     *                    #items string
     *                    }
     *                    #item string cnum
     *                    #item array  contract_type {
     *                    #items string
     *                    }
     *                    #item string contract_status
     *                    #item array  farming {
     *                    #items string
     *                    }
     *                    #item string date_from
     *                    #item string date_to
     *                    #item string due_date_from
     *                    #item string due_date_to
     *                    #item string owner_name
     *                    #item string owner_egn
     *                    #item string rep_name
     *                    #item string rep_egn
     *                    #item string company_name
     *                    #item string company_eik
     *                    #item bool   is_edited
     *                    }
     *                    #item bool   area_by_geom
     *                    #item bool   area_by_zp
     *                    #item bool   area_by_ownage
     *                    #item string mestnost
     *                    #item bool   usable
     *                    #item bool   include
     *                    #item bool   participate
     *                    #item bool   white_spots
     *                    #item string category
     *                    #item string area_farming
     *                    #item string area_year
     *                    #item string area_type
     *                    #item string block
     *                    }
     *
     * @throws TException
     *
     * @return array
     */
    public function executeMultiEdit($data)
    {
        $this->initializeControllers('executeMultiEdit');

        $filterParams = $data['filterParams'];

        date_default_timezone_set('Europe/Sofia');
        $currentDate = date('Y-m-d', time());

        $arrayHelper = $this->FarmingController->ArrayHelper;

        if ($data['has_filters'] && !empty($_SESSION['filtered_plots']['layer_kvs'])) {
            $id_array = $_SESSION['filtered_plots']['layer_kvs'];
        } else {
            $options = [
                'tablename' => $this->UserDbController->DbHandler->tableKVS,
                'return' => [
                    'DISTINCT(kvs.gid)',
                ],
                'where' => [
                    // plots filter
                    'plot_id' => ['column' => 'gid', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $arrayHelper->filterEmptyStringArr(explode(',', $filterParams['plot_id']))],
                    'kad_ident' => ['column' => 'kad_ident', 'compare' => '=', 'prefix' => 'kvs', 'value' => $filterParams['kad_ident']],
                    'ekate' => ['column' => 'ekate', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $arrayHelper->filterEmptyStringArr($filterParams['ekate'])],
                    'masiv' => ['column' => 'masiv', 'compare' => '=', 'prefix' => 'kvs', 'value' => $filterParams['masiv']],
                    'number' => ['column' => 'number', 'compare' => '=', 'prefix' => 'kvs', 'value' => $filterParams['number']],
                    'area_type' => ['column' => 'area_type', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $arrayHelper->filterEmptyStringArr($filterParams['area_type'])],
                    'mestnost' => ['column' => 'mestnost', 'compare' => '=', 'prefix' => 'kvs', 'value' => $filterParams['mestnost']],
                    'is_edited' => ['column' => 'is_edited', 'compare' => '=', 'prefix' => 'kvs', 'value' => $filterParams['is_edited'] ? 'true' : 'false'],

                    // contracts filter
                    'cnum' => ['column' => 'c_num', 'compare' => 'ILIKE', 'prefix' => 'c', 'value' => $filterParams['cnum']],
                    'contract_type' => ['column' => 'nm_usage_rights', 'compare' => 'IN', 'prefix' => 'c', 'value' => $arrayHelper->filterEmptyStringArr($filterParams['contract_type'])],
                    'farming' => ['column' => 'farming_id', 'compare' => 'IN', 'prefix' => 'c', 'value' => $arrayHelper->filterEmptyStringArr($filterParams['farming'])],
                    'date_from' => ['column' => 'c_date', 'compare' => '>=', 'prefix' => 'c', 'value' => $filterParams['date_from']],
                    'date_to' => ['column' => 'c_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $filterParams['date_to']],
                    'due_date' => ['column' => "(CASE WHEN c.due_date IS NULL THEN '9999-01-01' ELSE c.due_date END)", 'compare' => '>=', 'value' => $filterParams['start_date']],
                    'start_date' => ['column' => 'start_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $filterParams['due_date']],

                    // owners filter
                    'owner_egn' => ['column' => 'egn', 'compare' => '=', 'prefix' => 'o', 'value' => $filterParams['owner_egn']],
                    'rep_egn' => ['column' => 'rep_egn', 'compare' => '=', 'prefix' => 'o_r', 'value' => $filterParams['rep_egn']],
                    'company_name' => ['column' => 'company_name', 'compare' => 'ILIKE', 'prefix' => 'o', 'value' => $filterParams['company_name']],
                    'company_eik' => ['column' => 'eik', 'compare' => '=', 'prefix' => 'o', 'value' => $filterParams['company_eik']],
                ],
                'group' => 'kvs.gid',
                'category' => $arrayHelper->filterEmptyStringArr($filterParams['category']),
            ];

            if ($filterParams['cnum_exact']) {
                $options['where']['cnum'] = ['column' => 'c_num', 'compare' => '=', 'prefix' => 'c', 'value' => $filterParams['cnum']];
            }

            if ($filterParams['plot_statuses']) {
                $plotStatusesFilter = self::getPlotStatusesFilter($filterParams['plot_statuses']);

                if (count($plotStatusesFilter) > 0) {
                    $options['whereOr'] = array_merge($options['whereOr'] ?? [], $plotStatusesFilter);
                }
            }

            if ($filterParams['owner_name']) {
                $tmp_owner_names = preg_replace('/\s+/', '.*', $filterParams['owner_name']);
                $tmp_owner_names = mb_strtolower($tmp_owner_names, 'UTF-8');
                $options['where']['owner_names'] = ['column' => "lower(TRIM (o.NAME)) || ' ' || lower(TRIM (o.surname)) || ' ' || lower(TRIM (o.lastname))", 'compare' => '~', 'value' => $tmp_owner_names];
                $options['group'] .= ',o.id';
            }

            if ($filterParams['rep_name']) {
                $tmp_rep_names = preg_replace('/\s+/', '.*', $filterParams['rep_name']);
                $tmp_rep_names = mb_strtolower($tmp_rep_names, 'UTF-8');
                $options['where']['rep_names'] = ['column' => "lower(TRIM (o_r.rep_NAME)) || ' ' || lower(TRIM (o_r.rep_surname)) || ' ' || lower(TRIM (o_r.rep_lastname))", 'compare' => '~', 'value' => $tmp_rep_names];
                $options['group'] .= ',o_r.id';
            }

            if ($filterParams['ime_subekt']) {
                $tmp_subekt_names = preg_replace('/\s+/', '.*', $filterParams['ime_subekt']);
                $tmp_subekt_names = mb_strtolower($tmp_subekt_names, 'UTF-8');
                $options['where']['ime_subekt'] = ['column' => 'lower(TRIM (tkvs.ime_subekt))', 'compare' => '~', 'value' => $tmp_subekt_names];
                $options['group'] .= ',tkvs.ime_subekt';
            }

            if ($filterParams['egn_subekt']) {
                $tmp_subekt_egn = preg_replace('/\s+/', '.*', $filterParams['egn_subekt']);
                $tmp_subekt_egn = mb_strtolower($tmp_subekt_egn, 'UTF-8');
                $options['where']['egn_subekt'] = ['column' => 'lower(TRIM (tkvs.egn_subekt))', 'compare' => '~', 'value' => $tmp_subekt_egn];
                $options['group'] .= ',tkvs.egn_subekt';
            }

            if (self::CONTRACT_STATUS_CANCELLED == $filterParams['contract_status']) {
                $options['where']['active'] = ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'];
                $options['where']['annex_action'] = ['column' => 'annex_action', 'compare' => '=', 'prefix' => 'pc', 'value' => 'added'];
            }

            if (self::CONTRACT_STATUS_ACTIVE == $filterParams['contract_status']) {
                $options['where']['active'] = ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'TRUE'];
                $options['where']['due_date'] = ['column' => "(CASE WHEN c.due_date IS NULL THEN '9999-01-01' ELSE c.due_date END)", 'compare' => '>=', 'value' => $currentDate];
                $options['where']['annex_action'] = ['column' => 'annex_action', 'compare' => '=', 'prefix' => 'pc', 'value' => 'added'];
            }

            if (self::CONTRACT_STATUS_EXPIRED == $filterParams['contract_status']) {
                $options['where']['due_date'] = ['column' => 'due_date', 'compare' => '<', 'prefix' => 'c', 'value' => $currentDate];
                $options['where']['annex_action'] = ['column' => 'annex_action', 'compare' => '=', 'prefix' => 'pc', 'value' => 'added'];
            }

            if ($filterParams['contract_status'] || $filterParams['contract_type'][0]) {
                $options['having'] = "length(string_agg(distinct(annex_action)::text, ', ')) = 5";
            }

            if ($filterParams['participation']) {
                switch ($filterParams['participation']) {
                    case 'participate':
                        $options['where']['participate'] = ['column' => 'participate', 'compare' => '=', 'prefix' => 'kvs', 'value' => 'true'];
                        $options['where']['include'] = ['column' => 'include', 'compare' => '=', 'prefix' => 'kvs', 'value' => 'false'];
                        $options['where']['white_spots'] = ['column' => 'white_spots', 'compare' => '=', 'prefix' => 'kvs', 'value' => 'false'];

                        break;
                    case 'no_participate':
                        $options['where']['participate'] = ['column' => 'participate', 'compare' => '=', 'prefix' => 'kvs', 'value' => 'false'];
                        $options['where']['include'] = ['column' => 'include', 'compare' => '=', 'prefix' => 'kvs', 'value' => 'true'];
                        $options['where']['white_spots'] = ['column' => 'white_spots', 'compare' => '=', 'prefix' => 'kvs', 'value' => 'false'];

                        break;
                    case 'without':
                        $options['where']['participate'] = ['column' => 'participate', 'compare' => '=', 'prefix' => 'kvs', 'value' => 'false'];
                        $options['where']['include'] = ['column' => 'include', 'compare' => '=', 'prefix' => 'kvs', 'value' => 'false'];
                        $options['where']['white_spots'] = ['column' => 'white_spots', 'compare' => '=', 'prefix' => 'kvs', 'value' => 'false'];

                        break;
                    default:
                        break;
                }
            }

            // check to see whether irrigation should be considered
            if ($filterParams['irrigated_area'] && 'all' != $filterParams['irrigated_area']) {
                $options['where']['irrigated_area'] = ['column' => 'irrigated_area', 'compare' => '=', 'prefix' => 'kvs', 'value' => $filterParams['irrigated_area']];
            }

            // HACK: Добавено е в случай, че заявката е от грид "Исторически имоти"
            // Защото идва като string и понякога проверката в $options->where
            // връща falsie true, когато всъщност се подава false като стойност
            if ('false' === $filterParams['is_edited']) {
                $options['where']['is_edited'] = false;
            }

            $results = $this->UserDbPlotsController->getDataForPlotsTree($options, false, false);

            $id_array = [];
            $result_count = count($results);
            if (0 == $result_count) {
                return [];
            }

            // creating the KVS gid array
            for ($i = 0; $i < $result_count; $i++) {
                $id_array[] = $results[$i]['gid'];
            }
        }

        // creating the main UPDATE options
        $options = [
            'id_string' => implode(', ', $id_array),
            'update' => [
                'include' => ['bind' => true, 'value' => $data['include'] ? true : false],
                'participate' => ['bind' => true, 'value' => $data['participate'] ? true : false],
                'white_spots' => ['bind' => true, 'value' => $data['white_spots'] ? true : false],
                'usable' => ['bind' => true, 'value' => $data['usable'] ? true : false],
            ],
        ];

        if (null != $data['category']) {
            $options['update']['category'] = [
                'bind' => true,
                'value' => $data['category'],
            ];
        }
        if (null != $data['area_type']) {
            $options['update']['area_type'] = [
                'bind' => true,
                'value' => $data['area_type'],
            ];
        }

        if (null !== $data['mestnost'] && '' !== trim($data['mestnost']) && '-' !== trim($data['mestnost'])) {
            $options['update']['mestnost'] = [
                'bind' => true,
                'value' => $data['mestnost'],
            ];
        }

        $options['update']['irrigated_area'] = [
            'bind' => true,
            'value' => $data['irrigated_area'] ? true : false,
        ];

        $this->UserDbPlotsController->KVSMultiEdit($options);

        $logsGidArray = ['AffectedGids' => $id_array];
        $this->UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $data, $logsGidArray, 'Multiedit performed');
    }

    public function getSubleasedPlotsCte($rpcParams, $returnOnlySQL = false)
    {
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);
        $FarmingController = new FarmingController('Farming');
        $arrayHelper = $FarmingController->ArrayHelper;

        $options = [
            'return' => [
                'array [pc.contract_id, a.id, c1.parent_id] as contract_ids',
                'kvs.gid',
                'c.start_date::date as sublease_contract_start_date',
                'c.due_date::date as sublease_contract_due_date',
                'spa.contract_area as subleased_area',
                'pc.contract_area as total_area',
            ],
        ];

        $groupOptions = [];
        foreach ($rpcParams as $key => $filterParams) {
            $group['where'] = [
                "sales_contracts_plots_{$key}" => [
                    'column' => 'plot_id',
                    'compare' => 'IS',
                    'prefix' => 't2', 'value' => null,
                ],
                "is_sublease_{$key}" => [
                    'column' => 'is_sublease',
                    'compare' => '=',
                    'prefix' => 'c',
                    'value' => true,
                ],
                "active_contract_{$key}" => [
                    'column' => 'active',
                    'compare' => '=',
                    'prefix' => 'c', 'value' => true,
                ],
                "farming_id_{$key}" => [
                    'column' => 'farming_id',
                    'compare' => is_array($filterParams['farming']) ? 'IN' : '=',
                    'prefix' => 'c',
                    'value' => $arrayHelper->filterEmptyStringArr($filterParams['farming']) ?? '',
                ],
                "farming_name_{$key}" => [
                    'column' => 'farming_name',
                    'compare' => is_array($filterParams['farming_name']) ? 'IN' : 'ILIKE',
                    'prefix' => 'c',
                    'value' => $filterParams['farming_name'] ?? '',
                ],
                "ekate_{$key}" => [
                    'column' => 'ekate',
                    'prefix' => 'kvs',
                    'compare' => is_array($filterParams['ekate']) ? 'IN' : '=',
                    'value' => $arrayHelper->filterEmptyStringArr($filterParams['ekate']) ?? '',
                ],
            ];

            if (!empty($rpcParams['for_sublease_farm_years'])) {
                list($startYear, $endYear) = explode('/', $rpcParams['for_sublease_farm_years']);
                $startFarmingYear = "{$startYear}-10-01";
                $endYearYear = "{$endYear}-09-30";

                $group['where']["farm_year_in_owned_contracts_{$key}"] = [
                    'column' => "(
                        (c.due_date is null and c.start_date::date < '{$endYearYear}') 
                        or (c.due_date notnull and (
                            -- The contract starts before the beginning of the farming year and ends after it
                            (c.start_date::date < '{$startFarmingYear}' and c.due_date::date > '{$endYearYear}')
                                -- The contract starts before the beginning of the farming year and ends within it
                            or (c.start_date::date < '{$startFarmingYear}'and c.due_date::date between '{$startFarmingYear}' and '{$endYearYear}') 
                                --The contract starts in the farming year and ends after it
                            or (c.start_date::date between '{$startFarmingYear}' and '{$endYearYear}' and c.due_date::date >'{$endYearYear}') 
                                -- The contract starts and ends within the farming year
                            or (c.start_date::date between '{$startFarmingYear}' and '{$endYearYear}' and c.due_date::date between '{$startFarmingYear}' and '{$endYearYear}') 
                            )
                        )
                    )",
                    'compare' => '=',
                    'value' => 'true',
                ];
            }

            $groupOptions[] = $group;
        }

        $options['whereOrGroup'] = $groupOptions;
        $options['where'] = array_merge($options['where'], self::getPlotStatusesFilter([self::PLOT_STATUS_ACTIVE]));

        $farmingYearsCte = "SELECT
                            generate_series('2012-10-01'::date, '2099-09-30'::date, interval '1 year') AS start_date,
                            generate_series('2013-09-30'::date, '2100-09-30'::date, interval '1 year' ) AS end_date
                        ";
        $subleasedPlotsCte = $UserDbPlotsController->getSubleasedPlotsReport($options, false, true);

        $subleasePerYearCte = 'SELECT
                            sp.contract_ids,
                            sp.gid,
                            fy.start_date::date AS farming_year_start,
                            fy.end_date::date AS farming_year_end,
                            SUM(COALESCE(sp.subleased_area, 0)) AS total_subleased_area,
                            MAX(sp.total_area) AS total_area_for_rent
                        FROM
                            subleased_plots sp
                        CROSS JOIN farming_years fy
                        WHERE
                            sp.sublease_contract_start_date <= fy.end_date
                            AND sp.sublease_contract_due_date >= fy.start_date
                        GROUP BY
                            sp.contract_ids, sp.gid, fy.start_date, fy.end_date
                    ';

        return "WITH farming_years AS({$farmingYearsCte}),
                subleased_plots AS ({$subleasedPlotsCte}),
                sublease_per_year AS ({$subleasePerYearCte})";
    }

    /**
     * Initialize the database controllers, depending on the caller method.
     *
     * @throws TException
     *
     * @internal param string $method method name to load controllers for
     */
    protected function initializeControllers($method)
    {
        switch ($method) {
            // Controllers for generating the plots tree
            case 'getPlots':
                $this->FarmingController = new FarmingController('Farming');
                $this->UsersController = new UsersController('Users');
                $this->UserDbPlotsController = new UserDbPlotsController($this->User->Database);
                $this->UserDbOwnersController = new UserDbOwnersController($this->User->Database);

                break;
                // additional controllers for editing a plot
            case 'loadPlotForEdit':
                $this->UserDbController = new UserDbController($this->User->Database);

                break;
                // additional controllers for saving an edited plot
            case 'updatePlotValues':
                $this->UserDbController = new UserDbController($this->User->Database);
                $this->UsersController = new UsersController('Users');

                break;
                // additional controllers for calculating an area
            case 'calculateUsedAreaByZP':
                $this->UserDbController = new UserDbController($this->User->Database);
                $this->LayersController = new LayersController('Layers');
                $this->UserDbZPlotsController = new UserDbZPlotsController($this->User->Database);

                break;
                // additional controllers for calculating an area
            case 'calculateUsedAreaByOwnage':
                $this->ContractsController = new UserDbContractsController($this->User->Database);
                $this->UserDbController = new UserDbController($this->User->Database);

                break;
                // additional controllers for calculating an area
            case 'calculateUsedAreaByGeom':
                $this->UserDbController = new UserDbController($this->User->Database);

                break;
            case 'executeMultiEdit':
                $this->UserDbPlotsController = new UserDbPlotsController($this->User->Database);
                $this->UserDbController = new UserDbController($this->User->Database);
                $this->FarmingController = new FarmingController('Farming');
                $this->UsersController = new UsersController('Users');
                $this->LayersController = new LayersController('Layers');
                $this->UserDbZPlotsController = new UserDbZPlotsController($this->User->Database);

                break;
            default:
                throw new TException('Invalid method selected', 1);

                break;
        }
    }

    private function getOSZOwner($ownerString)
    {
        $final_owners = [];
        $owners = explode('| ', $ownerString);
        foreach ($owners as $owner) {
            $ownerArray = explode(',', $owner);
            if (count($ownerArray) > 1) {
                array_pop($ownerArray);
                $details = explode('~', $ownerArray[0]);
                $final_owners[] = $details[1] . ' (' . $details[0] . ')';
            }
        }

        if (count($final_owners) > 0) {
            $final_owners = array_unique($final_owners);

            return implode(', ', $final_owners);
        }

        return '-';
    }

    private function getSoldPlotsIds($returnOnlySQL = false)
    {
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);

        $options = [
            'return' => [
                'scpr.contract_id',
                'scpr.plot_id',
                'SUM(scpr.contract_area_for_sale) OVER (PARTITION BY scpr.contract_id ORDER BY sc.start_date ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) AS cumulative_sold_area',
                'MAX(scpr.contract_area) as total_area',
                'sc.start_date as sale_start_date',
            ],
            'group' => 'scpr.contract_id,
					    scpr.plot_id,
					    sc.start_date,
					    scpr.contract_area_for_sale',
        ];

        $results = $UserDbPlotsController->getSoldPlotsData($options, false, $returnOnlySQL);

        if ($returnOnlySQL) {
            return $results;
        }

        return array_column($results, 'plot_id');
    }

    private function generateDateOverlapCondition($startDate, $endDate): string
    {
        return "(
                    -- The annex starts before the beginning of the farming year and ends after it
                    (
                        a.start_date::date < {$startDate}
                            and a.due_date::date > {$endDate}
                    )
                    -- The annex starts before the beginning of the farming year and ends within it
                    or (
                        a.start_date::date < {$startDate}
                            and a.due_date::date between {$startDate} and {$endDate}
                    )
                    --The annex starts in the farming year and ends after it
                    or (
                        a.start_date::date between {$startDate} and {$endDate}
                            and a.due_date::date > {$endDate}
                    )
                    -- The annex starts and ends within the farming year
                    or (
                        a.start_date::date between {$startDate} and {$endDate}
                            and a.due_date::date between {$startDate} and {$endDate}
                    )
                )";
    }

    private function buildGroupingOptions(array $filterParams, ?string $groupBy, ?string $sort, ?string $order): array
    {
        $groupByColumns = [];

        if (isset($filterParams['owner_name'])) {
            array_push($groupByColumns, 'o.id');
        }

        if (isset($filterParams['rep_name'])) {
            array_push($groupByColumns, 'o_r.id');
        }

        if (isset($filterParams['egn_subekt'])) {
            array_push($groupByColumns, 'tkvs.egn_subekt');
        }

        $groupingOptions = [
            'gid' => [
                'group' => implode(', ', ['kvs.gid', 'uccps.contracts_count_by_status', ...$groupByColumns]),
                'select' => 'kvs.gid',
            ],
            'mestnost' => [
                'group' => implode(', ', ['kvs.mestnost', ...$groupByColumns]),
                'select' => 'kvs.mestnost',
            ],
            'virtual_category_title' => [
                'group' => implode(', ', ['kvs.virtual_category_title', ...$groupByColumns]),
                'select' => 'kvs.virtual_category_title',
            ],
            'virtual_ntp_title' => [
                'group' => implode(', ', ['kvs.virtual_ntp_title', ...$groupByColumns]),
                'select' => 'kvs.virtual_ntp_title',
            ],
            'owners_osz' => [
                'group' => implode(', ', array_unique(['tkvs.owner_name', 'tkvs.egn_subekt', ...$groupByColumns])),
                'select' => "
                    STRING_AGG(DISTINCT 
                        CASE WHEN tkvs.egn_subekt NOTNULL
                            THEN tkvs.owner_name || ' (' || tkvs.egn_subekt || ')'
                            ELSE tkvs.owner_name
                        END,
                        ', '
                    )",
            ],
            'allowable_type' => [
                'group' => implode(', ', ['kvs.allowable_type', ...$groupByColumns]),
                'select' => 'kvs.allowable_type',
            ],
        ];

        $result = $groupingOptions['gid'];
        if (isset($groupingOptions[$groupBy])) {
            $result = $groupingOptions[$groupBy];
        }

        if (strlen($sort ?? '') > 0 && strlen($order ?? '') > 0 && !$groupBy && false === strpos($result['group'], $sort)) {
            // Add the sort column to group by clause
            $result['group'] .= ", {$sort}";
        }

        return $result;
    }

    private function buildSelect(array $groupingOptions, ?string $groupBy, ?LayerStyles $layerStyles, ?UserLayers $layer): array
    {
        if (!$groupBy) {
            // Return flat result
            return [
                'kvs.gid', 'ST_Area(kvs.geom) as area_kvs', 'kvs.area_type',
                'uccps.contracts_count_by_status AS number_of_contracts',
                'kvs.category as category_id', "coalesce(kvs.virtual_category_title, '-') as category",
                'get_kvs_plot_status(kvs.is_edited, kvs.edit_active_from) as plot_status',
                'kvs.ekate as ekate', 'kvs.has_contracts', 'kvs.include', 'kvs.virtual_ntp_title', 'kvs.virtual_category_title',
                'kvs.kad_ident', 'kvs.masiv', "coalesce(kvs.mestnost, '-') as mestnost", 'kvs. number', 'kvs.participate', 'kvs.white_spots', 'kvs.used_area',
                'case when document_area is null OR document_area = 0 then ST_AREA(kvs.geom)/1000 else document_area end as document_area',
                'kvs.is_edited', "to_char(kvs.edit_active_from,'DD.MM.YYYY') as edit_date",
                "to_char(kvs.edit_active_from,'DD.MM.YYYY') as edit_active_from",
                "(case when string_agg(tkvs.egn_subekt, ',') is not null then string_agg(tkvs.egn_subekt || '~' || tkvs.ime_subekt, '| ') else string_agg(tkvs.ime_subekt, ', ') end) as ime_subekt",
                "(case when string_agg(tkvs.egn_subekt, ',') is not null then string_agg(distinct tkvs.owner_name || ' (' || tkvs.egn_subekt || ')', ', ') else string_agg(tkvs.owner_name, ', ') end) as owners_osz",
                'irrigated_area',
                "coalesce(kvs.comment,'-') as comment", "coalesce(kvs.old_kad_ident,'-') as old_kad_ident",
                'count(*) as full_count',
                'SUM (round(kvs.used_area::numeric,3)) AS total_used_area',
                'SUM (round((ST_Area(kvs.geom) / 1000)::numeric,3)) AS total_area_kvs',
                'SUM (round(CASE WHEN kvs.document_area is null then st_area(kvs.geom)/1000 else kvs.document_area end::numeric,3)) AS total_document_area',
                'SUM (round(kvs.allowable_area::numeric,3)) AS total_allowable_area ',
                'kvs.allowable_area',
                "round((case when kvs.allowable_area is not null and kvs.allowable_area > 0 then kvs.allowable_area / (ST_AREA(kvs.geom)/1000) * 100 else 0 end)::numeric,2) || ' %' as allow_prec",
                'allowable_type',
                'kvs.block',
                'kvs.geom',
                'kvs.mestnost',
                'kvs.fill_color',
                'kvs.border_color',
                'kvs.virtual_ekatte_name',
            ];
        }

        // Return JSONB objects WITH children

        $isColoringTypeByAttribute = isset($layerStyles) && LayerStyles::BY_ATTRIBUTE_COLORING_TYPE === $layerStyles->type;

        $virtualColumnDefinitions = UserLayers::filterDefinitions($layer->getDefinitions(), [['col_virtual' => true]]);
        $virtualColumnDefinitions = array_combine(array_column($virtualColumnDefinitions, 'col_reference'), $virtualColumnDefinitions);

        $fillVirtualColumnName = $virtualColumnDefinitions[$layerStyles->fill_column_name]['col_name'];
        $borderVirtualColumnName = $virtualColumnDefinitions[$layerStyles->border_column_name]['col_name'];

        $fillColumnName = LayerStyles::BY_ATTRIBUTE_COLORING_TYPE === $layerStyles->type && $layerStyles->fill_column_name
            ? ($fillVirtualColumnName ? "'{$fillVirtualColumnName}'" : "'{$layerStyles->fill_column_name}'")
            : 'NULL';
        $borderColumnName = LayerStyles::BY_ATTRIBUTE_COLORING_TYPE === $layerStyles->type && $layerStyles->border_column_name
            ? ($borderVirtualColumnName ? "'{$borderVirtualColumnName}'" : "'{$layerStyles->border_column_name}'")
            : 'NULL';

        if ('gid' === $groupBy) {
            $showFillAndBorderColors = $isColoringTypeByAttribute ? 'TRUE' : 'FALSE';
            // Group by gid means no grouping
            return ["
                -- NOTE: 
                -- The area values are casted to text, because JSON doesn't support the concept of fixed precision for numeric values
                -- and returns all digits after the decimal point
                JSONB_BUILD_OBJECT(
                    'key', uuid_generate_v4(),
                    'gid', kvs.gid,
                    'geom', kvs.geom,
                    'kad_ident', kad_ident,
                    'mestnost', mestnost,
                    'virtual_category_title', virtual_category_title,
                    'virtual_ntp_title', virtual_ntp_title,
                    'allowable_area', ROUND(allowable_area::numeric, 3)::TEXT,
                    'allow_prec', ROUND(
                        (
                            CASE WHEN kvs.allowable_area NOTNULL and kvs.allowable_area > 0 
                                THEN kvs.allowable_area / (ST_AREA(kvs.geom)/1000) * 100 
                                ELSE 0 
                            END
                        )::numeric,
                        2
                    )::TEXT || ' %',
                    'number_of_contracts', uccps.contracts_count_by_status,
                    'owners_osz', STRING_AGG(DISTINCT 
                        CASE WHEN tkvs.egn_subekt NOTNULL
                            THEN tkvs.owner_name || ' (' || tkvs.egn_subekt || ')'
                            ELSE tkvs.owner_name
                        END,
                        ', '
                    ),
                    'allowable_type', kvs.allowable_type,
                    'used_area', ROUND(kvs.used_area::numeric, 3)::TEXT,
                    'area_kvs', ROUND((ST_Area(kvs.geom) / 1000)::numeric, 3)::TEXT,
                    'document_area', ROUND(
                        (
                            CASE WHEN kvs.document_area IS NULL
                                THEN st_area(kvs.geom)/1000 
                                ELSE kvs.document_area 
                            END
                        )::numeric,
                        3
                    )::TEXT,
                    'plot_status', get_kvs_plot_status(kvs.is_edited, kvs.edit_active_from),
                    'fill_color', CASE WHEN {$showFillAndBorderColors} THEN kvs.fill_color ELSE NULL END,
                    'fill_column_name', {$fillColumnName},
                    'border_color', CASE WHEN {$showFillAndBorderColors} THEN kvs.border_color ELSE NULL END,
                    'border_column_name', {$borderColumnName},
                    'virtual_ekatte_name', kvs.virtual_ekatte_name,
                    'children', '[]'::JSONB
            ) AS row"];
        }

        $groupByExpression = $groupingOptions['select'];

        /**
         * NOTE:.
         *
         * The fill and border colors of the parent rows are shown only when the layer style type is 'by attribute'
         * and the groupBy column is the same as the fill column name or border column name
         */
        $showFillColor = $isColoringTypeByAttribute && in_array($groupBy, [$fillVirtualColumnName, $layerStyles->fill_column_name]) ? 'TRUE' : 'FALSE';
        $showBorderColor = $isColoringTypeByAttribute && in_array($groupBy, [$borderVirtualColumnName, $layerStyles->border_column_name]) ? 'TRUE' : 'FALSE';

        return ["
                -- NOTE:
                -- The area values are casted to text, because JSON doesn't support the concept of fixed precision for numeric values
                -- and returns all digits after the decimal point
                --
                -- NOTE 1:
                -- To avoid duplicates the chilren are agregatted with JSONB_AGG using DISCTICT.
                -- This is not applicable for the area values in the parent because there might be 
                -- multiple plots with same area, so using DISTINCT will not work.
                JSONB_BUILD_OBJECT(
                    'key', uuid_generate_v4(),
                    'gid', ROW_NUMBER() OVER (), -- ensure there is key 'gid' in the result (needed in FE)
                    'geom', NULL,
                    'kad_ident', NULL,
                    'mestnost', NULL,
                    'virtual_category_title', NULL,
                    'virtual_ntp_title', NULL,
                    'allowable_area', NULL,
                    'allow_prec', NULL,
                    'number_of_contracts', NULL,
                    'owners_osz', NULL,
                    'allowable_type', NULL,
                    'used_area', NULL,
                    'area_kvs', NULL,
                    'document_area', NULL,
                    'plot_status', NULL,
                    'fill_color', CASE WHEN {$showFillColor} THEN MAX(kvs.fill_color) ELSE NULL END,
                    'fill_column_name', {$fillColumnName},
                    'border_color', CASE WHEN {$showBorderColor} THEN MAX(kvs.border_color) ELSE NULL END,
                    'border_column_name', {$borderColumnName},
                    'virtual_ekatte_name', NULL,
                    '{$groupBy}', CASE WHEN {$groupByExpression} IS NULL THEN 'No value' ELSE normalize_value(({$groupByExpression})::TEXT) END, -- overwrite the value for the column we use for grouping
                    'children', JSONB_AGG(DISTINCT
                        JSONB_BUILD_OBJECT(
                            'gid', kvs.\"gid\",
                            'geom', kvs.\"geom\",
                            'kad_ident', \"kad_ident\",
                            'mestnost', \"mestnost\",
                            'virtual_category_title', \"virtual_category_title\",
                            'virtual_ntp_title', \"virtual_ntp_title\",
                            'allowable_area', ROUND(\"allowable_area\"::numeric, 3)::TEXT,
                            'allow_prec', ROUND(
                                (
                                    CASE WHEN kvs.allowable_area NOTNULL AND kvs.allowable_area > 0 
                                        THEN kvs.allowable_area / (ST_AREA(kvs.geom)/1000) * 100 
                                        ELSE 0 
                                    END
                                )::numeric,
                                2
                            )::TEXT || ' %',
                            'number_of_contracts', uccps.contracts_count_by_status,
                            'owners_osz', CASE WHEN tkvs.egn_subekt NOTNULL
                                THEN tkvs.owner_name || ' (' || tkvs.egn_subekt || ')'
                                ELSE tkvs.owner_name
                            END,
                            'allowable_type', kvs.allowable_type,
                            'used_area', ROUND(kvs.used_area::numeric, 3)::TEXT,
                            'area_kvs', ROUND((ST_Area(kvs.geom) / 1000)::numeric, 3)::TEXT,
                            'document_area', ROUND(
                                (
                                    CASE WHEN kvs.document_area IS NULL
                                        THEN st_area(kvs.geom)/1000 
                                        ELSE kvs.document_area 
                                    END
                                )::numeric,
                                3
                            )::TEXT,
                            'plot_status', get_kvs_plot_status(kvs.is_edited, kvs.edit_active_from),
                            'fill_color', kvs.fill_color,
                            'fill_column_name', {$fillColumnName},
                            'border_color', kvs.border_color,
                            'border_column_name', {$borderColumnName},
                            'virtual_ekatte_name', kvs.virtual_ekatte_name
                        )
                    )
            ) AS row"];
    }

    private function buildSort(UserLayers $layer, string $sort, string $order, ?string $groupBy): ?string
    {
        $formattedSort = null;

        if (!strlen($sort ?? '') || !strlen($order ?? '')) {
            return $formattedSort;
        }

        if (!isset($groupBy)) {
            $layerIdColumnName = $layer->getDefinitionByCategory(Config::LAYER_COLUMN_CATEGORY_GID)['col_name'];
            $formattedSort = 'kvs.' . ('kad_ident' === $sort ? 'kad_ident COLLATE "alpha_numeric_bg"' : $sort);
            $formattedSort .= " {$order}";

            if ($sort !== $layerIdColumnName) {
                $formattedSort .= ", kvs.{$layerIdColumnName} {$order}";
            }

            return $formattedSort;
        }

        switch ($sort) {
            case 'area_kvs':
                $formattedSort = 'SUM(ROUND((ST_Area(kvs.geom) / 1000)::numeric, 3))';

                break;
            case 'allowable_area':
                $formattedSort = 'SUM(ROUND(COALESCE(kvs.allowable_area, 0)::numeric, 3))';

                break;
            case 'used_area':
                $formattedSort = 'SUM(ROUND(COALESCE(kvs.used_area, 0)::numeric, 3))';

                break;
            case 'document_area':
                $formattedSort = 'SUM(ROUND(
                        (
                            CASE WHEN kvs.document_area IS NULL
                                THEN st_area(kvs.geom)/1000 
                                ELSE kvs.document_area 
                            END
                        )::numeric,
                        3
                    ))';

                break;
            case 'owners_osz':
                $formattedSort = "STRING_AGG(
                        CASE WHEN tkvs.egn_subekt NOTNULL
                            THEN tkvs.owner_name || ' (' || tkvs.egn_subekt || ')'
                            ELSE tkvs.owner_name
                        END,
                        ', '
                    )";

                break;
            case 'number_of_contracts':
                $formattedSort = "SUM(COALESCE((uccps.contracts_count_by_status->>'All')::INT, 0))";

                break;
            default:
                $formattedSort = null;
        }

        return isset($formattedSort)
            ? "{$formattedSort} {$order}"
            : null;
    }

    private function getGroupedPlotsResult(UserLayers $layer, array $results, array $totalData, ?string $groupBy): array
    {
        $rows = [];
        $gidDef = $layer->getDefinitionByCategory(Config::LAYER_COLUMN_CATEGORY_GID);

        foreach ($results as $result) {
            $row = json_decode($result['row'], true);

            if ($groupBy !== $gidDef['col_name']) {
                /**
                 * Note:
                 * The parent areas are calculated here when grouped by column different than gid, because the children records are distincted in the query
                 * and it is not ok to use SUM(distinct ..) for area columns in the parent records (there might be different plots with same area).
                 */
                $row['document_area'] = 0;
                $row['allowable_area'] = 0;
                $row['area_kvs'] = 0;

                $processedGids = [];
                foreach ($row['children'] as $child) {
                    if (!in_array($child['gid'], $processedGids)) {
                        $row['document_area'] += $child['document_area'];
                        $row['allowable_area'] += $child['allowable_area'];
                        $row['area_kvs'] += $child['area_kvs'];
                        $processedGids[] = $child['gid'];
                    }
                }

                $row['document_area'] = round($row['document_area'], 3);
                $row['allowable_area'] = round($row['allowable_area'], 3);
                $row['area_kvs'] = round($row['area_kvs'], 3);
            }

            $rows[] = $row;
        }

        // Generate header
        $layerDefinitions = UserLayers::filterDefinitions(
            $layer->getDefinitions(),
            [['col_visible' => true]]
        );

        $layerDefinitions = array_combine(array_column($layerDefinitions, 'col_name'), $layerDefinitions);

        $geomDef = $layer->getDefinitionByCategory(Config::LAYER_COLUMN_CATEGORY_GEOM);
        $columns = array_keys($rows[0] ?? []);

        $header = [];

        /**
         * @var array $excludeColumns Columns to exclude from the header
         */
        $excludeColumns = [
            $gidDef['col_name'], $geomDef['col_name'], 'children', 'key',
            'fill_color', 'border_color', 'used_area', 'fill_column_name', 'border_column_name',
        ];
        foreach ($columns as $column) {
            if (in_array($column, $excludeColumns)) {
                continue;
            }

            $header[] = [
                'field' => $layerDefinitions[$column]['col_name'] ?? $column,
                'title' => $layerDefinitions[$column]['col_title'] ?? ucfirst(str_replace('_', ' ', $column)),
                'visible' => $layerDefinitions[$column]['col_visible'] ?? true,
                'sortable' => $layerDefinitions[$column]['col_sortable'] ?? true,
            ];
        }

        return [
            'header' => $header,
            'rows' => $rows,
            'total' => $totalData['count'] ?? 0,
            'footer' => [
                'document_area' => $totalData['document_area'] ?? 0,
                'allowable_area' => $totalData['allowable_area'] ?? 0,
                'area_kvs' => $totalData['area_kvs'] ?? 0,
            ],
            'filtered_gids' => $totalData['filtered_gids'],
        ];
    }

    private function getPlotsTotalData(UserLayers $layer, array $options, array $groupingOptions): array
    {
        $gidDef = $layer->getDefinitionByCategory(Config::LAYER_COLUMN_CATEGORY_GID);
        $countColumn = $groupingOptions['select'] ?? "kvs.{$gidDef['col_name']}";

        $totalColumns = [
            'area_kvs' => 'ROUND((ST_Area(kvs.geom) / 1000)::numeric, 3)',
            'allowable_area' => 'ROUND(COALESCE(kvs.allowable_area, 0)::numeric, 3)',
            'document_area' => 'ROUND(COALESCE(kvs.document_area, ST_Area(kvs.geom) / 1000)::numeric, 3)',
            'used_area' => 'ROUND(COALESCE(kvs.used_area, 0)::numeric, 3)',
            'count' => $countColumn,
            'gid' => "kvs.{$gidDef['col_name']}",
        ];

        /**
         * Generate subquery with all filters applied in $options that will be used to select
         * the columns distincted by gid.
         */
        $subQueryOptions = array_merge(
            $options,
            [
                'return' => array_reduce(
                    array_keys($totalColumns),
                    function ($carry, $key) use ($totalColumns) {
                        $carry[] = "{$totalColumns[$key]} AS {$key}";

                        return $carry;
                    },
                    []
                ),
                'group' => "kvs.{$gidDef['col_name']}",
                'limit' => null,
                'offset' => null,
                'sort' => null,
            ]
        );
        $subQuerySql = $this->UserDbPlotsController->getPlotsData($subQueryOptions, false, true);

        /**
         * Build the query options for selecting the distincted values from the subquery with an aggregate functions.
         */
        $queryOptions = [
            'return' => array_reduce(
                array_keys($totalColumns),
                function ($carry, $key) {
                    if ('count' === $key) {
                        // Note: Use array_length and array_agg instead of count in order to count the null values too;
                        $carry[] = "ARRAY_LENGTH(ARRAY_AGG(DISTINCT {$key}), 1) as {$key}";

                        return $carry;
                    }

                    if ('gid' === $key) {
                        $carry[] = "    JSONB_AGG(DISTINCT {$key}) as filtered_gids";

                        return $carry;
                    }

                    $carry[] = "SUM({$key}) AS {$key}";

                    return $carry;
                },
                []
            ),
            'tablename' => "({$subQuerySql}) AS kvs",
        ];

        [$result] = $this->UserDbPlotsController->getItemsByParams($queryOptions);
        $result['filtered_gids'] = json_decode($result['filtered_gids'] ?? [], true);
        $result['filtered_gids'] = array_slice($result['filtered_gids'] ?? [], 0, self::KVS_MAX_FILTERED_GIDS_COUNT);

        return $result;
    }
}
