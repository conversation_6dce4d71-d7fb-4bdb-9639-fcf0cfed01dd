<?php

namespace TF\Engine\APIClasses\Annexes;

use DateTime;
use Prado\Prado;
use Prado\Web\Services\TRpcApiProvider;
use TF\Application\Common\Config;
use TF\Engine\APIClasses\KVSContractsUpdate\KVSContractsUpdate;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbContracts\UserDbContractsController;
use TF\Engine\Plugins\Core\UserDbOwners\UserDbOwnersController;
use TF\Engine\Plugins\Core\UserDbPayments\UserDbPaymentsController;
use TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController;
use TF\Engine\Plugins\Core\UserDbSubleases\UserDbSubleasesController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * @rpc-module Annexes
 *
 * @rpc-service-id annexes-plots-grid
 */
class AnnexesPlotsGrid extends TRpcApiProvider
{
    use KVSContractsUpdate;
    private $module = 'Annexes';
    private $service_id = 'annexes-plots-grid';

    /**
     * Registers the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'read']],
            'deleteAnnexPlots' => ['method' => [$this, 'deletePlots']],
            'addAnnexPlotRelation' => ['method' => [$this, 'addAnnexPlotRelation']],
        ];
    }

    /**
     * Reads the plots of a subleased contract.
     *
     * @param array $params {
     * @param null|mixed $page
     * @param null|mixed $rows
     * @param null|mixed $sort
     * @param null|mixed $order
     *
     * @item string type
     * @item integer sublease_id
     * @item boolean many_contracts
     * @item integer sublease_farming_id
     * @item string  sublease_due_date
     * @item string  sublease_start_date
     * @item array  filters{
     * @item string  ekate
     * @item string  masiv
     * @item string  number
     *     }
     * }
     *
     * @return array
     */
    public function read($params = [], $page = null, $rows = null, $sort = null, $order = null)
    {
        $type = $params['type'];
        $annex_id = $params['annex_id'];
        $contract_id = $params['contract_id'];
        $filters = $params['filters'];

        $filterEkatte = $params['filters']['ekate'];
        $filterMasiv = $params['filters']['masiv'];
        $filterNumber = $params['filters']['number'];

        $UserDbController = new UserDbController($this->User->Database);
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);
        $UsersController = new UsersController('Users');
        $FarmingController = new FarmingController('Farming');
        $UserDbSubleasesController = new UserDbSubleasesController($this->User->Database);
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);
        $UserDbContractsController = new UserDbContractsController($this->User->Database);

        // create default return
        $return = [
            'rows' => [],
            'total' => 0,
            'footer' => [
                [
                    'kad_ident' => '<b>ОБЩО</b>',
                    'used_area' => '',
                    'area' => '',
                ],
            ],
        ];

        // check if default grid is not loaded with contract_id = 0
        if (!$annex_id || 0 == $annex_id) {
            return $return;
        }

        // get contract rent in money
        $contractDataOptions = [
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $annex_id],
            ],
        ];
        $contractDataResults = $UserDbContractsController->getContractsData($contractDataOptions, false, false);

        $options = [
            'sort' => $sort,
            'order' => $order,
            'return' => [
                'kvs.gid', 'COALESCE(kvs.kad_ident, \'[Няма информация]\') as kad_ident',
                'kvs.ekate',
                'kvs.virtual_ekatte_name as land',
                'kvs.virtual_category_title as category',
                'kvs.virtual_ntp_title as area_type',
                'kvs.is_edited',
                'kvs.edit_active_from',
                'round(pc.contract_area::numeric, 3) as contract_area',
                'round(kvs.document_area::numeric, 3) as document_area',
                'round(pc.area_for_rent::numeric, 3 ) as area_for_rent',
                'round(pc.rent_per_plot::numeric, 2 ) as rent_per_plot',
                'round((st_area(geom)/1000)::numeric, 3) as kvs_area',
                'pc.annex_action', 'pc.id as pc_rel_id',
            ],
            'where' => [
                'contract_id' => ['column' => 'contract_id', 'compare' => '=', 'prefix' => 'pc', 'value' => $annex_id],
                // filters
                'ekate' => ['column' => 'ekate', 'compare' => '=', 'prefix' => 'kvs', 'value' => $filterEkatte],
                'masiv' => ['column' => 'masiv', 'compare' => '=', 'prefix' => 'kvs', 'value' => $filterMasiv],
                'number' => ['column' => 'number', 'compare' => '=', 'prefix' => 'kvs', 'value' => $filterNumber],
            ],
        ];

        if (!$type || '' == $type) {
            return $return;
        }
        switch ($type) {
            case 'view': // used when displaying already added elements
                $options['id_string'] = null;

                break;

            case 'add': // use when displaying non added elements
                $options['where']['annex_action'] = ['column' => 'annex_action', 'compare' => '=', 'prefix' => 'pc', 'value' => 'removed'];

                break;
        }

        // get all ekate data
        $ekateData = $UsersController->getAllEkatteData();
        $ekateDataCount = count($ekateData);
        $ekateNames = [];
        for ($i = 0; $i < $ekateDataCount; $i++) {
            $ekateNames[$ekateData[$i]['ekatte_code']] = $ekateData[$i]['ekatte_name'];
        }

        $counter = $UserDbContractsController->getContractPlotData($options, true, false);
        $result = $UserDbContractsController->getContractPlotData($options, false, false);

        $total_area = 0;
        $total_contract_area = 0;
        $removedAnnexes = 0;
        $resultsCount = count($result);
        for ($i = 0; $i < $resultsCount; $i++) {
            $result[$i]['used_area'] = number_format($result[$i]['used_area'], 3, '.', '');
            $result[$i]['area'] = number_format($result[$i]['area'] / 1000, 3, '.', '');
            $result[$i]['rent_per_plot'] = (null !== $result[$i]['rent_per_plot']) ? $result[$i]['rent_per_plot'] : $contractDataResults[0]['renta'];
            $result[$i]['rent_per_plot'] = number_format($result[$i]['rent_per_plot'], 2, '.', '');

            if (!$result[$i]['document_area']) {
                $result[$i]['document_area'] = $result[$i]['area'];
            }

            if (!$result[$i]['contract_area']) {
                $result[$i]['contract_area'] = ($result[$i]['document_area'] < $result[$i]['kvs_area']) ? $result[$i]['document_area'] : $result[$i]['kvs_area'];
            }

            if ('view' == $type) {
                if ('removed' == $result[$i]['annex_action'] || true == $result[$i]['is_edited']) {
                    $removedAnnexes++;
                } else {
                    $total_area += $result[$i]['document_area'];
                    $total_contract_area += $result[$i]['contract_area'];
                }
            } else {
                // type add
                $total_area += $result[$i]['document_area'];
                $total_contract_area += $result[$i]['contract_area'];
            }

            $result[$i]['contract_area'] = number_format($result[$i]['contract_area'], 3, '.', '');
            $result[$i]['document_area'] = number_format($result[$i]['document_area'], 3, '.', '');
        }

        $return['rows'] = $result;
        $return['total'] = $counter[0]['count'];
        $return['footer'] = [
            [
                'kad_ident' => '<b>ОБЩО</b>',
                'contract_area' => number_format($total_contract_area, 3, '.', ''),
                'document_area' => number_format($total_area, 3, '.', ''),
            ],
        ];

        if ('view' == $type) {
            $return['footer'][] = [
                'kad_ident' => '<b>Брой имоти</b>',
                'contract_area' => $counter[0]['count'] - $removedAnnexes,
            ];
        }

        return $return;
    }

    /**
     * @param array $params
     */
    public function deletePlots($params)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        $UserDbContractsController = new UserDbContractsController($this->User->Database);
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableContracts,
            'return' => [
                'parent_id',
                'active',
            ],
            'where' => [
                'contract_id' => ['column' => 'id', 'compare' => '=', 'value' => $params['contract_id']],
            ],
        ];

        $annex = $UserDbController->getItemsByParams($options);
        $annexData = $annex[0];

        $options = [
            'tablename' => $UserDbController->DbHandler->tableContracts,
            'return' => [
                'due_date',
            ],
            'where' => [
                'contract_id' => ['column' => 'id', 'compare' => '=', 'value' => $annexData['parent_id']],
            ],
        ];

        $result = $UserDbController->getItemsByParams($options);
        $contractData = $result[0];
        $oldOptions = [
            'tablename' => $UserDbController->DbHandler->contractsPlotsRelTable . ' as cp',
            'return' => [
                'contracts.start_date',
                'contracts.due_date',
                'contracts.parent_id',
                'cp.*',
                'max(file_id) as file_id',
                'max(date_uploaded) as date_uploaded',
                'kvs.ekate',
                'kvs.kad_ident',
            ],
            'innerjoin' => [
                'table' => $UserDbController->DbHandler->tableContracts . ' as contracts',
                'condition' => ' ON (contracts.id = cp.contract_id)',
            ],
            'leftjoin' => [
                'table' => $UserDbController->DbHandler->tableKVS . ' kvs',
                'condition' => '  ON cp.plot_id = kvs.gid',
            ],
            'joins' => [
                "LEFT JOIN dblink('host=" . DEFAULT_DB_HOST . ' port=' . DEFAULT_DB_PORT . ' user=' . DEFAULT_DB_USERNAME . ' password=' . DEFAULT_DB_PASSWORD . ' dbname=' . DEFAULT_DB_DATABASE . "',
                'SELECT id AS file_id, ekate AS ekatte_code, date_uploaded, group_id, shape_type FROM su_users_files') AS files (file_id integer, ekatte_code text, date_uploaded timestamp, group_id integer, shape_type integer) 
                ON kvs.ekate = files.ekatte_code AND files.group_id = {$this->User->GroupID} AND files.shape_type = " . Config::LAYER_TYPE_KVS_OSZ,
            ],
            'where' => [
                'id' => ['column' => 'cp.id', 'compare' => '=', 'value' => $params['pc_rel_id']],
            ],
            'group' => 'cp.id, kvs.ekate, kvs.kad_ident, contracts.id',
        ];

        $oldData = $UserDbController->getItemsByParams($oldOptions);
        $oldPlotData = $oldData[0];

        $additionalParams = [];
        if (!empty($oldPlotData['parent_id'])) {
            $additionalParams = [
                'parent_id' => $oldPlotData['parent_id'],
                'start_date' => $oldPlotData['start_date'],
                'due_date' => $oldPlotData['due_date'],
            ];
        }
        $UserDbPaymentsController->hasPaymentRestriction($params['contract_id'], $additionalParams);

        if ($annexData['active']) {
            $plotsWithActiveContracts = $UserDbContractsController->getNotAvailableContractsPlots(
                $oldPlotData['start_date'],
                $oldPlotData['due_date'],
                [$oldPlotData['parent_id'], $params['contract_id']],
                [],
                [$oldPlotData['plot_id']]
            );
            $UserDbContractsController->validatePlotAreas([$oldPlotData], $plotsWithActiveContracts);
        }

        // remove plot from annex by changing annex_action to 'removed'
        $options = [
            'tablename' => $UserDbController->DbHandler->contractsPlotsRelTable,
            'mainData' => [
                'annex_action' => 'removed',
                'contract_end_date' => $contractData['due_date'],
            ],
            'where' => ['id' => $params['pc_rel_id']],
        ];

        $UserDbController->editItem($options);
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options, $oldData, 'Remove plot from annex');

        $UserDbContractsController->manageOverallRenta($params['contract_id']);

        if ($oldData[0]['file_id']) {
            $this->tryResolveContracts(
                $oldData[0]['file_id'],
                $oldData[0]['ekate'],
                [
                    [
                        'kad_ident' => $oldData[0]['kad_ident'],
                        'edit_active_from' => (DateTime::createFromFormat('Y-m-d H:i:s.u', $oldData[0]['date_uploaded']))->modify('-1 day')->format('Y-m-d'),
                    ],
                ]
            );
        }
    }

    /**
     * @param array $params
     */
    public function addAnnexPlotRelation($params)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        $UserDbContractsController = new UserDbContractsController($this->User->Database);

        $id_array = [];
        if (!$params['plot_data_array'] || 0 == count($params['plot_data_array']) || !$params['contract_id'] || !(int) $params['contract_id'] || !$params['annex_id'] || !(int) $params['annex_id']) {
            return [];
        }

        $options = [
            'tablename' => $UserDbController->DbHandler->tableContracts,
            'return' => [
                'start_date', 'due_date', 'renta', 'active',
            ],
            'where' => [
                'contract_id' => ['column' => 'id', 'compare' => '=', 'value' => $params['annex_id']],
            ],
        ];

        $result = $UserDbController->getItemsByParams($options);
        $contractData = $result[0];

        if ($contractData['active']) {
            // Check contracts plots exist in another contract with overlapping period
            $plotsWithActiveContracts = $UserDbContractsController->getNotAvailableContractsPlots(
                $contractData['start_date'],
                ($contractData['due_date']),
                [$params['annex_id']],
                [
                    'add_plot_to_annex' => true,
                ],
                array_column($params['plot_data_array'], 'gid')
            );
            $UserDbContractsController->validatePlotAreas($params['plot_data_array'], $plotsWithActiveContracts);
        }

        $count = count($params['plot_data_array']);
        for ($i = 0; $i < $count; $i++) {
            $oldOptions = [
                'tablename' => $UserDbController->DbHandler->contractsPlotsRelTable,
                'return' => [
                    '*',
                ],
                'where' => [
                    'id' => ['column' => 'id', 'compare' => '=', 'value' => $params['plot_data_array'][$i]['pc_rel_id']],
                ],
            ];

            $oldData = $UserDbController->getItemsByParams($oldOptions);

            if ($contractData['retna'] == $params['plot_data_array'][$i]['rent_per_plot']) {
                $params['plot_data_array'][$i]['rent_per_plot'] = null;
            }

            // edit plot-contract relation
            $options = [
                'tablename' => $UserDbController->DbHandler->contractsPlotsRelTable,
                'mainData' => [
                    'contract_id' => $params['annex_id'],
                    'plot_id' => $params['plot_data_array'][$i]['gid'],
                    'contract_area' => $params['plot_data_array'][$i]['contract_area'],
                    'area_for_rent' => $params['plot_data_array'][$i]['area_for_rent'],
                    'rent_per_plot' => $params['plot_data_array'][$i]['rent_per_plot'],
                    'annex_action' => 'added',
                    'contract_end_date' => $contractData['due_date'],
                ],
                'where' => ['id' => $params['plot_data_array'][$i]['pc_rel_id']],
            ];

            $UserDbController->editItem($options);

            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options, $oldData, 'edit plot from annex');

            $groupId = Prado::getApplication()->getUser()->GroupID;

            $oldOptions = [
                'tablename' => $UserDbController->DbHandler->tableKVS . ' as kvs',
                'return' => [
                    'document_area',
                    'ekate',
                    'kad_ident',
                    'max(date_uploaded) as date_uploaded',
                    'max(file_id) as file_id',
                ],
                'joins' => [
                    "LEFT JOIN dblink('host=" . DEFAULT_DB_HOST . ' port=' . DEFAULT_DB_PORT . ' user=' . DEFAULT_DB_USERNAME . ' password=' . DEFAULT_DB_PASSWORD . ' dbname=' . DEFAULT_DB_DATABASE . "',
                    'SELECT id AS file_id, ekate AS ekatte_code, date_uploaded, group_id, shape_type FROM su_users_files') AS files (file_id integer, ekatte_code text, date_uploaded timestamp, group_id integer, shape_type integer) 
                    ON kvs.ekate = files.ekatte_code AND files.group_id = {$groupId} AND files.shape_type = " . Config::LAYER_TYPE_KVS_OSZ,
                ],
                'where' => [
                    'gid' => ['column' => 'gid', 'compare' => '=', 'value' => $params['plot_data_array'][$i]['gid']],
                ],
                'group' => 'kvs.ekate, kvs.kad_ident, kvs.ekate, kvs.document_area',
            ];

            $oldData = $UserDbController->getItemsByParams($oldOptions);

            $options = [
                'tablename' => $UserDbController->DbHandler->tableKVS,
                'mainData' => [
                    'document_area' => $params['plot_data_array'][$i]['document_area'],
                ],
                'where' => ['gid' => $params['plot_data_array'][$i]['gid']],
            ];

            $UserDbController->editItem($options);

            if ($oldData[0]['file_id']) {
                $this->tryResolveContracts(
                    $oldData[0]['file_id'],
                    $oldData[0]['ekate'],
                    array_map(function ($data) {
                        return [
                            'kad_ident' => $data['kad_ident'],
                            'edit_active_from' => (DateTime::createFromFormat('Y-m-d H:i:s.u', $data['date_uploaded']))->modify('-1 day')->format('Y-m-d'),
                        ];
                    }, $oldData)
                );
            }

            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options, $oldData, 'edit plot document area in KVS');
        }

        $UserDbContractsController->manageOverallRenta($params['annex_id']);
    }
}
