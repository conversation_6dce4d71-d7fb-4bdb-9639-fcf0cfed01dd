<?php

namespace TF\Engine\APIClasses\Annexes;

use Prado\Web\Services\TRpcApiProvider;
use TF\Application\Common\Config;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbContracts\UserDbContractsController;
use TF\Engine\Plugins\Core\Users\UsersController;

// Prado::using('Plugins.Core.UserDb.*');
// Prado::using('Plugins.Core.UserDb.conf');
// Prado::using('Plugins.Core.Users.*');
// Prado::using('Plugins.Core.Users.conf');
// Prado::using('Plugins.Core.Contracts.conf');
// Prado::using('Plugins.Core.UserDbContracts.*');

/**
 * @rpc-module Annexes
 *
 * @rpc-service-id annexes-tree
 */
class AnnexesTree extends TRpcApiProvider
{
    private $module = 'Annexes';
    private $service_id = 'annexes-tree';

    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readAnnexesTree'],
                'validators' => [
                    'filterObj' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'getParentContractData' => ['method' => [$this, 'getParentContractData']],
            'deleteAnnex' => ['method' => [$this, 'deleteAnnex']],
            'changeActiveStatus' => ['method' => [$this, 'changeActiveStatus']],
            'markAnnexForEdit' => ['method' => [$this, 'markAnnexForEdit']],
            'editAnnex' => ['method' => [$this, 'editAnnex'],
                'validators' => [
                    'contractId' => 'validateInteger, validateRequired',
                    'due_date' => 'validateText',
                    'c_num' => 'validateText',
                    'c_date' => 'validateText',
                    'start_date' => 'validateText',
                    'sv_num' => 'validateText',
                    'renta' => 'validateText',
                    'comment' => 'validateText',
                    'sv_date' => 'validateText',
                    'renta_id' => 'validateText',
                    'renta_value' => 'validateNumber',
                ]],
        ];
    }

    /**
     * Displays the annexes tree, based on current filter criteria.
     *
     * @api-method read
     *
     * @param array $filterObj filter parameters (if any)
     *                         {
     *                         #item int annex_id
     *                         #item string c_num
     *                         #item array renta_types
     *                         {
     *                         #items int renta types
     *                         }
     *                         #item date date_from
     *                         #item date date_to
     *                         #item date due_date_from
     *                         #item date due_date_to
     *                         #item int  farming
     *                         #item int  c_type
     *                         }
     * @param int $page pagination rpc parameter
     * @param int $rows pagination rpc parameter
     * @param string $sort pagination rpc parameter
     * @param string $order pagination rpc parameter
     *
     * @return array relevant results
     */
    public function readAnnexesTree(array $filterObj, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        if ($this->User->isGuest || !$this->User->Database) {
            return [];
        }

        // init controllers
        $UserDbController = new UserDbController($this->User->Database);
        $FarmingController = new FarmingController('Farming');
        $arrayHelper = $FarmingController->ArrayHelper;

        $farmings = $FarmingController->getUserFarmings();
        $userFarmingIds = array_keys($farmings);
        $farmingIds = $arrayHelper->filterEmptyStringArr($filterObj['farming']);
        $farmingIds = isset($farmingIds) && count($farmingIds) > 0
            ? array_intersect($farmingIds, $userFarmingIds)
            : $userFarmingIds;
        $filterObj['farming'] = $farmingIds;

        // get renta natura types and create predefined array
        $renta_types = [];
        // options for renta nat types
        $options = [
            'tablename' => $UserDbController->DbHandler->tableRentaTypes,
        ];
        $renta_results = $UserDbController->getItemsByParams($options, false, false);

        $rentCount = count($renta_results);
        for ($i = 0; $i < $rentCount; $i++) {
            $renta_types[$renta_results[$i]['id']]['name'] = $renta_results[$i]['name'];
            $renta_types[$renta_results[$i]['id']]['unit'] = $GLOBALS['Contracts']['renta_units'][$renta_results[$i]['unit']]['name'];
        }

        if (true == $filterObj['annex_complete_match']) {
            $options['where']['c_num']['compare'] = '=';
        }

        if (true == $filterObj['contract_note']) {
            $options['where']['contract_note'] = ['column' => 'comment', 'compare' => 'ILIKE', 'prefix' => 'c', 'value' => $filterObj['contract_note']];
        }

        if (0 === count($filterObj['farming'])) {
            return [];
        }

        $contractsClass = makeApiClass('contracts-rpc', 'contracts-tree');

        $results = $contractsClass->executeContractsQuery(false, false, false, true, $filterObj, $page, $rows, $sort, $order);

        $resultsCount = count($results);

        if (0 == $resultsCount) {
            return [];
        }

        // clear old data
        $return = [];

        date_default_timezone_set('Europe/Sofia');
        $currentDate = date('Y-m-d');

        // transform results into tree format
        $resultsCount = count($results);
        for ($i = 0; $i < $resultsCount; $i++) {
            $contractData = $results[$i];

            $contractData['c_date'] = strftime('%d.%m.%Y', strtotime($contractData['c_date']));
            $contractData['start_date'] = strftime('%d.%m.%Y', strtotime($contractData['start_date']));
            if ($contractData['active']) {
                $contractData['active_text'] = (!$contractData['due_date'] || $contractData['due_date'] > $currentDate) ? 'Действащ' : 'Изтекъл';
            } else {
                $contractData['active_text'] = 'Анулиран';
            }

            if (!$contractData['comment']) {
                $contractData['comment'] = '-';
            }

            if (!$contractData['sv_num']) {
                $contractData['sv_num'] = '-';
            }

            if (!$contractData['osz_num']) {
                $contractData['osz_num'] = '-';
            }

            if ('' != $contractData['due_date']) {
                $contractData['due_date'] = strftime('%d.%m.%Y', strtotime($contractData['due_date']));
                $text = $contractData['c_num'] . ' (' . $contractData['start_date'] . ' - ' . $contractData['due_date'] . ')';
            } else {
                $contractData['due_date'] = '-';
                $text = $contractData['c_num'] . ' (' . $contractData['c_date'] . ')';
            }

            if (!$contractData['renta']) {
                $contractData['renta_text'] = '-';
            } else {
                $contractData['renta'] = number_format($contractData['renta'], 2, '.', '');
                $contractData['renta_text'] = BGNtoEURO($contractData['renta']);
            }

            if ($contractData['overall_renta']) {
                $contractData['overall_renta'] = number_format($contractData['overall_renta'], 2, '.', '');
            }

            $contractData['c_type'] = $contractData['nm_usage_rights'];
            $contractData['nm_usage_rights'] = $GLOBALS['Contracts']['ContractTypes'][$contractData['nm_usage_rights']]['name'];
            $contractData['farming'] = $farmings[$contractData['farming_id']];

            if ('' == $contractData['sv_date']) {
                $contractData['sv_date'] = '-';
            } else {
                $contractData['sv_date'] = strftime('%d.%m.%Y', strtotime($contractData['sv_date']));
            }

            if ('' == $contractData['osz_date']) {
                $contractData['osz_date'] = '-';
            } else {
                $contractData['osz_date'] = strftime('%d.%m.%Y', strtotime($contractData['osz_date']));
            }
            if ('' == $contractData['payday']) {
                $contractData['payday'] = '-';
                $contractData['paymonth'] = '';
            } else {
                $payday = explode('-', $contractData['payday']);
                $contractData['payday'] = $payday[0];
                $contractData['paymonth'] = $payday[1];
            }

            $rentaOptions = [
                'tablename' => $UserDbController->DbHandler->contractsRentsRelTable,
                'where' => [
                    'id' => ['column' => 'contract_id', 'compare' => '=', 'value' => $contractData['id']],
                ],
            ];

            $tmpRenta = $UserDbController->getItemsByParams($rentaOptions);
            $tmpRentCount = count($tmpRenta);
            for ($j = 0; $j < $tmpRentCount; $j++) {
                $tmpRenta[$j]['renta_nat_type'] = $renta_types[$tmpRenta[$j]['renta_id']]['name'];
                $tmpRenta[$j]['renta_nat_text'] = $tmpRenta[$j]['renta_value'] . ' ' . $renta_types[$tmpRenta[$j]['renta_id']]['unit'];
            }

            $contractData['additionalRentas'] = $tmpRenta;

            $return[] = [
                'id' => $contractData['id'],
                'text' => $text,
                'attributes' => $contractData,
                'iconCls' => 'icon-tree-document',
            ];
        }

        // add attribute to first listed element of three for custom pagination
        // information for total can not be sent otherwise
        $page_limit = 30;
        $return[0]['attributes']['pagination']['total'] = $resultsCount;
        $return[0]['attributes']['pagination']['limit'] = $page_limit;

        return $return;
    }

    /**
     * Gets the information about the annexed contract.
     *
     * @api-method getParentContractData
     *
     * @param int $rpcParam ID of the selected annex
     *
     * @return array information about the contract
     */
    public function getParentContractData($rpcParam)
    {
        $FarmingController = new FarmingController('Farming');
        $UserDbController = new UserDbController($this->User->Database);
        $contract_id = $rpcParam;

        if (!$contract_id || !(int) $contract_id) {
            return [];
        }

        $farmings = $FarmingController->getUserFarmings();
        $farmingIds = array_keys($farmings);

        // options for contract query
        $options = [
            'tablename' => $UserDbController->DbHandler->tableContracts,
            'where' => [
                'contract_id' => ['column' => 'id', 'compare' => '=', 'value' => $contract_id],
                'farming_id' => ['column' => 'farming_id', 'compare' => 'IN', 'value' => $farmingIds],
            ],
        ];

        $contract_results = $UserDbController->getItemsByParams($options);

        if (0 == count($contract_results)) {
            return [];
        }

        // get renta natura types and create predefined array
        $renta_types = [];
        // options for renta nat types
        $options = [
            'tablename' => $UserDbController->DbHandler->tableRentaTypes,
        ];
        $renta_results = $UserDbController->getItemsByParams($options, false, false);

        $rentCount = count($renta_results);
        for ($i = 0; $i < $rentCount; $i++) {
            $renta_types[$renta_results[$i]['id']]['name'] = $renta_results[$i]['name'];
            $renta_types[$renta_results[$i]['id']]['unit'] = $GLOBALS['Contracts']['renta_units'][$renta_results[$i]['unit']]['name'];
        }

        $contractData = $contract_results[0];

        // convert data for info panel
        $contractData['c_date'] = strftime('%d.%m.%Y', strtotime($contractData['c_date']));
        $contractData['start_date'] = strftime('%d.%m.%Y', strtotime($contractData['start_date']));

        if (!$contractData['comment']) {
            $contractData['comment'] = '-';
        }

        if (!$contractData['sv_num']) {
            $contractData['sv_num'] = '-';
        }

        if (!$contractData['osz_num']) {
            $contractData['osz_num'] = '-';
        }

        if ('' == $contractData['sv_date']) {
            $contractData['sv_date'] = '-';
        } else {
            $contractData['sv_date'] = strftime('%d.%m.%Y', strtotime($contractData['sv_date']));
        }

        if ('' == $contractData['osz_date']) {
            $contractData['osz_date'] = '-';
        } else {
            $contractData['osz_date'] = strftime('%d.%m.%Y', strtotime($contractData['osz_date']));
        }

        if ('' == $contractData['payday']) {
            $contractData['payday'] = '-';
        } else {
            $contractData['payday'] = strftime('%d.%m.%Y', strtotime($contractData['payday']));
        }

        if ('' == $contractData['due_date']) {
            $contractData['due_date'] = '-';
        } else {
            $contractData['due_date'] = strftime('%d.%m.%Y', strtotime($contractData['due_date']));
        }

        if (!$contractData['renta']) {
            $contractData['renta_text'] = '-';
        } else {
            $contractData['renta_text'] = BGNtoEURO($contractData['renta']);
        }

        $rentaOptions = [
            'tablename' => $UserDbController->DbHandler->contractsRentsRelTable,
            'where' => [
                'id' => ['column' => 'contract_id', 'compare' => '=', 'value' => $contractData['id']],
            ],
        ];

        $tmpRenta = $UserDbController->getItemsByParams($rentaOptions);
        $tmpRentCount = count($tmpRenta);
        for ($j = 0; $j < $tmpRentCount; $j++) {
            $tmpRenta[$j]['renta_nat_type'] = $renta_types[$tmpRenta[$j]['renta_id']]['name'];
            $tmpRenta[$j]['renta_nat_text'] = $tmpRenta[$j]['renta_value'] . ' ' . $renta_types[$tmpRenta[$j]['renta_id']]['unit'];
        }

        $contractData['additionalRentas'] = $tmpRenta;
        $contractData['nm_usage_rights'] = $GLOBALS['Contracts']['ContractTypes'][$contractData['nm_usage_rights']]['name'];
        $contractData['farming'] = $farmings[$contractData['farming_id']];

        return $contractData;
    }

    /**
     * Deletes the selected annex.
     *
     * @api-method deleteAnnex
     *
     * @param int $rpcParam selected annex ID
     *
     * @return array|void
     */
    public function deleteAnnex($rpcParam)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $annex_id = (int)$rpcParam;
        $contract_tablename = $UserDbController->DbHandler->tableContracts;

        if (!$annex_id) {
            return [];
        }
        // delete natural rentas
        $deleteOptions = [
            'tablename' => $UserDbController->DbHandler->contractsRentsRelTable,
            'id_name' => 'contract_id',
            'id_string' => $annex_id,
        ];
        $UserDbController->deleteItemsByParams($deleteOptions);

        $oldOptions = [
            'tablename' => $UserDbController->DbHandler->tableContracts,
            'return' => [
                '*',
            ],
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $annex_id],
            ],
        ];

        $oldData = $UserDbController->getItemsByParams($oldOptions);

        // delete annex
        $options = [
            'tablename' => $contract_tablename,
            'id_string' => $annex_id,
        ];

        $UserDbController->deleteItemsByParams($options);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $oldData, [], 'Delete annex');
    }

    /**
     * Changes the selected annex status.
     *
     * @api-method changeActiveStatus
     *
     * @param array $rpcParam status change parameters
     *                        {
     *                        #item int id
     *                        #item bool status
     *                        #item string comment
     *                        }
     *
     * @throws MTRpcException
     */
    public function changeActiveStatus($rpcParam)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $contract_id = $rpcParam['id'];

        $oldOptions = [
            'tablename' => $UserDbController->DbHandler->tableContracts,
            'return' => [
                '*',
            ],
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $contract_id],
            ],
        ];

        $oldData = $UserDbController->getItemsByParams($oldOptions);

        // check if has annex with same period
        $options = [
            'tablename' => $UserDbController->DbHandler->tableContracts,
            'where' => [
                'parent_id' => ['column' => 'parent_id', 'compare' => '=', 'value' => $oldData[0]['parent_id']],
                'annex_id' => ['column' => 'id', 'compare' => '<>', 'value' => $contract_id],
                'active' => ['column' => 'active', 'compare' => '=', 'value' => 'true'],
            ],
        ];

        $results = $UserDbController->getItemsByParams($options);

        $resultsCount = count($results);
        for ($i = 0; $i < $resultsCount; $i++) {
            if (!(date('Y-m-d', strtotime($results[$i]['due_date'])) < date('Y-m-d', strtotime($oldData[0]['start_date']))
                || date('Y-m-d', strtotime($results[$i]['start_date'])) > date('Y-m-d', strtotime($oldData[0]['due_date'])))) {
                throw new MTRpcException('invalid_annex_date', -33202);
            }
        }
        // END check if has annex with same period

        $options = [
            'tablename' => $UserDbController->DbHandler->tableContracts,
            'mainData' => [
                'active' => $rpcParam['status'],
                'comment' => $rpcParam['comment'],
            ],
            'where' => [
                'id' => $contract_id,
            ],
        ];

        $UserDbController->editItem($options);
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options, $oldData, 'Deactivate/Activate annex');
    }

    /**
     * Gets selected annex information.
     *
     * @api-method markAnnexForEdit
     *
     * @param int $rpcParam selected annex ID
     *
     * @return array all available annex information
     */
    public function markAnnexForEdit($rpcParam)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $annex_id = $rpcParam;

        $options = [
            'tablename' => $UserDbController->DbHandler->tableContracts,
            'where' => [
                'annex_id' => ['column' => 'id', 'compare' => '=', 'value' => $annex_id],
            ],
        ];

        $results = $UserDbController->getItemsByParams($options);
        $return = [];
        $return['annexData'] = $results[0];

        $return['editRecordID'] = $annex_id;

        if (!is_null($return['annexData']['c_date'])) {
            $return['annexData']['c_date'] = date('Y-m-d', strtotime($return['annexData']['c_date']));
        }

        if (!is_null($return['annexData']['sv_date'])) {
            $return['annexData']['sv_date'] = date('Y-m-d', strtotime($return['annexData']['sv_date']));
        }

        if (!is_null($return['annexData']['start_date'])) {
            $return['annexData']['start_date'] = date('Y-m-d', strtotime($return['annexData']['start_date']));
        }

        if (!is_null($return['annexData']['due_date'])) {
            $return['annexData']['due_date'] = date('Y-m-d', strtotime($return['annexData']['due_date']));
        }

        $rentaOptions = [
            'tablename' => $UserDbController->DbHandler->contractsRentsRelTable,
            'where' => [
                'id' => ['column' => 'contract_id', 'compare' => '=', 'value' => $annex_id],
            ],
        ];

        $rentaResult = $UserDbController->getItemsByParams($rentaOptions);

        $return['rentaResult'] = $rentaResult;

        return $return;
    }

    /**
     * Edit selected annex.
     *
     * @api-method editAnnex
     *
     * @param array $rpcParams new annex parameters
     *                         {
     *                         #item int contractId
     *                         #item start_date
     *                         #item date due_date
     *                         #item string c_num
     *                         #item date c_date
     *                         #item date start_date
     *                         #item string sv_num
     *                         #item float renta
     *                         #item date due_date
     *                         #item int renta_nat_type_id
     *                         #item float renta_nat
     *                         #item string comment
     *                         #item date sv_date
     *                         #item array additionalRentas
     *                         {
     *                         #item int renta_id
     *                         #item float renta_value
     *                         }
     *                         }
     *
     * @throws MTRpcException
     *
     * @return array|int status code OK (200)
     */
    public function editAnnex($rpcParams)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        $UserDbContractsController = new UserDbContractsController($this->User->Database);

        // get annex data before update
        $options = [
            'tablename' => $UserDbController->DbHandler->tableContracts,
            'where' => [
                'contract_id' => ['column' => 'id', 'compare' => '=', 'value' => $rpcParams['contractId']],
            ],
        ];

        $annex_results = $UserDbController->getItemsByParams($options);
        if (0 == count($annex_results)) {
            return [];
        }

        $annexData = $annex_results[0];

        // check if has annex with same period
        $options = [
            'tablename' => $UserDbController->DbHandler->tableContracts,
            'where' => [
                'parent_id' => ['column' => 'parent_id', 'compare' => '=', 'value' => $annexData['parent_id']],
                'annex_id' => ['column' => 'id', 'compare' => '<>', 'value' => $rpcParams['contractId']],
                'active' => ['column' => 'active', 'compare' => '=', 'value' => 'true'],
            ],
        ];

        $results = $UserDbController->getItemsByParams($options);

        $resultsCount = count($results);
        for ($i = 0; $i < $resultsCount; $i++) {
            if (!(date('Y-m-d', strtotime($results[$i]['due_date'])) < date('Y-m-d', strtotime($rpcParams['start_date']))
                    || date('Y-m-d', strtotime($results[$i]['start_date'])) > date('Y-m-d', strtotime($rpcParams['due_date'])))) {
                throw new MTRpcException('invalid_annex_date', -33202);
            }
        }
        // END check if has annex with same period

        // edit annex
        $options = [
            'tablename' => $UserDbController->DbHandler->tableContracts,
            'mainData' => [
                'c_num' => $rpcParams['c_num'],
                'c_date' => $rpcParams['c_date'],
                'start_date' => $rpcParams['start_date'],
                'sv_num' => $rpcParams['sv_num'],
                'renta' => ($rpcParams['renta']) ? $rpcParams['renta'] : null,
                'overall_renta' => ($rpcParams['overall_renta']) ? $rpcParams['overall_renta'] : null,
                'due_date' => $rpcParams['due_date'],
                'comment' => $rpcParams['comment'],
            ],
            'where' => [
                'id' => $rpcParams['contractId'],
            ],
        ];

        if ('' != $rpcParams['sv_date']) {
            $options['mainData']['sv_date'] = $rpcParams['sv_date'];
        }

        if ('' != $rpcParams['pd_day'] && '' != $rpcParams['pd_month']) {
            $options['mainData']['payday'] = $rpcParams['pd_day'] . '-' . $rpcParams['pd_month'];
        }

        $UserDbController->editItem($options);
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options, $annexData, 'edit annex');

        if (count($rpcParams['additionalRentas']) > 0) {
            $deleteOptions = [
                'tablename' => $UserDbController->DbHandler->contractsRentsRelTable,
                'id_name' => 'contract_id',
                'id_string' => $rpcParams['contractId'],
            ];

            $UserDbController->deleteItemsByParams($deleteOptions);
            $rentaParams = [
                'tablename' => $UserDbController->DbHandler->contractsRentsRelTable,
                'mainData' => [
                    'contract_id' => $rpcParams['contractId'],
                ],
            ];

            foreach ($rpcParams['additionalRentas'] as $renta) {
                if (0 != $renta['value']) {
                    $rentaParams['mainData']['renta_id'] = $renta['type'];
                    $rentaParams['mainData']['renta_value'] = $renta['value'];

                    $UserDbController->addItem($rentaParams);
                }
            }
        }

        // Update contract_end_date for all plots associated with the annex
        $options = [
            'tablename' => $UserDbController->DbHandler->tableContracts,
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $annexData['parent_id']],
            ],
        ];
        $parent = $UserDbController->getItemsByParams($options);
        $parent = $parent[0];
        $parent_end_date = $parent['due_date'];

        $options = [
            'tablename' => $UserDbController->DbHandler->contractsPlotsRelTable,
            'where' => [
                'contract_id' => ['column' => 'contract_id', 'compare' => '=', 'value' => $rpcParams['contractId']],
            ],
        ];

        $plots = $UserDbController->getItemsByParams($options);

        foreach ($plots as $plot) {
            $options = [
                'tablename' => $UserDbController->DbHandler->contractsPlotsRelTable,
                'mainData' => [
                    'contract_end_date' => 'added' == $plot['annex_action'] ? $rpcParams['due_date'] : $parent_end_date,
                ],
                'where' => [
                    'id' => $plot['id'],
                ],
            ];

            $UserDbController->editItem($options);
            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options, $plot, 'Edit annex-plot relation');
        }

        if (null != $rpcParams['overall_renta']) {
            $UserDbContractsController->manageOverallRenta($rpcParams['contractId']);
        }

        return Config::STATUS_CODE_OK;
    }
}
