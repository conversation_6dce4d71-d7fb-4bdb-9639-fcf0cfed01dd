<?php

namespace TF\Engine\APIClasses\Annexes;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbContracts\UserDbContractsController;
use TF\Engine\Plugins\Core\Users\UsersController;

// Prado::using('Plugins.Core.UserDb.*');
// Prado::using('Plugins.Core.UserDb.conf');
// Prado::using('Plugins.Core.Users.*');
// Prado::using('Plugins.Core.Users.conf');
// Prado::using('Plugins.Core.UserDbCooperators.*');
// Prado::using('Plugins.Core.Contracts.conf');
// Prado::using('Plugins.Core.Farming.conf');
// Prado::using('Plugins.Core.Farming.*');

/**
 * @rpc-module Annexes
 *
 * @rpc-service-id annexes-files
 *
 * @property UserDbController $UserDbController
 * @property UserDbContractsController $UserDbContractsController
 * @property UsersController $UsersController
 */
class AnnexesFiles extends TRpcApiProvider
{
    private $UserDbController = false;
    private $UserDbContractsController = false;
    private $UsersController = false;
    private $module = 'Annexes';
    private $service_id = 'annexes-files';

    public function registerMethods()
    {
        return [
            'downloadAttached' => ['method' => [$this, 'downloadAttached']],
            'deleteAnnexFile' => ['method' => [$this, 'deleteAnnexFile']],
        ];
    }

    /**
     * Creates a download link for selected file.
     *
     * @api-method downloadAttached
     *
     * @param int $data the ID of the selected file
     *
     * @return array|string relative path to the file
     */
    public function downloadAttached($data)
    {
        if ($this->User->isGuest) {
            return [];
        }

        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'tablename' => $UserDbController->DbHandler->userFilesTable,
            'where' => [
                'file_id' => ['column' => 'id', 'compare' => '=', 'value' => $data],
            ],
        ];

        $results = $UserDbController->getItemsByParams($options, false, false);
        if (!count($results)) {
            return [];
        }

        $file_fragments = explode('.', $results[0]['filename']);
        $ext = end($file_fragments);

        return PUBLIC_CONTRACTS_RELATIVE_PATH . '/' . $results[0]['group_id'] . '/' . $results[0]['user_id'] . '/' . $results[0]['id'] . '.' . $ext;
    }

    /**
     * Deletes the selected annex file ID.
     *
     * @api-method deleteAnnexFile
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer annex_id
     *                         #item integer file_id
     *                         }
     *
     * @return null|array
     */
    public function deleteAnnexFile($rpcParams)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbContractsController = new UserDbContractsController($this->User->Database);
        $UsersController = new UsersController('Users');

        $fileID = $rpcParams['file_id'];

        $options = [
            'tablename' => $UserDbController->DbHandler->userFilesTable,
            'where' => [
                'file_id' => ['column' => 'id', 'compare' => '=', 'value' => $fileID],
            ],
        ];

        $file_results = $UserDbController->getItemsByParams($options);
        if (0 == count($file_results)) {
            return [];
        }

        $fileData = $file_results[0];

        // delete contract_file_relation
        $UserDbContractsController->deleteContractFileRelation($fileID, $rpcParams['annex_id']);

        // check if there are any relations left with this fileID
        $options = [
            'tablename' => $UserDbController->DbHandler->contractsFilesRelTable,
            'where' => [
                'file_id' => ['column' => 'file_id', 'compare' => '=', 'value' => $fileID],
            ],
        ];

        $relationResults = $UserDbController->getItemsByParams($options, false, false);
        $is_hard_delete = false;
        // if the file is not asociated with any other contract - delete it from the file structure
        if (!count($relationResults)) {
            $is_hard_delete = true;
            $fname_fragments = explode('.', $fileData['filename']);
            $filename = current($fname_fragments);
            // get extension
            $ext = end($fname_fragments);

            // delete file
            $filePath = LAYERS_CONTRACTS_PATH . $fileData['group_id'] . '/' . $fileData['user_id'] . '/' . $fileData['id'] . '.' . $ext;
            $newFileName = LAYERS_CONTRACTS_PATH . $fileData['group_id'] . '/' . $fileData['user_id'] . '/' . $filename . '_' . $fileData['id'] . '.' . $ext;

            if (file_exists($filePath)) {
                unlink($filePath);
            }
            if (file_exists($newFileName)) {
                unlink($newFileName);
            }

            // delete record from the table
            $options = [
                'tablename' => $UserDbController->DbHandler->userFilesTable,
                'id_string' => $fileID,
            ];

            $UserDbController->deleteItemsByParams($options);
            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $fileData, [], 'delete file in a annex');
        }

        $UserDbController->logFileDeletion($fileID, $rpcParams['annex_id'], $is_hard_delete, $this->User->UserID);
    }
}
