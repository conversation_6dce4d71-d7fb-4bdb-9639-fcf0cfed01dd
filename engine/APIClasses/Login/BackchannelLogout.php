<?php

namespace TF\Engine\APIClasses\Login;

use Prado\Prado;
use Prado\Web\Services\TJsonResponse;

class BackchannelLogout extends TJsonResponse
{
    public function __construct() {}

    public function getJsonContent()
    {
        $logoutToken = $_POST['logout_token'];
        $keycloak = Prado::getApplication()->getModule('keycloak');
        $keycloak->backChannelLogout($logoutToken);
    }
}
