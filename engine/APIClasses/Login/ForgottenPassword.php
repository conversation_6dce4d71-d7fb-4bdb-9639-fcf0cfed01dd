<?php

namespace TF\Engine\APIClasses\Login;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * ForgottenPassword Class.
 *
 * @rpc-module Login
 *
 * @rpc-service-id login-user
 */
class ForgottenPassword extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'initForgottenPasswordAction' => ['method' => [$this, 'initForgottenPasswordAction']],
            'setNewPassword' => ['method' => [$this, 'setNewPassword']],
        ];
    }

    /**
     * ForgottenPassword.
     *
     * @api-method initForgottenPasswordAction
     *
     * @param array $rpcParams{
     *                          #item string $email
     *                          #item string $password
     *                          }
     *
     * @return string redirect url
     */
    public function initForgottenPasswordAction($rpcParams)
    {
        $UsersController = new UsersController('Users');
        $result = $this->getUserByUsernameAndEmail($rpcParams['username'], $rpcParams['email']);

        if (false != $result) {
            $hash = $UsersController->setUserHash($result['id']);

            $options = [];
            $options['email'] = $rpcParams['email'];

            $options['change_link'] = SITE_URL . 'index.php?page=ChangePassword&user_id=' . $result['id'] . '&hash=' . $hash['hash'];
            $options['username'] = $result['username'];
            $options['name'] = $result['name'];

            $UsersController->sendForgottenPasswordEmail($options);
            $obj['missing_email'] = false;
        } else {
            $obj['missing_email'] = true;
        }

        return $obj;
    }

    /**
     * Login User.
     *
     * @api-method setNewPassword
     *
     * @param array $rpcParams{
     *                          string user_id
     *                          string hash
     *                          string password
     *                          }
     */
    public function setNewPassword($rpcParams)
    {
        $UsersController = new UsersController('Users');

        if (empty($rpcParams['user_id']) || empty($rpcParams['hash']) || empty($rpcParams['password'])) {
            throw new MTRpcException('REQUIRED_FIELDS', -33016);
        }

        $UsersController->setNewPassword((int)$rpcParams['user_id'], $rpcParams['hash'], crypt($rpcParams['password']));

        return true;
    }

    private function getUserByUsernameAndEmail($username, $email)
    {
        $UsersController = new UsersController('Users');
        $options = [
            'return' => ['id', 'username', 'email', 'name'],
            'tablename' => 'su_users',
            'whereFields' => ['username', 'email'],
            'whereValues' => [$username, $email],
        ];

        return $UsersController->getItemByParams($options);
    }
}
