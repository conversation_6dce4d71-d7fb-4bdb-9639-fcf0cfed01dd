<?php

namespace TF\Engine\APIClasses\Login;

use DateInterval;
use DateTime;
use Prado\Prado;
use Prado\Web\Services\TRpcApiProvider;
use Prado\Web\THttpCookie;
use TF\Application\Common\Config;
use TF\Engine\Kernel\LoggerMessages;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * LoginForm Class.
 *
 * @deprecated
 *
 * @rpc-module Login
 *
 * @rpc-service-id login-user
 */
class LoginForm extends TRpcApiProvider
{
    public const WRONG_USERNAME_OR_PASSWORD = -33307;
    public const ACCOUNT_IS_NOT_ACTIVE = -33313;

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'loginUser' => ['method' => [$this, 'loginUser'],
                'validators' => [
                    'username' => 'validateText, validateRequired, validateNotNull',
                    'password' => 'validateText, validateRequired, validateNotNull',
                ],
            ],
            'getSessionTime' => ['method' => [$this, 'getSessionTime']],
            'logout' => ['method' => [$this, 'logout']],
            'appLogout' => ['method' => [$this, 'appLogout']],
        ];
    }

    /**
     * Returns the seconds until the current session expire.
     *
     * @api-method getSessionTime
     *
     * @return int
     */
    public function getSessionTime()
    {
        return $this->getSession()->itemAt('AuthExpireTime');
    }

    /**
     * Login User.
     *
     * @deprecated
     *
     * @api-method loginUser
     *
     * @param string $username
     * @param string $password
     *
     * @throws MTRpcException
     *
     * @return array redirect url
     */
    public function loginUser($username, $password)
    {
        $UsersController = new UsersController();

        // login the user
        $authManager = $this->Application->getModule(Config::AUTH_MODULE);
        if (!$authManager->login(strtolower($username), $password)) {
            $UsersController->log(0, 'Anonymous', LoggerMessages::WRONG_ACCOUNT . ' User:' . strtolower($username) . ' Pass:' . $password);

            throw new MTRpcException('wrong_username_or_password', LoginForm::WRONG_USERNAME_OR_PASSWORD);
        }

        // check if the user is logged in and return redirect url
        if (false == $this->User->isGuest) {
            $this->logLogin($this->User->UserID, $this->User->Name);
            $UsersController->log(0, $this->User->Name, LoggerMessages::USER_LOGGED);

            if (in_array($this->User->UserLevel, [Config::USERS_SUPPORT_FLAG, Config::USERS_SALES_FLAG, Config::USERS_SUPER_ADMIN_FLAG])) {
                $redirect = 'index.php?page=Users.Home';
            } elseif ($this->User->HasMapRightsR) {
                $redirect = 'index.php?page=Map.Home';
            } elseif ($this->User->HasPlotRightsR) {
                $redirect = 'index.php?page=Plots.Home';
            } elseif ($this->User->HasSubsidyRights) {
                $redirect = 'index.php?page=ZPlots.Home';
            } else {
                $this->Application->getModule(Config::AUTH_MODULE)->logout();
                $redirect = 'index.php?page=Home';
            }

            $userData = $UsersController->getUserDataById($this->User->UserID);

            $loginTokenHash = sha1($userData['login_token'] . getenv('APP_UNIQUE_KEY'));
            $levelHash = sha1($userData['level'] . getenv('APP_UNIQUE_KEY'));

            $cookieLoginToken = new THttpCookie($authManager->getUserKey() . '_login_hash', $loginTokenHash);
            $cookieLevel = new THttpCookie($authManager->getUserKey() . '_level_hash', $levelHash);

            Prado::getApplication()->getResponse()->getCookies()->add($cookieLoginToken);
            Prado::getApplication()->getResponse()->getCookies()->add($cookieLevel);

            $return = [
                'redirect_url' => $redirect,
                'user_id' => $this->User->UserID,
                'group_id' => $this->User->GroupID,
                'name' => $this->User->Name,
                'email' => $this->User->Email,
                'fullname' => $this->User->Fullname,
                'machine' => MACHINE_NAME,
                'rights' => [
                    'is_admin' => $this->User->getIsSuperAdmin(),
                ],
                'app_version' => $this->User->AppVersion,
                'app_critical_upd' => $this->User->AppCriticalUpd,
                'wms_map_path' => WMS_MAP_PATH,
                'wms_server' => WMS_SERVER,
                'year' => $this->User->FullPaidSupportYear,
            ];

            if ('Debug' == Prado::getApplication()->getMode()) {
                $return['session'] = session_id();
            }

            return $return;
        }
    }

    /**
     * Performs keycloak logout and app logout.
     */
    public function logout(): void
    {
        $keycloak = Prado::getApplication()->getModule('keycloak');
        $keycloak->logout();
    }

    /**
     * Prado app logout.
     */
    public function appLogout(): void
    {
        Prado::getApplication()->getModule(Config::AUTH_MODULE)->logout();
    }

    /**
     * Logs the user IP and date for his last login.
     *
     * @param int $userId the user ID
     * @param string $username the user name
     */
    private function logLogin($userId, $username)
    {
        $UsersController = new UsersController('Users');
        $pattern = '/(?<date>[\\w]+ [\\d]{2} [\\d]{2}:[\\d]{2}:[\\d]{2}).*User: ' . $username . '.*?\\[IP\\] (?<ip>[\\d]{1,3}\\.[\\d]{1,3}\\.[\\d]{1,3}\\.[\\d]{1,3}|::1)/';
        $logFileName = SITE_PATH . 'logs/logger.log';

        if (!file_exists($logFileName)) {
            $fh = fopen($logFileName, 'w');
            fclose($fh);
        }

        $results = $UsersController->regExpLogsSearch($logFileName, $pattern, true);

        if (0 == count($results)) {
            return;
        }

        $dt = DateTime::createFromFormat('M d H:i:s', $results[0]['date'][0]);
        $timeZoneInterval = new DateInterval('PT2H');
        $dt->add($timeZoneInterval);
        $ip = $results[0]['ip'][0];

        $UsersController->updateUsersData([
            'mainData' => [
                'last_login_date' => $dt->format('Y-m-d H:i:s'),
                'last_login_ip' => $ip,
            ],
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $userId],
            ],
        ]);
    }
}
