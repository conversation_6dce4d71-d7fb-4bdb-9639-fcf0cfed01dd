<?php

namespace TF\Engine\APIClasses\SalesContracts;

use Prado\Web\Services\TRpcApiProvider;
use TF\Application\Common\Config;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbContracts\UserDbContractsController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * SalesContracts Add Plots Grid.
 *
 * @rpc-module SalesContracts
 *
 * @rpc-service-id sales-contracts-add-plots-datagrid
 */
class SalesContractsAddPlotsGrid extends TRpcApiProvider
{
    /**
     * Registers the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getSalesContractsAddPlotData'],
                'validators' => [
                    'filterObj' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * Gets all plots for add, associated with selected sale contract.
     *
     * @api-method read
     *
     * @param array $filterObj {
     *
     * @item integer contract_id
     * @item string ekate
     * @item string masiv
     * @item string number
     * }
     *
     * @param int|string $page pagination parameters
     * @param int|string $rows pagination parameters
     * @param string $sort pagination parameters
     * @param string $order pagination parameters
     *
     * @return array [description]
     */
    public function getSalesContractsAddPlotData(array $filterObj, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbContractsController = new UserDbContractsController($this->User->Database);
        $UsersController = new UsersController('Users');

        $FarmingController = new FarmingController('Farming');

        $arrayHelper = $FarmingController->ArrayHelper;

        // create default return
        $return = [
            'rows' => [],
            'total' => 0,
            'footer' => [
                [
                    'kad_ident' => '<b>ОБЩО</b>',
                    'used_area' => '',
                    'area' => '',
                ],
            ],
        ];

        // check if default grid is not loaded with contract_id = 0
        if (!$filterObj['contract_id'] || 0 == $filterObj['contract_id']) {
            return $return;
        }

        // content
        $options = [
            'tablename' => $UserDbController->DbHandler->tableKVS,
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
            'return' => [
                'c.id', 'c.c_num', 'MAX(DISTINCT scpr.contract_area) sale_area', 'SUM(DISTINCT scpr.contract_area_for_sale) total_area',
                'kvs.virtual_ntp_title as area_type', 'kvs.virtual_category_title as category', 'kvs.ekate', 'kvs.virtual_ekatte_name as land', 'cpr.id', 'kvs.gid', 'kvs.include', 'COALESCE(kvs.kad_ident, \'[Няма информация]\') as kad_ident', 'kvs.masiv', 'kvs.mestnost',
                'kvs.number', 'kvs.participate', 'kvs.white_spots', 'kvs.used_area', 'St_Area(kvs.geom) as area', 'kvs.document_area', 'kvs.is_edited',
                'cpr.contract_id', 'cpr.price_per_acre', 'cpr.price_sum', 'cpr.contract_area', 'round((cpr.contract_area*cpr.price_per_acre)::numeric, 2) AS price_procurement',
            ],
            'where' => [
                'nm_usage_rights' => ['column' => 'nm_usage_rights', 'prefix' => 'c', 'compare' => '=', 'value' => Config::CONTRACT_TYPE_OWN],
                'is_annex' => ['column' => 'is_annex', 'prefix' => 'c', 'compare' => '=', 'value' => 'FALSE'],
                'farming_id' => ['column' => 'farming_id', 'prefix' => 'c', 'compare' => '=', 'value' => $filterObj['farming_id']],
                'c_active' => ['column' => 'active', 'prefix' => 'c', 'compare' => '=', 'value' => true],
            ],
            'group' => 'c.id, kvs.gid, cpr.id',
            'having' => ' having case when MAX(DISTINCT scpr.contract_area) is not null then MAX(DISTINCT scpr.contract_area) > SUM(DISTINCT scpr.contract_area_for_sale) else c.id > 0 end ',
        ];

        // Filter
        if ($filterObj['ekate']) {
            $options['where']['ekate'] = ['column' => 'ekate', 'prefix' => 'kvs', 'compare' => '=', 'value' => $filterObj['ekate']];
        }
        if ($filterObj['masiv']) {
            $options['where']['masiv'] = ['column' => 'masiv', 'prefix' => 'kvs', 'compare' => '=', 'value' => $filterObj['masiv']];
        }
        if ($filterObj['number']) {
            $options['where']['number'] = ['column' => 'number', 'prefix' => 'kvs', 'compare' => '=', 'value' => $filterObj['number']];
        }
        if ($filterObj['category']) {
            $category = $arrayHelper->filterEmptyStringArr($filterObj['category']);
            $options['where']['category'] = ['column' => 'category', 'prefix' => 'kvs', 'compare' => 'IN', 'value' => $category];
        }
        if ($filterObj['ntp']) {
            $ntp = $arrayHelper->filterEmptyStringArr($filterObj['ntp']);
            $options['where']['area_type'] = ['column' => 'area_type', 'prefix' => 'kvs', 'compare' => 'IN', 'value' => $ntp];
        }
        if ($filterObj['contract_number']) {
            $options['where']['c_num'] = ['column' => 'c_num', 'prefix' => 'c', 'compare' => 'ILIKE', 'value' => $filterObj['contract_number']];
        }
        if ('1' == $filterObj['with_hypothec']) {
            // Да
            $options['where']['hypothec_id'] = ['column' => '(CASE WHEN hpr.hypothec_id IS NULL THEN 0 ELSE hpr.hypothec_id END)', 'compare' => '>', 'value' => '0'];
        }
        if ('2' == $filterObj['with_hypothec']) {
            // Не
            $options['where']['hypothec_id'] = ['column' => '(CASE WHEN hpr.hypothec_id IS NULL THEN 0 ELSE hpr.hypothec_id END)', 'compare' => '=', 'value' => '0'];
        }
        if ('1' == $filterObj['with_sublease']) {
            // Да
            $options['where']['sublease_id'] = ['column' => '(CASE WHEN spcr.sublease_id IS NULL THEN 0 ELSE spcr.sublease_id END)', 'compare' => '>', 'value' => '0'];
        }
        if ('2' == $filterObj['with_sublease']) {
            // Не
            $options['where']['sublease_id'] = ['column' => '(CASE WHEN spcr.sublease_id IS NULL THEN 0 ELSE spcr.sublease_id END)', 'compare' => '=', 'value' => '0'];
        }
        if ($filterObj['start_date']) {
            // Не
            $options['where']['start_date'] = ['column' => 'start_date', 'prefix' => 'c', 'compare' => '<=', 'value' => $filterObj['start_date']];
        }

        $counter = count($UserDbContractsController->getPlotDataForSalesContracts($options, true, false));

        if (0 == $counter) {
            return $return;
        }

        $result = $UserDbContractsController->getPlotDataForSalesContracts($options, false, false);
        $resultCount = count($result);

        $total_area = 0;
        $total_contract_area = 0;

        for ($i = 0; $i < $resultCount; $i++) {
            $result[$i]['used_area'] = number_format($result[$i]['used_area'], 3, '.', '');
            $result[$i]['area'] = number_format($result[$i]['area'] / 1000, 3, '.', '');

            $result[$i]['price_per_acre'] = BGNtoEURO($result[$i]['price_per_acre']);
            $result[$i]['price_sum'] = BGNtoEURO($result[$i]['price_sum']);
            $result[$i]['price_procurement'] = BGNtoEURO($result[$i]['price_procurement']);

            if (!$result[$i]['document_area']) {
                $result[$i]['document_area'] = $result[$i]['area'];
            }

            $total_area += $result[$i]['document_area'];
            $total_contract_area += $result[$i]['contract_area'];

            $result[$i]['contract_area'] = number_format($result[$i]['contract_area'], 3, '.', '');
            if ($result[$i]['sale_area']) {
                $result[$i]['contract_area_for_sale'] = number_format(($result[$i]['sale_area'] - $result[$i]['total_area']), 3, '.', '');
            } else {
                $result[$i]['contract_area_for_sale'] = number_format($result[$i]['contract_area'], 3, '.', '');
            }

            $result[$i]['sublease_contract_name'] = '';
            $resultSubleaseContracts = $UserDbContractsController->getSubleaseContractName($result[$i]['gid'], $filterObj['start_date']);
            $subCount = count($resultSubleaseContracts);
            if ($subCount > 0) {
                for ($j = 0; $j < $subCount; $j++) {
                    $result[$i]['sublease_contract_name'] .= $resultSubleaseContracts[$j]['c_num'] . '(' . $resultSubleaseContracts[$j]['start_date'] . '-' . $resultSubleaseContracts[$j]['due_date'] . ')';
                    if ($j < $subCount) {
                        $result[$i]['sublease_contract_name'] .= '<br>';
                    }
                }
            }
        }

        $return['rows'] = $result;
        $return['total'] = $counter;
        $return['footer'] = [
            [
                'kad_ident' => '<b>ОБЩО</b>',
                'contract_area' => number_format($total_contract_area, 3, '.', ''),
                'document_area' => number_format($total_area, 3, '.', ''),
            ],
        ];

        return $return;
    }
}
