<?php

namespace TF\Engine\APIClasses\SalesContracts;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbOwners\UserDbOwnersController;
use TF\Engine\Plugins\Core\Users\UsersController;

// Prado::using('Plugins.Core.UserDbOwners.*');
// Prado::using('Plugins.Core.UserDbContracts.*');
// Prado::using('Plugins.Core.Contracts.conf');

/**
 * Buyers Grid.
 *
 * @rpc-module SalesContracts
 *
 * @rpc-service-id buyers-sales-contracts-grid
 */
class BuyersGrid extends TRpcApiProvider
{
    private $module = 'SalesContracts';
    private $service_id = 'buyers-sales-contracts-grid';

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readBuyersGrid'],
                'validators' => [
                    'salesContractId' => 'validateNumber, validateRequired',
                ],
            ],
            'addBuyer' => ['method' => [$this, 'addBuyer'],
                'validators' => [
                    'name' => 'validateText, validateRequired',
                    'contacts' => 'validateText, validateRequired',
                ],
            ],
            'updateBuyer' => ['method' => [$this, 'updateBuyer'],
                'validators' => [
                    'id' => 'validateInteger, validateRequired',
                    'name' => 'validateText, validateRequired',
                    'contacts' => 'validateText, validateRequired',
                ],
            ],
            'deleteBuyer' => ['method' => [$this, 'deleteBuyer'],
                'validators' => [
                    'id' => 'validateInteger, validateRequired',
                ],
            ],

            'addRelationBuyerSalesContract' => ['method' => [$this, 'addRelationBuyerSalesContract'],
                'validators' => [
                    'buyerId' => 'validateInteger, validateRequired',
                    'salesContractId' => 'validateInteger, validateRequired',
                ],
            ],
        ];
    }

    /**
     * Add Relation Buyer SalesContract.
     *
     * @api-method addRelationBuyerSalesContract
     *
     * @param int $buyerId
     * @param int $salesContractId
     *
     * @throws MTRpcException
     *
     * @return int recordID
     */
    public function addRelationBuyerSalesContract($buyerId, $salesContractId)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        // Check if the input data is valid
        $this->CheckForExistingCotnractId($salesContractId);
        $this->CheckForExistingBuyerId($buyerId);
        $this->CheckForExistingCotnractOwnerRelation($buyerId, $salesContractId);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableSalesContractsBuyersRel,
            'mainData' => [
                'buyer_id' => $buyerId,
                'sales_contract_id' => $salesContractId,
            ],
        ];

        $recordID = $UserDbController->addItem($options);

        if ($recordID) {
            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['new_data' => $options], ['created_rel_id' => $recordID], 'Adding sales contract buyer relation');
        }

        return $recordID;
    }

    /**
     * Delete Buyer.
     *
     * @api-method deleteBuyer
     *
     * @param int $id
     *
     * @throws MTRpcException
     *
     * @return array|bool
     */
    public function deleteBuyer($id)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        if (!$id) {
            return [];
        }

        $buyer = $this->CheckForExistingBuyerId($id, true);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableBuyers,
            'id_string' => $id,
        ];
        $UserDbController->deleteItemsByParams($options);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['old_data' => $buyer], ['deleted_id' => $id], 'Deleting buyer');

        return true;
    }

    /**
     * Update Buyer.
     *
     * @api-method updateBuyer
     *
     * @param int $id
     * @param string $name
     * @param string $contacts
     *
     * @throws MTRpcException
     *
     * @return bool
     */
    public function updateBuyer($id, $name, $contacts)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $buyer = $this->CheckForExistingBuyerId($id, true);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableBuyers,
            'mainData' => [
                'name' => $name,
                'contacts' => $contacts,
            ],
            'where' => [
                'id' => $id,
            ],
        ];

        $UserDbController->editItem($options);
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['old_data' => $buyer], ['new_data' => $options], 'Updating buyer');

        return true;
    }

    /**
     * Add Buyer.
     *
     * @api-method addBuyer
     *
     * @param string $name
     * @param string $contacts
     *
     * @return int $recordID
     */
    public function addBuyer($name, $contacts)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $options = [
            'tablename' => $UserDbController->DbHandler->tableBuyers,
            'mainData' => [
                'name' => $name,
                'contacts' => $contacts,
            ],
        ];

        $recordID = $UserDbController->addItem($options);

        if ($recordID) {
            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['new_data' => $options], ['created_id' => $recordID], 'Adding buyer');
        }

        return $recordID;
    }

    /**
     * Read Buyers Grid.
     *
     * @param int $salesContractId
     * @param int $page pagination parameters
     * @param int $rows pagination parameters
     * @param string $sort pagination parameters
     * @param string $order pagination parameters
     *
     * @return array
     */
    public function readBuyersGrid($salesContractId, $page = null, $rows = null, $sort = '', $order = '')
    {
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);

        $id_array = [];

        // Get relation ids
        $options = [
            'tablename' => $UserDbOwnersController->DbHandler->tableSalesContractsBuyersRel,
            'return' => [
                'id', 'sales_contract_id', 'buyer_id',
            ],
            'where' => [
                'sales_contract_id' => ['column' => 'sales_contract_id', 'compare' => '=', 'value' => $salesContractId],
            ],
        ];
        $results = $UserDbOwnersController->getItemsByParams($options, false, false);

        $resultsCount = count($results);
        for ($i = 0; $i < $resultsCount; $i++) {
            $id_array[] = $results[$i]['buyer_id'];
        }

        // Get result
        $options = [
            'tablename' => $UserDbOwnersController->DbHandler->tableBuyers,
            'return' => [
                'id', 'name', 'contacts',
            ],
            'where' => [
                'id' => ['column' => 'id', 'compare' => 'NOT IN', 'value' => $id_array],
            ],
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
        ];

        $counter = $UserDbOwnersController->getItemsByParams($options, true, false);

        if (0 == $counter[0]['count']) {
            return [
                'rows' => [],
                'total' => 0,
            ];
        }

        $results = $UserDbOwnersController->getItemsByParams($options, false, false);

        $data['rows'] = $results;
        $data['total'] = $counter[0]['count'];

        return $data;
    }

    private function CheckForExistingBuyerId($buyerId, $returnBuyer = false)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $buyer_options = [
            'tablename' => $UserDbController->DbHandler->tableBuyers,
            'return' => ['*'],
            'where' => [
                'sales_contract_id' => ['column' => 'id', 'compare' => '=', 'value' => $buyerId],
            ],
        ];

        $buyer_results = $UserDbController->getItemsByParams($buyer_options);

        if (!isset($buyer_results[0]['id'])) {
            throw new MTRpcException('NON_EXISTING_BUYER_ID', -33552);
        }

        if ($returnBuyer) {
            return $buyer_results[0];
        }
    }

    private function CheckForExistingCotnractId($salesContractId)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $contract_options = [
            'tablename' => $UserDbController->DbHandler->tableSalesContracts,
            'return' => ['id'],
            'where' => [
                'sales_contract_id' => ['column' => 'id', 'compare' => '=', 'value' => $salesContractId],
            ],
        ];

        $contract_results = $UserDbController->getItemsByParams($contract_options);

        if (!isset($contract_results[0]['id'])) {
            throw new MTRpcException('NON_EXISTING_SALES_CONTRACT_NUMBER', -33551);
        }
    }

    private function CheckForExistingCotnractOwnerRelation($buyerId, $salesContractId)
    {
        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableSalesContractsBuyersRel,
            'return' => ['*'],
            'where' => [
                'sales_contract_id' => ['column' => 'sales_contract_id', 'compare' => '=', 'value' => $salesContractId],
                'buyer_id' => ['column' => 'buyer_id', 'compare' => '=', 'value' => $buyerId],
            ],
        ];

        $results = $UserDbController->getItemsByParams($options);

        if (isset($results[0]['id'])) {
            throw new MTRpcException('CONTRACT_BUYER_RELATION_ALREADY_EXISTS', -33553);
        }
    }
}
