<?php

namespace TF\Engine\APIClasses\SalesContracts;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\Export2XlsClass;
use TF\Engine\Plugins\Core\UserDbContracts\UserDbContractsController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Report Sales Contracts Grid.
 *
 * @rpc-module SalesContracts
 *
 * @rpc-service-id report-sales-contracts-grid
 */
class ReportSalesContractsGrid extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readReportSalesContracts'],
                'validators' => [
                    'params' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'exportToExcelReportSalesContractsData' => ['method' => [$this, 'exportToExcelReportSalesContractsData'],
                'validators' => [
                    'data' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * Export To Excel Report SalesContracts Data.
     *
     * @param array $data {
     *                    #item string ekate
     *                    #item string masiv
     *                    #item string number
     *                    #item string c_num
     *                    #item string c_num_sale
     *                    }
     * @param int|string $page pagination parameters
     * @param int|string $rows pagination parameters
     * @param string $sort pagination parameters
     * @param string $order pagination parameters
     *
     * @return array
     */
    public function exportToExcelReportSalesContractsData(array $data, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $results = $this->readReportSalesContracts($data, $page, $rows, $sort, $order);

        $result = $results['rows'];

        unset($result[0]['attributes']);

        $result = $this->formatRowsForExport($result);

        $columns = [
            'Землище',
            'Идентификатор',
            'Площ по дог.',
            'Продадена площ',
            'Договор за покупка',
            'Договор за продажба',
            'Цена на дка за придобиване',
            'Обща цена за придобиване',
            'Цена на дка за продажба',
            'Обща цена за продажба',
            'Разлика',
        ];
        $fileName = 'dogovori_za_prodajba_spravka_' . $this->User->GroupID . '.xlsx';
        $path = PUBLIC_UPLOAD_EXPORT . '/' . $this->User->GroupID . '/' . $fileName;

        $export2Xls = new Export2XlsClass();
        $filePath = $export2Xls->exportUrlPath($path, $result, $columns);

        return ['file_path' => $filePath, 'file_name' => $fileName];
    }

    /**
     * Read Report Sales Contracts.
     *
     * @api-method read
     *
     * @param array $filterObj {
     *                         #item string ekate
     *                         #item string masiv
     *                         #item string number
     *                         #item string c_num
     *                         #item string c_num_sale
     *                         }
     * @param int|string $page pagination parameters
     * @param int|string $rows pagination parameters
     * @param string $sort pagination parameters
     * @param string $order pagination parameters
     *
     * @return array
     */
    public function readReportSalesContracts(array $filterObj, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $UserDbContractsController = new UserDbContractsController($this->User->Database);
        $UsersController = new UsersController('Users');

        // Get result
        $options = [
            'return' => [
                'scpr.id', 'scpr.sales_contract_id', 'scpr.contract_id', 'scpr.sublease_contract_id', 'scpr.plot_id',
                'scpr.contract_area_for_sale', 'scpr.price_per_acre', 'scpr.price_sum',
                'sc.c_num as c_num_sale', 'kvs.ekate', 'kvs.kad_ident', 'c.c_num', 'cpr.contract_area', 'cpr.price_per_acre as price_per_acre_buy',
                'round((cpr.contract_area*cpr.price_per_acre)::numeric, 2) AS price_procurement',
                'round((St_Area(kvs.geom)/1000)::numeric, 3) as area',
            ],
            'sort' => $sort,
            'order' => $order,
        ];

        // get all ekate data
        $ekateData = $UsersController->getAllEkatteData();
        $ekateNames = [];
        $ekateCount = count($ekateData);
        for ($i = 0; $i < $ekateCount; $i++) {
            $ekateNames[$ekateData[$i]['ekatte_code']] = $ekateData[$i]['ekatte_name'];
        }

        // Filter
        $filteredEkateName = '';
        if ($filterObj['ekate']) {
            $options['where']['ekate'] = ['column' => 'ekate', 'prefix' => 'kvs', 'compare' => '=', 'value' => $filterObj['ekate']];

            $filteredEkateName = $ekateNames[$filterObj['ekate']];
        }
        if ($filterObj['masiv']) {
            $options['where']['masiv'] = ['column' => 'masiv', 'prefix' => 'kvs', 'compare' => '=', 'value' => $filterObj['masiv']];
        }
        if ($filterObj['number']) {
            $options['where']['number'] = ['column' => 'number', 'prefix' => 'kvs', 'compare' => '=', 'value' => $filterObj['number']];
        }
        if ($filterObj['c_num']) {
            $options['where']['c_num'] = ['column' => 'c_num', 'prefix' => 'c', 'compare' => 'ILIKE', 'value' => $filterObj['c_num']];
        }
        if ($filterObj['c_num_sale']) {
            $options['where']['c_num_sale'] = ['column' => 'c_num', 'prefix' => 'sc', 'compare' => 'ILIKE', 'value' => $filterObj['c_num_sale']];
        }

        // total
        $results_total = $UserDbContractsController->getReportSalesContracts($options, false, false);

        $counter = count($results_total);

        // with pagination
        $options['offset'] = ($page - 1) * $rows;
        $options['limit'] = $rows;

        // $counter = $UserDbContractsController->getReportSalesContracts($options, true, false);

        if (0 == $counter) {
            return [
                'rows' => [],
                'total' => 0,
            ];
        }

        $result = $UserDbContractsController->getReportSalesContracts($options, false, false);
        $resultCount = count($result);
        $total_price_sum_for_page = 0;
        $total_difference_for_page = 0;
        $total_price_procurement_for_page = 0;
        for ($i = 0; $i < $resultCount; $i++) {
            $result[$i]['land'] = $ekateNames[$result[$i]['ekate']];

            $result[$i]['price_per_acre'] = $result[$i]['price_per_acre'] ? $result[$i]['price_per_acre'] : '-';

            if ($result[$i]['contract_area_for_sale'] && '-' != $result[$i]['price_per_acre']) {
                $result[$i]['price_sum'] = $result[$i]['contract_area_for_sale'] * $result[$i]['price_per_acre'];
            } else {
                $result[$i]['price_sum'] = $result[$i]['price_sum'] ? $result[$i]['price_sum'] : '-';
            }

            $result[$i]['difference'] = '';
            if (is_numeric($result[$i]['price_sum'])) {
                $result[$i]['price_sum'] = number_format($result[$i]['price_sum'], 2, '.', '');

                // продажба - придобиване
                $result[$i]['difference'] = $result[$i]['price_sum'] - $result[$i]['price_procurement'];
                $result[$i]['difference'] = number_format($result[$i]['difference'], 2, '.', '');

                $total_price_sum_for_page += $result[$i]['price_sum'];
                $total_difference_for_page += $result[$i]['difference'];

                $result[$i]['difference'] = $result[$i]['difference'] > 0 ? '+' . $result[$i]['difference'] : $result[$i]['difference'];
            }

            $total_price_procurement_for_page += $result[$i]['price_procurement'];
        }

        $total_price_procurement_for_page = number_format($total_price_procurement_for_page, 2, '.', '');
        $total_price_sum_for_page = number_format($total_price_sum_for_page, 2, '.', '');

        $total_difference_for_page = number_format($total_difference_for_page, 2, '.', '');
        $total_difference_for_page = $total_difference_for_page > 0 ? '+' . $total_difference_for_page : $total_difference_for_page;

        $total_price_sum = 0;
        $total_difference = 0;
        $total_price_procurement = 0;
        for ($i = 0; $i < $counter; $i++) {
            $results_total[$i]['price_per_acre'] = $results_total[$i]['price_per_acre'] ? $results_total[$i]['price_per_acre'] : '-';

            if ($results_total[$i]['contract_area_for_sale'] && '-' != $results_total[$i]['price_per_acre']) {
                $results_total[$i]['price_sum'] = $results_total[$i]['contract_area_for_sale'] * $results_total[$i]['price_per_acre'];
            } else {
                $results_total[$i]['price_sum'] = $results_total[$i]['price_sum'] ? $results_total[$i]['price_sum'] : '-';
            }

            $results_total[$i]['difference'] = '';
            if (is_numeric($results_total[$i]['price_sum'])) {
                $results_total[$i]['price_sum'] = number_format($results_total[$i]['price_sum'], 2, '.', '');

                // продажба - придобиване
                $results_total[$i]['difference'] = $results_total[$i]['price_sum'] - $results_total[$i]['price_procurement'];
                $results_total[$i]['difference'] = number_format($results_total[$i]['difference'], 2, '.', '');

                $total_price_sum += $results_total[$i]['price_sum'];
                $total_difference += $results_total[$i]['difference'];

                $results_total[$i]['difference'] = $results_total[$i]['difference'] > 0 ? '+' . $results_total[$i]['difference'] : $results_total[$i]['difference'];
            }

            $total_price_procurement += $results_total[$i]['price_procurement'];
        }

        $total_price_procurement = number_format($total_price_procurement, 2, '.', '');
        $total_price_sum = number_format($total_price_sum, 2, '.', '');

        $total_difference = number_format($total_difference, 2, '.', '');
        $total_difference = $total_difference > 0 ? '+' . $total_difference : $total_difference;

        return [
            'total' => $counter,
            'rows' => $result,
            'footer' => [
                [
                    'contract_area' => '<b>Общо за стр.</b>',
                    'price_procurement' => $total_price_procurement_for_page,
                    'price_sum' => $total_price_sum_for_page,
                    'difference' => $total_difference_for_page,
                ],
                [
                    'contract_area' => '<b>Общо</b>',
                    'price_procurement' => $total_price_procurement,
                    'price_sum' => $total_price_sum,
                    'difference' => $total_difference,
                ],
            ],
            'filteredEkateName' => $filteredEkateName,
        ];
    }

    private function formatRowsForExport($rows)
    {
        $results = [];

        $count = count($rows);
        for ($i = 0; $i < $count; $i++) {
            $results[$i]['land'] = $rows[$i]['land'];
            $results[$i]['kad_ident'] = $rows[$i]['kad_ident'];
            $results[$i]['contract_area'] = $rows[$i]['contract_area'];
            $results[$i]['contract_area_for_sale'] = $rows[$i]['contract_area_for_sale'];
            $results[$i]['c_num'] = $rows[$i]['c_num'];
            $results[$i]['c_num_sale'] = $rows[$i]['c_num_sale'];
            $results[$i]['price_per_acre_buy'] = $rows[$i]['price_per_acre_buy'];
            $results[$i]['price_procurement'] = $rows[$i]['price_procurement'];
            $results[$i]['price_per_acre'] = $rows[$i]['price_per_acre'];
            $results[$i]['price_sum'] = $rows[$i]['price_sum'];
            $results[$i]['difference'] = $rows[$i]['difference'];
        }

        return $results;
    }
}
