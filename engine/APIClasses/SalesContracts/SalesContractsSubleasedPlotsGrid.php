<?php

namespace TF\Engine\APIClasses\SalesContracts;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbContracts\UserDbContractsController;
use TF\Engine\Plugins\Core\Users\UsersController;

// Prado::using('Plugins.Core.Contracts.conf');
// Prado::using('Plugins.Core.Contracts.*');
// Prado::using('Plugins.Core.Farming.conf');
// Prado::using('Plugins.Core.Farming.*');
// Prado::using('Plugins.Core.Layers.conf');
// Prado::using('Plugins.Core.Layers.*');
// Prado::using('Plugins.Core.UserDb.*');
// Prado::using('Plugins.Core.UserDbContracts.*');

/**
 * Subleased Plots Grid SalesContracts.
 *
 * @rpc-module SalesContracts
 *
 * @rpc-service-id sales-contracts-subleased-plots-grid
 */
class SalesContractsSubleasedPlotsGrid extends TRpcApiProvider
{
    // Constants
    public const DATE_OPTION_CONTRACT = 1;
    public const DATE_OPTION_YEAR = 2;

    public const APPLY_TO_ALL = 1;
    public const APPLY_TO_FILTERED = 2;
    public const APPLY_TO_SELECTED = 3;

    /**
     * Registers the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getSalesContractsSubleasedPlotData'],
                'validators' => [
                    'plotIds' => 'validateArray',
                    'filterObj' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateText',
                    'order' => 'validateOrder',
                ], ],
            'saveSubleasedPlotDate' => ['method' => [$this, 'saveSubleasedPlotDate'],
                'validators' => [
                    'rpcParams' => [
                        'date_option' => 'validateInteger, validateRequired',
                        'start_date_salescontract' => 'validateDate',
                        'apply_to' => 'validateInteger, validateRequired',
                        'plotIds' => 'validateIntegerArray',
                    ],
                ],
            ],
        ];
    }

    /**
     * Save Subleased Plot Date.
     *
     * @api-method saveSubleasedPlotDate
     *
     * @param array $rpcParams
     *                         {
     *                         #item array plotIds
     *                         {
     *                         #item int id
     *                         }
     *
     *         		#item date start_date_salescontract
     *         		#item integer date_option
     *         		#item integer apply_to
     *         }
     */
    public function saveSubleasedPlotDate($rpcParams)
    {
        $UserDbContractsController = new UserDbContractsController($this->User->Database);

        $dueDate = $this->getDueDate($rpcParams['date_option'], $rpcParams['start_date_salescontract']);

        // get Contract Ids
        $options = [
            'return' => [
                'DISTINCT c.id',
            ],
            'where' => [
                'active' => ['column' => 'active', 'prefix' => 'c', 'compare' => '=', 'value' => 'TRUE'],
            ],
        ];

        if (SalesContractsSubleasedPlotsGrid::APPLY_TO_SELECTED == $rpcParams['apply_to']) {
            $options['plot_id_string'] = implode(', ', $rpcParams['plotIds']);
        } else {
            $filteredPlots = [];
            $i = 0;
            foreach (array_unique($_SESSION['filtered_sales_contract_plots']) as $key => $value) {
                $filteredPlots[$i] = $value;
                $i++;
            }

            $options['plot_id_string'] = implode(', ', $filteredPlots);
        }

        $result = $UserDbContractsController->getSalesContractsSubleasedPlot($options, false, false);
        $resultCount = count($result);
        $contractIds = [];
        for ($i = 0; $i < $resultCount; $i++) {
            $contractIds[$i] = $result[$i]['id'];
        }

        $UserDbContractsController->updateContractsInIds($contractIds, $dueDate);
    }

    /**
     * Gets all plots for add, associated with selected sale contract.
     *
     * @api-method read
     *
     * @param array $filterObj {
     *
     * @item integer contract_id
     * @item string ekate
     * @item string masiv
     * @item string number
     * }
     *
     * @param int|string $page pagination parameters
     * @param int|string $rows pagination parameters
     * @param string $sort pagination parameters
     * @param string $order pagination parameters
     *
     * @return array {
     *               #item integer total
     *               #item array rows {
     *               #item string area_type
     *               #item string c_num
     *               #item string category
     *               #item string due_date
     *               #item string ekate
     *               #item string gid
     *               #item string id
     *               #item string kad_ident
     *               #item string land
     *               }
     *               #item array footer {}
     *               }
     */
    public function getSalesContractsSubleasedPlotData(array $plotIds, array $filterObj = [], int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbContractsController = new UserDbContractsController($this->User->Database);
        $UsersController = new UsersController('Users');

        $FarmingController = new FarmingController('Farming');

        $arrayHelper = $FarmingController->ArrayHelper;

        // create default return
        $return = [
            'rows' => [],
            'total' => 0,
            'footer' => [
            ],
        ];

        // get result
        $options = [
            'return' => [
                'kvs.gid', 'kvs.ekate', 'kvs.virtual_ekatte_name as land', 'kvs.kad_ident', 'kvs.category', 'kvs.virtual_ntp_title as area_type',
                "to_char(c.due_date, 'DD.MM.YYYY') AS due_date",
                'c.c_num', 'c.id', 'cpr.id as cp_rel_id', 'cpr.contract_area',
            ],
            'where' => [
                'active' => ['column' => 'active', 'prefix' => 'c', 'compare' => '=', 'value' => 'TRUE'],
            ],
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
        ];

        // #drop the constraint and
        $plotsInputParams = $plotIds;
        $plotIds = array_map(function ($plot) {
            return $plot['gid'];
        }, $plotsInputParams);

        $contractPlotsRelations = array_map(function ($plot) {
            return $plot['cp_rel'];
        }, $plotsInputParams);

        $options['plot_id_string'] = implode(', ', $plotIds);

        $start_date = array_map(function ($plot) {
            return $plot['start_date'];
        }, $plotsInputParams);

        // Filter
        if ($filterObj['ekate']) {
            $options['where']['ekate'] = ['column' => 'ekate', 'prefix' => 'kvs', 'compare' => '=', 'value' => $filterObj['ekate']];
        }
        if ($filterObj['masiv']) {
            $options['where']['masiv'] = ['column' => 'masiv', 'prefix' => 'kvs', 'compare' => '=', 'value' => $filterObj['masiv']];
        }
        if ($filterObj['number']) {
            $options['where']['number'] = ['column' => 'number', 'prefix' => 'kvs', 'compare' => '=', 'value' => $filterObj['number']];
        }
        if ($filterObj['ntp']) {
            $ntp = $arrayHelper->filterEmptyStringArr($filterObj['ntp']);
            $options['where']['area_type'] = ['column' => 'area_type', 'prefix' => 'kvs', 'compare' => 'IN', 'value' => $ntp];
        }
        if ($filterObj['due_date_from'] && strlen($filterObj['due_date_from'])) {
            $options['where']['due_date_from'] = ['column' => 'due_date', 'compare' => '>=', 'value' => $filterObj['due_date_from']];
        }
        if ($filterObj['due_date_to'] && strlen($filterObj['due_date_to'])) {
            $options['where']['due_date_to'] = ['column' => 'due_date', 'compare' => '<=', 'value' => $filterObj['due_date_to']];
        }
        if ($start_date) {
            $options['where']['start_date'] = ['column' => 'start_date', 'prefix' => 'c', 'compare' => '<=', 'value' => $start_date[0]];
            $options['where']['due_date'] = ['column' => 'due_date', 'prefix' => 'c', 'compare' => '>', 'value' => $start_date[0]];
        }

        if ($contractPlotsRelations) {
            $options['where']['contract_plots_relation'] = ['column' => 'id', 'prefix' => 'cpr', 'compare' => 'IN', 'value' => $contractPlotsRelations];
        }

        $counter = $UserDbContractsController->getSalesContractsSubleasedPlot($options, true, false);

        $count = $counter[0]['count'];

        $result = $UserDbContractsController->getSalesContractsSubleasedPlot($options, false, false);
        $resultCount = count($result);
        if (0 == $count) {
            $_SESSION['filtered_sales_contract_plots'][] = 0;

            return $return;
        }

        for ($i = 0; $i < $resultCount; $i++) {
            $result[$i]['due_date_to'] = $start_date;

            $_SESSION['filtered_sales_contract_plots'][] = $result[$i]['gid'];
        }

        // get all data without pagination to set $_SESSION['filtered_sales_contract_plots']
        if ($count > $rows) {
            $options = [
                'return' => [
                    'kvs.gid', 'kvs.ekate', 'kvs.kad_ident', 'kvs.category', 'kvs.area_type',
                    "to_char(c.due_date, 'DD.MM.YYYY') AS due_date",
                    'c.c_num', 'c.id',
                ],
                'where' => [
                    'active' => ['column' => 'active', 'prefix' => 'c', 'compare' => '=', 'value' => 'TRUE'],
                ],
            ];

            $options['plot_id_string'] = implode(', ', $plotIds);

            // Filter
            if ($filterObj['ekate']) {
                $options['where']['ekate'] = ['column' => 'ekate', 'prefix' => 'kvs', 'compare' => '=', 'value' => $filterObj['ekate']];
            }
            if ($filterObj['masiv']) {
                $options['where']['masiv'] = ['column' => 'masiv', 'prefix' => 'kvs', 'compare' => '=', 'value' => $filterObj['masiv']];
            }
            if ($filterObj['number']) {
                $options['where']['number'] = ['column' => 'number', 'prefix' => 'kvs', 'compare' => '=', 'value' => $filterObj['number']];
            }
            if ($start_date) {
                $options['where']['start_date'] = ['column' => 'start_date', 'prefix' => 'c', 'compare' => '<=', 'value' => $start_date[0]];
                $options['where']['due_date'] = ['column' => 'due_date', 'prefix' => 'c', 'compare' => '>', 'value' => $start_date[0]];
            }

            $resultAll = $UserDbContractsController->getSalesContractsSubleasedPlot($options, false, false);
            $resultAllCount = count($resultAll);
            for ($i = 0; $i < $resultAllCount; $i++) {
                $_SESSION['filtered_sales_contract_plots'][] = $resultAll[$i]['gid'];
            }
        }

        $return['rows'] = $result;
        $return['total'] = $count;
        $return['footer'] = [
        ];

        return $return;
    }

    /**
     * Get Due Date.
     *
     * @param int $dateOption
     * @param string $startDateSalesContract
     *
     * @return string
     */
    private function getDueDate($dateOption, $startDateSalesContract)
    {
        // "07.09.2015"

        $dueDate = '';

        if (SalesContractsSubleasedPlotsGrid::DATE_OPTION_CONTRACT == (int)$dateOption) {
            $dueDate = $startDateSalesContract;
        }

        if (SalesContractsSubleasedPlotsGrid::DATE_OPTION_YEAR == (int)$dateOption) {
            $dueDate = $this->getEndOfBusinessYear();
        }

        return date('Y-m-d', strtotime($dueDate));
    }

    /**
     * Get End Of Business Year.
     *
     * @return string
     */
    private function getEndOfBusinessYear()
    {
        $currentMonth = (int)date('m');

        if ($currentMonth <= 9) {
            $date = '30.09.' . date('Y');
        } else {
            $date = '30.09.' . date('Y', strtotime('+1 year'));
        }

        return $date;
    }
}
