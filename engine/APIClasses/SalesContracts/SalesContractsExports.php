<?php

namespace TF\Engine\APIClasses\SalesContracts;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\ExportWordDocClass;
use TF\Engine\Kernel\PrintPdf;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbOwners\UserDbOwnersController;
use TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Creates the export files for printing sales contracts.
 *
 * @rpc-module SalesContracts
 *
 * @rpc-service-id sales-contracts-exports
 *
 * @property UserDbController $UserDbController
 * @property UserDbPlotsController $UserDbPlotsController
 * @property UsersController $UsersController
 */
class SalesContractsExports extends TRpcApiProvider
{
    /**
     * Initialize the required database controllers.
     */
    private $UserDbController;
    private $UserDbPlotsController;
    private $UserDbOwnersController;
    private $UsersController;
    private $FarmingController;

    /**
     * Register all public rpc methods.
     */
    public function registerMethods()
    {
        return [
            'exportSaleContractBlank' => ['method' => [$this, 'exportSaleContractBlank']],
            'deleteFile' => ['method' => [$this, 'deleteFile']],
            'downloadAttached' => ['method' => [$this, 'downloadAttached']],
        ];
    }

    /**
     * Export Sales Contract Blank.
     *
     * @api-method exportSaleContractBlank
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer template_id
     *                         #item integer sale_contract_id
     *                         #item string  blank_type
     *                         }
     *
     * @return array
     */
    public function exportSaleContractBlank($rpcParams)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);
        $UsersController = new UsersController('Users');
        $FarmingController = new FarmingController('Farming');

        // get template data
        $options = [
            'tablename' => $UserDbController->DbHandler->tableTemplates,
            'where' => [
                'template_id' => ['column' => 'id', 'compare' => '=', 'value' => $rpcParams['template_id']],
            ],
        ];

        $template_results = $UserDbController->getItemsByParams($options, false, false);

        if (0 == count($template_results)) {
            $this->Response->reload();
        }

        $template = $template_results[0]['html'];

        // get contract data
        $options = [
            'tablename' => $UserDbController->DbHandler->tableSalesContracts,
            'where' => [
                'contract_id' => ['column' => 'id', 'compare' => '=', 'value' => $rpcParams['sale_contract_id']],
            ],
        ];

        $contract_results = $UserDbController->getItemsByParams($options, false, false);

        if (0 == count($contract_results)) {
            $this->Response->reload();
        }

        $contractData = $contract_results[0];

        // get farming
        $options = [
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $contractData['farming_id']],
            ],
        ];

        $farming_results = $FarmingController->getFarmings($options, false, false);

        if (0 == count($farming_results)) {
            $this->Response->reload();
        }

        // get plots data and create table
        if (strstr($template, '[[imoti]]') || strstr($template, '[[imoti_podrobno')) {
            $options = [
                'return' => [
                    'DISTINCT(kvs.gid)', 'kvs.kad_ident', 'kvs.virtual_ntp_title as area_type', 'kvs.virtual_category_title as category', 'kvs.mestnost', 'kvs.number', 'kvs.ekate', 'kvs.virtual_ekatte_name as land', 'scpr.contract_area_for_sale', 'kvs.document_area',
                    "LPAD(masiv::text, 3, '0') || LPAD(number::text, 3, '0') as imoten_nomer",
                    'price_per_acre',
                ],
                'where' => [
                    'id' => ['column' => 'id', 'prefix' => 'sc',  'compare' => '=', 'value' => $rpcParams['sale_contract_id']],
                ],
            ];

            $plotsResults = $UserDbPlotsController->getPlotSalesContractData($options, false, false);
            $plotsCount = count($plotsResults);
            $ekates = [];
            for ($i = 0; $i < $plotsCount; $i++) {
                if (!in_array($plotsResults[$i]['ekate'], $ekates)) {
                    $ekates[] = $plotsResults[$i]['ekate'];
                }
                $plotsResults[$i]['contract_area_for_sale'] = number_format($plotsResults[$i]['contract_area_for_sale'], 3, '.', '');
                $plotsResults[$i]['document_area'] = number_format($plotsResults[$i]['document_area'], 3, '.', '');

                if ('' == $plotsResults[$i]['kad_ident']) {
                    $plotsResults[$i]['kad_ident'] = '[Няма информация]';
                }
            }

            if (strstr($template, '[[imoti]]') && !empty($ekates)) {
                $plotsData = '<table align="center" cellspacing="0" cellpadding="3" border="1">';
                $plotsData .= '<thead>';
                $plotsData .= '<tr align="center">';
                $plotsData .= '<th>№</th>';
                $plotsData .= '<th>Землище</th>';
                $plotsData .= '<th>Местност</th>';
                $plotsData .= '<th>Идентификатор</th>';
                $plotsData .= '<th>НТП</th>';
                $plotsData .= '<th>Продадена<br/>площ(дка)</th>';
                $plotsData .= '<th>Площ по <br/>документ(дка)</th>';
                $plotsData .= '</tr>';
                $plotsData .= '</thead>';
                $plotsData .= '<tbody>';

                $total_area = 0;
                $total_document_area = 0;

                for ($i = 0; $i < $plotsCount; $i++) {
                    $plotsData .= '<tr>';
                    $plotsData .= '<td width="20" align="center">' . ($i + 1) . '</td>';
                    $plotsData .= '<td width="120" align="center">' . $plotsResults[$i]['land'] . '</td>';
                    $plotsData .= '<td width="115" align="center">' . $plotsResults[$i]['mestnost'] . '</td>';
                    $plotsData .= '<td width="115" align="center">' . $plotsResults[$i]['kad_ident'] . '</td>';
                    $plotsData .= '<td width="180" align="center">' . $plotsResults[$i]['area_type'] . '</td>';
                    $plotsData .= '<td width="70" align="center">' . $plotsResults[$i]['contract_area_for_sale'] . '</td>';
                    $plotsData .= '<td width="70" align="center">' . $plotsResults[$i]['document_area'] . '</td>';
                    $plotsData .= '</tr>';

                    $total_area += $plotsResults[$i]['contract_area_for_sale'];
                    $total_document_area += $plotsResults[$i]['document_area'];
                }

                $plotsData .= '<tr>';
                $plotsData .= '<td width="20" align="center"></td>';
                $plotsData .= '<td width="120" align="center"></td>';
                $plotsData .= '<td width="115" align="center"></td>';
                $plotsData .= '<td width="115" align="center"></td>';
                $plotsData .= '<td width="180" align="center"><b>Общо</b></td>';
                $plotsData .= '<td width="70" align="center"><b>' . number_format($total_area, 3, '.', '') . '</b></td>';
                $plotsData .= '<td width="70" align="center"><b>' . number_format($total_document_area, 3, '.', '') . '</b></td>';
                $plotsData .= '</tr>';

                $plotsData .= '</tbody>';
                $plotsData .= '</table>';
            }

            if (strstr($template, '[[imoti_podrobno') && !empty($ekates)) {
                $regex = '/\\[\\[imoti_podrobno (.*)\\]\\]/';
                preg_match_all($regex, $template, $matches);

                $plotsDetailedDataArr = [];
                foreach ($matches[1] as $key => $match) {
                    $columns = explode(' ', $match);
                    $columnsCount = count($columns);
                    if ($columnsCount > 0) {
                        $plotsDetailedData = '<table cellspacing="0" cellpadding="3" border="1">';

                        // header
                        $plotsDetailedData .= '<thead>';
                        $plotsDetailedData .= '<tr align="center">';
                        $plotsDetailedData .= '<th>№</th>';

                        foreach ($columns as $keyC => $column) {
                            $plotsDetailedData .= '<th>' . $GLOBALS['Contracts']['variables_plots_detailed'][$column] . '</th>';
                        }

                        $plotsDetailedData .= '</tr>';
                        $plotsDetailedData .= '</thead>';

                        // body
                        $plotsDetailedData .= '<tbody>';

                        $total_contract_area_for_sale = 0;
                        $total_document_area = 0;
                        $total_price_sum = 0;
                        $total_price_per_acre = 0;
                        for ($i = 0; $i < $plotsCount; $i++) {
                            $plotsResults[$i]['zemlishte'] = $plotsResults[$i]['land'];
                            $plotsResults[$i]['ntp'] = $plotsResults[$i]['area_type'];
                            $plotsResults[$i]['price_sum'] = number_format($plotsResults[$i]['contract_area_for_sale'] * $plotsResults[$i]['price_per_acre'], 2, '.', '');

                            $plotsDetailedData .= '<tr>';
                            $plotsDetailedData .= '<td style="white-space: nowrap;" align="center">' . ($i + 1) . '</td>';
                            foreach ($columns as $keyCo => $column) {
                                if ('ntp' === $column) {
                                    $plotsDetailedData .= '<td width="115" align="center">' . $plotsResults[$i][$column] . '</td>';

                                    continue;
                                }

                                $plotsDetailedData .= '<td style="white-space: nowrap;" align="center">' . $plotsResults[$i][$column] . '</td>';
                            }
                            $plotsDetailedData .= '</tr>';

                            if (in_array('contract_area_for_sale', $columns) || in_array('document_area', $columns)
                                || in_array('price_sum', $columns) || in_array('price_per_acre', $columns)) {
                                $total_contract_area_for_sale += $plotsResults[$i]['contract_area_for_sale'];
                                $total_document_area += $plotsResults[$i]['document_area'];
                                $total_price_sum += $plotsResults[$i]['price_sum'];
                                $total_price_per_acre += $plotsResults[$i]['price_per_acre'];
                            }
                        }

                        // footer
                        if ($total_contract_area_for_sale > 0 || $total_document_area > 0) {
                            $plotsDetailedData .= '<tr>';

                            // column for numeration
                            $plotsDetailedData .= '<td style="white-space: nowrap;">Общо</td>';

                            for ($m = 0; $m < $columnsCount; $m++) {
                                $column = $columns[$m];

                                if ('contract_area_for_sale' === $column) {
                                    $plotsDetailedData .= '<td style="white-space: nowrap;" align="center"><b>' . number_format($total_contract_area_for_sale, 3, '.', '') . '</b></td>';
                                } elseif ('document_area' === $column) {
                                    $plotsDetailedData .= '<td style="white-space: nowrap;" align="center"><b>' . number_format($total_document_area, 3, '.', '') . '</b></td>';
                                } elseif ('price_sum' === $column) {
                                    $plotsDetailedData .= '<td style="white-space: nowrap;" align="center"><b>' . number_format($total_price_sum, 2, '.', '') . '</b></td>';
                                } elseif ('price_per_acre' === $column) {
                                    $plotsDetailedData .= '<td style="white-space: nowrap;" align="center"><b>' . number_format($total_price_per_acre, 2, '.', '') . '</b></td>';
                                } else {
                                    $plotsDetailedData .= '<td style="white-space: nowrap;"></td>';
                                }
                            }

                            $plotsDetailedData .= '</tr>';
                        }

                        $plotsDetailedData .= '</tbody>';
                        $plotsDetailedData .= '</table>';

                        $plotsDetailedDataArr[$key] = $plotsDetailedData;
                    }
                }
            }
        }

        // replace the data
        $template = str_replace('[[nomer_na_dogovor]]', $contractData['c_num'], $template);
        $template = str_replace('[[data_na_dogovor]]', strftime('%d.%m.%Y', strtotime($contractData['c_date'])) . 'г.', $template);
        $template = str_replace('[[vlizane_v_sila]]', strftime('%d.%m.%Y', strtotime($contractData['start_date'])) . 'г.', $template);
        $template = str_replace('[[kraina_data]]', strftime('%d.%m.%Y', strtotime($contractData['due_date'])) . 'г.', $template);
        $template = str_replace('[[stopanstvo]]', $farming_results[0]['name'], $template);
        $template = str_replace('[[stopanstvo_address]]', $farming_results[0]['address'], $template);
        $template = str_replace('[[stopanstvo_firma]]', $farming_results[0]['company'], $template);
        $template = str_replace('[[stopanstvo_bulstat]]', $farming_results[0]['bulstat'], $template);
        $template = str_replace('[[stopanstvo_firma_address]]', $farming_results[0]['company_address'], $template);
        $template = str_replace('[[stopanstvo_mol]]', $farming_results[0]['mol'], $template);
        $template = str_replace('[[stopanstvo_mol_egn]]', $farming_results[0]['mol_egn'], $template);
        $template = str_replace('[[nomer_na_vpisvane]]', $contractData['sv_num'], $template);
        $template = str_replace('[[data_na_vpisvane]]', strftime('%d.%m.%Y', strtotime($contractData['sv_date'])) . 'г.', $template);
        $template = str_replace('[[renta]]', number_format($contractData['renta'], 2, ',', ''), $template);
        $template = str_replace('[[imoti]]', $plotsData, $template);

        // imoti_podrobno
        $matchCount = count($matches[0]);
        for ($n = 0; $n < $matchCount; $n++) {
            $plotDetailedData = $plotsDetailedDataArr[$n];
            $allMatchesPlotDet = $matches[0][$n];

            $template = str_replace($allMatchesPlotDet, $plotDetailedData, $template);
        }

        $date = date('Y-m-d-H-i-s');
        $blankName = 'dogovor_' . $contractData['id'] . '_' . $date;

        @mkdir(SALES_CONTRACTS_PATH);
        @mkdir(SALES_CONTRACTS_PATH . $this->User->GroupID);

        if ('pdf' == $rpcParams['blank_type']) {
            $blankName .= '.pdf';
            $headerTxt = $this->getHeaderFooterTag('page_header', $template);
            $footerTxt = $this->getHeaderFooterTag('page_footer', $template);
            $headerTxt = $this->formatPDFHeaderFooter('page_header', $headerTxt);
            $footerTxt = $this->formatPDFHeaderFooter('page_footer', $footerTxt);
            $template = $headerTxt . $template . $footerTxt;
            $template = '<page style="font-family: freeserif" backtop="50px" backbottom="50px" >' . $template . '</page>';

            $newPDFFilePath = PUBLIC_SALES_CONTRACTS_RELATIVE_PATH . $this->User->GroupID . '/' . $blankName;

            $printPdf = new PrintPdf();
            $printPdf->generateFromHtml($template, $newPDFFilePath, ['pageNumber' => $template_results[0]['show_page_numbers']], true);

            return ['file_path' => $newPDFFilePath, 'file_name' => $blankName];
        }

        if ('doc' == $rpcParams['blank_type']) {
            $docOpts = [
                'sections' => [
                    'WordSection' => [
                        'size' => '21cm 29.7cm',
                        'margin' => '1.1cm 2cm 1.5cm 2cm',
                        'mso-page-orientation' => 'portrait',
                    ],
                ],
            ];

            $exportWordDoc = new ExportWordDocClass();
            if (!empty($template_results[0]['show_page_numbers'])) {
                $template = $exportWordDoc->addPageNumbers($template, $template_results[0]['show_page_numbers']);
            }
            $newWordDoc = $exportWordDoc->export($blankName, $template, true, $docOpts);

            return ['file_path' => $newWordDoc, 'file_name' => $blankName . '.doc'];
        }
    }

    public function getHeaderFooterTag($tagName, &$template)
    {
        $tagContentRe = "/\[\[{$tagName}_\]\](?P<content>.*?)\[\[_{$tagName}\]\]/s";
        $matches = [];

        if (!preg_match_all($tagContentRe, $template, $matches)) {
            return '';
        }
        $template = preg_replace($tagContentRe, '', $template);

        return $matches['content'][0];
    }

    public function formatPDFHeaderFooter($tagName, $tagCont)
    {
        $imageRe = "/<img\\s[^>]*?src\\s*=\\s*['\\\"](?:(?P<img_src>[^'\\\"]*?)['\\\"][^>]*?>)/s";
        $matches = [];
        $formatedTag = "<{$tagName}>{$tagCont}</{$tagName}>";

        if (preg_match_all($imageRe, $tagCont, $matches)) {
            $newImage = '<p style="text-align: center"><img src="' . SITE_URL . '\1" /></p>';
            $formatedTag = "<{$tagName}>" . preg_replace($imageRe, $newImage, $tagCont) . "</{$tagName}>";
        }

        return $formatedTag;
    }

    /**
     * Download File from  "Архив файлове".
     *
     * @param int $fileId
     *
     * @return array|string
     */
    public function downloadAttached($fileId)
    {
        $UserDbController = new UserDbController($this->User->Database);

        @mkdir(SALES_CONTRACTS_PATH);
        @mkdir(SALES_CONTRACTS_PATH . $this->User->GroupID);

        $options = [
            'tablename' => $UserDbController->DbHandler->salesContractsFilesTable,
            'where' => [
                'file_id' => ['column' => 'id', 'compare' => '=', 'value' => $fileId],
            ],
        ];

        $results = $UserDbController->getItemsByParams($options, false, false);

        if (!count($results)) {
            return [];
        }

        $file_fragments = explode('.', $results[0]['filename']);
        $filename = current($file_fragments);
        $ext = end($file_fragments);

        $filePath = SALES_CONTRACTS_PATH . $results[0]['group_id'] . '/' . $results[0]['user_id'] . '/' . $results[0]['id'] . '.' . $ext;
        $newFileName = SALES_CONTRACTS_PATH . $results[0]['group_id'] . '/' . $results[0]['user_id'] . '/' . $filename . '_' . $results[0]['id'] . '.' . $ext;

        if (file_exists($filePath)) {
            $isRenamed = rename($filePath, $newFileName);
            if ($isRenamed) {
                return PUBLIC_SALES_CONTRACTS_RELATIVE_PATH . $results[0]['group_id'] . '/' . $results[0]['user_id'] . '/' . $filename . '_' . $results[0]['id'] . '.' . $ext;
            }

            return PUBLIC_SALES_CONTRACTS_RELATIVE_PATH . $results[0]['group_id'] . '/' . $results[0]['user_id'] . '/' . $results[0]['id'] . '.' . $ext;
        }

        if (file_exists($newFileName)) {
            return PUBLIC_SALES_CONTRACTS_RELATIVE_PATH . $results[0]['group_id'] . '/' . $results[0]['user_id'] . '/' . $filename . '_' . $results[0]['id'] . '.' . $ext;
        }

        return 'aa';
    }

    /**
     * Delete created file.
     *
     * @api-method deleteFile
     *
     * @param string $fileName
     */
    public function deleteFile($fileName)
    {
        $ext = pathinfo($fileName, PATHINFO_EXTENSION);

        if ('pdf' == $ext) {
            @unlink(PUBLIC_SALES_CONTRACTS_RELATIVE_PATH . $this->User->GroupID . '/' . $fileName);
        } elseif ('doc' == $ext) {
            @unlink('files/uploads/blanks/' . $fileName);
        }
    }
}
