<?php

namespace TF\Engine\APIClasses\SalesContracts;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDbContracts\UserDbContractsController;
use TF\Engine\Plugins\Core\Users\UsersController;

// Prado::using('Plugins.Core.Contracts.conf');
// Prado::using('Plugins.Core.Contracts.*');
// Prado::using('Plugins.Core.Farming.conf');
// Prado::using('Plugins.Core.Farming.*');
// Prado::using('Plugins.Core.Layers.conf');
// Prado::using('Plugins.Core.Layers.*');
// Prado::using('Plugins.Core.UserDb.*');
// Prado::using('Plugins.Core.UserDbContracts.*');

/**
 * Plots Grid SalesContracts.
 *
 * @rpc-module SalesContracts
 *
 * @rpc-service-id sales-contracts-plots-datagrid
 */
class SalesContractsPlotsGrid extends TRpcApiProvider
{
    /**
     * Registers the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getSalesContractsPlotData'],
                'validators' => [
                    'filterObj' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * Gets all plots, associated with selected sale contract.
     *
     * @api-method read
     *
     * @param array $filterObj {
     *
     * @item integer contract_id
     * @item string ekate
     * @item string masiv
     * @item string number
     * }
     *
     * @param int $page pagination parameters
     * @param int $rows pagination parameters
     * @param string $sort pagination parameters
     * @param string $order pagination parameters
     *
     * @return array
     */
    public function getSalesContractsPlotData(array $filterObj, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $UserDbContractsController = new UserDbContractsController($this->User->Database);
        $UsersController = new UsersController('Users');

        // create default return
        $return = [
            'rows' => [],
            'total' => 0,
            'footer' => [
                [
                    'kad_ident' => '<b>ОБЩО</b>',
                    'used_area' => '',
                    'area' => '',
                ],
            ],
        ];

        // check if default grid is not loaded with contract_id = 0
        if (!$filterObj['contract_id'] || 0 == $filterObj['contract_id']) {
            return $return;
        }

        $options = [
            'return' => [
                '*',
            ],
            'where' => [
                'sales_contract_id' => ['column' => 'sales_contract_id', 'compare' => '=', 'value' => $filterObj['contract_id']],
            ],
        ];

        $counter = $UserDbContractsController->getSalesContractsPlotsData($options, true, false);

        $count = $counter[0]['count'];

        if (0 == $count) {
            return $return;
        }

        $result = $UserDbContractsController->getSalesContractsPlotsData($options, false, false);
        $resultCount = count($result);

        $total_area = 0;
        $total_contract_area = 0;

        for ($i = 0; $i < $resultCount; $i++) {
            $result[$i]['category'] = $result[$i]['virtual_category_title'];
            $result[$i]['area_type'] = $result[$i]['virtual_ntp_title'];
            $result[$i]['used_area'] = number_format($result[$i]['used_area'], 3, '.', '');
            $result[$i]['area'] = number_format($result[$i]['area'] / 1000, 3, '.', '');

            if (!$result[$i]['document_area']) {
                $result[$i]['document_area'] = $result[$i]['area'];
            }

            $result[$i]['price_per_acre'] = $result[$i]['price_per_acre'] ? BGNtoEURO($result[$i]['price_per_acre']) : '-';

            if ($result[$i]['contract_area_for_sale'] && '-' != $result[$i]['price_per_acre']) {
                $result[$i]['price_sum'] = $result[$i]['contract_area_for_sale'] * $result[$i]['price_per_acre'];
                $result[$i]['price_sum'] = BGNtoEURO($result[$i]['price_sum']);
            } else {
                $result[$i]['price_sum'] = $result[$i]['price_sum'] ? $result[$i]['price_sum'] : '-';

                if (is_numeric($result[$i]['price_sum'])) {
                    $result[$i]['price_sum'] = BGNtoEURO($result[$i]['price_sum']);
                }
            }

            if ('' == $result[$i]['kad_ident']) {
                $result[$i]['kad_ident'] = '[Няма информация]';
            }

            $result[$i]['land'] = $result[$i]['virtual_ekatte_name'];

            $total_area += $result[$i]['document_area'];
            $total_contract_area += $result[$i]['contract_area'];

            $result[$i]['contract_area'] = number_format($result[$i]['contract_area'], 3, '.', '');
            $result[$i]['document_area'] = number_format($result[$i]['document_area'], 3, '.', '');
        }

        $return['rows'] = $result;
        $return['total'] = $count;
        $return['footer'] = [
            [
                'kad_ident' => '<b>ОБЩО</b>',
                'contract_area' => number_format($total_contract_area, 3, '.', ''),
                'document_area' => number_format($total_area, 3, '.', ''),
            ],
        ];

        return $return;
    }
}
