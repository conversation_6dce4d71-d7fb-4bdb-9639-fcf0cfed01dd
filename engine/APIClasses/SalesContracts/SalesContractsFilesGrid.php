<?php

namespace TF\Engine\APIClasses\SalesContracts;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\Users\UsersController;

// Prado::using('Plugins.Core.Users.*');

/**
 * Files Grid SalesContracts.
 *
 * @rpc-module SalesContracts
 *
 * @rpc-service-id sales-contracts-files-maingrid
 *
 * @property UserDbController $UserDbController
 * @property UsersController $UsersController
 */
class SalesContractsFilesGrid extends TRpcApiProvider
{
    private $module = 'SalesContracts';
    private $service_id = 'sales-contracts-files-maingrid';

    /**
     * Registers the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getSalesContractsFilesGrid'],
                'validators' => [
                    'contractId' => 'validateInteger',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateText',
                    'order' => 'validateOrder',
                ], ],
            'delete' => ['method' => [$this, 'deleteSalesContractsFiles']],
        ];
    }

    /**
     * Get Sales Contracts Files Grid.
     *
     * @api-method read
     *
     * @param int $page pagination parameters
     * @param int $rows pagination parameters
     * @param string $sort pagination parameters
     * @param string $order pagination parameters
     *
     * @return array
     */
    public function getSalesContractsFilesGrid(int $contractId, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        // create the default return
        $return = [
            'rows' => [],
            'total' => 0,
        ];

        if ($this->User->isGuest) {
            return $return;
        }

        if ('' == $contractId) {
            return $return;
        }

        // init controllers
        $UserDbController = new UserDbController($this->User->Database);

        // options for contract files query
        $options = [
            'tablename' => $UserDbController->DbHandler->salesContractsFilesTable,
            'where' => [
                'sales_contract_id' => ['column' => 'sales_contract_id', 'compare' => '=', 'value' => $contractId],
            ],
        ];
        $options['sort'] = $sort;
        $options['order'] = $order;
        $options['offset'] = ($page - 1) * $rows;
        $options['limit'] = $rows;

        $counter = $UserDbController->getItemsByParams($options, true, false);
        if (0 == $counter[0]['count']) {
            return $return;
        }

        $results = $UserDbController->getItemsByParams($options, false, false);
        $resultsCount = count($results);

        for ($i = 0; $i < $resultsCount; $i++) {
            $results[$i]['date'] = strftime('%d.%m.%Y', strtotime($results[$i]['date']));
        }

        $return['rows'] = $results;
        $return['total'] = $counter[0]['count'];

        return $return;
    }

    /**
     * Delete Sales Contracts Files.
     *
     * @api-method delete
     *
     * @param int $id
     *
     * @return array
     */
    public function deleteSalesContractsFiles($id)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $fileID = $id;

        // delete file
        $options = [
            'tablename' => $UserDbController->DbHandler->salesContractsFilesTable,
            'where' => [
                'file_id' => ['column' => 'id', 'compare' => '=', 'value' => $fileID],
            ],
        ];

        $results = $UserDbController->getItemsByParams($options, false, false);
        if (!count($results)) {
            return [];
        }

        $file_fragments = explode('.', $results[0]['filename']);
        $filename = current($file_fragments);
        $ext = end($file_fragments);

        $filePath = SALES_CONTRACTS_PATH . $results[0]['group_id'] . '/' . $results[0]['user_id'] . '/' . $results[0]['id'] . '.' . $ext;
        $newFileName = SALES_CONTRACTS_PATH . $results[0]['group_id'] . '/' . $results[0]['user_id'] . '/' . $filename . '_' . $results[0]['id'] . '.' . $ext;

        if (file_exists($filePath)) {
            unlink($filePath);
        }
        if (file_exists($newFileName)) {
            unlink($newFileName);
        }

        // delete record from the table
        $options = [
            'tablename' => $UserDbController->DbHandler->salesContractsFilesTable,
            'id_string' => $fileID,
        ];

        $UserDbController->deleteItemsByParams($options);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['requested_id' => $id], ['deleted_id_info' => $results], 'Deleting sales contract file');
    }
}
