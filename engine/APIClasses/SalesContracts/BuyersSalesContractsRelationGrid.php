<?php

namespace TF\Engine\APIClasses\SalesContracts;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbContracts\UserDbContractsController;
use TF\Engine\Plugins\Core\Users\UsersController;

// Prado::using('Plugins.Core.UserDbOwners.*');
// Prado::using('Plugins.Core.UserDbContracts.*');
// Prado::using('Plugins.Core.Contracts.conf');

/**
 * Buyers Sales Contracts Relation Grid.
 *
 * @rpc-module SalesContracts
 *
 * @rpc-service-id buyers-sales-contracts-relation-grid
 */
class BuyersSalesContractsRelationGrid extends TRpcApiProvider
{
    private $module = 'SalesContracts';
    private $service_id = 'buyers-sales-contracts-relation-grid';

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readBuyersSalesContractsRelation'],
                'validators' => [
                    'salesContractId' => 'validateNumber, validateRequired',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateText',
                    'order' => 'validateOrder',
                ],
            ],
            'deleteBuyerRel' => ['method' => [$this, 'deleteBuyerRel'],
                'validators' => [
                    'relationId' => 'validateInteger, validateRequired',
                ],
            ],
        ];
    }

    /**
     * Delete Buyer Relation.
     *
     * @api-method deleteBuyerRel
     *
     * @param int $relationId
     *
     * @throws MTRpcException
     *
     * @return array|bool
     */
    public function deleteBuyerRel($relationId)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $relInfo = $this->CheckForExistingCotnractOwnerRelation($relationId, true);

        if (!$relationId) {
            return [];
        }

        $options = [
            'tablename' => $UserDbController->DbHandler->tableSalesContractsBuyersRel,
            'id_string' => $relationId,
        ];
        $UserDbController->deleteItemsByParams($options);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['requested_id' => $relationId], ['deleted_id_info' => $relInfo], 'Deleting sales contract buyer relation');

        return true;
    }

    /**
     * Read Buyers SalesContracts Relation.
     *
     * @api-method read
     *
     * @param int|string $page pagination parameters
     * @param int|string $rows pagination parameters
     * @param string $sort pagination parameters
     * @param string $order pagination parameters
     *
     * @return array
     */
    public function readBuyersSalesContractsRelation(int $salesContractId, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $UserDbContractsController = new UserDbContractsController($this->User->Database);

        // Get result
        $options = [
            'return' => [
                'scbr.id', 'scbr.buyer_id', 'b.name', 'b.contacts',
            ],
            'where' => [
                'sales_contract_id' => ['column' => 'sales_contract_id', 'prefix' => 'scbr', 'compare' => '=', 'value' => $salesContractId],
            ],
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
        ];

        $counter = $UserDbContractsController->getBuyersSalesContractsRelation($options, true, false);

        if (0 == $counter[0]['count']) {
            return [
                'rows' => [],
                'total' => 0,
            ];
        }

        $results = $UserDbContractsController->getBuyersSalesContractsRelation($options, false, false);

        $data['rows'] = $results;
        $data['total'] = $counter[0]['count'];

        return $data;
    }

    private function CheckForExistingCotnractOwnerRelation($relationId, $returnRelInfo = false)
    {
        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableSalesContractsBuyersRel,
            'return' => ['*'],
            'where' => [
                'id' => $relationId,
            ],
        ];

        $results = $UserDbController->getItemsByParams($options);

        if (!isset($results[0]['id'])) {
            throw new MTRpcException('NON_EXISTING_CONTRACT_BUYER_RELATION', -33554);
        }

        if ($returnRelInfo) {
            return $results[0];
        }
    }
}
