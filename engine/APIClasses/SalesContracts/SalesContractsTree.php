<?php

namespace TF\Engine\APIClasses\SalesContracts;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbContracts\UserDbContractsController;
use TF\Engine\Plugins\Core\Users\UsersController;

// Prado::using('Plugins.Core.UserDb.*');
// Prado::using('Plugins.Core.UserDb.conf');
// Prado::using('Plugins.Core.Users.*');
// Prado::using('Plugins.Core.Users.conf');
// Prado::using('Plugins.Core.Contracts.conf');
// Prado::using('Plugins.Core.Farming.conf');
// Prado::using('Plugins.Core.Farming.*');
// Prado::using('Plugins.Core.UserDbContracts.*');

/**
 * Subleased Plots Grid SalesContracts.
 *
 * @rpc-module SalesContracts
 *
 * @rpc-service-id sales-contracts-tree
 *
 * @property UserDbController $UserDbController
 * @property UserDbContractsController $UserDbContractsController
 * @property FarmingController $FarmingController
 */
class SalesContractsTree extends TRpcApiProvider
{
    private $UserDbController;
    private $UserDbContractsController;
    private $FarmingController;
    private $module = 'SalesContracts';
    private $service_id = 'sales-contracts-tree';

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readSalesContractsTree']],
            'addSalesContract' => ['method' => [$this, 'addSalesContract'],
                'validators' => [
                    'params' => [
                        'c_num' => 'validateText, validateRequired',
                        'contract_start_date' => 'validateText, validateRequired',
                        'contract_date' => 'validateText, validateRequired',
                        'farming' => 'validateDigitsOnly, validateRequired',
                        'tom' => 'validateText',
                        'na_num' => 'validateText',
                        'delo' => 'validateText',
                        'court' => 'validateText',
                        'price_per_acre' => 'validateNumber',
                        'contract_comment' => 'validateText',
                    ],
                ],
            ],
            'updateSalesContract' => ['method' => [$this, 'updateSalesContract'],
                'validators' => [
                    'contractId' => 'validateNumber, validateRequired',
                ],
            ],
            'loadSalesContract' => ['method' => [$this, 'loadSalesContract'],
                'validators' => [
                    'contractId' => 'validateNumber , validateRequired',
                ],
            ],
            'deleteSalesContract' => ['method' => [$this, 'deleteSalesContract'],
                'validators' => [
                    'path' => 'validateText , validateRequired , validateNotNull ',
                ],
            ],
            'checkForExistence' => ['method' => [$this, 'checkForExistence']],
        ];
    }

    /**
     * Check Sales Contract For Existence.
     *
     * @api-method checkForExistence
     *
     * @param string $c_num
     * @param int $farming
     *
     * @return bool
     */
    public function checkForExistence($c_num, $farming)
    {
        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableSalesContracts,
            'where' => [
                'c_num' => ['column' => 'c_num', 'compare' => '=', 'value' => $c_num],
                'farming_id' => ['column' => 'farming_id', 'compare' => '=', 'value' => $farming],
                'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'value' => 'false'],
                'is_annnex' => ['column' => 'is_annex', 'compare' => '=', 'value' => 'false'],
            ],
        ];

        $count = $UserDbController->getItemsByParams($options, true, false);

        return (bool) ($count[0]['count']);
    }

    /**
     * Read Sales Contracts Tree.
     *
     * @api-method read
     *
     * @param array $filterObj {
     *
     * @item string c_num
     * @item string farming
     * @item string date_from
     * @item string date_to
     * @item string due_date_from
     * @item string due_date_to
     * @item string kad_ident
     * @item string ekatte
     * @item string masiv
     * @item string number
     * @item string buyer_name
     * }
     *
     * @param int $page pagination parameters
     * @param int $rows pagination parameters
     * @param string $sort pagination parameters
     * @param string $order pagination parameters
     *
     * @return array
     */
    public function readSalesContractsTree($filterObj, $page = null, $rows = null, $sort = '', $order = '')
    {
        // init controllers
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbContractsController = new UserDbContractsController($this->User->Database);
        $FarmingController = new FarmingController('Farming');
        $arrayHelper = $FarmingController->ArrayHelper;

        $userFarmings = $FarmingController->getUserFarmings();
        $userFarmingIds = array_keys($userFarmings);

        $farmingIds = $arrayHelper->filterEmptyStringArr($filterObj['farming']);
        $farmingIds = isset($farmingIds) && count($farmingIds) > 0
            ? array_intersect($farmingIds, $userFarmingIds)
            : [...$userFarmingIds, null];

        // default array
        $return = [];

        $options = [
            'custom_counter' => 'COUNT(DISTINCT(sc.id))',
            'return' => [
                'sc.*',
                'array_agg(kvs.gid) as gid',
                'array_agg(kvs.kad_ident) as kad_ident',
                'array_agg(kvs.ekate) as ekatte',
                'array_agg(kvs.masiv) as masiv',
                'array_agg(kvs.number) as number',
                'array_agg(b.name) as name',
            ],
            'where' => [
                // filter
                'c_num' => ['column' => 'c_num', 'compare' => 'ILIKE', 'value' => $filterObj['c_num']],
                'farming' => ['column' => 'farming_id', 'compare' => 'IN', 'value' => $farmingIds],
                'start_date_from' => ['column' => 'start_date', 'compare' => '>=', 'value' => $filterObj['date_from']],
                'start_date_to' => ['column' => 'start_date', 'compare' => '<=', 'value' => $filterObj['date_to']],
                'due_date_from' => ['column' => 'due_date', 'compare' => '>=', 'value' => $filterObj['due_date_from']],
                'due_date_to' => ['column' => 'due_date', 'compare' => '<=', 'value' => $filterObj['due_date_to']],
                'kad_ident' => ['column' => 'kad_ident', 'compare' => '=', 'value' => $filterObj['kad_ident']],
                'ekate' => ['column' => 'ekate', 'compare' => 'IN', 'value' => $arrayHelper->filterEmptyStringArr($filterObj['ekatte'])],
                'masiv' => ['column' => 'masiv', 'compare' => '=', 'value' => $filterObj['masiv']],
                'number' => ['column' => 'number', 'compare' => '=', 'value' => $filterObj['number']],
                'name' => ['column' => 'name', 'compare' => 'ILIKE', 'value' => $filterObj['buyer_name']],
                'id' => ['column' => 'id', 'compare' => '=', 'prefix' => 'sc', 'value' => $filterObj['id']],
            ],
            'group' => 'sc.id',
        ];

        if (true == $filterObj['c_num_complete_match']) {
            $options['where']['c_num']['compare'] = '=';
        }

        $counter = $UserDbContractsController->getSalesContractsPlotsAndBayers($options, true, false);

        if (!$counter[0]['count']) {
            return $return;
        }

        $page_limit = 30;
        $options['sort'] = $sort;
        $options['order'] = $order;
        $options['offset'] = ($page - 1) * $page_limit;
        $options['limit'] = $page_limit;

        $results = $UserDbContractsController->getSalesContractsPlotsAndBayers($options, false, false);
        $resultsCount = count($results);
        // transform results into tree format
        for ($i = 0; $i < $resultsCount; $i++) {
            $contractData = $results[$i];

            $contractData['c_date'] = strftime('%d.%m.%Y', strtotime($contractData['c_date']));
            $contractData['start_date'] = strftime('%d.%m.%Y', strtotime($contractData['start_date']));
            $contractData['contract_start_date'] = strftime('%Y-%m-%d', strtotime($contractData['start_date']));

            if ('' != $contractData['due_date']) {
                $contractData['due_date'] = strftime('%d.%m.%Y', strtotime($contractData['due_date']));
                $text = $contractData['c_num'] . ' (' . $contractData['start_date'] . ' - ' . $contractData['due_date'] . ')';
            } else {
                $contractData['due_date'] = '-';
                $text = $contractData['c_num'] . ' (' . $contractData['start_date'] . ')';
            }

            $contractData['farming'] = $userFarmings[$contractData['farming_id']];

            $return[] = [
                'id' => $contractData['id'],
                'text' => $text,
                'attributes' => $contractData,
                'iconCls' => 'icon-tree-document',
            ];
        }

        // add attribute to first listed element of three for custom pagination
        // information for total can not be sent otherwise
        $return[0]['attributes']['pagination']['total'] = $counter[0]['count'];
        $return[0]['attributes']['pagination']['limit'] = $page_limit;

        return $return;
    }

    /**
     * Add Sales Contract.
     *
     * @api-method addSalesContract
     *
     * @param array $params {
     *
     * @item string c_num
     * @item string contract_start_date
     * @item string contract_date
     * @item string farming
     * @item string na_num
     * @item string tom
     * @item string delo
     * @item string court
     * @item string contract_comment
     * }
     *
     * @return int recordID
     */
    public function addSalesContract($params)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $options = [
            'tablename' => $UserDbController->DbHandler->tableSalesContracts,
            'mainData' => [
                'c_num' => $params['c_num'],
                'start_date' => $params['contract_start_date'],
                'c_date' => $params['contract_date'],
                'farming_id' => $params['farming'],
                'na_num' => $params['na_num'],
                'tom' => $params['tom'],
                'delo' => $params['delo'],
                'court' => $params['court'],
                'comment' => $params['contract_comment'],
            ],
        ];

        $recordID = $UserDbController->addItem($options);

        if ($recordID) {
            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['new_data' => $options], ['created_id' => $recordID], 'Adding sales contract');
        }

        return $recordID;
    }

    /**
     * Update Sales Contract.
     *
     * @api-method updateSalesContract
     *
     * @param int $contractId
     * @param array $params {
     *
     * @item string c_num
     * @item string contract_start_date
     * @item string contract_date
     * @item string farming
     * @item string na_num
     * @item string tom
     * @item string delo
     * @item string court
     * @item string contract_comment
     * }
     *
     * @return bool
     */
    public function updateSalesContract($contractId, $params)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        // old contract info for logging
        $contract_options = [
            'tablename' => $UserDbController->DbHandler->tableSalesContracts,
            'return' => ['*'],
            'where' => [
                'sales_contract_id' => ['column' => 'id', 'compare' => '=', 'value' => $contractId],
            ],
        ];

        $contract_results = $UserDbController->getItemsByParams($contract_options);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableSalesContracts,
            'mainData' => [
                'c_num' => $params['c_num'],
                'start_date' => $params['contract_start_date'],
                'c_date' => $params['contract_date'],
                'farming_id' => $params['farming'],
                'na_num' => $params['na_num'],
                'tom' => $params['tom'],
                'delo' => $params['delo'],
                'court' => $params['court'],
                'comment' => $params['contract_comment'],
            ],
            'where' => [
                'id' => $contractId,
            ],
        ];

        $UserDbController->editItem($options);
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['new_data' => $options], ['old_data' => $contract_results[0]], 'Editing sales contract');

        return true;
    }

    /**
     * Load Sales Contract.
     *
     * @api-method loadSalesContract
     *
     * @param int $contractId
     *
     * @return array
     */
    public function loadSalesContract($contractId)
    {
        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableSalesContracts,
            'return' => [
                '*',
                'date(c_date) as c_converted_date',
                'date(start_date) as converted_start_date',
                'date(sv_date) as converted_sv_date',
                'date(due_date) as converted_due_date',
            ],
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $contractId],
            ],
        ];
        $result = $UserDbController->getItemsByParams($options);

        $result[0]['c_date'] = date('Y-m-d', strtotime($result[0]['c_date']));
        $result[0]['start_date'] = date('Y-m-d', strtotime($result[0]['start_date']));

        return $result[0];
    }

    /**
     * Delete Sales Contract.
     *
     * @api-method deleteSalesContract
     *
     * @param int $contractId
     *
     * @return array|bool
     */
    public function deleteSalesContract($contractId)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        // old contract info for logging
        $contract_options = [
            'tablename' => $UserDbController->DbHandler->tableSalesContracts,
            'return' => ['*'],
            'where' => [
                'sales_contract_id' => ['column' => 'id', 'compare' => '=', 'value' => $contractId],
            ],
        ];

        $contract_results = $UserDbController->getItemsByParams($contract_options);
        if (!$contractId) {
            return [];
        }

        $options = [
            'tablename' => $UserDbController->DbHandler->tableSalesContracts,
            'id_string' => $contractId,
        ];
        $UserDbController->deleteItemsByParams($options);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['requested_id' => $contractId], ['old_data' => $contract_results[0]], 'Deleting sales contract');

        return true;
    }
}
