<?php

namespace TF\Engine\APIClasses\KvsStoreService;

use Exception;
use Prado\Web\Services\TRpcApiProvider;
use TF\Application\Entity\RequestedEkatte;
use TF\Engine\Kernel\CmsModule;
use TF\Engine\Kernel\KvsStoreModule;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

include_once __DIR__ . '/conf.php';

/**
 * @rpc-module kvsStoreModule
 *
 * @rpc-service-id kvs-store-service
 */
class KvsStore extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'requestKvs' => ['method' => [$this, 'requestKvs']],
            'searchKvs' => ['method' => [$this, 'searchKvs']],
            'listKvs' => ['method' => [$this, 'listKvs']],
            'getAllowedEkatteCount' => ['method' => [$this, 'getAllowedEkatteCount']],
        ];
    }

    public function requestKvs(array $rpcParams): void
    {
        try {
            if (!$this->canRequestKvs($rpcParams['ekatte_code'])) {
                throw new Exception('You have reached the limit of requested ekatte count');
            }

            /** @var KvsStoreModule */
            $storeModule = $this->getApplication()->getModule('kvsStoreModule');
            $response = $storeModule->requestEkatte($rpcParams['ekatte_code']);

            if (!$response['uuid']) {
                throw new Exception('Missing requested ekatte uuid');
            }

            $requestedEkatte = new RequestedEkatte();
            $requestedEkatte->user_id = $this->User->getUserID();
            $requestedEkatte->group_id = $this->User->getGroupId();
            $requestedEkatte->ekatte_code = $rpcParams['ekatte_code'];
            $requestedEkatte->ekatte_name = $rpcParams['ekatte_name'];
            $requestedEkatte->status = RequestedEkatte::STATUS_REQUESTED;
            $requestedEkatte->kvs_store_uuid = $response['uuid'];
            $requestedEkatte->save();
        } catch (Exception $ex) {
            throw $ex;
        }
    }

    public function listKvs(array $rpcParams, int $page = null, int $rows = null, string $sort = null, string $order = null)
    {
        return RequestedEkatte::list($this->User->getGroupId(), $rpcParams, $page, $rows, $sort, $order);
    }

    public function canRequestKvs($ekatte)
    {
        return ($this->getAllowedEkatteCount() > $this->getCurrentEkatteCount()) || $this->organizationOwnsEkatte($ekatte);
    }

    public function organizationOwnsEkatte($ekatte)
    {
        $UserDbController = new UserDbController($this->User->Database);

        return array_pop($UserDbController->getItemsByParams(
            [
                'return' => ['count(ekate)'],
                'tablename' => 'ekate_combobox',
                'where' => [
                    'ekate' => ['column' => 'ekate', 'compare' => '=', 'value' => $ekatte],
                ],
            ]
        ))['count'] > 0;
    }

    public function getCurrentEkatteCount()
    {
        $UserDbController = new UserDbController($this->User->Database);

        return array_pop($UserDbController->getItemsByParams(
            [
                'return' => ['count(ekate)'],
                'tablename' => 'ekate_combobox',
            ]
        ))['count'];
    }

    public function getAllowedEkatteCount()
    {
        /** @var CmsModule */
        $cmsModule = $this->getApplication()->getModule('cmsModule');
        $packages = $cmsModule->getSubscriptionPackage(CmsModule::PACKAGE_SLUG_ADMINISTRATIVE_MAP);
        $currentDate = strtotime(date('Y-m-d H:i:s')); // Get current date and time

        return array_reduce($packages, function ($carry, $package) use ($currentDate) {
            $startDate = strtotime($package['startDate']['date']);
            $endDate = strtotime($package['endDate']['date']);

            // Check if current date is between startDate and endDate
            if ($currentDate >= $startDate && $currentDate <= $endDate) {
                // If this package has a later endDate than the current carry, or if carry is null, update carry
                if (!$carry || $endDate > strtotime($carry['endDate']['date'])) {
                    $carry = $package; // Update carry to the current package
                }
            }

            return $carry; // Return the carry (which may be null or a package)
        }, null)['amount'] ?? 0;
    }
}
