<?php

namespace TF\Engine\APIClasses\SubsidiesWizard;

use Prado\Web\Services\TRpcApiProvider;
use TF\Application\Common\Config;
use TF\Engine\Kernel\Export2XlsClass;
use TF\Engine\Kernel\PrintPdf;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController;

/**
 * @rpc-module SubsidiesWizard
 *
 * @rpc-service-id for-isak-subsidies-datagrid
 */
class ForIsakSubsidiesGrid extends TRpcApiProvider
{
    private $module = 'SubsidiesWizard';
    private $service_id = 'for-isak-subsidies-datagrid';

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readForIsakSubsidiesGrid'],
                'validators' => [
                    'params' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'saveMultiEdit' => ['method' => [$this, 'saveMultiEdit']],
            'editForIsakPlot' => ['method' => [$this, 'editForIsakPlot']],
            'exportGridToXLS' => ['method' => [$this, 'exportGridToXLS']],
            'exportGridToPDF' => ['method' => [$this, 'exportGridToPDF']],
        ];
    }

    /**
     * Reads plots compatible with permanently green areas.
     *
     * @api-method read
     *
     * @param array $params
     *                      {
     *                      #item integer $layer_id
     *                      #item array filterObj {
     *                      #item string comment
     *                      #item string culture
     *                      #item string edited
     *                      #item string ekate
     *                      #item string prc_name
     *                      #item string schema
     *                      }
     *                      }
     * @param int $page pagination parameter
     * @param int $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array {
     *               #item integer total
     *               #item array rows {
     *               #item integer gid
     *               #item string prc_name
     *               #item string edited
     *               #item string comment
     *               #item string ekatte,
     *               #item string cropname
     *               #item string cropcode
     *               #item string natura_sitecode
     *               #item boolean sepp
     *               #item boolean zdp
     *               #item boolean pndp
     *               #item boolean nr1
     *               #item boolean nr2
     *               #item integer vps_type
     *               #item float area
     *               #item string common_cultures
     *               #item boolean crop_short_type
     *               #item boolean is_active_culture
     *               #item string schema
     *               #item string st_astext
     *
     *     }
     *     #item array footer {
     *         #item array {
     *             #item string area,
     *             #item string cropname,
     *         }
     *     }
     * }
     */
    public function readForIsakSubsidiesGrid(array $params, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $filterObj = $params['filterObj'];
        $layer_id = $params['layer_id'];

        $PlotsDbController = new UserDbPlotsController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);
        $FarmingController = new FarmingController('Farming');
        $LayersController = new LayersController('Layers');

        // clean old results
        $results = [];
        $return = [];

        // creating a default empty return
        $empty_return = [
            'rows' => [],
            'footer' => [],
            'total' => 0,
        ];

        if (isset($layer_id) && (int) $layer_id) {
            $layer_result = $LayersController->getLayerData($layer_id);
        } else {
            return $empty_return;
        }

        $tableExists = $UserDbController->getTableNameExist($layer_result['table_name']);
        if (!$tableExists) {
            return $empty_return;
        }

        if ('cropname' == $sort) {
            $sort = 'cropcode';
        }
        $farmSchemas = $GLOBALS['Farming']['schema'];
        if (isset($filterObj) && '' != $filterObj) {
            $is_edited = null;
            if (1 == $filterObj['is_edited']) {
                $is_edited = 'true';
            } elseif (2 == $filterObj['is_edited']) {
                $is_edited = 'false';
            }

            $filter_nr1 = (($GLOBALS['Farming']['lfa_schema_types'][Config::NR1]['id'] == $filterObj['schema']) ? 'true' : null);
            $filter_nr2 = (($GLOBALS['Farming']['lfa_schema_types'][Config::NR2]['id'] == $filterObj['schema']) ? 'true' : null);

            $options = [
                'tablename' => $layer_result['table_name'],
                'return' => [
                    'gid', 'prc_name', 'round((ST_Area(geom) / 10000)::numeric, 4) AS area', 'ekatte', 'ST_ASTEXT(geom)', 'cropcode', 'edited', 'comment', 'sepp', 'zdp', 'pndp', 'natura_sitecode', 'nr1', 'nr2', 'cropname', 'common_cultures', 'crop_short_type', 'vps_type',
                ],
                'where' => [
                    'ekatte' => ['column' => 'ekatte', 'compare' => '=', 'value' => $filterObj['ekate']],
                    'cropcode' => ['column' => 'cropcode', 'compare' => '=', 'value' => $filterObj['culture']],
                    'sepp' => ['column' => 'sepp', 'compare' => '=', 'value' => ($farmSchemas[Config::SEPP]['id'] == $filterObj['schema']) ? 'true' : null],
                    'zdp' => ['column' => 'zdp', 'compare' => '=', 'value' => ($farmSchemas[Config::ZDP]['id'] == $filterObj['schema']) ? 'true' : null],
                    'pndp' => ['column' => 'pndp', 'compare' => '=', 'value' => ($farmSchemas[Config::PNDP]['id'] == $filterObj['schema']) ? 'true' : null],
                    'natura' => ['column' => 'natura_sitecode', 'compare' => 'IS NOT', 'value' => ($farmSchemas[Config::NATURA]['id'] == $filterObj['schema']) ? 'NULL' : null],
                    'nr1' => ['column' => 'nr1', 'compare' => '=', 'value' => $filter_nr1],
                    'nr2' => ['column' => 'nr2', 'compare' => '=', 'value' => $filter_nr2],
                    'prc_name' => ['column' => 'prc_name', 'compare' => 'ILIKE', 'value' => $filterObj['prc_name']],
                    'prc_uin' => ['column' => 'prc_uin', 'compare' => '=', 'value' => $filterObj['prc_uin']],
                    'comment' => ['column' => 'comment', 'compare' => 'ILIKE', 'value' => $filterObj['comment']],
                    'edited' => ['column' => 'edited', 'compare' => '=', 'value' => $is_edited],
                ],
                'offset' => ($page - 1) * $rows,
                'limit' => $rows,
                'sort' => $sort,
                'order' => $order,
            ];
            // todo check what is $request['data']['schema'], which is undefined
            if (($GLOBALS['Farming']['schema'][Config::VPS]['id'] == $request['data']['schema'])) {
                $options['where']['vps_type'] = ['column' => 'vps_type', 'compare' => 'IS NOT', 'value' => 'NULL'];
            }
            if (isset($params['export2xls']) && true == $params['export2xls']) {
                unset($options['limit'], $options['offset']);
            }
            if ('null' == $filterObj['culture']) {
                $options['where']['cropcode'] = ['column' => 'cropcode', 'compare' => 'IS', 'value' => 'NULL'];
            }
            if ('null' == $filterObj['ekate']) {
                $options['where']['ekatte'] = ['column' => "(ekatte = '' OR ekatte IS NULL)", 'compare' => '=', 'value' => 'TRUE'];
            }

            $schema = $filterObj['schema'];
            if ('null' == $schema) {
                $options['where']['sepp'] = ['column' => 'sepp', 'compare' => '=', 'value' => 'false'];
                $options['where']['zdp'] = ['column' => 'zdp', 'compare' => '=', 'value' => 'false'];
                $options['where']['pndp'] = ['column' => 'pndp', 'compare' => '=', 'value' => 'false'];
                $options['where']['nr1'] = ['column' => 'nr1', 'compare' => '=', 'value' => 'false'];
                $options['where']['nr2'] = ['column' => 'nr2', 'compare' => '=', 'value' => 'false'];
                $options['where']['natura'] = ['column' => 'natura_sitecode', 'compare' => 'IS', 'value' => 'NULL'];
                $options['where']['vps_type'] = ['column' => 'vps_type', 'compare' => 'IS', 'value' => 'NULL'];
            }

            $results = $UserDbController->getItemsByParams($options, false);
            $counter = $UserDbController->getItemsByParams($options, true);
        } else {
            $options = [
                'tablename' => $layer_result['table_name'],
                'offset' => ($page - 1) * $rows,
                'limit' => $rows,
                'sort' => $sort,
                'order' => $order,
                'return' => [
                    'gid', 'prc_name', 'round((ST_Area(geom) / 10000)::numeric, 4) AS area', 'ekatte', 'ST_ASTEXT(geom)', 'cropcode', 'edited', 'comment', 'sepp', 'zdp', 'pndp', 'natura_sitecode', 'nr1', 'nr2', 'cropname', 'common_cultures', 'crop_short_type', 'vps_type',
                ],
            ];
            if (isset($params['export2document']) && true == $params['export2document']) {
                unset($options['limit'], $options['offset']);
            }

            $results = $UserDbController->getItemsByParams($options, false, false);
            $counter = $UserDbController->getItemsByParams($options, true);
        }

        unset($options['limit'], $options['offset'], $options['sort'], $options['order']);

        $_SESSION['filtered_plots'] = [];
        $options['return'] = ['sum(round((ST_Area(geom) / 10000)::numeric, 4))'];
        $totalResults = $UserDbController->getItemsByParams($options, false);

        // needed for copying layers
        $options['return'] = ['gid'];
        $gidsOfFilteredPlots = $UserDbController->getItemsByParams($options, false);

        foreach ($gidsOfFilteredPlots as $gid) {
            $_SESSION['filtered_plots'][] = $gid['gid'];
        }

        $totalAreaPerPage = 0;
        $resultsCount = count($results);
        if (0 != $resultsCount) {
            for ($i = 0; $i < $resultsCount; $i++) {
                $schemasArr = [];
                $naturaSitecode = $results[$i]['natura_sitecode'];

                $schemasArr[] = (($results[$i]['sepp']) ? $farmSchemas[Config::SEPP]['name'] : null);
                $schemasArr[] = (($results[$i]['zdp']) ? $farmSchemas[Config::ZDP]['name'] : null);
                $schemasArr[] = (($results[$i]['pndp']) ? $farmSchemas[Config::PNDP]['name'] : null);
                $schemasArr[] = ($naturaSitecode ? 'Н2000'
                    . ' - ' . $GLOBALS['Farming']['natura_zones'][$naturaSitecode]['name'] . ' (' . $naturaSitecode . ')' : null);
                $schemasArr[] = (($results[$i]['nr1']) ? $GLOBALS['Farming']['lfa_schema_types'][Config::NR1]['name'] : null);
                $schemasArr[] = (($results[$i]['nr2']) ? $GLOBALS['Farming']['lfa_schema_types'][Config::NR2]['name'] : null);
                $schemasArr[] = (($results[$i]['vps_type']) ? ('ВПС - ' . $GLOBALS['Farming']['vps_schema_types'][$results[$i]['vps_type']]['name']) : null);
                foreach ($schemasArr as $key => $schema) {
                    if (empty($schema)) {
                        unset($schemasArr[$key]);
                    }
                }
                $results[$i]['schema'] = implode(',', $schemasArr);

                $results[$i]['is_active_culture'] = $GLOBALS['Farming']['crops'][$results[$i]['cropcode']]['is_active'];

                if ('' == $results[$i]['ekatte'] || null == $results[$i]['ekatte']) {
                    $results[$i]['ekatte'] = '-';
                }

                $results[$i]['area'] = $results[$i]['area'];
                $results[$i]['edited'] = ($results[$i]['edited']) ? 'Да' : 'Не';
                $results[$i]['common_cultures'] = ($results[$i]['common_cultures']) ? 'Да' : 'Не';
                $totalAreaPerPage += $results[$i]['area'];
            }
            $return['rows'] = $results;
        } else {
            $return['rows'] = false;
        }
        $return['rows'] = $results;
        $return['footer'][0]['cropname'] = '<b>Общо за страницата:<b/>';
        $return['footer'][0]['area'] = '<b>' . $totalAreaPerPage . '<b/>';
        $return['footer'][1]['cropname'] = '<b>Общо:<b/>';
        $return['footer'][1]['area'] = '<b>' . $totalResults[0]['sum'] . '<b/>';
        $return['total'] = $counter[0]['count'];

        // if the export to document flag is set
        if (isset($params['export2document']) && true == $params['export2document']) {
            $printData = [];
            $rowsCount = count($return['rows']);
            for ($i = 0; $i < $rowsCount; $i++) {
                $naturaSitecodeXls = $return['rows'][$i]['natura_sitecode'];

                $printData[$i] = [
                    'prc_name' => $return['rows'][$i]['prc_name'],
                    'ekatte' => $return['rows'][$i]['ekatte'],
                    'cropname' => $return['rows'][$i]['cropname'],
                    'schema' => $return['rows'][$i]['schema'],
                    'area' => $return['rows'][$i]['area'],
                    'edited' => $return['rows'][$i]['edited'],
                    'common_cultures' => $return['rows'][$i]['common_cultures'],
                    'comment' => $return['rows'][$i]['comment'],
                ];
            }
            $column_headers = [
                'Име',
                'ЕКАТТЕ',
                'Култура',
                'Схеми/мерки',
                'Площ(ха)',
                'За редактиране',
                'Междинна култура',
                'Коментар',
            ];

            $return['column_headers'] = $column_headers;
            $return['printData'] = $printData;

            /*if(isset($params['document_type']) && $params['document_type'] == 'xls')
            {
                $this->exportGridToXLS($printData, $column_headers);
            }elseif (isset($params['document_type']) && $params['document_type'] == 'pdf')
            {
                return $this->exportGridToPDF($params, $page, $rows, $sort, $order);
            }*/
        }

        return $return;
    }

    /**
     * exports plots data in xls format.
     *
     * @api-method exportGridToXLS
     *
     * @param array $rpcParams {
     *                         #item integer layer_id
     *                         #item string layer_name
     *                         #item array filterObj {
     *                         #item string prc_name
     *                         #item integer culture
     *                         #item string comment
     *                         #item boolean edited
     *                         #item boolean sepp
     *                         }
     *                         }
     * @param string $rows
     * @param string $sort
     * @param string $order
     *
     * @return array {
     *               #item string exl_report_file
     *               }
     */
    public function exportGridToXLS($rpcParams, $rows = '50', $sort = 'id', $order = 'asc')
    {
        $rpcParams['export2document'] = true;
        $type = 'atr_info';

        $type = str_replace([' ', '.'], '_', $type);

        if (!is_dir(PUBLIC_UPLOAD_RELATIVE_PATH . '/history/' . $this->User->GroupID)) {
            mkdir(PUBLIC_UPLOAD_RELATIVE_PATH . '/history/' . $this->User->GroupID);
        }

        $path = PUBLIC_UPLOAD_HISTORY . '/' . $this->User->GroupID . '/' . $type . '.xlsx';
        $relativePath = PUBLIC_UPLOAD_RELATIVE_PATH . '/history/' . $this->User->GroupID . '/' . $type . '.xlsx';

        $return = [];

        $results = $this->readForIsakSubsidiesGrid($rpcParams, '1', $rows, $sort, $order);
        $result = $results['printData'];

        unset($result[0]['attributes']);

        $column_headers = ($results['column_headers']) ? $results['column_headers'] : [];

        $export2Xls = new Export2XlsClass();
        $export2Xls->exportUrlPath($path, $result, $column_headers);
        $return['exl_report_file'] = $relativePath;

        return $return;
    }

    /**
     * exports plots data in pdf format.
     *
     * @api-method exportGridToPDF
     *
     * @param array $rpcParams {
     *                         #item integer layer_id
     *                         #item string layer_name
     *                         #item array filterObj {
     *                         #item string prc_name
     *                         #item integer culture
     *                         #item string comment
     *                         #item boolean edited
     *                         #item boolean sepp
     *                         }
     *                         }
     * @param string $rows
     * @param string $sort
     * @param string $order
     *
     * @return array {
     *               #item string pdf_report_file
     *               }
     */
    public function exportGridToPDF($rpcParams, $rows = '50', $sort = 'prc_uin', $order = 'asc')
    {
        $LayersController = new LayersController('Layers');

        $rpcParams['export2document'] = true;
        $type = 'atr_info';

        $type = str_replace([' ', '.'], '_', $type);

        if (!is_dir(PUBLIC_UPLOAD_RELATIVE_PATH . '/history/' . $this->User->GroupID)) {
            mkdir(PUBLIC_UPLOAD_RELATIVE_PATH . '/history/' . $this->User->GroupID);
        }

        $path = PUBLIC_UPLOAD_HISTORY . '/' . $this->User->GroupID . '/' . $type . '.pdf';
        $relativePath = PUBLIC_UPLOAD_RELATIVE_PATH . '/history/' . $this->User->GroupID . '/' . $type . '.pdf';
        $rpcParams['export2document'] = false;
        $results = $this->readForIsakSubsidiesGrid($rpcParams, '1', $rows, $sort, $order);

        $ltext = $LayersController->StringHelper->loadTemplate($GLOBALS['Templates'][30]['template'], ['pdf_data' => $results['rows']]);
        $ltext = '<page style="font-family: freeserif"><br />' . $ltext . '</page>';

        $printPdf = new PrintPdf();
        $printPdf->generateFromHtml($ltext, $path, [], true);

        $return['pdf_report_file'] = $relativePath;

        return $return;
    }

    /**
     * adds landshaft elements.
     *
     * @api-method saveMultiEdit
     *
     * @param string $tableName
     * @param string $ekatte
     * @param bool $as_intermediate_crop
     * @param string $culture
     * @param array $plotIds {
     *                       #item integer 0
     *                       }
     * @param array $filters {
     *                       #item string comment
     *                       #item boolean common_culture
     *                       #item string culture
     *                       #item string ekate
     *                       #item string is_edited
     *                       #item string schema
     *                       #item string prc_name
     *                       #item integer farming
     *                       #item integer year
     *                       }
     *
     * @return array
     */
    public function saveMultiEdit($filters, $tableName, $ekatte, $as_intermediate_crop, $culture, $plotIds)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbForIsakController = new UserDbForIsakController($this->User->Database);
        $UsersController = new UsersController('Users');

        if ('null' === $culture) {
            $culture = ' ';
        }

        $tableExists = $UserDbController->getTableNameExist($tableName);

        if (!$tableExists) {
            return [];
        }

        $filters = (object) $filters;
        $sepp_filter = ((Config::SEPP == $filters->schema) ? 'true' : 'false');
        $zdp_filter = ((Config::ZDP == $filters->schema) ? 'true' : 'false');
        $pndp_filter = ((Config::PNDP == $filters->schema) ? 'true' : 'false');
        $natura_filter = ((Config::NATURA == $filters->schema) ? 'true' : 'false');
        $filter_nr1 = ((Config::NR1 == $filters->schema) ? 'true' : 'false');
        $filter_nr2 = ((Config::NR2 == $filters->schema) ? 'true' : 'false');
        $filter_vps = ((Config::VPS == $filters->schema) ? 'true' : 'false');

        $is_edited = null;
        if (1 == $filters->is_edited) {
            $is_edited = 'true';
        } elseif (2 == $filters->is_edited) {
            $is_edited = 'false';
        }

        $crop = $GLOBALS['Farming']['crops'][$culture];

        // set crop_short_type and green_area_factor
        $crop_short_type = '';
        $green_area_factor = 1;

        if ($crop['azot_fixed_crop']) {
            $crop_short_type .= 'АФК, ';
            $green_area_factor = $GLOBALS['Farming']['crops_short_type']['azot_fixed_crop']['factor_enp'];
        }
        if ($crop['is_tree_short_rotation']) {
            $crop_short_type .= 'ДВКЦР, ';
            $green_area_factor = $GLOBALS['Farming']['crops_short_type']['is_tree_short_rotation']['factor_enp'];
        }
        // Угар
        if ('190000' == $culture) {
            $crop_short_type .= 'УГАР, ';
            $green_area_factor = $GLOBALS['Farming']['crops_short_type']['190000']['factor_enp'];
        }

        if (strlen($crop_short_type)) {
            $crop_short_type = substr($crop_short_type, 0, -2);
        }

        if (empty($plotIds)) {
            $plotIds[] = 0;
        }

        if ('getAllPlotIds' == $plotIds) {
            $optionsAllIds = [
                'tablename' => $tableName,
                'return' => [
                    'gid',
                ],
                'where' => [
                    'ekatte' => ['column' => 'ekatte', 'compare' => '=', 'value' => $filters->ekate],
                    'cropcode' => ['column' => 'cropcode', 'compare' => '=', 'value' => $filters->culture],
                    'prc_name' => ['column' => 'prc_name', 'compare' => 'ILIKE', 'value' => $filters->prc_name],
                    'prc_uin' => ['column' => 'prc_uin', 'compare' => '=', 'value' => $filters->prc_uin],
                    'comment' => ['column' => 'comment', 'compare' => 'ILIKE', 'value' => $filters->comment],
                    'edited' => ['column' => 'edited', 'compare' => '=', 'value' => $is_edited],
                ],
            ];

            if ('true' == $filter_vps) {
                $optionsAllIds['where']['vps_type'] = ['column' => 'vps_type', 'compare' => 'IS NOT', 'value' => 'NULL'];
            }
            if ('true' == $natura_filter) {
                $optionsAllIds['where']['natura'] = ['column' => 'natura_sitecode', 'compare' => 'IS NOT', 'value' => 'NULL'];
            }
            if ('true' == $sepp_filter) {
                $optionsAllIds['where']['sepp'] = ['column' => 'sepp', 'compare' => '=', 'value' => $sepp_filter];
            }
            if ('true' == $zdp_filter) {
                $optionsAllIds['where']['zdp'] = ['column' => 'zdp', 'compare' => '=', 'value' => $zdp_filter];
            }
            if ('true' == $pndp_filter) {
                $optionsAllIds['where']['pndp'] = ['column' => 'pndp', 'compare' => '=', 'value' => $pndp_filter];
            }
            if ('true' == $filter_nr1) {
                $optionsAllIds['where']['nr1'] = ['column' => 'nr1', 'compare' => '=', 'value' => $filter_nr1];
            }
            if ('true' == $filter_nr2) {
                $optionsAllIds['where']['nr2'] = ['column' => 'nr2', 'compare' => '=', 'value' => $filter_nr2];
            }

            if (null == $filters->culture) {
                unset($optionsAllIds['where']['cropcode']);
            }
            if (null == $filters->ekate) {
                unset($optionsAllIds['where']['ekatte']);
            }
            if (null == $filters->prc_name) {
                unset($optionsAllIds['where']['prc_name']);
            }
            if (null == $filters->prc_uin) {
                unset($optionsAllIds['where']['prc_uin']);
            }
            if (null == $filters->comment) {
                unset($optionsAllIds['where']['comment']);
            }
            if (null == $filters->is_edited) {
                unset($optionsAllIds['where']['edited']);
            }

            $resultAllIds = $UserDbController->getItemsByParams($optionsAllIds, false, false);
            $resultAllIdsCount = count($resultAllIds);
            $plotIds = [];

            for ($i = 0; $i < $resultAllIdsCount; $i++) {
                $plotIds[] = $resultAllIds[$i]['gid'];
            }
        }

        $options = [
            'tablename' => $tableName,
            'id_string' => implode(', ', $plotIds),

            'update' => [
                'cropcode' => $culture,
                'ekatte' => 'null' == $ekatte ? ' ' : $ekatte,
                'crop_type' => $crop['crop_type'] ?? ' ',
                'crop_genus' => $crop['crop_genus'] ?? ' ',
                'azot_fixed_crop' => (($crop['azot_fixed_crop']) ? 'true' : 'false'),
                'is_intermediate_crop' => (($crop['is_intermediate_crop']) ? 'true' : 'false'),
                'is_intermediate_weat_crop' => (($crop['is_intermediate_weat_crop']) ? 'true' : 'false'),
                'is_tree_short_rotation' => (($crop['is_tree_short_rotation']) ? 'true' : 'false'),
                'no_pndn' => (($crop['no_pndn']) ? 'true' : 'false'),
                'cropname' => $crop['crop_name'] ?? null,
                'crop_short_type' => strlen($crop_short_type) ? $crop_short_type : ' ',
                'green_area_factor' => $green_area_factor,
                'common_cultures' => 'false',
                'weat_crops' => ' ',
                'non_weat_crops' => ' ',
            ],
        ];

        foreach ($options['update'] as $key => $option) {
            if (null == $option && 'cropname' != $key) {
                unset($options['update'][$key]);
            }
        }

        if (null == $culture) {
            unset($options['update']['cropname']);
        }

        $UserDbForIsakController->ForIsakMultiEdit($options);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options, ['affected_plots' => $plotIds], 'Multi edit performed');
    }

    /**
     * updates edited plot.
     *
     * @api-method editForIsakPlot
     *
     * @param array $filters {
     *                       #item string comment
     *                       #item string culture
     *                       #item string ekate
     *                       #item string is_edited
     *                       #item string prc_name
     *                       #item string schema
     *                       #item integer edit_id
     *                       #item integer farming
     *                       #item integer year
     *                       }
     * @param array $new_vals {
     *                        #item string area
     *                        #item string as_intermediate_crop
     *                        #item string culture
     *                        #item string ekatte
     *                        #item string name
     *                        #item boolean nr1
     *                        #item boolean nr2
     *                        #item boolean pndp
     *                        #item boolean sepp
     *                        #item boolean zdp
     *                        }
     * @param string $tableName
     */
    public function editForIsakPlot($filters, $new_vals, $tableName)
    {
        $filters = (object) $filters;
        $new_vals = (object) $new_vals;

        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new FarmingController('Users');
        $LayersController = new LayersController('Layers');

        $tableExists = $UserDbController->getTableNameExist($tableName);

        if (!$tableExists) {
            return [];
        }

        $crop = $GLOBALS['Farming']['crops'][$new_vals->culture];

        $green_area_factor = 1;
        $crop_short_type = '';

        $options = [
            'tablename' => $tableName,
            'return' => [
                'gid', 'prc_name', 'round((ST_Area(geom) / 10000)::numeric, 4) AS area', 'ekatte',  'cropcode', 'edited', 'comment', 'sepp', 'zdp', 'pndp', 'natura_sitecode', 'nr1', 'nr2', 'cropname', 'common_cultures', 'crop_short_type', 'vps_type',
            ],
            'where' => [
                'gid' => ['column' => 'gid', 'compare' => '=', 'value' => $filters->edit_id],
            ],
        ];
        $results = $UserDbController->getItemsByParams($options, false);

        if (1 == $new_vals->as_intermediate_crop) {
            $green_area_factor = $GLOBALS['Farming']['crops_short_type']['is_intermediate_crop']['factor_enp'];
        } else {
            if ($crop['azot_fixed_crop']) {
                $crop_short_type .= 'АФК, ';
                $green_area_factor = $GLOBALS['Farming']['crops_short_type']['azot_fixed_crop']['factor_enp'];
            }
            if ($crop['is_tree_short_rotation']) {
                $crop_short_type .= 'ДВКЦР, ';
                $green_area_factor = $GLOBALS['Farming']['crops_short_type']['is_tree_short_rotation']['factor_enp'];
            }
            // Угар
            if ('190000' == $new_vals->culture) {
                $crop_short_type .= 'УГАР, ';
                $green_area_factor = $GLOBALS['Farming']['crops_short_type']['190000']['factor_enp'];
            }
        }

        if (strlen($crop_short_type)) {
            $crop_short_type = substr($crop_short_type, 0, -2);
        }

        $options['tablename'] = $tableName;
        $options['where']['gid'] = $filters->edit_id;
        $options['mainData'] = [
            'prc_name' => $new_vals->name,
            'cropcode' => $new_vals->culture,
            'ekatte' => $new_vals->ekatte,
            'crop_type' => $crop['crop_type'],
            'crop_genus' => $crop['crop_genus'],
            'azot_fixed_crop' => (($crop['azot_fixed_crop']) ? 'true' : 'false'),
            'is_intermediate_crop' => (($crop['is_intermediate_crop']) ? 'true' : 'false'),
            'is_intermediate_weat_crop' => (($crop['is_intermediate_weat_crop']) ? 'true' : 'false'),
            'is_tree_short_rotation' => (($crop['is_tree_short_rotation']) ? 'true' : 'false'),
            'common_cultures' => ((1 == $new_vals->as_intermediate_crop) ? 'true' : 'false'),
            'no_pndn' => (($crop['no_pndn']) ? true : false),
            'sepp' => (($new_vals->sepp) ? true : false),
            'zdp' => (($new_vals->zdp) ? true : false),
            'pndp' => (($new_vals->pndp) ? true : false),
            'nr1' => (($new_vals->nr1) ? true : false),
            'nr2' => (($new_vals->nr2) ? true : false),
            'green_area_factor' => $green_area_factor,
            'cropname' => $crop['crop_name'],
        ];

        if (strlen($crop_short_type)) {
            $options['mainData']['crop_short_type'] = $crop_short_type;
        }

        if (1 != $new_vals->as_intermediate_crop) {
            $options['mainData']['weat_crops'] = '';
            $options['mainData']['non_weat_crops'] = '';
        }

        $UserDbController->editItem($options);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options, ['old_values' => $results], 'Editing plot values');
    }
}
