<?php

namespace TF\Engine\APIClasses\SubsidiesWizard;

use Prado\Web\Services\TRpcApiProvider;
use TF\Application\Common\Config;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

/**
 * @rpc-module SubsidiesWizard
 *
 * @rpc-service-id for-isak-layers-tree
 */
class LayersTree extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readLayersTree']],
        ];
    }

    /**
     * Read Layers Tree.
     *
     * @api-method read
     *
     * @return array {
     *               #item integer total
     *               #item array {
     *               #item array children{
     *               #item array {
     *               #item array attributes {
     *               #item string layer_name
     *               #item string farming_name
     *               #item string extent
     *               #item integer farming_id
     *               }
     *               #item string iconCls
     *               #item integer id
     *               #item string text
     *               #item integer year_id
     *               }
     *               }
     *               #item integer id
     *               #item string state
     *               #item string text
     *               }
     *               }
     */
    public function readLayersTree()
    {
        $UserDbController = new UserDbController($this->User->Database);
        $FarmingController = new FarmingController('Farming');
        $LayersController = new LayersController('Layers');

        $farmingYear2015 = 6;

        // farming year for 2015
        $years = [$farmingYear2015];

        // current farming year
        $currentFarmingYearID = (int) $FarmingController->getCurrentFarmingYearID();

        // count of farming years to show in combobox
        $yearsCount = ($currentFarmingYearID - $farmingYear2015) + 1;

        while ($yearsCount > 0) {
            // next year
            $nextFarmingYearID = $farmingYear2015 + $yearsCount;
            $years[] = $nextFarmingYearID;

            $yearsCount--;
        }

        // get all farmings
        $options = [
            'return' => [
                'f.id as farming_id', 'f.name as farming_name', 't.*',
            ],
            'where' => [
                'group_id_layer' => ['column' => 'group_id', 'compare' => '=', 'prefix' => 'f', 'value' => $this->User->GroupID],
                'group_id_farming' => ['column' => 'group_id', 'compare' => '=', 'prefix' => 't', 'value' => $this->User->GroupID],
                'layer_type' => ['column' => 'layer_type', 'compare' => '=', 'prefix' => 't', 'value' => Config::LAYER_TYPE_FOR_ISAK],
                'is_exist' => ['column' => 'is_exist', 'compare' => '=', 'prefix' => 't', 'value' => 'TRUE'],
            ],
            'sort' => 'farming asc, year asc, layer_type',
            'order' => 'desc',
        ];
        $results = $LayersController->getLayers($options, false, false);
        $resultsCount = count($results);
        for ($i = 0; $i < $resultsCount; $i++) {
            if ($years && !in_array($results[$i]['year'], $years)) {
                continue;
            }

            $layers[$results[$i]['farming_id']]['id'] = $results[$i]['farming_id'];
            $layers[$results[$i]['farming_id']]['text'] = $results[$i]['farming_name'];
            $layers[$results[$i]['farming_id']]['state'] = 'open';
            $layers[$results[$i]['farming_id']]['children'][] = [
                'id' => $results[$i]['id'],
                'year_id' => $results[$i]['year'],
                'text' => $GLOBALS['Farming']['years'][$results[$i]['year']]['title'],
                'iconCls' => 'icon-tree-calendar',
                'attributes' => [
                    'layer_name' => $results[$i]['table_name'],
                    'extent' => str_replace(' ', ',', $results[$i]['extent']),
                    'farming_name' => $results[$i]['farming_name'],
                    'farming_id' => $results[$i]['farming_id'],
                ],
            ];
        }
        $layers = array_values($layers);

        return $layers;
    }
}
