<?php

namespace TF\Engine\APIClasses\SubsidiesWizard;

use Prado\Web\Services\TRpcApiProvider;
use TF\Application\Common\Config;
use TF\Engine\Kernel\ExportToExcelClass;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Kernel\PrintPdf;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbForIsak\UserDbForIsakController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * @rpc-module SubsidiesWizard
 *
 * @rpc-service-id for-isak-report-pzp-grid
 *
 * @property UserDbController $UserDbController
 * @property LayersController $LayersController
 * @property UserDbForIsakController $UserDbForIsakController
 * @property UsersController $UsersController
 * @property FarmingController $FarmingController
 */
class ForIsakPZPReportGrid extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readForIsakPZPReportGrid'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'saveForIsakData' => ['method' => [$this, 'saveForIsakData']],
            'saveForRequest' => ['method' => [$this, 'saveForRequest']],
            'getReportPZP' => ['method' => [$this, 'getReportPZP']],
            'initForIsakDiffPZP' => ['method' => [$this, 'initForIsakDiffPZP']],
            'exportGridToXLS' => ['method' => [$this, 'exportGridToXLS']],
            'exportGridToPDF' => ['method' => [$this, 'exportGridToPDF']],
        ];
    }

    /**
     * Reads plots compatiable with permanently green areas.
     *
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item string layer_name
     *                         #item boolean is_schema
     *                         #item array filterObj {
     *                         #item string comment
     *                         #item string culture
     *                         #item string edited
     *                         #item string ekate
     *                         #item string prc_name
     *                         #item string schema
     *                         }
     *                         }
     * @param int $page pagination parameter
     * @param int $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array {
     *               #item integer total
     *               #item array rows {
     *               #item integer gid
     *               #item string prc_name
     *               #item boolean edited
     *               #item string comment
     *               #item string ekatte,
     *               #item string cropname
     *               #item string natura_sitecode
     *               #item boolean sepp
     *               #item boolean zdp
     *               #item boolean pndp
     *               #item boolean nr1
     *               #item boolean nr2
     *               #item boolean vps_type
     *               #item integer cropcode
     *               #item integer area
     *               #item integer crop_type
     *               #item boolean in_area_pzp
     *               #item string imotcode
     *               #item string sub_areas
     *               }
     *               #item array footer {
     *               #item string prc_name,
     *               #item string area,
     *               #item string in_area_pzp,
     *               }
     *               }
     */
    public function readForIsakPZPReportGrid(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $return = [
            'total' => 0,
            'rows' => [],
            'footer' => [],
        ];

        // init all needed controllers

        $UserDbForIsakController = new UserDbForIsakController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        $LayersController = new LayersController('Layers');
        $FarmingController = new FarmingController('Farming');
        $UserDbForIsakController = new UserDbForIsakController($this->User->Database);

        $layer_name = $rpcParams['layer_name'];
        $is_schema = $rpcParams['is_schema'];
        $request = $rpcParams['filterObj'];
        // check if table exists
        $tableExist = $UserDbController->getTableNameExist($layer_name);
        if (!$tableExist) {
            return $return;
        }

        $options = [
            'tablename' => $layer_name,
            'return' => ['ST_Extent(geom) as extent'],
        ];

        $extent_result = $UserDbController->getItemsByParams($options);

        // get FOR ISAK extent and transform it
        $maxextent = $extent_result[0]['extent'];
        $maxextent = str_replace('BOX(', '', $maxextent);
        $maxextent = str_replace(')', '', $maxextent);
        $maxextent = str_replace(' ', ',', $maxextent);

        $forIsakLayerData = $LayersController->getLayers([
            'return' => ['t.id'],
            'where' => [
                'table_name' => ['column' => 't.table_name', 'compare' => '=', 'value' => $layer_name],
            ],
        ]);
        $layerId = $forIsakLayerData[0]['id'];
        $pzpView = 'pzp_for_isak_' . $layerId;

        if (!$UserDbController->getViewNameExists($pzpView)) {
            $UserDbController->createPZPReportView($layerId);
        }

        $options = [
            'tablename' => $layer_name,
            'custom_counter' => 'COUNT(DISTINCT(gid)), SUM(area) as total_area, SUM(in_area_pzp) as total_in_area_pzp',
            'return' => [
                '*',
            ],
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
            'extent' => $maxextent,
            'pzpView' => $pzpView,
            'isSchema' => $is_schema,
            'searchInCropType' => true,
        ];

        if (strlen($request['prc_name'])) {
            $options['where']['prc_name'] = ['column' => 'prc_name', 'compare' => 'ILIKE', 'prefix' => 'pzp_report', 'value' => $request['prc_name']];
        }
        if (strlen($request['culture'])) {
            $options['where']['cropcode'] = ['column' => 'cropcode', 'compare' => '=', 'prefix' => 'pzp_report', 'value' => $request['culture']];
        }
        if (strlen($request['comment'])) {
            $options['where']['comment'] = ['column' => 'comment', 'compare' => 'ILIKE', 'prefix' => 'pzp_report', 'value' => $request['comment']];
        }
        if (strlen($request['edited']) && ('1' == $request['edited'] || '0' == $request['edited'])) {
            $options['where']['edited'] = ['column' => 'edited', 'compare' => '=', 'prefix' => 'pzp_report', 'value' => $request['edited']];
        }

        $farmSchemas = $GLOBALS['Farming']['schema'];

        if (strlen($request['schema']) && 'null' != $request['schema']) {
            if ($farmSchemas[Config::SEPP]['id'] == $request['schema']) {
                $options['where']['sepp'] = ['column' => 'sepp', 'compare' => '=', 'prefix' => 'pzp_report', 'value' => 'true'];
            }

            if ($farmSchemas[Config::ZDP]['id'] == $request['schema']) {
                $options['where']['zdp'] = ['column' => 'zdp', 'compare' => '=', 'prefix' => 'pzp_report', 'value' => 'true'];
            }

            if ($farmSchemas[Config::PNDP]['id'] == $request['schema']) {
                $options['where']['pndp'] = ['column' => 'pndp', 'compare' => '=', 'prefix' => 'pzp_report', 'value' => 'true'];
            }

            if ($farmSchemas[Config::NATURA]['id'] == $request['schema']) {
                $options['where']['natura'] = ['column' => 'natura_sitecode', 'compare' => 'IS NOT', 'prefix' => 'pzp_report', 'value' => 'NULL'];
            }

            if ($GLOBALS['Farming']['lfa_schema_types'][Config::NR1]['id'] == $request['schema']) {
                $options['where']['nr1'] = ['column' => 'nr1', 'compare' => '=', 'prefix' => 'pzp_report', 'value' => 'true'];
            }

            if ($GLOBALS['Farming']['lfa_schema_types'][Config::NR2]['id'] == $request['schema']) {
                $options['where']['nr2'] = ['column' => 'nr2', 'compare' => '=', 'prefix' => 'pzp_report', 'value' => 'true'];
            }

            if ($GLOBALS['Farming']['schema'][Config::VPS]['id'] == $request['schema']) {
                $options['where']['vps_type'] = ['column' => 'vps_type', 'compare' => 'IS NOT', 'prefix' => 'pzp_report', 'value' => 'NUll'];
            }
        }

        if ('null' == $request['ekate']) {
            $options['where']['ekatte'] = ['column' => 'ekatte', 'compare' => 'IS', 'prefix' => 'pzp_report', 'value' => 'NULL'];
        }

        if ((int)$request['ekate'] > 0) {
            $options['where']['ekatte'] = ['column' => 'ekatte', 'compare' => '=', 'prefix' => 'pzp_report', 'value' => $request['ekate']];
        }

        if ($rpcParams['export2document'] && true == $rpcParams['export2document']) {
            unset($options['offset'], $options['limit']);
        }

        $counter = $UserDbForIsakController->getForIsakDataPZP($options, true, false);

        $total_area = $counter[0]['total_area'];
        $total_in_area_pzp = $counter[0]['total_in_area_pzp'];

        if (0 == $counter[0]['count']) {
            return $return;
        }

        $data = $UserDbForIsakController->getForIsakDataPZP($options, false, false);
        $dataCount = count($data);
        $total_area_per_page = 0;
        $total_in_area_pzp_per_page = 0;
        // iterate results
        for ($i = 0; $i < $dataCount; $i++) {
            // add to total
            $total_area_per_page += $data[$i]['area'];
            $total_in_area_pzp_per_page += $data[$i]['in_area_pzp'];

            if ($data[$i]['ekatte']) {
                $data[$i]['ekatte'] = $UsersController->getEkatteName($data[$i]['ekatte']);
            } else {
                $data[$i]['ekatte'] = '-';
            }

            $data[$i]['schema'] = $UsersController->getForIsakSchema($data[$i]);
        }

        // if the export to document flag is set
        if ($rpcParams['export2document'] && true == $rpcParams['export2document']) {
            $printData = [];
            for ($i = 0; $i < $dataCount; $i++) {
                $printData[$i] = [
                    'prc_name' => $data[$i]['prc_name'],
                    'ekatte' => $data[$i]['ekate'],
                    'cropname' => $data[$i]['cropname'],
                    'schema' => $data[$i]['schema'],
                    'area' => $data[$i]['area'],
                    'in_area_pzp' => $data[$i]['in_area_pzp'],
                    'imotcode' => $data[$i]['imotcode'],
                    'sub_areas' => $data[$i]['sub_areas'],
                    'edited' => ($data[$i]['edited']) ? 'Да' : 'Не',
                    'comment' => $data[$i]['comment'],
                ];
            }
            $column_headers = [
                'prc_name' => 'Име на парцел',
                'ekatte' => 'Землище',
                'cropname' => 'Култура',
                'schema' => 'Схеми/мерки',
                'area' => 'Площ на парцела (ха)',
                'in_area_pzp' => 'Площ в на парцела в слой "ПЗП"(ха)',
                'imotcode' => 'Засегнати имоти от слой ПЗП',
                'sub_areas' => 'Засегната площ от имота',
                'edited' => 'За редактиране',
                'comment' => 'Коментар',
            ];

            return [
                'rows' => $printData,
                'column_headers' => $column_headers,
                'footer' => [
                    'prc_name' => 'Общо:',
                    'area' => number_format((float)$total_area, 4, '.', ''),
                    'in_area_pzp' => number_format((float)$total_in_area_pzp, 4, '.', ''),
                ],
            ];
        }

        $return = [
            'total' => $counter[0]['count'],
            'rows' => $data,
            'footer' => [
                [
                    'prc_name' => '<b>Общо за стр.</b>',
                    'area' => '<b>' . number_format((float)$total_area_per_page, 4, '.', '') . '</b>',
                    'in_area_pzp' => '<b>' . number_format((float)$total_in_area_pzp_per_page, 4, '.', '') . '</b>',
                ],
                [
                    'prc_name' => '<b>Общо</b>',
                    'area' => '<b>' . number_format((float)$total_area, 4, '.', '') . '</b>',
                    'in_area_pzp' => '<b>' . number_format((float)$total_in_area_pzp, 4, '.', '') . '</b>',
                ],
            ],
        ];
        $return['rows'] = $data;

        return $return;
    }

    /**
     * updates edited plots.
     *
     * @api-method saveForIsakData
     *
     * @param array $data {
     *                    #item string layer_name
     *                    #item boolean edited
     *                    #item sting comment
     *                    #item integer gid
     *                    }
     */
    public function saveForIsakData($data)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbForIsakController = new UserDbForIsakController($this->User->Database);
        // check if table exists
        $tableExist = $UserDbController->getTableNameExist($data[0]['layer_name']);
        if (!$tableExist) {
            return [];
        }
        $count = count($data);
        for ($i = 0; $i < $count; $i++) {
            $data[$i]['edited'] = $data[$i]['edited'] ? true : false;
            $UserDbForIsakController->updateForIsakComment((object)$data[$i]);
        }
    }

    /**
     * requests plot.
     *
     * @api-method saveForRequest
     *
     * @param array $rows {
     *                    #item string layer_name
     *                    #item string vps_type
     *                    #item float vps_inside_area
     *                    #item string field
     *                    #item string field_value
     *                    #item integer gid
     *                    }
     *
     * @return array {
     *               #item integer id
     *               }
     */
    public function saveForRequest($rows)
    {
        $UserDbForIsakController = new UserDbForIsakController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);
        // check if table exists
        $tableExist = $UserDbController->getTableNameExist($rows[0]['layer_name']);
        if (!$tableExist) {
            return [];
        }

        $count = count($rows);
        for ($i = 0; $i < $count; $i++) {
            $UserDbForIsakController->updateForRequest((object)$rows[$i]);
        }

        return ['id' => $rows[0]['id']];
    }

    /**
     * Reads permanently green areas requirements.
     *
     * @api-method getReportPZP
     *
     * @param string $layer_name
     *
     * @throws MTRpcException -33053 MAP_REQUESTED_LAYER_NOT_FOUND
     *
     * @return array|string
     */
    public function getReportPZP($layer_name)
    {
        $UserDbForIsakController = new UserDbForIsakController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);
        $LayersController = new LayersController('Layers');

        $table_exists = $UserDbController->getTableNameExist($layer_name);
        if (!$table_exists) {
            return ['rows' => [], 'total' => 0];
        }

        // PZP check
        $options = [
            'tablename' => $layer_name,
            'return' => ['ST_Extent(geom) as extent'],
        ];

        $extent_result = $UserDbController->getItemsByParams($options);

        // get FOR ISAK extent and transform it
        $maxextent = $extent_result[0]['extent'];
        $maxextent = str_replace('BOX(', '', $maxextent);
        $maxextent = str_replace(')', '', $maxextent);
        $maxextent = str_replace(' ', ',', $maxextent);

        $forIsakLayerData = $LayersController->getLayers([
            'return' => ['t.id'],
            'where' => [
                'table_name' => ['column' => 't.table_name', 'compare' => '=', 'value' => $layer_name],
            ],
        ]);
        $layerId = $forIsakLayerData[0]['id'];
        $pzpView = 'pzp_for_isak_' . $layerId;

        if (!$UserDbController->getViewNameExists($pzpView)) {
            $UserDbController->createPZPReportView($layerId);
        }

        // get all
        $options = [
            'tablename' => $layer_name,
            'return' => [
                '*',
            ],
            'sort' => 'gid',
            'order' => 'desc',
            'extent' => $maxextent,
            'pzpView' => $pzpView,
            'searchInCropType' => false,
        ];

        $result = $UserDbForIsakController->getForIsakDataPZP($options, false, false);

        if (count($result)) {
            // search in Справка "Парцели в ПЗП"
            $options = [
                'tablename' => $layer_name,
                'return' => [
                    '*',
                ],
                'sort' => 'gid',
                'order' => 'desc',
                'extent' => $maxextent,
                'pzpView' => $pzpView,
                'isSchema' => true,
                'searchInCropType' => true,
            ];

            $result = $UserDbForIsakController->getForIsakDataPZP($options, false, false);

            if (count($result)) {
                $textPZP = 'Условието за неразораване на постоянно затревени площи от слой ПЗП във вашето стопанство <span style="color: #cc1c1c;">не е спазено</span>.';
            } else {
                $textPZP = 'Във вашето стопанство <span style="color: #079b07;">условието за неразораване на площи от слой ПЗП е спазено</span>.';
            }
        } else {
            $textPZP = 'При вашето земеделско стопанство не бе установено пресичане между земеделски парцели от слой "За ИСАК" с поземлени имоти от слой "ПЗП".';
        }

        $data = [
            'textPZP' => $textPZP,
        ];

        $returnData = $LayersController->StringHelper->loadTemplate($GLOBALS['Templates'][26]['template'], $data);

        return $returnData;
    }

    /**
     * Creates pzp layer view table and updates map file.
     *
     * @api-method initForIsakDiffPZP
     *
     * @param string $layer_name
     * @param int $layer_id
     * @param bool $is_schema
     *
     * @return array
     */
    public function initForIsakDiffPZP($layer_name, $layer_id, $is_schema)
    {
        $UserDbForIsakController = new UserDbForIsakController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);
        $LayersController = new LayersController('Layers');

        // check if table exists
        $tableExist = $UserDbController->getTableNameExist($layer_name);
        if (!$tableExist) {
            return [];
        }

        $options = [
            'tablename' => $layer_name,
            'return' => ['ST_Extent(geom) as extent'],
        ];

        $extent_result = $UserDbController->getItemsByParams($options);

        // get FOR ISAK extent and transform it
        $maxextent = $extent_result[0]['extent'];
        $maxextent = str_replace('BOX(', '', $maxextent);
        $maxextent = str_replace(')', '', $maxextent);
        $maxextent = str_replace(' ', ',', $maxextent);

        $forIsakLayerData = $LayersController->getLayers([
            'return' => ['t.id'],
            'where' => [
                'table_name' => ['column' => 't.table_name', 'compare' => '=', 'value' => $layer_name],
            ],
        ]);
        $layerId = $forIsakLayerData[0]['id'];
        $pzpView = 'pzp_for_isak_' . $layerId;

        if (!$UserDbController->getViewNameExists($pzpView)) {
            $UserDbController->createPZPReportView($layerId);
        }

        $options = [
            'tablename' => $layer_name,
            'return' => [
                '*',
            ],
            'extent' => $maxextent,
            'pzpView' => $pzpView,
            'isSchema' => $is_schema,
        ];

        $query = $UserDbForIsakController->getForIsakDataPZPDiffLayer($options, false, true);

        $options = [];
        $options['database'] = $this->User->Database;
        $options['user_id'] = $this->User->GroupID;
        $LayersController->generateMapFile($options);

        $mapFileData = [];

        $color1 = '000000';
        $color2 = 'ff0000';
        $mapFileData['layername'] = 'for_isak_diff_pzp';
        $mapFileData['maxextent'] = str_replace(',', ' ', $maxextent);
        $mapFileData['host'] = DEFAULT_DB_HOST;
        $mapFileData['dbname'] = $this->User->Database;
        $mapFileData['username'] = DEFAULT_DB_USERNAME;
        $mapFileData['password'] = DEFAULT_DB_PASSWORD;
        $mapFileData['port'] = DEFAULT_DB_PORT;
        $mapFileData['query'] = "({$query}) as subquery using unique gid using srid=32635";
        $mapFileData['gid'] = 'gid';
        $mapFileData['transparency'] = '100';
        $mapFileData['classes'][0]['name'] = 'for_isak_diff_pzp';
        $mapFileData['classes'][0]['symbol']['name'] = 'hatch_sym';
        $mapFileData['classes'][0]['symbol']['size'] = 20;
        $mapFileData['classes'][0]['symbol']['angle'] = 45;
        $mapFileData['classes'][0]['symbol']['color'] = hexdec(substr($color2, 0, 2)) . ' ' . hexdec(substr($color2, 2, 2)) . ' ' . hexdec(substr($color2, 4, 2));

        $ltext = $LayersController->StringHelper->loadTemplate($GLOBALS['Templates'][2]['template'], $mapFileData);

        @unlink(WMS_MAP_PATH . $this->User->GroupID . '_layer_for_isak_diff_pzp.map');
        $fp = fopen(WMS_MAP_PATH . $this->User->GroupID . '_layer_for_isak_diff_pzp.map', 'w');
        $res = fwrite($fp, $ltext);
        fclose($fp);
    }

    /**
     * Exports LFA plots data in xls format.
     *
     * @api-method exportGridToPDF
     *
     * @param array $rpcParams
     *                         {
     *                         #item string layer_name
     *                         #item array filterObj {
     *                         #item string prc_name
     *                         #item integer ekate
     *                         #item string comment
     *                         #item boolean edited
     *                         #item boolean lfa
     *                         #item integer nm_lfa
     *                         }
     *                         }
     * @param int $page pagination parameter
     * @param int $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array {
     *               #item string xls_report_file
     *}
     */
    public function exportGridToXLS($rpcParams, $rows = '50', $sort = 'id', $order = 'asc')
    {
        $rpcParams['export2document'] = true;
        $type = 'pzp';
        $rpcParams['is_schema'] = 1;

        $type = str_replace([' ', '.'], '_', $type);

        $results = $this->readForIsakPZPReportGrid($rpcParams, '1', $rows, $sort, $order);

        $time = strtotime(date('Y-m-d H:i:s'));
        $filename = PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID . '/' . $type . $time . '.xlsx';

        if (!file_exists(PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID)) {
            mkdir(PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID, 0777);
        }

        $exportExcelDoc = new ExportToExcelClass();
        $exportExcelDoc->export($results['rows'], $results['column_headers'], [$results['footer']]);
        $exportExcelDoc->saveFile($filename);

        return [
            'exl_report_file' => 'files/uploads/export/' . $this->User->UserID . '/' . $type . $time . '.xlsx',
        ];
    }

    /**
     * Exports LFA plots data in pdf format.
     *
     * @api-method exportGridToPDF
     *
     * @param array $rpcParams
     *                         {
     *                         #item string layer_name
     *                         #item array filterObj {
     *                         #item string prc_name
     *                         #item integer ekate
     *                         #item string comment
     *                         #item boolean edited
     *                         #item boolean lfa
     *                         #item integer nm_lfa
     *                         }
     *                         }
     * @param int $page pagination parameter
     * @param int $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array {
     *               #item string pdf_report_file
     *}
     */
    public function exportGridToPDF($rpcParams, $rows = '50', $sort = 'id', $order = 'asc')
    {
        $LayersController = new LayersController('Layers');

        $rpcParams['export2document'] = true;
        $type = 'pzp';
        $rpcParams['is_schema'] = 1;
        $type = str_replace([' ', '.'], '_', $type);

        if (!is_dir(PUBLIC_UPLOAD_RELATIVE_PATH . '/history/' . $this->User->GroupID)) {
            mkdir(PUBLIC_UPLOAD_RELATIVE_PATH . '/history/' . $this->User->GroupID);
        }

        $path = PUBLIC_UPLOAD_HISTORY . '/' . $this->User->GroupID . '/' . $type . '.pdf';
        $relativePath = PUBLIC_UPLOAD_RELATIVE_PATH . '/history/' . $this->User->GroupID . '/' . $type . '.pdf';

        $results = $this->readForIsakPZPReportGrid($rpcParams, '1', $rows, $sort, $order);
        $ltext = $LayersController->StringHelper->loadTemplate($GLOBALS['Templates'][40]['template'], ['pdf_data' => $results['rows']]);
        $ltext = '<page style="font-family: freeserif"><br />' . $ltext . '</page>';

        $printPdf = new PrintPdf();
        $printPdf->generateFromHtml($ltext, $path, [], true);

        $return['pdf_report_file'] = $relativePath;

        return $return;
    }
}
