<?php

namespace TF\Engine\APIClasses\SubsidiesWizard;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\ExportToExcelClass;
use TF\Engine\Kernel\PrintPdf;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbForIsak\UserDbForIsakController;
use TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController;
use TF\Engine\Plugins\Core\UserDbZPlots\UserDbZPlotsController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * @rpc-module SubsidiesWizard
 *
 * @rpc-service-id for-isak-diff-vps-main-grid
 *
 * @property UserDbController $UserDbController
 * @property UserDbZPlotsController $UserDbZPlotsController
 * @property LayersController $LayersController
 * @property UserDbForIsakController $UserDbForIsakController
 * @property UsersController $UsersController
 * @property FarmingController $FarmingController
 */
class ForIsakDiffVPSMainGrid extends TRpcApiProvider
{
    private $UserDbController = false;
    private $UserDbZPlotsController = false;
    private $LayersController = false;
    private $UserDbForIsakController = false;
    private $UsersController = false;
    private $FarmingController = false;
    private $UserDbPlotsController = false;

    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readForIsakDiffVPSMainGrid'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'exportGridToXLS' => ['method' => [$this, 'exportGridToXLS']],
            'exportGridToPDF' => ['method' => [$this, 'exportGridToPDF']],
        ];
    }

    /**
     * Reads plots from all vps layers together.
     *
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item string layer_name
     *                         #item array filterObj {
     *                         #item string prc_name
     *                         #item string cropname
     *                         #item string ekate
     *                         #item integer vps_type
     *                         }
     *                         }
     * @param int $page pagination parameter
     * @param int $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array {
     *               #item integer total
     *               #item array rows {
     *               #item string prc_name,
     *               #item float area,
     *               #item string comment
     *               #item string cropname
     *               #item boolean edited
     *               #item string gid
     *               #item string vps_type
     *               #item float vps_inside_area,
     *               #item string st_astext
     *               #item string ekate
     *               #item string zeml
     *               }
     *               #item array footer {
     *               #item string cropname,
     *               #item string area,
     *               }
     *               }
     */
    public function readForIsakDiffVPSMainGrid(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        // creating a default empty return
        $empty_return = [
            'rows' => [],
            'footer' => [],
            'total' => 0,
        ];

        if (!$rpcParams['layer_name']) {
            return $empty_return;
        }

        // init all needed controllers
        $UserDbForIsakController = new UserDbForIsakController($this->User->Database);
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        $LayersController = new LayersController('Layers');
        $FarmingController = new FarmingController('Farming');

        $layer_name = $rpcParams['layer_name'];
        $request = $rpcParams['filterObj'];

        $_POST['data'] = [
            'prc_name' => $request['prc_name'],
            'cropcode' => $request['cropcode'],
            'vps_type' => $request['vps_type'],
            'ekatte' => $request['ekatte'],
        ];
        $return = [];

        $tableExists = $UserDbController->getTableNameExist($layer_name);
        if (!$tableExists) {
            return $empty_return;
        }

        if (isset($request) && '' != $request) {
            $is_edited = null;
            if (1 == $request['is_edited']) {
                $is_edited = 'true';
            } elseif (2 == $request['is_edited']) {
                $is_edited = 'false';
            }

            $options = [
                'tablename' => $layer_name,
                'custom_counter' => 'COUNT(DISTINCT(gid)), SUM(area) as total_area',
                'return' => [
                    'gid', 'prc_name', 'round((ST_Area(geom) / 1000)::numeric, 3) AS area', 'ekatte', 'ST_ASTEXT(geom)', 'cropcode', 'edited', 'comment', 'cropname', 'is_intermediate_crop', 'vps_type', 'vps_inside_area',
                ],
                'where' => [
                    'ekatte' => ['column' => 'ekatte', 'compare' => '=', 'value' => $request['ekatte']],
                    'cropcode' => ['column' => 'cropcode', 'compare' => '=', 'value' => $request['cropcode']],
                    'prc_name' => ['column' => 'prc_name', 'compare' => 'ILIKE', 'value' => $request['prc_name']],
                    'vps_type' => ['column' => 'vps_type', 'compare' => '=', 'value' => $request['vps_type']],
                    'vps_type_exists' => ['column' => 'vps_type', 'compare' => 'IS NOT', 'value' => 'NULL'],
                ],
                'offset' => ($page - 1) * $rows,
                'limit' => $rows,
                'sort' => $sort,
                'order' => $order,
            ];
            if (strstr($options['sort'], 'for_isak')) {
                $options['sort'] = explode('.', $options['sort'])[1];
            }
            if ('zeml' == $options['sort']) {
                $options['sort'] = 'ekatte';
            }

            if ($rpcParams['export2document'] && true == $rpcParams['export2document']) {
                unset($options['offset'], $options['limit']);
            }

            $results = $UserDbForIsakController->getForIsakDiffVPSReportData($options, false, false);

            $counter = $UserDbForIsakController->getForIsakDiffVPSReportData($options, true, false);

            $total_area = $counter[0]['total_area'];
        } else {
            $options = [
                'tablename' => $layer_name . ' as for_isak',
                'custom_counter' => 'COUNT(DISTINCT(gid)), SUM(area) as total_area',
                'offset' => ($page - 1) * $rows,
                'limit' => $rows,
                'sort' => $sort,
                'order' => $order,
                'return' => [
                    'gid', 'prc_name', 'round((ST_Area(geom) / 1000)::numeric, 3) AS area', 'ekatte', 'ST_ASTEXT(geom)', 'cropcode', 'edited', 'comment', 'cropname', 'vps_type', 'vps_inside_area',
                ],
                'where' => [
                    'ekatte' => ['column' => 'ekatte', 'compare' => '=', 'value' => $request['ekatte']],
                    'cropcode' => ['column' => 'cropcode', 'compare' => '=', 'value' => $request['cropcode']],
                    'prc_name' => ['column' => 'prc_name', 'compare' => 'ILIKE', 'value' => $request['prc_name']],
                    'vps_type' => ['column' => 'vps_type', 'compare' => '=', 'value' => $request['vps_type']],
                    'vps_type_exists' => ['column' => 'vps_type', 'compare' => 'IS NOT', 'value' => 'NULL'],
                ],
            ];

            if ($rpcParams['export2document'] && true == $rpcParams['export2document']) {
                unset($options['offset'], $options['limit']);
            }

            $results = $UserDbForIsakController->getForIsakDiffVPSReportData($options, false, false);

            $counter = $UserDbForIsakController->getForIsakDiffVPSReportData($options, true, false);

            $total_area = $counter[0]['total_area'];
        }

        unset($options['limit'], $options['offset'], $options['sort'], $options['order']);

        $_SESSION['filtered_plots'] = [];
        $options['where']['vps_type_exists'] = ['column' => 'vps_inside_area', 'compare' => '!=', 'value' => 0];
        $options['return'] = ['sum(round((ST_Area(geom) / 1000)::numeric, 3))'];

        $totalResults = $UserDbController->getItemsByParams($options, false);
        // needed for copying layers
        $options['return'] = ['gid'];
        $gidsOfFilteredPlots = $UserDbController->getItemsByParams($options, false);
        foreach ($gidsOfFilteredPlots as $gid) {
            $_SESSION['filtered_plots'][] = $gid['gid'];
        }

        $totalAreaPerPage = 0;
        $resultsCount = count($results);
        if (0 != $resultsCount) {
            $vps_results = [];
            $printData = [];
            for ($i = 0; $i < $resultsCount; $i++) {
                $vps_results[$i] = [];
                $vps_results[$i]['gid'] = $results[$i]['gid'];
                $vps_results[$i]['prc_name'] = $results[$i]['prc_name'];
                $vps_results[$i]['st_astext'] = $results[$i]['st_astext'];

                if (null == $results[$i]['ekatte'] || empty($results[$i]['ekatte'])) {
                    $vps_results[$i]['zeml'] = $UsersController->getEkatteName($results[$i]['ekatte']);
                } else {
                    $vps_results[$i]['zeml'] = $UsersController->getEkatteName($results[$i]['ekatte']) . '(' . $results[$i]['ekatte'] . ')';
                }

                $vps_results[$i]['vps_type'] = $GLOBALS['Farming']['vps_schema_types'][$results[$i]['vps_type']]['name'];
                $vps_results[$i]['area'] = $results[$i]['area'];
                $vps_results[$i]['cropname'] = $results[$i]['cropname'];
                $vps_results[$i]['comment'] = $results[$i]['comment'];
                $vps_results[$i]['edited'] = $results[$i]['edited'];
                $vps_results[$i]['vps_inside_area'] = $results[$i]['vps_inside_area'];
                $totalAreaPerPage += $vps_results[$i]['area'];

                // if the export to document flag is set
                if ($rpcParams['export2document'] && true == $rpcParams['export2document']) {
                    $printData[$i] = [
                        'prc_name' => $vps_results[$i]['prc_name'],
                        'zeml' => $vps_results[$i]['zeml'],
                        'cropname' => $vps_results[$i]['cropname'],
                        'vps_type' => $vps_results[$i]['vps_type'],
                        'area' => $vps_results[$i]['area'],
                        'vps_inside_area' => $vps_results[$i]['vps_inside_area'],
                        'edited' => ($vps_results[$i]['edited']) ? 'Да' : 'Не',
                        'comment' => $vps_results[$i]['comment'],
                    ];
                }
            }
            $return['rows'] = $vps_results;
        } else {
            $return['rows'] = false;
        }

        if ($rpcParams['export2document'] && true == $rpcParams['export2document']) {
            $column_headers = [
                'prc_name' => 'Име',
                'zeml' => 'Землище',
                'cropname' => 'Култура',
                'vps_type' => 'Тип заявено ВПС',
                'area' => 'Обща площ (дка) ',
                'vps_inside_area' => 'Площ в ВПС(дка)',
                'edited' => 'За редактиране',
                'comment' => 'Коментар',
            ];

            return [
                'rows' => $printData,
                'column_headers' => $column_headers,
                'footer' => [
                    'cropname' => 'Общо:',
                    'area' => $total_area,
                ],
            ];
        }

        $return['footer'][0]['cropname'] = '<b>Общо за стр.<b/>';
        $return['footer'][0]['area'] = '<b>' . $totalAreaPerPage . '<b/>';
        $return['footer'][1]['cropname'] = '<b>Общо<b/>';
        $return['footer'][1]['area'] = '<b>' . $total_area . '<b/>';
        $return['total'] = $counter[0]['count'];

        return $return;
    }

    /**
     * Exports LFA plots data in xls format.
     *
     * @api-method exportGridToPDF
     *
     * @param array $rpcParams
     *                         {
     *                         #item string layer_name
     *                         #item array filterObj {
     *                         #item string prc_name
     *                         #item integer ekate
     *                         #item string comment
     *                         #item boolean edited
     *                         #item boolean lfa
     *                         #item integer nm_lfa
     *                         }
     *                         }
     * @param int $page pagination parameter
     * @param int $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array {
     *               #item string xls_report_file
     *}
     */
    public function exportGridToXLS($rpcParams, $rows = '50', $sort = 'id', $order = 'asc')
    {
        $rpcParams['export2document'] = true;
        $type = 'vps_main_grid';

        $type = str_replace([' ', '.'], '_', $type);

        $results = $this->readForIsakDiffVPSMainGrid($rpcParams, '1', $rows, $sort, $order);

        $time = strtotime(date('Y-m-d H:i:s'));
        $filename = PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID . '/' . $type . $time . '.xlsx';

        if (!file_exists(PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID)) {
            mkdir(PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID, 0777);
        }

        $exportExcelDoc = new ExportToExcelClass();
        $exportExcelDoc->export($results['rows'], $results['column_headers'], [$results['footer']]);
        $exportExcelDoc->saveFile($filename);

        return [
            'exl_report_file' => 'files/uploads/export/' . $this->User->UserID . '/' . $type . $time . '.xlsx',
        ];
    }

    /**
     * Exports LFA plots data in pdf format.
     *
     * @api-method exportGridToPDF
     *
     * @param array $rpcParams
     *                         {
     *                         #item string layer_name
     *                         #item array filterObj {
     *                         #item string prc_name
     *                         #item integer ekate
     *                         #item string comment
     *                         #item boolean edited
     *                         #item boolean lfa
     *                         #item integer nm_lfa
     *                         }
     *                         }
     * @param int $page pagination parameter
     * @param int $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array {
     *               #item string pdf_report_file
     *}
     */
    public function exportGridToPDF($rpcParams, $rows = '50', $sort = 'id', $order = 'asc')
    {
        $LayersController = new LayersController('Layers');

        $rpcParams['export2document'] = true;
        $type = 'vps_main_grid';
        $type = str_replace([' ', '.'], '_', $type);

        if (!is_dir(PUBLIC_UPLOAD_RELATIVE_PATH . '/history/' . $this->User->GroupID)) {
            mkdir(PUBLIC_UPLOAD_RELATIVE_PATH . '/history/' . $this->User->GroupID);
        }

        $path = PUBLIC_UPLOAD_HISTORY . '/' . $this->User->GroupID . '/' . $type . '.pdf';
        $relativePath = PUBLIC_UPLOAD_RELATIVE_PATH . '/history/' . $this->User->GroupID . '/' . $type . '.pdf';

        $results = $this->readForIsakDiffVPSMainGrid($rpcParams, '1', $rows, $sort, $order);

        $ltext = $LayersController->StringHelper->loadTemplate($GLOBALS['Templates'][39]['template'], ['pdf_data' => $results['rows']]);
        $ltext = '<page style="font-family: freeserif"><br />' . $ltext . '</page>';

        $printPdf = new PrintPdf();
        $printPdf->generateFromHtml($ltext, $path, ['orientation' => 'Landscape'], true);

        $return['pdf_report_file'] = $relativePath;

        return $return;
    }
}
