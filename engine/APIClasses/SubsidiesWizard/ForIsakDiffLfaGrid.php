<?php

namespace TF\Engine\APIClasses\SubsidiesWizard;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\ExportToExcelClass;
use TF\Engine\Kernel\PrintPdf;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbForIsak\UserDbForIsakController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * @rpc-module SubsidiesWizard
 *
 * @rpc-service-id for-isak-diff-lfa-grid
 *
 * @property UserDbController $UserDbController
 * @property LayersController $LayersController
 * @property UserDbForIsakController $UserDbForIsakController
 * @property UsersController $UsersController
 * @property FarmingController $FarmingController
 */
class ForIsakDiffLfaGrid extends TRpcApiProvider
{
    private $UserDbController = false;
    private $UserDbZPlotsController = false;
    private $LayersController = false;
    private $UserDbForIsakController = false;
    private $UsersController = false;
    private $FarmingController = false;

    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readForIsakDiffLfaGrid'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'saveForNR' => ['method' => [$this, 'saveForNR']],
            'saveOffNR' => ['method' => [$this, 'saveOffNR']],
            'initForIsakDiffLfa' => ['method' => [$this, 'initForIsakDiffLfa']],
            'exportGridToXLS' => ['method' => [$this, 'exportGridToXLS']],
            'exportGridToPDF' => ['method' => [$this, 'exportGridToPDF']],
        ];
    }

    /**
     * Reads plots in least favored areas.
     *
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item string layer_name
     *                         #item boolean export2document
     *                         #item array filterObj {
     *                         #item string prc_name
     *                         #item integer ekate
     *                         #item string comment
     *                         #item boolean edited
     *                         #item boolean lfa
     *                         #item integer nm_lfa
     *                         }
     *                         }
     * @param int $page pagination parameter
     * @param int $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array {
     *               #item integer total
     *               #item array rows {
     *               #item string prc_name,
     *               #item string nm_lfa_lfa
     *               #item float area,
     *               #item float inside_area,
     *               #item float outside_area,
     *               #item string nr
     *               #item string edited
     *               #item string comment
     *               #item string ekate
     *               }
     *               #item array footer {
     *               #item string prc_name,
     *               #item string area,
     *               #item string outside_area,
     *               #item string inside_area
     *               }
     *               #item array column_headers {
     *               #item string
     *               }
     *               }
     */
    public function readForIsakDiffLfaGrid(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $return = [
            'total' => 0,
            'rows' => [],
            'footer' => [],
        ];

        if (!$rpcParams['layer_name']) {
            return $return;
        }

        // init all needed controllers
        $UserDbForIsakController = new UserDbForIsakController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        $LayersController = new LayersController('Layers');
        $FarmingController = new FarmingController('Farming');
        $UserDbForIsakController = new UserDbForIsakController($this->User->Database);

        $layer_name = $rpcParams['layer_name'];
        $request = $rpcParams['filterObj'];

        // check if table exists
        $tableExist = $UserDbController->getTableNameExist($layer_name);
        if (!$tableExist) {
            return $return;
        }

        $options = [
            'tablename' => $layer_name,
            'return' => ['ST_Extent(geom) as extent'],
        ];

        $extent_result = $UserDbController->getItemsByParams($options);

        // get FOR ISAK extent and transform it
        $maxextent = $extent_result[0]['extent'];
        $maxextent = str_replace('BOX(', '', $maxextent);
        $maxextent = str_replace(')', '', $maxextent);
        $maxextent = str_replace(' ', ',', $maxextent);

        $options = [
            'tablename' => $layer_name,
            'custom_counter' => 'COUNT(DISTINCT(gid)), SUM(round(area::numeric/10000, 4)) as total_area, 
                                SUM(round(outside_area::numeric/10000, 4)) as total_outside_area, 
                                (SUM(round(area::numeric/10000, 4)) - SUM(round(outside_area::numeric/10000, 4))) as total_inside_area',
            'return' => [
                'gid',
                'prc_name',
                'ekate',
                'nm_lfa_lfa',
                'st_astext',
                'round(area::numeric/10000, 4) as area',
                'round(outside_area::numeric/10000, 4) as outside_area',
                '(round(area::numeric/10000, 4) - round(outside_area::numeric/10000, 4)) AS inside_area',
                'edited',
                'comment',
                'nr',
            ],
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
            'extent' => $maxextent,
        ];

        if (strlen($request['prc_name'])) {
            $options['where']['prc_name'] = ['column' => 'prc_name', 'compare' => 'ILIKE', 'value' => $request['prc_name']];
        }
        if (strlen($request['ekate'])) {
            $options['where']['ekate'] = ['column' => 'ekate', 'compare' => 'ILIKE', 'value' => $request['ekate']];
        }
        if (strlen($request['comment'])) {
            $options['where']['comment'] = ['column' => 'comment', 'compare' => 'ILIKE', 'value' => $request['comment']];
        }
        if (strlen($request['edited']) && ('1' == $request['edited'] || '0' == $request['edited'])) {
            $options['where']['edited'] = ['column' => 'edited', 'compare' => '=', 'value' => $request['edited']];
        }
        if (strlen($request['nm_lfa']) && ('1' == $request['nm_lfa'] || '2' == $request['nm_lfa'])) {
            $options['where']['nm_lfa'] = ['column' => 'nm_lfa_lfa', 'compare' => '=', 'value' => $request['nm_lfa']];
        }
        if (strlen($request['lfa']) && ('1' == $request['lfa'] || '0' == $request['lfa'])) {
            $options['where']['lfa'] = ['column' => 'nr', 'compare' => '=', 'value' => $request['lfa']];
        }

        if ($rpcParams['export2document'] && true == $rpcParams['export2document']) {
            unset($options['offset'], $options['limit']);
        }

        $counter = $UserDbForIsakController->getForIsakDiffLfaData($options, true, false);

        if (0 == $counter[0]['count']) {
            return $return;
        }

        $data = $UserDbForIsakController->getForIsakDiffLfaData($options, false, false);
        $dataCount = count($data);
        $total_area = $counter[0]['total_area'];
        $total_inside_area = $counter[0]['total_inside_area'];
        $total_outside_area = $counter[0]['total_outside_area'];

        // total variables
        $total_inside_area_per_page = 0;
        $total_outside_area_per_page = 0;
        $total_area_per_page = 0;

        // iterate results
        for ($i = 0; $i < $dataCount; $i++) {
            // add to 4
            $total_area_per_page += $data[$i]['area'];
            $total_outside_area_per_page += $data[$i]['outside_area'];
            $total_inside_area_per_page += $data[$i]['area'] - $data[$i]['outside_area'];

            if ($data[$i]['ekate']) {
                $ekateCodes = explode(',', $data[$i]['ekate']);
                $ekateCount = count($ekateCodes);
                for ($j = 0; $j < $ekateCount; $j++) {
                    $ekateCodes[$j] = $UsersController->getEkatteName($ekateCodes[$j]);
                }
                $data[$i]['ekate'] = implode(', ', $ekateCodes);
            } else {
                $data[$i]['ekate'] = '-';
            }
        }
        // if the export to document flag is set
        if ($rpcParams['export2document'] && true == $rpcParams['export2document']) {
            $printData = [];
            for ($i = 0; $i < $dataCount; $i++) {
                $printData[$i] = [
                    'prc_name' => $data[$i]['prc_name'],
                    'ekate' => $data[$i]['ekate'],
                    'nm_lfa_lfa' => $data[$i]['nm_lfa_lfa'],
                    'area' => $data[$i]['area'],
                    'inside_area' => $data[$i]['inside_area'],
                    'outside_area' => $data[$i]['outside_area'],
                    'edited' => ($data[$i]['edited']) ? 'Да' : 'Не',
                    'comment' => $data[$i]['comment'],
                    'nr' => ($data[$i]['nr']) ? 'Да' : 'Не',
                ];
            }
            $column_headers = [
                'prc_name' => 'Име',
                'ekate' => 'Землище',
                'nm_lfa_lfa' => 'НР',
                'area' => 'Площ (ха)',
                'inside_area' => 'Площ в "НР"(ха)',
                'outside_area' => 'Площ извън "НР"(ха)',
                'edited' => 'За редактиране',
                'comment' => 'Коментар',
                'nr' => 'Заявен',
            ];

            return [
                'rows' => $printData,
                'column_headers' => $column_headers,
                'footer' => [
                    'prc_name' => 'Общо:',
                    'area' => $total_area,
                    'outside_area' => $total_outside_area,
                    'inside_area' => $total_inside_area,
                ],
            ];
        }
        $return = [
            'total' => $counter[0]['count'],
            'rows' => $data,
            'footer' => [
                [
                    'prc_name' => '<b>Общо за стр.</b>',
                    'area' => '<b>' . $total_area_per_page . '</b>',
                    'outside_area' => '<b>' . $total_outside_area_per_page . '</b>',
                    'inside_area' => '<b>' . $total_inside_area_per_page . '</b>',
                ],
                [
                    'prc_name' => '<b>Общо</b>',
                    'area' => '<b>' . $total_area . '</b>',
                    'outside_area' => '<b>' . $total_outside_area . '</b>',
                    'inside_area' => '<b>' . $total_inside_area . '</b>',
                ],
            ],
        ];
        $return['rows'] = $data;

        return $return;
    }

    /**
     * requests plot to leat favoured areas.
     *
     * @api-method saveForNR
     *
     * @param array $params {
     *                      #item string layer_name
     *                      #item array nr1 {
     *                      #item integer
     *                      }
     *                      #item array nr2 {
     *                      #item integer
     *                      }
     *                      }
     *
     * @return array
     */
    public function saveForNR($params)
    {
        $UserDbForIsakController = new UserDbForIsakController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);

        $data = (object)$params;

        // check if table exists
        $tableExist = $UserDbController->getTableNameExist($data->layer_name);
        if (!$tableExist) {
            return [];
        }

        $UserDbForIsakController->updateForNR($data);
    }

    /**
     * Removes requested plot from leat favoured areas.
     *
     * @api-method saveOffNR
     *
     * @param array $params {
     *                      #item string layer_name
     *                      #item array nr1 {
     *                      #item integer
     *                      }
     *                      #item array nr2 {
     *                      #item integer
     *                      }
     *                      }
     *
     * @return array
     */
    public function saveOffNR($params)
    {
        $UserDbForIsakController = new UserDbForIsakController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);

        $data = (object)$params;

        // check if table exists
        $tableExist = $UserDbController->getTableNameExist($data->layer_name);
        if (!$tableExist) {
            return [];
        }

        $UserDbForIsakController->updateOffNR($data);
    }

    /**
     * Creates lfa view table and updates map file.
     *
     * @api-method initForIsakDiffLfa
     *
     * @param string $layer_name
     * @param string $layer_id
     *
     * @return array
     */
    public function initForIsakDiffLfa($layer_name, $layer_id)
    {
        $UserDbForIsakController = new UserDbForIsakController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);
        $LayersController = new LayersController('Layers');

        // check if table exists
        $tableExist = $UserDbController->getTableNameExist($layer_name);
        if (!$tableExist) {
            return [];
        }

        $options = [
            'tablename' => $layer_name,
            'return' => ['ST_Extent(geom) as extent'],
        ];

        $extent_result = $UserDbController->getItemsByParams($options);

        // get FOR ISAK extent and transform it
        $maxextent = $extent_result[0]['extent'];
        $maxextent = str_replace('BOX(', '', $maxextent);
        $maxextent = str_replace(')', '', $maxextent);
        $maxextent = str_replace(' ', ',', $maxextent);
        $seppView = 'sepp_for_isak_' . $layer_id;
        if (!$UserDbController->getViewNameExists($seppView)) {
            $UserDbController->createSEPPReportView($layer_id);
        }
        $options = [
            'tablename' => $layer_name,
            'layer' => 'layer_lfa',
            'return' => [
                'for_isak.gid AS gid',
                'CASE WHEN ST_Difference(for_isak.geom, ST_Union(layer.geom)) IS NULL THEN for_isak.geom ELSE ST_Difference(for_isak.geom, ST_Union(layer.geom)) END AS geom',
            ],
            'group' => 'for_isak.gid',
            'extent' => $maxextent,
        ];

        $query = $UserDbForIsakController->getForIsakDiffLayer($options, false, true);

        $options = [];
        $options['database'] = $this->User->Database;
        $options['user_id'] = $this->User->GroupID;
        $LayersController->generateMapFile($options);

        $mapFileData = [];

        $color1 = '000000';
        $color2 = 'ff0000';
        $mapFileData['layername'] = 'for_isak_diff_lfa';
        $mapFileData['maxextent'] = $extent_result[0]['extent'];
        $mapFileData['host'] = DEFAULT_DB_HOST;
        $mapFileData['dbname'] = $this->User->Database;
        $mapFileData['username'] = DEFAULT_DB_USERNAME;
        $mapFileData['password'] = DEFAULT_DB_PASSWORD;
        $mapFileData['port'] = DEFAULT_DB_PORT;
        $mapFileData['query'] = "({$query}) as subquery using unique gid using srid=32635";
        $mapFileData['gid'] = 'gid';
        $mapFileData['transparency'] = '100';
        $mapFileData['classes'][0]['name'] = 'for_isak_diff_lfs';
        $mapFileData['classes'][0]['symbol']['name'] = 'hatch_sym';
        $mapFileData['classes'][0]['symbol']['size'] = 20;
        $mapFileData['classes'][0]['symbol']['angle'] = 45;
        $mapFileData['classes'][0]['symbol']['color'] = hexdec(substr($color2, 0, 2)) . ' ' . hexdec(substr($color2, 2, 2)) . ' ' . hexdec(substr($color2, 4, 2));

        $ltext = $LayersController->StringHelper->loadTemplate($GLOBALS['Templates'][2]['template'], $mapFileData);

        @unlink(WMS_MAP_PATH . $this->User->GroupID . '_layer_for_isak_diff_lfa.map');
        $fp = fopen(WMS_MAP_PATH . $this->User->GroupID . '_layer_for_isak_diff_lfa.map', 'w');
        $res = fwrite($fp, $ltext);
        fclose($fp);
    }

    /**
     * Exports LFA plots data in xls format.
     *
     * @api-method exportGridToPDF
     *
     * @param array $rpcParams
     *                         {
     *                         #item string layer_name
     *                         #item array filterObj {
     *                         #item string prc_name
     *                         #item integer ekate
     *                         #item string comment
     *                         #item boolean edited
     *                         #item boolean lfa
     *                         #item integer nm_lfa
     *                         }
     *                         }
     * @param int|string $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array {
     *               #item string xls_report_file
     *}
     */
    public function exportGridToXLS($rpcParams, $rows = '50', $sort = 'id', $order = 'asc')
    {
        $rpcParams['export2document'] = true;
        $type = 'neoblagodetelstvani_raioni';

        $type = str_replace([' ', '.'], '_', $type);

        $results = $this->readForIsakDiffLfaGrid($rpcParams, '1', $rows, $sort, $order);

        $time = strtotime(date('Y-m-d H:i:s'));
        $filename = PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID . '/' . $type . $time . '.xlsx';

        if (!file_exists(PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID)) {
            mkdir(PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID, 0777);
        }

        $exportExcelDoc = new ExportToExcelClass();
        $exportExcelDoc->export($results['rows'], $results['column_headers'], [$results['footer']]);
        $exportExcelDoc->saveFile($filename);

        return [
            'exl_report_file' => 'files/uploads/export/' . $this->User->UserID . '/' . $type . $time . '.xlsx',
        ];
    }

    /**
     * Exports LFA plots data in pdf format.
     *
     * @api-method exportGridToPDF
     *
     * @param array $rpcParams
     *                         {
     *                         #item string layer_name
     *                         #item array filterObj {
     *                         #item string prc_name
     *                         #item integer ekate
     *                         #item string comment
     *                         #item boolean edited
     *                         #item boolean lfa
     *                         #item integer nm_lfa
     *                         }
     *                         }
     * @param int|string $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array {
     *               #item string pdf_report_file
     *}
     */
    public function exportGridToPDF($rpcParams, $rows = '50', $sort = 'id', $order = 'asc')
    {
        $LayersController = new LayersController('Layers');

        $rpcParams['export2document'] = true;
        $type = 'neoblagodetelstvani_raioni';
        $type = str_replace([' ', '.'], '_', $type);

        if (!is_dir(PUBLIC_UPLOAD_RELATIVE_PATH . '/history/' . $this->User->GroupID)) {
            mkdir(PUBLIC_UPLOAD_RELATIVE_PATH . '/history/' . $this->User->GroupID);
        }

        $path = PUBLIC_UPLOAD_HISTORY . '/' . $this->User->GroupID . '/' . $type . '.pdf';
        $relativePath = PUBLIC_UPLOAD_RELATIVE_PATH . '/history/' . $this->User->GroupID . '/' . $type . '.pdf';

        $results = $this->readForIsakDiffLfaGrid($rpcParams, '1', $rows, $sort, $order);
        $ltext = $LayersController->StringHelper->loadTemplate($GLOBALS['Templates'][24]['template'], ['pdf_data' => $results['rows']]);
        $ltext = '<page style="font-family: freeserif"><br />' . $ltext . '</page>';

        $printPdf = new PrintPdf();
        $printPdf->generateFromHtml($ltext, $path, [], true);

        $return['pdf_report_file'] = $relativePath;

        return $return;
    }
}
