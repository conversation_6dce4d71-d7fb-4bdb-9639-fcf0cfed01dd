<?php

namespace TF\Engine\APIClasses\SubsidiesWizard;

use Prado\Web\Services\TRpcApiProvider;
use TF\Application\Common\Config;
use TF\Engine\Kernel\ExportToExcelClass;
use TF\Engine\Kernel\PrintPdf;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbForIsak\UserDbForIsakController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * @rpc-module SubsidiesWizard
 *
 * @rpc-service-id for-isak-report-zdp-grid
 */
class ForIsakZDPReportGrid extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readForIsakZDPReportGrid'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'addENPData' => ['method' => [$this, 'addENPData']],
            'editENPData' => ['method' => [$this, 'editENPData']],
            'getReportZDP' => ['method' => [$this, 'getReportZDP']],
            'exportGridToXLS' => ['method' => [$this, 'exportGridToXLS']],
            'exportGridToPDF' => ['method' => [$this, 'exportGridToPDF']],
        ];
    }

    /**
     * Reads plots compatiable with zdp.
     *
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item string layer_name
     *                         #item boolean export2document
     *                         #item array filterObj {
     *                         #item string prc_name
     *                         #item string culture_short_type
     *                         #item string culture
     *                         #item string comment
     *                         #item string edited
     *                         #item string zdp
     *                         }
     *                         }
     * @param int $page pagination parameter
     * @param int $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array {
     *               #item integer total
     *               #item array rows {
     *               #item string prc_name,
     *               #item string cropname,
     *               #item stringcrop_short_type
     *               #item float area,
     *               #item float green_area
     *               #item boolean zdp
     *               #item boolean edited
     *               #item string comment
     *               #item string st_astext,
     *               }
     *               #item array footer {
     *               #item string prc_name,
     *               #item string area,
     *               #item string green_area,
     *               }
     *               #item array column_headers {
     *               #item string
     *               }
     *               }
     */
    public function readForIsakZDPReportGrid(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $return = [
            'total' => 0,
            'rows' => [],
            'footer' => [],
        ];

        // init all needed controllers

        $UserDbForIsakController = new UserDbForIsakController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        $LayersController = new LayersController('Layers');
        $FarmingController = new FarmingController('Farming');
        $UserDbForIsakController = new UserDbForIsakController($this->User->Database);

        $layer_name = $rpcParams['layer_name'];
        $request = $rpcParams['filterObj'];

        // check if table exists
        $tableExist = $UserDbController->getTableNameExist($layer_name);
        if (!$tableExist) {
            return $return;
        }

        $options = [
            'tablename' => $layer_name,
            'return' => ['ST_Extent(geom) as extent'],
        ];

        $extent_result = $UserDbController->getItemsByParams($options);

        // get FOR ISAK extent and transform it
        $maxextent = $extent_result[0]['extent'];
        $maxextent = str_replace('BOX(', '', $maxextent);
        $maxextent = str_replace(')', '', $maxextent);
        $maxextent = str_replace(' ', ',', $maxextent);

        $options = [
            'tablename' => $layer_name,
            'custom_counter' => 'COUNT(DISTINCT(gid)), SUM(area) as total_area, SUM(green_area) as total_green_area',
            'return' => [
                'for_isak.prc_name',
                'ST_AsText(for_isak.geom) as st_astext',
                'for_isak.gid',
                'for_isak.zdp',
                'round((ST_Area(geom)/10000)::numeric, 4) as area',
                'for_isak.cropcode',
                'for_isak.edited',
                'for_isak.cropname',
                'for_isak.azot_fixed_crop',
                'for_isak.is_intermediate_crop',
                'for_isak.is_intermediate_weat_crop',
                'for_isak.is_tree_short_rotation',
                'for_isak.no_pndn',
                'round((ST_Area(geom)/10000)::numeric, 4)*green_area_factor as green_area',
                'for_isak.crop_short_type',
                'for_isak.comment',
            ],
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
            'group' => 'for_isak.gid',
            'extent' => $maxextent,
        ];

        if (strlen($request['prc_name'])) {
            $options['where']['prc_name'] = ['column' => 'prc_name', 'compare' => 'ILIKE', 'prefix' => 'for_isak', 'value' => $request['prc_name']];
        }
        if (strlen($request['culture'])) {
            $options['where']['cropcode'] = ['column' => 'cropcode', 'compare' => '=', 'value' => $request['culture']];
        }
        if (strlen($request['culture_short_type'])) {
            // Угар
            if ('190000' == $request['culture_short_type']) {
                $options['where']['cropcode'] = ['column' => 'cropcode', 'compare' => '=', 'value' => $request['culture_short_type']];
            } else {
                if ('is_intermediate_crop' == $request['culture_short_type']) {
                    $options['where']['common_cultures'] = ['column' => 'common_cultures', 'compare' => '=', 'value' => 1];
                } else {
                    $options['where'][$request['culture_short_type']] = ['column' => $request['culture_short_type'], 'compare' => '=', 'value' => 1];
                }
            }
        }
        if (strlen($request['comment'])) {
            $options['where']['comment'] = ['column' => 'comment', 'compare' => 'ILIKE', 'prefix' => 'for_isak', 'value' => $request['comment']];
        }
        if (strlen($request['edited']) && ('1' == $request['edited'] || '0' == $request['edited'])) {
            $options['where']['edited'] = ['column' => 'edited', 'compare' => '=', 'prefix' => 'for_isak', 'value' => $request['edited']];
        }
        if (strlen($request['zdp']) && ('1' == $request['zdp'] || '0' == $request['zdp'])) {
            $options['where']['zdp'] = ['column' => 'zdp', 'compare' => '=', 'prefix' => 'for_isak', 'value' => $request['zdp']];
        }

        if ($rpcParams['export2document'] && true == $rpcParams['export2document']) {
            unset($options['offset'], $options['limit']);
        }

        $counter = $UserDbForIsakController->getForIsakDataZDP($options, true, false);
        $total_area = $counter[0]['total_area'];
        $total_green_area = $counter[0]['total_green_area'];

        if (0 === $counter[0]['count']) {
            return $return;
        }

        $data = $UserDbForIsakController->getForIsakDataZDP($options, false, false);
        $dataCount = count($data);
        // total variables
        $total_green_area_per_page = 0;
        $total_area_per_page = 0;

        // iterate results
        for ($i = 0; $i < $dataCount; $i++) {
            $data[$i]['green_area'] = number_format((float)$data[$i]['green_area'], 4, '.', '');

            $data[$i]['is_active_culture'] = $GLOBALS['Farming']['crops'][$data[$i]['cropcode']]['is_active'];

            // add to total
            $total_area_per_page += $data[$i]['area'];
            $total_green_area_per_page += $data[$i]['green_area'];
        }

        // if the export to document flag is set
        if ($rpcParams['export2document'] && true == $rpcParams['export2document']) {
            $printData = [];
            for ($i = 0; $i < $dataCount; $i++) {
                $printData[$i] = [
                    'prc_name' => $data[$i]['prc_name'],
                    'cropname' => $data[$i]['cropname'],
                    'crop_short_type' => $data[$i]['crop_short_type'],
                    'area' => $data[$i]['area'],
                    'green_area' => $data[$i]['green_area'],
                    'zdp' => ($data[$i]['zdp']) ? 'Да' : 'Не',
                    'edited' => ($data[$i]['edited']) ? 'Да' : 'Не',
                    'comment' => $data[$i]['comment'],
                ];
            }
            $column_headers = [
                'prc_name' => 'Име',
                'cropname' => 'Култура',
                'crop_short_type' => 'Тип',
                'area' => 'Площ(ха)',
                'green_area' => 'Зелена площ(ха)',
                'zdp' => 'Заявен',
                'edited' => 'За редактиране',
                'comment' => 'Коментар',
            ];

            return [
                'rows' => $printData,
                'column_headers' => $column_headers,
                'footer' => [
                    'prc_name' => 'Общо:',
                    'area' => number_format((float)$total_area, 4, '.', ''),
                    'green_area' => number_format((float)$total_green_area, 4, '.', ''),
                ],
            ];
        }

        $return = [
            'total' => $counter[0]['count'],
            'rows' => $data,
            'footer' => [
                [
                    'prc_name' => '<b>Общо за стр.</b>',
                    'area' => '<b>' . number_format((float)$total_area_per_page, 4, '.', '') . '</b>',
                    'green_area' => '<b>' . number_format((float)$total_green_area_per_page, 4, '.', '') . '</b>',
                ],
                [
                    'prc_name' => '<b>Общо</b>',
                    'area' => '<b>' . number_format((float)$total_area, 4, '.', '') . '</b>',
                    'green_area' => '<b>' . number_format((float)$total_green_area, 4, '.', '') . '</b>',
                ],
            ],
        ];

        return $return;
    }

    /**
     * adds landshaft elements.
     *
     * @api-method addENPData
     *
     * @param string $layer_name
     * @param int $year
     * @param int $farming
     * @param array $ENP_quantities {
     *                              #item string quantityBufferStrips
     *                              #item string quantityDitch
     *                              #item string quantityFieldBoundaries
     *                              #item string quantityGroupTree
     *                              #item string quantityHedge
     *                              #item string quantityIsolatedTree
     *                              #item string quantityLakes
     *                              #item string quantityRowsTree
     *                              #item string quantityStripsArea
     *                              #item string quantityTerraces
     *                              }
     */
    public function addENPData($farming, $year, $layer_name, $ENP_quantities)
    {
        $UserDbController = new UserDbController($this->User->Database);

        if (!$UserDbController->getTableNameExist('layer_enp_data')) {
            $UserDbController->createTableEnpData();
        }

        $options = [
            'tablename' => 'layer_enp_data',
            'id_name' => 'layer_name',
            'id_string' => $layer_name,
        ];
        $UserDbController->deleteItemsByParams($options);

        // Тераси (м)
        if (strlen($ENP_quantities['quantityTerraces'])) {
            $options = [
                'tablename' => 'layer_enp_data',
                'mainData' => [
                    'name' => 'Тераси',
                    'type' => 'terraces',
                    'layer_name' => $layer_name,
                    'farming' => $farming,
                    'year' => $year,
                    'quantity' => $ENP_quantities['quantityTerraces'],
                    'green_area_factor' => $GLOBALS['Farming']['crops_short_type']['terraces']['factor_enp'],
                ],
            ];
            $recordID = $UserDbController->addItem($options);
        }
        // Жив плет/обрасла с дървесна растителност ивица (м)
        if (strlen($ENP_quantities['quantityHedge'])) {
            $options = [
                'tablename' => 'layer_enp_data',
                'mainData' => [
                    'name' => 'Жив плет/обрасла с дървесна растителност ивица',
                    'type' => 'hedge',
                    'layer_name' => $layer_name,
                    'farming' => $farming,
                    'year' => $year,
                    'quantity' => $ENP_quantities['quantityHedge'],
                    'green_area_factor' => $GLOBALS['Farming']['crops_short_type']['hedge']['factor_enp'],
                ],
            ];
            $recordID = $UserDbController->addItem($options);
        }
        // Изолирани дървета
        if (strlen($ENP_quantities['quantityIsolatedTree'])) {
            $options = [
                'tablename' => 'layer_enp_data',
                'mainData' => [
                    'name' => 'Изолирани дървета',
                    'type' => 'isolated_tree',
                    'layer_name' => $layer_name,
                    'farming' => $farming,
                    'year' => $year,
                    'quantity' => $ENP_quantities['quantityIsolatedTree'],
                    'green_area_factor' => $GLOBALS['Farming']['crops_short_type']['isolated_tree']['factor_enp'],
                ],
            ];
            $recordID = $UserDbController->addItem($options);
        }
        // Редици от дървета
        if (strlen($ENP_quantities['quantityRowsTree'])) {
            $options = [
                'tablename' => 'layer_enp_data',
                'mainData' => [
                    'name' => 'Редици от дървета',
                    'type' => 'rows_tree',
                    'layer_name' => $layer_name,
                    'farming' => $farming,
                    'year' => $year,
                    'quantity' => $ENP_quantities['quantityRowsTree'],
                    'green_area_factor' => $GLOBALS['Farming']['crops_short_type']['rows_tree']['factor_enp'],
                ],
            ];
            $recordID = $UserDbController->addItem($options);
        }
        // Групи от дървета (кв.м)
        if (strlen($ENP_quantities['quantityGroupTree'])) {
            $options = [
                'tablename' => 'layer_enp_data',
                'mainData' => [
                    'name' => 'Групи от дървета',
                    'type' => 'group_tree',
                    'layer_name' => $layer_name,
                    'farming' => $farming,
                    'year' => $year,
                    'quantity' => $ENP_quantities['quantityGroupTree'],
                    'green_area_factor' => $GLOBALS['Farming']['crops_short_type']['group_tree']['factor_enp'],
                ],
            ];
            $recordID = $UserDbController->addItem($options);
        }
        // Синори (полски граници) (м)
        if (strlen($ENP_quantities['quantityFieldBoundaries'])) {
            $options = [
                'tablename' => 'layer_enp_data',
                'mainData' => [
                    'name' => 'Синори (полски граници)',
                    'type' => 'field_boundaries',
                    'layer_name' => $layer_name,
                    'farming' => $farming,
                    'year' => $year,
                    'quantity' => $ENP_quantities['quantityFieldBoundaries'],
                    'green_area_factor' => $GLOBALS['Farming']['crops_short_type']['field_boundaries']['factor_enp'],
                ],
            ];
            $recordID = $UserDbController->addItem($options);
        }
        // Езерца (за 1 кв.м)
        if (strlen($ENP_quantities['quantityLakes'])) {
            $options = [
                'tablename' => 'layer_enp_data',
                'mainData' => [
                    'name' => 'Езерца',
                    'type' => 'lakes',
                    'layer_name' => $layer_name,
                    'farming' => $farming,
                    'year' => $year,
                    'quantity' => $ENP_quantities['quantityLakes'],
                    'green_area_factor' => $GLOBALS['Farming']['crops_short_type']['lakes']['factor_enp'],
                ],
            ];
            $recordID = $UserDbController->addItem($options);
        }
        // Канавки и открити водни течения (м)
        if (strlen($ENP_quantities['quantityDitch'])) {
            $options = [
                'tablename' => 'layer_enp_data',
                'mainData' => [
                    'name' => 'Канавки и открити водни течения',
                    'type' => 'ditch',
                    'layer_name' => $layer_name,
                    'farming' => $farming,
                    'year' => $year,
                    'quantity' => $ENP_quantities['quantityDitch'],
                    'green_area_factor' => $GLOBALS['Farming']['crops_short_type']['ditch']['factor_enp'],
                ],
            ];
            $recordID = $UserDbController->addItem($options);
        }
        // Буферни ивици (м)
        if (strlen($ENP_quantities['quantityBufferStrips'])) {
            $options = [
                'tablename' => 'layer_enp_data',
                'mainData' => [
                    'name' => 'Буферни ивици',
                    'type' => 'buffer_strips',
                    'layer_name' => $layer_name,
                    'farming' => $farming,
                    'year' => $year,
                    'quantity' => $ENP_quantities['quantityBufferStrips'],
                    'green_area_factor' => $GLOBALS['Farming']['crops_short_type']['buffer_strips']['factor_enp'],
                ],
            ];
            $recordID = $UserDbController->addItem($options);
        }
        // Ивици допустими площи на границата между обработваеми земи и гори без производство (м)
        if (strlen($ENP_quantities['quantityStripsArea'])) {
            $options = [
                'tablename' => 'layer_enp_data',
                'mainData' => [
                    'name' => 'Ивици допустими площи на границата между обработваеми земи и гори без производство',
                    'type' => 'strips_area',
                    'layer_name' => $layer_name,
                    'farming' => $farming,
                    'year' => $year,
                    'quantity' => $ENP_quantities['quantityStripsArea'],
                    'green_area_factor' => $GLOBALS['Farming']['crops_short_type']['strips_area']['factor_enp'],
                ],
            ];
            $recordID = $UserDbController->addItem($options);
        }
    }

    /**
     * reads landshaft elements.
     *
     * @api-method addENPData
     *
     * @param string $layer_name
     *
     * @return array {
     *               #item string quantityBufferStrips
     *               #item string quantityDitch
     *               #item string quantityFieldBoundaries
     *               #item string quantityGroupTree
     *               #item string quantityHedge
     *               #item string quantityIsolatedTree
     *               #item string quantityLakes
     *               #item string quantityRowsTree
     *               #item string quantityStripsArea
     *               #item string quantityTerraces
     *               #item string greenAreaTerraces
     *               #item string greenAreaHedge
     *               #item string greenAreaIsolatedTree
     *               #item string greenAreaRowsTree
     *               #item string greenAreaGroupTree
     *               #item string greenAreaFieldBoundaries
     *               #item string greenAreaLakes
     *               #item string greenAreaDitch
     *               #item string greenAreaBufferStrips
     *               #item string greenAreaStripsArea
     *               }
     */
    public function editENPData($layer_name)
    {
        $UserDbController = new UserDbController($this->User->Database);

        if (!$UserDbController->getTableNameExist('layer_enp_data')) {
            return;
        }

        $options = [
            'tablename' => 'layer_enp_data',
            'where' => [
                'layer_name' => ['column' => 'layer_name', 'compare' => '=', 'value' => $layer_name],
            ],
        ];
        $result = $UserDbController->getItemsByParams($options);
        $resultCount = count($result);
        $return = [
            'greenAreaTerraces' => '',
            'greenAreaHedge' => '',
            'greenAreaIsolatedTree' => '',
            'greenAreaRowsTree' => '',
            'greenAreaGroupTree' => '',
            'greenAreaFieldBoundaries' => '',
            'greenAreaLakes' => '',
            'greenAreaDitch' => '',
            'greenAreaBufferStrips' => '',
            'greenAreaStripsArea' => '',
        ];

        for ($i = 0; $i < $resultCount; $i++) {
            if ('terraces' == $result[$i]['type'] && $result[$i]['quantity']) {
                $return['quantityTerraces'] = $result[$i]['quantity'];
                $return['greenAreaTerraces'] = $result[$i]['quantity'] * $GLOBALS['Farming']['crops_short_type'][$result[$i]['type']]['factor_enp'];
            }

            if ('hedge' == $result[$i]['type'] && $result[$i]['quantity']) {
                $return['quantityHedge'] = $result[$i]['quantity'];
                $return['greenAreaHedge'] = $result[$i]['quantity'] * $GLOBALS['Farming']['crops_short_type'][$result[$i]['type']]['factor_enp'];
            }

            if ('isolated_tree' == $result[$i]['type'] && $result[$i]['quantity']) {
                $return['quantityIsolatedTree'] = $result[$i]['quantity'];
                $return['greenAreaIsolatedTree'] = $result[$i]['quantity'] * $GLOBALS['Farming']['crops_short_type'][$result[$i]['type']]['factor_enp'];
            }

            if ('rows_tree' == $result[$i]['type'] && $result[$i]['quantity']) {
                $return['quantityRowsTree'] = $result[$i]['quantity'];
                $return['greenAreaRowsTree'] = $result[$i]['quantity'] * $GLOBALS['Farming']['crops_short_type'][$result[$i]['type']]['factor_enp'];
            }

            if ('group_tree' == $result[$i]['type'] && $result[$i]['quantity']) {
                $return['quantityGroupTree'] = $result[$i]['quantity'];
                $return['greenAreaGroupTree'] = $result[$i]['quantity'] * $GLOBALS['Farming']['crops_short_type'][$result[$i]['type']]['factor_enp'];
            }

            if ('field_boundaries' == $result[$i]['type'] && $result[$i]['quantity']) {
                $return['quantityFieldBoundaries'] = $result[$i]['quantity'];
                $return['greenAreaFieldBoundaries'] = $result[$i]['quantity'] * $GLOBALS['Farming']['crops_short_type'][$result[$i]['type']]['factor_enp'];
            }

            if ('lakes' == $result[$i]['type'] && $result[$i]['quantity']) {
                $return['quantityLakes'] = $result[$i]['quantity'];
                $return['greenAreaLakes'] = $result[$i]['quantity'] * $GLOBALS['Farming']['crops_short_type'][$result[$i]['type']]['factor_enp'];
            }

            if ('ditch' == $result[$i]['type'] && $result[$i]['quantity']) {
                $return['quantityDitch'] = $result[$i]['quantity'];
                $return['greenAreaDitch'] = $result[$i]['quantity'] * $GLOBALS['Farming']['crops_short_type'][$result[$i]['type']]['factor_enp'];
            }

            if ('buffer_strips' == $result[$i]['type'] && $result[$i]['quantity']) {
                $return['quantityBufferStrips'] = $result[$i]['quantity'];
                $return['greenAreaBufferStrips'] = $result[$i]['quantity'] * $GLOBALS['Farming']['crops_short_type'][$result[$i]['type']]['factor_enp'];
            }

            if ('strips_area' == $result[$i]['type'] && $result[$i]['quantity']) {
                $return['quantityStripsArea'] = $result[$i]['quantity'];
                $return['greenAreaStripsArea'] = $result[$i]['quantity'] * $GLOBALS['Farming']['crops_short_type'][$result[$i]['type']]['factor_enp'];
            }
        }

        return $return;
    }

    /**
     * Reads green payments requirements.
     *
     * @api-method getReportZDP
     *
     * @param string $layer_name
     *
     * @return array|string
     */
    public function getReportZDP($layer_name)
    {
        $UserDbForIsakController = new UserDbForIsakController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);
        $LayersController = new LayersController('Layers');

        $bDiverShortText = false;
        $bENPShortText = false;

        $table_exists = $UserDbController->getTableNameExist($layer_name);
        if (!$table_exists) {
            return ['rows' => [], 'total' => 0];
        }

        // Размера на обработваемата площ
        $options = [
            'return' => [
                'SUM(round((ST_Area(geom)/10000)::numeric, 4)) AS sum_area',
            ],
            'tablename' => $layer_name,
            'where' => [
                'crop_type' => ['column' => 'crop_type', 'compare' => '=', 'prefix' => 'for_isak', 'value' => 'Обработваема земя'],
            ],
        ];
        $result = $UserDbForIsakController->getReportForIsakDataZDP($options, false, false);

        // if no result
        if (0 == (int)$result[0]['sum_area']) {
            $data = ['bDefaultText' => true];

            $returnData = $LayersController->StringHelper->loadTemplate($GLOBALS['Templates'][19]['template'], $data);

            return $returnData;
        }

        $iTotalProcessedSumArea = $result[0]['sum_area'] > 0 ? $result[0]['sum_area'] : 0;

        if ($iTotalProcessedSumArea < Config::LOW_TREATMENT_AREA_DIVER) {
            $bDiverShortText = true;
        }

        // Размера на 'Заявената' обработваема площ
        $options = [
            'return' => [
                'SUM(round((ST_Area(geom)/10000)::numeric, 4)) AS area',
                'azot_fixed_crop',
                'is_intermediate_crop',
                'is_tree_short_rotation',
                'common_cultures',
                'cropcode',
            ],
            'tablename' => $layer_name,
            'where' => [
                'zdp' => ['column' => 'zdp', 'compare' => '=', 'prefix' => 'for_isak', 'value' => 1],
            ],
            'group' => 'for_isak.gid',
        ];
        $result = $UserDbForIsakController->getReportForIsakDataZDP($options, false, false);
        $resultCount = count($result);
        $total_green_area = 0;
        // iterate results
        for ($i = 0; $i < $resultCount; $i++) {
            $result[$i]['green_area'] = 0;

            if ($result[$i]['common_cultures']) {
                $result[$i]['green_area'] = $result[$i]['area'] * $GLOBALS['Farming']['crops_short_type']['is_intermediate_crop']['factor_enp'];
            } else {
                if ($result[$i]['azot_fixed_crop']) {
                    $result[$i]['green_area'] = $result[$i]['area'] * $GLOBALS['Farming']['crops_short_type']['azot_fixed_crop']['factor_enp'];
                }
                if ($result[$i]['is_tree_short_rotation']) {
                    $result[$i]['green_area'] = $result[$i]['area'] * $GLOBALS['Farming']['crops_short_type']['is_tree_short_rotation']['factor_enp'];
                }
                // Угар
                if ('190000' == $result[$i]['cropcode']) {
                    $result[$i]['green_area'] = $result[$i]['area'] * $GLOBALS['Farming']['crops_short_type']['190000']['factor_enp'];
                }
            }

            $total_green_area += $result[$i]['green_area'];
        }

        // Елементи на ландшафта
        $elements_green_area = 0;
        if ($UserDbController->getTableNameExist('layer_enp_data')) {
            $options = [
                'tablename' => 'layer_enp_data',
                'return' => [
                    'SUM(quantity*green_area_factor) as green_area',
                ],
                'where' => [
                    'layer_name' => ['column' => 'layer_name', 'compare' => '=', 'value' => $layer_name],
                ],
            ];
            $result = $UserDbController->getItemsByParams($options);

            if ($result[0]['green_area']) {
                $elements_green_area = $result[0]['green_area'];
                $elements_green_area = $elements_green_area / 10000;
            }
        }

        $iTotalProcessedSumAreaRequested = $total_green_area + $elements_green_area;
        if ($iTotalProcessedSumArea < Config::LOW_TREATMENT_AREA_ENP) {
            $bENPShortText = true;
        }

        // Сумарна площ на културите по cropcode (за да вземем площа на 1-ва и 2-ра по големина култура)
        $options = [
            'return' => [
                'SUM(round((ST_Area(geom)/10000)::numeric, 4)) AS max_area', 'cropcode',
            ],
            'tablename' => $layer_name,
            'where' => [
                'crop_type' => ['column' => 'crop_type', 'compare' => '=', 'prefix' => 'for_isak', 'value' => 'Обработваема земя'],
            ],
            'group' => 'cropcode',
            'sort' => 'max_area',
            'order' => 'desc',
        ];
        $result = $UserDbForIsakController->getReportForIsakDataZDP($options, false, false);
        $iCropsCount = count($result);

        $iFirstCropArea = $result[0]['max_area'] ?? 0;
        $iSecondCropArea = $result[1]['max_area'] ?? 0;

        $iMaxProcessedAreaPercent = ($iFirstCropArea / $iTotalProcessedSumArea) * 100;

        // Изискването за основната култура
        $textMainCropRequirement = '';

        $iFirstPlusSecondCrop = $iFirstCropArea + $iSecondCropArea;
        $iFirstPlusSecondCropPercent = ($iFirstPlusSecondCrop / $iTotalProcessedSumArea) * 100;

        $sTextMinCrops = '';
        if ($iTotalProcessedSumArea >= Config::MIN_TREATMENT_AREA) {
            $iMinCropsCount = 3;
            $sTextMinCrops = 'В земеделските стопанства с обработваема площ над ' . Config::MIN_TREATMENT_AREA . 'ха трябва да се отглеждат минимум ' . $iMinCropsCount . ' култури. ';
        }
        if ($iTotalProcessedSumArea >= Config::LOW_TREATMENT_AREA_DIVER && $iTotalProcessedSumArea < Config::MIN_TREATMENT_AREA) {
            $iMinCropsCount = 2;
            $sTextMinCrops = 'В земеделските стопанства с обработваема площ между ' . Config::LOW_TREATMENT_AREA_DIVER . 'ха и ' . Config::MIN_TREATMENT_AREA . 'ха трябва да се отглеждат минимум ' . $iMinCropsCount . ' култури. ';
        }

        // case 1
        if ($iCropsCount >= $iMinCropsCount) {
            $textMinCropsCount = "<span style='color: #079b07;'>е спазено</span>";
        } else {
            $textMinCropsCount = "<span style='color: #cc1c1c;'>не е спазено</span>";
        }

        // case 2
        if ($iMaxProcessedAreaPercent <= Config::MAX_PERCENT_FIRST_CROP) {
            $textMainCropRequirement = "<span style='color: #079b07;'>е спазено</span>";
        } else {
            $textMainCropRequirement = "<span style='color: #cc1c1c;'>не е спазено</span>";
        }

        // case 3
        if ($iFirstPlusSecondCropPercent <= Config::MAX_PERCENT_FIRST_PLUS_SECOND_CROP) {
            $textFirstPlusSecondRequirement = "<span style='color: #079b07;'>е спазено</span>";
        } else {
            $textFirstPlusSecondRequirement = "<span style='color: #cc1c1c;'>не е спазено</span>";
        }

        // cases 1,2,3
        if ($iCropsCount >= $iMinCropsCount
        && $iMaxProcessedAreaPercent <= Config::MAX_PERCENT_FIRST_CROP
        && $iFirstPlusSecondCropPercent <= Config::MAX_PERCENT_FIRST_PLUS_SECOND_CROP) {
            $textDiverRequirement = "<span style='color: #079b07;'>е спазено</span>";
        } else {
            $textDiverRequirement = "<span style='color: #cc1c1c;'>не е спазено</span>";
        }

        $iMinENPArea = $iTotalProcessedSumArea * (Config::MIN_PERCENT_ENP / 100);

        // case ENP
        if ($iTotalProcessedSumAreaRequested > $iMinENPArea) {
            $textENPRequirement = "<span style='color: #079b07;'>е спазено</span>";
            $textENPMoreOrLess = 'повече';
        } else {
            $textENPRequirement = "<span style='color: #cc1c1c;'>не е спазено</span>";
            $textENPMoreOrLess = 'по-малко';
        }

        // PZP check
        $options = [
            'tablename' => $layer_name,
            'return' => ['ST_Extent(geom) as extent'],
        ];

        $extent_result = $UserDbController->getItemsByParams($options);

        // get FOR ISAK extent and transform it
        $maxextent = $extent_result[0]['extent'];
        $maxextent = str_replace('BOX(', '', $maxextent);
        $maxextent = str_replace(')', '', $maxextent);
        $maxextent = str_replace(' ', ',', $maxextent);

        $forIsakLayerData = $LayersController->getLayers([
            'return' => ['t.id'],
            'where' => [
                'table_name' => ['column' => 't.table_name', 'compare' => '=', 'value' => $layer_name],
            ],
        ]);
        $layerId = $forIsakLayerData[0]['id'];
        $pzpView = 'pzp_for_isak_' . $layerId;

        if (!$UserDbController->getViewNameExists($pzpView)) {
            $UserDbController->createPZPReportView($layerId);
        }

        $options = [
            'tablename' => $layer_name,
            'return' => [
                '*',
            ],
            'sort' => 'gid',
            'order' => 'desc',
            'extent' => $maxextent,
            'pzpView' => $pzpView,
            'isSchema' => false,
            'searchInCropType' => true,
        ];

        $result = $UserDbForIsakController->getForIsakDataPZP($options, false, false);

        if (count($result)) {
            $textPZP = '<span style="color: #f79232;">Това условие във вашето стопанство трябва да се спазва в земеделските парцели и пресичаните от тях поземлени имоти, посочени в наличната по-долу справка "Постоянно затревени площи"</span>';
        } else {
            $textPZP = "Във вашето стопанство <span style='color: #079b07;'>не са установени постоянно затревени площи в защитени зони по смисъла на закона за биологичното разнообразие</span>";
        }

        $data = [
            'bDefaultText' => false,
            'textMinCropsCount' => $textMinCropsCount,
            'iMinCropsCount' => $iMinCropsCount,
            'sTextMinCrops' => $sTextMinCrops,
            'bDiverShortText' => $bDiverShortText,
            'bENPShortText' => $bENPShortText,
            'iTotalProcessedSumArea' => $iTotalProcessedSumArea,
            'iFirstCropArea' => $iFirstCropArea,
            'iMaxProcessedAreaPercent' => number_format((float)$iMaxProcessedAreaPercent, 2, '.', ''),
            'textMainCropRequirement' => $textMainCropRequirement,
            'iFirstPlusSecondCropPercent' => number_format((float)$iFirstPlusSecondCropPercent, 2, '.', ''),
            'textFirstPlusSecondRequirement' => $textFirstPlusSecondRequirement,
            'textDiverRequirement' => $textDiverRequirement,
            'iMinENPArea' => number_format((float)$iMinENPArea, 4, '.', ''),
            'iTotalProcessedSumAreaRequested' => number_format((float)$iTotalProcessedSumAreaRequested, 4, '.', ''),
            'textENPRequirement' => $textENPRequirement,
            'textENPMoreOrLess' => $textENPMoreOrLess,
            'textPZP' => $textPZP,
        ];

        $returnData = $LayersController->StringHelper->loadTemplate($GLOBALS['Templates'][19]['template'], $data);

        return $returnData;
    }

    /**
     * Exports zdp plots data in xls format.
     *
     * @api-method exportGridToXLS
     *
     * @param array $rpcParams
     *                         {
     *                         #item string layer_name
     *                         #item array filterObj {
     *                         #item string prc_name
     *                         #item string culture_short_type
     *                         #item string culture
     *                         #item string comment
     *                         #item string edited
     *                         #item string zdp
     *                         }
     *                         }
     * @param int $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array {
     *               #item string xls_report_file
     *}
     */
    public function exportGridToXLS($rpcParams, $rows = '50', $sort = 'id', $order = 'asc')
    {
        $rpcParams['export2document'] = true;
        $type = 'zdp';

        $type = str_replace([' ', '.'], '_', $type);

        $return = [];

        $results = $this->readForIsakZDPReportGrid($rpcParams, '1', $rows, $sort, $order);

        $time = strtotime(date('Y-m-d H:i:s'));
        $filename = PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID . '/' . $type . $time . '.xlsx';

        if (!file_exists(PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID)) {
            mkdir(PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID, 0777);
        }

        $exportExcelDoc = new ExportToExcelClass();
        $exportExcelDoc->export($results['rows'], $results['column_headers'], [$results['footer']]);
        $exportExcelDoc->saveFile($filename);

        return [
            'exl_report_file' => 'files/uploads/export/' . $this->User->UserID . '/' . $type . $time . '.xlsx',
        ];
    }

    /**
     * Exports zdp plots data in pdf format.
     *
     * @api-method exportGridToPDF
     *
     * @param array $rpcParams
     *                         {
     *                         #item string layer_name
     *                         #item array filterObj {
     *                         #item string prc_name
     *                         #item string culture_short_type
     *                         #item string culture
     *                         #item string comment
     *                         #item string edited
     *                         #item string zdp
     *                         }
     *                         }
     * @param int $page pagination parameter
     * @param int $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array {
     *               #item string pdf_report_file
     *}
     */
    public function exportGridToPDF($rpcParams, $rows = '50', $sort = 'id', $order = 'asc')
    {
        $LayersController = new LayersController('Layers');

        $rpcParams['export2document'] = true;
        $type = 'zdp';

        $type = str_replace([' ', '.'], '_', $type);

        if (!is_dir(PUBLIC_UPLOAD_RELATIVE_PATH . '/history/' . $this->User->GroupID)) {
            mkdir(PUBLIC_UPLOAD_RELATIVE_PATH . '/history/' . $this->User->GroupID);
        }

        $path = PUBLIC_UPLOAD_HISTORY . '/' . $this->User->GroupID . '/' . $type . '.pdf';
        $relativePath = PUBLIC_UPLOAD_RELATIVE_PATH . '/history/' . $this->User->GroupID . '/' . $type . '.pdf';
        $results = $this->readForIsakZDPReportGrid($rpcParams, '1', $rows, $sort, $order);

        $ltext = $LayersController->StringHelper->loadTemplate($GLOBALS['Templates'][21]['template'], ['pdf_data' => $results['rows']]);
        $ltext = '<page style="font-family: freeserif"><br />' . $ltext . '</page>';

        $printPdf = new PrintPdf();
        $printPdf->generateFromHtml($ltext, $path, [], true);

        $return['pdf_report_file'] = $relativePath;

        return $return;
    }
}
