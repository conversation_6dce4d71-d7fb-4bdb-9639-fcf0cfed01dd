<?php

namespace TF\Engine\APIClasses\SubsidiesWizard;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\ExportToExcelClass;
use TF\Engine\Kernel\PrintPdf;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbForIsak\UserDbForIsakController;
use TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController;

/**
 * @rpc-module SubsidiesWizard
 *
 * @rpc-service-id for-isak-diff-allowable-final-grid
 */
class ForIsakDiffAllowableFinalGrid extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'read'],
                'validators' => [
                    'params' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'initForIsakDiffAllowableFinal' => ['method' => [$this, 'initForIsakDiffAllowableFinal']],
            'exportGridToXLS' => ['method' => [$this, 'exportGridToXLS']],
            'exportGridToPDF' => ['method' => [$this, 'exportGridToPDF']],
        ];
    }

    /**
     * Reads SEPP compatiable plots.
     *
     * @api-method read
     *
     * @param array $params {
     *                      #item integer layer_id
     *                      #item string layer_name
     *                      #item boolean export2document
     *                      #item array filterObj {
     *                      #item string prc_name
     *                      #item integer culture
     *                      #item string comment
     *                      #item boolean edited
     *                      #item boolean sepp
     *                      }
     *                      }
     * @param string $page
     * @param string $rows
     * @param ?string $sort
     * @param ?string $order
     *
     * @return array {
     *               #item integer total
     *               #item array rows {
     *               #item string prc_name,
     *               #item string cropname,
     *               #item float area,
     *               #item float inside_area,
     *               #item float outside_area,
     *               #item string sepp
     *               #item string edited
     *               #item string comment
     *               }
     *               #item array footer {
     *               #item string prc_name,
     *               #item string area,
     *               #item string outside_area,
     *               #item string inside_area
     *               }
     *               #item array column_headers {
     *               #item string
     *               }
     *               }
     */
    public function read(array $params, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $return = [
            'total' => 0,
            'rows' => [],
            'footer' => [],
        ];

        $layer_id = $params['layer_id'];
        $layer_name = $params['layer_name'];
        $request = $params['filterObj'];

        $UserDbForIsakController = new UserDbForIsakController($this->User->Database);
        $PlotsDbController = new UserDbPlotsController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);
        $FarmingController = new FarmingController('Farming');
        $LayersController = new LayersController('Layers');

        $tableExist = $UserDbController->getTableNameExist($layer_name);
        if (!$tableExist) {
            return $return;
        }

        $options = [
            'tablename' => $layer_name,
            'return' => ['ST_Extent(geom) as extent'],
        ];

        $extent_result = $UserDbController->getItemsByParams($options);

        // get FOR ISAK extent and transform it
        $maxextent = $extent_result[0]['extent'];
        $maxextent = str_replace('BOX(', '', $maxextent);
        $maxextent = str_replace(')', '', $maxextent);
        $maxextent = str_replace(' ', ',', $maxextent);

        if ('' == $maxextent) {
            return $return;
        }

        $forIsakLayerData = $LayersController->getLayers([
            'return' => ['t.id'],
            'where' => [
                'table_name' => ['column' => 't.table_name', 'compare' => '=', 'value' => $layer_name],
            ],
        ]);
        $layerId = $forIsakLayerData[0]['id'];
        $seppView = 'sepp_for_isak_' . $layerId;

        if (!$UserDbController->getViewNameExists($seppView)) {
            $UserDbController->createSEPPReportView($layerId);
        }

        $options = [
            'tablename' => $layer_name,
            // 'custom_counter' => 'COUNT(DISTINCT(gid)), SUM(area) as total_area, SUM(outside_area) as total_outside_area, SUM(inside_area) as total_inside_area',
            'return' => [
                'count(*) OVER () as total_count',
                'sum(round(ST_Area (for_isak.geom) :: NUMERIC / 10000, 4 )) OVER () as total_area',
                'sum(CASE
                WHEN (
                round(((ST_Area (for_isak.geom) :: NUMERIC ) - (ST_Area (ST_Difference (st_buffer(for_isak.geom, 0.0001), st_buffer(ST_Union (A .geom),0.0001) ) ) :: NUMERIC ) ) :: NUMERIC / 10000, 4 ) ) ISNULL THEN
                round(ST_Area (for_isak.geom) :: NUMERIC / 10000, 4 ) ELSE
                round(ST_Area (ST_Difference (st_buffer(for_isak.geom, 0.0001), st_buffer(ST_Union (A .geom),0.0001) ) ) :: NUMERIC / 10000, 4 ) END) OVER () as total_outside_area',
                'sum(CASE
                WHEN (
                round(((ST_Area (for_isak.geom) :: NUMERIC ) - (ST_Area (ST_Difference (st_buffer(for_isak.geom, 0.0001), st_buffer(ST_Union (A .geom),0.0001) ) ) :: NUMERIC ) ) :: NUMERIC / 10000, 4 ) ) ISNULL THEN
                0
                ELSE
                round(((ST_Area (for_isak.geom) :: NUMERIC ) - (ST_Area (ST_Difference (st_buffer(for_isak.geom, 0.0001), st_buffer(ST_Union (A .geom),0.0001) ) ) :: NUMERIC ) ) :: NUMERIC / 10000, 4 ) END) OVER () as total_inside_area',
                'for_isak.prc_name', 'ST_AsText (for_isak.geom) AS st_astext', 'gid', 'sepp',
                'round(ST_Area (for_isak.geom) :: NUMERIC / 10000, 4 ) AS area',
                'CASE
                WHEN (
                round(((ST_Area (for_isak.geom) :: NUMERIC ) - (ST_Area (ST_Difference (st_buffer(for_isak.geom, 0.0001), st_buffer(ST_Union (A .geom),0.0001) ) ) :: NUMERIC ) ) :: NUMERIC / 10000, 4 ) ) ISNULL THEN
                round(ST_Area (for_isak.geom) :: NUMERIC / 10000, 4 ) ELSE
                round(ST_Area (ST_Difference (st_buffer(for_isak.geom, 0.0001), st_buffer(ST_Union (A .geom),0.0001) ) ) :: NUMERIC / 10000, 4 ) END AS outside_area',
                'CASE
                WHEN (
                round(((ST_Area (for_isak.geom) :: NUMERIC ) - (ST_Area (ST_Difference (st_buffer(for_isak.geom, 0.0001), st_buffer(ST_Union (A .geom),0.0001) ) ) :: NUMERIC ) ) :: NUMERIC / 10000, 4 ) ) ISNULL THEN
                0
                ELSE
                round(((ST_Area (for_isak.geom) :: NUMERIC ) - (ST_Area (ST_Difference (st_buffer(for_isak.geom, 0.0001), st_buffer(ST_Union (A .geom),0.0001) ) ) :: NUMERIC ) ) :: NUMERIC / 10000, 4 ) END AS inside_area',
                'for_isak.cropcode',
                'for_isak.cropname',
                'for_isak.edited',
                'for_isak.comment',
            ],
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
            'group' => 'for_isak.gid',
            'extent' => $maxextent,
            'seppView' => $seppView,
        ];

        if (strlen($request['prc_name'])) {
            $options['where']['prc_name'] = ['column' => 'prc_name', 'compare' => 'ILIKE', 'prefix' => 'for_isak', 'value' => $request['prc_name']];
        }
        if (strlen($request['culture'])) {
            $options['where']['cropcode'] = ['column' => 'cropcode', 'compare' => '=', 'value' => $request['culture']];

            if ('null' == $request['culture']) {
                $options['where']['cropcode'] = ['column' => 'cropcode', 'compare' => 'IS', 'value' => 'NULL'];
            }
        }
        if (strlen($request['comment'])) {
            $options['where']['comment'] = ['column' => 'comment', 'compare' => 'ILIKE', 'prefix' => 'for_isak', 'value' => $request['comment']];
        }
        if (strlen($request['edited']) && ('1' == $request['edited'] || '0' == $request['edited'])) {
            $options['where']['edited'] = ['column' => 'edited', 'compare' => '=', 'prefix' => 'for_isak', 'value' => $request['edited']];
        }
        if (strlen($request['sepp']) && ('1' == $request['sepp'] || '0' == $request['sepp'])) {
            $options['where']['sepp'] = ['column' => 'sepp', 'compare' => '=', 'prefix' => 'for_isak', 'value' => $request['sepp']];
        }

        if ($params['export2document'] && true == $params['export2document']) {
            unset($options['offset'], $options['limit']);
        }

        // $counter = $UserDbForIsakController->getForIsakDiffAllowableFinalData($options, true, false);

        $data = $UserDbForIsakController->getForIsakDiffAllowableFinalData($options, false, false);
        $dataCount = count($data);

        if (0 == $data[0]['total_count']) {
            return $return;
        }

        $total_area = $data[0]['total_area'];
        $total_outside_area = $data[0]['total_outside_area'];
        $total_inside_area = $data[0]['total_inside_area'];
        // total variables
        $total_area_per_page = 0;
        $total_ouside_area_per_page = 0;
        $total_inside_area_per_page = 0;

        // iterate results
        for ($i = 0; $i < $dataCount; $i++) {
            // add to total
            $total_area_per_page += $data[$i]['area'];
            $total_ouside_area_per_page += $data[$i]['outside_area'];
            $total_inside_area_per_page += $data[$i]['inside_area'];
        }

        // if the export document flag is set
        if ($params['export2document'] && true == $params['export2document']) {
            $printData = [];
            for ($i = 0; $i < $dataCount; $i++) {
                $printData[$i] = [
                    'prc_name' => $data[$i]['prc_name'],
                    'cropname' => $data[$i]['cropname'],
                    'area' => $data[$i]['area'],
                    'inside_area' => $data[$i]['inside_area'],
                    'outside_area' => $data[$i]['outside_area'],
                    'sepp' => ($data[$i]['sepp']) ? 'Да' : 'Не',
                    'edited' => ($data[$i]['edited']) ? 'Да' : 'Не',
                    'comment' => $data[$i]['comment'],
                ];
            }
            $column_headers = [
                'prc_name' => 'Име',
                'cropname' => 'Култура',
                'area' => 'Площ (ха)',
                'inside_area' => 'Площ в "Допустим слой"(ха)',
                'outside_area' => 'Площ извън "Допустим слой"(ха)',
                'sepp' => 'Заявен',
                'edited' => 'За редактиране',
                'comment' => 'Коментар',
            ];

            return [
                'rows' => $printData,
                'column_headers' => $column_headers,
                'footer' => [
                    'cropname' => 'Общо:',
                    'area' => $total_area,
                    'outside_area' => $total_outside_area,
                    'inside_area' => $total_inside_area,
                ],
            ];
        }

        return [
            'total' => $data[0]['total_count'],
            'rows' => $data,
            'footer' => [
                [
                    'cropname' => '<b>Общо за стр.</b>',
                    'area' => '<b>' . number_format($total_area_per_page, 4, '.', '') . '</b>',
                    'inside_area' => '<b>' . number_format($total_inside_area_per_page, 4, '.', '') . '</b>',
                    'outside_area' => '<b>' . number_format($total_ouside_area_per_page, 4, '.', '') . '</b>',
                ],
                [
                    'cropname' => '<b>Общо</b>',
                    'area' => '<b>' . number_format($total_area, 4, '.', '') . '</b>',
                    'inside_area' => '<b>' . number_format($total_inside_area, 4, '.', '') . '</b>',
                    'outside_area' => '<b>' . number_format($total_outside_area, 4, '.', '') . '</b>',
                ],
            ],
        ];
    }

    /**
     * Creates sepp view table and updates map file.
     *
     * @api-method initForIsakDiffAllowableFinal
     *
     * @param array $params {
     *                      #item string layer_name
     *                      #item string layer_id
     *                      }
     *
     * @return array
     */
    public function initForIsakDiffAllowableFinal($params)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $LayersController = new LayersController('Layers');
        $UserDbForIsakController = new UserDbForIsakController($this->User->Database);

        // check if table exists
        $tableExist = $UserDbController->getTableNameExist($params['layer_name']);
        if (!$tableExist) {
            return [];
        }

        $options = [
            'tablename' => $params['layer_name'],
            'return' => ['ST_Extent(geom) as extent'],
        ];

        $extent_result = $UserDbController->getItemsByParams($options);

        // get FOR ISAK extent and transform it
        $maxextent = $extent_result[0]['extent'];
        $maxextent = str_replace('BOX(', '', $maxextent);
        $maxextent = str_replace(')', '', $maxextent);
        $maxextent = str_replace(' ', ',', $maxextent);

        $forIsakLayerData = $LayersController->getLayers([
            'return' => ['t.id'],
            'where' => [
                'table_name' => ['column' => 't.table_name', 'compare' => '=', 'value' => $params['layer_name']],
            ],
        ]);
        $layerId = $forIsakLayerData[0]['id'];
        $seppView = 'sepp_for_isak_' . $params['layer_id'];

        if (!$UserDbController->getViewNameExists($seppView)) {
            $UserDbController->createSEPPReportView($layerId);
        }

        $options = [
            'tablename' => $params['layer_name'],
            'return' => [
                'for_isak.gid AS gid',
                'CASE WHEN ST_Difference(st_buffer(for_isak.geom, 0.00001), ST_Union(st_buffer(a.geom,0.00001))) IS NULL THEN st_buffer(for_isak.geom, 0.00001) ELSE ST_Difference(st_buffer(for_isak.geom, 0.00001), ST_Union(st_buffer(a.geom,0.00001))) END AS geom',
            ],
            'group' => 'for_isak.gid',
            'extent' => $maxextent,
            'seppView' => $seppView,
        ];

        $query = $UserDbForIsakController->getForIsakDiffAllowableFinalData($options, false, true);

        $options = [];
        $options['database'] = $this->User->Database;
        $options['user_id'] = $this->User->GroupID;
        $LayersController->generateMapFile($options);

        $mapFileData = [];

        $color1 = '000000';
        $color2 = 'ff0000';
        $mapFileData['layername'] = 'for_isak_diff_allowable_final';
        $mapFileData['maxextent'] = $extent_result[0]['extent'];
        $mapFileData['host'] = DEFAULT_DB_HOST;
        $mapFileData['dbname'] = $this->User->Database;
        $mapFileData['username'] = DEFAULT_DB_USERNAME;
        $mapFileData['password'] = DEFAULT_DB_PASSWORD;
        $mapFileData['port'] = DEFAULT_DB_PORT;
        $mapFileData['query'] = "({$query}) as subquery using unique gid using srid=32635";
        $mapFileData['gid'] = 'gid';
        $mapFileData['transparency'] = '100';
        $mapFileData['classes'][0]['name'] = 'for_isak_diff_allowable_final';
        $mapFileData['classes'][0]['symbol']['name'] = 'hatch_sym';
        $mapFileData['classes'][0]['symbol']['size'] = 20;
        $mapFileData['classes'][0]['symbol']['angle'] = 45;
        $mapFileData['classes'][0]['symbol']['color'] = hexdec(substr($color2, 0, 2)) . ' ' . hexdec(substr($color2, 2, 2)) . ' ' . hexdec(substr($color2, 4, 2));

        $ltext = $LayersController->StringHelper->loadTemplate($GLOBALS['Templates'][2]['template'], $mapFileData);

        @unlink(WMS_MAP_PATH . $this->User->GroupID . '_layer_for_isak_diff_allowable_final.map');
        $fp = fopen(WMS_MAP_PATH . $this->User->GroupID . '_layer_for_isak_diff_allowable_final.map', 'w');
        $res = fwrite($fp, $ltext);
        fclose($fp);
    }

    /**
     * exports plots data in xls format.
     *
     * @api-method exportGridToXLS
     *
     * @param array $rpcParams {
     *                         #item integer layer_id
     *                         #item string layer_name
     *                         #item array filterObj {
     *                         #item string prc_name
     *                         #item integer culture
     *                         #item string comment
     *                         #item boolean edited
     *                         #item boolean sepp
     *                         }
     *                         }
     * @param string $rows
     * @param string $sort
     * @param string $order
     *
     * @return array {
     *               #item string exl_report_file
     *               }
     */
    public function exportGridToXLS($rpcParams, $rows = '50', $sort = 'id', $order = 'asc')
    {
        $rpcParams['export2document'] = true;
        $type = 'sepp';

        $type = str_replace([' ', '.'], '_', $type);

        $results = $this->read($rpcParams, '1', $rows, $sort, $order);

        $time = strtotime(date('Y-m-d H:i:s'));
        $filename = PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID . '/' . $type . $time . '.xlsx';

        if (!file_exists(PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID)) {
            mkdir(PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID, 0777);
        }

        $exportExcelDoc = new ExportToExcelClass();
        $exportExcelDoc->export($results['rows'], $results['column_headers'], [$results['footer']]);
        $exportExcelDoc->saveFile($filename);

        return [
            'exl_report_file' => 'files/uploads/export/' . $this->User->UserID . '/' . $type . $time . '.xlsx',
        ];
    }

    /**
     * exports plots data in pdf format.
     *
     * @api-method exportGridToPDF
     *
     * @param array $rpcParams {
     *                         #item integer layer_id
     *                         #item string layer_name
     *                         #item array filterObj {
     *                         #item string prc_name
     *                         #item integer culture
     *                         #item string comment
     *                         #item boolean edited
     *                         #item boolean sepp
     *                         }
     *                         }
     * @param string $rows
     * @param string $sort
     * @param string $order
     *
     * @return array {
     *               #item string pdf_report_file
     *               }
     */
    public function exportGridToPDF($rpcParams = '', $rows = '50', $sort = 'id', $order = 'asc')
    {
        $LayersController = new LayersController('Layers');

        $rpcParams['export2document'] = true;
        $type = 'sepp';

        $type = str_replace([' ', '.'], '_', $type);

        if (!is_dir(PUBLIC_UPLOAD_RELATIVE_PATH . '/history/' . $this->User->GroupID)) {
            mkdir(PUBLIC_UPLOAD_RELATIVE_PATH . '/history/' . $this->User->GroupID);
        }

        $path = PUBLIC_UPLOAD_HISTORY . '/' . $this->User->GroupID . '/' . $type . '.pdf';
        $relativePath = PUBLIC_UPLOAD_RELATIVE_PATH . '/history/' . $this->User->GroupID . '/' . $type . '.pdf';
        $results = $this->read($rpcParams, '1', $rows, $sort, $order);

        $ltext = $LayersController->StringHelper->loadTemplate($GLOBALS['Templates'][22]['template'], ['pdf_data' => $results['rows']]);
        $ltext = '<page style="font-family: freeserif"><br />' . $ltext . '</page>';

        $printPdf = new PrintPdf();
        $printPdf->generateFromHtml($ltext, $path, [], true);

        $return['pdf_report_file'] = $relativePath;

        return $return;
    }
}
