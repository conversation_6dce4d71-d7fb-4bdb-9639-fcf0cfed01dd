<?php

namespace TF\Engine\APIClasses\SubsidiesWizard;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\ExportToExcelClass;
use TF\Engine\Kernel\PrintPdf;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbForIsak\UserDbForIsakController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * @rpc-module SubsidiesWizard
 *
 * @rpc-service-id for-isak-report-pndp-grid
 */
class ForIsakPNDPReportGrid extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readForIsakPNDPReportGrid'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'exportGridToXLS' => ['method' => [$this, 'exportGridToXLS']],
            'exportGridToPDF' => ['method' => [$this, 'exportGridToPDF']],
        ];
    }

    /**
     * Reads plots compatiable with pndp.
     *
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item string layer_name
     *                         #item boolean export2document
     *                         #item array filterObj {
     *                         #item string prc_name
     *                         #item integer ekate
     *                         #item string cropname
     *                         }
     *                         }
     * @param int|string $page pagination parameter
     * @param int|string $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array {
     *               #item integer total
     *               #item array rows {
     *               #item string prc_name,
     *               #item float area,
     *               #item boolean pndp
     *               #item boolean edited
     *               #item string comment
     *               #item string ekatte
     *               #item boolean unallowed_crop_type
     *               #item string cropcode,
     *               #item string cropname,
     *               #item integer gid,
     *               #item string is_active_culture,
     *               #item string is_requested,
     *               #item string st_astext,
     *               }
     *               #item array footer {
     *               #item string prc_name,
     *               #item string area,
     *               }
     *               #item array column_headers {
     *               #item string
     *               }
     *               }
     */
    public function readForIsakPNDPReportGrid(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $return = [
            'total' => 0,
            'rows' => [],
            'footer' => [],
        ];

        if (!$rpcParams['layer_name']) {
            return $return;
        }

        // init all needed controllers
        $UserDbForIsakController = new UserDbForIsakController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        $LayersController = new LayersController('Layers');
        $FarmingController = new FarmingController('Farming');
        $UserDbForIsakController = new UserDbForIsakController($this->User->Database);

        $layer_name = $rpcParams['layer_name'];
        $is_schema = $rpcParams['is_schema'];
        $request = $rpcParams['filterObj'];

        // check if table exists
        $tableExist = $UserDbController->getTableNameExist($layer_name);
        if (!$tableExist) {
            return $return;
        }

        $options = [
            'tablename' => $layer_name,
            'return' => ['ST_Extent(geom) as extent'],
        ];

        $extent_result = $UserDbController->getItemsByParams($options);

        // get FOR ISAK extent and transform it
        $maxextent = $extent_result[0]['extent'];
        $maxextent = str_replace('BOX(', '', $maxextent);
        $maxextent = str_replace(')', '', $maxextent);
        $maxextent = str_replace(' ', ',', $maxextent);

        if ('is_requested' == $sort) {
            $sort = 'pndp';
        }

        $options = [
            'tablename' => $layer_name,
            'custom_counter' => 'COUNT(DISTINCT(gid)), SUM(area) as total_area',
            'return' => [
                'prc_name', 'ST_AsText(geom) as st_astext', 'gid', 'cropcode',
                'round((ST_Area(geom)/10000)::numeric, 4) as area',
                'edited', 'comment', 'ekatte', 'pndp', 'cropname',
            ],
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
            'group' => 'gid',
        ];

        if (strlen($request['prc_name'])) {
            $options['where']['prc_name'] = ['column' => 'prc_name', 'compare' => 'ILIKE', 'value' => $request['prc_name']];
        }
        if (strlen($request['cropname'])) {
            $options['where']['cropcode'] = ['column' => 'cropcode', 'compare' => '=', 'value' => $request['cropname']];

            if ('null' == $request['cropname']) {
                $options['where']['cropcode'] = ['column' => 'cropcode', 'compare' => 'IS', 'value' => 'NULL'];
            }
        }
        if (strlen($request['ekatte'])) {
            $options['where']['ekatte'] = ['column' => 'ekatte', 'compare' => '=', 'value' => $request['ekatte']];
        }

        if ($rpcParams['export2document'] && true == $rpcParams['export2document']) {
            unset($options['offset'], $options['limit']);
        }

        $counter = $UserDbForIsakController->getForIsakPNDReportData($options, true, false);
        $total_area = $counter[0]['total_area'];

        if (0 == $counter[0]['count']) {
            return $return;
        }

        $data = $UserDbForIsakController->getForIsakPNDReportData($options, false, false);
        $dataCount = count($data);
        $total_area_per_page = 0;
        // iterate results
        for ($i = 0; $i < $dataCount; $i++) {
            if (null == $data[$i]['ekatte'] || empty($data[$i]['ekatte'])) {
                $data[$i]['ekatte'] = $UsersController->getEkatteName($data[$i]['ekatte']);
            } else {
                $data[$i]['ekatte'] = $UsersController->getEkatteName($data[$i]['ekatte']) . '(' . $data[$i]['ekatte'] . ')';
            }

            $data[$i]['unallowed_crop_type'] = false;
            $data[$i]['is_requested'] = ($data[$i]['pndp']) ? 'Да' : 'Не';

            $data[$i]['is_active_culture'] = $GLOBALS['Farming']['crops'][$data[$i]['cropcode']]['is_active'];

            if (in_array($data[$i]['cropcode'], $GLOBALS['Farming']['pndp_unallowed_cropcodes'])) {
                $data[$i]['unallowed_crop_type'] = true;
            }
            // add to total
            $total_area_per_page += $data[$i]['area'];
        }

        // if the export to document flag is set
        if ($rpcParams['export2document'] && true == $rpcParams['export2document']) {
            $printData = [];
            for ($i = 0; $i < $dataCount; $i++) {
                $printData[$i] = [
                    'prc_name' => $data[$i]['prc_name'],
                    'ekatte' => $data[$i]['ekatte'],
                    'cropname' => $data[$i]['cropname'],
                    'area' => $data[$i]['area'],
                    'is_requested' => $data[$i]['is_requested'],
                    'edited' => ($data[$i]['edited']) ? 'Да' : 'Не',
                    'comment' => $data[$i]['comment'],
                ];
            }
            $column_headers = [
                'prc_name' => 'Име',
                'ekatte' => 'Землище',
                'cropname' => 'Култура',
                'area' => 'Площ(ха)',
                'is_requested' => 'Заявено',
                'edited' => 'За редактиране',
                'comment' => 'Коментар',
            ];

            return [
                'rows' => $printData,
                'column_headers' => $column_headers,
                'footer' => [
                    'prc_name' => 'Общо:',
                    'area' => $total_area,
                ],
            ];
        }
        $return = [
            'total' => $counter[0]['count'],
            'rows' => $data,
            'footer' => [
                [
                    'prc_name' => '<b>Общо за стр.</b>',
                    'area' => '<b>' . $total_area_per_page . '</b>',
                ],
                [
                    'prc_name' => '<b>Общо</b>',
                    'area' => '<b>' . $total_area . '</b>',
                ],
            ],
        ];

        return $return;
    }

    /**
     * Exports pndp plots data in xls format.
     *
     * @api-method exportGridToPDF
     *
     * @param array $rpcParams
     *                         {
     *                         #item string layer_name
     *                         #item boolean export2document
     *                         #item array filterObj {
     *                         #item string prc_name
     *                         #item integer ekate
     *                         #item string cropname
     *                         }
     *                         }
     * @param int|string $page pagination parameter
     * @param int|string $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array {
     *               #item string xls_report_file
     *}
     */
    public function exportGridToXLS($rpcParams, $rows = '50', $sort = 'id', $order = 'asc')
    {
        $rpcParams['export2document'] = true;
        $type = 'pndp';

        $type = str_replace([' ', '.'], '_', $type);

        $results = $this->readForIsakPNDPReportGrid($rpcParams, '1', $rows, $sort, $order);

        $time = strtotime(date('Y-m-d H:i:s'));
        $filename = PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID . '/' . $type . $time . '.xlsx';

        if (!file_exists(PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID)) {
            mkdir(PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID, 0777);
        }

        $exportExcelDoc = new ExportToExcelClass();
        $exportExcelDoc->export($results['rows'], $results['column_headers'], [$results['footer']]);
        $exportExcelDoc->saveFile($filename);

        return [
            'exl_report_file' => 'files/uploads/export/' . $this->User->UserID . '/' . $type . $time . '.xlsx',
        ];
    }

    /**
     * Exports pndp plots data in pdf format.
     *
     * @api-method exportGridToPDF
     *
     * @param array $rpcParams
     *                         {
     *                         #item string layer_name
     *                         #item boolean export2document
     *                         #item array filterObj {
     *                         #item string prc_name
     *                         #item integer ekate
     *                         #item string cropname
     *                         }
     *                         }
     * @param int|string $page pagination parameter
     * @param int|string $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array {
     *               #item string pdf_report_file
     *}
     */
    public function exportGridToPDF($rpcParams, $rows = '50', $sort = 'id', $order = 'asc')
    {
        $LayersController = new LayersController('Layers');

        $rpcParams['export2document'] = true;
        $type = 'pndp';

        $type = str_replace([' ', '.'], '_', $type);

        if (!is_dir(PUBLIC_UPLOAD_RELATIVE_PATH . '/history/' . $this->User->GroupID)) {
            mkdir(PUBLIC_UPLOAD_RELATIVE_PATH . '/history/' . $this->User->GroupID);
        }

        $path = PUBLIC_UPLOAD_HISTORY . '/' . $this->User->GroupID . '/' . $type . '.pdf';
        $relativePath = PUBLIC_UPLOAD_RELATIVE_PATH . '/history/' . $this->User->GroupID . '/' . $type . '.pdf';
        $results = $this->readForIsakPNDPReportGrid($rpcParams, '1', $rows, $sort, $order);

        $ltext = $this->LayersController->StringHelper->loadTemplate($GLOBALS['Templates'][23]['template'], ['pdf_data' => $results['rows']]);

        $printPdf = new PrintPdf();
        $printPdf->generateFromHtml($ltext, $path, [], true);

        $return['pdf_report_file'] = $relativePath;

        return $return;
    }
}
