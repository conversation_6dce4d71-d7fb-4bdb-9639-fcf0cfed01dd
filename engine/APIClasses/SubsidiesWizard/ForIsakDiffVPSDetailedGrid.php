<?php

namespace TF\Engine\APIClasses\SubsidiesWizard;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\ExportToExcelClass;
use TF\Engine\Kernel\PrintPdf;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbForIsak\UserDbForIsakController;
use TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * @rpc-module SubsidiesWizard
 *
 * @rpc-service-id for-isak-diff-vps-detailed-grid
 */
class ForIsakDiffVPSDetailedGrid extends TRpcApiProvider
{
    private $UserDbController = false;
    private $UserDbZPlotsController = false;
    private $LayersController = false;
    private $UserDbForIsakController = false;
    private $UsersController = false;
    private $FarmingController = false;
    private $UserDbPlotsController = false;

    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readForIsakDiffVPSDetailedGrid'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'initForIsakDiffVPS' => ['method' => [$this, 'initForIsakDiffVPS']],
            'exportGridToXLS' => ['method' => [$this, 'exportGridToXLS']],
            'exportGridToPDF' => ['method' => [$this, 'exportGridToPDF']],
        ];
    }

    /**
     * Reads plots from specified vps layer.
     *
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item string layer_name
     *                         #item string vps_layer_name
     *                         #item array filterObj {
     *                         #item string prc_name
     *                         #item string cropname
     *                         #item string comment
     *                         #item boolean edited
     *                         }
     *                         }
     * @param int $page pagination parameter
     * @param int $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array {
     *               #item integer total
     *               #item array rows {
     *               #item string prc_name,
     *               #item float area,
     *               #item string comment
     *               #item string cropcode
     *               #item string cropname
     *               #item boolean edited
     *               #item string gid
     *               #item string vps_type
     *               #item float vps_inside_area,
     *               #item float vps_outside_area,
     *               #item string st_astext
     *               #item boolean requested_in_natura2000
     *               #item boolean inside_natura_2000
     *               #item boolean compatiable_cropcode
     *               }
     *               #item array footer {
     *               #item string cropname,
     *               #item string area,
     *               }
     *               }
     */
    public function readForIsakDiffVPSDetailedGrid(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        // creating a default empty return
        $empty_return = [
            'rows' => [],
            'footer' => [],
            'total' => 0,
        ];

        if (!$rpcParams['layer_name'] || !$rpcParams['vps_layer_name']) {
            return $empty_return;
        }

        // init all needed controllers
        $UserDbForIsakController = new UserDbForIsakController($this->User->Database);
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        $LayersController = new LayersController('Layers');
        $FarmingController = new FarmingController('Farming');

        $layer_name = $rpcParams['layer_name'];
        $vps_layer_name = $rpcParams['vps_layer_name'];
        $request = $rpcParams['filterObj'];

        $_POST['data'] = [
            'prc_name' => $request['prc_name'],
            'cropcode' => $request['cropcode'],
            'vps_type' => $request['vps_type'],
            'ekatte' => $request['ekatte'],
        ];
        // clean old results
        $results = [];
        $return = [];

        // creating a default empty return
        $empty_return = [
            'rows' => [],
            'footer' => [],
            'total' => 0,
        ];

        $tableExists = $UserDbController->getTableNameExist($layer_name);
        if (!$tableExists) {
            return $empty_return;
        }

        $options = [
            'tablename' => $layer_name,
            'return' => ['ST_Extent(geom) as extent'],
        ];

        $extent_result = $UserDbController->getItemsByParams($options);

        // get FOR ISAK extent and transform it
        $maxextent = $extent_result[0]['extent'];
        $maxextent = str_replace('BOX(', '', $maxextent);
        $maxextent = str_replace(')', '', $maxextent);
        $maxextent = str_replace(' ', ',', $maxextent);

        $options = [
            'tablename' => $layer_name,
            'vps_tablename' => $vps_layer_name,
            'return' => [
                'gid', 'prc_name', 'area', 'outside_area', 'cropcode', 'edited', 'comment', 'cropname', 'vps_type',
            ],
            'where' => [
                'ekatte' => ['column' => 'ekatte', 'compare' => '=', 'value' => $request['ekatte']],
                'cropcode' => ['column' => 'cropcode', 'compare' => '=', 'value' => $request['cropname']],
                'prc_name' => ['column' => 'prc_name', 'compare' => 'ILIKE', 'value' => $request['prc_name']],
                'edited' => ['column' => 'edited', 'compare' => '=', 'value' => $request['edited']],
            ],
            'dblink_where' => "geom && ST_MakeEnvelope({$maxextent})",
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
        ];

        if ($rpcParams['export2document'] && true == $rpcParams['export2document']) {
            unset($options['offset'], $options['limit']);
        }

        $results = $UserDbForIsakController->getForIsakDiffVPSDetailedData($options, false);

        unset($options['limit'], $options['offset'], $options['sort'], $options['order']);

        $counter = $UserDbForIsakController->getForIsakDiffVPSDetailedData($options, false);

        $count = count($counter);
        $vps_search_type = 0;
        switch ($vps_layer_name) {
            case 'layer_vps_merg':
                $vps_search_type = 1;

                break;
            case 'layer_vps_gaski_chervenogushi':
                $vps_search_type = 2;

                break;
            case 'layer_vps_gaski_zimni':
                $vps_search_type = 3;

                break;
            case 'layer_vps_livaden_blatar':
                $vps_search_type = 4;

                break;
            case 'layer_vps_orli_leshoyadi':
                $vps_search_type = 5;

                break;
            default:
                $vps_search_type = 0;

                break;
        }

        $tоtalAreaPerPage = 0;

        if (0 != count($results)) {
            $vps_results = [];
            $j = 0;
            for ($i = 0; $i < count($results); $i++) {
                $results[$i]['vps_type'] = ($results[$i]['vps_type']) ? $results[$i]['vps_type'] : 0;

                $vps_results[$i] = [];
                $vps_results[$i]['gid'] = $results[$i]['gid'];
                $vps_results[$i]['prc_name'] = $results[$i]['prc_name'];
                $vps_results[$i]['cropname'] = $results[$i]['cropname'];
                $vps_results[$i]['edited'] = $results[$i]['edited'];
                $vps_results[$i]['vps_type'] = $GLOBALS['Farming']['vps_schema_types'][$results[$i]['vps_type']]['name'];
                $vps_results[$i]['area'] = $results[$i]['area'];
                $vps_results[$i]['vps_inside_area'] = $results[$i]['inside_area'];
                $vps_results[$i]['vps_outside_area'] = $results[$i]['outside_area'];
                $vps_results[$i]['comment'] = $results[$i]['comment'];
                $vps_results[$i]['st_astext'] = $results[$i]['st_astext'];
                $vps_results[$i]['requested_in_natura2000'] = (null != $results[$i]['natura_sitecode']);
                $vps_results[$i]['inside_natura_2000'] = ($results[$i]['natura_2000_inside_area'] > 0.005);
                if ($GLOBALS['Farming']['crop_vps_compatiable'][$results[$i]['cropcode']]) {
                    $crop_array = $GLOBALS['Farming']['crop_vps_compatiable'][$results[$i]['cropcode']];
                } else {
                    $crop_array = [];
                }

                $vps_results[$i]['compatiable_cropcode'] = in_array($vps_search_type, $crop_array);
                $tоtalAreaPerPage += $vps_results[$i]['area'];
            }
            $return['rows'] = $vps_results;
        } else {
            $return['rows'] = false;
        }

        // if the export to document flag is set
        if ($rpcParams['export2document'] && true == $rpcParams['export2document']) {
            $column_headers = [
                'prc_name' => 'Име',
                'cropname' => 'Култура',
                'vps_type' => 'Заявен във ВПС',
                'area' => 'Площ на парцела(ха)',
                'vps_inside_area' => 'Площ на парцела в даденото ВПС (ха)',
                'vps_outside_area' => 'Площ на парцела извън даденото ВПС (ха)',
                'edited' => 'За редактиране',
                'comment' => 'Коментар',
            ];

            $printData = [];
            for ($i = 0; $i < count($return['rows']); $i++) {
                $printData[$i] = [
                    'prc_name' => $vps_results[$i]['prc_name'],
                    'cropname' => $vps_results[$i]['cropname'],
                    'vps_type' => $vps_results[$i]['vps_type'],
                    'area' => $vps_results[$i]['area'],
                    'vps_inside_area' => $vps_results[$i]['vps_inside_area'],
                    'vps_outside_area' => $vps_results[$i]['vps_outside_area'],
                    'edited' => ($vps_results[$i]['edited']) ? 'Да' : 'Не',
                    'comment' => $vps_results[$i]['comment'],
                ];
            }

            return [
                'rows' => $printData,
                'column_headers' => $column_headers,
                'footer' => [
                    'prc_name' => 'Общо:',
                    'area' => $tоtalAreaPerPage,
                ],
            ];
        }

        $return['footer'][0]['cropname'] = '<b>Общо за страницата:<b/>';
        $return['footer'][0]['area'] = '<b>' . $tоtalAreaPerPage . '<b/>';
        $return['total'] = $count;

        return $return;
    }

    /**
     * Creates specified vps layer view table and updates map file.
     *
     * @api-method initForIsakDiffVPS
     *
     * @param string $layer_name
     * @param string $vps_layer_name
     */
    public function initForIsakDiffVPS($layer_name, $vps_layer_name)
    {
        $UserDbForIsakController = new UserDbForIsakController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);
        $LayersController = new LayersController('Layers');

        // check if table exists
        $tableExist = $UserDbController->getTableNameExist($layer_name);
        if (!$tableExist) {
            return [];
        }

        $options = [
            'tablename' => $layer_name,
            'return' => ['ST_Extent(geom) as extent'],
        ];

        $extent_result = $UserDbController->getItemsByParams($options);

        // get FOR ISAK extent and transform it
        $maxextent = $extent_result[0]['extent'];
        $maxextent = str_replace('BOX(', '', $maxextent);
        $maxextent = str_replace(')', '', $maxextent);
        $maxextent = str_replace(' ', ',', $maxextent);

        $options = [
            'tablename' => $layer_name,
            'layer' => $vps_layer_name,
            'return' => [
                'for_isak.gid AS gid',
                'CASE WHEN ST_Difference(for_isak.geom, ST_Union(layer.geom)) IS NULL THEN for_isak.geom ELSE ST_Difference(for_isak.geom, ST_Union(layer.geom)) END AS geom',
            ],
            'group' => 'for_isak.gid',
            'extent' => $maxextent,
        ];

        $query = $UserDbForIsakController->getForIsakDiffLayer($options, false, true);

        $options = [];
        $options['database'] = $this->User->Database;
        $options['user_id'] = $this->User->GroupID;
        $LayersController->generateMapFile($options);

        $mapFileData = [];

        $color1 = '000000';
        $color2 = 'ff0000';
        $mapFileData['layername'] = 'for_isak_diff_' . $vps_layer_name;
        $mapFileData['maxextent'] = $extent_result[0]['extent'];
        $mapFileData['host'] = DEFAULT_DB_HOST;
        $mapFileData['dbname'] = $this->User->Database;
        $mapFileData['username'] = DEFAULT_DB_USERNAME;
        $mapFileData['password'] = DEFAULT_DB_PASSWORD;
        $mapFileData['port'] = DEFAULT_DB_PORT;
        $mapFileData['query'] = "({$query}) as subquery using unique gid using srid=32635";
        $mapFileData['gid'] = 'gid';
        $mapFileData['transparency'] = '100';
        $mapFileData['classes'][0]['name'] = 'for_isak_diff_' . $vps_layer_name;
        $mapFileData['classes'][0]['symbol']['name'] = 'hatch_sym';
        $mapFileData['classes'][0]['symbol']['size'] = 20;
        $mapFileData['classes'][0]['symbol']['angle'] = 45;
        $mapFileData['classes'][0]['symbol']['color'] = hexdec(substr($color2, 0, 2)) . ' ' . hexdec(substr($color2, 2, 2)) . ' ' . hexdec(substr($color2, 4, 2));

        $ltext = $LayersController->StringHelper->loadTemplate($GLOBALS['Templates'][2]['template'], $mapFileData);

        @unlink(WMS_MAP_PATH . $this->User->GroupID . '_layer_for_isak_diff_' . $vps_layer_name . '.map');
        $fp = fopen(WMS_MAP_PATH . $this->User->GroupID . '_layer_for_isak_diff_' . $vps_layer_name . '.map', 'w');
        $res = fwrite($fp, $ltext);
        fclose($fp);
    }

    /**
     * Exports LFA plots data in xls format.
     *
     * @api-method exportGridToPDF
     *
     * @param array $rpcParams
     *                         {
     *                         #item string layer_name
     *                         #item array filterObj {
     *                         #item string prc_name
     *                         #item integer ekate
     *                         #item string comment
     *                         #item boolean edited
     *                         #item boolean lfa
     *                         #item integer nm_lfa
     *                         }
     *                         }
     * @param int $page pagination parameter
     * @param int $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array {
     *               #item string xls_report_file
     *}
     */
    public function exportGridToXLS($rpcParams = '', $rows = '50', $sort = 'id', $order = 'asc')
    {
        $rpcParams['export2document'] = true;
        $type = $rpcParams['vps_layer_name'];

        $type = str_replace([' ', '.'], '_', $type);

        $results = $this->readForIsakDiffVPSDetailedGrid($rpcParams, '1', $rows, $sort, $order);

        $time = strtotime(date('Y-m-d H:i:s'));
        $filename = PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID . '/' . $type . $time . '.xlsx';

        if (!file_exists(PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID)) {
            mkdir(PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID, 0777);
        }

        $exportExcelDoc = new ExportToExcelClass();
        $exportExcelDoc->export($results['rows'], $results['column_headers'], [$results['footer']]);
        $exportExcelDoc->saveFile($filename);

        return [
            'exl_report_file' => 'files/uploads/export/' . $this->User->UserID . '/' . $type . $time . '.xlsx',
        ];
    }

    /**
     * Exports LFA plots data in pdf format.
     *
     * @api-method exportGridToPDF
     *
     * @param array $rpcParams
     *                         {
     *                         #item string layer_name
     *                         #item array filterObj {
     *                         #item string prc_name
     *                         #item integer ekate
     *                         #item string comment
     *                         #item boolean edited
     *                         #item boolean lfa
     *                         #item integer nm_lfa
     *                         }
     *                         }
     * @param int $page pagination parameter
     * @param int $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array {
     *               #item string pdf_report_file
     *}
     */
    public function exportGridToPDF($rpcParams = '', $rows = '50', $sort = 'id', $order = 'asc')
    {
        $LayersController = new LayersController('Layers');

        $rpcParams['export2document'] = true;
        $type = $rpcParams['vps_layer_name'];
        $type = str_replace([' ', '.'], '_', $type);

        if (!is_dir(PUBLIC_UPLOAD_RELATIVE_PATH . '/history/' . $this->User->GroupID)) {
            mkdir(PUBLIC_UPLOAD_RELATIVE_PATH . '/history/' . $this->User->GroupID);
        }

        $path = PUBLIC_UPLOAD_HISTORY . '/' . $this->User->GroupID . '/' . $type . '.pdf';
        $relativePath = PUBLIC_UPLOAD_RELATIVE_PATH . '/history/' . $this->User->GroupID . '/' . $type . '.pdf';

        $results = $this->readForIsakDiffVPSDetailedGrid($rpcParams, '1', $rows, $sort, $order);

        $ltext = $LayersController->StringHelper->loadTemplate($GLOBALS['Templates'][38]['template'], ['pdf_data' => $results['rows']]);

        $ltext = '<page style="font-family: freeserif"><br />' . $ltext . '</page>';

        $printPdf = new PrintPdf();
        $printPdf->generateFromHtml($ltext, $path, ['orientation' => 'Landscape'], true);

        $return['pdf_report_file'] = $relativePath;

        return $return;
    }
}
