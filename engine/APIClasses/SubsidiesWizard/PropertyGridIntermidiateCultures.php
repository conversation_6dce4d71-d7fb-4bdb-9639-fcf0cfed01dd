<?php

namespace TF\Engine\APIClasses\SubsidiesWizard;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

/**
 * @rpc-module SubsidiesWizard
 *
 * @rpc-service-id pg-intermidiate-cultures
 */
class PropertyGridIntermidiateCultures extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readIntermediateCulturesPropertyGrid']],
            'savePropertyGridCultures' => ['method' => [$this, 'savePropertyGridCultures']],
        ];
    }

    /**
     * Read PropertyGridIntermediateCultures.
     *
     * @api-method read
     *
     * @param int $gid
     * @param string $layer_name
     *
     * @return array {
     *               #item integer total
     *               #item array rows {
     *               #item string name,
     *               #item string value,
     *               #item array editor{
     *               #item string type
     *               #item array options {
     *               #item string on
     *               #item string off
     *               },
     *               #item string group
     *               },
     *               #item string name,
     *               }
     *               }
     */
    public function readIntermediateCulturesPropertyGrid($layer_name, $gid)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $FarmingController = new FarmingController('Farming');
        $LayersController = new LayersController('Layers');

        $return = [];

        $weat_crops = $GLOBALS['Farming']['weat_crops'];
        $non_weat_crops = $GLOBALS['Farming']['non_weat_crops'];

        $rows = [];

        $options = [
            'tablename' => $layer_name,
            'return' => ['weat_crops', 'non_weat_crops'],
        ];

        if (isset($gid)) {
            $options['where']['gid'] = ['column' => 'gid', 'compare' => '=', 'value' => $gid];
        }

        $result = $UserDbController->getItemsByParams($options);

        $sWeatCrops = $result[0]['weat_crops'];
        $sNonWeatCrops = $result[0]['non_weat_crops'];

        $aWeatCrops = explode(',', str_replace(' ', '', $sWeatCrops));
        $aWeatCount = count($aWeatCrops);
        $aNonWeatCrops = explode(',', str_replace(' ', '', $sNonWeatCrops));
        $aNonWeatCount = count($aNonWeatCrops);

        // Житни култури
        foreach ($weat_crops as $key => $value) {
            $rows[] = [
                'name' => $value['crop_name'],
                'value' => 'Не',
                'editor' => ['type' => 'checkbox', 'options' => ['on' => 'Да', 'off' => 'Не']],
                'group' => 'Житни култури',
            ];
        }

        $rowsCount = count($rows);
        for ($i = 0; $i < $rowsCount; $i++) {
            for ($j = 0; $j < $aWeatCount; $j++) {
                $exist = array_search($aWeatCrops[$j], $rows[$i]);
                if ($exist) {
                    $rows[$i]['value'] = 'Да';
                }
            }
        }

        // Нежитни култури
        foreach ($non_weat_crops as $key => $value) {
            $rows[] = [
                'name' => $value['crop_name'],
                'value' => 'Не',
                'editor' => ['type' => 'checkbox', 'options' => ['on' => 'Да', 'off' => 'Не']],
                'group' => 'Нежитни култури',
            ];
        }

        $rowsCount = count($rows);
        for ($i = 0; $i < $rowsCount; $i++) {
            for ($j = 0; $j < $aNonWeatCount; $j++) {
                $exist = array_search($aNonWeatCrops[$j], $rows[$i]);
                if ($exist) {
                    $rows[$i]['value'] = 'Да';
                }
            }
        }

        $return['total'] = count($rows);
        $return['rows'] = $rows;

        return $return;
    }

    /**
     * Saves selected intermediate cultures to a plot.
     *
     * @api-method savePropertyGridCultures
     *
     * @param array $params{
     *                       #item array rows{
     *                       #item string value
     *                       #item string group
     *                       #item string name
     *                       }
     *                       #item string layer_name
     *                       #item integer gid
     *                       }
     */
    public function savePropertyGridCultures($params)
    {
        $UserDbController = new UserDbController($this->User->Database);

        $sWeatCrops = '';
        $sNonWeatCrops = '';

        $cultures = $params['rows'];
        $layer_name = $params['layer_name'];
        $gid = $params['gid'];

        $count = count($cultures);
        for ($i = 0; $i < $count; $i++) {
            if ('Да' == $cultures[$i]['value']) {
                if ('Житни култури' == $cultures[$i]['group']) {
                    $sWeatCrops .= $cultures[$i]['name'] . ', ';
                }
                if ('Нежитни култури' == $cultures[$i]['group']) {
                    $sNonWeatCrops .= $cultures[$i]['name'] . ', ';
                }
            }
        }

        if (strlen($sWeatCrops)) {
            $sWeatCrops = substr($sWeatCrops, 0, -2);
        }

        if (strlen($sNonWeatCrops)) {
            $sNonWeatCrops = substr($sNonWeatCrops, 0, -2);
        }
        $crop_short_type = '';
        $crop_short_type .= 'МК<br>
            <b>Житни:</b><br> ' . str_replace(',', '<br>', $sWeatCrops) . '<br>
            <b>Нежитни:</b><br> ' . str_replace(',', '<br>', $sNonWeatCrops) . '';

        $options['tablename'] = $layer_name;
        $options['where']['gid'] = $gid;
        $options['mainData'] = [
            'weat_crops' => $sWeatCrops,
            'non_weat_crops' => $sNonWeatCrops,
            'common_cultures' => true,
            'green_area_factor' => $GLOBALS['Farming']['crops_short_type']['is_intermediate_crop']['factor_enp'],
            'crop_short_type' => $crop_short_type,
        ];

        $UserDbController->editItem($options);
    }
}
