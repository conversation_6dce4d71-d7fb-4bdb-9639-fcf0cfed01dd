<?php

namespace TF\Engine\APIClasses\Payroll;

use DOMDocument;
use Prado\Prado;
use Prado\Web\Services\TRpcApiProvider;
use SimpleXMLElement;
use TF\Application\Common\Config;
use TF\Engine\Kernel\ExportToExcelClass;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\Payments\PaymentsController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Payroll Grid Export To Excel And Print.
 *
 * @rpc-module Payroll
 *
 * @rpc-service-id payroll-grid-export-print
 */
class PayrollGridExportAndPrint extends TRpcApiProvider
{
    private const RENT_NAT_PREFIX = 'nat-';
    private const PAID_RENT_NAT_PREFIX = 'paid-nat-';
    private const UNPAID_RENT_NAT_PREFIX = 'unpaid-nat-';
    private const OVERPAID_RENT_NAT_PREFIX = 'overpaid-nat-';
    public $renta_types = [];
    public $allOwnersForPayroll = [];
    public $arrayHelper;
    private $ekateData = [];
    private $footerArray = [];
    // Помощен масив, който пази всички уникални натури
    // за да може само те да са включени в експорта
    private $uniqueNatura = [
        'renta_nat_text' => [],
        'charged_renta_nat_text' => [],
        'paid_renta_by' => [],
        'paid_renta_nat' => [],
        'paid_renta_nat_by' => [],
        'paid_renta_nat_by_detailed' => [],
        'unpaid_renta_nat' => [],
        'unpaid_renta_nat_unit_value' => [],
        'over_paid_renta_nat' => [],
        'total_by_renta_nat' => [],
    ];

    // Помощен масив, който пази всички колони
    // които да бъдат сумирани при експорт
    private $summableColumns = [
        'Площ за рента',
        'Площ по договор (дка)',
        'Обработваема площ (дка)',
        'Площ за лично ползване (дка)',
        'Рента в лева',
        'Рента в евро',
        'Платена рента в лева',
        'Платена рента в евро',
        'Оставаща рента в лева',
        'Оставаща рента в евро',
        'Надплатена рента в лева',
        'Надплатена рента в евро',
    ];

    private $headers = [
        'ownerNames' => 'Собственик',
        'inheritorOf' => 'Наследник на',
        'ownerEgnEik' => 'ЕГН/ЕИК',
        'ownerPhone' => 'Телефон на собственик',
        'ownerIban' => 'IBAN на собственик',
        'repNames' => 'Представител',
        'repIban' => 'IBAN на представител',
        'contracts' => 'Договори',
        'rentPlace' => 'Място за получаване на рента',
        'plots' => 'Имоти',
        'plotDetailed' => 'Имоти - детайлно',
        'natsDetailed' => 'Натури - детайлно',
        'areaForRent' => 'Площ за рента',
        'contractArea' => 'Площ по договор (дка)',
        'arableArea' => 'Обработваема площ (дка)',
        'personalUseArea' => 'Площ за лично ползване (дка)',
        'rentMoney' => 'Рента в лева',
        'rentMoneyEuro' => 'Рента в евро',
        'paidRentMoney' => 'Платена рента в лева',
        'paidRentMoneyEuro' => 'Платена рента в евро',
        'unpaidRentMoney' => 'Оставаща рента в лева',
        'unpaidRentMoneyEuro' => 'Оставаща рента в евро',
        'overpaidRentMoney' => 'Надплатена рента в лева',
        'overpaidRentMoneyEuro' => 'Надплатена рента в евро',
        'rentInKind' => 'Рента в натура',
        'paidRentInKind' => 'Платена рента в натура',
        'unpaidRentInKind' => 'Оставаща рентента в натура',
        'overpaidRentInKind' => 'Надплатена рентента в натура',
    ];

    private $wrapTextKeys = [
        'contracts',
        'rentPlace',
        'plots',
        'rentInKind',
        'paidRentInKind',
        'unpaidRentInKind',
        'overpaidRentInKind',
        'repNames',
        'repIban',
    ];

    private $mergeRowsHeadersByContract = [
        'paidRentMoney',
        'paidRentMoneyEuro',
        'unpaidRentMoney',
        'unpaidRentMoneyEuro',
        'overpaidRentMoney',
        'overpaidRentMoneyEuro',
        'paidRentInKind',
        'unpaidRentInKind',
        'overpaidRentInKind',
    ];

    private $mergeRowsHeadersByContractAndPlot = [
        'contractArea',
        'arableArea',
        'personalUseArea',
    ];

    private $usedRentaTypes = [];

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'exportToExcelPayrollGrid' => ['method' => [$this, 'exportToExcelPayrollGrid']],
            'exportToExcelPayrollPlotsGrid' => ['method' => [$this, 'exportToExcelPayrollPlotsGrid'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * Export payroll grid to excel.
     *
     * @api-method exportToExcelPayrollGrid
     *
     * @param array $data -data for export to excel
     *                    {
     *                    #item string type
     *                    #item string subtype
     *                    #item boolean is_heritor
     *                    #item string payroll_from_date
     *                    #item string payroll_to_date
     *                    #item string payroll_ekate
     *                    #item array payroll_farming
     *                    #item string farming_year
     *                    #item string owner_names
     *                    #item string rent_place
     *                    #item string egn
     *                    #item string eik
     *                    #item string company_name
     *                    #item string rep_names
     *                    #item string rep_egn
     *                    #item string rep_rent_place
     *                    #item string custom_columns
     *                    #item string print_export
     *                    #item string sort
     *                    #item string order
     *                    }
     * @param string $fileName -The file name
     *
     * @return string filePath      The file name from the database
     */
    public function exportToExcelPayrollGrid($data, $fileName)
    {
        $allDataExport = $this->getPayrollDataToExport($data);

        $this->generateHeaders($data['columns'], $allDataExport);

        $returnData = $this->formatReturnData($allDataExport, $data['columns']);

        if (true == $data['columns']['natsDetailed'] && !empty($returnData['natsValues'])) {
            $this->removeUnusedNats($returnData['natsValues']);
        }

        $footer = $this->createFooter($returnData['data']);

        foreach ($returnData['data'] as $key => $row) {
            foreach ($row as $colName => $colValue) {
                if (array_key_exists($colName, $footer[0]) && strpos($footer[0][$colName], 'SUM(')) {
                    $options['format'][$colName] = ['type' => 'number', 'value' => '#,##0.000'];
                }

                if (in_array($colName, $this->wrapTextKeys)) {
                    $options['wrapText'][] = $colName;
                }
            }

            if ($data['columns']['plotDetailed']) {
                if ($key > 0) {
                    $mergeRowsContracts = $this->generateMergingRowsOptions($allDataExport, $key, $mergeRowsContracts['startRowNum'] ?? null, 'contract');
                    $options['mergeRows'] = array_merge($options['mergeRows'] ?? [], $mergeRowsContracts['results']);

                    $mergeRowsPlots = $this->generateMergingRowsOptions($allDataExport, $key, $mergeRowsPlots['startRowNum'] ?? null, 'contract_plot');
                    $options['mergeRows'] = array_merge($options['mergeRows'] ?? [], $mergeRowsPlots['results']);
                }
            }
        }

        $filePath = PAYROLL_EXPORTS_PATH . $this->User->GroupID . '/' . $fileName;
        if (!file_exists(PAYROLL_EXPORTS_PATH)) {
            mkdir(PAYROLL_EXPORTS_PATH, 0777);
        }
        if (!file_exists(PAYROLL_EXPORTS_PATH . $this->User->GroupID)) {
            mkdir(PAYROLL_EXPORTS_PATH . $this->User->GroupID, 0777);
        }

        $options['verticalAlignCenter'] = true;
        /** @var ExportToExcelClass $exportExcelDoc */
        $exportExcelDoc = new ExportToExcelClass();
        $exportExcelDoc->export($returnData['data'], $this->headers, $footer, 0, $options);
        $exportExcelDoc->saveFile($filePath);

        return SITE_URL . 'files/payrolls/' . $this->User->GroupID . '/' . $fileName;
    }

    public function exportMassPayments($report)
    {
        $UsersController = new UsersController();
        $inputParams = json_decode($report['export_params'], true);

        // Defensive checks to ensure required keys exist
        if (!isset($inputParams['columns']) || !is_array($inputParams['columns'])) {
            $inputParams['columns'] = [];
        }
        if (!isset($inputParams['filters']) || !is_array($inputParams['filters'])) {
            $inputParams['filters'] = [];
        }

        $owners = $this->getPayrollDataForMassPayment($inputParams);
        $paymentData = $this->preparePayrollExportForPayments($owners, $inputParams);

        // Generates excel preview
        switch ($report['masspayment_type']) {
            case Config::MASS_PAYMENT_DSKXML:
                $this->exportMassPaymentAsExcelPayrollPlotsGrid($paymentData['transactions'], $report['filename']);

                break;
            case Config::MASS_PAYMENT_BULBANKTXT:
                $this->exportBulbankMassPaymentAsExcel($paymentData['transactions'], $report['filename'], $report['id']);

                break;
            case Config::MASS_PAYMENT_UBBXML:
                $this->exportUbbMassPaymentAsExcelPayrollGrid($paymentData['transactions'], $report['filename']);

                break;
            default:
                throw new MTRpcException('Unknown mass payment type: ' . $report['masspayment_type']);
        }

        if (!empty($paymentData['validation_errors'])) {
            $paymentData['validation_errors'] = array_merge($paymentData['validation_errors'], $paymentData['ignored_validation_errors']);
            $UsersController->setPayrollExportMessage($report['id'], json_encode($paymentData['validation_errors'], JSON_UNESCAPED_UNICODE), 'validation_error');

            return false;
        }

        if (!empty($paymentData['ignored_validation_errors'])) {
            if (empty($paymentData['transactions'])) {
                // skip export only if no valid transtations are available
                $UsersController->setPayrollExportMessage($report['id'], json_encode($paymentData['ignored_validation_errors'], JSON_UNESCAPED_UNICODE), 'validation_error');

                return false;
            }

            $UsersController->setPayrollExportMessage($report['id'], json_encode($paymentData['ignored_validation_errors'], JSON_UNESCAPED_UNICODE));
        }

        switch ($report['masspayment_type']) {
            case Config::MASS_PAYMENT_DSKXML:
                $exportPath = $this->exportToDSKPayrollPlotsGrid($paymentData['transactions'], $report['filename']);

                break;
            case Config::MASS_PAYMENT_BULBANKTXT:
                $exportPath = $this->exportToBulbankPayrollPlotsGrid($paymentData['transactions'], $report['filename'], $report['id']);

                break;
            case Config::MASS_PAYMENT_UBBXML:
                $exportPath = $this->exportToUbbMassPaymentPayrollPlotsGrid($paymentData['transactions'], $report['filename']);

                break;
            default:
                throw new MTRpcException('Unknown mass payment type: ' . $report['masspayment_type']);
        }

        if (true == $report['generate_transactions']) {
            $this->generatePaymentTransactios($paymentData['transactions']);
        }

        return $exportPath;
    }

    public function exportBulbankMassPaymentAsExcel(array $paymentData, string $filename, int $reportId): string
    {
        $exportData = [];
        $senderData = $paymentData[0];
        $totalAmount = number_format(array_sum(array_column($paymentData, 'sum')), 2, '.', '');

        $headers = [
            'date' => 'Дата на писмото',
            'reference' => 'Референция (Изх. №)',
            'payment_date' => 'Дата на плащане',
            'payment_end_date' => 'Крайна дата на плащане',
            'total_sum' => 'Обща сума',
            'senders_iban' => 'Фирмена сметка',
            'currency' => 'Код Валута',
            'details_line1' => 'Основание за плащане',
        ];
        $paymentDate = date('d/m/Y', time());
        $paymentEndDate = date('d/m/Y', strtotime(' + 2 week'));
        $refDate = date('d.m.y', time());

        foreach ($paymentData as $key => $payment) {
            if (0 == $key) {
                $exportData[$key] = [
                    'date' => $paymentDate,
                    'reference' => $reportId . '/' . $refDate,
                    'payment_date' => $paymentDate,
                    'payment_end_date' => $paymentEndDate,
                    'total_sum' => $totalAmount,
                    'senders_iban' => $senderData['senders_iban'],
                    'currency' => 'BGN',
                    'details_line1' => '',
                ];

                $exportData[$key + 1] = [
                    'date' => 'ЕГН',
                    'reference' => 'Име',
                    'payment_date' => 'Презиме',
                    'payment_end_date' => 'Фамилия',
                    'total_sum' => 'Сума за получаване',
                    'senders_iban' => 'IBAN Сметка на получател',
                    'currency' => 'BIC код на Банка',
                    'details_line1' => 'Основание за плащане',
                ];
            }

            $exportData[$key + 2] = [
                'date' => $payment['recipient_eik'],
                'total_sum' => $payment['sum'],
                'senders_iban' => $payment['recipient_iban'],
                'currency' => $payment['recipient_bic'],
                'details_line1' => 'НАЕМ ЗЕМЕДЕЛСКА ЗЕМЯ',
            ];

            [$fistname, $surname, $lastname] = explode(' ', $payment['recipient_full_name']);

            $exportData[$key + 2]['reference'] = $fistname;
            $exportData[$key + 2]['payment_date'] = $surname;
            $exportData[$key + 2]['payment_end_date'] = $lastname;
        }

        $path = PAYROLL_EXPORTS_PATH . '/' . $this->User->GroupID;
        $xlsPath = $path . '/' . substr($filename, 0, -3) . 'xlsx';

        /** @var ExportToExcelClass */
        $exportExcelDoc = Prado::getApplication()->getModule('ExportToExcel');

        $exportExcelDoc->export($exportData, $headers, []);
        $exportExcelDoc->saveFile($xlsPath);

        return 'files/payrolls/' . $this->User->GroupID . '/' . $filename;
    }

    public function exportMassPaymentAsExcelPayrollPlotsGrid($paymentData, $filename)
    {
        $headers = [
            'reference' => 'Референция №',
            'date' => 'Дата (ггммдд)',
            'senders_iban' => 'Подател IBAN',
            'senders_name' => 'Подател име',
            'senders_bic' => 'Подател BAE',
            'senders_bank_name' => 'Подател банка',
            'sum' => 'Сума',
            'recipient_iban' => 'Получател IBAN',
            'recipient_name' => 'Получател име',
            'recipient_bic' => 'Получател BAE',
            'recipient_bank_name' => 'Получател банка',
            'details_line1' => 'Информация',
            'details_line2' => 'Допълнителна информация',
        ];

        $sum = 0;
        foreach ($paymentData as $payment) {
            $sum += $payment['sum'];
        }

        $footer = [
            [
                'senders_bank_name' => 'ОБЩО:',
                'sum' => $sum,
                'recipient_iban' => 'лв.',
            ],
        ];

        $path = PAYROLL_EXPORTS_PATH . $this->User->GroupID;
        if (!file_exists($path)) {
            mkdir($path, 0777);
        }

        $xlsPath = $path . '/' . substr($filename, 0, -3) . 'xlsx';

        /** @var ExportToExcelClass */
        $exportExcelDoc = Prado::getApplication()->getModule('ExportToExcel');
        $exportExcelDoc->export($paymentData, $headers, $footer);
        $exportExcelDoc->saveFile($xlsPath);

        return 'files/payrolls/' . $this->User->GroupID . '/' . $filename;
    }

    public function exportUbbMassPaymentAsExcelPayrollGrid($paymentData, $filename)
    {
        $headers = [
            'transid' => 'Идентификатор на операцията',
            'recipient_name' => 'Име на бенефициента',
            'recipient_iban' => 'IBAN на бенефициентa',
            'recipient_bic' => 'BIC на банката на бенефициента',
            'currency' => 'Код валута',
            'sum' => 'Сума',
            'details_line1' => 'Основание за плащане',
            'details_line2' => 'Още пояснения',
            'decl30000' => 'Декларация за произход на средства',
        ];

        $exportData = [];
        $sum = 0;

        foreach ($paymentData as $key => $payment) {
            $sum += $payment['sum'];

            $exportData[$key] = [
                'transid' => 'IBAN311',
                'recipient_name' => $this->sanitizeUbbTextField($payment['recipient_name']),
                'recipient_iban' => $payment['recipient_iban'],
                'recipient_bic' => $payment['recipient_bic'],
                'currency' => 'BGN',
                'sum' => number_format($payment['sum'], 2, '.', ''),
                'details_line1' => $this->sanitizeUbbTextField($payment['details_line1']),
                'details_line2' => !empty($payment['details_line2']) ? $this->sanitizeUbbTextField($payment['details_line2']) : '',
                'decl30000' => $payment['sum'] >= 30000 ? $this->sanitizeUbbTextField('ДЕКЛАРАЦИЯ 30000 ЛВ') : '',
            ];
        }

        $footer = [
            [
                'recipient_name' => 'ОБЩО:',
                'sum' => number_format($sum, 2, '.', ''),
                'details_line1' => 'лв.',
            ],
        ];

        $path = PAYROLL_EXPORTS_PATH . $this->User->GroupID;
        if (!file_exists($path)) {
            mkdir($path, 0777);
        }

        $xlsPath = $path . '/' . substr($filename, 0, -3) . 'xlsx';

        /** @var ExportToExcelClass */
        $exportExcelDoc = Prado::getApplication()->getModule('ExportToExcel');
        $exportExcelDoc->export($exportData, $headers, $footer);
        $exportExcelDoc->saveFile($xlsPath);

        return 'files/payrolls/' . $this->User->GroupID . '/' . $filename;
    }

    private function generateMergingRowsOptions(array $allDataExport, int $key, $startRowNum = null, string $mode = 'contract'): array
    {
        $mergeRows = [];
        $endRowNum = null;
        $rowNum = $key + 1;

        // Choose comparator and headers based on mode
        if ('contract' === $mode) {
            $headers = $this->mergeRowsHeadersByContract;
            $isSameGroup = function (array $a = null, array $b = null): bool {
                if (empty($a) || empty($b)) {
                    return false;
                }

                return isset($a['contract_id'], $b['contract_id']) && $a['contract_id'] == $b['contract_id']
                    && isset($a['owner_id'], $b['owner_id']) && $a['owner_id'] == $b['owner_id'];
            };
        } elseif ('contract_plot' === $mode) {
            $headers = $this->mergeRowsHeadersByContractAndPlot;
            $isSameGroup = function (array $a = null, array $b = null): bool {
                if (empty($a) || empty($b)) {
                    return false;
                }

                return isset($a['contract_id'], $b['contract_id']) && $a['contract_id'] == $b['contract_id']
                    && isset($a['plot_id'], $b['plot_id']) && $a['plot_id'] == $b['plot_id'];
            };
        } else {
            // Fallback to contract behavior
            $headers = $this->mergeRowsHeadersByContract;
            $isSameGroup = function (array $a = null, array $b = null): bool {
                if (empty($a) || empty($b)) {
                    return false;
                }

                return isset($a['contract_id'], $b['contract_id']) && $a['contract_id'] == $b['contract_id'];
            };
        }

        // Determine start of a merging block
        if (!$startRowNum && isset($allDataExport[$key - 1]) && $isSameGroup($allDataExport[$key - 1], $allDataExport[$key])) {
            $startRowNum = $rowNum;
        }

        // Determine end of a merging block
        if ($startRowNum && (!isset($allDataExport[$key + 1]) || !$isSameGroup($allDataExport[$key + 1], $allDataExport[$key]))) {
            $endRowNum = $rowNum + 1;
        }

        if ($startRowNum && $endRowNum) {
            foreach ($headers as $header) {
                $col = $this->getColumnLetterByHeader($header);
                if ($col) {
                    $mergeRows[] = [
                        'startRow' => $startRowNum,
                        'endRow' => $endRowNum,
                        'col' => $col,
                    ];
                }
            }
            $startRowNum = null;
            $endRowNum = null;
        }

        return [
            'results' => $mergeRows,
            'startRowNum' => $startRowNum,
        ];
    }

    private function generateMergingContractPlotRowsOptions(array $allDataExport, int $key, $startRowNum = null): array
    {
        $mergeRows = [];
        $endRowNum = null;
        $rowNum = $key + 1;

        // Helper comparator: rows are considered in the same group if they share the same contract_id AND plot
        $isSameGroup = function (array $a = null, array $b = null): bool {
            if (empty($a) || empty($b)) {
                return false;
            }

            return
                isset($a['contract_id'], $b['contract_id']) && $a['contract_id'] == $b['contract_id']
                && isset($a['plot_id'], $b['plot_id']) && $a['plot_id'] == $b['plot_id'];
        };

        // Determine start of a merging block
        if (!$startRowNum && isset($allDataExport[$key - 1]) && $isSameGroup($allDataExport[$key - 1], $allDataExport[$key])) {
            $startRowNum = $rowNum;
        }

        // Determine end of a merging block
        if ($startRowNum && (!isset($allDataExport[$key + 1]) || !$isSameGroup($allDataExport[$key + 1], $allDataExport[$key]))) {
            $endRowNum = $rowNum + 1;
        }

        if ($startRowNum && $endRowNum) {
            foreach ($this->mergeRowsHeadersByContractAndPlot as $header) {
                $col = $this->getColumnLetterByHeader($header);
                if ($col) {
                    $mergeRows[] = [
                        'startRow' => $startRowNum,
                        'endRow' => $endRowNum,
                        'col' => $col,
                    ];
                }
            }
            $startRowNum = null;
            $endRowNum = null;
        }

        return [
            'results' => $mergeRows,
            'startRowNum' => $startRowNum,
        ];
    }

    private function generatePaymentTransactios($paymentData)
    {
        $defaultValues = [
            'payment_type_money' => true,
            'payment_type_natura' => false,
            'payment_date' => date('Y-m-d'),
            'payment_natura_type' => null,
            'payment_natura_price' => null,
            'payment_natura_amount' => null,
            'payment_method_bank' => true,
            'payment_method_post_order' => false,
            'payment_order' => true,
            'weighing_note' => null,
        ];

        $paymentsRpc = makeApiClass('payments-rpc', 'contract-add-payment');

        foreach ($paymentData as $payment) {
            $data = $defaultValues;
            $data['payment_amount'] = $payment['sum'];
            $data['payment_recipient'] = $payment['recipient_full_name'];
            $data['payment_recipient_egn'] = $payment['recipient_eik'];
            $data['payment_bank_account'] = $payment['recipient_iban'];

            $paymentsRpc->savePayment($payment['payment_data'], $data);
        }
    }

    private function preparePayrollExportForPayments($payrollData, $inputParams)
    {
        $FarmingController = new FarmingController('Farming');

        $inputParams['separated_natura'] = false;
        $transactions_count = count($payrollData);
        if ($transactions_count < 1) {
            throw new MTRpcException('Error Processing Request', 1);
        }

        $paymentData = [
            'validation_errors' => [],
            'ignored_validation_errors' => [],
            'transactions' => [],
        ];

        $senders_iban = $inputParams['payer_iban'];
        $farmRecord = array_filter($payrollData, function ($farming) use ($inputParams) {
            return $farming['farming_id'] == $inputParams['farming_id'];
        });
        $firstFarmRecord = reset($farmRecord);
        $senders_name = strtoupper($firstFarmRecord['farming_name']);

        if (empty($senders_iban) || 22 !== strlen($senders_iban) || 'BG' !== substr($senders_iban, 0, 2)) {
            $paymentData['validation_errors'][] = strtr(
                'Липсващ или невалиден IBAN на наредителя! Наредител: :name, IBAN: :iban',
                [
                    ':name' => $senders_name,
                    ':iban' => $senders_iban,
                ]
            );
        }

        $tmpBankCode = substr($inputParams['payer_iban'], 4, 8);
        $tmpBankInfo = $this->getBankInfoFromBankCode($tmpBankCode);

        if (empty($tmpBankInfo)) {
            $paymentData['validation_errors'][] = strtr(
                'Невалиден IBAN на наредителя! Наредител: :name, IBAN: :iban',
                [
                    ':name' => $senders_name,
                    ':iban' => $senders_iban,
                ]
            );
        }

        $senders_bic = $tmpBankInfo['bank_code'];
        $senders_swift = $tmpBankInfo['bic_code'];
        $senders_bank_name = $tmpBankInfo['bank_name_en'];
        $senders_bank_name = strtoupper($FarmingController->StringHelper->trimStringToLength($senders_bank_name, 35, false));

        foreach ($payrollData as $ownerData) {
            if ($ownerData['renta'] <= 0) {
                continue;
            }

            $tmpContracts = is_array($ownerData['c_num_array']) ? implode(', ', $ownerData['c_num_array']) : '';
            $tmpContracts = str_replace('"', '', $tmpContracts);
            $tmpContracts = str_replace("'", '', $tmpContracts);

            $sum = number_format($ownerData['unpaid_renta'], 2, '.', '');

            $recipient_iban = trim($ownerData['rep_iban']) ?: trim($ownerData['iban']);
            $recipient_egn_eik = $ownerData['rep_egn'] ? $ownerData['rep_egn'] : $ownerData['egn_eik'];
            $recipient_name_original = $ownerData['rep_names'] ? $ownerData['rep_names'] : $ownerData['owner_names'];

            $recipient_name = strtoupper($FarmingController->StringHelper->trimStringToLength($recipient_name_original, 35, false));

            if (empty($recipient_name)) {
                $paymentData['ignored_validation_errors'][] = strtr(
                    'Липсващо име на получателя! Получател: :egn_eik',
                    [
                        ':egn_eik' => $recipient_egn_eik,
                    ]
                );

                continue;
            }

            if (empty($recipient_iban) || 22 !== strlen($recipient_iban) || 'BG' !== substr($recipient_iban, 0, 2)) {
                $paymentData['ignored_validation_errors'][] = strtr(
                    'Липсващ или невалиден IBAN на получателя! Получател: :name, ЕГН/ЕИК: :egn_eik, IBAN: :iban',
                    [
                        ':egn_eik' => $recipient_egn_eik,
                        ':name' => $recipient_name_original,
                        ':iban' => $recipient_iban,
                    ]
                );

                continue;
            }

            $tmpRecipientBankCode = substr($recipient_iban, 4, 8);
            $tmpRecipientBankInfo = $this->getBankInfoFromBankCode($tmpRecipientBankCode);

            if (empty($tmpRecipientBankInfo)) {
                $paymentData['ignored_validation_errors'][] = strtr(
                    'Невалиден IBAN на получателя! Получател: :name, ЕГН/ЕИК: :egn_eik, IBAN: :iban',
                    [
                        ':egn_eik' => $recipient_egn_eik,
                        ':name' => $recipient_name_original,
                        ':iban' => $recipient_iban,
                    ]
                );

                continue;
            }

            $recipient_bic = $tmpRecipientBankInfo['bic_code'];

            $recipient_bank_name = strtoupper($FarmingController->StringHelper->trimStringToLength($tmpRecipientBankInfo['bank_name_en'], 35, false));
            $details_line1 = 'PLASHTANE PO DOGOWORI';
            $details_line2 = strtoupper($FarmingController->StringHelper->convertToLatin($tmpContracts));
            $details_line2 = $FarmingController->StringHelper->trimStringToLength($details_line2, 35, true);

            if (strlen($details_line2) < 1) {
                $details_line2 = '...';
            }

            $ownerNames = explode(' ', $recipient_name_original);

            $transaction['date'] = $inputParams['order_date'];
            $transaction['senders_iban'] = $senders_iban;
            $transaction['senders_name'] = $senders_name;
            $transaction['senders_bic'] = $senders_bic;
            $transaction['senders_swift'] = $senders_swift;
            $transaction['senders_bank_name'] = $senders_bank_name;
            $transaction['sum'] = $sum;
            $transaction['recipient_bic'] = $recipient_bic;
            $transaction['recipient_bank_name'] = $recipient_bank_name;
            $transaction['recipient_iban'] = $recipient_iban;
            $transaction['recipient_name'] = $recipient_name;
            $transaction['recipient_eik'] = $recipient_egn_eik;
            $transaction['details_line1'] = $details_line1;
            $transaction['details_line2'] = $details_line2;
            $transaction['recipient_first_name'] = $ownerNames[0] ?? '';
            $transaction['recipient_surname'] = $ownerNames[1] ?? '';
            $transaction['recipient_lastname'] = $ownerNames[2] ?? '';
            $transaction['recipient_full_name'] = $recipient_name_original;
            $transaction['owner_type'] = $ownerData['owner_type'];
            $transaction['payment_data'] = $ownerData['payment_data'];

            $paymentData['transactions'][] = $transaction;
        }

        return $paymentData;
    }

    private function exportToBulbankPayrollPlotsGrid($paymentData, $filename, $reportId): string
    {
        $senderData = $paymentData[0];
        $totalAmount = number_format(array_sum(array_column($paymentData, 'sum')), 2, '.', '');
        $content = $header = '';

        $paymentDate = date('d/m/Y', time());
        $paymentEndDate = date('d/m/Y', strtotime(' + 2 week'));
        $refDate = date('d.m.Y', time());
        $reference = str_pad('1/' . $refDate, 12, ' ', STR_PAD_RIGHT);
        $paymentsCount = str_pad(count($paymentData), 5, '0', STR_PAD_LEFT);
        foreach ($paymentData as $key => $payment) {
            if (0 == $key) {
                $header = 'FIX WIN BULBANK %s %s %s %s %s %s %s %s %s';
                $headerData = [
                    $paymentDate,
                    $reference,
                    $paymentDate,
                    $paymentEndDate,
                    $paymentsCount,
                    str_pad($totalAmount, 12, ' ', STR_PAD_RIGHT),
                    $senderData['senders_iban'],
                    'BGN',
                    str_pad('', 35, ' ', STR_PAD_RIGHT),
                ];

                $content = sprintf($header, ...$headerData) . PHP_EOL;
            }

            $nameConcat = $payment['recipient_full_name'];

            if (mb_strlen($nameConcat, 'utf-8') < 35) {
                $clientName = $nameConcat . str_repeat(' ', 35 - mb_strlen($nameConcat, 'utf-8'));
            }

            $note = 'НАЕМ ЗЕМЕДЕЛСКА ЗЕМЯ' . str_repeat(' ', 70 - mb_strlen('НАЕМ ЗЕМЕДЕЛСКА ЗЕМЯ', 'utf-8'));

            $contentStr = '%s %s %s %s %s %s';
            $contentData = [
                $payment['recipient_iban'],
                str_pad($payment['recipient_eik'], 10, ' ', STR_PAD_RIGHT),
                $clientName,
                str_pad($payment['sum'], 12, ' ', STR_PAD_RIGHT),
                $note,
                $payment['recipient_bic'],
            ];

            $content .= sprintf($contentStr, ...$contentData) . PHP_EOL;
        }

        // $content = iconv(mb_detect_encoding($content), 'cp1251', $content);
        $content = mb_convert_encoding($content, 'Windows-1251', 'UTF-8');

        $filename = pathinfo($filename, PATHINFO_FILENAME);
        $groupId = Prado::getApplication()->getUser()->GroupID;
        $exportPath = PAYROLL_EXPORTS_PATH . "{$groupId}";

        if (!file_exists($exportPath)) {
            mkdir($exportPath, 0755, true);
        }

        $path = "{$exportPath}/{$filename}.txt";

        file_put_contents($path, $content);

        return $path;
    }

    private function exportToDSKPayrollPlotsGrid($paymentData, $filename)
    {
        $count = count($paymentData);
        $senderData = $paymentData[0];
        $totalAmount = number_format(array_sum(array_column($paymentData, 'sum')), 2, '.', '');

        $doc = new SimpleXMLElement('<?xml version="1.0" encoding="UTF-8" ?><Payments></Payments>');

        $header = $doc->addChild('Header');
        $header->addChild('CustomerName', $senderData['senders_name']);
        $header->addChild('DocumentAccount', $senderData['senders_iban']);
        $header->addChild('DocumentBankCode', $senderData['senders_swift']);
        $header->addChild('DocumentBankName', $senderData['senders_bank_name']);
        $header->addChild('Currency', 'BGN');
        $header->addChild('TotalAmount', $totalAmount);
        $header->addChild('TotalRows', $count);
        $rows = $doc->addChild('Rows');

        foreach ($paymentData as $payment) {
            if (22 != strlen($payment['recipient_iban'])) {
                throw new MTRpcException('INVALID_IBAN_LENGTH', -33225);
            }

            $creditPayment = $rows->addChild('CP');
            $creditPayment->addChild('Amount', $payment['sum']);
            $creditPayment->addChild('Reason', $payment['details_line1']);
            $creditPayment->addChild('Reason2', $payment['details_line2']);
            $creditPayment->addChild('Charges', 'OUR');
            $creditPayment->addChild('ProcessingDate', $payment['date']);
            $creditPayment->addChild('Payroll', 'false');
            $creditPayment->addChild('MassPayment', 'true');
            $creditPayment->addChild('Aviso', 'false');
            $creditPayment->addChild('ExternalReference', '');
            $creditPayment->addChild('PaymentSystem', 'BISERA');
            $beneficiary = $creditPayment->addChild('Beneficiary');
            $beneficiary->addChild('Name', $payment['recipient_name']);
            $beneficiary->addChild('IBAN', $payment['recipient_iban']);
            $beneficiary->addChild('Address', '');
            $beneficiary->addChild('Town', '');
            $beneficiary->addChild('BIC', $payment['recipient_bic']);
            $beneficiary->addChild('BankName', $payment['recipient_bank_name']);
        }

        libxml_use_internal_errors(true);
        $xsd = file_get_contents(TEMPLATE_PATH . 'DSKBGNPaymentsXSD.xsd');

        if (empty($xsd)) {
            throw new MTRpcException('DSKBGNPaymentsXSD.xsd file not found or file is empty. Path to file: ' . TEMPLATE_PATH . 'DSKBGNPaymentsXSD.xsd', -33225);
        }

        $filename = pathinfo($filename, PATHINFO_FILENAME);
        $groupId = Prado::getApplication()->getUser()->GroupID;
        $exportPath = PAYROLL_EXPORTS_PATH . "{$groupId}";

        if (!file_exists($exportPath)) {
            mkdir($exportPath, 0755, true);
        }

        $path = "{$exportPath}/{$filename}.xml";
        $docXml = $doc->asXML();
        file_put_contents($path, $docXml);
        $domDoc = new DOMDocument();
        $domDoc->loadXML($docXml);

        if (!file_exists($path)) {
            throw new MTRpcException('Mass export is not exists in: ' . $path, -33229);
        }

        if (!$domDoc->schemaValidateSource($xsd)) {
            $error = libxml_get_last_error();
            if (false === strpos($error->message, 'ProcessingDate')) {
                throw new MTRpcException('DSK XML ERROR: ' . $error->message, -33229);
            }
        }

        return $path;
    }

    private function exportToUbbMassPaymentPayrollPlotsGrid($paymentData, $filename)
    {
        $doc = new SimpleXMLElement('<?xml version="1.0" encoding="windows-1251" ?><PAYMENTS></PAYMENTS>');

        foreach ($paymentData as $payment) {
            if (22 != strlen($payment['recipient_iban'])) {
                throw new MTRpcException('INVALID_IBAN_LENGTH', -33225);
            }

            $paymentElement = $doc->addChild('PAYMENT');

            // TRANSID - using report ID as transaction identifier
            $paymentElement->addChild('TRANSID', 'IBAN311'); // This value refers to the payment type, in this case 'Преводно нареждане за кредитен превод'

            // NAME_R - recipient name (sanitized)
            $paymentElement->addChild('NAME_R', $this->sanitizeUbbTextField($payment['recipient_name']));

            // IBAN_R - recipient IBAN
            $paymentElement->addChild('IBAN_R', $payment['recipient_iban']);

            // BIC_R - recipient BIC/SWIFT code
            $paymentElement->addChild('BIC_R', $payment['recipient_bic']);

            // CURRENCY - currency code, always 'BGN'
            $paymentElement->addChild('CURRENCY', 'BGN');

            // JSUM - payment amount
            $paymentElement->addChild('JSUM', number_format($payment['sum'], 2, '.', ''));

            // REM_I - payment reason line 1 (sanitized)
            $paymentElement->addChild('REM_I', $this->sanitizeUbbTextField($payment['details_line1']));

            // REM_II - payment reason line 2 (if exists, sanitized)
            if (!empty($payment['details_line2'])) {
                $paymentElement->addChild('REM_II', $this->sanitizeUbbTextField($payment['details_line2']));
            }

            // Add DECL30000 element if payment amount is >= 30000 (sanitized)
            if ($payment['sum'] >= 30000) {
                $paymentElement->addChild('DECL30000', $this->sanitizeUbbTextField('ДЕКЛАРАЦИЯ 30000 ЛВ'));
            }
        }

        $filename = pathinfo($filename, PATHINFO_FILENAME);
        $groupId = Prado::getApplication()->getUser()->GroupID;
        $exportPath = PAYROLL_EXPORTS_PATH . "{$groupId}";

        if (!file_exists($exportPath)) {
            mkdir($exportPath, 0755, true);
        }

        $path = "{$exportPath}/{$filename}.xml";

        // Get XML content (already in windows-1251 encoding)
        $docXml = $doc->asXML();

        // Simply replace the encoding declaration - no conversion needed!
        // The content is already properly encoded in windows-1251
        $docXml = str_replace('encoding="UTF-8"', 'encoding="windows-1251"', $docXml);

        file_put_contents($path, $docXml);

        if (!file_exists($path)) {
            throw new MTRpcException('Mass export file not created at: ' . $path, -33229);
        }

        return $path;
    }

    /**
     * Sanitize text fields for UBB XML format
     * Allowed characters: Cyrillic and Latin letters, numbers, spaces and the symbols /.;&=%-*+.
     *
     * @param string $text
     *
     * @return string
     */
    private function sanitizeUbbTextField($text)
    {
        if (empty($text)) {
            return '';
        }

        // Remove or replace invalid characters
        // Keep: Cyrillic (А-я), Latin (A-Za-z), numbers (0-9), spaces, and symbols: /.;&=%-*+
        $sanitized = preg_replace('/[^А-я\u0401\u0451A-Za-z0-9\s\/\.;&#=%-*+]/u', '', $text);

        // Trim whitespace and return
        return trim($sanitized);
    }

    private function getBankInfoFromBankCode($code)
    {
        $UsersController = new UsersController('Users');

        $options = [
            'tablename' => 'su_banks',
            'where' => [
                'bank_code' => ['column' => 'bank_code', 'compare' => '=', 'value' => $code],
            ],
        ];

        $bankDetails = $UsersController->getItemsByParams($options);

        return $bankDetails[0];
    }

    private function getPayrollDataToExport($data)
    {
        $result = [];
        $paymentsController = new PaymentsController($this->User->Database);

        $payrollData = $paymentsController->getOwnersPayroll($data['filters']['farming_year'], $data['filters']);
        $exportData = $this->getLeafOwners($payrollData['rows']);

        if (true == $data['columns']['plotDetailed']) {
            $exportData = $this->separateOwnersByPlots($data['filters']['farming_year'], $exportData);
        }

        $result = $this->aggregateOwners($exportData, $data['columns']['inheritorOf'], $data['columns']['plotDetailed']);

        return $paymentsController->formattingOwnersData($result);
    }

    private function getPayrollDataForMassPayment(array $inputParams): array
    {
        $paymentsController = new PaymentsController($this->User->Database);
        $payrollData = $paymentsController->getOwnersPayroll($inputParams['filters']['farming_year'], $inputParams['filters'], null, null, true);

        // Format data for mass payment structure
        return $this->formatDataToMasspayment($payrollData['rows'], $inputParams);
    }

    /**
     * Format data for mass payment structure.
     *
     * @param array $data Flat array of owner-contract records
     *
     * @return array Grouped array by owner with aggregated totals
     */
    private function formatDataToMasspayment(array $data, array $inputParams)
    {
        $ownerGrouped = [];

        // Process each contract
        foreach ($data as $contractData) {
            // Skip footer/summary rows that don't contain owner data
            if (!is_array($contractData) || empty($contractData)) {
                continue;
            }

            // Process each owner in the contract
            foreach ($contractData as $ownerKey => $ownerRecord) {
                // Skip non-owner data (summary rows, etc.)
                if (!is_array($ownerRecord) || !isset($ownerRecord['owner_id'])) {
                    continue;
                }

                if ($ownerRecord['is_dead']) {
                    continue;
                }

                $ownerId = $ownerRecord['owner_id'];
                $contractId = $ownerRecord['contract_id'];

                // Initialize owner if not exists
                if (!isset($ownerGrouped[$ownerId])) {
                    $ownerGrouped[$ownerId] = [
                        'owner_id' => $ownerRecord['owner_id'],
                        'owner_type' => null,
                        'owner_names' => $ownerRecord['owner_names'],
                        'first_name' => null,
                        'surname' => null,
                        'lastname' => null,
                        'phone' => $ownerRecord['phone'],
                        'mobile' => $ownerRecord['mobile'],
                        'rent_place' => $ownerRecord['rent_place_name']
                            ? $ownerRecord['rent_place_name'] . ' (' . $ownerRecord['rent_place_ekatte'] . ')' : '',
                        'egn_eik' => $ownerRecord['egn_eik'],
                        'rep_names' => $ownerRecord['rep_names'] ?: '',
                        'rep_iban' => $ownerRecord['rep_iban'] ?: '',
                        'iban' => $ownerRecord['iban'] ?: '',
                        'full_rep_info' => null,
                        'area' => 0,
                        'cultivated_area' => 0,
                        'pu_area' => 0,
                        'renta' => 0,
                        'charged_renta' => 0,
                        'paid_renta' => 0,
                        'unpaid_renta' => 0,
                        'total_by_renta' => 0,
                        'over_paid' => 0,
                        'c_num_array' => [],
                        'payment_data' => [
                            'owner_array' => [],
                            'payment_type' => 'owner_payments',
                            'year' => $inputParams['filters']['farming_year'],
                        ],
                    ];
                }

                // Add c_num to the array if not already present
                if (!in_array($ownerRecord['c_num'], $ownerGrouped[$ownerId]['c_num_array'])) {
                    $ownerGrouped[$ownerId]['c_num_array'][] = $ownerRecord['c_num'];
                }

                // Add contract to owner array (data is already aggregated correctly)
                $ownerGrouped[$ownerId]['payment_data']['owner_array'][] = [
                    'owner_id' => $ownerRecord['owner_id'],
                    'path' => $ownerRecord['path'],
                    'contract_id' => $contractId,
                    'owner_area' => (float)$ownerRecord['owner_area'],
                    'charged_renta' => null === $ownerRecord['charged_renta'] ? 0 : (float)$ownerRecord['charged_renta'],
                    'renta' => null === $ownerRecord['renta'] ? 0 : (float)$ownerRecord['renta'],
                    'unpaid_renta' => (float)$ownerRecord['unpaid_renta'],
                    'is_heritor' => $ownerRecord['is_heritor'],
                    'farming_id' => $ownerRecord['farming_id'],
                ];

                // Accumulate owner totals
                $ownerGrouped[$ownerId]['area'] += (float)$ownerRecord['owner_area'];
                $ownerGrouped[$ownerId]['cultivated_area'] += (float)$ownerRecord['cultivated_area'];
                $ownerGrouped[$ownerId]['pu_area'] += 0 === $ownerRecord['pu_area'] ? 0 : (float)$ownerRecord['pu_area'];
                $ownerGrouped[$ownerId]['renta'] += null === $ownerRecord['renta'] ? 0 : (float)$ownerRecord['renta'];
                $ownerGrouped[$ownerId]['charged_renta'] += null === $ownerRecord['charged_renta'] ? 0 : (float)$ownerRecord['charged_renta'];
                $ownerGrouped[$ownerId]['paid_renta'] += (float)$ownerRecord['paid_renta'];
                $ownerGrouped[$ownerId]['unpaid_renta'] += (float)$ownerRecord['unpaid_renta'];
                $ownerGrouped[$ownerId]['total_by_renta'] = $ownerGrouped[$ownerId]['paid_renta'];
                $ownerGrouped[$ownerId]['over_paid'] += (float)$ownerRecord['overpaid_renta'];
            }
        }

        return $ownerGrouped;
    }

    /**
     * Formats the return data for payroll grid export.
     *
     * @param array $data the data to be formatted
     * @param array $columns the columns to include in the formatted output
     *
     * @return array the formatted data ready for export
     */
    private function formatReturnData($data, $columns)
    {
        foreach ($data as $key => $row) {
            $returnData[$key]['ownerNames'] = $row['owner_names'];
            $returnData[$key]['areaForRent'] = $row['is_dead'] ? '' : $row['all_owner_area'];
            $returnData[$key]['rentMoney'] = $row['is_dead'] ? '' : $row['all_renta_money'];
            $returnData[$key]['rentMoneyEuro'] = $row['is_dead'] ? '' : convertBGNtoEURO($row['all_renta_money']);
            $returnData[$key]['paidRentMoney'] = $row['paid_renta'];
            $returnData[$key]['paidRentMoneyEuro'] = $row['is_dead'] ? '' : convertBGNtoEURO($row['paid_renta']);
            $returnData[$key]['unpaidRentMoney'] = $row['is_dead'] ? '' : $row['unpaid_renta'];
            $returnData[$key]['unpaidRentMoneyEuro'] = $row['is_dead'] ? '' : convertBGNtoEURO($row['unpaid_renta']);
            $returnData[$key]['overpaidRentMoney'] = $row['is_dead'] ? '' : $row['overpaid_renta'];
            $returnData[$key]['overpaidRentMoneyEuro'] = $row['is_dead'] ? '' : convertBGNtoEURO($row['overpaid_renta']);

            $returnData[$key]['rentInKind'] = $row['is_dead'] ? '' : str_replace('<br>', "\n", rtrim($row['all_renta_nat_with_type'], '<br>'));
            $returnData[$key]['paidRentInKind'] = str_replace('<br>', "\n", rtrim($row['paid_renta_nat_with_type'], '<br>'));
            $returnData[$key]['unpaidRentInKind'] = $row['is_dead'] ? '' : str_replace('<br>', "\n", rtrim($row['unpaid_renta_nat_with_type'], '<br>'));
            $returnData[$key]['overpaidRentInKind'] = $row['is_dead'] ? '' : str_replace('<br>', "\n", rtrim($row['overpaid_renta_nat_with_type'], '<br>'));

            if ($columns['inheritorOf']) {
                $returnData[$key]['inheritorOf'] = $row['owner_parent_names'];
            }
            if ($columns['ownerEgnEik']) {
                $returnData[$key]['ownerEgnEik'] = $row['egn_eik'];
            }

            if ($columns['ownerPhone']) {
                $returnData[$key]['ownerPhone'] = $row['mobile'] . (!empty($row['phone']) ? ', ' . $row['phone'] : '');
            }

            if ($columns['ownerIban']) {
                $returnData[$key]['ownerIban'] = $row['iban'];
            }

            if ($columns['repNames']) {
                $returnData[$key]['repNames'] = implode("\n", $row['rep_names_array']);
            }

            if ($columns['repIban']) {
                $returnData[$key]['repIban'] = implode("\n", $row['rep_ibans_array']);
            }

            if ($columns['contracts']) {
                $returnData[$key]['contracts'] = empty($row['c_num_group_name_array']) ? $row['c_num_with_group_name'] : implode("\n", $row['c_num_group_name_array']);
            }

            if ($columns['rentPlace']) {
                $returnData[$key]['rentPlace'] = $row['rent_place_name'];
            }

            if ($columns['plots']) {
                $returnData[$key]['plots'] = implode("\n", $row['kad_idents_array']);
            }

            if ($columns['plotDetailed']) {
                $returnData[$key]['plotDetailed'] = $row['kad_ident'];
            }

            if ($columns['contractArea']) {
                $returnData[$key]['contractArea'] = $row['is_dead'] ? '' : $row['all_owner_no_rounded_contract'];
            }

            if ($columns['arableArea']) {
                $returnData[$key]['arableArea'] = $row['is_dead'] ? '' : $row['cultivated_area'];
            }

            if ($columns['personalUseArea']) {
                $returnData[$key]['personalUseArea'] = $row['is_dead'] ? '' : str_replace('-', 0, $row['pu_area']);
            }

            if ($columns['natsDetailed']) {
                foreach ($this->usedRentaTypes as $rentaNatId) {
                    $rentNat = isset($row['renta_nat'][$rentaNatId]) && is_numeric($row['renta_nat'][$rentaNatId]) ? $row['renta_nat'][$rentaNatId] : 0;
                    $chargedRentNat = isset($row['charged_renta_nat'][$rentaNatId]) && is_numeric($row['charged_renta_nat'][$rentaNatId]) ? $row['charged_renta_nat'][$rentaNatId] : 0;
                    $natSum = $rentNat + $chargedRentNat;
                    $returnData[$key][self::RENT_NAT_PREFIX . $rentaNatId] = $row['is_dead'] ? '' : $natSum;

                    $returnData[$key][self::PAID_RENT_NAT_PREFIX . $rentaNatId] = isset($row['paid_renta_nat_sum'][$rentaNatId]) && is_numeric($row['paid_renta_nat_sum'][$rentaNatId]) ? $row['paid_renta_nat_sum'][$rentaNatId] : 0;
                    $returnData[$key][self::UNPAID_RENT_NAT_PREFIX . $rentaNatId] = $row['is_dead'] ? '' : isset($row['unpaid_renta_nat_arr'][$rentaNatId]) && is_numeric($row['unpaid_renta_nat_arr'][$rentaNatId]) ? $row['unpaid_renta_nat_arr'][$rentaNatId] : 0;
                    $returnData[$key][self::OVERPAID_RENT_NAT_PREFIX . $rentaNatId] = $row['is_dead'] ? '' : isset($row['overpaid_renta_nat_arr'][$rentaNatId]) && is_numeric($row['overpaid_renta_nat_arr'][$rentaNatId]) ? $row['overpaid_renta_nat_arr'][$rentaNatId] : 0;

                    if (!isset($natsValues[$rentaNatId])) {
                        $natsValues[$rentaNatId] = 0;
                    }

                    // Sum the rent in kind values for each type
                    $natsValues[$rentaNatId] += $natSum;
                }
            }
        }

        return [
            'natsValues' => $natsValues,
            'data' => $returnData,
        ];
    }

    /**
     * Generates header rows for the payroll grid export.
     *
     * @param array $columns an array of column definitions to be included in the header
     * @param array $data the data rows that may be used to determine dynamic (rent in kind) headers or formatting
     */
    private function generateHeaders($columns, $data)
    {
        foreach ($columns as $column => $value) {
            if (!$value) {
                unset($this->headers[$column]);
            }
        }

        if ($this->headers['natsDetailed']) {
            unset($this->headers['natsDetailed'], $this->headers['rentInKind'], $this->headers['paidRentInKind'], $this->headers['unpaidRentInKind'], $this->headers['overpaidRentInKind']);
            $this->unsetArrayElementByValue($this->mergeRowsHeadersByContract, 'paidRentInKind');
            $this->unsetArrayElementByValue($this->mergeRowsHeadersByContract, 'unpaidRentInKind');
            $this->unsetArrayElementByValue($this->mergeRowsHeadersByContract, 'overpaidRentInKind');

            // Generate headers for rent in kind. It is needed to be separated for each type of rent in kind, in order to be ordered by group.
            // For example first headers will be for rents in kind, after all rents in kind are filled, the next headers will be for paid rents in kind, then unpaid rents in kind and so on.
            foreach ($data as $row) {
                foreach ($row['renta_nat_info'] as $rentaNat) {
                    $this->fillHeadersInfo($rentaNat, self::RENT_NAT_PREFIX, 'Рента в натура', $columns);
                }
            }
            foreach ($data as $row) {
                foreach ($row['renta_nat_info'] as $rentaNat) {
                    $this->fillHeadersInfo($rentaNat, self::PAID_RENT_NAT_PREFIX, 'Платена рента в натура', $columns);
                }
            }
            foreach ($data as $row) {
                foreach ($row['renta_nat_info'] as $rentaNat) {
                    $this->fillHeadersInfo($rentaNat, self::UNPAID_RENT_NAT_PREFIX, 'Оставаща рента в натура', $columns);
                }
            }
            foreach ($data as $row) {
                foreach ($row['renta_nat_info'] as $rentaNat) {
                    $this->fillHeadersInfo($rentaNat, self::OVERPAID_RENT_NAT_PREFIX, 'Надплатена рента в натура', $columns);
                }
            }
        }

        $this->headers['date'] = 'Дата';
        $this->headers['receiver'] = 'Получил';
        $this->headers['sign'] = 'Подпис';
    }

    private function fillHeadersInfo($rentaNat, $prefix, $label, $columns)
    {
        $natKey = $prefix . $rentaNat['renta_nat_id'];

        $this->headers[$natKey] = $label . ': ' . $rentaNat['renta_nat_name'] . ' (' . $rentaNat['unit_name'] . ')';

        if (!in_array($this->headers[$natKey], $this->summableColumns)) {
            $this->summableColumns[] = $this->headers[$natKey];
        }

        if (!in_array($natKey, $this->mergeRowsHeadersByContract) && (false == $columns['natsDetailed'] || self::RENT_NAT_PREFIX != $prefix)) {
            $this->mergeRowsHeadersByContract[] = $natKey;
        }

        if (!in_array($rentaNat['renta_nat_id'], $this->usedRentaTypes)) {
            $this->usedRentaTypes[] = $rentaNat['renta_nat_id'];
        }
    }

    /**
     * Separates owners by their associated plots for a given year.
     *
     * @param int $year the year for which the separation is to be performed
     * @param array $owners an array of owner data to be separated by plots
     *
     * @return array returns an array where owners are grouped by their plots
     */
    private function separateOwnersByPlots($year, array $owners): array
    {
        $result = [];
        $paymentsController = new PaymentsController($this->User->Database);

        foreach ($owners as $owner) {
            $ownerResult = $paymentsController->getOwnerPayroll($year, $owner['owner_id'], $owner['path']);

            $result = array_merge($result, $ownerResult);
        }

        return $result;
    }

    /**
     * Retrieves the leaf owners from a hierarchical array of owners.
     *
     * This method processes the provided array of owners and returns an array
     * containing only the leaf nodes (owners without children) in the hierarchy.
     *
     * @param array $owners the hierarchical array of owners to process
     *
     * @return array an array of leaf owners extracted from the input
     */
    private function getLeafOwners(array $owners): array
    {
        $result = [];
        foreach ($owners as $owner) {
            // If the owner has no children meants that the owner is live and add it to the result
            // If the owner has children but has paid rent as well, add it to the result but show only the paid rents, all other colums like areas or rents have to be set to 0
            // This is needed for correctly displaying the payroll totals. In other case, the payroll totals will be with duplicated areas for examples (for the parent and children).
            if (empty($owner['children']) || (!empty($owner['children']) && $owner['paid_renta'] > 0)) {
                $result[] = $owner;
            }

            if (!empty($owner['children'])) {
                $result = array_merge($result, $this->getLeafOwners($owner['children']));
            }
        }

        return $result;
    }

    /**
     * Aggregates owner information into a structured array.
     *
     * @param array $owners array of owner data to be aggregated
     * @param bool $includeParentId Optional. Whether to include the parent ID in the aggregation. Default is false.
     * @param bool $inpludePlotId Optional. Whether to include the plot ID in the aggregation. Default is false.
     *
     * @return array the aggregated owner information
     */
    private function aggregateOwners(array $owners, bool $includeParentId = false, $inpludePlotId = false): array
    {
        $paymentsController = new PaymentsController($this->User->Database);

        $result = [];
        foreach ($owners as $owner) {
            $ownerKey = $owner['owner_id'];

            if ($includeParentId) {
                $ownerKey .= '_' . $owner['owner_parent_id'];
            }
            if ($inpludePlotId) {
                $ownerKey .= '_' . $owner['plot_id'] . '_' . $owner['plot_rent_type_id'];
            }

            if (!isset($result[$ownerKey])) {
                $result[$ownerKey] = $owner;
            } else {
                $result[$ownerKey] = $paymentsController->calculateOwnerData($result[$ownerKey], $owner);
            }
        }

        return $result;
    }

    // Помощен метод, който да вземе индекса на даден ключ/стойност
    // в асоциативен масив
    private function getAssocArrayIndex($array, $searchedKey, $searchByKey = false)
    {
        $tmpCounter = 0;
        if ($searchByKey) {
            if (!array_key_exists($searchedKey, $array)) {
                return -1;
            }
            foreach ($array as $key => $value) {
                if ($key != $searchedKey) {
                    $tmpCounter++;
                } else {
                    return $tmpCounter;
                }
            }
        } else {
            if (!array_search($searchedKey, $array)) {
                return -1;
            }
            foreach ($array as $key => $value) {
                if ($key != $searchedKey) {
                    $tmpCounter++;
                } else {
                    return $tmpCounter;
                }
            }
        }
    }

    // Помощен метод, който да създава footer-a
    // и добавя формула за сума
    // на база брой резултати и
    // информация дали колоната трябва да бъде сумирана или не
    private function createFooter($data)
    {
        $countRows = count($data);

        foreach ($this->headers as $key => $value) {
            $footer[$key] = '';
        }

        $footer['ownerNames'] = 'ОБЩО';
        $start = 2;
        $end = $countRows + 1;

        $headerKeys = array_keys($this->headers);
        $headerValues = array_values($this->headers);

        $letters = $this->mapColumnIndex($this->headers);

        foreach ($this->summableColumns as $column) {
            $index = array_search($column, $headerValues);
            if ($index) {
                $tmpColumn = $headerKeys[$index];
                $footer[$tmpColumn] = '=ROUND(SUM(' . $letters[$index] . $start . ':' . $letters[$index] . $end . '), 3)';
            }
        }

        return [$footer];
    }

    // Помощен метод, който взема всички колони,
    // отбелязани като колони за сумиране,
    // добавяйки ги в отделен помощен масив
    private function getSummableColumns($columns)
    {
        foreach ($columns as $column) {
            if ($column['summable']) {
                array_push($this->summableColumns, $column['text']);
            }
        }
    }

    // Помощен метод, който взема
    // индексите на отделните колони, в
    // зависимост от броя на всички колони,
    // които ще участват в експорта.
    private function mapColumnIndex($headers)
    {
        $columnIndexes = [];
        $letters = range('A', 'Z');
        $headersCount = count($headers);
        $letterCount = count($letters);
        $finalLetters = $letters;
        for ($i = 0; $i < $letterCount; $i++) {
            $finalLetters[] = 'A' . $letters[$i];
        }
        for ($i = 0; $i < $letterCount; $i++) {
            $finalLetters[] = 'B' . $letters[$i];
        }
        for ($i = 0; $i < $letterCount; $i++) {
            $finalLetters[] = 'C' . $letters[$i];
        }

        return $finalLetters;
    }

    /**
     * returns the children of a person.
     *
     * @param array $parent
     * @param string $parentNumber
     */
    private function _getChildren($parent, $parentNumber)
    {
        if (is_array($parent['children'])) {
            for ($j = 0; $j < $children_count = count($parent['children']); $j++) {
                $heritor = $parent['children'][$j];
                $heritor['farming_year'] = $parent['farming_year'];

                $heritor['number'] = $parentNumber . '.' . ($j + 1);
                $this->allOwnersForPayroll[] = $heritor;
                if (!empty($heritor['children'])) {
                    $this->_getChildren($heritor, $parentNumber . '.' . ($j + 1));
                }
            }
        }
    }

    private function strrtrim($strip, $message)
    {
        // break message apart by strip string
        $lines = explode($strip, $message);
        $last = '';
        // pop off empty strings at the end
        do {
            $last = array_pop($lines);
        } while (empty($last) && (count($lines)));
        // re-assemble what remains
        return implode($strip, array_merge($lines, [$last]));
    }

    private function setEkateData()
    {
        $UsersController = new UsersController('Users');
        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'return' => ['DISTINCT(ekate)'],
            'tablename' => $UserDbController->DbHandler->tableKVS,
        ];

        $results = $UserDbController->getItemsByParams($options);

        $return = [];
        $countResults = count($results);
        // todo: make a single query using in , like select ekatte_code , ekatte_name from su_ekatte WHERE ekatte_code IN ('73242' , '46749')
        for ($i = 0; $i < $countResults; $i++) {
            $return[$results[$i]['ekate']] = $UsersController->getEkatteName($results[$i]['ekate']) . ' (' . $results[$i]['ekate'] . ')';
        }
        $this->ekateData = $return;
    }

    private function removeUnusedNats($natValues)
    {
        foreach ($natValues as $key => $value) {
            if (0 == $value) {
                unset($this->headers[self::RENT_NAT_PREFIX . $key], $this->headers[self::PAID_RENT_NAT_PREFIX . $key], $this->headers[self::UNPAID_RENT_NAT_PREFIX . $key], $this->headers[self::OVERPAID_RENT_NAT_PREFIX . $key]);
            }
        }
    }

    private function getColumnLetterByHeader($header)
    {
        $letters = range('A', 'Z');

        // get the position of key $header in $this->headers
        $keys = array_keys($this->headers);
        $index = array_search($header, $keys);

        if (false === $index) {
            return '';
        }

        // Calculate the column letter based on the index
        $columnLetter = '';
        while ($index >= 0) {
            $columnLetter = $letters[$index % 26] . $columnLetter;
            $index = (int)($index / 26) - 1;
        }

        return $columnLetter;
    }

    private function unsetArrayElementByValue(&$array, $value)
    {
        $key = array_search($value, $array);
        if (false !== $key) {
            unset($array[$key]);
        }
    }
}
