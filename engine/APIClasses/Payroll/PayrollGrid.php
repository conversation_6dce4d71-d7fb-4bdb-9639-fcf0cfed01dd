<?php

namespace TF\Engine\APIClasses\Payroll;

use Prado\Web\Services\TRpcApiProvider;
use Prado\Web\Services\TRpcServer;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\Payments\PaymentsController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbAreaTypes\UserDbAreaTypesController;
use TF\Engine\Plugins\Core\UserDbOwners\UserDbOwnersController;
use TF\Engine\Plugins\Core\UserDbPayments\UserDbPaymentsController;
use TF\Engine\Plugins\Core\UserDbPlotCategoriesType\UserDbPlotCategoriesTypeController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Payroll Grid.
 *
 * @rpc-module Payroll
 *
 * @rpc-service-id payroll-grid
 */
class PayrollGrid extends TRpcApiProvider
{
    public $renta_types = [];
    public $renta_types_values = [];
    public $iterator = 0;
    public $relation_id;
    public $all_renta;
    public $all_renta_detailed;
    public $percent = [];
    public $arrayHelper;
    public $total_heritors_paid_renta = 0;
    public $total_heritors_over_paid_renta = 0;
    public $total_heritors_paid_renta_by_amount = 0;
    public $total_heritors_paid_renta_by_nat_amount = 0;
    public $total_heritors_unpaid_renta = 0;
    public $total_heritors_sum_by_paid_renta = 0;
    public $total_heritors_sum_area = 0;
    public $ownerArea = 0;
    public $total_heritors_paid_renta_by = [];
    public $total_heritors_paid_renta_by_nat = [];
    public $total_heritors_paid_renta_nat = [];
    public $total_heritors_all_paid_renta_nat_by_detailed = [];
    public $total_heritors_unpaid_renta_nat = [];
    public $total_heritors_over_paid_renta_nat = [];
    public $total_heritors_sum_by_paid_renta_nat = [];
    protected $return = [
        'rows' => [],
        'total' => 0,
        'footer' => [],
    ];

    /**
     * PayrollGrid constructor.
     */
    public function __construct(TRpcServer $rpcServer)
    {
        parent::__construct($rpcServer);
        $FarmingController = new FarmingController('Farming');
        $this->arrayHelper = $FarmingController->ArrayHelper;
    }

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readPayrollGrid'],
                'validators' => [
                    'data' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * Read payroll grid.
     *
     * @api-method read
     *
     * @param array|object $data
     *                           {
     *                           #item string type               -The payroll type.
     *                           #item boolean is_heritor        -User is heritor ot not.
     *                           #item string payroll_from_date  -The payroll from date.
     *                           #item string payroll_to_date    -The payroll to date.
     *                           #item string payroll_ekate      -The payroll ekate.
     *                           #item string farming_year       -The farming year.
     *                           #item int owner_id              -The owner id.
     *                           }
     * @param int|string $page -The current page number
     * @param int|string $rows -Rows per page
     * @param string $sort -A grid column by which the grid is sorted
     * @param string $order -The sort order ASC/DESC
     *
     * @return array result
     *               {
     *               #item array rows                -The results.
     *               #item string total              -The count of all results.
     *               #item array footer              -The footer results.
     *               }
     */
    public function readPayrollGrid(array $data, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        if (null !== $data) {
            $_POST = array_merge($_POST, (array)$data);
        }
        if (!$_POST['type']) {
            return $this->return;
        }

        // add grid properties to POST method
        $_POST['rows'] = $rows;
        $_POST['pager'] = $page;

        // check because for export and print $sort and $order are in $data array
        if ('' != $sort) {
            $_POST['sort'] = $sort;
        }
        if ('' != $order) {
            $_POST['order'] = $order;
        }

        $UserDbController = new UserDbController($this->User->Database);
        $paymentsController = new PaymentsController($this->User->Database);
        $FarmingController = new FarmingController('Farming');

        // get renta types
        $options = [
            'tablename' => $UserDbController->DbHandler->tableRentaTypes,
        ];

        $renta_results = $UserDbController->getItemsByParams($options, false, false);

        // create renta types array
        $count_renta_results = count($renta_results);
        for ($i = 0; $i < $count_renta_results; $i++) {
            $this->renta_types[$renta_results[$i]['id']] = $renta_results[$i]['name'] . '(' . $GLOBALS['Contracts']['renta_units'][$renta_results[$i]['unit']]['name'] . ')';
            $this->renta_types_values[$renta_results[$i]['id']] = $renta_results[$i]['unit_value'];
        }

        $userFarmings = $FarmingController->getUserFarmings();
        $userFarmingIds = array_keys($userFarmings);
        $farmingIds = $this->arrayHelper->filterEmptyStringArr($data['payroll_farming']);
        $farmingIds = isset($farmingIds) && count($farmingIds) > 0
            ? array_intersect($farmingIds, $userFarmingIds)
            : [...$userFarmingIds, null];
        $data['payroll_farming'] = $farmingIds;

        switch ($_POST['type']) {
            case 'sums':
                $year = $data['farming_year'];
                if (empty($data['payroll_ekate'])) {
                    unset($data['payroll_ekate']);
                }
                unset($data['farming_year'], $data['type'], $data['payroll_from_date'], $data['payroll_to_date']);

                $allPayrollData = $paymentsController->getOwnersPayroll($year, $data);
                $allPayrollData['footer'][0]['area_type'] = 'ОБЩО';

                return [
                    'rows' => $allPayrollData['footer'],
                ];
            case 'owners':
                // Отделя се стопанската година и се премахват ненужните данни. Оставят се само тези от филтрите.
                // Това се прави за да се разграничи, кога се използват филтри и кога не.
                $year = $data['farming_year'];
                if (empty($data['payroll_ekate'])) {
                    unset($data['payroll_ekate']);
                }
                unset($data['farming_year'], $data['type'], $data['payroll_from_date'], $data['payroll_to_date']);

                return $paymentsController->getOwnersPayroll($year, $data, $rows, $page);
            case 'payroll_by_owner':
                $year = $data['farming_year'];
                $ownerId = $data['owner_id'];
                $path = $data['path'];
                if (empty($data['payroll_ekate'])) {
                    unset($data['payroll_ekate']);
                }
                unset($data['farming_year'], $data['type'], $data['payroll_from_date'], $data['payroll_to_date']);

                $pagedReturn['rows'] = $paymentsController->getOwnerPayroll($year, $ownerId, $path, $data);

                return $pagedReturn;
            default:
                return $this->return;
        }
    }

    /**
     * @param null $dataExportPrint
     *
     * @return array
     */
    public function getOwnersPayroll($dataExportPrint = null)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);
        $UsersController = new UsersController('Users');
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);
        $UserDbPlotCategoriesTypeController = new UserDbPlotCategoriesTypeController($this->User->Database);
        $FarmingController = new FarmingController('Farming');

        // create the default return
        $return = [
            'rows' => [],
            'total' => 0,
            'footer' => [],
        ];

        // data to export or print
        if (null !== $dataExportPrint) {
            $_POST = $dataExportPrint;
        }

        list($farming_years, $farming_years_str, $farming_years_string) = $this->getFarmings($UsersController);

        $userFarmings = $FarmingController->getUserFarmings();
        $userFarmingIds = array_keys($userFarmings);
        $farmingIds = $this->arrayHelper->filterEmptyStringArr($_POST['payroll_farming']);
        $farmingIds = isset($farmingIds) && count($farmingIds) > 0
            ? array_intersect($farmingIds, $userFarmingIds)
            : $userFarmingIds;

        $filteredIds = [];

        if (!empty($_POST['owner_egns']) || !empty($_POST['company_eiks'])) {
            $filteredIds = $this->getOwnersIds($UserDbController, $_POST['owner_egns'], $_POST['company_eiks']);
        }

        $ownerTypeCompareType = 'IN';
        $ownerTypes = [0, 1];

        if (array_key_exists('owner_type', $_POST) && strlen($_POST['owner_type'])) {
            $ownerTypeCompareType = '=';
            $ownerTypes = (int) $_POST['owner_type'];

            if (false !== strpos($_POST['owner_type'], ',')) {
                $ownerTypeCompareType = 'IN';
                $ownerTypes = explode(',', $_POST['owner_type']);
            }
        }

        $options = [
            'custom_counter' => 'COUNT(DISTINCT(o.id))',
            'offset' => ($_POST['pager'] - 1) * $_POST['rows'],
            'limit' => $_POST['rows'],
            'sort' => $_POST['sort'],
            'order' => $_POST['order'],
            'return' => [
                // get owners data
                'DISTINCT(o.id) as owner_id',
                $UserDbOwnersController::isDead('o', [$_POST['payroll_from_date'], $_POST['payroll_to_date']]),
                'o.owner_type',
                'o.iban',
                "(CASE WHEN owner_type = 1 THEN name || ' ' || surname || ' ' || lastname ELSE company_name END) as owner_names",
                '(CASE WHEN owner_type = 1 THEN egn ELSE eik END) as egn_eik',
                'name as first_name',
                'surname',
                'lastname',
                'phone',
                'mobile',
                "string_agg(DISTINCT( (CASE WHEN a.id IS NULL THEN c.c_num ELSE a.c_num END) || ' - ' || r.rep_name || ' ' || r.rep_surname || ' ' || r.rep_lastname), ',<br/>') as rep_names",
                "CASE WHEN string_agg(DISTINCT(r.rent_place), ',<br/>') IS NULL THEN string_agg(DISTINCT(o.rent_place), ',<br/>') ELSE string_agg(DISTINCT(r.rent_place), ',<br/>') END AS rent_place",
                // get owners area
                'array_agg((pc.area_for_rent * po.percent / 100)) as contr_area',
                'SUM((pc.area_for_rent * po.percent / 100)::numeric) as area',
                'SUM(
                        (
                            CASE WHEN 
                                coalesce(kvs_allowable_area, allowable_area) < contract_area 
                            THEN 
                                coalesce(kvs_allowable_area, allowable_area) * po.percent / 100 
                            ELSE 
                                contract_area * po.percent / 100
                            END
                        )
                ) as cultivated_area',
                'case when max(pu.id) > 0 then true else false end as has_personal_use',
                'array_agg(cr.id) as charged_renta_id',
                'c.farming_id as farming',
                'SUM((
                CASE WHEN a.renta IS NULL THEN 
                    (CASE
                    WHEN pc.rent_per_plot IS NOT NULL THEN 0 ELSE C.renta
                    END
                ) ELSE (
                    CASE
                    WHEN pc.rent_per_plot IS NOT NULL THEN
                       0
                    ELSE
                        A .renta
                    END
                ) END ) * (((pc.area_for_rent * po.percent / 100)::numeric))) as contract_renta',
                "array_agg((
                    CASE WHEN a.renta IS NULL THEN 
                        (CASE
                        WHEN pc.rent_per_plot IS NOT NULL THEN
                            0
                        ELSE
                            C .renta
                        END
                    ) ELSE (
                        CASE
                        WHEN pc.rent_per_plot IS NOT NULL THEN
                            0
                        ELSE
                            A .renta
                        END
                    ) END
                ) * (((pc.area_for_rent * po.percent / 100)::numeric))  || '|' || c.id || '|' || kvs.gid || '|' || pc.id || '|' || (pc.area_for_rent * po.percent / 100)::numeric || '|' || CASE WHEN a.renta IS NULL THEN 
                        (CASE
                        WHEN pc.rent_per_plot IS NOT NULL THEN
                            0
                        ELSE
                            C .renta
                        END
                    ) ELSE (
                        CASE
                        WHEN pc.rent_per_plot IS NOT NULL THEN
                            0
                        ELSE
                            A .renta
                        END
                    ) END
                    || '|' || (CASE WHEN pc.rent_per_plot IS NOT NULL THEN pc.rent_per_plot ELSE 0 END)) as contracts_plots_renta",

                'SUM((
                        CASE
                        WHEN pc.rent_per_plot IS NOT NULL THEN
                            0
                        ELSE
                            cr.renta
                        END
                    ) * (pc.area_for_rent * po.percent / 100)) as charged_renta',
                "ARRAY_AGG ((
                    (
                        cr.renta
                    ) * (pc.area_for_rent * (po.percent / 100))) :: NUMERIC || '|' || C . ID || '|' || kvs.gid || '|' || pc.id || '|' || (pc.area_for_rent * po.percent / 100)::numeric || '|' || cr.renta || '|' || (CASE WHEN pc.rent_per_plot IS NOT NULL THEN pc.rent_per_plot ELSE 0 END)) AS contracts_plots_charged_renta",

                "ARRAY_AGG (
                    (
                        CASE
                        WHEN pc.rent_per_plot IS NOT NULL THEN
                            pc.rent_per_plot
                        ELSE
                            NULL
                        END
                    ) * (
                        (
                            pc.area_for_rent * po. PERCENT / 100
                        )
                    ) :: NUMERIC || '|' || C . ID || '|' || kvs.gid 
                ) AS contracts_rent_per_plot",

                'array_agg(c.id) as contract_array',
                'array_agg(DISTINCT(c.c_num)) as c_num_array',
                'array_agg(DISTINCT(c.sv_num)) as sv_num_array',
                'array_agg(kvs.gid) as plots_array', "array_agg(c.id || '|' || kvs.gid || '|' || po.percent) as plots_percent",
                'array_agg(case when a.id is not null then a.id else c.id end) as contract_anex_arr',
                'array_agg(kvs.kad_ident) as plots_name_array',
                "string_agg(( pc.id )::TEXT, ',')  AS plt_contr_rel_ids",
                "string_agg(( po.id )::TEXT, ',')  AS plt_owner_rel_ids",
                "string_agg((c.id || '|' || c.c_num || '|' || kvs.kad_ident || '|' || (pc.area_for_rent * po.percent / 100))::text, '~~') as pcrel",
                "array_agg(c.id || '|' || kvs.gid || '|' || (pc.area_for_rent * po.percent / 100) || '|' || pc.id) as pcrel_gid",
                '(SELECT array_agg(cr.id)
                   FROM su_contracts_rents cr
                   WHERE cr.contract_id = case when max(a.id) is not null then max(a.id) else max(c.id) end) 
                AS renta_nat_type_id',
            ],
            'where' => [
                'contract_type' => ['column' => 'nm_usage_rights', 'compare' => 'NOT IN', 'prefix' => 'c', 'value' => [1, 4]],
                'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
                'is_annnex' => ['column' => 'is_annex', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
                'annex_action' => ['column' => 'annex_action', 'compare' => '=', 'prefix' => 'pc', 'value' => 'added'],
                'is_heritor' => ['column' => 'is_heritor', 'compare' => '=', 'prefix' => 'po', 'value' => 'FALSE'],
                'active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'TRUE'],
                'percent' => ['column' => 'percent', 'compare' => '>', 'prefix' => 'po', 'value' => '0'],
                // filters
                'owner_type' => ['column' => 'owner_type', 'compare' => $ownerTypeCompareType, 'prefix' => 'o', 'value' => $ownerTypes],
                'ekate' => ['column' => 'ekate', 'compare' => $this->getPayrollEkateCompareType($_POST), 'prefix' => 'kvs', 'value' => $this->filterPayrollEkateValue($_POST['payroll_ekate'])],
                'farming' => ['column' => 'farming_id', 'compare' => 'IN', 'prefix' => 'c', 'value' => $farmingIds],
                'egn' => ['column' => 'egn', 'compare' => 'ILIKE', 'prefix' => 'o', 'value' => $_POST['egn']],
                'eik' => ['column' => 'eik', 'compare' => 'ILIKE', 'prefix' => 'o', 'value' => $_POST['eik']],
                'company_name' => ['column' => 'company_name', 'compare' => 'ILIKE', 'prefix' => 'o', 'value' => $_POST['company_name']],
                'rep_egn' => ['column' => 'rep_egn', 'compare' => 'ILIKE', 'prefix' => 'r', 'value' => $_POST['rep_egn']],
                'is_edited' => ['column' => "(case when kvs.is_edited = false then true else kvs.edit_active_from > '" . $_POST['payroll_to_date'] . "' end)", 'compare' => '=', 'value' => 'TRUE'],
            ],
            'rent_place' => $_POST['rent_place'],
            'rep_rent_place' => $_POST['rep_rent_place'],
            'group' => 'o.id,c.farming_id',
            'start_date' => $_POST['payroll_to_date'],
            'due_date' => $_POST['payroll_from_date'],
            'year_id' => $farming_years,
            'without_table_charged_natura' => true,
        ];

        if ($_POST['owner_names']) {
            $tmp_owner_names = preg_replace('/\s+/', '.*', $_POST['owner_names']);
            $tmp_owner_names = mb_strtolower($tmp_owner_names, 'UTF-8');
            $options['where']['owner_names'] = ['column' => "lower(TRIM (o.NAME)) || ' ' || lower(TRIM (o.surname)) || ' ' || lower(TRIM (o.lastname))", 'compare' => '~', 'value' => $tmp_owner_names];
            $options['group'] .= ',o.id';
        }
        if ($_POST['rep_names']) {
            $tmp_owner_names = preg_replace('/\s+/', '.*', $_POST['rep_names']);
            $tmp_owner_names = mb_strtolower($tmp_owner_names, 'UTF-8');
            $options['where']['rep_names'] = ['column' => "lower(TRIM (r.rep_name)) || ' ' || lower(TRIM (r.rep_surname)) || ' ' || lower(TRIM (r.rep_lastname))", 'compare' => '~', 'value' => $tmp_owner_names];
            $options['group'] .= ',o.id';
        }

        if (count($filteredIds)) {
            $options['where']['owner_ids'] = ['column' => 'id', 'compare' => 'IN', 'prefix' => 'o', 'value' => $filteredIds];
            unset($options['where']['egn'], $options['where']['eik']);
        }
        $ansestors = [];
        if ((isset($_POST['heritor_egn']) && '' != $_POST['heritor_egn'])
            || (isset($_POST['heritor_names']) && '' != $_POST['heritor_names'])) {
            $ancestors = $this->_getHeritorAncestors();
            if (empty($ancestors) || empty($ancestors[0])) {
                return $return;
            }
            $options['where']['ancestors'] = ['column' => 'id', 'compare' => 'IN', 'prefix' => 'o', 'value' => $this->arrayHelper->filterEmptyStringArr($ancestors)];
        }

        $counter = $UserDbPaymentsController->getPayrollData($options, true, false);

        if (0 == $counter[0]['count']) {
            return $this->return;
        }
        $results = $UserDbPaymentsController->getPayrollData($options);

        $personalUseOptions = [
            'owner_ids' => array_unique(array_column($results, 'owner_id')),
            'year' => $_POST['farming_year'],
            'chosen_years' => $_POST['farming_year'],
        ];
        $personalUse = $UserDbPaymentsController->getPersonalUseForOwners($personalUseOptions, false, false);

        $total_renta_nat = [];
        $total_paid_renta_nat = [];
        $total_paid_renta_by = [];
        $total_paid_renta_by_nat = [];
        $total_all_paid_renta_nat_by_detailed = [];
        $total_unpaid_renta_nat = [];
        $total_sum_paid_renta_nat = [];
        $total_paid_renta_by_amount = 0;
        $total_paid_renta_by_nat_amount = 0;
        $total_over_paid_renta = 0;
        $total_over_paid_renta_nat = [];
        $resultsCount = count($results);
        for ($i = 0; $i < $resultsCount; $i++) {
            $pca_elements = explode('~~', trim($results[$i]['pcrel'], '{}'));
            $pca_elements_gid = explode(',', trim($results[$i]['pcrel_gid'], '{}'));
            $contracts_plots_renta = explode(',', trim($results[$i]['contracts_plots_renta'], '{}'));
            $contracts_plots_charged_renta = explode(',', trim($results[$i]['contracts_plots_charged_renta'], '{}'));
            $plots_percent = explode(',', trim($results[$i]['plots_percent'], '{}'));
            $contracts_rent_per_plot = explode(',', trim($results[$i]['contracts_rent_per_plot'], '{}'));
            $totalPersonalUseArea = 0; // Тук ще се събере общата площ на личните ползвания, която ще се покаже в грида и която ще се извади от цялата площ

            foreach ($plots_percent as $element) {
                $launch = explode('|', $element);

                $results[$i]['owner_plots_percent'][$launch[0]][$launch[1]] = $launch[2];
            }

            foreach ($pca_elements as $element) {
                $launch = explode('|', $element);

                $results[$i]['plots_contracts_area_array'][] = [
                    'pc_id' => $launch[0],
                    'c_num' => $launch[1],
                    'plot_name' => $launch[2],
                    'area' => $launch[3],
                ];

                $results[$i]['total_area_contract'][$launch[0]] += $launch[3];
            }

            foreach ($pca_elements_gid as $element) {
                $launch = explode('|', $element);

                $results[$i]['plots_contracts_area_array_gids'][] = [
                    'pc_id' => $launch[0],
                    'plot_gid' => $launch[1],
                    'area' => $launch[2],
                    'rel_id' => $launch[3],
                ];
            }

            foreach ($contracts_rent_per_plot as $element) {
                $launch = explode('|', $element);

                $results[$i]['plots_contracts_rent_per_plot'][] = [
                    'renta_by_plot' => $launch[0],
                    'contract_id' => $launch[1],
                    'plot_gid' => $launch[2],
                ];
            }

            $plots_contracts_charged_renta = [];
            foreach ($contracts_plots_charged_renta as $element) {
                if ('NULL' == $element) {
                    continue;
                }

                $launch = explode('|', $element);

                $filteredPlotsWithRentPerPlot = array_filter($results[$i]['plots_contracts_rent_per_plot'], function ($elem) use ($launch) {
                    return $elem['plot_gid'] == $launch[2] && $elem['contract_id'] == $launch[1];
                });

                if (!empty($filteredPlotsWithRentPerPlot)) {
                    $RentPerPlot = reset($filteredPlotsWithRentPerPlot);
                    $results[$i]['plots_contracts_rent_per_plot'][key($filteredPlotsWithRentPerPlot)]['has_charged_renta'] = true;
                    $launch[0] = $RentPerPlot['renta_by_plot'];
                }

                $results[$i]['plots_contracts_charged_renta'][] = [
                    'charged_renta_by_plot' => $launch[0],
                    'contract_id' => $launch[1],
                    'plot_gid' => $launch[2],
                    'pc_rel_id' => $launch[3],
                    'plot_area' => $launch[4],
                    'plot_contract_charged_renta' => $launch[5],
                    'rent_per_plot' => $launch[6],
                ];

                $plots_contracts_charged_renta[$launch[1]][$launch[2]] = $launch[0];
            }

            foreach ($contracts_plots_renta as $element) {
                $launch = explode('|', $element);

                $filteredPlotsWithRentPerPlot = array_filter($results[$i]['plots_contracts_rent_per_plot'], function ($elem) use ($launch) {
                    return $elem['plot_gid'] == $launch[2] && $elem['contract_id'] == $launch[1];
                });

                if (!empty($filteredPlotsWithRentPerPlot)) {
                    $RentPerPlot = reset($filteredPlotsWithRentPerPlot);

                    if (true != $results[$i]['plots_contracts_rent_per_plot'][key($filteredPlotsWithRentPerPlot)]['has_charged_renta']) {
                        $launch[0] = $RentPerPlot['renta_by_plot'];
                    }
                }

                $results[$i]['plots_contracts_renta'][] = [
                    'renta_by_plot' => $launch[0],
                    'contract_id' => $launch[1],
                    'plot_gid' => $launch[2],
                    'pc_rel_id' => $launch[3],
                    'plot_area' => $launch[4],
                    'plot_contract_renta' => $launch[5],
                    'rent_per_plot' => round($launch[6], 2),
                ];
            }

            $contractPlotArea = [];
            $contractPlotsAreaSum = [];
            if (!empty($results[$i]['plots_contracts_area_array_gids'])) {
                foreach ($results[$i]['plots_contracts_area_array_gids'] as $key => $value) {
                    $contractId = $value['pc_id'];
                    $plotGid = $value['plot_gid'];
                    $areaPlot = $value['area'];

                    $contractPlotArea[$contractId][$plotGid] = $areaPlot;
                    $contractPlotsAreaSum[$contractId] += $areaPlot;
                }
            }

            $plotsStr = trim($results[$i]['plots_array'], '{}');

            $charged_renta_nat_id = explode(',', trim($results[$i]['charged_renta_id'], '{}'));
            $charged_renta_nat_id = array_unique($charged_renta_nat_id);
            $charged_renta_ids_text = implode(',', $charged_renta_nat_id);

            $contracts_data = [];
            $contr_area_array = explode(',', trim($results[$i]['contr_area'], '{}'));
            $contr_area_array = array_sum($contr_area_array);
            $contract_array = explode(',', trim($results[$i]['contract_array'], '{}'));
            $renta_nat_array = explode(',', trim($results[$i]['renta_nat'], '{}'));
            $owners_pu_renta_nat_array = explode(',', trim($results[$i]['owners_pu_renta_nat'], '{}'));
            $charged_renta_nat_array = explode(',', trim($results[$i]['charged_renta_nat'], '{}'));
            $renta_nat_type_id_array = explode(',', trim($results[$i]['renta_nat_type_id'], '{}'));
            $results[$i]['c_num_array'] = explode(',', trim($results[$i]['c_num_array'], '{}'));
            $results[$i]['sv_num_array'] = explode(',', trim($results[$i]['sv_num_array'], '{}'));
            $results[$i]['plots_name_array'] = explode(',', trim($results[$i]['plots_name_array'], '{}'));
            $implodedArr = implode(',', $contract_array);
            $contracts_anex_arr = trim($results[$i]['contract_anex_arr'], '{}');

            // count all heritors
            $pathOwner = $results[$i]['owner_id'] . '.*{1}';
            $options = [
                'return' => [
                    'h.id',
                ],
                'where' => [
                    'path' => ['column' => 'path', 'compare' => '~', 'value' => $pathOwner],
                ],
            ];

            $counterHeritors = $UserDbOwnersController->getOwnersHeritors($options, true);

            $paidOptions = [
                'custom_counter' => 'COUNT(DISTINCT(p.owner_id))',
                'order' => 'asc',
                'sort' => 'p.contract_id',
                'return' => [
                    'p.id as payment_id',
                    'p.owner_id as owner_id',
                    'p.contract_id as contract_id',
                    'case when pn.amount notnull and pn.unit_value notnull  then round((pn.amount * pn.unit_value)::numeric, 2) else round(p.amount::numeric, 2) end as trans_amount',
                    'case when pn.amount notnull then round(pn.amount::numeric,3) else round(p.amount_nat::numeric, 3) end as amount_nat',
                    'round(pn.amount::numeric, 3) as trans_amount_nat',
                    'round(pn.unit_value::numeric, 2) as unit_value',
                    'pn.nat_type as nat_type',
                    'p.paid_in',
                    'p.paid_from',
                    'rent.name as trans_nat_type_text',
                ],
                'where' => [
                    'owner_id' => ['column' => 'owner_id', 'compare' => '=', 'prefix' => 'p', 'value' => $results[$i]['owner_id']],
                    'path' => ['column' => 'path', 'compare' => 'IS', 'prefix' => 'p', 'value' => 'NULL'],
                    'contract_id' => ['column' => 'contract_id', 'compare' => 'IN', 'prefix' => 'p', 'value' => $contract_array],
                    'farming_year' => ['column' => 'farming_year', 'compare' => 'IN', 'prefix' => 'p', 'value' => $farming_years],
                ],
            ];

            $paidResults = $UserDbPaymentsController->getPaidData($paidOptions);
            $results[$i] = $this->addNewRentaPayrolls($paidResults, $results[$i]);
            $paidResultsCount = count($paidResults);
            // Платена рента в лева и натура чрез
            $paid_renta = 0;
            $paid_renta_by_nat = 0;
            $paid_renta_nat = [];
            $paid_renta_nat_by_nat = [];
            $paid_renta_nat_by_detailed = [];
            $paid_renta_nat_by_detailed_unit_value = [];
            $paymentIds = [];
            $paidRentaByContract = [];
            $paidRentaNatByContract = [];
            for ($m = 0; $m < $paidResultsCount; $m++) {
                $paidResult = $paidResults[$m];
                $renta_type = $paidResult['nat_type'];
                $contract_id = $paidResult['contract_id'];

                if (1 == $paidResult['paid_from']) {
                    // Изчисляване на платената рента по договор
                    $paidRentaByContract[$contract_id] += $paidResult['trans_amount'];
                    if (2 == $paidResult['paid_in']) {
                        $paid_renta_nat[$renta_type] += $paidResult['amount_nat'];

                        continue;
                    }

                    $paid_renta += $paidResult['trans_amount'];
                } elseif (2 == $paidResult['paid_from']) {
                    // Изчисляване на платената рента по договор
                    $paidRentaNatByContract[$contract_id][$renta_type] += $paidResult['trans_amount_nat'];

                    if (2 == $paidResult['paid_in']) {
                        $paid_renta_nat_by_nat[$renta_type] += $paidResult['trans_amount_nat'];

                        continue;
                    }

                    // Платена рента в натура детайлно
                    if (array_key_exists($renta_type, $paid_renta_nat_by_detailed)) {
                        $paid_renta_nat_by_detailed[$renta_type] += $paidResult['trans_amount_nat'];
                    } else {
                        $paid_renta_nat_by_detailed[$renta_type] = $paidResult['trans_amount_nat'];
                        $paid_renta_nat_by_detailed_unit_value[$renta_type] = $paidResult['unit_value'];
                    }

                    $paymentIds[] = $paidResult['payment_id'];
                    $paid_renta_by_nat += $paidResult['trans_amount'];
                }
            }

            $paidRentaByPlot = [];
            if (!empty($contractPlotArea)) {
                foreach ($contractPlotArea as $contractId => $plot) {
                    foreach ($plot as $plotGid => $area) {
                        $totalArea = $contractPlotsAreaSum[$contractId];

                        if ($totalArea <= 0) {
                            break;
                        }

                        $areaPercent = $area / $totalArea;
                        $paidRentaByContractSum = $paidRentaByContract[$contractId];
                        $paidRentaByPlots = $paidRentaByContractSum * $areaPercent;

                        $paidRentaByPlot[$contractId][$plotGid] = $paidRentaByPlots;
                    }
                }
            }

            $results[$i]['paid_renta_by_contract'] = $paidRentaByContract;
            $results[$i]['paid_renta_nat_by_contract'] = $paidRentaNatByContract;

            // Начислена рента в натура и конвертирана от натура в лева
            $charged_rents = $UserDbController->DbHandler->getDataByQuery(
                "SELECT 
                    (CASE WHEN crn.nat_is_converted = TRUE THEN NULL ELSE crn.amount * ((pc.area_for_rent * po.percent / 100))  END) AS charged_renta_nat,
                    (CASE WHEN crn.nat_is_converted = TRUE THEN NULL ELSE crn.amount END) AS charged_renta_nat_value,
			        c.id as contract_id, 
                    kvs.gid as plot_id, 
                    pc.area_for_rent as contract_area,
                    pc.rent_per_plot as rent_per_plot,
                    pc.id as pc_rel_id,
                    po.owner_id,
                    po.percent,
                    crn.nat_type,
			        (CASE WHEN crn.nat_is_converted = TRUE THEN (crn.amount * crn.nat_unit_price) * ((pc.area_for_rent * po.percent / 100)) ELSE NULL END) AS converted_charged_renta_nat,
			        (CASE WHEN crn.nat_is_converted = TRUE THEN (crn.amount * crn.nat_unit_price) ELSE NULL END) AS converted_charged_renta_nat_value 
			    FROM su_contracts c
                LEFT JOIN su_contracts a ON(a.parent_id = c.id
                                            AND a.active = TRUE
                                            AND a.start_date <= '{$_POST['payroll_to_date']}'
                                            AND a.due_date >= '{$_POST['payroll_from_date']}')
                INNER JOIN su_contracts_plots_rel pc ON(pc.contract_id = (CASE WHEN a.id IS NULL THEN c.id ELSE a.id END))
                INNER JOIN layer_kvs kvs ON(kvs.gid = pc.plot_id)
                LEFT JOIN su_plots_owners_rel po ON(po.pc_rel_id = pc.id)
                LEFT JOIN su_personal_use pu ON (pu.owner_id = {$results[$i]['owner_id']} AND pu.year in {$farming_years_string} AND pu.pc_rel_id = pc.id)
                LEFT JOIN su_charged_renta cr ON(cr.plot_id = kvs.gid
                                                 AND cr.contract_id = c.id
                                                 AND cr.YEAR in {$farming_years_string}
                                                 AND cr.owner_id = {$results[$i]['owner_id']})
                LEFT JOIN su_charged_renta_natura crn ON(crn.renta_id = cr.id)
                where c.id in ({$implodedArr})
                and kvs.gid in ({$plotsStr})
                and pc.annex_action ='added'
			    and po.owner_id = {$results[$i]['owner_id']}
                and po.path is null
			    and cr.owner_id = {$results[$i]['owner_id']}"
            );

            // По договор рента в натура
            $renta_nats = $UserDbController->DbHandler->getDataByQuery(
                "SELECT 
                    crt.renta_value * ((pc.area_for_rent * po.percent / 100)) AS renta_nat, 
                    kvs.gid as plot_id, 
                    crt.renta_value,
			        pc.area_for_rent as contract_area,
                    pc.id as pc_rel_id, 
                    po.percent, 
                    c.id as contract_id, 
                    parent_id, 
                    po.owner_id, 
                    crt.renta_id 
                FROM su_contracts c
			    INNER JOIN su_contracts_plots_rel pc ON(pc.contract_id = c.id)
			    INNER JOIN layer_kvs kvs ON(kvs.gid = pc.plot_id)
			    LEFT JOIN su_plots_owners_rel po ON(po.pc_rel_id = pc.id)
			    LEFT JOIN su_personal_use pu ON (pu.owner_id = {$results[$i]['owner_id']} AND pu.year in {$farming_years_string} AND pu.pc_rel_id = pc.id)
			    LEFT join su_contracts_rents crt ON (c.id = crt.contract_id) 
			where c.id in ({$contracts_anex_arr})
            and kvs.gid in ({$plotsStr})
			and pc.annex_action ='added'
			and po.owner_id = {$results[$i]['owner_id']}
            and pc.rent_per_plot isnull
            "
            );

            $contract_rents = [];
            $charged_rents_arr = [];
            $rentaNatByPlot = [];
            foreach ($renta_nats as $renta_nat) {
                // Проверка за лично ползване за конкретния имот и намиране на площта за лично ползване
                $totalContractArea = $results[$i]['total_area_contract'][$renta_nat['contract_id']];
                $rentaNatPuArea = $UserDbPaymentsController->getPersonalUseArea($personalUse, $renta_nat['owner_id'], $renta_nat, $totalContractArea);

                $contractAreaNats = $renta_nat['contract_area'] * ($renta_nat['percent'] / 100);
                $contractAreaNatsWitoutPuArea = $contractAreaNats - $rentaNatPuArea;

                $rentaNat = $renta_nat['renta_value'] * $contractAreaNatsWitoutPuArea;

                if (!isset($contract_rents[$renta_nat['renta_id']])) {
                    $contract_rents[$renta_nat['renta_id']] = $rentaNat;
                } else {
                    $contract_rents[$renta_nat['renta_id']] += $rentaNat;
                }

                // get renta natura by plot
                $contractID = $renta_nat['contract_id'];

                if ($renta_nat['parent_id']) {
                    $contractID = $renta_nat['parent_id'];
                }

                $plotID = $renta_nat['plot_id'];
                $rentaNatID = $renta_nat['renta_id'];
                $rentaNaturaValue = $rentaNat;

                if ($rentaNatID) {
                    $rentaNatByPlot[$contractID][$plotID][$rentaNatID] = $rentaNaturaValue;
                }
            }

            $convertedChargedRentaByPlot = [];
            $chargedRentaNaturaByPlot = [];
            $results[$i]['converted_renta_nat'];
            $results[$i]['plots_contracts_charged_renta_nat_down_grid'] = [];

            foreach ($charged_rents as $charged_rent) {
                // can't charge rent of plots with individual rents
                if ($charged_rent['rent_per_plot'] > 0) {
                    continue;
                }

                $contractID = $charged_rent['contract_id'];
                $plotID = $charged_rent['plot_id'];
                $naturaType = $charged_rent['nat_type'];

                // Проверка за лично ползване за конкретния имот и намиране на площта за лично ползване
                $totalContractArea = $results[$i]['total_area_contract'][$charged_rent['contract_id']];
                $chargedRentaNatPuArea = $UserDbPaymentsController->getPersonalUseArea($personalUse, $results[$i]['owner_id'], $charged_rent, $totalContractArea);

                $chargedContractAreaNats = $charged_rent['contract_area'] * ($charged_rent['percent'] / 100);
                $contractAreaNatsWitoutPuArea = round($chargedContractAreaNats - $chargedRentaNatPuArea, 3);

                $chargedRentaNat = $charged_rent['charged_renta_nat_value'] * $contractAreaNatsWitoutPuArea;

                if (!isset($charged_rents_arr[$charged_rent['nat_type']])) {
                    $charged_rents_arr[$charged_rent['nat_type']] = $chargedRentaNat;
                } else {
                    $charged_rents_arr[$charged_rent['nat_type']] += $chargedRentaNat;
                }

                $results[$i]['plots_contracts_charged_renta_nat_values'][$contractID][$plotID][$naturaType] = $charged_rent['charged_renta_nat_value'];

                if (!is_null($charged_rent['converted_charged_renta_nat_value']) && $charged_rent['converted_charged_renta_nat_value'] >= 0) {
                    $convertedChargedRenta = $charged_rent['converted_charged_renta_nat_value'] * $contractAreaNatsWitoutPuArea;
                    $results[$i]['converted_renta_nat'] += $convertedChargedRenta;
                    $convertedChargedRentaByPlot[$contractID][$plotID] += $convertedChargedRenta;
                }

                if ($naturaType && !is_null($chargedRentaNat)) {
                    $chargedRentaNaturaByPlot[$contractID][$plotID][$naturaType] = $chargedRentaNat;

                    $results[$i]['plots_contracts_charged_renta_nat_down_grid'][$contractID][$plotID][$naturaType] = $chargedRentaNat;
                }
            }

            $sumRentaNatByContract = [];
            $sumDueRentaNat = [];
            $results[$i]['plots_contracts_renta_nat_down_grid'] = [];

            if (!empty($rentaNatByPlot)) {
                foreach ($rentaNatByPlot as $contractID => $rentaValue) {
                    foreach ($rentaValue as $plotID => $value) {
                        foreach ($value as $rentaType => $naturaValue) {
                            if (is_null($chargedRentaNaturaByPlot[$contractID][$plotID][$rentaType])) {
                                $sumDueRentaNat[$rentaType] += $naturaValue;
                                $results[$i]['plots_contracts_renta_nat_down_grid'][$contractID][$plotID][$rentaType] = $naturaValue;
                            } else {
                                if (is_null($sumDueRentaNat[$rentaType])) {
                                    $sumDueRentaNat[$rentaType] = '0.000';
                                }
                            }

                            $sumRentaNatByContract[$contractID][$rentaType] += $naturaValue;
                        }
                    }
                }
            }

            // Сортиране на дължимата рентата в натура по договор спрямо renta_type_id
            ksort($sumDueRentaNat);

            $results[$i]['plots_contracts_due_renta_nat'] = $rentaNatByPlot;
            $sumChargedRentaNaturaByContract = [];
            if (!empty($chargedRentaNaturaByPlot)) {
                foreach ($chargedRentaNaturaByPlot as $contractID => $chargedValue) {
                    foreach ($chargedValue as $plotID => $value) {
                        foreach ($value as $rentaType => $naturaValue) {
                            $rentaNatByPlot[$contractID][$plotID][$rentaType] = $naturaValue;

                            // Сумиране на начислената рента в натура по договор
                            $sumChargedRentaNaturaByContract[$contractID][$rentaType] += $naturaValue;
                        }
                    }
                }
            }

            $results[$i]['plots_contracts_renta_nat'] = $rentaNatByPlot;
            $results[$i]['plots_contracts_charged_renta_nat'] = $chargedRentaNaturaByPlot;

            $merged_renta_nat_array = [];
            foreach ($contract_rents as $key => $rent) {
                if (!is_null($rent)) {
                    $merged_renta_nat_array[$key]['renta_nat'] = $rent;
                }
            }

            foreach ($charged_rents_arr as $key => $rent) {
                if (!is_null($rent)) {
                    $merged_renta_nat_array[$key]['charged_renta_nat'] = $rent;
                }
            }
            ksort($merged_renta_nat_array);
            foreach ($merged_renta_nat_array as $rentaNatTypeID => $rentaNat) {
                if (null != $this->renta_types[$rentaNatTypeID]) {
                    // Начислена Рента в натура
                    if (!is_null($rentaNat['charged_renta_nat']) && $rentaNat['charged_renta_nat'] >= 0) {
                        $results[$i]['charged_renta_nat_text'] .= number_format($rentaNat['charged_renta_nat'], 3, '.', '') . ' X ' . $this->renta_types[$rentaNatTypeID] . '<br/>';
                        $results[$i]['charged_renta_nat'][$rentaNatTypeID] = number_format($rentaNat['charged_renta_nat'], 3, '.', '');
                    } else {
                        $results[$i]['charged_renta_nat_text'] .= '-<br/>';
                        $results[$i]['charged_renta_nat'][$rentaNatTypeID] = '-';
                    }

                    // Общо
                    $total_renta_nat['charged_renta_nat'][$this->renta_types[$rentaNatTypeID]] += $rentaNat['charged_renta_nat'];
                }
            }

            foreach ($sumDueRentaNat as $rentaNatTypeID => $rentaNat) {
                // Рента в натура По договор
                $results[$i]['renta_nat_text'] .= number_format($rentaNat, 3, '.', '') . ' X ' . $this->renta_types[$rentaNatTypeID] . '<br/>';

                $results[$i]['renta_nat'][$rentaNatTypeID] = number_format($rentaNat, 3, '.', '');

                // Общо
                $total_renta_nat['renta_nat'][$this->renta_types[$rentaNatTypeID]] += $rentaNat;
            }

            $results[$i]['total_by_renta'] = '-';
            $results[$i]['total_by_renta_nat'] = '-';

            if (!$results[$i]['renta_nat_text']) {
                $results[$i]['renta_nat_text'] = '-';
            } else {
                $results[$i]['renta_nat_text'] = rtrim($results[$i]['renta_nat_text'], ',<br/>');
                if (!empty($results[$i]['paid_renta_nat_details'])) {
                    foreach ($results[$i]['paid_renta_nat_details'] as $key => $value) {
                        if ('' != $key) {
                            $total_paid_renta_nat[$key] += $value;
                        }
                    }
                }
            }

            if (!$results[$i]['charged_renta_nat_text']) {
                $results[$i]['charged_renta_nat_text'] = '-';
            } else {
                $results[$i]['charged_renta_nat_text'] = rtrim($results[$i]['charged_renta_nat_text'], ',<br/>');
            }

            $UserDbAreaTypesController = new UserDbAreaTypesController($this->User->Database);
            $results[$i]['land'] = $UsersController->getEkatteName($results[$i]['rent_place']);
            $results[$i]['c_type'] = $GLOBALS['Contracts']['ContractTypes'][$results[$i]['c_type']]['name'];
            $results[$i]['area_type'] = $UserDbAreaTypesController->getNtpTitle($results[$i]['area_type']);

            if (!$results[$i]['mestnost']) {
                $results[$i]['mestnost'] = '-';
            }

            if (!$results[$i]['category']) {
                $results[$i]['category'] = '-';
            } else {
                $results[$i]['category'] = $UserDbPlotCategoriesTypeController->getPlotCategoryTitle($results[$i]['category']);
            }

            // $results[$i]['area'] = number_format($results[$i]['area'], 3, '.', '');
            $results[$i]['paid_renta'] = number_format($results[$i]['paid_renta'], 2, '.', '');

            if (0 == $results[$i]['paid_renta']) {
                $results[$i]['paid_renta'] = null;
            }

            if (count($results[$i]['plots_contracts_renta']) > 0) {
                // TODO:: Да се изтества с конвертирането на рентите. Дали тази логика не е излишна

                // if (!empty($convertedChargedRentaByPlot) || !empty($plots_contracts_charged_renta)) {
                // foreach ($convertedChargedRentaByPlot as $contractId => $convertedValue) {
                //     foreach ($convertedValue as $plotId => $convertedChargedRentaNatura) {
                //         if ($plots_contracts_charged_renta[$contractId] && $plots_contracts_charged_renta[$contractId][$plotId]) {
                //             $plots_contracts_charged_renta[$contractId][$plotId] += $convertedChargedRentaNatura;
                //         } elseif ($plots_contracts_charged_renta[$contractId] && !$plots_contracts_charged_renta[$contractId][$plotId]) {
                //             $plots_contracts_charged_renta[$contractId][$plotId] = $convertedChargedRentaNatura;
                //         } elseif (!$plots_contracts_charged_renta[$contractId] && $plots_contracts_charged_renta[$contractId][$plotId]) {
                //             $plots_contracts_charged_renta[$contractId][$plotId] += $convertedChargedRentaNatura;
                //         } elseif (!$plots_contracts_charged_renta[$contractId] && !$plots_contracts_charged_renta[$contractId][$plotId]) {
                //             $plots_contracts_charged_renta[$contractId][$plotId] = $convertedChargedRentaNatura;
                //         }
                //     }
                // }

                // $results[$i]['plots_contracts_charged_renta'] = [];

                // if ($plots_contracts_charged_renta) {
                //     foreach ($plots_contracts_charged_renta as $contractID => $value) {
                //         foreach ($value as $plotID => $chargedValue) {
                //             $results[$i]['plots_contracts_charged_renta'][] = [
                //                 'contract_id' => $contractID,
                //                 'plot_gid' => $plotID,
                //                 'charged_renta_by_plot' => $chargedValue,
                //             ];
                //         }
                //     }
                // }
                // }

                $plotsContractsRenta = $results[$i]['plots_contracts_renta'];
                $plotsContractsChargedRenta = $results[$i]['plots_contracts_charged_renta'];
                $unpaidRenta = 0;
                $sumContractsRenta = [];
                $sumContractsChargedRenta = [];
                $sumContractsDueRenta = [];
                $results[$i]['plots_contracts_charged_renta_down_grid'] = [];
                $results[$i]['plots_contracts_renta_down_grid'] = [];
                if (!empty($plotsContractsChargedRenta)) {
                    foreach ($plotsContractsRenta as $rentaKey => $rentaValue) {
                        foreach ($plotsContractsChargedRenta as $chargedRentaKey => $chargedRentaValue) {
                            $isCharged = false;

                            if ($chargedRentaValue['pc_rel_id'] == $rentaValue['pc_rel_id'] && !is_null($chargedRentaValue['plot_contract_charged_renta'])) {
                                $totalContractArea = $results[$i]['total_area_contract'][$chargedRentaValue['contract_id']];
                                $puArea = $UserDbPaymentsController->getPersonalUseArea($personalUse, $results[$i]['owner_id'], $chargedRentaValue, $totalContractArea);

                                $totalPersonalUseArea += $puArea;
                                $chargedRentaByPlot = round(($chargedRentaValue['plot_area'] - $puArea), 3) * ($chargedRentaValue['rent_per_plot'] > 0 ? $chargedRentaValue['rent_per_plot'] : $chargedRentaValue['plot_contract_charged_renta']);
                                $results[$i]['plots_contracts_renta'][$rentaKey]['renta_by_plot'] = $chargedRentaByPlot;

                                if ($chargedRentaValue['rent_per_plot'] > 0) {
                                    continue;
                                }
                                $results[$i]['plots_contracts_charged_renta_down_grid'][$chargedRentaValue['contract_id']][$chargedRentaValue['plot_gid']] = $chargedRentaByPlot;
                                $sumContractsChargedRenta[$chargedRentaValue['contract_id']] += $chargedRentaByPlot;
                                $isCharged = true;

                                break;
                            }
                        }

                        if (!$isCharged) {
                            $totalContractArea = $results[$i]['total_area_contract'][$rentaValue['contract_id']];
                            $puArea = $UserDbPaymentsController->getPersonalUseArea($personalUse, $results[$i]['owner_id'], $rentaValue, $totalContractArea);
                            $totalPersonalUseArea += $puArea;
                            $rentaByPlot = ($rentaValue['plot_area'] - $puArea) * ($rentaValue['rent_per_plot'] > 0 ? $rentaValue['rent_per_plot'] : $rentaValue['plot_contract_renta']);

                            $sumContractsDueRenta[$rentaValue['contract_id']] += $rentaByPlot;
                            $results[$i]['plots_contracts_renta_down_grid'][$rentaValue['contract_id']][$rentaValue['plot_gid']] = $rentaByPlot;

                            // Сумиране на рентата по договор за договор
                            $sumContractsRenta[$rentaValue['contract_id']] += $rentaValue['renta_by_plot'];
                        }
                    }
                } else {
                    foreach ($plotsContractsRenta as $rentaKey => $rentaValue) {
                        $puArea = $UserDbPaymentsController->getPersonalUseArea($personalUse, $results[$i]['owner_id'], $rentaValue, $contractPlotsAreaSum[$rentaValue['contract_id']]);
                        $totalPersonalUseArea += $puArea;
                        $rentaByPlot = round(($rentaValue['plot_area'] - $puArea), 3) * ($rentaValue['rent_per_plot'] > 0 ? $rentaValue['rent_per_plot'] : $rentaValue['plot_contract_renta']);
                        $sumContractsDueRenta[$rentaValue['contract_id']] += $rentaByPlot;
                        $sumContractsRenta[$rentaValue['contract_id']] += $rentaValue['renta_by_plot'];

                        $results[$i]['plots_contracts_renta_down_grid'][$rentaValue['contract_id']][$rentaValue['plot_gid']] = $rentaByPlot;
                    }
                }

                foreach ($results[$i]['plots_contracts_renta_down_grid'] as $contractID => $rentaValue) {
                    if (!$unpaidRenta) {
                        $unpaidRenta = 0;
                    }

                    foreach ($rentaValue as $plotID => $value) {
                        if ($paidRentaByPlot[$contractID] && $paidRentaByPlot[$contractID][$plotID]) {
                            $rentaToPaid = $paidRentaByPlot[$contractID][$plotID];
                            $rentaLeft = $value - $rentaToPaid;

                            $unpaidRenta += $rentaLeft;
                        } else {
                            $unpaidRenta += $value;
                        }
                    }
                }

                foreach ($results[$i]['plots_contracts_charged_renta_down_grid'] as $contractID => $rentaValue) {
                    if (!$unpaidRenta) {
                        $unpaidRenta = 0;
                    }

                    foreach ($rentaValue as $plotID => $value) {
                        if ($paidRentaByPlot[$contractID] && $paidRentaByPlot[$contractID][$plotID]) {
                            $rentaToPaid = $paidRentaByPlot[$contractID][$plotID];
                            $rentaLeft = $value - $rentaToPaid;

                            $unpaidRenta += $rentaLeft;
                        } else {
                            $unpaidRenta += $value;
                        }
                    }
                }

                foreach ($convertedChargedRentaByPlot as $contractID => $rentaValue) {
                    if (!$unpaidRenta) {
                        $unpaidRenta = 0;
                    }

                    foreach ($rentaValue as $plotID => $value) {
                        if ($paidRentaByPlot[$contractID] && $paidRentaByPlot[$contractID][$plotID]) {
                            $rentaToPaid = $paidRentaByPlot[$contractID][$plotID];
                            $rentaLeft = $value - $rentaToPaid;

                            $unpaidRenta += $rentaLeft;
                        } else {
                            $unpaidRenta += $value;
                        }
                    }
                }

                $results[$i]['unpaid_renta'] = ($unpaidRenta < 0) ? '0.00' : number_format($unpaidRenta, 2, '.', '');

                // Надплатено рента в лева
                $results[$i]['over_paid'] = ($unpaidRenta >= 0) ? '0.00' : number_format($unpaidRenta * (-1), 2, '.', '');
            }

            $rentaTypes = [];
            $sumRentaType = [];
            if (!empty($rentaNatByPlot)) {
                // sum natura by renta type

                foreach ($rentaNatByPlot as $contractID => $rentaValue) {
                    foreach ($rentaValue as $plotID => $value) {
                        foreach ($value as $rentaType => $naturaValue) {
                            $sumRentaType[$rentaType] += $naturaValue;
                        }
                    }
                }

                // Сортиране на рентата в натура спрямо renta_type_id
                ksort($sumRentaType);

                $unpaid_renta_nat = [];
                $unpaid_renta_nat_arr = [];
                $unpaid_renta_nat_id_arr = [];
                $over_paid_renta_nat = [];
                $over_paid_renta_nat_arr = [];
                $unpaid_renta_nat_unit_value = [];
                $unpaid_renta_nat_unit_value_arr = [];

                foreach ($sumRentaType as $rentaType => $rentaNatura) {
                    $paidRentaNatura = $results[$i]['paid_renta_nat_details'][$rentaType];
                    $unpaidRentaNatura = $rentaNatura - $paidRentaNatura;
                    $rentaTypes[] = $this->renta_types[$rentaType];

                    $quantity = number_format(($unpaidRentaNatura < 0) ? 0 : $unpaidRentaNatura, 3, '.', '');
                    $quantityOverPaid = number_format(($unpaidRentaNatura >= 0) ? '0.00' : $unpaidRentaNatura * (-1), 3, '.', '');
                    $quantityValue = number_format($quantity * $this->renta_types_values[$rentaType], 2, '.', '');

                    $unpaid_renta_nat_unit_value[] = $quantityValue . ' лв.';
                    $unpaid_renta_nat_unit_value_arr[$rentaType] = $quantityValue;

                    $unpaid_renta_nat[] = $quantity . ' X ' . $this->renta_types[$rentaType];
                    $unpaid_renta_nat_arr[$rentaType] = $quantity;
                    $unpaid_renta_nat_id_arr['unpaid_renta_nat_arr_' . $rentaType] = number_format($quantity, 3, '.', '');

                    $over_paid_renta_nat[] = $quantityOverPaid . ' X ' . $this->renta_types[$rentaType];
                    $over_paid_renta_nat_arr[$rentaType] = $quantityOverPaid;

                    if (!$results[$i]['is_dead']) {
                        $total_unpaid_renta_nat[$rentaType] += $quantity;
                    } else {
                        if (0 == $counterHeritors[0]['count']) {
                            $total_unpaid_renta_nat[$rentaType] += $quantity;
                        }
                    }

                    // Натура - Общо за страница
                    $total_over_paid_renta_nat[$rentaType] += $quantityOverPaid;
                }

                $results[$i]['unpaid_renta_nat_arr'] = $unpaid_renta_nat_arr;
                $results[$i] = array_merge($results[$i], $unpaid_renta_nat_id_arr);
                $results[$i]['over_paid_renta_nat_arr'] = $over_paid_renta_nat_arr;
                $results[$i]['unpaid_renta_nat_unit_value_arr'] = $unpaid_renta_nat_unit_value_arr;
                $results[$i]['unpaid_renta_nat'] = implode('</br>', $unpaid_renta_nat);
                $results[$i]['over_paid_nat'] = implode('</br>', $over_paid_renta_nat);
                $results[$i]['unpaid_renta_nat_unit_value'] = implode('</br>', $unpaid_renta_nat_unit_value);
                $results[$i]['renta_nat_type'] = implode('</br>', $rentaTypes);
            }

            $paid_renta_by = [];
            $paid_renta_by_arr = [];
            if ($paid_renta) {
                $amount = number_format($paid_renta, 2, '.', '');
                $paid_renta_by[] = $amount . ' лв.';
                $total_paid_renta_by_amount += $amount;

                $paid_renta_by_arr['amount'] = $amount;
            }

            if (!empty($paid_renta_nat)) {
                foreach ($paid_renta_nat as $key => $value) {
                    $quantity = number_format($value, 3, '.', '');
                    $paid_renta_by[] = $quantity . ' X ' . $this->renta_types[$key];
                    $total_paid_renta_by[$key] += $value;

                    $paid_renta_by_arr['nat_amount'][$key] = $quantity;
                }
            }
            $results[$i]['paid_renta_by_arr'] = $paid_renta_by_arr;
            $results[$i]['paid_renta_by'] = implode('</br>', $paid_renta_by);
            $results[$i]['paid_renta_by'] = '' != $results[$i]['paid_renta_by'] ? $results[$i]['paid_renta_by'] : '-';

            $paid_renta_nat_by = [];
            $paid_renta_nat_by_arr = [];
            if ($paid_renta_by_nat) {
                $amount = number_format($paid_renta_by_nat, 2, '.', '');
                $paid_renta_nat_by[] = $amount . ' лв.';
                $total_paid_renta_by_nat_amount += $amount;

                $paid_renta_nat_by_arr['amount'] = $amount;
            }
            if (!empty($paid_renta_nat_by_nat)) {
                foreach ($paid_renta_nat_by_nat as $key => $value) {
                    $quantity = number_format($value, 3, '.', '');
                    $paid_renta_nat_by[] = $quantity . ' X ' . $this->renta_types[$key];
                    $total_paid_renta_by_nat[$key] += $value;

                    $paid_renta_nat_by_arr['nat_amount'][$key] = $quantity;
                }
            }
            $results[$i]['paid_renta_nat_by_arr'] = $paid_renta_nat_by_arr;
            $results[$i]['paid_renta_nat_by'] = implode('</br>', $paid_renta_nat_by);
            $results[$i]['paid_renta_nat_by'] = '' != $results[$i]['paid_renta_nat_by'] ? $results[$i]['paid_renta_nat_by'] : '-';

            $all_paid_renta_nat_by_detailed = [];
            $all_paid_renta_nat_by_detailed_quantity_arr = [];
            $all_paid_renta_nat_by_detailed_unit_value_arr = [];
            if (!empty($paid_renta_nat_by_detailed)) {
                foreach ($paid_renta_nat_by_detailed as $key => $value) {
                    $quantity = number_format($value, 3, '.', '');
                    $unitValue = null != $paid_renta_nat_by_detailed_unit_value[$key] ? $paid_renta_nat_by_detailed_unit_value[$key] : $this->renta_types_values[$key];

                    if (null == $unitValue) {
                        $unitValue = '-';
                    }

                    $all_paid_renta_nat_by_detailed[] = $quantity . ' X ' . $this->renta_types[$key] . ' X ' . $unitValue . ' ед.ст.';
                    $total_all_paid_renta_nat_by_detailed[$key] += $value;

                    $all_paid_renta_nat_by_detailed_quantity_arr[$key] += $quantity;
                    if ('-' != $unitValue) {
                        $all_paid_renta_nat_by_detailed_unit_value_arr[$key] += $unitValue;
                    }
                }
            }
            $results[$i]['all_paid_renta_nat_by_detailed_quantity_arr'] = $all_paid_renta_nat_by_detailed_quantity_arr;
            $results[$i]['all_paid_renta_nat_by_detailed_unit_value_arr'] = $all_paid_renta_nat_by_detailed_unit_value_arr;
            $results[$i]['paid_renta_nat_by_detailed'] = implode('</br>', $all_paid_renta_nat_by_detailed);
            $results[$i]['paid_renta_nat_by_detailed'] = '' != $results[$i]['paid_renta_nat_by_detailed'] ? $results[$i]['paid_renta_nat_by_detailed'] : '-';

            $sumPaidRenta = 0;
            if ($paid_renta || $paid_renta_by_nat) {
                $sumPaidRenta = $paid_renta + $paid_renta_by_nat;
                $results[$i]['total_by_renta'] = number_format($sumPaidRenta, 2, '.', '') . ' лв.';
                $results[$i]['total_by_renta_sum'] = $sumPaidRenta;
            }
            // Сумиране на Общо платена рента в лева
            $total_sum_by_paid_renta += $sumPaidRenta;

            $totalByRentaNatura = [];
            if (!empty($paid_renta_nat) || !empty($paid_renta_nat_by_nat)) {
                $totalByRentaNatura = $this->arraySumIdenticalKeys($paid_renta_nat, $paid_renta_nat_by_nat);

                $results[$i]['total_paid_rent_natura_arr'] = $this->calcSumIdenticalKeys($paid_renta_nat, $paid_renta_nat_by_nat);
                foreach (array_keys($total_sum_paid_renta_nat + $totalByRentaNatura) as $key) {
                    $total_sum_paid_renta_nat[$key] = @($total_sum_paid_renta_nat[$key] + $totalByRentaNatura[$key]);
                }
            }

            $results[$i]['total_by_renta_nat_arr'] = $totalByRentaNatura;
            $results[$i]['total_by_renta_nat'] = implode('</br>', $totalByRentaNatura);
            $results[$i]['total_by_renta_nat'] = '' != $results[$i]['total_by_renta_nat'] ? $results[$i]['total_by_renta_nat'] : '-';

            if (!empty($rentaNatByPlot)) {
                foreach ($sumRentaType as $rentaType => $rentaNatura) {
                    if (!is_null($results[$i]['paid_renta_nat_details'][$rentaType])) {
                        $results[$i]['paid_renta_nat'] .= $results[$i]['paid_renta_nat_details'][$rentaType] . ' X ' . $this->renta_types[$rentaType] . '</br>';
                    } else {
                        $results[$i]['paid_renta_nat'] .= '0.000 X ' . $this->renta_types[$rentaType] . '</br>';
                    }
                }
            } else {
                $results[$i]['paid_renta_nat'] = '0.000';
                $results[$i]['unpaid_renta_nat'] = '0.000';
            }

            if ((empty($rentaNatByPlot) || empty($sumRentaType)) && !empty($results[$i]['paid_renta_nat_details'])) {
                $results[$i]['paid_renta_nat'] = '';

                foreach ($results[$i]['paid_renta_nat_details'] as $rentaType => $rentaNatura) {
                    $results[$i]['paid_renta_nat'] .= $rentaNatura . ' X ' . $this->renta_types[$rentaType] . '</br>';
                }
            }

            if (is_null($results[$i]['paid_renta_nat']) || '' == $results[$i]['paid_renta_nat']) {
                $results[$i]['paid_renta_nat'] = '-';
            }

            if (empty($rentaTypes)) {
                $results[$i]['renta_nat_type'] = '[Без рента в натура]';
                $results[$i]['unpaid_renta_nat'] = '-';
                $results[$i]['over_paid_nat'] = '-';
                $results[$i]['unpaid_renta_nat_unit_value'] = '-';
            }

            foreach ($sumContractsDueRenta as $contractID => $value) {
                $results[$i]['renta'] += $value;
            }

            $results[$i]['contract_renta'] = $results[$i]['renta'];
            $results[$i]['renta'] = number_format($results[$i]['renta'], 2, '.', '');

            $results[$i]['owner_contracts_renta'] = [
                'area' => $contractPlotsAreaSum,
                'renta' => $sumContractsRenta,
                'charged_renta' => $sumContractsChargedRenta,
                'renta_nat' => $sumRentaNatByContract,
                'charged_renta_nat' => $sumChargedRentaNaturaByContract,
                'unpaid_renta' => $sumContractsDueRenta,
            ];

            if (!$results[$i]['sv_num']) {
                $results[$i]['sv_num'] = '-';
            }

            if (!$results[$i]['sv_date']) {
                $results[$i]['sv_date'] = '-';
            }

            if (!$results[$i]['owner_names']) {
                $results[$i]['owner_names'] = '-';
            }

            $sumChargedRentaMoney = array_sum($results[$i]['owner_contracts_renta']['charged_renta']);

            if (!empty($sumChargedRentaMoney) || !empty($results[$i]['converted_renta_nat'])) {
                $results[$i]['charged_renta'] = number_format($sumChargedRentaMoney + $results[$i]['converted_renta_nat'], 2);
            } else {
                $results[$i]['charged_renta'] = '-';
            }

            $results[$i]['area'] = number_format(($results[$i]['area'] - $totalPersonalUseArea), 3, '.', '');
            $results[$i]['pu_area'] = number_format($totalPersonalUseArea, 3, '.', '');
            $results[$i]['cultivated_area'] = number_format($results[$i]['cultivated_area'], 3, '.', '');

            // increment global iterator
            $this->iterator++;

            // owner can occur more than once because of the heritors
            // grid elements can not have the same ID
            // assign global iterator value to grid row ID
            $results[$i]['id'] = $this->iterator;

            // if owner is dead then heritor info is required
            if ($results[$i]['is_dead']) {
                // get heritors data and put it inside row element children
                $results[$i]['children'] = $this->getOwnersHeritorsPayroll($pathOwner, $results[$i]['owner_id'], false, $results[$i]);
                $results[$i]['iconCls'] = 'icon-tree-user-rip';
                // ЛП: От Изплолзваната площ на родителя изваждаме площта за лично ползване на наследниците
                $results[$i]['renta'] = 0;
                $results[$i]['charged_renta'] = 0;
                $results[$i]['unpaid_renta'] = 0;
                $results[$i]['renta_nat'] = [];
                $results[$i]['renta_nat_text'] = '';
                $results[$i]['charged_renta_nat'] = [];
                $results[$i]['charged_renta_nat_text'] = '';

                foreach ($results[$i]['children'] as $child) {
                    $results[$i]['area'] -= number_format((($child['pu_area'] + $child['children_pu_area'])), 3, '.', '');

                    // Изчисляване рентата на родителя, като сумираме рентите на наследниците.
                    // Това се налага, защото наследниците може да имат лично ползване и общата рента да е по-малка от тази на родителя
                    $results[$i]['renta'] += $child['renta'];
                    $results[$i]['charged_renta'] += $child['charged_renta'];
                    $results[$i]['unpaid_renta'] += $child['unpaid_renta'];

                    if (!empty($child['renta_nat'])) {
                        foreach ($child['renta_nat'] as $rentaType => $rentaNat) {
                            if (isset($results[$i]['renta_nat'][$rentaType])) {
                                $results[$i]['renta_nat'][$rentaType] += $rentaNat;
                            } else {
                                $results[$i]['renta_nat'][$rentaType] = $rentaNat;
                            }
                        }
                    }

                    if (!empty($child['charged_renta_nat'])) {
                        foreach ($child['charged_renta_nat'] as $rentaType => $rentaNat) {
                            if (isset($results[$i]['charged_renta_nat'][$rentaType])) {
                                $results[$i]['charged_renta_nat'][$rentaType] += $rentaNat;
                            } else {
                                $results[$i]['charged_renta_nat'][$rentaType] = $rentaNat;
                            }
                        }
                    }
                }
                $results[$i]['renta'] = number_format($results[$i]['renta'], 2, '.', '');
                $results[$i]['charged_renta'] = number_format($results[$i]['charged_renta'], 2, '.', '');
                $results[$i]['unpaid_renta'] = number_format($results[$i]['unpaid_renta'], 2, '.', '');

                if (!empty($results[$i]['renta_nat'])) {
                    foreach ($results[$i]['renta_nat'] as $rentaType => $rentaNat) {
                        $results[$i]['renta_nat_text'] .= number_format($rentaNat, 3) . ' X ' . $child['renta_nat_types_arr'][$rentaType] . '</br>';
                    }
                }

                if (!empty($results[$i]['charged_renta_nat'])) {
                    foreach ($results[$i]['charged_renta_nat'] as $rentaType => $rentaNat) {
                        $results[$i]['charged_renta_nat_text'] .= number_format($rentaNat, 3) . ' X ' . $child['renta_nat_types_arr'][$rentaType] . '</br>';
                    }
                }
            } elseif (1 == $results[$i]['owner_type']) {
                $results[$i]['iconCls'] = 'icon-tree-user';
            } else {
                $results[$i]['iconCls'] = 'icon-tree-users';
            }

            $results[$i]['is_heritor'] = false;
        }

        // //Recalculate payment grid data with personal use
        // if ($this->hasPersonalUse($results)) {
        //     $UserDbPaymentsController->calculatePersonalUse($results, [], 'payroll');
        // }

        $return['rows'] = $results;
        $return['total'] = $counter[0]['count'];

        return $return;
    }

    /** Recursive function to get all the heritors of an owner.
     * @param string $path
     * @param int $root_id
     * @param bool $parent_plots_percent
     * @param array $ownerResults
     * @param bool $recursive
     *
     * @return array
     */
    public function getOwnersHeritorsPayroll($path, $root_id, $parent_plots_percent = false, &$ownerResults, $recursive = true)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);
        $UsersController = new UsersController('Users');

        $heritor_data = [];
        $heritor_percent_by_plots = [];
        $total_heritor_paid_renta = 0;

        $paid_renta_owner = $ownerResults['paid_renta_contract'];
        $paid_renta_nat_owner = $ownerResults['paid_renta_nat_details_contract'];
        $plots_contracts_renta = $ownerResults['plots_contracts_renta'];
        $plots_contracts_renta_nat = $ownerResults['plots_contracts_renta_nat'];
        $plots_contracts_due_renta_nat = $ownerResults['plots_contracts_due_renta_nat'];
        $plots_contracts_charged_renta_nat = $ownerResults['plots_contracts_charged_renta_nat'];

        $owner_plots_percent = $ownerResults['owner_plots_percent'];

        // get all heritors
        $options = [
            'return' => [
                'h.id', "name || ' ' || surname || ' ' || lastname as owner_names",
                'h.owner_id', 'phone', 'mobile',
                $UserDbOwnersController::isDead('o', [$_POST['payroll_from_date'], $_POST['payroll_to_date']]),
                'h.path', 'o.egn as egn_eik', 'o.iban',
                'o.rent_place',
                "string_agg(c.c_num || ' - ' || r.rep_name || ' ' || r.rep_surname || ' ' || r.rep_lastname , ',') as rep_names",
            ],
            'where' => [
                'path' => ['column' => 'path', 'prefix' => 'h', 'compare' => '~', 'value' => $path],
            ],
            'joins' => [
                'left join ' . $UserDbOwnersController->DbHandler->plotsOwnersRelTable . '  spor on spor.path = h.path',
                'left join ' . $UserDbOwnersController->DbHandler->tableOwnersReps . ' r on r.id = spor.rep_id',
                'left join ' . $UserDbOwnersController->DbHandler->contractsPlotsRelTable . ' pc on pc.id = spor.pc_rel_id',
                'left join ' . $UserDbOwnersController->DbHandler->tableContracts . ' c on c.id = pc.contract_id',
            ],
            'group' => 'h.id, o.id',
        ];

        $counter = $UserDbOwnersController->getOwnersHeritors($options, true, false);

        if (0 == $counter[0]['count']) {
            return [];
        }

        $heritor_results = $UserDbOwnersController->getOwnersHeritors($options, false, false);
        // we have all heritors here

        // РЕНТА В ЛЕВА
        $plotsContractsRentaLegator = [];
        if (is_array($plots_contracts_renta)) {
            for ($m = 0; $m < $plots_contracts_renta_count = count($plots_contracts_renta); $m++) {
                $plot_contract_renta = $plots_contracts_renta[$m];
                $contractID = $plot_contract_renta['contract_id'];
                $plotID = $plot_contract_renta['plot_gid'];

                $plotsContractsRentaLegator[$contractID][$plotID] = $plot_contract_renta['renta_by_plot'];
            }
        }

        // Обща рента по договор
        $sumPlotsContractsRentaLegator = [];
        foreach ($plotsContractsRentaLegator as $contract_ID => $value) {
            foreach ($value as $plot_ID => $renta) {
                $sumPlotsContractsRentaLegator[$contract_ID] += $renta;
            }
        }

        // Изчисляване на намалената стойност от всеки имот, ако собственика е изплащал рента в лева
        $percentPlotsContractsRentaLegator = [];
        foreach ($plotsContractsRentaLegator as $contract_ID => $value) {
            foreach ($value as $plot_ID => $renta) {
                $rentaByPlot = 0;

                if ($sumPlotsContractsRentaLegator[$contract_ID] > 0) {
                    $rentaByPlot = $renta / $sumPlotsContractsRentaLegator[$contract_ID];
                }

                $percentPlotsContractsRentaLegator[$contract_ID][$plot_ID] = $rentaByPlot * $paid_renta_owner[$contract_ID];
            }
        }

        // Изчисляване на останалата стойност от всеки имот, ако собственика е изплащал рента в лева
        $leftPlotsContractsRentaLegator = [];
        foreach ($plotsContractsRentaLegator as $contract_ID => $value) {
            foreach ($value as $plot_ID => $renta) {
                $percentRenta = $percentPlotsContractsRentaLegator[$contract_ID][$plot_ID];

                $leftPlotsContractsRentaLegator[$contract_ID][$plot_ID] = $renta - $percentRenta;
            }
        }

        // РЕНТА В НАТУРА
        if (!empty($plots_contracts_renta_nat)) {
            $sumPlotsContractsRentaNatLegator = [];
            foreach ($plots_contracts_renta_nat as $contract_ID => $value) {
                foreach ($value as $plot_ID => $rentaValue) {
                    foreach ($rentaValue as $rentaType => $rentaNat) {
                        $sumPlotsContractsRentaNatLegator[$contract_ID][$rentaType] += $rentaNat;
                    }
                }
            }

            // Изчисляване на намалената стойност от всеки имот, ако собственика е изплащал рента в натура
            $percentPlotsContractsRentaNatLegator = [];
            foreach ($plots_contracts_renta_nat as $contract_ID => $value) {
                foreach ($value as $plot_ID => $rentaValue) {
                    foreach ($rentaValue as $rentaType => $rentaNat) {
                        $rentaNatByPlot = 0;

                        if ($sumPlotsContractsRentaNatLegator[$contract_ID][$rentaType] > 0) {
                            $rentaNatByPlot = $rentaNat / $sumPlotsContractsRentaNatLegator[$contract_ID][$rentaType];
                        }

                        $percentPlotsContractsRentaNatLegator[$contract_ID][$plot_ID][$rentaType] = $rentaNatByPlot * $paid_renta_nat_owner[$contract_ID][$rentaType];
                    }
                }
            }

            // Изчисляване на останалата стойност от всеки имот, ако собственика е изплащал рента в натура
            $leftPlotsContractsRentaNatLegator = [];
            foreach ($plots_contracts_renta_nat as $contract_ID => $value) {
                foreach ($value as $plot_ID => $rentaValue) {
                    foreach ($rentaValue as $rentaType => $rentaNat) {
                        $percentRentaNat = $percentPlotsContractsRentaNatLegator[$contract_ID][$plot_ID][$rentaType];

                        $leftPlotsContractsRentaNatLegator[$contract_ID][$plot_ID][$rentaType] = $rentaNat - $percentRentaNat;
                    }
                }
            }
        }
        list($farming_years, $farming_years_str, $farming_years_string) = $this->getFarmings($UsersController);

        $options = [
            'return' => [
                'o.id as root_id', 'o.phone', 'o.mobile', 'po.percent', 'gid', 'c.id as contract_id', 'pc.id as pc_rel_id', 'c.c_num',
                'pc.area_for_rent as contract_area', 'kvs.kad_ident as plots_name',
                '(case when a.id is not null then a.id else c.id end) as contract_anex_arr',
                'SUM((CASE WHEN coalesce(kvs_allowable_area, allowable_area) < contract_area THEN coalesce(kvs_allowable_area, allowable_area) ELSE contract_area END)) as cultivated_area',
                '(CASE WHEN a.renta IS NULL THEN 
                    (
                        CASE
                        WHEN pc.rent_per_plot IS NOT NULL THEN
                            0
                        ELSE
                            C .renta
                        END
                    )
                ELSE 
                (
                    CASE
                    WHEN pc.rent_per_plot IS NOT NULL THEN
                        0
                    ELSE
                        A .renta
                    END
                )
                END) as contract_renta',

                '(CASE
                    WHEN pc.rent_per_plot IS NOT NULL THEN
                        pc.rent_per_plot
                    ELSE
                        NULL
                    END
                ) AS contracts_rent_per_plot',

                'cr.renta as charged_renta',
                'MAX(crn.converted_charged_renta_nat) AS converted_charged_renta_nat',
                '(CASE WHEN a.renta_nat IS NULL THEN c.renta_nat ELSE a.renta_nat END) as renta_nat',
                'CASE WHEN o.rent_place IS NULL THEN r.rent_place ELSE o.rent_place END AS rent_place',
                '(CASE WHEN a.renta_nat_type_id IS NULL THEN c.renta_nat_type_id ELSE a.renta_nat_type_id END) as renta_nat_type_id',
                'array_agg(kvs.kad_ident) as plots_name_array',
            ],
            'where' => [
                'owner_id' => ['column' => 'id', 'compare' => '=', 'prefix' => 'o', 'value' => $root_id],
                'contract_type' => ['column' => 'nm_usage_rights', 'compare' => 'NOT IN', 'prefix' => 'c', 'value' => [1, 4]],
                'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
                'is_annnex' => ['column' => 'is_annex', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
                'annex_action' => ['column' => 'annex_action', 'compare' => '=', 'prefix' => 'pc', 'value' => 'added'],
                'percent' => ['column' => 'percent', 'compare' => '>', 'prefix' => 'po', 'value' => '0'],
                'is_heritor' => ['column' => 'is_heritor', 'compare' => '=', 'prefix' => 'po', 'value' => 'FALSE'],
                'active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'TRUE'],
                'ekate' => ['column' => 'ekate', 'compare' => $this->getPayrollEkateCompareType($_POST), 'prefix' => 'kvs', 'value' => $this->filterPayrollEkateValue($_POST['payroll_ekate'])],
                'farming' => ['column' => 'farming_id', 'compare' => 'IN', 'prefix' => 'c', 'value' => [$ownerResults['farming']]],
            ],
            'start_date' => $_POST['payroll_to_date'],
            'due_date' => $_POST['payroll_from_date'],
            'year_id' => $farming_years,
            'group' => 'gid, o.id, po.percent, c.id, pc.id, cr.renta, cr.nat_is_converted, cr.renta_nat, r.rent_place, a.id, a.renta, a.renta_nat, a.renta_nat_type_id, pu.area',
        ];

        $contract_plot_results = $UserDbPaymentsController->getPayrollData($options, false, false);

        $count_contract_plot_results = count($contract_plot_results);

        $contractsAreas = [];
        foreach ($contract_plot_results as $contract_plot) {
            if (!isset($contractsAreas[$contract_plot['contract_id']])) {
                $contractsAreas[$contract_plot['contract_id']] = 0;
            }

            $contractsAreas[$contract_plot['contract_id']] += $contract_plot['contract_area'];
        }

        // iterate all contracts_plots results
        $contract_arr = [];
        for ($i = 0; $i < $count_contract_plot_results; $i++) {
            $plotID = $contract_plot_results[$i]['gid'];

            // get heritors for the current plot
            $options = [
                'return' => [
                    'h.id', 'owner_id', 'is_dead', 'path',
                    'phone', 'mobile',
                    '(
                        select (case when max(pu.id) > 0 then true else false end) as has_personal_use
                        from su_personal_use pu
                        where pu.owner_id = h.owner_id
                    ) as has_personal_use',
                    "(SELECT poi.percent FROM su_plots_owners_rel poi WHERE poi.path = h.path AND poi.is_heritor = true AND poi.pc_rel_id = {$contract_plot_results[$i]['pc_rel_id']}) as percent",
                ],
                'where' => [
                    'path' => ['column' => 'path', 'compare' => '~', 'value' => $path],
                ],
            ];

            $results = $UserDbOwnersController->getOwnersHeritors($options, false, false);

            $personalUseOptions = [
                'owner_ids' => array_unique(array_column($results, 'owner_id')),
                'year' => $_POST['farming_year'],
                'chosen_years' => $_POST['farming_year'],
            ];
            $personalUse = $UserDbPaymentsController->getPersonalUseForOwners($personalUseOptions, false, false);

            $sum_custom_ownage = 0;
            $heritors = 0;
            $all_heritors_count = count($results);

            $contract_arr[] = $contract_plot_results[$i]['contract_id'];

            for ($j = 0; $j < $all_heritors_count; $j++) {
                if (null != $results[$j]['percent']) {
                    $sum_custom_ownage += $results[$j]['percent'];
                } else {
                    $heritors++;
                }
            }

            $personPlotPercent = [];
            $personTotalArea = 0;
            for ($j = 0; $j < $all_heritors_count; $j++) {
                $ownerID = $results[$j]['owner_id'];
                $parent_path_e = explode('.', $results[$j]['path'], -1);
                $parent_path = implode('.', $parent_path_e);

                if (!$results[$j]['percent'] || 0 == $results[$j]['percent']) {
                    if ($parent_plots_percent[$plotID][$parent_path] || '0' == $parent_plots_percent[$plotID][$parent_path]) {
                        if ('0' == $results[$j]['percent']) {
                            $results[$j]['percent'] = '0';
                        } else {
                            $results[$j]['percent'] = ($parent_plots_percent[$plotID][$parent_path] - $sum_custom_ownage) / $heritors;
                        }
                        $heritor_percent_by_plots[$plotID][$results[$j]['path']] = $results[$j]['percent'];
                    } else {
                        if (null == $results[$j]['percent']) {
                            $results[$j]['percent'] = ($contract_plot_results[$i]['percent'] - $sum_custom_ownage) / $heritors;
                            $heritor_percent_by_plots[$plotID][$results[$j]['path']] = $results[$j]['percent'];
                        } elseif ('0' == $results[$j]['percent']) {
                            $heritor_percent_by_plots[$plotID][$results[$j]['path']] = '0';
                        }
                    }
                } else {
                    $heritor_percent_by_plots[$plotID][$results[$j]['path']] = $results[$j]['percent'];
                }

                $results[$j]['percent_heritor'] = 0;

                if ($owner_plots_percent[$contract_plot_results[$i]['contract_id']][$plotID] > 0) {
                    $results[$j]['percent_heritor'] = $results[$j]['percent'] / $owner_plots_percent[$contract_plot_results[$i]['contract_id']][$plotID];
                }
                $heritor_data[$ownerID]['owner_plots_percent'][$contract_plot_results[$i]['contract_id']][$plotID] = $results[$j]['percent'];

                $contractArea = $contractsAreas[$contract_plot_results[$i]['contract_id']] * ($results[$j]['percent'] / 100);
                $puArea = $UserDbPaymentsController->getPersonalUseArea($personalUse, $ownerID, $contract_plot_results[$i], $contractArea);
                $contractAreaWitoutPuArea = round(($contract_plot_results[$i]['contract_area'] * ($results[$j]['percent'] / 100)), 3) - $puArea;

                // calculate all needed information about heritor
                $heritor_data[$ownerID]['root_id'] = $contract_plot_results[$i]['root_id'];
                $heritor_data[$ownerID]['has_personal_use'] = $results[$i]['has_personal_use'];
                $heritor_data[$ownerID]['c_num_array'][] = $contract_plot_results[$i]['c_num'];

                $heritor_data[$ownerID]['area_array'][$contract_plot_results[$i]['plots_name']] = $contractAreaWitoutPuArea;
                $heritor_data[$ownerID]['area'] += $contractAreaWitoutPuArea;
                $heritor_data[$ownerID]['pu_area'] += $puArea;
                $heritor_data[$ownerID]['cultivated_area'] += $contract_plot_results[$i]['cultivated_area'] * ($results[$j]['percent'] / 100);

                $heritor_data[$ownerID]['contract_id'] = $contract_plot_results[$i]['contract_id'];
                $heritor_data[$ownerID]['plots_name_array'] = explode(',', trim($results[$i]['plots_name_array'], '{}'));

                $heritor_data[$ownerID]['plots_contracts_renta_heritors'][$contract_plot_results[$i]['contract_id']][$plotID] = $leftPlotsContractsRentaLegator[$contract_plot_results[$i]['contract_id']][$plotID] * $results[$j]['percent_heritor'];

                if (!empty($leftPlotsContractsRentaNatLegator[$contract_plot_results[$i]['contract_id']])) {
                    foreach ($leftPlotsContractsRentaNatLegator[$contract_plot_results[$i]['contract_id']][$plotID] as $rentaType => $rentaNat) {
                        $heritor_data[$ownerID]['plots_contracts_renta_nat_heritors'][$contract_plot_results[$i]['contract_id']][$plotID][$rentaType] = $rentaNat * $results[$j]['percent_heritor'];
                    }
                }

                $heritor_data[$ownerID]['total_area_contract'][$contract_plot_results[$i]['contract_id']] += $contractAreaWitoutPuArea;

                $personPlotPercent[$ownerID] += $heritor_data[$ownerID]['area'];
                $personTotalArea += $heritor_data[$ownerID]['area'];

                $heritor_data[$ownerID]['area'] = number_format($heritor_data[$ownerID]['area'], 3, '.', '');

                $heritor_data[$ownerID]['plots_contracts_area_array'][] = [
                    'c_num' => $contract_plot_results[$i]['c_num'],
                    'contract_id' => $contract_plot_results[$i]['contract_id'],
                    'pc_rel_id' => $contract_plot_results[$i]['pc_rel_id'],
                    'plot_gid' => $contract_plot_results[$i]['gid'],
                    'plot_name' => $contract_plot_results[$i]['plots_name'],
                    'area' => $contractAreaWitoutPuArea,
                ];

                // Изчислява се рентата по договор за всеки имот
                $heritor_data[$ownerID]['contracts_renta_plots'][$contract_plot_results[$i]['contract_id']][$plotID] = $contract_plot_results[$i]['contract_renta'] * round($contractAreaWitoutPuArea, 2);

                if (!is_null($contract_plot_results[$i]['charged_renta']) && $contract_plot_results[$i]['charged_renta'] >= 0) {
                    if (null != $contract_plot_results[$i]['contracts_rent_per_plot']) {
                        $contract_plot_results[$i]['charged_renta'] = $contract_plot_results[$i]['contracts_rent_per_plot'];
                    }

                    // Изчислява се начислената рента за всеки имот
                    $heritor_data[$ownerID]['charged_renta_plots'][$contract_plot_results[$i]['contract_id']][$plotID] = $contract_plot_results[$i]['charged_renta'] * $contractAreaWitoutPuArea;
                }

                // Взимат се всички ренти в натура (включително и начислените) за всеки имот
                $renta_nats = $UserDbController->DbHandler->getDataByQuery(
                    "SELECT 
                        crt.renta_value * round((pc.area_for_rent) ::numeric, 3) AS renta_nat, 
                        crt.renta_value,
                    	pc.area_for_rent as contract_area, 
                        pc.id as pc_rel_id,
                        po.percent, 
                        c.id as contract_id,
                        po.owner_id, 
                        crt.renta_id,
                        charged_renta_nat.charged_renta_nat_value,
                        charged_renta_nat.converted_renta_nat
                    FROM su_contracts c
                    INNER JOIN su_contracts_plots_rel pc ON(pc.contract_id = c.id)
                    INNER JOIN layer_kvs kvs ON(kvs.gid = pc.plot_id)
                    LEFT JOIN su_plots_owners_rel po ON(po.pc_rel_id = pc.id)
                    LEFT JOIN su_personal_use pu ON (pu.owner_id = {$root_id} AND pu.year IN {$farming_years_string} AND pu.pc_rel_id = pc.id)
                    LEFT JOIN su_contracts_rents crt ON (c.id = crt.contract_id)
                    left join lateral (
                        select	
                            sum(CASE WHEN crn.nat_is_converted = TRUE THEN NULL ELSE crn.amount END) as charged_renta_nat_value,
                            sum(CASE WHEN crn.nat_is_converted = TRUE THEN crn.amount * crn.nat_unit_price ELSE NULL END) as converted_renta_nat,
                            crn.nat_type
                        from su_charged_renta cr
                        left join su_charged_renta_natura crn on crn.renta_id = cr.id
                        where 
                            cr.contract_id = (case when c.parent_id is not null then c.parent_id else c.id end)
                            and cr.plot_id = pc.plot_id 
                            and cr.owner_id = po.owner_id 
                            and cr.year in {$farming_years_string}
                        group by crn.nat_type
                    ) charged_renta_nat on charged_renta_nat.nat_type = crt.renta_id
                    where 
                        c.id = {$contract_plot_results[$i]['contract_anex_arr']}
                        and pc.annex_action ='added'
                        and po.owner_id ={$root_id}
                        and pc.plot_id = {$plotID}
                        and pc.rent_per_plot isnull
                    group by crt.id, pc.id, po.id, c.id, charged_renta_nat.charged_renta_nat_value, charged_renta_nat.converted_renta_nat
                    ORDER BY crt.renta_id"
                );

                foreach ($renta_nats as $rentaKey => $rentaVal) {
                    $puArea = $UserDbPaymentsController->getPersonalUseArea($personalUse, $ownerID, $contract_plot_results[$i], $contractArea);
                    $contractAreaWitoutPuArea = round(($rentaVal['contract_area'] * ($results[$j]['percent'] / 100)) - $puArea, 3);

                    $rentaNat = $rentaVal['renta_value'] * $contractAreaWitoutPuArea;
                    $heritor_data[$ownerID]['contracts_renta_nat_plots'][$contract_plot_results[$i]['contract_id']][$plotID][$rentaVal['renta_id']] += $rentaNat;

                    // Ако е начислена рента в натура, се добавя към масива с начисленията в натура
                    if (!empty($rentaVal['charged_renta_nat_value'])) {
                        $chargedRentaNat = $rentaVal['charged_renta_nat_value'] * $contractAreaWitoutPuArea;
                        $heritor_data[$ownerID]['charged_renta_nat_plots'][$contract_plot_results[$i]['contract_id']][$plotID][$rentaVal['renta_id']] += $chargedRentaNat;
                    }
                }

                if (!is_null($contract_plot_results[$i]['converted_charged_renta_nat']) && $contract_plot_results[$i]['converted_charged_renta_nat'] >= 0) {
                    if (null != $contract_plot_results[$i]['contracts_rent_per_plot']) {
                        $contract_plot_results[$i]['converted_charged_renta_nat'] = $contract_plot_results[$i]['contracts_rent_per_plot'];
                    }

                    $heritor_data[$ownerID]['charged_renta_plots'][$contract_plot_results[$i]['contract_id']][$plotID] += $contract_plot_results[$i]['converted_charged_renta_nat'] * $contractAreaWitoutPuArea;
                }
            }
        }

        $countHeritorRes = count($heritor_results);
        for ($i = 0; $i < $countHeritorRes; $i++) {
            $ownerID = $heritor_results[$i]['owner_id'];

            $paidOptions = [
                'custom_counter' => 'COUNT(DISTINCT(p.owner_id))',
                'order' => 'asc',
                'sort' => 'p.contract_id',
                'return' => [
                    'p.id as payment_id',
                    'p.owner_id as owner_id',
                    'p.contract_id as contract_id',
                    'case when pn.amount notnull and pn.unit_value notnull  then round((pn.amount * pn.unit_value)::numeric, 2) else round(p.amount::numeric, 2) end as trans_amount',
                    'case when pn.amount notnull then round(pn.amount::numeric,3) else round(p.amount_nat::numeric, 3) end as amount_nat',
                    'round(pn.amount::numeric, 3) as trans_amount_nat',
                    'pn.nat_type as nat_type',
                    'p.paid_in',
                    'p.paid_from',
                    'rent.name as trans_nat_type_text',
                    'round(pn.unit_value::numeric, 2)',
                ],
                'where' => [
                    'path' => ['column' => 'path', 'compare' => '~', 'prefix' => 'p', 'value' => $heritor_results[$i]['path']],
                    'contract_id' => ['column' => 'contract_id', 'compare' => 'IN', 'prefix' => 'p', 'value' => $contract_arr],
                    'year' => ['column' => 'farming_year', 'compare' => 'IN', 'prefix' => 'p', 'value' => $farming_years],
                ],
                'year_id' => $farming_years_string,
            ];

            $paidResults = $UserDbPaymentsController->getPaidData($paidOptions);

            $paid_renta = 0;
            $paid_renta_by_nat = 0;
            $paid_renta_nat = [];
            $paid_renta_nat_by_nat = [];
            $paid_renta_nat_by_detailed = [];
            $paid_renta_nat_by_detailed_unit_value = [];
            $paymentIds = [];
            $paidRentaByContract = [];
            $paidRentaNatByContract = [];
            $paidResultsCount = count($paidResults);
            for ($m = 0; $m < $paidResultsCount; $m++) {
                $paidResult = $paidResults[$m];
                $renta_type = $paidResult['nat_type'];
                $contract_id = $paidResult['contract_id'];

                if (1 == $paidResult['paid_from']) {
                    if (2 == $paidResult['paid_in']) {
                        $paid_renta_nat[$renta_type] += $paidResult['amount_nat'];

                        continue;
                    }

                    $paid_renta += $paidResult['trans_amount'];
                } elseif (2 == $paidResult['paid_from']) {
                    if (2 == $paidResult['paid_in']) {
                        $paid_renta_nat_by_nat[$renta_type] += $paidResult['trans_amount_nat'];

                        continue;
                    }
                    if (array_key_exists($renta_type, $paid_renta_nat_by_detailed)) {
                        $paid_renta_nat_by_detailed[$renta_type] += $paidResult['trans_amount_nat'];
                    } else {
                        $paid_renta_nat_by_detailed[$renta_type] = $paidResult['trans_amount_nat'];
                        $paid_renta_nat_by_detailed_unit_value[$renta_type] = $paidResult['unit_value'];
                    }

                    $this->total_heritors_all_paid_renta_nat_by_detailed[$renta_type] += $paidResult['trans_amount_nat'];

                    $paymentIds[] = $paidResult['payment_id'];
                    $paid_renta_by_nat += $paidResult['trans_amount'];
                }
            }

            // Сортиране на платерената рента чрез натура
            ksort($paid_renta_nat_by_nat);
            ksort($paid_renta_nat);

            // count all heritors
            $pathOwner = $heritor_results[$i]['path'] . '.*{1}';

            $options = [
                'return' => [
                    'h.id',
                ],
                'where' => [
                    'path' => ['column' => 'path', 'compare' => '~', 'value' => $pathOwner],
                ],
            ];

            $counterHeritors = $UserDbOwnersController->getOwnersHeritors($options, true);

            // Наследник - Платена рента в лева чрез - format to grid
            $paid_renta_by = [];
            $paid_renta_by_arr = [];
            if ($paid_renta) {
                $amount = number_format($paid_renta, 2, '.', '');
                $paid_renta_by[] = $amount . ' лв.';
                $this->total_heritors_paid_renta_by_amount += $paid_renta;

                $paid_renta_by_arr['amount'] = $amount;
            }
            if (!empty($paid_renta_nat)) {
                foreach ($paid_renta_nat as $key => $value) {
                    $quantity = number_format($value, 3, '.', '');
                    $paid_renta_by[] = $quantity . ' X ' . $this->renta_types[$key];
                    $this->total_heritors_paid_renta_by[$key] += $value;

                    $paid_renta_by_arr['nat_amount'][$key] = $quantity;
                }
            }
            $heritor_results[$i]['paid_renta_by_arr'] = $paid_renta_by_arr;
            $heritor_results[$i]['paid_renta_by'] = implode('</br>', $paid_renta_by);
            $heritor_results[$i]['paid_renta_by'] = '' != $heritor_results[$i]['paid_renta_by'] ? $heritor_results[$i]['paid_renta_by'] : '-';

            // Наследник - Платена рента в натура чрез - format to grid
            $paid_renta_nat_by = [];
            $paid_renta_nat_by_arr = [];
            if ($paid_renta_by_nat) {
                $amount = number_format($paid_renta_by_nat, 2, '.', '');
                $paid_renta_nat_by[] = $amount . ' лв.';
                $this->total_heritors_paid_renta_by_nat_amount += $paid_renta_by_nat;

                $paid_renta_nat_by_arr['amount'] = $amount;
            }
            if (!empty($paid_renta_nat_by_nat)) {
                foreach ($paid_renta_nat_by_nat as $key => $value) {
                    $quantity = number_format($value, 3, '.', '');
                    $paid_renta_nat_by[] = $quantity . ' X ' . $this->renta_types[$key];
                    $this->total_heritors_paid_renta_by_nat[$key] += $value;

                    $paid_renta_nat_by_arr['nat_amount'][$key] = $quantity;
                }
            }
            $heritor_results[$i]['paid_renta_nat_by_arr'] = $paid_renta_nat_by_arr;
            $heritor_results[$i]['paid_renta_nat_by'] = implode('</br>', $paid_renta_nat_by);

            // Наследник - Платена рента в натура детайлно - format to grid
            $all_paid_renta_nat_by_detailed = [];
            $all_paid_renta_nat_by_detailed_quantity_arr = [];
            $all_paid_renta_nat_by_detailed_unit_value_arr = [];
            if (!empty($paid_renta_nat_by_detailed)) {
                foreach ($paid_renta_nat_by_detailed as $key => $value) {
                    $quantity = number_format($value, 3, '.', '');
                    $unitValue = null != $paid_renta_nat_by_detailed_unit_value[$key] ? $paid_renta_nat_by_detailed_unit_value[$key] : $this->renta_types_values[$key];

                    if (null == $unitValue) {
                        $unitValue = '-';
                    }

                    $all_paid_renta_nat_by_detailed[] = $quantity . ' X ' . $this->renta_types[$key] . ' X ' . $unitValue . ' ед.ст.';
                    $all_paid_renta_nat_by_detailed_quantity_arr[$key] += $quantity;
                    if ('-' != $unitValue) {
                        $all_paid_renta_nat_by_detailed_unit_value_arr[$key] += $unitValue;
                    }
                }
            }
            $heritor_results[$i]['all_paid_renta_nat_by_detailed_quantity_arr'] = $all_paid_renta_nat_by_detailed_quantity_arr;
            $heritor_results[$i]['all_paid_renta_nat_by_detailed_unit_value_arr'] = $all_paid_renta_nat_by_detailed_unit_value_arr;
            $heritor_results[$i]['paid_renta_nat_by_detailed'] = implode('</br>', $all_paid_renta_nat_by_detailed);
            $heritor_results[$i]['paid_renta_nat_by_detailed'] = '' != $heritor_results[$i]['paid_renta_nat_by_detailed'] ? $heritor_results[$i]['paid_renta_nat_by_detailed'] : '-';

            // Наследник - Общо платена рента в лева
            $sumPaidRenta = 0;
            if ($paid_renta || $paid_renta_by_nat) {
                $sumPaidRenta = $paid_renta + $paid_renta_by_nat;
                $heritor_results[$i]['total_by_renta'] = number_format($sumPaidRenta, 2, '.', '') . ' лв.';
                $heritor_results[$i]['total_by_renta_sum'] = $sumPaidRenta;
            }
            $heritor_results[$i]['total_by_renta'] = '' != $heritor_results[$i]['total_by_renta'] ? $heritor_results[$i]['total_by_renta'] : '-';

            // Сумиране на Общо платена рента в лева за Наследник
            $this->total_heritors_sum_by_paid_renta += $sumPaidRenta;

            // Наследник - Общо платена рента в натура
            $totalByRentaNatura = [];
            if (!empty($paid_renta_nat) || !empty($paid_renta_nat_by_nat)) {
                $totalByRentaNatura = $this->arraySumIdenticalKeys($paid_renta_nat, $paid_renta_nat_by_nat);

                // Наследници - Сумиране на Общо платена рента в натура - Общо за страница
                foreach (array_keys($this->total_heritors_sum_by_paid_renta_nat + $totalByRentaNatura) as $key) {
                    $this->total_heritors_sum_by_paid_renta_nat[$key] = @($this->total_heritors_sum_by_paid_renta_nat[$key] + $totalByRentaNatura[$key]);
                }
            }

            $heritor_results[$i]['total_by_renta_nat_arr'] = $totalByRentaNatura;
            $heritor_results[$i]['total_by_renta_nat'] = implode('</br>', $totalByRentaNatura);
            $heritor_results[$i]['total_by_renta_nat'] = '' != $heritor_results[$i]['total_by_renta_nat'] ? $heritor_results[$i]['total_by_renta_nat'] : '-';

            $heritor_results[$i] = $this->addNewRentaPayrolls($paidResults, $heritor_results[$i]);
            $heritor_results[$i]['root_id'] = $heritor_data[$ownerID]['root_id'];

            if (!$heritor_data[$ownerID]['c_num_array']) {
                $heritor_data[$ownerID]['c_num_array'] = [];
            }

            // Use the same farming as owner farming. It is needed on selecting the contracts when there are nested heritors
            $heritor_results[$i]['farming'] = $ownerResults['farming'];

            $heritor_results[$i]['c_num_array'] = array_unique($heritor_data[$ownerID]['c_num_array']);
            $heritor_results[$i]['contract_array'] = [$heritor_data[$ownerID]['contract_id']];
            // $heritor_results[$i]['area'] = number_format($heritor_data[$ownerID]['area'], 3, '.', '');
            $heritor_results[$i]['cultivated_area'] = number_format($heritor_data[$ownerID]['cultivated_area'], 3, '.', '');
            $heritor_results[$i]['area'] = $heritor_data[$ownerID]['area'];
            $heritor_results[$i]['pu_area'] = number_format($heritor_data[$ownerID]['pu_area'], 3, '.', '');
            $heritor_results[$i]['has_personal_use'] = $heritor_data[$ownerID]['has_personal_use'];
            $heritor_results[$i]['total_area_contract'] = $heritor_data[$ownerID]['total_area_contract'];
            $heritor_results[$i]['owner_area'] = $heritor_data[$ownerID]['area'];
            // $heritor_results[$i]['owner_area'] = number_format($heritor_data[$ownerID]['area'], 3, '.', '');
            $heritor_results[$i]['area_array'] = $heritor_data[$ownerID]['area_array'];
            $heritor_results[$i]['owner_plots_percent'] = $heritor_data[$ownerID]['owner_plots_percent'];
            $heritor_results[$i]['plots_name_array'] = $heritor_data[$ownerID]['plots_name_array'];

            $heritor_results[$i]['plots_contracts_area_array'] = $heritor_data[$ownerID]['plots_contracts_area_array'];
            $heritor_results[$i]['plots_contracts_charged_renta_nat_values'] = $ownerResults['plots_contracts_charged_renta_nat_values'];
            $heritor_results[$i]['due_renta'] = 0;

            // РЕНТА В ЛЕВА
            $heritor_results[$i]['plots_contracts_charged_renta_down_grid'] = [];
            if (!empty($heritor_data[$ownerID]['charged_renta_plots'])) {
                foreach ($heritor_data[$ownerID]['charged_renta_plots'] as $contractID => $value) {
                    foreach ($value as $plotID => $plotRenta) {
                        // $plotContractRentaHeritor = $heritor_data[$ownerID]['plots_contracts_renta_heritors'][$contractID];
                        $plotContractRentaHeritor = $heritor_data[$ownerID]['charged_renta_plots'][$contractID];

                        if (array_key_exists($plotID, $plotContractRentaHeritor)) {
                            $heritor_results[$i]['charged_renta'] += $plotContractRentaHeritor[$plotID];

                            $heritor_results[$i]['plots_contracts_charged_renta_down_grid'][$contractID][$plotID] = $plotContractRentaHeritor[$plotID];
                        }
                    }
                }
            }
            if (!is_null($heritor_results[$i]['charged_renta'])) {
                $heritor_results[$i]['charged_renta'] = $heritor_results[$i]['charged_renta'] > 0 ? number_format($heritor_results[$i]['charged_renta'], 2, '.', '') : '0.00';
            } else {
                $heritor_results[$i]['charged_renta'] = '-';
            }

            $heritor_results[$i]['plots_contracts_renta_down_grid'] = [];
            if (!empty($heritor_data[$ownerID]['contracts_renta_plots'])) {
                foreach ($heritor_data[$ownerID]['contracts_renta_plots'] as $contractID => $value) {
                    foreach ($value as $plotID => $plotRenta) {
                        // $plotContractRentaHeritor = $heritor_data[$ownerID]['plots_contracts_renta_heritors'][$contractID];
                        $plotContractRentaHeritor = $heritor_data[$ownerID]['contracts_renta_plots'][$contractID];
                        $onlyContractChargedRenta = $heritor_data[$ownerID]['charged_renta_plots'][$contractID];

                        $onlyContractChargedRenta ??= [];

                        if ($plotContractRentaHeritor[$plotID] > 0) {
                            if (!array_key_exists($plotID, $onlyContractChargedRenta)) {
                                $heritor_results[$i]['renta'] += $plotContractRentaHeritor[$plotID];
                                $heritor_results[$i]['plots_contracts_renta_down_grid'][$contractID][$plotID] = $plotContractRentaHeritor[$plotID];
                            }

                            $heritor_results[$i]['due_renta'] += $plotContractRentaHeritor[$plotID];
                        }
                    }
                }
            }

            $heritor_results[$i]['renta'] = $heritor_results[$i]['renta'] > 0 ? number_format($heritor_results[$i]['renta'], 2, '.', '') : '0.00';

            $heritor_results[$i]['due_renta'] = $heritor_results[$i]['renta'] + $heritor_results[$i]['charged_renta'];

            // РЕНТА В НАТУРА

            $heritor_results[$i]['due_renta_nat'] = [];
            $heritor_results[$i]['plots_contracts_renta_nat_down_grid'] = [];
            $heritor_results[$i]['plots_contracts_charged_renta_nat_down_grid'] = [];
            // if (!empty($heritor_results[$i]['plots_contracts_charged_renta_nat_values'])) {

            //     foreach ($heritor_results[$i]['plots_contracts_charged_renta_nat_values'] as $contractID => $value) {
            //         foreach ($value as $plotID => $plotIDRenta) {
            //             foreach ($plotIDRenta as $rentaType => $rentaNat) {

            //                 // $plotContractRentaNatHeritor = $heritor_data[$ownerID]['plots_contracts_renta_nat_heritors'][$contractID];
            //                 foreach ($heritor_results[$i]['plots_contracts_area_array'] as $contractPlot) {

            //                     if ($contractPlot['contract_id'] == $contractID && $contractPlot['plot_gid'] == $plotID) {
            //                         $puArea = $UserDbPaymentsController->getPersonalUseArea($personalUse, $ownerID, ['contract_id' => $contractID, 'pc_rel_id' => $contractPlot['pc_rel_id']], $heritor_data[$ownerID]['area']);
            //                         $plotArea = $contractPlot['area'] - $puArea;
            //                         break;
            //                     }
            //                 }

            //                 if(!empty($heritor_data[$ownerID]['charged_renta_nat_plots'][$contractID][$plotID][$rentaType])){
            //                     $rentaNatValue = $rentaNat * $plotArea;
            //                     $heritor_results[$i]['charged_renta_nat'][$rentaType] += $rentaNatValue;
            //                     $heritor_results[$i]['plots_contracts_charged_renta_nat_down_grid'][$contractID][$plotID][$rentaType] = $rentaNatValue;
            //                 } else {

            //                 }

            //             }
            //         }
            //     }
            // }

            if (!empty($heritor_data[$ownerID]['contracts_renta_nat_plots'])) {
                foreach ($heritor_data[$ownerID]['contracts_renta_nat_plots'] as $contractID => $value) {
                    foreach ($value as $plotID => $plotIDRenta) {
                        foreach ($plotIDRenta as $rentaType => $rentaNat) {
                            $plotContractRentaNatHeritor = $heritor_data[$ownerID]['contracts_renta_nat_plots'][$contractID][$plotID];
                            $onlyContractChargedRentaNat = $heritor_results[$i]['plots_contracts_charged_renta_nat'][$contractID][$plotID];

                            $onlyContractChargedRentaNat ??= [];

                            if (!empty($heritor_data[$ownerID]['charged_renta_nat_plots'][$contractID][$plotID][$rentaType])) {
                                $rentaNatValue = $rentaNat * $plotArea;
                                $heritor_results[$i]['charged_renta_nat'][$rentaType] += $heritor_data[$ownerID]['charged_renta_nat_plots'][$contractID][$plotID][$rentaType];
                                $heritor_results[$i]['plots_contracts_charged_renta_nat_down_grid'][$contractID][$plotID][$rentaType] = $heritor_data[$ownerID]['charged_renta_nat_plots'][$contractID][$plotID][$rentaType];
                            } else {
                                $heritor_results[$i]['renta_nat'][$rentaType] += $plotContractRentaNatHeritor[$rentaType];
                                $heritor_results[$i]['plots_contracts_renta_nat_down_grid'][$contractID][$plotID][$rentaType] = $plotContractRentaNatHeritor[$rentaType];
                            }

                            // if (
                            //     // !is_null($plotContractRentaNatHeritor)
                            //     // && array_key_exists($rentaType, $plotContractRentaNatHeritor)
                            //     // && !is_null($onlyContractChargedRentaNat)
                            //     !array_key_exists($rentaType, $onlyContractChargedRentaNat)
                            // ) {
                            //     $heritor_results[$i]['renta_nat'][$rentaType] += $plotContractRentaNatHeritor[$rentaType];
                            //     $heritor_results[$i]['plots_contracts_renta_nat_down_grid'][$contractID][$plotID][$rentaType] = $plotContractRentaNatHeritor[$rentaType];
                            // } else {
                            //     if (is_null($heritor_results[$i]['renta_nat'][$rentaType])) {
                            //         $heritor_results[$i]['renta_nat'][$rentaType] = '0.000';
                            //     }
                            // }

                            $heritor_results[$i]['due_renta_nat'][$rentaType] += $plotContractRentaNatHeritor[$rentaType];
                        }
                    }
                }
            }

            if (!empty($heritor_results[$i]['renta_nat'])) {
                ksort($heritor_results[$i]['renta_nat']);
                foreach ($heritor_results[$i]['renta_nat'] as $rentaNatTypeID => $rentaNat) {
                    if (0 == $rentaNatTypeID) {
                        continue;
                    }

                    $rentaNat = $rentaNat > 0 ? $rentaNat : '0.000';

                    $heritor_results[$i]['renta_nat_text'] .= number_format($rentaNat, 3, '.', '') . ' X ' . $this->renta_types[$rentaNatTypeID] . '<br/>';
                    $heritor_results[$i]['renta_nat'][$rentaNatTypeID] = number_format($rentaNat, 3, '.', '');
                }
            }

            if (!empty($heritor_results[$i]['charged_renta_nat'])) {
                ksort($heritor_results[$i]['charged_renta_nat']);
                foreach ($heritor_results[$i]['charged_renta_nat'] as $chargedRentaNatTypeID => $chargedRentaNat) {
                    if (0 == $chargedRentaNatTypeID) {
                        continue;
                    }
                    $chargedRentaNat = $chargedRentaNat > 0 ? $chargedRentaNat : '0.000';
                    $heritor_results[$i]['charged_renta_nat'][$chargedRentaNatTypeID] = number_format($chargedRentaNat, 3, '.', '');
                }
            }

            if (!empty($heritor_results[$i]['charged_renta_nat'])) {
                foreach ($heritor_results[$i]['charged_renta_nat'] as $natTypeID => $rentaNat) {
                    $chargedRentaNat = $heritor_results[$i]['charged_renta_nat'][$natTypeID];
                    if ('-' != $chargedRentaNat && '' != $chargedRentaNat && $chargedRentaNat >= 0) {
                        $heritor_results[$i]['charged_renta_nat_text'] .= number_format($chargedRentaNat, 3, '.', '') . ' X ' . $this->renta_types[$natTypeID] . '<br/>';
                    } else {
                        $heritor_results[$i]['charged_renta_nat_text'] .= '-<br/>';
                    }
                }
            }

            if (!$heritor_results[$i]['renta_nat_text']) {
                $heritor_results[$i]['renta_nat_text'] = '-';
            } else {
                $heritor_results[$i]['renta_nat_text'] = rtrim($heritor_results[$i]['renta_nat_text'], ',<br/>');
                if (!empty($heritor_results[$i]['paid_renta_nat_details'])) {
                    foreach ($heritor_results[$i]['paid_renta_nat_details'] as $key => $value) {
                        if ('' != $key) {
                            $this->total_heritors_paid_renta_nat[$key] += $value;
                        }
                    }
                }
            }

            if (!$heritor_results[$i]['charged_renta_nat_text']) {
                $heritor_results[$i]['charged_renta_nat_text'] = '-';
            }

            $heritor_results[$i]['paid_renta'] = '' != $heritor_results[$i]['paid_renta'] ? number_format($heritor_results[$i]['paid_renta'], 2, '.', '') : '0.00';

            // Общо платена рента в лева за наследник
            $this->total_heritors_paid_renta += $heritor_results[$i]['paid_renta'];

            if ($personTotalArea > 0) {
                $unpaid_renta = $heritor_results[$i]['due_renta'] - $heritor_results[$i]['paid_renta'];
                $heritor_results[$i]['unpaid_renta'] = number_format(($unpaid_renta < 0) ? '0.00' : $unpaid_renta, 2, '.', '');

                // This calculation is used by getOwnersPayroll // its total unpaid to owner  // its not unpaid renta by plot
                $ownerResults['unpaid_renta'] -= $heritor_results[$i]['paid_renta'];
                $ownerResults['unpaid_renta'] = number_format($ownerResults['unpaid_renta'], 2, '.', '');

                $heritor_results[$i]['over_paid'] = ($unpaid_renta >= 0) ? '0.00' : number_format($unpaid_renta * (-1), 2, '.', '');
            }

            if ($heritor_results[$i]['due_renta_nat']) {
                $rentaTypes = [];
                foreach ($heritor_results[$i]['due_renta_nat'] as $rentaType => $rentaNatura) {
                    $rentaTypes[$rentaType] = $this->renta_types[$rentaType];
                }

                $heritor_results[$i]['renta_nat_type'] = implode('</br>', $rentaTypes);
                $heritor_results[$i]['renta_nat_types_arr'] = $rentaTypes;
            }

            if (!empty($heritor_results[$i]['renta_nat'])) {
                $unpaid_renta_nat = [];
                $unpaid_renta_nat_arr = [];
                $over_paid_renta_nat = [];
                $over_paid_renta_nat_arr = [];
                $total_unpaid_renta_nat = [];
                $unpaid_renta_nat_unit_value = [];
                $unpaid_renta_nat_unit_value_arr = [];

                foreach ($heritor_results[$i]['due_renta_nat'] as $rentaType => $rentaNatura) {
                    $paidRentaNatura = $heritor_results[$i]['paid_renta_nat_details'];

                    $unpaidRentaNatura = $rentaNatura - $paidRentaNatura[$rentaType];
                    $quantity = number_format(($unpaidRentaNatura < 0) ? '0.000' : $unpaidRentaNatura, 3, '.', '');
                    $quantityOverPaid = number_format(($unpaidRentaNatura >= 0) ? '0.000' : $unpaidRentaNatura * (-1), 3, '.', '');
                    $quantityValue = number_format($quantity * $this->renta_types_values[$rentaType], 2, '.', '');

                    $unpaid_renta_nat_unit_value[] = $quantityValue . ' лв.';
                    $unpaid_renta_nat_unit_value_arr[$rentaType] = $quantityValue;
                    $unpaid_renta_nat[] = $quantity . ' X ' . $this->renta_types[$rentaType];
                    $unpaid_renta_nat_arr[$rentaType] = $quantity;
                    $over_paid_renta_nat[] = $quantityOverPaid . ' X ' . $this->renta_types[$rentaType];
                    $over_paid_renta_nat_arr[$rentaType] = $quantityOverPaid;

                    if (!$heritor_results[$i]['is_dead']) {
                        $this->total_heritors_unpaid_renta_nat[$rentaType] += $quantity;
                    } else {
                        if (0 == $counterHeritors[0]['count']) {
                            $this->total_heritors_unpaid_renta_nat[$rentaType] += $quantity;
                        }
                    }

                    $this->total_heritors_over_paid_renta_nat[$rentaType] += $quantityOverPaid;
                }

                $heritor_results[$i]['unpaid_renta_nat_arr'] = $unpaid_renta_nat_arr;
                $heritor_results[$i]['over_paid_renta_nat_arr'] = $over_paid_renta_nat_arr;
                $heritor_results[$i]['unpaid_renta_nat_unit_value_arr'] = $unpaid_renta_nat_unit_value_arr;
                $heritor_results[$i]['unpaid_renta_nat'] = implode('</br>', $unpaid_renta_nat);
                $heritor_results[$i]['over_paid_nat'] = implode('</br>', $over_paid_renta_nat);
                $heritor_results[$i]['unpaid_renta_nat_unit_value'] = implode('</br>', $unpaid_renta_nat_unit_value);
            }

            $this->iterator++;
            $heritor_results[$i]['id'] = $this->iterator;

            // Сумиране на Надплатена рента в лева на наследниците
            $this->total_heritors_over_paid_renta += $heritor_results[$i]['over_paid'];

            if (!empty($heritor_data[$ownerID]['plots_contracts_renta_heritors'])) {
                foreach ($heritor_data[$ownerID]['plots_contracts_renta_heritors'] as $contractIDHer => $value) {
                    foreach ($value as $plotIDHer => $rentaHer) {
                        $heritor_results[$i]['plots_contracts_renta'][] = ['renta_by_plot' => $rentaHer, 'contract_id' => $contractIDHer, 'plot_gid' => $plotIDHer];
                    }
                }
            }

            $heritor_results[$i]['plots_contracts_renta_nat'] = $heritor_data[$ownerID]['plots_contracts_renta_nat_heritors'];

            // Платена рента в натура
            if (!empty($heritor_results[$i]['renta_nat'])) {
                foreach ($heritor_results[$i]['renta_nat'] as $rentaType => $rentaNatura) {
                    if (!is_null($heritor_results[$i]['paid_renta_nat_details'][$rentaType])) {
                        $heritor_results[$i]['paid_renta_nat'] .= $heritor_results[$i]['paid_renta_nat_details'][$rentaType] . ' X ' . $this->renta_types[$rentaType] . '<br/>';
                    } else {
                        $heritor_results[$i]['paid_renta_nat'] .= '0.000 X ' . $this->renta_types[$rentaType] . '<br/>';
                    }
                }
            } else {
                $heritor_results[$i]['unpaid_renta_nat'] = '0.000';
            }

            if (empty($heritor_results[$i]['renta_nat']) && !empty($heritor_results[$i]['paid_renta_nat_details'])) {
                $heritor_results[$i]['paid_renta_nat'] = '';

                foreach ($heritor_results[$i]['paid_renta_nat_details'] as $rentaType => $rentaNatura) {
                    $heritor_results[$i]['paid_renta_nat'] .= $rentaNatura . ' X ' . $this->renta_types[$rentaType] . '</br>';
                }
            }

            if (is_null($results[$i]['paid_renta_nat']) || '' == $results[$i]['paid_renta_nat']) {
                $results[$i]['paid_renta_nat'] = '-';
            }

            if (empty($rentaTypes)) {
                $heritor_results[$i]['renta_nat_type'] = '[Без рента в натура]';
                $heritor_results[$i]['unpaid_renta_nat'] = '-';
                $heritor_results[$i]['over_paid_nat'] = '-';
                $heritor_results[$i]['unpaid_renta_nat_unit_value'] = '-';
            } else {
                // used for parent unpaid renta calcualation
                foreach ($ownerResults['unpaid_renta_nat_arr'] as $naturaType => $unpaidNatRenta) {
                    $ownerResults['paid_renta_nat_to_herritors_by_type'][$naturaType] += $heritor_results[$i]['paid_renta_nat_details'][$naturaType];
                }
            }

            // put new rows if reps are more than one
            $tmp_reps_array = explode(', ', $heritor_results[$i]['rep_names']);
            if (count($tmp_reps_array) > 1) {
                $heritor_results[$i]['rep_names'] = implode(', </br>', $tmp_reps_array);
            }
            if (null == $heritor_results[$i]['c_num_array']) {
                $heritor_results[$i]['c_num_array'] = $ownerResults['c_num_array'];
            }
            if (null == $heritor_results[$i]['plots_contracts_area_array']) {
                $heritor_results[$i]['plots_contracts_area_array'] = $ownerResults['plots_contracts_area_array'];
            }
            if ($heritor_results[$i]['is_dead'] && $recursive) {
                $heritor_results[$i]['children'] = $this->getOwnersHeritorsPayroll($pathOwner, $root_id, $heritor_percent_by_plots, $heritor_results[$i]);

                $heritor_results[$i]['iconCls'] = 'icon-tree-user-rip';

                if (0 == $counterHeritors[0]['count']) {
                    $this->total_heritors_unpaid_renta += $unpaid_renta;
                }

                // ЛП: От Изплолзваната площ на родителя изваждаме площта за лично ползване на наследниците
                $heritor_results[$i]['renta'] = 0;
                $heritor_results[$i]['charged_renta'] = 0;
                $heritor_results[$i]['unpaid_renta'] = 0;

                $heritor_results[$i]['renta_nat'] = [];
                $heritor_results[$i]['renta_nat_text'] = '';
                $heritor_results[$i]['charged_renta_nat'] = [];
                $heritor_results[$i]['charged_renta_nat_text'] = '';

                foreach ($heritor_results[$i]['children'] as $child) {
                    $heritor_results[$i]['area'] -= $child['pu_area'];
                    $heritor_results[$i]['children_pu_area'] += $child['pu_area'];

                    // Изчисляване рентата на родителя, като сумираме рентите на наследниците.
                    // Това се налага, защото наследниците може да имат лично ползване и общата рента да е по-малка от тази на родителя
                    $heritor_results[$i]['renta'] += $child['renta'];
                    $heritor_results[$i]['charged_renta'] += $child['charged_renta'];
                    $heritor_results[$i]['unpaid_renta'] += $child['unpaid_renta'];

                    if (!empty($child['renta_nat'])) {
                        foreach ($child['renta_nat'] as $rentaType => $rentaNat) {
                            if (isset($heritor_results[$i]['renta_nat'][$rentaType])) {
                                $heritor_results[$i]['renta_nat'][$rentaType] += $rentaNat;
                            } else {
                                $heritor_results[$i]['renta_nat'][$rentaType] = $rentaNat;
                            }
                        }
                    }

                    if (!empty($child['charged_renta_nat'])) {
                        foreach ($child['charged_renta_nat'] as $rentaType => $rentaNat) {
                            if (isset($heritor_results[$i]['charged_renta_nat'][$rentaType])) {
                                $heritor_results[$i]['charged_renta_nat'][$rentaType] += $rentaNat;
                            } else {
                                $heritor_results[$i]['charged_renta_nat'][$rentaType] = $rentaNat;
                            }
                        }
                    }
                }

                $heritor_results[$i]['renta'] = number_format($heritor_results[$i]['renta'], 2, '.', '');
                $heritor_results[$i]['charged_renta'] = number_format($heritor_results[$i]['charged_renta'], 2, '.', '');
                $heritor_results[$i]['unpaid_renta'] = number_format($heritor_results[$i]['unpaid_renta'], 2, '.', '');

                if (!empty($heritor_results[$i]['renta_nat'])) {
                    foreach ($heritor_results[$i]['renta_nat'] as $rentaType => $rentaNat) {
                        $heritor_results[$i]['renta_nat_text'] .= number_format($rentaNat, 3) . ' X ' . $heritor_results[$i]['renta_nat_types_arr'][$rentaType] . '</br>';
                    }
                }

                if (!empty($heritor_results[$i]['charged_renta_nat'])) {
                    foreach ($heritor_results[$i]['charged_renta_nat'] as $rentaType => $rentaNat) {
                        $heritor_results[$i]['charged_renta_nat_text'] .= number_format($rentaNat, 3) . ' X ' . $heritor_results[$i]['renta_nat_types_arr'][$rentaType] . '</br>';
                    }
                }
            } else {
                $heritor_results[$i]['iconCls'] = 'icon-tree-user';

                $this->total_heritors_unpaid_renta += $unpaid_renta;
            }

            $heritor_results[$i]['is_heritor'] = true;

            // used for parent unpaid renta calcualation
            foreach ($heritor_results[$i]['paid_renta_contract'] as $contract_id => $paid_renta) {
                $ownerResults['total_paid_renta_to_herritors_by_contracts'][$contract_id] += $paid_renta;
            }
            // used for parent unpaid renta nat calcualation
            foreach ($heritor_results[$i]['paid_renta_nat_details_contract'] as $contract_id => $paid_renta_nat) {
                foreach ($paid_renta_nat as $natura_type => $nat_value) {
                    $ownerResults['total_paid_renta_to_herritors_by_contracts_nat'][$contract_id][$natura_type] += $nat_value;
                }
            }
        }

        // used for parent row unpaid renta nat calcualation
        $unpaidRentaNatUnitText = $unpaidRentaNatText = [];
        if (isset($ownerResults['paid_renta_nat_to_herritors_by_type'])) {
            foreach ($ownerResults['unpaid_renta_nat_arr'] as $naturaType => $unpaidNatRenta) {
                $unpaidNatura = $unpaidNatRenta - $ownerResults['paid_renta_nat_to_herritors_by_type'][$naturaType];
                $unpaidRentaNatText[] = $unpaidNatura . ' X ' . $this->renta_types[$naturaType];
                $quantityValue = number_format($unpaidNatura * $this->renta_types_values[$naturaType], 2, '.', '');
                $unpaidRentaNatUnitText[] = number_format($unpaidNatura * $this->renta_types_values[$naturaType], 2, '.', '') . ' лв.';
            }

            $ownerResults['unpaid_renta_nat'] = implode('</br>', $unpaidRentaNatText);
            $ownerResults['unpaid_renta_nat_unit_value'] = implode('</br>', $unpaidRentaNatUnitText);
            $ownerResults['unpaid_renta_nat_text'] = $ownerResults['unpaid_renta_nat'];
        }

        return $heritor_results;
    }

    /**
     * @return array
     */
    public function getPayrollByOwner($isHeritor, $allRenta, $totalArea)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);
        $UsersController = new UsersController('Users');
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);

        $ownerID = $_POST['owner_id'];
        $pathHeritor = $_POST['path'];
        $ownerHeritorID = $_POST['owner_id'];

        if ('true' == $isHeritor) {
            $heritorNameOptions = [
                'return' => ["o.name || ' ' || o.surname || ' ' || o.lastname as owner_names", 'egn', 'mobile', 'phone', 'o.iban'],
                'where' => [
                    'owner_id' => ['column' => 'id', 'compare' => '=', 'prefix' => 'o', 'value' => $ownerID],
                ],
            ];

            $heritorNames = $UserDbOwnersController->getOwnersData($heritorNameOptions);

            $heritorName = $heritorNames[0]['owner_names'] ? $heritorNames[0]['owner_names'] : '';
            $heritorEgn = $heritorNames[0]['egn'] ? $heritorNames[0]['egn'] : '';

            $ownerHeritorID = $ownerID;
            $ownerID = $_POST['root_id'];
        }

        if (0 == $ownerID) {
            return $this->return;
        }

        $plots_contracts_renta = $allRenta['plots_contracts_renta_down_grid'];
        $plots_contracts_charged_renta = $allRenta['plots_contracts_charged_renta_down_grid'];
        $plots_contracts_renta_nat = $allRenta['plots_contracts_renta_nat_down_grid'];
        $plots_contracts_charged_renta_nat = $allRenta['plots_contracts_charged_renta_nat_down_grid'];

        list($farming_years, $farming_years_str, $farming_years_string) = $this->getFarmings($UsersController);

        $options = [
            'return' => [
                'DISTINCT(pc.id)', 'o.id as owner_id', 'kvs.gid as kvs_gid',
                $UserDbOwnersController::isDead('o', [$_POST['payroll_from_date'], $_POST['payroll_to_date']]),
                "(CASE WHEN o.owner_type = 1 THEN o.name || ' ' || o.surname || ' ' || o.lastname ELSE o.company_name END) as owner_names",
                'o.iban',
                '(CASE WHEN owner_type = 1 THEN egn ELSE eik END) as egn_eik',
                'o.phone',
                'o.mobile',
                "string_agg(DISTINCT(r_her.rep_name || ' ' || r_her.rep_surname || ' ' || r_her.rep_lastname), ',<br/>') as rep_names",
                "CASE WHEN string_agg(DISTINCT(r.rent_place), ',<br/>') IS NULL THEN string_agg(DISTINCT(o.rent_place), ',<br/>') ELSE string_agg(DISTINCT(r.rent_place), ',<br/>') END AS rent_place",
                'c.id as contract_id',
                'c.farming_id as farming',
                '(case when a.id is not null then a.id else c.id end) as contract_anex',
                // get plot data
                'gid', 'ekate', 'virtual_ekatte_name as land', 'kad_ident', 'COALESCE(mestnost, \'-\') as mestnost', 'COALESCE(virtual_category_title, \'-\') as category', 'virtual_ntp_title as area_type', 'pc.id as pc_rel_id',
                // calculate plot area for current contract
                'pc.area_for_rent as contract_area', 'po.percent', 'pc.area_for_rent * po.percent / 100 as plot_area',
                // get contract data
                'c.c_num', 'c.sv_num', "to_char(c.sv_date,'DD.MM.YYYY') as sv_date", 'c.virtual_contract_type as c_type',
                "to_char((CASE WHEN a.due_date IS NULL THEN c.due_date ELSE a.due_date END),'DD.MM.YYYY') as due_date",
                '(CASE WHEN a.renta_nat_type_id IS NULL THEN c.renta_nat_type_id ELSE a.renta_nat_type_id END) as renta_nat_type_id',
                'cr.id as charged_renta_id',
                // calculate renta
                'ROUND((CASE WHEN a.renta IS NULL THEN ( CASE WHEN pc.rent_per_plot IS NOT NULL THEN pc.rent_per_plot ELSE C .renta  END  ) ELSE ( CASE WHEN pc.rent_per_plot IS NOT NULL THEN pc.rent_per_plot  ELSE A .renta END  ) END)::numeric,2) as contract_renta',
                '(CASE   WHEN pc.rent_per_plot IS NOT NULL THEN  pc.rent_per_plot ELSE    cr.renta    END  ) as charged_renta_sum',
                // calculate renta nat
                '(CASE WHEN a.renta_nat IS NULL THEN c.renta_nat ELSE a.renta_nat END) as renta_nat',
                "(SELECT string_agg(renta_value::TEXT, ',') FROM su_contracts_rents WHERE contract_id = (CASE WHEN a.id IS NULL THEN c.id ELSE a.id END)) as all_renta_nat",
                "(SELECT string_agg(amount::TEXT, ',') FROM su_charged_renta_natura WHERE renta_id = cr.id) as all_charged_renta_nat",
            ],
            'where' => [
                'contract_type' => ['column' => 'nm_usage_rights', 'compare' => 'NOT IN', 'prefix' => 'c', 'value' => [1, 4]],
                'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'prefix' => 'c', 'value' => 'false'],
                'is_annnex' => ['column' => 'is_annex', 'compare' => '=', 'prefix' => 'c', 'value' => 'false'],
                'annex_action' => ['column' => 'annex_action', 'compare' => '=', 'prefix' => 'pc', 'value' => 'added'],
                'is_heritor' => ['column' => 'is_heritor', 'compare' => '=', 'prefix' => 'po', 'value' => $isHeritor],
                'percent' => ['column' => 'percent', 'compare' => '>', 'prefix' => 'po', 'value' => '0'],
                'active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'true'],
                'farming' => ['column' => 'farming_id', 'compare' => 'IN', 'prefix' => 'c', 'value' => $this->arrayHelper->filterEmptyStringArr($_POST['payroll_farming'])],
                'ekate' => ['column' => 'ekate', 'compare' => $this->getPayrollEkateCompareType($_POST), 'prefix' => 'kvs', 'value' => $this->filterPayrollEkateValue($_POST['payroll_ekate'])],
                'owner_id' => ['column' => 'id', 'compare' => '=', 'prefix' => 'o', 'value' => $ownerHeritorID],
                'is_edited' => ['column' => "(case when kvs.is_edited = false then true else kvs.edit_active_from > '" . $_POST['payroll_to_date'] . "' end)", 'compare' => '=', 'value' => 'TRUE'],
            ],
            'start_date' => $_POST['payroll_to_date'],
            'due_date' => $_POST['payroll_from_date'],
            'year_id' => $farming_years,
            'custom_counter' => 'COUNT(DISTINCT(pc.id))',
            'offset' => ($_POST['pager'] - 1) * $_POST['rows'],
            'limit' => $_POST['rows'],
            'sort' => $_POST['sort'],
            'order' => $_POST['order'],
            'group' => 'pc.id, o.id, c.id, kvs.gid, po.percent, cr.id, cr.renta, a.id, a.renta, a.renta_nat, a.renta_nat_type_id, a.due_date',
        ];

        if ($isHeritor) {
            $options['where']['path'] = ['column' => 'path', 'compare' => '=', 'prefix' => 'po', 'value' => $_POST['path']];
        }

        $options['joins']['por_her'] = 'left join ' . $UserDbController->DbHandler->plotsOwnersRelTable . " por_her on por_her.pc_rel_id = pc.id and por_her.owner_id = '" . $ownerID . "'";
        if (!empty($pathHeritor)) {
            $options['joins']['por_her'] = 'left join ' . $UserDbController->DbHandler->plotsOwnersRelTable . " por_her on por_her.path = '" . $pathHeritor . "'";
        }

        $options['joins']['r_her'] = 'left join ' . $UserDbController->DbHandler->tableOwnersReps . ' r_her on r_her.id = por_her.rep_id';

        $counter = $UserDbPaymentsController->getPayrollData($options, true, false);

        if (0 == $counter[0]['count']) {
            return $this->return;
        }

        $results = $UserDbPaymentsController->getPayrollData($options, false, false);

        $personalUseOptions = [
            'contract_id' => implode(',', array_unique(array_column($results, 'contract_id'))),
            'year' => $_POST['farming_year'],
            'chosen_years' => $_POST['farming_year'],
            'owner_ids' => array_unique($ownerID),
        ];
        $personalUses = $UserDbPaymentsController->getPersonalUseForOwners($personalUseOptions, false, false);

        $resultsCount = count($results);
        $paidByContract = [];
        for ($y = 0; $y < $resultsCount; $y++) {
            if (array_key_exists($results[$y]['contract_id'], $paidByContract)) {
                continue;
            }

            // get paid renta by contract
            $paidOptionsByContract = [
                'return' => [
                    'round(SUM(p.amount)::numeric, 2) as trans_amount',
                ],
                'where' => [
                    'contract_id' => ['column' => 'contract_id', 'compare' => '=', 'prefix' => 'p', 'value' => $results[$y]['contract_id']],
                    'owner_id' => ['column' => 'owner_id', 'compare' => '=', 'prefix' => 'p', 'value' => $results[$y]['owner_id']],
                    'farming_year' => ['column' => 'farming_year', 'compare' => 'IN', 'prefix' => 'p', 'value' => $farming_years],
                    'paid_from' => ['column' => 'paid_from', 'compare' => '=', 'prefix' => 'p', 'value' => '1'],
                ],
                'year_id' => $farming_years_string,
                'order' => 'asc',
                'group' => 'p.contract_id',
            ];

            $paidResultsByContract = $UserDbPaymentsController->getPaidData($paidOptionsByContract, false, false);
            $paidByContract[$results[$y]['contract_id']] = $paidResultsByContract[0]['trans_amount'];

            // get paid renta by contract
            $paidNatOptionsByContract = [
                'return' => [
                    'round(SUM(pn.amount)::numeric, 3) as trans_amount_nat',
                    'pn.nat_type as nat_type',
                ],
                'where' => [
                    'contract_id' => ['column' => 'contract_id', 'compare' => '=', 'prefix' => 'p', 'value' => $results[$y]['contract_id']],
                    'owner_id' => ['column' => 'owner_id', 'compare' => '=', 'prefix' => 'p', 'value' => $results[$y]['owner_id']],
                    'farming_year' => ['column' => 'farming_year', 'compare' => 'IN', 'prefix' => 'p', 'value' => $farming_years],
                    'paid_from' => ['column' => 'paid_from', 'compare' => '=', 'prefix' => 'p', 'value' => '2'],
                ],
                'year_id' => $farming_years_string,
                'order' => 'asc',
                'group' => 'p.contract_id, pn.nat_type',
            ];

            $paidNatResultsByContract = $UserDbPaymentsController->getPaidData($paidNatOptionsByContract, false, false);
            $paidNatResCount = count($paidNatResultsByContract);
        }

        // define total variables
        $total_area = 0;
        $total_personal_use_area = 0;
        $total_renta = 0;
        $total_charged_renta = 0;
        $total_paid_renta = 0;
        $total_contract_renta = [];
        $total_contract_area = [];
        $total_contract_area_nat = [];
        $total_contract_area_no_percent = [];
        // define temp variables
        $temp_total_contract_area = [];
        $temp_total_contract_area_nat = [];
        $temp_total_contract_area_no_percent = [];
        $temp_total_contract_area_nat_no_percent = [];
        $haveTotalContractArea = false;
        $contractsAreas = [];

        foreach ($results as $result) {
            if (!isset($contractsAreas[$result['contract_id']])) {
                $contractsAreas[$result['contract_id']] = 0;
            }

            $contractsAreas[$result['contract_id']] += $result['plot_area'];
        }

        for ($i = 0; $i < $resultsCount; $i++) {
            $results[$i]['pu_area'] = $UserDbPaymentsController->getPersonalUseArea($personalUses, $ownerHeritorID, ['contract_id' => $results[$i]['contract_id'], 'pc_rel_id' => $results[$i]['pc_rel_id']], $contractsAreas[$results[$i]['contract_id']]);

            $contractAreaWitoutPuArea = round(($results[$i]['plot_area'] - $results[$i]['pu_area']), 3);
            $temp_total_contract_area[$results[$i]['contract_id']] += $contractAreaWitoutPuArea;
            $temp_total_contract_area_no_percent[$results[$i]['contract_id']] += ($results[$i]['contract_area'] - $results[$i]['pu_area']);

            if ($results[$i]['contract_renta'] <= 0 && is_null($results[$i]['charged_renta_sum'])) {
                continue;
            }

            $total_contract_area[$results[$i]['contract_id']] += $contractAreaWitoutPuArea;
            $total_contract_area_no_percent[$results[$i]['contract_id']] += ($results[$i]['contract_area'] - $results[$i]['pu_area']);
            $haveTotalContractArea = true;
        }

        $haveTotalContractAreaAndNat = false;
        $totalResultByContractPlot = [];
        for ($i = 0; $i < $resultsCount; $i++) {
            $contractAreaWitoutPuArea = $results[$i]['plot_area'] - $results[$i]['pu_area'];

            $totalResultByContractPlot[$results[$i]['contract_id']][$results[$i]['kvs_gid']] = $results[$i]['percent'];

            $temp_total_contract_area_nat[$results[$i]['contract_id']] += $contractAreaWitoutPuArea;
            $temp_total_contract_area_nat_no_percent[$results[$i]['contract_id']] += ($results[$i]['contract_area'] - $results[$i]['pu_area']);

            $allRentaNat = explode(',', $results[$i]['all_renta_nat']);

            $haveRentaNat = false;
            $haveChargedRentaNat = false;

            foreach ($allRentaNat as $key => $rentaNat) {
                if ($rentaNat > 0) {
                    $haveRentaNat = true;

                    break;
                }
            }

            $allChargedRentaNat = $results[$i]['all_charged_renta_nat'];

            if (!is_null($allChargedRentaNat)) {
                $haveChargedRentaNat = true;
            }

            if (!$haveRentaNat && !$haveChargedRentaNat) {
                continue;
            }

            $total_contract_area_nat[$results[$i]['contract_id']] += $contractAreaWitoutPuArea;
            $haveTotalContractAreaAndNat = true;
        }

        $total_renta_nat = [];
        $total_charged_renta_nat = [];
        $total_renta_nat_type = [];
        $total_paid_renta_nat = [];
        $total_paid_renta_by_amount = 0;
        $total_paid_renta_by_nat_amount = 0;
        $total_paid_renta_by = [];
        $total_paid_renta_nat_by = [];
        $total_all_paid_renta_nat_by_detailed = [];
        $total_unpaid_renta_nat = [];
        $total_sum_paid_renta_nat = [];

        for ($i = 0; $i < $resultsCount; $i++) {
            $whereClause = " where crn.renta_id = {$results[$i]['charged_renta_id']}";

            if (null == $results[$i]['charged_renta_id']) {
                $whereClause = ' where crn.renta_id is NULL';
            }

            $charged_rentas = $UserDbController->DbHandler->getDataByQuery(
                "SELECT 
		        sum(CASE WHEN crn.nat_is_converted = TRUE THEN NULL ELSE crn.amount END) as charged_renta_nat,
		        sum(CASE WHEN crn.nat_is_converted = TRUE THEN crn.amount * crn.nat_unit_price ELSE NULL END) as converted_renta_nat,
		        crn.nat_type
		        from su_charged_renta cr 
		        join su_charged_renta_natura crn on (cr.id = crn.renta_id)
		        {$whereClause}
		        group by crn.nat_type"
            );

            // $charged = []; // не се ползва никъде

            foreach ($charged_rentas as $key => $rent) {
                // $charged[$rent['nat_type']] = $rent; // не се ползва никъде

                if (!is_null($rent['converted_renta_nat'])) {
                    $results[$i]['converted_renta_nat'] += $rent['converted_renta_nat'];
                }
            }

            $contract_rentas_nat = $UserDbController->DbHandler->getDataByQuery(
                "SELECT distinct 
                    crt.renta_value as renta_nat,
  			        crt.renta_id as crt_renta_nat_id 
                from su_contracts_rents crt 
                where crt.contract_id = {$results[$i]['contract_anex']}
                ORDER BY crt.renta_id"
            );

            if (!$total_contract_area[$results[$i]['contract_id']]) {
                $total_contract_area[$results[$i]['contract_id']] = $temp_total_contract_area[$results[$i]['contract_id']];
            }
            if (!$total_contract_area_nat[$results[$i]['contract_id']]) {
                $total_contract_area_nat[$results[$i]['contract_id']] = $temp_total_contract_area_nat[$results[$i]['contract_id']];
            }

            $results[$i]['total_by_renta'] = '-';
            $results[$i]['total_by_renta_nat'] = '-';

            $results[$i]['path'] = null;

            if ('true' == $isHeritor) {
                $this->relation_id = $results[$i]['id'];
                $this->getPayrollByHeritor($results[$i]['owner_id'] . '.*{1}', $results[$i]['percent'], $pathHeritor);

                $results[$i]['percent'] = $this->percent[$results[$i]['id']];
                $results[$i]['paid_renta'] = $this->all_renta[$results[$i]['id']][$results[$i]['contract_id']]['paid_renta'];

                $percent_heritor = $results[$i]['percent'] / $totalResultByContractPlot[$results[$i]['contract_id']][$results[$i]['kvs_gid']];
                $results[$i]['area'] = number_format((($results[$i]['plot_area'] - $results[$i]['pu_area'])), 3, '.', '');
                $results[$i]['pu_area'] = number_format($results[$i]['pu_area'], 3, '.', '');
                $results[$i]['owner_names'] = '' != $heritorName ? $heritorName : $results[$i]['owner_names'];
                $results[$i]['egn_eik'] = '' != $heritorEgn ? $heritorEgn : $results[$i]['egn_eik'];
                $results[$i]['path'] = $pathHeritor;

                if (is_array($totalArea)) {
                    $area_coef = 0 == $results[$i]['area'] ? 0
                        : $results[$i]['area'] / $totalArea[$results[$i]['contract_id']];

                    $area_coef_nat = 0 == $results[$i]['area'] ? 0
                        : $results[$i]['area'] / $totalArea[$results[$i]['contract_id']];
                } else {
                    $area_coef = 0 == $results[$i]['area'] ? 0 : $results[$i]['area'] / $totalArea;

                    $area_coef_nat = 0 == $results[$i]['area'] ? 0 : $results[$i]['area'] / $totalArea;
                }

                $paidOptions = [
                    'custom_counter' => 'COUNT(DISTINCT(p.owner_id))',
                    'return' => [
                        'p.id as payment_id',
                        'p.owner_id as owner_id',
                        'p.contract_id as contract_id',
                        'case when pn.amount notnull and pn.unit_value notnull  then round((pn.amount * pn.unit_value)::numeric, 2) else round(p.amount::numeric, 2) end as trans_amount',
                        'case when pn.amount notnull then round(pn.amount::numeric,3) else round(p.amount_nat::numeric, 3) end as amount_nat',
                        'round(pn.amount::numeric, 3) as trans_amount_nat',
                        'pn.nat_type as nat_type',
                        'p.paid_in',
                        'p.paid_from',
                        'rent.name as trans_nat_type_text',
                        'round(pn.unit_value::numeric, 2) as unit_value',
                    ],
                    'where' => [
                        'path' => ['column' => 'path', 'compare' => '~', 'prefix' => 'p', 'value' => $pathHeritor],
                        'contract_id' => ['column' => 'contract_id', 'compare' => '=', 'prefix' => 'p', 'value' => $results[$i]['contract_id']],
                        'farming_year' => ['column' => 'farming_year', 'compare' => 'IN', 'prefix' => 'p', 'value' => $farming_years],
                    ],
                    'year_id' => $farming_years_string,
                    'order' => 'asc',
                    'sort' => 'p.contract_id',
                ];

                $paidResults = $UserDbPaymentsController->getPaidData($paidOptions);
            } else {
                $paidOptions = [
                    'custom_counter' => 'COUNT(DISTINCT(p.owner_id))',
                    'return' => [
                        'p.id as payment_id',
                        'p.owner_id as owner_id',
                        'p.contract_id as contract_id',
                        'case when pn.amount notnull and pn.unit_value notnull  then round((pn.amount * pn.unit_value)::numeric, 2) else round(p.amount::numeric, 2) end as trans_amount',
                        'case when pn.amount notnull then round(pn.amount::numeric,3) else round(p.amount_nat::numeric, 3) end as amount_nat',
                        'round(pn.amount::numeric, 3) as trans_amount_nat',
                        'pn.nat_type as nat_type',
                        'p.paid_in',
                        'p.paid_from',
                        'rent.name as trans_nat_type_text',
                        'round(pn.unit_value::numeric, 2) as unit_value',
                    ],
                    'where' => [
                        'owner_id' => ['column' => 'owner_id', 'compare' => '=', 'prefix' => 'p', 'value' => $ownerID],
                        'path' => ['column' => 'path', 'compare' => 'IS', 'prefix' => 'p', 'value' => 'NULL'],
                        'contract_id' => ['column' => 'contract_id', 'compare' => '=', 'prefix' => 'p', 'value' => $results[$i]['contract_id']],
                        'farming_year' => ['column' => 'farming_year', 'compare' => 'IN', 'prefix' => 'p', 'value' => $farming_years],
                    ],
                    'year_id' => $farming_years_string,
                    'order' => 'asc',
                    'sort' => 'p.contract_id',
                ];

                $paidResults = $UserDbPaymentsController->getPaidData($paidOptions);

                $results[$i] = $this->addNewRentaPayrolls($paidResults, $results[$i]);

                $results[$i]['area'] = number_format((($results[$i]['contract_area'] * $results[$i]['percent'] / 100) - $results[$i]['pu_area']), 3, '.', '');

                $area_coef = 0 == $results[$i]['area'] ? 0 : $results[$i]['area'] / $total_contract_area[$results[$i]['contract_id']];

                $area_coef_nat = 0 == $results[$i]['area'] ? 0 : $results[$i]['area'] / $total_contract_area_nat[$results[$i]['contract_id']];

                if ($results[$i]['contract_renta'] <= 0 & is_null($results[$i]['charged_renta_sum']) && $haveTotalContractArea) {
                    $area_coef = 0;
                }

                $allRentaNat = explode(',', $results[$i]['all_renta_nat']);

                $haveRentaNat = false;
                $haveChargedRentaNat = false;
                foreach ($allRentaNat as $key => $rentaNat) {
                    if ($rentaNat > 0) {
                        $haveRentaNat = true;

                        break;
                    }
                }

                $allChargedRentaNat = $results[$i]['all_charged_renta_nat'];

                if (!is_null($allChargedRentaNat)) {
                    $haveChargedRentaNat = true;
                }

                if (!$haveRentaNat && !$haveChargedRentaNat && $haveTotalContractAreaAndNat) {
                    $area_coef_nat = 0;
                }
            }

            if (!$results[$i]['paid_renta_nat_details'] && $this->all_renta_detailed) {
                $results[$i]['paid_renta_nat_details'] = $this->all_renta_detailed[$results[$i]['id']][$results[$i]['contract_id']]['paid_renta_nat'];
            }

            // calculate paid renta nat
            $paid_renta_nat_details = [];
            if (!empty($results[$i]['paid_renta_nat_details']) && '' != $results[$i]['paid_renta_nat_details'] && $area_coef_nat > 0) {
                ksort($results[$i]['paid_renta_nat_details']);

                foreach ($results[$i]['paid_renta_nat_details'] as $key => $value) {
                    $quantity = $value * $area_coef_nat;
                    $paid_renta_nat_details[] = number_format($quantity, 3, '.', '') . ' X ' . $this->renta_types[$key];
                    $total_paid_renta_nat[$key] += $quantity;
                }

                $results[$i]['paid_renta_nat'] = implode('</br>', $paid_renta_nat_details);
            } else {
                $results[$i]['paid_renta_nat'] = '-';
            }

            foreach ($plots_contracts_renta as $contractID => $value) {
                foreach ($value as $plotID => $rentaValue) {
                    $currentContract = $results[$i]['contract_id'];
                    $currentPlot = $results[$i]['gid'];

                    if ($currentContract == $contractID) {
                        $results[$i]['total_contract_rent'] += round($rentaValue, 2);
                    }

                    if ($currentContract == $contractID && $currentPlot == $plotID) {
                        $results[$i]['renta'] = $rentaValue;
                    }
                }
            }

            $contractRentaCoef = $results[$i]['renta'] / $results[$i]['total_contract_rent'];

            foreach ($plots_contracts_charged_renta as $contractID => $value) {
                foreach ($value as $plotID => $rentaValue) {
                    $currentContract = $results[$i]['contract_id'];
                    $currentPlot = $results[$i]['gid'];

                    if ($currentContract == $contractID) {
                        $results[$i]['total_contract_charged_rent'] += round($rentaValue, 2);
                    }

                    if ($currentContract == $contractID && $currentPlot == $plotID) {
                        $results[$i]['charged_renta'] = $rentaValue;
                    }
                }
            }

            if (empty($results[$i]['renta'])) {
                $contractRentaCoef = $results[$i]['charged_renta'] / $results[$i]['total_contract_charged_rent'];
            }

            $results[$i]['paid_renta'] = $contractRentaCoef * $results[$i]['paid_renta'];

            $results[$i]['renta'] = $results[$i]['renta'] < 0 ? 0 : $results[$i]['renta'];

            if (is_null($results[$i]['charged_renta'])) {
                $results[$i]['charged_renta'] = '-';
            } else {
                $results[$i]['charged_renta'] = $results[$i]['charged_renta'] <= 0 ? '0.00' : number_format($results[$i]['charged_renta'], 2, '.', '');
            }

            // calculate total

            $total_area += $results[$i]['area'];
            $results[$i]['area'] = number_format($results[$i]['area'], 3, '.', '');
            $total_personal_use_area += $results[$i]['pu_area'];
            $total_renta += $results[$i]['renta'];
            $total_charged_renta += $results[$i]['charged_renta'];
            $total_paid_renta += $results[$i]['paid_renta'];

            $results[$i]['renta'] = number_format($results[$i]['renta'], 2, '.', '');
            $results[$i]['renta_value'] = number_format(($results[$i]['charged_renta_sum'] ?? $results[$i]['contract_renta']), 2, '.', '');
            $results[$i]['paid_renta'] = number_format($results[$i]['paid_renta'], 2, '.', '');

            $renta_nat_array = explode(',', trim($results[$i]['renta_nat'], '{}'));
            $renta_nat_id_array = explode(',', trim($results[$i]['crt_renta_nat_id'], '{}'));

            if (0 == $results[$i]['paid_renta']) {
                $results[$i]['paid_renta'] = null;
            }
            if (!$results[$i]['sv_num']) {
                $results[$i]['sv_num'] = '-';
            }

            if (!$results[$i]['sv_date']) {
                $results[$i]['sv_date'] = '-';
            }

            if (!$results[$i]['owner_names']) {
                $results[$i]['owner_names'] = '-';
            }

            $results[$i]['renta_nat_type'] = '';
            $results[$i]['charged_renta_nat'] = [];
            $results[$i]['renta_nat'] = [];
            $contractRentNatCount = count($contract_rentas_nat);
            if (0 == $contractRentNatCount) {
                $results[$i]['renta_nat_type'] .= '-';
                $results[$i]['renta_nat'][] = '-';
            } else {
                foreach ($plots_contracts_renta_nat as $contractID => $value) {
                    foreach ($value as $plotID => $valueRentaNat) {
                        foreach ($valueRentaNat as $rentaType => $rentaNat) {
                            $currentContract = $results[$i]['contract_id'];
                            $currentPlot = $results[$i]['gid'];

                            if ($currentContract == $contractID && $currentPlot == $plotID) {
                                if ($rentaNat < 0) {
                                    $rentaNat = 0;
                                }
                                $results[$i]['renta_nat'][$rentaType] = number_format($rentaNat, 3, '.', '');

                                // sum renta natura
                                $total_renta_nat[$rentaType] += number_format($rentaNat, 3, '.', '');
                            }
                        }
                    }
                }

                foreach ($plots_contracts_charged_renta_nat as $contractID => $value) {
                    foreach ($value as $plotID => $valueRentaNat) {
                        foreach ($valueRentaNat as $rentaType => $rentaNat) {
                            $currentContract = $results[$i]['contract_id'];
                            $currentPlot = $results[$i]['gid'];

                            if ($rentaNat < 0) {
                                $rentaNat = 0;
                            }
                            if ($currentContract == $contractID && $currentPlot == $plotID) {
                                $results[$i]['charged_renta_nat'][$rentaType] = number_format($rentaNat, 3, '.', '');

                                // sum charged renta nat
                                $total_charged_renta_nat[$rentaType] += number_format($rentaNat, 3, '.', '');
                            }
                        }
                    }
                }

                for ($j = 0; $j < $contractRentNatCount; $j++) {
                    $renta_type = $contract_rentas_nat[$j]['crt_renta_nat_id'];

                    if (!array_key_exists($renta_type, $results[$i]['renta_nat'])) {
                        $results[$i]['renta_nat'][$renta_type] = '-';
                    }
                    if (!array_key_exists($renta_type, $results[$i]['charged_renta_nat'])) {
                        $results[$i]['charged_renta_nat'][$renta_type] = '-';
                    }
                    $results[$i]['renta_nat_type'] .= $this->renta_types[$contract_rentas_nat[$j]['crt_renta_nat_id']] . '<br/>';

                    // get all renta nat type
                    $renta_type_name = $this->renta_types[$renta_type];
                    if (!in_array($renta_type_name, $total_renta_nat_type)) {
                        $total_renta_nat_type[] = $renta_type_name;
                    }
                }
                ksort($results[$i]['renta_nat']);
                ksort($results[$i]['charged_renta_nat']);
            }

            $paid_renta = 0;
            $paid_renta_by_nat = 0;
            $paid_renta_nat = [];
            $paid_renta_nat_by_nat = [];
            $paid_renta_nat_by_detailed = [];
            $paid_renta_nat_by_detailed_unit_value = [];
            $paymentIds = [];

            $paidResultCount = count($paidResults);
            for ($m = 0; $m < $paidResultCount; $m++) {
                $paidResult = $paidResults[$m];
                $renta_type = $paidResult['nat_type'];

                if (1 == $paidResult['paid_from'] && $area_coef) {
                    if (2 == $paidResult['paid_in']) {
                        $paid_renta_nat[$renta_type] += $area_coef * $paidResult['amount_nat'];

                        continue;
                    }

                    $paid_renta += $contractRentaCoef * $paidResult['trans_amount'];
                } elseif (2 == $paidResult['paid_from'] && $area_coef_nat) {
                    if (2 == $paidResult['paid_in']) {
                        $paid_renta_nat_by_nat[$renta_type] += $area_coef_nat * $paidResult['trans_amount_nat'];

                        continue;
                    }

                    // Платена рента в натура детайлно
                    if (array_key_exists($renta_type, $paid_renta_nat_by_detailed)) {
                        $paid_renta_nat_by_detailed[$renta_type] += $area_coef_nat * $paidResult['trans_amount_nat'];
                    } else {
                        $paid_renta_nat_by_detailed[$renta_type] = $area_coef_nat * $paidResult['trans_amount_nat'];
                        $paid_renta_nat_by_detailed_unit_value[$renta_type] = $paidResult['unit_value'];
                    }

                    $paymentIds[] = $paidResult['payment_id'];
                    $paid_renta_by_nat += $area_coef * $paidResult['trans_amount'];
                }
            }

            // Платена рента в лева чрез
            $paid_renta_by = [];
            if ($paid_renta) {
                $amount = number_format($paid_renta, 2, '.', '');
                $paid_renta_by[] = $amount . ' лв.';
                $total_paid_renta_by_amount += (float) $amount;
            }
            if (!empty($paid_renta_nat)) {
                foreach ($paid_renta_nat as $key => $value) {
                    $quantity = number_format($value, 3, '.', '');
                    $paid_renta_by[] = $quantity . ' X ' . $this->renta_types[$key];
                    $total_paid_renta_by[$key] += $value;
                }
            }

            $results[$i]['paid_renta_by'] = implode('</br>', $paid_renta_by);

            // Платена рента в натура чрез
            $paid_renta_nat_by = [];
            $paid_renta_nat_by_arr = [];
            if ($paid_renta_by_nat) {
                $amount = number_format($paid_renta_by_nat, 2, '.', '');
                $paid_renta_nat_by[] = $amount . ' лв.';
                $total_paid_renta_by_nat_amount += (float)$amount;

                $paid_renta_nat_by_arr['amount'] = $amount;
            }
            if (!empty($paid_renta_nat_by_nat)) {
                foreach ($paid_renta_nat_by_nat as $key => $value) {
                    $quantity = number_format($value, 3, '.', '');
                    $paid_renta_nat_by[] = $quantity . ' X ' . $this->renta_types[$key];
                    $total_paid_renta_nat_by[$key] += $value;

                    $paid_renta_nat_by_arr['nat_amount'][$key] = $quantity;
                }
            }
            $results[$i]['paid_renta_nat_by_arr'] = $paid_renta_nat_by_arr;
            $results[$i]['paid_renta_nat_by'] = implode('</br>', $paid_renta_nat_by);

            if ($contractRentNatCount > 0) {
                $unpaid_renta_nat = [];
                $unpaid_renta_nat_unit_value = [];
                for ($m = 0; $m < $contractRentNatCount; $m++) {
                    $rentaType = $contract_rentas_nat[$m]['crt_renta_nat_id'];
                    $rentaNatura = $results[$i]['renta_nat'][$rentaType];

                    $chargedRentaNatura = $results[$i]['charged_renta_nat'];
                    $paidRentaNatura = $results[$i]['paid_renta_nat_details'];

                    if ('-' != $chargedRentaNatura[$rentaType]) {
                        $unpaidRentaNatura = $chargedRentaNatura[$rentaType] - ($area_coef_nat * $paidRentaNatura[$rentaType]);
                        $quantity = number_format(($unpaidRentaNatura < 0) ? 0 : $unpaidRentaNatura, 3, '.', '');
                        $quantityValue = number_format($quantity * $this->renta_types_values[$rentaType], 2, '.', '');

                        $unpaid_renta_nat_unit_value[] = $quantityValue . ' лв.';
                        $unpaid_renta_nat[] = $quantity . ' X ' . $this->renta_types[$rentaType];

                        $total_unpaid_renta_nat[$rentaType] += $quantity;

                        continue;
                    }

                    $unpaidRentaNatura = $rentaNatura - ($area_coef_nat * $paidRentaNatura[$rentaType]);
                    $quantity = number_format(($unpaidRentaNatura < 0) ? 0 : $unpaidRentaNatura, 3, '.', '');
                    $quantityValue = number_format($quantity * $this->renta_types_values[$rentaType], 2, '.', '');

                    $unpaid_renta_nat_unit_value[] = $quantityValue . ' лв.';
                    $unpaid_renta_nat[] = $quantity . ' X ' . $this->renta_types[$rentaType];

                    $total_unpaid_renta_nat[$rentaType] += $quantity;
                }

                $results[$i]['unpaid_renta_nat'] = implode('</br>', $unpaid_renta_nat);
                $results[$i]['unpaid_renta_nat_unit_value'] = implode('</br>', $unpaid_renta_nat_unit_value);
            }

            // Остатък рента в лева
            $unpaid_renta = (!is_null($results[$i]['charged_renta']) && '-' != $results[$i]['charged_renta'])
                                                    ? ($results[$i]['charged_renta'] - $results[$i]['paid_renta'])
                                                            : ($results[$i]['renta'] - $results[$i]['paid_renta']);

            $results[$i]['unpaid_renta'] = ($unpaid_renta < 0) ? 0 : number_format($unpaid_renta, 2, '.', '');

            if ($results[$i]['is_dead'] && false == $isHeritor) {
                $pathOwner = $results[$i]['owner_id'] . '.*{1}';
                // Update by reference owners records
                $this->getOwnersHeritorsPayroll($pathOwner, $results[$i]['owner_id'], false, $results[$i]);
            }

            // Update parent/owner record logic start
            // If the owner/parent is dead (owed 100BGN) and we pay 100BGN to an heir, this amount should not be marked as owed to the owner.
            // If the owner/parent is dead (owed 200bgn) and 100lv is paid to him/her, and 50bgn is paid to the heir, then this should result in 50bgn owed to the owner
            // These cases refer to the year of the owner's death, in which both owner and heir can be paid
            $contract_id = $results[$i]['contract_id'];
            if (isset($results[$i]['total_paid_renta_to_herritors_by_contracts'][$contract_id])) {
                $total_renta = 0;
                $contract_id = $results[$i]['contract_id'];
                // contract rent
                $total_renta = array_sum($plots_contracts_renta[$contract_id]);
                // charged rent //accrued rent
                $total_renta += array_sum($plots_contracts_charged_renta[$contract_id]);

                $plot_renta = $plot_nat_renta = 0;
                // contract rent
                $plot_renta = $results[$i]['renta'];
                // charged rent //accrued rent
                $plot_charged_renta = $results[$i]['charged_renta'];
                $plot_renta += $plot_charged_renta;

                $coef = $plot_renta / $total_renta;
                $total_paid = $results[$i]['total_paid_renta_to_herritors_by_contracts'][$contract_id];
                $unpaid = $total_renta - $total_paid;
                $plot_unpaid = $coef * $unpaid;
                $results[$i]['plot_unpaid_renta'] = round($plot_unpaid, 3);
            } else {
                // calculated rent for herritor
                $results[$i]['plot_unpaid_renta'] = $results[$i]['unpaid_renta'];
            }

            if (isset($results[$i]['total_paid_renta_to_herritors_by_contracts_nat'][$contract_id])) {
                $total_nat_renta_by_type_arr = [];

                // charged nat rent //accrued nat rent
                // $plots_charged_renta_nat_sum = 0;
                foreach ($plots_contracts_charged_renta_nat[$contract_id] as $charged_renta_nat) {
                    foreach ($charged_renta_nat as $natura_type => $natura_value) {
                        $total_nat_renta_by_type_arr[$natura_type] += $natura_value;
                    }
                }
                // contract charged nat rent //contract accrued nat rent
                foreach ($plots_contracts_renta_nat[$contract_id] as $contract_charged_renta_nat) {
                    foreach ($contract_charged_renta_nat as $natura_type => $natura_value) {
                        $total_nat_renta_by_type_arr[$natura_type] += $natura_value;
                    }
                }

                $results[$i]['plot_unpaid_renta_nat'] = '';
                $results[$i]['plot_unpaid_renta_nat_unit_value'] = '';
                foreach ($total_nat_renta_by_type_arr as $natura_type => $total_nat_renta) {
                    $plot_nat_renta = 0;
                    // charged nat rent //accrued nat rent
                    if (isset($results[$i]['charged_renta_nat'][$natura_type])) {
                        $plot_nat_renta += $results[$i]['charged_renta_nat'][$natura_type];
                    }

                    // contract charged nat rent //contract accrued nat rent
                    if (isset($results[$i]['renta_nat'][$natura_type])) {
                        $plot_nat_renta += $results[$i]['renta_nat'][$natura_type];
                    }

                    $total_paid = $results[$i]['total_paid_renta_to_herritors_by_contracts_nat'][$contract_id][$natura_type];
                    $coef_nat = $plot_nat_renta / $total_nat_renta;
                    $unpaid = $total_nat_renta - $total_paid;
                    $plot_unpaid = $coef_nat * $unpaid;

                    $results[$i]['plot_unpaid_renta_nat'] .= round($plot_unpaid, 2) . ' X ' . $this->renta_types[$natura_type] . '</br>';
                    $results[$i]['plot_unpaid_renta_nat_unit_value'] .= round($plot_unpaid * $this->renta_types_values[$natura_type], 2) . ' X ' . $this->renta_types[$natura_type] . '</br>';
                }
            } else {
                // calculated rent for herritor
                $results[$i]['plot_unpaid_renta_nat'] = $results[$i]['unpaid_renta_nat'];
                $results[$i]['plot_unpaid_renta_nat_unit_value'] = $results[$i]['unpaid_renta_nat_unit_value'];
            }
            // Update parent/owner record logic end

            // Сумиране на Остатък рента в лева - Общо за страница
            $total_unpaid_renta += $results[$i]['unpaid_renta'];

            // Рента в натура по договор - grid
            $results[$i]['renta_nat'] = implode('</br>', $results[$i]['renta_nat']);

            // Начислена рента в натура - grid
            $results[$i]['charged_renta_nat'] = implode('</br>', $results[$i]['charged_renta_nat']);

            // Платена рента в натура детайлно
            $all_paid_renta_nat_by_detailed = [];
            if (!empty($paid_renta_nat_by_detailed)) {
                foreach ($paid_renta_nat_by_detailed as $key => $value) {
                    $quantity = number_format($value, 3, '.', '');
                    $unitValue = null != $paid_renta_nat_by_detailed_unit_value[$key] ? $paid_renta_nat_by_detailed_unit_value[$key] : $this->renta_types_values[$key];

                    if (null == $unitValue) {
                        $unitValue = '-';
                    }

                    $all_paid_renta_nat_by_detailed[] = $quantity . ' X ' . $this->renta_types[$key] . ' X ' . $unitValue . ' ед.ст.';
                    $total_all_paid_renta_nat_by_detailed[$key] += $value;
                }
            }
            $results[$i]['paid_renta_nat_by_detailed'] = implode('</br>', $all_paid_renta_nat_by_detailed);

            $sumPaidRenta = 0;

            // Общо платена рента в лева
            if ($paid_renta || $paid_renta_by_nat) {
                $sumPaidRenta = $paid_renta + $paid_renta_by_nat;
                $results[$i]['total_by_renta'] = number_format($sumPaidRenta, 2, '.', '') . ' лв.';
            }
            // Сумиране на Общо платена рента в лева
            $total_sum_by_paid_renta += $sumPaidRenta;

            // Общо платена рента в натура
            $totalByRentaNatura = [];
            if (!empty($paid_renta_nat) || !empty($paid_renta_nat_by_nat)) {
                $totalByRentaNatura = $this->arraySumIdenticalKeys($paid_renta_nat, $paid_renta_nat_by_nat);

                // Сумиране на Общо платена рента в натура - Общо за страница
                foreach (array_keys($total_sum_paid_renta_nat + $totalByRentaNatura) as $key) {
                    $total_sum_paid_renta_nat[$key] = @($total_sum_paid_renta_nat[$key] + $totalByRentaNatura[$key]);
                }
            }
            $results[$i]['total_by_renta_nat'] = implode('</br>', $totalByRentaNatura);

            $results[$i]['owned_area'] = number_format(($results[$i]['area'] + $results[$i]['pu_area']), 3, '.', '');
            $results[$i]['pu_area'] = number_format($results[$i]['pu_area'], 3, '.', '');
        }

        // Платена рента в натура - Общо за страница
        $total_paid_renta_nat_arr = [];
        if (!empty($total_paid_renta_nat)) {
            foreach ($total_paid_renta_nat as $key => $value) {
                $quantity = number_format($value, 3, '.', '');
                $total_paid_renta_nat_arr[] = $quantity . ' X ' . $this->renta_types[$key];
            }
        }
        $total_paid_renta_nat_text = implode('</br>', $total_paid_renta_nat_arr);

        // Платена рента в лева чрез - Общо за страница
        $total_paid_renta_by_arr = [];
        if ($total_paid_renta_by_amount > 0) {
            $total_paid_renta_by_arr[] = number_format($total_paid_renta_by_amount, 2, '.', '') . ' лв.';
        }
        if (!empty($total_paid_renta_by)) {
            foreach ($total_paid_renta_by as $key => $value) {
                $total_paid_renta_by_arr[] = number_format($value, 3, '.', '') . ' X ' . $this->renta_types[$key];
            }
        }
        $total_paid_renta_by_text = implode('</br>', $total_paid_renta_by_arr);

        // Платена рента в натура чрез - Общо за страница
        $total_paid_renta_nat_by_arr = [];
        if ($total_paid_renta_by_nat_amount > 0) {
            $total_paid_renta_nat_by_arr[] = number_format($total_paid_renta_by_nat_amount, 2, '.', '') . ' лв.';
        }
        if (!empty($total_paid_renta_nat_by)) {
            foreach ($total_paid_renta_nat_by as $key => $value) {
                $total_paid_renta_nat_by_arr[] = number_format($value, 3, '.', '') . ' X ' . $this->renta_types[$key];
            }
        }
        $total_paid_renta_nat_by_text = implode('</br>', $total_paid_renta_nat_by_arr);

        // Платена рента в натура детайлно - Общо за страница
        $total_all_paid_renta_nat_by_detailed_arr = [];
        if (!empty($total_all_paid_renta_nat_by_detailed)) {
            foreach ($total_all_paid_renta_nat_by_detailed as $key => $value) {
                $total_all_paid_renta_nat_by_detailed_arr[] = number_format($value, 3, '.', '') . ' X ' . $this->renta_types[$key];
            }
        }
        $total_all_paid_renta_nat_by_detailed_text = implode('</br>', $total_all_paid_renta_nat_by_detailed_arr);

        // Остатък рента в натура и в лева ед.ст. - Общо за страница
        $total_unpaid_renta_nat_arr = [];
        $total_unpaid_renta_nat_unit_value_arr = [];

        if (!empty($total_unpaid_renta_nat)) {
            foreach ($total_unpaid_renta_nat as $key => $value) {
                $total_unpaid_renta_nat_arr[] = number_format($value, 3, '.', '') . ' X ' . $this->renta_types[$key];

                $quantityValue = number_format($value * $this->renta_types_values[$key], 2, '.', '');
                $total_unpaid_renta_nat_unit_value_arr[] = $quantityValue . ' лв.';
            }
        }
        $total_unpaid_renta_nat_text = implode('</br>', $total_unpaid_renta_nat_arr);
        $total_unpaid_renta_nat_unit_value_text = implode('</br>', $total_unpaid_renta_nat_unit_value_arr);

        // Сумиране на Общо платена рента в натура - Общо за страница
        $total_sum_paid_renta_nat_arr = [];

        if (!empty($total_sum_paid_renta_nat)) {
            foreach ($total_sum_paid_renta_nat as $key => $value) {
                $total_sum_paid_renta_nat_arr[] = number_format($value, 3, '.', '') . ' X ' . $this->renta_types[$key];
            }
        }
        $total_sum_paid_renta_nat_text = implode('</br>', $total_sum_paid_renta_nat_arr);

        // Сумиране на Рента в натура по договор - Общо за страница
        $total_renta_nat_arr = [];
        if (!empty($total_renta_nat)) {
            foreach ($total_renta_nat as $key => $value) {
                $total_renta_nat_arr[] = number_format($value, 3, '.', '') . ' X ' . $this->renta_types[$key];
            }
        }

        $total_renta_nat_arr_text = implode('</br>', $total_renta_nat_arr);

        // Сумиране на Начислена Рента в натура по договор - Общо за страница
        $total_charged_renta_nat_arr = [];
        if (!empty($total_charged_renta_nat)) {
            foreach ($total_charged_renta_nat as $key => $value) {
                $total_charged_renta_nat_arr[] = number_format($value, 3, '.', '') . ' X ' . $this->renta_types[$key];
            }
        }

        $total_charged_renta_nat_arr_text = implode('</br>', $total_charged_renta_nat_arr);

        // Всички Ренти в натура
        $total_renta_nat_type_text = implode('</br>', $total_renta_nat_type);

        $ownerIds = $UserDbPaymentsController->getOwnersPropertyFromOwnersTree($results);
        $contractIds = $UserDbPaymentsController->getOwnersPropertyFromOwnersTree($results, 'contract_id');

        // //Recalculate results in order to be correct with personal use
        // $UserDbPaymentsController->calculatePersonalUseByPlot($results, $personalUse, 'payroll');

        $return['rows'] = $results;
        $return['total'] = $counter[0]['count'];
        $return['footer'] = [
            [
                'area_type' => '<b>ОБЩО</b>',
                'area' => number_format($total_area, 3, '.', ''),
                'pu_area' => number_format($total_personal_use_area, 3, '.', ''),
                'renta' => number_format($total_renta, 2, '.', ''),
                'charged_renta' => number_format($total_charged_renta, 2, '.', ''),
                'renta_nat' => $total_renta_nat_arr_text,
                'charged_renta_nat' => $total_charged_renta_nat_arr_text,
                'renta_nat_type' => $total_renta_nat_type_text,
                'paid_renta' => number_format($total_paid_renta, 2, '.', ''),
                'paid_renta_by' => $total_paid_renta_by_text,
                'paid_renta_nat' => $total_paid_renta_nat_text,
                'paid_renta_nat_by' => $total_paid_renta_nat_by_text,
                'paid_renta_nat_by_detailed' => $total_all_paid_renta_nat_by_detailed_text,
                'unpaid_renta' => number_format($total_unpaid_renta, 2, '.', ''),
                'unpaid_renta_nat' => $total_unpaid_renta_nat_text,
                'unpaid_renta_nat_unit_value' => $total_unpaid_renta_nat_unit_value_text,
                'total_by_renta' => number_format($total_sum_by_paid_renta, 2, '.', ''),
                'total_by_renta_nat' => $total_sum_paid_renta_nat_text,
            ],
        ];

        return $return;
    }

    /**
     * @return array
     */
    public function getPayrollByHeritor($path, $percent, $pathHeritor)
    {
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);
        $UsersController = new UsersController('Users');

        list($farming_years, $farming_years_str, $farming_years_string) = $this->getFarmings($UsersController);

        $options = [
            'return' => [
                'h.owner_id', 'h.path as path', 'poi.percent as percent',
            ],
            'where' => [
                'path' => ['column' => 'h.path', 'compare' => '~', 'value' => $path],
            ],
            'onCondition' => 'AND poi.pc_rel_id = ' . $this->relation_id,
        ];

        $counter = $UserDbOwnersController->getOwnersHeritors($options, true);

        if (0 == $counter[0]['count']) {
            return [];
        }

        $results = $UserDbOwnersController->getOwnersHeritors($options, false, false);
        $resultsCount = count($results);
        $sum_custom_ownage = 0;
        $heritors = 0;
        for ($i = 0; $i < $resultsCount; $i++) {
            if (isset($results[$i]['percent'])) {
                $sum_custom_ownage += $results[$i]['percent'];
            } else {
                $heritors++;
            }
        }

        for ($i = 0; $i < $resultsCount; $i++) {
            if (!$results[$i]['percent']) {
                $results[$i]['percent'] = ($percent - $sum_custom_ownage) / $heritors;
            }

            $paidOptions = [
                'custom_counter' => 'COUNT(DISTINCT(p.owner_id))',
                'order' => 'asc',
                'sort' => 'p.contract_id',
                'return' => [
                    'p.owner_id as owner_id',
                    'p.contract_id as contract_id',
                    'array_agg(p.id) as payment_id',
                    'array_agg(round(pn.amount::numeric, 3)) as trans_amount_nat',
                    'array_agg(pn.nat_type) as nat_type',
                    'array_agg(p.paid_in) as paid_in',
                    'array_agg(p.paid_from) as paid_from',

                    'array_agg(round(p.amount::numeric, 2)) as trans_amount',
                    'array_agg(round(p.amount_nat::numeric, 3)) as amount_nat',

                    'array_agg(rent.id) as trans_nat_type_text',
                ],
                'where' => [
                    'path' => ['column' => 'path', 'compare' => '~', 'prefix' => 'p', 'value' => $pathHeritor],
                    'is_heritor' => ['column' => 'is_heritor', 'compare' => '=', 'prefix' => 'p', 'value' => 'TRUE'],
                    'year' => ['column' => 'farming_year', 'compare' => 'IN', 'prefix' => 'p', 'value' => $farming_years],
                    'owner_id' => ['column' => 'owner_id', 'compare' => '=', 'prefix' => 'p', 'value' => $results[$i]['owner_id']],
                ],
                'group' => 'p.owner_id, p.contract_id',
                'year_id' => $farming_years_string,
            ];

            $paidResults = $UserDbPaymentsController->getPaidData($paidOptions, false, false);

            $renta_results = [];
            $payment_ids = [];
            $renta_nat_results = [];

            foreach ($paidResults as $paidRes) {
                $payment_ids = [];
                $payment_ids_to_be_checked = explode(',', trim($paidRes['payment_id'], '{}'));
                $paid_renta_array = explode(',', trim($paidRes['trans_amount'], '{}'));
                $paid_rent_count = count($paid_renta_array);
                $paid_from_array = explode(',', trim($paidRes['paid_from'], '{}'));

                $renta_results[$paidRes['contract_id']]['paid_renta'] = 0;
                for ($j = 0; $j < $paid_rent_count; $j++) {
                    if (1 == $paid_from_array[$j]) {
                        if (!in_array($payment_ids_to_be_checked[$j], $payment_ids)) {
                            $renta_results[$paidRes['contract_id']]['paid_renta'] += $paid_renta_array[$j];
                            $payment_ids[] = $payment_ids_to_be_checked[$j];
                        }
                    }
                }

                $paid_renta_nat_array = explode(',', trim($paidRes['trans_amount_nat'], '{}'));
                $paidRentNatTCount = count($paid_renta_nat_array);
                $paid_renta_nat_types = explode(',', trim($paidRes['trans_nat_type_text'], '{}'));
                $natural_renta = [];
                for ($j = 0; $j < $paidRentNatTCount; $j++) {
                    if (2 == $paid_from_array[$j]) {
                        if (isset($paid_renta_nat_types[$j])) {
                            if (isset($natural_renta[$paid_renta_nat_types[$j]])) {
                                $natural_renta[$paid_renta_nat_types[$j]] += $paid_renta_nat_array[$j];
                            } else {
                                $natural_renta[$paid_renta_nat_types[$j]] = $paid_renta_nat_array[$j];
                            }
                        }
                    }
                }
                $natural_keys = array_keys($natural_renta);
                $renta_results[$paidRes['contract_id']]['paid_renta_nat'] = '';

                foreach ($natural_keys as $nat_rent_key) {
                    if ('NULL' != $nat_rent_key && '' != $nat_rent_key) {
                        $renta_results[$paidRes['contract_id']]['paid_renta_nat'] .= number_format($natural_renta[$nat_rent_key], 3, '.', '') . ' X ' . $this->renta_types[$nat_rent_key] . ',<br/>';
                        $renta_nat_results[$paidRes['contract_id']]['paid_renta_nat'][$nat_rent_key] += $natural_renta[$nat_rent_key];
                    }
                }

                if (!empty($renta_results[$paidRes['contract_id']]['paid_renta_nat'])) {
                    $renta_results[$paidRes['contract_id']]['paid_renta_nat'] = substr($renta_results[$paidRes['contract_id']]['paid_renta_nat'], 0, -6);
                }
            }
            if ($results[$i]['owner_id'] == $_POST['owner_id'] && $results[$i]['path'] == $pathHeritor) {
                $this->percent[$this->relation_id] = $results[$i]['percent'];
                $this->all_renta[$this->relation_id] = $renta_results;
                $this->all_renta_detailed[$this->relation_id] = $renta_nat_results;
            } else {
                $this->getPayrollByHeritor($results[$i]['path'] . '.*{1}', $results[$i]['percent'], $pathHeritor);
            }
        }
    }

    /** Get footer totals by Person.
     * @param ?int $limit
     * @param ?int $offset
     *
     * @return array
     */
    public function getFooterOwnersPayroll(?int $limit = null, ?int $offset = null)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);

        // create the default return
        $return = [
            'rows' => [],
            'total' => 0,
            'footer' => [],
        ];

        list($farming_years, $farming_years_str, $farming_years_string) = $this->getFarmings($UsersController);

        $filteredIds = [];

        if (!empty($_POST['owner_egns']) || !empty($_POST['company_eiks'])) {
            $filteredIds = $this->getOwnersIds($UserDbController, $_POST['owner_egns'], $_POST['company_eiks']);
        }

        $filteredIds = [];

        if (!empty($_POST['owner_egns']) || !empty($_POST['company_eiks'])) {
            $filteredIds = $this->getOwnersIds($UserDbController, $_POST['owner_egns'], $_POST['company_eiks']);
        }

        $ownerTypeCompareType = 'IN';
        $ownerTypes = [0, 1];

        if (array_key_exists('owner_type', $_POST)) {
            $ownerTypeCompareType = '=';
            $ownerTypes = (int)$_POST['owner_type'];

            if (false !== strpos($_POST['owner_type'], ',')) {
                $ownerTypeCompareType = 'IN';
                $ownerTypes = explode(',', $_POST['owner_type']);
            }
        }

        $options = [
            'custom_counter' => 'COUNT(DISTINCT(o.id))',
            'offset' => $offset,
            'limit' => $limit,
            'sort' => 'owner_names',
            'order' => 'asc',
            'return' => [
                // get owners data
                'DISTINCT(o.id) as owner_id', 'o.is_dead', 'o.owner_type',
                "(CASE WHEN owner_type = 1 THEN name || ' ' || surname || ' ' || lastname ELSE company_name END) as owner_names",

                'SUM((
                        CASE
                        WHEN A .renta IS NULL THEN
                            (
                                CASE
                                WHEN pc.rent_per_plot IS NOT NULL THEN
                                    pc.rent_per_plot
                                ELSE
                                    C .renta
                                END
                            )
                        ELSE
                            (
                                CASE
                                WHEN pc.rent_per_plot IS NOT NULL THEN
                                    pc.rent_per_plot
                                ELSE
                                    A .renta
                                END
                            )
                        END
                ) * (((pc.area_for_rent * po.percent / 100)::numeric) - COALESCE(pu.area, 0))) as contract_renta',

                'SUM(
                    (
                        CASE
                        WHEN pc.rent_per_plot IS NOT NULL THEN
                            pc.rent_per_plot
                        ELSE
                            cr.renta
                        END
                    ) * (pc.area_for_rent * po.percent / 100 - COALESCE(pu.area, 0))) as charged_renta',

                'array_agg(c.id) as contract_array',
                'array_agg(case when a.id is not null then a.id else c.id end) as contract_anex_arr',
                'array_agg(kvs.gid) as plots_array',
                "array_agg(c.id || '|' || kvs.gid || '|' || (pc.area_for_rent * po.percent / 100) - COALESCE(pu.area, 0)) as pcrel_gid",
                '(SELECT array_agg(cr.id)
                   FROM su_contracts_rents cr
                   WHERE cr.contract_id = case when max(a.id) is not null then max(a.id) else max(c.id) end) 
                AS renta_nat_type_id',
                'sum((((pc.area_for_rent * po.percent) / (100)::double precision) - COALESCE(pu.area, (0)::real))) AS area_total',
            ],
            'where' => [
                'contract_type' => ['column' => 'nm_usage_rights', 'compare' => 'NOT IN', 'prefix' => 'c', 'value' => [1, 4]],
                'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
                'is_annnex' => ['column' => 'is_annex', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
                'annex_action' => ['column' => 'annex_action', 'compare' => '=', 'prefix' => 'pc', 'value' => 'added'],
                'is_heritor' => ['column' => 'is_heritor', 'compare' => '=', 'prefix' => 'po', 'value' => 'FALSE'],
                'active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'TRUE'],
                'percent' => ['column' => 'percent', 'compare' => '>', 'prefix' => 'po', 'value' => '0'],
                // filter params
                'owner_type' => ['column' => 'owner_type', 'compare' => $ownerTypeCompareType, 'prefix' => 'o', 'value' => $ownerTypes],
                'ekate' => ['column' => 'ekate', 'compare' => $this->getPayrollEkateCompareType($_POST), 'prefix' => 'kvs', 'value' => $this->filterPayrollEkateValue($_POST['payroll_ekate'])],
                'farming' => ['column' => 'farming_id', 'compare' => 'IN', 'prefix' => 'c', 'value' => $this->arrayHelper->filterEmptyStringArr($_POST['payroll_farming'])],
                'owner_names' => ['column' => "name || ' ' || surname || ' ' || lastname", 'compare' => 'ILIKE', 'prefix' => 'o', 'value' => $_POST['owner_names']],
                'egn' => ['column' => 'egn', 'compare' => 'ILIKE', 'prefix' => 'o', 'value' => $_POST['egn']],
                'eik' => ['column' => 'eik', 'compare' => 'ILIKE', 'prefix' => 'o', 'value' => $_POST['eik']],
                'company_name' => ['column' => 'company_name', 'compare' => 'ILIKE', 'prefix' => 'o', 'value' => $_POST['company_name']],
                'rep_names' => ['column' => "rep_name || ' ' || rep_surname || ' ' || rep_lastname", 'compare' => 'ILIKE', 'prefix' => 'r', 'value' => $_POST['rep_names']],
                'rep_egn' => ['column' => 'rep_egn', 'compare' => 'ILIKE', 'prefix' => 'r', 'value' => $_POST['rep_egn']],
            ],
            'rent_place' => $_POST['rent_place'],
            'rep_rent_place' => $_POST['rep_rent_place'],
            'group' => 'o.id',
            'start_date' => $_POST['payroll_to_date'],
            'due_date' => $_POST['payroll_from_date'],
            'year_id' => $farming_years,
        ];

        if (count($filteredIds)) {
            $options['where']['owner_ids'] = ['column' => 'id', 'compare' => 'IN', 'prefix' => 'o', 'value' => $filteredIds];
            unset($options['where']['egn'], $options['where']['eik']);
        }
        $ancestors = [];
        if ((isset($_POST['heritor_egn']) && '' != $_POST['heritor_egn']) || (isset($_POST['heritor_names']) && '' != $_POST['heritor_names'])) {
            $ancestors = $this->_getHeritorAncestors();
            if (empty($ancestors) || empty($ancestors[0])) {
                return $this->return;
            }

            $options['where']['ancestors'] = ['column' => 'id', 'compare' => 'IN', 'prefix' => 'o', 'value' => $this->arrayHelper->filterEmptyStringArr($ancestors)];
        }

        $results = $UserDbPaymentsController->getPayrollData($options, false, false);

        // HERE
        $ownerIds = $UserDbPaymentsController->getOwnersPropertyFromOwnersTree($results);
        $personalUseOptions = [
            'chosen_years' => $_POST['farming_year'],
            'owner_ids' => implode(',', $ownerIds),
        ];
        $personalUse = $UserDbPaymentsController->getPersonalUseForOwners($personalUseOptions, false, false);
        // //Recalculate payment grid data with personal use
        $UserDbPaymentsController->calculatePersonalUse($results, $personalUse, 'payroll');

        $result_count = count($results);
        if (0 == $result_count) {
            return $this->return;
        }

        $ownerIdArray = [];
        $plotsArray = '';
        $finalContractArray = [];
        $finalAnnexArray = [];
        $total_paid_renta_nat = [];
        $total_renta_nat = [];
        $total_paid_renta_by = [];
        $total_paid_renta_by_nat = [];
        $total_all_paid_renta_nat_by_detailed = [];
        $total_unpaid_renta_nat = [];
        $total_sum_paid_renta_nat = [];
        $total_paid_renta_by_amount = 0;
        $total_paid_renta_by_nat_amount = 0;
        $total_sum_by_paid_renta = 0;
        $paidRentaByOwnerContract = [];
        $paidRentaByOwnerContractPlot = [];
        $paidRentaNatByOwnerContract = [];
        $paidRentaNatByOwnerContractPlot = [];
        $total_area_owner_new = 0;
        $total_renta = 0;
        $total_paid_renta = 0;
        $paid_rent_arr = [];
        $unpaid_rent_arr = [];
        $total_rent_arr = [];
        $unpaid_rent_id_arr = [];
        for ($i = 0; $i < $result_count; $i++) {
            $pca_elements_gid = explode(',', trim($results[$i]['pcrel_gid'], '{}'));

            foreach ($pca_elements_gid as $element) {
                $launch = explode('|', $element);

                $results[$i]['plots_contracts_area_array_gids'][] = [
                    'contract_id' => $launch[0],
                    'plot_gid' => $launch[1],
                    'area' => $launch[2],
                ];
            }

            $contractPlotArea = [];
            $contractPlotsAreaSum = [];
            $total_area_owner_new += (float)$results[$i]['area_total'];
            $results[$i]['area_total'] = (float)number_format((float)$results[$i]['area_total'], 3, '.', '');
            if (!empty($results[$i]['plots_contracts_area_array_gids'])) {
                foreach ($results[$i]['plots_contracts_area_array_gids'] as $key => $value) {
                    $contractId = $value['contract_id'];
                    $plotGid = $value['plot_gid'];
                    $areaPlot = $value['area'];

                    $contractPlotArea[$contractId][$plotGid] = $areaPlot;
                    $contractPlotsAreaSum[$contractId] += $areaPlot;
                }
            }

            $contract_array = explode(',', trim($results[$i]['contract_array'], '{}'));
            $contract_anex_arr = explode(',', trim($results[$i]['contract_anex_arr'], '{}'));

            $finalContractArray[] = $contract_array;
            $finalAnnexArray[] = $contract_anex_arr;

            $owners_ids = [];
            $paths_ids = [];

            $paidOptions = [
                'order' => 'asc',
                'sort' => 'p.contract_id',
                'return' => [
                    'p.id as payment_id',
                    'p.owner_id as owner_id',
                    'p.contract_id as contract_id',
                    'case when pn.amount notnull and pn.unit_value notnull  then round((pn.amount * pn.unit_value)::numeric, 2) else round(p.amount::numeric, 2) end as trans_amount',
                    'case when pn.amount notnull then round(pn.amount::numeric,3) else round(p.amount_nat::numeric, 3) end as amount_nat',
                    'round(pn.amount::numeric, 3) as trans_amount_nat',
                    'p.paid_in',
                    'p.paid_from',
                    'pn.nat_type as nat_type',
                    'rent.name as trans_nat_type_text',
                    'p.path',
                ],
                'where' => [
                    'owner_id' => ['column' => 'owner_id', 'compare' => '=', 'prefix' => 'p', 'value' => $results[$i]['owner_id']],
                    'path' => ['column' => 'path', 'compare' => 'IS', 'prefix' => 'p', 'value' => 'NULL'],
                    'contract_id' => ['column' => 'contract_id', 'compare' => 'IN', 'prefix' => 'p', 'value' => $contract_array],
                    'farming_year' => ['column' => 'farming_year', 'compare' => 'IN', 'prefix' => 'p', 'value' => $farming_years],
                ],
                'group_by' => 'p.contract_id',
            ];
            $paidResults = $UserDbPaymentsController->getPaidData($paidOptions, false, false);

            // Масив за всички собственици
            $ownerIdArray[] = $results[$i]['owner_id'];
            if ($results[$i]['is_dead']) {
                $this->getOwnersHeritorsData($results[$i], $UserDbOwnersController, $UserDbPaymentsController, $paidResults, $contract_array, $farming_years);
            }

            $results[$i] = $this->addNewRentaPayrolls($paidResults, $results[$i], true);

            if (count($results[$i]['paid_renta_nat_details']) > 0) {
                foreach ($results[$i]['paid_renta_nat_details'] as $key => $value) {
                    if ('' != $key && 0 != $value) {
                        $total_paid_renta_nat[$key] += $value;
                    }
                }
            }

            $plotsStr = trim($results[$i]['plots_array'], '{ }');
            $plotsArray .= ',' . $plotsStr;
            $total_paid_renta += $results[$i]['paid_renta'];

            $paid_renta = 0;
            $paid_renta_by_nat = 0;
            $paid_renta_nat = [];
            $paid_renta_nat_by_nat = [];
            $paid_renta_nat_by_detailed = [];
            $paid_renta_nat_by_detailed_unit_value = [];
            $paymentIds = [];
            $owner_id = $results[$i]['owner_id'];

            $paidResultsCount = count($paidResults);
            for ($m = 0; $m < $paidResultsCount; $m++) {
                $paidResult = $paidResults[$m];
                $renta_type = $paidResult['nat_type'];
                $contract_id = $paidResult['contract_id'];

                if (1 == $paidResult['paid_from']) {
                    // Изчисляване на платената рента по договор
                    $paidRentaByOwnerContract[$owner_id][$contract_id] += $paidResult['trans_amount'];

                    if (2 == $paidResult['paid_in']) {
                        $paid_renta_nat[$renta_type] += $paidResult['amount_nat'];

                        continue;
                    }

                    $paid_renta += $paidResult['trans_amount'];
                } elseif (2 == $paidResult['paid_from']) {
                    // Изчисляване на платената рента в натура по договор
                    $paidRentaNatByOwnerContract[$owner_id][$contract_id][$renta_type] += $paidResult['trans_amount_nat'];

                    if (2 == $paidResult['paid_in']) {
                        $paid_renta_nat_by_nat[$renta_type] += $paidResult['trans_amount_nat'];

                        continue;
                    }

                    if (array_key_exists($renta_type, $paid_renta_nat_by_detailed)) {
                        $paid_renta_nat_by_detailed[$renta_type] += $paidResult['trans_amount_nat'];
                    } else {
                        $paid_renta_nat_by_detailed[$renta_type] = $paidResult['trans_amount_nat'];
                        $paid_renta_nat_by_detailed_unit_value[$renta_type] = $paidResult['unit_value'];
                    }

                    $paymentIds[] = $paidResult['payment_id'];
                    $paid_renta_by_nat += $paidResult['trans_amount'];
                }
            }

            if (!empty($contractPlotArea)) {
                foreach ($contractPlotArea as $contractId => $plot) {
                    foreach ($plot as $plotGid => $area) {
                        // РЕНТА В ЛЕВА ПО ПЛОТ
                        $totalArea = $contractPlotsAreaSum[$contractId];

                        if ($totalArea <= 0) {
                            break;
                        }

                        $areaPercent = $area / $totalArea;
                        $paidRentaByContractSum = $paidRentaByOwnerContract[$results[$i]['owner_id']][$contractId];
                        $paidRentaByPlots = $paidRentaByContractSum * $areaPercent;

                        $paidRentaByOwnerContractPlot[$results[$i]['owner_id']][$contractId][$plotGid] = number_format($paidRentaByPlots, 2, '.', '');

                        // РЕНТА В НАТУРА ПО ПЛОТ
                        $paidRentaNatByContractSum = $paidRentaNatByOwnerContract[$results[$i]['owner_id']][$contractId];

                        if (!empty($paidRentaNatByContractSum)) {
                            foreach ($paidRentaNatByContractSum as $rentaType => $paidRentaNat) {
                                $paidRentaNatByPlots = $paidRentaNat * $areaPercent;

                                $paidRentaNatByOwnerContractPlot[$results[$i]['owner_id']][$contractId][$plotGid][$rentaType] = number_format($paidRentaNatByPlots, 3, '.', '');
                            }
                        }
                    }
                }
            }

            $paid_renta_by = [];
            if (is_numeric($paid_renta)) {
                $amount = number_format($paid_renta, 2, '.', '');

                $total_paid_renta_by_amount += $amount;
            }
            if (!empty($paid_renta_nat)) {
                foreach ($paid_renta_nat as $key => $value) {
                    $quantity = number_format($value, 3, '.', '');

                    $total_paid_renta_by[$key] += $value;
                }
            }

            // Платена рента в натура чрез и Общо
            $paid_renta_nat_by = [];
            if (is_numeric($paid_renta_by_nat)) {
                $amount = number_format($paid_renta_by_nat, 2, '.', '');

                $total_paid_renta_by_nat_amount += $amount;
            }
            if (!empty($paid_renta_nat_by_nat)) {
                foreach ($paid_renta_nat_by_nat as $key => $value) {
                    $quantity = number_format($value, 3, '.', '');

                    $total_paid_renta_by_nat[$key] += $value;
                }
            }

            // Платена рента в натура детайлно
            $all_paid_renta_nat_by_detailed = [];
            if (!empty($paid_renta_nat_by_detailed)) {
                foreach ($paid_renta_nat_by_detailed as $key => $value) {
                    $quantity = number_format($value, 3, '.', '');
                    $unitValue = null != $paid_renta_nat_by_detailed_unit_value[$key] ? $paid_renta_nat_by_detailed_unit_value[$key] : $this->renta_types_values[$key];

                    if (null == $unitValue) {
                        $unitValue = '-';
                    }

                    $total_all_paid_renta_nat_by_detailed[$key] += $value;
                }
            }

            // Общо платена рента в лева
            $sumPaidRenta = 0;
            if ($paid_renta || $paid_renta_by_nat) {
                $sumPaidRenta = $paid_renta + $paid_renta_by_nat;
            }
            // Сумиране на Общо платена рента в лева - Общо
            $total_sum_by_paid_renta += $sumPaidRenta;

            // Общо платена рента в натура
            $totalByRentaNatura = [];
            if (!empty($paid_renta_nat) || !empty($paid_renta_nat_by_nat)) {
                $totalByRentaNatura = $this->arraySumIdenticalKeys($paid_renta_nat, $paid_renta_nat_by_nat);

                // Сумиране на Общо платена рента в натура - Общо
                foreach (array_keys($total_sum_paid_renta_nat + $totalByRentaNatura) as $key) {
                    $total_sum_paid_renta_nat[$key] = @($total_sum_paid_renta_nat[$key] + $totalByRentaNatura[$key]);
                }
            }
        }

        // Сумиране на Общо платена рента в натура - Общо
        $total_sum_paid_renta_nat_arr = [];
        if (!empty($total_sum_paid_renta_nat)) {
            // Визуализиране на Общо платена рента в натура
            foreach ($total_sum_paid_renta_nat as $key => $value) {
                $total_sum_paid_renta_nat_arr[] = number_format($value, 3, '.', '') . ' X ' . $this->renta_types[$key];
            }
        }
        ksort($total_sum_paid_renta_nat_arr);
        $total_sum_paid_renta_nat_text = implode('</br>', $total_sum_paid_renta_nat_arr);

        // Платена рента в лева чрез - Общо
        $total_paid_renta_by_arr = [];
        if (is_numeric($total_paid_renta_by_amount)) {
            $total_paid_renta_by_arr[] = number_format($total_paid_renta_by_amount, 2, '.', '') . ' лв.';
        }
        if (!empty($total_paid_renta_by)) {
            foreach ($total_paid_renta_by as $key => $value) {
                $total_paid_renta_by_arr[] = number_format($value, 3, '.', '') . ' X ' . $this->renta_types[$key];
            }
        }
        $total_paid_renta_by_text = implode('</br>', $total_paid_renta_by_arr);

        // Платена рента в натура чрез - Общо
        $total_paid_renta_by_nat_arr = [];
        if (is_numeric($total_paid_renta_by_nat_amount)) {
            $total_paid_renta_by_nat_arr[] = number_format($total_paid_renta_by_nat_amount, 2, '.', '') . ' лв.';
        }
        if (!empty($total_paid_renta_by_nat)) {
            foreach ($total_paid_renta_by_nat as $key => $value) {
                $total_paid_renta_by_nat_arr[] = number_format($value, 3, '.', '') . ' X ' . $this->renta_types[$key];
                $paid_rent_arr[$this->renta_types[$key]] = $value;
            }
        }
        $total_paid_renta_by_nat_text = implode('</br>', $total_paid_renta_by_nat_arr);

        // Платена рента в натура детайлно - Общо
        $total_all_paid_renta_nat_by_detailed_arr = [];
        if (!empty($total_all_paid_renta_nat_by_detailed)) {
            foreach ($total_all_paid_renta_nat_by_detailed as $key => $value) {
                $quantity = number_format($value, 3, '.', '');
                $unitValue = null != $paid_renta_nat_by_detailed_unit_value[$key] ? $paid_renta_nat_by_detailed_unit_value[$key] : $this->renta_types_values[$key];

                if (null == $unitValue) {
                    $unitValue = '-';
                }

                $total_all_paid_renta_nat_by_detailed_arr[] = $quantity . ' X ' . $this->renta_types[$key] . ' X ' . $unitValue . ' ед.ст.';
            }
        }
        $total_all_paid_renta_nat_by_detailed_text = implode('</br>', $total_all_paid_renta_nat_by_detailed_arr);

        // Платена рента в натура - Общо
        $total_paid_renta_nat_text = '';
        foreach ($total_paid_renta_nat as $key => $value) {
            if ('' != $key && 0 != $value) {
                $total_paid_renta_nat_text .= number_format($value, 3, '.', '') . ' X ' . $this->renta_types[$key] . '<br/>';
            }
        }
        sort($ownerIdArray);

        $ownerIdArray = implode(', ', $ownerIdArray);

        $finalPlots = implode(', ', array_unique(array_filter(explode(',', $plotsArray))));

        $contract_string = '';
        $finaContCount = count($finalContractArray);
        for ($i = 0; $i < $finaContCount; $i++) {
            $finaContCount2 = count($finalContractArray[$i]);
            for ($j = 0; $j < $finaContCount2; $j++) {
                $contract_string .= $finalContractArray[$i][$j];
                $contract_string .= '~~';
            }
        }
        $finalContractArray = array_filter(explode('~~', $contract_string));
        sort($finalContractArray);
        $finalContractArray = array_unique($finalContractArray);
        $contract_string = implode(', ', $finalContractArray);
        $finaAnnexCount = count($finalAnnexArray);
        $annex_string = '';
        for ($i = 0; $i < $finaAnnexCount; $i++) {
            $finaAnnexCount2 = count($finalAnnexArray[$i]);
            for ($j = 0; $j < $finaAnnexCount2; $j++) {
                $annex_string .= $finalAnnexArray[$i][$j];
                $annex_string .= ', ';
            }
        }
        $finalAnnexArray = array_filter(explode(', ', $annex_string));
        sort($finalAnnexArray);
        $finalAnnexArray = array_unique($finalAnnexArray);
        $annex_string = implode(', ', $finalAnnexArray);
        $annexesRentas = $this->getAnnexes($UserDbController, $ownerIdArray, $contract_string, $finalPlots, $farming_years_str, $annex_string);
        $annexesRentCount = count($annexesRentas);
        $owners_annexes_array = [];
        $rentaByAnnexesAndContracts = [];
        $rentaNatByAnnexesAndContracts = [];
        $total_area = 0;
        for ($k = 0; $k < $annexesRentCount; $k++) {
            $annexRenta = $annexesRentas[$k];

            $ownerAnexes = &$owners_annexes_array[$annexRenta['owner_id']];
            if (is_null($ownerAnexes)) {
                $ownerAnexes = [];
            }

            if (!array_key_exists($annexRenta['owner_id'], $owners_annexes_array)
                    || !in_array([$annexRenta['c_id'] => $annexRenta['plot_id']], $ownerAnexes)) {
                $owners_annexes_array[$annexRenta['owner_id']][] = [$annexRenta['c_id'] => $annexRenta['plot_id']];

                if (!is_null($annexRenta['contract_area']) || !is_null($annexRenta['renta'])) {
                    $total_area += $annexRenta['contract_area'];
                    $rentaByAnnexesAndContracts[$annexRenta['owner_id']][] = [$annexRenta['c_id'] => [$annexRenta['plot_id'] => $annexRenta['renta']]];
                }
            }

            if (!is_null($annexRenta['renta_nat'])) {
                $rentaNatByAnnexesAndContracts[$annexRenta['owner_id']][$annexRenta['c_id']][$annexRenta['plot_id']][$annexRenta['nat_type']] = $annexRenta['renta_nat'];
            }
        }

        $contractsRentas = $this->getContracts($UserDbController, $ownerIdArray, $contract_string, $finalPlots, $farming_years_str);
        $contractsRentCount = count($contractsRentas);
        $owners_contracts_array = [];

        for ($k = 0; $k < $contractsRentCount; $k++) {
            $contractRenta = $contractsRentas[$k];

            $ownerContracts = &$owners_annexes_array[$contractRenta['owner_id']];
            if (is_null($ownerContracts)) {
                $ownerContracts = [];
            }

            if (array_key_exists($contractRenta['owner_id'], $owners_annexes_array)
                    && in_array([$contractRenta['c_id'] => $contractRenta['plot_id']], $ownerContracts)) {
                continue;
            }

            $ownersContracts = &$owners_contracts_array[$contractRenta['owner_id']];
            if (is_null($ownersContracts)) {
                $ownersContracts = [];
            }

            if (!array_key_exists($contractRenta['owner_id'], $owners_contracts_array)
                    || !in_array([$contractRenta['c_id'] => $contractRenta['plot_id']], $ownersContracts)) {
                $owners_contracts_array[$contractRenta['owner_id']][] = [$contractRenta['c_id'] => $contractRenta['plot_id']];

                // sum total area and renta
                $total_area += $contractRenta['contract_area'];

                $rentaByAnnexesAndContracts[$contractRenta['owner_id']][] = [$contractRenta['c_id'] => [$contractRenta['plot_id'] => number_format($contractRenta['renta'], 2, '.', '')]];
            }

            if (!is_null($contractRenta['renta_nat']) && is_null($rentaNatByAnnexesAndContracts[$contractRenta['owner_id']][$contractRenta['c_id']][$contractRenta['plot_id']][$contractRenta['nat_type']])) {
                $rentaNatByAnnexesAndContracts[$contractRenta['owner_id']][$contractRenta['c_id']][$contractRenta['plot_id']][$contractRenta['nat_type']] = $contractRenta['renta_nat'];
            }
        }

        $chargedRentsAnnexes = $this->getChargedRentAnnexes($UserDbController, $ownerIdArray, $contract_string, $finalPlots, $farming_years_str, $annex_string);
        $chargedRentsAnnexesCount = count($chargedRentsAnnexes);
        $owners_annexes_charged_array = [];
        $chargedRentaByAnnexesAndContracts = [];
        $total_charged_renta_nat = [];
        $chargedRentaNatByAnnexesAndContracts = [];
        for ($k = 0; $k < $chargedRentsAnnexesCount; $k++) {
            $annexChargedRenta = $chargedRentsAnnexes[$k];

            $ownerAnexesCharged = &$owners_annexes_charged_array[$annexChargedRenta['owner_id']];
            if (is_null($ownerAnexesCharged)) {
                $ownerAnexesCharged = [];
            }

            if (!array_key_exists($annexChargedRenta['owner_id'], $owners_annexes_charged_array)
                    || !in_array([$annexChargedRenta['c_id'] => $annexChargedRenta['plot_id']], $ownerAnexesCharged)) {
                $owners_annexes_charged_array[$annexChargedRenta['owner_id']][] = [$annexChargedRenta['c_id'] => $annexChargedRenta['plot_id']];

                // sum charged renta
                if (!is_null($annexChargedRenta['charged_renta_sum'])) {
                    $total_charged_renta += $annexChargedRenta['charged_renta_sum'];

                    $chargedRentaByAnnexesAndContracts[$annexChargedRenta['owner_id']][] = [$annexChargedRenta['c_id'] => [$annexChargedRenta['plot_id'] => $annexChargedRenta['charged_renta_sum']]];
                }
            }

            // sum charged renta natura
            if (!is_null($annexChargedRenta['charged_renta_nat_sum'])) {
                $total_charged_renta_nat[$annexChargedRenta['nat_type']] += $annexChargedRenta['charged_renta_nat_sum'];

                $chargedRentaNatByAnnexesAndContracts[$annexChargedRenta['owner_id']][$annexChargedRenta['c_id']][$annexChargedRenta['plot_id']][$annexChargedRenta['nat_type']]
                        = $annexChargedRenta['charged_renta_nat_sum'];
            }

            // sum converted charged renta
            if (!is_null($annexChargedRenta['converted_charged_renta_sum'])) {
                $total_charged_renta += $annexChargedRenta['converted_charged_renta_sum'];

                $chargedRentaByAnnexesAndContracts[$annexChargedRenta['owner_id']][] = [$annexChargedRenta['c_id'] => [$annexChargedRenta['plot_id'] => $annexChargedRenta['converted_charged_renta_sum']]];
            }
        }

        $chargedRents = $this->getChargedRents($UserDbController, $ownerIdArray, $contract_string, $finalPlots, $farming_years_str);
        $chargedRentsCount = count($chargedRents);
        $owners_contracts_charged_array = [];

        for ($k = 0; $k < $chargedRentsCount; $k++) {
            $contractChargedRenta = $chargedRents[$k];

            $ownerContractsCharged = &$owners_annexes_charged_array[$contractChargedRenta['owner_id']];
            if (null === $ownerContractsCharged) {
                $ownerContractsCharged = [];
            }

            if (array_key_exists($contractChargedRenta['owner_id'], $owners_annexes_charged_array)
                    && in_array([$contractChargedRenta['c_id'] => $contractChargedRenta['plot_id']], $ownerContractsCharged)) {
                continue;
            }

            $ownersContracts = &$owners_contracts_charged_array[$contractChargedRenta['owner_id']];
            if (null === $ownersContracts) {
                $ownersContracts = [];
            }

            if (!array_key_exists($contractChargedRenta['owner_id'], $owners_contracts_charged_array)
                    || !in_array([$contractChargedRenta['c_id'] => $contractChargedRenta['plot_id']], $ownersContracts)) {
                $owners_contracts_charged_array[$contractChargedRenta['owner_id']][] = [$contractChargedRenta['c_id'] => $contractChargedRenta['plot_id']];

                // sum charged renta
                if (null !== $contractChargedRenta['charged_renta_sum']) {
                    $total_charged_renta += $contractChargedRenta['charged_renta_sum'];

                    $chargedRentaByAnnexesAndContracts[$contractChargedRenta['owner_id']][] = [$contractChargedRenta['c_id'] => [$contractChargedRenta['plot_id'] => number_format($contractChargedRenta['charged_renta_sum'], 2, '.', '')]];
                }
            }

            // sum charged renta natura
            if (null !== $contractChargedRenta['charged_renta_nat_sum']) {
                $total_charged_renta_nat[$contractChargedRenta['nat_type']] += $contractChargedRenta['charged_renta_nat_sum'];

                $chargedRentaNatByAnnexesAndContracts[$contractChargedRenta['owner_id']][$contractChargedRenta['c_id']][$contractChargedRenta['plot_id']][$contractChargedRenta['nat_type']]
                                                                                                                                                                = $contractChargedRenta['charged_renta_nat_sum'];
            }
            if (!is_null($contractChargedRenta['converted_charged_renta_sum'])) {
                $total_charged_renta += $contractChargedRenta['converted_charged_renta_sum'];

                $chargedRentaByAnnexesAndContracts[$contractChargedRenta['owner_id']][] = [$contractChargedRenta['c_id'] => [$contractChargedRenta['plot_id'] => number_format($contractChargedRenta['converted_charged_renta_sum'], 2, '.', '')]];
            }
        }

        $allChargedRentaNatByAnnexesAndContractsAndOwner = [];
        foreach ($rentaNatByAnnexesAndContracts as $keyOwner => $rentaNatByOwner) {
            foreach ($rentaNatByOwner as $keyContracts => $rentaNatByContracts) {
                foreach ($rentaNatByContracts as $keyPlot => $rentaNatByPlot) {
                    foreach ($rentaNatByPlot as $rentaType => $rentaNat) {
                        $chargedRentaNat = $chargedRentaNatByAnnexesAndContracts[$keyOwner][$keyContracts][$keyPlot][$rentaType];
                        $paidRentaNat = $paidRentaNatByOwnerContractPlot[$keyOwner][$keyContracts][$keyPlot][$rentaType];

                        if (is_null($chargedRentaNat)) {
                            $total_renta_nat[$rentaType] += $rentaNat;

                            $allChargedRentaNatByAnnexesAndContractsAndOwner[$keyOwner][$rentaType] += $rentaNat - $paidRentaNat;
                        } else {
                            $allChargedRentaNatByAnnexesAndContractsAndOwner[$keyOwner][$rentaType] += $chargedRentaNat - $paidRentaNat;
                        }
                    }
                }
            }
        }

        $allChargedRentaNatByAnnexesAndContracts = [];
        $overPaidAllChargedRentaNatByAnnexesAndContracts = [];
        foreach ($allChargedRentaNatByAnnexesAndContractsAndOwner as $ownerID => $value) {
            foreach ($value as $rentaType => $rentaNatura) {
                if ($rentaNatura < 0) {
                    $overPaidAllChargedRentaNatByAnnexesAndContracts[$rentaType] += $rentaNatura * (-1);
                    $rentaNatura = 0;
                }
                $allChargedRentaNatByAnnexesAndContracts[$rentaType] += $rentaNatura;
            }
        }
        ksort($allChargedRentaNatByAnnexesAndContracts);

        $total_unpaid_renta_nat = [];
        $total_overpaid_renta_nat = [];
        foreach ($allChargedRentaNatByAnnexesAndContracts as $rentaType => $rentaNatura) {
            $total_unpaid_renta_nat[] = number_format($rentaNatura, 3, '.', '') . ' X ' . $this->renta_types[$rentaType];

            $overPaidRentaNatura = $overPaidAllChargedRentaNatByAnnexesAndContracts[$rentaType];
            if ($overPaidRentaNatura) {
                $total_overpaid_renta_nat[] = number_format($overPaidRentaNatura, 3, '.', '') . ' X ' . $this->renta_types[$rentaType];
            }
            $unpaid_rent_arr[$this->renta_types[$rentaType]] = $rentaNatura;
            $unpaid_rent_id_arr['unpaid_renta_nat_arr_' . $rentaType] = number_format($rentaNatura, 3, '.', '');
        }
        $total_unpaid_renta_nat_text = implode('</br>', $total_unpaid_renta_nat);
        $total_overpaid_renta_nat_text = implode('</br>', $total_overpaid_renta_nat);

        $total_unpaid_renta_nat_unit_value_arr = [];
        if (!empty($allChargedRentaNatByAnnexesAndContracts)) {
            foreach ($allChargedRentaNatByAnnexesAndContracts as $key => $value) {
                $quantityValue = number_format($value * $this->renta_types_values[$key], 2, '.', '');
                $total_unpaid_renta_nat_unit_value_arr[] = $quantityValue . ' лв.';
            }
        }
        $total_unpaid_renta_nat_unit_value_text = implode('</br>', $total_unpaid_renta_nat_unit_value_arr);

        $allRentaByAnnexesAndContracts = [];
        foreach ($chargedRentaByAnnexesAndContracts as $keyOwner => $chargedRentaByOwner) {
            if (!array_key_exists($keyOwner, $allRentaByAnnexesAndContracts)) {
                $allRentaByAnnexesAndContracts[$keyOwner] = [];
            }
            foreach ($chargedRentaByOwner as $keyContracts => $chargedRentaByContracts) {
                foreach ($chargedRentaByContracts as $keyContract => $chargedRentaByContract) {
                    if (!array_key_exists($keyContract, $allRentaByAnnexesAndContracts[$keyOwner])) {
                        $allRentaByAnnexesAndContracts[$keyOwner][$keyContract] = [];
                    }
                    foreach ($chargedRentaByContract as $keyPlot => $chargedRentaByPlot) {
                        if (!array_key_exists($keyPlot, $allRentaByAnnexesAndContracts[$keyOwner][$keyContract])) {
                            $allRentaByAnnexesAndContracts[$keyOwner][$keyContract][$keyPlot] = 0;
                        }

                        $allRentaByAnnexesAndContracts[$keyOwner][$keyContract][$keyPlot] += number_format($chargedRentaByPlot, 2, '.', '');
                    }
                }
            }
        }

        $temp_total_unpaid_renta = 0;
        $allRenta = [];
        $allUnpaidRenta = [];
        $total_unpaid_renta = 0;
        $total_renta = 0;
        $total_unpaid_renta_by_owner = [];
        $total_over_paid_renta_by_owner = [];
        foreach ($rentaByAnnexesAndContracts as $keyOwner => $rentaByOwner) {
            $totalPaidRentaLast = 0;
            $totalRentaLast = 0;

            foreach ($rentaByOwner as $keyContracts => $rentaByContracts) {
                foreach ($rentaByContracts as $keyContract => $rentaByContract) {
                    foreach ($rentaByContract as $keyPlot => $rentaByPlot) {
                        $rentaByAnnexAndContract = $allRentaByAnnexesAndContracts[$keyOwner][$keyContract][$keyPlot];
                        $paidRenta = $paidRentaByOwnerContractPlot[$keyOwner][$keyContract][$keyPlot];

                        if (!is_null($rentaByAnnexAndContract)) {
                            $tempRentaUnPaid = ($rentaByAnnexAndContract - $paidRenta) > 0 ? ($rentaByAnnexAndContract - $paidRenta) : 0;

                            $total_unpaid_renta += $tempRentaUnPaid;

                            $totalPaidRentaLast += $paidRenta;
                            $totalRentaLast += $rentaByAnnexAndContract;
                        } else {
                            $total_renta += $rentaByPlot;
                            $tempRentaUnPaid = ($rentaByPlot - $paidRenta) > 0 ? ($rentaByPlot - $paidRenta) : 0;

                            $total_unpaid_renta += $tempRentaUnPaid;

                            $totalPaidRentaLast += $paidRenta;
                            $totalRentaLast += $rentaByPlot;
                        }
                    }
                }
            }

            $total_unpaid_renta_by_owner[$keyOwner] = ($totalRentaLast - $totalPaidRentaLast) > 0 ? number_format(($totalRentaLast - $totalPaidRentaLast), 2, '.', '') : 0;
            $total_over_paid_renta_by_owner[$keyOwner] = ($totalRentaLast - $totalPaidRentaLast) >= 0 ? 0 : number_format(($totalRentaLast - $totalPaidRentaLast), 2, '.', '');
        }

        if (!empty($total_unpaid_renta_by_owner)) {
            $temp_total_unpaid_renta = 0;
            foreach ($total_unpaid_renta_by_owner as $key => $value) {
                $temp_total_unpaid_renta += $value;
            }
        }
        $temp_total_unpaid_renta = $temp_total_unpaid_renta > 0 ? $temp_total_unpaid_renta : '0.00';
        $total_unpaid_renta = number_format($temp_total_unpaid_renta, 2, '.', '');

        if (!empty($total_over_paid_renta_by_owner)) {
            $temp_total_over_paid_renta = 0;
            foreach ($total_over_paid_renta_by_owner as $key => $value) {
                $temp_total_over_paid_renta += $value;
            }
        }
        $total_over_paid_renta = $temp_total_over_paid_renta >= 0 ? '0.00' : $temp_total_over_paid_renta * (-1);

        if ($total_charged_renta_nat) {
            ksort($total_charged_renta_nat);

            $charged_renta_nats_text = '';
            foreach ($total_charged_renta_nat as $key => $value) {
                if ('' != $value) {
                    $charged_renta_nats_text .= number_format($value, 3, '.', '') . ' X ' . $this->renta_types[$key] . '</br>';
                }
            }
        }

        if ($total_renta_nat) {
            ksort($total_renta_nat);

            $renta_nats_text = '';
            foreach ($total_renta_nat as $key => $value) {
                if ('' != $value) {
                    $renta_nats_text .= number_format($value, 3, '.', '') . ' X ' . $this->renta_types[$key] . '</br>';
                    $total_rent_arr[$this->renta_types[$key]] = $value;
                }
            }
        }
        $footer = [
            'iconCls' => 'no-background',
            'owner_names' => '',
            'rents_arr' => $renta_arr,
            'egn_eik' => '<b>ОБЩО' . (null != $limit ? ' за стр.' : '') . '</b>',
            'area_by_owners' => number_format($total_area_owner_new, 3, '.', ''),
            'area_by_heritors' => $this->calculateHeritorsArea($results),
            'renta' => number_format($total_renta, 2, '.', ''),
            'charged_renta' => number_format($total_charged_renta, 2, '.', ''),
            'paid_renta' => number_format($total_paid_renta, 2, '.', ''),
            'paid_renta_by' => $total_paid_renta_by_text,
            'renta_nat_text' => $renta_nats_text,
            'charged_renta_nat_text' => $charged_renta_nats_text,
            'paid_renta_nat' => $total_paid_renta_nat_text, // $total_paid_renta_nat_text,
            'paid_renta_nat_by' => $total_paid_renta_by_nat_text,
            'paid_renta_nat_by_detailed' => $total_all_paid_renta_nat_by_detailed_text,
            'unpaid_renta' => number_format($total_unpaid_renta, 2, '.', ''),
            'unpaid_renta_nat' => $total_unpaid_renta_nat_text,
            'unpaid_renta_nat_unit_value' => $total_unpaid_renta_nat_unit_value_text,
            'over_paid_renta' => number_format($total_over_paid_renta, 2, '.', ''),
            'over_paid_renta_nat' => $total_overpaid_renta_nat_text,
            'total_by_renta' => number_format($total_sum_by_paid_renta, 2, '.', ''),
            'total_by_renta_nat' => $total_sum_paid_renta_nat_text,
            'paid_rent_arr' => $paid_rent_arr,
            'unpaid_rent_arr' => $unpaid_rent_arr,
            'total_rent_arr' => $total_rent_arr,
        ];

        $footer = array_merge($footer, $unpaid_rent_id_arr);

        $return['footer'] = [
            $footer,
        ];

        return $return;
    }

    public function hasPersonalUse($ownersTree)
    {
        foreach ($ownersTree as $owner) {
            if (!empty($owner['children'])) {
                if ($this->hasPersonalUse($owner['children'])) {
                    return true;
                }
            }
            if ($owner['has_personal_use']) {
                return true;
            }
        }

        return false;
    }

    /**
     * @return array
     */
    protected function getFarmings(&$UsersController)
    {
        $farming_year_from_id = $UsersController->StringHelper->getFarmingYearByDate($_POST['payroll_from_date']);
        $farming_year_to_id = $UsersController->StringHelper->getFarmingYearByDate($_POST['payroll_to_date']);
        $farming_years = [];

        for ($i = $farming_year_from_id; $i <= $farming_year_to_id; $i++) {
            $farming_years[] = $i;
        }

        $farming_years_str = implode(',', $farming_years);
        $farming_years_string = '(' . implode(',', $farming_years) . ')';

        return [$farming_years, $farming_years_str, $farming_years_string];
    }

    private function addNewRentaPayrolls($rentas = [], $owner = [], $totalPaidRenta = false)
    {
        $paid_renta = 0;
        $natural_renta = [];
        $natural_renta_contract = [];
        $paid_renta_contract = [];

        foreach ($rentas as $rentaData) {
            if ($owner['owner_id'] == $rentaData['owner_id'] || $totalPaidRenta) {
                if (1 == $rentaData['paid_from']) {
                    $paid_renta += $rentaData['trans_amount'];
                    $paid_renta_contract[$rentaData['contract_id']] += $rentaData['trans_amount'];
                } else {
                    if (isset($natural_renta[$rentaData['nat_type']])) {
                        $natural_renta[$rentaData['nat_type']] += $rentaData['trans_amount_nat'];
                        $natural_renta_contract[$rentaData['contract_id']][$rentaData['nat_type']] += $rentaData['trans_amount_nat'];
                    } else {
                        $natural_renta[$rentaData['nat_type']] = $rentaData['trans_amount_nat'];
                        $natural_renta_contract[$rentaData['contract_id']][$rentaData['nat_type']] = $rentaData['trans_amount_nat'];
                    }
                }
            }
        }

        foreach ($natural_renta as $nat_rent_key => $nat_renta) {
            if ('NULL' != $nat_rent_key) {
                $owner['paid_renta_nat_details'][$nat_rent_key] += $nat_renta;
                $owner['paid_renta_nat_details'][$nat_rent_key] = number_format($owner['paid_renta_nat_details'][$nat_rent_key], 3, '.', '');
            }
        }
        foreach ($natural_renta_contract as $contract_id => $value) {
            if (empty($value)) {
                continue;
            }
            foreach ($value as $nat_rent_key => $nat_renta) {
                if ('NULL' != $nat_rent_key) {
                    $owner['paid_renta_nat_details_contract'][$contract_id][$nat_rent_key] += $nat_renta;
                    $owner['paid_renta_nat_details_contract'][$contract_id][$nat_rent_key] = number_format($owner['paid_renta_nat_details_contract'][$contract_id][$nat_rent_key], 3, '.', '');
                }
            }
        }

        $owner['paid_renta'] = $paid_renta;
        $owner['paid_renta_contract'] = $paid_renta_contract;

        return $owner;
    }

    private function _getHeritorAncestors()
    {
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);

        $options = [
            'return' => [
                'array_agg(DISTINCT(subltree(h.path,0,1)))',
            ],
            'where' => [
                'heritor_egn' => ['column' => 'egn', 'compare' => '=', 'prefix' => 'o', 'value' => $_POST['heritor_egn']],
            ],
        ];

        if (isset($_POST['heritor_names']) && '' != $_POST['heritor_names']) {
            $tmp_owner_names = preg_replace('/\s+/', '.*', $_POST['heritor_names']);
            $tmp_owner_names = mb_strtolower($tmp_owner_names, 'UTF-8');
            $options['where']['heritor_name'] = ['column' => "lower(TRIM (o.NAME)) || ' ' || lower(TRIM (o.surname)) || ' ' || lower(TRIM (o.lastname))", 'compare' => '~', 'value' => $tmp_owner_names];
        }

        $ancestors = $UserDbOwnersController->getOwnersHeritors($options);
        $ancestors = str_replace('{', '', $ancestors[0]['array_agg']);
        $ancestors = str_replace('}', '', $ancestors);
        $ancestors = explode(',', $ancestors);

        return $ancestors;
    }

    private function arraySumIdenticalKeys()
    {
        $arrays = func_get_args();
        $keys = array_keys(array_reduce($arrays, function ($keys, $arr) { return $keys + $arr; }, []));
        $sums = [];

        foreach ($keys as $key) {
            $sums[$key] = array_reduce(
                $arrays,
                function ($sum, $arr) use ($key) {
                    $quantity = number_format($sum + @$arr[$key], 3, '.', '');
                    if ('0.000' != $quantity) {
                        return $quantity . ' X ' . $this->renta_types[$key];
                    }
                }
            );
        }

        return $sums;
    }

    private function calcSumIdenticalKeys()
    {
        $arrays = func_get_args();
        $keys = array_keys(array_reduce($arrays, function ($keys, $arr) { return $keys + $arr; }, []));
        $sums = [];

        foreach ($keys as $key) {
            $sums[$key] = array_reduce(
                $arrays,
                function ($sum, $arr) use ($key) {
                    $quantity = number_format($sum + @$arr[$key], 3, '.', '');
                    if ('0.000' != $quantity) {
                        return $quantity;
                    }
                }
            );
        }

        return $sums;
    }

    private function calculateHeritorsArea($data)
    {
        $heirs_total = 0;
        foreach ($data as $owner) {
            if (false == $owner['is_dead']) {
                $heirs_total += (float)number_format((float)$owner['area_total'], 3, '.', '');
            }
            if (!empty($owner['children'])) {
                foreach ($owner['children'] as $heirs) {
                    if (false == $heirs['is_dead']) {
                        $heirs_total += (float)$heirs['area'];
                    }
                }
            }
        }

        return number_format($heirs_total, 3, '.', '');
    }

    private function getOwnersHeritorsData(&$result, &$UserDbOwnersController, &$UserDbPaymentsController, &$paidResults, $contract_array, $farming_years)
    {
        $path = $result['owner_id'] . '.*';
        // get all heritors
        $options = [
            'return' => ['h.owner_id', 'h.path'],
            'where' => ['path' => ['column' => 'path', 'compare' => '~', 'value' => $path]],
        ];

        $heritor_results = $UserDbOwnersController->getOwnersHeritors($options);
        $result['children'] = $this->getOwnersHeritorsPayroll($path, $result['owner_id'], false, $result, false);
        if (empty($heritor_results)) {
            return;
        }
        foreach ($heritor_results as $key => $value) {
            $paidOptions = [
                'order' => 'asc',
                'sort' => 'p.contract_id',
                'return' => [
                    'p.id as payment_id',
                    'p.owner_id as owner_id',
                    'p.contract_id as contract_id',
                    'case when pn.amount notnull and pn.unit_value notnull  then round((pn.amount * pn.unit_value)::numeric, 2) else round(p.amount::numeric, 2) end as trans_amount',
                    'case when pn.amount notnull then round(pn.amount::numeric,3) else round(p.amount_nat::numeric, 3) end as amount_nat',
                    'round(pn.amount::numeric, 3) as trans_amount_nat',
                    'p.paid_in',
                    'p.paid_from',
                    'pn.nat_type as nat_type',
                    'rent.name as trans_nat_type_text',
                    'p.path',
                    'round(pn.unit_value::numeric, 2)',
                ],
                'where' => [
                    'owner_id' => ['column' => 'owner_id', 'compare' => '=', 'prefix' => 'p', 'value' => $value['owner_id']],
                    'path' => ['column' => 'path', 'compare' => '=', 'prefix' => 'p', 'value' => (string)$value['path']],
                    'contract_id' => ['column' => 'contract_id', 'compare' => 'IN', 'prefix' => 'p', 'value' => $contract_array],
                    'farming_year' => ['column' => 'farming_year', 'compare' => 'IN', 'prefix' => 'p', 'value' => $farming_years],
                ],
                'group_by' => 'p.contract_id',
            ];

            $paidResultsHeritor = $UserDbPaymentsController->getPaidData($paidOptions);

            $paidResults = array_merge($paidResults, $paidResultsHeritor);
        }
    }

    private function getAnnexes(&$UserDbController, $ownerIdArray, $contract_string, $finalPlots, $farming_years_str, $annex_string)
    {
        $annexesQueryString = 'SELECT sum(renta_nat) as renta_nat, sum(renta) as renta, unit, nat_type, c_id, a_id, owner_id, round(contract_area::numeric, 3) as contract_area, plot_id';

        $annexesQueryString .= " FROM (SELECT
                                    crt.renta_value * (pc.area_for_rent * po.percent / 100::double precision) AS renta_nat,
                                    CASE
                                        WHEN a.renta IS NULL THEN c.renta
                                        ELSE a.renta
                                    END::double precision * (pc.area_for_rent * (po.percent / 100::double precision)) AS renta,
                                    a.renta AS annex_renta,
                                    pc.area_for_rent AS annex_area,
                                    pc.area_for_rent * (po.percent / 100::double precision) AS contract_area,
                                    c.id AS c_id,
                                    a.id AS a_id,
                                    a.farming_id,
                                    a.start_date,
                                    a.due_date,
                                    po.owner_id,
                                    o.is_dead,
                                    kvs.ekate,
                                    kvs.gid AS plot_id,
                                    crt.renta_id AS nat_type,
                                    rt.name,
                                    rt.unit
                                FROM su_contracts c
                                LEFT JOIN su_contracts a ON a.parent_id = c.id AND a.active = true
                                JOIN su_contracts_plots_rel pc ON pc.contract_id = a.id
                                LEFT JOIN su_plots_owners_rel po ON po.pc_rel_id = pc.id
                                JOIN su_owners o ON o.id = po.owner_id
                                LEFT JOIN layer_kvs kvs ON kvs.gid = pc.plot_id
                                LEFT JOIN su_contracts_rents crt ON
                                    CASE
                                        WHEN a.id IS NULL THEN c.id = crt.contract_id
                                        ELSE a.id = crt.contract_id
                                    END
                                LEFT JOIN su_renta_types rt ON crt.renta_id = rt.id
                                WHERE
                                    pc.annex_action = 'added'::annex_action_enum
                                    AND a.is_annex = true
                                    and a.start_date <= '{$_POST['payroll_to_date']}'
                                    and a.due_date >= '{$_POST['payroll_from_date']}'
                                    and o.id IN ({$ownerIdArray})
                                    and c.id IN ({$contract_string})
                                    and pc.plot_id IN ({$finalPlots})
                                GROUP BY
                                    rt.name, crt.renta_value, crt.renta_id, pc.area_for_rent,
                                    po.percent, c.id, a.id, po.owner_id, a.farming_id,
                                    a.start_date, a.due_date, rt.unit, kvs.ekate, kvs.gid,
                                    o.is_dead
                                ORDER BY c.id
                            ) renta_nats_annexes_mat_view ";

        // $annexesQueryString .= ' FROM renta_nats_annexes_mat_view';

        $annexesQueryString .= " WHERE owner_id IN ({$ownerIdArray})
		AND c_id IN ({$contract_string})
		AND plot_id IN ({$finalPlots})
		AND start_date <= '{$_POST['payroll_to_date']}'
		AND due_date >= '{$_POST['payroll_from_date']}' ";

        $payrollFarmings = null != $_POST['payroll_farming'] ? implode(',', $_POST['payroll_farming']) : null;
        if (null != $payrollFarmings) {
            $annexesQueryString .= " AND farming_id in ({$payrollFarmings})";
        }
        if (null != $_POST['payroll_ekate']) {
            $payrollEkates = implode(', ', $_POST['payroll_ekate']);
            $annexesQueryString .= " AND ekate {$this->getPayrollEkateCompareType($_POST)} ('{$payrollEkates}')";
        }
        if (strlen($annex_string) > 0) {
            $annexesQueryString .= " AND a_id IN ({$annex_string})";
        }
        $annexesQueryString .= ' GROUP BY nat_type, unit, c_id, a_id, owner_id, contract_area, plot_id ORDER BY nat_type';

        $annexesRentas = $UserDbController->DbHandler->getDataByQuery($annexesQueryString);

        return $annexesRentas;
    }

    private function getContracts(&$UserDbController, $ownerIdArray, $contract_string, $finalPlots, $farming_years_str)
    {
        $contractsQueryString = "SELECT sum(renta_nat) as renta_nat, sum(renta) as renta, nat_type, c_id, owner_id, round(contract_area::numeric, 3) as contract_area, plot_id, year
		FROM renta_nats_mat_view
		WHERE owner_id IN ({$ownerIdArray})
		AND c_id IN ({$contract_string})
		AND plot_id IN ({$finalPlots})
		AND start_date <= '{$_POST['payroll_to_date']}'
		AND due_date >= '{$_POST['payroll_from_date']}'
		AND (year IN ({$farming_years_str}) OR year IS NULL)";
        $payrollFarmings = null != $_POST['payroll_farming'] ? implode(',', $_POST['payroll_farming']) : null;
        if (null != $payrollFarmings) {
            $contractsQueryString .= " AND farming_id in ({$payrollFarmings})";
        }
        if (null != $_POST['payroll_ekate']) {
            $payrollEkates = implode(', ', $_POST['payroll_ekate']);
            $contractsQueryString .= " AND ekate {$this->getPayrollEkateCompareType($_POST)} ('{$payrollEkates}')";
        }
        $contractsQueryString .= ' GROUP BY nat_type, c_id, owner_id, contract_area, plot_id, year ORDER BY nat_type';
        $contractsRentas = $UserDbController->DbHandler->getDataByQuery($contractsQueryString);

        return $contractsRentas;
    }

    private function getChargedRentAnnexes(&$UserDbController, $ownerIdArray, $contract_string, $finalPlots, $farming_years_str, $annex_string)
    {
        $charged_rents_annexes_query_string = "SELECT 
			c_id,
			owner_id, 
			plot_id,
			sum(charged_renta) as charged_renta_sum,
			sum(converted_charged_renta_nat) as converted_charged_renta_sum,
			sum(charged_renta_nat) as charged_renta_nat_sum,
			nat_type
		 FROM charged_rentas_annexes_mat_view 
		 WHERE owner_id IN ({$ownerIdArray})
		 AND year in ({$farming_years_str})
		 AND plot_id IN ({$finalPlots})
		 AND c_id IN ({$contract_string})
		 AND start_date <= '{$_POST['payroll_to_date']}'
		 AND due_date >= '{$_POST['payroll_from_date']}'
		 AND (pu_year IN ({$farming_years_str}) OR pu_year IS NULL)";
        $payrollFarmings = null != $_POST['payroll_farming'] ? implode(',', $_POST['payroll_farming']) : null;
        if (null != $payrollFarmings) {
            $charged_rents_annexes_query_string .= " AND farming_id in ({$payrollFarmings})";
        }
        if (null != $_POST['payroll_ekate']) {
            $payrollEkates = implode(', ', $_POST['payroll_ekate']);
            $charged_rents_annexes_query_string .= " AND ekate IN ('{$payrollEkates}')";
        }
        if (strlen($annex_string) > 0) {
            $charged_rents_annexes_query_string .= " AND a_id IN ({$annex_string})";
        }
        $charged_rents_annexes_query_string .= ' GROUP BY nat_type, c_id, owner_id, plot_id ORDER BY nat_type ';
        $chargedRentsAnnexes = $UserDbController->DbHandler->getDataByQuery($charged_rents_annexes_query_string);

        return $chargedRentsAnnexes;
    }

    private function getChargedRents(&$UserDbController, $ownerIdArray, $contract_string, $finalPlots, $farming_years_str)
    {
        $charged_rents_query_string = "SELECT 
			c_id,
			owner_id, 
			plot_id,
			sum(charged_renta) as charged_renta_sum,
			sum(converted_charged_renta_nat) as converted_charged_renta_sum,
			sum(charged_renta_nat) as charged_renta_nat_sum,
			nat_type
		 FROM charged_rentas_mat_view 
		 WHERE owner_id IN ({$ownerIdArray})
		 AND year in ({$farming_years_str})
		 AND plot_id IN ({$finalPlots})
		 AND c_id IN ({$contract_string})
		 AND start_date <= '{$_POST['payroll_to_date']}'
		 AND due_date >= '{$_POST['payroll_from_date']}'
		 AND (pu_year IN ({$farming_years_str}) OR pu_year IS NULL)";
        $payrollFarmings = null != $_POST['payroll_farming'] ? implode(',', $_POST['payroll_farming']) : null;
        if (null != $payrollFarmings) {
            $charged_rents_query_string .= " AND farming_id in ({$payrollFarmings})";
        }
        if (null != $_POST['payroll_ekate']) {
            $payrollEkates = implode(', ', $_POST['payroll_ekate']);
            $charged_rents_query_string .= " AND ekate {$this->getPayrollEkateCompareType($_POST)} ('{$payrollEkates}')";
        }
        $charged_rents_query_string .= ' GROUP BY nat_type, c_id, owner_id, plot_id ORDER BY nat_type ';
        $chargedRents = $UserDbController->DbHandler->getDataByQuery($charged_rents_query_string);

        return $chargedRents;
    }

    /**
     * @param UserDbController $UserDbController
     *
     * @return array
     */
    private function getOwnersIds(&$UserDbController, $owner_egns, $company_eiks)
    {
        $filteredIds = [];
        $options = [
            'tablename' => $UserDbController->DbHandler->tableOwners,
            'return' => ['id'],
        ];
        if (!empty($owner_egns)) {
            $options['whereOr']['egn'] = ['column' => 'egn', 'compare' => 'IN', 'value' => $owner_egns];
        }
        if (!empty($company_eiks)) {
            $options['whereOr']['eik'] = ['column' => 'eik', 'compare' => 'IN', 'value' => $company_eiks];
        }

        $results = $UserDbController->getItemsByParams($options, false, false);

        if (empty($results)) {
            return [];
        }
        foreach ($results as $result) {
            $filteredIds[] = $result['id'];
        }

        return $filteredIds;
    }

    /**
     * @param array $filters
     *
     * @return string
     */
    private function getPayrollEkateCompareType($filters)
    {
        if (array_key_exists('payroll_ekate', $filters)) {
            if (1 == count($filters['payroll_ekate']) && 0 == strlen($filters['payroll_ekate'][0])) {
                return '=';
            }
        }

        return 'IN';
    }

    /**
     * @param array $values
     *
     * @retrun string|arr
     */
    private function filterPayrollEkateValue($values)
    {
        if (1 == count($values) && 0 == strlen($values[0])) {
            return '';
        }

        return $values;
    }
}
