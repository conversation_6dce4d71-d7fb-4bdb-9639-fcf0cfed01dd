<?php

namespace TF\Engine\APIClasses\Payroll;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\ExportToExcelClass;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbPayments\UserDbPaymentsController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Light Payroll Grid.
 *
 * @rpc-module Payroll
 *
 * @rpc-service-id light-payroll-grid
 */
class LightPayrollGrid extends TRpcApiProvider
{
    protected $rentTypes = [];

    protected $defaultReturn = [
        'rows' => [],
        'total' => 0,
        'footer' => [
            0 => ['iconCls' => 'no-background',
                'owner_names' => '',
                'egn_eik' => '<b>ОБЩО за стр.</b>',
                'unpaid_renta' => '0.00'],
            1 => ['iconCls' => 'no-background',
                'owner_names' => '',
                'egn_eik' => '<b>ОБЩО</b>',
                'unpaid_renta' => '0.00'],
        ],
    ];

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'read'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'printReport' => ['method' => [$this, 'printReport'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'export' => ['method' => [$this, 'exportExcel'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    public function read($rpcParams, $page, $rows, $sort, $order)
    {
        $owners = $this->getPayroll($rpcParams, $page, $rows, $sort, $order);
        if (empty($owners)) {
            return $this->defaultReturn;
        }
        $return['rows'] = array_values($owners);
        $return['total'] = count($owners);
        $return['footer'] = $this->defaultReturn['footer'];
        $return['footer'][0] = array_merge($return['footer'][0], $this->summarize($owners));
        if (!$rpcParams['for_export']) {
            $rpcParams['grouping'] = 'c.active';
            $footer = $this->getTotalFooter($rpcParams, 0, 'ALL', $sort, $order);
            $return['footer'][1] = array_merge($return['footer'][1], $footer);
            $return['total'] = $footer['total'];
        }

        return $return;
    }

    public function exportExcel(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        list($rows_data, $column_headers, $footer) = $this->export($rpcParams, $page, $rows, $sort, $order);

        $filename = '/ostavashta_renta_' . strtotime(date('Y-m-d H:i:s')) . '.xlsx';
        $file_path_name = PAYROLL_EXPORTS_PATH . $this->User->GroupID . $filename;
        if (!file_exists(PAYROLL_EXPORTS_PATH)) {
            mkdir(PAYROLL_EXPORTS_PATH, 0777);
        }
        if (!file_exists(PAYROLL_EXPORTS_PATH . $this->User->GroupID)) {
            mkdir(PAYROLL_EXPORTS_PATH . $this->User->GroupID, 0777);
        }
        /** @var ExportToExcelClass $exportExcelDoc */
        $exportExcelDoc = new ExportToExcelClass();
        $exportExcelDoc->export($rows_data, $column_headers, [$footer], 0, ['freezePane' => 'A2', 'addAutoFilter' => true]);
        $exportExcelDoc->saveFile($file_path_name);

        return 'files/payrolls/' . $this->User->GroupID . $filename;
    }

    public function printReport(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        list($rows_data, $headers, $footer) = $this->export($rpcParams, $page, $rows, $sort, $order);
        $return['rows'] = $rows_data;
        foreach ($headers as $index => $header) {
            $return['header'][$index]['text'] = $header;
        }

        $overall_footer = null;
        if ('' !== $page && count($rows_data) < $rows) {
            $rpcParams['grouping'] = 'c.active';
            $overall_footer = $this->getTotalFooter($rpcParams, 0, 'ALL', $sort, $order);
            $overall_footer = array_merge(['number' => 'ОБЩО', 'owner' => '', 'egn_eik' => ''], $overall_footer);
            $footer['number'] = ' ОБЩО за стр.';
        }

        $return['rows'][] = $footer;
        if ($overall_footer) {
            $return['rows'][] = $overall_footer;
        }

        $FarmingController = new FarmingController('Farming');
        $ltext = $FarmingController->StringHelper->loadTemplate($GLOBALS['Templates'][11]['template'], $return);
        $ltext = '<meta charset="UTF-8"><style>@page{size: landscape;}</style>' . $ltext;

        return $ltext;
    }

    protected function getTotalFooter($rpc_params, $page, $rows, $sort, $order)
    {
        $owners = $this->getPayroll($rpc_params, $page, $rows, $sort, $order);
        $footer = $this->summarize($owners);
        $footer['total'] = count($owners);

        return $footer;
    }

    protected function getPayroll($rpc_params, $page, $rows, $sort, $order)
    {
        $UsersController = new UsersController('Users');
        $farming_years = $this->setFarmingYears($UsersController, $rpc_params);
        if (empty($farming_years)) {
            return [];
        }
        unset($from_year_id, $to_year_id);
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);
        $this->rentTypes = $this->getNatRentTypes($UserDbController);

        if (is_array($rpc_params['payroll_farming']) && '' == $rpc_params['payroll_farming'][0]) {
            $rpc_params['payroll_farming'] = [];
        }

        $options = [
            'custom_counter' => 'COUNT(DISTINCT(o.id))',
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
            'return' => [
                'distinct (o.id) as oid',
                '(CASE WHEN owner_type = 1 THEN name || \' \' || surname || \' \' || lastname ELSE company_name END) as owner_names',
                '(CASE WHEN owner_type = 1 THEN o.egn ELSE o.eik END) as egn_eik',
                'array_agg(distinct CASE WHEN a.id IS NULL THEN c.id ELSE a.id END) as c_ids',
                'round((sum(po.percent * (pc.area_for_rent) / 100) - sum(COALESCE(pu.area, 0)))::numeric ,3) as area',
                'round(sum((case when scr.renta IS not NULL THEN scr.renta * ((po.percent * pc.area_for_rent / 100) - COALESCE(pu.area,0))
                 else (case when pc.rent_per_plot IS not NULL THEN pc.rent_per_plot * ((po.percent * pc.area_for_rent / 100) - COALESCE(pu.area, 0)) 
                 else (case WHEN a.id IS not NULL THEN a.renta * ((po.percent * pc.area_for_rent / 100) - COALESCE(pu.area, 0)) 
                 else c.renta * ((po.percent * pc.area_for_rent / 100) - COALESCE(pu.area, 0)) END)END)END))::numeric,2) as due_rent',
                'array_agg(distinct P1.amount) as payment_data ',
                'array_agg(distinct P1.nat) as payment_data_nat',
                'array_agg(distinct P1.paid_from) as paid_from',
                'array_agg(distinct P1.paid_in) as paid_in',
                'array_agg(nat.due_rent_nat) as due_rent_nat',
                'array_agg(distinct(charged_nat.item)) as charged_nat',
                'array_agg(distinct(charged_a_nat.a_item)) as charged_a_nat',
            ],
            'where' => [
                'contract_type' => ['column' => 'nm_usage_rights', 'prefix' => 'c', 'compare' => 'NOT IN', 'value' => [1, 4]],
                'is_sublease' => ['column' => 'is_sublease', 'prefix' => 'c', 'compare' => '=', 'value' => 'FALSE'],
                'active' => ['column' => 'active', 'prefix' => 'c', 'compare' => '=', 'value' => 'TRUE'],
                'is_annex' => ['column' => 'is_annex', 'prefix' => 'c', 'compare' => '=', 'value' => 'FALSE'],
                'annex_action' => ['column' => 'annex_action', 'prefix' => 'pc', 'compare' => '=', 'value' => 'added'],
                'percent' => ['column' => 'percent', 'prefix' => 'po', 'compare' => '>', 'value' => '0'],
                // filters
                'ekate' => ['column' => 'ekate', 'compare' => '=', 'prefix' => 'kvs', 'value' => $rpc_params['payroll_ekate']],
                'farming' => ['column' => 'farming_id', 'compare' => 'IN', 'prefix' => 'c', 'value' => $rpc_params['payroll_farming']],
                'egn' => ['column' => 'egn', 'compare' => 'ILIKE', 'prefix' => 'o', 'value' => $rpc_params['egn']],
                'eik' => ['column' => 'eik', 'compare' => 'ILIKE', 'prefix' => 'o', 'value' => $rpc_params['eik']],
            ],

            'group' => 'o.id',
            'start_date' => $rpc_params['payroll_to_date'],
            'due_date' => $rpc_params['payroll_from_date'],
            'year_id' => $farming_years,
        ];
        if ($rpc_params['grouping']) {
            $option['group'] = $rpc_params['grouping'];
        }
        if ($rpc_params['owner_names']) {
            $tmp_owner_names = preg_replace('/\s+/', '.*', $rpc_params['owner_names']);
            $tmp_owner_names = mb_strtolower($tmp_owner_names, 'UTF-8');
            $options['where']['owner_names'] = ['column' => "lower(TRIM (o.NAME)) || ' ' || lower(TRIM (o.surname)) || ' ' || lower(TRIM (o.lastname))", 'compare' => '~', 'value' => $tmp_owner_names];
            unset($tmp_owner_names);
        }

        $counter = $UserDbPaymentsController->getPayrollDataWithPayments($options, true, false);
        if (0 == $counter[0]['count']) {
            return [];
        }

        $results = $UserDbPaymentsController->getPayrollDataWithPayments($options, false, false);
        if (0 == count($results)) {
            return [];
        }

        $owners = [];
        foreach ($results as $index => $owner) {
            $owners[$index] = $this->prepareOwner($owner);
        }

        $owners = $this->formatUnpaid($owners);

        $owners = array_filter($owners, function ($owner) {
            return $owner['unpaid_renta'] > 0 || true == $owner['has_unpaid_nat'];
        });

        if (0 == count($owners)) {
            return [];
        }

        return array_values($owners);
    }

    protected function summarize($owners)
    {
        $return = [];
        $return['unpaid_renta'] = array_sum(array_column($owners, 'unpaid_renta'));
        foreach ($this->rentTypes as $nat_id) {
            if (0 !== $nat_id['id']) {
                $return['unpaid_renta_nat_arr_' . $nat_id['id']] = null;
            }
        }
        foreach ($owners as $index => $owner) {
            foreach ($this->rentTypes as $rent_type) {
                $nat_id = $rent_type['id'];
                if (0 === $nat_id) {
                    continue;
                }
                $nat_key = 'unpaid_renta_nat_arr_' . $nat_id;
                if (array_key_exists($nat_key, $owner)) {
                    $return[$nat_key] += $owner[$nat_key];
                }
            }
        }

        return $return;
    }

    protected function prepareOwner($owner)
    {
        $owner['c_ids'] = explode(',', trim($owner['c_ids'], '{}'));
        $owner['nat_ids'] = [];

        if ('{NULL}' !== $owner['charged_a_nat']) {
            $owner['charged_a_nat'] = $this->breakChargedNat($owner['charged_a_nat']);
        } else {
            $owner['charged_a_nat'] = null;
        }

        if ('{NULL}' !== $owner['charged_nat']) {
            $owner['charged_nat'] = $this->breakChargedNat($owner['charged_nat']);
        } else {
            $owner['charged_nat'] = null;
        }

        if (null !== $owner['charged_a_nat']) {
            $owner['charged_nat'] = null;
        }

        if ('{NULL}' !== $owner['due_rent_nat']) {
            $owner['due_rent_nat'] = explode(',', str_replace('"', '', trim($owner['due_rent_nat'], '{""}')));
            foreach ($owner['due_rent_nat'] as $index => $rent_nat) {
                if ('NULL' === $rent_nat) {
                    continue;
                }
                $owner['due_rent_nat'][$index] = $this->breakString($rent_nat, 'due_rent_nat');
            }
            unset($rent_nat);
            $owner['due_rent_nat_total'] = [];
            foreach ($owner['due_rent_nat'] as $rent_nat) {
                if ('NULL' === $rent_nat) {
                    continue;
                }
                foreach ($rent_nat['nat_id'] as $index => $rent_value_id) {
                    $contract_id = $rent_nat['contract_id'];
                    $amount_nat = (float)$rent_nat['amount'][$index];
                    if ($rent_value_id && in_array($contract_id, $owner['c_ids'], false)) {
                        $owner['due_rent_nat_total'][$rent_value_id] += $amount_nat;
                        $owner['unpaid_renta_nat_arr_' . $rent_value_id] += $amount_nat;
                    }
                    if (!in_array($rent_value_id, $owner['nat_ids'], false)) {
                        $owner['nat_ids'][] = $rent_value_id;
                    }
                }
            }
        } else {
            $owner['due_rent_nat'] = null;
        }
        $charged_nat_tot = null;
        if (null !== $owner['charged_a_nat']) {
            $charged_nat_tot = $this->sumNatChargeRent($owner['charged_a_nat']);
        }
        if (null !== $owner['charged_nat']) {
            $charged_nat_tot = $this->sumNatChargeRent($owner['charged_nat']);
        }
        unset($owner['charged_a_nat'], $owner['charged_nat']);
        $owner['charged_nat_tot'] = $charged_nat_tot;

        if (null !== $charged_nat_tot) {
            if (!empty($charged_nat_tot['qts'])) {
                if (empty($owner['due_rent_nat_total'])) {
                    $owner['due_rent_nat_total'] = $charged_nat_tot['qts'];
                } else {
                    foreach ($charged_nat_tot['qts'] as $nat_id => $quantity) {
                        if ($quantity && $nat_id && in_array($nat_id, $owner['nat_ids'], false)) {
                            $owner['due_rent_nat_total'][$nat_id] = $charged_nat_tot['qts'][$nat_id];
                            $owner['unpaid_renta_nat_arr_' . $nat_id] = $charged_nat_tot['qts'][$nat_id];
                        }
                    }
                }
            }
            if (null !== $charged_nat_tot['price']) {
                $owner['due_rent'] = $charged_nat_tot['price'];
            }
        }

        $owner['payment_leva_total'] = 0;
        $payments_ids = [];
        if ('{NULL}' !== $owner['payment_data']) {
            $owner['payment_data'] = explode(',', str_replace('"', '', trim($owner['payment_data'], '{""}')));
            $owner['paid_from'] = explode(',', trim($owner['paid_from'], '{}"'));
            $owner['paid_in'] = explode(',', trim($owner['paid_in'], '{}"'));
            foreach ($owner['payment_data'] as $index => $payment) {
                if ('NULL' === $payment) {
                    continue;
                }
                $owner['payment_data'][$index] = $this->breakString($payment, 'payment_data');
                $owner['paid_from'][$index] = $this->breakString($owner['paid_from'][$index], 'paid_from');
                $owner['paid_in'][$index] = $this->breakString($owner['paid_in'][$index], 'paid_in');
                $owner['payment_data'][$index]['paid_from'] = $owner['paid_from'][$index]['value'];
                $owner['payment_data'][$index]['paid_in'] = $owner['paid_in'][$index]['value'];
                $owner['payment_data'][$index]['nat_payments'] = [];
            }
        } else {
            $owner['payment_data'] = null;
        }

        if ('{NULL}' !== $owner['payment_data_nat']) {
            $owner['payment_data_nat'] = explode(',', str_replace('"', '', trim($owner['payment_data_nat'], '{""}')));
            foreach ($owner['payment_data_nat'] as $index => $nat_payment) {
                if ('NULL' === $nat_payment) {
                    continue;
                }
                $nat_payment = $this->breakString($nat_payment, 'payment_data_nat');
                /** @var int $payment_id */
                $payment_id = array_search($nat_payment['payment_id'], array_column($owner['payment_data'], 'payment_id'), false);
                if (false === $payment_id) {
                    continue;
                }
                $nat_payment['paid_from'] = $owner['paid_from'][$payment_id]['value'];
                $nat_payment['paid_in'] = $owner['paid_in'][$payment_id]['value'];
                $owner['payment_data_nat'][$index] = $nat_payment;
                $owner['payment_data'][$payment_id]['nat_payments'][] = $nat_payment;
            }
        } else {
            $owner['payment_data_nat'] = null;
        }

        unset($owner['payment_data_nat'], $owner['paid_in'], $owner['paid_from']);

        if (count($owner['payment_data']) > 0) {
            foreach ($owner['payment_data'] as $index => $payment) {
                if ('NULL' === $payment) {
                    continue;
                }
                if (in_array($payment['payment_id'], $payments_ids, false)) {
                    continue;
                }
                if (1 == $payment['paid_from']) {
                    $owner['payment_leva_total'] += $payment['amount'];
                    $payments_ids[] = $payment['payment_id'];
                }
                if (2 == $payment['paid_from']) {
                    foreach ($payment['nat_payments'] as $nat_payment) {
                        if ('NULL' === $nat_payment) {
                            continue;
                        }
                        $amount_nat = $nat_payment['amount_nat'];
                        if ($amount_nat <= 0) {
                            continue;
                        }
                        $nat_id = $nat_payment['nat_id'];
                        $owner['payment_data_nat_total'][$nat_id] += $amount_nat;
                        $owner['unpaid_renta_nat_arr_' . $nat_id] -= $amount_nat;
                    }
                }
            }
        }

        $owner['unpaid_renta'] = number_format($owner['due_rent'] - $owner['payment_leva_total'], 2, '.', '');
        /* unset($owner['payment_data_nat'], $owner['paid_in'], $owner['paid_from'],
             $owner['charged_nat_tot'], $owner['payment_data'], $owner['payment_leva_total'], $owner['area'],
             $owner['c_ids'], $owner['due_rent_nat'], $owner['due_rent_nat_total'], $owner['due_rent']
         );*/
        return $owner;
    }

    protected function sumNatChargeRent($charged_nats)
    {
        $return = ['qts' => null, 'price' => null];
        foreach ($charged_nats as $index => $charged_nat) {
            if (null === $charged_nat) {
                continue;
            }
            $nat_id = $charged_nat['nat_id'];
            $is_converted = $charged_nat['nat_is_converted'];
            $amount = $is_converted ? $charged_nat['charged_nat_price'] : $charged_nat['charged_nat_qt'];
            if ($is_converted) {
                $return['price'] += $amount;

                continue;
            }
            $return['qts'][$nat_id] += $amount;
        }

        return $return;
    }

    protected function getNatRentTypes(&$UserDbController)
    {
        $options = ['tablename' => $UserDbController->DbHandler->tableRentaTypes];
        $results = $UserDbController->getItemsByParams($options, false, false);
        $return = [];
        foreach ($results as $result) {
            $return[$result['id']] = $result;
        }

        return $return;
    }

    protected function setFarmingYears(&$UsersController, $rpc_params)
    {
        $year_from_id = $UsersController->StringHelper->getFarmingYearByDate($rpc_params['payroll_from_date']);
        $year_to_id = $UsersController->StringHelper->getFarmingYearByDate($rpc_params['payroll_to_date']);

        return range($year_from_id, $year_to_id);
    }

    protected function formatUnpaid($owners)
    {
        foreach ($owners as $index => &$owner) {
            $its_unpaid_nat = [];
            foreach ($owner['nat_ids'] as $nat_id) {
                $unpaid_nat = $owner['unpaid_renta_nat_arr_' . $nat_id];
                $its_unpaid_nat[] = $unpaid_nat > 0;
                $owner['unpaid_renta_nat_arr_' . $nat_id] = ($unpaid_nat > 0) ? number_format($unpaid_nat, 3, '.', '') : '0.000';
            }
            $owner['has_unpaid_nat'] = !empty($its_unpaid_nat) && array_unique($its_unpaid_nat) !== [false];
            $owner['unpaid_renta'] = ($owner['unpaid_renta'] > 0) ? $owner['unpaid_renta'] : '0.00';
            unset($owners[$index]['nat_ids']);
        }

        return $owners;
    }

    private function export(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $results = $this->getPayroll($rpcParams, $page, $rows, $sort, $order);
        $footer = array_merge(['number' => 'ОБЩО', 'owner' => '', 'egn_eik' => ''], $this->summarize($results));

        // heading
        $column_headers = [
            'number' => 'Номер',
            'owner' => 'Собственик',
            'farming_year' => 'Стопанска година',
            'egn_eik' => 'ЕГН/ЕИК',
            'unpaid_renta' => 'В лева',
        ];

        $renta_types = [];
        foreach ($this->rentTypes as $nat_key => $rent_type) {
            if ('' == $rent_type['name']) {
                continue;
            }
            $rent_type_name = $rent_type['name'] . '(' . $GLOBALS['Contracts']['renta_units'][$rent_type['unit']]['name'] . ')';
            $renta_types[$nat_key] = $rent_type_name;
            $column_headers['unpaid_renta_nat_arr_' . $rent_type['id']] = $rent_type_name;
        }

        $rows_data = [];

        foreach ($results as $index => $row) {
            $row_data = [
                'number' => $index + 1,
                'owner' => $row['owner_names'],
                'egn_eik' => $row['egn_eik'],
                'farming_year' => $GLOBALS['Farming']['years'][$rpcParams['payroll_farming_year']]['farming_year_short'],
                'unpaid_renta' => $row['unpaid_renta'],
            ];

            foreach ($renta_types as $nat_key => $value) {
                $row_data['unpaid_renta_nat_arr_' . $nat_key] = array_key_exists('unpaid_renta_nat_arr_' . $nat_key, $row) ? $row['unpaid_renta_nat_arr_' . $nat_key] : 0;
            }

            $rows_data[] = $row_data;
        }

        return [$rows_data, $column_headers, $footer];
    }

    private function breakString($inputString, $fieldName)
    {
        $field = explode(';', $inputString);
        $field['contract_id'] = $field[0];
        $field_data = explode(':', $field[1]);
        if ('due_rent_nat' == $fieldName) {
            if (false !== strpos($field_data[0], '-')) {
                $field['amount'] = explode('|', $field_data[1]);
                $field['nat_id'] = explode('-', $field_data[0]);
            } else {
                $field['amount'] = [$field_data[1]];
                $field['nat_id'] = [$field_data[0]];
            }
        } elseif ('payment_data' == $fieldName) {
            $field['amount'] = (float)$field_data[1];
            $field['payment_id'] = $field_data[0];
        } elseif ('payment_data_nat' == $fieldName) {
            $field[1] = explode('|', $field_data[1]);
            $field['amount_nat'] = (float)$field[1][1];
            $field['nat_id'] = $field[1][0];
            $field['payment_id'] = $field_data[0];
        } else {
            $field['id'] = $field_data[0];
            $field['value'] = (float)$field_data[1];
        }
        unset($field[0], $field[1]);

        return $field;
    }

    private function breakChargedNat($charged_nats)
    {
        $charged_nats = str_replace(['"', '\\'], '', trim($charged_nats, '{""}'));
        $charged_nats = explode('|', $charged_nats);
        $return = [];
        $key_array = ['nat_id', 'charged_nat_qt', 'charged_nat_price', 'nat_is_converted'];
        foreach ($charged_nats as $charged_nat) {
            if (null === $charged_nat) {
                continue;
            }
            $charged_nat = str_replace(',NULL', '', $charged_nat);
            if (false === strpos($charged_nat, '],[')) {
                $nat_array = json_decode($charged_nat);
                $return[] = array_combine($key_array, $nat_array);

                continue;
            }
            $values = explode('],[', str_replace('],[', ']],[[', $charged_nat));
            foreach ($values as $value) {
                $nat_array = json_decode($value);
                $return[] = array_combine($key_array, $nat_array);
            }
        }

        return $return;
    }
}
