<?php

namespace TF\Engine\APIClasses\Users;

use DateTime;
use Exception;
use Prado\Exceptions\TDbException;
use Prado\Prado;
use Prado\Web\Services\TRpcApiProvider;
use Prado\Web\Services\TRpcServer;
use TF\Application\Common\Config;
use TF\Application\Entity\LayerStyles;
use TF\Application\Entity\ObjectPermissions;
use TF\Application\Entity\User;
use TF\Application\Entity\UserFarmings;
use TF\Application\Entity\UserLayers;
use TF\Application\Providers\ObjectPermissionsProvider;
use TF\Application\Providers\UserFarmingsProvider;
use TF\Application\Providers\UserProvider;
use TF\Engine\APIClasses\Common\FarmingCombobox;
use TF\Engine\Kernel\Export2XlsClass;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Kernel\Sentry\Sentry;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Настройки - Потребители.
 *
 * @rpc-module Users
 *
 * @rpc-service-id users
 */
class UsersMainGrid extends TRpcApiProvider
{
    public const DETACH_ORGANIZATION_ACTION = 'detach';
    public const ATTACH_ORGANIZATION_ACTION = 'attach';
    private $module = 'Users';
    private $service_id = 'users';
    private $default_label_size = 8;
    private $userProvider;
    private $farmProvider;

    public function __construct(TRpcServer $rpcServer)
    {
        parent::__construct($rpcServer);

        $this->userProvider = new UserProvider();
        $this->farmProvider = new UserFarmingsProvider();
    }

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getUserMainGridData'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'setUserRights' => ['method' => [$this, 'setUserRights']],
            'markForEdit' => ['method' => [$this, 'markForEdit']],
            'editUser' => ['method' => [$this, 'editUser']],
            'add' => ['method' => [$this, 'doAddUser']],
            'deleteSelectedUsers' => ['method' => [$this, 'deleteSelectedUsers']],
            'getLastLoginInfo' => ['method' => [$this, 'getLastLoginInfo']],
            'getUsernames' => ['method' => [$this, 'getUsernames']],
            'getNames' => ['method' => [$this, 'getNames']],
            'getEmails' => ['method' => [$this, 'getEmails']],
            'loginAsUser' => ['method' => [$this, 'loginAsUser']],
            'allModems' => ['method' => [$this, 'getAllModems']],
            'modems' => ['method' => [$this, 'getUserModems']],
            'addUserModems' => ['method' => [$this, 'addUserModems']],
            'removeUserModem' => ['method' => [$this, 'removeUserModem']],
            'getModemFiles' => ['method' => [$this, 'getModemFiles']],
            'getUserSubscriptions' => ['method' => [$this, 'getUserSubscriptions']],
            'setUserSubscriptions' => ['method' => [$this, 'setUserSubscriptions']],
            'getAllSubscriptions' => ['method' => [$this, 'getAllSubscriptions']],
            'getEkatteContractsCount' => ['method' => [$this, 'getEkatteContractsCount'],
                'validators' => [
                    'rpcParams' => [
                        'db' => 'validateRequired,validateText',
                        'ekatte' => 'validateRequired,validateText',
                    ],
                ],
            ],
            'deleteEkatte' => ['method' => [$this, 'deleteEkatte'],
                'validators' => [
                    'rpcParams' => [
                        'db' => 'validateRequired,validateText',
                        'ekatte' => 'validateRequired,validateText',
                    ],
                ],
            ],
            'exportToExcelUsersData' => ['method' => [$this, 'exportToExcelUsersData']],
            'getFarmings' => ['method' => [$this, 'getFarmings']],
            'checkUsernameExistence' => ['method' => [$this, 'checkUsernameExistence']],
            'checkEmailExistence' => ['method' => [$this, 'checkEmailExistence']],
            'getUserDataByUsername' => ['method' => [$this, 'getUserDataByUsername']],
            'manageUserOrganizationRelation' => ['method' => [$this, 'manageUserOrganizationRelation']],
            'getUserPermissions' => ['method' => [$this, 'getUserPermissions']],
            'setChosenOrganization' => ['method' => [$this, 'setChosenOrganization']],
            'updateOrganization' => ['method' => [$this, 'updateOrganization']],
            'addFarm' => ['method' => [$this, 'addFarm']],
            'editFarm' => ['method' => [$this, 'editFarm']],
            'deleteFarm' => ['method' => [$this, 'deleteFarm']],
            'manageFarmAccess' => ['method' => [$this, 'manageFarmAccess']],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item string username
     *                         #item string email
     *                         }
     * @param int|string $page pagination parameter
     * @param int|string $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array
     */
    public function getUserMainGridData(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $UsersController = new UsersController('Users');

        if ($this->User->isGuest || Config::USERS_NORMAL == $this->User->UserLevel) {
            if (array_key_exists('skip_user_level_validation', $rpcParams) && true === $rpcParams['skip_user_level_validation'] && Config::USERS_NORMAL == $this->User->UserLevel) {
                // allow user to request parent user sub users
            } else {
                return [
                    'total' => 0,
                    'rows' => [],
                ];
            }
        }

        $options = [
            'where' => [
                'userId' => ['column' => 'user_id', 'compare' => '=', 'value' => $rpcParams['user_id']],
            ],
        ];

        $options = [
            'custom_counter' => 'COUNT(DISTINCT(t.id))',
            'tablename' => $UsersController->DbHandler->tableName . ' t
                LEFT JOIN su_users p on p.id = t.parent_id
                LEFT JOIN su_users_rights s on s.user_id = t.id
                LEFT JOIN su_users_subscriptions sa on t.id = sa.user_id and sa.subscription_usage_type = \'map\'
                LEFT JOIN su_users_subscriptions sb on t.id = sb.user_id and sb.subscription_usage_type = \'plots\'
                LEFT JOIN su_users_subscriptions sc on t.id = sc.user_id and sc.subscription_usage_type = \'agro\'
                LEFT JOIN su_user_machine m on m.user_id = t.id',
            'return' => ['t.id', 't.username', 't.server', 't.name', 't.email', 't.phone', 't.address', 'to_char(t.last_login_date, \'DD.MM.YYYY\') as last_login_date',
                't.level', 't.active', 't.creation_date', 't.group_id', 't.parent_id', 'p.username as parent_username', 't.is_trial', 't.start_date', 't.due_date',
                't.salesperson_id', 't.ekatte_count', 't.total_plot_area', 't.identity_number',
                'CASE p."level" WHEN 2 THEN p.paid_support ELSE t.paid_support END paid_support',
                'CASE p."level" WHEN 2 THEN p.paid_support_start_date ELSE t.paid_support_start_date END paid_support_start_date',
                'CASE p."level" WHEN 2 THEN p.paid_support_due_date ELSE t.paid_support_due_date END paid_support_due_date',
                '(CASE p."level" WHEN 2 THEN p.paid_support_due_date ELSE t.paid_support_due_date END) :: DATE - now() :: DATE AS days_support_left',
                'array_to_string(array_agg(s.right_id), \'' . Config::USER_RIGHTS_DELIMITER . '\') AS user_rights',
                'sa.subscription_usage_id as subscription_usage_map', 'sb.subscription_usage_id as subscription_usage_plots', 'sc.subscription_usage_id as subscription_usage_agro',
            ],
            'where' => [
                'email' => ['column' => 'email', 'compare' => 'ILIKE', 'prefix' => 't', 'value' => $rpcParams['email']],
                'username' => ['column' => 'username', 'compare' => 'ILIKE', 'prefix' => 't', 'value' => $rpcParams['username']],
                'name' => ['column' => 'name', 'compare' => 'ILIKE', 'prefix' => 't', 'value' => $rpcParams['name']],
                'active' => ['column' => 'active', 'compare' => '=', 'prefix' => 't', 'value' => $rpcParams['active']],
                'is_trial' => ['column' => 'is_trial', 'compare' => '=', 'prefix' => 't', 'value' => $rpcParams['is_trial']],
                'parent_account' => ['column' => 'username', 'compare' => 'ILIKE', 'prefix' => 'p', 'value' => $rpcParams['parent_account']],
                'start_date' => ['column' => 'start_date', 'compare' => '>=', 'prefix' => 't', 'value' => $rpcParams['start_date']],
                'due_date' => ['column' => 'due_date', 'compare' => '<=', 'prefix' => 't', 'value' => $rpcParams['due_date']],
                'creation_date_from' => ['column' => 'creation_date', 'compare' => '>=', 'prefix' => 't', 'value' => $rpcParams['creation_date_from']],
                'creatio_date_to' => ['column' => 'creation_date', 'compare' => '<=', 'prefix' => 't', 'value' => $rpcParams['creation_date_to']],
                'paid_support' => ['column' => 'paid_support', 'compare' => '=', 'prefix' => 't', 'value' => $this->getFullYearFromIndex($rpcParams['paid_support'])],
                'paid_support_start_date' => ['column' => 'paid_support_due_date', 'compare' => '>=', 'prefix' => 't', 'value' => $rpcParams['paid_support_start_date']],
                'paid_support_due_date' => ['column' => 'paid_support_due_date', 'compare' => '<=', 'prefix' => 't', 'value' => $rpcParams['paid_support_due_date']],
                'machine' => ['column' => 'machine', 'compare' => 'LIKE', 'value' => $rpcParams['usb_modem']],
                'level' => ['column' => 'level', 'compare' => '=', 'prefix' => 't', 'value' => $rpcParams['user_role']],
            ],
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
        ];

        if (!empty($rpcParams['modules'])) {
            $options['group'] = 't.id';
            $options['having'] = 'array_agg(s.right_id) @> ARRAY[' . implode(',', $rpcParams['modules']) . ']';
        }

        if (!empty($rpcParams['sales_person'])) {
            $options['where']['salesperson_id'] = ['column' => 'salesperson_id', 'compare' => 'IN', 'prefix' => 't', 'value' => $rpcParams['sales_person']];
        }

        if (Config::USERS_ADMIN_FLAG == $this->User->UserLevel) {
            $options['where']['parent_id'] = ['column' => 'parent_id', 'compare' => '=', 'prefix' => 't', 'value' => $this->User->UserID];
        }

        if (Config::USERS_NORMAL == $this->User->UserLevel) {
            $options['where']['parent_id'] = ['column' => 'parent_id', 'compare' => '=', 'prefix' => 't', 'value' => $this->User->getParentID()];
        }

        if (Config::USERS_SALES_FLAG == $this->User->UserLevel && null == $rpcParams['user_role']) {
            $options['where']['level'] = ['column' => 'level', 'compare' => 'IN', 'prefix' => 't', 'value' => [Config::USERS_ADMIN_FLAG, Config::USERS_NORMAL]];
        }

        // This condition aways must be the final one because in it we get count of result
        if (!empty($rpcParams['modules'])) {
            $options['group'] = 't.id';
            $options['having'] = 'array_agg(s.right_id) @> ARRAY[' . implode(',', $rpcParams['modules']) . ']';
            // It is needed because we count doesn't work correctly with group by
            $counterArr = $UsersController->getItemsByParams($options, true, false);
            $counter[0]['count'] = count($counterArr);
        }

        $counter = isset($counter[0]['count']) ? $counter : $UsersController->getItemsByParams($options, true, false);
        if (0 === $counter[0]['count']) {
            return ['rows' => [], 'total' => 0];
        }

        $options['group'] = 't.id, p.id, p.username, sa.subscription_usage_id, sb.subscription_usage_id, sc.subscription_usage_id';
        $result = $UsersController->getItemsByParams($options, false, false);

        $resultCount = count($result);
        for ($i = 0; $i < $resultCount; $i++) {
            $result[$i]['active_status'] = (int)$result[$i]['active'];
            $result[$i]['active'] = ($result[$i]['active']) ? 'Активен' : 'Неактивен';
            $result[$i]['user_level'] = $result[$i]['level'];
            $result[$i]['level'] = $GLOBALS['Users']['account_types'][$result[$i]['level']]['name'];

            $result[$i]['category_map'] = 'Старт';
            $result[$i]['category_plots'] = '';

            if ($result[$i]['ekatte_count'] >= 3 && $result[$i]['ekatte_count'] <= 10) {
                $result[$i]['category_map'] = 'Оптимум';
            }
            if ($result[$i]['ekatte_count'] >= 11) {
                $result[$i]['category_map'] = 'Премиум';
            }
            if ($result[$i]['total_plot_area'] > 0 && $result[$i]['total_plot_area'] <= 5000.99) {
                $result[$i]['category_plots'] = 'Старт';
            }
            if ($result[$i]['total_plot_area'] >= 5001 && $result[$i]['total_plot_area'] <= 15000.99) {
                $result[$i]['category_plots'] = 'Оптимум';
            }
            if ($result[$i]['total_plot_area'] >= 15001.00) {
                $result[$i]['category_plots'] = 'Премиум';
            }

            $date = DateTime::createFromFormat('Y-m-d', $result[$i]['paid_support']);
            $result[$i]['paid_support'] = null;
            if (true == is_object($date)) {
                $result[$i]['paid_support'] = $date->format('Y');
            }
            $result[$i]['creation_date'] = strftime('%d.%m.%Y', strtotime($result[$i]['creation_date']));

            $result[$i]['start_date'] = strftime('%d.%m.%Y', strtotime($result[$i]['start_date']));
            $result[$i]['due_date'] = strftime('%d.%m.%Y', strtotime($result[$i]['due_date']));

            if ($result[$i]['paid_support_start_date']) {
                $result[$i]['paid_support_start_date'] = strftime('%d.%m.%Y', strtotime($result[$i]['paid_support_start_date']));
            }

            if ($result[$i]['paid_support_due_date']) {
                $result[$i]['paid_support_due_date'] = strftime('%d.%m.%Y', strtotime($result[$i]['paid_support_due_date']));
            }

            // Convert user rights' codes to their respective string equivalents.
            $userRights = explode(Config::USER_RIGHTS_DELIMITER, $result[$i]['user_rights']);

            $userRightsCount = count($userRights);
            for ($j = 0; $j < $userRightsCount; $j++) {
                $rightMap = Config::$USER_RIGHTS_MAP[$userRights[$j]];

                if ($rightMap['visible']) {
                    $userRights[$j] = $rightMap['name'];
                } else {
                    unset($userRights[$j]);
                }
            }
            $result[$i]['user_rights'] = implode(Config::USER_RIGHTS_DELIMITER, $userRights);
        }

        $return['total'] = $counter[0]['count'];
        $return['rows'] = $result;

        return $return;
    }

    /**
     * @api-method read
     *
     * @return array
     */
    public function setUserRights()
    {
        return [
            'is_superadmin' => ($this->User->IsSuperAdmin) ? true : false,
            'user_level' => $this->User->UserLevel,
            'original_user_level' => $this->getOriginalUserLevel(),

            'has_map_rights' => ($this->User->HasMapRightsR) ? true : false,
            'has_map_rights_rw' => ($this->User->HasMapRightsRW) ? true : false,

            'has_thematic_maps_rights' => ($this->User->HasThematicMapsRightsR) ? true : false,
            'has_thematic_maps_rights_rw' => ($this->User->HasThematicMapsRightsRW) ? true : false,

            'has_plot_rights' => ($this->User->HasPlotRightsR) ? true : false,
            'has_plot_rights_rw' => ($this->User->HasPlotRightsRW) ? true : false,

            'has_hypothecs_rights_r' => ($this->User->HasHypothecsRightsR) ? true : false,
            'has_hypothecs_rights_rw' => ($this->User->HasHypothecsRightsRW) ? true : false,

            'has_contracts_own_write_rights' => ($this->User->HasContractsOwnWriteRights) ? true : false,

            'has_equity_rights' => ($this->User->HasEquityRights) ? true : false,

            'has_subsidy_rights' => ($this->User->HasSubsidyRights) ? true : false,
            'has_subsidy_rights_rw' => ($this->User->HasSubsidyRightsRW) ? true : false,

            'has_agro_rights' => ($this->User->HasAgroRights) ? true : false,
            'has_agro_rights_rw' => ($this->User->HasAgroRightsRW) ? true : false,

            'has_collections_rights' => ($this->User->HasCollectionsRights) ? true : false,
            'has_collections_rights_rw' => ($this->User->HasCollectionsRightsRW) ? true : false,

            'has_sales_contracts_rights_r' => ($this->User->HasSalesContractsRightsR) ? true : false,
            'has_sales_contracts_rights_rw' => ($this->User->HasSalesContractsRightsRW) ? true : false,

            'has_geoscan_map_rights' => ($this->User->HasGeoscanMapRights) ? true : false,
            'has_dashboard_rights' => ($this->User->HasDashboardRights) ? true : false,
            'has_warehouse_rights' => ($this->User->HasWarehouseRights) ? true : false,
            'has_warehouse_admin_rights' => ($this->User->HasWarehouseAdminRights) ? true : false,
            'has_warehouse_editor_rights' => ($this->User->HasWarehouseEditorRights) ? true : false,
            'has_kvs_cutting_rights' => ($this->User->HasKVSCuttingRights) ? true : false,
            'has_export_mass_payment_rights' => ($this->User->HasExportMassPaymentRights) ? true : false,
            'has_slope_rights' => ($this->User->HasSlopeRights) ? true : false,
            'has_cadastre_rights' => ($this->User->HasCadastreRights) ? true : false,
        ];
    }

    public function getFarmings(?int $userId = null): iterable
    {
        $farmingCombobox = new FarmingCombobox($this->rpcServer);

        if ($userId) {
            $farmings = $farmingCombobox->getFarmingType(['record_all' => true]);
            $provider = new ObjectPermissionsProvider();
            $userFarmingPermissionIds = array_map(function ($permission) {
                return $permission->object_id;
            }, $provider->getUserClassPermissions(UserFarmings::class, $userId, [ObjectPermissions::PERMISSION_WRITE]));

            if (count($farmings) - 1 <= count($userFarmingPermissionIds)) {
                $farmings[0]['selected'] = true;
            } else {
                foreach ($farmings as &$farming) {
                    if (in_array($farming['id'], $userFarmingPermissionIds)) {
                        $farming['selected'] = true;
                    }
                }
            }
        } else {
            $farmings = $farmingCombobox->getFarmingType(['record_all' => true, 'selected' => true]);
        }

        return $farmings;
    }

    /**
     * @api-method markForEdit
     *
     * @param int $rpcParam
     *
     * @throws MTRpcException -33308 CANNOT_MODIFY_NON_GROUP_USER
     * @throws MTRpcException -33309 CANNOT_MODIFY_OWN_USER_DATA
     * @throws MTRpcException -33310 NON_EXISTING_USER_ID
     * @throws MTRpcException -33312 MODIFY_PERMISSIONS_DENIAL
     *
     * @return array
     */
    public function markForEdit($rpcParam)
    {
        $UsersController = new UsersController('Users');

        $id = $rpcParam;
        $query_result = $UsersController->getUserDataById($id);

        // Check if the requested user for deletion exists
        if (!$query_result) {
            throw new MTRpcException('NON_EXISTING_USER_ID', -33310);
        }

        if ($this->User->UserID == $query_result['id']) {
            throw new MTRpcException('CANNOT_MODIFY_OWN_USER_DATA', -33309);
        }

        $modifyRules = array_key_exists($this->User->UserLevel, Config::$MODIFY_PERMISSIONS) ? Config::$MODIFY_PERMISSIONS[$this->User->UserLevel] : [];

        // Logged user's rights
        $canModifyTarget = $modifyRules ? in_array($query_result['level'], $modifyRules) : true;
        $hasSupportRights = in_array($this->User->UserLevel, Config::$USERS_WITH_SUPPORT_PERMISSIONS);

        // Target user's rights
        $targetHasSupportRights = in_array($query_result['level'], Config::$USERS_WITH_SUPPORT_PERMISSIONS);

        // Check if we have rules for modifying the requested user
        if (!$this->User->IsSuperAdmin && !$canModifyTarget) {
            throw new MTRpcException('MODIFY_PERMISSIONS_DENIAL', -33312);
        }

        // Check if the logged user can modify the requested user
        if (!$this->User->IsSuperAdmin && !$hasSupportRights && !$canModifyTarget && $this->User->UserID != $query_result['group_id']) {
            throw new MTRpcException('CANNOT_MODIFY_NON_GROUP_USER', -33308);
        }

        if (!$this->User->IsSuperAdmin && !$targetHasSupportRights && !$canModifyTarget) {
            throw new MTRpcException('CANNOT_MODIFY_NON_GROUP_USER', -33308);
        }

        $rights_results = $UsersController->getUserRightsByUserID($id);
        $rights_array = [];
        $rightsCount = count($rights_results);
        // converting the data rows into array
        if (0 != $rightsCount) {
            for ($i = 0; $i < $rightsCount; $i++) {
                $rights_array[] = $rights_results[$i]['right_id'];
            }
        }

        if (!$this->User->IsSuperAdmin && Config::USERS_SUPPORT_FLAG != $this->User->UserLevel) {
            $data['userInfo']['address'] = $query_result['address'];
            $data['userInfo']['comment'] = $query_result['comment'];
            $data['userInfo']['email'] = $query_result['email'];
            $data['userInfo']['group_id'] = $query_result['group_id'];
            $data['userInfo']['id'] = $query_result['id'];
            $data['userInfo']['level'] = $query_result['level'];
            $data['userInfo']['map_type'] = $query_result['map_type'];
            $data['userInfo']['name'] = $query_result['name'];
            $data['userInfo']['parent_id'] = $query_result['parent_id'];
            $data['userInfo']['phone'] = $query_result['phone'];
            $data['userInfo']['username'] = $query_result['username'];
        } else {
            $data['userInfo'] = $query_result;
            $data['userInfo']['start_date'] = strftime('%d.%m.%Y', strtotime($data['userInfo']['start_date']));
            $data['userInfo']['due_date'] = strftime('%d.%m.%Y', strtotime($data['userInfo']['due_date']));
        }

        $data['userInfo']['paid_support'] = $GLOBALS['Farming']['years'][10]['id'];
        if ($query_result['paid_support']) {
            $paidSupportYear = DateTime::createFromFormat('Y-m-d', $query_result['paid_support'])->format('Y');

            foreach ($GLOBALS['Farming']['years'] as $year_id => $value) {
                if ($paidSupportYear == $value['year']) {
                    $data['userInfo']['paid_support'] = $year_id;

                    break;
                }
            }
        }
        $data['userInfo']['paid_support_start_date'] = $query_result['paid_support_start_date'] ? strftime('%d.%m.%Y', strtotime($data['userInfo']['paid_support_start_date'])) : null;
        $data['userInfo']['paid_support_due_date'] = $query_result['paid_support_due_date'] ? strftime('%d.%m.%Y', strtotime($data['userInfo']['paid_support_due_date'])) : null;
        $data['userInfo']['paid_support_allow_dates'] = (bool)($query_result['paid_support_start_date'] && $query_result['paid_support_due_date']);

        // Handle subscription usage
        foreach (Config::$SUBSCRIPTIONS as $key => $val) {
            $data['userInfo']['subscription_usage_' . $key] = null;
        }

        $subscription_levels = $UsersController->getUserSubscriptions($id);
        foreach ($subscription_levels as $subscription) {
            $data['userInfo']['subscription_usage_' . $subscription['subscription_usage_type']] = $subscription['subscription_usage_id'];
        }

        $data['userFarmings'] = $this->getFarmings($id);

        // setting radio button checks
        $data['userRights']['MAP_RIGHTS_R'] = in_array(Config::MAP_RIGHTS_R, $rights_array);
        $data['userRights']['MAP_RIGHTS_RW'] = in_array(Config::MAP_RIGHTS_RW, $rights_array);
        $data['userRights']['THEMATIC_MAPS_RIGHTS_R'] = in_array(Config::THEMATIC_MAPS_RIGHTS_R, $rights_array);
        $data['userRights']['THEMATIC_MAPS_RIGHTS_RW'] = in_array(Config::THEMATIC_MAPS_RIGHTS_RW, $rights_array);
        $data['userRights']['PLOT_RIGHTS_R'] = in_array(Config::PLOT_RIGHTS_R, $rights_array);
        $data['userRights']['PLOT_RIGHTS_RW'] = in_array(Config::PLOT_RIGHTS_RW, $rights_array);
        $data['userRights']['HYPOTHECS_RIGHTS_R'] = in_array(Config::HYPOTHECS_RIGHTS_R, $rights_array);
        $data['userRights']['HYPOTHECS_RIGHTS_RW'] = in_array(Config::HYPOTHECS_RIGHTS_RW, $rights_array);
        $data['userRights']['CONTRACTS_OWN_WRITE_RIGHTS'] = in_array(Config::CONTRACTS_OWN_WRITE_RIGHTS, $rights_array);
        $data['userRights']['SUBSIDY_RIGHTS'] = in_array(Config::SUBSIDY_RIGHTS, $rights_array);
        $data['userRights']['SUBSIDY_RIGHTS_RW'] = in_array(Config::SUBSIDY_RIGHTS_RW, $rights_array);
        $data['userRights']['AGRO_RIGHTS'] = in_array(Config::AGRO_RIGHTS, $rights_array);
        $data['userRights']['AGRO_RIGHTS_RW'] = in_array(Config::AGRO_RIGHTS_RW, $rights_array);
        $data['userRights']['SALES_CONTRACTS_RIGHTS_R'] = in_array(Config::SALES_CONTRACTS_RIGHTS_R, $rights_array);
        $data['userRights']['SALES_CONTRACTS_RIGHTS_RW'] = in_array(Config::SALES_CONTRACTS_RIGHTS_RW, $rights_array);
        $data['userRights']['COLLECTIONS_RIGHTS'] = in_array(Config::COLLECTIONS_RIGHTS, $rights_array);
        $data['userRights']['COLLECTIONS_RIGHTS_RW'] = in_array(Config::COLLECTIONS_RIGHTS_RW, $rights_array);
        $data['userRights']['EQUITY_RIGHTS'] = in_array(Config::EQUITY_RIGHTS, $rights_array);
        $data['userRights']['DASHBOARD_RIGHTS'] = in_array(Config::DASHBOARD_RIGHTS, $rights_array);
        $data['userRights']['WAREHOUSE_USER_RIGHTS'] = in_array(Config::WAREHOUSE_USER_RIGHTS, $rights_array);
        $data['userRights']['WAREHOUSE_ADMIN_RIGHTS'] = in_array(Config::WAREHOUSE_ADMIN_RIGHTS, $rights_array);
        $data['userRights']['WAREHOUSE_EDITOR_RIGHTS'] = in_array(Config::WAREHOUSE_EDITOR_RIGHTS, $rights_array);
        $data['userRights']['KVS_CUTTING_RIGHTS'] = in_array(Config::KVS_CUTTING_RIGHTS, $rights_array);
        $data['userRights']['EXPORT_MASS_PAYMENT_RIGHTS'] = in_array(Config::EXPORT_MASS_PAYMENT_RIGHTS, $rights_array);
        $data['userRights']['SLOPE_RIGHTS'] = in_array(Config::SLOPE_RIGHTS, $rights_array);
        $data['userRights']['CADASTRE_RIGHTS'] = in_array(Config::CADASTRE_RIGHTS, $rights_array);

        return $data;
    }

    /**
     * Редакция на избрания потребител.
     *
     * @api-method editUser
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer editUserID
     *                         #item string  username
     *                         #item timestamp account_date_limit
     *                         #item timestamp account_start_date
     *                         #item timestamp account_due_date
     *                         #item string password
     *                         #item string email
     *                         #item string name
     *                         #item string address
     *                         #item string phone
     *                         #item string comment
     *                         #item string sub_user_count_rights
     *                         #item string account_allowed_farmigns
     *                         #item boolean entry_flag
     *                         #item boolean account_is_trial
     *                         #item integer salesperson_id
     *                         #item integer account_entries
     *                         #item boolean isOperator
     *                         #item boolean AGRO_RIGHTS
     *                         #item boolean AGRO_RIGHTS_RW
     *                         #item boolean CONTRACTS_OWN_WRITE_RIGHTS
     *                         #item boolean EQUITY_RIGHTS
     *                         #item boolean HYPOTHECS_RIGHTS_R
     *                         #item boolean HYPOTHECS_RIGHTS_RW
     *                         #item boolean MAP_RIGHTS_R
     *                         #item boolean MAP_RIGHTS_RW
     *                         #item boolean PLOT_RIGHTS_R
     *                         #item boolean PLOT_RIGHTS_RW
     *                         #item boolean SALES_CONTRACTS_RIGHTS_R
     *                         #item boolean SALES_CONTRACTS_RIGHTS_RW
     *                         #item boolean SUBSIDY_RIGHTS
     *                         #item boolean SUBSIDY_RIGHTS_RW
     *                         #item string user_server_config
     *                         }
     *
     * @throws MTRpcException -33308 CANNOT_MODIFY_NON_GROUP_USER
     * @throws MTRpcException -33309 CANNOT_MODIFY_OWN_USER_DATA
     * @throws MTRpcException -33310 NON_EXISTING_USER_ID
     */
    public function editUser($rpcParams)
    {
        $UsersController = new UsersController('Users');
        // Get the user data for the selected ID
        if ($this->User->UserID == $rpcParams['editUserID']) {
            unset($rpcParams['level']);
        } else {
            try {
                $rpcParams['level'] = $this->mapUserRolelevel($rpcParams['role']);
            } catch (MTRpcException $ex) {
                return ['msg' => 'User role not integrated in TF'];
            }
        }
        $original_data = $UsersController->getUserDataByUsername($rpcParams['username']);

        // Check if the requested user for deletion exists
        if (!$original_data) {
            throw new MTRpcException('NON_EXISTING_USER_ID', -33310);
        }

        $rpcParams['editUserID'] = $original_data['id'];

        // Check if the logged user can modify the requested user
        if (!in_array($this->User->UserLevel, [Config::USERS_SUPER_ADMIN_FLAG, Config::USERS_ADMIN_FLAG, Config::USERS_SUPPORT_FLAG, Config::USERS_SALES_FLAG], true)) {
            throw new MTRpcException('CANNOT_MODIFY_NON_GROUP_USER', -33308);
        }

        if ($rpcParams['active'] != $original_data['active']) {
            // change only user active status and exit function
            return $this->changeActiveStatus($rpcParams['active'], $original_data['id']);
        }

        $rpcParams['permissions'] = $this->mapPermissions($rpcParams);

        $fields = [];

        if (trim($rpcParams['password'])) {
            $fields['password'] = crypt($rpcParams['password']);
        }

        $fields['email'] = $rpcParams['email'];
        $fields['name'] = $rpcParams['name'];
        $fields['address'] = $rpcParams['address'];
        $fields['phone'] = $rpcParams['phone'];
        $fields['comment'] = $rpcParams['comment'];

        if ($this->User->isSuperAdmin || Config::USERS_SUPPORT_FLAG == $this->User->UserLevel) {
            $fields['salesperson_id'] = $rpcParams['salesperson_id'];
            $fields['can_create'] = $rpcParams['sub_user_count_rights'] ? $rpcParams['sub_user_count_rights'] : $original_data['can_create'];
            $fields['allowed_farmings'] = $rpcParams['account_allowed_farmigns'] ? $rpcParams['account_allowed_farmigns'] : $original_data['allowed_farmings'];
            $fields['date_flag'] = ($rpcParams['account_date_limit']) ? true : false;

            if (true == $rpcParams['account_date_limit']) {
                $fields['start_date'] = $rpcParams['account_start_date'];
                $fields['due_date'] = $rpcParams['account_due_date'];
            }

            $fields['entry_flag'] = ($rpcParams['entry_flag']) ? true : false;
            $fields['entries_left'] = ($rpcParams['entry_flag']) ? (int)$rpcParams['account_entries'] : 0;

            if ($this->User->isSuperAdmin) {
                $fields['level'] = ($rpcParams['level']) ? $rpcParams['level'] : Config::USERS_ADMIN_FLAG;
            }
        }

        if (array_key_exists('userFarmings', $rpcParams) && is_array($rpcParams['userFarmings'])) {
            try {
                $userFarmingIds = $rpcParams['userFarmings'];
                $this->User->updateUserFarmingPermissions($userFarmingIds, (int) $rpcParams['editUserID'], ObjectPermissions::$permisionsMap);
            } catch (Exception $ex) {
                throw new Exception($ex->getMessage());
            }
        }

        $success = $UsersController->editUser($rpcParams['editUserID'], $fields);

        // adding the new user rights
        if ($success) {
            $UsersController->deleteUserRightsByUserID($rpcParams['editUserID']);
            $this->addUserRights($rpcParams);

            // update user system table if new password is set
            if ('' != trim($rpcParams['password'])) {
                $UsersController->updateUserSystemPassword($rpcParams['editUserID'], trim($rpcParams['password']));
            }
        }

        // if server has changed we should change the server info for the whole group
        // if user is inactive the whole group should be deactivated
        $options = [
            'mainData' => [
                'is_trial' => ($rpcParams['account_is_trial']) ? true : false,
                'salesperson_id' => $rpcParams['salesperson_id'],
            ],
            'where' => [
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'value' => $rpcParams['editUserID']],
            ],
        ];

        // Only admin acc can set if the user have paid support.
        if (Config::USERS_SUPER_ADMIN_FLAG == $this->User->UserLevel || Config::USERS_SUPPORT_FLAG == $this->User->UserLevel) {
            $options['mainData']['paid_support'] = $this->getFullYearFromIndex($rpcParams['paid_support']);
            $options['mainData']['paid_support_start_date'] = $rpcParams['paid_support_start_date'] ?: null;
            $options['mainData']['paid_support_due_date'] = $rpcParams['paid_support_due_date'] ?: null;
        }
        // update the new server data
        $UsersController->updateUsersData($options);

        if ($rpcParams['subscriptions']) {
            $UsersController->setUserSubscriptions($rpcParams['editUserID'], $rpcParams['subscriptions']);
        }

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $original_data, $rpcParams, 'Editing User');
    }

    /**
     * @param [type] $rpcParams
     */
    public function updateOrganization($rpcParams)
    {
        if (!in_array($this->User->UserLevel, [Config::USERS_SUPER_ADMIN_FLAG, Config::USERS_ADMIN_FLAG, Config::USERS_SUPPORT_FLAG, Config::USERS_SALES_FLAG], true)) {
            throw new MTRpcException('CANNOT_MODIFY_NON_GROUP_USER', -33308);
        }

        if ($this->checkIdentityNumberExists($rpcParams['organizationIdentityNumber'])) {
            throw new Exception('This identity number is already taken by another organization.', 409);
        }

        try {
            $user = User::finder()->find(
                'username = :name',
                [':name' => $rpcParams['countryCode'] . '_' . $rpcParams['organizationId']]
            );

            if (!$user) {
                throw new Exception('No organization found', 404);
            }

            $user->identity_number = $rpcParams['organizationIdentityNumber'];
            $user->name = $rpcParams['name'];
            $user->save();
        } catch (Exception $ex) {
            throw $ex;
        }
    }

    public function manageUserOrganizationRelation($rpcParams)
    {
        // Check if the logged user can modify the requested user
        if (!in_array($this->User->UserLevel, [Config::USERS_SUPER_ADMIN_FLAG, Config::USERS_ADMIN_FLAG, Config::USERS_SUPPORT_FLAG, Config::USERS_SALES_FLAG], true)) {
            throw new MTRpcException('CANNOT_MODIFY_NON_GROUP_USER', -33308);
        }

        $action = $rpcParams['action'];
        $username = $rpcParams['username'];

        $user = User::finder()->find(
            'username = :name',
            [':name' => $username]
        );

        if (!$user) {
            return ['validate' => 'User not integrated in TF'];
        }

        $organization = $this->userProvider->getOrganization($rpcParams['countryCode'], $rpcParams['organizationId']);

        if (!$organization) {
            throw new Exception('Organization not found');
        }

        if (self::DETACH_ORGANIZATION_ACTION == $action) {
            $this->detachUserOrganization($user, $organization);

            return;
        }

        $this->attachUserOrganization($user, $organization);
    }

    /**
     * @param [array] $rpcParams
     */
    public function setChosenOrganization($rpcParams): void
    {
        $organization = $this->userProvider->getOrganization($rpcParams['countryCode'], $rpcParams['organizationId']);

        if (!$organization) {
            throw new Exception('Organization not found');
        }

        $UsersController = new UsersController();
        $userRights = $UsersController->getUserOrganizationRights($this->User->UserID, $organization->getId());

        if (empty($userRights)) {
            throw new MTRpcException('No organization rights', 403);
        }

        try {
            $currentUser = User::finder()->findByPk($this->User->getUserID());
            $currentUser->database = $organization->getDatabase();
            $currentUser->group_id = $organization->getId();
            $currentUser->parent_id = $organization->getId();
            $currentUser->save();

            $authModule = Prado::getApplication()->getModule(Config::AUTH_MODULE);
            $userManager = $authModule->getUserManager();
            $IUser = $userManager->getUser($currentUser->username);

            Prado::getApplication()->getModule(Config::AUTH_MODULE)->regenerateSessionUser($IUser, $this->User->getAccessToken());
        } catch (Exception $ex) {
            throw $ex;
        }
    }

    public function getUserPermissions($rpcParams)
    {
        if (!$this->User->IsSuperAdmin) {
            throw new MTRpcException('NO_RIGHTS', 499);
        }

        $UsersController = new UsersController();

        return array_map(function ($permission) {
            $permission['rights'] = json_decode($permission['rights'], true);

            if ($pos = strripos($permission['organizationId'], '_')) {
                $permission['organizationId'] = substr($permission['organizationId'], $pos + 1);

                return $permission;
            }
        }, $UsersController->getUserRights($rpcParams['username']));
    }

    public function addFarm($rpcParams)
    {
        $organization = $this->userProvider->getOrganization($rpcParams['countryCode'], $rpcParams['organizationId']);

        if (!$organization) {
            throw new MTRpcException('No organization found', 400);
        }

        $existingFarm = $this->farmProvider->findBy(
            [
                'name' => $rpcParams['name'],
                'user_id' => $organization->id,
            ]
        );

        if ($existingFarm) {
            throw new MTRpcException('Farm with same name exist', 400);
        }

        try {
            $farmingId = $this->insertFarm($organization->id, $rpcParams);
            [$subUser] = $organization->getSubUsers();

            if (isset($subUser)) {
                LayerStyles::addUserLayersStyles($organization->id, $subUser->id);
            }

            $this->regenerateMapFiles($organization->id, $organization->username);

            return $farmingId;
        } catch (Exception $ex) {
            throw $ex;
        }
    }

    public function editFarm($rpcParams)
    {
        if (!in_array($this->User->UserLevel, [Config::USERS_SUPER_ADMIN_FLAG, Config::USERS_ADMIN_FLAG, Config::USERS_SUPPORT_FLAG, Config::USERS_SALES_FLAG], true)) {
            throw new MTRpcException('NO_FARMING_EDIT_PERMISSIONS', -33045);
        }

        $organization = $this->userProvider->getOrganization($rpcParams['countryCode'], $rpcParams['organizationId']);

        if (!$organization) {
            throw new Exception('No organization found', 404);
        }

        $farm = $this->farmProvider->findBy(['uuid' => $rpcParams['uuid']]);

        if (!$farm) {
            throw new Exception('No farm found', 404);
        }

        $existingFarm = $this->farmProvider->findBy(
            [
                'name' => $rpcParams['name'],
                'user_id' => $organization->getId(),
            ]
        );

        if ($existingFarm && $existingFarm->uuid !== $rpcParams['uuid']) {
            throw new MTRpcException('Farm with same name exist', 400);
        }

        $updatableProps = [
            'name',
            'address',
            'company',
            'company_address',
            'bulstat',
            'mol',
            'mol_egn',
            'farming_mol_phone',
            'iban_arr',
            'post_payment_fields',
            'company_ekatte',
        ];

        $updateParams = array_filter($rpcParams, function ($prop) use ($updatableProps) {
            return in_array($prop, $updatableProps);
        }, ARRAY_FILTER_USE_KEY);

        if (array_key_exists('iban_arr', $updateParams)) {
            $updateParams['iban_arr'] = json_encode($updateParams['iban_arr'] ?? []);
        }

        if (array_key_exists('post_payment_fields', $updateParams)) {
            $updateParams['post_payment_fields'] = json_encode($updateParams['post_payment_fields'] ?? []);
        }

        $farm->copyFrom($updateParams);
        $farm->save();
    }

    public function deleteFarm($rpcParams)
    {
        if (Config::USERS_SUPER_ADMIN_FLAG != $this->User->UserLevel) {
            throw new MTRpcException('NO_FARMING_DELETE_PERMISSIONS', -33045);
        }

        $farm = $this->farmProvider->findBy(['uuid' => $rpcParams['uuid']]);

        if ($farm) {
            if (count($this->getFarmContracts($farm)) > 0) {
                throw new MTRpcException('NO_FARMING_DELETE_PERMISSIONS_DUE_CONTRACTS_EXISTENCE', -33046);
            }

            $this->User->updateFarmingUsersPermissions($farm->getId(), [], ObjectPermissions::$permisionsMap);

            $farm->delete();

            $UsersController = new UsersController();
            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, [], $farm->toArray(), 'Delete farming');
        }
    }

    public function manageFarmAccess(array $rpcParams): void
    {
        if (!in_array($this->User->UserLevel, [Config::USERS_SUPER_ADMIN_FLAG, Config::USERS_ADMIN_FLAG, Config::USERS_SUPPORT_FLAG, Config::USERS_SALES_FLAG], true)) {
            throw new MTRpcException('NO_FARMING_EDIT_PERMISSIONS', -33045);
        }

        $user = $this->userProvider->getFinder()->find('keycloak_uid = ?', [$rpcParams['keycloakUid']]);

        foreach ($rpcParams['farms'] as $farmData) {
            $farm = $this->farmProvider->findBy(['uuid' => $farmData['uuid']]);

            if (false == $farmData['isAttached']) {
                $this->User->revokeUserObjectPermission(UserFarmings::class, $user->getId(), $farm->getId());
            } else {
                array_walk(ObjectPermissions::$permisionsMap, function ($permissionId) use ($user, $farm) {
                    $this->User->grantUserPermission(UserFarmings::class, $user->getId(), $permissionId, $farm->getId());
                });
            }
        }
    }

    /**
     * @api-method add
     *
     * @param array $rpcParams
     *                         {
     *                         #item string username
     *                         #item string salesperson_id
     *                         #item boolean account_date_limit
     *                         #item timestamp account_start_date
     *                         #item timestamp account_due_date
     *                         #item string password
     *                         #item string email
     *                         #item string name
     *                         #item string address
     *                         #item string phone
     *                         #item string comment
     *                         #item string username
     *                         #item string isOperator
     *                         #item integer id
     *                         #item boolean integer
     *                         #item integer user_server_config
     *                         #item boolean entry_flag
     *                         #item integer account_entries
     *                         #item boolean account_is_trial
     *                         #item integer account_allowed_farmigns
     *                         #item boolean AGRO_RIGHTS
     *                         }
     *
     * @throws MTRpcException -33308 CANNOT_MODIFY_NON_GROUP_USER
     * @throws MTRpcException -33309 CANNOT_MODIFY_OWN_USER_DATA
     * @throws MTRpcException -33310 NON_EXISTING_USER_ID
     *
     * @return array
     */
    public function doAddUser($rpcParams)
    {
        $UsersController = new UsersController('Users');
        $isValidUsername = $this->isValidUsername($rpcParams['username']);

        if (!$isValidUsername) {
            throw new MTRpcException('USERNAME_NOT_VALID', -33017);
        }

        $rpcParams['permissions'] = $this->mapPermissions($rpcParams);

        try {
            $rpcParams['level'] = $this->mapUserRolelevel($rpcParams['role']);
        } catch (MTRpcException $ex) {
            return ['uservalidate' => false, 'can_create' => false, 'user' => null];
        }

        $valid = false;
        $can_create = false;

        if (Config::USERS_SUPPORT_FLAG == $this->User->UserLevel || Config::USERS_SALES_FLAG == $this->User->UserLevel) {
            return ['uservalidate' => true, 'can_create' => false, 'user' => null];
        }

        $valid = true;
        // getting the current user data(the user that creates the account)
        $creator_data = $UsersController->getUserDataById($this->User->UserID);

        if (Config::USERS_SUPER_ADMIN_FLAG == $creator_data['level'] || null == $creator_data['level']) {
            foreach ($rpcParams['organizations'] as $key => $organization) {
                // There is no case to submit more than one organization for creation. Update valid and can create flags
                // We can set user more than one organization and user rights table should be updated
                $identity = $rpcParams['countryCode'] . '_' . $organization['id'];

                if (!$UsersController->getExistUser($identity)) {
                    // add level 2 user account

                    $organizationData = $rpcParams;
                    $organizationData['username'] = $identity;
                    $organizationData['name'] = $organization['name'];
                    $organizationData['password'] = uniqid();
                    $organizationData['identityNumber'] = $organization['identityNumber'];

                    $parentId = $this->createUserAccount($creator_data, $organizationData);
                }

                // change creator from session user to level 2 user (organization)
                $creator_data = isset($parentId)
                                    ? $UsersController->getUserDataById($parentId)
                                    : $UsersController->getUserDataByUsername($identity);

                if (isset($parentId, $rpcParams['permissions']) && empty($rpcParams['permissions'])) {
                    // The child user 3th level are created with read only rights by default.
                    $this->setDefaultUserRights($rpcParams, $parentId);
                }

                if ($UsersController->getExistUser($rpcParams['username'])) {
                    $valid = false;
                } else {
                    $userId = $this->createSubUserAccount($creator_data, $rpcParams);

                    $valid = true;
                    $can_create = true;
                }

                // Organization is created and sub user is created
                if (isset($parentId, $userId)) {
                    LayerStyles::addUserLayersStyles($parentId, $userId);
                }
            }
        }

        return ['uservalidate' => $valid, 'can_create' => $can_create, 'user' => $userId ?? null];
    }

    /**
     * @param [array] $rpcParams
     */
    public function checkUsernameExistence($rpcParams)
    {
        $UsersController = new UsersController('Users');

        return $UsersController->getExistUser($rpcParams['username']);
    }

    /**
     * @param [array] $rpcParams
     */
    public function checkEmailExistence($rpcParams)
    {
        $UsersController = new UsersController('Users');

        return $UsersController->getExistEmail($rpcParams['email']);
    }

    public function getUserDataByUsername($rpcParams)
    {
        if (!$this->User->IsSuperAdmin) {
            throw new MTRpcException('NO_RIGHTS', 499);
        }

        $UsersController = new UsersController('Users');

        return $UsersController->getUserDataByUsername($rpcParams['username']);
    }

    /**
     * @api-method deleteSelectedUsers
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer id
     *                         }
     *
     * @throws MTRpcException -33308 CANNOT_MODIFY_NON_GROUP_USER
     * @throws MTRpcException -33309 CANNOT_MODIFY_OWN_USER_DATA
     * @throws MTRpcException -33310 NON_EXISTING_USER_ID
     * @throws Exception
     */
    public function deleteSelectedUsers($rpcParams)
    {
        if (Config::USERS_SUPPORT_FLAG == $this->User->UserLevel) {
            throw new MTRpcException('TF.Rpc.ExceptionsList.NO_RIGHTS', 499);
        }

        $UsersController = new UsersController('Users');
        $data = $rpcParams;
        $dataCount = count($data);
        $deletedUsers = [];
        for ($i = 0; $i < $dataCount; $i++) {
            // Get all the data for the selected userID
            $deletedUserData = $UsersController->getUserDataById($data[$i]);
            $deletedUsers[] = $deletedUserData;
            // Check if the requested user for deletion exists
            if (!$deletedUserData) {
                throw new MTRpcException('NON_EXISTING_USER_ID', -33309, $data[$i]);
            }
            if ($this->User->UserID == $data[$i]) {
                throw new MTRpcException('CANNOT_MODIFY_OWN_USER_DATA', -33309);
            }
            // Logged user is not superAdmin
            if (!$this->User->IsSuperAdmin) {
                // Check to see if the deleted user is from the same group as the logged user
                if ($this->User->UserID == $deletedUserData['group_id']) {
                    // if the user is sub-account -> add the ID for deletion
                    $arrayID[] = $data[$i];
                } else {
                    // If the user is not superAdmin and the deleted user is not for the logged user group
                    // throw exception
                    throw new MTRpcException('CANNOT_MODIFY_NON_GROUP_USER', -33308, $data[$i]);
                }
            } else {
                // If the user is superAdmin - add the id to the deletion array.
                $arrayID[] = $data[$i];
            }
        }

        if (!count($arrayID)) {
            return;
        }

        $this->revokeUserPermissions($arrayID);
        $UsersController->deleteUsers($arrayID);
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $deletedUsers, $rpcParams, 'Deleting Users');
    }

    /**
     * Returns user last login date and time.
     *
     * @param int $userId
     *
     * @throws MTRpcException -33310 NON_EXISTING_USER_ID
     *
     * @return array {
     *               #item string last_login_ip
     *               #item datetime last_login_date
     *               }
     */
    public function getLastLoginInfo($userId)
    {
        $UsersController = new UsersController('Users');
        $userData = $UsersController->getUserDataById($userId);

        if (!$userData) {
            throw new MTRpcException('NON_EXISTING_USER_ID', -33310, $userId);
        }

        return [
            'last_login_ip' => $userData['last_login_ip'],
            'last_login_date' => strftime('%d.%m.%Y %H:%M:%S', strtotime($userData['last_login_date'])),
        ];
    }

    public function getUsernames($username)
    {
        $UsersController = new UsersController('Users');

        if ($this->User->isGuest || 3 == $this->User->UserLevel) {
            return [];
        }

        $options = [
            'tablename' => $UsersController->DbHandler->tableName,
            'return' => ['username'],
            'where' => [
                'username' => ['column' => 'username', 'compare' => 'ILIKE', 'value' => $username],
            ],
        ];

        if (Config::USERS_SUPER_ADMIN_FLAG != $this->User->UserLevel && Config::USERS_SUPPORT_FLAG != $this->User->UserLevel) {
            $options['where']['group_id'] = ['column' => 'group_id', 'compare' => '=', 'value' => $this->User->GroupID];
        }

        return $UsersController->getItemsByParams($options);
    }

    public function getNames($names)
    {
        $UsersController = new UsersController('Users');

        if ($this->User->isGuest || 3 == $this->User->UserLevel) {
            return [];
        }

        $options = [
            'tablename' => $UsersController->DbHandler->tableName,
            'return' => ['name'],
            'where' => [
                'name' => ['column' => 'name', 'compare' => 'ILIKE', 'value' => $names],
            ],
        ];

        if (Config::USERS_SUPER_ADMIN_FLAG != $this->User->UserLevel && Config::USERS_SUPPORT_FLAG != $this->User->UserLevel) {
            $options['where']['group_id'] = ['column' => 'group_id', 'compare' => '=', 'value' => $this->User->GroupID];
        }

        return $UsersController->getItemsByParams($options);
    }

    public function getEmails($email)
    {
        $UsersController = new UsersController('Users');

        if ($this->User->isGuest || 3 == $this->User->UserLevel) {
            return [];
        }

        $options = [
            'tablename' => $UsersController->DbHandler->tableName,
            'return' => ['email'],
            'where' => [
                'email' => ['column' => 'email', 'compare' => 'ILIKE', 'value' => $email],
            ],
        ];

        if (Config::USERS_SUPER_ADMIN_FLAG != $this->User->UserLevel && Config::USERS_SUPPORT_FLAG != $this->User->UserLevel) {
            $options['where']['group_id'] = ['column' => 'group_id', 'compare' => '=', 'value' => $this->User->GroupID];
        }

        return $UsersController->getItemsByParams($options);
    }

    public function loginAsUser($username)
    {
        $UsersController = new UsersController('Users');
        $auth = $this->getApplication()->getModule('auth');
        $postData = $this->getApplication()->getRequest()->getCookies();
        $loggedUser = $UsersController->getUserDataById($this->User->UserID);
        $loginHashFromCookie = $postData->findCookieByName($auth->getUserKey() . '_login_hash')->getValue();
        $loginHashFromDB = $loggedUser['login_token'];

        $reconstructedLoginHashFromDb = sha1($loginHashFromDB . getenv('APP_UNIQUE_KEY'));

        if (!$loginHashFromCookie || !$loginHashFromDB || !$reconstructedLoginHashFromDb) {
            throw new MTRpcException('MUST_BE_LOGGED_ON', -33852);
        }

        if ($loginHashFromCookie !== $reconstructedLoginHashFromDb) {
            throw new MTRpcException('MUST_BE_LOGGED_ON', -33852);
        }
        if ($this->User->isGuest) {
            throw new MTRpcException('MUST_BE_LOGGED_ON', -33852);
        }

        $requestedUser = $UsersController->getUserData($username);

        if ($UsersController->allowLoginAs($loggedUser, $requestedUser)) {
            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $requestedUser, null, 'Logging in (switch user)');
            $auth->switchUser($username);

            return 200;
        }

        throw new MTRpcException('POLICY_FORBIDDEN_LOGIN_AS', -33853);
    }

    public function getUserModems($rpcParams)
    {
        $UsersController = new UsersController('Users');
        $options = [
            'where' => [
                'userId' => ['column' => 'user_id', 'compare' => '=', 'value' => $rpcParams['user_id']],
            ],
        ];

        $result = $UsersController->getUserModems($options);

        $return['total'] = count($result);
        $return['rows'] = $result;

        return $return;
    }

    public function getAllModems()
    {
        $UsersController = new UsersController('Users');
        $data = [];
        $result = $UsersController->getAllModems($data);
        $return['total'] = count($result);
        $return['rows'] = $result;

        return $return;
    }

    public function addUserModems($rpcParams)
    {
        $UsersController = new UsersController('Users');
        if ($rpcParams) {
            $UsersController->addUserModems($rpcParams['data'], $rpcParams['id']);
        }

        return;
    }

    public function removeUserModem($rpcParams)
    {
        $UsersController = new UsersController('Users');
        if ($rpcParams) {
            $UsersController->removeUserModem($rpcParams);
        }

        return;
    }

    public function getModemFiles($rpcParams)
    {
        $UsersController = new UsersController('Users');
        $result = $UsersController->getModemFiles($rpcParams);
        $return['total'] = count($result);
        $return['rows'] = $result;

        return $return;
    }

    public function getUserSubscriptions($rpcParams)
    {
        $UsersController = new UsersController('Users');
        $result = $UsersController->getUserSubscriptions($rpcParams);
        $return['total'] = count($result);
        $return['rows'] = $result;

        return $return;
    }

    public function setUserSubscriptions($rpcParams)
    {
        $UsersController = new UsersController('Users');
        if ($rpcParams) {
            $UsersController->setUserSubscriptions($rpcParams['id'], $rpcParams['subscriptions']);
        }

        return;
    }

    public function getAllSubscriptions()
    {
        $UsersController = new UsersController('Users');
        $subscriptions = [];
        $subscriptionTypes = array_keys(Config::$SUBSCRIPTIONS);

        foreach ($subscriptionTypes as $type) {
            $subscriptions[$type] = $UsersController->getSubscriptionsByType($type);
        }

        return $subscriptions;
    }

    /**
     * @param array $params [string db, string ekatte]
     *
     * @throws TDbException
     *
     * @return int
     */
    public function getEkatteContractsCount($params)
    {
        $userDbController = new UserDbController($params['db']);
        $contracts = $userDbController->getEkatteContracts($params['ekatte']);

        return count($contracts);
    }

    /**
     * @param array $params [string db, string ekatte]
     *
     * @throws MTRpcException
     * @throws TDbException
     *
     * @return string
     */
    public function deleteEkatte($params)
    {
        // Check if we have rules for modifying the requested user
        if (!$this->User->IsSuperAdmin) {
            throw new MTRpcException('MODIFY_PERMISSIONS_DENIAL', -33312);
        }

        $userDbController = new UserDbController($params['db']);

        return $userDbController->deleteEkatte($params['ekatte']);
    }

    /**
     * @param array $params
     *
     * @return array
     */
    public function exportToExcelUsersData($params)
    {
        $data = [];
        $users = $this->getUserMainGridData($params);
        $columns = [
            'username' => 'Потребител',
            'name' => 'Име',
            'email' => 'Email',
            'level' => 'Тип',
            'active' => 'Статус',
            'is_trial' => 'Временен',
            'start_date' => 'Начална дата',
            'due_date' => 'Крайна дата',
            'creation_date' => 'Дата на създаване',
            'paid_support' => 'ГАП',
            'paid_support_due_date' => 'Платен до',
            'parent_username' => 'Родителски акаунт',
            'ekatte_count' => 'Брой землища',
            'category_map' => 'Категория карта',
            'user_rights' => 'Използвани модули',
            'total_plot_area' => 'Обща площ',
            'category_plots' => 'Категория имоти',
        ];

        foreach ($users['rows'] as $user) {
            $formattedUser = [];
            foreach ($columns as $columnKey => $columnLabel) {
                if (in_array($columnKey, array_keys($user))) {
                    if ('is_trial' === $columnKey) {
                        $user[$columnKey] = $user[$columnKey] ? 'Да' : 'Не';
                    }
                    $formattedUser[$columnKey] = $user[$columnKey];
                }
            }
            $data[] = $formattedUser;
        }
        $fileName = 'export_users_' . time() . '.xlsx';
        $path = PUBLIC_UPLOAD_EXPORT . '/' . $this->User->GroupID . '/' . $fileName;

        $export2Xls = new Export2XlsClass();
        $filePath = $export2Xls->exportUrlPath($path, $data, $columns);

        return [
            'file_path' => $filePath,
            'file_name' => $fileName,
        ];
    }

    private function addUserRights($rpcParams)
    {
        $userId = $rpcParams['editUserID'];
        $UsersController = new UsersController('Users');

        foreach ($rpcParams['permissions'] as $organizationId => $permission) {
            if ($permission['MAP_RIGHTS_R']) {
                $options = [
                    'user_id' => $userId,
                    'group_id' => $organizationId,
                    'right_id' => Config::MAP_RIGHTS_R,
                ];
                $UsersController->addUserRights($options);
            }
            if ($permission['MAP_RIGHTS_RW']) {
                $options = [
                    'user_id' => $userId,
                    'group_id' => $organizationId,
                    'right_id' => Config::MAP_RIGHTS_RW,
                ];
                $UsersController->addUserRights($options);
            }

            if ($permission['THEMATIC_MAPS_RIGHTS_R']) {
                $options = [
                    'user_id' => $userId,
                    'group_id' => $organizationId,
                    'right_id' => Config::THEMATIC_MAPS_RIGHTS_R,
                ];
                $UsersController->addUserRights($options);
            }
            if ($permission['THEMATIC_MAPS_RIGHTS_RW']) {
                $options = [
                    'user_id' => $userId,
                    'group_id' => $organizationId,
                    'right_id' => Config::THEMATIC_MAPS_RIGHTS_RW,
                ];
                $UsersController->addUserRights($options);
            }

            if ($permission['PLOT_RIGHTS_R']) {
                $options = [
                    'user_id' => $userId,
                    'group_id' => $organizationId,
                    'right_id' => Config::PLOT_RIGHTS_R,
                ];
                $UsersController->addUserRights($options);
            }
            if ($permission['PLOT_RIGHTS_RW']) {
                $options = [
                    'user_id' => $userId,
                    'group_id' => $organizationId,
                    'right_id' => Config::PLOT_RIGHTS_RW,
                ];
                $UsersController->addUserRights($options);
            }

            if ($permission['HYPOTHECS_RIGHTS_R']) {
                $options = [
                    'user_id' => $userId,
                    'group_id' => $organizationId,
                    'right_id' => Config::HYPOTHECS_RIGHTS_R,
                ];
                $UsersController->addUserRights($options);
            }
            if ($permission['HYPOTHECS_RIGHTS_RW']) {
                $options = [
                    'user_id' => $userId,
                    'group_id' => $organizationId,
                    'right_id' => Config::HYPOTHECS_RIGHTS_RW,
                ];
                $UsersController->addUserRights($options);
            }

            if ($permission['SUBSIDY_RIGHTS']) {
                $options = [
                    'user_id' => $userId,
                    'group_id' => $organizationId,
                    'right_id' => Config::SUBSIDY_RIGHTS,
                ];
                $UsersController->addUserRights($options);
            }
            if ($permission['SUBSIDY_RIGHTS_RW']) {
                $options = [
                    'user_id' => $userId,
                    'group_id' => $organizationId,
                    'right_id' => Config::SUBSIDY_RIGHTS_RW,
                ];
                $UsersController->addUserRights($options);
            }

            if ($permission['AGRO_RIGHTS']) {
                $options = [
                    'user_id' => $userId,
                    'group_id' => $organizationId,
                    'right_id' => Config::AGRO_RIGHTS,
                ];
                $UsersController->addUserRights($options);
            }
            if ($permission['AGRO_RIGHTS_RW']) {
                $options = [
                    'user_id' => $userId,
                    'group_id' => $organizationId,
                    'right_id' => Config::AGRO_RIGHTS_RW,
                ];
                $UsersController->addUserRights($options);
            }

            if ($permission['SALES_CONTRACTS_RIGHTS_R']) {
                $options = [
                    'user_id' => $userId,
                    'group_id' => $organizationId,
                    'right_id' => Config::SALES_CONTRACTS_RIGHTS_R,
                ];
                $UsersController->addUserRights($options);
            }

            if ($permission['SALES_CONTRACTS_RIGHTS_RW']) {
                $options = [
                    'user_id' => $userId,
                    'group_id' => $organizationId,
                    'right_id' => Config::SALES_CONTRACTS_RIGHTS_RW,
                ];
                $UsersController->addUserRights($options);
            }

            if ($permission['COLLECTIONS_RIGHTS']) {
                $options = [
                    'user_id' => $userId,
                    'group_id' => $organizationId,
                    'right_id' => Config::COLLECTIONS_RIGHTS,
                ];
                $UsersController->addUserRights($options);
            }
            if ($permission['COLLECTIONS_RIGHTS_RW']) {
                $options = [
                    'user_id' => $userId,
                    'group_id' => $organizationId,
                    'right_id' => Config::COLLECTIONS_RIGHTS_RW,
                ];
                $UsersController->addUserRights($options);
            }

            if ($permission['CONTRACTS_OWN_WRITE_RIGHTS']) {
                $options = [
                    'user_id' => $userId,
                    'group_id' => $organizationId,
                    'right_id' => Config::CONTRACTS_OWN_WRITE_RIGHTS,
                ];
                $UsersController->addUserRights($options);
            }

            if ($permission['EQUITY_RIGHTS']) {
                $options = [
                    'user_id' => $userId,
                    'group_id' => $organizationId,
                    'right_id' => Config::EQUITY_RIGHTS,
                ];
                $UsersController->addUserRights($options);
            }

            if ($permission['DASHBOARD_RIGHTS']) {
                $options = [
                    'user_id' => $userId,
                    'group_id' => $organizationId,
                    'right_id' => Config::DASHBOARD_RIGHTS,
                ];
                $UsersController->addUserRights($options);
            }

            if ($permission['WAREHOUSE_USER_RIGHTS']) {
                $options = [
                    'user_id' => $userId,
                    'group_id' => $organizationId,
                    'right_id' => Config::WAREHOUSE_USER_RIGHTS,
                ];
                $UsersController->addUserRights($options);
            }

            if ($permission['WAREHOUSE_ADMIN_RIGHTS']) {
                $options = [
                    'user_id' => $userId,
                    'group_id' => $organizationId,
                    'right_id' => Config::WAREHOUSE_ADMIN_RIGHTS,
                ];
                $UsersController->addUserRights($options);
            }

            if ($permission['WAREHOUSE_EDITOR_RIGHTS']) {
                $options = [
                    'user_id' => $userId,
                    'group_id' => $organizationId,
                    'right_id' => Config::WAREHOUSE_EDITOR_RIGHTS,
                ];
                $UsersController->addUserRights($options);
            }

            if ($permission['KVS_CUTTING_RIGHTS']) {
                $options = [
                    'user_id' => $userId,
                    'group_id' => $organizationId,
                    'right_id' => Config::KVS_CUTTING_RIGHTS,
                ];
                $UsersController->addUserRights($options);
            }

            if ($permission['EXPORT_MASS_PAYMENT_RIGHTS']) {
                $options = [
                    'user_id' => $userId,
                    'group_id' => $organizationId,
                    'right_id' => Config::EXPORT_MASS_PAYMENT_RIGHTS,
                ];
                $UsersController->addUserRights($options);
            }

            if ($permission['SLOPE_RIGHTS']) {
                $options = [
                    'user_id' => $userId,
                    'group_id' => $organizationId,
                    'right_id' => Config::SLOPE_RIGHTS,
                ];
                $UsersController->addUserRights($options);
            }

            if ($permission['CADASTRE_RIGHTS']) {
                $options = [
                    'user_id' => $userId,
                    'group_id' => $organizationId,
                    'right_id' => Config::CADASTRE_RIGHTS,
                ];
                $UsersController->addUserRights($options);
            }
        }
    }

    /**
     * @param [type] $identityNumber
     */
    private function checkIdentityNumberExists($identityNumber): ?User
    {
        return User::finder()->find(
            'identity_number = :identityNumber',
            ['identityNumber' => $identityNumber]
        );
    }

    private function changeActiveStatus($active, $userId)
    {
        try {
            $user = User::finder()->findByPk($userId);
            $user->active = $active;
            $user->save();
        } catch (Exception $ex) {
            throw $ex;
        }
    }

    private function detachUserOrganization(User $user, User $organization): void
    {
        $UsersController = new UsersController();

        try {
            $UsersController->deleteUserRightsByUserID($user->getId(), $organization->getId());
            $this->revokeFarmingPermissions($user->getId(), $organization->getId());

            $user->database = null;
            $user->group_id = null;
            $user->parent_id = null;
            $user->save();
        } catch (Exception $ex) {
            throw $ex;
        }
    }

    private function revokeFarmingPermissions(int $userId, int $parentId): void
    {
        $UsersController = new UsersController();
        $farmings = $UsersController->getOrganizationFarmings($parentId);

        array_walk($farmings, function ($farm) use ($userId) {
            $this->User->revokeUserObjectPermission(UserFarmings::class, $userId, $farm['id']);
        });
    }

    /**
     * @param [array] $userData
     * @param [string] $organizationIdent
     */
    private function attachUserOrganization(User $user, User $organization): void
    {
        try {
            $this->grantUserFarmingsPermissions($user->getId(), $organization->getId());

            $user->database = $organization->getDatabase();
            $user->group_id = $organization->getId();
            $user->parent_id = $organization->getId();
            $user->save();
        } catch (Exception $ex) {
            throw $ex;
        }
    }

    private function getFarmContracts(UserFarmings $farming)
    {
        if (!$farming) {
            return [];
        }

        $userHasFarmPermission = Config::USERS_SUPER_ADMIN_FLAG === $this->User->UserLevel || (int)$farming->user_id === $this->User->GroupID;
        if (!$userHasFarmPermission) {
            throw new MTRpcException('NO_FARMING_READ_PERMISSIONS', -33045);
        }

        $user = User::finder()->find(
            'id = :id',
            [':id' => $farming->user_id]
        );
        if (!$user) {
            return [];
        }

        $UserDbController = new UserDbController($user->Database);

        $options = [
            'tablename' => 'su_contracts c',
            'return' => [
                'c_num, nm_usage_rights, active',
            ],
            'where' => [
                'farming_id' => ['column' => 'c.farming_id', 'compare' => '=', 'value' => $farming->id],
                'nm_usage_rights' => ['column' => 'c.nm_usage_rights', 'compare' => '!=', 'value' => 4],
            ],
        ];

        return $UserDbController->getItemsByParams($options, false);
    }

    private function setDefaultUserRights(&$rpcParams, $parentId)
    {
        foreach ($this->getDefaultUserRights() as $name => $value) {
            $rpcParams['permissions'][$parentId][$name] = $value;
        }
    }

    private function getDefaultUserRights()
    {
        return [
            'MAP_RIGHTS_R' => Config::MAP_RIGHTS_R,
            'PLOT_RIGHTS_R' => Config::PLOT_RIGHTS_R,
        ];
    }

    private function mapUserRolelevel($roleName): int
    {
        switch (true) {
            case in_array($roleName, ['ORGANIZATION_MANAGER', 'ORGANIZATION_MANAGER_N_VRA', 'EMPLOYEE', 'STAFF']):
                $level = Config::USERS_NORMAL;

                break;
            case 'SUPER_ADMIN' == $roleName:
                $level = Config::USERS_SUPER_ADMIN_FLAG;

                break;
            case in_array($roleName, ['SELLER_ADMIN', 'SERVICE']):
                $level = Config::USERS_SALES_FLAG;

                break;
            case in_array($roleName, ['SERVICE_ADMIN']):
                $level = Config::USERS_SUPER_ADMIN_FLAG;

                break;
            default:
                throw new MTRpcException('GEOSCAN_USER_ROLE_NOT_MAPPED', -33047);
        }

        return $level;
    }

    private function mapPermissions($rpcParams)
    {
        $UsersController = new UsersController('Users');

        $permissions = [];
        foreach ($rpcParams['permissions'] as $organization) {
            $identity = ($rpcParams['countryCode'] . '_' . $organization['organizationId']);
            $tfOrganization = $UsersController->getUserDataByUsername($identity);

            $oragnizationId = $tfOrganization['id'];

            if (!$oragnizationId) {
                Sentry::logException(new Exception("Submitted organization with identity {$identity} was not found. Skipping setting permissions."));

                continue;
            }

            foreach ($organization['userPermissions'] as $permission) {
                $permissions[$oragnizationId][$permission['name']] = $permission['value'];
            }
        }

        return $permissions;
    }

    private function isValidUsername($username)
    {
        $pattern = '/^(?=.{3,20}$)(?![_.])(?!.*[_.]{2})[a-zA-Z0-9._]+(?<![_.])$/';

        return (bool) preg_match($pattern, $username);
    }

    /**
     * Validates that created sub user rights will not surpass current user rights.
     *
     * @return bool
     */
    private function canGrantUserRights($rpcParams)
    {
        $UsersController = new UsersController();
        $rightsResults = $UsersController->getUserRightsByUserID($this->User->UserID);
        $userRights = array_column($rightsResults, 'right_id');

        $canGrantRights = true;

        foreach ($rpcParams as $key => $rpcParamValue) {
            if (defined('Config::' . $key)) {
                $constValue = constant('Config::' . $key);

                // constant is defined && constant is related to user rights map
                if ($constValue && array_key_exists($constValue, Config::$USER_RIGHTS_MAP)) {
                    // trying to grant user right to user and current user does not have this right
                    if (true == $rpcParamValue && !in_array($constValue, $userRights)) {
                        $canGrantRights = false;

                        break;
                    }
                }
            }
        }

        return $canGrantRights;
    }

    private function createUserAccount($creator_data, $rpcParams)
    {
        $active = true;

        $UsersController = new UsersController('Users');

        // check if account should be active or inactive at the beginning
        if ($rpcParams['account_date_limit']) {
            // check if start date is >= today
            if ('' != $rpcParams['account_start_date']) {
                $start_date = $rpcParams['account_start_date'];

                if (strtotime($start_date) >= strtotime(date('Y-m-d'))) {
                    $active = true;
                } else {
                    $active = false;
                }
            } else {
                $active = false;
            }

            // check if due date <= today
            if ('' != $rpcParams['account_due_date']) {
                $due_date = $rpcParams['account_due_date'];

                if (strtotime($due_date) >= strtotime(date('Y-m-d'))) {
                    $active = true;
                } else {
                    $active = false;
                }
            } else {
                $active = false;
            }
        }

        // options for user account
        $options = [
            'username' => $rpcParams['username'],
            'password' => crypt($rpcParams['password']),
            'email' => $rpcParams['email'],
            'name' => $rpcParams['name'],
            'address' => $rpcParams['address'],
            'phone' => $rpcParams['phone'],
            'comment' => $rpcParams['comment'],
            'database' => 'db_' . strtolower($rpcParams['username']),
            'is_superadmin' => (Config::USERS_SUPER_ADMIN_FLAG == $rpcParams['level']) ? true : false,
            'parent_id' => $creator_data['id'],
            'can_create' => $rpcParams['sub_user_count_rights'] ? $rpcParams['sub_user_count_rights'] : 1,
            'level' => Config::USERS_ADMIN_FLAG,
            'group_id' => 0,
            'salesperson_id' => $rpcParams['salesperson_id'],
            'paid_support' => $this->getFullYearFromIndex((int)$GLOBALS['Farming']['years'][10]['id']),
            'paid_support_start_date' => $rpcParams['paid_support_start_date'] ?: null,
            'paid_support_due_date' => $rpcParams['paid_support_due_date'] ?: null,
            'date_flag' => ($rpcParams['account_date_limit']) ? true : false,
            'start_date' => ($rpcParams['account_date_limit']) ? $rpcParams['account_start_date'] : null,
            'due_date' => ($rpcParams['account_date_limit']) ? $rpcParams['account_due_date'] : null,
            'entry_flag' => ($rpcParams['entry_flag']) ? true : false,
            'entries_left' => ($rpcParams['entry_flag']) ? (int)$rpcParams['account_entries'] : 0,
            'active' => ($active) ? true : false,
            'is_trial' => ($rpcParams['account_is_trial']) ? true : false,
            'allowed_farmings' => $rpcParams['account_allowed_farmigns'] ? $rpcParams['account_allowed_farmigns'] : 1,
            'creation_date' => date('Y-m-d H:i:s'),
            'identity_number' => $rpcParams['identityNumber'],
        ];

        $user_id = $UsersController->addUser($options);

        if ($rpcParams['subscriptions']) {
            $UsersController->setUserSubscriptions($user_id, $rpcParams['subscriptions']);
        }

        // setting groupID
        $fields['group_id'] = $user_id;
        $UsersController->editUser($user_id, $fields);

        if ($user_id) {
            $user_options = [
                'user_id' => $user_id,
                'username' => $rpcParams['username'],
                'password' => $rpcParams['password'],
            ];
            $UsersController->addUserToSystem($user_options);
            $rpcParams['editUserID'] = $user_id;
            $this->addUserRights($rpcParams);
            $this->createUserDatabaseTables($rpcParams);
            $this->createUserDatabaseViews($rpcParams);

            $this->createGroupLogFile($user_id);

            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options, $user_id, 'Adding new user');
        }

        return $user_id;
    }

    private function grantAllFarmingsToUser(int $userId): void
    {
        $farmingIds = $this->farmProvider->getAllFarmingids();

        try {
            $this->User->updateUserFarmingPermissions($farmingIds, $userId, ObjectPermissions::$permisionsMap);
        } catch (Exception $ex) {
            throw new Exception($ex->getMessage());
        }
    }

    private function createSubUserAccount($creator_data, $rpcParams)
    {
        $UsersController = new UsersController();

        $options = [
            'username' => $rpcParams['username'],
            'password' => crypt($rpcParams['password']),
            'email' => $rpcParams['email'],
            'name' => $rpcParams['name'],
            'address' => $rpcParams['address'],
            'phone' => $rpcParams['phone'],
            'comment' => $rpcParams['comment'],
            'database' => $creator_data['database'],
            'is_superadmin' => false,
            'parent_id' => $creator_data['id'],
            'can_create' => 0,
            'level' => $rpcParams['level'],
            'group_id' => $creator_data['id'],
            'server' => $creator_data['server'],
            'date_flag' => ($creator_data['date_flag']) ? true : false,
            'start_date' => $creator_data['start_date'],
            'due_date' => $creator_data['due_date'],
            'entry_flag' => ($creator_data['entry_flag']) ? true : false,
            'entries_left' => $creator_data['entries_left'],
            'is_trial' => ($creator_data['is_trial']) ? true : false,
            'track_token' => $creator_data['track_token'],
            'creation_date' => date('Y-m-d H:i:s'),
            'paid_support' => $creator_data['paid_support'],
            'keycloak_uid' => $rpcParams['keycloakUid'],
        ];

        $user_id = $UsersController->addSubUser($options);
        if ($user_id) {
            // adding user to system table

            try {
                $user_options = [
                    'user_id' => $user_id,
                    'username' => $rpcParams['username'],
                    'password' => $rpcParams['password'],
                ];

                $UsersController->addUserToSystem($user_options);

                $rpcParams['editUserID'] = $user_id;
                $this->addUserRights($rpcParams);
                $this->grantUserFarmingsPermissions($user_id, $options['group_id']);
            } catch (Exception $ex) {
                throw $ex;
            }

            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options, $user_id, 'Adding Sub-user account');
        }

        return $user_id;
    }

    /**
     * Grant user permissions to farms based on organization .
     *
     * @param [type] $farmings
     * @param [int] $userId
     */
    private function grantUserFarmingsPermissions(int $userId, int $organizationId): void
    {
        $UsersController = new UsersController();
        $farmings = $UsersController->getOrganizationFarmings($organizationId);

        if (empty($farmings)) {
            // parent account has no permissions to Farm
            return;
        }

        try {
            foreach ($farmings as $farm) {
                $farmId = $farm['id'];
                array_walk(ObjectPermissions::$permisionsMap, function ($permissionId) use ($userId, $farmId) {
                    $this->User->grantUserPermission(UserFarmings::class, $userId, $permissionId, $farmId);
                });
            }
        } catch (Exception $ex) {
            throw $ex;
        }
    }

    private function createUserDatabaseTables($rpcParams)
    {
        $organizationId = $rpcParams['editUserID'];

        foreach ($rpcParams['organizations'] as $organization) {
            $kvsFarmId = null;
            foreach ($organization['farms'] as $farm) {
                // add farm and related layers
                $farmId = $this->insertFarm($organizationId, $farm);

                if (null === $kvsFarmId) {
                    $kvsFarmId = $farmId;
                }
            }
            // add organization layers
            $this->createOrganizationLayers($organizationId, $kvsFarmId);
        }

        $this->regenerateMapFiles($organizationId, $rpcParams['username']);
    }

    private function createUserDatabaseViews($rpcParams)
    {
        $organizationId = $rpcParams['editUserID'];
        $organizationDbName = 'db_' . strtolower($rpcParams['username']);
        $UserDbController = new UserDbController($organizationDbName);

        // Recreate user_farming_permissions view
        $UserDbController->dropViewIfExists('user_farming_permissions');
        $UserDbController->createUserFarmingPermissionsView($organizationId);
    }

    private function createOrganizationLayers(int $organizationId, int $farmId)
    {
        $LayersController = new LayersController('Layers');
        $user = User::finder()->find('group_id = :group_id', [':group_id' => $organizationId]);

        if (!$user) {
            throw new Exception("Cannot find user with group_id: {$organizationId}");
        }

        $UserDbController = new UserDbController($user->Database);

        foreach ($GLOBALS['Farming']['years'] as $item) {
            $year = $item['id'];
            // if year is default create layer kvs and gps
            if ($item['default']) {
                $color = $LayersController->StringHelper->randomColorCode();
                // dobavqne na sloi gps danni
                $fields = [];
                $style = [
                    'color' => $color,
                    'border_color' => '111111',
                    'transparency' => 100,
                    'border_only' => false,
                    'label_name' => [null],
                    'tags' => true,
                    'label_size' => $this->default_label_size,
                ];
                $tableName = 'layer_gps';
                $fields['name'] = 'Временни данни';
                $fields['user_id'] = $organizationId;
                $fields['farming'] = null;
                $fields['year'] = $year;
                $fields['extent'] = Config::DEFAULT_MAX_EXTENT;
                $fields['table_name'] = $tableName;
                $fields['layer_type'] = Config::LAYER_TYPE_GPS;
                $fields['color'] = $color;
                $fields['group_id'] = $organizationId;
                $fields['style'] = json_encode($style);
                $fields['is_exist'] = true;
                $settings['mainData'] = $fields;
                $gpsLayerId = $LayersController->addLayerItem($settings);
                $layerGps = UserLayers::getLayerById($gpsLayerId);
                $UserDbController->DbHandler->createLayerMissingColumns($layerGps);

                // dobavqne na sloi KVS
                $fields = [];
                $tableName = 'layer_kvs';
                $fields['name'] = 'КВС имоти';
                $fields['user_id'] = $organizationId;
                $fields['farming'] = null;
                $fields['year'] = $year;
                $fields['extent'] = Config::DEFAULT_MAX_EXTENT;
                $fields['table_name'] = $tableName;
                $fields['layer_type'] = Config::LAYER_TYPE_KVS;
                $fields['color'] = $LayersController->StringHelper->randomColorCode();
                $fields['group_id'] = $organizationId;
                $fields['is_exist'] = true;
                $settings['mainData'] = $fields;
                $kvsLayerId = $LayersController->addLayerItem($settings);
                $layerKvs = UserLayers::getLayerById($kvsLayerId);
                $UserDbController->DbHandler->createLayerMissingColumns($layerKvs);

                // dobavqne na natura 2000
                $fields = [];
                $tableName = 'layer_natura_2000';
                $fields['name'] = 'Натура 2000';
                $fields['user_id'] = $organizationId;
                $fields['farming'] = null;
                $fields['year'] = null;
                $fields['extent'] = Config::DEFAULT_MAX_EXTENT;
                $fields['table_name'] = $tableName;
                $fields['layer_type'] = Config::LAYER_TYPE_NATURA_2000;
                $fields['color'] = 'fafafa';
                $fields['border_color'] = 'A0A000';
                $fields['group_id'] = $organizationId;
                $fields['is_exist'] = true;
                $settings['mainData'] = $fields;
                $LayersController->addLayerItem($settings);

                // dobavqne na pzp
                $fields = [];
                $tableName = 'layer_pzp';
                $fields['name'] = 'Постоянно затревени площи';
                $fields['user_id'] = $organizationId;
                $fields['farming'] = null;
                $fields['year'] = null;
                $fields['extent'] = Config::DEFAULT_MAX_EXTENT;
                $fields['table_name'] = $tableName;
                $fields['layer_type'] = Config::LAYER_TYPE_PERMANETELY_GREEN_AREAS;
                $fields['color'] = 'fafafa';
                $fields['border_color'] = '00f000';
                $fields['group_id'] = $organizationId;
                $fields['is_exist'] = true;
                $settings['mainData'] = $fields;
                $LayersController->addLayerItem($settings);

                $fields = [];
                $fields['layer_type'] = Config::LAYER_TYPE_PERMANENT_GRASSLAND_FOR_MOWING;
                $fields['name'] = 'ПЗП за косене 2024';
                $fields['user_id'] = $organizationId;
                $fields['farming'] = null;
                $fields['year'] = null;
                $fields['extent'] = Config::DEFAULT_MAX_EXTENT;
                $fields['table_name'] = 'layer_permanent_grassland_for_mowing';
                $fields['color'] = '';
                $fields['border_color'] = '00f000';
                $fields['group_id'] = $organizationId;
                $fields['is_exist'] = true;
                $settings['mainData'] = $fields;
                $LayersController->addLayerItem($settings);

                $fields = [];
                $fields['layer_type'] = Config::LAYER_TYPE_LANDSCAPE_ELEMENTS_LINES;
                $fields['name'] = 'Ландшафтни елементи (линейни)';
                $fields['table_name'] = 'layer_landscape_elements_lines';
                $fields['user_id'] = $organizationId;
                $fields['farming'] = null;
                $fields['year'] = null;
                $fields['extent'] = Config::DEFAULT_MAX_EXTENT;
                $fields['color'] = '';
                $fields['border_color'] = 'f51441';
                $fields['group_id'] = $organizationId;
                $fields['is_exist'] = true;
                $settings['mainData'] = $fields;
                $LayersController->addLayerItem($settings);

                $fields = [];
                $fields['layer_type'] = Config::LAYER_TYPE_LANDSCAPE_ELEMENTS_POINTS;
                $fields['name'] = 'Ландшафтни елементи (точки)';
                $fields['table_name'] = 'layer_landscape_elements_points';
                $fields['user_id'] = $organizationId;
                $fields['farming'] = null;
                $fields['year'] = null;
                $fields['extent'] = Config::DEFAULT_MAX_EXTENT;
                $fields['color'] = '';
                $fields['border_color'] = 'f51441';
                $fields['group_id'] = $organizationId;
                $fields['is_exist'] = true;
                $settings['mainData'] = $fields;
                $LayersController->addLayerItem($settings);

                $fields = [];
                $fields['layer_type'] = Config::LAYER_TYPE_LANDSCAPE_ELEMENTS_POLYGONS;
                $fields['name'] = 'Ландшафтни елементи (площи)';
                $fields['table_name'] = 'layer_landscape_elements_polygons';
                $fields['user_id'] = $organizationId;
                $fields['farming'] = null;
                $fields['year'] = null;
                $fields['extent'] = Config::DEFAULT_MAX_EXTENT;
                $fields['color'] = '';
                $fields['border_color'] = 'f51441';
                $fields['group_id'] = $organizationId;
                $fields['is_exist'] = true;
                $settings['mainData'] = $fields;
                $LayersController->addLayerItem($settings);

                $fields = [];
                $fields['layer_type'] = Config::LAYER_TYPE_DS_PRC;
                $fields['name'] = 'Кампания 2024';
                $fields['table_name'] = 'layer_ds_prc';
                $fields['user_id'] = $organizationId;
                $fields['farming'] = null;
                $fields['year'] = null;
                $fields['extent'] = Config::DEFAULT_MAX_EXTENT;
                $fields['color'] = '';
                $fields['border_color'] = 'f51441';
                $fields['group_id'] = $organizationId;
                $fields['is_exist'] = true;
                $settings['mainData'] = $fields;
                $LayersController->addLayerItem($settings);
            }
        }
    }

    private function insertFarm(int $user_id, array $farm): int
    {
        $FarmingController = new FarmingController('Farming');
        $LayersController = new LayersController('Layers');

        $options['mainData'] = [
            'user_id' => $user_id,
            'group_id' => $user_id,
            'uuid' => $farm['uuid'],
            'name' => $farm['name'],
            'address' => $farm['address'],
            'company' => $farm['company'],
            'bulstat' => $farm['bulstat'],
            'company_address' => $farm['company_address'],
            'company_ekatte' => $farm['company_ekatte'],
            'mol' => $farm['mol'],
            'mol_egn' => $farm['mol_egn'],
            'farming_mol_phone' => $farm['farming_mol_phone'],
            'iban_arr' => json_encode($farm['iban_arr'] ?? []),
            'post_payment_fields' => json_encode($farm['post_payment_fields'] ?? []),
        ];

        $farming = $FarmingController->addFarmingItem($options);

        // Grant farm permissions to organization
        array_walk(ObjectPermissions::$permisionsMap, function ($permissionId) use ($user_id, $farming) {
            $this->User->grantUserPermission(UserFarmings::class, $user_id, $permissionId, $farming);
        });

        foreach ($GLOBALS['Farming']['years'] as $item) {
            $year = $item['id'];

            // dobavqne na sloi zemedelski parcel
            $color = $LayersController->StringHelper->randomColorCode();
            $tableName = 'layer_zp_' . time();
            $fields = [];
            $style = [
                'color' => $color,
                'border_color' => '111111',
                'transparency' => 100,
                'border_only' => false,
                'label_name' => [null],
                'tags' => true,
                'label_size' => $this->default_label_size,
            ];
            $fields['name'] = 'Земеделски парцели';
            $fields['user_id'] = $user_id;
            $fields['farming'] = $farming;
            $fields['year'] = $year;
            -$fields['extent'] = Config::DEFAULT_MAX_EXTENT;
            $fields['table_name'] = $tableName;
            $fields['layer_type'] = Config::LAYER_TYPE_ZP;
            $fields['style'] = json_encode($style);
            $fields['color'] = $color;
            $fields['group_id'] = $user_id;
            $settings['mainData'] = $fields;
            $LayersController->addLayerItem($settings);

            // dobavqne na sloi dopustimost za godinite predi 2014 (4 = 2013)
            if ($year <= 4) {
                $color = $LayersController->StringHelper->randomColorCode();
                $tableName = 'layer_dss_' . time();
                $style = [
                    'color' => $color,
                    'border_color' => '111111',
                    'transparency' => 100,
                    'border_only' => false,
                    'label_name' => [null],
                    'tags' => true,
                    'label_size' => $this->default_label_size,
                ];
                $fields['name'] = 'Слой за допустимост';
                $fields['user_id'] = $user_id;
                $fields['farming'] = $farming;
                $fields['year'] = $year;
                $fields['extent'] = Config::DEFAULT_MAX_EXTENT;
                $fields['table_name'] = $tableName;
                $fields['layer_type'] = Config::LAYER_TYPE_DSS;
                $fields['style'] = json_encode($style);
                $fields['color'] = $color;
                $fields['group_id'] = $user_id;
                $settings['mainData'] = $fields;
                $LayersController->addLayerItem($settings);
            }
            sleep(1);

            // dobavqne na sloi komasaciq
            $color = $LayersController->StringHelper->randomColorCode();
            $fields = [];
            $style = [
                'color' => $color,
                'border_color' => '111111',
                'transparency' => 100,
                'border_only' => false,
                'label_name' => [null],
                'tags' => true,
                'label_size' => $this->default_label_size,
            ];
            $tableName = 'layer_kms_' . time();
            $fields['name'] = 'Данни от комасация';
            $fields['user_id'] = $user_id;
            $fields['farming'] = $farming;
            $fields['year'] = $year;
            $fields['extent'] = Config::DEFAULT_MAX_EXTENT;
            $fields['table_name'] = $tableName;
            $fields['layer_type'] = Config::LAYER_TYPE_KMS;
            $fields['style'] = json_encode($style);
            $fields['color'] = $color;
            $fields['group_id'] = $user_id;
            $settings['mainData'] = $fields;
            $LayersController->addLayerItem($settings);

            // dobavqne na ISAK
            $color = $LayersController->StringHelper->randomColorCode();
            $fields = [];
            $tableName = 'layer_isak_' . time();
            $style = [
                'color' => $color,
                'border_color' => '111111',
                'transparency' => 100,
                'border_only' => false,
                'label_name' => [null],
                'tags' => true,
                'label_size' => $this->default_label_size,
            ];
            $fields['name'] = 'от ИСАК';
            $fields['user_id'] = $user_id;
            $fields['farming'] = $farming;
            $fields['year'] = $year;
            $fields['extent'] = Config::DEFAULT_MAX_EXTENT;
            $fields['table_name'] = $tableName;
            $fields['layer_type'] = Config::LAYER_TYPE_ISAK;
            $fields['color'] = $color;
            $fields['style'] = json_encode($style);
            $fields['group_id'] = $user_id;
            $settings['mainData'] = $fields;
            $LayersController->addLayerItem($settings);

            // sloj za ISAK sashtestvuva samo sled 2015 god
            if ($year >= 6) {
                // dobavqne na za ISAK
                $color = $LayersController->StringHelper->randomColorCode();
                $fields = [];
                $style = [
                    'color' => $color,
                    'border_color' => '111111',
                    'transparency' => 100,
                    'border_only' => false,
                    'label_name' => [null],
                    'tags' => true,
                    'label_size' => $this->default_label_size,
                ];
                $tableName = 'layer_for_isak_' . time();
                $fields['name'] = 'за ИСАК';
                $fields['user_id'] = $user_id;
                $fields['farming'] = $farming;
                $fields['year'] = $year;
                $fields['extent'] = Config::DEFAULT_MAX_EXTENT;
                $fields['table_name'] = $tableName;
                $fields['layer_type'] = Config::LAYER_TYPE_FOR_ISAK;
                $fields['color'] = $color;
                $fields['group_id'] = $user_id;
                $fields['style'] = json_encode($style);
                $settings['mainData'] = $fields;
                $LayersController->addLayerItem($settings);
            }
            sleep(1);
        }

        return $farming;
    }

    /**
     * @param [int] $userId
     * @param [string] $username
     */
    private function regenerateMapFiles($userId, $organizationUsername): void
    {
        $LayersController = new LayersController('Layers');

        $options = [];
        $options['database'] = 'db_' . strtolower($organizationUsername);
        $options['user_id'] = $userId;
        $options['maxextent'] = Config::DEFAULT_MAX_EXTENT;
        $LayersController->generateMapFile($options);
    }

    private function createGroupLogFile($user_id)
    {
        $filename = LOG_PATH . '/' . $user_id . '.log';
        fopen($filename, 'w');
    }

    /**
     * Returns a date formated string like 2015-01-01;.
     *
     * @param int $year an year index
     *
     * @return string a date formated string
     */
    private function getFullYearFromIndex($year)
    {
        if (!$year) {
            return;
        }

        $correctYear = $year + $GLOBALS['Farming']['years_key_year_offset'];
        $hdy = str_pad($correctYear, 3, '0', STR_PAD_LEFT); // HundredsDecadesYears

        return "2{$hdy}-01-01";
    }

    private function getOriginalUserLevel()
    {
        $UsersController = new UsersController('Users');

        $app = $this->getApplication();
        $auth = $app->getModule('auth');
        $postData = $app->getRequest()->getCookies();

        $userData = $UsersController->getUserDataById($this->User->UserID);
        $loginHashFromDB = $userData['login_token'];

        $loginHashFromCookie = $postData->findCookieByName($auth->getUserKey() . '_login_hash')->getValue();
        $reconstructedLoginHashFromDb = sha1($loginHashFromDB . getenv('APP_UNIQUE_KEY'));

        $loggedAsOtherUser = $loginHashFromCookie !== $reconstructedLoginHashFromDb;

        if (!$loggedAsOtherUser) {
            return $userData['level'];
        }

        $levelHashFromCookie = $postData->findCookieByName($auth->getUserKey() . '_level_hash')->getValue();
        $originalUserLevel = null;

        foreach (Config::$USERS_FLAGS as $flag) {
            if (sha1($flag . getenv('APP_UNIQUE_KEY')) === $levelHashFromCookie) {
                $originalUserLevel = $flag;

                break;
            }
        }

        return $originalUserLevel;
    }

    private function revokeUserPermissions(array $users): void
    {
        foreach ($users as $id) {
            $this->User->updateUserFarmingPermissions([], $id, ObjectPermissions::$permisionsMap);
        }
    }
}
