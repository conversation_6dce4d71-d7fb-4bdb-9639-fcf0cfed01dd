<?php

namespace TF\Engine\APIClasses\Isak;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbIsak\UserDbIsakController;

// Prado::using('Plugins.Core.Layers.conf');
// Prado::using('Plugins.Core.Layers.*');
// Prado::using('Plugins.Core.Farming.conf');

/**
 * @rpc-module Isak
 *
 * @rpc-service-id isak-diff-allowable-grid
 */
class IsakDiffAllowableGrid extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getIsakDiffAllowableGrid'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'init' => ['method' => [$this, 'initIsakDiffAllowable']],
        ];
    }

    /**
     * getIsakDiffAllowableGrid Площи в и извън допустим слой.
     *
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item string layer_name
     *                         }
     * @param int $page rpc pagination params
     * @param int $rows rpc pagination params
     * @param string $sort rpc pagination params
     * @param string $order rpc pagination params
     *
     * @return array
     */
    public function getIsakDiffAllowableGrid(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $return = [
            'total' => 0,
            'rows' => [],
            'footer' => [],
        ];

        if ($this->User->isGuest || !$rpcParams['layer_name']) {
            return $return;
        }

        // init all needed controllers
        $UserDbMapController = new UserDbIsakController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);
        $LayersController = new LayersController('Layers');

        // check if table exists
        $tableExist = $UserDbController->getTableNameExist($rpcParams['layer_name']);
        if (!$tableExist) {
            return $return;
        }

        $options = [
            'tablename' => $rpcParams['layer_name'],
            'return' => ['ST_Extent(geom) as extent'],
        ];

        $extent_result = $UserDbController->getItemsByParams($options);

        // get ISAK extent and transform it
        $maxextent = $extent_result[0]['extent'];
        $maxextent = str_replace('BOX(', '', $maxextent);
        $maxextent = str_replace(')', '', $maxextent);
        $maxextent = str_replace(' ', ',', $maxextent);

        if ('' == $maxextent) {
            return $return;
        }

        $IsakLayerData = $LayersController->getLayers([
            'return' => ['t.id'],
            'where' => [
                'table_name' => ['column' => 't.table_name', 'compare' => '=', 'value' => $rpcParams['layer_name']],
            ],
        ]);
        $layerId = $IsakLayerData[0]['id'];
        $isakView = 'allowable_from_isak_' . $layerId;

        if (!$UserDbController->getViewNameExists($isakView)) {
            $UserDbController->createAllowableFromIsakReportView($layerId);
        }

        $options = [
            'tablename' => $rpcParams['layer_name'],
            'return' => [
                'isak.prc_uin', 'ST_AsText(isak.geom) as st_astext',
                'round(isak.area::numeric, 2) as area',
                // 'round(ST_Area(ST_Difference(isak.geom, ST_Union(a.geom)))::numeric/10000, 2) as outside_area'
                'CASE
                  WHEN
                  round(((ST_Area (isak.geom) :: NUMERIC ) - (ST_Area (ST_Difference (ST_SnapToGrid(isak.geom, 0.0001), ST_Union (ST_SnapToGrid(a .geom, 0.0001)) ) ) :: NUMERIC ) ) :: NUMERIC / 10000, 2 ) ISNULL THEN
                  round(ST_Area (isak.geom) :: NUMERIC / 10000, 2 ) ELSE
                  round(ST_Area (ST_Difference (ST_SnapToGrid(isak.geom, 0.0001), ST_Union (ST_SnapToGrid(a .geom, 0.0001)) ) ) :: NUMERIC / 10000, 2 ) END AS outside_area',
                ' SUM (round(isak.area :: NUMERIC, 2)) OVER () AS total_area', ' SUM (CASE WHEN round(((ST_Area (isak.geom) :: NUMERIC) - (ST_Area (ST_Difference (ST_SnapToGrid (isak.geom, 0.0001), ST_Union (ST_SnapToGrid (A .geom, 0.0001) ) ) ) :: NUMERIC ) ) :: NUMERIC / 10000, 2 ) ISNULL THEN round(ST_Area (isak.geom) :: NUMERIC / 10000, 2 ) ELSE round(ST_Area (ST_Difference (ST_SnapToGrid (isak.geom, 0.0001), ST_Union (ST_SnapToGrid (A .geom, 0.0001) ) ) ) :: NUMERIC / 10000, 2 ) END ) OVER () AS total_outside_area',
                'count(*) OVER () as total_count',
            ],
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
            'group' => 'isak.gid',
            'having' => '(CASE
                    WHEN 
                    round(((ST_Area (isak.geom) :: NUMERIC ) - (ST_Area (ST_Difference (ST_SnapToGrid(isak.geom, 0.0001), ST_Union (ST_SnapToGrid(a .geom, 0.0001)) ) ) :: NUMERIC ) ) :: NUMERIC / 10000, 2 )  ISNULL THEN
                    round(ST_Area (isak.geom) :: NUMERIC / 10000, 2 ) ELSE
                    round(ST_Area (ST_Difference (ST_SnapToGrid(isak.geom, 0.0001), ST_Union (ST_SnapToGrid(a .geom, 0.0001)) ) ) :: NUMERIC / 10000, 2 ) END) > 0.00',
            'extent' => $maxextent,
            'isakView' => $isakView,
        ];

        $data = $this->UserDbIsakController->getIsakDiffAllowableData($options, false, false);
        $dataCount = count($data);
        if (0 == $dataCount) {
            return $return;
        }

        // iterate results
        for ($i = 0; $i < $dataCount; $i++) {
            $data[$i]['inside_area'] = number_format($data[$i]['area'] - $data[$i]['outside_area'], 3, '.', '');
            $data[$i]['area'] = number_format($data[$i]['area'], 3, '.', '');
            $data[$i]['outside_area'] = number_format($data[$i]['outside_area'], 3, '.', '');
        }

        $return = [
            'total' => $data[0]['total_count'],
            'rows' => $data,
            'footer' => [
                [
                    'prc_uin' => '<b>Общо:</b>',
                    'area' => '<b>' . number_format($data[0]['total_area'], 3, '.', ' ') . '</b>',
                    'outside_area' => '<b>' . number_format($data[0]['total_outside_area'], 3, '.', ' ') . '</b>',
                    'inside_area' => '<b>' . number_format($data[0]['total_area'] - $data[0]['total_outside_area'], 3, '.', ' ') . '</b>',
                ],
            ],
        ];
        $return['rows'] = $data;

        return $return;
    }

    /**
     * initIsakDiffAllowable Зарежда "Допустим слой".
     *
     * @api-method init
     *
     * @param array $rpcParams
     *                         {
     *                         #item string layer_name
     *                         }
     *
     * @return array
     */
    public function initIsakDiffAllowable($rpcParams)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $LayersController = new LayersController('Layers');

        // check if table exists
        $tableExist = $UserDbController->getTableNameExist($rpcParams['layer_name']);
        if (!$tableExist) {
            return [];
        }

        $options = [
            'tablename' => $rpcParams['layer_name'],
            'return' => ['ST_Extent(geom) as extent'],
        ];

        $extent_result = $UserDbController->getItemsByParams($options);

        // get ISAK extent and transform it
        $maxextent = $extent_result[0]['extent'];
        $maxextent = str_replace('BOX(', '', $maxextent);
        $maxextent = str_replace(')', '', $maxextent);
        $maxextent = str_replace(' ', ',', $maxextent);

        $IsakLayerData = $LayersController->getLayers([
            'return' => ['t.id'],
            'where' => [
                'table_name' => ['column' => 't.table_name', 'compare' => '=', 'value' => $rpcParams['layer_name']],
            ],
        ]);
        $layerId = $IsakLayerData[0]['id'];
        $isakView = 'allowable_from_isak_' . $layerId;

        if (!$UserDbController->getViewNameExists($isakView)) {
            $UserDbController->createAllowableFromIsakReportView($layerId);
        }

        $options = [
            'tablename' => $rpcParams['layer_name'],
            'return' => ['isak.gid AS gid',
                'CASE
                  WHEN 
                  ST_Difference (isak.geom, ST_Union (a .geom)) ISNULL THEN
                  isak.geom ELSE
                  ST_Difference (isak.geom, ST_union(a.geom)) END AS geom',
            ],
            'group' => 'isak.gid',
            'extent' => $maxextent,
            'isakView' => $isakView,
        ];

        $query = $this->UserDbIsakController->getIsakDiffAllowableData($options, false, true);

        $options = [];
        $options['database'] = $this->User->Database;
        $options['user_id'] = $this->User->GroupID;
        $LayersController->generateMapFile($options);

        $mapFileData = [];

        $color1 = '000000';
        $color2 = 'ff0000';
        $mapFileData['layername'] = 'isak_diff_allowable';
        $mapFileData['maxextent'] = $result[0]['extent']; // do he meant $extent_result[0]['extent'] ?
        $mapFileData['host'] = DEFAULT_DB_HOST;
        $mapFileData['dbname'] = $this->User->Database;
        $mapFileData['username'] = DEFAULT_DB_USERNAME;
        $mapFileData['password'] = DEFAULT_DB_PASSWORD;
        $mapFileData['port'] = DEFAULT_DB_PORT;
        $mapFileData['query'] = "({$query}) as subquery using unique gid using srid=32635";
        $mapFileData['gid'] = 'gid';
        $mapFileData['transparency'] = '100';
        $mapFileData['classes'][0]['name'] = 'isak_diff_allowable';
        $mapFileData['classes'][0]['symbol']['name'] = 'hatch_sym';
        $mapFileData['classes'][0]['symbol']['size'] = 20;
        $mapFileData['classes'][0]['symbol']['angle'] = 45;
        $mapFileData['classes'][0]['symbol']['color'] = hexdec(substr($color2, 0, 2)) . ' ' . hexdec(substr($color2, 2, 2)) . ' ' . hexdec(substr($color2, 4, 2));
        $ltext = $LayersController->StringHelper->loadTemplate($GLOBALS['Templates'][2]['template'], $mapFileData);

        @unlink(WMS_MAP_PATH . $this->User->GroupID . '_layer_isak_diff_allowable.map');
        $fp = fopen(WMS_MAP_PATH . $this->User->GroupID . '_layer_isak_diff_allowable.map', 'w');
        $res = fwrite($fp, $ltext);
        fclose($fp);
    }
}
