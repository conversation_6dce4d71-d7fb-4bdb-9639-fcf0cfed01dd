<?php

namespace TF\Engine\APIClasses\Isak;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

// Prado::using('Plugins.Core.Layers.conf');
// Prado::using('Plugins.Core.Layers.*');
// Prado::using('Plugins.Core.Farming.conf');
// Prado::using('Plugins.Core.Farming.*');

/**
 * @rpc-module Isak
 *
 * @rpc-service-id isak-report-grid
 */
class IsakReportGrid extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getIsakReportGrid'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * getIsakReportGrid Обща площ по схема/мярка.
     *
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item string layer_name
     *                         #item string item string prc_uin
     *                         #item string ekate
     *                         #item string schemata
     *                         #item string culture
     *                         }
     * @param int $page pagination parameters
     * @param int $rows pagination parameters
     * @param string $sort pagination parameters
     * @param string $order pagination parameters
     *
     * @return array
     */
    public function getIsakReportGrid(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $return = [
            'total' => 0,
            'rows' => [],
        ];

        if ($this->User->isGuest || !$rpcParams['layer_name']) {
            return $return;
        }

        $LayersController = new LayersController('Layers');
        $UserDbController = new UserDbController($this->User->Database);

        // check if table exists
        $tableExist = $UserDbController->getTableNameExist($rpcParams['layer_name']);
        if (!$tableExist) {
            return $return;
        }

        $options = [
            'tablename' => $rpcParams['layer_name'],
            // 'group' => 'schemata',
            'sort' => $sort,
            'order' => $order,
            'return' => [
                'schemata', 'round(area::numeric, 2) AS area',
            ],
            'where' => [
                'prc_uin' => ['column' => 'prc_uin', 'compare' => 'ILIKE', 'value' => $rpcParams['prc_uin']],
                'ekate' => ['column' => 'ekatte', 'compare' => '=', 'value' => $rpcParams['ekate']],
                'schemata' => ['column' => 'schemata', 'compare' => 'ILIKE', 'value' => $rpcParams['schemata']],
                'culture' => ['column' => 'cropcode', 'compare' => '=', 'value' => $rpcParams['culture']],
            ],
        ];

        $results = $UserDbController->getItemsByParams($options, false, false);
        $resultsCount = count($results);
        if (0 == $resultsCount) {
            return $return;
        }

        $group_by_schemata = [];

        for ($i = 0; $i < $resultsCount; $i++) {
            if (!$results[$i]['schemata']) {
                $results[$i]['schemata'] = '-';
            }

            $schemata_array = explode(',', $results[$i]['schemata']);
            $schemaCount = count($schemata_array);
            for ($j = 0; $j < $schemaCount; $j++) {
                $schema = trim($schemata_array[$j]);
                $group_by_schemata[$schema]['schemata'] = $schema;
                $group_by_schemata[$schema]['area'] += $results[$i]['area'];
            }
        }

        $return_rows = array_values($group_by_schemata);

        return [
            'total' => count($return_rows),
            'rows' => $return_rows,
        ];
    }
}
