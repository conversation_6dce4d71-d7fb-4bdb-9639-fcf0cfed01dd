<?php

namespace TF\Engine\APIClasses\Isak;

use Prado\Web\Services\TRpcApiProvider;
use TF\Application\Common\Config;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

// Prado::using('Plugins.Core.Farming.conf');
// Prado::using('Plugins.Core.Farming.*');
// Prado::using('Plugins.Core.Layers.conf');
// Prado::using('Plugins.Core.Layers.*');

/**
 * @rpc-module Isak
 *
 * @rpc-service-id isak-layers-tree
 */
class LayersTree extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getIsakLayersTree']],
        ];
    }

    /**
     * getIsakLayersTree Дърво със стопанства "ИСАК".
     *
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item int year
     *                         }
     *
     * @return array
     *
     * @deprecated
     */
    public function getIsakLayersTree($rpcParams)
    {
        // init all needed controllers
        $UserDbController = new UserDbController($this->User->Database);
        $FarmingController = new FarmingController('Farming');
        $LayersController = new LayersController('Layers');

        $farmingYear = $FarmingController->getCurrentFarmingYearID();
        $lastFarmingYear = $farmingYear - 2;
        // get all farmings
        $options = [
            'return' => [
                'f.id as farming_id', 'f.name as farming_name', 't.*',
            ],
            'where' => [
                'group_id_layer' => ['column' => 'group_id', 'compare' => '=', 'prefix' => 'f', 'value' => $this->User->GroupID],
                'group_id_farming' => ['column' => 'group_id', 'compare' => '=', 'prefix' => 't', 'value' => $this->User->GroupID],
                'layer_type' => ['column' => 'layer_type', 'compare' => '=', 'prefix' => 't', 'value' => Config::LAYER_TYPE_ISAK],
                'year_from' => ['column' => 'year', 'compare' => '>=', 'prefix' => 't', 'value' => $lastFarmingYear],
                'year_to' => ['column' => 'year', 'compare' => '<=', 'prefix' => 't', 'value' => $farmingYear],
            ],
            'sort' => 'farming asc, year asc, layer_type',
            'order' => 'desc',
        ];
        $results = $LayersController->getLayers($options);
        $resultsCount = count($results);
        $layers = [];

        for ($i = 0; $i < $resultsCount; $i++) {
            $layers[$results[$i]['farming_id']]['id'] = $results[$i]['farming_id'];
            $layers[$results[$i]['farming_id']]['text'] = $results[$i]['farming_name'];
            $layers[$results[$i]['farming_id']]['state'] = 'open';
            $layers[$results[$i]['farming_id']]['children'][] = [
                'id' => $results[$i]['year'],
                'text' => $GLOBALS['Farming']['years'][$results[$i]['year']]['title'],
                'iconCls' => 'icon-tree-calendar',
                'attributes' => [
                    'layer_name' => $results[$i]['table_name'],
                    'extent' => str_replace(' ', ',', $results[$i]['extent']),
                ],
            ];
        }
        $layers = array_values($layers);

        return $layers;
    }
}
