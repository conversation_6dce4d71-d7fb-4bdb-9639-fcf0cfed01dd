<?php

namespace TF\Engine\APIClasses\Isak;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

// Prado::using('Plugins.Core.Users.conf');
// Prado::using('Plugins.Core.Users.*');
// Prado::using('Plugins.Core.Farming.conf');
// Prado::using('Plugins.Core.Farming.*');
// Prado::using('Plugins.Core.Layers.conf');
// Prado::using('Plugins.Core.Layers.*');

/**
 * @rpc-module Isak
 *
 * @rpc-service-id isak-maingrid
 */
class IsakGrid extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getIsakGrid'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * getIsakGrid Грид "Исак".
     *
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item string $layer_name
     *                         #item string $item string prc_uin
     *                         #item string $ekate
     *                         #item string $schemata
     *                         #item string $culture
     *                         }
     * @param int $page pagination parameters
     * @param int $rows pagination parameters
     * @param string $sort pagination parameters
     * @param string $order pagination parameters
     *
     * @return array
     */
    public function getIsakGrid(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $return = [
            'total' => 0,
            'rows' => [],
        ];

        if ($this->User->isGuest || !$rpcParams['layer_name']) {
            return $return;
        }

        $UserDbController = new UserDbController($this->User->Database);
        $LayersController = new LayersController('Layers');

        // check if table exists
        $tableExist = $UserDbController->getTableNameExist($rpcParams['layer_name']);
        if (!$tableExist) {
            return $return;
        }

        $options = [
            'tablename' => $rpcParams['layer_name'],
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
            'return' => [
                '*', 'ST_AsText(geom) st_astext',
            ],
            'where' => [
                'prc_uin' => ['column' => 'prc_uin', 'compare' => 'ILIKE', 'value' => $rpcParams['prc_uin']],
                'ekate' => ['column' => 'ekatte', 'compare' => '=', 'value' => $rpcParams['ekate']],
                'schemata' => ['column' => 'schemata', 'compare' => 'ILIKE', 'value' => $rpcParams['schemata']],
                'culture' => ['column' => 'cropcode', 'compare' => '=', 'value' => $rpcParams['culture']],
            ],
        ];

        $counter = $UserDbController->getItemsByParams($options, true);
        if (0 == $counter[0]['count']) {
            return $return;
        }

        $result = $UserDbController->getItemsByParams($options, false);
        $resultsCount = count($result);
        for ($i = 0; $i < $resultsCount; $i++) {
            $result[$i]['culture'] = $result[$i]['cropcode'];
            $culture = (int) $result[$i]['culture'];
            $result[$i]['culture'] = $GLOBALS['Farming']['crops'][$culture]['crop_name'];
            $result[$i]['area'] = number_format($result[$i]['area'], 2);
        }

        $return['rows'] = $result;
        $return['total'] = $counter[0]['count'];

        return $return;
    }
}
