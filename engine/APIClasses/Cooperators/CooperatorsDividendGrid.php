<?php

namespace TF\Engine\APIClasses\Cooperators;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbCooperators\UserDbCooperatorsController;

/**
 * Cooperators Dividend Grid Class.
 *
 * @rpc-module Cooperators
 *
 * @rpc-service-id cooperator-dividend
 */
class CooperatorsDividendGrid extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readCooperatorDividend'],
                'validators' => [
                    'cooperatorId' => 'validateInteger',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * readCooperatorDividend Reads Cooperator Dividend.
     *
     * @api-method read
     *
     * @param int $page
     * @param int $rows
     * @param ?string $sort
     * @param ?string $order
     *
     * @return array
     */
    public function readCooperatorDividend(int $cooperatorId, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        if ($this->User->isGuest) {
            return;
        }

        $UserDbCooperatorsController = new UserDbCooperatorsController($this->User->Database);

        // creating the default return
        $default = [
            'rows' => [],
            'total' => 0,
            'footer' => [
            ],
        ];

        $options = [
            'return' => [
                "(to_char(start_date, 'DD.MM.YYYY') || ' - ' ||  to_char(end_date, 'DD.MM.YYYY')) as period",
                'round(cad.current_capital::NUMERIC, 2) as current_capital',
                'round(cad.dividend::NUMERIC, 2) as dividend',
                'round(cad.tax::NUMERIC, 2) as tax',
                'round(SUM(dp.cashout)::NUMERIC, 2) AS cashout',
            ],
            'where' => [
                'cooperator_id' => ['column' => 'cooperator_id', 'prefix' => 'cad', 'compare' => '=', 'value' => $cooperatorId],
                'cooperator_id2' => ['column' => 'cooperator_id', 'prefix' => 'dp', 'compare' => '=', 'value' => $cooperatorId],
            ],
            'group' => 'dar.start_date, dar.end_date, cad.current_capital, cad.dividend, cad.tax, dar.id',
            'sort' => $sort,
            'order' => $order,
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
        ];

        $counter = $UserDbCooperatorsController->getCooperatorDividendByAnnualReport($options, true);

        if (0 == $counter[0]['count']) {
            return $default;
        }

        $results = $UserDbCooperatorsController->getCooperatorDividendByAnnualReport($options, false, false);

        /*var_export($results);
        die;*/

        return [
            'total' => $counter[0]['count'],
            'rows' => $results,
            'footer' => [
            ],
        ];
    }

    /**
     * _loadCooperator Loads cooperator by id.
     *
     * @param int $id
     *
     * @return array
     */
    private function _loadCooperator($id)
    {
        if ($this->User->isGuest) {
            return;
        }

        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableCooperators,
            'return' => [
                '*',
                "to_char(date_entry, 'YYYY-MM-DD') as dateЕntry",
                "to_char(date_excluded, 'YYYY-MM-DD') as dateЕxcluded",
            ],
        ];
        $options['where'] = ['id' => ['column' => 'id', 'compare' => '=', 'value' => $id]];

        $result = $UserDbController->getItemsByParams($options, false, false);

        return $result[0];
    }
}
