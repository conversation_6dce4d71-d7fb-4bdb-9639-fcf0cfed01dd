<?php

namespace TF\Engine\APIClasses\Cooperators;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbCooperators\UserDbCooperatorsController;

/**
 * Cooperator Heritors Combobox Class.
 *
 * @rpc-module Cooperators
 *
 * @rpc-service-id cooperators-heritors-combobox
 */
class CooperatorHeritorsCombobox extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'read']],
        ];
    }

    /**
     * Return cooperator names by egn for the combobox.
     *
     * @api-method read
     *
     * @param int $root_id
     * @param string $egn
     * @param bool $selected
     *
     * @return array
     */
    public function read($root_id, $egn = '', $selected = false)
    {
        if ($this->User->isGuest) {
            return [];
        }

        $UserDbCooperatorsController = new UserDbCooperatorsController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);

        $cooperators_id_array = [];

        if ($root_id) {
            $options = [
                'return' => [
                    'DISTINCT(cooperator_id)',
                ],
                'where' => [
                    'path' => ['column' => 'path', 'compare' => '~', 'value' => $root_id . '.*'],
                ],
            ];
            $cooperators_id_result = $UserDbCooperatorsController->getCooperatorHeritors($options, false, false);

            $cooperatorsCount = count($cooperators_id_result);
            for ($i = 0; $i < $cooperatorsCount; $i++) {
                $cooperators_id_array[] = $cooperators_id_result[$i]['cooperator_id'];
            }
            $cooperators_id_array[] = $root_id;
        }

        $cooperators_options = [
            'tablename' => $UserDbController->DbHandler->tableCooperators,
            'sort' => 'id',
            'order' => 'desc',
            'return' => [
                'DISTINCT(id)', "name || ' ' || surname || ' ' || lastname as cooperator_names",
            ],
            'where' => [
                'egn' => ['column' => 'egn', 'compare' => 'ILIKE', 'value' => $egn],
                'anti_id' => ['column' => 'id', 'compare' => 'NOT IN', 'value' => $cooperators_id_array],
            ],
        ];

        $results = $UserDbController->getItemsByParams($cooperators_options);

        if ($selected && count($results)) {
            $results[0]['selected'] = true;
        }

        return $results;
    }
}
