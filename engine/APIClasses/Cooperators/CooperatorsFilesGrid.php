<?php

namespace TF\Engine\APIClasses\Cooperators;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Cooperators Files Grid Class.
 *
 * @rpc-module Cooperators
 *
 * @rpc-service-id cooperators-files-maingrid
 */
class CooperatorsFilesGrid extends TRpcApiProvider
{
    private $module = 'Cooperators';
    private $service_id = 'cooperators-files-maingrid';

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readCooperatorFiles'],
                'validators' => [
                    'cooperator_id' => 'validateInteger',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'delete' => ['method' => [$this, 'deleteCooperatorFiles']],
        ];
    }

    /**
     * Delete Cooperator Files.
     *
     * @api-method delete
     *
     * @param int $fileID
     */
    public function deleteCooperatorFiles($fileID)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $options = [
            'tablename' => $UserDbController->DbHandler->tableCooperatorsFiles,
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $fileID],
            ],
        ];
        $results = $UserDbController->getItemsByParams($options, false, false);

        @unlink(COOPERATORS_DOCUMENTS_PATH . $this->User->GroupID . '/' . $this->User->UserID . '/' . $results[0]['id'] . '_' . $results[0]['filename']);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableCooperatorsFiles,
            'id_string' => $fileID,
        ];

        $UserDbController->deleteItemsByParams($options);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['affected_file' => $results, 'delete_options' => $options], [], 'Delete cooperator file');
    }

    /**
     * Reads Cooperator Files.
     *
     * @api-method read
     *
     * @param int $page
     * @param int $rows
     * @param ?string $sort
     * @param ?string $order
     *
     * @return array
     */
    public function readCooperatorFiles(int $cooperator_id, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        if ($this->User->isGuest) {
            return;
        }

        // create the default return
        $return = [
            'rows' => [],
            'total' => 0,
        ];

        if (!$cooperator_id) {
            return $return;
        }

        $UserDbController = new UserDbController($this->User->Database);

        // options for contract files query
        $options = [
            'tablename' => $UserDbController->DbHandler->tableCooperatorsFiles,
            'where' => [
                'cooperator_id' => ['column' => 'cooperator_id', 'compare' => '=', 'value' => $cooperator_id],
            ],
        ];

        $counter = $UserDbController->getItemsByParams($options, true, false);
        if (0 == $counter[0]['count']) {
            return $return;
        }

        $results = $UserDbController->getItemsByParams($options, false, false);
        $resultsCount = count($results);
        for ($i = 0; $i < $resultsCount; $i++) {
            $results[$i]['date'] = strftime('%d.%m.%Y', strtotime($results[$i]['date']));
        }

        $return['rows'] = $results;
        $return['total'] = $counter[0]['count'];

        return $return;
    }
}
