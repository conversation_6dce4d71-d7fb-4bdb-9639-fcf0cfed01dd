<?php

namespace TF\Engine\APIClasses\Cooperators;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

/**
 * Cooperators Templates Grid Class.
 *
 * @rpc-module Cooperators
 *
 * @rpc-service-id cooperators-templates-grid
 */
class CooperatorsTemplatesGrid extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readCooperatorsTemplates'],
                'validators' => [
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * readCooperatorsTemplates Reads Cooperators Templates.
     *
     * @api-method read
     *
     * @param int $page
     * @param int $rows
     * @param ?string $sort
     * @param ?string $order
     *
     * @return array
     */
    public function readCooperatorsTemplates(int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        if ($this->User->isGuest) {
            return [];
        }

        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableTemplates,
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
        ];

        $counter = $UserDbController->getItemsByParams($options, true, false);

        if (0 === $counter[0]['count']) {
            return [];
        }

        $results = $UserDbController->getItemsByParams($options, false, false);

        return [
            'rows' => $results,
            'total' => $counter[0]['count'],
        ];
    }
}
