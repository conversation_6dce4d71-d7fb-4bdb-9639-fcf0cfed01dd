<?php

namespace TF\Engine\APIClasses\Cooperators;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbCooperators\UserDbCooperatorsController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Cooperators Capital Grid Class.
 *
 * @rpc-module Cooperators
 *
 * @rpc-service-id cooperator-capital
 */
class CooperatorsCapitalGrid extends TRpcApiProvider
{
    private $module = 'Cooperators';
    private $service_id = 'cooperator-capital';

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'create' => ['method' => [$this, 'createCooperatorCapital']],
            'createReversal' => ['method' => [$this, 'createCooperatorCapitalReversal']],
            'read' => ['method' => [$this, 'readCooperatorCapital'],
                'validators' => [
                    'cooperator_id' => 'validateInteger',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * createCooperatorCapital Creates Capital.
     *
     * @api-method create
     *
     * @param array $formData {
     *                        #item integer cooperator_id
     *                        #item float cashout
     *                        #item string iban
     *                        #item string representative
     *                        #item string pay_date (e.g. '2015-06-19')
     *                        #item boolean generate_order
     *                        #item boolean generate_payment
     *                        }
     */
    public function createCooperatorCapital($formData)
    {
        if ($this->User->isGuest) {
            return;
        }

        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        $cooperator = $this->_loadCooperator($formData['cooperator_id']);

        $owe_capital = $cooperator['current_capital'] - abs($formData['cashout']);

        // update current_capital
        $options = [
            'tablename' => $UserDbController->DbHandler->tableCooperators,
            'mainData' => [
                'current_capital' => $owe_capital,
            ],
        ];
        $options['where'] = ['id' => $formData['cooperator_id']];
        $UserDbController->editItem($options);

        // add capital payment
        $options = [
            'tablename' => $UserDbController->DbHandler->tableCooperatorsCapital,
            'mainData' => [
                'cooperator_id' => $formData['cooperator_id'],
                'owe_capital' => $owe_capital,
                'cashout' => $formData['cashout'],
                'iban' => $formData['iban'],
                'representative' => $formData['representative'],
                'pay_date' => $formData['pay_date'],
                'generate_order' => $formData['generate_order'] ? true : false,
                'generate_payment' => $formData['generate_payment'] ? true : false,
                'date_created' => 'now()',
            ],
        ];
        $recordID = $UserDbController->addItem($options);
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options, $recordID, 'Adding cooperator capital');
    }

    /**
     * createCooperatorCapitalReversal create Capital Reversal.
     *
     * @api-method createReversal
     *
     * @param array $formData {
     *                        #item integer cooperator_id
     *                        #item float cashout
     *                        #item string pay_date (e.g. '2015-06-19')
     *                        }
     */
    public function createCooperatorCapitalReversal($formData)
    {
        if ($this->User->isGuest) {
            return;
        }

        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        $cooperator = $this->_loadCooperator($formData['cooperator_id']);

        $owe_capital = $cooperator['current_capital'] + abs($formData['cashout']);

        // update current_capital
        $options = [
            'tablename' => $UserDbController->DbHandler->tableCooperators,
            'mainData' => [
                'current_capital' => $owe_capital,
            ],
        ];
        $options['where'] = ['id' => $formData['cooperator_id']];
        $UserDbController->editItem($options);

        // add capital payment
        $options = [
            'tablename' => $UserDbController->DbHandler->tableCooperatorsCapital,
            'mainData' => [
                'cooperator_id' => $formData['cooperator_id'],
                'owe_capital' => $owe_capital,
                'cashout' => '-' . $formData['cashout'],
                'pay_date' => $formData['pay_date'],
                'date_created' => 'now()',
            ],
        ];

        $recordID = $UserDbController->addItem($options);
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options, $recordID, 'Adding cooperator capital reversal');
    }

    /**
     * readCooperatorCapital Reads Cooperator Capital by cooperator_id.
     *
     * @api-method read
     *
     * @param int $page
     * @param int $rows
     * @param ?string $sort
     * @param ?string $order
     *
     * @return array
     */
    public function readCooperatorCapital(int $cooperator_id, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        if ($this->User->isGuest) {
            return;
        }

        $UserDbCooperatorsController = new UserDbCooperatorsController($this->User->Database);

        // creating the default return
        $default = [
            'rows' => [],
            'total' => 0,
            'footer' => [
            ],
        ];

        $options = [
            'tablename' => $UserDbCooperatorsController->DbHandler->tableCooperatorsCapital,
            'return' => [
                "name || ' ' || surname || ' ' || lastname as cooperator_names",
                'egn',
                'owe_capital',
                'cashout',
                "to_char(pay_date, 'YYYY-MM-DD') as pay_date",
                "to_char(date_excluded, 'YYYY-MM-DD') as date_excluded",
            ],
            'where' => [
                'cooperator_id' => ['column' => 'cooperator_id', 'prefix' => 'cc', 'compare' => '=', 'value' => $cooperator_id],
            ],
        ];

        // pagination
        $options['sort'] = $sort;
        $options['order'] = $order;
        $options['offset'] = ($page - 1) * $rows;
        $options['limit'] = $rows;

        $counter = $UserDbCooperatorsController->getCooperatorCapitals($options, true);

        if (0 == $counter[0]['count']) {
            return $default;
        }

        $results = $UserDbCooperatorsController->getCooperatorCapitals($options, false, false);

        return [
            'total' => $counter[0]['count'],
            'rows' => $results,
            'footer' => [
            ],
        ];
    }

    /**
     * _loadCooperator Loads cooperator by id.
     *
     * @param int $id
     *
     * @return array
     */
    private function _loadCooperator($id)
    {
        if ($this->User->isGuest) {
            return;
        }

        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableCooperators,
            'return' => [
                '*',
                "to_char(date_entry, 'YYYY-MM-DD') as dateЕntry",
                "to_char(date_excluded, 'YYYY-MM-DD') as dateЕxcluded",
            ],
        ];
        $options['where'] = ['id' => ['column' => 'id', 'compare' => '=', 'value' => $id]];

        $result = $UserDbController->getItemsByParams($options, false, false);

        return $result[0];
    }
}
