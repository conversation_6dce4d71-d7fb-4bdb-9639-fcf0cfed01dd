<?php

namespace TF\Engine\APIClasses\Cooperators;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbCooperators\UserDbCooperatorsController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Cooperator Heritors Tree Class.
 *
 * @rpc-module Cooperators
 *
 * @rpc-service-id cooperator-heritors-tree
 */
class CooperatorHeritorsTree extends TRpcApiProvider
{
    private $module = 'Cooperators';
    private $service_id = 'cooperator-heritors-tree';

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'create' => ['method' => [$this, 'createCooperatorHeritors']],
            'read' => ['method' => [$this, 'readCooperatorHeritors']],
            'delete' => ['method' => [$this, 'deleteCooperatorHeritor']],
            'allowAddingHeritor' => ['method' => [$this, 'allowAddingHeritor']],
        ];
    }

    /**
     * deleteCooperatorHeritor Delete Cooperator Heritor.
     *
     * @api-method delete
     *
     * @param array $param {
     *                     #item string path
     *                     }
     *
     * @throws MTRpcException
     *
     * @return array
     */
    public function deleteCooperatorHeritor($param)
    {
        if ($this->User->isGuest) {
            return [];
        }

        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $path = $param['path'];

        $options = [
            'tablename' => $UserDbController->DbHandler->tableCooperatorHeritors,
            'return' => [
                'cooperator_id', 'heritor_current_capital',
            ],
            'where' => [
                'path' => ['column' => 'path', 'compare' => '~', 'value' => '*.' . $path . '.*'],
            ],
        ];
        $results = $UserDbController->getItemsByParams($options);
        $resultsCount = count($results);
        if (0 == $resultsCount) {
            throw new MTRpcException('invalid_cooperator_heritor_relation', -33551);
        }

        $cooperatorId = $results[0]['cooperator_id'];
        $capitalToSubtract = $results[0]['heritor_current_capital'];

        for ($i = 0; $i < $resultsCount; $i++) {
            $id_array[] = $results[$i]['cooperator_id'];
        }
        $id_string = implode(',', $id_array);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableCooperatorHeritors,
            'id_name' => 'cooperator_id',
            'id_string' => $id_string,
            'where' => [
                'path' => ['column' => 'path', 'compare' => '~', 'value' => '*.' . $path . '.*'],
            ],
        ];

        $UserDbController->deleteItemsByParams($options);
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['affected_cooperators' => $results], [], 'Delete cooperator heritor');
        // Изваждане на капитала, когато се изтрива наследник
        $options = [
            'tablename' => $UserDbController->DbHandler->tableCooperators,
            'return' => [
                'current_capital',
            ],
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $cooperatorId],
            ],
        ];
        $results = $UserDbController->getItemsByParams($options);
        $currentCapital = $results[0]['current_capital'];

        $finalCapital = $currentCapital - $capitalToSubtract;

        $options = [
            'tablename' => $UserDbController->DbHandler->tableCooperators,
            'mainData' => [
                'current_capital' => $finalCapital < 0 ? 0 : $finalCapital,
            ],
        ];
        $options['where'] = ['id' => $cooperatorId];

        $UserDbController->editItem($options);
    }

    /**
     * createCooperatorHeritors Create Cooperator Heritors.
     *
     * @api-method create
     *
     * @param array $formData {
     *                        #item integer cooperator_id
     *                        #item integer parent
     *                        #item float heritor_current_capital
     *                        }
     *
     * @return array
     */
    public function createCooperatorHeritors($formData)
    {
        if ($this->User->isGuest) {
            return [];
        }

        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        $UserDbCooperatorsController = new UserDbCooperatorsController($this->User->Database);

        $cooperator_id = $formData['cooperator_id'];

        $options = [
            'tablename' => $UserDbController->DbHandler->tableCooperatorHeritors,
            'mainData' => [
                'cooperator_id' => $cooperator_id,
                'path' => $formData['parent'] . '.' . $cooperator_id,
                'heritor_current_capital' => $formData['heritor_current_capital'],
            ],
        ];

        $recordID = $UserDbController->addItem($options);
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options, $recordID, 'Adding cooperator heritor');
        $UserDbCooperatorsController->editCooperatorCurrentCapital($cooperator_id, $formData['heritor_current_capital']);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableCooperatorHeritors,
            'return' => [
                '*',
            ],
            'where' => [
                'path' => ['column' => 'path', 'compare' => '~', 'value' => $cooperator_id . '.*'],
            ],
        ];
        $results = $UserDbController->getItemsByParams($options);
        $resultsCount = count($results);
        $options = [
            'tablename' => $UserDbController->DbHandler->tableCooperatorHeritors,
            'return' => [
                '*',
            ],
            'where' => [
                'path' => ['column' => 'path', 'compare' => '~', 'value' => '*.' . $formData['parent']],
            ],
        ];

        $parent_results = $UserDbController->getItemsByParams($options);
        $parent_results[] = ['path' => $formData['parent']];
        $parent_resultsCount = count($parent_results);

        for ($j = 0; $j < $parent_resultsCount; $j++) {
            for ($i = 0; $i < $resultsCount; $i++) {
                $options = [
                    'tablename' => $UserDbController->DbHandler->tableCooperatorHeritors,
                    'mainData' => [
                        'cooperator_id' => $results[$i]['cooperator_id'],
                        'path' => $parent_results[$j]['path'] . '.' . $results[$i]['path'],
                    ],
                ];

                $recordID = $UserDbController->addItem($options);
                $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options, $recordID, 'Adding cooperator parent-heritor relation');
            }
        }

        $options = [
            'tablename' => $UserDbController->DbHandler->tableCooperatorHeritors,
            'return' => [
                '*',
            ],
            'where' => [
                'path' => ['column' => 'path', 'compare' => '~', 'value' => '*.' . $formData['parent']],
            ],
        ];
        $results = $UserDbController->getItemsByParams($options);
        $resultsCount = count($results);
        for ($i = 0; $i < $resultsCount; $i++) {
            $options = [
                'tablename' => $UserDbController->DbHandler->tableCooperatorHeritors,
                'mainData' => [
                    'cooperator_id' => $cooperator_id,
                    'path' => $results[$i]['path'] . '.' . $cooperator_id,
                ],
            ];

            $recordID = $UserDbController->addItem($options);
            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options, $recordID, 'Adding cooperator parent-heritor relation');
        }
    }

    /**
     * readCooperatorHeritors Read Cooperator Heritors.
     *
     * @api-method read
     *
     * @param array $params {
     *                      #item string path
     *                      }
     * @param int|string $page
     * @param string $sort
     * @param string $order
     *
     * @return array
     */
    public function readCooperatorHeritors($params, $page = '', $sort = '', $order = '')
    {
        if ($this->User->isGuest) {
            return;
        }

        $UserDbCooperatorsController = new UserDbCooperatorsController($this->User->Database);

        $options = [
            'return' => [
                'h.id', "name || ' ' || surname || ' ' || lastname as cooperator_names", 'cooperator_id', 'is_dead', 'path', 'h.heritor_current_capital as current_capital',
            ],
            'where' => [
                'path' => ['column' => 'path', 'compare' => '~', 'value' => $params['path']],
            ],
        ];

        $counter = $UserDbCooperatorsController->getCooperatorHeritors($options, true);

        if (0 == $counter[0]['count']) {
            return [];
        }
        $return = [];
        $results = $UserDbCooperatorsController->getCooperatorHeritors($options, false, false);
        $resultsCount = count($results);
        for ($i = 0; $i < $resultsCount; $i++) {
            if ($results[$i]['is_dead']) {
                $results[$i]['iconCls'] = 'icon-tree-user-rip';
            } else {
                $results[$i]['iconCls'] = 'icon-tree-user';
            }

            $return[] = [
                'text' => $results[$i]['cooperator_names'],
                'id' => $results[$i]['id'],
                'state' => 'closed',
                'attributes' => $results[$i],
                'iconCls' => $results[$i]['iconCls'],
            ];
        }

        return $return;
    }

    /**
     * Check "current_capital" of the dead person(in su_cooperators) with sum of the "heritor_current_capital".
     *
     * @api-method allowAddingHeritor
     *
     * @param int $parent
     *
     * @return array|bool
     */
    public function allowAddingHeritor($parent)
    {
        if ($this->User->isGuest) {
            return [];
        }

        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        // Get $sumHeritorCurrentCapital
        $options = [
            'tablename' => $UserDbController->DbHandler->tableCooperatorHeritors,
            'return' => [
                'SUM(heritor_current_capital) as sum',
            ],
            'where' => [
                'path' => ['column' => 'path', 'compare' => '~', 'value' => '' . $parent . '.*'],
            ],
        ];
        $result = $UserDbController->getItemsByParams($options);
        $sumHeritorCurrentCapital = $result[0]['sum'];

        // Get $currentCapitalCooperator
        $options = [
            'tablename' => $UserDbController->DbHandler->tableCooperators,
            'return' => [
                'current_capital',
            ],
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $parent],
            ],
        ];
        $result = $UserDbController->getItemsByParams($options);
        $currentCapitalCooperator = $result[0]['current_capital'];

        $allow = true;

        if ($sumHeritorCurrentCapital >= $currentCapitalCooperator) {
            $allow = false;
        }

        return $allow;
    }
}
