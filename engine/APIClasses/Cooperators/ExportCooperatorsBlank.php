<?php

namespace TF\Engine\APIClasses\Cooperators;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\ExportWordDocClass;
use TF\Engine\Kernel\PrintPdf;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

/**
 * Export Cooperators Blank.
 *
 * @rpc-module Cooperators
 *
 * @rpc-service-id export-cooperators-blank
 */
class ExportCooperatorsBlank extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'expCooperatorsBlank' => ['method' => [$this, 'expCooperatorsBlank']],

            'removeFile' => ['method' => [$this, 'removeFile']],
        ];
    }

    /**
     * expCooperatorsBlank Export Cooperators Blank.
     *
     * @api-method expCooperatorsBlank
     *
     * @param int $blankId blank id
     * @param int $cooperatorId cooperator id
     * @param string $blankType type of blank
     *
     * @return array
     */
    public function expCooperatorsBlank($blankId, $cooperatorId, $blankType)
    {
        if ($this->User->isGuest) {
            return;
        }

        $UserDbController = new UserDbController($this->User->Database);
        $FarmingController = new FarmingController('Farming');

        // get template data
        $options = [
            'tablename' => $UserDbController->DbHandler->tableTemplates,
            'where' => [
                'template_id' => ['column' => 'id', 'compare' => '=', 'value' => $blankId],
            ],
        ];

        $templateResults = $UserDbController->getItemsByParams($options, false, false);

        if (0 == count($templateResults)) {
            return [];
        }

        $template = $templateResults[0]['html'];

        // get cooperator data
        $options = [
            'tablename' => $UserDbController->DbHandler->tableCooperators,
            'where' => [
                'cooperators_id' => ['column' => 'id', 'compare' => '=', 'value' => $cooperatorId],
            ],
        ];

        $cooperatorsResults = $UserDbController->getItemsByParams($options, false, false);

        if (0 == count($cooperatorsResults)) {
            return [];
        }

        $cooperator = $cooperatorsResults[0];
        $this->tempReplaceKeywords($template, $cooperator);
        $blankName = $templateResults[0]['title'];

        $blankName = preg_replace('/\s+/', '_', $blankName);

        $return = [];

        if ('pdf' == $blankType) {
            $blanksDir = SITE_PATH . 'public/files/uploads/blanks/';
            if (!file_exists($blanksDir)) {
                $makeDir = mkdir($blanksDir, 0700);
            }

            $blanksPath = 'files/uploads/blanks/';
            $pdfPath = $blanksPath . $this->User->GroupID . '_' . $blankName . '.pdf';

            $headerTxt = $this->getHeaderFooterTag('page_header', $template);
            $footerTxt = $this->getHeaderFooterTag('page_footer', $template);
            $headerTxt = $this->formatPDFHeaderFooter('page_header', $headerTxt);
            $footerTxt = $this->formatPDFHeaderFooter('page_footer', $footerTxt);
            $template = $headerTxt . $template . $footerTxt;
            $template = '<page style="font-family: freeserif" backtop="50px" backbottom="50px" >' . $template . '</page>';

            $printPdf = new PrintPdf();
            $printPdf->generateFromHtml($template, $pdfPath, [], true);

            $return['path'] = $pdfPath;
            $return['file_name'] = $this->User->GroupID . '_' . $blankName . '.pdf';
        }

        if ('doc' == $blankType) {
            $exportWordDoc = new ExportWordDocClass();
            $return['path'] = $exportWordDoc->export($this->User->GroupID . '_' . $blankName, $template, true);
            $return['file_name'] = $this->User->GroupID . '_' . $blankName . '.doc';
        }

        return $return;
    }

    /**
     * removeFile remove File from the server.
     *
     * @api-method removeFile
     *
     * @param string $file_name
     */
    public function removeFile($file_name)
    {
        $path = PUBLIC_UPLOAD_RELATIVE_PATH . '/blanks/' . $file_name;
        unlink($path);
    }

    /**
     * tempReplaceKeywords Replace Keywords.
     *
     * @param string &$template
     * @param array $cooperator
     */
    private function tempReplaceKeywords(&$template, $cooperator)
    {
        $template_variables_names = [
            '[[ime_kooperator]]' => 'name',
            '[[razmer_dyalov_kapital]]' => 'paid_in_capital',
            '[[tekusht_kapital]]' => 'current_capital',
            '[[egn_kooperator]]' => 'egn',
            '[[l_k_kooperator]]' => 'lk_nomer',
            '[[l_k_data_izdavane]]' => 'lk_izdavane'];

        foreach ($template_variables_names as $tempVar => $columnName) {
            if (!strstr($template, $tempVar)) {
                continue;
            }

            if ($cooperator[$columnName] && 'name' == $columnName) {
                $cooperator[$columnName] = $cooperator['name'] . ' ' . $cooperator['surname'] . ' ' . $cooperator['lastname'];
            } elseif (!$cooperator[$columnName]) {
                $cooperator[$columnName] = '-';
            }

            $template = str_replace($tempVar, $cooperator[$columnName], $template);
        }
    }

    /**
     * getHeaderFooterTag Get Header Footer Tag.
     *
     * @param string $tagName
     * @param string &$template
     *
     * @return array|string
     */
    private function getHeaderFooterTag($tagName, &$template)
    {
        $tagContentRe = "/\[\[{$tagName}_\]\](?P<content>.*?)\[\[_{$tagName}\]\]/s";
        $matches = [];

        if (!preg_match_all($tagContentRe, $template, $matches)) {
            return '';
        }
        $template = preg_replace($tagContentRe, '', $template);

        return $matches['content'][0];
    }

    private function formatPDFHeaderFooter($tagName, $tagCont)
    {
        $imageRe = "/<img\\s[^>]*?src\\s*=\\s*['\\\"](?:(?P<img_src>[^'\\\"]*?)['\\\"][^>]*?>)/s";
        $matches = [];
        $formatedTag = "<{$tagName}>{$tagCont}</{$tagName}>";

        if (preg_match_all($imageRe, $tagCont, $matches)) {
            $newImage = '<img src="' . SITE_URL . '\1" height="50" />';
            $formatedTag = "<{$tagName}>" . preg_replace($imageRe, $newImage, $tagCont) . "</{$tagName}>";
        }

        return $formatedTag;
    }
}
