<?php

namespace TF\Engine\APIClasses\Cooperators;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\Export2XlsClass;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

/**
 * Summary Report Cooperators Grid Class.
 *
 * @rpc-module Cooperators
 *
 * @rpc-service-id summary-report-cooperators
 */
class SummaryReportCooperatorsGrid extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readSummaryReport'],
                'validators' => [
                    'filterParam' => [
                        'name' => 'validateText',
                        'egn' => 'validateText',
                        'dateExcludedFrom' => 'validateText',
                        'dateExcludedTo' => 'validateText',
                        'dateEntryFrom' => 'validateText',
                        'dateEntryTo' => 'validateText',
                        'excluded' => 'validateText',
                        'paidInCapitalFrom' => 'validateText',
                        'paidInCapitalTo' => 'validateText',
                    ],
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],

            'expSummaryReport' => ['method' => [$this, 'expSummaryReport']],

            'removeFile' => ['method' => [$this, 'removeFile']],
        ];
    }

    /**
     * readSummaryReport Reads Summary Report.
     *
     * @api-method read
     *
     * @param array $filterParam {
     *                           #item string name
     *                           #item string egn
     *                           #item string dateExcludedFrom (e.g. '2015-06-19')
     *                           #item string dateExcludedTo (e.g. '2015-06-19')
     *                           #item string dateEntryFrom (e.g. '2015-06-19')
     *                           #item string dateEntryTo (e.g. '2015-06-19')
     *                           #item integer excluded
     *                           #item float paidInCapitalFrom
     *                           #item float paidInCapitalTo
     *                           }
     * @param int $page
     * @param int $rows
     * @param ?string $sort
     * @param ?string $order
     *
     * @return array {
     *               #item integer total
     *               #item array rows {
     *               #item string book_number
     *               #item string cooperator_names
     *               #item string current_capital
     *               #item string date_entry
     *               #item string date_excluded
     *               #item string egn
     *               #item string excluded
     *               #item string is_dead
     *               #item string lk_izdavan
     *               #item string lk_nomer
     *               #item string paid_in_capital
     *               #item string partida_number
     *               }
     *               #item array footer {
     *               #item string current_capital
     *               #item string date_entry
     *               #item string excluded
     *               #item string is_dead
     *               #item string paid_in_capital
     *               }
     *               }
     */
    public function readSummaryReport(array $filterParam, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        if ($this->User->isGuest) {
            return;
        }

        $UserDbController = new UserDbController($this->User->Database);

        // creating the default return
        $default = [
            'rows' => [],
            'total' => 0,
            'footer' => [
            ],
        ];

        $filterParam['name'] = $filterParam['name'] ? preg_replace('/\s+/', ' ', $filterParam['name']) : '';

        $options = [
            'tablename' => $UserDbController->DbHandler->tableCooperators,
            'return' => [
                "name || ' ' || surname || ' ' || lastname as cooperator_names",
                'egn', 'lk_nomer', 'lk_izdavane', 'book_number', 'partida_number',
                "to_char(date_entry, 'DD.MM.YYYY') AS date_entry",
                'round(paid_in_capital::numeric, 2) as paid_in_capital',
                'round(current_capital::numeric, 2) as current_capital',
                'excluded',
                "to_char(date_excluded, 'DD.MM.YYYY') AS date_excluded",
                'is_dead',
            ],
            'where' => [
                'heritor_only' => ['column' => 'heritor_only', 'compare' => '=', 'value' => 'FALSE'],
            ],
        ];

        // Filter
        if (isset($filterParam['name'])) {
            $options['where']['cooperator_names'] = ['column' => "TRIM(name) || ' ' || TRIM(surname) || ' ' || TRIM(lastname)", 'compare' => 'ILIKE', 'value' => $filterParam['name']];
        }
        if (isset($filterParam['egn'])) {
            $options['where']['egn'] = ['column' => 'egn', 'compare' => 'ILIKE', 'value' => $filterParam['egn']];
        }
        if (isset($filterParam['dateExcludedFrom'])) {
            $options['where']['dateExcludedFrom'] = ['column' => 'date_excluded', 'compare' => '>=', 'value' => $filterParam['dateExcludedFrom']];
        }
        if (isset($filterParam['dateExcludedTo'])) {
            $options['where']['dateExcludedTo'] = ['column' => 'date_excluded', 'compare' => '<=', 'value' => $filterParam['dateExcludedTo']];
        }
        if (isset($filterParam['dateEntryFrom'])) {
            $options['where']['dateEntryFrom'] = ['column' => 'date_entry', 'compare' => '>=', 'value' => $filterParam['dateEntryFrom']];
        }
        if (isset($filterParam['dateEntryTo'])) {
            $options['where']['dateEntryTo'] = ['column' => 'date_entry', 'compare' => '<=', 'value' => $filterParam['dateEntryTo']];
        }
        if (isset($filterParam['excluded']) && -1 != $filterParam['excluded']) {
            $options['where']['excluded'] = ['column' => 'excluded', 'compare' => '=', 'value' => $filterParam['excluded']];
        }
        if (isset($filterParam['paidInCapitalFrom'])) {
            $options['where']['paidInCapitalFrom'] = ['column' => 'paid_in_capital', 'compare' => '>', 'value' => $filterParam['paidInCapitalFrom']];
        }
        if (isset($filterParam['paidInCapitalTo'])) {
            $options['where']['paidInCapitalTo'] = ['column' => 'paid_in_capital', 'compare' => '<', 'value' => $filterParam['paidInCapitalTo']];
        }

        // pagination
        $pageLimit = $rows;
        $options['sort'] = $sort;
        $options['order'] = $order;
        $options['offset'] = ($page - 1) * $pageLimit;
        $options['limit'] = $pageLimit;

        $counter = $UserDbController->getItemsByParams($options, true, false);

        if (0 == $counter[0]['count']) {
            return $default;
        }

        $results = $UserDbController->getItemsByParams($options, false, false);

        $totalPaidInCapital = 0;
        $totalCurrentCapital = 0;
        // iterate results
        $resultsCount = count($results);
        for ($i = 0; $i < $resultsCount; $i++) {
            $results[$i]['excluded'] = $results[$i]['excluded'] ? 'Да' : 'Не';
            $results[$i]['is_dead'] = $results[$i]['is_dead'] ? 'Да' : 'Не';

            // add to total
            $totalPaidInCapital += $results[$i]['paid_in_capital'];
            $totalCurrentCapital += $results[$i]['current_capital'];
        }
        // var_export($results); die;
        return [
            'total' => $counter[0]['count'],
            'rows' => $results,
            'footer' => [
                [
                    'date_entry' => '<b>Общо:</b>',
                    'paid_in_capital' => '<b>' . $totalPaidInCapital . '</b>',
                    'current_capital' => '<b>' . $totalCurrentCapital . '</b>',
                    'excluded' => '',
                    'is_dead' => '',
                ],
            ],
        ];
    }

    /**
     * expSummaryReport Export summary report.
     *
     * @api-method expSummaryReport
     *
     * @param array $filterParam {
     *                           #item string name
     *                           #item string egn
     *                           #item string dateExcludedFrom (e.g. '2015-06-19')
     *                           #item string dateExcludedTo (e.g. '2015-06-19')
     *                           #item string dateEntryFrom (e.g. '2015-06-19')
     *                           #item string dateEntryTo (e.g. '2015-06-19')
     *                           #item integer excluded
     *                           #item float paidInCapitalFrom
     *                           #item float paidInCapitalTo
     *                           }
     *
     * @return array {
     *
     * @item string exl_report_file path to export file
     * }
     */
    public function expSummaryReport($filterParam)
    {
        if ($this->User->isGuest) {
            return [];
        }

        $type = 'obshta_spravka_cooperatori';
        $path = PUBLIC_UPLOAD_RELATIVE_PATH . '/cooperator/' . $this->User->GroupID . '/' . $type . '.xlsx';

        if (!is_dir(PUBLIC_UPLOAD_COOPERATOR)) {
            mkdir(PUBLIC_UPLOAD_COOPERATOR);
        }
        if (!is_dir(PUBLIC_UPLOAD_COOPERATOR . '/' . $this->User->GroupID)) {
            mkdir(PUBLIC_UPLOAD_COOPERATOR . '/' . $this->User->GroupID);
        }

        $return = [];

        $results = $this->readSummaryReport($filterParam);
        $result = $results['rows'];

        $column_headers = [
            'Име',
            'ЕГН',
            'Лична карта номер',
            'Дата на издаване ЛК',
            'Номер на книга',
            'Номер на партида',
            'Дата на вписване',
            'Внесен капитал(лв)',
            'Текущ капитал(лв)',
            'Изключен от кооперацията',
            'Дата на изклюване',
            'Починал',
        ];

        $export2Xls = new Export2XlsClass();
        $export2Xls->exportUrlPath($path, $result, $column_headers);

        $return['path'] = $path;
        $return['file_name'] = $type . '.xlsx';

        return $return;
    }

    /**
     * removeFile remove File from the server.
     *
     * @api-method removeFile
     *
     * @param string $file_name
     */
    public function removeFile($file_name)
    {
        $path = PUBLIC_UPLOAD_RELATIVE_PATH . '/cooperator/' . $this->User->GroupID . '/' . $file_name;
        unlink($path);
    }
}
