<?php

namespace TF\Engine\APIClasses\Cooperators;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Cooperators Tree Class.
 *
 * @rpc-module Cooperators
 *
 * @rpc-service-id cooperators-tree
 */
class CooperatorsTree extends TRpcApiProvider
{
    private $module = 'Cooperators';
    private $service_id = 'cooperators-tree';

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'create' => ['method' => [$this, 'createCooperator']],

            'read' => ['method' => [$this, 'readCooperators']],

            'update' => ['method' => [$this, 'updateCooperator']],

            'load' => ['method' => [$this, 'loadCooperator'],
                'validators' => [
                    'id' => 'validateInteger, validateRequired',
                ],
            ],

            'delete' => ['method' => [$this, 'deleteCooperator']],
        ];
    }

    /**
     * Create Cooperator.
     *
     * @param array $formData {
     *                        #item string name
     *                        #item string surname
     *                        #item string lastname
     *                        #item string egn
     *                        #item boolean lk_nomer
     *                        #item string lk_izdavane (e.g. '2015-06-19')
     *                        #item string book_number
     *                        #item string partida_number
     *                        #item string date_entry (e.g. '2015-06-19')
     *                        #item float paid_in_capital
     *                        #item float current_capital
     *                        #item boolean excluded
     *                        #item string excluded_reason
     *                        #item string date_excluded (e.g. '2015-06-19')
     *                        #item boolean is_dead
     *                        #item string date_dead (e.g. '2015-06-19')
     *                        }
     *
     * @return array
     */
    public function createCooperator($formData)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        if ('' == $formData['date_excluded']) {
            $formData['date_excluded'] = null;
        }
        if ('1' == $formData['is_dead']) {
            $formData['date_dead'] = 'now()';
        }
        if (null == $formData['paid_in_capital']) {
            $formData['paid_in_capital'] = 0;
        }
        if (null == $formData['current_capital']) {
            $formData['current_capital'] = 0;
        }
        $options = [
            'tablename' => $UserDbController->DbHandler->tableCooperators,
            'mainData' => [
                'name' => $formData['name'],
                'surname' => $formData['surname'],
                'lastname' => $formData['lastname'],
                'egn' => $formData['egn'],
                'lk_nomer' => $formData['lk_nomer'],
                'lk_izdavane' => $formData['lk_izdavane'],
                'book_number' => $formData['book_number'],
                'partida_number' => $formData['partida_number'],
                'date_entry' => $formData['date_entry'],
                'paid_in_capital' => $formData['paid_in_capital'],
                'current_capital' => $formData['current_capital'],
                'excluded' => $formData['excluded'],
                'excluded_reason' => $formData['excluded_reason'],
                'date_excluded' => $formData['date_excluded'],
                'is_dead' => $formData['is_dead'],
                'date_created' => 'now()',
                'date_dead' => $formData['date_dead'],
                'heritor_only' => $formData['heritor_only'],
            ],
        ];

        $id = $UserDbController->addItem($options);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options, $id, 'Adding Cooperator');

        $return = [];
        if ($this->checkDateExistInReport($formData['date_entry'])) {
            $return['warning'] = 'report_existed_for_coop_join_date';
        }
        $return['cooperator_id'] = $id;

        return $return;
    }

    /**
     * readCooperators Read Cooperators.
     *
     * @api-method read
     *
     * @param array $filterParams {
     *                            #item string cooperator_names
     *                            #item string egn
     *                            #item integer id
     *                            }
     * @param int $page
     * @param string $sort
     * @param string $order
     *
     * @return array
     */
    public function readCooperators($filterParams, $page = '', $sort = '', $order = '')
    {
        if ($this->User->isGuest) {
            return [];
        }

        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableCooperators,
            'return' => [
                "name || ' ' || surname || ' ' || lastname as cooperator_names",
                '*',
                "to_char(date_entry, 'YYYY-MM-DD') as dateЕntry",
                "to_char(date_excluded, 'YYYY-MM-DD') as dateЕxcluded",
            ],
        ];

        if (isset($filterParams['cooperator_names']) && '' != $filterParams['cooperator_names']) {
            $tmp_owner_names = preg_replace('/\s+/', '.*', $filterParams['cooperator_names']);
            $tmp_owner_names = mb_strtolower($tmp_owner_names, 'UTF-8');
            $options['where']['owner_names'] = ['column' => "lower(TRIM (su_cooperators.name)) || ' ' || lower(TRIM (su_cooperators.surname)) || ' ' || lower(TRIM (su_cooperators.lastname))", 'compare' => '~', 'value' => $tmp_owner_names];
        }
        if (isset($filterParams['egn']) && '' != $filterParams['egn']) {
            $options['where']['egn'] = ['column' => 'egn', 'compare' => 'ILIKE', 'value' => $filterParams['egn']];
        }
        if (isset($filterParams['cooperator_id']) && (int)($filterParams['cooperator_id']) > 0) {
            $options['where']['cooperator_id'] = ['column' => 'id', 'compare' => '=', 'value' => $filterParams['cooperator_id']];
        }

        // pagination
        $page_limit = 50;
        $options['sort'] = "(TRIM(name) || ' ' || TRIM(surname) || ' ' || TRIM(lastname)) COLLATE \"alpha_numeric_bg\"";
        $options['order'] = 'asc';
        $options['offset'] = ($page - 1) * $page_limit;
        $options['limit'] = $page_limit;

        $counter = $UserDbController->getCooperators($options, true, false);

        if (0 == $counter[0]['count']) {
            return [];
        }

        $results = $UserDbController->getCooperators($options, false, false);
        $resultsCount = count($results);
        if (0 == $resultsCount) {
            return [];
        }
        for ($i = 0; $i < $resultsCount; $i++) {
            $results[$i]['iconCls'] = 'icon-tree-user';

            if ($results[$i]['is_dead']) {
                $results[$i]['is_dead_text'] = 'Да';
                $results[$i]['iconCls'] = 'icon-tree-user-rip';
            } else {
                $results[$i]['is_dead_text'] = 'Не';
            }

            $return[] = [
                'text' => $results[$i]['cooperator_names'],
                'id' => $results[$i]['id'],
                'attributes' => $results[$i],
                'iconCls' => $results[$i]['iconCls'],
            ];
        }

        // add attribute to first listed element of three for custom pagination
        // information for total can not be sent otherwise
        $return[0]['attributes']['pagination']['total'] = $counter[0]['count'];
        $return[0]['attributes']['pagination']['limit'] = $page_limit;

        return $return;
    }

    /**
     * loadCooperator Load cooperator.
     *
     * @api-method load
     *
     * @param int $id
     *
     * @return array {
     *               #item string book_number,
     *               #item string current_capital,
     *               #item string date_created,
     *               #item string date_dead,
     *               #item string date_entry,
     *               #item string date_excluded,
     *               #item string dateЕntry,
     *               #item string dateЕxcluded,
     *               #item string egn,
     *               #item string excluded,
     *               #item string excluded_reason,
     *               #item string id,
     *               #item string is_dead,
     *               #item string lastname,
     *               #item string lk_izdavane,
     *               #item string lk_nomer,
     *               #item string name,
     *               #item string paid_in_capital,
     *               #item string partida_number,
     *               #item string surname,
     *               }
     */
    public function loadCooperator($id)
    {
        if ($this->User->isGuest) {
            return;
        }

        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableCooperators,
            'return' => [
                '*',
                'round(paid_in_capital::numeric, 2) as paid_in_capital',
                'round(current_capital::numeric, 2) as current_capital',
                "to_char(date_entry, 'YYYY-MM-DD') as dateЕntry",
                "to_char(date_excluded, 'YYYY-MM-DD') as dateЕxcluded",
            ],
        ];
        $options['where'] = ['id' => ['column' => 'id', 'compare' => '=', 'value' => $id]];

        $results = $UserDbController->getItemsByParams($options, false, false);
        $result = $results[0];
        $result['date_entry'] = date('Y-m-d', strtotime($result['date_entry']));
        $result['date_excluded'] = date('Y-m-d', strtotime($result['date_excluded']));

        return $result;
    }

    /**
     * updateCooperator Update Cooperator.
     *
     * @api-method update
     *
     * @param array $formData {
     *
     * @item integer id
     * @item string name
     * @item string surname
     * @item string lastname
     * @item string egn
     * @item boolean lk_nomer
     * @item string lk_izdavane (e.g. '2015-06-19')
     * @item string book_number
     * @item string partida_number
     * @item string date_entry (e.g. '2015-06-19')
     * @item float paid_in_capital
     * @item float current_capital
     * @item boolean excluded
     * @item string excluded_reason
     * @item string date_excluded (e.g. '2015-06-19')
     * @item boolean is_dead
     * @item string date_dead (e.g. '2015-06-19')
     * }
     */
    public function updateCooperator($formData)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        if ('' == $formData['date_excluded']) {
            $formData['date_excluded'] = null;
        }
        if ('1' == $formData['is_dead']) {
            $formData['date_dead'] = 'now()';
        }

        $old_data = $this->loadCooperator($formData['id']);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableCooperators,
            'mainData' => [
                'name' => $formData['name'],
                'surname' => $formData['surname'],
                'lastname' => $formData['lastname'],
                'egn' => $formData['egn'],
                'lk_nomer' => $formData['lk_nomer'],
                'lk_izdavane' => $formData['lk_izdavane'],
                'book_number' => $formData['book_number'],
                'partida_number' => $formData['partida_number'],
                'date_entry' => $formData['date_entry'],
                'paid_in_capital' => (float)$formData['paid_in_capital'],
                'current_capital' => (float)$formData['current_capital'],
                'excluded' => $formData['excluded'],
                'excluded_reason' => $formData['excluded_reason'],
                'date_excluded' => $formData['date_excluded'],
                'is_dead' => $formData['is_dead'],
                'date_dead' => $formData['date_dead'],
                'heritor_only' => ($formData['heritor_only']) ? $formData['heritor_only'] : 'false',
            ],
        ];
        $options['where'] = ['id' => $formData['id']];

        $UserDbController->editItem($options);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['old_data' => $old_data], ['new_data' => $options['mainData']], 'Editing cooperator');

        $isExcluded = 1;

        // if cooperator is excluded
        if ($formData['excluded'] == $isExcluded) {
            $this->createCooperatorCapital($formData);
        }
    }

    /**
     * deleteCooperator delete Cooperator.
     *
     * @api-method delete
     *
     * @param int $id
     */
    public function deleteCooperator($id)
    {
        if ($this->User->isGuest || !$id) {
            return;
        }

        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $old_data = $this->loadCooperator($id);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableCooperators,
            'id_string' => $id,
        ];

        $UserDbController->deleteItemsByParams($options);
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['old_data' => $old_data], ['requested_id' => $id], 'Deleting cooperator');
    }

    /**
     * checkDateExistInReport  Checks if Date Exist In Report.
     *
     * @param string $date
     *
     * @return bool
     */
    private function checkDateExistInReport($date)
    {
        if ($this->User->isGuest) {
            return;
        }

        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableDividendsAnnualReport,
            'return' => [
                "to_char(start_date, 'YYYY-MM-DD') as start_date",
                "to_char(end_date, 'YYYY-MM-DD') as end_date",
            ],
        ];

        $results = $UserDbController->getItemsByParams($options, false, false);
        $dateTimeStamp = strtotime($date);
        $resultsCount = count($results);

        for ($i = 0; $i < $resultsCount; $i++) {
            $result = $results[$i];
            $startDateTimeStamp = strtotime($result['start_date']);
            $endDateTimeStamp = strtotime($result['end_date']);

            if ($dateTimeStamp >= $startDateTimeStamp && $dateTimeStamp <= $endDateTimeStamp) {
                return true;
            }
        }
    }

    /**
     * createCooperatorCapital Creates Cooperator Capital.
     *
     * @param array $formData {
     *                        #item integer id
     *                        }
     */
    private function createCooperatorCapital($formData)
    {
        $cooperatorId = $formData['id'];
        $cooperator = $this->loadCooperator($cooperatorId);
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        // add capital payment
        $options = [
            'tablename' => $UserDbController->DbHandler->tableCooperatorsCapital,
            'mainData' => [
                'cooperator_id' => $cooperatorId,
                'owe_capital' => $cooperator['current_capital'],
                'cashout' => 0,
                'date_created' => 'now()',
            ],
        ];

        $recordID = $UserDbController->addItem($options);
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options, $recordID, 'Adding cooperator capital');
    }
}
