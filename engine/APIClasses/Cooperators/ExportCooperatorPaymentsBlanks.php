<?php

namespace TF\Engine\APIClasses\Cooperators;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\PrintPdf;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

/**
 * Export Cooperator Payments Blanks.
 *
 * @rpc-module Cooperators
 *
 * @rpc-service-id export-cooperator-payments-blanks
 */
class ExportCooperatorPaymentsBlanks extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'expCooperatorOrder' => ['method' => [$this, 'expCooperatorOrder']],
            'expCooperatorPayment' => ['method' => [$this, 'expCooperatorPayment']],
        ];
    }

    /**
     * expCooperatorOrder Export Cooperator Order.
     *
     * @api-method expCooperatorOrder
     *
     * @param int $cooperatorId
     * @param string $payDate with dyalov capital pay date
     * @param string $cashOut with dyalov capital value for pay
     *
     * @return array
     */
    public function expCooperatorOrder($cooperatorId, $payDate, $cashOut)
    {
        if ($this->User->isGuest) {
            return [];
        }

        $UserDbController = new UserDbController($this->User->Database);
        $FarmingController = new FarmingController('Farming');

        // get cooperator data
        $options = [
            'tablename' => $UserDbController->DbHandler->tableCooperators,
            'where' => [
                'cooperator_id' => ['column' => 'id', 'compare' => '=', 'value' => $cooperatorId],
            ],
        ];

        $cooperatorResults = $UserDbController->getItemsByParams($options, false, false);

        if (0 == count($cooperatorResults)) {
            return [];
        }

        $cooperator = $cooperatorResults[0];
        $return = [];

        $ordersDir = SITE_PATH . 'public/files/uploads/orders/';
        if (!file_exists($ordersDir)) {
            $makeDir = mkdir($ordersDir, 0700);
        }

        $ordersPath = 'files/uploads/orders/';
        $pdfPath = $ordersPath . $payDate . '_' . $cooperatorId . '_razhoden_order.pdf';

        $amount = number_format($cashOut, 2, '.', '');
        // todo check fraction, which is undefined
        sscanf($cashOut, '%d.%d', $whole, $fraction);
        $printData['date'] = strftime('%d.%m.%Y', strtotime($payDate));
        $printData['name'] = $cooperator['name'] . ' ' . $cooperator['surname'] . ' ' . $cooperator['lastname'];
        $printData['lk_izdavane'] = $cooperator['lk_nomer'] . ', ' . $cooperator['lk_izdavane'];
        $printData['egn'] = $cooperator['egn'];
        $printData['for'] = 'Изплащане на дялов капитал';
        $printData['price'] = $amount . ' лева';
        $printData['price_text'] = $FarmingController->StringHelper->numToString(abs($amount)) . ' лева и ' . ((int) $fraction) . ' ст.';

        $template = $FarmingController->StringHelper->loadTemplate($GLOBALS['Templates'][4]['template'], $printData);
        $template = '<page style="font-family: freeserif"><br />' . $template . '</page>';

        $printPdf = new PrintPdf();
        $printPdf->generateFromHtml($template, $pdfPath, ['orientation' => 'Landscape'], true);

        $return['pdf_blank_file'] = $pdfPath;

        return $return;
    }

    /**
     * expCooperatorPayment export Cooperator Payment.
     *
     * @api-method expCooperatorPayment
     *
     * @param int $cooperatorId
     * @param string $cashout with dyalov capital value for pay
     * @param string $iban
     * @param string $representative
     *
     * @return array
     */
    public function expCooperatorPayment($cooperatorId, $cashout, $iban, $representative)
    {
        if ($this->User->isGuest) {
            return;
        }

        $UserDbController = new UserDbController($this->User->Database);
        $FarmingController = new FarmingController('Farming');

        // get cooperator data
        $options = [
            'tablename' => $UserDbController->DbHandler->tableCooperators,
            'where' => [
                'cooperator_id' => ['column' => 'id', 'compare' => '=', 'value' => $cooperatorId],
            ],
        ];

        $cooperatorResults = $UserDbController->getItemsByParams($options, false, false);

        if (0 == count($cooperatorResults)) {
            return [];
        }

        $cooperator = $cooperatorResults[0];
        $return = [];

        $paymentsDir = SITE_PATH . 'public/files/uploads/payments/';
        if (!file_exists($paymentsDir)) {
            $makeDir = mkdir($paymentsDir, 0700);
        }

        $paymentPath = 'files/uploads/payments/';
        $pdfPath = $paymentPath . $cooperatorId . '_payment_order.pdf';

        if ($cooperator['is_dead']) {
            $name = $representative;
        } else {
            $name = $cooperator['name'] . ' ' . $cooperator['surname'] . ' ' . $cooperator['lastname'];
        }

        $printData['recipient'] = $FarmingController->StringHelper->strToBlankFormat($name, 36);
        $printData['bank_acc'] = $FarmingController->StringHelper->strToBlankFormat($iban, 22);
        $printData['bic'] = '&emsp;' . str_repeat('|&emsp;', 7);
        $printData['bank'] = '&emsp;' . str_repeat('|&emsp;', 36);
        $printData['amount'] = $FarmingController->StringHelper->strToBlankFormat(number_format($cashout, 2, '.', ''), 13, 'right');
        $printData['amount'] = str_replace('|&nbsp;.&nbsp;|', '<span style="font-size: 20px;vertical-align:text-bottom;">|</span>', $printData['amount']);
        $printData['details'] = $FarmingController->StringHelper->strToBlankFormat('Изплащане на дялов капитал', 36);
        $printData['additionalDetails'] = '&emsp;' . str_repeat('|&emsp;', 36);
        $printData['orderer'] = '&emsp;' . str_repeat('|&emsp;', 36);
        $printData['orderer_bank_acc'] = '&emsp;' . str_repeat('|&emsp;', 21);
        $printData['orderer_bic'] = '&emsp;' . str_repeat('|&emsp;', 7);
        $printData['payment_system'] = '&emsp;' . str_repeat('|&emsp;', 16);
        $printData['charges'] = '&emsp;' . str_repeat('|&emsp;', 2);
        $printData['date'] = '&emsp;' . str_repeat('|&emsp;', 5);

        $template = $FarmingController->StringHelper->loadTemplate($GLOBALS['Templates'][15]['template'], $printData);

        $template = '<page style="font-family: freeserif">' . $template . '</page>';

        $printPdf = new PrintPdf();
        $printPdf->generateFromHtml($template, $pdfPath, [], true);

        $return['pdf_blank_file'] = $pdfPath;

        return $return;
    }
}
