<?php

namespace TF\Engine\APIClasses\Collections;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Грид "Плащания".
 *
 * @rpc-module Collections
 *
 * @rpc-service-id collections-payments-grid
 */
class CollectionPaymentsGrid extends TRpcApiProvider
{
    private $module = 'Collections';
    private $service_id = 'collection-payments-grid';

    /**
     * Registers the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getCollectionPayments'],
                'validators' => [
                    'filterObj' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'addCollectionPayment' => ['method' => [$this, 'addCollectionPayment']],
            'getPersonalUseCollections' => ['method' => [$this, 'getPersonalUseCollections'],
                'validators' => [
                    'filterObj' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'cancellationPersonalUseCollection' => ['method' => [$this, 'cancellationPersonalUseCollection']],
        ];
    }

    /**
     * Gets all payments, associated with selected contract.
     *
     * @api-method read
     *
     * @param array $filterObj
     *                         {
     *                         #item int    sublease_id
     *                         }
     * @param int $page pagination parameters
     * @param int $rows pagination parameters
     * @param string $sort pagination parameters
     * @param string $order pagination parameters
     *
     * @return array
     */
    public function getCollectionPayments(array $filterObj, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        if ($this->User->isGuest) {
            return [];
        }

        $UserDbController = new UserDbController($this->User->Database);

        // create default return
        $return = [
            'rows' => [],
            'total' => 0,
        ];

        $options = [
            'tablename' => 'su_collections',
            'return' => ["
                id,
                contract_id,
                round(cast(amount as numeric), 2) as amount,
                to_char(date, 'dd-mm-YYYY') as date,
                (CASE WHEN recieved_from = '' THEN '-' ELSE recieved_from END) as recieved_from,
                bank_payment,
                user_name,
                (CASE WHEN amount < 0 THEN 'Сторниране' ELSE 'Плащане' END) as payment_type,
                payment_data
            "],

            'where' => [
                'contract_id' => ['column' => 'contract_id', 'compare' => '=', 'value' => $filterObj['sublease_id']],
            ],
            'whereOr' => [
                'farming_year' => ['column' => 'farming_year', 'compare' => '=', 'value' => (int)$filterObj['farming_year']],
                'farming_year_default' => ['column' => 'farming_year', 'compare' => '=', 'value' => -1],
            ],
        ];

        $options['sort'] = $sort;
        $options['order'] = $order;
        $options['offset'] = ($page - 1) * $rows < 0 ? 0 : ($page - 1) * $rows;
        $options['limit'] = $rows;

        $results = $UserDbController->getItemsByParams($options, false, false);

        foreach ($results as $key => $result) {
            $results[$key]['amount_txt'] = BGNtoEURO($result['amount']);
        }

        $return['rows'] = $results;
        $return['total'] = count($results);

        return $return;
    }

    /**
     * adds payments to selected contract.
     *
     * @api-method addCollectionPayment
     *
     * @param array $rpcParams
     *                         {
     *                         #item boolean    is_reverse_payment,
     *                         #item double     amount,
     *                         #item integer    sublease_id,
     *                         #item string     date_recieved,
     *                         #item string     recieved_from,
     *                         #item boolean    bank_payment,
     *                         #item string     payment_order
     *                         }
     *
     * @return null|array
     */
    public function addCollectionPayment($rpcParams)
    {
        if ($this->User->isGuest) {
            return [];
        }

        $collectionType = COLLECTION_TYPE_RENT;
        if (!empty($rpcParams['type']) && COLLECTION_TYPE_PERSONAL_USE === $rpcParams['type']) {
            $collectionType = COLLECTION_TYPE_PERSONAL_USE;
        }

        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        if (COLLECTION_TYPE_PERSONAL_USE === $collectionType && empty($rpcParams['payment_data']['renta_type_id'])) {
            throw new MTRpcException('MISSING_RENTA_TYPE', -33047);
        }

        if (true == $rpcParams['is_reverse_payment']) {
            $rpcParams['amount'] = -1 * $rpcParams['amount'];
        }

        $options = [
            'tablename' => 'su_collections',
            'mainData' => [
                'contract_id' => $rpcParams['sublease_id'],
                'date' => $rpcParams['date_recieved'],
                'amount' => $rpcParams['amount'],
                'recieved_from' => $rpcParams['recieved_from'],
                'bank_payment' => $rpcParams['bank_payment'],
                'payment_order' => $rpcParams['payment_order'],
                'user_name' => $this->User->Name,
                'farming_year' => $rpcParams['farming_year'],
                'payment_data' => json_encode($rpcParams['payment_data']),
                'type' => $collectionType,
            ],
        ];
        $recordID = $UserDbController->addItem($options);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['new_values' => $options], ['created_id' => $recordID], 'Adding collection payment');

        if ($rpcParams['payment_order_generate']) {
            return [
                'payment_type' => $rpcParams['payment_data']['payment_method_cash'] ? 'cash' : 'bank',
                'collection_id' => $recordID,
            ];
        }
    }

    /**
     * Gets all payments, associated with selected personal use.
     *
     * @api-method read
     *
     * @param array $filterObj
     *                         {
     *                         #item int    sublease_id
     *                         }
     * @param int $page pagination parameters
     * @param int $rows pagination parameters
     * @param string $sort pagination parameters
     * @param string $order pagination parameters
     *
     * @return array
     */
    public function getPersonalUseCollections(array $filterObj, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $UserDbController = new UserDbController($this->User->Database);
        // create default return
        $return = [
            'rows' => [],
            'total' => 0,
        ];

        $options = [
            'tablename' => 'su_collections sc',
            'return' => [
                'sc.id as collection_id',
                'c.id as contract_id',
                'o.id as owner_id',
                'c.c_num as c_num',
                "to_char(c.start_date, 'DD.MM.YYYY') as start_date",
                "to_char(c.due_date, 'DD.MM.YYYY') as due_date",
                '(case when o.owner_type = 1 then concat(o."name", \' \', o.surname, \' \',o.lastname) else o.company_name end) as owner_names',
                '(case when o.owner_type = 1 then o.egn else o.eik end) as egn_eik',
                'sc."date"',
                'sc.amount',
                "sc.payment_data->>'rko_text' as rko_text",
                "sc.payment_data->>'recipient' as recipient",
                "sc.payment_data->>'farming_id' as farming_id",
                "sc.payment_data->>'rko_number' as rko_number",
                "sc.payment_data->>'orderer_iban' as orderer_iban",
                "sc.payment_data->>'recipient_lk' as recipient_lk",
                "sc.payment_data->>'recipient_egn' as recipient_egn",
                "sc.payment_data->>'recipient_address' as recipient_address",
                "sc.payment_data->>'recipient_company' as recipient_company",
                "sc.payment_data->>'payment_method_bank' as payment_method_bank",
                "sc.payment_data->>'payment_method_cash' as payment_method_cash",
                "sc.payment_data->>'renta_type_id' as renta_type_id",
                "sc.payment_data->>'renta_type_name' as renta_type_name",
            ],
            'where' => [
                'contract_id' => ['column' => 'id', 'prefix' => 'c', 'compare' => '=', 'value' => $filterObj['contract_id']],
                'su_owners' => ['column' => "(sc.payment_data->>'owner_id' = " . $filterObj['owner_id'] . '::text)', 'compare' => '=', 'value' => true],
                'renta_type_id' => ['column' => "(sc.payment_data->>'renta_type_id' = '" . $filterObj['renta_type_id'] . '\'::text)', 'compare' => '=', 'value' => true],
                'status' => ['column' => 'status', 'prefix' => 'sc', 'compare' => '=', 'value' => true],
                'farming_year' => ['column' => 'farming_year', 'prefix' => 'sc', 'compare' => '=', 'value' => $filterObj['farming_year']],
            ],
            'group' => 'c.id, o.id, sc.id',
            'custom_counter' => 'COUNT(distinct o.id)',
            'joins' => [
                'left join su_contracts c on c.id = sc.contract_id',
                'left join su_contracts p on p.id = c.parent_id',
                'inner join su_owners o on o.id::text = sc.payment_data->>\'owner_id\'',
            ],
        ];

        if ($filterObj['owner_names']) {
            $tmp_person_names = preg_replace('/\s+/', '.*', $filterObj['owner_names']);
            $tmp_person_names = mb_strtolower($tmp_person_names, 'UTF-8');
            $options['where']['owner_names'] = ['column' => "lower(TRIM (o.name)) || ' ' || lower(TRIM (o.surname)) || ' ' || lower(TRIM (o.lastname)) || ' ' || lower(TRIM (o.company_name) )", 'compare' => '~', 'value' => $tmp_person_names];
        }

        if ($filterObj['egn_eik']) {
            $options['where']['owner_names'] = ['column' => "lower(TRIM (o.egn)) || ' ' || lower(TRIM (o.eik))", 'compare' => '~', 'value' => $filterObj['egn_eik']];
        }

        $options['sort'] = $sort;
        $options['order'] = $order;
        $options['offset'] = ($page - 1) * $rows < 0 ? 0 : ($page - 1) * $rows;
        $options['limit'] = $rows;
        $results = $UserDbController->getItemsByParams($options, false, false);

        foreach ($results as $key => $result) {
            $results[$key]['amount_txt'] = BGNtoEURO($result['amount']);
        }

        $return['rows'] = $results;
        $return['total'] = count($results);

        return $return;
    }

    public function cancellationPersonalUseCollection($rpcParams)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        $options = [
            'tablename' => 'su_collections',
            'mainData' => [
                'status' => false,
                'cancellation_info' => json_encode([
                    'user' => $this->User->Name,
                    'datetime' => date('Y-m-d H:i:s'),
                    'reason' => $rpcParams['reason'],
                ]),
            ],
            'where' => ['id' => $rpcParams['collection_id']],
        ];

        $recordID = $UserDbController->editItem($options);
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['new_values' => $options], ['created_id' => $recordID], 'Cancel personal use collection');

        return true;
    }
}
