<?php

namespace TF\Engine\APIClasses\OSZ;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\Export2XlsClass;
use TF\Engine\Plugins\Core\UserDbOSZ\UserDbOSZController;

/**
 * OSZFilesPlots is responsible for geting osz files plots list.
 *
 * @rpc-module OSZ
 *
 * @rpc-service-id osz-files-plots
 */
class OSZFilesPlots extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getOSZFilesPlots'],
                'validators' => [
                    'filterParam' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'exportExcel' => ['method' => [$this, 'exportExcel'],
                'validators' => [
                    'filterParam' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'deleteFile' => ['method' => [$this, 'deleteFile']],
            'importOwners' => ['method' => [$this, 'importOwners']],
        ];
    }

    /**
     * Returns list of osz files plots.
     *
     * @api-method read
     *
     * @param array $filterParams {
     *                            #item integer file_id
     *                            #item string ekatte
     *                            #item string kad_no
     *                            #item string ime_subekt
     *                            #item string egn_subekt
     *                            #item string kategoria
     *                            #item string txt_ntp
     *                            #item boolean not_in_kvs
     *                            #item boolean only_in_kvs
     *                            #item boolean different_subekt
     *                            #item string action new||add
     *                            }
     * @param int $page
     * @param int $rows
     * @param ?string $sort
     * @param ?string $order
     *
     * @return array {
     *               #item integer total
     *               #item array footer {
     *               #item string kad_ident
     *               #item float pl_dka_po
     *               }
     *               #item array rows {
     *               #item string contract_subekt
     *               #item string data
     *               #item string egn_subekt
     *               #item string ekatte
     *               #item integer file_id
     *               #item integer gid
     *               #item integer id
     *               #item string id_both
     *               #item integer id_imot
     *               #item string ime_subekt
     *               #item string kad_ident
     *               #item string kad_no
     *               #item string kategoria
     *               #item string kod_imot
     *               #item string kod_ntp
     *               #item string kod_pr_osn
     *               #item string kod_sobstv
     *               #item string kod_subekt
     *               #item string kvs_no
     *               #item string pl_dka
     *               #item string pl_dka_po
     *               #item string txt_imot
     *               #item string txt_ntp
     *               #item string txt_pr_osn
     *               #item string txt_sobstv
     *               #item string txt_subekt
     *               #item string ver_no
     *               #item string vreme
     *               }
     *               }
     */
    public function getOSZFilesPlots(?array $filterParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $UserDbOSZController = new UserDbOSZController($this->User->Database);

        if (!$filterParams['file_id'] || !$filterParams['ekatte']) {
            return [
                'rows' => [],
                'total' => 0,
            ];
        }

        if (!is_array($_SESSION['osz_filtered_plots'])) {
            $_SESSION['osz_filtered_plots'] = [];
        }

        $options = [
            'where' => [
                'kad_no' => ['prefix' => 'p', 'column' => 'kad_no', 'compare' => 'ILIKE', 'value' => $filterParams['kad_no']],
                'ime_subekt' => ['prefix' => 'p', 'column' => 'ime_subekt', 'compare' => 'ILIKE', 'value' => $filterParams['ime_subekt']],
                'egn_subekt' => ['prefix' => 'p', 'column' => 'egn_subekt', 'compare' => 'ILIKE', 'value' => $filterParams['egn_subekt']],
                'kategoria' => ['prefix' => 'p', 'column' => 'kategoria', 'compare' => 'ILIKE', 'value' => $filterParams['kategoria']],
                'txt_ntp' => ['prefix' => 'p', 'column' => 'txt_ntp', 'compare' => 'ILIKE', 'value' => $filterParams['txt_ntp']],
            ],
            'file_id' => $filterParams['file_id'],
            'ekatte' => $filterParams['ekatte'],
        ];

        if ($filterParams['not_in_kvs'] && true == $filterParams['not_in_kvs']) {
            $options['where_or']['not_in_kvs'] = ['prefix' => 'p', 'column' => 'kad_ident', 'compare' => 'IS', 'value' => 'NULL'];
        }
        if ($filterParams['only_in_kvs'] && true == $filterParams['only_in_kvs']) {
            $options['where_or']['only_in_kvs'] = ['prefix' => 'p', 'column' => 'kad_no', 'compare' => 'IS', 'value' => 'NULL'];
        }
        if ($filterParams['different_subekt'] && true == $filterParams['different_subekt']) {
            $options['where_or']['different_subekt'] = ['column' => '(f.bulstat_arr IS NOT NULL AND p.egn_subekt IS NOT NULL AND NOT f.bulstat_arr @> ARRAY[p.egn_subekt]::varchar[])',
                'compare' => '=', 'value' => 'TRUE'];
        }

        if ($filterParams['action'] && 'add' == $filterParams['action']) {
            // query to get sum of all records
            $options['return'] = ['p.id_both'];

            $add_filter_results = $UserDbOSZController->getOSZFilesPlots($options, false, false);

            $filterCount = count($add_filter_results);
            for ($i = 0; $i < $filterCount; $i++) {
                if (!in_array($add_filter_results[$i]['id_both'], $_SESSION['osz_filtered_plots'])) {
                    $_SESSION['osz_filtered_plots'][] = $add_filter_results[$i]['id_both'];
                }
            }

            unset($options['where_or']);
            $options['where'] = [
                'id_both' => ['prefix' => 'p', 'column' => 'id_both', 'compare' => 'IN', 'value' => $_SESSION['osz_filtered_plots']],
            ];
        }

        $options['return'] = ['*'];

        $counter = $UserDbOSZController->getOSZFilesPlots($options, true, false);

        if (0 == $counter[0]['count']) {
            $_SESSION['osz_filtered_plots'][] = '0';

            return [
                'rows' => [],
                'total' => 0,
            ];
        }

        // query to get sum of all records
        $options['return'] = ['SUM(p.pl_dka_po) AS pl_dka_po'];

        $sum_results = $UserDbOSZController->getOSZFilesPlots($options, false, false);

        // actual query
        $options['offset'] = ($page - 1) * $rows;
        $options['limit'] = $rows;
        $options['sort'] = $sort;
        $options['order'] = $order;
        $options['return'] = ['p.*', 'f.bulstat as contract_subekt'];

        $results = $UserDbOSZController->getOSZFilesPlots($options, false, false);
        $resultsCount = count($results);
        $_SESSION['osz_filtered_plots'] = [];
        $total_pl_dka_po = 0;
        for ($i = 0; $i < $resultsCount; $i++) {
            $total_pl_dka_po += $results[$i]['pl_dka_po'];
            $results[$i]['pl_dka_po'] = number_format($results[$i]['pl_dka_po'], 3, '.', '');
            $results[$i]['pl_dka'] = number_format($results[$i]['pl_dka'], 3, '.', '');

            if ($filterParams['action'] && ('new' == $filterParams['action'] || 'add' == $filterParams['action'])
                && !in_array($results[$i]['id_both'], $_SESSION['osz_filtered_plots'])) {
                $_SESSION['osz_filtered_plots'][] = $results[$i]['id_both'];
            }
        }

        $return['rows'] = $results;
        $return['total'] = $counter[0]['count'];
        $return['footer'] = [
            [
                'kad_ident' => '<b>Общо за стр.</b>',
                'pl_dka_po' => number_format($total_pl_dka_po, 3, '.', ''),
            ],
            [
                'kad_ident' => '<b>Общо</b>',
                'pl_dka_po' => number_format($sum_results[0]['pl_dka_po'], 3, '.', ''),
            ],
        ];

        return $return;
    }

    /**
     * Returns list of osz files plots.
     *
     * @api-method exportExcel
     *
     * @param array $filterParams {
     *                            #item integer file_id
     *                            #item string ekatte
     *                            #item string kad_no
     *                            #item string ime_subekt
     *                            #item string egn_subekt
     *                            #item string kategoria
     *                            #item string txt_ntp
     *                            #item boolean not_in_kvs
     *                            #item boolean only_in_kvs
     *                            #item boolean different_subekt
     *                            #item string action new||add
     *                            }
     * @param ?string $sort
     * @param ?string $order
     *
     * @return string
     */
    public function exportExcel(array $filterParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $UserDbOSZController = new UserDbOSZController($this->User->Database);

        $column_headers = [
            'kad_ident',
            'contract_subekt',
            'kvs_no',
            'kad_no',
            'kod_subekt',
            'txt_subekt',
            'ime_subekt',
            'egn_subekt',
            'kod_pr_osn',
            'txt_pr_osn',
            'pl_dka',
            'pl_dka_po',
            'kategoria',
            'kod_ntp',
            'txt_ntp',
            'kod_sobstv',
            'txt_sobstv',
            'kod_imot',
            'txt_imot',
            'ekatte',
            'ver_no',
            'data',
            'vreme',
        ];

        $options = [
            'where' => [
                'kad_no' => ['prefix' => 'p', 'column' => 'kad_no', 'compare' => 'ILIKE', 'value' => $filterParams['kad_no']],
                'ime_subekt' => ['prefix' => 'p', 'column' => 'ime_subekt', 'compare' => 'ILIKE', 'value' => $filterParams['ime_subekt']],
                'egn_subekt' => ['prefix' => 'p', 'column' => 'egn_subekt', 'compare' => 'ILIKE', 'value' => $filterParams['egn_subekt']],
                'kategoria' => ['prefix' => 'p', 'column' => 'kategoria', 'compare' => 'ILIKE', 'value' => $filterParams['kategoria']],
                'txt_ntp' => ['prefix' => 'p', 'column' => 'txt_ntp', 'compare' => 'ILIKE', 'value' => $filterParams['txt_ntp']],
            ],
            'file_id' => $filterParams['file_id'],
            'ekatte' => $filterParams['ekatte'],
        ];

        if ($filterParams['not_in_kvs'] && true == $filterParams['not_in_kvs']) {
            $options['where_or']['not_in_kvs'] = ['prefix' => 'p', 'column' => 'kad_ident', 'compare' => 'IS', 'value' => 'NULL'];
        }
        if ($filterParams['only_in_kvs'] && true == $filterParams['only_in_kvs']) {
            $options['where_or']['only_in_kvs'] = ['prefix' => 'p', 'column' => 'kad_no', 'compare' => 'IS', 'value' => 'NULL'];
        }
        if ($filterParams['different_subekt'] && true == $filterParams['different_subekt']) {
            $options['where_or']['different_subekt'] = ['column' => '(f.bulstat_arr IS NOT NULL AND p.egn_subekt IS NOT NULL AND NOT f.bulstat_arr @> ARRAY[p.egn_subekt]::varchar[])',
                'compare' => '=', 'value' => 'TRUE'];
        }

        if ($filterParams['action'] && 'add' == $filterParams['action']) {
            // query to get sum of all recors
            $options['return'] = ['p.id_both'];

            $add_filter_results = $UserDbOSZController->getOSZFilesPlots($options, false, false);
            $filterCount = count($add_filter_results);

            for ($i = 0; $i < $filterCount; $i++) {
                if (!in_array($add_filter_results[$i]['id_both'], $_SESSION['osz_filtered_plots'])) {
                    $_SESSION['osz_filtered_plots'][] = $add_filter_results[$i]['id_both'];
                }
            }

            unset($options['where_or']);
            $options['where'] = [
                'id_both' => ['prefix' => 'p', 'column' => 'id_both', 'compare' => 'IN', 'value' => $_SESSION['osz_filtered_plots']],
            ];
        }

        $options['return'] = ['*'];

        $counter = $UserDbOSZController->getOSZFilesPlots($options, true, false);

        // query to get sum of all records
        $options['return'] = ['SUM(p.pl_dka_po) AS pl_dka_po'];

        // actual query
        $options['sort'] = $sort;
        $options['order'] = $order;
        $options['return'] = ['p.*', 'f.bulstat as contract_subekt'];

        $results = $UserDbOSZController->getOSZFilesPlots($options, false, false);
        $resultsCount = count($results);
        $data = [];
        for ($i = 0; $i < $resultsCount; $i++) {
            $data[] = [
                'kad_ident' => $results[$i]['kad_ident'],
                'contract_subekt' => $results[$i]['contract_subekt'],
                'kvs_no' => $results[$i]['kvs_no'],
                'kad_no' => $results[$i]['kad_no'],
                'kod_subekt' => $results[$i]['kod_subekt'],
                'txt_subekt' => $results[$i]['txt_subekt'],
                'ime_subekt' => $results[$i]['ime_subekt'],
                'egn_subekt' => $results[$i]['egn_subekt'],
                'kod_pr_osn' => $results[$i]['kod_pr_osn'],
                'txt_pr_osn' => $results[$i]['txt_pr_osn'],
                'pl_dka' => number_format($results[$i]['pl_dka'], 3, '.', ''),
                'pl_dka_po' => number_format($results[$i]['pl_dka_po'], 3, '.', ''),
                'kategoria' => $results[$i]['kategoria'],
                'kod_ntp' => $results[$i]['kod_ntp'],
                'txt_ntp' => $results[$i]['txt_ntp'],
                'kod_sobstv' => $results[$i]['kod_sobstv'],
                'txt_sobstv' => $results[$i]['txt_sobstv'],
                'kod_imot' => $results[$i]['kod_imot'],
                'txt_imot' => $results[$i]['txt_imot'],
                'ekatte' => $results[$i]['ekatte'],
                'ver_no' => $results[$i]['ver_no'],
                'data' => $results[$i]['data'],
                'vreme' => $results[$i]['vreme'],
            ];
        }

        $date = date('Y-m-d-H-i-s');
        $fileName = 'danni_ot_osz_' . $this->User->GroupID;
        $path = PUBLIC_UPLOAD . '/blanks/' . $fileName . '_' . $date . '.xlsx';

        $export2Xls = new Export2XlsClass();
        $filePath = $export2Xls->exportUrlPath($path, $data, $column_headers, false);

        $return['filePath'] = $filePath;
        $return['fileName'] = $fileName . '.xlsx';

        return $return;
    }

    /**
     * Returns list of osz files plots.
     *
     * @api-method exportExcel
     *
     * @param array $filterParams {
     *                            #item integer file_id
     *                            #item string ekatte
     *                            #item string kad_no
     *                            #item string ime_subekt
     *                            #item string egn_subekt
     *                            #item string kategoria
     *                            #item string txt_ntp
     *                            #item boolean not_in_kvs
     *                            #item boolean only_in_kvs
     *                            #item boolean different_subekt
     *                            }
     */
    public function importOwners($filterParams)
    {
        $UserDbOSZController = new UserDbOSZController($this->User->Database);

        $options = [
            'return' => ['DISTINCT p.egn_subekt', 'max(DISTINCT (p.ime_subekt)::text) AS ime_subekt', 'p.kod_subekt'],
            'where' => [
                'kad_no' => ['prefix' => 'p', 'column' => 'kad_no', 'compare' => 'ILIKE', 'value' => $filterParams['kad_no']],
                'ime_subekt' => ['prefix' => 'p', 'column' => 'ime_subekt', 'compare' => 'ILIKE', 'value' => $filterParams['ime_subekt']],
                'egn_subekt' => ['prefix' => 'p', 'column' => 'egn_subekt', 'compare' => 'ILIKE', 'value' => $filterParams['egn_subekt']],
                'kategoria' => ['prefix' => 'p', 'column' => 'kategoria', 'compare' => 'ILIKE', 'value' => $filterParams['kategoria']],
                'txt_ntp' => ['prefix' => 'p', 'column' => 'txt_ntp', 'compare' => 'ILIKE', 'value' => $filterParams['txt_ntp']],
            ],
            'file_id' => $filterParams['file_id'],
            'ekatte' => $filterParams['ekatte'],
            'group' => 'p.egn_subekt, p.kod_subekt',
        ];

        if ($filterParams['not_in_kvs'] && true == $filterParams['not_in_kvs']) {
            $options['where_or']['not_in_kvs'] = ['prefix' => 'p', 'column' => 'kad_ident', 'compare' => 'IS', 'value' => 'NULL'];
        }
        if ($filterParams['only_in_kvs'] && true == $filterParams['only_in_kvs']) {
            $options['where_or']['only_in_kvs'] = ['prefix' => 'p', 'column' => 'kad_no', 'compare' => 'IS', 'value' => 'NULL'];
        }
        if ($filterParams['different_subekt'] && true == $filterParams['different_subekt']) {
            $options['where_or']['different_subekt'] = ['column' => '(f.bulstat_arr IS NOT NULL AND p.egn_subekt IS NOT NULL AND NOT f.bulstat_arr @> ARRAY[p.egn_subekt]::varchar[])',
                'compare' => '=', 'value' => 'TRUE'];
        }

        $ownersResults = $UserDbOSZController->getOwnersToAddFromOSZ($options, false, false);

        $arrResult = array_map(function ($item) {
            if (1 == $item['kod_subekt']) {
                // физическо лице
                $piece = preg_split("/[\s,]+/", $item['ime_subekt']);
                $lastName = $piece[count($piece) - 1];
                $sureName = $piece[count($piece) - 2];
                $firstName = $piece[count($piece) - 3];
                $pos = strpos($item['ime_subekt'], $sureName);
                $firstName = substr($item['ime_subekt'], 0, $pos);

                $item['name'] = rtrim($firstName);
                $item['surname'] = $sureName;
                $item['lastname'] = $lastName;
                $item['egn'] = $item['egn_subekt'];
                $item['eik'] = $item['company_name'] = '';
                $item['owner_type'] = 1;
            } else {
                // юридическо лице
                $item['name'] = $item['surname'] = $item['lastname'] = $item['egn'] = '';
                $item['company_name'] = $item['ime_subekt'];
                $item['eik'] = $item['egn_subekt'];
                $item['owner_type'] = 0;
            }

            return $item;
        }, $ownersResults);

        // batch insert
        if (count($arrResult)) {
            $UserDbOSZController->addOwnersFromOSZ($arrResult);
        }
    }

    /**
     * Delete created file.
     *
     * @api-method deleteFile
     *
     * @param string $fileName
     */
    public function deleteFile($fileName)
    {
        @unlink(PUBLIC_UPLOAD . '/blanks/' . $fileName);
    }
}
