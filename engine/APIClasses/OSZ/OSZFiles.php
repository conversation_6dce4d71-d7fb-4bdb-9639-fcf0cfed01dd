<?php

namespace TF\Engine\APIClasses\OSZ;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\Users\UsersController;

// Prado::using('Plugins.Core.UserDb.*');
// Prado::using('Plugins.Core.Users.*');

/**
 * OSZFiles is responsible for geting osz files list.
 *
 * @rpc-module OSZ
 *
 * @rpc-service-id osz-files
 */
class OSZFiles extends TRpcApiProvider
{
    private $module = 'OSZ';
    private $service_id = 'osz-files';

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getOSZFiles']],
            'delete' => ['method' => [$this, 'deleteOSZFile'],
                'validators' => [
                    'file_id' => 'validateRequired',
                ],
            ],
        ];
    }

    /**
     * Returns list of osz files matching filters.
     *
     * @api-method read
     *
     * @param array $filterParams {
     *                            #item string land
     *                            #item string ekatte
     *                            #item string date
     *                            }
     * @param int $page
     * @param int $rows
     * @param string $sort
     * @param string $order
     *
     * @return array {
     *               #item string text
     *               #item string iconCls
     *               #item integer id
     *               #item array attributes {
     *               #item string date
     *               #item string date_text
     *               #item string ekatte
     *               #item integer id
     *               #item string land
     *               }
     *               }
     */
    public function getOSZFiles($filterParams, $page = '', $rows = '', $sort = '', $order = '')
    {
        $UserDbController = new UserDbController($this->User->Database);

        // options for contract files query
        $options = [
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
            'tablename' => $UserDbController->DbHandler->tableOSZFiles,
            'return' => ['*'],
            'where' => [
                'land' => ['column' => 'land', 'compare' => 'ILIKE', 'value' => $filterParams['land']],
                'ekatte' => ['column' => 'ekatte', 'compare' => 'ILIKE', 'value' => $filterParams['ekatte']],
                'date' => ['column' => 'date', 'compare' => '=', 'value' => $filterParams['date']],
            ],
        ];

        $counter = $UserDbController->getItemsByParams($options, true, false);
        if (0 == $counter[0]['count']) {
            return [];
        }

        $results = $UserDbController->getItemsByParams($options, false, false);

        $resultsCount = count($results);
        for ($i = 0; $i < $resultsCount; $i++) {
            $results[$i]['date_text'] = strftime('%d.%m.%Y', strtotime($results[$i]['date']));

            $return[] = [
                'text' => $results[$i]['land'] . '(' . $results[$i]['ekatte'] . ') - ' . $results[$i]['date_text'],
                'id' => $results[$i]['id'],
                'attributes' => $results[$i],
                'iconCls' => 'icon-tree-document',
            ];
        }

        // add attribute to first listed element of three for custom pagination
        $return[0]['attributes']['pagination']['total'] = (int)$counter[0]['count'];
        $return[0]['attributes']['pagination']['limit'] = (int)$rows;

        return $return;
    }

    /**
     * Deletes osz file.
     *
     * @api-method delete
     *
     * @param int $recordId
     */
    public function deleteOSZFile($recordId)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $options = [
            'tablename' => $UserDbController->DbHandler->tableOSZFiles,
            'return' => ['*'],
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $recordId],
            ],
        ];

        $old_data = $UserDbController->getItemsByParams($options, false, false);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableOSZFiles,
            'id_string' => $recordId,
        ];

        $UserDbController->deleteItemsByParams($options);

        $UserDbController->refreshTopicLayerKVSViews();
        $UserDbController->refreshOszEkateCombobox();

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['old_data' => $old_data[0]], ['requested_id' => $recordId], 'Deleting OSZ File');
    }
}
