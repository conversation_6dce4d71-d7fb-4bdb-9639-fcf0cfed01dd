<?php

namespace TF\Engine\APIClasses\Payments;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbPayments\UserDbPaymentsController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Personal Use Grid.
 *
 * @rpc-module Payments
 *
 * @rpc-service-id payments-reports-renta-natura-grid
 */
class PaymentsReportsRentaNaturaGrid extends TRpcApiProvider
{
    public $renta_types = [];
    public $iterator = 0;
    public $paid_renta = 0;
    public $paid_renta_nat = 0;
    public $unpaid_renta = 0;
    public $unpaid_renta_nat = 0;

    // added for heritors
    public $relation_id = 0;
    public $percent = [];
    public $owner_id = 0;

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readPaymentsReportsRentaNaturaGrid'],
                'validators' => [
                    'data' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * Read payments reports renta natura grid.
     *
     * @api-method read
     *
     * @param array $data
     *                    {
     *                    #item string report_type      -The payment report type
     *                    #item array contracts         -Only for detailed report renta natura.
     *                    #item string nat_type         -Only for detailed report renta natura.
     *                    #item array year              -Only for summary report renta natura.
     *                    #item array renta_nat_type    -Only for summary report renta natura.
     *                    #item array ekate             -For detailed and summary report renta natura.
     *                    #item array farming           -Only for summary report renta natura.
     *                    #item string contract_type    -Only for summary report renta natura.
     *                    #item string area_type        -Only for summary report renta natura.
     *                    }
     * @param int $page -The current page number
     * @param int $rows -Rows per page
     * @param string $sort -A grid column by which the grid is sorted
     * @param string $order -The sort order ASC/DESC
     *
     * @return array result
     *               {
     *               #item array rows              -The results.
     *               #item string total            -The count of all results.
     *               #item array footer            -The footer results.
     *               }
     */
    public function readPaymentsReportsRentaNaturaGrid(array $data, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        // creating the default return
        $default = [
            'rows' => [],
            'total' => 0,
            'footer' => [],
        ];

        if (!$data['report_type']) {
            return $default;
        }

        // add grid properties to data
        $data['rows'] = $rows;
        $data['pager'] = $page;

        // check because for export and print $sort and $order are in $data array
        if ('' != $sort) {
            $data['sort'] = $sort;
        }
        if ('' != $order) {
            $data['order'] = $order;
        }
        if (!$data['report_type']) {
            return $default;
        }

        switch ($data['report_type']) {
            case 'summary-report-by-ekate-renta-natura':
                return $this->summaryReportNaturaByEkate($data);
            case 'detailed-report-renta-natura':
                return $this->detailedReportNaturaByContractsAndNaturaType($data);
            default:
                return $default;
        }
    }

    public function detailedReportNaturaByContractsAndNaturaType($data)
    {
        // init controllers
        $FarmingController = new FarmingController('Farming');
        $UsersController = new UsersController('Users');
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);

        // creating the default return
        $default = [
            'rows' => [],
            'total' => 0,
            'footer' => [
                [
                    'ekate' => '<b>ОБЩО</b>',
                ],
            ],
        ];

        $options = [
            'tablename' => $UserDbController->DbHandler->tableRentaTypes,
        ];

        $renta_results = $UserDbController->getItemsByParams($options, false, false);
        $rentCount = count($renta_results);
        // create renta types array
        for ($i = 0; $i < $rentCount; $i++) {
            $this->renta_types[$renta_results[$i]['id']] = $renta_results[$i]['name'] . '(' . $GLOBALS['Contracts']['renta_units'][$renta_results[$i]['unit']]['name'] . ')';
        }

        $options = [
            'return' => [
                'distinct c.id as contract_id', 'c.c_num as contract_name', 'kvs.ekate', 'c.farming_id', 'P.farming_year', 'pn.nat_type',
            ],
            'tablename' => $UserDbController->DbHandler->contractsPlotsRelTable,
            'where' => [
                'contract_id' => ['column' => 'contract_id', 'compare' => 'IN', 'prefix' => 'p', 'value' => $data['contracts']],
                'paid_from' => ['column' => 'paid_from', 'compare' => '=', 'prefix' => 'p', 'value' => 2],
                'nat_type' => ['column' => 'nat_type', 'compare' => '=', 'prefix' => 'pn', 'value' => $data['nat_type']],
                'ekate' => ['column' => 'ekate', 'compare' => '=', 'prefix' => 'kvs', 'value' => $data['ekate']],
            ],
            'sort' => $data['sort'],
            'order' => $data['order'],
        ];

        $resultCounter = $UserDbPaymentsController->getRentaNaturaByContractsAndNaturaType($options, false, false);
        $counter = count($resultCounter);

        $options['limit'] = $data['rows'];
        $options['offset'] = ($data['pager'] - 1) * $data['rows'];

        $results = $UserDbPaymentsController->getRentaNaturaByContractsAndNaturaType($options, false, false);

        $options = [
            'return' => [
                'distinct c.id as contract_id', 'c.c_num as contract_name', 'c.farming_id', 'cr.renta_id as nat_type',
            ],
            'tablename' => $UserDbController->DbHandler->contractsPlotsRelTable,
            'where' => [
                'contract_id' => ['column' => 'id', 'compare' => 'IN', 'prefix' => 'c', 'value' => $data['contracts']],
                'nat_type' => ['column' => 'renta_id', 'compare' => '=', 'prefix' => 'cr', 'value' => $data['nat_type']],
            ],
            'farming_year' => [$results[0]['farming_year']],
        ];

        $contractsWithoutPayments = $UserDbPaymentsController->getRentasAndAreasByContractsAndNaturaType($options, false, false);

        foreach ($contractsWithoutPayments as $wkey => $wres) {
            $contractsWithoutPayments[$wkey]['ekate'] = $results[0]['ekate'];
            $contractsWithoutPayments[$wkey]['farming_year'] = $results[0]['farming_year'];

            foreach ($results as $res) {
                if ($res['contract_id'] == $wres['contract_id']) {
                    continue 2;
                }
            }

            // add 1 to counter cause results increase with 1
            $counter++;
            $results[] = $contractsWithoutPayments[$wkey];
        }

        if (0 == $counter || !$counter) {
            return $default;
        }

        $resultsCount = count($results);
        for ($i = 0; $i < $resultsCount; $i++) {
            $options = [
                'return' => [
                    'name as farming',
                ],
                'where' => [
                    'id' => ['column' => 'id', 'compare' => '=', 'value' => $results[$i]['farming_id']],
                ],
            ];

            $aFarming = $FarmingController->getFarmings($options, false, false);

            $results[$i]['farming'] = $aFarming[0]['farming'];
            $aFarmingYear = [$results[$i]['farming_year']];
            $results[$i]['farming_year_id'] = $results[$i]['farming_year'];
            $results[$i]['farming_year'] = $GLOBALS['Farming']['years'][$results[$i]['farming_year']]['farming_year'];
            $results[$i]['renta_nat_type'] = $this->renta_types[$results[$i]['nat_type']];

            $aContract = [0 => $results[$i]['contract_id']];

            $options = [
                'return' => [
                    'cr.renta_value', '(cp.area_for_rent * po.percent / 100) as area',
                ],
                'tablename' => $UserDbController->DbHandler->contractsPlotsRelTable,
                'where' => [
                    'contract_id' => ['column' => 'contract_id', 'compare' => 'IN', 'prefix' => 'cp', 'value' => $aContract],
                    'renta_id' => ['column' => 'renta_id', 'compare' => '=', 'prefix' => 'cr', 'value' => $results[$i]['nat_type']],
                ],
            ];

            $aReturn = $UserDbPaymentsController->getRentasAndAreasByContractsAndNaturaType($options, false, false);

            $renta_value = 0;
            $area = 0;
            $renta_nat = 0;
            foreach ($aReturn as $key => $value) {
                $renta_value = $value['renta_value'];
                $renta_nat = $renta_nat + $value['renta_value'] * $value['area'];
                $area = $area + $value['area'];
            }

            $results[$i]['renta_value'] = $renta_value;
            $results[$i]['area_size'] = number_format($area, 3, '.', '');

            $results[$i]['renta_nat'] = number_format($renta_nat, 3, '.', '');

            $optionsNaturaCalculatedAmount = [
                'return' => [
                    'sum((CASE WHEN crn.nat_is_converted = TRUE THEN 0 ELSE crn.amount END) * cp.area_for_rent * po.percent / 100) as charged_renta_nat',
                ],
                'tablename' => $UserDbController->DbHandler->tableNaturaChargedRenta,
                'where' => [
                    'contract_id' => ['column' => 'contract_id', 'compare' => 'IN', 'prefix' => 'cr', 'value' => $aContract],
                    'nat_type' => ['column' => 'nat_type', 'compare' => '=', 'prefix' => 'crn', 'value' => $results[$i]['nat_type']],
                ],
            ];
            $aChargedRentaNat = $UserDbPaymentsController->getNaturaCalculatedAmountByContracts($optionsNaturaCalculatedAmount, false, false);

            if (null == $aChargedRentaNat[0]['charged_renta_nat']) {
                $results[$i]['charged_renta_nat'] = '';
            } else {
                $results[$i]['charged_renta_nat'] = number_format($aChargedRentaNat[0]['charged_renta_nat'], 2, '.', '');
            }

            $optionsStavkaNaturaCalculatedAmount = [
                'return' => [
                    'crn.amount as charged_renta_nat_stavka',
                ],
                'tablename' => $UserDbController->DbHandler->tableNaturaChargedRenta,
                'where' => [
                    'contract_id' => ['column' => 'contract_id', 'compare' => 'IN', 'prefix' => 'cr', 'value' => $aContract],
                    'nat_type' => ['column' => 'nat_type', 'compare' => '=', 'prefix' => 'crn', 'value' => $results[$i]['nat_type']],
                ],
            ];

            $aStavkaChargedRentaNat = $UserDbPaymentsController->getNaturaCalculatedAmountByContracts($optionsStavkaNaturaCalculatedAmount, false, false);
            $results[$i]['charged_renta_nat_stavka'] = number_format($aStavkaChargedRentaNat[0]['charged_renta_nat_stavka'], 2, '.', '');

            $options = [
                'return' => [
                    'SUM(pn.amount) as paid_renta_nat',
                ],
                'tablename' => $UserDbController->DbHandler->tablePayments,
                'where' => [
                    'contract_id' => ['column' => 'contract_id', 'compare' => 'IN', 'prefix' => 'p', 'value' => $aContract],
                    'paid_from' => ['column' => 'paid_from', 'compare' => '=', 'prefix' => 'p', 'value' => 2],
                    'nat_type' => ['column' => 'nat_type', 'compare' => '=', 'prefix' => 'pn', 'value' => $results[$i]['nat_type']],
                ],
            ];
            $aPaidRentaNat = $UserDbPaymentsController->getPaidRentaNaturaAmount($options, false, false);
            $results[$i]['paid_renta_nat'] = $aPaidRentaNat[0]['paid_renta_nat'];

            $aCalculatedValue = $UserDbPaymentsController->calculateUnpaidAndOverpaidRentaNat($results[$i]['renta_nat'], $aChargedRentaNat[0]['charged_renta_nat'], $results[$i]['paid_renta_nat']);
            $results[$i]['unpaid_renta_nat'] = number_format($aCalculatedValue['unpaid_renta_nat'], 2, '.', '');
            $results[$i]['overpaid_renta_nat'] = number_format($aCalculatedValue['overpaid_renta_nat'], 2, '.', '');
        }

        return [
            'rows' => $results,
            'total' => $counter,
        ];
    }

    public function summaryReportNaturaByEkate($data)
    {
        // init controllers
        $FarmingController = new FarmingController('Farming');
        $UsersController = new UsersController('Users');
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);

        // creating the default return
        $default = [
            'rows' => [],
            'total' => 0,
            'footer' => [
                [
                    'ekate' => '<b>ОБЩО</b>',
                ],
            ],
        ];

        $options = [
            'tablename' => $UserDbController->DbHandler->tableRentaTypes,
        ];

        $renta_results = $UserDbController->getItemsByParams($options, false, false);
        // create renta types array

        $rentCount = count($renta_results);
        for ($i = 0; $i < $rentCount; $i++) {
            $this->renta_types[$renta_results[$i]['id']] = $renta_results[$i]['name'] . '(' . $GLOBALS['Contracts']['renta_units'][$renta_results[$i]['unit']]['name'] . ')';
        }

        if (!is_array($data['year'])) {
            $aFarmingYear[] = $data['year'];
        } else {
            $aFarmingYear = $data['year'];
        }

        $options = [
            'return' => [
                'kvs.ekate', 'c.farming_id', 'p.farming_year', 'pn.nat_type', '"array_agg"(DISTINCT c.id) as contracts',
            ],
            'tablename' => $UserDbController->DbHandler->contractsPlotsRelTable,
            'where' => [
                'paid_from' => ['column' => 'paid_from', 'compare' => '=', 'prefix' => 'p', 'value' => 2],
                'farming_year' => ['column' => 'farming_year', 'compare' => 'IN', 'prefix' => 'p', 'value' => $aFarmingYear],
            ],
            'group' => 'kvs.ekate, c.farming_id, p.farming_year, pn.nat_type',
            'sort' => $data['sort'],
            'order' => $data['order'],
            'limit' => $data['rows'],
            'offset' => ($data['pager'] - 1) * $data['rows'],
        ];

        if ($data['ekate'] && !in_array('', $data['ekate'])) {
            $options['where']['ekate'] = ['column' => 'ekate', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $data['ekate']];
        }

        if ($data['farming'] && !in_array('', $data['farming'])) {
            $options['where']['farming_id'] = ['column' => 'farming_id', 'compare' => 'IN', 'prefix' => 'c', 'value' => $data['farming']];
        }

        if ($data['renta_nat_type'][0] > 0) {
            $options['where']['nat_type'] = ['column' => 'nat_type', 'compare' => '=', 'prefix' => 'pn', 'value' => $data['renta_nat_type'][0]];
        }

        if (strlen($data['contract_type']) > 0) {
            $options['where']['contract_type'] = ['column' => 'nm_usage_rights', 'compare' => '=', 'prefix' => 'c', 'value' => $data['contract_type']];
        }

        if (strlen($data['area_type']) > 0) {
            $options['where']['area_type'] = ['column' => 'area_type', 'compare' => '=', 'prefix' => 'kvs', 'value' => $data['area_type']];
        }

        $limit = $options['limit'];
        $offset = $options['offset'];
        unset($options['limit'], $options['offset']);

        $counter = $UserDbPaymentsController->getRentaNaturaContracts($options, false, false);
        $counter = count($counter);

        if (0 == $counter) {
            return $default;
        }

        $options['limit'] = $limit;
        $options['offset'] = $offset;

        $results = $UserDbPaymentsController->getRentaNaturaContracts($options, false, false);

        $options['farming_year'] = $options['where']['farming_year']['value'];

        unset($options['where']['paid_from'], $options['where']['farming_year'], $options['where']['nat_type']);

        $options['return'] = [
            'kvs.ekate', 'c.farming_id', '"array_agg"(DISTINCT c.id) as contracts, cr.renta_id',
        ];

        $options['group'] = 'kvs.ekate, c.farming_id, cr.renta_id';

        $contractsWithoutPayments = $UserDbPaymentsController->getContractsWithRentaNaturaData($options, false, false);

        $resultsCount = count($results);
        for ($i = 0; $i < $resultsCount; $i++) {
            $bCountracts = '';

            foreach ($contractsWithoutPayments as $bres) {
                if ($bres['ekate'] == $results[$i]['ekate']
                && $bres['farming_id'] == $results[$i]['farming_id']
                && $bres['renta_id'] == $results[$i]['nat_type']) {
                    $bCountracts = $bres['contracts'];

                    break;
                }
            }

            $bCountracts = explode(',', str_replace(['{', '}'], ['', ''], $bCountracts));
            $aContracts = explode(',', str_replace(['{', '}'], ['', ''], $results[$i]['contracts']));
            $aContracts = array_unique(array_merge($aContracts, $bCountracts));

            $j = 0;
            $results[$i]['contracts'] = [];
            $subresults = [];

            foreach ($aContracts as $aContract) {
                $subresults[$j] = $this->getDataByContract($aContract, $results[$i]);

                $results[$i]['farming_year'] = ($subresults[$j]['farming_year']) ? $subresults[$j]['farming_year'] : $results[$i]['farming_year'];
                $results[$i]['farming'] = $subresults[$j]['farming'];
                $results[$i]['land'] = $subresults[$j]['land'];
                $results[$i]['renta_nat_type'] = $subresults[$j]['renta_nat_type'];
                $results[$i]['contracts'][] = $aContract;

                $results[$i]['paid_renta_nat'] += $subresults[$j]['paid_renta_nat'];
                $results[$i]['renta_nat'] += $subresults[$j]['renta_nat'];
                $results[$i]['charged_renta_nat'] += $subresults[$j]['charged_renta_nat'];
                $results[$i]['unpaid_renta_nat'] += $subresults[$j]['unpaid_renta_nat'];
                $results[$i]['overpaid_renta_nat'] += $subresults[$j]['overpaid_renta_nat'];

                $j++;
            }
        }

        return [
            'rows' => $results,
            'total' => $counter,
        ];
    }

    private function getDataByContract($contract_id, $ekatteData)
    {
        // init controllers
        $FarmingController = new FarmingController('Farming');
        $UsersController = new UsersController('Users');
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);

        $subres = [];

        $options = [
            'return' => [
                'SUM(pn.amount) as paid_renta_nat',
            ],
            'tablename' => $UserDbController->DbHandler->tablePayments,
            'where' => [
                'contract_id' => ['column' => 'contract_id', 'compare' => '=', 'prefix' => 'p', 'value' => $contract_id],
                'paid_from' => ['column' => 'paid_from', 'compare' => '=', 'prefix' => 'p', 'value' => 2],
                'nat_type' => ['column' => 'nat_type', 'compare' => '=', 'prefix' => 'pn', 'value' => $ekatteData['nat_type']],
            ],
        ];

        $aPaidRentaNat = $UserDbPaymentsController->getPaidRentaNaturaAmount($options, false, false);

        $subres['paid_renta_nat'] = $aPaidRentaNat[0]['paid_renta_nat'];

        if (null == $subres['paid_renta_nat']) {
            $subres['paid_renta_nat'] = '0';
        }

        $subres['farming_year'] = $GLOBALS['Farming']['years'][$ekatteData['farming_year']]['farming_year'];

        $options = [
            'return' => [
                'name as farming',
            ],
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $ekatteData['farming_id']],
            ],
        ];

        $aFarming = $FarmingController->getFarmings($options, false, false);

        $subres['farming'] = $aFarming[0]['farming'];
        $subres['land'] = $UsersController->getEkatteName($ekatteData['ekate']);
        $subres['renta_nat_type'] = $this->renta_types[$ekatteData['nat_type']];

        $optionsAmountDueByContracts = [
            'return' => [
                'cr.renta_value', '(cp.area_for_rent * po.percent / 100) as area',
            ],
            'tablename' => $UserDbController->DbHandler->contractsPlotsRelTable,
            'where' => [
                'contract_id' => ['column' => 'contract_id', 'compare' => '=', 'prefix' => 'cp', 'value' => $contract_id],
                'renta_id' => ['column' => 'renta_id', 'compare' => '=', 'prefix' => 'cr', 'value' => $ekatteData['nat_type']],
            ],
        ];

        $subres['renta_nat'] = $UserDbPaymentsController->getNaturaAmountDueByContracts($optionsAmountDueByContracts, false, false);

        $optionsNaturaCalculatedAmount = [
            'return' => [
                'sum((CASE WHEN crn.nat_is_converted = TRUE THEN 0 ELSE crn.amount END) * cp.area_for_rent * po.percent / 100) as charged_renta_nat',
            ],
            'tablename' => $UserDbController->DbHandler->tableNaturaChargedRenta,
            'where' => [
                'contract_id' => ['column' => 'contract_id', 'compare' => '=', 'prefix' => 'cr', 'value' => $contract_id],
                'nat_type' => ['column' => 'nat_type', 'compare' => '=', 'prefix' => 'crn', 'value' => $ekatteData['nat_type']],
            ],
        ];

        $aChargedRentaNat = $UserDbPaymentsController->getNaturaCalculatedAmountByContracts($optionsNaturaCalculatedAmount, false, false);

        if (null == $aChargedRentaNat[0]['charged_renta_nat']) {
            $subres['charged_renta_nat'] = '0';
        } else {
            $subres['charged_renta_nat'] = number_format($aChargedRentaNat[0]['charged_renta_nat'], 2, '.', '');
        }

        $aCalculatedValue = $UserDbPaymentsController->calculateUnpaidAndOverpaidRentaNat($subres['renta_nat'], $aChargedRentaNat[0]['charged_renta_nat'], $subres['paid_renta_nat']);
        $subres['unpaid_renta_nat'] = number_format($aCalculatedValue['unpaid_renta_nat'], 2, '.', '');
        $subres['overpaid_renta_nat'] = number_format($aCalculatedValue['overpaid_renta_nat'], 2, '.', '');

        $subres['contracts'] = $contract_id;

        return $subres;
    }
}
