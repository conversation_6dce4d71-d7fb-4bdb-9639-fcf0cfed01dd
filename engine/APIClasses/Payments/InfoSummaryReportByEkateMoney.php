<?php

namespace TF\Engine\APIClasses\Payments;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Layers\LayersController;

/**
 * Info Summary Report By Ekate Money.
 *
 * @rpc-module Payments
 *
 * @rpc-service-id info-summary-report-by-ekate-money
 */
class InfoSummaryReportByEkateMoney extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'loadInfo' => ['method' => [$this, 'loadInfo']],

            'refreshReport' => ['method' => [$this, 'refreshReport']],
        ];
    }

    /**
     * Load Status and Date from Memcached.
     *
     * @api-method loadInfo
     *
     * @param int $reportId -The report id
     *
     * @return array result
     *               {
     *               #item string status    -The status
     *               #item string date      -The date
     *               }
     */
    public function loadInfo($reportId)
    {
        $LayersController = new LayersController('Layers');

        $hash = md5($reportId . '-' . $this->User->GroupID);
        $status = $LayersController->MemCache->get($hash . '-status');
        $date = $LayersController->MemCache->get($hash . '-date');

        return [
            'status' => $status,
            'date' => $date,
        ];
    }

    /**
     * Deletes memcached data.
     *
     * @api-method refreshReport
     *
     * @param int $reportId -The report id
     *
     * @return bool -True
     */
    public function refreshReport($reportId)
    {
        $LayersController = new LayersController('Layers');

        $hash = md5($reportId . '-' . $this->User->GroupID);

        $LayersController->MemCache->delete($hash);
        $LayersController->MemCache->delete($hash . '-status');
        $LayersController->MemCache->delete($hash . '-date');

        return true;
    }
}
