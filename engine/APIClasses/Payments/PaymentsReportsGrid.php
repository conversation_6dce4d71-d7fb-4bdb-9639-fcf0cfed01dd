<?php

namespace TF\Engine\APIClasses\Payments;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbOwners\UserDbOwnersController;
use TF\Engine\Plugins\Core\UserDbPayments\UserDbPaymentsController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Payroll Grid.
 *
 * @rpc-module Payments
 *
 * @rpc-service-id payments-reports-grid
 */
class PaymentsReportsGrid extends TRpcApiProvider
{
    public $renta_types = [];
    public $iterator = 0;
    public $paid_renta = 0;
    public $paid_renta_nat = 0;
    public $unpaid_renta = 0;
    public $unpaid_renta_nat = 0;

    // added for heritors
    public $relation_id = 0;
    public $percent = [];
    public $owner_id = 0;

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readPaymentsReportsGrid'],
                'validators' => [
                    'data' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * Read payments reports grid.
     *
     * @api-method read
     *
     * @param array $data
     *                    {
     *                    #item string type               -The payment report type
     *                    #item boolean is_heritor        -The user is heritor or not
     *                    #item string payroll_from_date  -The payroll from date
     *                    #item string payroll_to_date    -The payroll to date
     *                    #item string payroll_ekate      -The payroll ekate
     *                    #item string farming_year       -The farming year
     *                    #item int owner_id              -The owner id
     *                    }
     * @param int $page -The current page number
     * @param int $rows -Rows per page
     * @param string $sort -A grid column by which the grid is sorted
     * @param string $order -The sort order ASC/DESC
     *
     * @return array result
     *               {
     *               #item array rows         -The results
     *               #item string total       -The count of all results
     *               #item array footer       -The footer results
     *               }
     */
    public function readPaymentsReportsGrid(array $data, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        // init controller
        $LayersController = new LayersController('Layers');

        // creating the default return
        $default = [
            'rows' => [],
            'total' => 0,
            'footer' => [
                [
                    'ekate' => '<b>ОБЩО</b>',
                ],
            ],
        ];

        // add grid properties to data
        $data['rows'] = $rows;
        $data['pager'] = $page;

        // check because for export and print $sort and $order are in $data array
        if ('' != $sort) {
            $data['sort'] = $sort;
        }
        if ('' != $order) {
            $data['order'] = $order;
        }
        if (!$data['report_type']) {
            return $default;
        }

        switch ($data['report_type']) {
            case 'summary-report-by-ekate-money':
                // memcached
                $hash = md5('summary-report-by-ekate-money-' . $this->User->GroupID);

                $result = $LayersController->MemCache->get($hash);

                if ($result && $this->allowCachedResult($data)) {
                    return $result;
                }

                return $this->summaryReportByEkateMoney($hash, $data);
            case 'summary-report-by-ekate':
                return $this->summaryReportByEkate($data);
            case 'detail-report-by-owner':
                return $this->detailReportByOwner($data);
            default:
                return $default;
        }
    }

    public function allowCachedResult($data)
    {
        $allowCachedResult = true;

        if ($data['filter_clicked'] || '1' != $data['pager']) {
            $allowCachedResult = false;
        }

        return $allowCachedResult;
    }

    public function summaryReportByEkate($data)
    {
        // init controllers
        $UsersController = new UsersController('Users');
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);

        // creating the default return
        $default = [
            'rows' => [],
            'total' => 0,
            'footer' => [
                [
                    'ekate' => '<b>ОБЩО</b>',
                ],
            ],
        ];

        $options = [
            'tablename' => $UserDbController->DbHandler->tableRentaTypes,
        ];

        $renta_results = $UserDbController->getItemsByParams($options, false, false);
        $rentCount = count($renta_results);
        // create renta types array
        for ($i = 0; $i < $rentCount; $i++) {
            $this->renta_types[$renta_results[$i]['id']] = $renta_results[$i]['name'] . '(' . $GLOBALS['Contracts']['renta_units'][$renta_results[$i]['unit']]['name'] . ')';
        }

        // build subquery for personal use area
        $pu_areaQuery = "(SELECT area FROM {$UserDbController->DbHandler->tablePersonalUse} ipu
					WHERE ipu.year = {$data['year']}
					AND ipu.contract_id = c.id
					AND ipu.owner_id = o.id)";

        // build subquery for personal use charged renta
        $pu_charged_rentaQuery = "(SELECT area * renta FROM {$UserDbController->DbHandler->tablePersonalUse} ipu
					WHERE ipu.year = {$data['year']}
					AND ipu.contract_id = c.id
					AND ipu.owner_id = o.id)";

        // build subquery for personal use charged renta_nat
        $pu_charged_renta_natQuery = "(SELECT area * renta_nat FROM {$UserDbController->DbHandler->tablePersonalUse} ipu
					WHERE ipu.year = {$data['year']}
					AND ipu.contract_id = c.id
					AND ipu.owner_id = o.id)";

        // build main query(getting all payments by owners)
        $options = [
            'custom_counter' => 'COUNT(DISTINCT(ekate))',
            'sort' => $data['sort'],
            'order' => $data['order'],
            'limit' => $data['rows'],
            'offset' => ($data['pager'] - 1) * $data['rows'],
            'return' => [
                'ekate',
                "array_agg('&quot;' || c.id || '-' || o.id || '&quot;:' || {$pu_areaQuery}) as pu_area_by_owner",
                "array_agg('&quot;' || c.id || '-' || o.id || '&quot;:' || {$pu_areaQuery} * CASE WHEN a.renta IS NULL THEN c.renta ELSE a.renta END) as pu_renta_by_owner",
                "array_agg('&quot;' || c.id || '-' || o.id || '&quot;:' || {$pu_areaQuery} * CASE WHEN a.renta_nat IS NULL THEN c.renta_nat ELSE a.renta_nat END) as pu_renta_nat_by_owner",
                "array_agg('&quot;' || c.id || '-' || o.id || '&quot;:' || {$pu_charged_rentaQuery}) as pu_charged_renta_by_owner",
                "array_agg('&quot;' || c.id || '-' || o.id || '&quot;:' || {$pu_charged_renta_natQuery}) as pu_charged_renta_nat_by_owner",
                'SUM(trunc(CAST((CASE WHEN a.renta IS NULL THEN c.renta ELSE a.renta END) * area_for_rent * percent / 100 AS numeric), 3)) AS renta',
                "array_agg('&quot;' || c.id || '&quot;:' || CASE WHEN a.renta_nat_type_id IS NULL THEN c.renta_nat_type_id ELSE a.renta_nat_type_id END) as renta_nat_type_id",
                'array_agg((CASE WHEN a.renta_nat IS NULL THEN c.renta_nat ELSE a.renta_nat END) * area_for_rent * percent / 100) as renta_nat',
                'SUM(CASE WHEN cr.nat_is_converted = TRUE THEN cr.renta_nat * area_for_rent * percent / 100 * nat_unit_price ELSE 0 END) as converted_renta_nat',
                'SUM(cr.renta * area_for_rent * percent / 100) AS charged_renta',
                'array_agg((CASE WHEN cr.nat_is_converted = TRUE THEN 0 ELSE cr.renta_nat END) * area_for_rent * percent / 100) AS charged_renta_nat',
                "SUM((CASE WHEN annex_action = 'added' THEN area_for_rent ELSE 0 END) * percent / 100) AS area",
                'array_agg(DISTINCT c.id) as contract_id',
            ],
            'where' => [
                'is_heritor' => ['column' => 'is_heritor', 'compare' => '=', 'prefix' => 'po', 'value' => 'FALSE'],
                'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
                'is_annex' => ['column' => 'is_annex', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
                'active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'TRUE'],
                'c_type' => ['column' => 'nm_usage_rights', 'compare' => 'IN', 'prefix' => 'c', 'value' => [2, 3]],
                // filters
                // plots filter
                'ekate' => ['column' => 'ekate', 'compare' => '=', 'prefix' => 'kvs', 'value' => $data['ekate']],
                'masiv' => ['column' => 'masiv', 'compare' => '=', 'prefix' => 'kvs', 'value' => $data['masiv']],
                'number' => ['column' => 'number', 'compare' => '=', 'prefix' => 'kvs', 'value' => $data['number']],
                'category' => ['column' => 'category', 'compare' => '=', 'prefix' => 'kvs', 'value' => $data['category']],
                'area_type' => ['column' => 'area_type', 'compare' => '=', 'prefix' => 'kvs', 'value' => $data['area_type']],
                // contract filter
                'c_num' => ['column' => 'c_num', 'compare' => 'ILIKE', 'prefix' => 'c', 'value' => $data['c_num']],
                'contract_type' => ['column' => 'nm_usage_rights', 'compare' => '=', 'prefix' => 'c', 'value' => $data['contract_type']],
                'farming' => ['column' => 'farming_id', 'compare' => '=', 'prefix' => 'c', 'value' => $data['farming']],
                // owner filter
                'owner_name' => ['column' => 'name', 'compare' => 'ILIKE', 'prefix' => 'o', 'value' => $data['owner_name']],
                'owner_egn' => ['column' => 'egn', 'compare' => '=', 'prefix' => 'o', 'value' => $data['owner_egn']],
                'rep_name' => ['column' => 'rep_name', 'compare' => 'ILIKE', 'prefix' => 'rep', 'value' => $data['rep_name']],
                'rep_egn' => ['column' => 'rep_egn', 'compare' => '=', 'prefix' => 'rep', 'value' => $data['rep_egn']],
                'company_name' => ['column' => 'company_name', 'compare' => 'ILIKE', 'prefix' => 'o', 'value' => $data['company_name']],
                'company_eik' => ['column' => 'eik', 'compare' => '=', 'prefix' => 'o', 'value' => $data['company_eik']],
            ],
            'group' => 'ekate',
            // this parameter will be used for joining charged renta table
            'chosen_year' => $data['year'],
            'start_date' => $GLOBALS['Farming']['years'][$data['year']]['year'] . '-09-30',
            'due_date' => ($GLOBALS['Farming']['years'][$data['year']]['year'] - 1) . '-10-01',
        ];

        // adding with or without renta nat filter
        if (isset($data['with_renta_nat']) && (1 == $data['with_renta_nat'] || 0 == $data['with_renta_nat'])) {
            $options['where']['with_renta_nat'] = [
                'column' => '(CASE WHEN a.renta_nat_type_id IS NULL THEN c.renta_nat_type_id ELSE a.renta_nat_type_id END)',
                'compare' => (1 == $data['with_renta_nat']) ? '>' : '=',
                'value' => '0',
            ];
        }

        $counter = $UserDbPaymentsController->getPaymentsForOwners($options, true, false);
        if (0 == $counter[0]['count']) {
            return $default;
        }

        // get all ekate data
        $ekateData = $UsersController->getAllEkatteData();
        $ekateCount = count($ekateData);
        for ($i = 0; $i < $ekateCount; $i++) {
            $ekateNames[$ekateData[$i]['ekatte_code']] = $ekateData[$i]['ekatte_name'];
        }

        $results = $UserDbPaymentsController->getPaymentsForOwners($options, false, false);
        $resultsCount = count($results);
        $renta_nat_helper = [];

        // init total variables
        $total_renta = 0;
        $total_charged_renta = 0;
        $pu_charged_renta_nat = [];
        $charged_renta_nat = [];
        for ($i = 0; $i < $resultsCount; $i++) {
            $renta_nat = [];
            $pu_renta_nat = [];
            $renta_nat_by_type = [];
            $charged_renta_nat_by_type = [];

            $results[$i]['renta_nat_type_id'] = str_replace('&quot;', '"', $results[$i]['renta_nat_type_id']);
            $renta_nat_type_id = json_decode($results[$i]['renta_nat_type_id'], true);

            $contract_ids = str_replace('{', '(', $results[$i]['contract_id']);
            $contract_ids = str_replace('}', ')', $contract_ids);

            $renta_naturas = $UserDbController->DbHandler->getDataByQuery(
                "SELECT pc.contract_id, cr.renta_id,array_agg(contract_area), 
			    SUM(cr.renta_value * (CASE WHEN pc.annex_action = 'added' THEN area_for_rent ELSE 0 END) * percent / 100) as nat_amount FROM su_contracts c
			    left join su_contracts a on (c.id = a.parent_id)
			    join su_contracts_plots_rel pc on (pc.contract_id = (CASE WHEN a.id IS NULL THEN c.id ELSE a.id END))
			    join su_plots_owners_rel po on (po.pc_rel_id = pc.id)
			    join su_contracts_rents cr on (pc.contract_id = cr.contract_id)
			    WHERE c.id IN {$contract_ids}
			    AND po.is_heritor = false
			    GROUP BY pc.contract_id, cr.renta_id"
            );

            $results[$i]['pu_renta_nat_by_owner'] = str_replace('&quot;', '"', $results[$i]['pu_renta_nat_by_owner']);
            $results[$i]['pu_renta_nat_by_owner'] = str_replace('NULL,', '', $results[$i]['pu_renta_nat_by_owner']);
            $results[$i]['pu_renta_nat_by_owner'] = str_replace(',NULL', '', $results[$i]['pu_renta_nat_by_owner']);
            $results[$i]['pu_renta_nat_by_owner'] = str_replace('NULL', '', $results[$i]['pu_renta_nat_by_owner']);
            $pu_renta_nat_by_owners = json_decode($results[$i]['pu_renta_nat_by_owner'], true);

            foreach ($pu_renta_nat_by_owners as $key => $value) {
                $keys = explode('-', $key);
                $contractID = $keys[0];
                $pu_renta_nat[$contractID] += $value;
            }

            $results[$i]['pu_charged_renta_nat_by_owner'] = str_replace('&quot;', '"', $results[$i]['pu_charged_renta_nat_by_owner']);
            $results[$i]['pu_charged_renta_nat_by_owner'] = str_replace('NULL,', '', $results[$i]['pu_charged_renta_nat_by_owner']);
            $results[$i]['pu_charged_renta_nat_by_owner'] = str_replace(',NULL', '', $results[$i]['pu_charged_renta_nat_by_owner']);
            $results[$i]['pu_charged_renta_nat_by_owner'] = str_replace('NULL', '', $results[$i]['pu_charged_renta_nat_by_owner']);
            $pu_charged_renta_nat_by_owners = json_decode($results[$i]['pu_charged_renta_nat_by_owner'], true);

            foreach ($pu_charged_renta_nat_by_owners as $key => $value) {
                $keys = explode('-', $key);
                $contractID = $keys[0];
                $pu_charged_renta_nat[$contractID] += $value;
            }

            $contract_id_array = explode(',', trim($results[$i]['contract_id'], '{}'));
            $contract_id_count = count($contract_id_array);
            $renta_nat_array = explode(',', trim($results[$i]['renta_nat'], '{}'));
            $charged_renta_nat_array = explode(',', trim($results[$i]['charged_renta_nat'], '{}'));
            for ($j = 0; $j < $contract_id_count; $j++) {
                $contractID = $contract_id_array[$j];
                $renta_nat[$contractID] += $renta_nat_array[$j];
                $charged_renta_nat[$contractID] += $charged_renta_nat_array[$j];
            }

            foreach ($renta_nat as $key => $value) {
                $typeID = $renta_nat_type_id[$key];
                $renta_nat_by_type[$typeID] += ($value - $pu_renta_nat[$key]);
            }

            foreach ($renta_nat_by_type as $key => $value) {
                if (0 != $key) {
                    $results[$i]['renta_nat_text'] .= number_format($value, 2, '.', '') . ' X ' . $this->renta_types[$key] . ',<br/>';
                }
            }
            // renta_nat
            $renta_nat_new = [];
            $renta_nat_by_type_new = [];
            for ($j = 0; $j < $contract_id_count; $j++) {
                $contractID = $contract_id_array[$j];
                foreach ($renta_naturas as $rn) {
                    if ($rn['contract_id'] == $contractID) {
                        $renta_nat_new[$contractID][$rn['renta_id']] += $rn['nat_amount'];
                    }
                }
            }
            foreach ($renta_nat_new as $contract_id => $contract_renta_nats) {
                foreach ($contract_renta_nats as $type_id => $renta_val) {
                    $renta_nat_by_type_new[$type_id] += $renta_val;
                }
            }
            foreach ($renta_nat_by_type_new as $key => $value) {
                $renta_nat_helper[$i][$key] .= number_format($value, 2, '.', '') . ' X ' . $this->renta_types[$key] . ',<br/>';
            }
            // end renta_nat
            foreach ($charged_renta_nat as $key => $value) {
                $typeID = $renta_nat_type_id[$key];
                $charged_renta_nat_by_type[$typeID] += ($value - $pu_charged_renta_nat[$key]);
            }

            foreach ($charged_renta_nat_by_type as $key => $value) {
                if (0 != $key) {
                    $results[$i]['charged_renta_nat_text'] .= number_format($value, 2, '.', '') . ' X ' . $this->renta_types[$key] . ',<br/>';
                }
            }

            if (!$results[$i]['renta_nat_text']) {
                $results[$i]['renta_nat_text'] = '-';
            } else {
                $results[$i]['renta_nat_text'] = rtrim($results[$i]['renta_nat_text'], ',<br/>');
            }

            if (!$results[$i]['charged_renta_nat_text']) {
                $results[$i]['charged_renta_nat_text'] = '-';
            } else {
                $results[$i]['charged_renta_nat_text'] = rtrim($results[$i]['charged_renta_nat_text'], ',<br/>');
            }

            $results[$i]['pu_area_by_owner'] = str_replace('&quot;', '"', $results[$i]['pu_area_by_owner']);
            $results[$i]['pu_area_by_owner'] = str_replace('NULL,', '', $results[$i]['pu_area_by_owner']);
            $results[$i]['pu_area_by_owner'] = str_replace(',NULL', '', $results[$i]['pu_area_by_owner']);
            $results[$i]['pu_area_by_owner'] = str_replace('NULL', '', $results[$i]['pu_area_by_owner']);
            $pu_area_by_owners = json_decode($results[$i]['pu_area_by_owner'], true);
            foreach ($pu_area_by_owners as $key => $value) {
                $results[$i]['pu_area'] += $value;
            }

            $results[$i]['pu_renta_by_owner'] = str_replace('&quot;', '"', $results[$i]['pu_renta_by_owner']);
            $results[$i]['pu_renta_by_owner'] = str_replace('NULL,', '', $results[$i]['pu_renta_by_owner']);
            $results[$i]['pu_renta_by_owner'] = str_replace(',NULL', '', $results[$i]['pu_renta_by_owner']);
            $results[$i]['pu_renta_by_owner'] = str_replace('NULL', '', $results[$i]['pu_renta_by_owner']);
            $pu_renta_by_owners = json_decode($results[$i]['pu_renta_by_owner'], true);
            foreach ($pu_renta_by_owners as $key => $value) {
                $results[$i]['pu_renta'] += $value;
            }

            $results[$i]['pu_charged_renta_by_owner'] = str_replace('&quot;', '"', $results[$i]['pu_charged_renta_by_owner']);
            $results[$i]['pu_charged_renta_by_owner'] = str_replace('NULL,', '', $results[$i]['pu_charged_renta_by_owner']);
            $results[$i]['pu_charged_renta_by_owner'] = str_replace(',NULL', '', $results[$i]['pu_charged_renta_by_owner']);
            $results[$i]['pu_charged_renta_by_owner'] = str_replace('NULL', '', $results[$i]['pu_charged_renta_by_owner']);
            $pu_charged_renta_by_owners = json_decode($results[$i]['pu_charged_renta_by_owner'], true);
            foreach ($pu_charged_renta_by_owners as $key => $value) {
                $results[$i]['pu_charged_renta'] += $value;
            }

            $results[$i]['area'] = $results[$i]['area'] - $results[$i]['pu_area'];
            $results[$i]['renta'] = $results[$i]['renta'] - $results[$i]['pu_renta'];
            $results[$i]['charged_renta'] = $results[$i]['charged_renta'] - $results[$i]['pu_charged_renta'] + $results[$i]['converted_renta_nat'];

            $results[$i]['land'] = $ekateNames[$results[$i]['ekate']];

            // format renta values
            $results[$i]['renta'] = number_format($results[$i]['renta'], 2, '.', '');
            $results[$i]['charged_renta'] = number_format($results[$i]['charged_renta'], 2, '.', '');

            // calculate footer datа
            $total_renta += $results[$i]['renta'];
            $total_charged_renta += $results[$i]['charged_renta'];
        }

        $superRes = [];
        $j = 0;

        for ($i = 0; $i < $resultsCount; $i++) {
            if (isset($renta_nat_helper[$i])) {
                foreach ($renta_nat_helper[$i] as $rnt) {
                    $superRes[$j] = $results[$i];
                    $superRes[$j]['renta_nat_text'] = $rnt;
                    $j++;
                }
            } else {
                $superRes[$j] = $results[$i];
                $j++;
            }
        }

        return [
            'rows' => $superRes,
            'total' => $counter[0]['count'],
            'footer' => [
                [
                    'ekate' => '<b>ОБЩО</b>',
                    'renta' => number_format($total_renta, 2, '.', ''),
                    'charged_renta' => number_format($total_charged_renta, 2, '.', ''),
                ],
            ],
        ];
    }

    public function detailReportByOwner($data)
    {
        // init controllers
        $UsersController = new UsersController('Users');
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);

        // creating the default return
        $default = [
            'rows' => [],
            'total' => 0,
            'footer' => [
                [
                    'ekate' => '<b>ОБЩО</b>',
                ],
            ],
        ];

        // get personal use
        $options = [
            'tablename' => $UserDbController->DbHandler->tablePersonalUse,
            'return' => [
                'owner_id',
                'contract_id',
                'area as pu_area',
                'renta * area as pu_renta',
                'renta_nat * area as pu_renta_nat',
            ],
            'where' => [
                // personal use data
                'year' => ['column' => 'year', 'compare' => '=', 'value' => $data['year']],
            ],
        ];

        $pu_results = $UserDbController->getItemsByParams($options);
        $puCount = count($pu_results);
        $personal_use_array = [];

        for ($i = 0; $i < $puCount; $i++) {
            $personal_use_array[$pu_results[$i]['owner_id']][$pu_results[$i]['contract_id']] = [
                'pu_area' => $pu_results[$i]['pu_area'],
                'pu_renta' => $pu_results[$i]['pu_renta'],
                'pu_renta_nat' => $pu_results[$i]['pu_renta_nat'],
            ];
        }

        $options = [
            'tablename' => $UserDbController->DbHandler->tableRentaTypes,
        ];

        $renta_results = $UserDbController->getItemsByParams($options, false, false);
        $rentCount = count($renta_results);
        // create renta types array
        for ($i = 0; $i < $rentCount; $i++) {
            $this->renta_types[$renta_results[$i]['id']] = $renta_results[$i]['name'] . '(' . $GLOBALS['Contracts']['renta_units'][$renta_results[$i]['unit']]['name'] . ')';
        }

        // build main query(getting all payments by owners)
        $options = [
            'custom_counter' => 'COUNT(*)',
            'sort' => $data['sort'],
            'order' => $data['order'],
            'limit' => $data['rows'],
            'offset' => ($data['pager'] - 1) * $data['rows'],
            'return' => [
                'o.id as owner_id', 'c.id as contract_id', 'c.c_num',
                'array_agg(DISTINCT(ekate)) as ekate_array',
                '(CASE WHEN a.renta IS NULL THEN c.renta ELSE a.renta END) as contract_renta',
                "(CASE WHEN owner_type = 1 THEN name || ' ' || surname || ' ' || lastname ELSE company_name END) as owner_names",
                'SUM(area_for_rent * percent / 100) AS area',
                'SUM((CASE WHEN a.renta IS NULL THEN c.renta ELSE a.renta END) * area_for_rent * percent / 100) AS renta',
            ],
            'where' => [
                'is_heritor' => ['column' => 'is_heritor', 'compare' => '=', 'prefix' => 'po', 'value' => 'FALSE'],
                'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
                'is_annex' => ['column' => 'is_annex', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
                'active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'TRUE'],
                'c_type' => ['column' => 'nm_usage_rights', 'compare' => 'IN', 'prefix' => 'c', 'value' => [2, 3]],
                // filters
                // plots filter
                'ekate' => ['column' => 'ekate', 'compare' => '=', 'prefix' => 'kvs', 'value' => $data['ekate']],
                'masiv' => ['column' => 'masiv', 'compare' => '=', 'prefix' => 'kvs', 'value' => $data['masiv']],
                'number' => ['column' => 'number', 'compare' => '=', 'prefix' => 'kvs', 'value' => $data['number']],
                'category' => ['column' => 'category', 'compare' => '=', 'prefix' => 'kvs', 'value' => $data['category']],
                'area_type' => ['column' => 'area_type', 'compare' => '=', 'prefix' => 'kvs', 'value' => $data['area_type']],
                // contract filter
                'c_num' => ['column' => 'c_num', 'compare' => 'ILIKE', 'prefix' => 'c', 'value' => $data['c_num']],
                'contract_type' => ['column' => 'nm_usage_rights', 'compare' => '=', 'prefix' => 'c', 'value' => $data['contract_type']],
                'farming' => ['column' => 'farming_id', 'compare' => '=', 'prefix' => 'c', 'value' => $data['farming']],
                // owner filter
                'owner_name' => ['column' => 'name', 'compare' => 'ILIKE', 'prefix' => 'o', 'value' => $data['owner_name']],
                'owner_egn' => ['column' => 'egn', 'compare' => '=', 'prefix' => 'o', 'value' => $data['owner_egn']],
                'rep_name' => ['column' => 'rep_name', 'compare' => 'ILIKE', 'prefix' => 'rep', 'value' => $data['rep_name']],
                'rep_egn' => ['column' => 'rep_egn', 'compare' => '=', 'prefix' => 'rep', 'value' => $data['rep_egn']],
                'company_name' => ['column' => 'company_name', 'compare' => 'ILIKE', 'prefix' => 'o', 'value' => $data['company_name']],
                'company_eik' => ['column' => 'eik', 'compare' => '=', 'prefix' => 'o', 'value' => $data['company_eik']],
            ],
            'group' => 'o.id, c.id, a.id',
            // this parameter will be used for joining charged renta table
            'chosen_year' => $data['year'],
            'start_date' => $GLOBALS['Farming']['years'][$data['year']]['year'] . '-09-30',
            'due_date' => ($GLOBALS['Farming']['years'][$data['year']]['year'] - 1) . '-10-01',
        ];

        // adding with or without renta nat filter
        if (isset($data['with_renta_nat']) && (1 == $data['with_renta_nat'] || 0 == $data['with_renta_nat'])) {
            $options['where']['with_renta_nat'] = [
                'column' => '(CASE WHEN a.renta_nat_type_id IS NULL THEN c.renta_nat_type_id ELSE a.renta_nat_type_id END)',
                'compare' => (1 == $data['with_renta_nat']) ? '>' : '=',
                'value' => '0',
            ];
        }

        $counter = $UserDbPaymentsController->getPaymentsForOwners($options, true, false);

        if (0 == $counter[0]['count']) {
            return $default;
        }

        // get all ekate data
        $ekateData = $UsersController->getAllEkatteData();
        $ekateCount = count($ekateData);
        for ($i = 0; $i < $ekateCount; $i++) {
            $ekateNames[$ekateData[$i]['ekatte_code']] = $ekateData[$i]['ekatte_name'];
        }

        $results = $UserDbPaymentsController->getPaymentsForOwners($options, false, false);
        $resultsCount = count($results);
        for ($i = 0; $i < $resultsCount; $i++) {
            // assign variables for easy access
            $ownerID = $results[$i]['owner_id'];
            $contractID = $results[$i]['contract_id'];
            $pu_area = $personal_use_array[$ownerID][$contractID]['pu_area'];
            $pu_renta = $personal_use_array[$ownerID][$contractID]['pu_renta'];
            $pu_renta_nat = $personal_use_array[$ownerID][$contractID]['pu_renta_nat'];

            $results[$i]['area'] = $results[$i]['area'] - $pu_area;
            $results[$i]['renta'] = $results[$i]['renta'] - ($pu_area * $results[$i]['contract_renta']);

            $ekate_array = explode(',', trim($results[$i]['ekate_array'], '{}'));
            $ekate_count = count($ekate_array);
            for ($j = 0; $j < $ekate_count; $j++) {
                $results[$i]['land'] .= $ekateNames[$ekate_array[$j]] . ' (' . $ekate_array[$j] . '),<br/>';
            }

            $results[$i]['land'] = trim($results[$i]['land'], ',<br/>');

            // format renta values
            $results[$i]['renta'] = number_format($results[$i]['renta'], 2, '.', '');
        }

        // init total variables
        $total_renta = 0;
        $total_charged_renta = 0;

        return [
            'rows' => $results,
            'total' => $counter[0]['count'],
            'footer' => [
                [
                    'ekate' => '<b>ОБЩО</b>',
                    'renta' => number_format($total_renta, 2, '.', ''),
                    'charged_renta' => number_format($total_charged_renta, 2, '.', ''),
                ],
            ],
        ];
    }

    public function summaryReportByEkateMoney($hash, $data)
    {
        // init controllers
        $FarmingController = new FarmingController('Farming');
        $UsersController = new UsersController('Users');
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);
        $LayersController = new LayersController('Layers');

        $result_memcached = $LayersController->MemCache->get($hash);

        if (!$result_memcached) {
            $LayersController->MemCache->delete($hash . '-status');
            $LayersController->MemCache->add($hash . '-status', 'Processing', $LayersController->default_memcache_expire);
        }

        // creating the default return
        $default = [
            'rows' => [],
            'total' => 0,
            'footer' => [
                [
                    'ekate' => '<b>ОБЩО</b>',
                ],
            ],
        ];

        $options = [
            'tablename' => $UserDbController->DbHandler->tableRentaTypes,
        ];

        $renta_results = $UserDbController->getItemsByParams($options, false, false);
        $rentCount = count($renta_results);
        // create renta types array
        for ($i = 0; $i < $rentCount; $i++) {
            $this->renta_types[$renta_results[$i]['id']] = $renta_results[$i]['name'] . '(' . $GLOBALS['Contracts']['renta_units'][$renta_results[$i]['unit']]['name'] . ')';
        }

        // build subquery for personal use area
        $year_string = $data['year'];
        if (is_array($data['year'])) {
            $year_string = implode(',', $data['year']);
        }
        $year_array = (array)$data['year'];
        $farming_array = (array)$data['farming'];
        if (in_array('', $farming_array)) {
            $farming_array = null;
        }
        $original_ekate_array = (array)$data['ekate'];
        $ekate_array = $original_ekate_array;
        if (in_array('', $ekate_array)) {
            $ekate_array = null;
        }

        $pu_areaQuery = "(SELECT area FROM {$UserDbController->DbHandler->tablePersonalUse} ipu
	    WHERE ipu.year in ({$year_string})
        AND ipu.contract_id = c.id
        AND ipu.owner_id = o.id)";

        // build subquery for personal use charged renta
        $pu_charged_rentaQuery = "(SELECT area * renta FROM {$UserDbController->DbHandler->tablePersonalUse} ipu
        WHERE ipu.year in ({$year_string})
        AND ipu.contract_id = c.id
        AND ipu.owner_id = o.id)";

        // build subquery for converted renta natura
        $converted_renta_naturaQuery = "(SELECT (CASE WHEN crn.nat_is_converted = TRUE THEN SUM(crn.amount * crn.nat_unit_price) ELSE 0 END) as converted FROM {$UserDbController->DbHandler->tableNaturaChargedRenta} crn
        WHERE crn.renta_id = cr.id
        group by crn.nat_is_converted)";

        $options = [
            'custom_counter' => 'COUNT(DISTINCT(ekate))',
            'sort' => $data['sort'],
            'order' => $data['order'],
            'limit' => $data['rows'],
            'offset' => ($data['pager'] - 1) * $data['rows'],
            'return' => [
                'ekate',
                "array_agg('&quot;' || c.id || '-' || o.id || '&quot;:' || {$pu_areaQuery}) as pu_area_by_owner",
                "array_agg('&quot;' || c.id || '-' || o.id || '&quot;:' || {$pu_areaQuery} * CASE WHEN a.renta IS NULL THEN c.renta ELSE a.renta END) as pu_renta_by_owner",
                "array_agg('&quot;' || c.id || '-' || o.id || '&quot;:' || {$pu_charged_rentaQuery}) as pu_charged_renta_by_owner",
                "SUM(trunc(CAST((CASE WHEN a.renta IS NULL THEN c.renta ELSE a.renta END) * (CASE WHEN annex_action = 'added' THEN area_for_rent ELSE 0 END) * po.percent / 100 AS numeric), 3)) AS renta",
                "array_agg('&quot;' || c.id || '-' || o.id || '-' || pc.plot_id ||'&quot;:' || CASE WHEN annex_action = 'added' THEN area_for_rent ELSE 0 END) as area_by_owner",
                "SUM(cr.renta * (CASE WHEN annex_action = 'added' THEN area_for_rent ELSE 0 END) * po.percent / 100) AS charged_renta",
                'array_agg(cr.id) as charged_renta_ids',
                'array_agg(DISTINCT c.farming_id) as farmings',
                "SUM((CASE WHEN annex_action = 'added' THEN area_for_rent ELSE 0 END) * po.percent / 100) AS area",
                'array_agg(DISTINCT c.id) as contract_id',
            ],
            'where' => [
                'is_heritor' => ['column' => 'is_heritor', 'compare' => '=', 'prefix' => 'po', 'value' => 'FALSE'],
                'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
                'is_annex' => ['column' => 'is_annex', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
                'active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'TRUE'],
                'c_type' => ['column' => 'nm_usage_rights', 'compare' => 'IN', 'prefix' => 'c', 'value' => [2, 3, 5]],
                // filters
                // plots filter
                'ekate' => ['column' => 'ekate', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $ekate_array],
                'category' => ['column' => 'category', 'compare' => '=', 'prefix' => 'kvs', 'value' => $data['category']],
                'area_type' => ['column' => 'area_type', 'compare' => '=', 'prefix' => 'kvs', 'value' => $data['area_type']],
                // contract filter
                'c_num' => ['column' => 'c_num', 'compare' => 'ILIKE', 'prefix' => 'c', 'value' => $data['c_num']],
                'contract_type' => ['column' => 'nm_usage_rights', 'compare' => '=', 'prefix' => 'c', 'value' => $data['contract_type']],
                'farming' => ['column' => 'farming_id', 'compare' => 'IN', 'prefix' => 'c', 'value' => $farming_array],
            ],
            'group' => 'ekate',
            // this parameter will be used for joining charged renta table
            'chosen_year' => $year_array,
            'start_date' => $GLOBALS['Farming']['years'][(int)max($year_array)]['year'] . '-09-30',
            'due_date' => ($GLOBALS['Farming']['years'][(int)min($year_array)]['year'] - 1) . '-10-01',
        ];

        // adding with or without renta nat filter
        if (isset($data['with_renta_nat']) && (1 == $data['with_renta_nat'] || 0 == $data['with_renta_nat'])) {
            $options['where']['with_renta_nat'] = [
                'column' => '(CASE WHEN a.renta_nat_type_id IS NULL THEN c.renta_nat_type_id ELSE a.renta_nat_type_id END)',
                'compare' => (1 == $data['with_renta_nat']) ? '>' : '=',
                'value' => '0',
            ];
        }

        $counter = $UserDbPaymentsController->getPaymentsForOwnersReport($options, true, false);
        if (0 == $counter[0]['count']) {
            $LayersController->MemCache->delete($hash . '-date');
            $LayersController->MemCache->add($hash . '-date', date('d.m.Y H:i'), $LayersController->default_memcache_expire);

            $LayersController->MemCache->delete($hash . '-status');
            $LayersController->MemCache->add($hash . '-status', 'Done', $LayersController->default_memcache_expire);

            return $default;
        }

        // get all ekate data
        $ekateData = $UsersController->getAllEkatteData();
        $ekateCount = count($ekateData);
        for ($i = 0; $i < $ekateCount; $i++) {
            $ekateNames[$ekateData[$i]['ekatte_code']] = $ekateData[$i]['ekatte_name'];
        }
        $total_person_contract_area = [];
        $person_contract_total_area_ekate = [];
        $pu_area_by_owners = [];
        $results = $UserDbPaymentsController->getPaymentsForOwnersReport($options, false, false);
        $resultsCount = count($results);
        for ($i = 0; $i < $resultsCount; $i++) {
            $contract_ids_string = trim($results[$i]['contract_id'], '{}');
            $charged_renta_ids = trim($results[$i]['charged_renta_ids'], '{}');
            $converted_renta_nat = $UserDbController->DbHandler->getDataByQuery("SELECT ekate, SUM(crn.amount*crn.nat_unit_price*pc.area_for_rent*po.percent/100) as converted FROM {$UserDbController->DbHandler->tableNaturaChargedRenta} crn
            JOIN {$UserDbController->DbHandler->tableChargedRenta} as cr ON (cr.id = crn.renta_id)
            JOIN {$UserDbController->DbHandler->contractsPlotsRelTable} pc ON(cr.plot_id = pc.plot_id and pc.contract_id = cr.contract_id)
            JOIN {$UserDbController->DbHandler->plotsOwnersRelTable} po ON(po.pc_rel_id = pc.id)
            JOIN {$UserDbController->DbHandler->tableKVS} kvs ON(kvs.gid = pc.plot_id)
                WHERE crn.renta_id IN ({$charged_renta_ids})
                AND crn.nat_is_converted = true
                AND po.is_heritor = false
                AND pc.annex_action = 'added'
                group by kvs.ekate");
            $results[$i]['land'] = $ekateNames[$results[$i]['ekate']];
            foreach ($converted_renta_nat as $crn) {
                if ($crn['ekate'] = $results[$i]['ekate']) {
                    $results[$i]['charged_renta'] += $crn['converted'];
                }
            }
            $farming_years = [];
            foreach ($year_array as $year) {
                $farming_years[] = $GLOBALS['Farming']['years'][$year]['farming_year'];
            }
            $results[$i]['farming_year'] = implode(',', $farming_years);

            // paid by owners
            $paid_owners = $UserDbController->DbHandler->getDataByQuery("
                SELECT kvs.ekate,p.contract_id,
                sum(p.amount*pc.area_for_rent*po.percent/(select sum(pos.percent*pcs.area_for_rent)from su_contracts_plots_rel pcs join su_plots_owners_rel pos ON(pos.pc_rel_id = pcs.id) where
                pos.owner_id = p.owner_id and pcs.contract_id = p.contract_id )) as total FROM su_payments p
                            JOIN su_contracts_plots_rel pc ON(p.contract_id = pc.contract_id)
                            JOIN su_plots_owners_rel po ON(po.pc_rel_id = pc.id)
                            JOIN layer_kvs kvs ON(kvs.gid = pc.plot_id)
                where (case when p.is_heritor = false then p.owner_id else (ltree2text(subltree(p.path,0,1))::numeric) end) = po.owner_id
                AND pc.annex_action = 'added'
                AND p.paid_from = 1
                and p.is_heritor = false
                and kvs.ekate = '{$results[$i]['ekate']}'
                and p.contract_id in ({$contract_ids_string})
                group by kvs.ekate, p.contract_id");

            $test_coef = $UserDbController->DbHandler->getDataByQuery("
            SELECT kvs.ekate,c.id as contract_id,pc.plot_id, pc.id as pc_id,
            array_agg(po.owner_id || ':' || pc.area_for_rent * po.percent/(select 
            	case when 
					SUM (
						pos.percent * pcs.area_for_rent
					) = 0 THEN 1
				else
				SUM (
						pos.percent * pcs.area_for_rent
					)
				end
            	from su_contracts_plots_rel pcs join su_plots_owners_rel pos ON(pos.pc_rel_id = pcs.id)
                where
                pos.owner_id = po.owner_id and pcs.contract_id = c.id and pcs.annex_action = 'added'))as coef,
                array_agg(po.owner_id || ':' ||(select sum(pos.percent * pcs.area_for_rent/100)from su_contracts_plots_rel pcs join su_plots_owners_rel pos ON(pos.pc_rel_id = pcs.id)
                where
                pos.owner_id = po.owner_id and pcs.contract_id = c.id and pcs.annex_action = 'added')) as owner_contract_area,
            array_agg(po.owner_id || ':' || po.percent) as prc FROM su_contracts c
                        JOIN su_contracts_plots_rel pc ON(c.id = pc.contract_id)
                        JOIN su_plots_owners_rel po ON(po.pc_rel_id = pc.id)
                        JOIN layer_kvs kvs ON(kvs.gid = pc.plot_id)
            AND pc.annex_action = 'added'
            and po.is_heritor = false
                and kvs.ekate = '{$results[$i]['ekate']}'
                and c.id in ({$contract_ids_string})
                group by kvs.ekate,c.id, pc.plot_id, pc.id");

            $results[$i]['area_by_owner'] = str_replace('&quot;', '"', $results[$i]['area_by_owner']);
            $results[$i]['area_by_owner'] = str_replace('NULL,', '', $results[$i]['area_by_owner']);
            $results[$i]['area_by_owner'] = str_replace(',NULL', '', $results[$i]['area_by_owner']);
            $results[$i]['area_by_owner'] = str_replace('NULL', '', $results[$i]['area_by_owner']);
            $area_by_owners = json_decode($results[$i]['area_by_owner'], true);
            foreach ($test_coef as $coef) {
                $plot = $coef['plot_id'];
                $this->relation_id = $coef['pc_id'];
                $coef_arr = explode(',', trim($coef['prc'], '{}'));
                foreach ($coef_arr as $c) {
                    $c = explode(':', $c);
                    $this->owner_id = $c[0];
                    $this->percent[$coef['pc_id']][$c[0]] = (float)$c[1];
                    $this->getHeritorPercents($c[0] . '.*{1}', $c[1]);
                }
            }
            foreach ($test_coef as $coef) {
                $person_prc = $this->percent[$coef['pc_id']];
                $owner_id = min(array_keys($person_prc));
                foreach ($person_prc as $key => $pp) {
                    $person_contract_total_area_ekate[$coef['contract_id']][$coef['ekate']][$key] += $pp * 0.01 * $area_by_owners[$coef['contract_id'] . '-' . $owner_id . '-' . $coef['plot_id']];
                    $total_person_contract_area[$coef['contract_id']][$key] += $pp * 0.01 * $area_by_owners[$coef['contract_id'] . '-' . $owner_id . '-' . $coef['plot_id']];
                }
            }

            $results[$i]['paid_renta'] = ($paid_owners[0]['total']) ? $paid_owners[0]['total'] : 0;

            $farming_ids_string = trim($results[$i]['farmings'], '{}');
            $farmings = $FarmingController->getFarmingItemsByIDString($farming_ids_string, $this->User->GroupID);
            $results[$i]['farming'] = [];
            foreach ($farmings as $farming) {
                $results[$i]['farming'][] = $farming['name'];
            }
            $results[$i]['farming'] = implode(',', $results[$i]['farming']);
            $results[$i]['pu_area_by_owner'] = str_replace('&quot;', '"', $results[$i]['pu_area_by_owner']);
            $results[$i]['pu_area_by_owner'] = str_replace('NULL,', '', $results[$i]['pu_area_by_owner']);
            $results[$i]['pu_area_by_owner'] = str_replace(',NULL', '', $results[$i]['pu_area_by_owner']);
            $results[$i]['pu_area_by_owner'] = str_replace('NULL', '', $results[$i]['pu_area_by_owner']);
            $pu_area_by_owners[$results[$i]['ekate']] = json_decode($results[$i]['pu_area_by_owner'], true);
            foreach ($pu_area_by_owners[$results[$i]['ekate']] as $key => $value) {
                $results[$i]['pu_area'] += $value;
            }

            $results[$i]['pu_renta_by_owner'] = str_replace('&quot;', '"', $results[$i]['pu_renta_by_owner']);
            $results[$i]['pu_renta_by_owner'] = str_replace('NULL,', '', $results[$i]['pu_renta_by_owner']);
            $results[$i]['pu_renta_by_owner'] = str_replace(',NULL', '', $results[$i]['pu_renta_by_owner']);
            $results[$i]['pu_renta_by_owner'] = str_replace('NULL', '', $results[$i]['pu_renta_by_owner']);
            $pu_renta_by_owners = json_decode($results[$i]['pu_renta_by_owner'], true);
            foreach ($pu_renta_by_owners as $key => $value) {
                $results[$i]['pu_renta'] += $value;
            }

            $results[$i]['pu_charged_renta_by_owner'] = str_replace('&quot;', '"', $results[$i]['pu_charged_renta_by_owner']);
            $results[$i]['pu_charged_renta_by_owner'] = str_replace('NULL,', '', $results[$i]['pu_charged_renta_by_owner']);
            $results[$i]['pu_charged_renta_by_owner'] = str_replace(',NULL', '', $results[$i]['pu_charged_renta_by_owner']);
            $results[$i]['pu_charged_renta_by_owner'] = str_replace('NULL', '', $results[$i]['pu_charged_renta_by_owner']);
            $pu_charged_renta_by_owners = json_decode($results[$i]['pu_charged_renta_by_owner'], true);

            foreach ($pu_charged_renta_by_owners as $key => $value) {
                $results[$i]['pu_charged_renta'] += $value;
            }

            $results[$i]['area'] = $results[$i]['area'] - $results[$i]['pu_area'];
            $results[$i]['renta'] = $results[$i]['renta'] - $results[$i]['pu_renta'];
            $results[$i]['charged_renta'] = $results[$i]['charged_renta'] - $results[$i]['pu_charged_renta'];

            $results[$i]['renta'] = number_format($results[$i]['renta'], 2, '.', '');
            $results[$i]['charged_renta'] = number_format($results[$i]['charged_renta'], 2, '.', '');
        }
        $area_coeficient = [];
        foreach ($person_contract_total_area_ekate as $contract_key => $contract) {
            foreach ($contract as $ekate_key => $people) {
                foreach ($people as $path => $amount) {
                    if (0 == $person_contract_total_area_ekate[$contract_key][$ekate_key][$path] || 0 == $total_person_contract_area[$contract_key][$path]) {
                        $area_coeficient[$contract_key][$ekate_key][$path] = 0;
                    } else {
                        $area_coeficient[$contract_key][$ekate_key][$path] = $person_contract_total_area_ekate[$contract_key][$ekate_key][$path] / $total_person_contract_area[$contract_key][$path];
                    }
                }
            }
        }
        $total_paid_renta = 0;
        $renta_by_ekate = [];
        $renta_by_ekate_person = [];
        $unpaid_overpaid = [];
        $paid_renta_by_ekate = [];
        $due_year = $GLOBALS['Farming']['years'][(int)min($year_array)]['year'] - 1;
        $start_year = $GLOBALS['Farming']['years'][(int)max($year_array)]['year'];
        for ($i = 0; $i < $resultsCount; $i++) {
            $contract_ids_string = trim($results[$i]['contract_id'], '{}');
            $pu_renta_by_owners = json_decode($results[$i]['pu_charged_renta_by_owner'], true);
            $payments_by_people = $UserDbController->DbHandler->getDataByQuery("
                SELECT p.contract_id,
                sum(p.amount) as total,p.owner_id as own, p.path as path FROM su_payments p
                where
                p.paid_from = 1
                and p.contract_id in ({$contract_ids_string})
                and p.farming_year in ({$year_string})
                group by  p.contract_id, p.path, p.owner_id");
            $results[$i]['paid_renta'] = 0;
            foreach ($payments_by_people as $paid) {
                $person_path = ($paid['path']) ? $paid['path'] : $paid['own'];
                $results[$i]['paid_renta'] += $paid['total'] * $area_coeficient[$paid['contract_id']][$results[$i]['ekate']][$person_path];
                $paid_renta_by_ekate[$results[$i]['ekate']][$paid['contract_id']][$person_path] = $paid['total'] * $area_coeficient[$paid['contract_id']][$results[$i]['ekate']][$person_path];
            }
            $results[$i]['paid_renta'] = number_format($results[$i]['paid_renta'], 2, '.', '');
            if (null === $ekate_array || empty($original_ekate_array) || in_array($results[$i]['ekate'], $original_ekate_array)) {
                $total_paid_renta += $results[$i]['paid_renta'];
            }

            $renta = $UserDbController->DbHandler->getDataByQuery("
                SELECT DISTINCT c.id, 
                ( CASE 
                    WHEN ( a.renta IS NOT NULL 
                           AND a.start_date <= '{$start_year}-09-30' 
                           AND a.due_date >= '{$due_year}-10-1' ) THEN a.renta 
                    ELSE c.renta
                  END ), 
                (SELECT DISTINCT(cr.renta + (select sum(CASE when crn.amount is NULL then 0 else (crn.amount*crn.nat_unit_price) end) 
                    FROM su_charged_renta chr 
                    LEFT JOIN su_charged_renta_natura crn on (crn.renta_id = cr.id))
                )
                 FROM   su_charged_renta cr
                 WHERE  cr.contract_id = c.id 
                        AND cr.year IN ({$year_string}) LIMIT 1) AS charged_renta
                FROM   su_contracts c 
                       LEFT JOIN su_contracts a 
                              ON( c.id = a.parent_id )
                WHERE  c.id IN ({$contract_ids_string})
                       AND c.start_date <= '{$start_year}-09-30' 
                       AND c.due_date >= '{$due_year}-10-1' 
                ");
            foreach ($renta as $renta_per_contr) {
                foreach ($person_contract_total_area_ekate[$renta_per_contr['id']] as $ekate_key => $ekate_value) {
                    foreach ($ekate_value as $path => $area) {
                        if (!strpos($path, '.')) {
                            $renta_by_ekate[$ekate_key][$renta_per_contr['id']] = $area * $renta_per_contr['renta'];
                        }
                        $paths = array_keys($ekate_value);
                        $count = 0;
                        foreach ($paths as $person_id) {
                            $haystack_arr = explode('.', $person_id);
                            $haystackCount = count($haystack_arr);
                            $needle_arr = explode('.', $path);

                            $needle_index = 0;
                            $match_started = false;
                            for ($hay_index = 0; $hay_index < $haystackCount; $hay_index++) {
                                if ($match_started && $haystack_arr[$hay_index] != $needle_arr[$needle_index]) {
                                    break;
                                }
                                if ($haystack_arr[$hay_index] == $needle_arr[$needle_index]) {
                                    if (0 != $hay_index && !$match_started) {
                                        continue;
                                    }
                                    $needle_index++;
                                    $match_started = true;
                                    if ($needle_index == count($needle_arr)) {
                                        $count++;
                                    }
                                    if ($count > 1) {
                                        break;
                                    }
                                }
                            }
                        }
                        if ($count > 1) {
                            $unpaid_overpaid[$ekate_key][$renta_per_contr['id']][$path] = 0;
                        } else {
                            $owner_id = explode('.', $path);
                            $substitude_percent = (0 == $area) ? 0 : $area / $ekate_value[$owner_id[0]];
                            $renta_by_ekate_person[$ekate_key][$renta_per_contr['id']][$path] = $area * $renta_per_contr['renta'];

                            if ($renta_per_contr['charged_renta']) {
                                $owned_charged_renta = $area * $renta_per_contr['charged_renta'];
                                $sub_amount = $pu_renta_by_owners[$renta_per_contr['id'] . '-' . $owner_id[0]] * $substitude_percent * $area_coeficient[$renta_per_contr['id']][$ekate_key][$path];
                                $owned_charged_renta = $owned_charged_renta - $sub_amount;
                            } else {
                                $sub_amount = $pu_area_by_owners[$ekate_key][$renta_per_contr['id'] . '-' . $owner_id[0]] * $substitude_percent * $area_coeficient[$renta_per_contr['id']][$ekate_key][$path];
                                $owned_charged_renta = ($area - $sub_amount) * $renta_per_contr['renta'];
                            }
                            $unpaid_overpaid[$ekate_key][$renta_per_contr['id']][$path] = $owned_charged_renta - $paid_renta_by_ekate[$ekate_key][$renta_per_contr['id']][$path];
                        }
                    }
                }
            }
        }

        $original_results = [];
        $total_overpaid = 0;
        $total_unpaid_renta = 0;
        $total_renta = 0;
        $total_charged_renta = 0;
        for ($i = 0; $i < $resultsCount; $i++) {
            foreach ($unpaid_overpaid[$results[$i]['ekate']] as $un_ov_contract) {
                $chargedRenta = null;
                if ($results[$i]['charged_renta'] > 0) {
                    $chargedRenta = $results[$i]['charged_renta'];
                }

                $aCalculatedValue = $UserDbPaymentsController->calculateUnpaidAndOverpaidRentaNat($results[$i]['renta'], $chargedRenta, $results[$i]['paid_renta']);
                $results[$i]['overpaid'] = number_format($aCalculatedValue['overpaid_renta_nat'], 2, '.', '');
                $results[$i]['unpaid_renta'] = number_format($aCalculatedValue['unpaid_renta_nat'], 2, '.', '');
            }
            if (null === $ekate_array || empty($original_ekate_array) || in_array($results[$i]['ekate'], $original_ekate_array)) {
                $original_results[] = $results[$i];
                $total_renta += $results[$i]['renta'];
                $total_charged_renta += $results[$i]['charged_renta'];
                $total_overpaid += $results[$i]['overpaid'];
                $total_unpaid_renta += $results[$i]['unpaid_renta'];
            }
        }

        $result = [
            'rows' => $original_results,
            'total' => $counter[0]['count'],
            'footer' => [
                [
                    'ekate' => '<b>ОБЩО</b>',
                    'renta' => number_format($total_renta, 2, '.', ''),
                    'charged_renta' => number_format($total_charged_renta, 2, '.', ''),
                    'paid_renta' => number_format($total_paid_renta, 2, '.', ''),
                    'unpaid_renta' => number_format($total_unpaid_renta, 2, '.', ''),
                    'overpaid' => number_format($total_overpaid, 2, '.', ''),
                ],
            ],
        ];

        if (!$result_memcached) {
            // memcached
            $LayersController->MemCache->add($hash, $result, $LayersController->default_memcache_expire);

            $LayersController->MemCache->delete($hash . '-date');
            $LayersController->MemCache->add($hash . '-date', date('d.m.Y H:i'), $LayersController->default_memcache_expire);

            $LayersController->MemCache->delete($hash . '-status');
            $LayersController->MemCache->add($hash . '-status', 'Done', $LayersController->default_memcache_expire);
        }

        return $result;
    }

    private function getHeritorPercents($path, $percent)
    {
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);

        $options = [
            'return' => [
                'owner_id', 'path',
                "(SELECT poi.percent FROM su_plots_owners_rel poi WHERE poi.path = h.path AND poi.is_heritor = true AND poi.pc_rel_id = {$this->relation_id}) as percent",
            ],
            'where' => [
                'path' => ['column' => 'path', 'compare' => '~', 'value' => $path],
            ],
        ];

        $counter = $UserDbOwnersController->getOwnersHeritors($options, true);

        if (0 == $counter[0]['count']) {
            return [];
        }

        $results = $UserDbOwnersController->getOwnersHeritors($options, false, false);
        $resultsCount = count($results);
        $sum_custom_ownage = 0;
        $heritors = 0;
        for ($i = 0; $i < $resultsCount; $i++) {
            if ($results[$i]['percent']) {
                $sum_custom_ownage += $results[$i]['percent'];
            } else {
                $heritors++;
            }
        }

        for ($i = 0; $i < $resultsCount; $i++) {
            if (!$results[$i]['percent']) {
                $results[$i]['percent'] = ($percent - $sum_custom_ownage) / $heritors;
            }
            $this->percent[$this->relation_id][$results[$i]['path']] += $results[$i]['percent'];
            $this->getHeritorPercents($results[$i]['path'] . '.*{1}', $results[$i]['percent']);
        }
    }
}
