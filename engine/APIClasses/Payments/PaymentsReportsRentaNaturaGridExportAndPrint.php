<?php

namespace TF\Engine\APIClasses\Payments;

use Prado\Web\Services\TJsonRpcProtocol;
use Prado\Web\Services\TRpcApiProvider;
use Prado\Web\Services\TRpcServer;
use TF\Engine\Kernel\Export2XlsClass;

/**
 * Payments Reports Renta Natura Grid Export And Print.
 *
 * @rpc-module Payments
 *
 * @rpc-service-id payments-reports-renta-natura-grid-export-print
 */
class PaymentsReportsRentaNaturaGridExportAndPrint extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'printPaymentsReportsRentaNaturaGrid' => ['method' => [$this, 'printPaymentsReportsRentaNaturaGrid']],

            'exportToExcelPaymentsReportsRentaNaturaGrid' => ['method' => [$this, 'exportToExcelPaymentsReportsRentaNaturaGrid']],

            'exportToDetailedExcelPaymentsReportsRentaNaturaGrid' => ['method' => [$this, 'exportToDetailedExcelPaymentsReportsRentaNaturaGrid']],
        ];
    }

    /**
     * Print payments reports renta natura grid.
     *
     * @api-method printPaymentsReportsRentaNaturaGrid
     *
     * @param array $data
     *                    {
     *                    #item string report_type      -The payment report type
     *                    #item array year              -The year
     *                    #item array renta_nat_type    -Use only for filter.
     *                    #item array ekate             -Use only for filter.
     *                    #item array farming           -Use only for filter.
     *                    #item string contract_type    -Use only for filter.
     *                    #item string area_type        -Use only for filter.
     *                    #item string sort             -A grid column by which the grid is sorted.
     *                    #item string order            -The sort order ASC/DESC.
     *                    }
     *
     * @return array result
     *               {
     *               #item array rows              -The results.
     *               #item string total            -The count of all results.
     *               }
     */
    public function printPaymentsReportsRentaNaturaGrid($data)
    {
        $server = new TRpcServer(new TJsonRpcProtocol());
        $PaymentsReportsRentaNaturaGrid = new PaymentsReportsRentaNaturaGrid($server);

        return $PaymentsReportsRentaNaturaGrid->readPaymentsReportsRentaNaturaGrid($data);
    }

    /**
     * Export to excel payments reports renta natura grid.
     *
     * @api-method exportToExcelPaymentsReportsRentaNaturaGrid
     *
     * @param array $data
     *                    {
     *                    #item string report_type      -The payment report type
     *                    #item array year              -The year
     *                    #item array renta_nat_type    -Use only for filter.
     *                    #item array ekate             -Use only for filter.
     *                    #item array farming           -Use only for filter.
     *                    #item string contract_type    -Use only for filter.
     *                    #item string area_type        -Use only for filter.
     *                    #item string sort             -A grid column by which the grid is sorted.
     *                    #item string order            -The sort order ASC/DESC.
     *                    }
     *
     * @return string filePath           -Path to the file to excel export
     */
    public function exportToExcelPaymentsReportsRentaNaturaGrid($data)
    {
        $server = new TRpcServer(new TJsonRpcProtocol());
        $PaymentsReportsRentaNaturaGrid = new PaymentsReportsRentaNaturaGrid($server);

        $results = $PaymentsReportsRentaNaturaGrid->readPaymentsReportsRentaNaturaGrid($data);

        // heading
        $column_headers = [
            'Номер',
            'Землище',
            'EKATTE',
            'Стопанство',
            'Стопанска година',
            'Тип натура рента',
            'Дължимо количество по договор (т/бр./л.)',
            'Начислено количество (т/бр./л.)',
            'Изплатено количество (т/бр./л.)',
            'Остатък за изплащане (т/бр./л.)',
            'Наддадено (т/бр./л.)',
        ];

        $data = [];
        $rowsCount = count($results['rows']);
        for ($i = 0; $i < $rowsCount; $i++) {
            $data[] = [
                'number' => $i + 1,
                'land' => $results['rows'][$i]['land'],
                'ekate' => $results['rows'][$i]['ekate'],
                'farming' => $results['rows'][$i]['farming'],
                'farming_year' => $results['rows'][$i]['farming_year'],
                'renta_nat_type' => $results['rows'][$i]['renta_nat_type'],
                'renta_nat' => str_replace('.', ',', $results['rows'][$i]['renta_nat']),
                'charged_renta_nat' => str_replace('.', ',', $results['rows'][$i]['charged_renta_nat']),
                'paid_renta_nat' => str_replace('.', ',', $results['rows'][$i]['paid_renta_nat']),
                'unpaid_renta_nat' => str_replace('.', ',', $results['rows'][$i]['unpaid_renta_nat']),
                'overpaid_renta_nat' => str_replace('.', ',', $results['rows'][$i]['overpaid_renta_nat']),
            ];
        }

        $date = date('Y-m-d-H-i-s');
        $fileName = 'obshta_spravka_po_zemlishte_natura' . $date;
        $path = PUBLIC_UPLOAD_EXPORT . '/' . $fileName . '.xlsx';

        $export2Xls = new Export2XlsClass();

        return $export2Xls->exportUrlPath($path, $data, $column_headers);
    }

    /**
     * Export to detailed excel payments reports renta natura grid.
     *
     * @api-method exportToDetailedExcelPaymentsReportsRentaNaturaGrid
     *
     * @param array $data
     *                    {
     *                    #item string report_type      -The payment report type.
     *                    #item array contracts         -Array with contracts.
     *                    #item string nat_type         -Natura type.
     *                    #item string ekate            -Ekate.
     *                    #item string sort             -A grid column by which the grid is sorted.
     *                    #item string order            -The sort order ASC/DESC.
     *                    }
     *
     * @return string filePath           -Path to the file to excel export
     */
    public function exportToDetailedExcelPaymentsReportsRentaNaturaGrid($data)
    {
        $server = new TRpcServer(new TJsonRpcProtocol());
        $PaymentsReportsRentaNaturaGrid = new PaymentsReportsRentaNaturaGrid($server);

        $results = $PaymentsReportsRentaNaturaGrid->readPaymentsReportsRentaNaturaGrid($data);

        // heading
        $column_headers = [
            'Номер',
            'Договор',
            'Стопанска година',
            'Стопанство',
            'Площ в съответното землище (дка)',
            'Тип натура рента',
            'Рента по договор (кг./бр./л. /дка)',
            'Дължимо количество по договор (т/бр./л.)',
            'Начислено количество (ставка) (кг./бр./л. /дка)',
            'Начислено количество (т/бр./л.)',
            'Изплатено количество (т/бр./л.)',
            'Остатък за изплащане (т/бр./л.)',
            'Наддадено (т/бр./л.)',
        ];

        $data = [];
        $rowsCount = count($results['rows']);
        for ($i = 0; $i < $rowsCount; $i++) {
            $data[] = [
                'number' => $i + 1,
                'contract_name' => $results['rows'][$i]['contract_name'],
                'farming_year' => $results['rows'][$i]['farming_year'],
                'farming' => $results['rows'][$i]['farming'],
                'area_size' => str_replace('.', ',', $results['rows'][$i]['area_size']),
                'renta_nat_type' => $results['rows'][$i]['renta_nat_type'],
                'renta_value' => str_replace('.', ',', $results['rows'][$i]['renta_value']),
                'renta_nat' => str_replace('.', ',', $results['rows'][$i]['renta_nat']),
                'charged_renta_nat_stavka' => str_replace('.', ',', $results['rows'][$i]['charged_renta_nat_stavka']),
                'charged_renta_nat' => str_replace('.', ',', $results['rows'][$i]['charged_renta_nat']),
                'paid_renta_nat' => str_replace('.', ',', $results['rows'][$i]['paid_renta_nat']),
                'unpaid_renta_nat' => str_replace('.', ',', $results['rows'][$i]['unpaid_renta_nat']),
                'overpaid_renta_nat' => str_replace('.', ',', $results['rows'][$i]['overpaid_renta_nat']),
            ];
        }

        $date = date('Y-m-d-H-i-s');
        $fileName = 'podrobna_spravka_po_dogovori_natura_' . $date;
        $path = PUBLIC_UPLOAD_EXPORT . '/' . $fileName . '.xlsx';

        $export2Xls = new Export2XlsClass();

        return $export2Xls->exportUrlPath($path, $data, $column_headers);
    }
}
