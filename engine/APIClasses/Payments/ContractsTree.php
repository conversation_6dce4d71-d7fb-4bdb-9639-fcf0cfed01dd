<?php

namespace TF\Engine\APIClasses\Payments;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbOwners\UserDbOwnersController;
use TF\Engine\Plugins\Core\UserDbPayments\UserDbPaymentsController;

/**
 * Contracts Tree.
 *
 * @rpc-module Payments
 *
 * @rpc-service-id contracts-tree
 */
class ContractsTree extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getContracts']],
        ];
    }

    /**
     * Get contracts.
     *
     * @api-method read
     *
     * @param array $filterObj
     *                         {
     *                         #item string cnum          -The contract number
     *                         #item string company_eik   -The company EIK
     *                         #item string company_name  -The company name
     *                         #item string heritor_egn   -The heritor EGN
     *                         #item string heritor_name  -The heritor name
     *                         #item string kad_ident     -Kadident(ekate.masiv.number)
     *                         #item string masiv         -The masiv
     *                         #item string number        -The number
     *                         #item string owner_egn     -The owner EGN
     *                         #item string owner_name    -The owner name
     *                         #item string rep_egn       -The representative EGN
     *                         #item string rep_name      -The representative name
     *                         #item string year          -The year
     *                         #item array area_type      -The area type
     *                         #item array category       -The category
     *                         #item array contract_type  -The contract type
     *                         #item array ekate          -Ekate
     *                         #item array farming        -Farming
     *                         }
     * @param int $page -The current page number
     * @param int $rows -Rows per page
     * @param string $sort -A grid column by which the grid is sorted
     * @param string $order -The sort order ASC/DESC
     *
     * @return array
     */
    public function getContracts($filterObj, $page = null, $rows = null, $sort = null, $order = null)
    {
        // init Controllers
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);
        $FarmingController = new FarmingController('Farming');
        $arrayHelper = $FarmingController->ArrayHelper;

        $userFarmings = $FarmingController->getUserFarmings(true);
        $userFarmingIds = array_keys($userFarmings);
        $farmingIds = $arrayHelper->filterEmptyStringArr($filterObj['farming']);
        $farmingIds = isset($farmingIds) && count($farmingIds) > 0
            ? array_intersect($farmingIds, $userFarmingIds)
            : $userFarmingIds;

        // options for farming query
        $options = [
            'where' => [
                'id' => ['column' => 'id', 'compare' => 'IN', 'value' => $farmingIds],
            ],
        ];

        // get renta natura types and create predefined array
        $renta_types = [];

        // options for renta nat types
        $options = [
            'tablename' => $UserDbController->DbHandler->tableRentaTypes,
        ];

        $renta_results = $UserDbController->getItemsByParams($options, false, false);
        $rentaCount = count($renta_results);
        for ($i = 0; $i < $rentaCount; $i++) {
            $renta_types[$renta_results[$i]['id']]['name'] = $renta_results[$i]['name'];
            $renta_types[$renta_results[$i]['id']]['unit'] = $GLOBALS['Contracts']['renta_units'][$renta_results[$i]['unit']]['name'];
        }

        $yearID = $FarmingController->getCurrentFarmingYearID();
        if (isset($filterObj['year'])) {
            $yearID = $filterObj['year'];
        }

        $ancestors = [];

        if (!empty($filterObj['heritor_egn']) || !empty($filterObj['heritor_name']) || !empty($filterObj['person_name']) || !empty($filterObj['person_egn'])) {
            $ancestors = $this->_getHeritorAncestors($filterObj);
        }

        $filterObj['owner_name'] = $filterObj['owner_name'] ? preg_replace('/\s+/', ' ', $filterObj['owner_name']) : '';
        $filterObj['rep_name'] = $filterObj['rep_name'] ? preg_replace('/\s+/', ' ', $filterObj['rep_name']) : '';
        $filterObj['company_name'] = $filterObj['company_name'] ? preg_replace(
            '/\s+/',
            ' ',
            $filterObj['company_name']
        ) : '';

        // prepare options for contract query
        $options = [
            'return' => [
                'DISTINCT(c.id)',
                'c.*',
                'a.id as annex_id',
                'a.c_num as a_num',
                '(CASE WHEN a.renta IS NULL THEN c.renta ELSE a.renta END) as renta',
                '(CASE WHEN a.renta_nat IS NULL THEN c.renta_nat ELSE a.renta_nat END) as renta_nat',
                '(CASE WHEN a.due_date IS NULL THEN c.due_date ELSE a.due_date END) as due_date',
                'cg.name as group_name',
            ],
            'where' => [
                'is_sublease' => [
                    'column' => 'is_sublease',
                    'compare' => '=',
                    'prefix' => 'c',
                    'value' => 'FALSE',
                ],
                'is_annex' => ['column' => 'is_annex', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
                'annex_action' => [
                    'column' => 'annex_action',
                    'compare' => '=',
                    'prefix' => 'pc',
                    'value' => 'added',
                ],
                'is_heritor' => ['column' => 'is_heritor', 'compare' => '=', 'prefix' => 'po', 'value' => 'FALSE'],
                'active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'TRUE'],
                'c_type' => [
                    'column' => 'nm_usage_rights',
                    'compare' => 'IN',
                    'prefix' => 'c',
                    'value' => [2, 3, 5],
                ],
                // filters
                // plot filter
                'kad_ident' => [
                    'column' => 'kad_ident',
                    'compare' => '=',
                    'prefix' => 'kvs',
                    'value' => $filterObj['kad_ident'],
                ],
                'ekate' => [
                    'column' => 'ekate',
                    'compare' => 'IN',
                    'prefix' => 'kvs',
                    'value' => $arrayHelper->filterEmptyStringArr($filterObj['ekate']),
                ],
                'mestnost' => [
                    'column' => 'mestnost',
                    'compare' => 'IN',
                    'prefix' => 'kvs',
                    'value' => $arrayHelper->filterEmptyStringArr($filterObj['mestnost']),
                ],
                'masiv' => [
                    'column' => 'masiv',
                    'compare' => '=',
                    'prefix' => 'kvs',
                    'value' => $filterObj['masiv'],
                ],
                'number' => [
                    'column' => 'number',
                    'compare' => '=',
                    'prefix' => 'kvs',
                    'value' => $filterObj['number'],
                ],
                'category' => [
                    'column' => 'category',
                    'compare' => 'IN',
                    'prefix' => 'kvs',
                    'value' => $arrayHelper->filterEmptyStringArr($filterObj['category']),
                ],
                'area_type' => [
                    'column' => 'area_type',
                    'compare' => 'IN',
                    'prefix' => 'kvs',
                    'value' => $arrayHelper->filterEmptyStringArr($filterObj['area_type']),
                ],
                // contract filter
                'cnum' => [
                    'column' => 'c_num',
                    'compare' => 'ILIKE',
                    'prefix' => 'c',
                    'value' => $filterObj['cnum'],
                ],
                'contract_type' => [
                    'column' => 'nm_usage_rights',
                    'compare' => 'IN',
                    'prefix' => 'c',
                    'value' => $arrayHelper->filterEmptyStringArr($filterObj['contract_type']),
                ],
                'na_num' => [
                    'column' => 'na_num',
                    'compare' => 'ILIKE',
                    'prefix' => 'c',
                    'value' => trim($filterObj['na_num']),
                ], // Нотар. акт
                'farming' => [
                    'column' => 'farming_id',
                    'compare' => 'IN',
                    'prefix' => 'c',
                    'value' => $farmingIds,
                ],
                // owner filter
                'owner_egn' => [
                    'column' => 'egn',
                    'compare' => '=',
                    'prefix' => 'o',
                    'value' => $filterObj['owner_egn'],
                ],
                'rep_egn' => [
                    'column' => 'rep_egn',
                    'compare' => '=',
                    'prefix' => 'o_r',
                    'value' => $filterObj['rep_egn'],
                ],
                'company_name' => [
                    'column' => 'TRIM(o.company_name)',
                    'compare' => 'ILIKE',
                    'value' => $filterObj['company_name'],
                ],
                'company_eik' => [
                    'column' => 'eik',
                    'compare' => '=',
                    'prefix' => 'o',
                    'value' => $filterObj['company_eik'],
                ],
                'owner_note' => [
                    'column' => 'remark',
                    'compare' => 'ILIKE',
                    'prefix' => 'o',
                    'value' => $filterObj['owner_note'],
                ],
            ],
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
            // this parameter will be used for joining charged renta table
            'charged_year' => $yearID,
            'start_date' => $GLOBALS['Farming']['years'][$yearID]['year'] . '-09-30',
            'due_date' => ($GLOBALS['Farming']['years'][$yearID]['year'] - 1) . '-10-01',
        ];

        if (!empty($filterObj['contract_group'])) {
            $options['where']['contract_group'] = [
                'column' => 'CASE 
                                    WHEN a.id IS NOT NULL THEN a.group
                                    ELSE c.group
                                END',
                'compare' => 'IN',
                'value' => $arrayHelper->filterEmptyStringArr($filterObj['contract_group']),
            ];
        }

        if (true == $filterObj['c_num_complete_match']) {
            $options['where']['cnum']['compare'] = '=';
        }

        if (true == $filterObj['na_num_complete_match']) {
            $options['where']['na_num']['compare'] = '=';
        }

        if (isset($filterObj['is_closed_for_editing'])) {
            $options['where']['is_closed_for_editing'] = [
                'column' => 'is_closed_for_editing',
                'compare' => '=',
                'prefix' => 'c',
                'value' => $filterObj['is_closed_for_editing'],
            ];
        }

        if ($filterObj['owner_name']) {
            $tmp_owner_names = preg_replace('/\s+/', '.*', $filterObj['owner_name']);
            $tmp_owner_names = mb_strtolower($tmp_owner_names, 'UTF-8');
            array_push(
                $options['return'],
                "regexp_matches(lower(TRIM (o.NAME)) || ' ' || lower(TRIM (o.surname)) || ' ' || lower(TRIM (o.lastname)), '{$tmp_owner_names}','g')"
            );
        }

        if ($filterObj['rep_name']) {
            $tmp_rep_names = preg_replace('/\s+/', '.*', $filterObj['rep_name']);
            $tmp_rep_names = mb_strtolower($tmp_rep_names, 'UTF-8');
            array_push(
                $options['return'],
                "regexp_matches(lower(TRIM (o_r.rep_name)) || ' ' || lower(TRIM (o_r.rep_surname)) || ' ' || lower(TRIM (o_r.rep_lastname)), '{$tmp_rep_names}','g')"
            );
        }

        if ($filterObj['person_name']) {
            $tmp_person_names = preg_replace('/\s+/', '.*', $filterObj['person_name']);
            $tmp_person_names = mb_strtolower($tmp_person_names, 'UTF-8');
            if (!empty($ancestors)) {
                $options['whereOr']['ancestors'] = ['column' => 'id', 'compare' => 'IN', 'prefix' => 'o', 'value' => $arrayHelper->filterEmptyStringArr($ancestors)];
            }
            $options['whereOr']['owner_name'] = ['column' => "lower(TRIM (o.name)) || ' ' || lower(TRIM (o.surname)) || ' ' || lower(TRIM (o.lastname))", 'compare' => '~', 'value' => $tmp_person_names];
            $options['whereOr']['rep_names'] = ['column' => "lower(TRIM (o_r.rep_name)) || ' ' || lower(TRIM (o_r.rep_surname)) || ' ' || lower(TRIM (o_r.rep_lastname))", 'compare' => '~', 'value' => $tmp_person_names];
        }

        if ($filterObj['person_egn']) {
            if (!empty($ancestors)) {
                $options['whereOr']['ancestors'] = ['column' => 'id', 'compare' => 'IN', 'prefix' => 'o', 'value' => $arrayHelper->filterEmptyStringArr($ancestors)];
            }
            $options['whereOr']['owner_egn'] = ['column' => 'egn', 'compare' => 'ILIKE', 'prefix' => 'o', 'value' => $filterObj['person_egn']];
            $options['whereOr']['rep_egn'] = ['column' => 'rep_egn', 'compare' => 'ILIKE', 'prefix' => 'o_r', 'value' => $filterObj['person_egn']];
        }

        if ($filterObj['owner_phone']) {
            $options['whereOr']['owner_mobile'] = ['column' => 'mobile', 'compare' => 'ILIKE', 'prefix' => 'o', 'value' => $filterObj['owner_phone']];
            $options['whereOr']['owner_phone'] = ['column' => 'phone', 'compare' => 'ILIKE', 'prefix' => 'o', 'value' => $filterObj['owner_phone']];
        }

        if (isset($filterObj['contract_ids'])) {
            $filterObj['contract_ids'] = explode(',', $filterObj['contract_ids']);
            $options['whereOr']['contract_id'] = [
                'column' => 'id',
                'compare' => 'IN',
                'prefix' => 'c',
                'value' => $filterObj['contract_ids'],
            ];
            $options['whereOr']['annex_id'] = [
                'column' => 'id',
                'compare' => 'IN',
                'prefix' => 'a',
                'value' => $filterObj['contract_ids'],
            ];
        }

        if (!empty($ancestors) && empty($filterObj['person_name']) && empty($filterObj['person_egn'])) {
            $options['where']['ancestors'] = [
                'column' => 'id',
                'compare' => 'IN',
                'prefix' => 'o',
                'value' => $arrayHelper->filterEmptyStringArr($ancestors),
            ];
        }

        // adding with renta nat filter
        if (isset($filterObj['with_renta_nat']) && 1 == $filterObj['with_renta_nat']) {
            $options['where']['with_renta_nat'] = [
                'column' => 'renta_id',
                'prefix' => 'c_r',
                'compare' => '>',
                'value' => '0',
            ];
        }
        // adding without renta nat filter
        if (isset($filterObj['with_renta_nat']) && 0 == $filterObj['with_renta_nat']) {
            $options['where']['with_renta_nat'] = [
                'column' => 'renta_id',
                'prefix' => 'c_r',
                'compare' => 'IS',
                'value' => 'NULL',
            ];
        }

        if ('all' != $filterObj['irrigated_area']) {
            $options['where']['irrigated_area'] = [
                'column' => 'irrigated_area',
                'compare' => '=',
                'prefix' => 'kvs',
                'value' => $filterObj['irrigated_area'],
            ];
        }
        // get all contracts for pagination total
        $counter = $UserDbPaymentsController->getPaymentsForContracts($options, true, false);

        if (0 == $counter[0]['count']) {
            return [];
        }

        $results = $UserDbPaymentsController->getPaymentsForContracts($options, false, false);
        $resultsCount = count($results);
        if (0 == $resultsCount) {
            return [];
        }
        // clear old data
        $return = [];

        // transform results into tree format
        for ($i = 0; $i < $resultsCount; $i++) {
            $contractData = $results[$i];

            $contractData['c_date'] = strftime('%d.%m.%Y', strtotime($contractData['c_date']));
            $contractData['start_date'] = strftime('%d.%m.%Y', strtotime($contractData['start_date']));
            $contractData['due_date'] = strftime('%d.%m.%Y', strtotime($contractData['due_date']));

            if (!$contractData['comment']) {
                $contractData['comment'] = '-';
            }

            if (!$contractData['sv_num']) {
                $contractData['sv_num'] = '-';
            }

            if ('' == $contractData['sv_date']) {
                $contractData['sv_date'] = '-';
            } else {
                $contractData['sv_date'] = strftime('%d.%m.%Y', strtotime($contractData['sv_date']));
            }

            if ('' == $contractData['payday']) {
                $contractData['payday'] = '-';
            } else {
                $payday = explode('-', $contractData['payday']);
                $payYear = $GLOBALS['Farming']['years'][$yearID]['year'];
                // if the month is oct., nov ,or dec
                if ($payday[1] >= 9) {
                    $payYear--;
                }
                $contractData['payday'] = $payday[0] . '.' . ($payday[1] + 1) . '.' . $payYear;
            }

            if ($contractData['a_num']) {
                $text = $contractData['c_num'] . ' - ' . $contractData['a_num'] . ' (' . $contractData['start_date'] . ' - ' . $contractData['due_date'] . ')';
            } else {
                $text = $contractData['c_num'] . ' (' . $contractData['start_date'] . ' - ' . $contractData['due_date'] . ')';
            }

            if (!$contractData['charged_renta']) {
                $contractData['charged_renta'] = '-';
            }

            if (!$contractData['charged_renta_nat']) {
                $contractData['charged_renta_nat'] = '-';
            }

            if (!$contractData['renta']) {
                $contractData['renta_text'] = '-';
            } else {
                $contractData['renta'] = number_format($contractData['renta'], 2, '.', '');
                $contractData['renta_text'] = BGNtoEURO($contractData['renta']);
            }

            if ($contractData['overall_renta']) {
                $contractData['overall_renta'] = number_format($contractData['overall_renta'], 2, ',', '');
            }

            if (!$contractData['renta_nat']) {
                $contractData['renta_nat_text'] = '-';
            } else {
                $contractData['renta_nat_text'] = $contractData['renta_nat'] . ' ' . $renta_types[$contractData['renta_nat_type_id']]['unit'];
            }

            $contractData['is_closed_for_editing_text'] = $contractData['is_closed_for_editing'] ? 'Да' : 'Не';

            $contractData['c_type'] = $contractData['nm_usage_rights'];
            $contractData['nm_usage_rights'] = $GLOBALS['Contracts']['ContractTypes'][$contractData['nm_usage_rights']]['name'];
            $contractData['farming'] = $userFarmings[$contractData['farming_id']]['name'];
            $contractData['post_payment_fields'] = $userFarmings[$contractData['farming_id']]['post_payment_fields'];
            $contractData['year'] = $GLOBALS['Farming']['years'][$yearID]['farming_year'];
            $contractData['year_id'] = $yearID;

            $rentaOptions = [
                'tablename' => $UserDbController->DbHandler->contractsRentsRelTable,
                'where' => [
                    'id' => ['column' => 'contract_id', 'compare' => '=', 'value' => $contractData['id']],
                ],
            ];

            $individualRentaOptions = [
                'tablename' => $UserDbController->DbHandler->contractsPlotsRelTable,
                'return' => ['(CASE WHEN SUM (rent_per_plot) IS NULL THEN FALSE ELSE TRUE END) as has_rent_per_plot'],
                'where' => [
                    'id' => ['column' => 'contract_id', 'compare' => '=', 'value' => $contractData['id']],
                ],
            ];

            if ($contractData['annex_id']) {
                $rentaOptions['where']['id'] = [
                    'column' => 'contract_id',
                    'compare' => '=',
                    'value' => $contractData['annex_id'],
                ];
                $individualRentaOptions['where']['id'] = [
                    'column' => 'contract_id',
                    'compare' => '=',
                    'value' => $contractData['annex_id'],
                ];
            }

            $tmpRenta = $UserDbController->getItemsByParams($rentaOptions);
            $tmpRentaCount = count($tmpRenta);
            $hasIndividualRenta = $UserDbController->getItemsByParams($individualRentaOptions);

            $contractData['has_rent_per_plot'] = $hasIndividualRenta[0]['has_rent_per_plot'];

            for ($j = 0; $j < $tmpRentaCount; $j++) {
                $tmpRenta[$j]['renta_nat_type'] = $renta_types[$tmpRenta[$j]['renta_id']]['name'];
                $tmpRenta[$j]['renta_nat_text'] = $tmpRenta[$j]['renta_value'] . ' ' . $renta_types[$tmpRenta[$j]['renta_id']]['unit'];
            }
            $contractData['additionalRentas'] = $tmpRenta;

            $return[] = [
                'id' => $contractData['id'],
                'text' => $text,
                'attributes' => $contractData,
                'iconCls' => 'icon-tree-document',
            ];
        }

        // add attribute to first listed element of three for custom pagination
        // information for total can not be sent otherwise
        $return[0]['attributes']['pagination']['total'] = $counter[0]['count'];
        $return[0]['attributes']['pagination']['limit'] = $rows;

        return $return;
    }

    /**
     * get heritor ancestors.
     *
     * @param array $filterObj
     *                         {
     *                         #item string cnum
     *                         #item string company_eik
     *                         #item string company_name
     *                         #item string heritor_egn
     *                         #item string heritor_name
     *                         #item string kad_ident
     *                         #item string masiv
     *                         #item string number
     *                         #item string owner_egn
     *                         #item string owner_name
     *                         #item string rep_egn
     *                         #item string rep_name
     *                         #item string year
     *                         #item array area_type
     *                         #item array category
     *                         #item array contract_type
     *                         #item array ekate
     *                         #item array farming
     *                         }
     *
     * @return array
     */
    private function _getHeritorAncestors($filterObj)
    {
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);

        if (!empty($filterObj['heritor_name'])) {
            $filterObj['heritor_name'] = preg_replace('/\s+/', ' ', $filterObj['heritor_name']);
        } elseif ($filterObj['person_name']) {
            $filterObj['heritor_name'] = preg_replace('/\s+/', ' ', $filterObj['person_name']);
        } else {
            $filterObj['heritor_name'] = '';
        }

        $options = [
            'return' => [
                'array_agg(DISTINCT(subltree(h.path,0,1)))',
            ],
            'where' => [
                'heritor_name' => [
                    'column' => "TRIM(o.name) || ' ' || TRIM(o.surname) || ' ' || TRIM(o.lastname)",
                    'compare' => 'ILIKE',
                    'value' => $filterObj['heritor_name'],
                ],
                'heritor_egn' => [
                    'column' => 'egn',
                    'compare' => '=',
                    'prefix' => 'o',
                    'value' => $filterObj['heritor_egn'] ?: $filterObj['person_egn'] ?: '',
                ],
            ],
        ];

        $ancestors = $UserDbOwnersController->getOwnersHeritors($options, false, false);

        // formatting the result
        $ancestors = str_replace('{', '', $ancestors[0]['array_agg']);
        $ancestors = str_replace('}', '', $ancestors);

        return explode(',', $ancestors);
    }
}
