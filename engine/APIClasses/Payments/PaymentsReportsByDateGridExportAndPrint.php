<?php

namespace TF\Engine\APIClasses\Payments;

use Prado\Web\Services\TJsonRpcProtocol;
use Prado\Web\Services\TRpcApiProvider;
use Prado\Web\Services\TRpcServer;
use TF\Engine\Kernel\Export2XlsClass;

/**
 * Payments Reports By DateGrid Export And Print.
 *
 * @rpc-module Payments
 *
 * @rpc-service-id payments-reports-by-date-grid-export-print
 */
class PaymentsReportsByDateGridExportAndPrint extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'printPaymentsReportsByData' => ['method' => [$this, 'printPaymentsReportsByData']],

            'exportToExcelPaymentsReportsByData' => ['method' => [$this, 'exportToExcelPaymentsReportsByData']],

            'exportToExcelDetailedReportRentaByDate' => ['method' => [$this, 'exportToExcelDetailedReportRentaByDate']],
        ];
    }

    /**
     * Print payments reports by data.
     *
     * @api-method printPaymentsReportsByData
     *
     * @param array $data
     *                    {
     *                    #item string report_type
     *                    #item array year
     *                    #item array ekate             -Use only for filter.
     *                    #item string contract_type    -Use only for filter.
     *                    #item array farming           -Use only for filter.
     *                    #item string date_from        -Use only for filter.
     *                    #item string date_to          -Use only for filter.
     *                    }
     *
     * @return array result
     *               {
     *               #item array rows       -The results.
     *               #item string total     -The count of all results.
     *               #item array footer     -The footer results.
     *               {
     *               #item string ekate             -Ekate
     *               #item string paid_renta        -The paid renta
     *               #item string unpaid_renta      -The unpaid renta
     *               #item string paid_renta_nat    -The paid renta natura
     *               }
     *               }
     */
    public function printPaymentsReportsByData($data)
    {
        $server = new TRpcServer(new TJsonRpcProtocol());
        $PaymentsReportsByDateGrid = new PaymentsReportsByDateGrid($server);

        return $PaymentsReportsByDateGrid->readPaymentsReportsByDateGrid($data);
    }

    /**
     * Export to excel payments reports by data.
     *
     * @api-method exportToExcelPaymentsReportsByData
     *
     * @param array $data -Data for export to excel
     *                    {
     *                    #item string report_type
     *                    #item array year
     *                    #item array ekate             -Use only for filter.
     *                    #item string contract_type    -Use only for filter.
     *                    #item array farming           -Use only for filter.
     *                    #item string date_from        -Use only for filter.
     *                    #item string date_to          -Use only for filter.
     *                    }
     *
     * @return string filePath           -Path to the file to excel export
     */
    public function exportToExcelPaymentsReportsByData($data)
    {
        $server = new TRpcServer(new TJsonRpcProtocol());
        $PaymentsReportsByDateGrid = new PaymentsReportsByDateGrid($server);

        $date_range = '';
        if ('' != $data['date_from']) {
            $date_range .= $data['date_from'];
        }
        if ('' != $data['date_to']) {
            $date_range .= 'до ' . $data['date_to'];
        }

        $results = $PaymentsReportsByDateGrid->readPaymentsReportsByDateGrid($data);

        // heading
        $column_headers = [
            'Номер',
            'Дата',
            'EKATTE',
            'Стопанство',
            'Стопанска година)',
            'Изплатена сума (лв.)',
            'Остатък за плащане (лв.)',
            'Изплатена натура',
            'Оставаща натура',
        ];

        $data = [];
        $rowsCount = count($results['rows']);
        for ($i = 0; $i < $rowsCount; $i++) {
            $data[] = [
                'number' => $i + 1,
                'land' => $results['rows'][$i]['date'],
                'ekate' => $results['rows'][$i]['ekate'],
                'farming' => $results['rows'][$i]['farming'],
                'farming_year' => $results['rows'][$i]['farming_year'],
                'paid_renta' => str_replace('.', ',', $results['rows'][$i]['paid_renta']),
                'unpaid_renta' => str_replace('.', ',', $results['rows'][$i]['unpaid_renta']),
                'paid_renta_nat' => str_replace('<br/>', ',', $results['rows'][$i]['paid_renta_nat']),
                'unpaid_renta_nat' => str_replace('<br/>', ',', $results['rows'][$i]['unpaid_renta_nat']),
            ];
        }

        $date = date('Y-m-d-H-i-s');
        $fileName = 'obshta_spravka_po_data_renta_' . $date;
        $path = PUBLIC_UPLOAD_EXPORT . '/' . $fileName . '.xlsx';

        $export2Xls = new Export2XlsClass();

        return $export2Xls->exportUrlPath($path, $data, $column_headers);
    }

    /**
     * Export to excel detailed report renta by Date.
     *
     * @api-method exportToExcelDetailedReportRentaByDate
     *
     * @param array $data - data for export to excel
     *                    {
     *                    #item string report_type    -The report type
     *                    #item array contracts       -Array with contracts
     *                    #item string date_compare   -The date compare
     *                    #item string ekate          -Ekate
     *                    #item string farming_id     -The farming id
     *                    #item int year              -The year
     *                    #item string sort           -A grid column by which the grid is sorted.
     *                    #item string order          -The sort order ASC/DESC.
     *                    }
     *
     * @return string filePath         - path to the file to excel export
     */
    public function exportToExcelDetailedReportRentaByDate($data)
    {
        $server = new TRpcServer(new TJsonRpcProtocol());
        $PaymentsReportsByDateGrid = new PaymentsReportsByDateGrid($server);

        $results = $PaymentsReportsByDateGrid->readPaymentsReportsByDateGrid($data);

        // heading
        $column_headers = [
            'Номер',
            'Договор',
            'Площ в съответното землище (дка)',
            'Изплатена рента (лв.)',
            'Остатък за плащане (лв.)',
            'Изплатена натура',
            'Оставаща натура',
        ];

        $data = [];
        $rowsCount = count($results['rows']);
        for ($i = 0; $i < $rowsCount; $i++) {
            $data[] = [
                'number' => $i + 1,
                'contract_name' => $results['rows'][$i]['contract_name'],
                'area_size' => str_replace('.', ',', $results['rows'][$i]['area_size']),
                'paid_renta' => str_replace('.', ',', $results['rows'][$i]['paid_renta']),
                'unpaid_renta' => str_replace('.', ',', $results['rows'][$i]['unpaid_renta']),
                'paid_renta_nat' => str_replace('<br/>', ',', $results['rows'][$i]['paid_renta_nat']),
                'unpaid_renta_nat' => str_replace('<br/>', ',', $results['rows'][$i]['unpaid_renta_nat']),
            ];
        }

        $date = date('Y-m-d-H-i-s');
        $fileName = 'podrobna_spravka_natura_po_dati_' . $date;
        $path = PUBLIC_UPLOAD_EXPORT . '/' . $fileName . '.xlsx';

        $export2Xls = new Export2XlsClass();

        return $export2Xls->exportUrlPath($path, $data, $column_headers);
    }
}
