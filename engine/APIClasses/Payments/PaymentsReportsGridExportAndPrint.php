<?php

namespace TF\Engine\APIClasses\Payments;

use Prado\Web\Services\TJsonRpcProtocol;
use Prado\Web\Services\TRpcApiProvider;
use Prado\Web\Services\TRpcServer;
use TF\Engine\Kernel\Export2XlsClass;

/**
 * Payments Reports Grid Export And Print.
 *
 * @rpc-module Payments
 *
 * @rpc-service-id payments-reports-grid-export-print
 */
class PaymentsReportsGridExportAndPrint extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'printPaymentsSummaryReports' => ['method' => [$this, 'printPaymentsSummaryReports']],

            'exportToExcelPaymentsSummaryReports' => ['method' => [$this, 'exportToExcelPaymentsSummaryReports']],
        ];
    }

    /**
     * Print payments reports by data.
     *
     * @api-method printPaymentsSummaryReports
     *
     * @param array $data
     *                    {
     *                    #item string report_type
     *                    #item array year
     *                    #item array ekate             -Use only for filter.
     *                    #item string area_type        -Use only for filter.
     *                    #item string contract_type    -Use only for filter.
     *                    #item array farming           -Use only for filter.
     *                    #item boolean filter_clicked  -Use only for filter.
     *                    #item string sort             -Use only for filter.
     *                    #item string order            -Use only for filter.
     *                    }
     *
     * @return array result
     *               {
     *               #item array rows              -The results.
     *               #item string total            -The count of all results .
     *               #item array footer            -The footer results.
     *               {
     *               #item string ekate
     *               #item string renta
     *               #item string charged_renta
     *               #item string paid_renta
     *               #item string unpaid_renta
     *               #item string overpaid
     *               }
     *               }
     */
    public function printPaymentsSummaryReports($data)
    {
        $server = new TRpcServer(new TJsonRpcProtocol());
        $PaymentsReportsGrid = new PaymentsReportsGrid($server);

        return $PaymentsReportsGrid->readPaymentsReportsGrid($data);
    }

    /**
     * Export to excel payments summary reports.
     *
     * @api-method exportToExcelPaymentsSummaryReports
     *
     * @param array $data - data for export to excel
     *                    {
     *                    #item string report_type
     *                    #item array year
     *                    #item array ekate             -Use only for filter.
     *                    #item string area_type        -Use only for filter.
     *                    #item string contract_type    -Use only for filter.
     *                    #item array farming           -Use only for filter.
     *                    #item boolean filter_clicked  -Use only for filter.
     *                    #item string sort             -Use only for filter.
     *                    #item string order            -Use only for filter.
     *                    }
     *
     * @return string filePath      - path to the file to excel export
     */
    public function exportToExcelPaymentsSummaryReports($data)
    {
        $server = new TRpcServer(new TJsonRpcProtocol());
        $PaymentsReportsGrid = new PaymentsReportsGrid($server);

        $results = $PaymentsReportsGrid->readPaymentsReportsGrid($data);

        // heading
        $column_headers = [
            'Номер',
            'Землище',
            'EKATTE',
            'Стопанство',
            'Стопанска година',
            'Дължима сума по договор (лв.)',
            'Начислена сума (лв.',
            'Изплатена сума (лв.)',
            'Остатък за плащане (лв.)',
            'Надплатено (лв.)',
        ];

        $data = [];
        $rowsCount = count($results['rows']);
        for ($i = 0; $i < $rowsCount; $i++) {
            $data[] = [
                'number' => $i + 1,
                'land' => $results['rows'][$i]['land'],
                'ekate' => $results['rows'][$i]['ekate'],
                'farming' => $results['rows'][$i]['farming'],
                'farming_year' => $results['rows'][$i]['farming_year'],
                'renta' => str_replace('.', ',', $results['rows'][$i]['renta']),
                'charged_renta' => str_replace('.', ',', $results['rows'][$i]['charged_renta']),
                'paid_renta' => str_replace('.', ',', $results['rows'][$i]['paid_renta']),
                'unpaid_renta' => str_replace('.', ',', $results['rows'][$i]['unpaid_renta']),
                'overpaid' => str_replace('.', ',', $results['rows'][$i]['overpaid']),
            ];
        }

        $data[] = [
            'number' => 'ОБЩО',
            'land' => '',
            'ekate' => '',
            'farming' => '',
            'farming_year' => '',
            'renta' => str_replace('.', ',', $results['footer'][0]['renta']),
            'charged_renta' => str_replace('.', ',', $results['footer'][0]['charged_renta']),
            'paid_renta' => str_replace('.', ',', $results['footer'][0]['paid_renta']),
            'unpaid_renta' => str_replace('.', ',', $results['footer'][0]['unpaid_renta']),
            'overpaid' => str_replace('.', ',', $results['footer'][0]['overpaid']),
        ];

        $date = date('Y-m-d-H-i-s');
        $fileName = 'obshta_spravka_po_zemlishte_leva_' . $date;
        $path = PUBLIC_UPLOAD_EXPORT . '/' . $fileName . '.xlsx';

        $export2Xls = new Export2XlsClass();

        return $export2Xls->exportUrlPath($path, $data, $column_headers);
    }
}
