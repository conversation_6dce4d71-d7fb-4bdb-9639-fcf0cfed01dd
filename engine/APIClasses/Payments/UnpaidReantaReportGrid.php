<?php

namespace TF\Engine\APIClasses\Payments;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\ExportToExcelClass;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbPayments\UserDbPaymentsController;

/**
 *  Справка "Оставаща рента за изплащане".
 *
 * @rpc-module Plots
 *
 * @rpc-service-id unpaid-rented-report-grid
 */
class UnpaidReantaReportGrid extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getUnpaidRentaByOwner']],
            'exportExcel' => ['method' => [$this, 'exportExcel']],
        ];
    }

    /**
     * Gets all unpaid renta grouped by owners.
     *
     * @param array $rpcParams
     *                         {
     *                         #item string payroll_farming
     *                         #item string payroll_farming_year
     *                         #item string payroll_from_date
     *                         #item string payroll_to_date
     *                         #item string egn
     *                         #item string eik
     *                         #item boolean for_export
     *                         #item string owner_names
     *                         #item string payroll_ekate
     *                         }
     *
     * @return array result
     */
    public function getUnpaidRentaByOwner($rpcParams, $page = '', $rows = '', $sortBy = '', $sortDir = '')
    {
        return [
            'rows' => [],
            'total' => 0,
            'footer' => [
            ],
        ];

        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);
        $options = [
            'where' => [
                'start_date' => ['column' => 'start_date', 'compare' => '=', 'value' => $rpcParams['payroll_to_date']],
                'due_date' => ['column' => 'due_date', 'compare' => '=', 'value' => $rpcParams['payroll_from_date']],
                'farming_years' => ['column' => 'farming_years', 'compare' => '=', 'value' => [$rpcParams['payroll_farming_year']]],
                'farm_ids' => ['column' => 'farm_ids', 'compare' => '=', 'value' => $rpcParams['payroll_farming']],
                'owner_names' => ['column' => 'owner_names', 'compare' => '=', 'value' => $rpcParams['owner_names']],
                'owner_egn' => ['column' => 'owner_egn', 'compare' => '=', 'value' => $rpcParams['egn']],
                'owner_eik' => ['column' => 'owner_eik', 'compare' => '=', 'value' => $rpcParams['eik']],
                'ekatte' => ['column' => 'ekatte', 'compare' => '=', 'value' => $rpcParams['payroll_ekate']],
            ],
            'sort' => $sortBy,
            'order' => $sortDir,
            'limit' => $rows,
            'offset' => ($page - 1) * $rows,
        ];
        $natByType = $this->getNatByType($UserDbPaymentsController);
        if (array_key_exists($sortBy, $natByType)) {
            unset($options['sort']);
            $options['sortByNat'] = $natByType[$sortBy];
        }

        return $UserDbPaymentsController->getUnpaidRentaByOwner($options);
    }

    public function exportExcel($rpcParams, $page = '', $rows = '', $sortBy = '', $sortDir = '')
    {
        $UserDbController = new UserDbController($this->User->Database);
        $staticColumns = [
            'owner_names' => 'Собственик',
            'egn_eik' => 'ЕГН/ЕИК',
            'farming_year' => 'Стопанска година',
            'unpaid_leva' => 'В лева',
        ];
        $rentaColumns = $this->getNatColumnNames($UserDbController);
        $allColumns = array_merge($staticColumns, $rentaColumns);
        $result = $this->getUnpaidRentaByOwner($rpcParams, $page, $rows, $sortBy, $sortDir);

        $filename = '/ostavashta_renta_' . strtotime(date('Y-m-d H:i:s')) . '.xlsx';
        $file_path_name = PAYROLL_EXPORTS_PATH . $this->User->GroupID . $filename;

        if (!file_exists(PAYROLL_EXPORTS_PATH)) {
            mkdir(PAYROLL_EXPORTS_PATH, 0777);
        }
        if (!file_exists(PAYROLL_EXPORTS_PATH . $this->User->GroupID)) {
            mkdir(PAYROLL_EXPORTS_PATH . $this->User->GroupID, 0777);
        }
        $result['footer'][0]['egn_eik'] = 'Общо';
        /** @var ExportToExcelClass $exportExcelDoc */
        $exportExcelDoc = new ExportToExcelClass();
        $exportExcelDoc->export($result['rows'], $allColumns, $result['footer'], 0, ['freezePane' => 'A2', 'addAutoFilter' => true]);
        $exportExcelDoc->saveFile($file_path_name);

        return 'files/payrolls/' . $this->User->GroupID . $filename;
    }

    private function getNatByType($UserDbController)
    {
        $results = $UserDbController->getItemsByParams([
            'return' => [
                'id',
                "'unpaid_renta_nat_arr_' || id renta_type",
            ],
            'tablename' => $UserDbController->DbHandler->tableRentaTypes,
        ]);

        return array_column($results, 'id', 'renta_type');
    }

    private function getNatColumnNames($UserDbController)
    {
        $results = $UserDbController->getItemsByParams([
            'return' => [
                'id', 'name', 'unit',
                "'unpaid_renta_nat_arr_' || id renta_type",
            ],
            'tablename' => $UserDbController->DbHandler->tableRentaTypes,
        ]);

        $results = array_map(function ($item) {
            $units = $GLOBALS['Contracts']['renta_units'][$item['unit']]['name'];
            $item['full_name'] = "{$item['name']} ({$units})";

            return $item;
        }, $results);

        return array_column($results, 'full_name', 'renta_type');
    }
}
