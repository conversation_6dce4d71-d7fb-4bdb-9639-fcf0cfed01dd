<?php

namespace TF\Engine\APIClasses\Payments;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbPayments\UserDbPaymentsController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Contracts Tree.
 *
 * @rpc-module Payments
 *
 * @rpc-service-id payments-reports-by-date-grid
 */
class PaymentsReportsByDateGrid extends TRpcApiProvider
{
    public $renta_types = [];
    public $iterator = 0;
    public $paid_renta = 0;
    public $paid_renta_nat = 0;
    public $unpaid_renta = 0;
    public $unpaid_renta_nat = 0;

    // added for heritors
    public $relation_id = 0;
    public $percent = [];
    public $owner_id = 0;

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readPaymentsReportsByDateGrid'],
                'validators' => [
                    'data' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * Read payments reports by date grid.
     *
     * @api-method read
     *
     * @param array $data
     *                    {
     *                    #item string report_type
     *                    #item array year
     *                    #item array ekate             -Use only for filter.
     *                    #item string contract_type    -Use only for filter.
     *                    #item array farming           -Use only for filter.
     *                    #item string date_from        -Use only for filter.
     *                    #item string date_to          -Use only for filter.
     *                    }
     * @param int $page -The current page number
     * @param int $rows -Rows per page
     * @param string $sort -A grid column by which the grid is sorted
     * @param string $order -The sort order ASC/DESC
     *
     * @return array result
     *               {
     *               #item array rows     -The results.
     *               #item string total   -The count all results.
     *               #item array footer   -The footer results.
     *               {
     *               #item string contract_name   -The contract name
     *               #item string area_size       -The area size
     *               #item string paid_renta      -The paid renta
     *               }
     *               }
     */
    public function readPaymentsReportsByDateGrid(array $data, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        // creating the default return
        $default = [
            'rows' => [],
            'total' => 0,
            'footer' => [],
        ];

        // add to data grid properties
        $data['pager'] = $page;
        $data['rows'] = $rows;

        // check cause in export and print $sort and $oder are in $data array
        if ('' != $sort) {
            $data['sort'] = $sort;
        }
        if ('' != $order) {
            $data['order'] = $order;
        }

        switch ($data['report_type']) {
            case 'summary-report-by-date':
                return $this->summaryReportByDate($data);
            case 'detailed-report-by-date':
                return $this->detailedReportByDate($data);
            default:
                return $default;
        }
    }

    private function detailedReportByDate($data)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);

        // creating the default return
        $default = [
            'rows' => [],
            'total' => 0,
            'footer' => [
                [
                    'ekate' => '<b>ОБЩО</b>',
                ],
            ],
        ];

        // get renta types
        $options = [
            'tablename' => $UserDbController->DbHandler->tableRentaTypes,
        ];

        $renta_results = $UserDbController->getItemsByParams($options, false, false);
        $rentaResCount = count($renta_results);
        // create renta types array
        for ($i = 0; $i < $rentaResCount; $i++) {
            $this->renta_types[$renta_results[$i]['id']] = $renta_results[$i]['name'] . '(' . $GLOBALS['Contracts']['renta_units'][$renta_results[$i]['unit']]['name'] . ')';
        }

        if (!is_array($data['year'])) {
            $aFarmingYear[] = $data['year'];
        } else {
            $aFarmingYear = $data['year'];
        }

        $options = [
            'return' => [
                'distinct c.id as contract_id', 'c.c_num as contract_name', 'kvs.ekate', 'c.farming_id', 'p.farming_year',
            ],
            'tablename' => $UserDbController->DbHandler->contractsPlotsRelTable,
            'where' => [
                'paid_from' => ['column' => 'paid_from', 'compare' => '=', 'prefix' => 'p', 'value' => 1],
                'id' => ['column' => 'id', 'compare' => 'IN', 'prefix' => 'c', 'value' => $data['contracts']],
                'farming_year' => ['column' => 'farming_year', 'compare' => 'IN', 'prefix' => 'p', 'value' => $aFarmingYear],
                'ekate' => ['column' => 'ekate', 'compare' => '=', 'prefix' => 'kvs', 'value' => $data['ekate']],
                'farming_id' => ['column' => 'farming_id', 'compare' => '=', 'prefix' => 'c', 'value' => $data['farming_id']],
                'date' => ['column' => 'date', 'compare' => '=', 'prefix' => 'p', 'value' => $data['date_compare']],
            ],
            'limit' => $data['rows'],
            'offset' => ($data['pager'] - 1) * $data['rows'],
        ];
        $results = $UserDbPaymentsController->getDetailedPaymentReportsByDate($options, false, false);
        $resultsCount = count($results);
        $options['return'] = ['count(distinct c.id)'];
        unset($options['limit'],$options['offset']);

        $total_count = $UserDbPaymentsController->getDetailedPaymentReportsByDate($options, false, false);

        if (0 == $resultsCount) {
            return $default;
        }

        $total_area_size = 0;
        $total_paid_renta = 0;

        for ($i = 0; $i < $resultsCount; $i++) {
            $options = [
                'return' => [
                    'DISTINCT c.id',
                    'cp.id as contr_plot_id',
                    '(CASE WHEN a.renta IS NULL THEN c.renta ELSE a.renta END) as renta',
                    "((CASE WHEN cp.annex_action = 'added' THEN cp.area_for_rent ELSE 0 END)  * po.percent / 100) as area",
                ],
                'tablename' => $UserDbController->DbHandler->tableContracts,
                'where' => [
                    'paid_from' => ['column' => 'paid_from', 'compare' => '=', 'prefix' => 'p', 'value' => 1],
                    'id' => ['column' => 'id', 'compare' => '=', 'prefix' => 'c', 'value' => $results[$i]['contract_id']],
                    'farming_year' => ['column' => 'farming_year', 'compare' => '=', 'prefix' => 'p', 'value' => $results[$i]['farming_year']],
                    'ekate' => ['column' => 'ekate', 'compare' => '=', 'prefix' => 'kvs', 'value' => $results[$i]['ekate']],
                    'farming_id' => ['column' => 'farming_id', 'compare' => '=', 'prefix' => 'c', 'value' => $results[$i]['farming_id']],
                    'date' => ['column' => 'date', 'compare' => '=', 'prefix' => 'p', 'value' => $data['date_compare']],
                ],
            ];

            // The area size
            $results[$i]['area_size'] = $UserDbPaymentsController->getAreaUsed($options, false, false);
            $total_area_size = $total_area_size + $results[$i]['area_size'];

            unset($options['where']['ekate']);
            $options['return'] = [
                'DISTINCT c.id',
                'cp.id as contr_plot_id',
                '(CASE WHEN a.renta IS NULL THEN c.renta ELSE a.renta END) as renta',
                "(CASE WHEN cp.annex_action = 'added' THEN cp.area_for_rent ELSE 0 END) as area",
            ];

            $totalContractArea = $UserDbPaymentsController->getAreaUsed($options, false, false);

            $payment_ekate_coeficient = $results[$i]['area_size'] / $totalContractArea;

            $options = [
                'return' => [
                    'sum(p.amount) as paid_renta',
                ],
                'tablename' => $UserDbController->DbHandler->tablePayments,
                'where' => [
                    'contract_id' => ['column' => 'contract_id', 'compare' => '=', 'prefix' => 'p', 'value' => $results[$i]['contract_id']],
                    'paid_from' => ['column' => 'paid_from', 'compare' => '=', 'prefix' => 'p', 'value' => 1],
                    'farming_year' => ['column' => 'farming_year', 'compare' => '=', 'prefix' => 'p', 'value' => $results[$i]['farming_year']],
                    'farming_id' => ['column' => 'farming_id', 'compare' => '=', 'prefix' => 'c', 'value' => $results[$i]['farming_id']],
                    'date' => ['column' => 'date', 'compare' => '<=', 'prefix' => 'p', 'value' => $data['date_compare']],
                ],
            ];

            // Paid amount renta
            $aPaidAmount = $UserDbPaymentsController->getPaidAmount($options, false, false);

            $options = [
                'return' => [
                    'sum(pn.amount) as paid_renta_nat, pn.nat_type',
                ],
                'tablename' => $UserDbController->DbHandler->tablePayments,
                'where' => [
                    'contract_id' => ['column' => 'contract_id', 'compare' => '=', 'prefix' => 'p', 'value' => $results[$i]['contract_id']],
                    'paid_from' => ['column' => 'paid_from', 'compare' => '=', 'prefix' => 'p', 'value' => 2],
                    'date' => ['column' => 'date', 'compare' => '<=', 'prefix' => 'p', 'value' => $data['date_compare']],
                    'farming_year' => ['column' => 'farming_year', 'compare' => '=', 'prefix' => 'p', 'value' => $results[$i]['farming_year']],
                    'farming_id' => ['column' => 'farming_id', 'compare' => '=', 'prefix' => 'c', 'value' => $results[$i]['farming_id']],
                ],
                'group' => 'pn.nat_type',
            ];

            // Paid amount renta natura
            $aPaidAmountNat = $UserDbPaymentsController->getPaymentsByParams($options, false, false);

            $paid_in_amounts = $this->getPaidInAmounts([$results[$i]['contract_id']], [$results[$i]['farming_year']], $data['date_compare'], $results[$i]['farming_id']);
            $paid_in_amounts[1] = number_format((float)($paid_in_amounts[1] * $payment_ekate_coeficient), 2, '.', '');

            $paid_in_amount_natura = '';
            foreach ($paid_in_amounts[2] as $renta_type => $renta_amount) {
                $paid_in_amount_natura .= number_format((float)($renta_amount * $payment_ekate_coeficient), 3, '.', '') . ' ' . $this->renta_types[$renta_type] . '<br/>';
            }

            $results[$i]['paid_renta'] = $aPaidAmount[0]['paid_renta'] * $payment_ekate_coeficient;
            $results[$i]['paid_renta'] = number_format((float)$results[$i]['paid_renta'], 2, '.', '');

            $paidAmountNat = '';
            $sortedPaidAmountNat = [];

            foreach ($aPaidAmountNat as $paid_amout_nat) {
                $paidAmountNat .= number_format((float)($paid_amout_nat['paid_renta_nat'] * $payment_ekate_coeficient), 3, '.', '') . ' ' . $this->renta_types[$paid_amout_nat['nat_type']] . '<br/>';
                $sortedPaidAmountNat[$paid_amout_nat['nat_type']] = number_format((float)($paid_amout_nat['paid_renta_nat'] * $payment_ekate_coeficient), 3, '.', '');
            }

            $results[$i]['paid_renta_nat'] = $paidAmountNat;

            $optionsAmountDue = [
                'return' => [
                    'distinct c.id', 'cp.id as contr_plot_id',
                    '(CASE WHEN a.renta IS NULL THEN c.renta ELSE a.renta END) as renta',
                    "((CASE WHEN cp.annex_action = 'added' THEN cp.area_for_rent ELSE 0 END)  * po.percent / 100) as area",
                ],
                'tablename' => $UserDbController->DbHandler->tableContracts,
                'where' => [
                    'contract_id' => ['column' => 'id', 'compare' => '=', 'prefix' => 'c', 'value' => $results[$i]['contract_id']],
                    'paid_from' => ['column' => 'paid_from', 'compare' => '=', 'prefix' => 'p', 'value' => 1],
                    'date' => ['column' => 'date', 'compare' => '=', 'prefix' => 'p', 'value' => $data['date_compare']],
                    'farming_id' => ['column' => 'farming_id', 'compare' => '=', 'prefix' => 'c', 'value' => $results[$i]['farming_id']],
                    'farming_year' => ['column' => 'farming_year', 'compare' => '=', 'prefix' => 'p', 'value' => $results[$i]['farming_year']],
                    'ekate' => ['column' => 'ekate', 'compare' => '=', 'prefix' => 'kvs', 'value' => $results[$i]['ekate']],
                ],
            ];

            // Total contract amount due
            $results[$i]['renta'] = $UserDbPaymentsController->getAmountDue($optionsAmountDue, false, false);

            // Total contract natura due
            $optionsAmountDue['return'][] = '(CASE WHEN a.id IS NULL THEN c.id ELSE a.id END) as contract_annex_id';
            $contractPlots = $UserDbPaymentsController->getRentasAndAreas($optionsAmountDue, false, false);

            $optionsRentaNatura = [
                'return' => [
                    'cr.renta_value as renta_value',
                    'cr.renta_id as renta_id',
                ],
                'where' => [
                    'contract_id' => ['column' => 'id', 'compare' => '=', 'prefix' => 'c', 'value' => $contractPlots[0]['contract_annex_id']],
                ],
            ];

            $contractRentaNatura = $UserDbPaymentsController->getContractRentaNatura($optionsRentaNatura, false, false);
            $contractRentaNatCount = count($contractRentaNatura);
            $results[$i]['renta_natura'] = [];

            for ($j = 0; $j < $contractRentaNatCount; $j++) {
                $results[$i]['renta_natura'][$j]['renta_id'] = $contractRentaNatura[$j]['renta_id'];
                $results[$i]['renta_natura'][$j]['renta_amount'] = $contractRentaNatura[$j]['renta_value'] * $results[$i]['area_size'];
            }

            $options = [
                'return' => [
                    'distinct cr.contract_id', 'cr.renta', "((CASE WHEN cp.annex_action = 'added' THEN cp.area_for_rent ELSE 0 END)  * po.percent / 100) as area",
                ],
                'tablename' => $UserDbController->DbHandler->tableChargedRenta,
                'where' => [
                    'contract_id' => ['column' => 'contract_id', 'compare' => '=', 'prefix' => 'cr', 'value' => $results[$i]['contract_id']],
                    'paid_from' => ['column' => 'paid_from', 'compare' => '=', 'prefix' => 'p', 'value' => 1],
                    'farming_year' => ['column' => 'farming_year', 'compare' => '=', 'prefix' => 'p', 'value' => $results[$i]['farming_year']],
                    'farming_id' => ['column' => 'farming_id', 'compare' => '=', 'prefix' => 'c', 'value' => $results[$i]['farming_id']],
                    'ekate' => ['column' => 'ekate', 'compare' => '=', 'prefix' => 'kvs', 'value' => $results[$i]['ekate']],
                    'date' => ['column' => 'date', 'compare' => '=', 'prefix' => 'p', 'value' => $data['date_compare']],
                ],
            ];

            // Total charged renta
            $results[$i]['charged_renta'] = $UserDbPaymentsController->getChargedAmount($options, false, false);

            // Total charged renta natura
            $options['return'][] = 'crn.amount';
            $options['return'][] = 'crn.nat_type';
            $options['where']['paid_from'] = ['column' => 'paid_from', 'compare' => '=', 'prefix' => 'p', 'value' => 2];
            $aReturn = $UserDbPaymentsController->getChargedRentasNatura($options, false, false);
            $aReturnCount = count($aReturn);
            $charged_renta_natura = [];

            for ($j = 0; $j < $aReturnCount; $j++) {
                $charged_renta_natura[$aReturn[$j]['nat_type']] = $charged_renta_natura[$aReturn[$j]['nat_type']] + ($aReturn[$j]['amount'] * $aReturn[$j]['area']);
            }

            $results[$i]['paid_renta'] = $paid_in_amounts[1];

            // The rest unpaid renta
            $aCalculatedValue = $UserDbPaymentsController->calculateUnpaidAndOverpaidRenta($results[$i]['renta'], $results[$i]['charged_renta'], $results[$i]['paid_renta']);
            $results[$i]['unpaid_renta'] = number_format($aCalculatedValue['unpaid_renta'], 2, '.', '');

            // The rest unpaid renta natura
            $results[$i]['unpaid_renta_nat'] = '';
            $unpaid_natura = [];
            $resRentaNatCount = count($results[$i]['renta_natura']);
            for ($j = 0; $j < $resRentaNatCount; $j++) {
                $unpaid_natura[$results[$i]['renta_natura'][$j]['renta_id']]
                 = $UserDbPaymentsController->calculateUnpaidAndOverpaidRenta(
                     $results[$i]['renta_natura'][$j]['renta_amount'],
                     $charged_renta_natura[$results[$i]['renta_natura'][$j]['renta_id']],
                     $sortedPaidAmountNat[$results[$i]['renta_natura'][$j]['renta_id']]
                 );
                $results[$i]['unpaid_renta_nat'] .= number_format($unpaid_natura[$results[$i]['renta_natura'][$j]['renta_id']]['unpaid_renta'], 3, '.', '')
                . ' ' . $this->renta_types[$results[$i]['renta_natura'][$j]['renta_id']] . '<br/>';
            }

            $results[$i]['paid_renta_nat'] = $paid_in_amount_natura;

            $total_paid_renta = $total_paid_renta + $results[$i]['paid_renta'];
        }

        return [
            'rows' => $results,
            'total' => $total_count[0]['count'],
            'footer' => [
                [
                    'contract_name' => '<b>ОБЩО:</b>',
                    'area_size' => number_format($total_area_size, 2, '.', '') . ' (дка)',
                    'paid_renta' => number_format($total_paid_renta, 2, '.', '') . ' (лв.)',
                ],
            ],
        ];
    }

    private function summaryReportByDate($data)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);
        $UsersController = new UsersController('Users');
        $FarmingController = new FarmingController('Farming');

        // creating the default return
        $default = [
            'rows' => [],
            'total' => 0,
            'footer' => [
                [
                    'ekate' => '<b>ОБЩО</b>',
                ],
            ],
        ];

        // get renta types
        $options = [
            'tablename' => $UserDbController->DbHandler->tableRentaTypes,
        ];

        $renta_results = $UserDbController->getItemsByParams($options, false, false);
        $rentaCount = count($renta_results);
        // create renta types array
        for ($i = 0; $i < $rentaCount; $i++) {
            $this->renta_types[$renta_results[$i]['id']] = $renta_results[$i]['name'] . '(' . $GLOBALS['Contracts']['renta_units'][$renta_results[$i]['unit']]['name'] . ')';
        }

        if (!is_array($data['year'])) {
            $aFarmingYear[] = $data['year'];
        } else {
            $aFarmingYear = $data['year'];
        }

        if (isset($data['sort'], $data['order'])) {
            $sort = $data['sort'] . ' ' . $data['order'] . ',c.farming_id';
        }

        $options = [
            'return' => [
                'kvs.ekate', 'c.farming_id', 'p.farming_year', "to_char(p.date, 'DD.MM.YYYY') as date", "to_char(p.date, 'YYYY-MM-DD') as date_compare", '"array_agg"(distinct p.contract_id) as contracts',
            ],
            'tablename' => $UserDbController->DbHandler->contractsPlotsRelTable,
            'where' => [
                'paid_from' => ['column' => 'paid_from', 'compare' => '=', 'prefix' => 'p', 'value' => 1],
                'farming_year' => ['column' => 'farming_year', 'compare' => 'IN', 'prefix' => 'p', 'value' => $aFarmingYear],
            ],
            'group' => 'kvs.ekate, c.farming_id, p.farming_year, p.date',
            'sort' => $sort,
            'order' => $data['order'],
            'limit' => $data['rows'],
            'offset' => ($data['pager'] - 1) * $data['rows'],
        ];

        if ($data['ekate'] && !in_array('', $data['ekate'])) {
            $options['where']['ekate'] = ['column' => 'ekate', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $data['ekate']];
        }

        if ($data['farming'] && !in_array('', $data['farming'])) {
            $options['where']['farming_id'] = ['column' => 'farming_id', 'compare' => 'IN', 'prefix' => 'c', 'value' => $data['farming']];
        }

        if (strlen($data['contract_type']) > 0) {
            $options['where']['contract_type'] = ['column' => 'nm_usage_rights', 'compare' => '=', 'prefix' => 'c', 'value' => $data['contract_type']];
        }

        if (strlen($data['date_from']) > 0) {
            $options['where']['date_from'] = ['column' => 'date', 'compare' => '>=', 'prefix' => 'p', 'value' => $data['date_from']];
        }

        if (strlen($data['date_to']) > 0) {
            $options['where']['date_to'] = ['column' => 'date', 'compare' => '<=', 'prefix' => 'p', 'value' => $data['date_to']];
        }

        $results = $UserDbPaymentsController->getPaymentReports($options, false, false);
        $resultsCount = count($results);
        unset($options['limit'], $options['offset']);

        $counter = $UserDbPaymentsController->getPaymentReports($options, false, false);
        $counter = count($counter);

        if (0 == $resultsCount) {
            return $default;
        }

        $total_paid_renta = 0;
        $total_unpaid_renta = 0;
        $total_paid_in_renta_natura = [];

        for ($i = 0; $i < $resultsCount; $i++) {
            $aContracts = explode(',', str_replace(['{', '}'], ['', ''], $results[$i]['contracts']));

            $options = [
                'return' => [
                    'sum(p.amount) as paid_renta, contract_id',
                ],
                'tablename' => $UserDbController->DbHandler->tablePayments,
                'where' => [
                    'contract_id' => ['column' => 'contract_id', 'compare' => 'IN', 'prefix' => 'p', 'value' => $aContracts],
                    'paid_from' => ['column' => 'paid_from', 'compare' => '=', 'prefix' => 'p', 'value' => 1],
                    'date' => ['column' => 'date', 'compare' => '<=', 'prefix' => 'p', 'value' => $results[$i]['date_compare']],
                    'farming_year' => ['column' => 'farming_year', 'compare' => 'IN', 'prefix' => 'p', 'value' => $aFarmingYear],
                    'farming_id' => ['column' => 'farming_id', 'compare' => '=', 'prefix' => 'c', 'value' => $results[$i]['farming_id']],
                ],
                'group' => 'contract_id',
            ];

            // Paid amount renta
            $aPaidAmount = $UserDbPaymentsController->getPaidAmount($options, false, false);

            $options = [
                'return' => [
                    'sum(pn.amount) as paid_renta_nat, pn.nat_type, contract_id',
                ],
                'tablename' => $UserDbController->DbHandler->tablePayments,
                'where' => [
                    'contract_id' => ['column' => 'contract_id', 'compare' => 'IN', 'prefix' => 'p', 'value' => $aContracts],
                    'paid_from' => ['column' => 'paid_from', 'compare' => '=', 'prefix' => 'p', 'value' => 2],
                    'date' => ['column' => 'date', 'compare' => '<=', 'prefix' => 'p', 'value' => $results[$i]['date_compare']],
                    'farming_year' => ['column' => 'farming_year', 'compare' => 'IN', 'prefix' => 'p', 'value' => $aFarmingYear],
                    'farming_id' => ['column' => 'farming_id', 'compare' => '=', 'prefix' => 'c', 'value' => $results[$i]['farming_id']],
                ],
                'group' => 'pn.nat_type, contract_id',
            ];

            // Paid amount renta natura
            $aPaidAmountNat = $UserDbPaymentsController->getPaymentsByParams($options, false, false);

            $options = [
                'return' => [
                    'DISTINCT c.id',
                    'cp.id as contr_plot_id',
                    '(CASE WHEN a.renta IS NULL THEN c.renta ELSE a.renta END) as renta',
                    "((CASE WHEN cp.annex_action = 'added' THEN cp.area_for_rent ELSE 0 END)  * po.percent / 100) as area",
                ],
                'tablename' => $UserDbController->DbHandler->tableContracts,
                'where' => [
                    'paid_from' => ['column' => 'paid_from', 'compare' => '=', 'prefix' => 'p', 'value' => 1],
                    'id' => ['column' => 'id', 'compare' => 'IN', 'prefix' => 'c', 'value' => $aContracts],
                    'farming_year' => ['column' => 'farming_year', 'compare' => 'IN', 'prefix' => 'p', 'value' => $aFarmingYear],
                    'ekate' => ['column' => 'ekate', 'compare' => '=', 'prefix' => 'kvs', 'value' => $results[$i]['ekate']],
                    'farming_id' => ['column' => 'farming_id', 'compare' => '=', 'prefix' => 'c', 'value' => $results[$i]['farming_id']],
                    'date' => ['column' => 'date', 'compare' => '=', 'prefix' => 'p', 'value' => $results[$i]['date_compare']],
                ],
                'grouped_by_contract' => true,
            ];

            // Площ в съответното землище
            $concrete_ekate_area = $UserDbPaymentsController->getAreaUsed($options, false, false);

            unset($options['where']['ekate']);
            $options['return'] = [
                'DISTINCT c.id',
                'cp.id as contr_plot_id',
                '(CASE WHEN a.renta IS NULL THEN c.renta ELSE a.renta END) as renta',
                "(CASE WHEN cp.annex_action = 'added' THEN cp.area_for_rent ELSE 0 END) as area",
            ];

            $totalContractArea = $UserDbPaymentsController->getAreaUsed($options, false, false);

            $paid_in_money = 0;
            $paid_in_natura = [];
            foreach ($concrete_ekate_area as $contract_key => $contr_ekate_area) {
                $payment_ekate_coeficient = $concrete_ekate_area[$contract_key] / $totalContractArea[$contract_key];

                foreach ($aPaidAmount as $pkey => $pvalue) {
                    if ($pvalue['contract_id'] == $contract_key) {
                        $results[$i]['paid_renta'] += $pvalue['paid_renta'] * $payment_ekate_coeficient;
                    }
                }

                $paid_in_amounts = $this->getPaidInAmounts([$contract_key], $aFarmingYear, $results[$i]['date_compare'], $results[$i]['farming_id']);
                $paid_in_amounts[1] = number_format((float)($paid_in_amounts[1] * $payment_ekate_coeficient), 2, '.', '');
                $paid_in_money += $paid_in_amounts[1];

                foreach ($paid_in_amounts[2] as $renta_type => $renta_amount) {
                    $paid_in_natura[$renta_type] += number_format((float)($renta_amount * $payment_ekate_coeficient), 3, '.', '');
                }
            }

            $results[$i]['paid_renta'] = number_format((float)$results[$i]['paid_renta'], 2, '.', '');
            $paidAmountNat = '';

            $paid_in_amount_natura = '';
            foreach ($paid_in_natura as $renta_type => $renta_amount) {
                if (!isset($total_paid_in_renta_natura[$renta_type])) {
                    $total_paid_in_renta_natura[$renta_type] = 0;
                }
                $total_paid_in_renta_natura[$renta_type] += number_format((float)($renta_amount), 3, '.', '');
                $paid_in_amount_natura .= number_format((float)($renta_amount), 3, '.', '') . ' ' . $this->renta_types[$renta_type] . '<br/>';
            }
            $sortedPaidAmountNat = [];
            foreach ($aPaidAmountNat as $paid_amout_nat) {
                $paidAmountNat .= number_format((float)($paid_amout_nat['paid_renta_nat'] * $payment_ekate_coeficient), 3, '.', '') . $this->renta_types[$paid_amout_nat['nat_type']] . '<br/>';
                $sortedPaidAmountNat[$paid_amout_nat['nat_type']] = number_format((float)($paid_amout_nat['paid_renta_nat'] * $payment_ekate_coeficient), 3, '.', '');
            }
            $results[$i]['paid_renta_nat'] = $paidAmountNat;

            $options = [
                'return' => [
                    'name as farming',
                ],
                'where' => [
                    'id' => ['column' => 'id', 'compare' => '=', 'value' => $results[$i]['farming_id']],
                ],
            ];
            $aFarming = $FarmingController->getFarmings($options, false, false);
            $results[$i]['farming'] = $aFarming[0]['farming'];

            $results[$i]['farming_year_id'] = $results[$i]['farming_year'];
            $results[$i]['farming_year'] = $GLOBALS['Farming']['years'][$results[$i]['farming_year_id']]['farming_year'];

            $optionsAmountDue = [
                'return' => [
                    'distinct c.id',
                    'cp.id as contr_plot_id',
                    '(CASE WHEN a.renta IS NULL THEN c.renta ELSE a.renta END) as renta',
                    "((CASE WHEN cp.annex_action = 'added' THEN cp.area_for_rent ELSE 0 END)  * po.percent / 100) as area",
                ],
                'tablename' => $UserDbController->DbHandler->tableContracts,
                'where' => [
                    'contract_id' => ['column' => 'id', 'compare' => 'IN', 'prefix' => 'c', 'value' => $aContracts],
                    'paid_from' => ['column' => 'paid_from', 'compare' => '=', 'prefix' => 'p', 'value' => 1],
                    'date' => ['column' => 'date', 'compare' => '=', 'prefix' => 'p', 'value' => $results[$i]['date_compare']],
                    'farming_id' => ['column' => 'farming_id', 'compare' => '=', 'prefix' => 'c', 'value' => $results[$i]['farming_id']],
                    'farming_year' => ['column' => 'farming_year', 'compare' => 'IN', 'prefix' => 'p', 'value' => $aFarmingYear],
                    'ekate' => ['column' => 'ekate', 'compare' => '=', 'prefix' => 'kvs', 'value' => $results[$i]['ekate']],
                ],
            ];

            // Total contract amount due
            $results[$i]['renta'] = $UserDbPaymentsController->getAmountDue($optionsAmountDue, false, false);

            // //Total contract natura due
            $optionsAmountDue['return'] = [
                'distinct c.id',
                'cp.id as contr_plot_id',
                'cr.renta_value',
                'cr.renta_id',
                "((CASE WHEN cp.annex_action = 'added' THEN cp.area_for_rent ELSE 0 END)  * po.percent / 100) as area",
            ];

            $contractsWithoutPayments = $UserDbPaymentsController->getRentasAndAreasNatura($optionsAmountDue, false, false);
            $iAmountDue = [];
            $contractWithoutPayCount = count($contractsWithoutPayments);
            for ($j = 0; $j < $contractWithoutPayCount; $j++) {
                $iAmountDue[$contractsWithoutPayments[$j]['renta_id']] = $iAmountDue[$contractsWithoutPayments[$j]['renta_id']] + ($contractsWithoutPayments[$j]['renta_value'] * $contractsWithoutPayments[$j]['area']);
            }
            $results[$i]['renta_natura'] = $iAmountDue;

            $options = [
                'return' => [
                    'distinct cr.contract_id', 'cr.renta', "((CASE WHEN cp.annex_action = 'added' THEN cp.area_for_rent ELSE 0 END)  * po.percent / 100) as area",
                ],
                'tablename' => $UserDbController->DbHandler->tableChargedRenta,
                'where' => [
                    'contract_id' => ['column' => 'contract_id', 'compare' => 'IN', 'prefix' => 'cr', 'value' => $aContracts],
                    'paid_from' => ['column' => 'paid_from', 'compare' => '=', 'prefix' => 'p', 'value' => 1],
                    'date' => ['column' => 'date', 'compare' => '=', 'prefix' => 'p', 'value' => $results[$i]['date_compare']],
                    'farming_id' => ['column' => 'farming_id', 'compare' => '=', 'prefix' => 'c', 'value' => $results[$i]['farming_id']],
                    'farming_year' => ['column' => 'farming_year', 'compare' => 'IN', 'prefix' => 'p', 'value' => $aFarmingYear],
                    'ekate' => ['column' => 'ekate', 'compare' => '=', 'prefix' => 'kvs', 'value' => $results[$i]['ekate']],
                ],
            ];

            // Total charged renta
            $results[$i]['charged_renta'] = $UserDbPaymentsController->getChargedAmount($options, false, false);

            // Total charged renta natura
            $options['return'][] = 'crn.amount';
            $options['return'][] = 'crn.nat_type';
            $options['where']['paid_from'] = ['column' => 'paid_from', 'compare' => '=', 'prefix' => 'p', 'value' => 2];
            $aReturn = $UserDbPaymentsController->getChargedRentasNatura($options, false, false);
            $aReturnCount = count($aReturn);
            $charged_renta_natura = [];
            for ($j = 0; $j < $aReturnCount; $j++) {
                $charged_renta_natura[$aReturn[$j]['nat_type']] = $charged_renta_natura[$aReturn[$j]['nat_type']] + ($aReturn[$j]['amount'] * $aReturn[$j]['area']);
            }

            $results[$i]['paid_renta'] = number_format((float)($paid_in_money), 2, '.', '');

            // The rest unpaid renta
            $aCalculatedValue = $UserDbPaymentsController->calculateUnpaidAndOverpaidRenta($results[$i]['renta'], $results[$i]['charged_renta'], $results[$i]['paid_renta']);
            $results[$i]['unpaid_renta'] = number_format($aCalculatedValue['unpaid_renta'], 2, '.', '');

            $results[$i]['unpaid_renta_nat'] = '';
            $unpaid_natura = [];

            foreach ($results[$i]['renta_natura'] as $natura_key => $natura_value) {
                $unpaid_natura[$natura_key]
                = $UserDbPaymentsController->calculateUnpaidAndOverpaidRenta(
                    $natura_value,
                    $charged_renta_natura[$natura_key],
                    $sortedPaidAmountNat[$natura_key]
                );
                $results[$i]['unpaid_renta_nat'] .= number_format($unpaid_natura[$natura_key]['unpaid_renta'], 3, '.', '')
                . ' ' . $this->renta_types[$natura_key] . '<br/>';
            }

            $results[$i]['contracts'] = $aContracts;

            $results[$i]['ekate_name'] = $UsersController->getEkatteName($results[$i]['ekate']);

            $results[$i]['paid_renta_nat'] = $paid_in_amount_natura;

            $total_paid_renta += $results[$i]['paid_renta'];
            $total_unpaid_renta += $results[$i]['unpaid_renta'];
        }
        $total_paid_natura = '';
        foreach ($total_paid_in_renta_natura as $total_type => $total_natura_value) {
            $total_paid_natura .= number_format((float)($total_natura_value), 3, '.', '') . ' ' . $this->renta_types[$total_type] . '<br/>';
        }

        return [
            'rows' => $results,
            'total' => $counter,
            'footer' => [
                [
                    'ekate' => '<b>ОБЩО:</b>',
                    'paid_renta' => number_format($total_paid_renta, 2, '.', ''),
                    'unpaid_renta' => number_format($total_unpaid_renta, 2, '.', ''),
                    'paid_renta_nat' => $total_paid_natura,
                ],
            ],
        ];
    }

    private function getPaidInAmounts($contract_id, $farming_year, $date, $farming_id)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);

        $options = [
            'return' => [
                'sum(p.amount) as paid_renta',
                'p.paid_from',
                'p.paid_in',
                'sum(pn.amount) as paid_renta_nat',
                'pn.nat_type',
                'array_agg(p.id) as payment_ids',
            ],
            'tablename' => $UserDbController->DbHandler->tablePayments,
            'where' => [
                'contract_id' => ['column' => 'contract_id', 'compare' => 'IN', 'prefix' => 'p', 'value' => $contract_id],
                'farming_year' => ['column' => 'farming_year', 'compare' => 'IN', 'prefix' => 'p', 'value' => $farming_year],
                'farming_id' => ['column' => 'farming_id', 'compare' => '=', 'prefix' => 'c', 'value' => $farming_id],
                'date' => ['column' => 'date', 'compare' => '<=', 'prefix' => 'p', 'value' => $date],
            ],
            'group' => 'pn.nat_type, p.paid_from, p.paid_in',
        ];

        // The paid amount
        $aPaidAmountIn = $UserDbPaymentsController->getPaymentsByParams($options, false, false);

        $resultAmounts = [
            1 => 0,
            2 => [],
        ];

        $paidInMoneyPaymentIds = [];

        foreach ($aPaidAmountIn as $key => $value) {
            if (1 == $aPaidAmountIn[$key]['paid_in']) {
                if (!in_array($aPaidAmountIn[$key]['payment_ids'], $paidInMoneyPaymentIds)) {
                    $resultAmounts[1] += $aPaidAmountIn[$key]['paid_renta'];
                    $paidInMoneyPaymentIds[] = $aPaidAmountIn[$key]['payment_ids'];
                }
            } else {
                $resultAmounts[2][$value['nat_type']] += $value['paid_renta_nat'];
            }
        }

        return $resultAmounts;
    }
}
