<?php

namespace TF\Engine\APIClasses\Payments;

use DateTime;
use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\Payments\PaymentsController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbOwners\UserDbOwnersController;
use TF\Engine\Plugins\Core\UserDbPayments\UserDbPaymentsController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Contract Payments Grid.
 *
 * @rpc-module Payments
 *
 * @rpc-service-id contract-payments-grid
 */
class ContractPaymentsGrid extends TRpcApiProvider
{
    public $renta_types = [];
    public $renta_types_values = [];
    public $renta_nat_type = [];
    public $iterator = 0;
    public $paid_renta = 0;
    public $paid_renta_nat = [];
    public $unpaid_renta = 0;
    public $unpaid_renta_nat = [];
    public $relation_id;
    public $all_renta;
    public $all_renta_detailed;
    public $percent = [];
    public $arrayHelper;
    public $total_heritors_paid_renta = 0;
    public $total_heritors_over_paid_renta = 0;
    public $total_heritors_paid_renta_by_amount = 0;
    public $total_heritors_paid_renta_by_nat_amount = 0;
    public $total_heritors_unpaid_renta = 0;
    public $total_heritors_sum_by_paid_renta = 0;
    public $total_heritors_sum_area = 0;
    public $ownerArea = 0;
    public $total_heritors_paid_renta_by = [];
    public $total_heritors_paid_renta_by_nat = [];
    public $total_heritors_paid_renta_nat = [];
    public $total_heritors_all_paid_renta_nat_by_detailed = [];
    public $total_heritors_unpaid_renta_nat = [];
    public $total_heritors_over_paid_renta_nat = [];
    public $total_heritors_sum_by_paid_renta_nat = [];

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getContractPaymentsRefactoring']],
        ];
    }

    public function getContractPaymentsRefactoring($contractId, $annex_id, $year, $page = null, $rows = null, $sort = null, $order = null)
    {
        $paymentsController = new PaymentsController($this->User->Database);

        return $paymentsController->getContractPayments($year, $contractId, $annex_id, $page = null, $rows = null, $sort = null, $order = null);
    }

    /**
     * Get contract payments.
     *
     * @api-method read
     *
     * @param int $contract_id -The Contract id
     * @param int $annex_id -The Annex id
     * @param int $year -The year for contract payments
     * @param int $page -The current page number
     * @param int $rows -Rows per page
     * @param string $sort -A grid column by which the grid is sorted
     * @param string $order -The sort order ASC/DESC
     *
     * @return array
     */
    public function getContractPayments($contract_id, $annex_id, $year, $page = null, $rows = null, $sort = null, $order = null)
    {
        // init controllers
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);
        $UsersController = new UsersController('Users');
        $FarmingController = new FarmingController('Farming');
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);

        // creating the default return
        $default = [
            'rows' => [],
            'total' => 0,
        ];

        if (!$contract_id || 0 == $contract_id) {
            return $default;
        }

        $contractID = $contract_id;
        $start_date = $GLOBALS['Farming']['years'][$year]['year'] . '-09-30';
        $due_date = ($GLOBALS['Farming']['years'][$year]['year'] - 1) . '-10-01';

        $farmYearStart = ($GLOBALS['Farming']['years'][$year]['year'] - 1) . '-10-01';
        $farmYearEnd = $GLOBALS['Farming']['years'][$year]['year'] . '-09-30';

        $farming_year_from_id = $UsersController->StringHelper->getFarmingYearByDate($start_date);
        $farming_year_to_id = $UsersController->StringHelper->getFarmingYearByDate($due_date);
        $farming_years = [];

        for ($i = $farming_year_from_id; $i <= $farming_year_to_id; $i++) {
            $farming_years[] = $i;
        }

        $farming_years_str = implode(',', $farming_years);
        $farming_years_string = '(' . implode(',', $farming_years) . ')';
        $currentAnnexId = $UserDbPaymentsController->getCurrentAnnexId($contractID, $start_date, $due_date);
        if (count($currentAnnexId) && $currentAnnexId[0]['id'] > 0) {
            $currentContractOrAnnexID = $currentAnnexId[0]['id'];
        } else {
            $currentContractOrAnnexID = $contractID;
        }

        // build sub-query for rep names
        $repQuery = "SELECT string_agg(rep_name || ' ' || rep_surname || ' ' || rep_lastname, ', ') as rep_names FROM {$UserDbController->DbHandler->tableOwnersReps} ir
        WHERE ir.id IN
        (SELECT rep_id FROM {$UserDbController->DbHandler->plotsOwnersRelTable} ipo
        WHERE ipo.owner_id = o.id
        AND ipo.pc_rel_id IN
        (SELECT DISTINCT(id) FROM {$UserDbController->DbHandler->contractsPlotsRelTable}
        WHERE contract_id = {$currentContractOrAnnexID}))";

        // build main query(getting all payments by owners)
        $options = [
            'return' => [
                'uuid_generate_v4() as uuid',
                'o.id as owner_id',
                $UserDbOwnersController::isDead('o', [$farmYearStart, $farmYearEnd]),
                $UserDbOwnersController::allowOwnerPayment('o', [$farmYearStart, $farmYearEnd]),
                'o.iban',
                'o.post_payment_fields',
                'c.id as contract_id',
                "ARRAY_AGG (
                    (
                        CASE
                        WHEN A .renta IS NULL THEN
                            (
                                CASE
                                WHEN pc.rent_per_plot IS NOT NULL THEN
                                    0
                                ELSE
                                    C .renta
                                END
                            )
                        ELSE
                            (
                                CASE
                                WHEN pc.rent_per_plot IS NOT NULL THEN
                                    0
                                ELSE
                                    A .renta
                                END
                            )
                        END
                    ) * (
                        (
                            pc.area_for_rent * po. PERCENT / 100
                        )
                    ) :: NUMERIC || '|' || (case when a.id is not null then a.id else c.id end ) || '|' || kvs.gid || '|' ||
                            ( CASE
                                WHEN pc.rent_per_plot IS NOT NULL THEN
                                    true
                                ELSE
                                    false
                                END
                            ) || '|' || (pc.area_for_rent * po. PERCENT / 100) || '|' || pc.id || '|' ||
                    (
                        CASE
                        WHEN A .renta IS NULL THEN
                            (
                                CASE
                                WHEN pc.rent_per_plot IS NOT NULL THEN
                                    0
                                ELSE
                                    C .renta
                                END
                            )
                        ELSE
                            (
                                CASE
                                WHEN pc.rent_per_plot IS NOT NULL THEN
                                    0
                                ELSE
                                    A .renta
                                END
                            )
                        END
                    ) || '|' || (CASE WHEN pc.rent_per_plot IS NOT NULL THEN pc.rent_per_plot ELSE 0 END)
                ) AS contracts_plots_renta",

                "ARRAY_AGG (
                    (
                        CASE
                        WHEN pc.rent_per_plot IS NOT NULL THEN
                            pc.rent_per_plot
                        ELSE
                            NULL
                        END
                    ) * (
                        (
                            pc.area_for_rent * po. PERCENT / 100
                        )
                    ) :: NUMERIC || '|' || C . ID || '|' || kvs.gid || '|' || (CASE WHEN pc.rent_per_plot IS NOT NULL THEN pc.rent_per_plot ELSE 0 END
                    )
                ) AS contracts_rent_per_plot",
                "ARRAY_AGG(c.id || '|' || kvs.gid || '|' || (pc.area_for_rent * po.percent / 100)) as pcrel_gid",
                "ARRAY_AGG ((
                            cr.renta
                 * (pc.area_for_rent * po.percent / 100)) :: NUMERIC || '|' || (CASE WHEN a.id IS NOT NULL THEN a.id ELSE c.id END) || '|' || kvs.gid || '|' || cr.renta) AS contracts_plots_charged_renta",
                // 'SUM (CASE WHEN cr.owner_id = o.ID THEN (
                //                 CASE
                //                 WHEN pc.rent_per_plot IS NOT NULL THEN
                //                     NULL
                //                 ELSE
                //                     cr.renta
                //                 END
                //             ) * (pc.area_for_rent * po.percent / 100) ELSE NULL END) as charged_renta',
                // @hack max is used because the field needs to be aggregated by some means
                'MAX(CASE WHEN a.renta IS NULL THEN c.renta ELSE a.renta END) as contract_renta',
                "(CASE WHEN owner_type = 1 THEN name || ' ' || surname || ' ' || lastname ELSE company_name END) as owner_names",
                '(CASE WHEN owner_type = 1 THEN egn ELSE eik END) as owner_egn',
                'o.dead_date as dead_date',
                'owner_type', 'array_agg(kvs.gid) as plots_array', "array_agg(c.id || '|' || kvs.gid || '|' || po.percent) as plots_percent",
                'array_agg(case when a.id is not null then a.id else c.id end) as contract_anex_arr',
                'round(SUM ((pc.area_for_rent * po.percent / 100)::numeric)::numeric, 4) as owner_area',
                'o.address as owner_address', 'lk_nomer', 'lk_izdavane',
                // get representative data
                "(rep.rep_name || ' ' || rep.rep_surname || ' ' || rep.rep_lastname) as rep_names",
                'rep.rep_egn as rep_egn',
                'rep.rep_lk as rep_lk',
                'rep.rep_lk_izdavane as rep_lk_izdavane',
                'rep.rep_address as rep_address',
                'rep.iban as rep_iban',
            ],
            'where' => [
                'is_heritor' => ['column' => 'is_heritor', 'compare' => '=', 'prefix' => 'po', 'value' => 'FALSE'],
                'annex_action' => ['column' => 'annex_action', 'compare' => '=', 'prefix' => 'pc', 'value' => 'added'],
                'percent' => ['column' => 'percent', 'compare' => '>', 'prefix' => 'po', 'value' => '0'],
                'is_edited' => ['column' => "(case when kvs.is_edited = false then true else kvs.edit_active_from > '" . $due_date . "' end)", 'compare' => '=', 'value' => 'TRUE'],
            ],
            'sort' => $sort,
            'order' => $order,
            'limit' => $rows,
            'offset' => ($page - 1) * $rows,
            'group' => 'o.id, c.id, rep.id',
            'custom_counter' => 'COUNT(DISTINCT(o.id))',
            // this parameter will be used for joining charged renta table
            'chosen_year' => $year,
            'contract_id_string' => $contractID,
            'start_date' => $start_date,
            'due_date' => $due_date,
        ];

        if (0 != $annex_id) {
            $options['where']['annex_id'] = ['column' => 'id', 'compare' => '=', 'prefix' => 'a', 'value' => $annex_id];
        }

        $counter = $UserDbPaymentsController->getPaymentsForOwners($options, true, false);

        $personalUseOptions = [
            'contract_id' => $annex_id ? $annex_id : $contract_id,
            'year' => $year,
            'chosen_years' => $year,
        ];
        $personalUse = $UserDbPaymentsController->getPersonalUseForOwners($personalUseOptions, false, false);
        if (0 == $counter[0]['count'] && 0 === count($personalUse)) {
            return $default;
        }

        $results = $UserDbPaymentsController->getPaymentsForOwners($options, false, false);

        $resultsCount = count($results);
        $options = [
            'tablename' => $UserDbController->DbHandler->tableRentaTypes,
        ];

        $renta_results = $UserDbController->getItemsByParams($options, false, false);
        $rentaCount = count($renta_results);
        // create renta types array
        for ($i = 0; $i < $rentaCount; $i++) {
            $this->renta_types[$renta_results[$i]['id']] = $renta_results[$i]['name'] . '(' . $GLOBALS['Contracts']['renta_units'][$renta_results[$i]['unit']]['name'] . ')';
            $this->renta_types_values[$renta_results[$i]['id']] = $renta_results[$i]['unit_value'];
        }

        // init total variables
        $total_renta_nat = [];
        $total_charged_renta_nat = [];
        $total_paid_renta_nat = [];
        $total_unpaid_renta_nat = [];

        // Допълнитени колони
        $total_paid_renta_by = [];
        $total_paid_renta_by_nat = [];
        $total_all_paid_renta_nat_by_detailed = [];
        $total_unpaid_renta_nat = [];
        $total_sum_paid_renta_nat = [];
        $total_paid_renta_by_amount = 0;
        $total_paid_renta_by_nat_amount = 0;
        $total_over_paid_renta = 0;
        $total_over_paid_renta_nat = [];
        $totalPersonalUse = [];

        // iterate and convert results to grid format
        for ($i = 0; $i < $resultsCount; $i++) {
            // assign variables for easy access
            $ownerID = $results[$i]['owner_id'];
            $contr_id = $contractID;
            $plotsStr = trim($results[$i]['plots_array'], '{}');
            $contracts_anex_arr = trim($results[$i]['contract_anex_arr'], '{}');
            $contracts_plots_renta = explode(',', trim($results[$i]['contracts_plots_renta'], '{}'));
            $contracts_plots_charged_renta = explode(',', trim($results[$i]['contracts_plots_charged_renta'], '{}'));
            $pca_elements_gid = explode(',', trim($results[$i]['pcrel_gid'], '{}'));
            $plots_percent = explode(',', trim($results[$i]['plots_percent'], '{}'));

            $contracts_rent_per_plot = explode(',', trim($results[$i]['contracts_rent_per_plot'], '{}'));

            // set default values. Will be calculated below
            $results[$i]['charged_renta'] = 0;

            foreach ($plots_percent as $element) {
                $launch = explode('|', $element);

                $results[$i]['owner_plots_percent'][$launch[0]][$launch[1]] = $launch[2];
            }

            foreach ($pca_elements_gid as $element) {
                $launch = explode('|', $element);

                $results[$i]['plots_contracts_area_array_gids'][] = [
                    'pc_id' => $launch[0],
                    'plot_gid' => $launch[1],
                    'area' => $launch[2],
                ];
            }
            foreach ($contracts_rent_per_plot as $element) {
                $launch = explode('|', $element);
                $results[$i]['plots_contracts_rent_per_plot'][] = [
                    'renta_by_plot' => $launch[0],
                    'contract_id' => $launch[1],
                    'plot_gid' => $launch[2],
                    'rent_per_plot' => $launch[3],
                ];
            }

            $ownerPersonalUsePlots = [];

            // $results[$i]['pu_area'] = 0;
            $areasByContractPlot = [];
            // Намиране на рентата за всеки имот
            foreach ($contracts_plots_renta as $element) {
                if ('NULL' == $element) {
                    continue;
                }

                $launch = explode('|', $element);
                $filteredPlotsWithRentPerPlot = array_filter($results[$i]['plots_contracts_rent_per_plot'], function ($elem) use ($launch) {
                    return $elem['plot_gid'] == $launch[2] && $elem['contract_id'] == $launch[1];
                });

                if (!empty($filteredPlotsWithRentPerPlot)) {
                    $RentPerPlot = reset($filteredPlotsWithRentPerPlot);

                    if (true != $results[$i]['plots_contracts_rent_per_plot'][key($filteredPlotsWithRentPerPlot)]['has_charged_renta']) {
                        $rentaByPlot = $RentPerPlot['renta_by_plot'];
                    }
                }

                $conntractPlotRenta = [
                    // 'renta_by_plot' => round($rentaByPlot, 2),
                    'root_contract_id' => $contract_id,
                    'contract_id' => $launch[1],
                    'plot_gid' => $launch[2],
                    'has_rent_per_plot' => $launch[3],
                    'plot_owned_area' => $launch[4],
                    'pc_rel_id' => $launch[5],
                    'plot_renta' => $launch[6],
                    'rent_per_plot' => $launch[7],
                ];

                // $rentaByPlot = $launch[0];
                $personalUseArea = 0;
                $puArea = $UserDbPaymentsController->getPersonalUseArea($personalUse, $ownerID, $conntractPlotRenta, $results[$i]['owner_area']);

                $contractAreaWithoutPUarea = $conntractPlotRenta['plot_owned_area'] - $puArea;

                if ($puArea) {
                    $results[$i]['pu_area'] += $puArea;
                }

                $areasByContractPlot[$conntractPlotRenta['contract_id']][$conntractPlotRenta['plot_gid']] = [
                    'pu_area' => fixRounding($puArea),
                    'area_without_pu' => $contractAreaWithoutPUarea,
                    'plot_owned_area' => round($conntractPlotRenta['plot_owned_area'], 3),
                ];

                $conntractPlotRenta['area_without_pu'] = round($contractAreaWithoutPUarea, 3);
                $conntractPlotRenta['pu_area'] = round($puArea, 3);

                // $rentaValue = $conntractPlotRenta['rent_per_plot'] > 0 ? $conntractPlotRenta['rent_per_plot'] : $conntractPlotRenta['plot_renta'];
                $rentaValue = $conntractPlotRenta['plot_renta'];
                $conntractPlotRenta['renta_by_plot'] = $contractAreaWithoutPUarea * $rentaValue;

                $results[$i]['plots_contracts_renta'][] = $conntractPlotRenta;
            }

            $sumContractsDueRenta = [];

            // Намиране на начислената рента за всеки имот
            $plots_contracts_charged_renta = [];
            foreach ($contracts_plots_charged_renta as $element) {
                if ('NULL' == $element) {
                    continue;
                }

                $launch = explode('|', $element);

                $chargedRentaResult = $launch[0];
                $chargedRentaContractId = $launch[1];
                $chargedRentaPlotId = $launch[2];
                $chargedRentaValue = $launch[3];

                $chargedRentaCalculated = $chargedRentaValue * $areasByContractPlot[$chargedRentaContractId][$chargedRentaPlotId]['area_without_pu'];

                $filteredPlotsWithRentPerPlot = array_filter($results[$i]['plots_contracts_rent_per_plot'], function ($elem) use ($chargedRentaPlotId, $chargedRentaContractId) {
                    return $elem['plot_gid'] == $chargedRentaPlotId && $elem['contract_id'] == $chargedRentaContractId;
                });

                if (!empty($filteredPlotsWithRentPerPlot)) {
                    $RentPerPlot = reset($filteredPlotsWithRentPerPlot);
                    $results[$i]['plots_contracts_rent_per_plot'][key($filteredPlotsWithRentPerPlot)]['has_charged_renta'] = true;
                    // $chargedRentaResult = $RentPerPlot['renta_by_plot'];
                    $rentaPerPlotCalculated = $RentPerPlot['rent_per_plot'] * $areasByContractPlot[$chargedRentaContractId][$chargedRentaPlotId]['area_without_pu'];
                    $sumContractsDueRenta[$chargedRentaContractId] += $rentaPerPlotCalculated;
                } else {
                    $results[$i]['charged_renta'] += $chargedRentaCalculated;
                }

                $results[$i]['plots_contracts_charged_renta'][] = [
                    'charged_renta_by_plot' => $cargerdRentaCalculated,
                    'contract_id' => $chargedRentaContractId,
                    'plot_gid' => $chargedRentaPlotId,
                ];

                $plots_contracts_charged_renta[$chargedRentaContractId][$chargedRentaPlotId] = $chargedRentaCalculated;
                $results[$i]['plots_contracts_charged_renta_values'][$chargedRentaContractId][$chargedRentaPlotId] = $chargedRentaValue;
            }

            // calculate contract plot area and sum area by contract
            $contractPlotArea = [];
            $contractPlotsAreaSum = [];
            if (!empty($results[$i]['plots_contracts_area_array_gids'])) {
                foreach ($results[$i]['plots_contracts_area_array_gids'] as $key => $value) {
                    $contractId = $value['pc_id'];
                    $plotGid = $value['plot_gid'];
                    $areaPlot = $value['area'];

                    $contractPlotArea[$contractId][$plotGid] = $areaPlot;
                    $contractPlotsAreaSum[$contractId] += $areaPlot;
                }
            }

            // count all heritors
            $pathOwner = $results[$i]['owner_id'] . '.*{1}';
            $options = [
                'return' => [
                    'h.id',
                ],
                'where' => [
                    'path' => ['column' => 'path', 'compare' => '~', 'value' => $pathOwner],
                ],
            ];

            $counterHeritors = $UserDbOwnersController->getOwnersHeritors($options, true);

            $paidOptions = [
                'custom_counter' => 'COUNT(DISTINCT(p.owner_id))',
                'order' => 'asc',
                'sort' => 'p.contract_id',
                'return' => [
                    'p.id as payment_id',
                    'p.owner_id as owner_id',
                    'p.contract_id as contract_id',
                    'case when pn.amount notnull and pn.unit_value notnull  then pn.amount::numeric * pn.unit_value else p.amount::numeric end as trans_amount',
                    'pn.amount::numeric as trans_amount_nat',
                    'case when pn.amount notnull then pn.amount::numeric else p.amount_nat::numeric end as amount_nat',
                    'pn.unit_value::numeric as unit_value',
                    'pn.nat_type as nat_type',
                    'p.paid_in',
                    'p.paid_from',
                    'rent.name as trans_nat_type_text',
                ],
                'where' => [
                    'owner_id' => ['column' => 'owner_id', 'compare' => '=', 'prefix' => 'p', 'value' => $results[$i]['owner_id']],
                    'path' => ['column' => 'path', 'compare' => 'IS', 'prefix' => 'p', 'value' => 'NULL'],
                    'contract_id' => ['column' => 'contract_id', 'compare' => '=', 'prefix' => 'p', 'value' => $contract_id],
                    'farming_year' => ['column' => 'farming_year', 'compare' => 'IN', 'prefix' => 'p', 'value' => $farming_years],
                ],
            ];

            $paidCounter = $UserDbPaymentsController->getPaidData($paidOptions, true, false);

            $paidResults = $UserDbPaymentsController->getPaidData($paidOptions, false, false);
            $paidResultsCount = count($paidResults);
            $results[$i] = $this->addNewRentaPayrolls($paidResults, $results[$i]);

            $paid_renta = 0;
            $paid_renta_by_nat = 0;
            $paid_renta_nat = [];
            $paid_renta_nat_by_nat = [];
            $paid_renta_nat_by_detailed = [];
            $paid_renta_nat_by_detailed_unit_value = [];
            $paymentIds = [];
            $paidRentaByContract = [];
            $paidRentaNatByContract = [];

            $ownerTotalPersonalUse = $UserDbPaymentsController->getOwnerPersonalUseInfo($personalUse, $results[$i]);
            $totalPersonalUse = array_merge($ownerTotalPersonalUse, $totalPersonalUse);

            for ($m = 0; $m < $paidResultsCount; $m++) {
                $paidResult = $paidResults[$m];
                $renta_type = $paidResult['nat_type'];
                $contract_id = $paidResult['contract_id'];

                if (1 == $paidResult['paid_from']) {
                    // Изчисляване на платената рента по договор
                    $paidRentaByContract[$contract_id] += $paidResult['trans_amount'];
                    if (2 == $paidResult['paid_in']) {
                        $paid_renta_nat[$renta_type] += $paidResult['amount_nat'];

                        continue;
                    }

                    $paid_renta += $paidResult['trans_amount'];
                } elseif (2 == $paidResult['paid_from']) {
                    // Изчисляване на платената рента по договор
                    $paidRentaNatByContract[$contract_id][$renta_type] += $paidResult['trans_amount_nat'];

                    if (2 == $paidResult['paid_in']) {
                        $paid_renta_nat_by_nat[$renta_type] += $paidResult['trans_amount_nat'];

                        continue;
                    }

                    // Платена рента в натура детайлно
                    if (array_key_exists($renta_type, $paid_renta_nat_by_detailed)) {
                        $paid_renta_nat_by_detailed[$renta_type] += $paidResult['trans_amount_nat'];
                    } else {
                        $paid_renta_nat_by_detailed[$renta_type] = $paidResult['trans_amount_nat'];
                        $paid_renta_nat_by_detailed_unit_value[$renta_type] = $paidResult['unit_value'];
                    }

                    $paymentIds[] = $paidResult['payment_id'];
                    $paid_renta_by_nat += $paidResult['trans_amount'];
                }
            }

            $paidRentaByPlot = [];
            if (!empty($contractPlotArea)) {
                foreach ($contractPlotArea as $contractId => $plot) {
                    foreach ($plot as $plotGid => $area) {
                        $totalArea = $contractPlotsAreaSum[$contractId];

                        $areaPercent = 0;
                        if ($area > 0) {
                            $areaPercent = $area / $totalArea;
                        }

                        $paidRentaByContractSum = $paidRentaByContract[$contractId];
                        $paidRentaByPlots = $paidRentaByContractSum * $areaPercent;

                        $paidRentaByPlot[$contractId][$plotGid] = $paidRentaByPlots;
                    }
                }
            }

            $results[$i]['paid_renta_by_contract'] = $paidRentaByContract;
            $results[$i]['paid_renta_nat_by_contract'] = $paidRentaNatByContract;

            // set renta type to results
            $results[$i]['renta_types'] = $renta_results;

            // Начислена рента в натура и конвертирано от натура в лева
            $charged_rents = $UserDbController->DbHandler->getDataByQuery(
                "SELECT 
                    (CASE WHEN crn.nat_is_converted = TRUE THEN NULL ELSE crn.amount * ((pc.area_for_rent * po.percent / 100))  END) AS charged_renta_nat,
                    (CASE WHEN crn.nat_is_converted = TRUE THEN NULL ELSE crn.amount END) AS charged_renta_nat_value,
                    pc.id as pc_rel_id,
                    pc.area_for_rent as area_for_rent,
                    po.percent as percent,
                    c.id as contract_id, 
                    kvs.gid as plot_id, 
                    po.owner_id, 
                    crn.nat_type,
                    (CASE WHEN crn.nat_is_converted = TRUE THEN (crn.amount * crn.nat_unit_price) * ((pc.area_for_rent * po.percent / 100)) ELSE NULL END)  AS converted_charged_renta_nat,
                    (CASE WHEN crn.nat_is_converted = TRUE THEN (crn.amount * crn.nat_unit_price) ELSE NULL END) AS converted_charged_renta_nat_value
                FROM su_contracts c
                LEFT JOIN su_contracts a ON(a.parent_id = c.id
                                            AND a.active = TRUE
                                            AND a.start_date <= '{$start_date}'
                                            AND a.due_date >= '{$due_date}')
                INNER JOIN su_contracts_plots_rel pc ON(pc.contract_id = (CASE WHEN a.id IS NULL THEN c.id ELSE a.id END))
                INNER JOIN layer_kvs kvs ON(kvs.gid = pc.plot_id)
                LEFT JOIN su_plots_owners_rel po ON(po.pc_rel_id = pc.id)
                LEFT JOIN su_personal_use pu ON (pu.owner_id = {$ownerID} AND pu.year in ({$year}) AND pu.pc_rel_id = pc.id)
                LEFT JOIN su_charged_renta cr ON(cr.plot_id = kvs.gid
                                                 AND cr.contract_id = c.id
                                                 AND cr.YEAR in ({$year})
                                                 AND cr.owner_id = {$ownerID})
                LEFT JOIN su_charged_renta_natura crn ON(crn.renta_id = cr.id)
                where c.id in ({$contr_id})
                and kvs.gid in ({$plotsStr})
                and pc.annex_action ='added'
                and po.owner_id = {$ownerID}
                and cr.owner_id = {$ownerID}
                and po.is_heritor = 'false'
                "
            );

            // По договор рента в натура
            $renta_nats = $UserDbController->DbHandler->getDataByQuery(
                "SELECT 
                    -- crt.renta_value * ((pc.area_for_rent * po.percent / 100) - COALESCE(pu.area, 0)) AS renta_nat, --- Тази стойност вече се изчислява в долния цъкък, за да може да се време в предвид и личното ползване
                    kvs.gid as plot_id, 
                    crt.renta_value,
                    pc.id as pc_rel_id,
                    pc.area_for_rent, 
                    po.percent, 
                    c.id as contract_id,
                    po.owner_id, 
                    crt.renta_id, 
                    c.parent_id 
                FROM su_contracts c
                INNER JOIN su_contracts_plots_rel pc ON(pc.contract_id = c.id)
                INNER JOIN layer_kvs kvs ON(kvs.gid = pc.plot_id)
                LEFT JOIN su_plots_owners_rel po ON(po.pc_rel_id = pc.id)
                LEFT JOIN su_personal_use pu ON (pu.owner_id = {$ownerID} AND pu.year in ({$year}) AND pu.pc_rel_id = pc.id)
                LEFT JOIN su_contracts_rents crt ON (c.id = crt.contract_id)
                where c.id in ({$contracts_anex_arr})
                and kvs.gid in ({$plotsStr})
                and pc.annex_action ='added'
                and po.owner_id = {$ownerID}
                and po.is_heritor = 'false'
                and pc.rent_per_plot isnull
            "
            );

            $contract_rents = [];
            $charged_rents_arr = [];
            $rentaNatByPlot = [];
            foreach ($renta_nats as $renta_nat) {
                // Проверка за лично ползване за конкретния имот и намиране на площта за лично ползване
                $puArea = $UserDbPaymentsController->getPersonalUseArea($personalUse, $ownerID, $renta_nat, $results[$i]['owner_area']);

                $rentaNatArea = ($renta_nat['area_for_rent'] * $renta_nat['percent'] / 100) - $puArea;
                $rentaNat = $renta_nat['renta_value'] * $rentaNatArea;

                if (!isset($contract_rents[$renta_nat['renta_id']])) {
                    $contract_rents[$renta_nat['renta_id']] = $rentaNat;
                } else {
                    $contract_rents[$renta_nat['renta_id']] += $rentaNat;
                }
                // get renta natura by plot
                $contractID = $renta_nat['contract_id'];

                if ($renta_nat['parent_id']) {
                    $contractID = $renta_nat['parent_id'];
                }

                $plotID = $renta_nat['plot_id'];
                $rentaNatID = $renta_nat['renta_id'];
                $rentaNaturaValue = $rentaNat;

                if ($rentaNatID) {
                    $rentaNatByPlot[$contractID][$plotID][$rentaNatID] = $rentaNaturaValue;
                }
            }

            $convertedChargedRentaByPlot = [];
            $chargedRentaNaturaByPlot = [];
            $results[$i]['converted_renta_nat'];

            // Обхождат се всички ренти в натура
            foreach ($charged_rents as $charged_rent) {
                $contractID = $charged_rent['contract_id'];
                $plotID = $charged_rent['plot_id'];
                $convertedChargedRenta = $charged_rent['converted_charged_renta_nat'];
                $naturaType = $charged_rent['nat_type'];

                // Ако за имота има индивидуална рента го пропускаме от начислението
                if ($RentPerPlot['contract_id'] == $contractID && $RentPerPlot['plot_gid'] == $plotID) {
                    continue;
                }

                // Проверка за лично ползване за конкретния имот и намиране на площта за лично ползване
                $puChargedArea = $UserDbPaymentsController->getPersonalUseArea($personalUse, $ownerID, $charged_rent, $results[$i]['owner_area']);

                $chargedRentaNatArea = ($charged_rent['area_for_rent'] * $charged_rent['percent'] / 100) - $puChargedArea;
                $chargedRentaNat = $charged_rent['charged_renta_nat_value'] * $chargedRentaNatArea;

                if (!isset($charged_rents_arr[$charged_rent['nat_type']])) {
                    $charged_rents_arr[$charged_rent['nat_type']] = $chargedRentaNat;
                } else {
                    $charged_rents_arr[$charged_rent['nat_type']] += $chargedRentaNat;
                }

                $results[$i]['plots_contracts_charged_renta_nat_values'][$contractID][$plotID][$naturaType] = $charged_rent['charged_renta_nat_value'];

                if (!is_null($convertedChargedRenta) && $convertedChargedRenta >= 0) {
                    $convertedChargedRentaNat = $charged_rent['converted_charged_renta_nat_value'] * $chargedRentaNatArea;
                    $results[$i]['converted_renta_nat'] += $convertedChargedRentaNat;
                    $convertedChargedRentaByPlot[$contractID][$plotID] += $convertedChargedRentaNat;

                    $results[$i]['plots_contracts_converted_charged_renta_nat_values'][$contractID][$plotID] += $charged_rent['converted_charged_renta_nat_value'];
                }
                if ($naturaType && !is_null($chargedRentaNat)) {
                    $chargedRentaNaturaByPlot[$contractID][$plotID][$naturaType] = $chargedRentaNat;
                }
            }

            $sumRentaNatByContract = [];
            $sumDueRentaNat = [];
            if (!empty($rentaNatByPlot)) {
                foreach ($rentaNatByPlot as $contractID => $rentaValue) {
                    foreach ($rentaValue as $plotID => $value) {
                        foreach ($value as $rentaType => $naturaValue) {
                            if (is_null($chargedRentaNaturaByPlot[$contractID][$plotID][$rentaType])) {
                                $sumDueRentaNat[$rentaType] += $naturaValue;
                            } else {
                                if (is_null($sumDueRentaNat[$rentaType])) {
                                    $sumDueRentaNat[$rentaType] = '0.000';
                                }
                            }

                            $sumRentaNatByContract[$contractID][$rentaType] += fixRounding($naturaValue);
                        }
                    }
                }
            }
            // Сортиране на дължимата рента в натура по договор спрямо renta_type_id
            ksort($sumDueRentaNat);

            $results[$i]['plots_contracts_due_renta_nat'] = $rentaNatByPlot;
            $sumChargedRentaNaturaByContract = [];
            if (!empty($chargedRentaNaturaByPlot)) {
                foreach ($chargedRentaNaturaByPlot as $contractID => $chargedValue) {
                    foreach ($chargedValue as $plotID => $value) {
                        foreach ($value as $rentaType => $naturaValue) {
                            $rentaNatByPlot[$contractID][$plotID][$rentaType] = $naturaValue;
                            $sumChargedRentaNaturaByContract[$contractID][$rentaType] += $naturaValue;
                        }
                    }
                }
            }

            $results[$i]['plots_contracts_renta_nat'] = $rentaNatByPlot;
            $results[$i]['plots_contracts_charged_renta_nat'] = $chargedRentaNaturaByPlot;

            $merged_renta_nat_array = [];
            foreach ($contract_rents as $key => $rent) {
                if (!is_null($rent)) {
                    $merged_renta_nat_array[$key]['renta_nat'] = $rent;
                }
            }

            foreach ($charged_rents_arr as $key => $rent) {
                if (!is_null($rent)) {
                    $merged_renta_nat_array[$key]['charged_renta_nat'] = $rent;
                }
            }
            ksort($merged_renta_nat_array);

            foreach ($merged_renta_nat_array as $rentaNatTypeID => $rentaNat) {
                if (null != $this->renta_types[$rentaNatTypeID]) {
                    // Начислена Рента в натура
                    if (!is_null($rentaNat['charged_renta_nat']) && $rentaNat['charged_renta_nat'] >= 0) {
                        $results[$i]['charged_renta_nat_text'] .= number_format($rentaNat['charged_renta_nat'], 3, '.', '') . '</br>';
                        $results[$i]['charged_renta_nat'][$rentaNatTypeID] = number_format($rentaNat['charged_renta_nat'], 3, '.', '');
                    } else {
                        $results[$i]['charged_renta_nat_text'] .= '-</br>';
                        $results[$i]['charged_renta_nat'][$rentaNatTypeID] = null;
                    }

                    // Общо
                    $total_renta_nat['charged_renta_nat'][$rentaNatTypeID] += $rentaNat['charged_renta_nat'];
                }
            }

            $results[$i]['nat_type_ids'] = [];
            foreach ($sumDueRentaNat as $rentaNatTypeID => $rentaNat) {
                // Рента в натура По договор
                $rentaNat = number_format(fixRounding($rentaNat), 3, '.', '');

                $results[$i]['renta_nat_text'] .= $rentaNat . '</br>';
                $results[$i]['renta_nat'][$rentaNatTypeID] = $rentaNat;

                $results[$i]['nat_type_ids'][] = $rentaNatTypeID;

                // Общо
                $total_renta_nat['renta_nat'][$rentaNatTypeID] += $rentaNat;
            }

            $results[$i]['renta_nat_type_id'] = $results[$i]['nat_type_ids'];

            $results[$i]['total_by_renta'] = null;
            $results[$i]['total_by_renta_nat'] = null;

            if (!$results[$i]['renta_nat_text']) {
                $results[$i]['renta_nat_text'] = '-';
            } else {
                if (!empty($results[$i]['paid_renta_nat_details'])) {
                    foreach ($results[$i]['paid_renta_nat_details'] as $key => $value) {
                        if ('' != $key) {
                            $total_paid_renta_nat[$key] += $value;
                        }
                    }
                }
            }

            if (!$results[$i]['charged_renta_nat_text']) {
                $results[$i]['charged_renta_nat_text'] = '-';
            } else {
                $results[$i]['charged_renta_nat_text'] = rtrim($results[$i]['charged_renta_nat_text'], ',</br>');
            }

            if ((!is_null($results[$i]['charged_renta']) && $results[$i]['charged_renta'] >= 0)
                 || (!is_null($results[$i]['converted_renta_nat']) && $results[$i]['converted_renta_nat'] >= 0)) {
                $results[$i]['charged_renta'] = number_format($results[$i]['charged_renta'] + $results[$i]['converted_renta_nat'], 2, '.', '');
            } else {
                $results[$i]['charged_renta'] = '-';
            }

            if (!empty($results[$i]['plots_contracts_renta'])) {
                if (!empty($convertedChargedRentaByPlot) || !empty($plots_contracts_charged_renta)) {
                    foreach ($convertedChargedRentaByPlot as $contractId => $convertedValue) {
                        foreach ($convertedValue as $plotId => $convertedChargedRentaNatura) {
                            if ($plots_contracts_charged_renta[$contractId] && $plots_contracts_charged_renta[$contractId][$plotId]) {
                                $plots_contracts_charged_renta[$contractId][$plotId] += $convertedChargedRentaNatura;
                            } elseif ($plots_contracts_charged_renta[$contractId] && !$plots_contracts_charged_renta[$contractId][$plotId]) {
                                $plots_contracts_charged_renta[$contractId][$plotId] = $convertedChargedRentaNatura;
                            } elseif (!$plots_contracts_charged_renta[$contractId] && $plots_contracts_charged_renta[$contractId][$plotId]) {
                                $plots_contracts_charged_renta[$contractId][$plotId] += $convertedChargedRentaNatura;
                            } elseif (!$plots_contracts_charged_renta[$contractId] && !$plots_contracts_charged_renta[$contractId][$plotId]) {
                                $plots_contracts_charged_renta[$contractId][$plotId] = $convertedChargedRentaNatura;
                            }
                        }
                    }

                    $results[$i]['plots_contracts_charged_renta'] = [];

                    if ($plots_contracts_charged_renta) {
                        foreach ($plots_contracts_charged_renta as $contractID => $value) {
                            foreach ($value as $plotID => $chargedValue) {
                                $results[$i]['plots_contracts_charged_renta'][] = [
                                    'contract_id' => $contractID,
                                    'plot_gid' => $plotID,
                                    'charged_renta_by_plot' => $chargedValue,
                                ];
                            }
                        }
                    }
                }

                $plotsContractsRenta = $results[$i]['plots_contracts_renta'];
                $plotsContractsChargedRenta = $results[$i]['plots_contracts_charged_renta'];

                $unpaidRenta = 0;
                $sumContractsRenta = [];
                $sumContractsChargedRenta = [];
                if (!empty($plotsContractsChargedRenta)) {
                    foreach ($plotsContractsRenta as $rentaKey => $rentaValue) {
                        foreach ($plotsContractsChargedRenta as $chargedRentaKey => $chargedRentaValue) {
                            $isCharged = false;

                            if (
                                $chargedRentaValue['contract_id'] == $rentaValue['contract_id']
                                && $chargedRentaValue['plot_gid'] == $rentaValue['plot_gid']
                                && !is_null($chargedRentaValue['charged_renta_by_plot'])
                            ) {
                                $rentToPayForPlot = $chargedRentaValue['charged_renta_by_plot'];

                                $filteredPlotsWithRentPerPlot = array_filter($results[$i]['plots_contracts_rent_per_plot'], function ($elem) use ($rentaValue) {
                                    return $elem['plot_gid'] == $rentaValue['plot_gid'] && $elem['contract_id'] == $rentaValue['contract_id'];
                                });

                                if ('true' == $rentaValue['has_rent_per_plot'] && !empty($filteredPlotsWithRentPerPlot)) {
                                    $rentToPayForPlot = 0;
                                }

                                $results[$i]['plots_contracts_renta'][$rentaKey]['renta_by_plot'] = $rentToPayForPlot;

                                // Сумиране на рентата по договор за договор
                                $sumContractsChargedRenta[$chargedRentaValue['contract_id']] += $rentToPayForPlot;
                                $isCharged = true;

                                break;
                            }
                        }

                        if (!$isCharged) {
                            // Проверяваме дали индивидуална рента, ако има вземаме нея вместо начислението
                            // $rentaVal = $rentaValue['rent_per_plot'] > 0 ? $rentaValue['rent_per_plot'] * $rentaValue['area_without_pu'] : 0;
                            // $sumContractsDueRenta[$rentaValue['contract_id']] += $rentaVal;

                            if ($rentaValue['rent_per_plot'] > 0) {
                                $sumContractsDueRenta[$rentaValue['contract_id']] += $rentaValue['rent_per_plot'] * $rentaValue['area_without_pu'];
                            } elseif ($rentaValue['renta_by_plot']) {
                                $sumContractsDueRenta[$rentaValue['contract_id']] += $rentaValue['renta_by_plot'];
                            } else {
                                $sumContractsDueRenta[$rentaValue['contract_id']] += 0;
                            }
                        }

                        // Сумиране на рентата по договор за договор
                        $sumContractsRenta[$rentaValue['contract_id']] += $rentaValue['renta_by_plot'];
                    }
                } else {
                    foreach ($plotsContractsRenta as $rentaKey => $rentaValue) {
                        $rentaVal = $rentaValue['rent_per_plot'] > 0 ? $rentaValue['rent_per_plot'] * $rentaValue['area_without_pu'] : $rentaValue['renta_by_plot'];
                        $sumContractsDueRenta[$rentaValue['contract_id']] += $rentaVal;
                    }
                }

                foreach ($results[$i]['plots_contracts_renta'] as $rentaKey => $rentaValue) {
                    $contractID = $rentaValue['contract_id'];
                    $plotID = $rentaValue['plot_gid'];
                    if ($rentaValue['rent_per_plot'] > 0) {
                        $rent_per_plot = $rentaValue['rent_per_plot'] * $rentaValue['area_without_pu'];
                        $results[$i]['plots_contracts_renta'][$rentaKey]['renta'] = $rent_per_plot;
                    } elseif (!empty($results[$i]['plots_contracts_charged_renta_values'][$contractID][$plotID])) {
                        $plot_charged_renta = $results[$i]['plots_contracts_charged_renta_values'][$contractID][$plotID] * $rentaValue['area_without_pu'];
                        $results[$i]['plots_contracts_renta'][$rentaKey]['charged_renta'] = $plot_charged_renta;
                    } elseif (!empty($results[$i]['plots_contracts_converted_charged_renta_nat_values'][$contractID][$plotID])) {
                        $plot_converted_charged_renta = $results[$i]['plots_contracts_converted_charged_renta_nat_values'][$contractID][$plotID] * $rentaValue['area_without_pu'];
                        $results[$i]['plots_contracts_renta'][$rentaKey]['charged_renta'] = $plot_converted_charged_renta;
                    } else {
                        $plot_renta = $rentaValue['plot_renta'] * $rentaValue['area_without_pu'];
                        $results[$i]['plots_contracts_renta'][$rentaKey]['renta'] = $plot_renta;
                    }
                    $rentaByPlot = $results[$i]['plots_contracts_renta'][$rentaKey]['renta'] + $results[$i]['plots_contracts_renta'][$rentaKey]['charged_renta'];

                    // Плащанията са към основния договор, дори и да има анекс към него в таблицата su_payments
                    // в колоната contract_id се записва id-то на основния договор.
                    if ($paidRentaByPlot[$rentaValue['root_contract_id']] && $paidRentaByPlot[$rentaValue['root_contract_id']][$plotID]) {
                        $rentaToPaid = $paidRentaByPlot[$rentaValue['root_contract_id']][$plotID];
                        $rentaLeft = $rentaByPlot - $rentaToPaid;

                        $unpaidRenta += $rentaLeft;
                    } else {
                        $unpaidRenta += $rentaByPlot;
                    }
                }

                $results[$i]['unpaid_renta'] = ($unpaidRenta < 0) ? '0.00' : fixRounding($unpaidRenta, 0.01);

                // Надплатено рента в лева
                $results[$i]['over_paid'] = ($unpaidRenta >= 0) ? '0.00' : number_format($unpaidRenta * (-1), 2, '.', '');
            }

            if (!empty($rentaNatByPlot)) {
                // sum natura by renta type
                $sumRentaType = [];
                foreach ($rentaNatByPlot as $contractID => $rentaValue) {
                    foreach ($rentaValue as $plotID => $value) {
                        foreach ($value as $rentaType => $naturaValue) {
                            $sumRentaType[$rentaType] += $naturaValue;
                        }
                    }
                }

                // Сортиране на рентата в натура спрямо renta_type_id
                ksort($sumRentaType);

                $unpaid_renta_nat = [];
                $over_paid_renta_nat = [];
                $unpaid_renta_nat_unit_value = [];
                $rentaTypes = [];

                foreach ($sumRentaType as $rentaType => $rentaNatura) {
                    $paidRentaNatura = $results[$i]['paid_renta_nat_details'][$rentaType];
                    $unpaidRentaNatura = fixRounding($rentaNatura) - $paidRentaNatura;
                    $rentaTypes[] = $this->renta_types[$rentaType];
                    $this->renta_nat_type[$rentaType] = $this->renta_types[$rentaType];

                    $quantity = number_format(($unpaidRentaNatura < 0) ? 0 : fixRounding($unpaidRentaNatura), 3, '.', '');
                    $quantityOverPaid = number_format(($unpaidRentaNatura >= 0) ? '0.00' : fixRounding($unpaidRentaNatura) * (-1), 3, '.', '');
                    $quantityValue = number_format($quantity * $this->renta_types_values[$rentaType], 2, '.', '');

                    $unpaid_renta_nat_unit_value[] = $quantityValue . ' лв.';
                    $unpaid_renta_nat[$rentaType] = $quantity;
                    $over_paid_renta_nat[] = $quantityOverPaid;

                    if (!$results[$i]['is_dead']) {
                        $total_unpaid_renta_nat[$rentaType] += $quantity;
                    } else {
                        if (0 == $counterHeritors[0]['count']) {
                            $total_unpaid_renta_nat[$rentaType] += $quantity;
                        }
                    }

                    // Сумиране Надплатена рента в натура - Общо за страница
                    $total_over_paid_renta_nat[$rentaType] += $quantityOverPaid;
                }

                $results[$i]['unpaid_renta_nat'] = implode('</br>', $unpaid_renta_nat);
                $results[$i]['unpaid_renta_nat_details'] = $unpaid_renta_nat;
                $results[$i]['over_paid_nat'] = implode('</br>', $over_paid_renta_nat);
                $results[$i]['unpaid_renta_nat_unit_value'] = implode('</br>', $unpaid_renta_nat_unit_value);
                $results[$i]['renta_nat_type'] = implode('</br>', $rentaTypes);
            }

            $paid_renta_by = [];
            if ($paid_renta) {
                $amount = number_format($paid_renta, 2, '.', '');
                $paid_renta_by[] = $amount . ' лв.';
                $total_paid_renta_by_amount += $amount;
            }
            if (!empty($paid_renta_nat)) {
                foreach ($paid_renta_nat as $key => $value) {
                    $quantity = number_format($value, 3, '.', '');
                    $paid_renta_by[] = $quantity . ' X ' . $this->renta_types[$key];
                    $total_paid_renta_by[$key] += $value;
                }
            }
            $results[$i]['paid_renta_by'] = implode('</br>', $paid_renta_by);
            $results[$i]['paid_renta_by'] = '' != $results[$i]['paid_renta_by'] ? $results[$i]['paid_renta_by'] : '-';

            // Платена рента в натура чрез и общо за страница
            $paid_renta_nat_by = [];
            if ($paid_renta_by_nat) {
                $amount = number_format($paid_renta_by_nat, 2, '.', '');
                $paid_renta_nat_by[] = $amount . ' лв.';
                $total_paid_renta_by_nat_amount += $amount;
            }
            if (!empty($paid_renta_nat_by_nat)) {
                foreach ($paid_renta_nat_by_nat as $key => $value) {
                    $quantity = number_format($value, 3, '.', '');
                    $paid_renta_nat_by[] = $quantity . ' X ' . $this->renta_types[$key];
                    $total_paid_renta_by_nat[$key] += $value;
                }
            }
            $results[$i]['paid_renta_nat_by'] = implode('</br>', $paid_renta_nat_by);
            $results[$i]['paid_renta_nat_by'] = '' != $results[$i]['paid_renta_nat_by'] ? $results[$i]['paid_renta_nat_by'] : '-';

            // Платена рента в натура детайлно
            $all_paid_renta_nat_by_detailed = [];
            if (!empty($paid_renta_nat_by_detailed)) {
                foreach ($paid_renta_nat_by_detailed as $key => $value) {
                    $quantity = number_format($value, 3, '.', '');
                    $unitValue = null != $paid_renta_nat_by_detailed_unit_value[$key] ? $paid_renta_nat_by_detailed_unit_value[$key] : $this->renta_types_values[$key];

                    if (null == $unitValue) {
                        $unitValue = '-';
                    }

                    $all_paid_renta_nat_by_detailed[] = $quantity . ' X ' . $this->renta_types[$key] . ' X ' . $unitValue . ' ед.ст.';
                    $total_all_paid_renta_nat_by_detailed[$key] += $value;
                }
            }
            $results[$i]['paid_renta_nat_by_detailed'] = implode('</br>', $all_paid_renta_nat_by_detailed);
            $results[$i]['paid_renta_nat_by_detailed'] = '' != $results[$i]['paid_renta_nat_by_detailed'] ? $results[$i]['paid_renta_nat_by_detailed'] : '-';

            // Общо платена рента в лева
            $sumPaidRenta = 0;
            if ($paid_renta || $paid_renta_by_nat) {
                $sumPaidRenta = $paid_renta + $paid_renta_by_nat;
                $results[$i]['total_by_renta'] = number_format($sumPaidRenta, 2, '.', '') . ' лв.';
            }

            // Общо платена рента в натура
            $totalByRentaNatura = [];
            if (!empty($paid_renta_nat) || !empty($paid_renta_nat_by_nat)) {
                $totalByRentaNatura = $this->arraySumIdenticalKeys($paid_renta_nat, $paid_renta_nat_by_nat);

                // Сумиране на Общо платена рента в натура - Общо за страница
                foreach (array_keys($total_sum_paid_renta_nat + $totalByRentaNatura) as $key) {
                    $total_sum_paid_renta_nat[$key] = @($total_sum_paid_renta_nat[$key] + $totalByRentaNatura[$key]);
                }
            }
            $results[$i]['total_by_renta_nat'] = implode('</br>', $totalByRentaNatura);
            $results[$i]['total_by_renta_nat'] = '' != $results[$i]['total_by_renta_nat'] ? $results[$i]['total_by_renta_nat'] : '-';

            // Платена рента в натура
            if (!empty($rentaNatByPlot)) {
                foreach ($sumRentaType as $rentaType => $rentaNatura) {
                    if (!is_null($results[$i]['paid_renta_nat_details'][$rentaType])) { // && $results[$i]['paid_renta_nat_details'][$rentaType] >= 0
                        $results[$i]['paid_renta_nat'] .= $results[$i]['paid_renta_nat_details'][$rentaType] . '</br>';
                    } else {
                        $results[$i]['paid_renta_nat'] .= '0.000</br>';
                    }
                }
            } else {
                $results[$i]['unpaid_renta_nat'] = '0.000';
            }
            if ((empty($rentaNatByPlot) || (is_array($sumRentaType) && empty($sumRentaType))) && !empty($results[$i]['paid_renta_nat_details'])) {
                $results[$i]['paid_renta_nat'] = '';

                foreach ($results[$i]['paid_renta_nat_details'] as $rentaType => $rentaNatura) {
                    $results[$i]['paid_renta_nat'] .= $rentaNatura . ' X ' . $this->renta_types[$rentaType] . '</br>';
                }
            }

            if (is_null($results[$i]['paid_renta_nat']) || '' == $results[$i]['paid_renta_nat']) {
                $results[$i]['paid_renta_nat'] = null;
            }

            if (0 == $rentaTypes) {
                $results[$i]['renta_nat_type'] = '[Без рента в натура]';
                $results[$i]['unpaid_renta_nat'] = null;
                $results[$i]['over_paid_nat'] = null;
                $results[$i]['unpaid_renta_nat_unit_value'] = null;
            }
            $results[$i]['paid_renta_nat_text'] = $results[$i]['paid_renta_nat'];
            $results[$i]['unpaid_renta_nat_text'] = $results[$i]['unpaid_renta_nat'];

            $results[$i]['renta'] = fixRounding($sumContractsDueRenta[$currentContractOrAnnexID], 0.01);
            $results[$i]['contract_renta'] = $results[$i]['renta'];

            $this->unpaid_renta += $results[$i]['unpaid_renta'];
            $results[$i]['unpaid_renta_orig'] = $results[$i]['unpaid_renta'];
            $results[$i]['unpaid_renta'] = number_format($results[$i]['unpaid_renta'], 2, '.', '');
            $results[$i]['renta'] = number_format($results[$i]['renta'], 2, '.', '');
            $results[$i]['paid_renta'] = number_format($results[$i]['paid_renta'], 2, '.', '');

            $results[$i]['all_owner_area'] = number_format($results[$i]['owner_area'], 3, '.', '');
            $results[$i]['owner_area'] = number_format($results[$i]['all_owner_area'], 3, '.', '');

            // ЛП: Ако имаме лично ползване изваждаме площта на личното ползване от площта на собственика
            if ($results[$i]['pu_area']) {
                $results[$i]['owner_area'] -= number_format($results[$i]['pu_area'], 3, '.', '');
            }
            // if ($personalUse) {
            //     foreach ($personalUse as $pu) {
            //         if ($pu['owner_id'] == $ownerID) {
            //             // Намираме, каква част от личното ползване е за конкретния собственик т.к. може да се явява и като наследник на друг собственик
            //             $personalUsePart = $results[$i]['owner_area'] / $pu['total_owned_area'];
            //             // Намираме площта на личното ползване за собственика
            //             $personalUseArea = round(($pu['total_personal_use_area'] * $personalUsePart), 3);
            //             // Добавяме площта за лично ползване към площта за лично ползване на собственика
            //             $results[$i]['pu_area'] += $personalUseArea;
            //         }
            //     }

            //     $results[$i]['owner_area'] -= number_format($results[$i]['pu_area'], 3, '.', '');
            // }

            // ### END multirent natura
            // put new rows if reps are more than one
            $tmp_reps_array = explode(', ', $results[$i]['rep_names']);
            if (count($tmp_reps_array) > 1) {
                $results[$i]['rep_names'] = implode(', </br>', $tmp_reps_array);
            }

            // increment global iterator
            $this->iterator++;

            // owner can occur more than once because of the heritors
            // grid elements can not have the same ID
            // assing global iterator value to grid row ID
            $results[$i]['id'] = $this->iterator;

            // if owner is dead then heritor info is required
            if ($results[$i]['is_dead']) {
                // get heritors data and put it inside row element children
                $results[$i]['children'] = $this->getOwnerHeritors(
                    $ownerID . '.*{1}',
                    1,
                    $ownerID,
                    null,
                    $contract_id,
                    $annex_id,
                    $year,
                    $renta_results,
                    $results[$i]
                );

                $results[$i]['renta'] = 0;
                $results[$i]['unpaid_renta'] = 0;
                $results[$i]['charged_renta'] = 0;
                $results[$i]['renta_nat'] = [];
                $results[$i]['renta_nat_text'] = '';
                $results[$i]['charged_renta_nat'] = [];
                $results[$i]['charged_renta_nat_text'] = '';
                $unpaidRentaNat = [];

                // ЛП: От Изплолзваната площ на родителя изваждаме площта за лично ползване на наследниците
                foreach ($results[$i]['children'] as $child) {
                    $results[$i]['owner_area'] -= ($child['pu_area'] + $child['children_pu_area']);
                    $results[$i]['renta'] += $child['renta'];

                    $results[$i]['charged_renta'] += $child['charged_renta'];
                    $results[$i]['unpaid_renta'] += $child['unpaid_renta'];

                    if (!empty($child['renta_nat'])) {
                        foreach ($child['renta_nat'] as $rentaType => $rentaNat) {
                            if (isset($results[$i]['renta_nat'][$rentaType])) {
                                $results[$i]['renta_nat'][$rentaType] += $rentaNat;
                                $unpaidRentaNat[$rentaType] += $rentaNat;
                            } else {
                                $results[$i]['renta_nat'][$rentaType] = $rentaNat;
                                $unpaidRentaNat[$rentaType] = $rentaNat;
                            }
                        }
                    }

                    if (!empty($child['charged_renta_nat'])) {
                        foreach ($child['charged_renta_nat'] as $rentaType => $rentaNat) {
                            if (isset($results[$i]['charged_renta_nat'][$rentaType])) {
                                $results[$i]['charged_renta_nat'][$rentaType] += $rentaNat;
                                $unpaidRentaNat[$rentaType] += $rentaNat;
                            } else {
                                $results[$i]['charged_renta_nat'][$rentaType] = $rentaNat;
                                $unpaidRentaNat[$rentaType] = $rentaNat;
                            }
                        }
                    }
                }
                // ЛП: След като се изчисляват дължимите ренти се изчисляват плащанията
                foreach ($results[$i]['children'] as $child) {
                    if (!empty($child['paid_renta_nat_details'])) {
                        foreach ($child['paid_renta_nat_details'] as $rentaType => $rentaNat) {
                            if (isset($unpaidRentaNat[$rentaType])) {
                                $unpaidRentaNat[$rentaType] -= $rentaNat;
                            }
                        }
                    }

                    if (!empty($child['children_paid_renta_nat_details'])) {
                        foreach ($child['children_paid_renta_nat_details'] as $rentaType => $rentaNat) {
                            if (isset($unpaidRentaNat[$rentaType])) {
                                $unpaidRentaNat[$rentaType] -= $rentaNat;
                            }
                        }
                    }
                }

                $results[$i]['charged_renta'] = number_format($results[$i]['charged_renta'], 2, '.', '');
                $results[$i]['renta'] = number_format($results[$i]['renta'], 2, '.', '');
                $results[$i]['unpaid_renta'] = number_format($results[$i]['unpaid_renta'], 2, '.', '');

                if (!empty($results[$i]['renta_nat'])) {
                    foreach ($results[$i]['renta_nat'] as $rentaType => $rentaNat) {
                        $results[$i]['renta_nat_text'] .= number_format(fixRounding($rentaNat), 3) . '</br>';
                    }
                }

                if (!empty($results[$i]['charged_renta_nat'])) {
                    foreach ($results[$i]['charged_renta_nat'] as $rentaType => $rentaNat) {
                        $results[$i]['charged_renta_nat_text'] .= number_format($rentaNat, 3) . '</br>';
                    }
                }

                if (!empty($unpaidRentaNat)) {
                    $results[$i]['unpaid_renta_nat'] = '';
                    $results[$i]['unpaid_renta_nat_unit_value'] = '';
                    foreach ($unpaidRentaNat as $rentaType => $rentaNat) {
                        $unitValue = 0;
                        foreach ($results[$i]['renta_types'] as $rentaTypeInfo) {
                            if ($rentaType == $rentaTypeInfo['id']) {
                                $unitValue = $rentaTypeInfo['unit_value'];

                                break;
                            }
                        }
                        $results[$i]['unpaid_renta_nat'] .= number_format($rentaNat, 3) . '</br>';
                        $results[$i]['unpaid_renta_nat_unit_value'] .= number_format(($rentaNat * $unitValue), 2) . ' лв. </br>';
                    }
                }

                if ($results[$i]['dead_date']) {
                    $results[$i]['owner_names'] .= ' - починал на: ' . (new DateTime($results[$i]['dead_date']))->format('d.m.Y');
                }

                $results[$i]['iconCls'] = 'icon-tree-user-rip';
            } else {
                switch ($results[$i]['owner_type']) {
                    case 0:
                        $results[$i]['iconCls'] = 'icon-tree-users';

                        break;
                    case 1:
                        $results[$i]['iconCls'] = 'icon-tree-user';

                        break;
                    default:
                        break;
                }
            }

            $results[$i]['owner_area'] = number_format($results[$i]['owner_area'], 3, '.', '');
            if ($results[$i]['pu_area']) {
                $results[$i]['pu_area'] = number_format($results[$i]['pu_area'], 3, '.', '');
            }

            // assign additional row values
            $results[$i]['is_heritor'] = false;
            $results[$i]['level'] = 1;
        }

        $total_paid_renta_by_amount += $this->total_heritors_paid_renta_by_amount;
        $arrHeritors = $this->total_heritors_paid_renta_by;
        $arrLegator = $total_paid_renta_by;

        if ((count($arrHeritors) + count($arrLegator)) > 0) {
            $sumTotalPaidRentaBy = [];
            foreach (array_keys($arrHeritors + $arrLegator) as $key) {
                $sumTotalPaidRentaBy[$key] = @($arrHeritors[$key] + $arrLegator[$key]);
            }
        }

        // Платена рента в лева чрез - Общо за страница
        $total_paid_renta_by_arr = [];
        if ($total_paid_renta_by_amount > 0) {
            $total_paid_renta_by_arr[] = number_format($total_paid_renta_by_amount, 2, '.', '') . ' лв.';
        }
        if (is_array($sumTotalPaidRentaBy) && $sumTotalPaidRentaBy) {
            foreach ($sumTotalPaidRentaBy as $key => $value) {
                $total_paid_renta_by_arr[] = number_format($value, 3, '.', '') . ' X ' . $this->renta_types[$key];
            }
        }
        $total_paid_renta_by_text = implode('</br>', $total_paid_renta_by_arr);

        // Сумиране на платена рента в натура чрез на наследодател и наследник - Общо за страница
        $total_paid_renta_by_nat_amount += $this->total_heritors_paid_renta_by_nat_amount;
        $arrHeritors = $this->total_heritors_paid_renta_by_nat;
        $arrLegator = $total_paid_renta_by_nat;

        if ((count($arrHeritors) + count($arrLegator)) > 0) {
            $sumTotalPaidRentaByNat = [];
            foreach (array_keys($arrHeritors + $arrLegator) as $key) {
                $sumTotalPaidRentaByNat[$key] = @($arrHeritors[$key] + $arrLegator[$key]);
            }
        }

        // Платена рента в натура чрез - Общо за страница
        $total_paid_renta_by_nat_arr = [];
        if ($total_paid_renta_by_nat_amount > 0) {
            $total_paid_renta_by_nat_arr[] = number_format($total_paid_renta_by_nat_amount, 2, '.', '') . ' лв.';
        }
        if (is_array($sumTotalPaidRentaByNat) && $sumTotalPaidRentaByNat) {
            foreach ($sumTotalPaidRentaByNat as $key => $value) {
                $total_paid_renta_by_nat_arr[] = number_format($value, 3, '.', '') . ' X ' . $this->renta_types[$key];
            }
        }
        $total_paid_renta_by_nat_text = implode('</br>', $total_paid_renta_by_nat_arr);

        // Сумиране на платена рента в натура детайлно на наследодател и наследник - Общо за страница
        $arrHeritors = $this->total_heritors_all_paid_renta_nat_by_detailed;
        $arrLegator = $total_all_paid_renta_nat_by_detailed;

        if ((count($arrHeritors) + count($arrLegator)) > 0) {
            $sumTotalPaidRentaByNatDetailed = [];
            foreach (array_keys($arrHeritors + $arrLegator) as $key) {
                $sumTotalPaidRentaByNatDetailed[$key] = @($arrHeritors[$key] + $arrLegator[$key]);
            }
        }

        // Платена рента в натура детайлно - Общо за страница
        $total_all_paid_renta_nat_by_detailed_arr = [];
        if (is_array($sumTotalPaidRentaByNatDetailed) && $sumTotalPaidRentaByNatDetailed) {
            foreach ($sumTotalPaidRentaByNatDetailed as $key => $value) {
                $quantity = number_format($value, 3, '.', '');
                $unitValue = null != $paid_renta_nat_by_detailed_unit_value[$key] ? $paid_renta_nat_by_detailed_unit_value[$key] : $this->renta_types_values[$key];

                if (null == $unitValue) {
                    $unitValue = '-';
                }

                $total_all_paid_renta_nat_by_detailed_arr[] = $quantity . ' X ' . $this->renta_types[$key] . ' X ' . $unitValue . ' ед.ст.';
            }
        }
        $total_all_paid_renta_nat_by_detailed_text = implode('</br>', $total_all_paid_renta_nat_by_detailed_arr);

        if (!empty($this->total_heritors_paid_renta_nat) || !empty($total_paid_renta_nat)) {
            $totalPaidRentaNat = $this->arraySumIdenticalKeys($this->total_heritors_paid_renta_nat, $total_paid_renta_nat);
        }

        $arrHeritors = $this->total_heritors_over_paid_renta_nat;
        $arrLegator = $total_over_paid_renta_nat;

        if ((count($arrHeritors) + count($arrLegator)) > 0) {
            $sumTotalOverPaidRentaByNat = [];
            foreach (array_keys($arrHeritors + $arrLegator) as $key) {
                $sumTotalOverPaidRentaByNat[$key] = @($arrHeritors[$key] + $arrLegator[$key]);
            }
        }

        // Визуализиране Надплатена рента в натура - Общо за страница
        $total_over_paid_renta_nat_text = '';
        $total_over_paid_renta_nat_arr = [];

        if (is_array($sumTotalOverPaidRentaByNat) && $sumTotalOverPaidRentaByNat) {
            foreach ($sumTotalOverPaidRentaByNat as $key => $value) {
                $total_over_paid_renta_nat_arr[] = number_format($value, 3, '.', '');
            }

            $total_over_paid_renta_nat_text = implode('</br>', $total_over_paid_renta_nat_arr);
        }

        // Сумиране на Остатък рента в натура на наследодател и наследник - Общо за страница
        $arrHeritors = $this->total_heritors_unpaid_renta_nat;
        $arrLegator = $total_unpaid_renta_nat;
        $sumTotalUnPaidRentaByNat = [];

        if ((count($arrHeritors) + count($arrLegator)) > 0) {
            foreach (array_keys($arrHeritors + $arrLegator) as $key) {
                $sumTotalUnPaidRentaByNat[$key] = @($arrHeritors[$key] + $arrLegator[$key]);
            }
        }
        ksort($sumTotalUnPaidRentaByNat);

        // Остатък рента в натура и в лева ед.ст. - Общо за страница
        $total_unpaid_renta_nat_arr = [];
        $total_unpaid_renta_nat_unit_value_arr = [];

        if (is_array($sumTotalUnPaidRentaByNat) && $sumTotalUnPaidRentaByNat) {
            foreach ($sumTotalUnPaidRentaByNat as $key => $value) {
                $total_unpaid_renta_nat_arr[] = number_format($value, 3, '.', '');

                $quantityValue = number_format($value * $this->renta_types_values[$key], 2, '.', '');
                $total_unpaid_renta_nat_unit_value_arr[] = $quantityValue . ' лв.';
            }
        }
        $total_unpaid_renta_nat_text = implode('</br>', $total_unpaid_renta_nat_arr);
        $total_unpaid_renta_nat_unit_value_text = implode('</br>', $total_unpaid_renta_nat_unit_value_arr);

        // Сумиране на Общо платена рента в натура - Общо за страница
        $total_sum_paid_renta_nat_arr = [];

        if (is_array($total_sum_paid_renta_nat) && $total_sum_paid_renta_nat) {
            foreach (array_keys($this->total_heritors_sum_by_paid_renta_nat + $total_sum_paid_renta_nat) as $key) {
                $total_sum_paid_renta_nat[$key] = @($this->total_heritors_sum_by_paid_renta_nat[$key] + $total_sum_paid_renta_nat[$key]);
            }
            foreach ($total_sum_paid_renta_nat as $key => $value) {
                $total_sum_paid_renta_nat_arr[] = number_format($value, 3, '.', '');
            }
        }
        $total_sum_paid_renta_nat_text = implode('</br>', $total_sum_paid_renta_nat_arr);

        $total_renta_type_text = '';
        // Типове рента
        if (is_array($this->renta_nat_type) && $this->renta_nat_type) {
            foreach ($this->renta_nat_type as $key => $value) {
                $total_renta_type_text .= $value . '</br>';
            }
        }

        // Recalculate payment grid data with personal use if exists
        // if ($this->hasPersonalUse($results)) {
        //     $UserDbPaymentsController->calculatePersonalUse($results, $personalUse, 'payments');
        //     $this->fixTextedRentsValues($results);
        // }

        $total_all_owner_area = 0;
        $total_pu_area = 0;
        $total_owner_area = 0;
        $total_renta = 0;
        $total_charged_renta = 0;
        $total_paid_renta = 0;
        $total_unpaid_renta = 0;
        $total_over_paid_renta = 0;
        $total_sum_by_paid_renta = 0;
        $total_renta_nat_renta_nat = [];
        $total_unpaid_renta_nat = [];
        $total_unpaid_renta_nat_unit_value = [];
        $total_renta_nat_charged_renta_nat_text = [];
        foreach ($results as $owner) {
            $total_all_owner_area += $owner['all_owner_area'];
            $total_pu_area += $owner['pu_area'];
            $total_owner_area += $owner['owner_area'];
            $total_renta += $owner['renta'];
            $total_charged_renta += $owner['charged_renta'];
            $total_paid_renta += $this->getOwnersPropSum([$owner], 'paid_renta');
            $total_unpaid_renta += $owner['unpaid_renta'];
            $total_over_paid_renta += $owner['over_paid'];
            $total_sum_by_paid_renta += $this->getOwnersPropSum([$owner], 'total_by_renta');

            foreach ($owner['renta_nat'] as $key => $value) {
                if (!isset($total_renta_nat_renta_nat[$key])) {
                    $total_renta_nat_renta_nat[$key] = 0;
                }
                $total_renta_nat_renta_nat[$key] += $value;
            }
            foreach ($total_renta_nat_renta_nat as $key => $value) {
                $total_renta_nat_renta_nat[$key] = number_format($value, 3, '.', '');
            }

            foreach ($owner['charged_renta_nat'] as $key => $value) {
                if (!isset($total_renta_nat_charged_renta_nat_text[$key])) {
                    $total_renta_nat_charged_renta_nat_text[$key] = 0;
                }
                $total_renta_nat_charged_renta_nat_text[$key] += $value;
            }
            foreach ($total_renta_nat_charged_renta_nat_text as $key => $value) {
                $total_renta_nat_charged_renta_nat_text[$key] = number_format($value, 3, '.', '');
            }
            $unpaidRentaNat = explode('</br>', $owner['unpaid_renta_nat']);
            foreach ($unpaidRentaNat as $key => $value) {
                if (!isset($total_unpaid_renta_nat[$key])) {
                    $total_unpaid_renta_nat[$key] = 0;
                }
                $total_unpaid_renta_nat[$key] += $value;
            }
            foreach ($total_unpaid_renta_nat as $key => $value) {
                $total_unpaid_renta_nat[$key] = number_format($value, 3, '.', '');
            }
            $unpaidRentaNatUnitValue = explode('</br>', $owner['unpaid_renta_nat_unit_value']);
            foreach ($unpaidRentaNatUnitValue as $key => $value) {
                if (!isset($total_unpaid_renta_nat_unit_value[$key])) {
                    $total_unpaid_renta_nat_unit_value[$key] = 0;
                }
                $total_unpaid_renta_nat_unit_value[$key] += $value;
            }
            foreach ($total_unpaid_renta_nat_unit_value as $key => $value) {
                $total_unpaid_renta_nat_unit_value[$key] = number_format($value, 3, '.', '');
            }
        }

        $return['rows'] = $results;
        $return['total'] = $counter[0]['count'];
        $return['footer'] = [
            [
                'iconCls' => 'no-background',
                'owner_names' => '',
                'rep_names' => '<b>ОБЩО за стр.</b>',
                'all_owner_area' => number_format($total_all_owner_area, 3, '.', ''),
                'pu_area' => number_format($total_pu_area, 3, '.', ''),
                'owner_area' => number_format($total_owner_area, 3, '.', ''),
                'renta' => number_format($total_renta, 2, '.', ''),
                'charged_renta' => number_format($total_charged_renta, 2, '.', ''),
                'renta_nat_text' => implode('</br>', $total_renta_nat_renta_nat),
                'charged_renta_nat_text' => implode('</br>', $total_renta_nat_charged_renta_nat_text),
                'paid_renta' => number_format($total_paid_renta, 2, '.', ''),
                // 'paid_renta_by'                => $total_paid_renta_by_text,
                'paid_renta_nat' => $total_paid_renta_nat_text,
                'paid_renta_nat_by' => $total_paid_renta_by_nat_text,
                'paid_renta_nat_by_detailed' => $total_all_paid_renta_nat_by_detailed_text,
                'unpaid_renta' => number_format($total_unpaid_renta, 2, '.', ''),
                'unpaid_renta_nat' => implode('</br>', $total_unpaid_renta_nat),
                'unpaid_renta_nat_unit_value' => implode('</br>', $total_unpaid_renta_nat_unit_value),
                'renta_nat_type' => $total_renta_type_text,
                'over_paid' => number_format($total_over_paid_renta, 2, '.', ''),
                'over_paid_nat' => $total_over_paid_renta_nat_text,
                'total_by_renta' => number_format($total_sum_by_paid_renta, 2, '.', ''),
                'personal_use_nat_types_names' => implode('</br>', array_column($totalPersonalUse, 'name')),
                'personal_use_amount' => implode('</br>', array_map(function ($num) {return number_format($num, 3);}, array_column($totalPersonalUse, 'amount'))),
                'personal_use_paid' => implode('</br>', array_map(function ($num) {return number_format($num, 3);}, array_column($totalPersonalUse, 'paid'))),
                'personal_use_unpaid' => implode('</br>', array_map(function ($num) {return number_format($num, 3);}, array_column($totalPersonalUse, 'unpaid'))),
            ],
        ];

        return $return;
    }

    public function hasPersonalUse($ownersTree)
    {
        foreach ($ownersTree as $owner) {
            if (!empty($owner['children'])) {
                if ($this->hasPersonalUse($owner['children'])) {
                    return true;
                }
            }
            if (!empty($owner['personal_use'])) {
                return true;
            }
        }

        return false;
    }

    /**
     * This method returns the sum of a property for all owners in the tree.
     * If the owner has any heritors, the values of the heritors for this property are also included in the sum.
     */
    private function getOwnersPropSum($ownersTree, $prop)
    {
        $sum = 0;
        foreach ($ownersTree as $owner) {
            if (!empty($owner['children'])) {
                $sum += $this->getOwnersPropSum($owner['children'], $prop);
            }
            $sum += $owner[$prop];
        }

        return $sum;
    }

    private function fixTextedRentsValues(&$ownersTree)
    {
        foreach ($ownersTree as &$owner) {
            if (!empty($owner['children'])) {
                $this->fixTextedRentsValues($owner['children']);
            }
            if (!empty($owner['unpaid_renta_nat'])) {
                $owner['unpaid_renta_nat_text'] = $owner['unpaid_renta_nat'];
            }
        }
    }

    /**
     * get owner heritors recursively.
     *
     * @param string $path
     * @param string $level
     * @param string $root_id
     * @param array|bool $parent_plots_ownage
     * @param int $contract_id
     * @param int $annex_id
     * @param int $year
     * @param array $renta_results
     *                             {
     *                             #item int id
     *                             #item string name
     *                             #item int unit
     *                             #item int unit_value
     *                             }
     *
     * @return array
     */
    private function getOwnerHeritors(
        $path,
        $level,
        $root_id,
        $parent_plots_ownage = false,
        $contract_id,
        $annex_id,
        $year,
        $renta_results,
        &$ownerResults
    ) {
        // init controllers
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);
        $UsersController = new UsersController('Users');
        $FarmingController = new FarmingController('Farming');

        $heritor_data = [];
        $heritor_percent_by_plots = [];
        $level++;

        $paid_renta_owner = $ownerResults['paid_renta'];
        $paid_renta_nat_owner = $ownerResults['paid_renta_nat_details'];
        $plots_contracts_renta = $ownerResults['plots_contracts_renta'];
        $plots_contracts_renta_nat = $ownerResults['plots_contracts_renta_nat'];
        $plots_contracts_due_renta_nat = $ownerResults['plots_contracts_due_renta_nat'];
        $plots_contracts_charged_renta_nat = $ownerResults['plots_contracts_charged_renta_nat'];
        $owner_plots_percent = $ownerResults['owner_plots_percent'];
        $plots_contracts_renta_per_plot = $ownerResults['plots_contracts_rent_per_plot'];

        $start_date = $GLOBALS['Farming']['years'][$year]['year'] . '-09-30';
        $due_date = ($GLOBALS['Farming']['years'][$year]['year'] - 1) . '-10-01';
        $farmYearStart = ($GLOBALS['Farming']['years'][$year]['year'] - 1) . '-10-01';
        $farmYearEnd = $GLOBALS['Farming']['years'][$year]['year'] . '-09-30';

        // get all heritors
        $options = [
            'return' => ['h.id'],
            'where' => [
                'path' => ['column' => 'path', 'compare' => '~', 'value' => $path],
            ],
        ];

        $counter = $UserDbOwnersController->getOwnersHeritors($options, true);

        if (0 == $counter[0]['count']) {
            return [];
        }

        // РЕНТА В ЛЕВА
        $plotsContractsRentaLegator = [];
        for ($m = 0; $m < $count = count($plots_contracts_renta); $m++) {
            $plot_contract_renta = $plots_contracts_renta[$m];

            $contractID = $plot_contract_renta['contract_id'];
            $plotID = $plot_contract_renta['plot_gid'];
            $renta_by_plot = $plot_contract_renta['renta_by_plot'];

            if (!is_null($plots_contracts_renta_per_plot)) {
                foreach ($plots_contracts_renta_per_plot as $owner_rent_per_plot) {
                    if ($owner_rent_per_plot['contract_id'] == $contractID && $owner_rent_per_plot['plot_gid'] == $plotID) {
                        $renta_by_plot = $owner_rent_per_plot['renta_by_plot'];

                        break;
                    }
                }
            }
            $plotsContractsRentaLegator[$contractID][$plotID] = $renta_by_plot;
        }
        $sumPlotsContractsRentaLegator = [];
        foreach ($plotsContractsRentaLegator as $contract_ID => $value) {
            foreach ($value as $plot_ID => $renta) {
                $sumPlotsContractsRentaLegator[$contract_ID] += $renta;
            }
        }

        $percentPlotsContractsRentaLegator = [];
        foreach ($plotsContractsRentaLegator as $contract_ID => $value) {
            foreach ($value as $plot_ID => $renta) {
                $rentaByPlot = 0;

                if ($sumPlotsContractsRentaLegator[$contract_ID] > 0) {
                    $rentaByPlot = $renta / $sumPlotsContractsRentaLegator[$contract_ID];
                }
                $percentPlotsContractsRentaLegator[$contract_ID][$plot_ID] = $rentaByPlot * $paid_renta_owner;
            }
        }

        $leftPlotsContractsRentaLegator = [];
        foreach ($plotsContractsRentaLegator as $contract_ID => $value) {
            foreach ($value as $plot_ID => $renta) {
                $percentRenta = $percentPlotsContractsRentaLegator[$contract_ID][$plot_ID];
                $leftPlotsContractsRentaLegator[$contract_ID][$plot_ID] = $renta - $percentRenta;
            }
        }

        // РЕНТА В НАТУРА
        if (!empty($plots_contracts_renta_nat)) {
            $sumPlotsContractsRentaNatLegator = [];
            foreach ($plots_contracts_renta_nat as $contract_ID => $value) {
                foreach ($value as $plot_ID => $rentaValue) {
                    foreach ($rentaValue as $rentaType => $rentaNat) {
                        $sumPlotsContractsRentaNatLegator[$contract_ID][$rentaType] += $rentaNat;
                    }
                }
            }

            // Изчисляване на намалената стойност от всеки имот, ако собственика е изплащал рента в натура
            $percentPlotsContractsRentaNatLegator = [];
            foreach ($plots_contracts_renta_nat as $contract_ID => $value) {
                foreach ($value as $plot_ID => $rentaValue) {
                    foreach ($rentaValue as $rentaType => $rentaNat) {
                        $rentaNatByPlot = 0;

                        if ($sumPlotsContractsRentaNatLegator[$contract_ID][$rentaType] > 0) {
                            $rentaNatByPlot = $rentaNat / $sumPlotsContractsRentaNatLegator[$contract_ID][$rentaType];
                        }

                        $percentPlotsContractsRentaNatLegator[$contract_ID][$plot_ID][$rentaType] = $rentaNatByPlot * $paid_renta_nat_owner[$rentaType];
                    }
                }
            }

            $leftPlotsContractsRentaNatLegator = [];
            foreach ($plots_contracts_renta_nat as $contract_ID => $value) {
                foreach ($value as $plot_ID => $rentaValue) {
                    foreach ($rentaValue as $rentaType => $rentaNat) {
                        $percentRentaNat = $percentPlotsContractsRentaNatLegator[$contract_ID][$plot_ID][$rentaType];

                        $leftPlotsContractsRentaNatLegator[$contract_ID][$plot_ID][$rentaType] = $rentaNat - $percentRentaNat;
                    }
                }
            }
        }

        $options = [
            'return' => [
                'o.id as root_id', 'o.post_payment_fields', 'po.percent', 'gid', 'c.id as contract_id', 'pc.id as pc_rel_id', 'c.c_num',
                '(pc.area_for_rent) as contract_area', 'COALESCE(pu.area, 0) as pu_area', 'kvs.kad_ident as plots_name',
                '(case when a.id is not null then a.id else c.id end) as contract_anex_arr',

                '(CASE WHEN a.renta IS NULL THEN

                            (
                                CASE
                                WHEN pc.rent_per_plot IS NOT NULL THEN
                                    0
                                ELSE
                                    C .renta
                                END
                            )
                        ELSE
                            (
                                CASE
                                WHEN pc.rent_per_plot IS NOT NULL THEN
                                    0
                                ELSE
                                    A .renta
                                END
                            )
                        END
                        ) as contract_renta',

                '(CASE
                    WHEN pc.rent_per_plot IS NOT NULL THEN
                        pc.rent_per_plot
                    ELSE
                        NULL
                    END
                ) AS contracts_rent_per_plot',
                ' cr.renta as charged_renta',

                'MAX(crn.converted_charged_renta_nat) AS converted_charged_renta_nat',
                '(CASE WHEN a.renta_nat IS NULL THEN c.renta_nat ELSE a.renta_nat END) as renta_nat',
                'CASE WHEN o.rent_place IS NULL THEN r.rent_place ELSE o.rent_place END AS rent_place',
                '(CASE WHEN a.renta_nat_type_id IS NULL THEN c.renta_nat_type_id ELSE a.renta_nat_type_id END) as renta_nat_type_id',
            ],
            'where' => [
                'owner_id' => ['column' => 'id', 'compare' => '=', 'prefix' => 'o', 'value' => $root_id],
                'contract_type' => ['column' => 'nm_usage_rights', 'compare' => 'NOT IN', 'prefix' => 'c', 'value' => [1, 4]],
                'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
                'is_annnex' => ['column' => 'is_annex', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
                'annex_action' => ['column' => 'annex_action', 'compare' => '=', 'prefix' => 'pc', 'value' => 'added'],
                'percent' => ['column' => 'percent', 'compare' => '>', 'prefix' => 'po', 'value' => '0'],
                'is_heritor' => ['column' => 'is_heritor', 'compare' => '=', 'prefix' => 'po', 'value' => 'FALSE'],
                'active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'TRUE'],
                'contract_id' => ['column' => 'id', 'compare' => '=', 'prefix' => 'c', 'value' => $contract_id],
            ],
            'start_date' => $start_date,
            'due_date' => $due_date,
            'year_id' => [$year],
            'group' => 'gid, o.id, po.percent, c.id, pc.id, cr.renta, cr.nat_is_converted, cr.renta_nat, r.rent_place, a.id, a.renta, a.renta_nat, a.renta_nat_type_id, pu.area',
        ];

        $plot_results = $UserDbPaymentsController->getPayrollData($options, false, false);

        // get all heritors
        $options = [
            'return' => [
                'distinct on(o.id) h.id', 'uuid_generate_v4() as uuid', "o.name || ' ' || o.surname || ' ' || o.lastname as owner_names",
                'h.owner_id',
                $UserDbOwnersController::isDead('o', [$farmYearStart, $farmYearEnd]),
                $UserDbOwnersController::allowOwnerPayment('o', [$farmYearStart, $farmYearEnd]),
                'o.post_payment_fields::text',
                'h.path',
                'o.egn as egn_eik',
                'o.iban',
                'o.lk_nomer',
                'o.lk_izdavane',
                'o.address as owner_address',
                'o.egn as owner_egn',
                "TRIM(r.rep_name) || ' ' || TRIM(r.rep_surname) || ' ' || TRIM(r.rep_lastname) as rep_names",
                'r.rep_egn as rep_egn',
                'r.id as rep_id',
                'r.rep_lk_izdavane as rep_lk_izdavane',
                'r.rep_lk as rep_lk',
                'r.iban as rep_iban',
                'r.rep_address as rep_address',
            ],
            'where' => [
                'path' => ['column' => 'path', 'prefix' => 'h', 'compare' => '~', 'value' => $path],
            ],
            'onCondition' => 'AND poi.pc_rel_id IN (' . implode(',', array_column($plot_results, 'pc_rel_id')) . ')',
        ];

        $heritor_results = $UserDbOwnersController->getOwnersHeritors($options, false, false);

        $personalUseOptions = [
            'contract_id' => $annex_id ? $annex_id : $contract_id,
            'year' => $year,
            'chosen_years' => $year,
        ];
        $personalUse = $UserDbPaymentsController->getPersonalUseForOwners($personalUseOptions, false, false);

        // iterate all plots
        for ($i = 0; $i < $count = count($plot_results); $i++) {
            $plotID = $plot_results[$i]['gid'];

            // get heritors for the current plot
            $options = [
                'return' => [
                    'h.id', 'owner_id', 'is_dead', 'path', 'dead_date',
                    "(SELECT poi.percent FROM su_plots_owners_rel poi WHERE poi.path = h.path AND poi.is_heritor = true AND poi.pc_rel_id = {$plot_results[$i]['pc_rel_id']}) as percent",
                ],
                'where' => [
                    'path' => ['column' => 'path', 'compare' => '~', 'value' => $path],
                ],
            ];

            $results = $UserDbOwnersController->getOwnersHeritors($options, false, false);
            $resultsCount = count($results);
            $contract_arr[] = $plot_results[$i]['contract_id'];

            $sum_custom_ownage = 0;
            $heritors = 0;
            $all_heritors_count = count($results);

            for ($j = 0; $j < $all_heritors_count; $j++) {
                if (null != $results[$j]['percent']) {
                    $sum_custom_ownage += $results[$j]['percent'];
                } else {
                    $heritors++;
                }
            }

            $personPlotPercent = [];
            $personTotalArea = 0;
            for ($j = 0; $j < $resultsCount; $j++) {
                $ownerID = $results[$j]['owner_id'];
                $parent_path_e = explode('.', $results[$j]['path'], -1);
                $parent_path = implode('.', $parent_path_e);

                // Намираме процента на наследника за всеки имот
                if (!$results[$j]['percent'] || 0 == $results[$j]['percent']) {
                    if ($parent_plots_ownage[$plotID][$parent_path] || '0' == $parent_plots_ownage[$plotID][$parent_path]) {
                        if ('0' == $results[$j]['percent']) {
                            $results[$j]['percent'] = '0';
                        } else {
                            $results[$j]['percent'] = ($parent_plots_ownage[$plotID][$parent_path] - $sum_custom_ownage) / $heritors;
                        }
                        $heritor_percent_by_plots[$plotID][$results[$j]['path']] = $results[$j]['percent'];
                    } else {
                        if (null == $results[$j]['percent']) {
                            $results[$j]['percent'] = ($plot_results[$i]['percent'] - $sum_custom_ownage) / $heritors;
                            $heritor_percent_by_plots[$plotID][$results[$j]['path']] = $results[$j]['percent'];
                        } elseif ('0' == $results[$j]['percent']) {
                            $heritor_percent_by_plots[$plotID][$results[$j]['path']] = '0';
                        }
                    }
                } else {
                    $heritor_percent_by_plots[$plotID][$results[$j]['path']] = $results[$j]['percent'];
                }

                $results[$j]['percent_heritor'] = 0;

                if ($owner_plots_percent[$plot_results[$i]['contract_id']][$plotID] > 0) {
                    $results[$j]['percent_heritor'] = $results[$j]['percent'] / $owner_plots_percent[$plot_results[$i]['contract_id']][$plotID];
                }
                $heritor_data[$ownerID]['owner_plots_percent'][$plot_results[$i]['contract_id']][$plotID] = $results[$j]['percent'];

                // В $heritor_data се съхранява информация за наследника

                $renta_nats = $UserDbController->DbHandler->getDataByQuery(
                    "SELECT 
                        crt.renta_value * round((pc.area_for_rent) ::numeric, 3) AS renta_nat, --- Тази стойност вече се изчислява в долния цъкък, за да може да се време в предвид и личното ползване
                        crt.renta_value, 
                        pc.id as pc_rel_id,
                        pc.area_for_rent, 
                        po.percent, 
                        c.id as contract_id,
                        po.owner_id, 
                        crt.renta_id
                    FROM su_contracts c
                    INNER JOIN su_contracts_plots_rel pc ON(pc.contract_id = c.id)
                    INNER JOIN layer_kvs kvs ON(kvs.gid = pc.plot_id)
                    LEFT JOIN su_plots_owners_rel po ON(po.pc_rel_id = pc.id)
                    LEFT JOIN su_personal_use pu ON (pu.owner_id = {$root_id} AND pu.year = {$year} AND pu.pc_rel_id = pc.id)
                    LEFT JOIN su_contracts_rents crt ON (c.id = crt.contract_id)
                    where c.id = {$plot_results[$i]['contract_anex_arr']}
                    and pc.annex_action ='added'
                    and po.owner_id ={$root_id}
                    and pc.plot_id = {$plotID}
                    and pc.rent_per_plot isnull
                    ORDER BY crt.renta_id"
                );

                $heritor_data[$ownerID]['dead_date'] = $results[$j]['dead_date'];

                $plotContractArea = number_format($plot_results[$i]['contract_area'] * ($results[$j]['percent'] / 100), 3, '.', '');

                // Запазва се площта на наследника по догоовр - преди да се приспадне личното ползване
                $heritor_data[$ownerID]['all_owner_area'] += $plotContractArea;

                // На база площта на родителя и процента на наследника намираме общата площ, която притежава наследника. Тя ни е нужна за определянето на процента ЛП за всеки наследник
                $оwnerArea = $ownerResults['all_owner_area'] * ($results[$j]['percent_heritor']);

                // Площта за лично ползване, ако има такава, ще бъде изчислена по-долу
                $puArea = $UserDbPaymentsController->getPersonalUseArea($personalUse, $ownerID, $plot_results[$i], $оwnerArea);

                $contractAreaWithoutPUarea = $plotContractArea - $puArea;

                // calculate all needed information about heritor
                $heritor_data[$ownerID]['root_id'] = $plot_results[$i]['root_id'];
                $heritor_data[$ownerID]['c_num_array'][] = $plot_results[$i]['c_num'];
                $heritor_data[$ownerID]['area_array'][$plot_results[$i]['contract_id']][$plotID] = $contractAreaWithoutPUarea;
                $heritor_data[$ownerID]['area'] += $plotContractArea - $puArea;
                $heritor_data[$ownerID]['pu_area'] += $puArea;
                $heritor_data[$ownerID]['contract_id'] = $plot_results[$i]['contract_id'];

                // if (!empty($leftPlotsContractsRentaNatLegator[$plot_results[$i]['contract_id']])) {
                //     foreach ($leftPlotsContractsRentaNatLegator[$plot_results[$i]['contract_id']][$plotID] as $rentaType => $rentaNat) {
                //         $heritor_data[$ownerID]['plots_contracts_renta_nat_heritors'][$plot_results[$i]['contract_id']][$plotID][$rentaType] = $rentaNat * $results[$j]['percent_heritor'];
                //     }
                // }

                $personPlotPercent[$ownerID] += $heritor_data[$ownerID]['area'];
                $personTotalArea += $heritor_data[$ownerID]['area'];

                $heritor_data[$ownerID]['plots_contracts_area_array'][] = [
                    'c_num' => $plot_results[$i]['c_num'],
                    'pc_id' => $plot_results[$i]['contract_id'],
                    'plot_name' => $plot_results[$i]['plots_name'],
                    'area' => $contractAreaWithoutPUarea,
                ];

                // Пресмята се рентата за всеки имот, като се взема в предвид площтта за лично ползване към него
                $heritor_data[$ownerID]['contracts_renta_plots'][$plot_results[$i]['contract_id']][$plotID] = ($plot_results[$i]['contracts_rent_per_plot'] ?? $plot_results[$i]['contract_renta']) * $contractAreaWithoutPUarea;

                // Този ред е закоментиран, защото, вече не взимаме рентата от родителя. Заради личното ползване за всеки човек се изчислява отделно на база площта, която му е останала след приспадане на площта за лично ползване
                // $heritor_data[$ownerID]['plots_contracts_renta_heritors'][$plot_results[$i]['contract_id']][$plotID] = $leftPlotsContractsRentaLegator[$plot_results[$i]['contract_id']][$plotID] * $results[$j]['percent_heritor'];
                $heritor_data[$ownerID]['nat_type_ids'] = [];
                foreach ($renta_nats as $rentaKey => $rentaVal) {
                    if (!is_null($rentaVal['renta_id']) && !is_null($rentaVal['renta_nat'])) {
                        $rentaNat = $rentaVal['renta_value'] * $contractAreaWithoutPUarea;
                        $heritor_data[$ownerID]['contracts_renta_nat_plots'][$plot_results[$i]['contract_id']][$plotID][$rentaVal['renta_id']] += $rentaNat;
                        $heritor_data[$ownerID]['nat_type_ids'][] = $rentaVal['renta_id'];
                    }
                }

                if (!is_null($plot_results[$i]['charged_renta']) && $plot_results[$i]['charged_renta'] >= 0) {
                    if (null != $plot_results[$i]['contracts_rent_per_plot']) {
                        $plot_results[$i]['charged_renta'] = $plot_results[$i]['contracts_rent_per_plot'];
                    }

                    // Пресмята се начислената рентата за всеки имот, като се взема в предвид площтта за лично ползване към него
                    $heritor_data[$ownerID]['charged_renta_plots'][$plot_results[$i]['contract_id']][$plotID] = $plot_results[$i]['charged_renta'] * $contractAreaWithoutPUarea;
                }

                if (!is_null($plot_results[$i]['converted_charged_renta_nat']) && $plot_results[$i]['converted_charged_renta_nat'] >= 0) {
                    if (null != $plot_results[$i]['contracts_rent_per_plot']) {
                        $plot_results[$i]['converted_charged_renta_nat'] = $plot_results[$i]['contracts_rent_per_plot'];
                    }
                    $heritor_data[$ownerID]['charged_renta_plots'][$plot_results[$i]['contract_id']][$plotID] += $plot_results[$i]['converted_charged_renta_nat'] * $contractAreaWithoutPUarea;
                }
            }
        }

        // В долния цикъл се извъртат отново всички наследници, като в него се изчисляват платените и остатъчните ренти. Както и се формира крайния масив, който ще се върне - $heritor_results

        // prepare heritor results for grid format
        for ($i = 0; $i < count($heritor_results); $i++) {
            $ownerID = $heritor_results[$i]['owner_id'];

            $paidOptions = [
                'custom_counter' => 'COUNT(DISTINCT(p.owner_id))',
                'order' => 'asc',
                'sort' => 'p.contract_id',
                'return' => [
                    'p.id as payment_id',
                    'p.owner_id as owner_id',
                    'p.contract_id as contract_id',
                    'case when pn.amount notnull and pn.unit_value notnull  then pn.amount * pn.unit_value else p.amount end as trans_amount',
                    'case when pn.amount notnull then pn.amount else p.amount_nat end as amount_nat',
                    'pn.amount::numeric as trans_amount_nat',
                    'pn.nat_type as nat_type',
                    'p.paid_in',
                    'p.paid_from',
                    'rent.name as trans_nat_type_text',
                    'round(pn.unit_value::numeric, 2)',
                ],
                'where' => [
                    'path' => ['column' => 'path', 'compare' => '~', 'prefix' => 'p', 'value' => $heritor_results[$i]['path']],
                    'contract_id' => ['column' => 'contract_id', 'compare' => '=', 'prefix' => 'p', 'value' => $contract_id],
                    'year' => ['column' => 'farming_year', 'compare' => '=', 'prefix' => 'p', 'value' => $year],
                ],
                'year_id' => $year,
            ];

            $paidResults = $UserDbPaymentsController->getPaidData($paidOptions, false, false);

            $paid_renta = 0;
            $paid_renta_by_nat = 0;
            $paid_renta_nat = [];
            $paid_renta_nat_by_nat = [];
            $paid_renta_nat_by_detailed = [];
            $paid_renta_nat_by_detailed_unit_value = [];
            $paymentIds = [];
            for ($m = 0; $m < $count = count($paidResults); $m++) {
                $paidResult = $paidResults[$m];
                $renta_type = $paidResult['nat_type'];
                $contractID = $paidResult['contract_id'];

                if (1 == $paidResult['paid_from']) {
                    if (2 == $paidResult['paid_in']) {
                        $paid_renta_nat[$renta_type] += $paidResult['amount_nat'];
                        $heritor_results[$i]['paid_renta_nat_by_contract'][$contractID][$renta_type] += $paidResult['amount_nat'];

                        continue;
                    }

                    $paid_renta += $paidResult['trans_amount'];
                } elseif (2 == $paidResult['paid_from']) {
                    if (2 == $paidResult['paid_in']) {
                        $paid_renta_nat_by_nat[$renta_type] += $paidResult['trans_amount_nat'];
                        $heritor_results[$i]['paid_renta_nat_by_contract'][$contractID][$renta_type] += $paidResult['trans_amount_nat'];

                        continue;
                    }

                    // Наследник - Платена рента в натура детайлно
                    if (array_key_exists($renta_type, $paid_renta_nat_by_detailed)) {
                        $paid_renta_nat_by_detailed[$renta_type] += $paidResult['trans_amount_nat'];
                    } else {
                        $paid_renta_nat_by_detailed[$renta_type] = $paidResult['trans_amount_nat'];
                        $paid_renta_nat_by_detailed_unit_value[$renta_type] = $paidResult['unit_value'];
                    }
                    $this->total_heritors_all_paid_renta_nat_by_detailed[$renta_type] += $paidResult['trans_amount_nat'];

                    $paymentIds[] = $paidResult['payment_id'];
                    $paid_renta_by_nat += $paidResult['trans_amount'];
                }
            }

            // count all heritors
            $pathOwner = $heritor_results[$i]['path'] . '.*{1}';

            $options = [
                'return' => [
                    'h.id',
                ],
                'where' => [
                    'path' => ['column' => 'path', 'compare' => '~', 'value' => $pathOwner],
                ],
            ];

            $counterHeritors = $UserDbOwnersController->getOwnersHeritors($options, true);

            // Сортиране на платерената рента чрез натура
            ksort($paid_renta_nat_by_nat);
            ksort($paid_renta_nat);

            // Наследник - Платена рента в лева чрез - format to grid
            $paid_renta_by = [];
            if ($paid_renta) {
                $amount = number_format($paid_renta, 2, '.', '');
                $paid_renta_by[] = $amount . ' лв.';
                $this->total_heritors_paid_renta_by_amount += $paid_renta;
            }
            if (!empty($paid_renta_nat)) {
                foreach ($paid_renta_nat as $key => $value) {
                    $quantity = number_format($value, 3, '.', '');
                    $paid_renta_by[] = $quantity . ' X ' . $this->renta_types[$key];
                    $this->total_heritors_paid_renta_by[$key] += $value;
                }
            }
            $heritor_results[$i]['paid_renta_by'] = implode('</br>', $paid_renta_by);
            $heritor_results[$i]['paid_renta_by'] = '' != $heritor_results[$i]['paid_renta_by'] ? $heritor_results[$i]['paid_renta_by'] : '-';

            // Наследник - Платена рента в натура чрез - format to grid
            $paid_renta_nat_by = [];
            if ($paid_renta_by_nat) {
                $amount = number_format($paid_renta_by_nat, 2, '.', '');
                $paid_renta_nat_by[] = $amount . ' лв.';
                $this->total_heritors_paid_renta_by_nat_amount += $paid_renta_by_nat;
            }
            if (!empty($paid_renta_nat_by_nat)) {
                foreach ($paid_renta_nat_by_nat as $key => $value) {
                    $quantity = number_format($value, 3, '.', '');
                    $paid_renta_nat_by[] = $quantity . ' X ' . $this->renta_types[$key];
                    $this->total_heritors_paid_renta_by_nat[$key] += $value;
                }
            }
            $heritor_results[$i]['paid_renta_nat_by'] = implode('</br>', $paid_renta_nat_by);
            $heritor_results[$i]['paid_renta_nat_by'] = '' != $heritor_results[$i]['paid_renta_nat_by'] ? $heritor_results[$i]['paid_renta_nat_by'] : '-';

            // Наследник - Платена рента в натура детайлно - format to grid
            $all_paid_renta_nat_by_detailed = [];
            if (!empty($paid_renta_nat_by_detailed)) {
                foreach ($paid_renta_nat_by_detailed as $key => $value) {
                    $quantity = number_format($value, 3, '.', '');
                    $unitValue = null != $paid_renta_nat_by_detailed_unit_value[$key] ? $paid_renta_nat_by_detailed_unit_value[$key] : $this->renta_types_values[$key];

                    if (null == $unitValue) {
                        $unitValue = '-';
                    }

                    $all_paid_renta_nat_by_detailed[] = $quantity . ' X ' . $this->renta_types[$key] . ' X ' . $unitValue . ' ед.ст.';
                }
            }
            $heritor_results[$i]['paid_renta_nat_by_detailed'] = implode('</br>', $all_paid_renta_nat_by_detailed);
            $heritor_results[$i]['paid_renta_nat_by_detailed'] = '' != $heritor_results[$i]['paid_renta_nat_by_detailed'] ? $heritor_results[$i]['paid_renta_nat_by_detailed'] : '-';

            // Наследник - Общо платена рента в лева
            $sumPaidRenta = 0;
            if ($paid_renta || $paid_renta_by_nat) {
                $sumPaidRenta = $paid_renta + $paid_renta_by_nat;
                $heritor_results[$i]['total_by_renta'] = number_format($sumPaidRenta, 2, '.', '') . ' лв.';
            }
            $heritor_results[$i]['total_by_renta'] = '' != $heritor_results[$i]['total_by_renta'] ? $heritor_results[$i]['total_by_renta'] : '-';

            // Сумиране на Общо платена рента в лева за Наследник
            $this->total_heritors_sum_by_paid_renta += $sumPaidRenta;

            // Наследник - Общо платена рента в натура
            $totalByRentaNatura = [];
            if (!empty($paid_renta_nat) || !empty($paid_renta_nat_by_nat)) {
                $totalByRentaNatura = $this->arraySumIdenticalKeys($paid_renta_nat, $paid_renta_nat_by_nat);

                // Наследници - Сумиране на Общо платена рента в натура - Общо за страница
                foreach (array_keys($this->total_heritors_sum_by_paid_renta_nat + $totalByRentaNatura) as $key) {
                    $this->total_heritors_sum_by_paid_renta_nat[$key] = @($this->total_heritors_sum_by_paid_renta_nat[$key] + $totalByRentaNatura[$key]);
                }
            }
            $heritor_results[$i]['total_by_renta_nat'] = implode('</br>', $totalByRentaNatura);
            $heritor_results[$i]['total_by_renta_nat'] = '' != $heritor_results[$i]['total_by_renta_nat'] ? $heritor_results[$i]['total_by_renta_nat'] : '-';

            $heritor_results[$i] = $this->addNewRentaPayrolls($paidResults, $heritor_results[$i]);
            $heritor_results[$i]['root_id'] = $heritor_data[$ownerID]['root_id'];

            if (!$heritor_data[$ownerID]['c_num_array']) {
                $heritor_data[$ownerID]['c_num_array'] = [];
            }

            $heritor_results[$i]['dead_date'] = $heritor_data[$ownerID]['dead_date'];
            $heritor_results[$i]['c_num_array'] = array_unique($heritor_data[$ownerID]['c_num_array']);
            $heritor_results[$i]['contract_id'] = $heritor_data[$ownerID]['contract_id'];
            $heritor_results[$i]['owner_area'] = number_format($heritor_data[$ownerID]['all_owner_area'], 3, '.', '');
            $heritor_results[$i]['area_array'] = $heritor_data[$ownerID]['area_array'];
            $heritor_results[$i]['owner_plots_percent'] = $heritor_data[$ownerID]['owner_plots_percent'];
            $heritor_results[$i]['nat_type_ids'] = $heritor_data[$ownerID]['nat_type_ids'];
            $heritor_results[$i]['renta_nat_type_id'] = $heritor_results[$i]['nat_type_ids'];

            $heritor_results[$i]['plots_contracts_area_array'] = $heritor_data[$ownerID]['plots_contracts_area_array'];

            $heritor_results[$i]['all_owner_area'] = number_format($heritor_results[$i]['owner_area'], 3, '.', '');
            if ($heritor_data[$ownerID]['pu_area']) {
                $heritor_results[$i]['pu_area'] = number_format($heritor_data[$ownerID]['pu_area'], 3, '.', '');
                $heritor_results[$i]['owner_area'] = number_format($heritor_results[$i]['all_owner_area'] - $heritor_results[$i]['pu_area'], 3, '.', '');
            }

            // ЛП: Ако имаме лично ползване изваждаме площта на личното ползване от площта на собственика
            // if ($personalUse) {
            //     foreach ($personalUse as $pu) {
            //         if ($pu['owner_id'] == $ownerID) {
            //             // Намираме, каква част от личното ползване е за конкретния собственик т.к. може да се явява и като наследник на друг собственик
            //             $personalUsePart = $heritor_results[$i]['owner_area'] / $pu['total_owned_area'];
            //             // Намираме площта на личното ползване за собственика
            //             $personalUseArea = round(($pu['total_personal_use_area'] * $personalUsePart), 3);
            //             // Добавяме площта за лично ползване към площта за лично ползване на собственика
            //             $heritor_results[$i]['pu_area'] += $personalUseArea;
            //         }
            //     }

            //     $heritor_results[$i]['owner_area'] -= number_format($heritor_results[$i]['pu_area'], 3, '.', '');
            // }

            // Начислена рента в пари
            if (!empty($heritor_data[$ownerID]['charged_renta_plots'])) {
                foreach ($heritor_data[$ownerID]['charged_renta_plots'] as $contractID => $value) {
                    foreach ($value as $plotID => $plotRenta) {
                        // ЛП: Вече не вземаме рентите от собствениците, а се изчисляват за всеки наследник, заради приспаданията на площи от лично ползване
                        // $plotContractRentaHeritor = $heritor_data[$ownerID]['plots_contracts_renta_heritors'][$contractID];

                        $plotContractRentaHeritor = $heritor_data[$ownerID]['charged_renta_plots'][$contractID];

                        if (array_key_exists($plotID, $plotContractRentaHeritor)) {
                            $heritor_results[$i]['charged_renta'] += $plotContractRentaHeritor[$plotID];
                        }
                    }
                }
            }

            $heritor_results[$i]['due_renta'] = 0;

            // Форматира се стойността за начислената рента. Ако няма начислена рента се показва '-'
            if (!is_null($heritor_results[$i]['charged_renta'])) {
                $heritor_results[$i]['charged_renta'] = $heritor_results[$i]['charged_renta'] > 0 ? number_format($heritor_results[$i]['charged_renta'], 2, '.', '') : '0.00';
            } else {
                $heritor_results[$i]['charged_renta'] = '-';
            }

            if (!empty($heritor_data[$ownerID]['contracts_renta_plots'])) {
                foreach ($heritor_data[$ownerID]['contracts_renta_plots'] as $contractID => $value) {
                    foreach ($value as $plotID => $plotRenta) {
                        // ЛП: Вече не вземаме рентите от собствениците, а се изчисляват за всеки наследник, заради приспаданията на площи от лично ползване
                        // $plotContractRentaHeritor = $heritor_data[$ownerID]['plots_contracts_renta_heritors'][$contractID];

                        $plotContractRentaHeritor = $heritor_data[$ownerID]['contracts_renta_plots'][$contractID];
                        $onlyContractChargedRenta = $heritor_data[$ownerID]['charged_renta_plots'][$contractID];

                        $onlyContractChargedRenta = null == $onlyContractChargedRenta ? $onlyContractChargedRenta = [] : $onlyContractChargedRenta;

                        if ($plotContractRentaHeritor[$plotID] > 0) {
                            if (array_key_exists($plotID, $plotContractRentaHeritor) && !array_key_exists($plotID, $onlyContractChargedRenta)) {
                                $heritor_results[$i]['renta'] += $plotContractRentaHeritor[$plotID];
                                $heritor_results[$i]['plots_contracts_renta_down_grid'][$contractID][$plotID] = $plotContractRentaHeritor[$plotID];
                            }
                        }
                    }
                }
            }

            $heritor_results[$i]['due_renta'] += $heritor_results[$i]['renta'] + $heritor_results[$i]['charged_renta'];

            $heritor_results[$i]['renta'] = $heritor_results[$i]['renta'] > 0 ? number_format($heritor_results[$i]['renta'], 2, '.', '') : '0.00';

            // Начислена рента в натура
            $heritor_results[$i]['plots_contracts_charged_renta_nat_values'] = $ownerResults['plots_contracts_charged_renta_nat_values'];
            $heritor_results[$i]['plots_contracts_renta'] = $ownerResults['plots_contracts_renta'];

            if (!empty($heritor_results[$i]['plots_contracts_charged_renta_nat_values'])) {
                foreach ($heritor_results[$i]['plots_contracts_charged_renta_nat_values'] as $contractID => $value) {
                    foreach ($value as $plotID => $plotIDRenta) {
                        foreach ($plotIDRenta as $rentaType => $rentaNat) {
                            foreach ($heritor_results[$i]['plots_contracts_renta'] as $contractPlot) {
                                if ($contractPlot['root_contract_id'] == $contractID && $contractPlot['plot_gid'] == $plotID) {
                                    $puArea = $UserDbPaymentsController->getPersonalUseArea($personalUse, $ownerID, ['contract_id' => $contractID, 'pc_rel_id' => $contractPlot['pc_rel_id']], $heritor_data[$ownerID]['owner_area']);

                                    $plotArea = $heritor_results[$i]['area_array'][$contractID][$plotID] - $puArea;
                                }
                            }

                            if ($heritor_results[$i]['charged_renta_nat'][$rentaType]) {
                                $heritor_results[$i]['charged_renta_nat'][$rentaType] += $rentaNat * $plotArea;
                            } else {
                                $heritor_results[$i]['charged_renta_nat'][$rentaType] = $rentaNat * $plotArea;
                            }

                            if ($heritor_results[$i]['contracts_charged_renta_nat_plots'][$contractID][$plotID][$rentaType]) {
                                $heritor_results[$i]['contracts_charged_renta_nat_plots'][$contractID][$plotID][$rentaType] = $rentaNat * $plotArea;
                            } else {
                                $heritor_results[$i]['contracts_charged_renta_nat_plots'][$contractID][$plotID][$rentaType] = $rentaNat * $plotArea;
                            }
                        }
                    }
                }
            }

            $heritor_results[$i]['due_renta_nat'] = [];
            if (!empty($heritor_data[$ownerID]['contracts_renta_nat_plots'])) {
                $heritor_results[$i]['contracts_renta_nat_plots'] = $heritor_data[$ownerID]['contracts_renta_nat_plots'];
                foreach ($heritor_data[$ownerID]['contracts_renta_nat_plots'] as $contractID => $value) {
                    foreach ($value as $plotID => $plotIDRenta) {
                        foreach ($plotIDRenta as $rentaType => $rentaNat) {
                            $plotContractRentaNatHeritor = $heritor_data[$ownerID]['contracts_renta_nat_plots'][$contractID][$plotID][$rentaType];
                            $onlyContractChargedRentaNat = $heritor_results[$i]['contracts_charged_renta_nat_plots'][$contractID][$plotID][$rentaType];

                            if (empty($onlyContractChargedRentaNat)) {
                                $heritor_results[$i]['renta_nat'][$rentaType] += $plotContractRentaNatHeritor;
                            } else {
                                if (is_null($heritor_results[$i]['renta_nat'][$rentaType])) {
                                    $heritor_results[$i]['renta_nat'][$rentaType] = '0.000';
                                }
                            }
                            $heritor_results[$i]['due_renta_nat'][$rentaType] += $onlyContractChargedRentaNat ?? $plotContractRentaNatHeritor;
                        }
                    }
                }
            }

            if (!empty($heritor_results[$i]['renta_nat'])) {
                ksort($heritor_results[$i]['renta_nat']);
                foreach ($heritor_results[$i]['renta_nat'] as $rentaNatTypeID => $rentaNat) {
                    if (0 == $rentaNatTypeID) {
                        continue;
                    }

                    // Зануляваме, ако изплатената натура е по-голяма от дължимата натура по договор
                    if ('-' != $rentaNat) {
                        $rentaNat = $rentaNat > 0 ? $rentaNat : '0.000';
                        $rentaNat = number_format($rentaNat, 3, '.', '');
                    }

                    $heritor_results[$i]['renta_nat_text'] .= $rentaNat . '</br>';
                    $heritor_results[$i]['renta_nat'][$rentaNatTypeID] = $rentaNat;
                }
            }

            if (!empty($heritor_results[$i]['charged_renta_nat'])) {
                ksort($heritor_results[$i]['charged_renta_nat']);
                foreach ($heritor_results[$i]['charged_renta_nat'] as $chargedRentaNatTypeID => $chargedRentaNat) {
                    if (0 == $chargedRentaNatTypeID) {
                        continue;
                    }

                    // Зануляваме, ако изплатената натура е по-голяма от начислената натура по договор
                    $chargedRentaNat = $chargedRentaNat > 0 ? $chargedRentaNat : '0.000';
                    $heritor_results[$i]['charged_renta_nat'][$chargedRentaNatTypeID] = number_format($chargedRentaNat, 3, '.', '');
                }
            }

            if (!empty($heritor_results[$i]['renta_nat'])) {
                foreach ($heritor_results[$i]['renta_nat'] as $natTypeID => $rentaNat) {
                    $chargedRentaNat = $heritor_results[$i]['charged_renta_nat'][$natTypeID];
                    if ('-' != $chargedRentaNat && '' != $chargedRentaNat && $chargedRentaNat >= 0) {
                        $heritor_results[$i]['charged_renta_nat_text'] .= number_format($chargedRentaNat, 3, '.', '') . '</br>';
                    } else {
                        $heritor_results[$i]['charged_renta_nat_text'] .= '-</br>';
                    }
                }
            }

            if (!$heritor_results[$i]['renta_nat_text']) {
                $heritor_results[$i]['renta_nat_text'] = '-';
            } else {
                if (!empty($heritor_results[$i]['paid_renta_nat_details'])) {
                    foreach ($heritor_results[$i]['paid_renta_nat_details'] as $key => $value) {
                        if ('' != $key && 0 != $value) {
                            $this->total_heritors_paid_renta_nat[$key] += $value;
                        }
                    }
                }
            }

            if (!$heritor_results[$i]['charged_renta_nat_text']) {
                $heritor_results[$i]['charged_renta_nat_text'] = '-';
            }

            $heritor_results[$i]['paid_renta'] = '' != $heritor_results[$i]['paid_renta'] ? number_format($heritor_results[$i]['paid_renta'], 2, '.', '') : '0.00';

            // Общо платена рента в лева за наследник
            $this->total_heritors_paid_renta += $heritor_results[$i]['paid_renta'];

            if ($personTotalArea > 0) {
                $unpaid_renta = $heritor_results[$i]['due_renta'] - $heritor_results[$i]['paid_renta'];

                $heritor_results[$i]['unpaid_renta'] = ($unpaid_renta < 0) ? '0.00' : $unpaid_renta;
                $heritor_results[$i]['over_paid'] = ($unpaid_renta >= 0) ? '0.00' : $unpaid_renta * (-1);
            }

            if (!empty($heritor_results[$i]['renta_nat'])) {
                $unpaid_renta_nat = [];
                $over_paid_renta_nat = [];
                $total_unpaid_renta_nat = [];
                $unpaid_renta_nat_unit_value = [];
                $rentaTypes = [];

                foreach ($heritor_results[$i]['due_renta_nat'] as $rentaType => $rentaNatura) {
                    $paidRentaNatura = $heritor_results[$i]['paid_renta_nat_details'];
                    $rentaTypes[$rentaType] = $this->renta_types[$rentaType];

                    $unpaidRentaNatura = $rentaNatura - $paidRentaNatura[$rentaType];
                    $quantity = number_format(($unpaidRentaNatura < 0) ? '0.000' : $unpaidRentaNatura, 3, '.', '');
                    $quantityOverPaid = number_format(($unpaidRentaNatura >= 0) ? '0.000' : $unpaidRentaNatura * (-1), 3, '.', '');
                    $quantityValue = number_format($quantity * $this->renta_types_values[$rentaType], 2, '.', '');

                    $unpaid_renta_nat_unit_value[] = $quantityValue . ' лв.';
                    $unpaid_renta_nat[$rentaType] = $quantity;
                    $over_paid_renta_nat[$rentaType] = $quantityOverPaid;

                    // Сумиране на Остатък рента в натура на наследниците
                    if (!$heritor_results[$i]['is_dead']) {
                        $this->total_heritors_unpaid_renta_nat[$rentaType] += $quantity;
                    } else {
                        if (0 == $counterHeritors[0]['count']) {
                            $this->total_heritors_unpaid_renta_nat[$rentaType] += $quantity;
                        }
                    }

                    $this->total_heritors_over_paid_renta_nat[$rentaType] += $quantityOverPaid;
                }

                $heritor_results[$i]['unpaid_renta_nat_details'] = $unpaid_renta_nat;
                $heritor_results[$i]['unpaid_renta_nat'] = implode('</br>', $unpaid_renta_nat);
                $heritor_results[$i]['over_paid_nat'] = implode('</br>', $over_paid_renta_nat);
                $heritor_results[$i]['unpaid_renta_nat_unit_value'] = implode('</br>', $unpaid_renta_nat_unit_value);
                $heritor_results[$i]['renta_nat_type'] = implode('</br>', $rentaTypes);
            }

            $this->iterator++;
            $heritor_results[$i]['id'] = $this->iterator;

            // Сумиране на Надплатена рента в лева на наследниците
            $this->total_heritors_over_paid_renta += $heritor_results[$i]['over_paid'];

            // Запазване на всички данни за договор в лева
            if (!empty($heritor_data[$ownerID]['plots_contracts_renta_heritors'])) {
                foreach ($heritor_data[$ownerID]['plots_contracts_renta_heritors'] as $contractIDHer => $value) {
                    foreach ($value as $plotIDHer => $rentaHer) {
                        $heritor_results[$i]['plots_contracts_renta'][] = ['renta_by_plot' => $rentaHer, 'contract_id' => $contractIDHer, 'plot_gid' => $plotIDHer];
                    }
                }
            }

            // Запазване на всички данни за договор в натура
            $heritor_results[$i]['plots_contracts_renta_nat'] = $heritor_data[$ownerID]['plots_contracts_renta_nat_heritors'];

            // форматинае рента в натура
            if (!empty($heritor_results[$i]['renta_nat'])) {
                foreach ($heritor_results[$i]['renta_nat'] as $rentaType => $rentaNatura) {
                    if (!is_null($heritor_results[$i]['paid_renta_nat_details'][$rentaType])) {
                        $heritor_results[$i]['paid_renta_nat'] .= $heritor_results[$i]['paid_renta_nat_details'][$rentaType] . '</br>';
                    } else {
                        $heritor_results[$i]['paid_renta_nat'] .= '0.000</br>';
                    }
                }
            } else {
                $heritor_results[$i]['unpaid_renta_nat'] = '0.000';
            }

            if (empty($heritor_results[$i]['renta_nat']) && !empty($heritor_results[$i]['paid_renta_nat_details'])) {
                $heritor_results[$i]['paid_renta_nat'] = '';

                foreach ($heritor_results[$i]['paid_renta_nat_details'] as $rentaType => $rentaNatura) {
                    $heritor_results[$i]['paid_renta_nat'] .= $rentaNatura . ' X ' . $this->renta_types[$rentaType] . '</br>';
                }
            }

            if (is_null($results[$i]['paid_renta_nat']) || '' == $results[$i]['paid_renta_nat']) {
                $results[$i]['paid_renta_nat'] = '-';
            }

            if (empty($rentaTypes)) {
                $heritor_results[$i]['renta_nat_type'] = '[Без рента в натура]';
                $heritor_results[$i]['unpaid_renta_nat'] = '-';
                $heritor_results[$i]['over_paid_nat'] = '-';
                $heritor_results[$i]['unpaid_renta_nat_unit_value'] = '-';
            }

            $heritor_results[$i]['paid_renta_nat_text'] = $heritor_results[$i]['paid_renta_nat'];
            $heritor_results[$i]['unpaid_renta_nat_text'] = $heritor_results[$i]['unpaid_renta_nat'];
            $heritor_results[$i]['renta_types'] = $renta_results;

            // put new rows if reps are more than one
            $tmp_reps_array = explode(', ', $heritor_results[$i]['rep_names']);
            if (count($tmp_reps_array) > 1) {
                $heritor_results[$i]['rep_names'] = implode(', </br>', $tmp_reps_array);
            }

            // change parent upaid renta with child paid
            $ownerResults['unpaid_renta'] -= $heritor_results[$i]['paid_renta'];
            $ownerResults['unpaid_renta'] = number_format($ownerResults['unpaid_renta'], 2, '.', '');
            $ownerResults['unpaid_renta_orig'] -= $heritor_results[$i]['paid_renta'];
            if (!empty($rentaTypes)) {
                $ownerUnpaidRentaNatUnitValue = [];
                foreach ($ownerResults['unpaid_renta_nat_details'] as $rentaType => $rentaTypeValue) {
                    $ownerResults['unpaid_renta_nat_details'][$rentaType] -= $heritor_results[$i]['paid_renta_nat_details'][$rentaType] ? $heritor_results[$i]['paid_renta_nat_details'][$rentaType] : 0;

                    $ownerUnpaidRentaNatUnitValue[$rentaType] = ($ownerResults['unpaid_renta_nat_details'][$rentaType] * $this->renta_types_values[$rentaType]) . ' лв.';
                }
                $ownerResults['unpaid_renta_nat'] = implode('</br>', $ownerResults['unpaid_renta_nat_details']);
                $ownerResults['unpaid_renta_nat_unit_value'] = implode('</br>', $ownerUnpaidRentaNatUnitValue);
                $ownerResults['unpaid_renta_nat_text'] = implode('</br>', $ownerResults['unpaid_renta_nat_details']);
            }

            if ($heritor_results[$i]['is_dead']) {
                $heritor_results[$i]['children'] = $this->getOwnerHeritors(
                    $heritor_results[$i]['path'] . '.*{1}',
                    $level,
                    $root_id,
                    $heritor_percent_by_plots,
                    $contract_id,
                    $annex_id,
                    $year,
                    $renta_results,
                    $heritor_results[$i]
                );

                if ($heritor_results[$i]['dead_date']) {
                    $heritor_results[$i]['owner_names'] .= ' - починал на: ' . (new DateTime($heritor_results[$i]['dead_date']))->format('d.m.Y');
                }
                $heritor_results[$i]['iconCls'] = 'icon-tree-user-rip';

                if (0 == $counterHeritors[0]['count']) {
                    $this->total_heritors_unpaid_renta += $heritor_results[$i]['unpaid_renta'];
                }

                // ЛП: От Изплолзваната площ на родителя изваждаме площта за лично ползване на наследниците
                $heritor_results[$i]['renta'] = 0;
                $heritor_results[$i]['charged_renta'] = 0;
                $heritor_results[$i]['unpaid_renta'] = 0;
                $heritor_results[$i]['renta_nat'] = [];
                $heritor_results[$i]['renta_nat_text'] = '';
                $heritor_results[$i]['charged_renta_nat'] = [];
                $heritor_results[$i]['charged_renta_nat_text'] = '';

                foreach ($heritor_results[$i]['children'] as $child) {
                    $heritor_results[$i]['owner_area'] -= $child['pu_area'];
                    $heritor_results[$i]['children_pu_area'] += $child['pu_area'];
                    $heritor_results[$i]['unpaid_renta'] += $child['unpaid_renta'];

                    // Изчисляване рентата на родителя, като сумираме рентите на наследниците.
                    // Това се налага, защото наследниците може да имат лично ползване и общата рента да е по-малка от тази на родителя
                    $heritor_results[$i]['renta'] += $child['renta'];
                    $heritor_results[$i]['charged_renta'] += $child['charged_renta'];

                    if (!empty($child['renta_nat'])) {
                        foreach ($child['renta_nat'] as $rentaType => $rentaNat) {
                            if (isset($heritor_results[$i]['renta_nat'][$rentaType])) {
                                $heritor_results[$i]['renta_nat'][$rentaType] += $rentaNat;
                            } else {
                                $heritor_results[$i]['renta_nat'][$rentaType] = $rentaNat;
                            }
                        }
                    }

                    if (!empty($child['paid_renta_nat_details'])) {
                        foreach ($child['paid_renta_nat_details'] as $rentaType => $rentaNat) {
                            if (isset($heritor_results[$i]['children_paid_renta_nat_details'][$rentaType])) {
                                $heritor_results[$i]['children_paid_renta_nat_details'][$rentaType] += $rentaNat;
                            } else {
                                $heritor_results[$i]['children_paid_renta_nat_details'][$rentaType] = $rentaNat;
                            }
                        }
                    }

                    if (!empty($child['charged_renta_nat'])) {
                        foreach ($child['charged_renta_nat'] as $rentaType => $rentaNat) {
                            if (isset($heritor_results[$i]['charged_renta_nat'][$rentaType])) {
                                $heritor_results[$i]['charged_renta_nat'][$rentaType] += $rentaNat;
                            } else {
                                $heritor_results[$i]['charged_renta_nat'][$rentaType] = $rentaNat;
                            }
                        }
                    }
                }

                if (!empty($heritor_results[$i]['renta_nat'])) {
                    foreach ($heritor_results[$i]['renta_nat'] as $rentaType => $rentaNat) {
                        $heritor_results[$i]['renta_nat_text'] .= number_format($rentaNat, 3) . '</br>';
                    }
                }

                if (!empty($heritor_results[$i]['charged_renta_nat'])) {
                    foreach ($heritor_results[$i]['charged_renta_nat'] as $rentaType => $rentaNat) {
                        $heritor_results[$i]['charged_renta_nat_text'] .= number_format($rentaNat, 3) . '</br>';
                    }
                }
            } else {
                $heritor_results[$i]['iconCls'] = 'icon-tree-user';
                $this->total_heritors_unpaid_renta += $heritor_results[$i]['unpaid_renta'];
            }

            $heritor_results[$i]['renta'] = number_format($heritor_results[$i]['renta'], 2, '.', '');
            $heritor_results[$i]['charged_renta'] = number_format($heritor_results[$i]['charged_renta'], 2, '.', '');
            $heritor_results[$i]['unpaid_renta'] = number_format($heritor_results[$i]['unpaid_renta'], 2, '.', '');
            $heritor_results[$i]['owner_area'] = number_format($heritor_results[$i]['owner_area'], 3, '.', '');

            $heritor_results[$i]['unpaid_renta_orig'] = $heritor_results[$i]['unpaid_renta'] ? $heritor_results[$i]['unpaid_renta'] : 0;
            $heritor_results[$i]['unpaid_renta'] = number_format($heritor_results[$i]['unpaid_renta'], 2, '.', '');
            $heritor_results[$i]['over_paid'] = number_format($heritor_results[$i]['over_paid'], 2, '.', '');
            $heritor_results[$i]['is_heritor'] = true;
            $heritor_results[$i]['level'] = $level;

            $personalUseOptions = [
                'contract_id' => $annex_id ? $annex_id : $contract_id,
                'year' => $year,
                'chosen_years' => $year,
            ];
            $personalUse = $UserDbPaymentsController->getPersonalUseForOwners($personalUseOptions, false, false);
            $UserDbPaymentsController->getOwnerPersonalUseInfo($personalUse, $heritor_results[$i]);
        }

        return $heritor_results;
    }

    private function addNewRentaPayrolls($rentas = [], $owner = [], $totalPaidRenta = false)
    {
        $natural_renta = [];
        $paid_renta = 0;

        foreach ($rentas as $rentaData) {
            if ($owner['owner_id'] == $rentaData['owner_id'] || $totalPaidRenta) {
                if (1 == $rentaData['paid_from']) {
                    $paid_renta += $rentaData['trans_amount'];
                } else {
                    if (isset($natural_renta[$rentaData['nat_type']])) {
                        $natural_renta[$rentaData['nat_type']] += $rentaData['trans_amount_nat'];
                    } else {
                        $natural_renta[$rentaData['nat_type']] = $rentaData['trans_amount_nat'];
                    }
                }
            }
        }

        foreach ($natural_renta as $nat_rent_key => $nat_renta) {
            if ('NULL' != $nat_rent_key) {
                $owner['paid_renta_nat_details'][$nat_rent_key] += $nat_renta;
                $owner['paid_renta_nat_details'][$nat_rent_key] = number_format($owner['paid_renta_nat_details'][$nat_rent_key], 3, '.', '');
            }
        }

        $owner['paid_renta'] = $paid_renta;

        return $owner;
    }

    private function arraySumIdenticalKeys()
    {
        $arrays = func_get_args();
        $keys = array_keys(array_reduce($arrays, function ($keys, $arr) { return $keys + $arr; }, []));
        $sums = [];

        foreach ($keys as $key) {
            $sums[$key] = array_reduce(
                $arrays,
                function ($sum, $arr) use ($key) {
                    $quantity = number_format($sum + @$arr[$key], 3, '.', '');
                    if ('0.000' != $quantity) {
                        return $quantity . ' X ' . $this->renta_types[$key];
                    }
                }
            );
        }

        return $sums;
    }
}
