<?php

namespace TF\Engine\APIClasses\Payments;

use Exception;
use mikeha<PERSON>l\pdftk\Pdf;
use TF\Engine\APIClasses\Exports\PaymentExports;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Kernel\PrintPdf;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbCollections\UserDbCollectionsController;
use TF\Engine\Plugins\Core\UserDbContracts\UserDbContractsController;
use TF\Engine\Plugins\Core\UserDbPayments\UserDbPaymentsController;

/**
 * Export Payment.
 *
 * @rpc-module Payments
 *
 * @rpc-service-id export-payment
 */
class ExportPayment extends PaymentExports
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'exportToPdfPaymentOrder' => ['method' => [$this, 'exportToPdfPaymentOrder']],
            'exportToPdfBankPaymentOrder' => ['method' => [$this, 'exportToPdfBankPaymentOrder']],
            'exportToPdfPostPaymentOrder' => ['method' => [$this, 'exportToPdfPostPaymentOrder']],
            'exportToPdfWeighingNote' => ['method' => [$this, 'exportToPdfWeighingNote']],
            'exportToPdfCombined' => ['method' => [$this, 'exportCombinedPaymentDocs']],
            'exportToPdfBankCombined' => ['method' => [$this, 'exportCombinedBankPaymentDocs']],
            'getTransactionPaymentNumbers' => ['method' => [$this, 'getTransactionPaymentNumbers']],
            'getNewTransactionRkoNumbers' => ['method' => [$this, 'getNewTransactionRkoNumbers']],
            'updatePaymentRkoNumbers' => ['method' => [$this, 'updatePaymentRkoNumbers']],
        ];
    }

    /**
     * Export payment order to pdf.
     *
     * @api-method exportToPdfPaymentOrder
     *
     * @param string $transactionId -Transaction id
     * @param string $paymentId -Default null cause it only use by export in transactions
     * @param bool $transactionDate -Insert date in document or not
     * @param int $paymentSubject -The id of the payment subject template
     * @param string $paymentSubjectText -payment subject text instead of id
     * @param bool $withoutRkoNumbering -Insert rko number or not
     * @param bool $returnAsText -Returns HTML for export
     *
     * @return array|string -Path to the file as array or data as a string
     */
    public function exportToPdfPaymentOrder(
        $transactionId,
        $paymentId = null,
        $transactionDate = null,
        $paymentSubject = null,
        $paymentSubjectText = null,
        $withoutRkoNumbering = false,
        $returnAsText = false,
        $collectionIds = [],
        $combineDocument = false,
        $collectАllPaymentАmounts,
        $paymentDocumentType,
        $paymentReceiptDeclaration,
        $paymentsParams
    ) {
        // check if transaction id exist
        if (!$transactionId) {
            return [];
        }
        $userDbContractsController = new UserDbContractsController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);
        $FarmingController = new FarmingController('Farming');
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);

        $transactionIdArr = explode(',', $transactionId);

        $repQuery = "SELECT string_agg(rep_name || ' ' || rep_surname || ' ' || rep_lastname, ', ') as rep_names FROM {$UserDbController->DbHandler->tableOwnersReps} ir
                    WHERE ir.id IN
                        (SELECT rep_id FROM {$UserDbController->DbHandler->plotsOwnersRelTable} ipo
                            WHERE ipo.owner_id = o.id
                            AND ipo.pc_rel_id IN
                                (SELECT DISTINCT(id) FROM {$UserDbController->DbHandler->contractsPlotsRelTable}
                                WHERE contract_id IN (c.id)))";

        $options = [
            'tablename' => $UserDbController->DbHandler->tablePayments,
            'return' => [
                'max(ekate.ekattes) as ekattes', 'p.contract_id',
                "(CASE WHEN owner_type = 1 THEN name || ' ' || surname || ' ' || lastname ELSE company_name END) as owner_names",
                '(CASE WHEN owner_type = 1 THEN egn ELSE eik END) as owner_egn', 'max(recipient) as recipient', 'max(recipient_egn) as recipient_egn', 'max(recipient_proxy) as recipient_proxy', 'max(recipient_address) as recipient_address', 'max(recipient_lk) as recipient_lk',
                "({$repQuery}) as rep_names",
                'coalesce(sum(pn.amount * pn.unit_value), sum(p.amount)) as amount',
                'sum(pn.amount * pn.unit_value) as amount_nat', 'p.paid_in', 'p.paid_from', 'p.rko_number',
                'o.*', 'c.c_date', 'c.c_num', 'c.farming_id',
                'p.owner_id', 'p.contract_id', 'p.transaction_id',
                'p.date', 'p.farming_year', 'p.path', 'p.is_heritor',
                'p.payment_meta_data',
            ],
            'where' => [
                'transaction' => ['column' => 'transaction_id', 'compare' => 'IN', 'value' => $transactionIdArr],
                'payment_id' => ['column' => 'id', 'compare' => '=', 'prefix' => 'p', 'value' => $paymentId],
            ],
            'group' => 'o.id, c.id, p.owner_id, p.contract_id, p.transaction_id, p.date, p.farming_year, p.path, p.is_heritor,
                        p.paid_in, p.paid_from, p.rko_number,p.payment_meta_data',
            'join_ekattes' => true,
        ];

        $results = $UserDbPaymentsController->getPaymentsByParams($options, false, false);

        if (0 == count($results)) {
            return [];
        }

        if ($collectАllPaymentАmounts) {
            $collectedOwnerPaymentAmounts = $UserDbPaymentsController->getAllOwnerPaymentsAmountByTransactionId($transactionId);
        }

        $options = [
            'return' => ['*'],
            'where' => [
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'value' => $this->User->GroupID],
            ],
            'sort' => 'id',
            'order' => 'ASC',
        ];

        $farming_results = $FarmingController->getFarmings($options, false, false);
        $farmingCount = count($farming_results);
        $finalFarmings = [];

        for ($i = 0; $i < $farmingCount; $i++) {
            $finalFarmings[$farming_results[$i]['id']]['name'] = $farming_results[$i]['name'];
            $finalFarmings[$farming_results[$i]['id']]['bulstat'] = $farming_results[$i]['bulstat'];
            $finalFarmings[$farming_results[$i]['id']]['company'] = $farming_results[$i]['company'];
        }

        $pages = [];

        if (true == $combineDocument) {
            $combinatedAmount = $results;
            if ($collectedOwnerPaymentAmounts) {
                $combinatedAmount = $collectedOwnerPaymentAmounts;
            }

            $payments = current($results);
            $payments['amount_nat'] = array_sum(array_column($results, 'amount_nat'));
            $payments['amount'] = array_sum(array_column($combinatedAmount, 'amount'));
            $payments['ekattes'] = implode(', ', array_column($results, 'ekattes'));
            $payments['c_num'] = implode(', ', array_column($results, 'c_num'));
            $payments['contract_ids'] = array_column($results, 'contract_id');
            $payments['payment_meta_data'] = array_column($results, 'payment_meta_data');

            $payments = [$payments];
        } else {
            $payments = $results;
        }

        if ($paymentsParams) {
            $paymentsParams = json_decode($paymentsParams, true);
        }

        foreach ($payments as $key => $result) {
            $printData = [];
            $isOwnerRecipient = $UserDbPaymentsController->checkIfOwnerIsRecipientRko($result);
            $paymentSubject = (int) $paymentSubject;

            if ($paymentSubject > 0) {
                $template = $paymentSubject ? $UserDbPaymentsController->getPaymentSubject($paymentSubject) : null;
                $farmYear = $GLOBALS['Farming']['years'][$result['farming_year']]['farming_year'];
            } elseif (0 === $paymentSubject) {
                $template = null;
                if (null != $paymentSubjectText) {
                    $template = $paymentSubjectText;
                }
            } else {
                $template = null;
            }

            $template = str_replace('[[zemlishte]]', $result['ekattes'], $template);
            $template = str_replace('[[nomer_na_dogovor]]', $result['c_num'], $template);
            $template = str_replace('[[stopanska_godina]]', $farmYear, $template);

            if (strpos($template, '[[plosht]]') || in_array($paymentDocumentType, [$GLOBALS['Payments']['RKO_TYPE']['RKO_CARD']])) {
                if ($combineDocument) {
                    if (null !== $result['payment_meta_data']) {
                        $result['area'] = $this->calculateAreasFromMetaData($result['payment_meta_data']);
                    } else {
                        $result['area'] = $this->calculateAreasFromContracts($result['contract_ids'], $result['farming_year'], $result['owner_id'], $result['path'], $userDbContractsController);
                    }
                } else {
                    if (null !== $result['payment_meta_data']) {
                        $paymentMetaData = json_decode($result['payment_meta_data'], true);
                        $result['area'] = $paymentMetaData['payment_area'];
                    } else {
                        $result['area'] = $this->findArea($result['contract_id'], $result['farming_year'], $result['owner_id'], $result['path'], $userDbContractsController);
                    }
                }

                $template = str_replace('[[plosht]]', $result['area'], $template);
            }

            if (in_array($paymentDocumentType, [$GLOBALS['Payments']['RKO_TYPE']['RKO_CARD']])) {
                if ($combineDocument) {
                    $result['contract_area'] = $this->calculateAreasFromContracts($result['contract_ids'], $result['farming_year'], $result['owner_id'], $result['path'], $userDbContractsController, 'contract_area');
                } else {
                    $result['contract_area'] = $this->findArea($result['contract_id'], $result['farming_year'], $result['owner_id'], $result['path'], $userDbContractsController, 'contract_area');
                }
            }

            // Ако се изплаща натура в лева
            if (1 == $result['paid_in'] && 2 == $result['paid_from']) {
                $amount = number_format($result['amount_nat'], 2, '.', '');
            } else {
                $amount = number_format($result['amount'], 2, '.', '');
                if ($collectedOwnerPaymentAmounts && !$combineDocument) {
                    foreach ($collectedOwnerPaymentAmounts as $collectedOwnerPaymentAmountsRecord) {
                        $ownerKey = $result['owner_id'] . $result['contract_id'] . $result['farming_year'];
                        if ($collectedOwnerPaymentAmountsRecord['owner_key'] == $ownerKey) {
                            $amount = number_format($collectedOwnerPaymentAmountsRecord['amount'], 2, '.', '');
                        }
                    }
                }
            }
            $amount_abs = abs($amount);
            $amountEuro = convertBGNtoEURO($amount_abs);
            sscanf($amount_abs, '%d.%d', $whole, $fraction);
            sscanf($amountEuro, '%d.%d', $wholeEuro, $fractionEuro);

            $printData['company'] = $finalFarmings[$result['farming_id']]['company'];
            $printData['bulstat'] = $finalFarmings[$result['farming_id']]['bulstat'];
            $printData['name'] = $isOwnerRecipient ? $result['owner_names'] : $result['recipient'];
            $printData['egn'] = $isOwnerRecipient ? $result['owner_egn'] : $result['recipient_egn'];
            $printData['ekattes'] = $result['ekattes'];
            $printData['c_num'] = $result['c_num'];
            $printData['farm_year'] = $farmYear;
            $printData['area'] = $result['area'];
            $printData['contract_area'] = $result['contract_area'];

            if ($transactionDate) {
                if (is_string($transactionDate)) {
                    $printData['date'] = date('d.m.Y', strtotime($transactionDate)) . 'г.';
                } else {
                    $printData['date'] = date('d.m.Y', strtotime($result['date'])) . 'г.';
                }
            } else {
                $printData['date'] = '';
            }

            if ($result['recipient'] && $result['recipient'] != $result['owner_names']) {
                $printData['recipient'] = $result['owner_names'];
            }

            if ('' == $result['address'] && '' != $result['recipient_address']) {
                $result['address'] = $result['recipient_address'];
            }
            if ('' == $result['lk_nomer'] && '' == $result['lk_izdavane'] && '' != $result['recipient_lk']) {
                $lk_nomer = $result['recipient_lk'];
            } else {
                $lk_nomer = (!empty($result['lk_nomer']) ? $result['lk_nomer'] . ' , ' : '') . $result['lk_izdavane'];
            }
            $printData['proxy'] = $isOwnerRecipient ? '' : $result['recipient_proxy'];
            $printData['address'] = $isOwnerRecipient ? $result['address'] : $result['recipient_address'];
            $printData['lk_izdavane'] = $isOwnerRecipient ? $lk_nomer : $result['recipient_lk'];

            $printData['rko_number'] = $withoutRkoNumbering ? '' : $result['rko_number'];
            $printData['for'] = mb_strlen($template, 'utf-8') > 235 ? (mb_substr($template, 0, 235, 'utf-8') . '...') : $template;
            $printData['price'] = BGNtoEURO($amount);

            $printData['price_text'] = '';
            if ($amount < 0) {
                $printData['price_text'] = 'минус ';
            }

            $printData['price_text'] .= $FarmingController->StringHelper->numToString($whole) . ' лева и ' . number_format(($fraction = ($amount_abs - $whole) * 100), 0) . ' ст. ';
            $printData['price_text'] .= ' / ';
            $printData['price_text'] .= str_replace('един ', 'едно ', $FarmingController->StringHelper->numToString($wholeEuro)) . ' евро и ' . number_format(($fractionEuro = ($amountEuro - $wholeEuro) * 100), 0) . ' цента.';

            $documentOrientation = 'Landscape';

            switch ($paymentDocumentType) {
                case $GLOBALS['Payments']['RKO_TYPE']['RKO_RECEIPT']:
                    $documentOrientation = 'Portrait';

                    $ltext = $FarmingController->StringHelper->loadTemplate($GLOBALS['Templates'][50]['template'], $printData);

                    if ($paymentReceiptDeclaration) {
                        $ltext .= $FarmingController->StringHelper->loadTemplate($GLOBALS['Templates'][52]['template'], $printData);
                    }

                    $ltext .= '<div style="border-bottom:1px dotted #4d4c4c; height:25px; margin-bottom: 35px; clear: both;"></div>' . $ltext;

                    break;
                case $GLOBALS['Payments']['RKO_TYPE']['RKO_CARD']:
                    $documentOrientation = 'Portrait';

                    if (isset($paymentsParams[$result['contract_id']][$result['owner_id']]['unpaid_renta'])) {
                        $ownerPayment = $paymentsParams[$result['contract_id']][$result['owner_id']];
                    } else {
                        $path = $result['path'] ?? $result['owner_id'];
                        $ownerPayment = $paymentsParams[$result['contract_id']][$result['owner_id']][$path];
                    }

                    $printData['unpaid_renta'] = BGNtoEURO($ownerPayment['unpaid_renta']);
                    $printData['personal_use_unpaid_treatments_sum'] = array_sum($ownerPayment['personal_use_unpaid_treatments_arr']);

                    $printData['personal_use_nat_types_names'] = implode('', array_map(function ($item) {
                        return "<p>{$item}</p>";
                    }, $ownerPayment['personal_use_nat_types_names_arr']));

                    $printData['personal_use_unpaid_treatments'] = implode('', array_map(function ($item) {
                        return '<p>' . BGNtoEURO($item) . '</p>';
                    }, $ownerPayment['personal_use_unpaid_treatments_arr']));

                    $printData['renta_nat_type'] = implode('', array_map(function ($item) {
                        return "<p>{$item}</p>";
                    }, $ownerPayment['renta_nat_type_arr']));

                    $printData['unpaid_renta_nat'] = implode('', array_map(function ($item) {
                        return "<p>{$item}</p>";
                    }, $ownerPayment['unpaid_renta_nat_arr']));
                    $paidRentaBy = array_filter($ownerPayment['paid_renta_by_arr'], function ($item) {
                        return !empty($item) && '-' !== trim($item);
                    });
                    $printData['paid_renta_by'] = implode('', array_map(function ($item) {
                        $item = str_replace('лв.', '', $item);

                        return '<p>' . BGNtoEURO((float)$item) . '</p>';
                    }, $paidRentaBy));
                    $printData['paid_renta_by_rows'] = implode('', array_map(function ($item) {
                        return '<p>&nbsp;</p>';
                    }, $paidRentaBy));

                    $paidRentaNatBy = array_filter($ownerPayment['paid_renta_nat_by_arr'], function ($item) {
                        return !empty($item) && '-' !== trim($item);
                    });
                    $printData['paid_renta_nat_by'] = implode('', array_map(function ($item) {
                        return "<p>{$item}</p>";
                    }, $paidRentaNatBy));
                    $printData['paid_renta_nat_by_rows'] = implode('', array_map(function ($item) {
                        return '<p>&nbsp;</p>';
                    }, $paidRentaNatBy));

                    $totalUnpaidRenta = $ownerPayment['unpaid_renta'] - ($printData['personal_use_unpaid_treatments_sum'] ?? 0);
                    $printData['total_unpaid_renta'] = $totalUnpaidRenta > 0 ? BGNtoEURO($totalUnpaidRenta) : '-';

                    $printData['pu_area'] = $ownerPayment['pu_area'];

                    $ltext = $FarmingController->StringHelper->loadTemplate($GLOBALS['Templates'][51]['template'], $printData);

                    $ltext .= '<div style="clear: both;page-break-after: always;"></div>' . $ltext;

                    break;
                default:
                    $ltext = $FarmingController->StringHelper->loadTemplate($GLOBALS['Templates'][4]['template'], $printData);

                    break;
            }

            if (count($payments) > 1 && $key != count($payments) - 1) {
                $ltext .= '<div style="clear: both;page-break-after: always;"></div>';
            }

            $pages[] = $ltext;
        }

        $date = date('Y-m-d-H-i-s');
        $name = 'platejno_' . $transactionId . '_' . $date . '.pdf';
        $userExportPath = PUBLIC_UPLOAD_EXPORT . DIRECTORY_SEPARATOR . $this->User->UserID;

        if (!is_dir($userExportPath)) {
            mkdir($userExportPath);
        }

        $pdfExpPath = $userExportPath . DIRECTORY_SEPARATOR . $name;
        $pdfDlPath = PUBLIC_UPLOAD_RELATIVE_PATH . DIRECTORY_SEPARATOR . 'export'
            . DIRECTORY_SEPARATOR . $this->User->UserID . DIRECTORY_SEPARATOR . $name;

        $finaltext = '';
        foreach ($pages as $page) {
            $finaltext .= '<page style="font-family: freeserif" format="A4">' . $page . '</page>';
        }

        if (!empty($collectionIds)) {
            $collectionsController = new UserDbCollectionsController($this->User->Database);
            $finaltext .= '<div style="clear: both;page-break-after: always;"></div>';
            $finaltext .= $collectionsController->exportCollectionsHtml($collectionIds, $transactionDate);
        }

        if ($returnAsText) {
            return $finaltext;
        }

        $printPdf = new PrintPdf();
        $printPdf->generateFromHtml($finaltext, $pdfExpPath, ['orientation' => $documentOrientation], true);

        return ['file_path' => $pdfDlPath, 'file_name' => $name];
    }

    public function exportToPdfPostPaymentOrder($transactionId, $transactionDate = true, $paymentSender = null, $paymentRecipient = null, $returnAsText = false)
    {
        // check if transaction id exist
        if (!$transactionId) {
            return [];
        }
        $userDbContractsController = new UserDbContractsController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);
        $FarmingController = new FarmingController('Farming');
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);

        $transactionIdArr = explode(',', $transactionId);

        $repQuery = "SELECT string_agg(rep_name || ' ' || rep_surname || ' ' || rep_lastname, ', ') as rep_names FROM {$UserDbController->DbHandler->tableOwnersReps} ir
                    WHERE ir.id IN
                        (SELECT rep_id FROM {$UserDbController->DbHandler->plotsOwnersRelTable} ipo
                            WHERE ipo.owner_id = o.id
                            AND ipo.pc_rel_id IN
                                (SELECT DISTINCT(id) FROM {$UserDbController->DbHandler->contractsPlotsRelTable}
                                WHERE contract_id IN (c.id)))";

        $options = [
            'tablename' => $UserDbController->DbHandler->tablePayments,
            'return' => [
                'max(ekate.ekattes) as ekattes',
                'p.contract_id',
                "(CASE WHEN owner_type = 1 THEN name || ' ' || surname || ' ' || lastname ELSE company_name END) as owner_names",
                '(CASE WHEN owner_type = 1 THEN egn ELSE eik END) as owner_egn', 'max(recipient) as recipient', 'max(recipient_egn) as recipient_egn', 'max(recipient_proxy) as recipient_proxy', 'max(recipient_address) as recipient_address', 'max(recipient_lk) as recipient_lk',
                "({$repQuery}) as rep_names", 'p.amount as amount', 'sum(pn.amount * pn.unit_value) as amount_nat', 'p.paid_in', 'p.paid_from', 'p.rko_number',
                'o.*', 'c.c_date', 'c.c_num', 'c.farming_id',
                'p.owner_id', 'p.contract_id', 'p.transaction_id',
                'p.date', 'p.farming_year', 'p.path', 'p.is_heritor',
            ],
            'where' => [
                'transaction' => ['column' => 'transaction_id', 'compare' => 'IN', 'value' => $transactionIdArr],
            ],
            'group' => 'o.id, c.id, p.owner_id, p.contract_id, p.transaction_id, p.date, p.farming_year, p.path, p.is_heritor,
                             p.amount, p.paid_in, p.paid_from, p.rko_number, p.id',
            'join_ekattes' => true,
        ];

        $results = $UserDbPaymentsController->getPaymentsByParams($options, false, false);

        if (0 == count($results)) {
            return [];
        }

        $result = reset($results);

        $options = [
            'return' => ['*'],
            'where' => [
                'farming_id' => ['column' => 'id', 'compare' => '=', 'value' => $result['farming_id']],
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'value' => $this->User->GroupID],
            ],
            'sort' => 'id',
            'order' => 'ASC',
        ];

        $farming = reset($FarmingController->getFarmings($options, false, false));

        // Ако се изплаща натура в лева
        if (1 == $result['paid_in'] && 2 == $result['paid_from']) {
            $amount = array_sum(array_column($results, 'amount_nat'));
            $amount = number_format($amount, 2, '.', '');
        } else {
            $amount = array_sum(array_column($results, 'amount'));
            $amount = number_format($amount, 2, '.', '');
        }
        $amount_abs = abs($amount);
        sscanf($amount_abs, '%d.%d', $whole, $fraction);

        $date = date('Y-m-d-H-i-s');
        $name = 'post_payment_702_' . $transactionId . '_' . $date . '.pdf';
        $userExportPath = PUBLIC_UPLOAD_EXPORT . DIRECTORY_SEPARATOR . $this->User->UserID;

        if (!is_dir($userExportPath)) {
            mkdir($userExportPath);
        }

        $pdfExpPath = $userExportPath . DIRECTORY_SEPARATOR . $name;
        $path = TEMPLATE_PATH . 'pdf/post_payment_702.pdf';

        // recipient_street_bank
        $pdf = new Pdf($path);

        $recipientPostalCode = str_split($paymentRecipient['owner-post-code'], 1);
        $senderPostalCode = str_split($paymentSender['sender-post-code'], 1);

        [$amountLeva, $amountSt] = explode('.', $amount);
        $amountLeva = array_pad(str_split($amountLeva, 1), -4, '');
        $amountSt = str_split($amountSt, 1);
        $amountText = $FarmingController->StringHelper->numToString($whole) . ' лева и ' . number_format(($fraction = ($amount_abs - $whole) * 100), 0) . ' ст.';

        $ownerPhone = $result['phone'];

        $filled = $pdf->fillForm([
            'recipient_money_leva_1' => $amountLeva[0],
            'recipient_money_leva_2' => $amountLeva[1],
            'recipient_money_leva_3' => $amountLeva[2],
            'recipient_money_leva_4' => $amountLeva[3],
            'recipient_money_st_1' => $amountSt[0],
            'recipient_money_st_2' => $amountSt[1],
            'recipient_money_text' => $amountText,

            'recipient_postal_code_1' => $recipientPostalCode[0],
            'recipient_postal_code_2' => $recipientPostalCode[1],
            'recipient_postal_code_3' => $recipientPostalCode[2],
            'recipient_postal_code_4' => $recipientPostalCode[3],
            'recipient_city' => $paymentRecipient['owner-city'],
            'recipient_region_YA1Z' => $paymentRecipient['owner-region'],
            'recipient_street_bank' => $paymentRecipient['owner-street'],
            'recipient_building' => $paymentRecipient['owner-building'],
            'recipient_entrance' => $paymentRecipient['owner-entrance'],
            'recipient_floor' => $paymentRecipient['owner-floor'],
            'recipient_apartment' => $paymentRecipient['owner-appartment'],
            'recipient_name' => $paymentRecipient['name'],
            'recipient_iban_SF2D' => $paymentRecipient['iban'],
            'recipient_phone_F3DY' => $ownerPhone,

            'sen_postal_code_1' => $senderPostalCode[0],
            'sen_postal_code_2' => $senderPostalCode[1],
            'sen_postal_code_3' => $senderPostalCode[2],
            'sen_postal_code_4' => $senderPostalCode[3],
            'sen_city' => $paymentSender['sender-city'],
            'sen_region' => $paymentSender['sender-region'],
            'sen_street' => $paymentSender['sender-street'],
            'sen_building' => $paymentSender['sender-building'],
            'sen_entrance' => $paymentSender['sender-entrance'],
            'sen_floor' => $paymentSender['sender-floor'],
            'sen_appartment' => $paymentSender['sender-appartment'],
            'sen_name' => $farming['company'] . '  ' . $farming['mol_egn'],
            'sen_phone' => $farming['farming_mol_phone'],

            'rec_money_leva_1' => $amountLeva[0],
            'rec_money_leva_2' => $amountLeva[1],
            'rec_money_leva_3' => $amountLeva[2],
            'rec_money_leva_4' => $amountLeva[3],
            'rec_money_st_1' => $amountSt[0],
            'rec_money_st_2' => $amountSt[1],
            'rec_postal_code_1' => $recipientPostalCode[0],
            'rec_postal_code_2' => $recipientPostalCode[1],
            'rec_postal_code_3' => $recipientPostalCode[2],
            'rec_postal_code_4' => $recipientPostalCode[3],
            'rec_city' => $paymentRecipient['owner-city'],
            'rec_region' => $paymentRecipient['owner-region'],
            'rec_street_bank' => $paymentRecipient['owner-street'],
            'rec_building' => $paymentRecipient['owner-building'],
            'rec_entrance' => $paymentRecipient['owner-entrance'],
            'rec_floor' => $paymentRecipient['owner-floor'],
            'rec_appartment' => $paymentRecipient['owner-appartment'],
            'rec_name' => $paymentRecipient['name'],
            'rec_phone' => $ownerPhone,

            'sender_postal_code_1' => $senderPostalCode[0],
            'sender_postal_code_2' => $senderPostalCode[1],
            'sender_postal_code_3' => $senderPostalCode[2],
            'sender_postal_code_4' => $senderPostalCode[3],
            'sender_city' => $paymentSender['sender-city'],
            'sender_region' => $paymentSender['sender-region'],
            'sender_street' => $paymentSender['sender-street'],
            'sender_building' => $paymentSender['sender-building'],
            'sender_entrance' => $paymentSender['sender-entrance'],
            'sender_floor' => $paymentSender['sender-floor'],
            'sender_appartment' => $paymentSender['sender-appartment'],
            'sender_name' => $farming['company'],
            'sender_phone' => $farming['farming_mol_phone'],
        ])
            ->needAppearances()
            ->saveAs($pdfExpPath);

        // Always check for errors
        if (false === $filled) {
            $error = $pdf->getError();
        }

        $pdfDlPath = PUBLIC_UPLOAD_RELATIVE_PATH . DIRECTORY_SEPARATOR . 'export' . DIRECTORY_SEPARATOR . $this->User->UserID . DIRECTORY_SEPARATOR . $name;

        return ['file_path' => $pdfDlPath, 'file_name' => $name];
    }

    /**
     * Export payment order with added iban to pdf.
     *
     * @api-method exportToPdfBankPaymentOrder
     *
     * @param string $transactionId -Transaction id
     * @param bool $transactionDate -Insert date in document or not
     * @param string $bank -Orderer bank info
     * @param int $paymentSubject -The id of the payment subject template
     * @param string $paymentSubjectText -payment subject text instead of id
     * @param bool $returnAsText -Returns HTML for export
     *
     * @return array|string pdfPath           -Path to the file to pdf export
     */
    public function exportToPdfBankPaymentOrder($transactionId, $transactionDate = true, $bank = '', $paymentSubject = null, $paymentSubjectText = null, $returnAsText = false, $combineDocument = false)
    {
        // check if transaction id exist
        if (!$transactionId) {
            return [];
        }

        $userDbContractsController = new UserDbContractsController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);
        $FarmingController = new FarmingController('Farming');
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);

        $transactionIdArr = explode(',', $transactionId);

        $options = [
            'tablename' => $UserDbController->DbHandler->tablePayments,
            'return' => [
                'max(ekate.ekattes) as ekattes',
                "(CASE WHEN owner_type = 1 THEN name || ' ' || surname || ' ' || lastname ELSE company_name END) as owner_names",
                '(CASE WHEN owner_type = 1 THEN egn ELSE eik END) as owner_egn', 'max(recipient) as recipient', 'max(recipient_egn) as recipient_egn', 'max(recipient_proxy) as recipient_proxy',
                'p.amount as amount', 't.bank_acc', 'sum(pn.amount * pn.unit_value) as amount_nat', 'p.paid_in', 'p.paid_from',
                'o.*', 'c.c_date', 'c.c_num', 'c.farming_id',
                'p.owner_id', 'p.contract_id', 'p.transaction_id',
                'p.date', 'p.farming_year', 'p.path', 'p.is_heritor',
            ],
            'where' => [
                'transaction' => ['column' => 'transaction_id', 'compare' => 'IN', 'value' => $transactionIdArr],
                'payment_id' => ['column' => 'id', 'compare' => '=', 'prefix' => 'p', 'value' => $paymentId],
            ],
            'group' => 'o.id, c.id, p.owner_id, p.contract_id, p.transaction_id, p.date, p.farming_year, p.path, p.is_heritor, t.bank_acc,
                     p.amount, p.paid_in, p.paid_from, pn.id',
            'join_ekattes' => true,
        ];

        $results = $UserDbPaymentsController->getPaymentsByParams($options, false, false);

        if (0 == count($results)) {
            return [];
        }

        $amount = 0;

        $pages = [];

        $farmingOptions = [
            'return' => ['*'],
            'where' => [
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'value' => $this->User->GroupID],
            ],
            'sort' => 'id',
            'order' => 'ASC',
        ];

        $farming_results = $FarmingController->getFarmings($farmingOptions, false, false);
        $farmingCount = count($farming_results);
        $finalFarmings = [];

        for ($i = 0; $i < $farmingCount; $i++) {
            $finalFarmings[$farming_results[$i]['id']]['name'] = $farming_results[$i]['name'];
            $finalFarmings[$farming_results[$i]['id']]['eik'] = $farming_results[$i]['bulstat'];
            $finalFarmings[$farming_results[$i]['id']]['company'] = $farming_results[$i]['company'];
        }

        if (true == $combineDocument) {
            $payments = current($results);
            $payments['amount_nat'] = array_sum(array_column($results, 'amount_nat'));
            $payments['amount'] = array_sum(array_column($results, 'amount'));
            $payments['contract_ids'] = array_column($results, 'contract_id');
            $payments['ekattes'] = implode(', ', array_column($results, 'ekattes'));
            $payments['payment_meta_data'] = array_column($results, 'payment_meta_data');

            $payments = [$payments];
        } else {
            $payments = $results;
        }

        foreach ($payments as $result) {
            // Ако се изплаща натура в лева
            if (1 == $result['paid_in'] && 2 == $result['paid_from']) {
                $amount = number_format($result['amount_nat'], 2, '.', '');
            } else {
                $amount = number_format($result['amount'], 2, '.', '');
            }

            $paymentSubject = (int) $paymentSubject;

            $template = '';
            if ($paymentSubject) {
                $template = $paymentSubject ? $this->getPaymentSubject($paymentSubject) : null;
            } else {
                if (null != $paymentSubjectText) {
                    $template = $paymentSubjectText;
                }
            }

            // Do additional calculations only if has plosht in the template
            if (strpos($template, '[[plosht]]')) {
                if ($combineDocument) {
                    if (null !== $result['payment_meta_data']) {
                        $result['area'] = $this->calculateAreasFromMetaData($result['payment_meta_data']);
                    } else {
                        $result['area'] = $this->calculateAreasFromContracts($result['contract_ids'], $result['farming_year'], $result['owner_id'], $result['path'], $userDbContractsController);
                    }
                } else {
                    if (null !== $result['payment_meta_data']) {
                        $paymentMetaData = json_decode($result['payment_meta_data'], true);
                        $result['area'] = $paymentMetaData['payment_area'];
                    } else {
                        $result['area'] = $this->findArea($result['contract_id'], $result['farming_year'], $result['owner_id'], $result['path'], $userDbContractsController);
                    }
                }
                $template = str_replace('[[plosht]]', $result['area'], $template);
            }

            $farmYear = $GLOBALS['Farming']['years'][$result['farming_year']]['farming_year'];
            $template = str_replace('[[nomer_na_dogovor]]', $result['c_num'], $template);
            $template = str_replace('[[stopanska_godina]]', $farmYear, $template);
            $template = str_replace('[[zemlishte]]', $result['ekattes'], $template);

            $template = html_entity_decode($template);
            $details = mb_substr($template, 0, 35, 'UTF-8');
            if (mb_strlen($template) > 35) {
                $additionalDetails = mb_substr($template, 35, 35, 'UTF-8');
            }

            list($bankName, $iban, $bic, $bankBranch, $bankAddress) = $this->transformBankData(json_decode($bank, true));

            $farmingCompany = $finalFarmings[$result['farming_id']]['company'];
            $printData['recipient'] = $FarmingController->StringHelper->strToBlankFormat($result['recipient'], 36);
            $printData['bank_acc'] = $FarmingController->StringHelper->strToBlankFormat(str_replace(' ', '', $result['bank_acc'] ?: $result['iban']), self::IBAN_LENGTH);
            $printData['bic'] = $FarmingController->StringHelper->strToBlankFormat(substr(!empty($result['bic']) ? $result['bic'] : ' ', 0, self::BIC_LENGTH), self::BIC_LENGTH);
            $printData['bank'] = $FarmingController->StringHelper->strToBlankFormat(mb_substr(!empty($result['bank_name']) ? $result['bank_name'] : ' ', 0, 32, 'UTF-8'), 32);
            $printData['amount'] = $FarmingController->StringHelper->strToBlankFormat(number_format($amount, 2, '.', ''), 13, 'right');
            $printData['details'] = $FarmingController->StringHelper->strToBlankFormat($details, 36);
            $printData['additionalDetails'] = $additionalDetails ? $FarmingController->StringHelper->strToBlankFormat($additionalDetails, 36) : ('&emsp;' . str_repeat('|&emsp;', 36));
            $printData['orderer'] = $FarmingController->StringHelper->strToBlankFormat($farmingCompany, 36);
            $printData['orderer_bank_acc'] = $FarmingController->StringHelper->strToBlankFormat($iban, self::IBAN_LENGTH);
            $printData['orderer_bic'] = $FarmingController->StringHelper->strToBlankFormat($bic, self::BIC_LENGTH);

            $printData['orderer_bank_name'] = $bankName;
            $printData['orderer_bank_branch'] = $bankBranch;
            $printData['orderer_bank_address'] = $bankAddress;

            $printData['payment_system'] = '&emsp;' . str_repeat('|&emsp;', 16);
            $printData['charges'] = '&emsp;' . str_repeat('|&emsp;', 2);

            if ($transactionDate) {
                if (is_string($transactionDate)) {
                    $newDate = explode('-', strftime('%d-%m-%y', strtotime($transactionDate)));
                    $newDate = implode('', $newDate);
                    $printData['date'] = $FarmingController->StringHelper->strToBlankFormat($newDate, 6) . 'г.';
                } else {
                    $newDate = explode('-', strftime('%d-%m-%y', strtotime($results[0]['date'])));
                    $newDate = implode('', $newDate);
                    $printData['date'] = $FarmingController->StringHelper->strToBlankFormat($newDate, 6) . 'г.';
                }
            } else {
                $printData['date'] = '&emsp;' . str_repeat('|&emsp;', 5);
            }

            $pages[] = $FarmingController->StringHelper->loadTemplate($GLOBALS['Templates'][15]['template'], $printData);
        }

        $finaltext = '';
        foreach ($pages as $page) {
            $finaltext .= '<page style="font-family: freeserif"><br />' . $page . '</page>';
        }

        $date = date('Y-m-d-H-i-s');
        $name = 'platejno_iban_' . $date . '.pdf';

        $userExportPath = PUBLIC_UPLOAD_EXPORT . DIRECTORY_SEPARATOR . $this->User->UserID;

        if (!is_dir($userExportPath)) {
            mkdir($userExportPath);
        }

        $pdfExpPath = $userExportPath . DIRECTORY_SEPARATOR . $name;
        $pdfDlPath = PUBLIC_UPLOAD_RELATIVE_PATH . DIRECTORY_SEPARATOR . 'export'
            . DIRECTORY_SEPARATOR . $this->User->UserID . DIRECTORY_SEPARATOR . $name;

        if ($returnAsText) {
            return $finaltext;
        }

        $printPdf = new PrintPdf();
        $printPdf->generateFromHtml($finaltext, $pdfExpPath, [], true);

        return ['file_path' => $pdfDlPath, 'file_name' => $name];
    }

    /**
     * Export weighing note to pdf.
     *
     * @api-method exportToPdfWeighingNote
     *
     * @param string $transactionId -Transaction id
     * @param bool $transactionDate -Insert date in document or not
     * @param bool $returnAsText -Returns HTML for export
     *
     * @return array|string pdfPath           -Path to the file to pdf export
     */
    public function exportToPdfWeighingNote($transactionId, $transactionDate = true, $returnAsText = false)
    {
        // check if transaction id exist
        if (!$transactionId) {
            return [];
        }

        $UserDbController = new UserDbController($this->User->Database);
        $FarmingController = new FarmingController('Farming');
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);

        $transactionIdArr = explode(',', $transactionId);

        $repQuery = "SELECT string_agg(rep_name || ' ' || rep_surname || ' ' || rep_lastname, ', ') as rep_names FROM {$UserDbController->DbHandler->tableOwnersReps} ir
					WHERE ir.id IN
						(SELECT rep_id FROM {$UserDbController->DbHandler->plotsOwnersRelTable} ipo
							WHERE ipo.owner_id = o.id
							AND ipo.pc_rel_id IN
								(SELECT DISTINCT(id) FROM {$UserDbController->DbHandler->contractsPlotsRelTable}
								WHERE contract_id IN (c.id)))";

        $options = [
            'tablename' => $UserDbController->DbHandler->tablePayments,
            'return' => [
                "(CASE WHEN owner_type = 1 THEN name || ' ' || surname || ' ' || lastname ELSE company_name END) as owner_names",
                '(CASE WHEN owner_type = 1 THEN egn ELSE eik END) as egn_eik',
                "({$repQuery}) as rep_names",
                'o.*', 'c.c_date', 'c.c_num', 'c.farming_id as farm_id',
                't.payment_nat_type',
                'p.*',
                'pn.amount as amount_nat',
                'unit_value',
                'pn.nat_type',
                't.recipient',
                't.recipient_egn',
                't.recipient_address',
            ],
            'sort' => $_POST['sort'],
            'order' => $_POST['order'],
            'limit' => $_POST['rows'],
            'offset' => ($_POST['pager'] - 1) * $_POST['rows'],
            'where' => [
                'transaction' => ['column' => 'transaction_id', 'compare' => 'IN', 'value' => $transactionIdArr],
            ],
        ];

        $results = $UserDbPaymentsController->getPaymentsByParams($options, false, false);

        // left only unique values in array
        $results = array_map('unserialize', array_unique(array_map('serialize', $results)));

        $newTmpResults = [];
        foreach ($results as $kay => $tmpRes) {
            if (!empty($tmpRes['farm_id'])) {
                $tmpRes['farming_id'] = $tmpRes['farm_id'];
                unset($tmpRes['farm_id']);
            }
            $newTmpResults[$kay] = $tmpRes;
        }

        $results = $newTmpResults;
        $resultsCount = count($results);
        if (0 == $resultsCount) {
            return [];
        }

        // get renta types
        $options = [
            'tablename' => $UserDbController->DbHandler->tableRentaTypes,
        ];

        $renta_results = $UserDbController->getItemsByParams($options, false, false);
        $rentaCount = count($renta_results);
        // form renta types array
        $renta_types = [];
        $finaltext = '';
        for ($i = 0; $i < $rentaCount; $i++) {
            $renta_types[$renta_results[$i]['id']]['name'] = $renta_results[$i]['name'];
            $renta_types[$renta_results[$i]['id']]['unit'] = $GLOBALS['Contracts']['renta_units'][$renta_results[$i]['unit']]['name'];
        }

        $uniqueOwnersData = [];
        // group results by owner
        for ($i = 0; $i < $resultsCount; $i++) {
            $ownerID = $results[$i]['owner_id'];
            $uniqueOwnersData[$ownerID][] = $results[$i];
        }

        foreach ($uniqueOwnersData as $ownerData) {
            $userData = $FarmingController->getFarmingItemsByIDString($ownerData[0]['farming_id'], $this->User->GroupID);

            $isOwnerRecipient = $this->checkIfOwnerIsRecipientWeighingNote($ownerData);

            $printData = [];
            $printData['provider_name'] = $userData[0]['company'];
            $printData['provider_bulstat'] = $userData[0]['bulstat'];
            $printData['provider_mol'] = $userData[0]['mol'];
            $printData['provider_address'] = $userData[0]['address'];

            $printData['customer_name'] = $isOwnerRecipient ? $ownerData[0]['owner_names'] : $ownerData[0]['recipient'];
            $printData['customer_bulstat'] = $isOwnerRecipient ? $ownerData[0]['egn_eik'] : $ownerData[0]['recipient_egn'];
            $printData['customer_mol'] = $ownerData[0]['mol'];
            $printData['farming_year'] = $GLOBALS['Farming']['years'][$ownerData[0]['farming_year']]['farming_year'];
            $printData['c_num'] = $ownerData[0]['c_num'];
            $printData['customer_address'] = $isOwnerRecipient ? wordwrap($ownerData[0]['address'], 100, '<br>') : wordwrap($ownerData[0]['recipient_address'], 100, '<br>');
            if ($transactionDate) {
                if (is_string($transactionDate)) {
                    $printData['date'] = date('d.m.Y', strtotime($transactionDate)) . 'г.';
                } else {
                    $printData['date'] = date('d.m.Y', strtotime($ownerData[0]['date'])) . 'г.';
                }
            } else {
                $printData['date'] = '';
            }

            $printData['rows'] = [];
            $rowsByNat = [];

            $ownerDataCount = count($ownerData);
            for ($i = 0; $i < $ownerDataCount; $i++) {
                $natTypeId = $ownerData[$i]['nat_type'];
                $natTypeName = $renta_types[$natTypeId]['name'];
                $natTypeUnit = $renta_types[$natTypeId]['unit'];
                $ownerNatAmount = $ownerData[$i]['amount_nat'];

                if (!array_key_exists($natTypeId, $rowsByNat)) {
                    $rowsByNat[$natTypeId] = [
                        'renta_nat_type' => $natTypeName,
                        'net' => ('бр' === $natTypeUnit) ? $ownerNatAmount : number_format($ownerNatAmount, 3, '.', ''),
                        'unit_value' => BGNtoEURO($ownerData[$i]['unit_value']),
                        'unit' => $natTypeUnit,
                        'amount' => (float)$ownerData[$i]['amount_nat'] * (float)$ownerData[$i]['unit_value'],
                    ];

                    continue;
                }

                $rowsByNat[$natTypeId]['net'] += number_format($ownerNatAmount, 3, '.', '');
                $rowsByNat[$natTypeId]['amount'] += (float)$ownerData[$i]['amount_nat'] * (float)$ownerData[$i]['unit_value'];
            }

            $printData['rows'] = array_values($rowsByNat);
            $printData['rows'] = array_map(function ($row) {
                $row['net'] .= ' ' . $row['unit'];

                return $row;
            }, $printData['rows']);

            $ltext = '';
            $copiesNumber = 2;
            for ($i = 1; $i <= $copiesNumber; $i++) {
                $printData['copies_left'] = $copiesNumber - $i;
                $ltext .= $FarmingController->StringHelper->loadTemplate($GLOBALS['Templates'][16]['template'], $printData);
            }
            $ltext = '<page style="font-family: freeserif">' . $ltext . '</page>';
            $finaltext .= $ltext;
        }

        $date = date('Y-m-d-H-i-s');

        $name = 'kantarna_belejka_' . $date . '.pdf';

        $userExportPath = PUBLIC_UPLOAD_EXPORT . DIRECTORY_SEPARATOR . $this->User->UserID;

        if (!is_dir($userExportPath)) {
            mkdir($userExportPath);
        }

        $pdfExpPath = $userExportPath . DIRECTORY_SEPARATOR . $name;
        $pdfDlPath = PUBLIC_UPLOAD_RELATIVE_PATH . DIRECTORY_SEPARATOR . 'export'
            . DIRECTORY_SEPARATOR . $this->User->UserID . DIRECTORY_SEPARATOR . $name;

        if ($returnAsText) {
            return $finaltext;
        }

        $printPdf = new PrintPdf();
        $printPdf->generateFromHtml($finaltext, $pdfExpPath, ['orientation' => 'Landscape', 'margin-top' => 7, 'margin-bottom' => 3], true);

        return ['file_path' => $pdfDlPath, 'file_name' => $name];
    }

    /**
     * Export weighing note and bank payment to pdf.
     *
     * @api-method exportToPdfWeighingNote
     *
     * @param string $transactionId -Transaction id
     * @param bool $transactionDate -Insert date in document or not
     * @param null|mixed $paymentSubject
     * @param null|mixed $paymentSubjectText
     *
     * @return array|string pdfPath           -Path to the file to pdf export
     */
    public function exportCombinedPaymentDocs(
        $transactionId,
        $transactionDate = true,
        $paymentSubject = null,
        $paymentSubjectText = null,
        $withoutRkoNumbering = false,
        $combineDocument = false,
        $collectАllPaymentАmounts,
        $paymentDocumentType,
        $paymentReceiptDeclaration,
        $paymentsParams
    ) {
        // check if transaction id exist
        if (!$transactionId) {
            return [];
        }

        $rko_text = $this->exportToPdfPaymentOrder(
            $transactionId,
            null,
            $transactionDate,
            $paymentSubject,
            $paymentSubjectText,
            $withoutRkoNumbering,
            true,
            [],
            $combineDocument = false,
            $collectАllPaymentАmounts,
            $paymentDocumentType,
            $paymentReceiptDeclaration,
            $paymentsParams
        );

        $kantarna_belejka_text = $this->exportToPdfWeighingNote($transactionId, $transactionDate, true);

        $finaltext = '';
        $finaltext .= $kantarna_belejka_text . '<div style="clear: both;page-break-after: always;"></div>';
        $finaltext .= $rko_text;

        $date = date('Y-m-d-H-i-s');

        $name = 'kantarna_belejka_i_rko_' . $date . '.pdf';
        $userExportPath = PUBLIC_UPLOAD_EXPORT . DIRECTORY_SEPARATOR . $this->User->UserID;

        if (!is_dir($userExportPath)) {
            mkdir($userExportPath);
        }

        $pdfExpPath = $userExportPath . DIRECTORY_SEPARATOR . $name;
        $pdfDlPath = PUBLIC_UPLOAD_RELATIVE_PATH . DIRECTORY_SEPARATOR . 'export'
            . DIRECTORY_SEPARATOR . $this->User->UserID . DIRECTORY_SEPARATOR . $name;

        $printPdf = new PrintPdf();
        $printPdf->generateFromHtml($finaltext, $pdfExpPath, ['orientation' => 'Landscape', 'margin-top' => 7, 'margin-bottom' => 3], true);

        return ['file_path' => $pdfDlPath, 'file_name' => $name];
    }

    /**
     * Export weighing note and bank payment to pdf.
     *
     * @api-method exportToPdfWeighingNote
     *
     * @param string $transactionId -Transaction id
     * @param bool $transactionDate -Insert date in document or not
     * @param string $bank - Orderer Bank Info
     * @param int $paymentSubject -The id of the payment subject template
     * @param string $paymentSubjectText -payment subject text instead of id
     *
     * @return array|string pdfPath           -Path to the file to pdf export
     */
    public function exportCombinedBankPaymentDocs($transactionId, $transactionDate = true, $bank = '', $paymentSubject = null, $paymentSubjectText = null)
    {
        // check if transaction id exist
        if (!$transactionId) {
            return [];
        }

        $rko_text = $this->exportToPdfBankPaymentOrder($transactionId, $transactionDate, $bank, $paymentSubject, $paymentSubjectText, true);

        $kantarna_belejka_text = $this->exportToPdfWeighingNote($transactionId, $transactionDate, true);

        $replacement = '<table style="margin-left: -10px;">';

        $kantarna_belejka_text = preg_replace('/\\<table\\>/', $replacement, $kantarna_belejka_text);

        $finaltext = '';
        $finaltext .= $rko_text;
        $finaltext .= $kantarna_belejka_text;

        $date = date('Y-m-d-H-i-s');
        $name = 'kantarna_belejka_i_platejno_' . $date . '.pdf';
        $userExportPath = PUBLIC_UPLOAD_EXPORT . DIRECTORY_SEPARATOR . $this->User->UserID;

        if (!is_dir($userExportPath)) {
            mkdir($userExportPath);
        }

        $pdfExpPath = $userExportPath . DIRECTORY_SEPARATOR . $name;
        $pdfDlPath = PUBLIC_UPLOAD_RELATIVE_PATH . DIRECTORY_SEPARATOR . 'export'
            . DIRECTORY_SEPARATOR . $this->User->UserID . DIRECTORY_SEPARATOR . $name;

        $printPdf = new PrintPdf();
        $printPdf->generateFromHtml($finaltext, $pdfExpPath, [], true);

        return ['file_path' => $pdfDlPath, 'file_name' => $name];
    }

    /**
     * Export weighing note and bank payment to pdf.
     *
     * @api-method getTransactionPaymentNumbers
     *
     * @param string $transactionId -Transaction id
     *
     * @throws MTRpcException
     *
     * @return array
     *               [
     *               #item integer id - payment id
     *               #item string rko_number - rko number
     *               ]
     */
    public function getTransactionPaymentNumbers($transactionId)
    {
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);
        $numbers = $UserDbPaymentsController->getPaymentDocNumbersByTransactionID($transactionId);

        if (0 == count($numbers)) {
            throw new MTRpcException('NON_EXISTING_PAYMENT_INFO', -33222);
        }

        return $numbers;
    }

    /**
     * Generate new numbers for the transaction payments.
     *
     * @param string $transactionId -Transaction id
     *
     * @return array
     *               [
     *               #item integer id - payment id
     *               #item string rko_number - rko number
     *               ]
     */
    public function getNewTransactionRkoNumbers($transactionId)
    {
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);

        $payments = $UserDbPaymentsController->getPaymentDocNumbersByTransactionID($transactionId);

        $lastNumber = $UserDbPaymentsController->getLastGeneratedRkoNumber();
        $lastNumber = $lastNumber[0]['rko_number'];

        for ($i = count($payments) - 1; $i >= 0; $i--) {
            if (is_numeric($lastNumber)) {
                $newRKONum = $lastNumber + 1;
                $newRKONum = $newRKONum . '';
            } else {
                $matches = [];
                preg_match('/(\d+)$/', $lastNumber, $matches);
                if (count($matches) > 0) {
                    $lastRkoTxtPart = substr($lastNumber, 0, -strlen($matches[0]));
                    $incrementedNum = $matches[1] + 1;
                    $newRKONum = $lastRkoTxtPart . $incrementedNum;
                } else {
                    $newRKONum = $lastNumber . 1;
                }
            }
            $lastNumber = $newRKONum;
            $payments[$i]['rko_number'] = $lastNumber;
        }

        return $payments;
    }

    /**
     * Export weighing note and bank payment to pdf.
     *
     * @api-method updatePaymentRkoNumbers
     *
     * @param array $data
     *                    [
     *                    #item array
     *                    [
     *                    #item integer id
     *                    #item string rko_number
     *                    ]
     *                    ]
     *
     * @return bool
     */
    public function updatePaymentRkoNumbers($data)
    {
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);

        try {
            foreach ($data as $payment) {
                $UserDbPaymentsController->updateRkoNumberByPaymentId($payment);
            }

            return true;
        } catch (Exception $e) {
            return false;
        }
    }

    private function findArea($contractId, $farmingYear, $ownerId, $path, $userDbContractsController, $areaType = 'owner_area')
    {
        $usedAreas = $userDbContractsController->getContractUsedAreas($contractId, $farmingYear);

        return $userDbContractsController->findAreaByOwner($ownerId, $path, $usedAreas, $areaType);
    }

    private function calculateAreasFromMetaData($paymentMetaDataArray)
    {
        $areas = [];
        foreach ($paymentMetaDataArray as $metaData) {
            $paymentMetaData = json_decode($metaData, true);
            $areas[] = $paymentMetaData['payment_area'];
        }

        return implode(', ', $areas);
    }

    private function calculateAreasFromContracts($contractIds, $farmingYear, $ownerId, $path, $userDbContractsController, $areaType = 'owner_area')
    {
        $areas = [];
        foreach ($contractIds as $contractId) {
            $areas[] = $this->findArea($contractId, $farmingYear, $ownerId, $path, $userDbContractsController, $areaType);
        }

        return implode(', ', $areas);
    }
}
