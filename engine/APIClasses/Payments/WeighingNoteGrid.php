<?php

namespace TF\Engine\APIClasses\Payments;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbPayments\UserDbPaymentsController;

// Prado::using('Plugins.Core.UserDb.*');
// Prado::using('Plugins.Core.UserDb.conf');
// Prado::using('Plugins.Core.Users.*');
// Prado::using('Plugins.Core.Users.conf');
// Prado::using('Plugins.Core.UserDbPayments.*');
// Prado::using('Plugins.Core.Layers.*');
// Prado::using('Plugins.Core.Contracts.conf');

/**
 * Weighing Note Grid.
 *
 * @rpc-module Payments
 *
 * @rpc-service-id weighing-note-grid
 */
class WeighingNoteGrid extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readWeighingNoteGrid'],
                'validators' => [
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * Read personal use grid.
     *
     * @api-method read
     *
     * @param int $contractId -The contract id for personal use
     * @param int $year -The year for personal use
     * @param int $page -The current page number
     * @param int $rows -Rows per page
     * @param string $sort -A grid column by which the grid is sorted
     * @param string $order -The sort order ASC/DESC
     *
     * @return array result
     *               {
     *               #item array rows             -The results
     *               #item string total           -The count of all results
     *               }
     */
    public function readWeighingNoteGrid(int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        // creating the default return
        $default = [
            'rows' => [],
            'total' => 0,
        ];

        $UserDbController = new UserDbController($this->User->Database);
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);

        $repQuery = "SELECT string_agg(rep_name || ' ' || rep_surname || ' ' || rep_lastname, ', ') as rep_names FROM {$UserDbController->DbHandler->tableOwnersReps} ir
					WHERE ir.id IN 
						(SELECT rep_id FROM {$UserDbController->DbHandler->plotsOwnersRelTable} ipo 
							WHERE ipo.owner_id = o.id
							AND ipo.pc_rel_id IN
								(SELECT DISTINCT(id) FROM {$UserDbController->DbHandler->contractsPlotsRelTable} 
								WHERE contract_id IN (c.id)))";

        $options = [
            'custom_counter' => 'count(DISTINCT(p.id))',
            'return' => [
                "(CASE WHEN owner_type = 1 THEN name || ' ' || surname || ' ' || lastname ELSE company_name END) as owner",
                "({$repQuery}) as rep_names",
                'o.company_name', 'c.c_date', 'c.c_num', 'c.farming_id',
                'max(t.id) as transaction_id', 'max(t.farming_year) as farming_year',
                'p.id as payment_id', 'max(round(p.amount::numeric, 2)) as amount', 'max(p.date) as date',
                'array_agg(pn.nat_type) as payment_nat_type',
                'array_agg(round(pn.amount::numeric, 2)) as amount_nat',
                'max(t.paid_from) as paid_from',
                'max(t.paid_in) as paid_in',
            ],
            'sort' => $sort,
            'order' => $order,
            'limit' => $rows,
            'offset' => ($page - 1) * $rows,
            'where' => [
                'paid_in' => ['column' => 'paid_in', 'compare' => '=', 'prefix' => 't', 'value' => 2],
            ],
            'group' => 'p.id, c.id, o.id',
        ];

        $counter = $UserDbPaymentsController->getPaymentsByParams($options, true, false);
        if (0 == $counter[0]['count']) {
            return $default;
        }

        $results = $UserDbPaymentsController->getPaymentsByParams($options, false, false);
        $resultsCount = count($results);
        // get renta types
        $options = [
            'tablename' => $UserDbController->DbHandler->tableRentaTypes,
        ];

        $renta_results = $UserDbController->getItemsByParams($options, false, false);
        $rentCount = count($renta_results);
        // form renta types array
        $renta_types = [];
        for ($i = 0; $i < $rentCount; $i++) {
            $renta_types[$renta_results[$i]['id']] = $renta_results[$i]['name'] . '(' . $GLOBALS['Contracts']['renta_units'][$renta_results[$i]['unit']]['name'] . ')';
        }

        // iterate and convert results to grid format
        for ($i = 0; $i < $resultsCount; $i++) {
            $results[$i]['ttype'] = 'от ' . (1 == $results[$i]['paid_from'] ? 'Лева' : 'Натура') . ' в ' . (1 == $results[$i]['paid_in'] ? 'Лева' : 'Натура');
            $results[$i]['date'] = strftime('%d.%m.%Y', strtotime($results[$i]['date']));
            $results[$i]['farming_year'] = $GLOBALS['Farming']['years'][$results[$i]['farming_year']]['farming_year_short'];

            if (1 == $results[$i]['paid_from']) {
                $results[$i]['paid_from_text'] = BGNtoEURO($results[$i]['amount']);
            } else {
                $payment_nat_types = explode(',', trim($results[$i]['payment_nat_type'], '{}'));
                $payment_nat_types_count = count($payment_nat_types);
                $amount_nat = explode(',', trim($results[$i]['amount_nat'], '{}'));
                for ($j = 0; $j < $payment_nat_types_count; $j++) {
                    $results[$i]['paid_from_text'] .= $amount_nat[$j] . ' X [' . $renta_types[$payment_nat_types[$j]] . ']<br/>';
                }
            }

            if (1 == $results[$i]['paid_in']) {
                $results[$i]['paid_in_text'] = BGNtoEURO($results[$i]['amount']);
            } else {
                $payment_nat_types = explode(',', trim($results[$i]['payment_nat_type'], '{}'));
                $payment_nat_types_count = count($payment_nat_types);
                $amount_nat = explode(',', trim($results[$i]['amount_nat'], '{}'));
                for ($j = 0; $j < $payment_nat_types_count; $j++) {
                    $results[$i]['paid_in_text'] .= $amount_nat[$j] . ' X [' . $renta_types[$payment_nat_types[$j]] . ']<br/>';
                }
            }

            $results[$i]['c_num'] = $results[$i]['c_num'] . ' / ' . strftime('%d.%m.%Y', strtotime($results[$i]['c_date']));

            // put new rows if reps are more than one
            $tmp_reps_array = explode(', ', $results[$i]['rep_names']);
            if (count($tmp_reps_array) > 1) {
                $results[$i]['rep_names'] = implode(', <br/>', $tmp_reps_array);
            }
        }

        return [
            'rows' => $results,
            'total' => $counter[0]['count'],
        ];
    }
}
