<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Prado;
use Prado\Web\Services\TRpcApiProvider;

// Prado::using('Plugins.Core.Users.*');

/**
 * Смяна на парола.
 *
 * @rpc-module Common
 *
 * @rpc-service-id irrigated-area-combobox
 */
class IrrigatedAreaCombobox extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getIrrigatedAreaCombobox']],
        ];
    }

    /**
     * @api-method getIrrigatedAreaCombobox
     *
     * @param array rpcParams
     *        {
     *          #item bool include_all
     *          #item bool selected
     *        }
     *
     * @return array
     *               {
     *               #item array {
     *               #item string label
     *               #item bool/string value
     *               #item bool selected
     *               }
     *               }
     */
    public function getIrrigatedAreaCombobox($rpcParams)
    {
        $return = [
            [
                'label' => 'Да',
                'value' => true,
            ],
            [
                'label' => 'Не',
                'value' => false,
            ],
        ];

        if (isset($rpcParams['include_all']) && $rpcParams['include_all']) {
            array_unshift(
                $return,
                [
                    'label' => 'Всички',
                    'value' => '',
                ]
            );
        }

        if (isset($rpcParams['selected']) && $rpcParams['selected']) {
            $return[0]['selected'] = true;
        }

        return $return;
    }
}
