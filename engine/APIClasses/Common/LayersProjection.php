<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;
use TF\Application\Common\Config;

/**
 * MTSoapService class file.
 *
 * <AUTHOR> Ltd. <<EMAIL>>
 *
 * @link http://www.devision.bg/
 *
 * @copyright Copyright &copy; 2008 Devision Ltd.
 */

/**
 * Returns all layer projections.
 *
 * @rpc-module Common
 *
 * @rpc-service-id projection
 */
class LayersProjection extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getLayersProjection']],
        ];
    }

    /**
     * @api-method read
     *
     * @return array
     */
    public function getLayersProjection()
    {
        $excludedLayerTypes = [
            Config::LAYER_TYPE_ZP,
            Config::LAYER_TYPE_GPS,
            Config::LAYER_TYPE_KVS,
            Config::LAYER_TYPE_SATELLITE_WORK,
            Config::LAYER_TYPE_DSS,
            Config::LAYER_TYPE_LFA,
            Config::LAYER_TYPE_NATURA_2000,
            Config::LAYER_TYPE_PERMANETELY_GREEN_AREAS,
            Config::LAYER_TYPE_KVS_OSZ,
            Config::LAYER_TYPE_EXCEL_IMPORT,
            Config::LAYER_TYPE_CSD,
            Config::LAYER_TYPE_FOR_ISAK,
            13, // Данни от комасация от други софтуери
            7, // Собствен растер
        ];

        $return = [];
        $i = 0;
        foreach ($GLOBALS['Layers']['srid'] as $item) {
            if (in_array($item['type'], $excludedLayerTypes)) {
                continue;
            }

            $return[$i]['name'] = $item['title'];
            $return[$i]['srid'] = $item['srid'];
            $return[$i]['layer_type'] = $item['type'];
            $return[$i]['id'] = $item['id'];

            if (0 == $i) {
                $return[$i]['selected'] = 'true';
            }
            $i++;
        }

        return $return;
    }
}
