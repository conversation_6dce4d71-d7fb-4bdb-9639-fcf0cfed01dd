<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;

/**
 * План за изготвяне на балансирано торене > Година.
 *
 * @rpc-module Common
 *
 * @rpc-service-id sample-norm-year-combobox
 */
class SampleNormYearCombobox extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getSampleNormYearCombobox']],
        ];
    }

    /**
     * Връща възможните години за изготвяне на план за балансирано торене.
     *
     * @api-module read
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer year
     *                         #item boolean selected
     *                         }
     *
     * @return array
     *               {
     *               #item boolean default
     *               #item string  farming_year
     *               #item string  farming_year_short
     *               #item integer id
     *               #item boolean selected
     *               #item string  title
     *               #item integer year
     *               }
     */
    public function getSampleNormYearCombobox($rpcParams)
    {
        if ($this->User->isGuest) {
            return [];
        }

        if (!$rpcParams['year'] || !(int) $rpcParams['year']) {
            return [];
        }
        $return = [];
        foreach ($GLOBALS['Farming']['years'] as $item) {
            if ($item['id'] >= $rpcParams['year']) {
                $return[] = $item;
            }
        }

        if ($rpcParams['selected'] && 'true' == $rpcParams['selected']) {
            $return[0]['selected'] = true;
        }

        return $return;
    }
}
