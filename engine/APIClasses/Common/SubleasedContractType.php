<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Prado;
use Prado\Web\Services\TRpcApiProvider;

// Prado::using('Plugins.Core.Contracts.conf');
// Prado::using('Plugins.Core.Contracts.*');

/**
 * Типове договори за преотдаване.
 *
 * @rpc-module Common
 *
 * @rpc-service-id subleased-contract-type
 */
class SubleasedContractType extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getSubleasedContractType']],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item boolean selected
     *                         }
     *
     * @return array
     *               {
     *               #item integer id
     *               #item string  name
     *               }
     */
    public function getSubleasedContractType($rpcParams = null)
    {
        $return = [];

        foreach ($GLOBALS['Contracts']['ContractTypes'] as $item) {
            if (2 == $item['id'] || 3 == $item['id']) {
                $return[] = $item;
            }
        }

        if ($rpcParams['selected'] && 'true' == $rpcParams['selected']) {
            $return[0]['selected'] = true;
        }

        return $return;
    }
}
