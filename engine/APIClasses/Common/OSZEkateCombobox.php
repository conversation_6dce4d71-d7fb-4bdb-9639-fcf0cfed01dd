<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

include_once __DIR__ . '/../../Plugins/Core/UserDb/conf.php';

/**
 * комбобокс за EKATTE, заредени в "Данни от ОСЗ".
 *
 * @rpc-module Common
 *
 * @rpc-service-id osz-ekate-combobox
 */
class OSZEkateCombobox extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getOSZEkateCombobox']],
        ];
    }

    /**
     * @api-method read
     *
     * @return array
     */
    public function getOSZEkateCombobox()
    {
        $UserDbController = new UserDbController($this->User->Database);

        $return = [];

        $options = [
            'tablename' => 'osz_ekatte_combobox',
            'return' => ['*'],
        ];

        $results = $UserDbController->getItemsByParams($options, false, false);

        $count = count($results);
        if (0 == $count) {
            return [0 => ['ekate' => 0, 'text' => 'Няма заредени данни от ОСЗ']];
        }

        for ($i = 0; $i < $count; $i++) {
            if (null != $results[$i]['ekatte']) {
                $return[] = [
                    'ekate' => $results[$i]['ekatte'],
                    'text' => $results[$i]['ekatte'] . ' (' . $results[$i]['ekatte_name'] . ')',
                ];
            }
        }

        $return[0]['selected'] = true;

        return $return;
    }
}
