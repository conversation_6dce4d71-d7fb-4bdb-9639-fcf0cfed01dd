<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;
use TF\Application\Common\Config;

/**
 * Get sublease types.
 *
 * @rpc-module Common
 *
 * @rpc-service-id allowable-layer-ntp-combobox
 */
class AllowableLayerNTPCombobox extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getNTPsCombobox']],
        ];
    }

    /**
     * @api-method read
     *
     * @return array
     */
    public function getNTPsCombobox()
    {
        return Config::$NTP;
    }
}
