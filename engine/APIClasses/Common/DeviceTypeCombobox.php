<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;

/**
 * Return all supported device names as text.
 *
 * @rpc-module Common
 *
 * @rpc-service-id device-type-combo
 */
class DeviceTypeCombobox extends TRpcApi<PERSON>rovider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getDeviceTypeCombobox']],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item boolean selected
     *                         }
     *
     * @return array
     */
    public function getDeviceTypeCombobox($rpcParams)
    {
        if ($this->User->isGuest) {
            return [];
        }
        $return = [];
        foreach ($GLOBALS['Layers']['device'] as $item) {
            $return[] = $item;
        }

        if ($rpcParams['selected'] && $rpcParams['selected'] = 'true') {
            $return[0]['selected'] = 'true';
        }

        return $return;
    }
}
