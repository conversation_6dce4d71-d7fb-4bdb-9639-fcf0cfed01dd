<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;

/**
 * Remove file from the server.
 *
 * @rpc-module Common
 *
 * @rpc-service-id remove-file
 */
class RemoveFile extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'remove' => ['method' => [$this, 'removeFileFromServer']],
        ];
    }

    /**
     * remove file from the server.
     *
     * @api-method remove
     *
     * @throws Exception
     */
    public function removeFileFromServer($fileName)
    {
        $fileInfo = pathinfo($fileName);

        $allowedFormats = ['pdf', 'doc', 'csv', 'xls'];
        $allowedPaths = [
            PUBLIC_UPLOAD_EXPORT,
            PUBLIC_UPLOAD_BLANK,
        ];
        if (!in_array($fileInfo['extension'], $allowedFormats)) {
            throw new Exception('Error Processing Request', 1);
        }

        $baseFileName = $fileInfo['basename'];

        foreach ($allowedPaths as $path) {
            $fullPath = $path . '/' . $baseFileName;
            if (file_exists($fullPath)) {
                unlink($fullPath);

                break;
            }
        }
    }
}
