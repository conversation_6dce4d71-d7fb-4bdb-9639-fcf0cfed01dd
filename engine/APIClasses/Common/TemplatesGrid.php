<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Returns Templates grid information.
 *
 * @rpc-module Common
 *
 * @rpc-service-id templates-grid
 */
class TemplatesGrid extends TRpcApiProvider
{
    private $module = 'Common';
    private $service_id = 'templates-grid';

    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readTemplatesGrid'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'add' => ['method' => [$this, 'addTemplate'],
                'validators' => [
                    'template_name' => 'validateText',
                ]],
            'markForEdit' => ['method' => [$this, 'templateEditMark'],
                'validators' => [
                    'rpcParam' => 'validateInteger, validateRequired, validateNotNull',
                ]],
            'editTemplate' => ['method' => [$this, 'editTemplate'],
                'validators' => [
                    'template_name' => 'validateText',
                ]],
            'deleteTemplate' => ['method' => [$this, 'deleteTemplate'],
                'validators' => [
                    'rpcParam' => 'validateInteger, validateRequired, validateNotNull',
                ]],
        ];
    }

    /**
     * @api-method read
     *
     * @param string $page
     * @param string $rows
     * @param ?array $rpcParams
     * @param ?string $sort
     * @param ?string $order
     *
     * @return array
     */
    public function readTemplatesGrid(?array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableTemplates,
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
        ];

        $counter = $UserDbController->getItemsByParams($options, true, false);

        if (0 === $counter[0]['count']) {
            return [];
        }

        $results = $UserDbController->getItemsByParams($options, false, false);

        return [
            'rows' => $results,
            'total' => $counter[0]['count'],
        ];
    }

    /**
     * Returns Templates grid information.
     *
     * @api-method add
     *
     * @param array $rpcParams
     *                         {
     *                         #item string template_name
     *                         #item string template_body
     *                         }
     */
    public function addTemplate($rpcParams)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $options = [
            'tablename' => $UserDbController->DbHandler->tableTemplates,
            'mainData' => [
                'title' => $rpcParams['template_name'],
                'html' => $rpcParams['template_body'],
                'add_date' => date('Y-m-d H:i:s'),
                'show_page_numbers' => empty($rpcParams['show_page_numbers']) ? null : $rpcParams['show_page_numbers'],
            ],
        ];

        $recordID = $UserDbController->addItem($options);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['title' => $rpcParams['template_name'], 'body_length' => strlen($rpcParams['template_body'])], $recordID, 'Adding template');
    }

    /**
     * Returns Templates grid information.
     *
     * @api-method deleteTemplate
     *
     * @param int $rpcParam
     */
    public function deleteTemplate($rpcParam)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $oldTemplate = $this->templateEditMark($rpcParam);

        $template_id = $rpcParam;

        $options = [
            'tablename' => $UserDbController->DbHandler->tableTemplates,
            'id_string' => $template_id,
        ];

        $UserDbController->deleteItemsByParams($options);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['title' => $oldTemplate[0]['title'], 'body_length' => strlen($oldTemplate[0]['html'])], $options, 'Deleting template');
    }

    /**
     * Returns data for the selected template ID.
     *
     * @api-method markForEdit
     *
     * @param int $rpcParam - selected template ID
     *
     * @return array
     */
    public function templateEditMark($rpcParam)
    {
        $UserDbController = new UserDbController($this->User->Database);

        $template_id = $rpcParam;

        $options = [
            'tablename' => $UserDbController->DbHandler->tableTemplates,
            'where' => [
                'template_id' => ['column' => 'id', 'compare' => '=', 'value' => $template_id],
            ],
        ];

        return $UserDbController->getItemsByParams($options, false, false);
    }

    /**
     * Returns Templates grid information.
     *
     * @api-method editTemplate
     *
     * @param array $rpcParams
     *                         {
     *                         #item int    id
     *                         #item string template_name
     *                         #item string template_body
     *                         }
     */
    public function editTemplate($rpcParams)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $template_id = $rpcParams['id'];

        $oldTemplate = $this->templateEditMark($template_id);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableTemplates,
            'mainData' => [
                'title' => $rpcParams['template_name'],
                'html' => $rpcParams['template_body'],
                'show_page_numbers' => empty($rpcParams['show_page_numbers']) ? null : $rpcParams['show_page_numbers'],
            ],
            'where' => [
                'id' => $template_id,
            ],
        ];

        $UserDbController->editItem($options);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['title' => $oldTemplate[0]['title'], 'body' => strlen($oldTemplate[0]['html'])], ['title' => $rpcParams['template_name'], 'body_length' => strlen($rpcParams['template_body'])], 'Editing template');
    }
}
