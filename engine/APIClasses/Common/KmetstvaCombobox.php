<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Returns all obshtini.
 *
 * @rpc-module Common
 *
 * @rpc-service-id kmetstva-combobox
 *
 * @property UserDbController $UserDbController
 * @property UsersController $UsersController
 * @property LayersController $LayersController
 */
class KmetstvaCombobox extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getKmetstva']],
        ];
    }

    /**
     * @api-method read
     *
     * @return array
     */
    public function getKmetstva($rpcParams = [])
    {
        $UsersController = new UsersController();

        $options = [
            'tablename' => 'su_kmetstva',
            'sort' => 'kmet_name',
            'order' => 'ASC',
        ];

        if (isset($rpcParams['nm_obst_id'])) {
            $options['where'] = [
                'nm_obst_id' => [
                    'column' => 'nm_obst_id',
                    'compare' => '=',
                    'value' => $rpcParams['nm_obst_id'],
                ],
            ];
        }

        return $UsersController->getItemsByParams($options, false, false);
    }
}
