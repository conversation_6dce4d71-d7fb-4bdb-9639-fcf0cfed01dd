<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

/**
 * Get all contract groups.
 *
 * @rpc-module Common
 *
 * @rpc-service-id contract-groups-combobox
 */
class ContractGroupsCombobox extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getContractGroupsCombobox']],
        ];
    }

    /**
     * @api-method read
     *
     * @return array
     */
    public function getContractGroupsCombobox()
    {
        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'tablename' => 'su_contract_group',
            'return' => ['id, name'],
        ];

        return $UserDbController->getItemsByParams($options);
    }
}
