<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;
use TF\Application\Common\Config;

/**
 * Get sublease types.
 *
 * @rpc-module Common
 *
 * @rpc-service-id sublease-type-combobox
 */
class SubleaseTypeCombobox extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getSubleaseTypeCombobox']],
        ];
    }

    /**
     * @api-method read
     *
     * @return array
     */
    public function getSubleaseTypeCombobox()
    {
        return [
            0 => [
                'type' => 'null',
                'text' => 'Всички',
                'selected' => true,
            ],
            1 => [
                'type' => Config::CONTRACT_SUBLEASE_TYPE_SUBLET,
                'text' => 'Преотдаден',
            ],
            2 => [
                'type' => Config::CONTRACT_SUBLEASE_TYPE_SUBLEASE,
                'text' => 'Пренает',
            ],
        ];
    }
}
