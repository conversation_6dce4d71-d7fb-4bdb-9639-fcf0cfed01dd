<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Prado;
use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\Users\UsersController;

// Prado::using('Plugins.Core.Layers.conf');
// Prado::using('Plugins.Core.Layers.*');

/**
 * Returns all information about the "ВПС зимуващи гъски" layer.
 *
 * @rpc-module Common
 *
 * @rpc-service-id zimni-ekate-combobox
 */
class VPSZimniGaskiEkateCombobox extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getVPSZimniGaskiEkateCombobox']],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item boolean record_all
     *                         #item boolean selected
     *                         }
     *
     * @return array
     */
    public function getVPSZimniGaskiEkateCombobox($rpcParams)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        $LayersController = new LayersController('Layers');
        $return = [];
        $options = [
            'tablename' => 'layer_vps_gaski_zimni',
            'return' => [
                'ekatte', ' zem',
            ],
            'group' => 'ekatte, zem',
        ];

        $hash = md5(json_encode($options));
        $ekate_results = $LayersController->MemCache->get($hash);
        if (!$ekate_results) {
            $ekate_results = $LayersController->getRemoteLayerData($options, false, false);
            $LayersController->MemCache->add($hash, $ekate_results, $LayersController->default_memcache_expire);
        }
        if ($rpcParams['record_all'] && 'true' == $rpcParams['record_all']) {
            $return[] = [
                'ekate' => '',
                'text' => 'Всички',
            ];
        }
        $count = count($ekate_results);

        for ($i = 0; $i < $count; $i++) {
            $return[] = [
                'ekate' => $ekate_results[$i]['ekatte'],
                'text' => $ekate_results[$i]['ekatte'] . ' (' . $ekate_results[$i]['zem'] . ')',
            ];
        }

        if (!empty($rpcParams['selected'])) {
            $return[0]['selected'] = true;
        }

        return $return;
    }
}
