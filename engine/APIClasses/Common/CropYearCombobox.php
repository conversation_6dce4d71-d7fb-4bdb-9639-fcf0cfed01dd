<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;

/**
 * Crop year combobox.
 *
 * @rpc-module Common
 *
 * @rpc-service-id crop-year-combobox
 */
class CropYearCombobox extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getCropYearCombobox']],
        ];
    }

    /**
     * Return combobox data.
     *
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer year
     *                         }
     *
     * @return array
     */
    public function getCropYearCombobox($rpcParams)
    {
        $return = [];
        $year = $rpcParams['year'];
        $yearsName = ['Година I', 'Година II', 'Година III', 'Година IV', 'Година V'];
        $yearsId = [1, 2, 3, 4, 5];

        switch ($year) {
            case '4':
                $showYears = 2;

                break;
            case '5':
                $showYears = 1;

                break;
            case '6':
                $showYears = 0;

                break;
            default:
                break;
        }

        $count = count($yearsName);
        for ($i = $showYears; $i < $count; $i++) {
            $return[] = [
                'id' => $yearsId[$i],
                'name' => $yearsName[$i],
            ];
        }

        return $return;
    }
}
