<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Prado;
use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\Users\UsersController;

// Prado::using('Plugins.Core.Layers.conf');
// Prado::using('Plugins.Core.Layers.*');

/**
 * Returns all EKATTE for 'Постоянно затревени площи'.
 *
 * @rpc-module Common
 *
 * @rpc-service-id pzp-ekate-combobox
 *
 * @property UserDbController $UserDbController
 * @property UsersController $UsersController
 * @property LayersController $LayersController
 */
class PZPEkateCombobox extends TRpcApiProvider
{
    public $UserDbController = false;
    public $UsersController = false;
    public $LayersController = false;

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getPZPEkateCombobox']],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item boolean record_all
     *                         #item boolean selected
     *                         }
     *
     * @return array
     */
    public function getPZPEkateCombobox($rpcParams)
    {
        if ($this->User->isGuest) {
            return [];
        }

        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        $LayersController = new LayersController('Layers');

        $options = [
            'tablename' => 'pzp_ekatte_combobox_data',
        ];

        $return = $UsersController->getItemsByParams($options, false, false);

        if ($rpcParams['record_all'] && 'true' == $rpcParams['record_all']) {
            $all_ellement = [
                'ekate' => '',
                'text' => 'Всички',
            ];
            array_unshift($return, $all_ellement);
        }

        if (!empty($rpcParams['selected'])) {
            $return[0]['selected'] = true;
        }

        return $return;
    }
}
