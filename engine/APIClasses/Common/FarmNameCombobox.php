<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Prado;
use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Farming\FarmingController;

include_once __DIR__ . '/../../Plugins/Core/Farming/conf.php';

// Prado::using('Plugins.Core.Farming.conf');
// Prado::using('Plugins.Core.Farming.*');

/**
 * Returns all farmings, associated with the current user.
 *
 * @rpc-module Common
 *
 * @rpc-service-id farming-name-combobox
 */
class FarmNameCombobox extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getFarmNameCombobox']],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *
     * @return array the list of implemented methods
     */
    public function getFarmNameCombobox($rpcParams = '')
    {
        $FarmingController = new FarmingController('Farming');
        $farmings = $FarmingController->getUserFarmings();

        $return = [];

        if ($rpcParams['record_all'] && 'true' == $rpcParams['record_all']) {
            $return[] = [
                'id' => '',
                'title' => 'Всички',
            ];
        }

        foreach ($farmings as $farmingId => $farmingName) {
            $return[] = [
                'id' => $farmingId,
                'title' => $farmingName,
            ];
        }

        if ($rpcParams['selected'] && 'true' == $rpcParams['selected']) {
            $return[0]['selected'] = true;
        }

        return $return;
    }
}
