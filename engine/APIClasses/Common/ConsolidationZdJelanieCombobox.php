<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;

class ConsolidationZdJelanieCombobox extends TRpcApiProvider
{
    public function registerMethods(): array
    {
        return [
            'read' => ['method' => [$this, 'getConsolidationZdJelanieCombobox']],
        ];
    }

    /**
     * Код за желание за имота.
     */
    public function getConsolidationZdJelanieCombobox(): array
    {
        return [
            [
                'name' => 'Желая  да участвам с имота в масиви за ползване по чл.37в от ЗСПЗЗ',
                'id' => 1,
            ],
            [
                'name' => 'Не желая имотът да бъде включван в масиви за ползване по чл.37в от ЗСПЗЗ',
                'id' => 2,
            ],
            [
                'name' => 'Желая имотът да бъде включен в масиви за ползване като имот по чл.37б, ал3, т.2 ЗСПЗЗ - бяло  петно',
                'id' => 3,
            ],
            [
                'name' => 'Имотът е отдаден с договор за ползване от друго лице и деклараторът не изразява  желание',
                'id' => 4,
            ],
        ];
    }
}
