<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;
use TF\Application\Common\Config;

/**
 * Get Technofarm Modules.
 *
 * @rpc-module Common
 *
 * @rpc-service-id tf-modules-combobox
 */
class TFModulesCombobox extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getTechnofarmModulesCombobox']],
        ];
    }

    /**
     * @api-method read
     *
     * @return array
     */
    public function getTechnofarmModulesCombobox()
    {
        $return = [];
        $configModules = Config::$USER_RIGHTS_MAP;
        foreach ($configModules as $module) {
            if (!$module['visible']) {
                continue;
            }
            $return[] = [
                'id' => $module['id'],
                'name' => $module['name'],
            ];
        }

        return $return;
    }
}
