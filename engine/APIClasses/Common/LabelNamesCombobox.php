<?php

namespace TF\Engine\APIClasses\Common;

// Prado::using('Plugins.Core.Contracts.conf');
// Prado::using('Plugins.Core.Contracts.*');
use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Label Names.
 *
 * @rpc-module Common
 *
 * @rpc-service-id labelNames
 *
 * @deprecated the collumn names are no more thaken from this class
 */
class LabelNamesCombobox extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'read']],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *
     * @return array
     */
    public function read($rpcParams)
    {
        $usersController = new UsersController('Users');
        $labelToSelect = false;

        if (19 != $rpcParams['layer_type']) {
            return $this->labelArrayTransform($GLOBALS['Layers']['labelNames'][$rpcParams['layer_type']], $labelToSelect);
        }
        if ($rpcParams['selected']) {
            $options = [
                'tablename' => 'su_users_layers',
                'return' => ['label_name', 'layer_type', 'table_name', 'definitions'],
                'where' => [
                    'id' => ['column' => 'id', 'compare' => '=', 'value' => $rpcParams['layer_id']],
                ],
            ];
            $selected = $usersController->getItemsByParams($options);
            $labelToSelect = $selected[0]['label_name'];
            $layerType = $selected[0]['layer_type'];
            $table_name = $selected[0]['table_name'];
            $definitions = [];
            if (!empty($selected[0]['definitions'])) {
                foreach (json_decode($selected[0]['definitions'], true) as $def) {
                    $definitions[$def['col_name']] = $def;
                }
            }
        }

        return $this->getLabelItemsForWorkLayer($table_name, $labelToSelect, $definitions);
    }

    private function labelArrayTransform($labelArray, $labelToSelect)
    {
        $transformedArray = [];
        foreach ($labelArray as $key => $value) {
            $transformedArray[] = [
                'key' => $key,
                'name' => $value,
                'selected' => ($key == $labelToSelect) ? true : false,
            ];
        }

        return $transformedArray;
    }

    private function getLabelItemsForWorkLayer($table_name, $labelToSelect, $definitions)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $options = [
            'tablename' => 'information_schema.columns',
            'return' => ['column_name'],
            'where' => [
                'table_name' => ['column' => 'table_name', 'compare' => '=', 'value' => $table_name],
            ],
        ];
        $results = $UserDbController->getItemsByParams($options, false, false);
        foreach ($results as $key => $value) {
            if ('gid' != $value['column_name'] && 'geom' != $value['column_name']) {
                $transformedArray[] = [
                    'key' => $value['column_name'],
                    'name' => (array_key_exists($value['column_name'], $definitions) ? $definitions[$value['column_name']]['col_title'] : $value['column_name']),
                    'selected' => ($value['column_name'] == $labelToSelect) ? true : false,
                ];
            }
        }

        $transformedArray[] = [
            'key' => 'area',
            'name' => (array_key_exists('area', $definitions) ? $definitions['area']['col_title'] : 'Площ (дка)'),
            'selected' => ('area' == $labelToSelect) ? true : false,
        ];

        return $transformedArray;
    }
}
