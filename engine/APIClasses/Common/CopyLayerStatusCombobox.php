<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;

/**
 * Return the status of layer copy operation as text.
 *
 * @rpc-module Common
 *
 * @rpc-service-id copy-layer-status-combobox
 */
class CopyLayerStatusCombobox extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getCopyLayerStatusCombobox']],
        ];
    }

    /**
     * @api-method read
     *
     * @return array
     */
    public function getCopyLayerStatusCombobox()
    {
        $result = [
            0 => ['id' => 0, 'value' => 'Обработва се'],
            1 => ['id' => 1, 'value' => 'Успешно обработен'],
            2 => ['id' => 2, 'value' => 'Ненамерен S<PERSON> файл'],
            3 => ['id' => 3, 'value' => 'Ненамерен DBF файл'],
            4 => ['id' => 4, 'value' => 'Невалиден архив'],
            5 => ['id' => 5, 'value' => 'Невалиднa геометрия на полигон.'],
            6 => ['id' => 6, 'value' => 'Невалиден ИСАК файл'],
            7 => ['id' => 7, 'value' => 'Системна грешка при обработката'],
            8 => ['id' => 8, 'value' => 'Невалидна атрибутна информация'],
            9 => ['id' => 9, 'value' => 'Пресичане със съществуващи обекти'],
            10 => ['id' => 10, 'value' => '-'],
        ];

        return array_reverse($result);
    }
}
