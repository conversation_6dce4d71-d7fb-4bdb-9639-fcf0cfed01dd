<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController;

/**
 * Returns all information about the selected plot.
 *
 * @rpc-module Common
 *
 * @rpc-service-id plot-info
 */
class PlotInfo extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getPlotInfo']],
        ];
    }

    /**
     * @rpc-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer plot_id
     *                         }
     *
     * @return array
     */
    public function getPlotInfo($rpcParams)
    {
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);

        $options = [
            'tablename' => 'layer_kvs',
            'return' => [
                'kad_ident', 'ekate', 'masiv', 'number', 'COALESCE(virtual_category_title, \'-\') as category', 'virtual_ntp_title as area_type', 'mestnost', 'include', 'participate', 'white_spots', 'ST_Area(geom) as area',
            ],
            'where' => [
                'gid' => ['column' => 'gid', 'compare' => '=', 'value' => $rpcParams['plot_id']],
            ],
        ];
        $result = $UserDbPlotsController->getPlotData($options);
        $data = $result[0];
        $data['area'] = number_format($data['area'] / 1000, 3);

        return $data;
    }
}
