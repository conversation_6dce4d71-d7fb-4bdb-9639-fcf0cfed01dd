<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbContracts\UserDbContractsController;

/**
 * Информация за договор
 *
 * @rpc-module Common
 *
 * @rpc-service-id contract-info
 */
class ContractInfo extends TRpcApiProvider
{
    public $FarmingController = false;
    public $UserDbController = false;
    public $UserDbContractsController = false;

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getContractInfo']],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer id
     *                         }
     *
     * @return array
     */
    public function getContractInfo($rpcParams)
    {
        if ($this->User->isGuest) {
            return [];
        }

        $FarmingController = new FarmingController('Farming');
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbContractsController = new UserDbContractsController($this->User->Database);

        $options = [
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $rpcParams['id']],
            ],
        ];

        $result = $UserDbContractsController->getContractsData($options);
        $data = $result[0];

        $farming_result = $FarmingController->getFarmingItemsByIDString($id_string = $data['farming_id'], $this->User->GroupID);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableRentaTypes,
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $result[0]['renta_nat_type_id']],
            ],
        ];

        $renta_results = $UserDbController->getItemsByParams($options, false, false);
        $rentaNatInfo = $renta_results[0];

        // converting data into the required user friendly format
        $return = [
            'farming' => $farming_result[0]['name'],
            'c_type' => $GLOBALS['Contracts']['ContractTypes'][$data['nm_usage_rights']]['name'],
            'c_num' => $data['c_num'],
            'c_date' => strftime('%d.%m.%Y', strtotime($data['c_date'])),
            'start_date' => strftime('%d.%m.%Y', strtotime($data['start_date'])),
            'due_date' => ($data['due_date']) ? strftime('%d.%m.%Y', strtotime($data['due_date'])) : '-',
            'sv_date' => ($data['sv_date']) ? strftime('%d.%m.%Y', strtotime($data['sv_date'])) : '-',
            'sv_num' => ($data['sv_num']) ? $data['sv_num'] : '-',
            'ds_type' => ($data['ds_type']) ? $GLOBALS['Contracts']['DSTypes'][$data['ds_type']]['name'] : '-',
            'ds_num_date' => ($data['ds_num_date']) ? $data['ds_num_date'] : '-',
            'renta' => ('' == $data['renta']) ? '-' : $data['renta'],
            'renta_nat_type' => $rentaNatInfo['name'] . '(' . $GLOBALS['Contracts']['renta_units'][$rentaNatInfo['unit']]['name'] . ')',
            'renta_nat' => ('' == $data['renta_nat']) ? '-' : $data['renta_nat'],
            'cont_name' => ($data['contragent_name']) ? $data['contragent_name'] : '-',
            'cont_number' => ($data['contragent_number']) ? $data['contragent_number'] : '-',
            'cont_land' => ($data['contragent_land']) ? $data['contragent_land'] : '-',
            'cont_address' => ($data['contragent_address']) ? $data['contragent_address'] : '-',
        ];

        return $return;
    }
}
