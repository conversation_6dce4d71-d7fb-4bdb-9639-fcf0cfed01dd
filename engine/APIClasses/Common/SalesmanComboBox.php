<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;
use Prado\Web\Services\TRpcServer;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Salesman list.
 *
 * @rpc-module Common
 *
 * @rpc-service-id salesman-list
 */
class SalesmanComboBox extends TRpcApiProvider
{
    /**
     * @var UsersController
     */
    private $UsersController;

    public function __construct(TRpcServer $rpcServer)
    {
        parent::__construct($rpcServer);
        $this->UsersController = new UsersController('Users');
    }

    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getSalesmanList']],
        ];
    }

    /**
     * Returns salesman list.
     *
     * @api-module read
     *
     * @return array {
     *               #item integer id
     *               #item string name
     *               }
     */
    public function getSalesmanList()
    {
        return $this->UsersController->getSalesPersons();
    }
}
