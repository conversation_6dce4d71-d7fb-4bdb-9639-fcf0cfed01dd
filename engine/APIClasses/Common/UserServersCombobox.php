<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;

/**
 * Лист с възможни сървъри.
 *
 * @rpc-module Common
 *
 * @rpc-service-id user-server-combobox
 */
class UserServersCombobox extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getUserServersCombobox']],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item boolean selected
     *                         }
     *
     * @return array
     */
    public function getUserServersCombobox($rpcParams = null)
    {
        if ($this->User->isGuest || !$this->User->isSuperAdmin) {
            return [];
        }

        $return = [];

        foreach ($GLOBALS['Users']['servers'] as $item) {
            $return[] = $item;
        }

        if ($rpcParams['selected'] && 'true' == $rpcParams['selected']) {
            $return[0]['selected'] = true;
        }

        return $return;
    }
}
