<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;

// Prado::using('Plugins.Core.Farming.conf');

/**
 * [description].
 *
 * @rpc-module Common
 *
 * @rpc-service-id soil-humus-combobox
 */
class SoilHumusCombobox extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getSoilHumusCombobox']],
        ];
    }

    /**
     * Get combobox data.
     *
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item boolean selected
     *                         }
     *
     * @return array
     */
    public function getSoilHumusCombobox($rpcParams)
    {
        if ($this->User->isGuest) {
            return [];
        }

        foreach ($GLOBALS['Farming']['sastav_humus'] as $item) {
            $return[] = $item;
        }

        if ($rpcParams['selected'] && 'true' == $rpcParams['selected']) {
            $return[0]['selected'] = true;
        }

        return $return;
    }
}
