<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;

/**
 * Get all available owner types and display them as text.
 *
 * @rpc-module Common
 *
 * @rpc-service-id owner-types-combobox
 */
class OwnerTypesCombobox extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getOwnerTypes']],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $params
     *
     * @return array the list of implemented methods
     */
    public function getOwnerTypes($params = [])
    {
        if ($this->User->isGuest) {
            return [];
        }

        $options = [
            [
                'id' => '',
                'name' => 'Всички',
            ],
            [
                'id' => '0',
                'name' => 'Юридически лица',
            ],
            [
                'id' => '1',
                'name' => 'Физически лица',
            ],
        ];

        if (isset($params['selected'])) {
            foreach ($options as &$option) {
                if ($params['selected'] == $option['id']) {
                    $option['selected'] = true;
                }
            }
        }

        return $options;
    }
}
