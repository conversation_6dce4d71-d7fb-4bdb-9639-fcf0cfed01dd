<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Prado;
use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDbPlotCategoriesType\UserDbPlotCategoriesTypeController;

// Prado::using('Plugins.Core.Farming.conf');
// Prado::using('Plugins.Core.Farming.*');
// Prado::using('Plugins.Core.Layers.conf');
// Prado::using('Plugins.Core.Layers.*');

/**
 * Get all available plot categories and display them as text.
 *
 * @rpc-module Common
 *
 * @rpc-service-id plot-category-combobox
 */
class PlotCategoryCombobox extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getPlotCategory']],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $filterObj
     *
     * @return array the list of implemented methods
     */
    public function getPlotCategory($filterObj = [])
    {
        if ($this->User->isGuest) {
            return [];
        }

        $return = [];

        if (isset($filterObj['record_all']) && true == $filterObj['record_all']) {
            $return[] = [
                'id' => '',
                'name' => 'Всички',
            ];
        }

        if (isset($filterObj['without_cat_ntp']) && true == $filterObj['without_cat_ntp']) {
            $return[] = [
                'id' => '-1',
                'name' => 'Без категория',
            ];
        }

        $UserDbPlotCategoriesTypeController = new UserDbPlotCategoriesTypeController($this->User->Database);
        $return = array_merge($return, $UserDbPlotCategoriesTypeController->getPlotCategories());

        if (isset($filterObj['selected']) && true == $filterObj['selected']) {
            $return[0]['selected'] = true;
        }

        return $return;
    }
}
