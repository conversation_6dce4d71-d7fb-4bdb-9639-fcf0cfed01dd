<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Deals with information about saved payment subjects.
 *
 * @rpc-module Common
 *
 * @rpc-service-id payment-subjects-grid
 */
class PaymentSubjectsGrid extends TRpcApiProvider
{
    private $module = 'Common';
    private $service_id = 'payment-subjects-grid';

    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getPaymentSubjectsGrid']],
            'add' => ['method' => [$this, 'addPaymentSubject'],
                'validators' => [
                    'rpcParams' => [
                        'name' => 'validateRequired, validateText',
                        'fulltext' => 'validateRequired',
                    ],
                ],
            ],
            'markForEdit' => ['method' => [$this, 'markForEdit'],
                'validators' => [
                    'id' => 'validateRequired, validateInteger',
                ],
            ],
            'edit' => ['method' => [$this, 'editPaymentSubject'],
                'validators' => [
                    'rpcParams' => [
                        'id' => 'validateRequired, validateInteger',
                        'name' => 'validateRequired, validateText',
                        'fulltext' => 'validateRequired, validateText',
                    ],
                ],
            ],
            'delete' => ['method' => [$this, 'deletePaymentSubject'],
                'validators' => [
                    'id' => 'validateRequired, validateInteger',
                ],
            ],
        ];
    }

    /**
     * @api-method read
     *
     * @return array
     *               {
     *               #item integer id
     *               #item string name
     *               #item string fulltext
     *               }
     */
    public function getPaymentSubjectsGrid()
    {
        // init controllers
        $UserDbController = new UserDbController($this->User->Database);

        $return = [];

        $options = [
            'tablename' => $UserDbController->DbHandler->tablePaymentSubjects,
            'sort' => 'id',
            'order' => 'asc',
        ];

        $results = $UserDbController->getItemsByParams($options);

        $return['rows'] = $results;
        $return['total'] = count($results);

        return $return;
    }

    /**
     * @api-method add
     *
     * @param array $rpcParams
     *                         {
     *                         #item string name|required
     *                         #item string fulltext|required
     *                         }
     *
     * @return int id
     */
    public function addPaymentSubject($rpcParams)
    {
        // init controllers
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $options = [
            'tablename' => $UserDbController->DbHandler->tablePaymentSubjects,
            'mainData' => [
                'name' => trim($rpcParams['name']),
                'fulltext' => trim($rpcParams['fulltext']),
            ],
        ];

        $recordID = $UserDbController->addItem($options);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['new_values' => $options], ['created_id' => $recordID], 'Adding payment subject');

        return $recordID;
    }

    /**
     * @api-method edt
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer id|required
     *                         #item string name|required
     *                         #item string fulltext|required
     *                         }
     *
     * @return array
     *               {
     *               #item int id
     *               }
     */
    public function editPaymentSubject($rpcParams)
    {
        // init controllers
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $options = [
            'tablename' => $UserDbController->DbHandler->tablePaymentSubjects,
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $rpcParams['id']],
            ],
        ];

        $old_data = $UserDbController->getItemsByParams($options);

        $options = [
            'tablename' => $UserDbController->DbHandler->tablePaymentSubjects,
            'mainData' => [
                'name' => trim($rpcParams['name']),
                'fulltext' => trim($rpcParams['fulltext']),
            ],
            'where' => [
                'id' => $rpcParams['id'],
            ],
        ];

        $UserDbController->editItem($options);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['new_values' => $options], ['old_values' => $old_data[0]], 'Editing payment subject');

        return $rpcParams['id'];
    }

    /**
     * @api-method markForEdit
     *
     * @param int $rpcParam
     *
     * @return array
     *               [
     *               array
     *               [
     *               #item integer  id
     *               #item string   name
     *               #item string   fulltext
     *               ]
     *               ]
     */
    public function markForEdit($rpcParam)
    {
        // init controllers
        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'tablename' => $UserDbController->DbHandler->tablePaymentSubjects,
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $rpcParam],
            ],
        ];

        return $UserDbController->getItemsByParams($options);
    }

    /**
     * @api-method delete
     *
     * @param array $rpcParam
     *                        {
     *                        #item integer id
     *                        }
     */
    public function deletePaymentSubject($rpcParam)
    {
        // init controllers
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $options = [
            'tablename' => $UserDbController->DbHandler->tablePaymentSubjects,
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $rpcParam],
            ],
        ];

        $oldData = $UserDbController->getItemsByParams($options);

        $options = [
            'tablename' => $UserDbController->DbHandler->tablePaymentSubjects,
            'id_string' => $rpcParam,
        ];
        $UserDbController->deleteItemsByParams($options);
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $oldData, [], 'deleting payment subject');
    }
}
