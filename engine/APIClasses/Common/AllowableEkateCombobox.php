<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * ЕКАТТЕ в допустим слой.
 *
 * @rpc-module Common
 *
 * @rpc-service-id allowable-ekate-combobox
 *
 * @property UserDbController $UserDbController
 * @property UsersController $UsersController
 * @property LayersController $LayersController
 */
class AllowableEkateCombobox extends TRpcApiProvider
{
    public $UserDbController = false;
    public $UsersController = false;
    public $LayersController = false;

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getAllowableEkateCombobox']],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item boolean record_all
     *                         #item boolean selected
     *                         }
     *
     * @return array
     */
    public function getAllowableEkateCombobox($rpcParams)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        $LayersController = new LayersController('Layers');

        $options = [
            'tablename' => 'allowable_ekatte_combobox_data',
        ];

        $ekate_results = $UsersController->getItemsByParams($options);

        if ($rpcParams['record_all'] && 'true' == $rpcParams['record_all']) {
            $all_elements = [
                'ekate' => '',
                'text' => 'Всички',
            ];
            array_unshift($ekate_results, $all_elements);
        }

        if (!empty($rpcParams['selected'])) {
            $ekate_results[0]['selected'] = true;
        }

        return $ekate_results;
    }
}
