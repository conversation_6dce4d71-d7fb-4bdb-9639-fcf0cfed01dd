<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Prado;
use Prado\Web\Services\TRpcApiProvider;

include_once __DIR__ . '/../../Plugins/Core/Farming/conf.php';

// Prado::using('Plugins.Core.Farming.conf');
// Prado::using('Plugins.Core.Farming.*');

/**
 * Return all cultures as text.
 *
 * @rpc-module Common
 *
 * @rpc-service-id culture-combobox
 */
class CultureCombobox extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @api common-rpc
     *
     * @module-rpc ekate-combobox
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getCultures']],
        ];
    }

    /**
     * Get all cultures.
     *
     * @api-method read
     *
     * @param array $filterObj
     *                         {
     *                         #item boolean priority_first
     *                         #item year year
     *                         #item boolean crop_rotation
     *                         #item boolean selected
     *                         #item boolean without_crops
     *                         }
     *
     * @return array
     */
    public function getCultures($filterObj = null)
    {
        $return = [];
        if (!empty($filterObj['priority_first'])) {
            usort($GLOBALS['Farming']['crops'], function ($itemA, $itemB) {
                return ($itemA['priority'] > $itemB['priority']) ? -1 : 1;
            });
        }

        if (!empty($filterObj['year']) && $filterObj['year'] >= $GLOBALS['Farming']['years'][6]['id']) {
            foreach ($GLOBALS['Farming']['crops'] as $item) {
                if ($item['is_active']) {
                    $return[] = [
                        'id' => $item['crop_code'],
                        'name' => $item['crop_name'],
                    ];
                }
            }
        } else {
            foreach ($GLOBALS['Farming']['crops'] as $item) {
                $return[] = [
                    'id' => $item['crop_code'],
                    'name' => $item['crop_name'],
                ];
            }
        }

        // check crop for crop_type=obrabotvaemazemya
        if (!empty($filterObj['year']) && in_array($filterObj['year'], [4, 5])) {
            if ('obrabotvaemazemya' == $filterObj['crop_type']) {
                $return = [];

                foreach ($GLOBALS['Farming']['crops'] as $item) {
                    if ('Обработваема земя' == $item['crop_type']) {
                        $return[] = [
                            'id' => $item['crop_code'],
                            'name' => $item['crop_name'],
                        ];
                    }
                }
            }
        }

        // check for crop rotation new crop
        if (!empty($filterObj['crop_rotation'])) {
            $return = [];

            foreach ($GLOBALS['Farming']['crops'] as $item) {
                if (true == $item['crop_rotation']) {
                    $return[] = [
                        'id' => $item['crop_code'],
                        'name' => $item['crop_name'],
                    ];
                }
            }
        }

        if (!empty($filterObj['selected']) && 'true' == $filterObj['selected']) {
            $return[0]['selected'] = 'true';
        }
        if (!empty($filterObj['without_crops'])) {
            $withoutCrops = [
                'id' => 'null',
                'name' => 'Без култура',
            ];

            array_unshift($return, $withoutCrops);
        }

        return $return;
    }
}
