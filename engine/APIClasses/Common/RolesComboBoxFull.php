<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;
use Prado\Web\Services\TRpcServer;
use TF\Application\Common\Config;

/**
 * Roles list.
 *
 * @rpc-module Common
 *
 * @rpc-service-id roles-list-full
 */
class RolesComboBoxFull extends TRpcApiProvider
{
    public function __construct(TRpcServer $rpcServer)
    {
        parent::__construct($rpcServer);
    }

    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getRolesList']],
        ];
    }

    /**
     * Returns roles list.
     *
     * @api-module read
     *
     * @return array {
     *               #item integer id
     *               #item string name
     *               }
     */
    public function getRolesList()
    {
        $roles = [];
        $roles[] = ['id' => '', 'name' => 'Всички', 'selected' => true];
        $roles[] = ['id' => Config::USERS_SUPPORT_FLAG, 'name' => $GLOBALS['Users']['account_types'][Config::USERS_SUPPORT_FLAG]['name'], 'selected' => false];
        $roles[] = ['id' => Config::USERS_ADMIN_FLAG, 'name' => $GLOBALS['Users']['account_types'][Config::USERS_ADMIN_FLAG]['name'], 'selected' => false];
        $roles[] = ['id' => Config::USERS_SUPER_ADMIN_FLAG, 'name' => $GLOBALS['Users']['account_types'][Config::USERS_SUPER_ADMIN_FLAG]['name'], 'selected' => false];
        $roles[] = ['id' => Config::USERS_SALES_FLAG, 'name' => $GLOBALS['Users']['account_types'][Config::USERS_SALES_FLAG]['name'], 'selected' => false];

        return $roles;
    }
}
