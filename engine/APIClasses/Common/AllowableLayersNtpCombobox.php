<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Layers\LayersController;

/**
 * @rpc-module Common
 *
 * @rpc-service-id allowable-layers-ntp-combobox
 */
class AllowableLayersNtpCombobox extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getAllowableLayersNtpCombobox']],
        ];
    }

    public function getAllowableLayersNtpCombobox()
    {
        $LayersController = new LayersController('Layers');

        return $LayersController->getAllowableLayersNtpData();
    }
}
