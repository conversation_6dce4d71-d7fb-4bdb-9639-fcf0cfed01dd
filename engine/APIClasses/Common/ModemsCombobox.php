<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Prado;
use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Users\UsersController;

// Prado::using('Plugins.Core.UserDb.*');
// Prado::using('Plugins.Core.UserDb.conf');
// Prado::using('Plugins.Core.Users.*');
// Prado::using('Plugins.Core.Users.conf');

class ModemsCombobox extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getModemsCombobox']],
        ];
    }

    public function getModemsCombobox()
    {
        $UsersController = new UsersController('Users');
        $userId = $this->User->UserID;
        $modems = $UsersController->getUserModems([
            'return' => ['*'],
            'where' => [
                'user_id' => ['column' => 'user_id', 'compare' => '=', 'value' => $userId],
            ],
        ]);

        return array_map(function ($modem) {
            return ['name' => $modem['machine'], 'value' => $modem['id']];
        }, $modems);
    }
}
