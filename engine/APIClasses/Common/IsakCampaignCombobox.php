<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

/**
 * Return all ekattes for certain table as text.
 *
 * @rpc-module Common
 *
 * @rpc-service-id isak-campaign-combobox
 */
class IsakCampaignCombobox extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @api common-rpc
     *
     * @module-rpc isak-ekate-combobox
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getIsakEkatte']],
        ];
    }

    /**
     * Get all cultures.
     *
     * @api-method read
     *
     * @param array $filterObj
     *                         {
     *                         #item boolean priority_first
     *                         #item year year
     *                         #item boolean crop_rotation
     *                         #item boolean selected
     *                         #item boolean without_crops
     *                         }
     *
     * @return array
     */
    public function getIsakEkatte($filterObj = null)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $options = [
            'tablename' => $filterObj['layer_name'],
            'return' => [
                'DISTINCT(campaign)',
            ],
        ];
        $results = $UserDbController->getItemsByParams($options, false, false);

        if (0 == count($results)) {
            return [];
        }

        return $results;
    }
}
