<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDbPayments\UserDbPaymentsController;

/**
 * Payer Names Combobox.
 *
 * @rpc-module Common
 *
 * @rpc-service-id payer-names-combobox
 */
class PayerNamesCombobox extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getPayerNamesCombobox']],
        ];
    }

    /**
     * get payer names combobox.
     *
     * @api-method read
     *
     * @return array $payers - all names of payers
     */
    public function getPayerNamesCombobox()
    {
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);

        $options = [
            'custom_counter' => 'count(DISTINCT(t.id))',
            'return' => [
                'DISTINCT(t.payer_name)',
            ],
        ];

        return $UserDbPaymentsController->getTransactionsByParams($options, false, false);
    }
}
