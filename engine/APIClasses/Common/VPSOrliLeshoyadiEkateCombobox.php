<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Returns all information about "ВСП Царски орел и Египетски лешояд" layer.
 *
 * @rpc-module Common
 *
 * @rpc-service-id orli-leshoyadi-ekate-combobox
 */
class VPSOrliLeshoyadiEkateCombobox extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getVPSOrliLeshoyadiEkateCombobox']],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item boolean record_all
     *                         #item boolean selected
     *                         }
     *
     * @return array
     */
    public function getVPSOrliLeshoyadiEkateCombobox($rpcParams)
    {
        if ($this->User->isGuest) {
            return [];
        }

        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        $LayersController = new LayersController('Layers');

        $options = [
            'tablename' => 'layer_vps_orli_leshoyadi',
            'return' => [
                'ekatte', ' ekatte_name',
            ],
            'group' => 'ekatte, ekatte_name',
        ];

        $hash = md5(json_encode($options));
        $ekate_results = $LayersController->MemCache->get($hash);
        if (!$ekate_results) {
            $ekate_results = $LayersController->getRemoteLayerOrliLeshoyadiData($options, false, false);
            $LayersController->MemCache->add($hash, $ekate_results, $LayersController->default_memcache_expire);
        }
        if ($rpcParams['record_all'] && 'true' == $rpcParams['record_all']) {
            $return[] = [
                'ekate' => '',
                'text' => 'Всички',
            ];
        }
        $count = count($ekate_results);
        $return = [];
        for ($i = 0; $i < $count; $i++) {
            $return[] = [
                'ekate' => $ekate_results[$i]['ekatte'],
                'text' => $ekate_results[$i]['ekatte'] . ' (' . $ekate_results[$i]['ekatte_name'] . ')',
            ];
        }

        if (!empty($rpcParams['selected'])) {
            $return[0]['selected'] = true;
        }

        return $return;
    }
}
