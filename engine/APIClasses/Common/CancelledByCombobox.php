<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDbPayments\UserDbPaymentsController;

// Prado::using('Plugins.Core.UserDbPayments.*');
// Prado::using('Plugins.Core.Layers.*');
// Prado::using('Plugins.Core.Layers.conf');

/**
 * Payer Names Combobox.
 *
 * @rpc-module Common
 *
 * @rpc-service-id cancelled-by-combobox
 */
class CancelledByCombobox extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getCancelledByCombobox']],
        ];
    }

    /**
     * get payer names combobox.
     *
     * @api-method read
     *
     * @return array $payers - all names of payers
     */
    public function getCancelledByCombobox()
    {
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);

        $options = [
            'tablename' => $UserDbPaymentsController->DbHandler->tableTransactions,
            'return' => [
                'cancelled_by',
            ],
            'status' => ['column' => 'status', 'compare' => '=', 'value' => 'cancelled'],
            'group' => 'cancelled_by',
        ];

        $names = $UserDbPaymentsController->getItemsByParams($options, false, false);

        return array_values(array_filter($names, function ($item) {
            return $item['cancelled_by'];
        }));
    }
}
