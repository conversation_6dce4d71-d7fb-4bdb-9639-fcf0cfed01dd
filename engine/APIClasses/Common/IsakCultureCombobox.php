<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

/**
 * Return all cultures for a certain table as text.
 *
 * @rpc-module Common
 *
 * @rpc-service-id isak-culture-combobox
 */
class Isak<PERSON>ulture<PERSON>ombobox extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @api common-rpc
     *
     * @module-rpc ekate-combobox
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getCultures']],
        ];
    }

    /**
     * Get all cultures.
     *
     * @api-method read
     *
     * @param array $filterObj
     *                         {
     *                         #item boolean priority_first
     *                         #item year year
     *                         #item boolean crop_rotation
     *                         #item boolean selected
     *                         #item boolean without_crops
     *                         }
     *
     * @return array
     */
    public function getCultures($filterObj = null)
    {
        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'tablename' => $filterObj['layer_name'],
            'return' => [
                'DISTINCT(cropcode)',
                'cropname',
            ],
        ];
        $results = $UserDbController->getItemsByParams($options, false, false);

        if (0 == count($results)) {
            return [];
        }

        return $results;
    }
}
