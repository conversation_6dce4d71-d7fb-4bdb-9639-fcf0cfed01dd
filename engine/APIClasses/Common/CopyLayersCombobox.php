<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;

// Prado::using('Plugins.Core.Layers.conf');
// Prado::using('Plugins.Core.Layers.*');
// Prado::using('Plugins.Core.Farming.conf');
// Prado::using('Plugins.Core.Farming.*');

/**
 * Get layer type names and return them as text.
 *
 * @rpc-module Common
 *
 * @rpc-service-id copy-layer-combobox
 */
class CopyLayersCombobox extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getCopyLayersCombobox']],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item string type
     *                         }
     *
     * @return array
     */
    public function getCopyLayersCombobox($rpcParams)
    {
        $return = [];

        foreach ($GLOBALS['Layers']['srid'] as $item) {
            $type = explode(',', $rpcParams['type']);
            if ($type && in_array($item['type'], $type)) {
                $return[] = [
                    'id' => $item['type'],
                    'name' => $item['title'],
                ];
            }
        }

        $return[0]['selected'] = true;

        return $return;
    }
}
