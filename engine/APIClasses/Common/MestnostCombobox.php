<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Prado;
use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

include_once __DIR__ . '/../../Plugins/Core/UserDb/conf.php';

// Prado::using('Plugins.Core.UserDb.*');
// Prado::using('Plugins.Core.UserDb.conf');
// Prado::using('Plugins.Core.Users.*');
// Prado::using('Plugins.Core.UserDbPlots.*');

/**
 * Gets 'mestnost' from layer_kvs as combobox.
 *
 * @rpc-module Common
 *
 * @rpc-service-id mestnost-combobox
 */
class MestnostCombobox extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getMestnostCombobox']],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item array ekattes
     *                         #item bool without_mestnost
     *                         #item bool record_all
     *                         }
     *
     * @return array the list of implemented methods
     */
    public function getMestnostCombobox($rpcParams = [])
    {
        if ($this->User->isGuest) {
            return [];
        }

        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'return' => ['DISTINCT(mestnost)'],
            'tablename' => $UserDbController->DbHandler->tableKVS,
            'sort' => 'mestnost',
            'order' => 'asc',
        ];

        if (isset($rpcParams['ekates']) && !empty($rpcParams['ekates'])) {
            $options['where']['ekate'] = ['column' => 'ekate', 'compare' => 'IN', 'value' => $rpcParams['ekates']];
        }

        $results = $UserDbController->getItemsByParams($options);

        if (!empty($rpcParams['record_all']) && 'true' == $rpcParams['record_all']) {
            $return[] = [
                'mestnost' => '',
                'text' => 'Всички',
            ];
        }

        if (!empty($rpcParams['without_mestnost'])) {
            $return[] = [
                'mestnost' => 'null',
                'text' => 'Без местност',
            ];
        }

        $count = count($results);
        for ($i = 0; $i < $count; $i++) {
            if ('' == $results[$i]['mestnost'] || null == $results[$i]['mestnost']) {
                continue;
            }
            $return[] = [
                'mestnost' => $results[$i]['mestnost'],
                'text' => $results[$i]['mestnost'],
            ];
        }

        if (!empty($rpcParams['selected'])) {
            $return[0]['selected'] = true;
        }

        return $return;
    }
}
