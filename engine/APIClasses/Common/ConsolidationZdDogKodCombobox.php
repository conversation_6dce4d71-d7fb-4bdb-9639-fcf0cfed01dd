<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;

class ConsolidationZdDogKodCombobox extends TRpcApiProvider
{
    public function registerMethods(): array
    {
        return [
            'read' => ['method' => [$this, 'getConsolidationZdDogKodCombobox']],
        ];
    }

    /**
     * Код за вид документ за наем/аренда.
     */
    public function getConsolidationZdDogKodCombobox(): array
    {
        return [
            [
                'name' => 'Неизвестен договор',
                'id' => 0,
            ],
            [
                'name' => 'Договор за аренда',
                'id' => 1,
            ],
            [
                'name' => 'Договор за наем',
                'id' => 2,
            ],
            [
                'name' => 'Договор за съвместна обработка по чл.31, ал.4, т.3 от ЗК',
                'id' => 3,
            ],
            [
                'name' => 'Договор за преарендоване',
                'id' => 4,
            ],
            [
                'name' => 'Договор за пренаемане',
                'id' => 5,
            ],
            [
                'name' => 'Договор за концесия',
                'id' => 6,
            ],
            [
                'name' => 'Договор за лизинг',
                'id' => 7,
            ],
        ];
    }
}
