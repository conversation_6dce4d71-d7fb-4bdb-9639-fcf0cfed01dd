<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;

/**
 * Get all contract statuses and display them as text.
 *
 * @rpc-module Common
 *
 * @rpc-service-id contract-status-combobox
 */
class ContractStatusCombobox extends TRpcApiProvider
{
    public const CONTRACT_STATUS_ANY = '';
    public const CONTRACT_STATUS_CANCELLED = 'Canceled';
    public const CONTRACT_STATUS_ACTIVE = 'Active';
    public const CONTRACT_STATUS_EXPIRED = 'Expired';

    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getContractStatus']],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $filterObj
     *
     * @return array
     */
    public function getContractStatus($filterObj = [])
    {
        if ($this->User->isGuest) {
            return [];
        }

        $statusData['ContractStatus'] = [
            1 => ['id' => self::CONTRACT_STATUS_CANCELLED, 'name' => 'Анулиран'],
            2 => ['id' => self::CONTRACT_STATUS_ACTIVE, 'name' => 'Действащ'],
            3 => ['id' => self::CONTRACT_STATUS_EXPIRED, 'name' => 'Изтекъл'],
        ];

        $return = [];

        if (isset($filterObj['record_all']) && true == $filterObj['record_all']) {
            $return[] = [
                'id' => self::CONTRACT_STATUS_ANY,
                'name' => 'Всички',
            ];
        }

        foreach ($statusData['ContractStatus'] as $item) {
            $return[] = $item;
        }

        return $return;
    }
}
