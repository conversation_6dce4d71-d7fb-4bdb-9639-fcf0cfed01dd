<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;

/**
 * Transaction Types Combobox.
 *
 * @rpc-module Common
 *
 * @rpc-service-id transaction-types-combobox
 */
class TransactionTypesCombobox extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getTransactionTypesCombobox']],
        ];
    }

    /**
     * get Transaction Types Combobox.
     *
     * @api-method read
     *
     * @return array $return
     *               {
     *               #item string id      - id of transaction type
     *               #item string name    - name of transaction type
     *               }
     */
    public function getTransactionTypesCombobox()
    {
        $return = [];

        foreach ($GLOBALS['Payments']['types'] as $key => $item) {
            $return[] = [
                'id' => $key,
                'name' => $item['payment_type_text'],
            ];
        }

        return $return;
    }
}
