<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;

include_once __DIR__ . '/../../Plugins/Core/Contracts/conf.php';

/**
 * Променливи в Шаблони за договори.
 *
 * @rpc-module Common
 *
 * @rpc-service-id contract-template-variables
 */
class ContractTemplateVariables extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readContractTemplateVariables']],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *
     * @return array
     */
    public function readContractTemplateVariables($rpcParams)
    {
        if ($this->User->isGuest) {
            return [];
        }

        $return = [];

        foreach ($GLOBALS['Contracts']['template_variables'] as $item) {
            $return[] = $item;
        }

        if ($rpcParams['selected'] && 'true' == $rpcParams['selected']) {
            $return[0]['selected'] = true;
        }

        return $return;
    }
}
