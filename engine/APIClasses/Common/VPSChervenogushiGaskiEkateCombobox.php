<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Returns all EKATTE information for the "ВПС Червеногуши гъски" layer.
 *
 * @rpc-module Common
 *
 * @rpc-service-id chervenogushi-ekate-combobox
 *
 * @property UserDbController $UserDbController
 * @property UsersController $UsersController
 * @property LayersController $LayersController
 */
class VPSChervenogushiGaskiEkateCombobox extends TRpcApiProvider
{
    public $UserDbController = false;
    public $UsersController = false;
    public $LayersController = false;

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getVPSChervenogushiGaskiEkateCombobox']],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item boolean record_all
     *                         #item boolean selected
     *                         }
     *
     * @return array
     */
    public function getVPSChervenogushiGaskiEkateCombobox($rpcParams)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        $LayersController = new LayersController('Layers');
        $return = [];
        $options = [
            'tablename' => 'layer_vps_gaski_chervenogushi',
            'return' => [
                'ekatte', ' name',
            ],
            'group' => 'ekatte, name',
        ];

        $hash = md5(json_encode($options));
        if ($ekate_results = $LayersController->MemCache->get($hash)) {
        } else {
            $ekate_results = $LayersController->getRemoteLayerData($options, false, false);
            $LayersController->MemCache->add($hash, $ekate_results, $LayersController->default_memcache_expire);
        }
        if ($rpcParams['record_all'] && 'true' == $rpcParams['record_all']) {
            $return[] = [
                'ekate' => '',
                'text' => 'Всички',
            ];
        }

        $count = count($ekate_results);
        for ($i = 0; $i < $count; $i++) {
            $zeml = explode(',', $ekate_results[$i]['name']);
            $return[] = [
                'ekate' => $ekate_results[$i]['ekatte'],
                'text' => $ekate_results[$i]['ekatte'] . ' (' . $zeml[1] . ')',
            ];
        }

        if (!empty($rpcParams['selected'])) {
            $return[0]['selected'] = true;
        }

        return $return;
    }
}
