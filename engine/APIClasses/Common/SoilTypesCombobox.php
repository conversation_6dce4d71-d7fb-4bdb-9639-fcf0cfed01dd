<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;

/**
 * Типове почва.
 *
 * @rpc-module Common
 *
 * @rpc-service-id soil-types-combobox
 */
class SoilTypesCombobox extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getSoilTypesCombobox']],
        ];
    }

    /**
     * Return combobox data.
     *
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item boolean selected
     *                         }
     *
     * @return array
     */
    public function getSoilTypesCombobox($rpcParams)
    {
        if ($this->User->isGuest) {
            return [];
        }

        foreach ($GLOBALS['Farming']['soil_types'] as $item) {
            $return[] = $item;
        }

        if ($rpcParams['selected'] && 'true' == $rpcParams['selected']) {
            $return[0]['selected'] = true;
        }

        return $return;
    }
}
