<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Prado;
use Prado\Web\Services\TRpcApiProvider;

include_once __DIR__ . '/../../Plugins/Core/Contracts/conf.php';
include_once __DIR__ . '/../../Plugins/Core/UserDb/conf.php';

// Prado::using('Plugins.Core.UserDb.*');
// Prado::using('Plugins.Core.UserDb.conf');
// Prado::using('Plugins.Core.Users.*');
// Prado::using('Plugins.Core.Contracts.*');
// Prado::using('Plugins.Core.Contracts.conf');

/**
 * Returns $GLOBALS['Contracts']['DSTypes'] as combobox.
 *
 * @rpc-module Common
 *
 * @rpc-service-id ds-type-combobox
 */
class DSTypeCombobox extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getDSTypeCombobox']],
        ];
    }

    /**
     * @api-method read
     *
     * @return array
     */
    public function getDSTypeCombobox()
    {
        if ($this->User->isGuest) {
            return [];
        }

        $return = [];
        foreach ($GLOBALS['Contracts']['DSTypes'] as $item) {
            $return[] = $item;
        }

        return $return;
    }
}
