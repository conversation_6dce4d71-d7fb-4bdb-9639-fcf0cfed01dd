<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

/**
 * Payments rent places combobox.
 *
 * @rpc-module Common
 *
 * @rpc-service-id payments-rent-places-combobox
 */
class PaymentsRentPlacesCombobox extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getRentPlaces']],
        ];
    }

    /**
     * Returns the places where the owners/representatives can receive their rent.
     *
     * @api-method read
     *
     * @param bool $isRepresentative -If true shows only the representatives place
     *
     * @return array
     *               {
     *               #item string text    -Rent place.
     *               }
     */
    public function getRentPlaces($isRepresentative = false)
    {
        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'return' => ['rent_place AS text'],
            'where' => [
                'rent_place' => ['column' => 'rent_place', 'compare' => 'IS NOT', 'value' => 'NULL'],
            ],
            'group' => 'rent_place',
        ];

        if ($isRepresentative) {
            $options['tablename'] = $UserDbController->DbHandler->tableOwnersReps;

            return $UserDbController->getItemsByParams($options);
        }

        $options['tablename'] = $UserDbController->DbHandler->tableOwners;

        return $UserDbController->getItemsByParams($options);
    }
}
