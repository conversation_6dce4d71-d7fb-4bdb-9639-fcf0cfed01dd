<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

// Prado::using('Plugins.Core.Contracts.conf');
/**
 * Returns list of saved payment subjects or payment subject variables.
 *
 * @rpc-module Common
 *
 * @rpc-service-id payment-subjects-combobox
 */
class PaymentSubjectsCombobox extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getPaymentSubjects']],
            'getVariables' => ['method' => [$this, 'getPaymentSubjectVariables']],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *                         [
     *                         #item boolean selected
     *                         ]
     *
     * @return array
     *               [
     *               #item integer id
     *               #item string shortname
     *               #item string description
     *               ]
     */
    public function getPaymentSubjects($rpcParams)
    {
        // init controllers
        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'tablename' => $UserDbController->DbHandler->tablePaymentSubjects,
            'sort' => 'id',
            'order' => 'asc',
        ];

        $results = $UserDbController->getItemsByParams($options);
        foreach ($results as $result) {
            $result['selected'] = false;
        }

        array_unshift($results, ['id' => 0, 'name' => 'Въведете ръчно основание', 'selected' => false]);
        array_unshift($results, ['id' => -1, 'name' => 'Без основание', 'selected' => true]);

        if (isset($rpcParams['selected']) && true == $rpcParams['selected']) {
            $results[0]['selected'] = false;
            $results[5]['selected'] = true;

            foreach ($results as &$result) {
                if ('Рент. плащане по дог.' === $result['name']) {
                    $result['selected'] = true;
                }
            }
        }

        return $results;
    }

    /**
     * @api-method getVariables
     *
     * @return array
     *               [
     *               #item integer id
     *               #item string shortname
     *               #item string description
     *               ]
     */
    public function getPaymentSubjectVariables()
    {
        return [
            ['id' => 1, 'title' => 'Номер на договор', 'code' => '[[nomer_na_dogovor]]', 'selected' => true],
            ['id' => 2, 'title' => 'Стопанска година', 'code' => '[[stopanska_godina]]'],
            ['id' => 3, 'title' => 'Землище', 'code' => '[[zemlishte]]'],
            ['id' => 4, 'title' => 'Площ', 'code' => '[[plosht]]'],
        ];
    }
}
