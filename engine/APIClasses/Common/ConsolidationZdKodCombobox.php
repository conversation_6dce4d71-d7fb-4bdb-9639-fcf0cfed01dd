<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;

class ConsolidationZdKodCombobox extends TRpcApiProvider
{
    public function registerMethods(): array
    {
        return [
            'read' => ['method' => [$this, 'getConsolidationZdKodCombobox']],
        ];
    }

    /**
     * Вид декларация.
     */
    public function getConsolidationZdKodCombobox(): array
    {
        return [
            [
                'name' => 'Декларация по чл.69,ал.1',
                'id' => 1,
            ],
            [
                'name' => 'Декларация по чл.70,ал.1',
                'id' => 2,
            ],
            [
                'name' => 'Решение на комисията по споразумение',
                'id' => 3,
            ],
        ];
    }
}
