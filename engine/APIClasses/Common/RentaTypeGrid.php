<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Reads the RentaTypeGrid.
 *
 * @rpc-module Common
 *
 * @rpc-service-id renta-type-grid
 */
class RentaTypeGrid extends TRpcApiProvider
{
    private $module = 'Common';
    private $service_id = 'renta-type-grid';

    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readRentaTypesGrid']],

            'add' => ['method' => [$this, 'saveRentaType'],
                'validators' => [
                    'renta_type_name' => 'validateText, validateRequired, validateNotNull',
                    'renta_type_unit' => 'validateInteger, validateRequired, validateNotNull',
                    'renta_type_unit_value' => 'validateNumber',
                ]],

            'edit' => ['method' => [$this, 'editRentaType']],
        ];
    }

    /**
     * @api-method read
     *
     * @return array
     *               {
     *               #item int total count all rows
     *               #item array rows
     *               {
     *               #item int id
     *               #item string name
     *               #item string unit
     *               #item string unit_value
     *               }
     *               }
     */
    public function readRentaTypesGrid()
    {
        // init controllers
        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableRentaTypes,
            'sort' => 'id',
            'order' => 'DESC',
        ];

        $results = $UserDbController->getItemsByParams($options, false, false);

        $count = count($results);
        for ($i = 0; $i < $count; $i++) {
            $results[$i]['unit'] = $GLOBALS['Contracts']['renta_units'][$results[$i]['unit']]['fullname'];
            $results[$i]['unit_value'] = BGNtoEURO($results[$i]['unit_value']);
            $results[$i]['treatment_price'] = BGNtoEURO($results[$i]['treatment_price']);
        }

        $return['total'] = $count;
        $return['rows'] = $results;

        return $return;
    }

    /**
     * Saves new renta in the database
     * Throws MTRpcException with code -33042 if renta type name existed.
     *
     * @api-method add
     *
     * @param array $rpcParams - new renta parameters
     *                         {
     *                         #item string renta_type_name
     *                         #item ineger renta_type_unit
     *                         #item float  renta_type_unit_value
     *                         }
     *
     * @throws MTRpcException
     */
    public function saveRentaType($rpcParams)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $options = [
            'tablename' => $UserDbController->DbHandler->tableRentaTypes,
            'where' => [
                'name' => ['column' => 'name', 'compare' => '=', 'value' => $rpcParams['renta_type_name']],
            ],
        ];

        $results = $UserDbController->getItemsByParams($options, false, false);

        if (0 != count($results)) {
            throw new MTRpcException('RENTA_TYPE_ALREADY_EXISTS', -33042);
        }

        $options = [
            'tablename' => $UserDbController->DbHandler->tableRentaTypes,
            'mainData' => [
                'name' => $rpcParams['renta_type_name'],
                'unit' => $rpcParams['renta_type_unit'],
                'unit_value' => '' == $rpcParams['renta_type_unit_value'] ? null : $rpcParams['renta_type_unit_value'],
                'avg_yield' => '' == $rpcParams['avg_yield_value'] ? null : $rpcParams['avg_yield_value'],
                'treatment_price' => '' == $rpcParams['treatment_price_value'] ? null : $rpcParams['treatment_price_value'],
                'comment' => '' == $rpcParams['comment_value'] ? null : $rpcParams['comment_value'],
            ],
        ];

        $recordID = $UserDbController->addItem($options);

        // logging the action
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options, $recordID, 'Adding new renta type');
    }

    /**
     * Edit renta type in the database.
     *
     * @api-method edit
     *
     * @param array $rpcParams - new renta parameters
     *                         {
     *                         #item string unit_value
     *                         #item int renta_type_id
     *                         }
     *
     * @return int
     */
    public function editRentaType($rpcParams)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $options = [
            'tablename' => $UserDbController->DbHandler->tableRentaTypes,
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $rpcParams['renta_type_id']],
            ],
        ];

        $oldRenta = $UserDbController->getItemsByParams($options, false, false);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableRentaTypes,
            'mainData' => [
                'unit_value' => $rpcParams['unit_value'] ?: null,
                'avg_yield' => $rpcParams['avg_yield_value'] ?: null,
                'treatment_price' => $rpcParams['treatment_price_value'] ?: null,
                'comment' => $rpcParams['comment_value'],
            ],
            'where' => [
                'id' => $rpcParams['renta_type_id'],
            ],
        ];

        $recordID = $UserDbController->editItem($options);

        // logging the action
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $oldRenta, $options, 'Editing existing renta');

        return $recordID;
    }
}
