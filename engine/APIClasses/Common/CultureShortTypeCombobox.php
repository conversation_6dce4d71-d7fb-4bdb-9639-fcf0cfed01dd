<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;

/**
 * комбобокс за култури (кратък период).
 *
 * @rpc-module Common
 *
 * @rpc-service-id culture-short-type-combobox
 */
class CultureShortTypeCombobox extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @api common-rpc
     *
     * @module-rpc ekate-combobox
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getCulturesShortType']],
        ];
    }

    /**
     * Get all EKATTE areas, associated with the current user.
     *
     * @return array
     */
    public function getCulturesShortType($selected)
    {
        $return = [];

        foreach ($GLOBALS['Farming']['crops_short_type'] as $item) {
            $return[] = [
                'id' => $item['db_field'],
                'name' => $item['name'],
            ];
        }

        if ($selected && 'true' == $selected) {
            $return[0]['selected'] = 'true';
        }

        return $return;
    }
}
