<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Exceptions\TInvalidDataValueException;
use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDbDiary\UserDbDiaryController;

/**
 * @rpc-module Common
 *
 * @rpc-service-id integrations-hooks
 */
class IntegrationsHooks extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'getUsedProductsQuantity' => ['method' => [$this, 'getUsedProductsQuantity']],
        ];
    }

    /**
     * @param int $page
     * @param int $rows
     * @param string $sort
     * @param string $order
     *
     * @throws TInvalidDataValueException
     * @throws Exception
     *
     * @return array
     */
    public function getUsedProductsQuantity($filterObj, $page = 1, $rows = 30, $sort = '', $order = '')
    {
        $result = [];
        $userDbDiaryController = new UserDbDiaryController($this->User->Database);
        $events = $userDbDiaryController->getUsedProductsQuantityByWarehouseId($filterObj['productsIds'], $filterObj['date'], $filterObj['farm']);

        if (empty($events)) {
            return $result;
        }

        foreach ($events as $event) {
            $key = $event['farming_id'] . '-' . $event['substance_id'];
            $result[$key]['quantity'] += $event['quantity'];
            $result[$key]['product_id'] = $event['warehouse_item_id'];
            $result[$key]['farming_id'] = $event['farming_id'];
        }

        return $result;
    }
}
