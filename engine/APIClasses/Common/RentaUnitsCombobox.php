<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;

include_once __DIR__ . '/../../Plugins/Core/Contracts/conf.php';

/**
 * Returns all renta units for the current user.
 *
 * @rpc-module Common
 *
 * @rpc-service-id renta-units-combobox
 */
class RentaUnitsCombobox extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readRentaUnitsCombobox']],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item boolean selected
     *                         }
     *
     * @return array
     *               {
     *               #item int id
     *               #item string name
     *               #item string fullname
     *               #item string per_dka_name
     *               #item boolean selected
     *               }
     */
    public function readRentaUnitsCombobox($rpcParams)
    {
        $return = [];

        foreach ($GLOBALS['Contracts']['renta_units'] as $item) {
            $return[] = $item;
        }

        if (isset($rpcParams['selected']) && 'true' == $rpcParams['selected']) {
            $return[0]['selected'] = true;
        }

        return $return;
    }
}
