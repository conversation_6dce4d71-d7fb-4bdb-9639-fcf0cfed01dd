<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Prado;
use Prado\Web\Services\TRpcApiProvider;

include_once __DIR__ . '/../../Plugins/Core/Contracts/conf.php';

// Prado::using('Plugins.Core.Contracts.conf');
// Prado::using('Plugins.Core.Contracts.*');

/**
 * Get all valid contract types and display them as text.
 *
 * @rpc-module Common
 *
 * @rpc-service-id contract-type-combobox
 */
class ContractTypeCombobox extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getContractType']],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $filterObj
     *
     * @return array
     */
    public function getContractType($filterObj = [])
    {
        $return = [];
        $forbidden_types = [];

        // if payment types are requested then agreements and ownership contracts should not be shown
        if (isset($filterObj['payment_types']) && true == $filterObj['payment_types']) {
            $forbidden_types = [1, 4];
        }

        // if element for all records is needed add it at the first position
        if (isset($filterObj['record_all']) && true == $filterObj['record_all']) {
            $return[] = [
                'id' => '',
                'name' => 'Всички',
            ];
        }

        foreach ($GLOBALS['Contracts']['ContractTypes'] as $item) {
            if (!in_array($item['id'], $forbidden_types)) {
                $return[] = $item;
            }
        }

        if (isset($filterObj['selected']) && true == $filterObj['selected']) {
            $return[0]['selected'] = true;
        }

        return $return;
    }
}
