<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * @rpc-module Common
 *
 * @rpc-service-id zp-ekate-combobox
 *
 * @property UserDbController $UserDbController
 * @property UsersController $UsersController
 * @property LayersController $LayersController
 */
class ZpEkateCombobox extends TRpcApiProvider
{
    private $UserDbController = false;
    private $UsersController = false;
    private $LayersController = false;

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getZpEkateCombobox']],
        ];
    }

    /**
     * @return array
     */
    public function getZpEkateCombobox($rpcParams = [])
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        $LayersController = new LayersController('Layers');
        $return = [];

        $options = [
            'where' => [
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'prefix' => 't', 'value' => $this->User->GroupID],
                'layer_type' => ['column' => 'layer_type', 'compare' => '=', 'prefix' => 't', 'value' => 1],
            ],
        ];

        $layer_results = $LayersController->getLayers($options);
        $layer_tables = [];

        $count = count($layer_results);
        for ($i = 0; $i < $count; $i++) {
            $layer_tables[] = $layer_results[$i]['table_name'];
        }

        $layer_tables_options = [
            'tablename' => 'pg_class',
            'return' => ['relname AS table_name'],
            'where' => [
                'relname' => ['column' => 'relname', 'compare' => 'IN', 'value' => $layer_tables],
            ],
        ];
        $existing_layer_tables = $UserDbController->getItemsByParams($layer_tables_options, false, false);

        $ekate_final = [];
        $layerTablesCount = count($existing_layer_tables);
        for ($i = 0; $i < $layerTablesCount; $i++) {
            $layer_zp_options = [
                'tablename' => $existing_layer_tables[$i]['table_name'],
                'return' => ['DISTINCT(ekatte)'],
            ];
            $ekate_results = $UserDbController->getItemsByParams($layer_zp_options, false, false);

            $ekateResCount = count($ekate_results);
            for ($j = 0; $j < $ekateResCount; $j++) {
                if (null != $ekate_results[$j]['ekatte'] && '' != $ekate_results[$j]['ekatte']) {
                    $ekate_final[$ekate_results[$j]['ekatte']] = $ekate_results[$j]['ekatte'];
                }
            }
        }

        $ekate_final = array_values($ekate_final);

        if (isset($rpcParams['record_all']) && true == $rpcParams['record_all']) {
            $return[] = [
                'ekate' => '',
                'text' => 'Всички',
            ];
        }

        if (isset($rpcParams['without_ekatte']) && true == $rpcParams['without_ekatte']) {
            $return[] = [
                'ekate' => 'null',
                'text' => 'Без ЕКАТТЕ',
            ];
        }
        $ekateFinal = count($ekate_final);

        for ($i = 0; $i < $ekateFinal; $i++) {
            $return[] = [
                'ekate' => $ekate_final[$i],
                'text' => $ekate_final[$i] . ' (' . $UsersController->getEkatteName($ekate_final[$i]) . ')',
            ];
        }

        if (isset($rpcParams['selected']) && true == $rpcParams['selected']) {
            $return[0]['selected'] = true;
        }

        return $return;
    }
}
