<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Returns all EKATTE, for 'Комасация'.
 *
 * @rpc-module Common
 *
 * @rpc-service-id kms-ekate-combobox
 */
class KmsEkateCombobox extends TRpcApiProvider
{
    private $LayersController = false;
    private $UserDbController = false;
    private $UserDbPlotsController = false;
    private $UsersController = false;

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getKmsEkateCombobox']],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer layer_id
     *                         #item boolean selected
     *                         }
     *
     * @return array
     */
    public function getKmsEkateCombobox($rpcParams)
    {
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);
        $LayersController = new LayersController('Layers');
        $UsersController = new UsersController('Users');

        // creating a default empty return
        $empty_return = [
            'rows' => [],
            'total' => 0,
        ];

        if (isset($rpcParams['layer_id']) && (int) $rpcParams['layer_id']) {
            $layer_result = $LayersController->getLayerData($rpcParams['layer_id']);
        } else {
            return $empty_return;
        }

        $tableExists = $UserDbController->getTableNameExist($layer_result['table_name']);
        if (!$tableExists) {
            return $empty_return;
        }

        $options = [
            'tablename' => $layer_result['table_name'],
            'return' => ['DISTINCT(ekatte)'],
            'where' => [
                'ekatte' => ['column' => 'ekatte', 'compare' => 'IS NOT', 'value' => 'NULL'],
            ],
        ];

        $results = $UserDbController->getItemsByParams($options);

        if ($rpcParams['record_all'] && 'true' == $rpcParams['record_all']) {
            $return[] = [
                'ekate' => '',
                'text' => 'Всички',
            ];
        }
        $return = [];
        $count = count($results);
        for ($i = 0; $i < $count; $i++) {
            if ('' == $results[$i]['ekatte'] || null == $results[$i]['ekatte']) {
                $return[] = [
                    'ekate' => 'null',
                    'text' => 'Без ЕКАТТЕ',
                ];

                continue;
            }

            $return[] = [
                'ekate' => $results[$i]['ekatte'],
                'text' => $results[$i]['ekatte'] . ' (' . $UsersController->getEkatteName($results[$i]['ekatte']) . ')',
            ];
        }

        if ($rpcParams['selected']) {
            $return[0]['selected'] = true;
        }

        return $return;
    }
}
