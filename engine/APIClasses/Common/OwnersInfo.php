<?php

namespace TF\Engine\APIClasses\Common;

use DateTime;
use Prado\Web\Services\TJsonRpcProtocol;
use Prado\Web\Services\TRpcApiProvider;
use Prado\Web\Services\TRpcServer;
use TF\Application\Common\Config;
use TF\Engine\APIClasses\Owners\OwnersPaymentsGrid;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbOwners\UserDbOwnersController;

// Prado::using('Plugins.Core.Owners.conf');
// Prado::using('Plugins.Core.UserDb.*');
// Prado::using('Plugins.Core.UserDbOwners.*');

/**
 * Returns all available information for the selected owner.
 *
 * @rpc-module Common
 *
 * @rpc-service-id owner-info
 *
 * @property UserDbController $UserDbController
 * @property UserDbOwnersController $UserDbOwnersController
 */
class OwnersInfo extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readOwnersInfo']],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer id
     *                         }
     *
     * @throws MTRpcException
     *
     * @return array
     */
    public function readOwnersInfo($rpcParams)
    {
        if ($this->User->isGuest) {
            return [];
        }

        $UserDbController = new UserDbController($this->User->Database);
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);

        $result = [];
        $options = [
            'return' => ['o.*'],
            'where' => [
                'id' => ['column' => 'id', 'prefix' => 'o', 'compare' => '=', 'value' => $rpcParams['id']],
            ],
        ];
        $result = $UserDbOwnersController->getOwnersData($options);
        if (0 == count($result)) {
            throw new MTRpcException('invalid_owner_id', Config::INVALID_OWNER_ID);
        }
        // replacing empty values with "-"
        if ('' == $result[0]['lk_nomer']) {
            $result[0]['lk_nomer'] = '-';
        }
        if ('' == $result[0]['lk_izdavane']) {
            $result[0]['lk_izdavane'] = '-';
        }
        if ('' == $result[0]['mol']) {
            $result[0]['mol'] = '-';
        }
        if ('' == $result[0]['company_address']) {
            $result[0]['company_address'] = '-';
        }
        if ('' == $result[0]['email']) {
            $result[0]['email'] = '-';
        }
        if ('' == $result[0]['phone']) {
            $result[0]['phone'] = '-';
        }
        if ('' == $result[0]['fax']) {
            $result[0]['fax'] = '-';
        }
        if ('' == $result[0]['mobile']) {
            $result[0]['mobile'] = '-';
        }
        if ('' == $result[0]['address']) {
            $result[0]['address'] = '-';
        }

        // owner type ID is required for info panel
        $result[0]['owner_type_id'] = $result[0]['owner_type'];

        if (0 == $result[0]['owner_type']) {
            $result[0]['owner_type'] = $GLOBALS['Owners']['Types'][0]['type_name'];
        } else {
            $result[0]['owner_type'] = $GLOBALS['Owners']['Types'][1]['type_name'];
        }

        if ($result[0]['is_dead']) {
            $result[0]['is_dead'] = 'Да';
        } else {
            $result[0]['is_dead'] = 'Не';
        }

        return $result[0];
    }

    /**
     * Validates dead date.
     * Dead date can not be changed if owner payments exists
     * Dead date should not be in future.
     *
     * @param [string] $newDeadDate
     * @param [sttring] $currentDeadDate
     * @param [?int] $ownerId
     * @param ?string $ownerDeadDate
     */
    public static function validateDeadDate(?string $newDeadDate, ?string $ownerDeadDate = null, ?int $ownerId = null)
    {
        if (null == $newDeadDate) {
            return;
        }

        $newDeadDate = DateTime::createFromFormat('Y-m-d', $newDeadDate);

        if ($newDeadDate || $ownerDeadDate) {
            // Validates dead date on change of value. Add date or edit date

            $newDeadYear = $newDeadDate->format('Y');
            if (null !== $ownerDeadDate) {
                $ownerDeadDate = DateTime::createFromFormat('Y-m-d H:i:s', $ownerDeadDate);
                $deadYear = $ownerDeadDate->format('Y');

                if ($newDeadYear == $deadYear) {
                    return;
                }
            } else {
                $deadYear = $newDeadYear;
            }

            if ($ownerId) {
                foreach ($GLOBALS['Farming']['years'] as $farmingYear) {
                    if ($farmingYear['year'] > $deadYear) {
                        // Current dead date year payments are allowed
                        $server = new TRpcServer(new TJsonRpcProtocol());
                        $payments = (new OwnersPaymentsGrid($server))->getPayments($ownerId, $farmingYear['id']);

                        if ($payments) {
                            // if payments after dead year throw exception
                            throw new MTRpcException('CAN_NOT_CHANGE_DEAD_DATE', -33235);
                        }

                        break;
                    }
                }
            }
        }

        $currentDate = new DateTime();

        if ($newDeadDate > $currentDate) {
            throw new MTRpcException('INVALID_DEAD_DATE', -33234);
        }
    }
}
