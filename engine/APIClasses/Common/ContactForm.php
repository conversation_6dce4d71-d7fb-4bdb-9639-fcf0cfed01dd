<?php

namespace TF\Engine\APIClasses\Common;

use Exception;
use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\Mail;

/**
 * Контактна форма.
 *
 * @rpc-module Common
 *
 * @rpc-service-id contact-form
 */
class ContactForm extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'send' => ['method' => [$this, 'sendContactFormData']],
        ];
    }

    /**
     * @api-method send
     *
     * @param  array data
     *
     * @throws Exception
     *
     * @return array|bool
     */
    public function sendContactFormData($data)
    {
        $mobile = $data['mobile'] ?? '';
        $email = $data['email'] ?? '';
        $message = $data['message'] ?? '';

        if (strlen(trim($message)) > 20) {
            $message = '
                <html>
                    <body>
                        <p>Клиент: ' . $this->User->FullName . '</p>
                        <p>Потребителско име: ' . $this->User->Name . '</p>
                        <p>Изпратено на: ' . date('Y-m-d H-i-s') . '</p>
                        <br/>
                        <p>Мобилен телефон: </p>
                        <p>' . nl2br($mobile) . '</p>
                        <p>Email: </p>
                        <p>' . nl2br($email) . '</p>
                        <br/>
                        <p>Запитване: </p>
                        <p>' . nl2br($message) . '</p>
                    </body>
                </html>
            ';

            $mail = new Mail();

            return $mail->sendMail(CONTACT_FORM_TO_MAIL, CONTACT_FORM_SUBJECT, $message, CONTACT_FORM_FROM_NAME);
        }
    }
}
