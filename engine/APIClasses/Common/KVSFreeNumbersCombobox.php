<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

/**
 * Returns all available KVS numbers when splitting or combining plots.
 *
 * @rpc-module Common
 *
 * @rpc-service-id kvs-free-numbers
 *
 * @property UserDbController $UserDbController
 */
class KVSFreeNumbersCombobox extends TRpcApiProvider
{
    private $UserDbController = false;

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getKVSFreeNumbersCombobox']],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer gid
     *                         }
     *
     * @return array
     */
    public function getKVSFreeNumbersCombobox($rpcParams)
    {
        if ($this->User->isGuest || (int)$rpcParams['gid'] < 1) {
            return [];
        }

        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableKVS,
            'return' => ['ekate, masiv'],
            'where' => [
                'gid' => ['column' => 'gid', 'compare' => '=', 'value' => (int)$rpcParams['gid']],
            ],
        ];

        $results = $UserDbController->getItemsByParams($options);
        if (0 == count($results)) {
            return [];
        }

        // get max number
        $options = [
            'tablename' => $UserDbController->DbHandler->tableKVS,
            'sort' => 'number',
            'order' => 'desc',
            'limit' => '1',
            'offset' => '0',
            'return' => ['number::numeric'],
            'where' => [
                'ekate' => ['column' => 'ekate', 'compare' => '=', 'value' => $results[0]['ekate']],
                'masiv' => ['column' => 'masiv', 'compare' => '=', 'value' => $results[0]['masiv']],
            ],
        ];

        $results = $UserDbController->getItemsByParams($options);

        for ($i = 1; $i <= 10; $i++) {
            $return[] = [
                'id' => (string)((int)$results[0]['number'] + $i),
                'name' => (string)((int)$results[0]['number'] + $i),
            ];
        }

        return $return;
    }
}
