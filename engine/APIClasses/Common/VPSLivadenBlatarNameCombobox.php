<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Returns all information for the "ВПС Ливаден блатар" layer.
 *
 * @rpc-module Common
 *
 * @rpc-service-id livaden-blatar-name-combobox
 */
class VPSLivadenBlatarNameCombobox extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getVPSLivadenBlatarNameCombobox']],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item boolean record_all
     *                         #item boolean selected
     *                         }
     *
     * @return array
     */
    public function getVPSLivadenBlatarNameCombobox($rpcParams)
    {
        if ($this->User->isGuest) {
            return [];
        }

        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        $LayersController = new LayersController('Layers');

        $return = [];
        $options = [
            'tablename' => 'layer_vps_livaden_blatar',
            'return' => [
                'ime',
            ],
        ];

        $hash = md5(json_encode($options));
        $ekate_results = $LayersController->MemCache->get($hash);
        if (!$ekate_results) {
            $ekate_results = $LayersController->getRemoteLayerData($options, false, false);
            $LayersController->MemCache->add($hash, $ekate_results, $LayersController->default_memcache_expire);
        }
        if ($rpcParams['record_all'] && 'true' == $rpcParams['record_all']) {
            $return[] = [
                'ime' => '',
                'text' => 'Всички',
            ];
        }
        $count = count($ekate_results);

        for ($i = 0; $i < $count; $i++) {
            $return[] = [
                'ime' => $ekate_results[$i]['ime'],
                'text' => $ekate_results[$i]['ime'],
            ];
        }

        if (!empty($rpcParams['selected'])) {
            $return[0]['selected'] = true;
        }

        return $return;
    }
}
