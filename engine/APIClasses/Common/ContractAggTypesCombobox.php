<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;

include_once __DIR__ . '/../../Plugins/Core/Contracts/conf.php';

/**
 * Типове договори.
 *
 * @rpc-module Common
 *
 * @rpc-service-id contract-agg-types-combo
 */
class ContractAggTypesCombobox extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getContractAggTypesCombobox']],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *
     * @return array
     */
    public function getContractAggTypesCombobox($rpcParams)
    {
        $return = [];

        foreach ($GLOBALS['Contracts']['agg_types'] as $item) {
            $return[] = $item;
        }

        if ($rpcParams['selected']) {
            $return[0]['selected'] = true;
        }

        return $return;
    }
}
