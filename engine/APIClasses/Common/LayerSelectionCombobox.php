<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;
use TF\Application\Common\Config;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\Layers\LayersController;

/**
 * Returns all layers.
 *
 * @rpc-module Common
 *
 * @rpc-service-id layer-selection-combobox
 *
 * @property LayersController $LayersController
 * @property FarmingController $FarmingController
 */
class LayerSelectionCombobox extends TRpcApiProvider
{
    private $LayersController;
    private $FarmingController;

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getLayersSelectionCombobox']],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer layer_type
     *                         #item boolean is_exist
     *                         }
     *
     * @return array
     */
    public function getLayersSelectionCombobox($rpcParams)
    {
        $LayersController = new LayersController('Layers');
        $FarmingController = new FarmingController('Farming');

        if (Config::LAYER_TYPE_WORK_LAYER == $rpcParams['layer_type']) {
            return $this->getWorkLayers($rpcParams['layer_id']);
        }

        $years = array_keys($GLOBALS['Farming']['years']);

        $options = [
            'user_id' => $this->User->GroupID,
            'layer_type' => $rpcParams['layer_type'],
            'year' => $years,
        ];

        if ($rpcParams['is_exist']) {
            $options['is_exist'] = true;
        }

        $data = $LayersController->getLayersCombobox($options);

        foreach ($data as $key => $value) {
            $farming_year_title = $GLOBALS['Farming']['years'][$data[$key]['farming_year']]['title'];
            $data[$key]['layer_path'] = $data[$key]['layer_name'] . ' / ' . $farming_year_title;
        }

        return $data;
    }

    private function getWorkLayers($layer_id)
    {
        $LayersController = new LayersController('Layers');
        $layers = $LayersController->getWorkLayersWithFarmings($this->User->GroupID, $layer_id);
        $data = [];
        foreach ($layers as $key => $value) {
            $data[$key] = $value;
            if (!is_null($data[$key]['farming']) && !is_null($data[$key]['year'])) {
                $farming_year_title = $GLOBALS['Farming']['years'][$data[$key]['year']]['title'];
                $data[$key]['layer_path'] = $data[$key]['farming_name'] . ' / ' . $farming_year_title . ' / ' . $data[$key]['name'];
            } else {
                $data[$key]['layer_path'] = $data[$key]['name'];
            }
        }
        array_unshift($data, ['user_id' => $this->User->UserID, 'name' => 'Нов работен слой', 'layer_path' => 'Нов работен слой', 'layer_id' => 'new', 'layer_type' => Config::LAYER_TYPE_WORK_LAYER]);

        return $data;
    }
}
