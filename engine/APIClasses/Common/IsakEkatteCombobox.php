<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;
use TF\Application\Common\Config;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\Users\UsersController;

include_once __DIR__ . '/../../Plugins/Core/Farming/conf.php';

/**
 * Return all ekattes for certain table as text.
 *
 * @rpc-module Common
 *
 * @rpc-service-id isak-ekatte-combobox
 */
class IsakEkatteCombobox extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @api common-rpc
     *
     * @module-rpc isak-ekate-combobox
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getIsakEkatte']],
            'getAllIsakEkatte' => ['method' => [$this, 'getAllIsakEkatte']],
        ];
    }

    /**
     * Get all cultures.
     *
     * @api-method read
     *
     * @param array $filterObj
     *                         {
     *                         #item boolean priority_first
     *                         #item year year
     *                         #item boolean crop_rotation
     *                         #item boolean selected
     *                         #item boolean without_crops
     *                         }
     *
     * @return array
     */
    public function getIsakEkatte($filterObj = null)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        $options = [
            'tablename' => $filterObj['layer_name'],
            'return' => [
                'DISTINCT(ekatte)',
            ],
        ];
        $results = $UserDbController->getItemsByParams($options, false, false);

        $count = count($results);
        if (0 == $count) {
            return [];
        }

        for ($i = 0; $i < $count; $i++) {
            $results[$i]['name'] = $results[$i]['ekatte'] . ' (' . $UsersController->getEkatteName($results[$i]['ekatte']) . ')';
        }

        return $results;
    }

    public function getAllIsakEkatte($rpcParams = [])
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $return = [];
        $ekatte_codes = [];

        $isakTables = $UsersController->getItemsByParams(
            [
                'tablename' => $UserDbController->DbHandler->tableLayers,
                'where' => [
                    'is_exist' => ['column' => 'is_exist', 'compare' => '=', 'value' => 't'],
                    'layer_type' => ['column' => 'layer_type', 'compare' => '=', 'value' => Config::LAYER_TYPE_ISAK],
                    'group_id' => ['column' => 'group_id', 'compare' => '=', 'value' => $this->User->groupID],
                ],
                'return' => ['table_name'],
            ]
        );

        if (empty($isakTables)) {
            return $return;
        }

        foreach ($isakTables as $index => $isakTable) {
            if (!$UserDbController->getTableNameExist($isakTable['table_name'])) {
                continue;
            }
            $result = $UserDbController->getItemsByParams(
                [
                    'tablename' => $isakTable['table_name'],
                    'return' => ['DISTINCT(ekatte)'],
                ]
            );
            $ekatte_values = array_values(array_column($result, 'ekatte'));
            foreach ($ekatte_values as $code) {
                $return[$isakTable['table_name']][$code] = null;
            }
            $ekatte_codes = array_merge($ekatte_codes, $ekatte_values);
        }

        unset($ekatte_values, $index, $isakTables, $isakTable, $result, $code);

        $ekatte_data = $UsersController->getEkatteNameMulti(
            [
                'return' => ['ekatte_code as code', 'ekatte_name as name'],
                'ekatte_codes' => array_filter(array_unique($ekatte_codes, SORT_REGULAR)),
            ]
        );
        $ekatte = [];
        foreach ($ekatte_data as $row) {
            $ekatte[$row['code']] = $row['name'];
        }

        unset($row ,$ekatte_data,$ekatte_codes,$UserDbController,$UsersController);
        $counter = 0;
        foreach ($return as $table_key => $table) {
            foreach ($table as $code => $name) {
                if (!isset($code) || '' == $code) {
                    continue;
                }
                $return[$table_key][$counter] = ['ekate' => $code, 'text' => $code . ' (' . $ekatte[$code] . ')'];
                unset($return[$table_key][$code]);
                ++$counter;
            }
            $return[$table_key] = array_filter(array_values($return[$table_key]));
            usort($return[$table_key], ['IsakEkatteCombobox', 'sortEkatte']);
            if (!empty($return[$table_key])) {
                array_unshift(
                    $return[$table_key],
                    ['ekate' => 'null', 'text' => 'Без ЕКАТТЕ']
                );
            }
            array_unshift(
                $return[$table_key],
                ['ekate' => '', 'text' => 'Всички']
            );
        }

        return $return;
    }

    private function sortEkatte($a, $b)
    {
        return strcmp($a['text'], $b['text']);
    }
}
