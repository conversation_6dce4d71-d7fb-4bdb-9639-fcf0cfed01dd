<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;

/**
 * @rpc-module Common
 *
 * @rpc-service-id kvs-update-action-type-combobox
 */
class KVSUpdateActionTypeCombobox extends TRpc<PERSON>piProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'read']],
        ];
    }

    /**
     * Get all EKATTE areas, associated with the current user.
     *
     * @return array {
     *               #item integer id,
     *               #item string name,
     *               #item string selected
     *               }
     */
    public function read($selected)
    {
        $return = [];
        foreach ($GLOBALS['Layers']['KVSUpdateActionTypes'] as $item) {
            $return[] = [
                'id' => $item['id'],
                'name' => $item['name'],
            ];
        }

        if ($selected && 'true' == $selected) {
            $return[0]['selected'] = 'true';
        }

        return $return;
    }
}
