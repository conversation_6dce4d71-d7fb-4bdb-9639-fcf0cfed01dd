<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;
use TF\Application\Common\Config;

/**
 * Списък със схеми за субсидии.
 *
 * @rpc-module Common
 *
 * @rpc-service-id schema-combobox
 */
class SchemaCombobox extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getSchemas']],
        ];
    }

    /**
     * Get all EKATTE areas, associated with the current user.
     *
     * @param array $filterObj {
     *                         #item string detailed_lfa,
     *                         #item string selected,
     *                         #item boolean without_schema
     *                         }
     *
     * @return array {
     *               #item integer id,
     *               #item string name,
     *               #item string selected
     *               }
     */
    public function getSchemas($filterObj)
    {
        $return = [];
        foreach ($GLOBALS['Farming']['schema'] as $item) {
            $return[] = [
                'id' => $item['id'],
                'name' => $item['name'],
            ];
        }
        if ($$filterObj['detailed_lfa'] && 'true' == $filterObj['detailed_lfa']) {
            $return[Config::NR - 1] = [
                'id' => $GLOBALS['Farming']['lfa_schema_types'][Config::NR1]['id'],
                'name' => $GLOBALS['Farming']['lfa_schema_types'][Config::NR1]['name'],
            ];
            $return[] = [
                'id' => $GLOBALS['Farming']['lfa_schema_types'][Config::NR2]['id'],
                'name' => $GLOBALS['Farming']['lfa_schema_types'][Config::NR2]['name'],
            ];
        }

        if ($filterObj['selected'] && 'true' == $filterObj['selected']) {
            $return[0]['selected'] = 'true';
        }

        if (true == $filterObj['without_schema']) {
            $withoutSchema = [
                'id' => 'null',
                'name' => 'Без схема/мярка',
            ];

            array_unshift($return, $withoutSchema);
        }

        return $return;
    }
}
