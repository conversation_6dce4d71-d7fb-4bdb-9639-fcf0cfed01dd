<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Prado;
use Prado\Web\Services\TRpcApiProvider;

// Prado::using('Plugins.Core.Farming.conf');
// Prado::using('Plugins.Core.Farming.*');

/**
 * Get all farming years, associated with the current user and display them as text.
 *
 * @rpc-module Common
 *
 * @rpc-service-id farming-year-combobox
 */
class FarmingYearCombobox extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getFarmingYear']],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $filterObj {
     *                         #item string selected = 'current' - the current year will be selected; selected = '2020' - 2020/2021 will be selected
     *                         #item boolean record_all Show "Всички" лабел.
     *                         #item array chosen_years - return list of only chosen years
     *
     * }
     *
     * @return array {
     *               #item boolean id
     *               #item boolean title
     *               #item boolean farming_year
     *               }
     */
    public function getFarmingYear($filterObj = [])
    {
        $farmYear = date('Y');

        if (!$filterObj['calendar_year_selected']) {
            $today = strtotime(date('Y-m-d'));
            $beginOfFarmYear = strtotime(date('Y') . '-10-01');
            if ($today >= $beginOfFarmYear) {
                $farmYear++;
            }
        }

        $return = [];
        foreach ($GLOBALS['Farming']['years'] as $key => $year) {
            if (is_array($filterObj['chosen_years']) and !in_array($year['id'], $filterObj['chosen_years'])) {
                continue;
            }

            $return[$key] = [
                'id' => $year['id'],
                'title' => $year['title'],
                'farming_year' => $year['farming_year'],
                'year' => $year['year'],
                'start_date' => $year['start_date'],
                'end_date' => $year['end_date'],
            ];

            if (!empty($filterObj['selected'])) {
                if ('current' === $filterObj['selected'] && (int)$farmYear === (int)$year['year']) {
                    $return[$key]['selected'] = true;
                }
                if ($filterObj['selected'] === $year['id']) {
                    $return[$key]['selected'] = true;
                }
            }
        }

        if (isset($filterObj['record_all']) && true == $filterObj['record_all']) {
            array_unshift($return, [
                'id' => '',
                'title' => 'Всички',
                'farming_year' => 'Всички',
                'year' => '',
                'start_date' => '',
                'end_date' => '',
            ]);
        }

        // normalize indexes
        return array_values($return);
    }
}
