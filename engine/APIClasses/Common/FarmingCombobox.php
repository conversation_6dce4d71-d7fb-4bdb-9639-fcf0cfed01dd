<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Prado;
use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Farming\FarmingController;

include_once __DIR__ . '/../../Plugins/Core/Farming/conf.php';

// Prado::using('Plugins.Core.Farming.conf');
// Prado::using('Plugins.Core.Farming.*');

/**
 * Get all the farmings associated with the current user.
 *
 * @rpc-module Common
 *
 * @rpc-service-id farming-combobox
 */
class FarmingCombobox extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getFarmingType']],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $filterObj
     *
     * @return array
     */
    public function getFarmingType($filterObj = [])
    {
        if ($this->User->isGuest) {
            return [];
        }

        $FarmingController = new FarmingController('Farming');
        $return = array_values($FarmingController->getUserFarmings(true));

        if (isset($filterObj['record_all']) && true == $filterObj) {
            array_unshift($return, [
                'id' => '',
                'name' => 'Всички',
            ]);
        }

        if (isset($filterObj['selected']) && true == $filterObj['selected']) {
            $return[0]['selected'] = true;
        }

        return $return;
    }
}
