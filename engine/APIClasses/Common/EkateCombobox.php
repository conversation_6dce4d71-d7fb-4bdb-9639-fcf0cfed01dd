<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;
use TF\Application\Entity\UserFarmings;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Get all EKATTE areas, associated with the current user.
 *
 * @rpc-module Common
 *
 * @rpc-service-id ekate-combobox
 */
class EkateCombobox extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getEKATTECombox']],
            'readAll' => ['method' => [$this, 'getAllEkattesCombobox']],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $filterObj
     * @param string $database
     * @param string $sort
     * @param string $order
     *
     * @return array
     */
    public function getEKATTECombox($filterObj = [], $database = null, $sort = 'ekatte_name', $order = 'asc')
    {
        $return = [];
        $UserDbController = new UserDbController(!empty($database) ? $database : $this->User->Database);

        $options = [
            'return' => ['*'],
            'tablename' => 'ekate_combobox',
            'sort' => 'ekatte_name',
            'order' => 'asc',
        ];

        $results = $UserDbController->getItemsByParams($options);

        if (isset($filterObj['record_all']) && true == $filterObj['record_all']) {
            $return[] = [
                'ekate' => '',
                'text' => 'Всички',
            ];
        }

        if (isset($filterObj['without_ekatte']) && true == $filterObj['without_ekatte']) {
            $return[] = [
                'ekate' => 'null',
                'text' => 'Без ЕКАТТЕ',
            ];
        }

        $count = count($results);
        for ($i = 0; $i < $count; $i++) {
            if ('' == $results[$i]['ekate'] || null == $results[$i]['ekate']) {
                continue;
            }

            $return[] = [
                'ekate' => $results[$i]['ekate'],
                'text' => $filterObj['reversed_ekatte']
                    ? $results[$i]['ekatte_name'] . ' (' . $results[$i]['ekate'] . ')'
                    : $results[$i]['ekate'] . ' (' . $results[$i]['ekatte_name'] . ')',
            ];
        }

        if (isset($filterObj['selected']) && true == $filterObj['selected']) {
            $return[0]['selected'] = true;
        }

        return $return;
    }

    public function getAllEkattesCombobox($filterObj = [], $sort = 'ekatte_name', $order = 'asc')
    {
        $UserControler = new UsersController();

        $options = [
            'return' => ['*'],
            'tablename' => 'su_ekatte',
            'sort' => 'ekatte_name',
            'order' => 'asc',
        ];

        $results = $UserControler->getItemsByParams($options);

        if (isset($filterObj['farm_uuid']) && $filterObj['farm_uuid']) {
            $farming = UserFarmings::finder()->find('uuid = :uuid', [':uuid' => $filterObj['farm_uuid']]);
        }

        $count = count($results);
        for ($i = 0; $i < $count; $i++) {
            if ('' == $results[$i]['ekatte_code'] || null == $results[$i]['ekatte_code']) {
                continue;
            }

            $selected = false;
            if ($farming && $farming->company_ekatte == $results[$i]['ekatte_code']) {
                $selected = true;
            }

            $return[] = [
                'ekatte' => $results[$i]['ekatte_code'],
                'text' => $results[$i]['ekatte_name'] . ' (' . $results[$i]['ekatte_code'] . ')',
                'selected' => $selected,
            ];
        }

        return $return;
    }
}
