<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Exceptions\TDbException;
use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

/**
 * Return all cultures, based on the layer name.
 *
 * @rpc-module Common
 *
 * @rpc-service-id culture-combobox-by-layername
 */
class CultureComboboxByLayerName extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getCultureComboboxByLayerName']],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item string layer_name
     *                         #item boolean selected
     *                         #item boolean without_crops
     *                         }
     *
     * @throws MTRpcException
     *
     * @return array
     */
    public function getCultureComboboxByLayerName($rpcParams)
    {
        $return = [];

        try {
            if ($rpcParams['layer_name']) {
                $UserDbController = new UserDbController($this->User->Database);

                $options = [
                    'tablename' => $rpcParams['layer_name'],
                    'return' => ['cropname', 'cropcode'],
                    'where' => [
                        'cropname' => ['column' => 'cropname', 'compare' => '!=', 'value' => ' '],
                    ],
                    'group' => 'cropname, cropcode',
                ];

                $result = $UserDbController->getItemsByParams($options);

                foreach ($result as $item) {
                    $return[] = [
                        'id' => $item['cropcode'],
                        'name' => $item['cropname'],
                    ];
                }
            }
        } catch (TDbException $e) {
            throw new MTRpcException('invalid_table_name', -33102);
        } finally {
            if ($rpcParams['selected'] && 'true' == $rpcParams['selected']) {
                $return[0]['selected'] = 'true';
            }
            if (true == $rpcParams['without_crops']) {
                $withoutCrops = [
                    'id' => '-1',
                    'name' => '-',
                ];

                array_unshift($return, $withoutCrops);
            }

            return $return;
        }
    }
}
