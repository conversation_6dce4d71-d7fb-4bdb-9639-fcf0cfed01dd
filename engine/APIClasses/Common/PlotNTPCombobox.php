<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Prado;
use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDbAreaTypes\UserDbAreaTypesController;

// Prado::using('Plugins.Core.Farming.conf');
// Prado::using('Plugins.Core.Farming.*');
// Prado::using('Plugins.Core.Layers.conf');
// Prado::using('Plugins.Core.Layers.*');

/**
 * Get all available area use types and display them as text.
 *
 * @rpc-module Common
 *
 * @rpc-service-id plot-ntp-combobox
 */
class PlotNTPCombobox extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getPlotNTPCombobox']],
        ];
    }

    /**
     * @api-method read
     *
     * @return array the list of implemented methods
     */
    public function getPlotNTPCombobox($filterObj = [])
    {
        if ($this->User->isGuest || !$this->User->Database) {
            return [];
        }

        $return = [];

        if (isset($filterObj['record_all']) && true == $filterObj['record_all']) {
            $return[] = [
                'id' => '',
                'name' => 'Всички',
            ];
        }

        if (isset($filterObj['without_cat_ntp']) && true == $filterObj['without_cat_ntp']) {
            $return[] = [
                'id' => '-1',
                'name' => 'Без НТП',
            ];
        }

        $UserDbAreaTypesController = new UserDbAreaTypesController($this->User->Database);
        $return = array_merge($return, $UserDbAreaTypesController->getNtps());

        if (isset($filterObj['selected']) && true == $filterObj['selected']) {
            $return[0]['selected'] = true;
        }

        return $return;
    }
}
