<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;

/**
 * Returns all map types as text.
 *
 * @rpc-module Common
 *
 * @rpc-service-id map-types-combobox
 */
class MapTypesCombobox extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getMapTypesCombobox']],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item boolean selected
     *                         }
     *
     * @return array
     */
    public function getMapTypesCombobox($rpcParams)
    {
        if ($this->User->isGuest) {
            return [];
        }

        $return = [];

        foreach ($GLOBALS['Layers']['maps'] as $item) {
            if ('9' == $item['id'] && !$this->User->getHasGeoscanMapRights()) {
                continue;
            }
            $return[] = $item;
        }

        if ($rpcParams['selected'] && 'true' == $rpcParams['selected']) {
            if (!$this->User->getHasGeoscanMapRights()) {
                $return[0]['selected'] = true;
            } else {
                $return[1]['selected'] = true;
            }
        }

        return $return;
    }
}
