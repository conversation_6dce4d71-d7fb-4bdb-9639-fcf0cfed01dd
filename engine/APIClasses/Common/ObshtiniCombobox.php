<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Returns all obshtini.
 *
 * @rpc-module Common
 *
 * @rpc-service-id obshtini-combobox
 *
 * @property UserDbController $UserDbController
 * @property UsersController $UsersController
 * @property LayersController $LayersController
 */
class ObshtiniCombobox extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getObshtini']],
        ];
    }

    /**
     * @api-method read
     *
     * @return array
     */
    public function getObshtini()
    {
        $UsersController = new UsersController();

        $options = [
            'tablename' => 'su_obshtini',
            'sort' => 'obsht_name',
            'order' => 'ASC',
        ];

        return $UsersController->getItemsByParams($options, false, false);
    }
}
