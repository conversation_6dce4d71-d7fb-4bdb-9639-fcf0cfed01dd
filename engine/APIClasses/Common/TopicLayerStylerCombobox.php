<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;

/**
 * комбобокс за типове тематични КВС карти.
 *
 * @rpc-module Common
 *
 * @rpc-service-id culture-short-type-combobox
 */
class TopicLayerStylerCombobox extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getTopicLayerStylerCombobox']],
        ];
    }

    /**
     * @api-method read
     *
     * @return array
     */
    public function getTopicLayerStylerCombobox()
    {
        $return = [
            [
                'name' => 'По собственици',
                'id' => 1,
            ],
            [
                'name' => 'По арендатори',
                'id' => 2,
            ],
            [
                'name' => 'По споразумение',
                'id' => 3,
            ],
            [
                'name' => 'По категория',
                'id' => 4,
            ],
            [
                'name' => 'По НТП',
                'id' => 5,
            ],
            [
                'name' => 'По вид собственост',
                'id' => 6,
            ],
        ];

        $return[0]['selected'] = 'true';

        return $return;
    }
}
