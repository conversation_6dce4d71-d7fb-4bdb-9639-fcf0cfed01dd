<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

/**
 * Get all countries.
 *
 * @rpc-module Common
 *
 * @rpc-service-id countries-combobox
 */
class CountriesCombobox extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getCountriesCombobox']],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $filterObj
     *
     * @return array
     */
    public function getCountriesCombobox()
    {
        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'tablename' => 'countries',
            'return' => ['id', 'bg_name', 'iso_alpha_2_code', 'iso_alpha_3_code'],
            'where' => [
                'active' => ['column' => 'active', 'compare' => '=', 'value' => true],
            ],
        ];

        return $UserDbController->getItemsByParams($options);
    }
}
