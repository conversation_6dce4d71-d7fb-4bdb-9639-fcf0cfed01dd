<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\WarehouseModuleClass;

/**
 * Лист с възможни сървъри.
 *
 * @rpc-module Common
 *
 * @rpc-service-id warehouse-constants-combobox
 */
class WarehouseConstantsCombobox extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'read']],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item boolean selected
     *                         }
     *
     * @return array
     */
    public function read($rpcParams = null)
    {
        $warehouseModule = new WarehouseModuleClass();
        $warehouses = $warehouseModule->getConstants($rpcParams);

        return $warehouses['result'];
    }
}
