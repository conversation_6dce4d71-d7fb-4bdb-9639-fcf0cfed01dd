<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;
use TF\Application\Common\Config;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\Users\UsersController;

include_once __DIR__ . '/../../Plugins/Core/Layers/conf/index.php';

/**
 * Списък със ЕКАТТЕ-та в даден "За ИСАК" слой.
 *
 * @rpc-module Common
 *
 * @rpc-service-id for-isak-ekate-combobox
 */
class ForIsakEkateCombobox extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getEKATTECombox']],
        ];
    }

    /**
     * Get all EKATTE areas, associated with the current user.
     *
     * @param array $filterObj
     *
     * @return array
     */
    public function getEKATTECombox($filterObj)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        $LayersController = new LayersController('Layers');

        if (!$filterObj['layer_id']) {
            return [];
        }

        $table = $LayersController->getLayerData($filterObj['layer_id']);
        if (!$table) {
            return [];
        }
        $tableName = $table['table_name'];

        $options = [
            'return' => ['DISTINCT(ekatte)'],
            'tablename' => $tableName,
        ];

        $results = $UserDbController->getItemsByParams($options, false, false);

        $return = [];
        $count = count($results);
        for ($i = 0; $i < $count; $i++) {
            if ('' != $results[$i]['ekatte'] || null != $results[$i]['ekatte']) {
                $return[] = [
                    'ekate' => $results[$i]['ekatte'],
                    'text' => $results[$i]['ekatte'] . ' (' . $UsersController->getEkatteName($results[$i]['ekatte']) . ')',
                ];
            }
        }

        if (true == $filterObj['without_farm']) {
            $withoutFarm = [
                'ekate' => 'null',
                'text' => 'Без ЕКАТТЕ',
            ];

            array_unshift($return, $withoutFarm);
        }

        return $return;
    }

    public function getAllForIsakEkatte($rpcParams = [])
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $return = [];
        $ekatte_codes = [];

        $isakTables = $UsersController->getItemsByParams(
            [
                'tablename' => $UserDbController->DbHandler->tableLayers,
                'where' => [
                    'is_exist' => ['column' => 'is_exist', 'compare' => '=', 'value' => 't'],
                    'layer_type' => ['column' => 'layer_type', 'compare' => '=', 'value' => Config::LAYER_TYPE_FOR_ISAK],
                    'group_id' => ['column' => 'group_id', 'compare' => '=', 'value' => $this->User->groupID],
                ],
                'return' => ['table_name'],
            ]
        );

        if (empty($isakTables)) {
            return $return;
        }

        foreach ($isakTables as $index => $isakTable) {
            if (!$UserDbController->getTableNameExist($isakTable['table_name'])) {
                continue;
            }
            $result = $UserDbController->getItemsByParams(
                [
                    'tablename' => $isakTable['table_name'],
                    'return' => ['DISTINCT(ekatte)'],
                ]
            );
            $ekatte_values = array_values(array_column($result, 'ekatte'));
            foreach ($ekatte_values as $code) {
                $return[$isakTable['table_name']][$code] = null;
            }
            $ekatte_codes = array_merge($ekatte_codes, $ekatte_values);
        }

        unset($ekatte_values, $index, $isakTables, $isakTable, $result, $code);

        $ekatte_data = $UsersController->getEkatteNameMulti(
            [
                'return' => ['ekatte_code as code', 'ekatte_name as name'],
                'ekatte_codes' => array_filter(array_unique($ekatte_codes, SORT_REGULAR)),
            ]
        );
        $ekatte = [];
        foreach ($ekatte_data as $row) {
            $ekatte[$row['code']] = $row['name'];
        }

        unset($row ,$ekatte_data,$ekatte_codes,$UserDbController,$UsersController);
        $counter = 0;
        foreach ($return as $table_key => $table) {
            foreach ($table as $code => $name) {
                if (!isset($code, $ekatte[$code]) || '' == $code) {
                    continue;
                }
                $return[$table_key][$counter] = ['ekate' => $code, 'text' => $code . ' (' . $ekatte[$code] . ')'];
                unset($return[$table_key][$code]);
                ++$counter;
            }
            $return[$table_key] = array_filter(array_values($return[$table_key]));
            usort($return[$table_key], ['ForIsakEkateCombobox', 'sortEkatte']);
            if (!empty($return[$table_key])) {
                array_unshift(
                    $return[$table_key],
                    ['ekate' => 'null', 'text' => 'Без ЕКАТТЕ']
                );
            }
            array_unshift(
                $return[$table_key],
                ['ekate' => '', 'text' => 'Всички']
            );
        }

        return $return;
    }

    private function sortEkatte($a, $b)
    {
        return strcmp($a['text'], $b['text']);
    }
}
