<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;

class ConsolidationDoDogKodCombobox extends TRpcApiProvider
{
    public function registerMethods(): array
    {
        return [
            'read' => ['method' => [$this, 'getConsolidationDoDogKodCombobox']],
        ];
    }

    /**
     * Код за вид документ за наем/аренда.
     */
    public function getConsolidationDoDogKodCombobox(): array
    {
        return [
            [
                'name' => 'Договор за аренда',
                'id' => 1,
            ],
            [
                'name' => 'Договор за наем',
                'id' => 2,
            ],
            [
                'name' => 'Договор за съвместна обработка по чл.31, ал.4, т.3 от ЗК',
                'id' => 3,
            ],
            [
                'name' => 'Договор за преарендоване',
                'id' => 4,
            ],
            [
                'name' => 'Договор за пренаемане',
                'id' => 5,
            ],
        ];
    }
}
