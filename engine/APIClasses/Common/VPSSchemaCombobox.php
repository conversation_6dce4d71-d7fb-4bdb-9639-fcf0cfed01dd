<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;

/**
 * @rpc-module Common
 *
 * @rpc-service-id vps-schema-types-combobox
 */
class VPSSchemaCombobox extends TRpcApiProvider
{
    /**
     * Списък с ВПС слоевете
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getVPSSchemas']],
        ];
    }

    /**
     * Get all EKATTE areas, associated with the current user.
     *
     * @return array {
     *               #item integer id,
     *               #item string name,
     *               #item string selected
     *               }
     */
    public function getVPSSchemas($selected)
    {
        $return = [];
        foreach ($GLOBALS['Farming']['vps_schema_types'] as $item) {
            $return[] = [
                'id' => $item['id'],
                'name' => $item['name'],
            ];
        }

        if ($selected && 'true' == $selected) {
            $return[0]['selected'] = 'true';
        }

        return $return;
    }
}
