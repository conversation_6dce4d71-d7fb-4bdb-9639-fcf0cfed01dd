<?php

namespace TF\Engine\APIClasses\Common;

// Prado::using('Plugins.Core.Users.*');
use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\LoggerMessages;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Смяна на парола.
 *
 * @rpc-module Common
 *
 * @rpc-service-id change-password
 */
class ChangeUserPassword extends TRpcApiProvider
{
    private $UserDbController = false;
    private $UsersController = false;

    public function registerMethods()
    {
        return [
            'changePassword' => ['method' => [$this, 'changePassword']],
        ];
    }

    /**
     * @api-method changePassword
     *
     * @param array $rpcParams
     *                         {
     *                         #item string password
     *                         #item string rePassword
     *                         }
     *
     * @throws Exception
     * @throws MTRpcException
     */
    public function changePassword($rpcParams)
    {
        $UsersController = new UsersController('Users');

        if (trim($rpcParams['password']) != trim($rpcParams['rePassword'])) {
            throw new MTRpcException('passwords_does_not_match', -33015);
        }

        if (trim($rpcParams['password'])) {
            $fields['password'] = crypt($rpcParams['password']);
        }

        $success = $UsersController->editUser($this->User->UserID, $fields);

        if ($success) {
            $UsersController->updateUserSystemPassword($this->User->UserID, $rpcParams['password']);
        }

        $UsersController->log(1, $this->User->Name, LoggerMessages::ITEM_EDIT, [$success]);
    }
}
