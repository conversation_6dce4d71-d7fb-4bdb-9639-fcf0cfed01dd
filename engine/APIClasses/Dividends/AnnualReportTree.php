<?php

namespace TF\Engine\APIClasses\Dividends;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Annual Report Tree Class.
 *
 * @rpc-module Dividends
 *
 * @rpc-service-id annual-report-tree
 */
class AnnualReportTree extends TRpcApiProvider
{
    private $module = 'Dividends';
    private $service_id = 'annual-report-tree';

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'create' => ['method' => [$this, 'createAnnualReport']],

            'read' => ['method' => [$this, 'readAnnualReports']],

            'update' => ['method' => [$this, 'updateAnnualReport']],

            'load' => ['method' => [$this, 'loadAnnualReport']],

            'delete' => ['method' => [$this, 'deleteAnnualReport']],
        ];
    }

    /**
     * Create Annual Report.
     *
     * @api-method create
     *
     * @param array $formData {
     *                        #item string end_date (e.g. '2015-06-19')
     *                        #item string start_date (e.g. '2015-06-19')
     *                        #item float distribution_amount
     *                        #item float increase_capital_amount
     *                        #item float win_amount
     *                        #item float tax_amount
     *                        }
     */
    public function createAnnualReport($formData)
    {
        $endDateForReport = $formData['end_date'];
        $startDateForReport = $formData['start_date'];
        $distributionAmount = $formData['distribution_amount'];
        $increaseCapitalAmount = $formData['increase_capital_amount'];
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $options = [
            'tablename' => $UserDbController->DbHandler->tableDividendsAnnualReport,
            'mainData' => [
                'start_date' => $startDateForReport,
                'end_date' => $endDateForReport,
                'win_amount' => $formData['win_amount'],
                'tax_amount' => $formData['tax_amount'],
                'distribution_amount' => $distributionAmount,
                'increase_capital_amount' => $increaseCapitalAmount,
                'date_created' => 'now()',
            ],
        ];

        $reportId = $UserDbController->addItem($options);
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options, $reportId, 'Creating annual report');

        $results = $this->getAllCooperatorsForReport($endDateForReport, $startDateForReport);
        $cooperators = $results[0];
        $sumPaidInCapital = $results[1];

        $countResults = count($results);
        if ($countResults > 0) {
            $this->countPercentForCooperators($cooperators, $sumPaidInCapital, $distributionAmount, $increaseCapitalAmount);
            $this->evaluateCooperator($cooperators, $reportId);
        }
    }

    /**
     * Read Annual Reports.
     *
     * @api-method read
     *
     * @param array $filterParams {
     *                            #item string start_date (e.g. '2015-06-19')
     *                            #item string end_date (e.g. '2015-06-19')
     *                            }
     * @param int $page
     * @param int $rows
     * @param string $sort
     * @param string $order
     *
     * @return array
     */
    public function readAnnualReports($filterParams, $page = '', $sort = '', $order = '')
    {
        if ($this->User->isGuest) {
            return;
        }

        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableDividendsAnnualReport,
            'return' => [
                "to_char(start_date, 'DD.MM.YYYY') || ' - ' || to_char(end_date, 'DD.MM.YYYY') as dates",
                '*',
            ],
            'where' => [
                'active' => ['column' => 'active', 'compare' => '=', 'value' => 'TRUE'],
            ],
            'sort' => $sort,
            'order' => $order,
        ];

        // filter
        if (isset($filterParams['start_date']) && '' != $filterParams['start_date']) {
            $options['where']['start_date'] = ['column' => 'start_date', 'compare' => '>=', 'value' => $filterParams['start_date']];
        }
        if (isset($filterParams['end_date']) && '' != $filterParams['end_date']) {
            $options['where']['end_date'] = ['column' => 'end_date', 'compare' => '<=', 'value' => $filterParams['end_date']];
        }

        $counter = $UserDbController->getItemsByParams($options, true, false);

        if (0 == $counter[0]['count']) {
            return [];
        }
        $rows = 30;
        $options['offset'] = ($page - 1) * $rows;
        $options['limit'] = $rows;

        $results = $UserDbController->getItemsByParams($options, false, false);
        $resultsCount = count($results);
        for ($i = 0; $i < $resultsCount; $i++) {
            $results[$i]['iconCls'] = 'icon-tree-document';

            $return[] = [
                'text' => 'Отчет ' . $results[$i]['dates'],
                'id' => $results[$i]['id'],
                'attributes' => $results[$i],
                'iconCls' => $results[$i]['iconCls'],
            ];
        }

        // add attribute to first listed element of three for custom pagination
        // information for total can not be sent otherwise
        $return[0]['attributes']['pagination']['total'] = (int) $counter[0]['count'];
        $return[0]['attributes']['pagination']['limit'] = (int) $rows;

        return $return;
    }

    /**
     * Loads Annual Report.
     *
     * @api-method load
     *
     * @param int $id
     *
     * @return array
     */
    public function loadAnnualReport($id)
    {
        if ($this->User->isGuest) {
            return;
        }

        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableDividendsAnnualReport,
            'return' => [
                '*',
                "to_char(start_date, 'YYYY-MM-DD') as start_date",
                "to_char(end_date, 'YYYY-MM-DD') as end_date",
            ],
        ];
        $options['where'] = ['id' => ['column' => 'id', 'compare' => '=', 'value' => $id]];

        $result = $UserDbController->getItemsByParams($options, false, false);

        return $result[0];
    }

    /**
     * updateAnnualReport Update Annual Report.
     *
     * @api-method update
     *
     * @param array $formData {
     *                        #item integer id
     *                        #item string end_date (e.g. '2015-06-19')
     *                        #item string start_date (e.g. '2015-06-19')
     *                        #item float distribution_amount
     *                        #item float increase_capital_amount
     *                        #item float win_amount
     *                        #item float tax_amount
     *                        }
     */
    public function updateAnnualReport($formData)
    {
        if ($this->User->isGuest) {
            return;
        }

        $UserDbController = new UserDbController($this->User->Database);

        $this->deleteAnnualReport($formData['id']);

        $this->createAnnualReport($formData);
    }

    /**
     * deleteAnnualReport delete AnnualReport.
     *
     * @api-method delete
     *
     * @param int $id
     */
    public function deleteAnnualReport($id)
    {
        if ($this->User->isGuest || !$id) {
            return;
        }

        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $old_report = $this->loadAnnualReport($id);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableDividendsAnnualReport,
            'id_string' => $id,
        ];

        $UserDbController->deleteItemsByParams($options);
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $old_report, ['report_id' => $id], 'Deleting Annual report');
    }

    /**
     * evaluateCooperator evaluate cooperator.
     *
     * @param array $cooperators
     * @param int $reportId
     */
    public function evaluateCooperator($cooperators, $reportId)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        $cooperatorsCount = count($cooperators);
        for ($i = 0; $i < $cooperatorsCount; $i++) {
            $cooperator = $cooperators[$i];
            $currentCapital = $cooperator['current_capital'] + (float)$cooperator['increase_capital'];

            $options = [
                'tablename' => $UserDbController->DbHandler->tableCooperators,
                'mainData' => [
                    'current_capital' => $currentCapital,
                ],
                'where' => ['id' => $cooperator['id']],
            ];

            $UserDbController->editItem($options);
            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $cooperator, ['new_capital' => $currentCapital], 'Editing cooperators capital');

            $options = [
                'tablename' => $UserDbController->DbHandler->tableCooperatorsAnualDividends,
                'mainData' => [
                    'cooperator_id' => $cooperator['id'],
                    'annual_report_id' => $reportId,
                    'dividend' => (float)$cooperator['dividend'],
                    'tax' => (float)$cooperator['tax'],
                    'increase_capital' => (float)$cooperator['increase_capital'],
                    'current_capital' => $currentCapital,
                ],
            ];
            $recordID = $UserDbController->addItem($options);
            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options, $recordID, 'Adding Cooperator Annual dividend');
        }
    }

    /**
     * getAllCooperatorsForReport Gets all cooperators for Report from start_date to end_date.
     *
     * @param string $end_date
     * @param string $start_date
     *
     * @return array
     */
    private function getAllCooperatorsForReport($end_date, $start_date)
    {
        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'return' => ['*'],
            'tablename' => $UserDbController->DbHandler->tableCooperators,
            'where' => [
                'excluded' => ['column' => 'excluded', 'compare' => '=', 'value' => 'FALSE'],
                'date_entry' => ['column' => 'date_entry', 'compare' => '<=', 'value' => $end_date],
            ],
            'group' => 'su_cooperators.id',
        ];

        $cooperators = $UserDbController->getItemsByParams($options, false, false);
        $sumPaidInCapital = 0;

        foreach ($cooperators as $cooperator) {
            $sumPaidInCapital += $cooperator['paid_in_capital'];
        }

        return [$cooperators, $sumPaidInCapital];
    }

    /**
     * countPercentForCooperators Count percent for cooperators.
     *
     * @param array &$cooperators
     * @param float $sumPaidInCapital
     * @param float $distributionAmount
     * @param float $increaseCapitalAmount
     */
    private function countPercentForCooperators(&$cooperators, $sumPaidInCapital, $distributionAmount, $increaseCapitalAmount)
    {
        $cooperatorsCount = count($cooperators);
        for ($i = 0; $i < $cooperatorsCount; $i++) {
            $cooperator = &$cooperators[$i];
            $cooperator['percent'] = ($cooperator['paid_in_capital'] / $sumPaidInCapital);

            $cooperator['dividend'] = $distributionAmount * $cooperator['percent'];
            $cooperator['increase_capital'] = $increaseCapitalAmount * $cooperator['percent'];
            $cooperator['tax'] = $cooperator['dividend'] * 0.05;
        }
    }
}
