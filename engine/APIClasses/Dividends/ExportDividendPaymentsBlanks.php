<?php

namespace TF\Engine\APIClasses\Dividends;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\PrintPdf;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

/**
 * Export Dividend Payments Blanks.
 *
 * @rpc-module Dividends
 *
 * @rpc-service-id export-dividend-payments-blanks
 */
class ExportDividendPaymentsBlanks extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'expDividendOrder' => ['method' => [$this, 'expDividendOrder']],
            'expDividendPayment' => ['method' => [$this, 'expDividendPayment']],
        ];
    }

    /**
     * expDividendOrder Export Dividend Order.
     *
     * @api-method expDividendOrder
     *
     * @param array $cooperatorsData data for cooperators
     * @param string $payDate with dyalov capital pay date
     * @param string $cashout with dyalov capital value for pay
     * @param string $representative
     *
     * @return array
     */
    public function expDividendOrder($cooperatorsData, $payDate, $cashout, $representative)
    {
        if ($this->User->isGuest) {
            return;
        }

        $UserDbController = new UserDbController($this->User->Database);
        $FarmingController = new FarmingController('Farming');

        $cooperatorsIds = array_map(function ($cooperatorData) {
            return $cooperatorData['id'];
        }, $cooperatorsData);

        // get cooperator data
        $options = [
            'tablename' => $UserDbController->DbHandler->tableCooperators,
            'where' => [
                'cooperators_id' => ['column' => 'id', 'compare' => 'IN', 'value' => $cooperatorsIds],
            ],
        ];

        $cooperatorsResults = $UserDbController->getItemsByParams($options, false, false);
        $cooperatorsCount = count($cooperatorsResults);
        if (0 == $cooperatorsCount) {
            return [];
        }
        $return = [];

        $ordersDir = SITE_PATH . 'public/files/uploads/orders/';

        if (!file_exists($ordersDir)) {
            $makeDir = mkdir($ordersDir, 0700);
        }

        $cooperatorsIds = implode('_', $cooperatorsIds);

        $ordersPath = 'files/uploads/orders/';
        $pdfPath = $ordersPath . $payDate . '_' . $cooperatorsIds . '_razhoden_order.pdf';

        for ($i = 0; $i < $cooperatorsCount; $i++) {
            $cooperator = $cooperatorsResults[$i];
            $amount = number_format($cashout, 2, '.', '');
            sscanf($cashout, '%d.%d', $whole, $fraction);
            $printData['date'] = strftime('%d.%m.%Y', strtotime($payDate));

            $cooperatorName = $cooperator['name'] . ' ' . $cooperator['surname'] . ' ' . $cooperator['lastname'];
            $printData['name'] = $representative['representative'] ? $representative['representative'] : $cooperatorName;
            $printData['egn'] = $representative['representative_egn'] ? $representative['representative_egn'] : $cooperator['egn'];

            if (!$representative[$i]['recipient']) {
                $printData['egn'] = $cooperator['egn'];
            }

            if ($representative[$i]['recipient'] && $representative[$i]['recipient'] != $cooperatorName) {
                $printData['recipient'] = $cooperatorName;
            }

            $printData['proxy'] = $results[$i]['representative_proxy'];
            $printData['lk_izdavane'] = $cooperator['lk_izdavane'];
            $printData['for'] = 'Изплащане на дялов капитал';
            $printData['price'] = $amount . ' лева';
            $printData['price_text'] = $FarmingController->StringHelper->numToString(abs($amount)) . ' лева и ' . ((int) $fraction) . ' ст.';

            $template = $FarmingController->StringHelper->loadTemplate($GLOBALS['Templates'][4]['template'], $printData);
            $finalTemplate .= '<page style="font-family: freeserif"><br />' . $template . '</page>';
        }

        $printPdf = new PrintPdf();
        $printPdf->generateFromHtml($finalTemplate, $pdfPath, ['orientation' => 'Landscape'], true);

        $return['pdf_blank_file'] = $pdfPath;

        return $return;
    }

    /**
     * expDividendPayment Export Dividend Payment.
     *
     * @api-method expDividendPayment
     *
     * @param array $cooperatorsData
     * @param string $cashout with dyalov capital value for pay
     * @param string $iban
     *
     * @return array
     */
    public function expDividendPayment($cooperatorsData, $cashout, $iban)
    {
        if ($this->User->isGuest) {
            return;
        }

        $UserDbController = new UserDbController($this->User->Database);
        $FarmingController = new FarmingController('Farming');

        $cooperatorsIds = array_map(function ($cooperatorData) {
            return $cooperatorData['id'];
        }, $cooperatorsData);

        // get cooperator data
        $options = [
            'tablename' => $UserDbController->DbHandler->tableCooperators,
            'where' => [
                'cooperator_id' => ['column' => 'id', 'compare' => 'IN', 'value' => $cooperatorsIds],
            ],
        ];

        $cooperatorsResults = $UserDbController->getItemsByParams($options, false, false);
        $cooperatorsCount = count($cooperatorsResults);

        if (0 == $cooperatorsCount) {
            return [];
        }

        $return = [];

        $paymentsDir = SITE_PATH . 'public/files/uploads/payments/';
        if (!file_exists($paymentsDir)) {
            $makeDir = mkdir($paymentsDir, 0700);
        }

        $cooperatorsIds = implode('_', $cooperatorsIds);

        $paymentPath = 'files/uploads/payments/';
        $pdfPath = $paymentPath . $cooperatorsIds . '_payment_order.pdf';

        for ($i = 0; $i < $cooperatorsCount; $i++) {
            $cooperator = $cooperatorsResults[$i];

            $name = $cooperator['name'] . ' ' . $cooperator['surname'] . ' ' . $cooperator['lastname'];
            $printData['recipient'] = $FarmingController->StringHelper->strToBlankFormat($name, 36);
            $printData['bank_acc'] = $FarmingController->StringHelper->strToBlankFormat($iban, 22);
            $printData['bic'] = '&emsp;' . str_repeat('|&emsp;', 7);
            $printData['bank'] = '&emsp;' . str_repeat('|&emsp;', 36);
            $printData['amount'] = $FarmingController->StringHelper->strToBlankFormat(number_format($cashout, 2, '.', ''), 13, 'right');
            $printData['amount'] = str_replace('|&nbsp;.&nbsp;|', '<span style="font-size: 20px;vertical-align:text-bottom;">|</span>', $printData['amount']);
            $printData['details'] = $FarmingController->StringHelper->strToBlankFormat('Изплащане на дялов капитал', 36);
            $printData['additionalDetails'] = '&emsp;' . str_repeat('|&emsp;', 36);
            $printData['orderer'] = '&emsp;' . str_repeat('|&emsp;', 36);
            $printData['orderer_bank_acc'] = '&emsp;' . str_repeat('|&emsp;', 21);
            $printData['orderer_bic'] = '&emsp;' . str_repeat('|&emsp;', 7);
            $printData['payment_system'] = '&emsp;' . str_repeat('|&emsp;', 16);
            $printData['charges'] = '&emsp;' . str_repeat('|&emsp;', 2);
            $printData['date'] = '&emsp;' . str_repeat('|&emsp;', 6);

            $template = $FarmingController->StringHelper->loadTemplate($GLOBALS['Templates'][15]['template'], $printData);
            $finalTemplate .= '<page style="font-family: freeserif"><br />' . $template . '</page>';
        }

        $printPdf = new PrintPdf();
        $printPdf->generateFromHtml($finalTemplate, $pdfPath, [], true);

        $return['pdf_blank_file'] = $pdfPath;

        return $return;
    }
}
