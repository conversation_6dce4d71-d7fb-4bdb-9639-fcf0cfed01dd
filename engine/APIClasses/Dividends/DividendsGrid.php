<?php

namespace TF\Engine\APIClasses\Dividends;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\ExportWordDocClass;
use TF\Engine\Kernel\PrintPdf;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbCooperators\UserDbCooperatorsController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Dividends Grid.
 *
 * @rpc-module Dividends
 *
 * @rpc-service-id dividends-grid
 */
class DividendsGrid extends TRpcApiProvider
{
    private $module = 'Dividends';
    private $service_id = 'dividends-grid';

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readDividends'],
                'validators' => [
                    'report_id' => 'validateInteger',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],

            'createDividend' => ['method' => [$this, 'createDividend']],

            'createDividendReversal' => ['method' => [$this, 'createDividendReversal']],

            'expDividendReport' => ['method' => [$this, 'expDividendReport']],

            'removeFile' => ['method' => [$this, 'removeFile']],
        ];
    }

    /**
     * readDividends read Dividends.
     *
     * @api-method read
     *
     * @param int $page
     * @param int $rows
     * @param ?string $sort
     * @param ?string $order
     *
     * @return array
     */
    public function readDividends(int $report_id, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        if ($this->User->isGuest) {
            return;
        }

        $UserDbCooperatorsController = new UserDbCooperatorsController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);

        // creating the default return
        $default = [
            'rows' => [],
            'total' => 0,
            'footer' => [
            ],
        ];

        $annualReport = $this->_loadAnnualReport($report_id);

        // main result
        $options = [
            'tablename' => $UserDbController->DbHandler->tableCooperators,
            'return' => [
                'c.id', 'c.is_dead', 'c.date_dead', 'egn',
                "NAME || ' ' || surname || ' ' || lastname AS cooperator_names",
                'round(SUM(COALESCE(cashout::numeric, 0))::numeric, 2) AS cashout',
                "MAX (to_char(dp.pay_date, 'YYYY-MM-DD')) AS pay_date",
                'paid_in_capital',
                'round(dividend::numeric, 2) as dividend',
                'round(tax::numeric, 2) as tax',
                'round(increase_capital::numeric, 2) as increase_capital',
                'round(cad.current_capital::numeric, 2) as current_capital',
                'CASE WHEN round((sum(COALESCE(cashout::numeric, 0)) - (dividend - tax))::numeric, 2) > 0
					THEN 0
					ELSE round(abs(sum(COALESCE(cashout::numeric, 0)) - (dividend - tax))::numeric, 2)
				END AS due_dividend',
                'CASE WHEN round((sum(COALESCE(cashout::numeric, 0)) - (dividend - tax))::numeric, 2) < 0
					THEN 0
					ELSE round(abs(sum(COALESCE(cashout::numeric, 0)) - (dividend - tax))::numeric, 2)
				END AS over_paid',
            ],
            'where' => [
                'excluded' => ['column' => 'excluded', 'compare' => '=', 'value' => 'FALSE'],
                'date_entry_end' => ['column' => 'date_entry', 'compare' => '<=', 'value' => $annualReport['end_date']],
                'annual_report_id' => ['column' => 'annual_report_id', 'prefix' => 'cad', 'compare' => '=', 'value' => '' . $report_id . ''],
                'dividend' => ['column' => 'dividend', 'prefix' => 'cad', 'compare' => '>', 'value' => '0'],
                'heritor_only' => ['column' => 'heritor_only', 'compare' => '=', 'value' => 'FALSE'],
            ],
            'group' => 'c.id,dp.cooperator_id, cooperator_names, paid_in_capital,cad.dividend, cad.tax, cad.increase_capital,cad.current_capital',
            'custom_counter' => 'count(distinct(c.id))',
            'join_is_heritor' => 'false',
        ];

        // pagination
        $options['sort'] = $sort;
        $options['order'] = $order;
        $options['offset'] = ($page - 1) * $rows;
        $options['limit'] = $rows;

        $counter = $UserDbCooperatorsController->getDevidendsByAnnualReport($options, true);

        if (0 == $counter[0]['count']) {
            return $default;
        }

        $allResults = $UserDbCooperatorsController->getDevidendsByAnnualReport($options, false, false);
        $allResultsCount = count($allResults);
        $totalPaidInCapital = 0;

        for ($i = 0; $i < $allResultsCount; $i++) {
            $cooperator = &$allResults[$i];
            $cooperator['uid'] = $cooperator['id'];
            $totalPaidInCapital += $allResults[$i]['paid_in_capital'];
            if ($allResults[$i]['is_dead']) {
                $this->getCooperatorHeritors($allResults[$i]['id'] . '.*{1}', $annualReport, $cooperator);
                $allResults[$i]['iconCls'] = 'icon-tree-user-rip';
            } else {
                $allResults[$i]['iconCls'] = 'icon-tree-user';
            }
        }

        $return = [
            'total' => $counter[0]['count'],
            'rows' => $allResults,
            'footer' => [
            ],
        ];

        return $return;
    }

    /**
     * createDividend create Dividend.
     *
     * @api-method createDividend
     *
     * @param array $formData {
     *
     * @item array cooperators_id
     * @item float cashout
     * @item string iban
     * @item string representative
     * @item string pay_date (e.g. '2015-06-19')
     * @item boolean generate_order
     * @item boolean generate_payment
     * }
     */
    public function createDividend($formData)
    {
        if ($this->User->isGuest) {
            return;
        }

        $UserDbController = new UserDbController($this->User->Database);
        $cooperatorsData = $formData['cooperators_data'];
        $UsersController = new UsersController('Users');
        $cooperatorsDataCount = count($cooperatorsData);
        for ($i = 0; $i < $cooperatorsDataCount; $i++) {
            $cooperatorId = $cooperatorsData[$i]['id'];
            $cooperatorPath = $cooperatorsData[$i]['path'];
            $isHeritor = $cooperatorsData[$i]['is_heritor'];

            $cooperator = $this->_loadCooperator($cooperatorId);
            $owe_capital = $cooperator['current_capital'];
            $cashout = $formData['cashout'];

            // add dividend payment
            $options = [
                'tablename' => $UserDbController->DbHandler->tableDividendsPayment,
                'mainData' => [
                    'cooperator_id' => $cooperatorId,
                    'annual_report_id' => $formData['report_id'],
                    'current_capital' => $owe_capital,
                    'cashout' => $cashout,
                    'iban' => $formData['iban'],
                    'representative' => $formData['representative'],
                    'representative_egn' => $formData['representative_egn'],
                    'representative_proxy' => $formData['representative_proxy'],
                    'pay_date' => $formData['pay_date'],
                    'generate_order' => $formData['generate_order'] ? true : false,
                    'generate_payment' => $formData['generate_payment'] ? true : false,
                    'date_created' => 'now()',
                    'path' => $cooperatorPath,
                    'is_heritor' => $isHeritor ? 'true' : 'false',
                ],
            ];

            $recordID = $UserDbController->addItem($options);
            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options, $recordID, 'Adding dividend payment');
        }
    }

    /**
     * createDividendReversal create Dividend Reversal.
     *
     * @api-method createDividendReversal
     *
     * @param array $formData {
     *
     * @item array cooperators_id
     * @item float cashout
     * @item string pay_date (e.g. '2015-06-19')
     * }
     */
    public function createDividendReversal($formData)
    {
        if ($this->User->isGuest) {
            return;
        }

        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $cooperatorsData = $formData['cooperators_data'];
        $cooperatorsDataCount = count($cooperatorsData);

        for ($i = 0; $i < $cooperatorsDataCount; $i++) {
            $cooperatorId = $cooperatorsData[$i]['id'];
            $cooperatorPath = $cooperatorsData[$i]['path'];
            $isHeritor = $cooperatorsData[$i]['is_heritor'];

            $cooperator = $this->_loadCooperator($cooperatorId);
            $owe_capital = $cooperator['current_capital'];
            $cashout = -1 * abs((float)$formData['cashout']);

            // add capital payment
            $options = [
                'tablename' => $UserDbController->DbHandler->tableDividendsPayment,
                'mainData' => [
                    'cooperator_id' => $cooperatorId,
                    'annual_report_id' => $formData['report_id'],
                    'current_capital' => $owe_capital,
                    'cashout' => $cashout,
                    'representative' => $formData['representative'],
                    'representative_egn' => $formData['representative_egn'],
                    'representative_proxy' => $formData['representative_proxy'],
                    'pay_date' => $formData['pay_date'],
                    'date_created' => 'now()',
                    'path' => $cooperatorPath,
                    'is_heritor' => $isHeritor ? 'true' : 'false',
                ],
            ];
            $recordID = $UserDbController->addItem($options);
            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options, $recordID, 'Adding dividend reversal payment');
        }
    }

    /**
     * expDividendReport Export dividend report.
     *
     * @api-method expDividendReport
     *
     * @param int $reportId
     * @param string $reportType
     * @param string $reportName
     *
     * @return array
     */
    public function expDividendReport($reportId, $reportType, $reportName)
    {
        if ($this->User->isGuest) {
            return;
        }

        $UserDbController = new UserDbController($this->User->Database);
        $FarmingController = new FarmingController('Farming');

        $results = $this->readDividends($reportId);

        $allResultsList = [];
        $this->makeTreeToLst($results['rows'], $allResultsList);

        $results['rows'] = $allResultsList;
        $results['title'] = $reportName;
        $results['header'] = [
            'number' => ['text' => 'Номер', 'width' => '30'],
            'cooperator_names' => ['text' => 'Три имена', 'width' => '150'],
            'dividend' => ['text' => 'Разпределен дивидент', 'width' => '120'],
            'tax' => ['text' => 'Данък', 'width' => '60'],
            'due_dividend' => ['text' => 'Дължим дивидент', 'width' => '80'],
            'cashout' => ['text' => 'Изплатен дивидент', 'width' => '80'],
            'over_paid' => ['text' => 'Надплатен дивидент', 'width' => '80'],
            'pay_date' => ['text' => 'Дата на последно изплащане', 'width' => '80'],
            'increase_capital' => ['text' => 'Увеличение на дяловия капитал', 'width' => '80'],
        ];

        $reportsPath = 'files/uploads/blanks/';
        $template = $FarmingController->StringHelper->loadTemplate($GLOBALS['Templates'][11]['template'], $results);
        $return = [];
        $date = date('Y-m-d-H-i-s');
        if ('pdf' == $reportType) {
            $fileName = 'godishen_otchet_' . $reportId . '_' . $date . '.pdf';
            $pdfPath = $reportsPath . $fileName;
            $template = '<page style="font-family: freeserif" backtop="50px" backbottom="50px" >' . $template . '</page>';

            $printPdf = new PrintPdf();
            $printPdf->generateFromHtml($template, $pdfPath, ['orientation' => 'Landscape'], true);

            $return['path'] = $pdfPath;
            $return['file_name'] = $fileName;
        }
        if ('doc' == $reportType) {
            $fileName = $reportId . '_' . $date;
            $docPath = $reportsPath . $fileName;
            $reportName = 'godishen_otchet_' . $fileName;

            $exportWordDoc = new ExportWordDocClass();
            $return['path'] = $exportWordDoc->export($reportName, $template, true);
            $return['file_name'] = $reportName . '.doc';
        }

        return $return;
    }

    /**
     * removeFile remove File from the server.
     *
     * @api-method removeFile
     *
     * @param string $path
     */
    public function removeFile($path)
    {
        @unlink(PUBLIC_UPLOAD_BLANK . '/' . $path);
    }

    /**
     * _loadCooperator Loads cooperator.
     *
     * @param int $id
     *
     * @return array
     */
    private function _loadCooperator($id)
    {
        if ($this->User->isGuest) {
            return;
        }

        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableCooperators,
            'return' => [
                '*',
                "to_char(date_entry, 'YYYY-MM-DD') as dateЕntry",
                "to_char(date_excluded, 'YYYY-MM-DD') as dateЕxcluded",
            ],
        ];
        $options['where'] = ['id' => ['column' => 'id', 'compare' => '=', 'value' => $id]];

        $result = $UserDbController->getItemsByParams($options, false, false);

        return $result[0];
    }

    /**
     * _loadAnnualReport load Annual Report.
     *
     * @param int $id
     *
     * @return array
     */
    private function _loadAnnualReport($id)
    {
        if ($this->User->isGuest) {
            return;
        }

        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableDividendsAnnualReport,
            'return' => [
                '*',
            ],
        ];
        $options['where'] = ['id' => ['column' => 'id', 'compare' => '=', 'value' => $id]];

        $result = $UserDbController->getItemsByParams($options, false, false);

        return $result[0];
    }

    /**
     * makeTreeToLst makes tree to list.
     *
     * @param array $tree
     * @param array &$list
     */
    private function makeTreeToLst($tree, &$list)
    {
        $treeCount = count($tree);
        for ($i = 0; $i < $treeCount; $i++) {
            $node = $tree[$i];
            if ($node['children']) {
                $this->makeTreeToLst($node['children'], $list);
            }
            $list[] = $node;
        }
    }

    /**
     * getCooperatorHeritors Get cooperator heritors.
     *
     * @param string $path
     * @param array $report
     * @param array &$allResults
     *
     * @return array
     */
    private function getCooperatorHeritors($path, $report, &$allResults)
    {
        $UserDbCooperatorsController = new UserDbCooperatorsController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableCooperators,
            'return' => [
                'ch.heritor_current_capital as heritor_current_capital',
                "NAME || ' ' || surname || ' ' || lastname AS cooperator_names",
                'c.id',
                'c.is_dead',
                'c.date_dead',
                'ch.path',
                'SUM(dp.cashout) as cashout',
                "MAX (to_char(dp.pay_date, 'YYYY-MM-DD')) AS pay_date",
            ],
            'where' => [
                'excluded' => ['column' => 'excluded', 'compare' => '=', 'value' => 'FALSE'],
                'date_entry_end' => ['column' => 'date_entry', 'compare' => '<=', 'value' => $report['end_date']],
                'date_entry_start' => ['column' => 'date_entry', 'compare' => '>=', 'value' => $report['start_date']],
                'annual_report_id' => ['column' => 'cad.annual_report_id', 'compare' => '=', 'value' => $report['id']],
                'path' => ['column' => 'ch.path', 'compare' => '~', 'value' => $path],
            ],
            'group' => 'c.id, dp.cooperator_id, cooperator_names, paid_in_capital, cad.dividend, cad.tax, cad.increase_capital, ch.path, ch.heritor_current_capital',
            'custom_counter' => 'count(distinct(c.id))',
            'join_is_heritor' => 'true',
            'join_heritors_path' => 'true',
        ];

        $allResults['children'] = $UserDbCooperatorsController->getDevidendsByAnnualReport($options, false, false);
        $counter = $UserDbCooperatorsController->getDevidendsByAnnualReport($options, true);

        if (0 == $counter[0]['count']) {
            return [];
        }
        $count = count($allResults['children']);
        for ($i = 0; $i < $count; $i++) {
            $heritor = &$allResults['children'][$i];
            $cooperatorTimeOfDead = date($allResults['date_dead']);
            $reportTimeCreated = date($report['date_created']);

            if (null != $cooperatorTimeOfDead && $reportTimeCreated > $cooperatorTimeOfDead) {
                $capital = (float)$allResults['current_capital'] - (float)$allResults['increase_capital'];
            } else {
                $capital = (float)$allResults['current_capital'];
            }

            $inheretedPercent = ($capital / (float)$heritor['heritor_current_capital']);

            $heritor['dividend'] = number_format($allResults['dividend'] / $inheretedPercent, 2, '.', '');
            $heritor['cashout'] = number_format($heritor['cashout'] ? ($allResults['cashout'] / $inheretedPercent) + $heritor['cashout']
                                                                                                 : $allResults['cashout'] / $inheretedPercent, 2, '.', '');
            $heritor['tax'] = number_format($heritor['dividend'] * 0.05, 2, '.', '');
            $heritor['uid'] = $allResults['id'] . '.' . $heritor['id'];
            $heritor['is_heritor'] = true;

            $dueDividend = number_format(($allResults['due_dividend'] / $inheretedPercent) - (float)$heritor['cashout'], 2, '.', '');
            $heritor['due_dividend'] = $dueDividend >= 0 ? $dueDividend : '0';
            $heritor['over_paid'] = $dueDividend <= 0 ? abs($dueDividend) : '0';
            $heritor['increase_capital'] = number_format($allResults['increase_capital'] / $inheretedPercent, 2, '.', '');

            if ($heritor['is_dead']) {
                $allResults['children'][$i]['iconCls'] = 'icon-tree-user-rip';
                $this->getCooperatorHeritors($heritor['id'] . '.*{1}', $report, $allResults['children']);
            } else {
                $allResults['children'][$i]['iconCls'] = 'icon-tree-user';
            }
        }
    }
}
