<?php

namespace TF\Engine\APIClasses\Diary;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\ArrayHelper;
use TF\Engine\Kernel\Loader;
use TF\Engine\Kernel\StringHelper;
use TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * All map related functions inside the Diary submodule.
 *
 * @rpc-module Diary
 *
 * @rpc-service-id kvs-diary-grid
 */

// Prado::using('Plugins.Core.Users.*');
// Prado::using('Plugins.Core.UserDbPlots.*');
// Prado::using('Plugins.Core.Layers.conf');

/**
 * Class KVSDiaryGrid.
 *
 * @property $arrayHelper ArrayHelper
 * @property $StringHelper String
 */
class KVSDiaryGrid extends TRpcApiProvider
{
    private $arrayHelper;
    private $StringHelper;

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return ['read' => ['method' => [$this, 'getPlotsSimple'],
            'validators' => [
                'rpcParams' => 'validateArray',
                'page' => 'validateInteger',
                'rows' => 'validateInteger',
                'sort' => 'validateSort',
                'order' => 'validateOrder',
            ],
        ]];
    }

    /**
     * Get the data for the plot, which will be used to populate the edit fields.
     *
     * @api-method getPlotsSimple
     *
     * @param string $page
     * @param string $rows
     * @param ?string $sort
     * @param ?string $order
     *
     * @return array
     *
     * @internal param array $filterParams {
     *     #item array  ekate {items[] string}
     *     #item string masiv
     *     #item string number
     *     #item string kad_ident
     *     #item array  category {items[] string}
     *     #item array  area_type {items[] string}
     *     #item array  mestnost  {items[] string}
     *     #item array  irrigated_area {items[] string}
     *     #item array  participation {items[] string}
     *  }
     */
    public function getPlotsSimple(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        if ($this->User->isGuest) {
            return [];
        }

        $UsersController = new UsersController('Users');
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);

        date_default_timezone_set('Europe/Sofia');

        $this->arrayHelper = new ArrayHelper();    // new Loader('Kernel.ArrayHelper');
        $this->StringHelper = new StringHelper();  // new Loader('Kernel.StringHelper');

        // if we receive multiple option for ekatte and one of these is '' , meaning all, simply return the all ('') option
        if (isset($rpcParams['ekate']) && in_array('', $rpcParams['ekate'])) {
            $rpcParams['ekate'] = '';
        }
        if (isset($rpcParams['category']) && in_array('', $rpcParams['category'])) {
            $rpcParams['category'] = '';
        }
        if (isset($rpcParams['area_type']) && in_array('', $rpcParams['area_type'])) {
            $rpcParams['area_type'] = '';
        }
        $select_options = [
            'tablename' => 'layer_kvs',
            'return' => [
                'ST_AsText(ST_Extent(kvs.geom)) AS extent',
                'count(kvs.gid) AS full_count',
            ],
            'where' => [
                'plot_id' => ['column' => 'gid', 'compare' => 'IN', 'value' => $this->arrayHelper->filterEmptyStringArr(explode(',', $rpcParams['plot_id']))],
                'kad_ident' => ['column' => 'kad_ident', 'compare' => '=', 'value' => $rpcParams['kad_ident']],
                'ekate' => ['column' => 'ekate', 'compare' => 'IN', 'value' => $this->arrayHelper->filterEmptyStringArr($rpcParams['ekate'])],
                'masiv' => ['column' => 'masiv', 'compare' => '=', 'value' => $rpcParams['masiv']],
                'number' => ['column' => 'number', 'compare' => '=', 'value' => $rpcParams['number']],
                'area_type' => ['column' => 'area_type', 'compare' => 'IN', 'value' => $this->arrayHelper->filterEmptyStringArr($rpcParams['area_type'])],
                'mestnost' => ['column' => 'mestnost', 'compare' => 'IN', 'value' => $rpcParams['mestnost']],
            ],
            'category' => $this->arrayHelper->filterEmptyStringArr($rpcParams['category']),
        ];

        $this->setParticipation($rpcParams, $select_options);

        // check to see whether irrigation should be considered
        if ($rpcParams['irrigated_area'] && 'all' != $rpcParams['irrigated_area']) {
            $select_options['where']['irrigated_area'] = ['column' => 'irrigated_area', 'compare' => '=', 'value' => $rpcParams['irrigated_area']];
        }

        foreach ($select_options['where'] as &$option) {
            $option['prefix'] = 'kvs';
        }

        $result = $UserDbPlotsController->getDataForPlotsExtent($select_options);

        if (!$result[0]) {
            return [];
        }

        $result = $result[0];

        $this->prepareExtent($result);

        $page_limit = ($rows) ? $rows : 20;

        $select_options['sort'] = $sort;
        $select_options['order'] = $order;

        if ('allow_prec' === $sort && 'desc' === $order) {
            $select_options['order'] .= ' NULLS LAST';
        }

        if ($page) {
            $select_options['offset'] = ($page - 1) * $page_limit;
            $select_options['offset'] = $select_options['offset'] >= 0 ? $select_options['offset'] : 0;
            $select_options['limit'] = $page_limit;
        }

        // get all kvs plots, maintaining the same filters

        $select_options['return'] = explode(',', 'DISTINCT(kvs.gid),ST_AsText(kvs.geom) as geom,ekate,masiv,number,mestnost, virtual_category_title as category, virtual_ntp_title as area_type, used_area,document_area,irrigated_area,allowable_area');
        $select_options['return'][] = 'ST_Area(kvs.geom) as area_kvs';
        $select_options['return'][] = 'ST_AsText(ST_Extent(kvs.geom)) AS extent';
        $select_options['return'][] = ' round(((allowable_area / (ceil(ST_Area(kvs.geom)::NUMERIC ) * 0.001)) * 100) ::NUMERIC,2) AS allow_prec';
        $select_options['group'] = 'kvs.gid';

        $gridRows = $UserDbPlotsController->getDataForPlotsExtent($select_options);
        $countRows = count($gridRows);
        if ($countRows > 0) {
            for ($i = 0; $i < $countRows; $i++) {
                $gridRows[$i]['area_kvs'] = number_format($gridRows[$i]['area_kvs'] * 0.001, 3);
                $gridRows[$i]['used_area'] = number_format($gridRows[$i]['used_area'], 3);
                $gridRows[$i]['allow_prec'] = $gridRows[$i]['allow_prec'] ? $gridRows[$i]['allow_prec'] . ' %' : null;
                $this->prepareExtent($gridRows[$i]);
            }
        }
        $gridData = [
            'rows' => $gridRows,
            'total' => $result['full_count'],
        ];

        return array_merge($result, $gridData);
    }

    private function setParticipation($rpcParams, &$options)
    {
        if ($rpcParams['participation']) {
            switch ($rpcParams['participation']) {
                case 'participate':
                    $options['where']['participate'] = ['column' => 'participate', 'compare' => '=', 'value' => 'true'];
                    $options['where']['include'] = ['column' => 'include', 'compare' => '=', 'value' => 'false'];
                    $options['where']['white_spots'] = ['column' => 'white_spots', 'compare' => '=', 'value' => 'false'];

                    break;
                case 'no_participate':
                    $options['where']['participate'] = ['column' => 'participate', 'compare' => '=', 'value' => 'false'];
                    $options['where']['include'] = ['column' => 'include', 'compare' => '=', 'value' => 'true'];
                    $options['where']['white_spots'] = ['column' => 'white_spots', 'compare' => '=', 'value' => 'false'];

                    break;
                case 'without':
                    $options['where']['participate'] = ['column' => 'participate', 'compare' => '=', 'value' => 'false'];
                    $options['where']['include'] = ['column' => 'include', 'compare' => '=', 'value' => 'false'];
                    $options['where']['white_spots'] = ['column' => 'white_spots', 'compare' => '=', 'value' => 'false'];

                    break;
                default:
                    break;
            }
        }
    }

    /**
     * @param array $data
     */
    private function prepareExtent(&$data = [])
    {
        if ($data['extent']) {
            if (false !== strpos($data['extent'], 'POLYGON')) {
                $data['extent'] = str_replace(' ', ',', trim(str_replace(['POLYGON((', ')'], '', $data['extent'])));
            }
            if (false !== strpos($data['extent'], 'MULTIPOLYGON')) {
                $data['extent'] = str_replace(' ', ',', trim(str_replace(['MULTIPOLYGON(((', ')'], '', $data['extent'])));
            }
        }
    }
}
