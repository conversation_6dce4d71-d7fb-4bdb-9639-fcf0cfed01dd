<?php

namespace TF\Engine\APIClasses\Diary;

use Exception;
use InvalidArgumentException;
use Prado\Web\Services\TRpcApiProvider;
use TF\Application\Common\Config;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbDiary\UserDbDiaryController;
use TF\Engine\Plugins\Core\UserDbZPlots\UserDbZPlotsController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Grid 'Обработки на земеделски парцели'.
 *
 * @rpc-module Diary
 *
 * @rpc-service-id diary-zplot-events-grid
 */
class ZPlotEventsGrid extends TRpcApiProvider
{
    public const EVENT_PLANNED = 1;
    public const EVENT_COMPLETE = 2;

    public const ACTIVITY_TYPES = 1;
    public const ACTIVITY_SUB_TYPES = 2;
    public const MACHINES_GROUPS = 3;
    public const MACHINES = 4;
    public const MEASURE_UNITS = 10;
    public const FUEL_CALC_BY_LITER = 'литра';
    public const FUEL_CALC_BY_AREA = 'литра/дка';
    private $module = 'Diary';
    private $service_id = 'diary-zplot-events-grid';

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getZPlotEventsGrid']],
            'getEventInfo' => ['method' => [$this, 'getEventInfo']],
            'singleEditEvent' => ['method' => [$this, 'loadEditSingleEvent']],
            'saveEvent' => ['method' => [$this, 'saveEvent'],
                'validators' => [
                    'phase_id' => 'validateInteger, validateRequired',
                    'type_id' => 'validateInteger, validateRequired',
                    'subtype_id' => 'validateInteger, validateRequired',
                    'performer_id' => 'validateInteger',
                    'machine_id' => 'validateInteger',
                    'attachment_id' => 'validateInteger',
                    'amortization_cost' => 'validateNumber',
                    'comment' => 'validateText',
                    'plan_fuel_cost' => 'validateNumber',
                    'material_cost_norm' => 'validateNumber',
                    'material_unit_type_id' => 'validateInteger',
                    'material_unit_cost' => 'validateNumber',
                    'is_rented' => 'validateText',
                    'rent_cost' => 'validateNumber',
                    'complete_date_from' => 'validateDate',
                    'complete_date_to' => 'validateDate',
                    'completed_area' => 'validateNumber',
                    'material_final_cost' => 'validateNumber',
                    'final_fuel_cost' => 'validateNumber',
                    'total_fuel_cost' => 'validateNumber',
                    'movement_fuel' => 'validateNumber',
                    'parking_fuel' => 'validateNumber',
                    'start_fuel' => 'validateNumber',
                    'end_fuel' => 'validateNumber',
                    'avg_engine_revs' => 'validateNumber',
                    'avg_speed' => 'validateNumber',
                    'completed_mileage' => 'validateNumber',
                    'time_in' => 'validateTime',
                    'time_out' => 'validateTime',
                    'total_time_in' => 'validateTime',
                    'movement_time' => 'validateTime',
                    'parking_time' => 'validateTime',
                    'plan_date_from' => 'validateDate',
                    'plan_date_to' => 'validateDate',
                    'pest_name' => 'validateText',
                    'substance_id' => 'validateInteger',
                    'substance_technic_id' => 'validateInteger',
                    'substance_dose' => 'validateNumber',
                    'substance_unit_type' => 'validateInteger',
                    'treated_area' => 'validateNumber',
                    'quarantine_period' => 'validateNumber',
                    'event_id' => 'validateInteger',
                    'price_per_litre' => 'validateNumber',
                    'zplot_area' => 'validateNumber',
                ]],
            'deleteSingleEvent' => ['method' => [$this, 'deleteSingleEvent'],
                'validators' => ['eventID' => 'validateInteger, validateRequired'],
            ],
            'loadExpensesForEvent' => ['method' => [$this, 'loadExpensesForEvent'],
                'validators' => [
                    'event_from' => 'validateDate',
                    'event_to' => 'validateDate',
                    'type_id' => 'validateInteger',
                    'subtype_id' => 'validateInteger',
                    'selected' => 'validateInteger',
                    'return_only_subtype' => 'validateInteger',
                ]],
        ];
    }

    /**
     * Returns grid data.
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer farming
     *                         #item integer year
     *                         #item integer zplot_id
     *                         }
     *
     * @return array
     */
    public function getZPlotEventsGrid($rpcParams)
    {
        // init all needed controllers
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbDiaryController = new UserDbDiaryController($this->User->Database);

        // create default return
        $return = [
            'rows' => [],
            'total' => 0,
        ];

        if (!$rpcParams['farming'] || !(int)$rpcParams['farming'] || !$rpcParams['year'] || !(int)$rpcParams['year'] || !$rpcParams['zplot_id'] || !(int)$rpcParams['zplot_id']) {
            return $return;
        }

        $options = [
            'sort' => 'add_date',
            'order' => 'asc',
            'tablename' => $UserDbController->DbHandler->tableDiaryEvents,
            'return' => [
                'de.id', 'de.phase_id', 'de.type_id', 'de.subtype_id', 'de.performer_id', 'de.plan_date_from',
                'de.plan_date_to', 'de.machine_id', 'de.attachment_id', 'de.amortization_cost', 'de.is_rented', 'de.rent_cost', 'de.plan_fuel_cost',
                'de.final_fuel_cost', 'de.price_per_litre', 'de.total_fuel_cost', 'de.add_date', 'de.last_edit',
                'de.farming_id', 'de.year_id', 'de.plot_id', 'de.serial_num', 'de.comment',
                'de.completed_area', 'de.complete_date_from', 'de.complete_date_to', 'de.movement_fuel',
                'de.parking_fuel', 'de.start_fuel', 'de.end_fuel', 'de.avg_engine_revs', 'de.avg_speed',
                'de.time_in', 'de.total_time_in', 'de.time_out', 'de.total_time_in', 'de.movement_time',
                'de.comment', 'de.parking_time', 'de.completed_mileage', 'de.price_per_area', 'de.price_per_area as products_and_expense_cost', 'de.fuel_unit',
                'round(sum(dtp.substance_dose * dtp.treated_area)::numeric, 3) as substance_quantity',
                'array_to_string(ARRAY_AGG(cnf.id || \'-\' || cnf.warehouse_item_id), \',\') as products',
                'string_agg(dp.id::text, \',\') as produces',
            ],
            'where' => [
                'farming_id' => ['column' => 'farming_id', 'compare' => '=', 'value' => $rpcParams['farming']],
                'year_id' => ['column' => 'year_id', 'compare' => '=', 'value' => $rpcParams['year']],
                'plot_id' => ['column' => 'plot_id', 'compare' => '=', 'value' => $rpcParams['zplot_id']],
            ],
        ];
        $events = $UserDbDiaryController->getEventsWithProductsAndProduces($options, false, false);

        $configs_array = $this->getConfigs([
            self::ACTIVITY_TYPES, self::ACTIVITY_SUB_TYPES, self::MACHINES, self::MEASURE_UNITS,
        ]);
        // END OF GET CONFIGS

        $farming_year_from = ($GLOBALS['Farming']['years'][$rpcParams['year']]['year'] - 1) . '-10-01 00:00:00';
        $farming_year_to = $GLOBALS['Farming']['years'][$rpcParams['year']]['year'] . '-09-30 00:00:00';
        $grand_total = 0;

        $count = count($events);
        for ($i = 0; $i < $count; $i++) {
            if ($events[$i]['complete_date_from'] && ($events[$i]['complete_date_from'] < $farming_year_from || $events[$i]['complete_date_from'] > $farming_year_to)) {
                $events[$i]['red_color'] = true;
            } else {
                $events[$i]['red_color'] = false;
            }
            $fuel_cost = 0;
            $events[$i]['event_type'] = $configs_array[$events[$i]['type_id']]['name'];
            $events[$i]['event_subtype'] = $configs_array[$events[$i]['subtype_id']]['name'];
            $events[$i]['phase'] = $GLOBALS['Diary']['event_phase'][$events[$i]['phase_id']]['name'];
            // machine number is the name!!!!!
            $events[$i]['machine_number'] = $configs_array[$events[$i]['machine_id']]['number'];
            if (!is_null($events[$i]['machine_id']) && is_null($events[$i]['machine_number'])) {
                $machine_id = $this->getMachineId($configs_array, $events[$i]['machine_id']);
                $events[$i]['machine_number'] = $configs_array[$machine_id]['number'];
                $events[$i]['machine_id'] = $machine_id;
            }
            $events[$i]['plan_date_from'] = $events[$i]['plan_date_from'] ? $events[$i]['plan_date_from'] : '-';
            $events[$i]['plan_date_to'] = $events[$i]['plan_date_to'] ? $events[$i]['plan_date_to'] : '-';
            $events[$i]['complete_date_from'] = $events[$i]['complete_date_from'] ? $events[$i]['complete_date_from'] : '-';
            $events[$i]['complete_date_to'] = $events[$i]['complete_date_to'] ? $events[$i]['complete_date_to'] : '-';

            if (self::EVENT_PLANNED == $events[$i]['phase_id']) {
                $events[$i]['plan_date_from'] = strftime('%d-%m-%Y %H:%M', strtotime($events[$i]['plan_date_from']));
                $events[$i]['plan_date_to'] = strftime('%d-%m-%Y %H:%M', strtotime($events[$i]['plan_date_to']));
            }

            if (self::EVENT_COMPLETE == $events[$i]['phase_id']) {
                $events[$i]['complete_date_from'] = strftime('%d-%m-%Y %H:%M', strtotime($events[$i]['complete_date_from']));
                $events[$i]['complete_date_to'] = strftime('%d-%m-%Y %H:%M', strtotime($events[$i]['complete_date_to']));
            }

            $events[$i]['fuel_unit'] = (self::FUEL_CALC_BY_LITER == $events[$i]['fuel_unit']) ? 1 : ((self::FUEL_CALC_BY_AREA == $events[$i]['fuel_unit']) ? 2 : 1);

            if (1 == $events[$i]['fuel_unit']) {
                $fuel_cost = $events[$i]['price_per_litre'] * $events[$i]['total_fuel_cost'];
            }
            if (2 == $events[$i]['fuel_unit']) {
                $fuel_cost = $events[$i]['price_per_litre'] * $events[$i]['total_fuel_cost'] * $events[$i]['completed_area'];
            }
            $products_and_expense_cost = $events[$i]['products_and_expense_cost'] ?? 0;
            // computed event_total field
            $event_total = $events[$i]['rent_cost'] + $events[$i]['amortization_cost'] + $products_and_expense_cost + $fuel_cost;
            $events[$i]['event_total'] = number_format($event_total, 2, '.', '');
            $grand_total += $event_total;
            $events[$i]['hasEnoughQuantity'] = true;
            if ($this->User->HasWarehouseRights && $events[$i]['products']) {
                $usedQuantity = [];
                $products = explode(',', $events[$i]['products']);
                foreach ($products as $product) {
                    list($productId, $warehouseId) = explode('-', $product);
                    $eventData = $UserDbDiaryController->getEventData($events[$i]['id'], $productId);
                    if (!isset($usedQuantity[$productId])) {
                        $warehouseQuantity = $UserDbDiaryController->getWarehouseProductQuantity($warehouseId, $events[$i]['complete_date_from'], $events[$i]['farming_id']);
                        $usedQuantity[$productId] = $warehouseQuantity['quantity'] - $eventData['quantity'];
                    } else {
                        $usedQuantity[$productId] = $usedQuantity[$productId] - $eventData['quantity'];
                    }

                    $events[$i]['hasEnoughQuantity'] = $usedQuantity[$productId] >= 0;
                    if (!$events[$i]['hasEnoughQuantity']) {
                        break;
                    }
                }
            }
        }

        return [
            'rows' => $events,
            'total' => $count,
            'footer' => [
                [
                    'machine_number' => '<b>Общо:</b>',
                    'event_total' => '<b>' . number_format($grand_total, 2, '.', '') . ' лв. </b>',
                ],
            ],
        ];
    }

    /**
     * Returns information about an event.
     *
     * @param int $event_id
     *
     * @return array
     *
     * @deprecated
     */
    public function getEventInfo($event_id)
    {
        if ($this->User->isGuest) {
            return [];
        }

        $UserDbController = new UserDbController($this->User->Database);
        $LayersController = new LayersController();

        $options = [
            'tablename' => $UserDbController->DbHandler->tableDiaryEvents,
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $event_id],
            ],
        ];

        $event_results = $UserDbController->getItemsByParams($options);

        if (0 == count($event_results)) {
            return [];
        }

        $options = [
            'where' => [
                'farming' => ['column' => 'farming', 'compare' => '=', 'prefix' => 't', 'value' => $event_results[0]['farming_id']],
                'year' => ['column' => 'year', 'compare' => '=', 'prefix' => 't', 'value' => $event_results[0]['year_id']],
                'layer_type' => ['column' => 'layer_type', 'compare' => '=', 'prefix' => 't', 'value' => 1],
            ],
        ];

        $layer_results = $LayersController->getLayers($options);

        if (0 == count($layer_results)) {
            return [];
        }

        $options = [
            'tablename' => $layer_results[0]['table_name'],
            'where' => [// 'id' => array('column' => 'id', 'compare' => '=', 'value' => $event_results[0]['plot_id']),
            ],
        ];

        $plot_results = $UserDbController->getItemsByParams($options);

        // GET CONFIGS FOR GRID INFO
        $options = [
            'tablename' => $UserDbController->DbHandler->tableDiaryConfigs,
        ];
        $configs_results = $UserDbController->getItemsByParams($options);
        $configs_array = [];
        $configsCount = count($configs_results);
        if ($configsCount) {
            for ($i = 0; $i < $configsCount; $i++) {
                $configs_array[$configs_results[$i]['id']] = $configs_results[$i];
            }
        }
        // END OF GET CONFIGS
        // convert results for info panel
        // clear old data
        $return = [];

        $return['phase'] = $GLOBALS['Diary']['event_phase'][$event_results[0]['phase_id']]['name'];
        $return['type'] = $configs_array[$event_results[0]['type_id']]['name'];
        $return['subtype'] = $configs_array[$event_results[0]['subtype_id']]['name'];
        $return['performer'] = $configs_array[$event_results[0]['performer_id']]['name'];

        if (3 == $event_results[0]['phase_id']) {
            $return['date_from'] = strftime('%d.%m.%Y', strtotime($event_results[0]['complete_date_from']));
            $return['date_to'] = strftime('%d.%m.%Y', strtotime($event_results[0]['complete_date_to']));
        } else {
            $return['date_from'] = strftime('%d.%m.%Y', strtotime($event_results[0]['plan_date_from']));
            $return['date_to'] = strftime('%d.%m.%Y', strtotime($event_results[0]['plan_date_to']));
        }

        $machine = $configs_array[$event_results[0]['machine_id']];
        $machine_type = $configs_array[$machine['type_id']];

        $return['machine'] = $machine_type['name'] . ' - ' . $machine['number'];

        $attachment = $configs_array[$event_results[0]['attachment_id']];
        $attachment_type = $configs_array[$attachment['type_id']];

        $return['attachment'] = $attachment_type['name'] . ' - ' . $attachment['number'];
        $return['amortization'] = $event_results[0]['amortization_cost'];
        $return['machine_rented'] = ($event_results[0]['is_rented']) ? 'Р”Р°' : 'РќРµ';
        $return['rent_cost'] = ($event_results[0]['is_rented']) ? $event_results[0]['rent_cost'] : '-';
        $return['plan_fuel_cost'] = ($event_results[0]['plan_fuel_cost']) ? $event_results[0]['plan_fuel_cost'] : '-';
        $return['final_fuel_cost'] = ($event_results[0]['final_fuel_cost']) ? $event_results[0]['final_fuel_cost'] : '-';
        $return['total_fuel_cost'] = ($event_results[0]['total_fuel_cost']) ? $event_results[0]['total_fuel_cost'] : '-';
        $return['material_cost_norm'] = $event_results[0]['material_cost_norm'] . ' - ' . $GLOBALS['Contracts']['renta_units'][$event_results[0]['material_unit_type_id']]['per_dka_name'];
        $return['material_unit_cost'] = ($event_results[0]['material_unit_cost']) ? $event_results[0]['material_unit_cost'] : '-';
        $return['material_final_cost'] = ($event_results[0]['material_final_cost']) ? $event_results[0]['material_final_cost'] : '-';

        $return['pest_name'] = $event_results[0]['pest_name'];
        $return['substance_name'] = $configs_array[$event_results[0]['substance_id']]['name'];
        $return['substance_technic'] = $configs_array[$event_results[0]['substance_technic_id']]['name'];
        $return['substance_dose'] = $event_results[0]['substance_dose'];
        $return['treated_area'] = $event_results[0]['treated_area'];
        $return['quarantine_period'] = $event_results[0]['quarantine_period'];

        $return['comment'] = ($event_results[0]['comment']) ? $event_results[0]['comment'] : '-';

        return $return;
    }

    /**
     * Returns information for single event, to be edited.
     *
     * @param int $eventID
     *
     * @return array
     */
    public function loadEditSingleEvent($eventID)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $options = [
            'tablename' => $UserDbController->DbHandler->tableDiaryEvents,
            'where' => [
                'event_id' => ['column' => 'id', 'compare' => '=', 'value' => $eventID],
            ],
        ];

        $event = $UserDbController->getItemsByParams($options);
        if (0 == count($event)) {
            return [];
        }
        $event = $event[0];
        if (self::EVENT_COMPLETE == $event['phase_id']) {
            $tmpTimeFrom = explode(' ', $event['complete_date_from']);
            $tmpTimeTo = explode(' ', $event['complete_date_to']);
            $event['complete_date_from'] = date('Y-m-d', strtotime($tmpTimeFrom[0]));
            $event['complete_date_to'] = date('Y-m-d', strtotime($tmpTimeTo[0]));
            $event['complete_time_from'] = date('H:i', strtotime($tmpTimeFrom[1]));
            $event['complete_time_to'] = date('H:i', strtotime($tmpTimeTo[1]));
            $event['plan_date_from'] = null;
            $event['plan_date_to'] = null;
            $event['plan_time_from'] = null;
            $event['plan_time_to'] = null;
        } else {
            $tmpTimeFrom = explode(' ', $event['plan_date_from']);
            $tmpTimeTo = explode(' ', $event['plan_date_to']);
            $event['plan_date_from'] = date('Y-m-d', strtotime($tmpTimeFrom[0]));
            $event['plan_date_to'] = date('Y-m-d', strtotime($tmpTimeTo[0]));
            $event['plan_time_from'] = date('H:i', strtotime($tmpTimeFrom[1]));
            $event['plan_time_to'] = date('H:i', strtotime($tmpTimeTo[1]));
            $event['complete_date'] = null;
            $event['complete_date_to'] = null;
            $event['complete_time_from'] = null;
            $event['complete_time_to'] = null;
        }
        $fuel_calculation = null;
        $event['fuel_unit'] = (self::FUEL_CALC_BY_LITER == $event['fuel_unit']) ? 1 : ((self::FUEL_CALC_BY_AREA == $event['fuel_unit']) ? 2 : 1);

        // querying (selecting) products
        $options = [
            'return' => ['dp.*', 'pc.name as substance_name', 'pc.warehouse_item_id'],
            'tablename' => $UserDbController->DbHandler->tableDiaryProducts . ' dp',
            'where' => [
                'event_id' => ['column' => 'event_id', 'compare' => '=', 'value' => $eventID],
            ],
            'leftjoin' => [
                'table' => $UserDbController->DbHandler->tableDiaryConfigs . ' pc',
                'condition' => ' ON (dp.substance_id = pc.id)',
            ],
        ];

        $UserDbDiaryController = new UserDbDiaryController($this->User->Database);
        $products = $UserDbController->getItemsByParams($options);
        if (!empty($products)) {
            foreach ($products as &$prd) {
                $prd['substance_dose'] ??= 0;
                $prd['treated_area'] ??= 0;
                if ($prd['treated_area'] > 0 && $prd['substance_unit_price'] > 0 && $prd['substance_dose'] > 0) {
                    $prd['price_per_area'] = $prd['treated_area'] * $prd['substance_unit_price'] * $prd['substance_dose'];
                } else {
                    $prd['price_per_area'] = 0;
                }

                if (empty($prd['substance_consumed'])) {
                    $prd['substance_consumed'] = 0;
                }

                if ($prd['warehouse_item_id']) {
                    $warehouseQuantity = $UserDbDiaryController->getWarehouseProductQuantity($prd['warehouse_item_id'], $event['complete_date_from'], $event['farming_id']);
                    $eventsUsedQuantity = $UserDbDiaryController->getEventQuantities($prd['substance_id'], $event['complete_date_from'], $event['farming_id']);

                    $prd['warehouse_quantity'] = $warehouseQuantity['quantity'] - $eventsUsedQuantity;
                }
            }
            $event['products'] = $products;
        } else {
            $event['products'] = null;
        }
        unset($prd);

        // querying (selecting) products
        $producesOptions = [
            'return' => ['*'],
            'tablename' => $UserDbController->DbHandler->tableDiaryProduces . ' dp',
            'where' => [
                'event_id' => ['column' => 'event_id', 'compare' => '=', 'value' => $eventID],
            ],
        ];

        $event['produces'] = null;
        $produces = $UserDbController->getItemsByParams($producesOptions);
        if (!empty($produces)) {
            foreach ($produces as &$produce) {
                $produce['culture_name'] = $GLOBALS['Farming']['crops'][$produce['culture']]['crop_name'];
            }
            $event['produces'] = $produces;
        }

        $event_expense_params = ['type_id' => $event['type_id'], 'subtype_id' => $event['subtype_id'], 'selected' => true];
        $event_expense_params['event_from'] = $tmpTimeFrom[0] . ' ' . $tmpTimeFrom[1];
        $event_expense_params['event_to'] = $tmpTimeTo[0] . ' ' . $tmpTimeTo[1];

        $event['sub_types'] = $this->loadExpensesForEvent($event_expense_params);

        return $event;
    }

    /**
     * Saves the new/edited event.
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer   phase_id
     *                         #item integer   type_id
     *                         #item integer   subtype_id
     *                         #item integer   performer_id
     *                         #item integer   machine_id
     *                         #item integer   attachment_id
     *                         #item double    amortization_cost
     *                         #item string    comment
     *                         #item double    plan_fuel_cost
     *                         #item boolean   is_rented
     *                         #item double    rent_cost
     *                         #item date      complete_date_from
     *                         #item date      complete_date_to
     *                         #item double    completed_area
     *                         #item double    material_final_cost
     *                         #item double    final_fuel_cost
     *                         #item double    total_fuel_cost
     *                         #item double    movement_fuel
     *                         #item double    parking_fuel
     *                         #item double    start_fuel
     *                         #item double    end_fuel
     *                         #item double    avg_engine_revs
     *                         #item double    avg_speed
     *                         #item double    completed_mileage
     *                         #item timestamp time_in
     *                         #item timestamp time_out
     *                         #item timestamp total_time_in
     *                         #item timestamp movement_time
     *                         #item timestamp parking_time
     *                         #item timestamp plan_date_from
     *                         #item timestamp plan_date_to
     *                         #item double    price_per_litre
     *                         #item integer   event_id
     *                         array products(
     *                         #item integer substance_id
     *                         #item double  substance_dose
     *                         #item integer substance_unit_type (kg , liters, ecc)
     *                         #item double  substance_unit_price
     *                         #item double  treated_area
     *                         #item double  price_per_area
     *                         #item bool    is_substance_treatment
     *                         #item string  pest_name
     *                         #item integer substance_technic_id (application id) request type 8
     *                         #item integer quarantine_period (number of days)
     *                         #item double  substance_consumed
     *                         )
     *                         }
     *
     * @throws TDbException
     *
     * @return array(boolean,integer)
     */
    public function saveEvent($rpcParams)
    {
        if (!empty($rpcParams['products']) && !empty($rpcParams['produces'])) {
            throw new InvalidArgumentException("The event can't contains products and produces at the same time");
        }

        $UsersController = new UsersController('Users');
        $UserDbController = new UserDbController($this->User->Database);
        $recordID = null;
        // create options that are the same for every record
        $options = [
            'tablename' => $UserDbController->DbHandler->tableDiaryEvents,
            'mainData' => [
                'phase_id' => $rpcParams['phase_id'],
                'type_id' => $rpcParams['type_id'],
                'subtype_id' => $rpcParams['subtype_id'] ? (int)$rpcParams['subtype_id'] : null,
                'performer_id' => $rpcParams['performer_id'] ? (int)$rpcParams['performer_id'] : null,
                'machine_id' => $rpcParams['machine_id'] ? (int)$rpcParams['machine_id'] : null,
                'attachment_id' => $rpcParams['attachment_id'] ? (int)$rpcParams['attachment_id'] : null,
                'amortization_cost' => $rpcParams['amortization_cost'] ? (float)$rpcParams['amortization_cost'] : null,
                'comment' => $rpcParams['comment'],
                'plan_fuel_cost' => $rpcParams['plan_fuel_cost'] ? (float)$rpcParams['plan_fuel_cost'] : null,
                'material_cost_norm' => $rpcParams['material_cost_norm'] ? (float)$rpcParams['material_cost_norm'] : null,
                'material_unit_type_id' => $rpcParams['material_unit_type_id'] ? (int)$rpcParams['material_unit_type_id'] : null,
                'material_unit_cost' => $rpcParams['material_unit_cost'] ? (float)$rpcParams['material_unit_cost'] : null,
                'price_per_litre' => $rpcParams['price_per_litre'] ? (float)$rpcParams['price_per_litre'] : null,
                'treated_area' => $rpcParams['treatable_area'] ? (float)$rpcParams['treatable_area'] : null, // todo change column name to treatable
            ],
        ];
        $options['mainData']['price_per_area'] = 0; // this hold the total for products and expenses;
        $options['mainData']['is_rented'] = $rpcParams['is_rented'] ? 'TRUE' : 'FALSE';
        $options['mainData']['rent_cost'] = $rpcParams['is_rented'] ? (is_numeric($rpcParams['rent_cost']) ? $rpcParams['rent_cost'] : null) : null;
        $options['mainData']['fuel_unit'] = (1 == $rpcParams['fuel_unit']) ? self::FUEL_CALC_BY_LITER : ((2 == $rpcParams['fuel_unit']) ? self::FUEL_CALC_BY_AREA : self::FUEL_CALC_BY_LITER);

        if (isset($rpcParams['products']) && !empty($rpcParams['products'])) {
            $products = $rpcParams['products'];
            foreach ($products as &$prd) {
                /*
                 * TS-3691 - event's treatable area influence product's treated_area. i.e.
                 * product's treated_area cannot be bigger than the event's treatable area
                 */
                if ($prd['treated_area'] > 0 && $options['mainData']['treated_area'] > 0) {
                    $prd['treated_area'] = ($prd['treated_area'] > $options['mainData']['treated_area']) ? $options['mainData']['treated_area'] : $prd['treated_area'];
                }
                if ($prd['treated_area'] > 0 && $prd['substance_unit_price'] > 0 && $prd['substance_dose'] > 0) {
                    $prd['price_per_area'] = $prd['treated_area'] * $prd['substance_unit_price'] * $prd['substance_dose'];
                    // temporary store the event total on this column (price_per_area)
                    // another one should be created for this purpose
                    $options['mainData']['price_per_area'] += $prd['price_per_area'];
                }
            }
            unset($prd);
        }

        // event is completed and complete only complete dates are needed
        if (self::EVENT_COMPLETE == $rpcParams['phase_id']) {
            $tmp_time_from = ($rpcParams['complete_time_from']) ? $rpcParams['complete_time_from'] : '00:00:00';
            $options['mainData']['complete_date_from'] = date('Y-m-d H:i:s', strtotime($rpcParams['complete_date_from'] . ' ' . $tmp_time_from));
            $tmp_time_to = ($rpcParams['complete_time_to']) ? $rpcParams['complete_time_to'] : '23:59:59';
            $options['mainData']['complete_date_to'] = date('Y-m-d H:i:s', strtotime($rpcParams['complete_date_to'] . ' ' . $tmp_time_to));

            $options['mainData']['plan_date_from'] = null;
            $options['mainData']['plan_date_to'] = null;
            $options['mainData']['completed_area'] = ($rpcParams['completed_area']) ? $rpcParams['completed_area'] : null;

            $options['mainData']['final_fuel_cost'] = ($rpcParams['final_fuel_cost']) ? $rpcParams['final_fuel_cost'] : null;
            $options['mainData']['total_fuel_cost'] = ($rpcParams['total_fuel_cost']) ? $rpcParams['total_fuel_cost'] : null;
            $options['mainData']['movement_fuel'] = ($rpcParams['movement_fuel']) ? $rpcParams['movement_fuel'] : null;
            $options['mainData']['parking_fuel'] = ($rpcParams['parking_fuel']) ? $rpcParams['parking_fuel'] : null;
            $options['mainData']['start_fuel'] = ($rpcParams['start_fuel']) ? $rpcParams['start_fuel'] : null;
            $options['mainData']['end_fuel'] = ($rpcParams['end_fuel']) ? $rpcParams['end_fuel'] : null;

            $options['mainData']['avg_engine_revs'] = ($rpcParams['avg_engine_revs']) ? $rpcParams['avg_engine_revs'] : null;
            $options['mainData']['avg_speed'] = ($rpcParams['avg_speed']) ? $rpcParams['avg_speed'] : null;
            $options['mainData']['completed_mileage'] = ($rpcParams['completed_mileage']) ? $rpcParams['completed_mileage'] : null;

            $options['mainData']['time_in'] = ($rpcParams['time_in']) ? $rpcParams['time_in'] : null;
            $options['mainData']['time_out'] = ($rpcParams['time_out']) ? $rpcParams['time_out'] : null;
            $options['mainData']['total_time_in'] = ($rpcParams['total_time_in']) ? $rpcParams['total_time_in'] : null;
            $options['mainData']['movement_time'] = ($rpcParams['movement_time']) ? $rpcParams['movement_time'] : null;
            $options['mainData']['parking_time'] = ($rpcParams['parking_time']) ? $rpcParams['parking_time'] : null;
        } else {
            $tmp_time_from = ($rpcParams['plan_time_from']) ? $rpcParams['plan_time_from'] : '00:00:00';
            $options['mainData']['plan_date_from'] = ($rpcParams['plan_date_from']) ? date('Y-m-d H:i:s', strtotime($rpcParams['plan_date_from'] . ' ' . $tmp_time_from)) : null;

            $tmp_time_to = ($rpcParams['plan_time_to']) ? $rpcParams['plan_time_to'] : '00:00:00';
            $options['mainData']['plan_date_to'] = ($rpcParams['plan_date_to']) ? date('Y-m-d H:i:s', strtotime($rpcParams['plan_date_to'] . ' ' . $tmp_time_to)) : null;

            // CLEAR old data if is edit (and is not a "complete" event)
            $options['mainData']['complete_date_from'] = null;
            $options['mainData']['complete_date_to'] = null;
            $options['mainData']['completed_area'] = null;

            $options['mainData']['material_final_cost'] = null;

            $options['mainData']['final_fuel_cost'] = null;
            $options['mainData']['total_fuel_cost'] = null;
            $options['mainData']['movement_fuel'] = null;
            $options['mainData']['parking_fuel'] = null;
            $options['mainData']['start_fuel'] = null;
            $options['mainData']['end_fuel'] = null;

            $options['mainData']['avg_engine_revs'] = null;
            $options['mainData']['avg_speed'] = null;
            $options['mainData']['completed_mileage'] = null;

            $options['mainData']['time_in'] = null;
            $options['mainData']['time_out'] = null;
            $options['mainData']['total_time_in'] = null;
            $options['mainData']['movement_time'] = null;
            $options['mainData']['parking_time'] = null;
        }
        $options['mainData']['last_edit'] = date('Y-m-d H:i:s');
        $options['mainData']['serial_num'] = 1; // serial_num is deprecated

        // EXPENSE FOR EVENT
        $event_expense_params = [];
        $expense_cost = $expensePrice = 0;

        $event_expense_params['type_id'] = $rpcParams['type_id'];
        $event_expense_params['subtype_id'] = $rpcParams['subtype_id'];
        $event_expense_params['performer_id'] = '' != $rpcParams['performer_id'] ? $rpcParams['performer_id'] : 'NULL';
        $event_expense_params['event_from'] = (self::EVENT_COMPLETE == $rpcParams['phase_id']) ? $options['mainData']['complete_date_from'] : $options['mainData']['plan_date_from'];
        $event_expense_params['event_to'] = (self::EVENT_COMPLETE == $rpcParams['phase_id']) ? $options['mainData']['complete_date_to'] : $options['mainData']['plan_date_to'];
        $event_expense_params['return_only_subtype'] = true;

        $expense = $this->loadExpensesForEvent($event_expense_params);

        // expense price will not exist if no record found for provided criteria
        if (isset($expense['price'])) {
            $expensePrice = $expense['price'];
        }

        $UserDbZPlotsController = new UserDbZPlotsController($this->User->Database);
        $plotData = $UserDbZPlotsController->getZPlotDataByLayerParams([
            'return' => ['round((st_area(geom)/1000)::decimal, 3) zplot_area'],
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $rpcParams['plot_id']],
            ],
            'farming' => $rpcParams['farming_id'],
            'year' => $rpcParams['year_id'],
            'user_id' => $this->User->groupId,
        ]);

        $treatableArea = $rpcParams['zplot_area'];
        if (array_key_exists('treatable_area', $rpcParams) && (int)$rpcParams['treatable_area'] > 0) {
            $treatableArea = $rpcParams['treatable_area'];
        }

        $expense_cost = $expensePrice * $treatableArea;

        $options['mainData']['price_per_area'] += $expense_cost;
        // END EXPENSE FOR EVENT
        // check is is an edit or an addition
        if ('' == $rpcParams['event_id']) {
            $options['mainData']['farming_id'] = $rpcParams['farming_id'];
            $options['mainData']['year_id'] = $rpcParams['year_id'];
            $options['mainData']['plot_id'] = $rpcParams['plot_id'];

            $recordID = $UserDbController->addItem($options);

            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options['mainData'], ['event_id' => $recordID], 'Add diary event');
        } else {
            // edit/update
            $oldOptions = [
                'tablename' => $UserDbController->DbHandler->tableDiaryEvents,
                'return' => ['*'],
                'where' => [
                    'id' => ['column' => 'id', 'compare' => '=', 'value' => $rpcParams['event_id']],
                ],
            ];
            $oldValues = $UserDbController->getItemsByParams($oldOptions);
            $options['where'] = ['id' => $rpcParams['event_id']];
            $saved = $UserDbController->editItem($options);

            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options['mainData'], $oldValues, 'Edit diary event');
        }
        // finally we save related products for the event
        $recordID ??= null;
        $eventID = $rpcParams['event_id'] ?? null;
        $id = (null !== $recordID) ? $recordID : $eventID;
        if ($id && isset($rpcParams['products']) && !empty($rpcParams['products'])) {
            $this->saveProducts($rpcParams['products'], $id);
        }
        if ($id && !empty($rpcParams['produces'])) {
            $this->saveProduces($rpcParams['produces'], $id);
        }

        if ((isset($saved) && 1 === $saved) || (is_int($recordID) && $recordID > 0)) {
            return ['success' => true, 'id' => $id];
        }

        return ['success' => false, 'id' => null];
    }

    /** Delete a Single Event.
     * @param int $eventID
     *
     * @return array|mixed|string
     */
    public function deleteSingleEvent($eventID)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $options = [
            'tablename' => $UserDbController->DbHandler->tableDiaryEvents,
            'id_string' => $eventID,
            'id_name' => 'id',
        ];

        return $UserDbController->deleteItemsByParams($options);
    }

    /**
     * Returns expense data, activity type and activity sub type for a given Event's time period.
     * Return also all config data regarding activity and sub-activity.
     *
     * @api-method loadExpensesForEvent
     *
     * @param array $rpcParams
     *
     * @return array {
     *               #item integer id                    , id of the expense
     *               #item float   price                 , price (leva for decar) of the expense
     *               #item string  valid_from            , expense starting date
     *               #item string  valid_to              , expense end date
     *               #item integer activity_type_id      , the parent activity id of the expense
     *               #item string  activity_type_name    , the parent activity name of the expense
     *               #item integer sub_type_id           , the activity id of the expense
     *               #item string  sub_type_name         , the activity name of the expense
     *               #item integer performer_id          , performer id linked to the expense
     *               #item string  performer_name        , performer name linked to the expense
     *               #item string  performer_description , performer's description linked to the expense
     *               #item string  performer_title       , performer's "title" linked to the expense
     *               }
     */
    public function loadExpensesForEvent($rpcParams)
    {
        // init all needed controllers
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbDiaryController = new UserDbDiaryController($this->User->Database);

        $config_options = [
            'return' => [
                'dc2.id as activity_type_id',
                'dc2.name as activity_type_name',
                'dc1.id as sub_type_id',
                'dc1.name as sub_type_name',
                'dc1.options as sub_type_options',
            ],
            'where' => [
                'config_type' => ['column' => 'config_type', 'prefix' => 'dc1', 'compare' => '=', 'value' => self::ACTIVITY_SUB_TYPES],
            ],
            'sort' => 'dc2.name,dc1.name',
            'order' => 'asc',
        ];
        if (isset($rpcParams['type_id'])) {
            $config_options['where']['activity_type_id'] = ['column' => 'id', 'compare' => '=', 'prefix' => 'dc2', 'value' => $rpcParams['type_id']];
        }
        $activities = $UserDbDiaryController->getFullDiaryConfigData($config_options);
        $activities_count = count($activities);
        if (0 == $activities_count) {
            return $activities;
        }
        $expense_options = [
            'return' => [
                'de.id as expense_id',
                'de.price as price',
                'de.valid_from as valid_from',
                'de.valid_to as valid_to',

                'dca.id as sub_type_id',
                'dca.name as sub_type_name',
                'dca.options as sub_type_options',

                'dt.id as activity_type_id',
                'dt.name as activity_type_name',

                'dcp.id as performer_id',
                'dcp.name as performer_name',
                'dcp.description as performer_description',
                'dcp.perf_title as performer_title',
            ],
            'join_activity_type' => true,
            'sort' => 'dt.name,dca.name',
            'order' => 'asc',
        ];
        if (isset($rpcParams['event_from'])) {
            $expense_options['where']['valid_to'] = ['column' => 'valid_to', 'compare' => '>=', 'prefix' => 'de', 'value' => $rpcParams['event_from']];
        }
        if (isset($rpcParams['event_to'])) {
            $expense_options['where']['valid_from'] = ['column' => 'valid_from', 'compare' => '<=', 'prefix' => 'de', 'value' => $rpcParams['event_to']];
        }
        if (isset($rpcParams['type_id'])) {
            $expense_options['where']['activity_type_id'] = ['column' => 'id', 'compare' => '=', 'prefix' => 'dt', 'value' => $rpcParams['type_id']];
        }

        $expense_options['where']['performer_id'] = ['column' => 'performer_id', 'compare' => '=', 'prefix' => 'de', 'value' => $rpcParams['performer_id']];

        $event_expenses = $UserDbDiaryController->getExpensesData($expense_options);
        $event_expenses_count = count($event_expenses);

        $return = [$expense_options];

        foreach ($activities as $index => $activity) {
            $sub_type_id = $activity['sub_type_id'];
            $expense_present = false;
            if ($event_expenses_count) {
                foreach ($event_expenses as $expense) {
                    if (in_array($sub_type_id, $expense, true)) {
                        $expense_present = true;

                        break;
                    }
                }
                $return[$index] = $expense_present ? $expense : $activity;
            } else {
                $activity['group'] = 'Вид';
                $return[$index] = $activity;
            }
            $is_event_date_range_in_expense = false;
            if (isset($return[$index]['valid_from'], $return[$index]['valid_to'])) {
                if ($rpcParams['event_from'] >= $return[$index]['valid_from'] && $rpcParams['event_to'] <= $return[$index]['valid_to']) {
                    $is_event_date_range_in_expense = true;
                }
            }
            $return[$index]['group'] = $is_event_date_range_in_expense ? 'Разходи' : 'Вид';

            if (isset($rpcParams['subtype_id']) && $rpcParams['subtype_id'] == $return[$index]['sub_type_id']) {
                $return[$index]['selected'] = true;
                if (isset($rpcParams['return_only_subtype']) && true == $rpcParams['return_only_subtype']) {
                    return $return[$index];
                }
            }
        }

        usort($return, function ($item1, $item2) {
            if ($item1['group'] == $item2['group']) {
                return 0;
            }

            return $item1['group'] < $item2['group'] ? -1 : 1;
        });
        /*$query = $UserDbDiaryController->getExpensesData($expense_options,false,true);
        $return['query'] = preg_replace("/\r\n|\r|\n/",' ',$query);*/
        return $return;
    }

    /** GET CONFIGS FOR GRID INFO.
     * @param array $config_types
     *
     * @return array
     */
    protected function getConfigs($config_types = [])
    {
        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableDiaryConfigs,
            'where' => [
                // get configs for event types and subtypes only
                'config_type' => ['column' => 'config_type', 'compare' => 'IN', 'value' => $config_types],
            ],
        ];
        $configs_results = $UserDbController->getItemsByParams($options);
        $configs_array = [];

        $config_count = count($configs_results);

        if (0 === $config_count) {
            return $configs_array;
        }

        foreach ($configs_results as $config_item) {
            $configs_array[$config_item['id']] = $config_item;
        }

        return $configs_array;
    }

    /**
     * @param $products (
     *                  #item integer  substance_id
     *                  #item double   substance_dose
     *                  #item integer  substance_unit_type (kg , liters, ecc)
     *                  #item double   substance_unit_price
     *                  #item double   treated_area
     *                  #item double   price_per_area
     *                  #item bool     is_substance_treatment
     *                  #item string   pest_name
     *                  #item integer  substance_technic_id (application id) request type 8
     *                  #item double   quarantine_period
     *                  )
     *
     * @throws TDbException
     *
     * @return bool
     */
    private function saveProducts(array $products, $eventID)
    {
        if (empty($products)) {
            return false;
        }

        $UsersController = new UsersController('Users');
        $UserDbController = new UserDbController($this->User->Database);
        /** @var TDbTransaction $transaction */
        $transaction = $UserDbController->DbHandler->DbModule->beginTransaction();

        try {
            $sql = 'DELETE FROM ' . $UserDbController->DbHandler->tableDiaryProducts . ' WHERE event_id = ' . $eventID;
            $cmd = $UserDbController->DbHandler->DbModule->createCommand($sql);
            $cmd->execute();
            $columns = array_keys($products[0]);
            array_unshift($columns, 'event_id');
            $options = [];
            $options['tablename'] = $UserDbController->DbHandler->tableDiaryProducts;
            $options['columns'] = implode(',', $columns);
            foreach ($products as &$product) {
                foreach ($product as $key => &$value) {
                    if (is_numeric($value) && !empty($value)) {
                        $value = (float)$value;
                    }
                    if ('' == $value) {
                        $value = null;
                    }
                }
                $product['substance_dose'] ??= 0;
                $product['treated_area'] ??= 0;
                if ($product['treated_area'] > 0 && $product['substance_unit_price'] > 0 && $product['substance_dose'] > 0) {
                    $product['price_per_area'] = $product['treated_area'] * $product['substance_unit_price'] * $product['substance_dose'];
                } else {
                    $product['price_per_area'] = 0;
                }

                if (empty($product['substance_consumed'])) {
                    $product['substance_consumed'] = 0;
                }

                $product = ['event_id' => $eventID] + $product;
            }
            unset($product);
            $options['values'] = $products;
            $UserDbController->addItems($options);
            $transaction->commit();

            return true;
        } catch (Exception $e) {
            $transaction->rollback();
            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $sql, $products, 'Inserting products before insert for diary event with id' . $eventID);

            throw $e;
        }
    }

    /**
     * @param $produces (
     *                  #item string  culture
     *                  #item string  sort
     *                  #item decimal area
     *                  #item decimal produce
     *                  )
     *
     * @throws TDbException
     *
     * @return bool
     */
    private function saveProduces(array $produces, $eventID)
    {
        if (empty($produces)) {
            return false;
        }

        $UsersController = new UsersController('Users');
        $UserDbController = new UserDbController($this->User->Database);
        /** @var TDbTransaction $transaction */
        $transaction = $UserDbController->DbHandler->DbModule->beginTransaction();

        try {
            $sql = 'DELETE FROM ' . $UserDbController->DbHandler->tableDiaryProduces . ' WHERE event_id = ' . $eventID;
            $cmd = $UserDbController->DbHandler->DbModule->createCommand($sql);
            $cmd->execute();

            $options = [];
            $options['tablename'] = $UserDbController->DbHandler->tableDiaryProduces;
            foreach ($produces as &$produce) {
                if (empty($produce['culture']) || empty($produce['area']) || empty($produce['produce'])) {
                    throw new InvalidArgumentException('Missing required value');
                }
                $produce['produce_per_dka'] = $produce['produce'] / $produce['area'];
                $produce['event_id'] = $eventID;
            }

            $options['columns'] = implode(',', array_keys($produces[0]));
            $options['values'] = $produces;

            $UserDbController->addItems($options);
            $transaction->commit();

            return true;
        } catch (\Prado\Exceptions\TDbException $e) {
            $transaction->rollback();
            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $sql, $produces, 'Inserting produces before insert for diary event with id' . $eventID);

            throw $e;
        }
    }

    private function getMachineId($configs_array, $wialon_id)
    {
        foreach ($configs_array as $config_el) {
            if ($config_el['wialon_id'] == $wialon_id && self::MACHINES == $config_el['config_type']) {
                return $config_el['id'];
            }
        }

        return;
    }
}
