<?php

namespace TF\Engine\APIClasses\Diary;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

/**
 * Grid 'Типове/видове дейности, машин<PERSON>, прикачен инвентар, обработки', изпълнители, разходи.
 *
 * @rpc-module Diary
 *
 * @rpc-service-id diary-configs-grid
 */
class DiaryConfigsGrid extends TRpcApiProvider
{
    public const ACTIVITY_TYPES = 1;
    public const ACTIVITY_SUB_TYPES = 2;
    public const MACHINES_GROUPS = 3;
    public const MACHINES = 4;
    public const MACHINE_EXTENSION_TYPES = 5;
    public const ATTACHMENTS = 6;
    public const PLANT_PROTECTION_PRODUCTS = 7;
    public const TECHNIQUES_APPLICATION = 8;
    public const PERFORMERS = 9;
    public const UNITS = 10;

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getDiaryConfigsGrid']],
        ];
    }

    /**
     * @api-module read
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer request_type
     *                         }
     *
     * @return array
     */
    public function getDiaryConfigsGrid($rpcParams)
    {
        // create default return to clear old results
        $return = [
            'rows' => [],
            'total' => 0,
        ];

        // check if request_type isset
        if (!$rpcParams['request_type'] || !(int)$rpcParams['request_type']) {
            return $return;
        }

        $UserDbController = new UserDbController($this->User->database);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableDiaryConfigs,
            'where' => [
                'config_type' => ['column' => 'config_type', 'compare' => '=', 'value' => $rpcParams['request_type']],
            ],
        ];

        switch ($rpcParams['request_type']) {
            case self::ACTIVITY_SUB_TYPES:
                $results = $UserDbController->getItemsByParams($options);
                $resultsCount = count($results);
                if (0 == $resultsCount) {
                    return $return;
                }
                // get event types
                $options['where']['config_type'] = ['column' => 'config_type', 'compare' => '=', 'value' => 1];
                $et_results = $UserDbController->getItemsByParams($options);
                $et_resultsCount = count($et_results);
                // create predefined array
                $event_types = [];
                for ($i = 0; $i < $et_resultsCount; $i++) {
                    $itemData = $et_results[$i];
                    $event_types[$itemData['id']] = $itemData['name'];
                }

                for ($i = 0; $i < $resultsCount; $i++) {
                    $results[$i]['event_type'] = $event_types[$results[$i]['type_id']];
                }

                return $this->returnGridResult($results);
            case self::MACHINES:
                $results = $UserDbController->getItemsByParams($options);
                $resultsCount = count($results);
                if (0 == $resultsCount) {
                    return $return;
                }
                // get machine types
                $options['where']['config_type'] = ['column' => 'config_type', 'compare' => '=', 'value' => 3];
                $m_types_results = $UserDbController->getItemsByParams($options);
                $mTypeCount = count($m_types_results);
                // create predefined array
                $machine_types = [];
                for ($i = 0; $i < $mTypeCount; $i++) {
                    $itemData = $m_types_results[$i];
                    $machine_types[$itemData['id']] = $itemData['name'];
                }

                for ($i = 0; $i < $resultsCount; $i++) {
                    $results[$i]['machine_type'] = $machine_types[$results[$i]['type_id']];
                }

                return $this->returnGridResult($results);
            case self::ATTACHMENTS:
                $results = $UserDbController->getItemsByParams($options);
                $resultsCount = count($results);
                if (0 == $resultsCount) {
                    return $return;
                }

                // get machine types
                $options['where']['config_type'] = ['column' => 'config_type', 'compare' => '=', 'value' => 5];
                $a_types_results = $UserDbController->getItemsByParams($options);
                $aTypeCount = count($a_types_results);

                // create predefined array
                $attachment_types = [];
                for ($i = 0; $i < $aTypeCount; $i++) {
                    $itemData = $a_types_results[$i];
                    $attachment_types[$itemData['id']] = $itemData['name'];
                }

                for ($i = 0; $i < $resultsCount; $i++) {
                    $results[$i]['attachment_type'] = $attachment_types[$results[$i]['type_id']];
                }

                return $this->returnGridResult($results);
            case self::UNITS:
                $options['sort'] = 'id';
                $options['order'] = 'asc';
                $options['return'] = ['id', 'name'];
                $results = $UserDbController->getItemsByParams($options);

                return $this->returnGridResult($results);
            case self::PLANT_PROTECTION_PRODUCTS:
                if (!empty($rpcParams['status'])) {
                    $options['where']['options'] = ['column' => "options->>'status'", 'compare' => '=', 'value' => $rpcParams['status']];
                }
                $options['sort'] = 'id';
                $options['order'] = 'asc';
                $options['return'] = ['id', 'name', 'options'];
                $results = $UserDbController->getItemsByParams($options);
                foreach ($results as &$result) {
                    $result['options'] = json_decode($result['options'], true);
                }

                return $this->returnGridResult($results);
            default:
                // 1 - event types
                // 3 - machine types
                // 5 - attachment types
                // 7 - substance types
                // 8 - substance apply technics
                $options['sort'] = 'id';
                $options['order'] = 'asc';
                $results = $UserDbController->getItemsByParams($options);

                return $this->returnGridResult($results);
        }
    }

    private function returnGridResult($results)
    {
        return [
            'rows' => $results,
            'total' => count($results),
        ];
    }
}
