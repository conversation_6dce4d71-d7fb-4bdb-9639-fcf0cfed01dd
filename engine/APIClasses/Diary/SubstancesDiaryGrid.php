<?php

namespace TF\Engine\APIClasses\Diary;

use Prado\Web\Services\TRpcApiProvider;
use TF\Application\Common\Config;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbDiary\UserDbDiaryController;

/**
 * Grid 'Проведени химични обработки'.
 *
 * @rpc-module Diary
 *
 * @rpc-service-id substances-diary-grid
 */
class SubstancesDiaryGrid extends TRpcApiProvider
{
    public const PLANT_PROTECTION_PRODUCTS = 7;
    public const TECHNIQUES_APPLICATION = 8;
    public const UNITS = 10;

    public const EVENT_COMPLETE = 2;

    protected $return = ['rows' => [], 'total' => 0];

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getSubstancesDiaryGrid'],
                'validators' => [
                    'rpcParams' => [
                        'farming' => 'validateInteger, validateRequired',
                        'year' => 'validateInteger, validateRequired',
                    ],
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer farming
     *                         #item integer year
     *                         }
     * @param int|string $page pagination parameter
     * @param int|string $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array
     */
    public function getSubstancesDiaryGrid(array $rpcParams = [], int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        if ($this->User->isGuest || !$this->User->Database) {
            return $this->return;
        }

        // init controllers
        $LayersController = new LayersController('Layers');
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbDiaryController = new UserDbDiaryController($this->User->Database);

        $options = [
            'where' => [
                'farming' => ['column' => 'farming', 'compare' => '=', 'prefix' => 't', 'value' => $rpcParams['farming']],
                'year' => ['column' => 'year', 'compare' => '=', 'prefix' => 't', 'value' => $rpcParams['year']],
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'prefix' => 't', 'value' => $this->User->GroupID],
                'layer_type' => ['column' => 'layer_type', 'compare' => '=', 'prefix' => 't', 'value' => Config::LAYER_TYPE_ZP],
            ],
        ];

        $layer_results = $LayersController->getLayers($options);

        if (0 == count($layer_results)) {
            return $this->return;
        }

        $table_name = $layer_results[0]['table_name'];
        $tableExists = $UserDbController->getTableNameExist($table_name);

        if ($tableExists <= 0) {
            return $this->return;
        }

        $options = [
            'zp_tablename' => $table_name,
            'return' => [
                'zp.id as zp_id',
                'zp.ekatte',
                'zp.culture',
                'round((st_area(zp.geom)/1000)::numeric, 3) as area',
                'zp.isak_prc_uin',
                'zp.area_name',
                'e.complete_date_from', 'e.complete_date_to',
                'prd.*',
            ],
            'where' => [
                'farming' => ['column' => 'farming_id', 'compare' => '=', 'value' => $rpcParams['farming']],
                'year' => ['column' => 'year_id', 'compare' => '=', 'value' => $rpcParams['year']],
                'is_substance_treatment' => ['column' => 'options::jsonb', 'prefix' => 'dc',  'compare' => '@>', 'value' => '{"product_type":"chemical_treatment"}'],
                'phase_id' => ['column' => 'phase_id', 'compare' => '=', 'prefix' => 'e', 'value' => self::EVENT_COMPLETE],
            ],
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
        ];

        $counter = $UserDbDiaryController->getDiaryReport($options, true, false);
        if (0 == $counter[0]['count']) {
            return $this->return;
        }
        $results = $UserDbDiaryController->getDiaryReport($options, false, false);

        // GET CONFIGS FOR GRID INFO
        $configs_array = $this->getConfig(
            [
                self::PLANT_PROTECTION_PRODUCTS,
                self::TECHNIQUES_APPLICATION,
                self::UNITS,
            ]
        );
        // END OF GET CONFIGS

        $return = [];
        foreach ($results as $i => $result) {
            $return[$i]['zp_id'] = $result['zp_id'];
            $return[$i]['area'] = $result['area'];
            $return[$i]['ekatte'] = $result['ekatte'];
            $return[$i]['culture'] = $result['culture'] ? $result['culture'] : '000000';
            $return[$i]['area_name'] = trim($result['area_name']);
            $return[$i]['isak'] = $result['area_name'] . ' (' . $result['isak_prc_uin'] . ')';
            $return[$i]['isak_prc_uin'] = $result['isak_prc_uin'];
            $return[$i]['date_from'] = strftime('%d.%m.%Y', strtotime($result['complete_date_from']));
            $return[$i]['date_to'] = strftime('%d.%m.%Y', strtotime($result['complete_date_to']));
            $return[$i]['pest_name'] = $result['pest_name'];
            $return[$i]['substance_name'] = $configs_array[$result['substance_id']]['name'];
            $return[$i]['substance_dose'] = $result['substance_dose'];
            $return[$i]['treated_area'] = $result['treated_area'];
            $return[$i]['apply_technic'] = $configs_array[$result['substance_technic_id']]['name'];
            $unit_type = $configs_array[$result['substance_unit_type']]['name'];
            $unit_type = explode('/', $unit_type);
            $return[$i]['unit_type'] = $unit_type[0];
            $return[$i]['quarantine_period'] = $result['quarantine_period'];
            $return[$i]['unit_type'] = $configs_array[$result['substance_unit_type']]['name'];
            if ($result['quarantine_period']) {
                switch ($result['quarantine_period']) {
                    case 0:
                        $return[$i]['quarantine_until'] = date('d.m.Y', strtotime($result['complete_date_to']));

                        break;
                    case 1:
                        $return[$i]['quarantine_until'] = date('d.m.Y', strtotime('+1 day', strtotime($result['complete_date_to'])));

                        break;
                    default:
                        $return[$i]['quarantine_until'] = date('d.m.Y', strtotime('+' . $result['quarantine_period'] . ' days', strtotime($result['complete_date_to'])));

                        break;
                }
            }
        }

        return [
            'rows' => $return,
            'total' => $counter[0]['count'],
        ];
    }

    /**
     * @return array
     */
    protected function getConfig($config_types)
    {
        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableDiaryConfigs,
            'where' => [
                'config_type' => ['column' => 'config_type', 'compare' => 'IN', 'value' => $config_types],
            ],
        ];
        $configs_results = $UserDbController->getItemsByParams($options);
        $configs_array = [];
        $config_count = count($configs_results);

        if ($config_count) {
            for ($i = 0; $i < $config_count; $i++) {
                $configs_array[$configs_results[$i]['id']] = $configs_results[$i];
            }
        }

        return $configs_array;
    }
}
