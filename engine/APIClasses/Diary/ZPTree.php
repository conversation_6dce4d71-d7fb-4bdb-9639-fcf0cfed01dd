<?php

namespace TF\Engine\APIClasses\Diary;

use Prado\Web\Services\TRpcApiProvider;
use TF\Application\Common\Config;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Diary > Zplots Tree (read, edit).
 *
 * @rpc-module diary
 *
 * @rpc-service-id diary-zp-tree
 */
class ZPTree extends TRpcApiProvider
{
    protected $return = [
        'rows' => [],
        'total' => 0,
        'footer' => [],
    ];
    private $module = 'Diary';
    private $service_id = 'diary-zp-tree';

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getZPTree']],
            'markForEdit' => ['method' => [$this, 'markForEdit']],
            'saveEdit' => ['method' => [$this, 'saveEdit'],
                'validators' => [
                    'rpcParams' => [
                        'farming_id' => 'validateInteger',
                        'year_id' => 'validateInteger',
                        'ekatte' => 'validateText',
                        'obrabotki' => 'validateText',
                        'dobivi' => 'validateText',
                        'napoqvane' => 'validateText',
                        'polivki' => 'validateText',
                        'polzvatel' => 'validateText',
                        'isak' => 'validateText',
                        'culture' => 'validateInteger',
                        'zplot_id' => 'validateInteger',
                    ],
                ]],
        ];
    }

    /**
     * Returns ZPlots tree data.
     *
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer year
     *                         #item integer farming
     *                         #item integer year
     *                         #item integer plot_id
     *                         #item bool    show_ablines
     *                         #item array filter
     *                         {
     *                         #item string zp_isak
     *                         #item string zp_ekate
     *                         #item integer zp_culture
     *                         #item integer event_phase
     *                         #item integer event_type
     *                         #item integer event_subtype
     *                         #item integer event_performer
     *                         #item timestamp event_date_from
     *                         #item timestamp event_date_to
     *                         }
     *                         }
     *
     * @return array
     */
    public function getZPTree($rpcParams)
    {
        if ($this->User->isGuest || !$this->User->Database) {
            return $this->return;
        }

        $FarmingController = new FarmingController('Farming');
        $userFarmings = $FarmingController->getUserFarmings();
        $userFarmingIds = array_keys($userFarmings);

        if ($rpcParams['farming'] && in_array($rpcParams['farming'], $userFarmingIds) && $rpcParams['year']) {
            $params = (object)[];
            $params->farming = $rpcParams['farming'];
            $params->year = $rpcParams['year'];
            $params->filter = $rpcParams['filter'];
            $params->show_ablines = $rpcParams['show_ablines'];

            if ($rpcParams['plot_id'] && (int)$rpcParams['plot_id'] && !empty(array_filter($params->filter))) {
                $params->plot_id = $rpcParams['plot_id'];

                return $this->loadLeafData($params);
            }

            return $this->loadPlotsData($params);
        }

        return $this->loadTreeData();
    }

    /**
     * Returns information about the selected plot.
     *
     * @api-method markForEdit
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer farming_id
     *                         #item integer year_id
     *                         #item integer zplot_id
     *                         }
     *
     * @return array
     */
    public function markForEdit($rpcParams)
    {
        $LayersController = new LayersController('Layers');
        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'where' => [
                'farming_id' => ['column' => 'farming', 'compare' => '=', 'prefix' => 't', 'value' => $rpcParams['farming_id']],
                'year_id' => ['column' => 'year', 'compare' => '=', 'prefix' => 't', 'value' => $rpcParams['year_id']],
                'layer_type' => ['column' => 'layer_type', 'compare' => '=', 'prefix' => 't', 'value' => 1],
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'prefix' => 't', 'value' => $this->User->GroupID],
            ],
        ];

        $layer_results = $LayersController->getLayers($options);

        if (0 == count($layer_results)) {
            return $this->return;
        }

        $options = [
            'tablename' => $layer_results[0]['table_name'],
            'where' => [
                'plot_id' => ['column' => 'id', 'compare' => '=', 'value' => $rpcParams['zplot_id']],
            ],
        ];

        $results = $UserDbController->getItemsByParams($options);

        if (0 == count($results)) {
            return $this->return;
        }

        return $results[0];
    }

    /**
     * Makes necessary database edits for the selected plot.
     *
     * @api-method saveEdit
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer farming_id
     *                         #item integer year_id
     *                         #item integer ekate
     *                         #item string  obrabotki
     *                         #item string  dobivi
     *                         #item string  napoqvane
     *                         #item string  polivki
     *                         #item string  polzvatel
     *                         #item string  isak
     *                         #item integer culture
     *                         #item integer zplot_id
     *                         }
     *
     * @throws MTRpcException
     *
     * @return array|int
     */
    public function saveEdit($rpcParams)
    {
        $LayersController = new LayersController('Layers');
        $UsersController = new UsersController('Users');
        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'where' => [
                'farming_id' => ['column' => 'farming', 'compare' => '=', 'prefix' => 't', 'value' => $rpcParams['farming_id']],
                'year_id' => ['column' => 'year', 'compare' => '=', 'prefix' => 't', 'value' => $rpcParams['year_id']],
                'layer_type' => ['column' => 'layer_type', 'compare' => '=', 'prefix' => 't', 'value' => 1],
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'prefix' => 't', 'value' => $this->User->GroupID],
            ],
        ];

        $layer_results = $LayersController->getLayers($options);

        if (0 == count($layer_results)) {// TODO: implement error return
            return [];
        }

        $tableExists = $UserDbController->getTableNameExist($layer_results[0]['table_name']);
        if (!$tableExists) {
            throw new MTRpcException('DATABASE_INVALID_TABLE_NAME', -33102);
        }

        $oldOptions = [
            'tablename' => $layer_results[0]['table_name'],
            'return' => [
                '*',
            ],
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $rpcParams['zplot_id']],
            ],
        ];
        $oldValues = $UserDbController->getItemsByParams($oldOptions);

        $options = [
            'tablename' => $layer_results[0]['table_name'],
            'mainData' => [
                'ekatte' => $rpcParams['ekate'],
                'obrabotki' => $rpcParams['obrabotki'],
                'dobivi' => $rpcParams['dobivi'],
                'napoqvane' => $rpcParams['napoqvane'],
                'polivki' => $rpcParams['polivki'],
                'polzvatel' => $rpcParams['polzvatel'],
                'isak_prc_uin' => $rpcParams['isak'],
                'culture' => $rpcParams['culture'],
            ],
            'where' => [
                'id' => $rpcParams['zplot_id'],
            ],
        ];

        $editResult = $UserDbController->editItem($options);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options, $oldValues, 'Edit diary event');

        return $editResult;
    }

    private function loadTreeData()
    {
        $LayersController = new LayersController('Layers');
        $FarmingController = new FarmingController('Farming');
        $userFarmings = $FarmingController->getUserFarmings();
        $userFarmingIds = array_keys($userFarmings);

        // get all farmings
        $options = [
            'return' => ['t.id', 't.farming', 't.year', 't.table_name', 't.extent', 'f.name', 't.color'],
            'where' => [
                'group_id_layer' => ['column' => 'group_id', 'compare' => '=', 'prefix' => 'f', 'value' => $this->User->GroupID],
                'farming' => ['column' => 'farming', 'compare' => 'IN', 'prefix' => 't', 'value' => $userFarmingIds],
                'layer_type' => ['column' => 'layer_type', 'compare' => '=', 'prefix' => 't', 'value' => Config::LAYER_TYPE_ZP],
                'is_exist' => ['column' => 'is_exist', 'compare' => '=', 'prefix' => 't', 'value' => 't'],
            ],
            'sort' => 'farming, year',
            'order' => 'asc',
        ];

        $results = $LayersController->getLayers($options);
        $results_count = count($results);
        if (0 == $results_count) {
            return $this->return;
        }
        $farming_array = [];
        $farming_keys = [];

        $return = [];

        for ($i = 0; $i < $results_count; $i++) {
            $farming = $results[$i]['farming'];
            $year = $results[$i]['year'];
            $table = $results[$i]['table_name'];
            $layerId = $results[$i]['id'];
            $extent = str_replace(' ', ',', trim($results[$i]['extent']));
            $weHaveFarm = in_array($farming, $farming_array);

            if (!$weHaveFarm) {
                $farming_array[] = $farming;
                $return[] = [
                    'id' => $farming,
                    'text' => $results[$i]['name'],
                    'children' => [],
                    'state' => 'open',
                    'iconCls' => 'icon-tree-rents',
                    'attributes' => ['level' => 1],
                ];
                $farming_keys[$farming] = count($return) - 1;
            }

            $return[$farming_keys[$farming]]['children'][] = [
                'text' => $GLOBALS['Farming']['years'][$year]['title'],
                'id' => $year,
                'iconCls' => 'icon-tree-calendar',
                'attributes' => [
                    'color' => $results[$i]['color'],
                    'extent' => $extent,
                    'table' => $table,
                    'id' => $layerId,
                ],
            ];
        }

        return $return;
    }

    private function loadPlotsData($params)
    {
        $LayersController = new LayersController('Layers');
        $UserDbController = new UserDbController($this->User->Database);

        // get all farmings from the layer table in central db
        $options = [
            'where' => [
                'group_id_layer' => ['column' => 'group_id', 'compare' => '=', 'prefix' => 'f', 'value' => $this->User->GroupID],
                'group_id_farming' => ['column' => 'group_id', 'compare' => '=', 'prefix' => 't', 'value' => $this->User->GroupID],
                'layer_type' => ['column' => 'layer_type', 'compare' => '=', 'prefix' => 't', 'value' => 1],
                'farming' => ['column' => 'farming', 'compare' => '=', 'prefix' => 't', 'value' => $params->farming],
                'year' => ['column' => 'year', 'compare' => '=', 'prefix' => 't', 'value' => $params->year],
            ],
            'sort' => 'farming, year',
            'order' => 'asc',
        ];
        $plots_id_array = [];

        $results = $LayersController->getLayers($options);
        if (0 == count($results)) {
            return $this->return;
        }

        // if event filter is requested
        if ($params->filter['event_phase'] || $params->filter['event_type'] || $params->filter['event_subtype'] || $params->filter['event_performer'] || $params->filter['event_date_from'] || $params->filter['event_date_to']) {
            $options = [
                'tablename' => $UserDbController->DbHandler->tableDiaryEvents,
                'where' => [
                    'farming_id' => ['column' => 'farming_id', 'compare' => '=', 'value' => $params->farming],
                    'year_id' => ['column' => 'year_id', 'compare' => '=', 'value' => $params->year],
                    'phase_id' => ['column' => 'phase_id', 'compare' => '=', 'value' => $params->filter['event_phase']],
                    'type_id' => ['column' => 'type_id', 'compare' => '=', 'value' => $params->filter['event_type']],
                    'subtype_id' => ['column' => 'subtype_id', 'compare' => '=', 'value' => $params->filter['event_subtype']],
                    'performer_id' => ['column' => 'performer_id', 'compare' => '=', 'value' => $params->filter['performer_id']],
                ],
            ];

            if (3 == $params->filter['event_phase']) {
                $options['where']['complete_date_from'] = ['column' => 'complete_date_from', 'compare' => '=', 'value' => $params->filter['event_date_from']];
                $options['where']['complete_date_to'] = ['column' => 'complete_date_to', 'compare' => '=', 'value' => $params->filter['event_date_to']];
            } else {
                $options['where']['plan_date_from'] = ['column' => 'plan_date_from', 'compare' => '=', 'value' => $params->filter['event_date_from']];
                $options['where']['plan_date_to'] = ['column' => 'plan_date_to', 'compare' => '=', 'value' => $params->filter['event_date_to']];
            }

            $event_results = $UserDbController->getItemsByParams($options);
            $event_count = count($event_results);
            for ($i = 0; $i < $event_count; $i++) {
                $plots_id_array[] = $event_results[$i]['plot_id'];
            }

            if (0 == count($plots_id_array)) {
                $plots_id_array[] = 0;
            }
        }

        $table = $results[0]['table_name'];
        if ($UserDbController->getTableNameExist($table)) {
            $options = [
                'tablename' => $table . ' zp',
                'leftjoin' => [
                    'table' => 'su_ab_lines ab',
                    'condition' => ' ON (zp.id = ab.plot_id) AND ab.layer_table = \'' . $table . '\' ',
                ],
                'return' => [
                    'zp.id', 'isak_prc_uin', 'ekatte', 'culture',
                    'ST_AsGeoJSON(zp.geom) as geometry', 'trunc(cast((st_area(zp.geom)/1000) as numeric),3) AS area',
                    'BOX2D(zp.geom) AS extent', 'area_name',
                    "json_agg(json_build_object(
                        'type',
                        'Feature',
                        'gid', ab.\"gid\",
                            'name', ab.\"name\",
                            'geom',st_asgeojson(ab.geom)::json,
                            'properties', json_build_object(
                            'name', ab.\"name\",
                            'plot_id', ab.\"plot_id\",
                            'offset', ab.\"marge\",
                            'direction', ab.\"direction\"
                         )
                        )) as ab_lines",
                ],
                'sort' => 'isak_prc_uin',
                'order' => 'asc',
                'where' => [
                    'ekatte' => ['column' => 'ekatte', 'compare' => '=', 'value' => $params->filter['zp_ekate']],
                    'culture' => ['column' => 'culture', 'compare' => '=', 'value' => $params->filter['zp_culture']],
                ],
                'whereOr' => [
                    'isak' => ['column' => 'isak_prc_uin', 'compare' => 'ILIKE', 'value' => $params->filter['zp_isak']],
                    'area_name' => ['column' => 'area_name', 'compare' => 'ILIKE', 'value' => $params->filter['zp_isak']],
                ],
                'group' => 'zp.id',
            ];

            if (empty($params->filter['zp_isak'])) {
                unset($options['whereOr']);
            }
            if (count($plots_id_array)) {
                $options['where']['plot_id_array'] = ['column' => 'id', 'compare' => 'IN', 'value' => $plots_id_array];
            }

            $results = $UserDbController->getItemsByParams($options);
        }

        $results = $this->returnPlotTree($results, $params);

        usort($results, function ($a, $b) {
            return mb_strtolower(trim($a['text'])) > mb_strtolower(trim($b['text'], 'UTF-8'));
        });

        return (0 == count($results)) ? $this->return : $results;
    }

    private function returnPlotTree($results, $params)
    {
        $return = [];
        $result_count = count($results);
        for ($i = 0; $i < $result_count; $i++) {
            if ('' != $results[$i]['area_name']) {
                $plot_name = $results[$i]['area_name'];
            } elseif ('' != $results[$i]['isak_prc_uin']) {
                $plot_name = $results[$i]['isak_prc_uin'];
            } else {
                $plot_name = '-';
            }
            $culture = $GLOBALS['Farming']['crops'][$results[$i]['culture']]['crop_name'];
            $plot_name .= ' (' . $culture . ')';
            $extent = $results[$i]['extent'];
            $extent = str_replace(['BOX(', ')'], '', $extent);
            $extent = str_replace(' ', ',', trim($extent));
            $culture_txt = $culture ? $culture . ' - ' : '';
            $ab_lines = json_decode($results[$i]['ab_lines'], true);

            $return[$i] = [
                'id' => $results[$i]['id'],
                'text' => $plot_name,
                'iconCls' => 'icon-tree-edit-geometry',
                'attributes' => [
                    'level' => 3,
                    'isak_number' => $results[$i]['isak_prc_uin'],
                    'ekate' => $results[$i]['ekatte'],
                    'culture' => $results[$i]['culture'],
                    'culture_name' => $culture,
                    'area' => $results[$i]['area'],
                    'area_name' => $results[$i]['area_name'],
                    'farming' => $params->farming,
                    'year' => $params->year,
                    'geometry' => $results[$i]['geometry'],
                    'extent' => $extent,
                    'label' => $culture_txt . $results[$i]['area'] . ' дка',
                    'ab_lines' => [],
                    'node_type' => 'plot',
                ],
            ];
            if ($params->show_ablines) {
                foreach ($ab_lines as $line) {
                    if (empty($line['gid'])) {
                        continue;
                    }

                    $return[$i]['children'][] = [
                        'text' => $line['properties']['name'],
                        'iconCls' => 'icon-abline',
                        'attributes' => [
                            'id' => $line['gid'],
                            'plot_id' => $line['properties']['plot_id'],
                            'offset' => $line['properties']['offset'],
                            'direction' => $line['properties']['direction'],
                            'geometry' => $line['geom'],
                            'node_type' => 'ab_line',
                        ],
                    ];
                }

                if (isset($return[$i]['children'])) {
                    usort($return[$i]['children'], function ($a, $b) {
                        return mb_strtolower(trim($a['text'])) > mb_strtolower(trim($b['text']));
                    });
                }
            }
        }

        return $return;
    }

    private function loadLeafData($params)
    {
        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableDiaryConfigs,
            'where' => [
                'type_1_and_2' => ['column' => 'config_type', 'compare' => 'IN', 'value' => [1, 2]],
            ],
        ];
        $config_results = $UserDbController->getItemsByParams($options);
        $event_types = [];
        $event_subtypes = [];
        $config_count = count($config_results);
        for ($i = 0; $i < $config_count; $i++) {
            if (1 == $config_results[$i]['config_type']) {
                $event_types[$config_results[$i]['id']] = $config_results[$i]['name'];
            } else {
                $event_subtypes[$config_results[$i]['id']] = $config_results[$i]['name'];
            }
        }

        $options = [
            'tablename' => $UserDbController->DbHandler->tableDiaryEvents,
            'where' => [
                'farming_id' => ['column' => 'farming_id', 'compare' => '=', 'value' => $params->farming],
                'year_id' => ['column' => 'year_id', 'compare' => '=', 'value' => $params->year],
                'plot_id' => ['column' => 'plot_id', 'compare' => '=', 'value' => $params->plot_id],
                'phase_id' => ['column' => 'phase_id', 'compare' => '=', 'value' => $params->filter['event_phase']],
                'type_id' => ['column' => 'type_id', 'compare' => '=', 'value' => $params->filter['event_type']],
                'subtype_id' => ['column' => 'subtype_id', 'compare' => '=', 'value' => $params->filter['event_subtype']],
                'performer_id' => ['column' => 'performer_id', 'compare' => '=', 'value' => $params->filter['performer_id']],
            ],
        ];

        if (3 == $params->filter['event_phase']) {
            $options['where']['complete_date_from'] = ['column' => 'complete_date_from', 'compare' => '=', 'value' => $params->filter['event_date_from']];
            $options['where']['complete_date_to'] = ['column' => 'complete_date_to', 'compare' => '=', 'value' => $params->filter['event_date_to']];
        } else {
            $options['where']['plan_date_from'] = ['column' => 'plan_date_from', 'compare' => '=', 'value' => $params->filter['event_date_from']];
            $options['where']['plan_date_to'] = ['column' => 'plan_date_to', 'compare' => '=', 'value' => $params->filter['event_date_to']];
        }

        $results = $UserDbController->getItemsByParams($options);

        $return = [];
        $result_count = count($results);
        if (0 == $result_count) {
            return $this->return;
        }
        for ($i = 0; $i < $result_count; $i++) {
            $return[] = [
                'id' => $results[$i]['id'],
                'text' => '[' . $results[$i]['serial_num'] . '] ' . $event_types[$results[$i]['type_id']] . ' - ' . $event_subtypes[$results[$i]['subtype_id']],
                'iconCls' => 'icon-tree-planting',
                'attributes' => [
                    'serial_num' => $results[$i]['serial_num'],
                    'level' => 4,
                ],
            ];
        }

        return $return;
    }
}
