<?php

namespace TF\Engine\APIClasses\Diary;

// Prado::using('Plugins.Core.Layers.conf');
// Prado::using('Plugins.Core.Layers.*');
// Prado::using('Plugins.Core.Farming.conf');

use Prado\Web\Services\TRpcApiProvider;
use Prado\Web\Services\TRpcServer;
use TF\Application\Common\Config;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbDiary\UserDbDiaryController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * The class manages all Wialon actions.
 *
 * @rpc-module Diary
 *
 * @rpc-service-id wialon-actions
 */
class WialonActions extends TRpcApiProvider
{
    protected $returnGridNoRows = [
        'rows' => [],
        'total' => 0,
    ];
    private $module = 'Diary';
    private $service_id = 'wialon-actions';

    private $sid;
    private $accountID;
    private $wialonUserID;

    public function __construct(TRpcServer $rpcServer)
    {
        parent::__construct($rpcServer);
    }

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'getAll' => ['method' => [$this, 'getAll'],
                'validators' => [
                    'rpcParams' => [
                        'plot_id' => 'validateInteger, validateRequired',
                        'farming' => 'validateInteger, validateRequired',
                        'year' => 'validateInteger, validateRequired',
                        'date_from' => 'validateDate, validateRequired',
                        'date_to' => 'validateDate, validateRequired',
                        'time_to' => 'validateRequired',
                        'time_from' => 'validateRequired',
                    ],
                ],
            ],
            'getFuelUnitsPlot' => ['method' => [$this, 'getFuelUnitsPlot'],
                'validators' => [
                    'rpcParams' => [
                        'plot_id' => 'validateInteger, validateRequired',
                        'farming_id' => 'validateInteger, validateRequired',
                        'year_id' => 'validateInteger, validateRequired',
                        'date_from' => 'validateDate, validateRequired',
                        'date_to' => 'validateDate, validateRequired',
                        'time_to' => 'validateRequired',
                        'time_from' => 'validateRequired',
                        'wialon_id' => 'validateInteger',
                        'timezoneOffset' => 'validateInteger',
                    ],
                ],
            ],
            'getMessages' => ['method' => [$this, 'getMessages']],
            'getWialonReportData' => ['method' => [$this, 'getWialonReportData']],
            'getWialonMachines' => ['method' => [$this, 'getWialonMachines']],
            'getWialonDrivers' => ['method' => [$this, 'getWialonDrivers']],
            'getWialonMachineTypes' => ['method' => [$this, 'getWialonMachineTypes']],
            'addWialonToken' => ['method' => [$this, 'addWialonToken'],
                'validators' => [
                    'token' => 'validateRequired,  validateText',
                ],
            ],
            'wialonLogin' => ['method' => [$this, 'wialonLogin']],
            'createGeofence' => ['method' => [$this, 'generateGeofenceFromZPlot']],
        ];
    }

    /** Get crossing unit of a Plot/ For each unit, add fuel report grouped by day.
     * @param array $rpcParams
     *
     * @throws MTRpcException -33352 TRACK_NOT_CROSSING_GEOZONE
     * @throws MTRpcException -33353 DIARY_REPORT_CREATION_ERROR
     * @throws MTRpcException -33354 INVALID_OR_EXPIRED_TOKEN
     * @throws MTRpcException -33355 PLOT_WITHOUT_CROSSING_UNITS
     * @throws MTRpcException -33356 PLOT_NOT_FOUND
     *
     * @return array
     *
     * @uses report TF33334.wlp
     */
    public function getAll($rpcParams = [])
    {
        $UserDbDiaryController = new UserDbDiaryController($this->User->Database);
        $LayersController = new LayersController('Layers');
        $UserDbController = new UserDbController($this->User->Database);

        $this->wialonLogin();
        $geofenceID = $this->generateGeofenceFromZPlot($rpcParams['farming'], $rpcParams['year'], $rpcParams['plot_id']);
        $units = $this->getUnits();
        if (0 == count($units->items)) {
            $this->deleteGeofence($geofenceID);

            return $this->returnGridNoRows;
        }
        list($units_ids, $units_arr) = $this->storeUnits($units);
        $master_grp = $this->createGroup();
        $master_grp_id = $master_grp->item->id;
        $this->updateGroup($master_grp_id, $units_ids);
        $report = $this->updateTemplate('TF33336.json', $master_grp_id, $geofenceID);
        $UserDbDiaryController->executeWialonRequest('report/cleanup_result', [], $this->sid, true);
        $updateReport = $this->createWialonReport($report);
        if ($updateReport->error) {
            $this->cleanUp($master_grp_id, $geofenceID, null);

            throw new MTRpcException('DIARY_REPORT_CREATION_ERROR', -33353);
        }
        $report_id = $updateReport[0];
        $fromTime = strtotime($rpcParams['date_from'] . ' ' . $rpcParams['time_from']);
        $toTime = strtotime($rpcParams['date_to'] . ' ' . $rpcParams['time_to']);
        $reportResults = $this->exec_report($updateReport, $master_grp_id, $fromTime, $toTime, $geofenceID, $report);
        if (!$this->isTableGeofencePresent($reportResults)) {
            $this->cleanUp($master_grp_id, $geofenceID, $report_id);

            throw new MTRpcException('TRACK_NOT_CROSSING_GEOZONE', -33352);
        }
        $allRows = $this->parseTables($reportResults, $master_grp_id, $geofenceID, $report_id);
        $this->cleanUp($master_grp_id, $geofenceID, $report_id);
        $this->formatData($allRows, 'unit_group_zones_visit', $units_arr);
        $this->formatData($allRows, 'unit_group_trips', $units_arr);
        $return = [];
        foreach ($allRows['unit_group_zones_visit'] as &$row) {
            if ('unit' == $row['_grouping']) {
                $row['iconCls'] = 'icon-blank';
                $return['rows'][] = $row;
            }
        }

        foreach ($return['rows'] as &$returnRow) {
            $level_index = 0;
            foreach ($allRows['unit_group_zones_visit'] as &$geoRow) {
                if (($returnRow['unit_id'] == $geoRow['unit_id']) && 'day' == $geoRow['_grouping']) {
                    $geoRow['state'] = 'closed';
                    $geoRow['iconCls'] = 'icon-tree-edit-geometry';
                    $returnRow['children'][$level_index] = $geoRow;
                    foreach ($allRows['unit_group_trips'] as &$trip) {
                        if ('day' == $trip['_grouping'] && ($geoRow['unit_id'] == $trip['unit_id']) && ($geoRow['Grouping'] == $trip['Grouping'])) {
                            $returnRow['children'][$level_index]['children'][] = $trip;
                        }
                    }
                    ++$level_index;
                }
            }
        }
        $return['total'] = count($return['rows']);

        return $return;
    }

    /**
     * @api-method getFuelUnitsPlot
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer farming_id
     *                         #item integer year_id
     *                         #item integer plot_id
     *                         #item string data_from
     *                         #item string data_to
     *                         #item string time_from
     *                         #item string time_to
     *                         #item integer unit_id (optional)
     *                         }
     *
     * @throws MTRpcException -33353 DIARY_REPORT_CREATION_ERROR
     * @throws MTRpcException -33354 INVALID_OR_EXPIRED_TOKEN
     * @throws MTRpcException -33355 PLOT_WITHOUT_CROSSING_UNITS
     *
     * @deprecated
     *
     * @return array {
     *               #item string complete_date
     *               #item string track_time_from
     *               #item string track_time_to
     *               #item integer time_in
     *               #item integer time_out
     *               #item string duration_in
     *               #item string movement_duration
     *               #item string parking_duration
     *               #item string mileage
     *               #item string avg_engine_revs
     *               #item string consumed_fuel
     *               #item string avg_fuel
     *               #item string avg_speed
     *               #item integer machine_id
     *               }
     */
    public function getFuelUnitsPlot($rpcParams = [])
    {
        $UserDbDiaryController = new UserDbDiaryController($this->User->Database);
        $LayersController = new LayersController('Layers');
        $UserDbController = new UserDbController($this->User->Database);

        $this->wialonLogin();
        $geofenceID = $this->generateGeofenceFromZPlot($rpcParams['farming_id'], $rpcParams['year_id'], $rpcParams['plot_id']);

        // clean old report results
        $UserDbDiaryController->executeWialonRequest('report/cleanup_result', [], $this->sid, true);

        // GET ALL VEHICLE (UNITS) IDs
        $units = $this->getUnits();

        if (0 == count($units->items)) {
            $this->deleteGeofence($geofenceID);

            return $this->returnGridNoRows;
        }

        // COLLECT UNITS IDs
        list($units_ids, $units_arr) = $this->storeUnits($units);

        // if we receive a unit id as a parameter, we add that unit to the group instead of all units, overwriting $units_ids
        if (isset($rpcParams['wialon_id']) && is_int($rpcParams['wialon_id'])) {
            $units_ids = [$rpcParams['wialon_id']];
        }
        // CREATE A GROUP OF UNITS (vehicle) CALLED MASTER_GROUP , to be used as a subject of this report
        $master_grp = $this->createGroup();
        $master_grp_id = $master_grp->item->id;
        // ADD collected UNITS IDs TO MASTER_GROUP
        $UserDbDiaryController->executeWialonRequest('unit_group/update_units', [
            'itemId' => $master_grp_id, // unit group ID
            'units' => $units_ids, // array of units IDs
        ], $this->sid);

        $file = TEMPLATE_PATH . 'TF33335.json';
        $reportTemplate = json_decode(file_get_contents($file), true);
        $reportTemplate['reports'][0]['n'] = 'TF33335';

        // bind MASTER_GROUP id to the template and update it on the server of wialon,
        $reportTemplate['reports'][0]['p'] = json_decode($reportTemplate['reports'][0]['p'], true);
        $reportTemplate['reports'][0]['p']['bind'] = [$master_grp_id];
        $reportTemplate['reports'][0]['p'] = json_encode($reportTemplate['reports'][0]['p']);

        // update parameter p of the template for table unit_group_zones_visit to query just the selected (and just created ) geofence id
        foreach ($reportTemplate['reports'][0]['tbl'] as $tableIndex => $table) {
            if ('unit_group_zones_visit' == $table['n']) {
                $reportTemplate['reports'][0]['tbl'][$tableIndex]['p'] = json_decode($reportTemplate['reports'][0]['tbl'][$tableIndex]['p'], true);
                $reportTemplate['reports'][0]['tbl'][$tableIndex]['p']['geozones'] = json_encode($geofenceID, true);
                $reportTemplate['reports'][0]['tbl'][$tableIndex]['p'] = json_encode($reportTemplate['reports'][0]['tbl'][$tableIndex]['p'], true);
            }
            /*   if ($table['n'] == 'unit_group_engine_hours') {
                   $reportTemplate['reports'][0]['tbl'][$tableIndex]['p'] = json_decode($reportTemplate['reports'][0]['tbl'][$tableIndex]['p'], true);
                   $reportTemplate['reports'][0]['tbl'][$tableIndex]['p']['geozones_ex']['zones'] = json_encode($geofenceID, true);
                   $reportTemplate['reports'][0]['tbl'][$tableIndex]['p'] = json_encode($reportTemplate['reports'][0]['tbl'][$tableIndex]['p'], true);

               }*/
        }
        // refresh reference to template json object
        $report = $reportTemplate['reports'][0];
        // update or create template
        $updateReport = $this->createWialonReport($report);
        if ($updateReport->error) {
            $this->cleanUp($master_grp->item->id, $geofenceID, null);

            throw new MTRpcException('DIARY_REPORT_CREATION_ERROR', -33353);
        }
        $report_id = $updateReport[0];
        // report settings
        $fromTime = strtotime($rpcParams['date_from'] . ' ' . $rpcParams['time_from']);
        $toTime = strtotime($rpcParams['date_to'] . ' ' . $rpcParams['time_to']);
        // if($rpcParams['timezoneOffset']) $fromTime +=((int)$rpcParams['timezoneOffset']*-1 * 60);
        // if($rpcParams['timezoneOffset']) $toTime +=((int)$rpcParams['timezoneOffset']*-1 * 60);
        $reportResults = $this->exec_report($updateReport, $master_grp_id, $fromTime, $toTime, $geofenceID, $report);

        if ($reportResults->error) {
            $this->cleanUp($master_grp->item->id, $geofenceID, $report_id);

            return $this->returnGridNoRows;
        }

        // delete MASTER GROUP, to avoid having duplicates on next requests
        $UserDbDiaryController->executeWialonRequest('item/delete_item', ['itemId' => $master_grp->item->id], $this->sid);
        if (!$this->isTableGeofencePresent($reportResults)) {
            $this->cleanUp($master_grp_id, $geofenceID, $report_id);

            throw new MTRpcException('TRACK_NOT_CROSSING_GEOZONE', -33352);
        }
        $reportRows = [];
        foreach ($reportResults->tables as $tableIndex => $table) {
            $temp = $UserDbDiaryController->executeWialonRequest('report/select_result_rows', [
                'tableIndex' => $tableIndex,
                'config' => [
                    'type' => 'range',
                    'data' => [
                        'from' => 0,                // first row index;
                        'to' => $table->rows,       // last row index;
                        'level' => $table->level,   // nesting level;
                        'flat' => 1,
                    ],
                ],
                'indexFrom' => 0,
            ], $this->sid);

            if (!$temp->error) {
                $keys = $reportResults->tables[$tableIndex]->header;
                $values = [];
                foreach ($temp as $i => $row) {
                    $values[$i] = json_decode(json_encode($row->c), true);
                    $values[$i] = array_combine($keys, $values[$i]);
                }
                $reportRows[$table->name] = $values;
            }
        }
        if (!isset($reportRows['unit_group_zones_visit'])) {
            $this->cleanUp($master_grp_id, $geofenceID, $report_id);

            throw new MTRpcException('TRACK_NOT_CROSSING_GEOZONE', -33352);
        }
        // finally we delete created geofence
        $UserDbDiaryController->executeWialonRequest('resource/update_zone', ['itemId' => $this->accountID, 'id' => $geofenceID, 'callMode' => 'delete'], $this->sid);
        $crossing_units = array_unique(array_column($reportRows['unit_group_zones_visit'], 'Grouping'));

        // GET ALL VEHICLE (UNITS) IDs
        $units = $UserDbDiaryController->executeWialonRequest('core/search_items', [
            'spec' => [
                'itemsType' => 'avl_unit', 'propName' => 'sys_id', 'propValueMask' => '*', 'sortType' => 'sys_id',
            ], 'force' => 1, 'from' => 0, 'to' => 0, 'flags' => 0x03], $this->sid);
        $units_arr = [];
        if (!empty($crossing_units)) {
            foreach ($units->items as $index => $unit) {
                if (in_array($unit->nm, $crossing_units)) {
                    $units_arr[$index]['wialon_id'] = $unit->id;
                    $units_arr[$index]['name'] = $unit->nm;
                }
            }
        }
        // delete report template
        $this->deleteReport($report_id);

        return [
            'farm_track_units' => $units_arr,
            'data' => $reportRows,
        ];
    }

    /** Get Vehicles's tracks, param date, return array of coordinates , lat and lon, for the track
     * [getMessages description].
     *
     * @api-method getMessages
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer   machine_id
     *                         #item date      date (optional)
     *                         #item timestamp time_from
     *                         #item timestamp time_to
     *                         }
     *
     * @throws MTRpcException
     *
     * @return array
     */
    public function getMessages($rpcParams)
    {
        $this->wialonLogin();
        $urlRequest = WIALON_PATH
            . 'ajax.html?svc=messages/unload&params={}&sid=' . $_SESSION['wialon_eid'];

        $curl = curl_init($urlRequest);
        curl_setopt($curl, CURLOPT_FAILONERROR, true);
        curl_setopt($curl, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);

        json_decode(curl_exec($curl));
        // we pass here directly a unix time stamp from a hidden field of the easy ui datagrid
        if ($this->isTimestamp($rpcParams['time_from']) && $this->isTimestamp($rpcParams['time_to'])) {
            $url_array = [
                'itemId' => $rpcParams['machine_id'],
                'timeFrom' => $rpcParams['time_from'],
                'timeTo' => $rpcParams['time_to'],
                'flags' => '0x0000', // messages with data
                'flagsMask' => '0xFF00', // all messages with data
                'loadCount' => 0xFFFFFFFF, // how many messages to return (0xffffffff - all found)
            ];
        } else {
            // OLD METHOD
            if ($rpcParams['date']) {
                $timeFrom = strtotime($rpcParams['date'] . ' ' . $rpcParams['time_from']);
                $timeTo = strtotime($rpcParams['date'] . ' ' . $rpcParams['time_to']);
            } else {
                $dateTimeFrom = date('Y-m-d H:i:s', strtotime($rpcParams['time_from']));
                $dateTimeTo = date('Y-m-d H:i:s', strtotime($rpcParams['time_to']));
                $timeFrom = strtotime($dateTimeFrom);
                $timeTo = strtotime($dateTimeTo);
            }

            $url_array = [
                'itemId' => $rpcParams['machine_id'],
                'timeFrom' => $timeFrom,
                'timeTo' => $timeTo,
                'flags' => '0x0000', // messages with data
                'flagsMask' => '0xFF00', // all messages with data
                'loadCount' => 0xFFFFFFFF, // how many messages to return (0xffffffff - all found)
            ];
        }

        $url_string = json_encode($url_array);

        $urlRequest = WIALON_PATH
            . 'ajax.html?svc=messages/load_interval&params='
            . $url_string
            . '&sid=' . $_SESSION['wialon_eid'];

        $curl = curl_init($urlRequest);
        curl_setopt($curl, CURLOPT_FAILONERROR, true);
        curl_setopt($curl, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        $results = json_decode(curl_exec($curl));

        if (!$results->messages && 0 == $results->count) {
            return [];
        }

        $return = [];

        for ($i = 0; $i < $results->count; $i++) {
            $return[] = [
                'lon' => $results->messages[$i]->pos->x,
                'lat' => $results->messages[$i]->pos->y,
            ];
        }

        return $return;
    }

    /** Get Fuel report for one day.
     * @api-method getWialonReportData
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer farming_id
     *                         #item integer year_id
     *                         #item integer plot_id
     *                         #item string machine_id
     *                         #item string track_date
     *                         #item string track_time_from
     *                         #item string track_time_to
     *                         }
     *
     * @throws MTRpcException -33353 DIARY_REPORT_CREATION_ERROR
     * @throws MTRpcException -33352 TRACK_NOT_CROSSING_GEOZONE
     *
     * @return array {
     *               #item string complete_date
     *               #item string track_time_from
     *               #item string track_time_to
     *               #item integer time_in
     *               #item integer time_out
     *               #item string duration_in
     *               #item string movement_duration
     *               #item string parking_duration
     *               #item string mileage
     *               #item string avg_engine_revs
     *               #item string consumed_fuel
     *               #item string avg_fuel
     *               #item string avg_speed
     *               #item integer machine_id
     *               }
     */
    public function getWialonReportData($rpcParams)
    {
        $UserDbDiaryController = new UserDbDiaryController($this->User->Database);
        $LayersController = new LayersController('Layers');
        $UserDbController = new UserDbController($this->User->Database);

        $login_results = $this->wialonLogin();
        if ($login_results->error && (4 == $login_results->error || 8 == $login_results->error)) {
            throw new MTRpcException('INVALID_OR_EXPIRED_TOKEN', -33354);
        }

        $session_id = $login_results->eid;
        // end of login

        // get resource ID from report template
        $wialon_params = [
            // search specification
            'spec' => [
                'itemsType' => 'avl_resource', // resource
                'propType' => 'propitemname',
                'propName' => 'reporttemplates',
                'propValueMask' => '*',
                'sortType' => 'reporttemplates',
            ],
            'force' => 1,
            'flags' => 0x1,
            'from' => 0,
            'to' => 0,
        ];
        $resource_results = $UserDbDiaryController->executeWialonRequest('core/search_items', $wialon_params, $session_id);

        $resourceID = $resource_results->items[0]->id;
        $templateID = $this->createTFTemplate($session_id, $resourceID, 'TF.wlp');
        // get table name from database
        $options = [
            'where' => [
                'farming' => ['column' => 'farming', 'compare' => '=', 'prefix' => 't', 'value' => $rpcParams['farming_id']],
                'year' => ['column' => 'year', 'compare' => '=', 'prefix' => 't', 'value' => $rpcParams['year_id']],
                'layer_type' => ['column' => 'layer_type', 'compare' => '=', 'prefix' => 't', 'value' => 1],
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'prefix' => 't', 'value' => $this->User->GroupID],
            ],
        ];
        $layer_results = $LayersController->getLayers($options);

        // get chosen plot info
        $options = [
            'tablename' => $layer_results[0]['table_name'],
            'return' => [
                'isak_prc_uin as isak', 'ST_AsText(ST_Transform(geom, 4326)) as geom',
            ],
            'where' => [
                'zplot_id' => ['column' => 'id', 'compare' => '=', 'value' => $rpcParams['plot_id']],
            ],
        ];

        $results = $UserDbController->getItemsByParams($options, false, false);
        $polyRegEx = '/MULTIPOLYGON\\(\\(\\((?P<coords>[^\\)]+?)\\)/i';
        $isak = $results[0]['isak'];
        $geom = $results[0]['geom'];
        preg_match_all($polyRegEx, $geom, $matches);

        $coords_array = explode(',', $matches['coords'][0]);
        $coords = [];

        for ($i = 0; $i < count($coords_array); $i++) {
            $fragments = explode(' ', $coords_array[$i]);
            $coords[] = [
                'x' => $fragments[0],
                'y' => $fragments[1],
                'r' => 0,
            ];
        }

        // add geofence
        $wialon_params = [
            'id' => 0,          // id of the geofence , zero to create
            'n' => $isak,       // name
            'd' => '',          // description
            't' => 2,           // type: 1 - line, 2 - polygon, 3 - circle
            'w' => 0,           // line thickness or circle radius
            'f' => 0,           // geofence flags ( 0x20 – show shape )
            'c' => **********,  // color (ARGB)
            'p' => $coords,     // array of geofence points  "x":<double>, longitude ,"y":<double>,	/* latitude
            'itemId' => $resourceID, // resource ID
            'callMode' => 'create',
        ];

        $geofence_results = $UserDbDiaryController->executeWialonRequest('resource/update_zone', $wialon_params, $session_id);
        $geofenceID = $geofence_results[0];

        // get report data
        $wialon_params = [
            'itemId' => $resourceID,
            'col' => [$templateID],
        ];
        $report_data_results = $UserDbDiaryController->executeWialonRequest('report/get_report_data', $wialon_params, $session_id);

        foreach ($report_data_results as $key => $value) {
            $reportData = $value;
        }
        // print_r($report_data_results);

        $update_report_options = [
            'itemId' => $resourceID,
            'id' => $templateID, // template ID
            'callMode' => 'update',
            'n' => 'TF', // name
            'ct' => 'avl_unit',
            'p' => '', // parameters
            'tbl' => [],
        ];

        $sch_array = [
            'f1' => 0, // beginning of interval 1
            'f2' => 0, // beginning of interval 2
            't1' => 0, // ending of interval 1
            't2' => 0, // ending of interval 2
            'm' => 0, // days of month mask
            'y' => 0, // months mask
            'w' => 0, // days of week mask
        ];

        for ($i = 0; $i < count($reportData->tbl); $i++) {
            $tblParams = $reportData->tbl[$i];
            $encodedInnerParams = null;
            if ('unit_zones_visit' == $tblParams->n) {
                $innerParams = json_decode($reportData->tbl[$i]->p);
                $innerParams->geozones = (string)$geofenceID;
                $encodedInnerParams = json_encode($innerParams);
            } elseif ('unit_stats' == $tblParams->n) {
                $innerParams = json_decode($reportData->tbl[$i]->p);
                $innerParams->us_units = 0;
                $encodedInnerParams = $UserDbDiaryController->StringHelper->jsonRemoveUnicodeSequences($innerParams);
            } elseif ('unit_engine_hours' == $tblParams->n) {
                $innerParams = json_decode(stripslashes($reportData->tbl[$i]->p));
                // $innerParams = stripslashes($innerParams);
                $encodedInnerParams = stripslashes($UserDbDiaryController->StringHelper->jsonRemoveUnicodeSequences($innerParams));
            } else {
                if ('' == $reportData->tbl[$i]->p) {
                    $encodedInnerParams = $reportData->tbl[$i]->p;
                }
            }

            $update_report_options['tbl'][] = [
                'n' => $tblParams->n, // table type
                'l' => $tblParams->l, // name
                'c' => $tblParams->c, // list of columns
                'cl' => $tblParams->cl, // list of column labels
                's' => $tblParams->s, // list of columns (if it is statistics table)
                'sl' => $tblParams->sl, // list of column labels (if it is statistics table)
                'p' => $encodedInnerParams, // table parameters
                'sch' => $sch_array, // time limitation
                'f' => $tblParams->f,
            ];
        }

        $UserDbDiaryController->executeWialonRequest('report/update_report', $update_report_options, $session_id);

        // clean old report results
        $UserDbDiaryController->executeWialonRequest('report/cleanup_result', [], $session_id, true);

        // execute report
        $url_array = [
            'reportResourceId' => $resourceID,
            'reportTemplateId' => $templateID,
            'reportObjectId' => (int)$rpcParams['machine_id'],
            'reportObjectSecId' => 0,
            'interval' => [
                'from' => strtotime($rpcParams['track_date'] . ' ' . $rpcParams['track_time_from']) - 3600,
                'to' => strtotime($rpcParams['track_date'] . ' ' . $rpcParams['track_time_to']) - 3600,
                'flags' => 0,
            ],
        ];

        $results = $UserDbDiaryController->executeWialonRequest('report/exec_report', $url_array, $session_id);

        $wialon_params = [
            'itemId' => $resourceID,
            'id' => $geofenceID,
            'callMode' => 'delete',
        ];

        $UserDbDiaryController->executeWialonRequest('resource/update_zone', $wialon_params, $session_id);
        $this->deleteTemplate($templateID, $session_id, $resourceID);

        if ($results->reportResult) {
            // get machine ID from database
            $options = [
                'tablename' => $UserDbController->DbHandler->tableDiaryConfigs,
                'where' => [
                    'wialon_id' => ['column' => 'wialon_id', 'compare' => '=', 'value' => $rpcParams['machine_id']],
                    'config_type' => ['column' => 'config_type', 'compare' => '=', 'value' => 4],
                ],
            ];
            $machine_results = $UserDbController->getItemsByParams($options, false, false);
            $machineID = 0;
            if (1 == count($machine_results)) {
                $machineID = $machine_results[0]['id'];
            }

            $reportTables = $results->reportResult->tables;
            $reportsByName = [];

            for ($i = 0; $i < count($reportTables); $i++) {
                $reportTable = $reportTables[$i];
                $reportTableName = $reportTable->name;
                $reportsByName[$reportTableName] = $reportTable;
            }

            $zoneVisitsReport = $reportsByName['unit_zones_visit'];

            if (is_null($zoneVisitsReport)) {
                throw new MTRpcException('TRACK_NOT_CROSSING_GEOZONE', -33352);
            }

            $timeInExplode = explode(' ', $zoneVisitsReport->total[2]);
            $timeOutExplode = explode(' ', $zoneVisitsReport->total[3]);
            $columnsByName = array_combine($zoneVisitsReport->header, $zoneVisitsReport->total);
            $columnsRealValues = array_combine($zoneVisitsReport->header, $zoneVisitsReport->totalRaw);

            $timeInDateTime = explode(' ', $columnsByName['Vreme_vav']);
            $timeInTime = $timeInDateTime[1];
            $timeOutDateTime = explode(' ', $columnsByName['Vreme_izvan']);
            $timeOutTime = $timeOutDateTime[1];

            return [
                'complete_date' => $rpcParams['track_date'],
                'track_time_from' => $rpcParams['track_time_from'],
                'track_time_to' => $rpcParams['track_time_to'],
                'time_in' => $columnsRealValues['Vreme_vav']->v,
                'time_out' => $columnsRealValues['Vreme_izvan']->v,
                'duration_in' => $columnsByName['Prodazhitelnost_vav'],
                'movement_duration' => date('H:i:s', strtotime($columnsByName['Prodazhitelnost_vav']) - strtotime($columnsByName['Parking_vreme'])),
                'parking_duration' => $columnsByName['Parking_vreme'],
                'mileage' => str_replace(' km', '', $columnsByName['Kilometrazh']),
                'avg_engine_revs' => $columnsByName['Sredno_natovarvane'],
                'consumed_fuel' => str_replace(' lt', '', $columnsByName['Izrazhodvano_gorivo']),
                'avg_fuel' => str_replace(' lt/100 km', '', $columnsByName['Sreden_razhod_gorivo']),
                'avg_speed' => str_replace(' km', '', $columnsByName['Sredna_skorost']),
                'machine_id' => $machineID,
            ];
        }

        return [];
    }

    /**
     * [getWialonMachines description].
     *
     * @throws MTRpcException
     */
    public function getWialonMachines()
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $results = $this->wialonLogin();

        if ($results->error && (4 == $results->error || 8 == $results->error)) {
            throw new MTRpcException('INVALID_OR_EXPIRED_TOKEN', -33354);
        }

        $_SESSION['wialon_eid'] = $results->eid;

        // end of login
        // get unit types from db and create predefined array
        $db_unit_types = [];
        $options = [
            'tablename' => $UserDbController->DbHandler->tableDiaryConfigs,
        ];
        $unit_types_results = $UserDbController->getItemsByParams($options);

        for ($i = 0; $i < count($unit_types_results); $i++) {
            if ('' != $unit_types_results[$i]['wialon_id']) {
                $db_unit_types[$unit_types_results[$i]['wialon_id']] = $unit_types_results[$i]['id'];
            }
        }

        $url_array = [
            'spec' => [
                'itemsType' => 'avl_unit_group',
                'propName' => 'sys_name',
                'propValueMask' => '*',
                'sortType' => 'sys_name',
            ],
            'force' => 1,
            'flags' => 1,
            'from' => 0,
            'to' => 0,
        ];
        $url_string = json_encode($url_array);

        $urlRequest = WIALON_PATH
            . 'ajax.html?svc=core/search_items&params='
            . $url_string
            . '&sid=' . $_SESSION['wialon_eid'];

        $curl = curl_init($urlRequest);
        curl_setopt($curl, CURLOPT_FAILONERROR, true);
        curl_setopt($curl, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        $results = json_decode(curl_exec($curl));

        $unit_types = [];
        for ($i = 0; $i < count($results->items); $i++) {
            for ($j = 0; $j < count($results->items[$i]->u); $j++) {
                $unit_types[$results->items[$i]->u[$j]] = $db_unit_types[$results->items[$i]->id];
            }
        }

        // get units
        $url_array = [
            'spec' => [
                'itemsType' => 'avl_unit',
                'propName' => 'sys_name',
                'propValueMask' => '*',
                'sortType' => 'sys_name',
            ],
            'force' => 1,
            'flags' => 1,
            'from' => 0,
            'to' => 0,
        ];
        $url_string = json_encode($url_array);

        $urlRequest = WIALON_PATH
            . 'ajax.html?svc=core/search_items&params='
            . $url_string
            . '&sid=' . $_SESSION['wialon_eid'];

        $curl = curl_init($urlRequest);
        curl_setopt($curl, CURLOPT_FAILONERROR, true);
        curl_setopt($curl, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        $unit_results = json_decode(curl_exec($curl));

        // iterate units and add to database
        for ($i = 0; $i < count($unit_results->items); $i++) {
            $options = [
                'tablename' => $UserDbController->DbHandler->tableDiaryConfigs,
                'where' => [
                    'wialon_id' => ['column' => 'wialon_id', 'compare' => '=', 'value' => $unit_results->items[$i]->id],
                ],
            ];

            $machine_db_results = $UserDbController->getItemsByParams($options);

            $options = [
                'tablename' => $UserDbController->DbHandler->tableDiaryConfigs,
                'mainData' => [
                    'number' => $unit_results->items[$i]->nm,
                    'wialon_id' => $unit_results->items[$i]->id,
                    'type_id' => $unit_types[$unit_results->items[$i]->id],
                    'config_type' => 4,
                ],
            ];

            if (0 != count($machine_db_results)) {
                // edit is needed
                $options['where']['wialon_id'] = $unit_results->items[$i]->id;
                $UserDbController->editItem($options);

                $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options, $machine_db_results, 'Edit machine record');
            } else {
                $recordID = $UserDbController->addItem($options);

                $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options['mainData'], ['machine_id' => $recordID], 'Add machine record from wialon');
            }
        }
    }

    /**
     * [getWialonDrivers description].
     *
     * @throws MTRpcException
     *
     * @return null|array if no result
     */
    public function getWialonDrivers()
    {
        $UsersController = new UsersController('Users');
        $UserDbController = new UserDbController($this->User->Database);
        $results = $this->wialonLogin();

        if ($results->error && (4 == $results->error || 8 == $results->error)) {
            throw new MTRpcException('INVALID_OR_EXPIRED_TOKEN', -33354);
        }

        $_SESSION['wialon_eid'] = $results->eid;
        // end of session reload

        $url_array = [
            'spec' => [
                'itemsType' => 'avl_resource',
                'propType' => 'propitemname',
                'propName' => 'drivers',
                'propValueMask' => '*',
                'sortType' => 'drivers',
            ],
            'force' => 1,
            'flags' => 0x00000100,
            'from' => 0,
            'to' => 0,
        ];

        $url_string = json_encode($url_array);

        $urlRequest = WIALON_PATH
            . 'ajax.html?svc=core/search_items&params='
            . $url_string
            . '&sid=' . $_SESSION['wialon_eid'];

        $curl = curl_init($urlRequest);
        curl_setopt($curl, CURLOPT_FAILONERROR, true);
        curl_setopt($curl, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        $results = json_decode(curl_exec($curl));

        if (0 == count($results->items)) {
            return [];
        }

        foreach ($results->items[0]->drvrs as $item) {
            $options = [
                'tablename' => $UserDbController->DbHandler->tableDiaryConfigs,
                'where' => [
                    'wialon_id' => ['column' => 'wialon_id', 'compare' => '=', 'value' => $item->id],
                ],
            ];

            $perf_results = $UserDbController->getItemsByParams($options);

            if (0 != count($perf_results)) {
                $options = [
                    'tablename' => $UserDbController->DbHandler->tableDiaryConfigs,
                    'mainData' => [
                        'name' => $item->n,
                        'description' => $item->ds,
                    ],
                    'where' => [
                        'wialon_id' => $item->id,
                    ],
                ];

                $UserDbController->editItem($options);

                $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options, $perf_results, 'Edit driver record');
            } else {
                $options = [
                    'tablename' => $UserDbController->DbHandler->tableDiaryConfigs,
                    'mainData' => [
                        'name' => $item->n,
                        'description' => $item->ds,
                        'config_type' => 9,
                        'wialon_id' => $item->id,
                    ],
                ];

                $recordID = $UserDbController->addItem($options);

                $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options['mainData'], ['driver_id' => $recordID], 'Add driver record from wialon');
            }
        }
    }

    /**
     * [getWialonMachineTypes description].
     *
     * @throws MTRpcException
     *
     * @return array
     */
    public function getWialonMachineTypes()
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbDiaryController = new UserDbDiaryController($this->User->Database);

        $results = $this->wialonLogin();

        if ($results->error && (4 == $results->error || 8 == $results->error)) {
            throw new MTRpcException('INVALID_OR_EXPIRED_TOKEN', -33354);
        }
        $session_id = $results->eid;

        $wialon_params = [
            'spec' => [
                'itemsType' => 'avl_unit_group',
                'propName' => 'sys_name',
                'propValueMask' => '*',
                'sortType' => 'sys_name',
            ],
            'force' => 1,
            'flags' => 1,
            'from' => 0,
            'to' => 0,
        ];
        $results = $UserDbDiaryController->executeWialonRequest('core/search_items', $wialon_params, $session_id);
        $groupsCount = count($results->items);
        $return = [];
        if (0 == $groupsCount) {
            return $return;
        }
        for ($i = 0; $i < $groupsCount; $i++) {
            $options = [
                'tablename' => $UserDbController->DbHandler->tableDiaryConfigs,
                'where' => [
                    'wialon_id' => ['column' => 'wialon_id', 'compare' => '=', 'value' => $results->items[$i]->id],
                ],
            ];

            $config_results = $UserDbController->getItemsByParams($options);
            if (0 != count($config_results)) {
                $options = [
                    'tablename' => $UserDbController->DbHandler->tableDiaryConfigs,
                    'mainData' => [
                        'name' => $results->items[$i]->nm,
                        'wialon_id' => $results->items[$i]->id,
                    ],
                    'where' => [
                        'wialon_id' => $results->items[$i]->id,
                    ],
                ];

                $return[] = $UserDbController->editItem($options);
            } else {
                $options = [
                    'tablename' => $UserDbController->DbHandler->tableDiaryConfigs,
                    'mainData' => [
                        'name' => $results->items[$i]->nm,
                        'wialon_id' => $results->items[$i]->id,
                        'config_type' => 3,
                    ],
                ];

                $return[] = $UserDbController->addItem($options);
            }
        }

        return $return;
    }

    /**
     * Updates users track_token in su_users.
     *
     * @api-method addWialonToken
     *
     * @param string $token
     */
    public function addWialonToken($token)
    {
        $UsersController = new UsersController('Users');

        $options = [
            'mainData' => [
                'track_token' => $token,
            ],
            'where' => [
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'value' => $this->User->GroupID],
            ],
        ];

        $UsersController->updateUsersData($options);
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options, [], 'Update wialon token');

        $_SESSION['track_token'] = $token;
    }

    /**
     * Login to wialon api.
     *
     * @param null|mixed $token
     *
     * @throws MTRpcException
     *
     * @return stdClass
     */
    public function wialonLogin($token = null)
    {
        if (isset($token)) {
            $_SESSION['track_token'] = $token;
        }

        $UserDbDiaryController = new UserDbDiaryController($this->User->Database);

        $login_results = $UserDbDiaryController->executeWialonRequest('token/login', ['token' => $_SESSION['track_token']]);
        if ($login_results->error && (4 == $login_results->error || 8 == $login_results->error)) {
            throw new MTRpcException('INVALID_OR_EXPIRED_TOKEN', -33354);
        }
        // session id for next requests
        $this->sid = $login_results->eid;
        $_SESSION['wialon_eid'] = $login_results->eid;
        // account ID
        $this->accountID = $login_results->user->bact;
        $_SESSION['wialon_accountID'] = $login_results->user->bact;
        // User ID
        $this->wialonUserID = $login_results->user->id;

        return $login_results;
    }

    /** query geometry coordinates.
     * @param int $farming_id
     * @param int $year
     * @param int $plot_id
     *
     * @throws MTRpcException
     *
     * @return array|mixed|string
     */
    public function generateGeofenceFromZPlot($farming_id, $year, $plot_id)
    {
        $LayersController = new LayersController('Layers');
        $UserDbController = new UserDbController($this->User->Database);

        $dbOptions = [
            'where' => [
                'farming' => ['column' => 'farming', 'compare' => '=', 'prefix' => 't', 'value' => $farming_id],
                'year' => ['column' => 'year', 'compare' => '=', 'prefix' => 't', 'value' => $year],
                'is_exist' => ['column' => 'is_exist', 'compare' => '=', 'prefix' => 't', 'value' => 't'],
                'layer_type' => ['column' => 'layer_type', 'compare' => '=', 'prefix' => 't', 'value' => Config::LAYER_TYPE_ZP],
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'prefix' => 't', 'value' => $this->User->GroupID],
            ],
        ];
        $layer_results = $LayersController->getLayers($dbOptions);
        if (empty($layer_results) || !isset($layer_results[0])) {
            throw new MTRpcException('ZPLOT_NOT_FOUND', -33356);
        }
        $plotData = $UserDbController->getItemsByParams(
            [
                'tablename' => $layer_results[0]['table_name'],
                'return' => ['isak_prc_uin as name', 'area_name', 'ST_AsText(ST_Transform(geom, 4326)) as geom'],
                'where' => ['zplot_id' => ['column' => 'id', 'compare' => '=', 'value' => $plot_id]],
            ]
        );
        if (empty($plotData) || !isset($plotData[0])) {
            throw new MTRpcException('ZPLOT_NOT_FOUND', -33356);
        }
        $geom = $plotData[0]['geom'];
        $coordinates = $this->getCoordsFromString($geom);
        $name = !empty($plotData[0]['name']) ? $plotData[0]['name'] : $plotData[0]['area_name'];
        $name = 'TF_' . $this->User->name . '_' . $name . '_id_' . $plot_id;

        return $this->createGeoFence($name, $coordinates);
    }

    protected function cleanKeys(&$table)
    {
        foreach ($table->header as $i => $key) {
            $table->header[$i] = trim(str_replace(['"', '[', ']'], '', $table->header[$i]));
        }
    }

    protected function change_key(&$allRows, $table, $oldKey, $newKey)
    {
        foreach ($allRows[$table] as &$row) {
            if (!isset($row[$oldKey])) {
                continue;
            }
            $row[$newKey] = $row[$oldKey];
            unset($row[$oldKey]);
        }
    }

    protected function compareDeepValue($a, $b)
    {
        return $a == $b;
    }

    /** We select the rows result from the executed report.
     * @param int $report_id
     *
     * @throws MTRpcException
     *
     * @return array
     */
    protected function parseTables(&$reportRes, $master_grp_id, $geofenceID, $report_id)
    {
        $UserDbDiaryController = new UserDbDiaryController($this->User->Database);

        $allRows = [];
        foreach ($reportRes->tables as $tableIndex => &$table) {
            $table_rows = $UserDbDiaryController->executeWialonRequest(
                'report/select_result_rows',
                [
                    'tableIndex' => $tableIndex,
                    'config' => [
                        'type' => 'range',
                        'data' => [
                            'from' => 0,                // first row index;
                            'to' => $table->rows,       // last row index;
                            'level' => $table->level,   // nesting level;
                            'flat' => 0,                // flat or nested list (bool);
                        ],
                    ],
                    'indexFrom' => 0,
                ],
                $this->sid
            );
            if ($table_rows->error) {
                $this->cleanUp($master_grp_id, $geofenceID, $report_id);

                throw new MTRpcException('DIARY_REPORT_CREATION_ERROR', -33353);
            }
            // clean the keys
            $this->cleanKeys($table);
            $tempRows = [];
            $this->recursiveMap($table, $tempRows, $table_rows, $table->header);
            $allRows[$table->name] = $tempRows;
        }

        return $allRows;
    }

    /**
     * @param array $allRows
     * @param string $table_name
     * @param array $units_arr
     */
    protected function formatData(&$allRows, $table_name, $units_arr)
    {
        $unit_label = null;
        $unit_id = null;
        $date = '';
        foreach ($allRows[$table_name] as $index => &$row) {
            if ('unit' == $row['_grouping']) {
                $unit_label = $row['Grouping'];
                $unit_id = $this->getUnitIdFromName($units_arr, $unit_label);
                $date = 0;
            }
            if ('day' == $row['_grouping']) {
                $date = $row['Grouping'];
            }
            $row['unit_id'] = $unit_id;
            $row['unit_name'] = $unit_label;
            $row['image'] = WIALON_SERVER . '/avl_item_image/' . $unit_id . '/16/null.png';
            $row['_key'] = $unit_id . '_' . $date . '_' . $row['_table'];
            $duration = $row['Duration'];
            $duration = strtotime("1970-01-01 {$duration} UTC");
            if ('unit_group_zones_visit' === $table_name) {
                $parkingTime = $row['Parkings duration'];
                $parkingTime = strtotime("1970-01-01 {$parkingTime} UTC");
                $inMovement = $duration - $parkingTime;
                $row['movement_duration'] = sprintf('%02d:%02d:%02d', $inMovement / 3600, $inMovement / 60 % 60, $inMovement % 60);
            }

            if ('unit_group_trips' === $table_name) {
                $engineHours = $row['Engine hours'];
                $engineHours = strtotime("1970-01-01 {$engineHours} UTC");
                $parkingTime = $duration - $engineHours;
                $row['Parkings duration'] = sprintf('%02d:%02d:%02d', $parkingTime / 3600, $parkingTime / 60 % 60, $parkingTime % 60);
                $row['movement_duration'] = sprintf('%02d:%02d:%02d', $engineHours / 3600, $engineHours / 60 % 60, $engineHours % 60);
            }
            $consumed_by_fls = (float)str_replace(' lt', '', $row['Consumed by FLS']);
            $consumed_by_math = (float)str_replace(' lt', '', $row['Consumed by math']);
            $row['fuel_spent'] = (0 == $consumed_by_fls && $consumed_by_math > 0) ? $consumed_by_math : $consumed_by_fls;
            $row['fuel_spent'] = number_format($row['fuel_spent'], 2, '.', '') . ' lt';
        }
    }

    /** ADD collected UNITS IDs TO MASTER_GROUP.
     */
    protected function updateGroup($master_grp_id, $units_ids)
    {
        $UserDbDiaryController = new UserDbDiaryController($this->User->Database);

        $UserDbDiaryController->executeWialonRequest(
            'unit_group/update_units',
            [
                'itemId' => $master_grp_id,
                'units' => $units_ids,
            ],
            $this->sid
        );
    }

    /**
     * Creates the "TF" template in the use account.
     *
     * @param string $session the Wialon session id
     * @param int $resourceID The Wialon user id
     * @param string $templateName
     *
     * @throws MTRpcException -33353 DIARY_REPORT_CREATION_ERROR
     *
     * @return int the template ID
     */
    protected function createTFTemplate($session, $resourceID, $templateName)
    {
        $UserDbDiaryController = new UserDbDiaryController($this->User->Database);

        $tfReportTmpl = TEMPLATE_PATH . $templateName;
        $reportTemplate = json_decode(file_get_contents($tfReportTmpl), true);
        $report = $reportTemplate['reports'][0];
        $report['callMode'] = 'create';
        $report['itemId'] = $resourceID;
        $result = $UserDbDiaryController->executeWialonRequest('report/update_report', $report, $session);

        if ($result->error) {
            throw new MTRpcException('DIARY_REPORT_CREATION_ERROR', -33353);
        }

        return $result[0];
    }

    /**
     * Deletes a template by ID.
     *
     * @param int $templateID
     * @param string $session
     * @param int $resourceID
     */
    protected function deleteTemplate($templateID, $session, $resourceID)
    {
        $UserDbDiaryController = new UserDbDiaryController($this->User->Database);

        $report = [
            'callMode' => 'delete',
            'itemId' => $resourceID,
            'id' => $templateID,
        ];
        $UserDbDiaryController->executeWialonRequest('report/update_report', $report, $session);
    }

    /** COLLECT UNITS IDs.
     * @return array
     */
    protected function storeUnits($units)
    {
        $units_ids = [];
        $units_arr = [];
        foreach ($units->items as $index => $unit) {
            $units_ids[] = $unit->id;
            $units_arr[$index]['id'] = $unit->id;
            $units_arr[$index]['name'] = $unit->nm;
        }

        return [$units_ids, $units_arr];
    }

    /** update or create template.
     * @param string JSON string of the template
     *
     * @return array|stdClass
     */
    protected function createWialonReport($report)
    {
        $UserDbDiaryController = new UserDbDiaryController($this->User->Database);

        $report['callMode'] = 'create';
        $report['itemId'] = $this->accountID;

        return $UserDbDiaryController->executeWialonRequest('report/update_report', $report, $this->sid);
    }

    /** @param string $template_name
     * @param int $master_grp_id
     * @param int $geofenceID
     *
     * @return string
     */
    protected function updateTemplate($template_name, $master_grp_id, $geofenceID)
    {
        $file = TEMPLATE_PATH . $template_name;
        $reportTemplate = json_decode(file_get_contents($file), true);

        // bind MASTER_GROUP id to the template and update it on the server of wialon,
        $reportTemplate['reports'][0]['p'] = json_decode($reportTemplate['reports'][0]['p'], true);
        $reportTemplate['reports'][0]['p']['bind'] = [$master_grp_id];
        $reportTemplate['reports'][0]['p'] = json_encode($reportTemplate['reports'][0]['p']);

        // update parameter p of the template for table unit_group_zones_visit to query just the selected (and just created ) geofence id
        foreach ($reportTemplate['reports'][0]['tbl'] as $tableIndex => $table) {
            if ('Geofences' == $table['l']) {
                $reportTemplate['reports'][0]['tbl'][$tableIndex]['p'] = json_decode($reportTemplate['reports'][0]['tbl'][$tableIndex]['p'], true);
                $reportTemplate['reports'][0]['tbl'][$tableIndex]['p']['geozones'] = json_encode($geofenceID, true);
                $reportTemplate['reports'][0]['tbl'][$tableIndex]['p'] = json_encode($reportTemplate['reports'][0]['tbl'][$tableIndex]['p'], true);
            }
            /* if ($table['l'] == 'Parkings_in_geofence') {
                  $reportTemplate['reports'][0]['tbl'][$tableIndex]['p'] = json_decode($reportTemplate['reports'][0]['tbl'][$tableIndex]['p'], true);
                  $reportTemplate['reports'][0]['tbl'][$tableIndex]['p']['geozones_ex']['zones'] = json_encode($geofenceID, true);
                  $reportTemplate['reports'][0]['tbl'][$tableIndex]['p'] = json_encode($reportTemplate['reports'][0]['tbl'][$tableIndex]['p'], true);
              }*/
        }
        // return report as json string
        return $reportTemplate['reports'][0];
    }

    /** Execute report.
     * @throws MTRpcException
     */
    protected function exec_report($updateReport, $master_grp_id, $fromTime, $toTime, $geofenceID, $report)
    {
        $UserDbDiaryController = new UserDbDiaryController($this->User->Database);

        $reportResults = $UserDbDiaryController->executeWialonRequest('report/exec_report', [
            'reportResourceId' => (int)$this->accountID,
            'reportTemplateId' => (int)$updateReport[0],
            'reportObjectId' => (int)$master_grp_id, // subject of the report , in this case the unit group id
            'reportObjectSecId' => 0,
            'interval' => [
                'from' => $fromTime,
                'to' => $toTime,
                'flags' => 0,
            ],
        ], $this->sid);
        if ($reportResults->error) {
            $this->cleanUp($master_grp_id, $geofenceID, $report);

            throw new MTRpcException('DIARY_REPORT_CREATION_ERROR', -33353);
        }

        return $reportResults->reportResult;
    }

    /**
     * @return bool
     */
    protected function isTableGeofencePresent($reportResults)
    {
        foreach ($reportResults->tables as $table) {
            if ('unit_group_zones_visit' == $table->name) {
                return true;
            }
        }

        return false;
    }

    private function recursiveMap(&$table, &$allRows, &$table_rows, &$keys, $level = 1)
    {
        foreach ($table_rows as $index => &$table_row) {
            if (isset($table_row->c)) {
                $table_row->c = array_combine($keys, $table_row->c);
                $table_row->c['_table'] = $table->label;
                $table_row->c['_grouping'] = (1 == $level) ? 'unit' : 'day';
                $allRows[] = $table_row->c;
            }
            if (isset($table_row->r)) {
                $this->recursiveMap($table, $allRows, $table_row->r, $keys, 2);
            }
        }
    }

    /**
     * @return array
     */
    private function getCoordsFromString($geom)
    {
        if (false === strpos($geom, '((') && false === strpos($geom, '))')) {
            return [];
        }
        preg_match_all('/MULTIPOLYGON\\(\\(\\((?P<coords>[^\\)]+?)\\)/i', $geom, $returned_array);
        $coords_array = explode(',', $returned_array['coords'][0]);
        $count_coords = count($coords_array);
        $coords = [];
        for ($i = 0; $i < $count_coords; $i++) {
            $fragments = explode(' ', $coords_array[$i]);
            $coords[] = [
                'x' => $fragments[0],     // longitude
                'y' => $fragments[1],     // latitude
                'r' => 0,                  // radius
            ];
        }

        return $coords;
    }

    /**
     * @param string $timestamp
     *
     * @return bool
     */
    private function isTimestamp($timestamp)
    {
        return ctype_digit($timestamp) && strtotime(date('Y-m-d H:i:s', $timestamp)) === (int)$timestamp;
    }

    /**
     * @param string $name
     * @param array $units
     *
     * @return bool|int
     */
    private function getUnitIdFromName($units, $name)
    {
        foreach ($units as $index => $unit) {
            if ($unit['name'] == $name) {
                return $unit['id'];
            }
        }

        return false;
    }

    /**
     * Utility function to delete create objects on Wialon side in case of unexpected results.
     *
     * @param int $report_id
     */
    private function cleanUp($groupID, $geofenceID, $report_id = null)
    {
        $UserDbDiaryController = new UserDbDiaryController($this->User->Database);

        // delete group
        $UserDbDiaryController->executeWialonRequest('item/delete_item', ['itemId' => $groupID], $this->sid);
        // delete geofence
        $this->deleteGeofence($geofenceID);
        // delete report
        if ($report_id) {
            $this->deleteReport($report_id);
        }
    }

    private function deleteGeofence($geofenceID)
    {
        $UserDbDiaryController = new UserDbDiaryController($this->User->Database);

        $UserDbDiaryController->executeWialonRequest('resource/update_zone', ['itemId' => $this->accountID, 'id' => $geofenceID, 'callMode' => 'delete'], $this->sid);
    }

    /**
     * @param int $report_id
     *
     * @return array|stdClass
     */
    private function deleteReport($report_id)
    {
        $UserDbDiaryController = new UserDbDiaryController($this->User->Database);

        $options = [];
        $options['callMode'] = 'delete';
        $options['itemId'] = $this->accountID;
        $options['id'] = $report_id;

        return $UserDbDiaryController->executeWialonRequest('report/update_report', $options, $this->sid);
    }

    /** CREATE A GROUP OF UNITS (vehicle) CALLED MASTER_GROUP , to be used as a subject of this report.
     * @param string $name
     *
     * @return stdClass
     *                  {
     *                  nm string (name) "MASTER_GROUP"
     *                  cls integer (class)
     *                  id integer
     *                  mu integer
     *                  u array (units array)
     *                  uacl
     *                  }
     */
    private function createGroup($name = 'MASTER_GROUP')
    {
        $UserDbDiaryController = new UserDbDiaryController($this->User->Database);

        return $UserDbDiaryController->executeWialonRequest('core/create_unit_group', ['creatorId' => $this->wialonUserID, 'name' => $name, 'dataFlags' => 1], $this->sid);
    }

    /**
     * @param string $name
     * @param array $coordinates
     *
     * @throws MTRpcException -33357 CANNOT_CREATE_GEOFENCE
     *
     * @return int
     */
    private function createGeoFence($name, $coordinates)
    {
        $UserDbDiaryController = new UserDbDiaryController($this->User->Database);

        $name = substr($name, 0, 65);
        // add geofence
        $wialon_params = [
            'id' => 0,          // id of the geofence , zero to create
            'n' => $name,       // name
            'd' => '',          // description
            't' => 2,           // type: 1 - line, 2 - polygon, 3 - circle
            'w' => 0,           // line thickness or circle radius
            'f' => 0,           // geofence flags ( 0x20 – show shape )
            'c' => **********,  // color (ARGB)
            'p' => $coordinates,     // array of geofence points  "x":<double>, longitude ,"y":<double>,	/* latitude
            'itemId' => $_SESSION['wialon_accountID'] ?? $this->accountID, // account ID
            'callMode' => 'create',
        ];
        $geofence = $UserDbDiaryController->executeWialonRequest('resource/update_zone', $wialon_params, $_SESSION['wialon_eid'] ?? $this->sid);
        if (isset($geofence->error)) {
            throw new MTRpcException('CANNOT CREATE GEOFENCE', -33357, $geofence);
        }

        return $geofence[0];
    }

    /** GET ALL VEHICLE (UNITS) IDs.
     * @return stdClass with property items (array of units)
     */
    private function getUnits()
    {
        $UserDbDiaryController = new UserDbDiaryController($this->User->Database);

        return $UserDbDiaryController->executeWialonRequest('core/search_items', [
            'spec' => [
                'itemsType' => 'avl_unit',
                'propName' => 'sys_id',
                'propValueMask' => '*',
                'sortType' => 'sys_id',
            ],
            'force' => 1, 'from' => 0, 'to' => 0, 'flags' => 0x03], $this->sid);
    }
}
