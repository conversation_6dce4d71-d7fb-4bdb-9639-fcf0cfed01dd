<?php

namespace TF\Engine\APIClasses\Diary;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbDiary\UserDbDiaryController;

/**
 * Grid 'Дневник за горива'.
 *
 * @rpc-module Diary
 *
 * @rpc-service-id fuel-diary-grid
 */
class FuelDiaryGrid extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getFuelDiaryGrid'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ], ],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer farming
     *                         #item integer year
     *                         }
     * @param int $page pagination parameter
     * @param int $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array
     */
    public function getFuelDiaryGrid(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        if ($this->User->isGuest || !$this->User->Database) {
            return [
                'rows' => [],
                'total' => 0,
            ];
        }

        if (!$rpcParams['farming'] || !(int) $rpcParams['farming'] || !$rpcParams['year'] || !(int) $rpcParams['year']) {
            return [
                'rows' => [],
                'total' => 0,
            ];
        }

        // init controllers
        $LayersController = new LayersController('Layers');
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbDiaryController = new UserDbDiaryController($this->User->Database);

        $calendar_year_from = $GLOBALS['Farming']['years'][$rpcParams['year']]['year'] . '-01-01 00:00:00';
        $calendar_year_to = $GLOBALS['Farming']['years'][$rpcParams['year']]['year'] . '-12-31 00:00:00';

        $options = [
            'where' => [
                'farming' => ['column' => 'farming', 'compare' => '=', 'prefix' => 't', 'value' => $rpcParams['farming']],
                'year' => ['column' => 'year', 'compare' => 'IN', 'prefix' => 't', 'value' => [$rpcParams['year'], $rpcParams['year'] + 1]],
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'prefix' => 't', 'value' => $this->User->GroupID],
                'layer_type' => ['column' => 'layer_type', 'compare' => '=', 'prefix' => 't', 'value' => 1],
            ],
        ];

        $layer_results = $LayersController->getLayers($options);
        $layersCount = count($layer_results);

        if (0 == count($layer_results)) {
            return [
                'rows' => [],
                'total' => 0,
            ];
        }

        for ($i = 0; $i < $layersCount; $i++) {
            $layer_tables[] = $layer_results[$i]['table_name'];
            $layer_year[$layer_results[$i]['table_name']] = $layer_results[$i]['year'];
        }

        // check if layer tables exist
        $layer_tables_options = [
            'tablename' => 'pg_class',
            'return' => ['relname AS table_name'],
            'where' => [
                'relname' => ['column' => 'relname', 'compare' => 'IN', 'value' => $layer_tables],
            ],
        ];
        $existing_layer_tables = $UserDbController->getItemsByParams($layer_tables_options, false, false);
        $existingLayersCount = count($existing_layer_tables);
        if (0 == count($existing_layer_tables)) {
            return [
                'rows' => [],
                'total' => 0,
            ];
        }

        $results = [];
        for ($i = 0; $i < $existingLayersCount; $i++) {
            $options = [
                'zp_tablename' => $existing_layer_tables[$i]['table_name'],
                'return' => [
                    'zp.culture', 'e.type_id', 'e.subtype_id',
                    'SUM(total_fuel_cost) AS total_fuel_cost',
                    'SUM(completed_area) AS completed_area',
                    "string_agg(CAST(EXTRACT(MONTH FROM complete_date_to) AS TEXT), ',') as work_months",
                    "string_agg(CAST(machine_id AS TEXT), ',') as used_machines",
                    "string_agg(CAST((CASE WHEN is_rented = TRUE THEN machine_id ELSE NULL END) AS TEXT), ',') as rented_machines",
                ],
                'where' => [
                    'farming' => ['column' => 'farming_id', 'compare' => '=', 'prefix' => 'e', 'value' => $rpcParams['farming']],
                    'year' => ['column' => 'year_id', 'compare' => '=', 'prefix' => 'e', 'value' => $layer_year[$existing_layer_tables[$i]['table_name']]],
                    'phase_id' => ['column' => 'phase_id', 'compare' => '=', 'prefix' => 'e', 'value' => 2],
                    'calendar_year_from' => ['column' => 'complete_date_from', 'compare' => '>=', 'prefix' => 'e', 'value' => $calendar_year_from],
                    'calendar_year_to' => ['column' => 'complete_date_from', 'compare' => '<=', 'prefix' => 'e', 'value' => $calendar_year_to],
                ],
                'group' => 'culture, e.type_id, e.subtype_id',
                'sort' => 'culture asc, e.type_id asc, e.subtype_id',
                'order' => 'asc',
                'offset' => ($page - 1) * $rows,
                'limit' => $rows,
            ];

            $results = array_merge($results, $UserDbDiaryController->getDiaryReport($options, false, false));
        }
        $resultsCount = count($results);
        $merged_results = [];
        for ($i = 0; $i < $resultsCount; $i++) {
            $key = $results[$i]['culture'] . '-' . $results[$i]['type_id'] . '-' . $results[$i]['subtype_id'];
            $merged_results[$key]['culture'] = $results[$i]['culture'];
            $merged_results[$key]['type_id'] = $results[$i]['type_id'];
            $merged_results[$key]['subtype_id'] = $results[$i]['subtype_id'];
            $merged_results[$key]['total_fuel_cost'] += (float) $results[$i]['total_fuel_cost'];
            $merged_results[$key]['completed_area'] += (float) $results[$i]['completed_area'];
            $merged_results[$key]['work_months'] .= ',' . $results[$i]['work_months'];
            $merged_results[$key]['used_machines'] .= ',' . $results[$i]['used_machines'];
            $merged_results[$key]['rented_machines'] .= ',' . $results[$i]['rented_machines'];
        }

        $results = array_values($merged_results);
        $resultsCount = count($results);
        // GET CONFIGS FOR GRID INFO
        $options = [
            'tablename' => $UserDbController->DbHandler->tableDiaryConfigs,
            'where' => [
                'config_type' => ['column' => 'config_type', 'compare' => 'IN', 'value' => [1, 2, 4]],
            ],
        ];
        $configs_results = $UserDbController->getItemsByParams($options);
        $configsCount = count($configs_results);
        $configs_array = [];

        if ($configsCount > 0) {
            for ($i = 0; $i < $configsCount; $i++) {
                $configs_array[$configs_results[$i]['id']] = $configs_results[$i];
            }
        }
        // END OF GET CONFIGS

        $return = [];

        for ($i = 0; $i < $resultsCount; $i++) {
            // format used machines
            $exploded_used_machines = explode(',', trim($results[$i]['used_machines'], ','));
            $used_machines = array_unique($exploded_used_machines);
            foreach ($used_machines as $key => $machine) {
                $used_machines[$key] = $configs_array[$machine]['number'];
            }
            $return[$i]['machines'] = implode(',<br/>', $used_machines);

            // format rented machines
            $exploded_rented_machines = explode(',', trim($results[$i]['rented_machines'], ','));
            $rented_machines = array_unique($exploded_rented_machines);
            foreach ($rented_machines as $key => $machine) {
                $rented_machines[$key] = $configs_array[$machine]['number'];
            }
            $return[$i]['rented_machines'] = implode(',<br/>', $rented_machines);

            // format work months
            $exploded_work_months = explode(',', trim($results[$i]['work_months'], ','));
            $work_months = array_unique($exploded_work_months);
            foreach ($work_months as $key => $month) {
                $work_months[$key] = $LayersController->StringHelper->getMonthName((int) $month);
            }
            $return[$i]['work_months'] = implode(',<br/>', $work_months);

            // format culture
            $return[$i]['culture'] = $GLOBALS['Farming']['crops'][$results[$i]['culture']]['crop_name'];

            // format event type
            $return[$i]['event_type'] = $configs_array[$results[$i]['type_id']]['name'];
            $return[$i]['event_type'] .= ' -<br/>' . $configs_array[$results[$i]['subtype_id']]['name'];

            // format completed area
            $return[$i]['completed_area'] = round($results[$i]['completed_area'], 2);

            // format fuel cost
            $return[$i]['fuel_cost'] = round($results[$i]['total_fuel_cost'], 2);

            // format completed work
            if (0 == $return[$i]['completed_area']) {
                $return[$i]['completed_work'] = 0;
            } else {
                $return[$i]['completed_work'] = round($return[$i]['fuel_cost'] / $return[$i]['completed_area'], 2);
            }
        }

        return [
            'rows' => $return,
            'total' => $resultsCount,
        ];
    }
}
