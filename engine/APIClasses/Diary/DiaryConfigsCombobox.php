<?php

namespace TF\Engine\APIClasses\Diary;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbDiary\UserDbDiaryController;

/**
 * Combobox 'Вид машина' / 'Вид обработка' / 'Вид прикачен инвентар' / 'Механизатор'.
 *
 * @rpc-module Diary
 *
 * @rpc-service-id diary-configs-combobox
 */
class DiaryConfigsCombobox extends TRpcApiProvider
{
    // config_types
    public const ACTIVITY_TYPES = 1;
    public const ACTIVITY_SUB_TYPES = 2;
    public const MACHINES_GROUPS = 3;
    public const MACHINES = 4;
    public const MACHINE_EXTENSION_TYPES = 5;
    public const ATTACHMENTS = 6;
    public const PLANT_PROTECTION_PRODUCTS = 7;
    public const TECHNIQUES_APPLICATION = 8;
    public const PERFORMERS = 9;
    public const UNITS = 10;

    public const WIALON_MACHINES_WITH_GRP = 34;

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getDiaryConfigsCombobox']],
            'getAll' => ['method' => [$this, 'getAllProductsConfigsForGrid']],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer request_type
     *                         #item boolean record_none
     *                         #item boolean selected
     *                         }
     *
     * @return array
     */
    public function getDiaryConfigsCombobox($rpcParams)
    {
        // check if request_type isset
        if (!$rpcParams['request_type'] || !(int) $rpcParams['request_type']) {
            return [];
        }

        // init all needed controllers
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbDiaryController = new UserDbDiaryController($this->User->Database);

        // options for configs query
        $options = [
            'tablename' => $UserDbController->DbHandler->tableDiaryConfigs,
            'where' => [
                'request_type' => ['column' => 'config_type', 'compare' => '=', 'value' => $rpcParams['request_type']],
            ],
        ];

        // clear old values
        $return = [];

        if ($rpcParams['record_none'] && 'true' == $rpcParams['record_none']) {
            $return[0] = [
                'id' => '',
                'name' => '-',
            ];
        }

        switch ($rpcParams['request_type']) {
            case self::ACTIVITY_SUB_TYPES:
                if (!$rpcParams['request_type'] || !(int)$rpcParams['request_type']) {
                    return [];
                }
                $operator = is_array($rpcParams['event_type']) ? 'IN' : '=';
                $options['where']['type_id'] = ['column' => 'type_id', 'compare' => $operator, 'value' => $rpcParams['event_type']];

                $results = $UserDbController->getItemsByParams($options);
                $return = array_merge($return, array_map(function ($result) {
                    return [
                        'id' => $result['id'],
                        'name' => $result['name'],
                    ];
                }, $results));

                break;

            case self::MACHINES:
                $options['sort'] = 'type_id';
                $options['order'] = 'asc';

                $results = $UserDbController->getItemsByParams($options);
                $resultsCount = count($results);
                if (0 == $resultsCount) {
                    return [];
                }
                $options['where']['request_type']['value'] = self::MACHINES_GROUPS;
                // get machine types
                $m_types_results = $UserDbController->getItemsByParams($options);
                $m_types = array_column($m_types_results, 'name', 'id');
                $return = array_merge($return, array_map(function ($result) use ($m_types) {
                    return [
                        'id' => $result['id'],
                        'name' => $result['number'],
                        'group' => ('' != $m_types[$result['type_id']]) ? '<b>' . $m_types[$result['type_id']] . '</b>' : '<b>Незададен тип</b>',
                        'wialon_id' => $result['wialon_id'],
                    ];
                }, $results));

                break;

            case self::ATTACHMENTS:
                // change options for full config data function
                // function joins config table with itself and prefix is needed
                $options['where']['request_type']['prefix'] = 'dc1';
                $options['return'] = [
                    'dc1.*', 'dc2.name as attachment_type',
                ];

                $results = $UserDbDiaryController->getFullDiaryConfigData($options);
                $return = array_merge($return, array_map(function ($result) {
                    return [
                        'id' => $result['id'],
                        'name' => $result['attachment_type'] . ' - ' . $result['number'],
                    ];
                }, $results));

                break;
            case self::PERFORMERS:
                $options['sort'] = 'perf_title';
                $options['order'] = 'asc';
                $results = $UserDbController->getItemsByParams($options);

                $return = array_merge($return, array_map(function ($result) {
                    return [
                        'id' => $result['id'],
                        'name' => $result['name'],
                        'group' => ('' != $result['perf_title']) ? '<b>' . $result['perf_title'] . '</b>' : '<b>Незададена длъжност</b>',
                    ];
                }, $results));

                break;
            case self::UNITS:
                $options['where']['request_type']['value'] = self::UNITS;
                $results = $UserDbController->getItemsByParams($options);
                $return = array_merge($return, array_map(function ($result) {
                    return [
                        'id' => $result['id'],
                        'name' => $result['name'],
                    ];
                }, $results));

                break;
            case self::WIALON_MACHINES_WITH_GRP:
                // added to make another request to same endpoint
                $options['sort'] = 'number';
                $options['order'] = 'asc';
                $options['where']['request_type']['value'] = self::MACHINES;
                $results = $UserDbController->getItemsByParams($options);
                $return = array_merge($return, array_map(function ($result) {
                    return [
                        'id' => $result['id'],
                        'name' => $result['number'],
                        'group' => 'Всички машини',
                        'wialon_id' => $result['wialon_id'],
                    ];
                }, $results));

                break;
            case self::PLANT_PROTECTION_PRODUCTS:
                $options['sort'] = 'name';
                $options['order'] = 'asc';
                $options['where']['product_status'] = ['column' => "options->>'status'", 'compare' => '=', 'value' => 'active'];
                if (!empty($rpcParams['product_type'])) {
                    $options['where']['product_type'] = ['column' => "options->>'product_type'", 'compare' => 'IN', 'value' => $rpcParams['product_type']];
                }
                $results = $UserDbController->getItemsByParams($options);
                $return = array_merge($return, array_map(function ($result) {
                    return [
                        'id' => $result['id'],
                        'name' => $result['name'],
                        'options' => $result['options'],
                        'warehouse_item_id' => $result['warehouse_item_id'],
                    ];
                }, $results));

                break;
            default:
                $results = $UserDbController->getItemsByParams($options);
                $return = array_merge($return, array_map(function ($result) {
                    return [
                        'id' => $result['id'],
                        'name' => $result['name'],
                    ];
                }, $results));

                break;
        }

        if ($rpcParams['selected'] && 'true' == $rpcParams['selected']) {
            $return[0]['selected'] = true;
        }

        return $return;
    }

    /**
     * @return array
     */
    public function getAllProductsConfigsForGrid()
    {
        // init all needed controllers
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbDiaryController = new UserDbDiaryController($this->User->Database);
        $return = $UserDbController->getItemsByParams(
            ['tablename' => $UserDbController->DbHandler->tableDiaryConfigs,
                'sort' => 'name',
                'order' => 'asc',
                'where' => [
                    'request_type' => ['column' => 'config_type', 'compare' => 'IN', 'value' => [7, 8, 9, 10],
                    ]],
            ]
        );
        if (0 == count($return)) {
            return [];
        }

        return $return;
    }
}
