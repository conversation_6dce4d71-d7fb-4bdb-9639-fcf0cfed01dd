<?php

namespace TF\Engine\APIClasses\Diary;

use Prado\Web\Services\TRpcApiProvider;
use TF\Application\Common\Config;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbDiary\UserDbDiaryController;

/**
 * Grid 'Употребени минерални и органични торове'.
 *
 * @rpc-module Diary
 *
 * @rpc-service-id fertilizers-diary-grid
 */
class FertilizersDiaryGrid extends TRpcApiProvider
{
    public const PLANT_PROTECTION_PRODUCTS = 7;
    public const UNITS = 10;
    public const EVENT_COMPLETE = 2;
    private $return = ['rows' => [], 'total' => 0];

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getFertilizersDiaryGrid'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer farming
     *                         #item integer year
     *                         }
     * @param int|string $page pagination parameter
     * @param int|string $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array
     */
    public function getFertilizersDiaryGrid(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        if ($this->User->isGuest || !$this->User->Database) {
            return $this->return;
        }
        if (!$rpcParams['farming'] || !(int) $rpcParams['farming'] || !$rpcParams['year'] || !(int) $rpcParams['year']) {
            return $this->return;
        }
        // init controllers
        $LayersController = new LayersController('Layers');
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbDiaryController = new UserDbDiaryController($this->User->Database);

        $options = [
            'where' => [
                'farming' => ['column' => 'farming', 'compare' => '=', 'prefix' => 't', 'value' => $rpcParams['farming']],
                'year' => ['column' => 'year', 'compare' => '=', 'prefix' => 't', 'value' => $rpcParams['year']],
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'prefix' => 't', 'value' => $this->User->GroupID],
                'layer_type' => ['column' => 'layer_type', 'compare' => '=', 'prefix' => 't', 'value' => Config::LAYER_TYPE_ZP],
            ],
        ];

        $layer_results = $LayersController->getLayers($options);

        if (0 == count($layer_results)) {
            return $this->return;
        }
        $tableExists = $UserDbController->getTableNameExist($layer_results[0]['table_name']);

        if ($tableExists <= 0) {
            return $this->return;
        }
        $chemical_subtype_results = $this->getFertilizersProducts();
        $chemicalCount = count($chemical_subtype_results);
        if (0 == $chemicalCount) {
            return $this->return;
        }
        $options = [
            'zp_tablename' => $layer_results[0]['table_name'],
            'return' => [
                'zp.id as zp_id',
                'zp.ekatte',
                'zp.culture',
                'round((st_area(zp.geom)/1000)::numeric, 3) as area',
                'zp.isak_prc_uin',
                'zp.area_name',
                'e.type_id', 'e.subtype_id',
                'e.complete_date_from', 'e.complete_date_to',
                'prd.*',
            ],
            'where' => [
                'farming' => ['column' => 'farming_id', 'compare' => '=', 'prefix' => 'e', 'value' => $rpcParams['farming']],
                'year' => ['column' => 'year_id', 'compare' => '=', 'prefix' => 'e', 'value' => $rpcParams['year']],
                'is_fertilizer_treatment' => ['column' => 'options::jsonb', 'prefix' => 'dc',  'compare' => '@>', 'value' => '{"product_type":"fertilizer"}'],
                'substance_id' => ['column' => 'substance_id', 'compare' => 'IN', 'prefix' => 'prd', 'value' => $chemical_subtype_results],
                'phase_id' => ['column' => 'phase_id', 'compare' => '=', 'prefix' => 'e', 'value' => self::EVENT_COMPLETE],
            ],
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
        ];

        $counter = $UserDbDiaryController->getDiaryReport($options, true, false);
        if (0 == $counter[0]['count']) {
            return $this->return;
        }

        $results = $UserDbDiaryController->getDiaryReport($options, false, false);
        $resultsCount = count($results);
        // GET CONFIGS FOR GRID INFO
        $configs_array = $this->getConfigs($UserDbController, [
            self::PLANT_PROTECTION_PRODUCTS, self::UNITS,
        ]);
        // END OF GET CONFIGS

        $return = [];

        foreach ($results as $i => $result) {
            $return[$i]['zp_id'] = $result['zp_id'];
            $return[$i]['area'] = $result['area'];
            $return[$i]['ekatte'] = $result['ekatte'];
            $return[$i]['culture'] = $result['culture'] ? $result['culture'] : '000000';
            $return[$i]['area_name'] = trim($result['area_name']);
            $return[$i]['isak'] = $result['area_name'] . ' (' . $result['isak_prc_uin'] . ')';
            $return[$i]['isak_prc_uin'] = $result['isak_prc_uin'];
            $return[$i]['culture'] = $GLOBALS['Farming']['crops'][$result['culture']]['crop_name'];
            $return[$i]['date_from'] = strftime('%d.%m.%Y', strtotime($result['complete_date_from']));
            $return[$i]['date_to'] = strftime('%d.%m.%Y', strtotime($result['complete_date_to']));
            $return[$i]['substance_name'] = $configs_array[$result['substance_id']]['name'];
            $return[$i]['treated_area'] = $result['treated_area'];
            $unit_type = $configs_array[$result['substance_unit_type']]['name'];
            $unit_type = explode('/', $unit_type);
            $return[$i]['unit_type'] = $unit_type[0];
            $return[$i]['used_material_qty'] = $result['substance_consumed'];
            $return[$i]['used_material_avg'] = round($result['substance_consumed'] / $result['treated_area'], 3) . ' ' . $return[$i]['unit_type'] . '/дка';
        }

        return [
            'rows' => $return,
            'total' => $counter[0]['count'],
        ];
    }

    /**
     * @return array
     */
    protected function getFertilizersProducts()
    {
        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableDiaryConfigs,
            'where' => [
                'options' => ['column' => "options->>'product_type'", 'compare' => '=', 'value' => 'fertilizer'],
                'options_status' => ['column' => "options->>'status'", 'compare' => '=', 'value' => 'active'],
            ],
        ];
        $results = $UserDbController->getItemsByParams($options);
        $return = [];
        foreach ($results as &$item) {
            $return[] = $item['id'];
        }

        return $return;
    }

    /**
     * @param UserDbController $UserDbController
     * @param array $config_types
     *
     * @return array
     */
    private function getConfigs(&$UserDbController, $config_types = [])
    {
        // init controllers
        $UserDbController = new UserDbController($this->User->Database);

        // GET CONFIGS FOR GRID INFO
        $options = [
            'tablename' => $UserDbController->DbHandler->tableDiaryConfigs,
        ];
        if (!empty($config_types)) {
            $options['where'] = [
                'config_type' => ['column' => 'config_type', 'compare' => 'IN', 'value' => $config_types],
            ];
        }
        $configs_results = $UserDbController->getItemsByParams($options);
        $configsCount = count($configs_results);
        $configs_array = [];

        if ($configsCount) {
            for ($i = 0; $i < $configsCount; $i++) {
                $configs_array[$configs_results[$i]['id']] = $configs_results[$i];
            }
        }
        // END OF GET CONFIGS
        return $configs_array;
    }
}
