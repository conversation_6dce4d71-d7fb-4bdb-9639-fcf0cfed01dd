<?php

namespace TF\Engine\APIClasses\Diary;

use DateTime;
use Prado\Web\Services\TRpcApiProvider;
use Prado\Web\Services\TRpcServer;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\UserDbDiary\UserDbDiaryController;

/**
 * @rpc-module Diary
 *
 * @rpc-service-id diary-expenses-grid
 *
 * <AUTHOR> Cherkezov
 */
class DiaryExpensesGrid extends TRpcApiProvider
{
    private $UserDbDiaryController;

    public function __construct(TRpcServer $rpcServer)
    {
        parent::__construct($rpcServer);

        $this->UserDbDiaryController = new UserDbDiaryController($this->User->Database);
    }

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readExpenses'],
                'validators' => [
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'createExpenses' => ['method' => [$this, 'createExpenses'],
                'validators' => [
                    'expensesData' => [
                        'activity_id' => 'validateInteger',
                        'performer_id' => 'validateInteger',
                        'valid_from' => 'validateDate',
                        'valid_to' => 'validateDate',
                        'price' => 'validateNumber',
                    ],
                ]],
            'updateExpenses' => ['method' => [$this, 'updateExpenses'],
                'validators' => [
                    'expensesData' => [
                        'activity_id' => 'validateInteger',
                        'performer_id' => 'validateInteger',
                        'valid_from' => 'validateDate',
                        'valid_to' => 'validateDate',
                        'price' => 'validateNumber',
                    ],
                ]],
            'loadExpenses' => ['method' => [$this, 'loadExpenses']],
            'deleteExpenses' => ['method' => [$this, 'deleteExpenses']],
        ];
    }

    /**
     * Adds an expenses filter.
     *
     * @api-method createExpenses
     *
     * @param array $expensesData {
     *                            #item integer activity_id
     *                            #item integer performer_id
     *                            #item string valid_from
     *                            #item string valid_to
     *                            #item float price
     *                            }
     *
     * @throws MTRpcException
     *
     * @return bool
     */
    public function createExpenses(array $expensesData)
    {
        if ($this->isExpenseExists($expensesData)) {
            throw new MTRpcException('EXPENSE_ALREADY_EXISTS', -33351);
        }
        $options = [
            'tablename' => $this->UserDbDiaryController->DbHandler->tableDiaryExpenses,
            'mainData' => [
                'activity_id' => (int) $expensesData['activity_id'],
                'performer_id' => (int) $expensesData['performer_id'],
                'valid_from' => $expensesData['valid_from'],
                'valid_to' => $expensesData['valid_to'],
                'price' => (float) $expensesData['price'],
            ],
        ];

        return $this->UserDbDiaryController->addItem($options);
    }

    /**
     * Returns all expenses as EasyUI grid rows.
     *
     * @api-method read
     *
     * @param int $page the current page number
     * @param int $rows rows per page
     * @param string $sort column by which data will be sorted
     * @param string $order the sort direction ASC/DESC
     *
     * @return array
     */
    public function readExpenses(int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $options = [
            'return' => ['de.id', 'de.performer_id', 'de.activity_id', 'dca."name" AS activity', 'dcp."name" AS performer', 'de.valid_from::date', 'de.valid_to::date', 'de.price'],
        ];

        $result = $this->UserDbDiaryController->getExpensesData($options);

        return [
            'rows' => $result,
            'total' => count($result),
        ];
    }

    /**
     * Returns an expense data for a given id.
     *
     * @api-method loadExpenses
     *
     * @param int $expenseId the expense id
     *
     * @return array {
     *               #item integer activity
     *               #item integer performer
     *               #item string valid_from
     *               #item string valid_to
     *               #item float price
     *               }
     */
    public function loadExpenses($expenseId)
    {
        $options = [
            'return' => ['dca."id" AS activity', 'dcp."id" AS performer', 'de.valid_from::date', 'de.valid_to::date', 'de.price'],
            'where' => [
                'id' => ['column' => 'de.id', 'compare' => '=', 'value' => $expenseId],
            ],
        ];

        $result = $this->UserDbDiaryController->getExpensesData($options);

        return $result[0];
    }

    /**
     * Updates an expense record.
     *
     * @api-method updateExpenses
     *
     * @param array $expensesData {
     *                            #item integer activity_id
     *                            #item integer performer
     *                            #item string valid_from
     *                            #item string valid_to
     *                            #item float price
     *                            }
     * @param int $expensesId
     *
     * @throws MTRpcException
     */
    public function updateExpenses(array $expensesData, $expensesId)
    {
        $expense = $this->UserDbDiaryController->getItemsByParams([
            'tablename' => $this->UserDbDiaryController->DbHandler->tableDiaryExpenses,
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $expensesId],
            ],
        ]);
        $expense = $expense[0];

        if ($expense['activity_id'] != $expensesData['activity_id']
            && $expense['performer_id'] != $expensesData['performer_id']
            && $expense['valid_from'] != $expensesData['valid_from']
            && $expense['valid_to'] != $expensesData['valid_to']) {
            if ($this->isExpenseExists($expensesData)) {
                throw new MTRpcException('EXPENSE_ALREADY_EXISTS', -33351);
            }
        }

        $options = [
            'tablename' => $this->UserDbDiaryController->DbHandler->tableDiaryExpenses,
            'mainData' => [
                'activity_id' => (int) $expensesData['activity_id'],
                'performer_id' => (int) $expensesData['performer_id'],
                'valid_from' => $expensesData['valid_from'],
                'valid_to' => $expensesData['valid_to'],
                'price' => (float) $expensesData['price'],
            ],
            'where' => ['id' => $expensesId],
        ];

        $this->UserDbDiaryController->editItem($options);
    }

    /**
     * Deletes an expense.
     *
     * @api-method deleteExpenses
     *
     * @param int $expenseId
     */
    public function deleteExpenses($expenseId)
    {
        $this->UserDbDiaryController->deleteItemsByParams([
            'tablename' => $this->UserDbDiaryController->DbHandler->tableDiaryExpenses,
            'id_string' => $expenseId,
        ]);
    }

    /**
     * Checks if an expense with the same activity, performer and period exist.
     *
     * @throws Exception
     *
     * @return bool
     */
    private function isExpenseExists($expensesData)
    {
        $startDate = new DateTime($expensesData['valid_from']);
        $endDate = new DateTime($expensesData['valid_to']);

        $options = [
            'start_date' => $startDate->format('Y-m-d'),
            'end_date' => $endDate->format('Y-m-d'),
            'activity_id' => $expensesData['activity_id'],
            'performer_id' => $expensesData['performer_id'],
        ];

        $results = $this->UserDbDiaryController->getDiaryExpensesForPeriod($options);

        return count($results) ? true : false;
    }
}
