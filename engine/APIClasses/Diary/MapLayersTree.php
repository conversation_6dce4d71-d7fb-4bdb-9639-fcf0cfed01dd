<?php

namespace TF\Engine\APIClasses\Diary;

use Prado\Web\Services\TRpcApiProvider;
use TF\Application\Common\Config;
use TF\Application\Entity\LayerStyles;
use TF\Application\Entity\UserLayers;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

/**
 * @rpc-module Diary
 *
 * @rpc-service-id all-layers-tree
 */
class MapLayersTree extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readMapLayersTree']],
        ];
    }

    /**
     * readMapLayersTree - returns array with map layers.
     *
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item boolean system_only
     *                         #item boolean allowable
     *                         #item boolean vps
     *                         #item boolean showAllLayers
     *                         }
     *
     * @return array {
     *               #item array {
     *               #item integer id
     *               #item string text
     *               #item array attributes {
     *               #item integer level
     *               #item boolean is_system
     *               }
     *               #item array children{
     *               #item array {
     *               #item string id
     *               #item string text
     *               #item string iconCls
     *               #item array attributes{
     *               #item integer level
     *               #item boolean is_system
     *               #item string layer_name
     *               #item string layer_type
     *               #item string name
     *               #item string extent
     *               }
     *               }
     *               }
     *               }
     *               }
     */
    public function readMapLayersTree($rpcParams)
    {
        $showAllLayers = $rpcParams['showAllLayers'] ?? false;

        // init all needed controllers
        $UserDbController = new UserDbController($this->User->Database);
        $LayersController = new LayersController('Layers');
        $FarmingController = new FarmingController('Farming');
        $wmsBaseUrl = WMS_SERVER . '?MAP=' . WMS_MAP_PATH . "{$this->User->GroupID}.map";
        $mapCacheUrl = LOGIN3_WMS_SERVER;
        // get all farmings
        $userFarmings = $FarmingController->getUserFarmings();
        $userFarmingIds = array_keys($userFarmings);

        $options = [
            'return' => [
                'f.name as farming_name',
                'f.*',
                't.*',
            ],
            'where' => [
                'farming_id' => ['column' => 'id', 'compare' => 'IN', 'prefix' => 'f', 'value' => [...$userFarmingIds, null]],
                'group_id_farming' => ['column' => 'group_id', 'compare' => '=', 'prefix' => 't', 'value' => $this->User->GroupID],
                'skip_layer_type' => ['column' => 'layer_type', 'compare' => 'NOT IN', 'prefix' => 't', 'value' => [
                    Config::LAYER_TYPE_LFA,
                    Config::LAYER_TYPE_VPS_GASKI_CHERVENOGUSHI,
                    Config::LAYER_TYPE_VPS_GASKI_ZIMNI,
                    Config::LAYER_TYPE_VPS_LIVADEN_BLATAR,
                    Config::LAYER_TYPE_VPS_PASISHTA,
                    Config::LAYER_TYPE_VPS_ORLI_LESHOYADI,
                ],
                ],
            ],
            'join_on_group_id' => $this->User->GroupID,
            'sort' => 'farming_name asc, farming asc, year asc, t.name, layer_type',
            'order' => 'desc',
        ];

        $results = $LayersController->getLayers($options, false, false);

        $resultsCount = count($results);
        if (0 == $resultsCount) {
            return [];
        }

        $return = [];
        $farmings = [];
        $farmings_keys = [];
        $farming_years = [];
        $farming_years_keys = [];

        $kvsLayerName = UserLayers::getLayerNameById(Config::LAYER_TYPE_KVS_OSZ);

        $kvsLayersRoot = [
            'uuid' => $this->generateHash(1, $kvsLayerName),
            'text' => $kvsLayerName,
            'attributes' => [
                'level' => 1,
                'layer_type' => 5,
                'layer_name' => 'layer_kvs',
                'mvt_layer_name' => 'kvs_mvt',
                'name' => $kvsLayerName,
                'is_system' => true,
                'is_work_layers' => false,
                'label' => $kvsLayerName,
                'wms' => $wmsBaseUrl . '&LAYERS=layer_kvs',
                'is_cached' => false,
                'style' => null,
                'is_cached_external' => false,
                'is_exist' => true,
            ],
            'children' => [],
        ];

        $kvsStyles = [];

        $csdLayerName = UserLayers::getLayerNameById(Config::LAYER_TYPE_CSD);
        $csdLayersRoot = [
            'id' => $this->generateHash(Config::LAYER_TYPE_CSD, $csdLayerName),
            'uuid' => $this->generateHash(Config::LAYER_TYPE_CSD, $csdLayerName),
            'text' => $csdLayerName,
            'attributes' => [
                'level' => 1,
                'layer_type' => Config::LAYER_TYPE_CSD,
                'layer_name' => $csdLayerName,
                'name' => $csdLayerName,
                'is_system' => false,
                'is_work_layers' => false,
                'label' => $csdLayerName,
                'wms' => null,
                'is_cached' => false,
                'style' => null,
                'is_cached_external' => false,
                'is_exist' => true,
            ],
            'children' => [],
        ];

        if (!($rpcParams['system_only'] && true == $rpcParams['system_only'])) {
            for ($i = 0; $i < $resultsCount; $i++) {
                if (Config::LAYER_TYPE_GPS != $results[$i]['layer_type']
                    && Config::LAYER_TYPE_KVS != $results[$i]['layer_type']
                    && Config::LAYER_TYPE_LFA != $results[$i]['layer_type']
                    && Config::LAYER_TYPE_NATURA_2000 != $results[$i]['layer_type']
                    && Config::LAYER_TYPE_PERMANETELY_GREEN_AREAS != $results[$i]['layer_type']
                    && Config::LAYER_TYPE_VPS_GASKI_CHERVENOGUSHI != $results[$i]['layer_type']
                    && Config::LAYER_TYPE_VPS_GASKI_ZIMNI != $results[$i]['layer_type']
                    && Config::LAYER_TYPE_VPS_LIVADEN_BLATAR != $results[$i]['layer_type']
                    && Config::LAYER_TYPE_VPS_PASISHTA != $results[$i]['layer_type']
                    && Config::LAYER_TYPE_VPS_ORLI_LESHOYADI != $results[$i]['layer_type']
                    && Config::LAYER_TYPE_CSD != $results[$i]['layer_type']
                ) {
                    if (null === $results[$i]['farming']) {
                        // there are layers without farming (CSD, work layers, etc.)
                        continue;
                    }

                    $farming = $results[$i]['farming'];
                    $year = $results[$i]['year'];
                    $hash = $this->generateHash($results[$i]['farming'], $year);
                    if (!in_array($farming, $farmings)) {
                        $return[] = [
                            'id' => $results[$i]['farming'],
                            'uuid' => $this->generateHash($results[$i]['farming'], $results[$i]['farming_name']),
                            'text' => $results[$i]['farming_name'],
                            'state' => 'open',
                            'iconCls' => 'tree-folder-tf',
                            'attributes' => [
                                'is_system' => false,
                                'is_farming' => true,
                                'level' => 1,
                                'label' => $results[$i]['farming_name'],
                                'style' => null,
                                'is_exist' => $results[$i]['is_exist'],
                            ],
                            'children' => [],
                        ];

                        $farmings[] = $farming;
                        $farmings_keys[$farming] = count($return) - 1;
                        $farming_years[$farming] = [];
                    }

                    $farming_key = $farmings_keys[$farming];

                    if ($showAllLayers || $results[$i]['is_exist']) {
                        if (!in_array($year, $farming_years[$farming])) {
                            $return[$farming_key]['iconCls'] = ''; // resetting default easyui icon.
                            $return[$farming_key]['children'][] = [
                                'id' => $year,
                                'uuid' => $this->generateHash($year, $GLOBALS['Farming']['years'][$year]['title'], $hash),
                                'text' => $GLOBALS['Farming']['years'][$year]['title'],
                                'state' => ($FarmingController->getFarmingYearIdByDate(date('Y-m-d')) == $GLOBALS['Farming']['years'][$year]['year']) ? 'open' : 'closed',
                                'attributes' => [
                                    'is_system' => false,
                                    'is_year' => true,
                                    'level' => 2,
                                    'label' => $GLOBALS['Farming']['years'][$year]['title'],
                                    'style' => null,
                                    'is_exist' => $results[$i]['is_exist'],
                                ],
                                'children' => [],
                            ];

                            $farming_years[$farming][] = $results[$i]['year'];
                            $farming_years_keys[$farming_key][$year] = count($return[$farming_key]['children']) - 1;
                        }
                    }

                    $year_key = $farming_years_keys[$farming_key][$year];

                    if ($showAllLayers || $results[$i]['is_exist']) {
                        if (isset($rpcParams['exclude_system']) && true == $rpcParams['exclude_system'] && Config::LAYER_TYPE_KMS == $results[$i]['layer_type']) {
                            continue;
                        }
                        $layer = UserLayers::getLayerById($results[$i]['id']);
                        [$style] = $layer->getStyles();

                        if (!$style) {
                            $style = LayerStyles::generateDefaultStyle($layer->layer_type, false);
                            $style->table_name = $layer->table_name;
                            $style->layer_id = $layer->id;
                        }

                        $columnDefinitions = $layer ? $layer->getDefinitions() : [];

                        $return[$farming_key]['children'][$year_key]['children'][] = [
                            'id' => $results[$i]['id'],
                            'uuid' => $this->generateHash($results[$i]['id'], $results[$i]['name']),
                            'text' => "<div style='width:10px;height:10px;background-color:#" . $style->fill_color . ';margin-top:3px;float:left;margin-right:3px;border: 1px solid #' . $style->border_color . "'></div><span class=\"layer-tree-tooltip\">" . $results[$i]['name'] . '</span>',
                            'iconCls' => 'tree-join-h',
                            'attributes' => [
                                'is_system' => false,
                                'layer_name' => $results[$i]['table_name'],
                                'mvt_layer_name' => $results[$i]['table_name'],
                                'layer_type' => $results[$i]['layer_type'],
                                'farming_id' => $farming,
                                'year_id' => $year,
                                'farming' => $results[$i]['farming_name'],
                                'year' => $GLOBALS['Farming']['years'][$year]['title'],
                                'name' => $results[$i]['name'],
                                'extent' => str_replace(' ', ', ', $results[$i]['extent']),
                                'level' => 3,
                                'label' => $results[$i]['name'],
                                'wms' => $wmsBaseUrl . '&LAYERS=' . $results[$i]['table_name'],
                                'is_cached' => false,
                                'style' => $this->makeStyleColorsAndLabelsBackwardCompatible($style),
                                'is_cached_external' => false,
                                'column_definitions' => $columnDefinitions,
                                'is_exist' => $layer->is_exist,
                            ],
                        ];
                    }
                }

                if (Config::LAYER_TYPE_CSD == $results[$i]['layer_type'] && !$showAllLayers) {
                    // Make sure that the CSD layers are not included in the copy layer dropdown
                    $layer = UserLayers::getLayerById($results[$i]['id']);
                    [$style] = $layer->getStyles();

                    if (!$style) {
                        $style = LayerStyles::generateDefaultStyle($layer->layer_type, false);
                        $style->table_name = $layer->table_name;
                        $style->layer_id = $layer->id;
                    }

                    $csdLayersRoot['attributes']['extent'] = str_replace(' ', ', ', $results[$i]['extent']);
                    $csdLayersRoot['children'][] = [
                        'id' => $results[$i]['id'],
                        'parent_id' => $csdLayersRoot['id'],
                        'uuid' => $this->generateHash($results[$i]['id'], $results[$i]['name']),
                        'text' => "<div style='width:10px;height:10px;background-color:#" . $style->fill_color . ';margin-top:3px;float:left;margin-right:3px;border: 1px solid #' . $style->border_color . "'></div><span class=\"layer-tree-tooltip\">" . $results[$i]['name'] . '</span>',
                        'iconCls' => 'tree-join-h',
                        'attributes' => [
                            'level' => 2,
                            'is_system' => true,
                            'layer_name' => $results[$i]['table_name'],
                            'mvt_layer_name' => $results[$i]['table_name'],
                            'layer_type' => $results[$i]['layer_type'],
                            'name' => $results[$i]['name'],
                            'extent' => str_replace(' ', ', ', $results[$i]['extent']),
                            'label' => $results[$i]['name'],
                            'wms' => $wmsBaseUrl . '&LAYERS=' . $results[$i]['table_name'],
                            'is_cached' => false,
                            'style' => $this->makeStyleColorsAndLabelsBackwardCompatible($style),
                            'is_cached_external' => false,
                            'column_definitions' => json_decode($results[$i]['definitions'], true),
                            'is_exist' => true,
                        ],
                    ];
                }

                if (Config::LAYER_TYPE_KVS == $results[$i]['layer_type']) {
                    $kvsLayersRoot['id'] = $results[$i]['id'];
                    $kvsLayersRoot['attributes']['extent'] = str_replace(' ', ', ', $results[$i]['extent']);
                    $kvsLayer = UserLayers::getLayerById($results[$i]['id']);
                    $kvsStyles = $kvsLayer->getStyles();
                    $kvsStyles = array_combine(array_column($kvsStyles, 'layer_id'), $kvsStyles);
                    $kvsColumnDefinitions = $kvsLayer ? $kvsLayer->getDefinitions() : [];
                    $kvsLayersRoot['attributes']['column_definitions'] = $kvsColumnDefinitions;
                }
            }
        }

        $workLayersRoot = [
            'id' => 1,
            'uuid' => $this->generateHash(1, 'Работни слоеве'),
            'text' => 'Работни слоеве',
            'attributes' => [
                'level' => 1,
                'is_system' => false,
                'is_work_layers' => true,
                'label' => 'Работни слоеве',
                'style' => null,
                'is_exist' => true,
            ],
            'children' => [],
        ];

        $workLayers = $LayersController->getWorkLayers($this->User->GroupID);
        $workLayersCount = count($workLayers);

        $workLayersChildren = [];
        for ($i = 0; $i < $workLayersCount; $i++) {
            $layer = UserLayers::getLayerById($workLayers[$i]['id']);
            [$style] = $layer->getStyles();

            if (!$style) {
                $style = LayerStyles::generateDefaultStyle($layer->layer_type, false);
                $style->table_name = $layer->table_name;
                $style->layer_id = $layer->id;
            }
            $columnDefinitions = $layer ? $layer->getDefinitions() : [];

            $workLayersChildren[] = [
                'id' => $workLayers[$i]['id'],
                'uuid' => $this->generateHash($workLayers[$i]['id'], $workLayers[$i]['table_name']),
                'text' => "<div style='width:10px;height:10px;background-color:#" . $style->fill_color . ';margin-top:3px;float:left;margin-right:3px;border: 1px solid #' . $style->border_color . "'></div><span class=\"layer-tree-tooltip\">" . $workLayers[$i]['name'] . '</span>',
                'iconCls' => 'tree-join-h',
                'attributes' => [
                    'level' => 2,
                    'is_system' => true,
                    'layer_name' => $workLayers[$i]['table_name'],
                    'mvt_layer_name' => $workLayers[$i]['table_name'],
                    'layer_type' => $workLayers[$i]['layer_type'],
                    'name' => $workLayers[$i]['name'],
                    'extent' => str_replace(' ', ', ', $workLayers[$i]['extent']),
                    'label' => $workLayers[$i]['name'],
                    'wms' => $wmsBaseUrl . '&LAYERS=' . $workLayers[$i]['table_name'],
                    'style' => $this->makeStyleColorsAndLabelsBackwardCompatible($style),
                    'is_cached' => false,
                    'is_cached_external' => false,
                    'column_definitions' => $columnDefinitions,
                    'is_exist' => $layer->is_exist,
                ],
            ];
        }

        if ($showAllLayers) {
            $layerName = 'Нов работен слой';
            array_unshift(
                $workLayersChildren,
                [
                    'id' => 'new',
                    'uuid' => $this->generateHash('new', $layerName),
                    'iconCls' => 'tree-join-h',
                    'path' => "{$workLayersRoot['path']}/{$layerName}}",
                    'attributes' => [
                        'level' => 2,
                        'is_system' => true,
                        'layer_type' => Config::LAYER_TYPE_WORK_LAYER,
                        'name' => $layerName,
                        'label' => $layerName,
                        'is_cached' => false,
                        'style' => null,
                        'is_cached_external' => false,
                        'is_exist' => false,
                        'column_definitions' => UserLayers::getDefinitionsByType(Config::LAYER_TYPE_WORK_LAYER),
                    ],
                ]
            );
        }

        if ($workLayersCount > 0 || $showAllLayers) {
            $workLayersRoot['children'] = $workLayersChildren;
        }

        $ekattes = $UserDbController->getItemsByParams([
            'return' => ['ekate', 'ekatte_name', 'extent'],
            'tablename' => 'ekate_combobox',
            'sort' => 'ekatte_name',
            'order' => 'asc',
        ]);

        foreach ($ekattes as $ekatte) {
            if (!$ekatte['ekate']) {
                continue;
            }

            $style = $kvsStyles[$kvsLayer->id . '_' . $ekatte['ekate']];

            if (!$style) {
                $style = LayerStyles::generateDefaultStyle(Config::LAYER_TYPE_KVS, false);
                $style->table_name = $kvsLayer->table_name;
                $style->layer_id = $kvsLayer->id . '_' . $ekatte['ekate'];
            }

            $kvsLayersRoot['children'][] = [
                'id' => $ekatte['ekate'],
                'parent_id' => $kvsLayersRoot['id'],
                'uuid' => $this->generateHash($ekatte['ekate'], $ekatte['ekatte_name']),
                'text' => "<div style='width:10px;height:10px;background-color:#" . $style->fill_color . ';margin-top:3px;float:left;margin-right:3px;border: 1px solid #' . $style->border_color . "'></div><span class=\"layer-tree-tooltip\">" . $ekatte['ekatte_name'] . ' (' . $ekatte['ekate'] . ')</span>',
                'iconCls' => 'tree-join-h',
                'attributes' => [
                    'level' => 3,
                    'is_system' => true,
                    'layer_name' => 'layer_kvs',
                    'mvt_layer_name' => 'kvs_mvt',
                    'parent_layer_name' => 'layer_kvs',
                    'layer_type' => 5,
                    'name' => $ekatte['ekatte_name'] . ' (' . $ekatte['ekate'] . ')',
                    'ekatte' => $ekatte['ekate'],
                    'extent' => $ekatte['extent'],
                    'label' => $ekatte['ekatte_name'] . ' (' . $ekatte['ekate'] . ')',
                    'wms' => $wmsBaseUrl . '&LAYERS=layer_kvs&EKATTE=' . $ekatte['ekate'],
                    'is_cached' => false,
                    'style' => $this->makeStyleColorsAndLabelsBackwardCompatible($style),
                    'is_cached_external' => false,
                    'column_definitions' => $kvsColumnDefinitions,
                    'is_exist' => true,
                ],
            ];
        }

        $systemLayersRoot = [
            'id' => 0,
            'uuid' => $this->generateHash(0, 'Системни'),
            'text' => 'Системни',
            'attributes' => [
                'level' => 1,
                'is_system' => true,
                'label' => 'Системни',
                'style' => null,
                'is_exist' => true,
            ],
            'children' => [],
        ];

        if ($this->User->HasCadastreRights) {
            $cadastreLayerStyle = LayerStyles::generateDefaultStyle(null, false);
            $cadastreLayerConfig = $GLOBALS['Layers']['customLayers'][Config::LAYER_TYPE_CADASTRE];
            $cadastreLayer = UserLayers::getLayerById($cadastreLayerConfig['id']);
            $cadastreLayerStyle->fill_color = $cadastreLayerConfig['fill_color'];
            $cadastreLayerStyle->border_color = $cadastreLayerConfig['border_color'];
            $cadastreLayerStyle->table_name = $cadastreLayerConfig['table_name'];

            $systemLayersRoot['children'][] = [
                'id' => $cadastreLayer->id,
                'uuid' => $this->generateHash(Config::LAYER_TYPE_CADASTRE, 'Cadastre'),
                'text' => "<div style='width:10px;height:10px;background-color:transparent;margin-top:3px;float:left;margin-right:3px;border: 1px solid #000'></div><span class=\"layer-tree-tooltip\">Кдастър</span>",
                'iconCls' => 'tree-join-h',
                'attributes' => [
                    'level' => 2,
                    'is_system' => true,
                    'layer_name' => 'layer_cadastre',
                    'mvt_layer_name' => 'layer_cadastre',
                    'is_cached' => true,
                    'is_cached_external' => false,
                    'layer_type' => $cadastreLayer->layer_type,
                    'name' => $cadastreLayer->name,
                    'extent' => str_replace(' ', ', ', Config::DEFAULT_MAX_EXTENT),
                    'label' => $cadastreLayer->name,
                    'wms' => 'http://localhost:5002?proxy=https://mapcache.geoscan.info/bg&MAP=/home/<USER>/qnap_storage/storage_bg/storage/maps/cadastre.map&LAYERS=cadastre',
                    'style' => $this->makeStyleColorsAndLabelsBackwardCompatible($cadastreLayerStyle),
                    'column_definitions' => $cadastreLayer->getDefinitions(),
                    'is_exist' => true,
                ],
            ];

            $orthophotoLayerStyle = LayerStyles::generateDefaultStyle(null, false);
            $orthophotoLayerStyle->fill_color = 'transparent';
            $orthophotoLayerStyle->border_color = '#000000';
            $orthophotoLayerStyle->table_name = 'orthophoto';

            $systemLayersRoot['children'][] = [
                'id' => Config::LAYER_TYPE_ORTHOPHOTO,
                'uuid' => $this->generateHash(Config::LAYER_TYPE_ORTHOPHOTO, 'Orthophoto'),
                'text' => "<div style='width:10px;height:10px;background-color:transparent;margin-top:3px;float:left;margin-right:3px;border: 1px solid #000'></div><span class=\"layer-tree-tooltip\">Ортофото</span>",
                'iconCls' => 'tree-join-h',
                'attributes' => [
                    'level' => 2,
                    'is_system' => true,
                    'layer_name' => 'orthophoto',
                    'mvt_layer_name' => 'orthophoto',
                    'is_cached' => true,
                    'is_cached_external' => true,
                    'layer_type' => Config::LAYER_TYPE_ORTHOPHOTO,
                    'name' => 'Ортофото',
                    'extent' => str_replace(' ', ', ', Config::DEFAULT_MAX_EXTENT),
                    'label' => 'Ортофото',
                    'wms' => 'http://localhost:5002?proxy=https://mapcache.geoscan.info/bg&LAYERS=orthophoto',
                    'style' => $this->makeStyleColorsAndLabelsBackwardCompatible($orthophotoLayerStyle),
                    'column_definitions' => [],
                    'is_exist' => true,
                ],
            ];
        }

        if ($this->User->HasSlopeRights) {
            $slopeLayerStyle = LayerStyles::generateDefaultStyle(null, false);
            $slopeLayerStyle->fill_color = '#1A9640';
            $slopeLayerStyle->border_color = '#CA2311';
            $slopeLayerStyle->table_name = 'slope';

            $systemLayersRoot['children'][] = [
                'id' => Config::LAYER_TYPE_SLOPE,
                'uuid' => $this->generateHash(Config::LAYER_TYPE_SLOPE, 'Slope'),
                'text' => "<div style='width:10px;height:10px;background-color:#1A9640;margin-top:3px;float:left;margin-right:3px;border: 1px solid #CA2311'></div><span class=\"layer-tree-tooltip\">Наклон</span>",
                'iconCls' => 'tree-join-h',
                'attributes' => [
                    'level' => 2,
                    'is_system' => true,
                    'is_cached' => true,
                    'is_cached_external' => true,
                    'layer_name' => 'slope',
                    'mvt_layer_name' => 'slope',
                    'layer_type' => Config::LAYER_TYPE_SLOPE,
                    'name' => 'Наклон',
                    'extent' => str_replace(' ', ', ', Config::DEFAULT_MAX_EXTENT),
                    'label' => 'Наклон',
                    'wms' => 'http://localhost:5002?proxy=https://mapcache.geoscan.info/bg&LAYERS=slope',
                    'style' => $this->makeStyleColorsAndLabelsBackwardCompatible($slopeLayerStyle),
                    'column_definitions' => [],
                    'is_exist' => true,
                ],
            ];

            $terrainLayerStyle = LayerStyles::generateDefaultStyle(null, false);
            $terrainLayerStyle->fill_color = '#D0D2AB';
            $terrainLayerStyle->border_color = '#3F4032';
            $terrainLayerStyle->table_name = 'hillshade_color';

            $systemLayersRoot['children'][] = [
                'id' => Config::LAYER_TYPE_TERRAIN,
                'uuid' => $this->generateHash(Config::LAYER_TYPE_TERRAIN, 'Terrain'),
                'text' => "<div style='width:10px;height:10px;background-color:#D0D2AB;margin-top:3px;float:left;margin-right:3px;border: 1px solid #3F4032'></div><span class=\"layer-tree-tooltip\">Терен</span>",
                'iconCls' => 'tree-join-h',
                'attributes' => [
                    'level' => 2,
                    'is_system' => true,
                    'is_cached' => true,
                    'is_cached_external' => true,
                    'layer_name' => 'hillshade_color',
                    'mvt_layer_name' => 'hillshade_color',
                    'layer_type' => Config::LAYER_TYPE_TERRAIN,
                    'name' => 'Терен',
                    'extent' => str_replace(' ', ', ', Config::DEFAULT_MAX_EXTENT),
                    'label' => 'Терен',
                    'wms' => 'http://localhost:5002?proxy=https://mapcache.geoscan.info/bg&LAYERS=hillshade_color',
                    'style' => $this->makeStyleColorsAndLabelsBackwardCompatible($terrainLayerStyle),
                    'column_definitions' => [],
                    'is_exist' => true,
                ],
            ];
        }
        $vps = [];
        $landscapeElements = [];
        $allowable = [];
        $declared = [];

        for ($i = 0; $i < $resultsCount; $i++) {
            if (Config::LAYER_TYPE_GPS == $results[$i]['layer_type']) {
                $layer = UserLayers::getLayerById($results[$i]['id']);
                [$style] = $layer->getStyles();

                if (!$style) {
                    $style = LayerStyles::generateDefaultStyle($layer->layer_type, false);
                    $style->table_name = $layer->table_name;
                    $style->layer_id = $layer->id;
                }

                $columnDefinitions = $layer ? $layer->getDefinitions() : [];

                $systemLayersRoot['children'][] = [
                    'id' => $results[$i]['id'],
                    'uuid' => $this->generateHash($results[$i]['id'], $results[$i]['name']),
                    'text' => "<div style='width:10px;height:10px;background-color:#" . $style->fill_color . ';margin-top:3px;float:left;margin-right:3px;border: 1px solid #' . $style->border_color . "'></div><span class=\"layer-tree-tooltip\">" . $results[$i]['name'] . '</span>',
                    'iconCls' => 'tree-join-h',
                    'attributes' => [
                        'level' => 2,
                        'is_system' => true,
                        'layer_name' => $results[$i]['table_name'],
                        'mvt_layer_name' => $results[$i]['table_name'],
                        'layer_type' => $results[$i]['layer_type'],
                        'name' => $results[$i]['name'],
                        'extent' => str_replace(' ', ', ', $results[$i]['extent']),
                        'label' => $results[$i]['name'],
                        'wms' => $wmsBaseUrl . '&LAYERS=' . $workLayers[$i]['table_name'],
                        'is_cached' => false,
                        'style' => $this->makeStyleColorsAndLabelsBackwardCompatible($style),
                        'is_cached_external' => false,
                        'column_definitions' => $columnDefinitions,
                        'is_exist' => $layer->is_exist,
                    ],
                ];
            }

            if (Config::LAYER_TYPE_LFA == $results[$i]['layer_type']
            || Config::LAYER_TYPE_NATURA_2000 == $results[$i]['layer_type']
            || Config::LAYER_TYPE_PERMANETELY_GREEN_AREAS == $results[$i]['layer_type']) {
                $layer = UserLayers::getLayerById($results[$i]['id']);
                [$style] = $layer->getStyles();

                if (!$style) {
                    $style = LayerStyles::generateDefaultStyle($layer->layer_type, false);
                    $style->table_name = $layer->table_name;
                    $style->layer_id = $layer->id;
                }

                $columnDefinitions = $layer ? $layer->getDefinitions() : [];

                $systemLayersRoot['children'][] = [
                    'id' => $results[$i]['id'],
                    'uuid' => $this->generateHash($results[$i]['id'], $results[$i]['name']),
                    'text' => "<div style='width:10px;height:10px;background-color:#" . $style->fill_color . ';margin-top:3px;float:left;margin-right:3px;border: 1px solid #' . $style->border_color . "'></div><span class=\"layer-tree-tooltip\">" . $results[$i]['name'] . '</span>',
                    'iconCls' => 'tree-join-h',
                    'attributes' => [
                        'level' => 2,
                        'is_system' => true,
                        'layer_name' => $results[$i]['table_name'],
                        'mvt_layer_name' => $results[$i]['table_name'],
                        'layer_type' => $results[$i]['layer_type'],
                        'name' => $results[$i]['name'],
                        'extent' => str_replace(' ', ', ', $results[$i]['extent']),
                        'label' => $results[$i]['name'],
                        'wms' => "{$mapCacheUrl}?MAP=/var/www/maps/{$results[$i]['table_name']}.map&LAYERS=" . $results[$i]['table_name'],
                        'is_cached' => true,
                        'style' => $this->makeStyleColorsAndLabelsBackwardCompatible($style),
                        'is_cached_external' => false,
                        'column_definitions' => $columnDefinitions,
                        'is_exist' => $layer->is_exist,
                    ],
                ];
            }

            if (in_array($results[$i]['layer_type'], [Config::LAYER_TYPE_LANDSCAPE_ELEMENTS_POINTS,
                Config::LAYER_TYPE_LANDSCAPE_ELEMENTS_LINES, Config::LAYER_TYPE_LANDSCAPE_ELEMENTS_POLYGONS])) {
                $layer = UserLayers::getLayerById($results[$i]['id']);
                [$style] = $layer->getStyles();
                $columnDefinitions = $layer ? $layer->getDefinitions() : [];
                $landscapeElements[] = [
                    'id' => $results[$i]['id'],
                    'uuid' => $this->generateHash($results[$i]['id'], $results[$i]['name']),
                    'text' => "<div style='width:10px;height:10px;background-color:#" . $style->fill_color . ';margin-top:3px;float:left;margin-right:3px;border: 1px solid #' . $style->border_color . "'></div><span class=\"layer-tree-tooltip\">" . $results[$i]['name'] . '</span>',
                    'iconCls' => 'tree-join-h',
                    'attributes' => [
                        'level' => 2,
                        'is_system' => true,
                        'layer_name' => $results[$i]['table_name'],
                        'mvt_layer_name' => $results[$i]['table_name'],
                        'layer_type' => $results[$i]['layer_type'],
                        'name' => $results[$i]['name'],
                        'extent' => str_replace(' ', ', ', $results[$i]['extent']),
                        'label' => $results[$i]['name'],
                        'wms' => "{$mapCacheUrl}?MAP=/var/www/maps/{$results[$i]['table_name']}.map&LAYERS=" . $results[$i]['table_name'],
                        'is_cached' => true,
                        'style' => $this->makeStyleColorsAndLabelsBackwardCompatible($style),
                        'is_cached_external' => false,
                        'column_definitions' => $columnDefinitions,
                        'is_exist' => $layer->is_exist,
                    ],
                ];
            }
            if (in_array($results[$i]['layer_type'], [Config::LAYER_TYPE_DS_PRC])) {
                $layer = UserLayers::getLayerById($results[$i]['id']);
                [$style] = $layer->getStyles();
                $columnDefinitions = $layer ? $layer->getDefinitions() : [];
                $declared[] = [
                    'id' => $results[$i]['id'],
                    'uuid' => $this->generateHash($results[$i]['id'], $results[$i]['name']),
                    'text' => "<div style='width:10px;height:10px;background-color:#" . $style->fill_color . ';margin-top:3px;float:left;margin-right:3px;border: 1px solid #' . $style->border_color . "'></div><span class=\"layer-tree-tooltip\">" . $results[$i]['name'] . '</span>',
                    'iconCls' => 'tree-join-h',
                    'attributes' => [
                        'level' => 2,
                        'is_system' => true,
                        'layer_name' => $results[$i]['table_name'],
                        'mvt_layer_name' => $results[$i]['table_name'],
                        'layer_type' => $results[$i]['layer_type'],
                        'name' => $results[$i]['name'],
                        'extent' => str_replace(' ', ', ', $results[$i]['extent']),
                        'label' => $results[$i]['name'],
                        'wms' => "{$mapCacheUrl}?MAP=/var/www/maps/{$results[$i]['table_name']}.map&LAYERS=" . $results[$i]['table_name'],
                        'is_cached' => true,
                        'style' => $this->makeStyleColorsAndLabelsBackwardCompatible($style),
                        'is_cached_external' => false,
                        'column_definitions' => $columnDefinitions,
                        'is_exist' => $layer->is_exist,
                    ],
                ];
            }

            if (in_array(
                $results[$i]['layer_type'],
                [Config::LAYER_TYPE_PHYSICAL_BLOCKS_PRELIMINARY,
                    Config::LAYER_TYPE_PERMANENT_GRASSLAND_FOR_MOWING_PRELIMINARY,
                    Config::LAYER_TYPE_PERMANENT_GRASSLAND_FOR_MOWING]
            )) {
                $layer = UserLayers::getLayerById($results[$i]['id']);
                [$style] = $layer->getStyles();
                $allowable[] = [
                    'id' => $results[$i]['id'],
                    'uuid' => $this->generateHash($results[$i]['id'], $results[$i]['name']),
                    'text' => "<div style='width:10px;height:10px;background-color:#" . $style->fill_color . ';margin-top:3px;float:left;margin-right:3px;border: 1px solid #' . $style->border_color . "'></div><span class=\"layer-tree-tooltip\">" . $results[$i]['name'] . '</span>',
                    'iconCls' => 'tree-join-h',
                    'attributes' => [
                        'level' => 2,
                        'is_system' => true,
                        'layer_name' => $results[$i]['table_name'],
                        'mvt_layer_name' => $results[$i]['table_name'],
                        'layer_type' => $results[$i]['layer_type'],
                        'name' => $results[$i]['name'],
                        'extent' => str_replace(' ', ', ', $results[$i]['extent']),
                        'label' => $results[$i]['name'],
                        'wms' => "{$mapCacheUrl}?MAP=/var/www/maps/{$results[$i]['table_name']}.map&LAYERS=" . $results[$i]['table_name'],
                        'is_cached' => true,
                        'style' => $this->makeStyleColorsAndLabelsBackwardCompatible($style),
                        'is_cached_external' => false,
                        'column_definitions' => $columnDefinitions,
                        'is_exist' => $layer->is_exist,
                    ],
                ];
            }

            if (Config::LAYER_TYPE_VPS_PASISHTA == $results[$i]['layer_type']
                || Config::LAYER_TYPE_VPS_GASKI_CHERVENOGUSHI == $results[$i]['layer_type']
                || Config::LAYER_TYPE_VPS_GASKI_ZIMNI == $results[$i]['layer_type']
                || Config::LAYER_TYPE_VPS_LIVADEN_BLATAR == $results[$i]['layer_type']
                || Config::LAYER_TYPE_VPS_ORLI_LESHOYADI == $results[$i]['layer_type']
            ) {
                $layer = UserLayers::getLayerById($results[$i]['id']);
                [$style] = $layer->getStyles();
                $columnDefinitions = $layer ? $layer->getDefinitions() : [];

                $vps[] = [
                    'id' => $results[$i]['id'],
                    'uuid' => $this->generateHash($results[$i]['id'], $results[$i]['name']),
                    'text' => "<div style='width:10px;height:10px;background-color:#" . $style->fill_color . ';margin-top:3px;float:left;margin-right:3px;border: 1px solid #' . $style->border_color . "'></div><span class=\"layer-tree-tooltip\">" . $results[$i]['name'] . '</span>',
                    'iconCls' => 'tree-join-h',
                    'attributes' => [
                        'level' => 2,
                        'is_system' => true,
                        'layer_name' => $results[$i]['table_name'],
                        'mvt_layer_name' => $results[$i]['table_name'],
                        'layer_type' => $results[$i]['layer_type'],
                        'name' => $results[$i]['name'],
                        'extent' => str_replace(' ', ', ', $results[$i]['extent']),
                        'label' => $results[$i]['name'],
                        'wms' => "{$mapCacheUrl}?MAP=/var/www/maps/{$results[$i]['table_name']}.map&LAYERS=" . $results[$i]['table_name'],
                        'is_cached' => true,
                        'style' => $this->makeStyleColorsAndLabelsBackwardCompatible($style),
                        'is_cached_external' => false,
                        'column_definitions' => $columnDefinitions,
                        'is_exist' => $layer->is_exist,
                    ],
                ];
            }
        }

        if ($rpcParams['allowable'] && true == $rpcParams['allowable']) {
            $physicalBlocksLayer = $GLOBALS['Layers']['customLayers'][Config::LAYER_TYPE_PHYSICAL_BLOCKS];
            $allowableFinalLayer = $GLOBALS['Layers']['customLayers'][Config::LAYER_TYPE_ALLOWABLE_FINAL];
            $physicalBlocksLayerStyle = LayerStyles::generateDefaultStyle(null, false);
            $physicalBlocksLayerStyle->fill_color = '';
            $physicalBlocksLayerStyle->border_color = $physicalBlocksLayer['border_color'];
            $physicalBlocksLayerStyle->table_name = 'hillshade_color';

            $allowable[] = [
                'id' => 'd',
                'uuid' => $this->generateHash('d', $physicalBlocksLayer['layer_name']),
                'text' => '<div style="width:10px;height:10px;background-color:#fafafa;margin-top:3px;float:left;margin-right:3px;border: 1px solid #84A3E0"></div><span class="layer-tree-tooltip"> ' . $physicalBlocksLayer['layer_name'] . ' </span>',
                'iconCls' => 'tree-join-h',
                'attributes' => [
                    'level' => 3,
                    'is_system' => true,
                    'layer_name' => 'layer_allowable',
                    'mvt_layer_name' => 'layer_allowable_draft',
                    'layer_type' => 'd',
                    'allowable_type' => 'physical_blocks',
                    'name' => $physicalBlocksLayer['layer_name'],
                    'extent' => str_replace(' ', ', ', Config::DEFAULT_MAX_EXTENT),
                    'label' => $physicalBlocksLayer['layer_name'],
                    'wms' => "{$mapCacheUrl}?MAP=/var/www/maps/layer_allowable_draft.map&LAYERS=layer_allowable_draft",
                    'is_cached' => true,
                    'style' => $this->makeStyleColorsAndLabelsBackwardCompatible($physicalBlocksLayerStyle),
                    'is_cached_external' => false,
                    'column_definitions' => UserLayers::getDefinitionsByType(Config::LAYER_TYPE_ALLOWABLE_FINAL),
                    'is_exist' => true,
                ],
            ];
            $allowableFinalLayerStyle = LayerStyles::generateDefaultStyle(null, false);
            $allowableFinalLayerStyle->fill_color = '';
            $allowableFinalLayerStyle->border_color = $allowableFinalLayer['border_color'];
            $allowableFinalLayerStyle->table_name = 'hillshade_color';
            $allowable[] = [
                'id' => 'df',
                'uuid' => $this->generateHash('df', $allowableFinalLayer['layer_name']),
                'text' => '<div style="width:10px;height:10px;background-color:#fafafa;margin-top:3px;float:left;margin-right:3px;border: 1px solid #0000ff"></div><span class="layer-tree-tooltip"> ' . $allowableFinalLayer['layer_name'] . ' </span>',
                'iconCls' => 'tree-join-h',
                'attributes' => [
                    'level' => 3,
                    'is_system' => true,
                    'layer_name' => 'layer_allowable_final',
                    'mvt_layer_name' => 'layer_allowable_final',
                    'layer_type' => 'df',
                    'allowable_type' => 'allowable_layer',
                    'name' => $allowableFinalLayer['layer_name'],
                    'extent' => str_replace(' ', ', ', Config::DEFAULT_MAX_EXTENT),
                    'label' => $allowableFinalLayer['layer_name'],
                    'wms' => "{$mapCacheUrl}?MAP=/var/www/maps/layer_allowable_final.map&LAYERS=layer_allowable_final",
                    'is_cached' => true,
                    'style' => $this->makeStyleColorsAndLabelsBackwardCompatible($allowableFinalLayerStyle),
                    'is_cached_external' => false,
                    'column_definitions' => UserLayers::getDefinitionsByType(Config::LAYER_TYPE_PHYSICAL_BLOCKS),
                    'is_exist' => true,
                ],
            ];

            $systemLayersRoot['children'][] = [
                'id' => 'system_2014',
                'uuid' => $this->generateHash('system_2014', 'Специализиран слой'),
                'text' => 'Специализиран слой',
                'state' => 'open',
                'attributes' => [
                    'is_system' => true,
                    'level' => 2,
                    'label' => 'Специализиран слой',
                    'style' => [
                        'fill_color' => null,
                        'border_color' => null,
                        'type' => LayerStyles::SINGLE_COLORING_TYPE,
                    ],
                    'is_exist' => true,
                ],
                'children' => $allowable,
            ];
        }

        if ($rpcParams['vps'] && true == $rpcParams['vps'] && count($vps) > 0) {
            $systemLayersRoot['children'][] = [
                'id' => 'vps',
                'uuid' => $this->generateHash('vps', 'ВПС'),
                'text' => 'ВПС',
                'state' => 'open',
                'attributes' => [
                    'is_system' => true,
                    'level' => 2,
                    'label' => 'ВПС',
                    'style' => null,
                    'is_exist' => true,
                ],
                'children' => $vps,
            ];
        }
        // $landscapeElements
        $systemLayersRoot['children'][] = [
            'id' => 'vps',
            'uuid' => $this->generateHash('landscape_elements', 'Ландшафтни елементи'),
            'text' => 'Ландшафтни елементи',
            'state' => 'open',
            'attributes' => [
                'is_system' => true,
                'level' => 2,
                'label' => 'Ландшафтни елементи',
                'style' => null,
                'is_exist' => true,
            ],
            'children' => $landscapeElements,
        ];
        $systemLayersRoot['children'][] = [
            'id' => 'vps',
            'uuid' => $this->generateHash('declared_prc', 'Декларирани за подпомагане'),
            'text' => 'Декларирани за подпомагане',
            'state' => 'open',
            'attributes' => [
                'is_system' => true,
                'level' => 2,
                'label' => 'Декларирани за подпомагане',
                'style' => null,
                'is_exist' => true,
            ],
            'children' => $declared,
        ];
        if (isset($rpcParams['exclude_system']) && true == $rpcParams['exclude_system']) {
            $return[] = $workLayersRoot;

            return $return;
        }

        if ($rpcParams['system_only']) {
            return [array_merge($systemLayersRoot, $kvsLayersRoot)];
        }

        $return[] = $workLayersRoot;
        $return[] = $kvsLayersRoot;
        if (!$showAllLayers) {
            // Make sure that the CSD layers are not included in the copy layer dropdown
            $return[] = $csdLayersRoot;
        }
        $return[] = $systemLayersRoot;

        return $return;
    }

    private function generateHash($id, $str, $optionalParam = '')
    {
        return mb_substr(md5($id . $str . $optionalParam), 0, 6);
    }

    /**
     * @param LayerStyles $style
     *                           Removes the '#' from the color values, so that they are compatible with the current mobile app
     *
     * @return array
     */
    private function makeStyleColorsAndLabelsBackwardCompatible(LayerStyles $style)
    {
        $styleArr = $style->toArray();
        $styleArr['color'] = trim($styleArr['fill_color'], '#');
        $styleArr['border_color'] = trim($styleArr['border_color'], '#');
        $styleArr['fill_color'] = trim($styleArr['fill_color'], '#');
        $styleArr['label_name'] = $styleArr['labels'];

        return $styleArr;
    }
}
