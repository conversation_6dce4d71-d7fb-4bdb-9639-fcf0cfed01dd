<?php

namespace TF\Engine\APIClasses\Diary;

use Exception;
use InvalidArgumentException;
use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Kernel\WarehouseModuleClass;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbDiary\UserDbDiaryController;
use TF\Engine\Plugins\Core\Users\UsersController;

// \Prado::using('Plugins.Core.UserDbDiary.conf');

/**
 * Add/edit actions, machines, attachments, performers,
 * chemical treatments.
 *
 * @rpc-module Diary
 *
 * @rpc-service-id diary-auxiliary-items
 */
class DiaryAuxiliaryItems extends TRpcApiProvider
{
    private $module = 'Diary';
    private $service_id = 'diary-auxiliary-items';

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'itemEditMark' => ['method' => [$this, 'itemEditMark']],
            'syncWarehouseItems' => ['method' => [$this, 'syncWarehouseItems']],
            'checkProductAvailability' => ['method' => [$this, 'checkProductAvailability']],
            'saveItem' => [
                'method' => [$this, 'saveItem'],
                'validators' => [
                    'rpcParams' => [
                        'is_chemical_treatment' => 'validateText',
                        'id' => 'validateInteger',
                        'name' => 'validateText',
                        'number' => 'validateText',
                        'type_id' => 'validateInteger',
                        'model' => 'validateText',
                        'manufacturer' => 'validateText',
                        'description' => 'validateText',
                        'request_type' => 'validateText',
                        'perf_title' => 'validateText',
                        'wialon_id' => 'validateInteger',
                    ],
                ]],
        ];
    }

    /**
     * Return selected item information for edit.
     *
     * @api-method itemEditMark
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer id
     *                         }
     *
     * @return array
     */
    public function itemEditMark($rpcParams)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        // check if data is correct
        if (!$rpcParams['id'] || !(int) $rpcParams['id']) {
            return [];
        }

        $options = [
            'tablename' => $UserDbController->DbHandler->tableDiaryConfigs,
            'where' => [
                'config_id' => ['column' => 'id', 'compare' => '=', 'value' => $rpcParams['id']],
            ],
        ];

        $results = $UserDbController->getItemsByParams($options);
        if (0 == count($results)) {
            return [];
        }

        $item = $results[0];
        $item['options'] = json_decode($item['options']);

        return $item;
    }

    /**
     * Save the new/edited item.
     *
     * @api-method saveItem
     *
     * @param array $rpcParams
     *                         {
     *                         #item boolean is_chemical_treatment
     *                         #item integer id
     *                         #item string  name
     *                         #item string  number
     *                         #item integer type_id
     *                         #item string  model
     *                         #item string  manufacturer
     *                         #item string  description
     *                         #item integer request_type
     *                         #item string  perf_title
     *                         #item integer wialon_id
     *                         }
     *
     * @return int
     */
    public function saveItem($rpcParams)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $oldOptions = [
            'tablename' => $UserDbController->DbHandler->tableDiaryConfigs,
            'return' => [
                'id', 'name', 'number', 'type_id', 'model', 'manufacturer', 'description',
                'config_type', 'perf_title', 'wialon_id', 'options', 'warehouse_item_id',
            ],
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $rpcParams['id']],
            ],
        ];
        $oldValues = $UserDbController->getItemsByParams($oldOptions, false, false);

        $optionsParams = [];
        $item = null;
        if (CONFIG_TYPE_PRODUCTS === $rpcParams['request_type']) { // Config Type 7 - products
            if (empty($rpcParams['product_type']) && empty($rpcParams['warehouse_item_id'])) {
                throw new InvalidArgumentException('Missing required parameter');
            }
            $optionsNewArray = [
                'status' => 'active',
                'product_type' => $rpcParams['product_type'],
            ];
            $optionsParams['new'] = json_encode($optionsNewArray);

            if (!empty($oldValues[0])) {
                $optionsEditArray = json_decode($oldValues[0]['options'], true);
                $optionsEditArray['status'] = 'active';
                $optionsEditArray['product_type'] = $rpcParams['product_type'];

                $optionsParams['edit'] = json_encode($optionsEditArray);
            }
        } elseif (CONFIG_TYPE_SUB_TYPES === $rpcParams['request_type']) { // Config Type 2 - Sub Types
            if (!isset($rpcParams['has_produce'])) {
                throw new InvalidArgumentException('Missing required parameter');
            }
            $optionsNewArray['hasProduce'] = $rpcParams['has_produce'];
            $optionsParams['new'] = json_encode($optionsNewArray);

            if (!empty($oldValues[0])) {
                $optionsEditArray = json_decode($oldValues[0]['options'], true);
                $optionsEditArray['hasProduce'] = $rpcParams['has_produce'];

                $optionsParams['edit'] = json_encode($optionsEditArray);
            }
        }

        $options = [
            'tablename' => $UserDbController->DbHandler->tableDiaryConfigs,
            'mainData' => [
                'name' => $rpcParams['name'],
                'number' => $rpcParams['number'],
                'type_id' => $rpcParams['type_id'],
                'model' => $rpcParams['model'],
                'manufacturer' => $rpcParams['manufacturer'],
                'description' => $rpcParams['description'],
                'config_type' => $rpcParams['request_type'],
                'perf_title' => $rpcParams['perf_title'],
                'wialon_id' => $rpcParams['wialon_id'],
                'options' => $optionsParams['edit'],
            ],
        ];

        if (!isset($rpcParams['id'])) { // add is requested
            $options['mainData']['options'] = $optionsParams['new'];
            if (!empty($rpcParams['warehouse_item_id'])) {
                $rpcParams['options']['product_type'] = '';
                $rpcParams['options']['status'] = 'pending';
                $options['mainData']['options'] = json_encode($rpcParams['options']);
                $options['mainData']['warehouse_item_id'] = $rpcParams['warehouse_item_id'];
            }

            $recordID = $UserDbController->addItem($options);

            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options['mainData'], ['attachment_id' => $recordID], 'Add diary Attachments');

            return $recordID;
        }

        // Don't change the options of warehouse product on second syncing
        if (!empty($rpcParams['warehouse_item_id'])) {
            unset($options['mainData']['options']);
        }

        $options['where'] = ['id' => $rpcParams['id']];
        $UserDbController->editItem($options);
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options, $oldValues, 'Edit diary Attachments');
    }

    /** TODO: Move warehouse methods to integration hooks class and remove the code below */

    /**
     * @throws MTRpcException
     *
     * @return array
     */
    public function syncWarehouseItems($data)
    {
        $result = [];
        $warehouseModule = new WarehouseModuleClass();
        $params = [
            'criteries' => [
                'types' => ['SUB'],
                'document_tr_types' => ['SUB_PLOT'],
                'companies' => $data['farm'] ? [$data['farm']] : [],
                'closed_at' => $data['date'],
            ],
        ];

        $items = $warehouseModule->getTransactionItems($params);
        if (empty($items['result']['items'])) {
            throw new MTRpcException('WAREHOUSE_ITEMS_NOT_FOUND', -33012);
        }
        foreach ($items['result']['items'] as $item) {
            $this->syncMaterialUnitTypes($item);

            $data = [
                'name' => $item['item_name'],
                'warehouse_item_id' => $item['item_id'],
            ];
            $options = [
                'measure_name' => $item['measure_name'],
                'measure_short_name' => $item['measure_short_name'],
                'measure_id' => $item['measure_id'],
            ];

            $formattedItem = $this->createItem($data, CONFIG_TYPE_PRODUCTS, $options);
            $this->saveWarehouseItem($formattedItem);

            $result[] = $formattedItem;
        }

        return $result;
    }

    public function checkProductAvailability($data)
    {
        $UserDbDiaryController = new UserDbDiaryController($this->User->Database);
        $warehouseQuantity = $UserDbDiaryController->getWarehouseProductQuantity($data['item']['productWarehouseId'], $data['date'], $data['company']);
        $eventsUsedQuantity = $UserDbDiaryController->getEventQuantities($data['item']['productId'], $data['date'], $data['company']);

        return [
            'warehouseQuantity' => $warehouseQuantity['quantity'],
            'warehouseProductPrice' => $warehouseQuantity['singlePrice'],
            'eventsUsedQuantity' => $eventsUsedQuantity,
            'totalUsedQuantity' => $warehouseQuantity['quantity'] - $eventsUsedQuantity,
            'haveItems' => $warehouseQuantity['haveItems'],
        ];
    }

    /**
     * @return int
     */
    private function saveWarehouseItem($formattedItem)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $options = [
            'tablename' => $UserDbController->DbHandler->tableDiaryConfigs,
            'return' => ['id'],
            'where' => [
                'warehouse_item_id' => ['column' => 'warehouse_item_id', 'compare' => '=', 'value' => strval($formattedItem['warehouse_item_id'])],
            ],
        ];
        $item = $UserDbController->getItemsByParams($options, false, false);

        if (!empty($item)) {
            $formattedItem['id'] = $item[0]['id'];
        }

        return $this->saveItem($formattedItem);
    }

    /**
     * @throws Exception
     *
     * @return null|int
     */
    private function syncMaterialUnitTypes($warehouseItem)
    {
        $unitId = null;
        $UserDbController = new UserDbController($this->User->Database);

        $materialUnitTypes = $UserDbController->getItemsByParams([
            'tablename' => $UserDbController->DbHandler->tableDiaryConfigs,
            'return' => ['id', 'name'],
            'where' => [
                'config_type' => ['column' => 'config_type', 'compare' => '=', 'value' => CONFIG_TYPE_MEASURES], // config_type = 10 is the type of measures/units
            ],
        ], false, false);

        foreach ($materialUnitTypes as $unit) {
            // Transform unit norm to unit measure by removing /дка from the norm. For example кг/дка becomes кг.
            // It is needed because the warehouse API works with measures but there are norms in techofarm
            $tfMeasure = str_replace('/дка', '', $unit['name']);
            if ($tfMeasure === strtolower($warehouseItem['measure_name'])) {
                $unitId = $unit['id'];

                break;
            }

            if ($tfMeasure === rtrim(strtolower($warehouseItem['measure_short_name']), '.')) {
                $unitId = $unit['id'];

                break;
            }
        }

        if (empty($unitId)) {
            $item = $this->createItem([
                'name' => $warehouseItem['measure_name'] . '/дка',  // It is needed to convert measure to norm per acr
            ], 10);

            $unitId = $this->saveItem($item);
        }

        if (empty($unitId)) {
            throw new Exception("Can't sync the material units");
        }

        return $unitId;
    }

    /**
     * @param array $data
     * @param null $options
     *
     * @return array
     */
    private function createItem($data, $type, $options = null)
    {
        $item = [];
        $item['name'] = $data['name'];
        $item['number'] = $data['number'] ? $data['number'] : null;
        $item['type_id'] = $data['type_id'] ? $data['type_id'] : null;
        $item['model'] = $data['model'] ? $data['model'] : null;
        $item['manufacturer'] = $data['manufacturer'] ? $data['manufacturer'] : null;
        $item['description'] = $data['description'] ? $data['description'] : null;
        $item['request_type'] = $type;
        $item['perf_title'] = $data['perf_title'] ? $data['perf_title'] : null;
        $item['wialon_id'] = $data['wialon_id'] ? $data['wialon_id'] : null;
        $item['warehouse_item_id'] = $data['warehouse_item_id'] ? $data['warehouse_item_id'] : null;

        if (!empty($options)) {
            foreach ($options as $key => $option) {
                $item['options'][$key] = $option;
            }
        }

        return $item;
    }
}
