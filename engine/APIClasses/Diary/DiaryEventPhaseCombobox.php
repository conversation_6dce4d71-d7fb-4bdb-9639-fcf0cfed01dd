<?php

namespace TF\Engine\APIClasses\Diary;

use Prado\Web\Services\TRpcApiProvider;

/**
 * Combobox 'Етап' на обработка.
 *
 * @rpc-module Diary
 *
 * @rpc-service-id diary-event-phase-combobox
 */
class DiaryEventPhaseCombobox extends TRpcApiProvider
{
    protected static $phases = [
        ['id' => 1, 'name' => 'Планирана'], // event planned
        ['id' => 2, 'name' => 'Изпълнена'], // event completed
    ];

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getDiaryEventPhaseCombobox']],
        ];
    }

    /**
     * @api-module read
     *
     * @return array
     */
    public function getDiaryEventPhaseCombobox($rpcParams = [])
    {
        if ($this->User->isGuest) {
            return [];
        }

        $return = self::$phases;

        if (isset($rpcParams['selected']) && true == $rpcParams['selected']) {
            $return[0]['selected'] = true;
        }

        return $return;
    }
}
