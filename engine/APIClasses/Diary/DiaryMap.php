<?php

namespace TF\Engine\APIClasses\Diary;

use Exception;
use Prado\Prado;
use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbDiary\UserDbDiaryController;
use TF\Engine\Plugins\Core\Users\UsersController;

// Prado::using('Plugins.Core.UserDbDiary.*');
// Prado::using('Plugins.Core.UserDbDiary.conf');

/**
 * All map related functions inside the Diary submodule.
 *
 * @rpc-module Diary
 *
 * @rpc-service-id diary-map
 */
class DiaryMap extends TRpcApiProvider
{
    private $module = 'Diary';
    private $service_id = 'diary-map';

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'getMapZPlotInfo' => ['method' => [$this, 'getMapZPlotInfo']],
            'saveAbLine' => ['method' => [$this, 'SaveAbLine']],
            'exportAbLines' => ['method' => [$this, 'exportAbLines']],
            'exportAbLinesTfConnect' => ['method' => [$this, 'exportAbLinesTfConnect']],
            'delAbLine' => ['method' => [$this, 'delAbLine']],
            'editAbLine' => ['method' => [$this, 'editAbLine']],
            'copyAbLines' => ['method' => [$this, 'copyAbLines']],
        ];
    }

    /**
     * Returns information for the selected map area.
     *
     * @api-method getMapZPlotInfo
     *
     * @internal param array $rpcParams
     *         {
     *         		#item string bbox
     *         		#item string x
     *         		#item string y
     *         		#item string layer
     *         		#item string width
     *         		#item string height
     *         }
     *
     * @throws MTRpcException
     *
     * @return array
     */
    public function getMapZPlotInfo($rpcParams)
    {
        $LayersController = new LayersController('Layers');
        $UserDbController = new UserDbController($this->User->Database);
        $urlRequest = WMS_SERVER_INTERNAL
                . '?REQUEST=GetFeatureInfo'
                . '&EXCEPTIONS=application/vnd.ogc.se_xml'
                . '&VERSION=1.1.1'
                . '&MAP=' . WMS_MAP_PATH . $this->User->GroupID . '.map'
                . '&BBOX=' . $rpcParams['bbox']
                . '&X=' . $rpcParams['x']
                . '&Y=' . $rpcParams['y']
                . '&INFO_FORMAT=text/plain'
                . '&QUERY_LAYERS=' . $rpcParams['layer']
                . '&LAYERS=' . $rpcParams['layer']
                . '&FEATURE_COUNT=50'
                . '&SRS=EPSG:900913'
                . '&STYLES='
                . '&WIDTH=' . $rpcParams['width']
                . '&HEIGHT=' . $rpcParams['height'];
        $curl = curl_init($urlRequest);
        curl_setopt($curl, CURLOPT_FAILONERROR, true);
        curl_setopt($curl, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($curl, CURLOPT_ENCODING, '');
        curl_setopt($curl, CURLOPT_IPRESOLVE, CURL_IPRESOLVE_V4);
        $result = curl_exec($curl);

        $exploded_result = @explode(' ', $result);
        if ('no' == $exploded_result[5]) {
            $return['error'] = true;

            throw new MTRpcException('MAP_EMPTY_AREA', -33056);
        }
        $zplotID = str_replace(':', '', $exploded_result[5]);

        if (!(int) $zplotID) {
            $return['error'] = true;

            throw new MTRpcException('MAP_EMPTY_AREA', -33056);
        }

        $options = [
            'where' => [
                'table_name' => ['column' => 'table_name', 'compare' => '=', 'value' => $rpcParams['layer']],
            ],
        ];
        $layer_results = $LayersController->getLayers($options);

        // get clicked plot data
        $options = [
            'tablename' => $rpcParams['layer'],
            'return' => ['St_AsText(geom) as geom'],
            'where' => [
                'plot_gid' => ['column' => 'id', 'compare' => '=', 'value' => $zplotID],
            ],
        ];
        $plot_results = $UserDbController->getItemsByParams($options);

        return [
            'error' => false,
            'farming_id' => $layer_results[0]['farming'],
            'year_id' => $layer_results[0]['year'],
            'zplot_id' => $zplotID,
            'geom' => $plot_results[0]['geom'],
        ];
    }

    /**
     * Save AB line into DB.
     *
     * @api-method saveAbLine
     *
     * @internal param array $rpcParams
     *         {
     *         #item integer  gid
     *         #item string   name
     *         #item string   offset
     *         #item string   direction
     *         #item string   layer_table
     *         #item geometry geom
     *         }
     *
     * @return array
     */
    public function SaveAbLine($rpcParams)
    {
        $UserDbDiaryController = new UserDbDiaryController($this->User->Database);
        $UserDbDiaryController->SaveAbLine($rpcParams);

        $UserDbDiaryController->generateABLinesMapFile($this->User->UserID, $this->User->Database);
        $UsersController = new UsersController('Users');
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $rpcParams, [], 'Save AB Line.');
    }

    /**
     * Export AB lines.
     *
     * @api-method exportAbLines
     *
     * @internal param array $rpcParams
     *         {
     *              #item string output_device
     *              #item string plots A JSON array {geom: geojson, id:int, name:string, offset: int}
     *         }
     *
     * @return array
     */
    public function exportAbLines($rpcParams)
    {
        $UserDbDiaryController = new UserDbDiaryController($this->User->Database);
        $filePath = $UserDbDiaryController->exportAbLines($rpcParams);

        $UsersController = new UsersController('Users');
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $rpcParams, [], 'Export AB Line.');

        return PUBLIC_UPLOAD_EXPORT_RELATIVE_PATH . $this->User->UserID . '/' . basename($filePath);
    }

    /**
     * Export AB lines.
     *
     * @api-method exportAbLines
     *
     * @internal param array $rpcParams
     *         {
     *              #item integer device_id The TF Connect device id.
     *              #item string output_device
     *              #item string plots A JSON array {geom: geojson, id:int, name:string, offset: int}
     *         }
     *
     * @return array
     */
    public function exportAbLinesTfConnect($rpcParams)
    {
        $UserDbDiaryController = new UserDbDiaryController($this->User->Database);
        $filePath = $UserDbDiaryController->exportAbLines($rpcParams);

        $UsersController = new UsersController('Users');
        $userId = $this->User->UserID;
        $modems = $UsersController->getUserModems([
            'return' => ['serial'],
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $rpcParams['device_id']],
                'user_id' => ['column' => 'user_id', 'compare' => '=', 'value' => $userId],
            ],
        ]);
        $modemSerial = $modems[0]['serial'];
        $deviceModule = Prado::getApplication()->getModule('deviceModule');
        $uplResp = $deviceModule->uploadFile($modemSerial, $filePath);
        unlink($filePath);
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $rpcParams, [], 'Export AB Line TFConnect.');

        return $deviceModule->setFileStatus($modemSerial, $uplResp['file_id'], 'sending');
    }

    /**
     * Delete AB lines.
     *
     * @api-method exportAbLines
     *
     * @internal param array $rpcParams
     *         {
     *         		array  gid
     *         }
     *
     * @return array
     */
    public function delAbLine($rpcParams)
    {
        $UserDbDiaryController = new UserDbDiaryController($this->User->Database);
        $UsersController = new UsersController('Users');
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $rpcParams, [], 'Delete AB Line.');

        return $UserDbDiaryController->delAbLine($rpcParams);
    }

    public function editAbLine($rpcParams)
    {
        $UserDbDiaryController = new UserDbDiaryController($this->User->Database);
        $UsersController = new UsersController('Users');
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $rpcParams, [], 'Edit AB Line.');

        return $UserDbDiaryController->editAbLine($rpcParams);
    }

    /**
     * copy AB line.
     *
     * @api-method copyAbLines
     *
     * @throws TDbException
     *
     * @return array
     *
     * @internal param array $rpcParams
     *         {
     *         #item integer  gid
     *         #item string   layer_table
     *         #item array    ab_lines
     *         }
     */
    public function copyAbLines($rpcParams)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $options = [
            'tablename' => $UserDbController->DbHandler->tableAbLines,
            'return' => [
                'name',
                'marge',
                'direction',
                'layer_table',
                'ST_AsText(geom) as geom',
            ],
            'where' => [
                'gid' => ['column' => 'gid', 'compare' => 'IN', 'value' => $rpcParams['ab_lines_ids']],
            ],
        ];

        $results = $UserDbController->getItemsByParams($options);

        $transaction = $UserDbController->DbHandler->DbModule->beginTransaction();

        try {
            foreach ($results as $abLine) {
                $params['gid'] = $rpcParams['gid'];
                $params['zp_layer_id'] = $rpcParams['zp_layer_id'];
                $params['name'] = $abLine['name'];
                $params['geom'] = $abLine['geom'];

                $this->saveAbLine($params);
            }

            $transaction->commit();
        } catch (Exception $e) {
            $transaction->rollback();
            $UsersController = new UsersController('Users');
            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $rpcParams, [], 'AB line Save rollback.');
        }
    }
}
