<?php

namespace TF\Engine\APIClasses\Diary;

use InvalidArgumentException;
use Prado\Web\Services\TRpcApiProvider;
use TF\Application\Entity\ObjectPermissions;
use TF\Application\Entity\UserFarmings;
use TF\Engine\Kernel\ExportToExcelClass;
use TF\Engine\Kernel\ExportWordDocClass;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbDiary\UserDbDiaryController;

/**
 * Агротехника > Мероприятия > Справки.
 *
 * @rpc-module Diary
 *
 * @rpc-service-id diary-reports-grid
 */
class DiaryReportsGrid extends TRpcApiProvider
{
    /**
     * @var ArrayHelper
     */
    private $arrayHelper;

    /**
     * @var UserDbController
     */
    private $UserDbController;

    /**
     * create the default return.
     *
     * @var array
     */
    private $return = [
        'rows' => [],
        'total' => 0,
        'footer' => [],
    ];

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'summaryByPerformer' => ['method' => [$this, 'getSummaryReportByPerformer'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'detailedByPerformer' => ['method' => [$this, 'getDetailedReportByPerformer'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'detailedBy' => ['method' => [$this, 'getDetailedReportBy'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'getReportByPlot' => ['method' => [$this, 'getReportByPlot'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'getReportByFuel' => ['method' => [$this, 'getReportByFuel'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'getReportByProduce' => ['method' => [$this, 'getReportByProduce'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ], ],
            'getReportByProduct' => ['method' => [$this, 'getReportByProduct'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'createFuelDiary' => ['method' => [$this, 'createFuelDiary']],
            'createChemicalDiary' => ['method' => [$this, 'createChemicalDiary']],
            'createChemicalDiaryWord' => ['method' => [$this, 'createChemicalDiaryWord']],
            'exportPerformerSummaryReportXLS' => ['method' => [$this, 'exportPerformerSummaryReportXLS'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'exportPerformerDetailedReportXLS' => ['method' => [$this, 'exportPerformerDetailedReportXLS'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'exportPerformerDetailedReportByXLS' => ['method' => [$this, 'exportPerformerDetailedReportByXLS'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'exportPlotsReportXLS' => ['method' => [$this, 'exportPlotsReportXLS'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'deleteFile' => ['method' => [$this, 'deleteFile']],
        ];
    }

    /**
     * Обобщена справка по механизатор/служител.
     *
     * @api-method summaryByPerformer
     *
     * @param array $rpcParams
     *                         {
     *                         #item string   zp_ekate
     *                         #item string   zp_name
     *                         #item integer  performer
     *                         #item integer  farming
     *                         #item datetime date_from
     *                         #item datetime date_to
     *                         #item integer  event_type
     *                         #item integer  machine
     *                         #item integer  attachment
     *                         }
     * @param int|string $page pagination parameter
     * @param int|string $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array {
     *               #item array rows {
     *               #item field: 'name',
     *               #item string moto_hours,
     *               #item float completed_area,
     *               #item float zp_area,
     *               }
     *               #item integer total
     *               #item array footer {
     *               #item array {
     *               #item string name,
     *               #item string moto_hours,
     *               #item string completed_area
     *               #item string zp_area
     *               }
     *               }
     *               }
     */
    public function getSummaryReportByPerformer(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $UserDbDiaryController = new UserDbDiaryController($this->User->Database);
        $LayersController = new LayersController('Layers');
        $UserDbController = new UserDbController($this->User->Database);
        $FarmingController = new FarmingController('Farming');

        $this->arrayHelper = $LayersController->ArrayHelper;
        $this->UserDbController = $UserDbController;

        // get all layers tablenames
        $layers_tablenames = $LayersController->getLayersTablenames();
        $existing_layer_tables = $LayersController->getExistingLayersTables($layers_tablenames);

        if ('zp_area' == $sort) {
            $custom_sort = $sort;
            $sort = '';
        }

        if ($rpcParams['zp_ekate'] || '' != $rpcParams['zp_name']) {
            $zp_final = $UserDbDiaryController->filterByZpData($existing_layer_tables, $rpcParams);
        }

        $farmings = $FarmingController->getUserFarmings();
        $farmingIds = array_keys($farmings);
        $userFarmingIds = $this->User->getPermissionObjectIds(ObjectPermissions::PERMISSION_READ, UserFarmings::class);
        $farmingIds = $this->arrayHelper->filterEmptyStringArr($rpcParams['farming']);
        $farmingIds = isset($farmingIds) && count($farmingIds) > 0
            ? array_intersect($farmingIds, $userFarmingIds)
            : [...$userFarmingIds, null];

        $options = [
            'sort' => $sort,
            'order' => $order,
            'return' => [
                'dc.id', 'dc.name',
                'array_agg(e.completed_area) AS completed_area',
                'array_agg(e.treated_area) AS treated_area',
                'SUM(total_time_in) AS moto_hours',
                "array_agg(DISTINCT(e.farming_id || '-' || e.year_id || '#' || e.plot_id)) AS zp_ident",
            ],
            'where' => [
                'config_type' => ['column' => 'config_type', 'compare' => '=', 'prefix' => 'dc', 'value' => 9],
                'performer' => ['column' => 'id', 'compare' => 'IN', 'prefix' => 'dc', 'value' => $this->arrayHelper->filterEmptyStringArr($rpcParams['performer'])],
                'farming' => ['column' => 'farming_id', 'compare' => 'IN', 'prefix' => 'e', 'value' => $farmingIds],
                'date_from' => ['column' => 'complete_date_from::date', 'compare' => '>=', 'prefix' => 'e', 'value' => $rpcParams['date_from']],
                'date_to' => ['column' => 'complete_date_from::date', 'compare' => '<=', 'prefix' => 'e', 'value' => $rpcParams['date_to']],
                'event_type' => ['column' => 'type_id', 'compare' => 'IN', 'prefix' => 'e', 'value' => $this->arrayHelper->filterEmptyStringArr($rpcParams['event_type'])],
                'event_kind' => ['column' => 'id', 'compare' => 'IN', 'prefix' => 'e', 'value' => $this->arrayHelper->filterEmptyStringArr($rpcParams['event_kind'])],
                'machine' => ['column' => 'machine_id', 'compare' => 'IN', 'prefix' => 'e', 'value' => $this->arrayHelper->filterEmptyStringArr($rpcParams['machine'])],
                'attachment' => ['column' => 'attachment_id', 'compare' => 'IN', 'prefix' => 'e', 'value' => $this->arrayHelper->filterEmptyStringArr($rpcParams['attachment'])],
                'zp_filter' => ['column' => "e.farming_id || '-' || e.year_id || '#' || e.plot_id", 'compare' => 'IN', 'value' => $zp_final],
            ],
            'group' => 'dc.id',
        ];

        $results = $UserDbDiaryController->getDiaryReportByPerformer($options, false, false);
        $resultsCount = count($results);
        if (0 == $resultsCount) {
            return $this->return;
        }
        $total_moto_hours = [];
        $total_completed_area = 0;
        $total_zp_area = 0;
        for ($i = 0; $i < $resultsCount; $i++) {
            $zp_idents = explode(',', trim($results[$i]['zp_ident'], '{}'));
            $completed_areas = explode(',', trim($results[$i]['completed_area'], '{}'));
            $treated_areas = explode(',', trim($results[$i]['treated_area'], '{}'));
            $countZp = count($zp_idents);
            for ($j = 0; $j < $countZp; $j++) {
                $zp_ident_array = explode('#', $zp_idents[$j]);
                if (!(null != $zp_ident_array[0] && null != $zp_ident_array[1])) {
                    continue;
                }
                if (!array_key_exists($zp_ident_array[0], $existing_layer_tables)) {
                    continue;
                }
                $layer_zp_options = [
                    'tablename' => $layers_tablenames[$zp_ident_array[0]],
                    'return' => ['round((ST_Area(geom)/1000)::numeric, 3) AS zp_area'],
                    'where' => [
                        'id' => ['column' => 'id', 'compare' => '=', 'value' => $zp_ident_array[1]],
                    ],
                ];
                $zp_area = $UserDbController->getItemsByParams($layer_zp_options, false, false);
                $results[$i]['zp_area'] += $zp_area[0]['zp_area'];
            }
            $moto_hours = explode(':', $results[$i]['moto_hours']);
            $total_moto_hours[0] += $moto_hours[0];
            $total_moto_hours[1] += $moto_hours[1];
            $total_moto_hours[2] += $moto_hours[2];
            $completed_areas_count = count($completed_areas);
            $performer_completed_area = 0;
            for ($k = 0; $k < $completed_areas_count; $k++) {
                if ('NULL' !== $completed_areas[$k]) {
                    $total_completed_area += $completed_areas[$k];
                    $performer_completed_area += $completed_areas[$k];
                } elseif ('NULL' !== $treated_areas[$k]) {
                    $total_completed_area += $treated_areas[$k];
                    $performer_completed_area += $treated_areas[$k];
                } else {
                    $total_completed_area += 0;
                    $performer_completed_area += 0;
                }
            }
            $results[$i]['completed_area'] = $performer_completed_area;
            $total_zp_area += $results[$i]['zp_area'];
        }

        // format the total moto_hours data
        $total_moto_hours = $this->formatMotoHours($total_moto_hours);

        // zp_area sorting
        if (null !== $custom_sort) {
            $results = $LayersController->ArrayHelper->sortResultArray($results, $custom_sort, $order);
        }

        // pagination
        $results_for_page = $results;
        if (null != $page && null != $rows) {
            $results_for_page = array_slice($results, ($page - 1) * $rows, $rows);
        }
        $pageCount = count($results_for_page);
        $total_moto_hours_for_page = [];
        $total_completed_area_for_page = 0;
        $total_zp_area_for_page = 0;

        foreach ($results_for_page as &$res_for_page) {
            $moto_hours_for_page = explode(':', $res_for_page['moto_hours']);
            $total_moto_hours_for_page[0] += $moto_hours_for_page[0];
            $total_moto_hours_for_page[1] += $moto_hours_for_page[1];
            $total_moto_hours_for_page[2] += $moto_hours_for_page[2];
            $total_zp_area_for_page += $res_for_page['zp_area'];
            $total_completed_area_for_page += $res_for_page['completed_area'];
            if (!$res_for_page['moto_hours']) {
                $res_for_page['moto_hours'] = '-';
            }
            if (!$res_for_page['zp_area']) {
                $res_for_page['zp_area'] = '-';
            }
            if (!$res_for_page['completed_area']) {
                $res_for_page['completed_area'] = '-';
            }
        }

        // format the total moto_hours data for page
        $total_moto_hours_for_page = $this->formatMotoHours($total_moto_hours_for_page);

        $return['rows'] = $results_for_page;
        $return['total'] = $resultsCount;
        $return['footer'] = [
            [
                'name' => '<b>Общо за стр.</b>',
                'moto_hours' => implode(':', $total_moto_hours_for_page),
                'completed_area' => number_format($total_completed_area_for_page, 3, '.', ''),
                'zp_area' => number_format($total_zp_area_for_page, 3, '.', ''),
            ],
            [
                'name' => '<b>Общо</b>',
                'moto_hours' => implode(':', $total_moto_hours),
                'completed_area' => number_format($total_completed_area, 3, '.', ''),
                'zp_area' => number_format($total_zp_area, 3, '.', ''),
            ],
        ];

        return $return;
    }

    /**
     * Подробна справка по механизатор/служител.
     *
     * @api-method detailedByPerformer
     *
     * @param array $rpcParams
     *                         {
     *                         #item string   zp_ekate
     *                         #item string   zp_name
     *                         #item integer  performer_id
     *                         #item integer  farming
     *                         #item datetime date_from
     *                         #item datetime date_to
     *                         #item integer  event_type
     *                         #item integer  machine
     *                         #item integer  attachment
     *                         }
     * @param string $page pagination parameter
     * @param string $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array {
     *               #item array rows {
     *               #item integer id
     *               #item string date
     *               #item string machine
     *               #item string attachment
     *               #item string event_type
     *               #item string zp_name
     *               #item float zp_area
     *               #item string farming
     *               #item string year
     *               #item string moto_hours
     *               #item float completed_area
     *               }
     *               #item integer total
     *               #item array footer {
     *               #item array {
     *               #item string zp_name,
     *               #item string moto_hours,
     *               #item string completed_area
     *               #item string zp_area
     *               }
     *               }
     *               }
     */
    public function getDetailedReportByPerformer(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $LayersController = new LayersController('Layers');
        $FarmingController = new FarmingController('Farming');
        $UserDbDiaryController = new UserDbDiaryController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);

        $this->arrayHelper = $LayersController->ArrayHelper;

        // get all zp layers tablenames
        $layers_tablenames = $LayersController->getLayersTablenames();
        $layers_tablenames = $LayersController->getExistingLayersTables($layers_tablenames);

        if ($rpcParams['zp_ekate'] || '' != $rpcParams['zp_name']) {
            $zp_final = $UserDbDiaryController->filterByZpData($layers_tablenames, $rpcParams);
        }

        // get all group farmings and create array like predefined config
        $final_farming = $FarmingController->getUserFarmings();
        $userFarmingIds = array_keys($final_farming);
        $farmingIds = $this->arrayHelper->filterEmptyStringArr($rpcParams['farming']);
        $farmingIds = isset($farmingIds) && count($farmingIds) > 0
            ? array_intersect($farmingIds, $userFarmingIds)
            : [...$userFarmingIds, null];

        $options = [
            'return' => [
                "farming_id || '-' || year_id || '#' || plot_id AS zp_ident",
                'dx.price',
                'de.farming_id',
                'de.year_id',
                'de.completed_area',
                'de.treated_area',
                'de.complete_date_from',
                'de.price_per_area',
                'de.id as event_id',
                'total_time_in'],
            'where' => [
                'performer_id' => ['column' => 'performer_id', 'prefix' => 'de', 'compare' => '=', 'value' => $rpcParams['performer_id']],
                'farming' => ['column' => 'farming_id', 'prefix' => 'de', 'compare' => 'IN', 'value' => $farmingIds],
                'date_from' => ['column' => 'complete_date_from::date', 'prefix' => 'de', 'compare' => '>=', 'value' => $rpcParams['date_from']],
                'date_to' => ['column' => 'complete_date_from::date', 'prefix' => 'de', 'compare' => '<=', 'value' => $rpcParams['date_to']],
                'event_type' => ['column' => 'type_id', 'prefix' => 'de', 'compare' => 'IN', 'value' => $this->arrayHelper->filterEmptyStringArr($rpcParams['event_type'])],
                'event_kind' => ['column' => 'id', 'compare' => 'IN', 'prefix' => 'dc4', 'value' => $this->arrayHelper->filterEmptyStringArr($rpcParams['event_kind'])],
                'machine' => ['column' => 'machine_id', 'prefix' => 'de', 'compare' => 'IN', 'value' => $this->arrayHelper->filterEmptyStringArr($rpcParams['machine'])],
                'attachment' => ['column' => 'attachment_id', 'prefix' => 'de', 'compare' => 'IN', 'value' => $this->arrayHelper->filterEmptyStringArr($rpcParams['attachment'])],
                'zp_filter' => ['column' => "farming_id || '-' || year_id || '#' || plot_id", 'compare' => 'IN', 'value' => $zp_final],
            ],
        ];

        $results = $UserDbDiaryController->getDetaildDetaildEventsReport($options, false, false);

        $resultsCount = count($results);
        if (0 == $resultsCount) {
            return $this->return;
        }

        $total_moto_hours = [];
        $resultRows = [];
        $totalPrice = 0;
        $total_completed_area = 0;
        $total_zp_area = 0;

        for ($i = 0; $i < $resultsCount; $i++) {
            $zp_ident_array = explode('#', $results[$i]['zp_ident']);
            if (!(null != $zp_ident_array[0] && null != $zp_ident_array[1])) {
                continue;
            }

            if ($layers_tablenames[$zp_ident_array[0]]) {
                $layer_zp_options = [
                    'tablename' => $layers_tablenames[$zp_ident_array[0]],
                    'return' => ['area_name', 'isak_prc_uin', 'round((ST_Area(geom)/1000)::numeric, 3) AS zp_area'],
                    'where' => [
                        'id' => ['column' => 'id', 'compare' => '=', 'value' => $zp_ident_array[1]],
                    ],
                ];
                $zp_area = $UserDbController->getItemsByParams($layer_zp_options, false, false);

                if ('' != $zp_area[0]['area_name']) {
                    $results[$i]['zp_name'] = $zp_area[0]['area_name'];
                } elseif ('' != $zp_area[0]['isak_prc_uin']) {
                    $results[$i]['zp_name'] = $zp_area[0]['isak_prc_uin'];
                }

                $results[$i]['zp_area'] = $zp_area[0]['zp_area'];
            } else {
                $results[$i]['zp_name'] = '-';
                $results[$i]['zp_area'] = '-';
            }

            if (!$results[$i]['completed_area']) {
                $results[$i]['completed_area'] = !empty($results[$i]['treated_area']) ? $results[$i]['treated_area'] : 0;
            }
            $moto_hours = explode(':', $results[$i]['total_time_in']);
            $total_moto_hours[0] += $moto_hours[0];
            $total_moto_hours[1] += $moto_hours[1];
            $total_moto_hours[2] += $moto_hours[2];

            $total_completed_area += $results[$i]['completed_area'];
            $total_zp_area += $results[$i]['zp_area'];
            $totalPrice += $results[$i]['completed_area'] * $results[$i]['price'];
            // format data for grid
            $resultRows[$i]['id'] = $results[$i]['id'];
            $resultRows[$i]['date'] = $results[$i]['complete_date_from'] ? strftime('%d.%m.%Y', strtotime($results[$i]['complete_date_from'])) : '-';
            $resultRows[$i]['machine'] = $results[$i]['machine_type'] . ' - ' . $results[$i]['machine'];
            $resultRows[$i]['attachment'] = $results[$i]['attachment_type'] . ' - ' . $results[$i]['attachment'];
            $resultRows[$i]['event_type'] = $results[$i]['type'] . ' - ' . $results[$i]['subtype'];
            $resultRows[$i]['zp_name'] = $results[$i]['zp_name'] ? $results[$i]['zp_name'] : '-';
            $resultRows[$i]['zp_area'] = $results[$i]['zp_area'];
            $resultRows[$i]['farming'] = $final_farming[(int)$results[$i]['farming_id']] ? $final_farming[(int)$results[$i]['farming_id']] : '-';
            $resultRows[$i]['year'] = $GLOBALS['Farming']['years'][$results[$i]['year_id']]['year'];
            $resultRows[$i]['moto_hours'] = $results[$i]['total_time_in'] ? $results[$i]['total_time_in'] : '-';
            $resultRows[$i]['completed_area'] = number_format($results[$i]['completed_area'], 3, '.', '');

            // price_per_area is calculated before insert in db using treated_area
            if (null != $results[$i]['price_per_area']) {
                $resultRows[$i]['price'] = BGNtoEURO($results[$i]['price_per_area']);
            } else {
                $productTreatmentPrice = $this->getEventProductTreatmentPrice($results[$i]['event_id']);
                $resultRows[$i]['price'] = BGNtoEURO(($results[$i]['price'] * $results[$i]['completed_area']) + $productTreatmentPrice);
            }
        }

        // format the total moto_hours data
        $total_moto_hours = $this->formatMotoHours($total_moto_hours);

        // sorting
        $resultRows = $LayersController->ArrayHelper->sortResultArray($resultRows, $sort, $order);

        // pagination
        $page_results = $resultRows;
        $pageResultsCount = count($page_results);
        if (null != $page && null != $rows) {
            $page_results = array_slice($resultRows, ($page - 1) * $rows, $rows);
        }
        $total_moto_hours_for_page = [];
        $total_completed_area_for_page = 0;
        $total_zp_area_for_page = 0;
        $totalPriceForPage = 0;
        for ($i = 0; $i < $pageResultsCount; $i++) {
            $moto_hours_for_page = explode(':', $page_results[$i]['moto_hours']);
            $total_moto_hours_for_page[0] += $moto_hours_for_page[0];
            $total_moto_hours_for_page[1] += $moto_hours_for_page[1];
            $total_moto_hours_for_page[2] += $moto_hours_for_page[2];

            $total_completed_area_for_page += $page_results[$i]['completed_area'];
            $total_zp_area_for_page += $page_results[$i]['zp_area'];
            $totalPriceForPage += $page_results[$i]['price'];
        }

        // format the total moto_hours data for page
        $total_moto_hours_for_page = $this->formatMotoHours($total_moto_hours_for_page);

        $return['rows'] = $page_results;
        $return['total'] = $resultsCount;
        $return['footer'] = [
            [
                'zp_name' => '<b>Общо за стр.</b>',
                'moto_hours' => implode(':', $total_moto_hours_for_page),
                'completed_area' => number_format($total_completed_area_for_page, 3, '.', ''),
                'zp_area' => number_format($total_zp_area_for_page, 3, '.', ''),
                'price' => BGNtoEURO($totalPriceForPage),
            ],
            [
                'zp_name' => '<b>Общо</b>',
                'moto_hours' => implode(':', $total_moto_hours),
                'completed_area' => number_format($total_completed_area, 3, '.', ''),
                'zp_area' => number_format($total_zp_area, 3, '.', ''),
                'price' => BGNtoEURO($totalPrice),
            ],
        ];

        return $return;
    }

    /**
     * Индивидуална справка за.
     *
     * @api-method detailedBy
     *
     * @param array $rpcParams
     *                         {
     *                         #item string   zp_ekate
     *                         #item string   zp_name
     *                         #item integer  performer_id
     *                         #item integer  farming
     *                         #item datetime date_from
     *                         #item datetime date_to
     *                         #item integer  event_type
     *                         #item integer  machine
     *                         #item integer  attachment
     *                         #item string group_by
     *                         }
     * @param string $page
     * @param string $rows
     * @param ?string $sort
     * @param ?string $order
     *
     * @return array {
     *               #item integer total,
     *               #item array footer {
     *               #item array {
     *               #item string rpcParams[group_by],
     *               #item string moto_hours
     *               #item string completed_area
     *               }
     *               }
     *               #item array rows {
     *               #item string date,
     *               #item string machine,
     *               #item string attachment,
     *               #item string zp_name,
     *               #item float zp_area,
     *               #item string farming,
     *               #item string year,
     *               #item string event_type,
     *               #item string moto_hours,
     *               #item float completed_area,
     *               #item float price,
     *               }
     *               }
     */
    public function getDetailedReportBy(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $UserDbDiaryController = new UserDbDiaryController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);
        $LayersController = new LayersController('Layers');

        $this->arrayHelper = $LayersController->ArrayHelper;

        // get all layers tablenames
        $layers_tablenames = $LayersController->getLayersTablenames();

        if ($rpcParams['zp_ekate'] || '' != $rpcParams['zp_name']) {
            $existing_layer_tables = $LayersController->getExistingLayersTables($layers_tablenames);
            $zp_final = $UserDbDiaryController->filterByZpData($existing_layer_tables, $rpcParams);
        }

        $options = [
            'tablename' => $UserDbController->DbHandler->tableDiaryEvents,
            'return' => [
                'SUM(total_time_in) AS moto_hours', 'SUM(completed_area) AS completed_area',
            ],
            'where' => [
                'performer_id' => ['column' => 'performer_id', 'compare' => '=', 'value' => $rpcParams['performer_id']],
                'farming' => ['column' => 'farming_id', 'compare' => 'IN', 'value' => $rpcParams['farming']],
                'date_from' => ['column' => 'complete_date_from::date', 'compare' => '>=', 'value' => $rpcParams['date_from']],
                'date_to' => ['column' => 'complete_date_from::date', 'compare' => '<=', 'value' => $rpcParams['date_to']],
                'event_type' => ['column' => 'type_id', 'compare' => 'IN', 'value' => $rpcParams['event_type']],
                'machine' => ['column' => 'machine_id', 'compare' => 'IN', 'value' => $rpcParams['machine']],
                'attachment' => ['column' => 'attachment_id', 'compare' => 'IN', 'value' => $rpcParams['attachment']],
                'zp_filter' => ['column' => "farming_id || '-' || year_id || '#' || plot_id", 'compare' => 'IN', 'value' => $zp_final],
            ],
        ];

        switch ($rpcParams['group_by']) {
            case 'date':
                $options['return'][] = 'complete_date_from::date';
                $options['group'] = 'complete_date_from::date';

                break;
            case 'machine':
                $options['return'][] = 'machine_id';
                $options['group'] = 'machine_id';

                break;
            case 'attachment':
                $options['return'][] = 'attachment_id';
                $options['group'] = 'attachment_id';

                break;
            case 'zp_name':
                $options['return'][] = "farming_id || '-' || year_id || '#' || plot_id AS zp_ident";
                $options['group'] = 'farming_id, year_id, plot_id';

                break;
            case 'event_type':
                $options['return'][] = 'type_id, subtype_id';
                $options['group'] = 'type_id, subtype_id';

                break;
            default:
                return $this->return;
        }

        $results = $UserDbController->getItemsByParams($options, false, false);
        $resultsCount = count($results);
        if (0 == $resultsCount) {
            return $this->return;
        }

        // GET CONFIGS FOR GRID INFO
        $configs_array = $this->getConfigs();
        $total_moto_hours = [];
        $total_completed_area = 0;

        for ($i = 0; $i < $resultsCount; $i++) {
            switch ($rpcParams['group_by']) {
                case 'date':
                    $results[$i]['date'] = $results[$i]['complete_date_from'] ? strftime('%d.%m.%Y', strtotime($results[$i]['complete_date_from'])) : '-';

                    break;
                case 'machine':
                    $machine_type = $configs_array[$results[$i]['machine_id']]['type_id'];
                    $results[$i]['machine'] = $configs_array[$machine_type]['name'] . ' - ' . $configs_array[$results[$i]['machine_id']]['number'];

                    break;
                case 'attachment':
                    $attachment_type = $configs_array[$results[$i]['attachment_id']]['type_id'];
                    $results[$i]['attachment'] = $configs_array[$attachment_type]['name'] . ' - ' . $configs_array[$results[$i]['attachment_id']]['number'];

                    break;
                case 'zp_name':
                    $zp_ident_array = explode('#', $results[$i]['zp_ident']);

                    if (!(null != $zp_ident_array[0] && null != $zp_ident_array[1])) {
                        continue 2;
                    }

                    $layer_zp_options = [
                        'tablename' => $layers_tablenames[$zp_ident_array[0]],
                        'return' => ['area_name', 'round((ST_Area(geom)/1000)::numeric, 3) AS zp_area'],
                        'where' => [
                            'id' => ['column' => 'id', 'compare' => '=', 'value' => $zp_ident_array[1]],
                        ],
                    ];
                    $zp_data = $UserDbController->getItemsByParams($layer_zp_options, false, false);

                    $results[$i]['zp_name'] = $zp_data[0]['area_name'] ? $zp_data[0]['area_name'] : '-';

                    break;
                case 'event_type':
                    $results[$i]['event_type'] = $configs_array[$results[$i]['type_id']]['name'] . ' - ' . $configs_array[$results[$i]['subtype_id']]['name'];

                    break;
                default:
                    break;
            }

            $results[$i]['moto_hours'] = $results[$i]['moto_hours'] ? $results[$i]['moto_hours'] : '-';
            $results[$i]['completed_area'] = number_format($results[$i]['completed_area'], 3, '.', '');

            $moto_hours = explode(':', $results[$i]['moto_hours']);
            $total_moto_hours[0] += $moto_hours[0];
            $total_moto_hours[1] += $moto_hours[1];
            $total_moto_hours[2] += $moto_hours[2];

            $total_completed_area += $results[$i]['completed_area'];
        }

        // format the total moto_hours data
        $total_moto_hours = $this->formatMotoHours($total_moto_hours);

        // sorting
        $results = $LayersController->ArrayHelper->sortResultArray($results, $sort, $order);

        // pagination
        $page_results = $results;
        if (null != $page && null != $rows) {
            $page_results = array_slice($results, ($page - 1) * $rows, $rows);
        }

        $pageResultsCount = count($page_results);
        $total_moto_hours_for_page = [];
        $total_completed_area_for_page = 0;
        for ($i = 0; $i < $pageResultsCount; $i++) {
            $moto_hours_for_page = explode(':', $page_results[$i]['moto_hours']);
            $total_moto_hours_for_page[0] += $moto_hours_for_page[0];
            $total_moto_hours_for_page[1] += $moto_hours_for_page[1];
            $total_moto_hours_for_page[2] += $moto_hours_for_page[2];

            $total_completed_area_for_page += $page_results[$i]['completed_area'];
        }

        // format the total moto_hours data for page
        $total_moto_hours_for_page = $this->formatMotoHours($total_moto_hours_for_page);

        $return['rows'] = $page_results;
        $return['total'] = $resultsCount;
        $return['footer'] = [
            [
                $rpcParams['group_by'] => '<b>Общо за стр.</b>',
                'moto_hours' => implode(':', $total_moto_hours_for_page),
                'completed_area' => number_format($total_completed_area_for_page, 3, '.', ''),
            ],
            [
                $rpcParams['group_by'] => '<b>Общо</b>',
                'moto_hours' => implode(':', $total_moto_hours),
                'completed_area' => number_format($total_completed_area, 3, '.', ''),
            ],
        ];

        return $return;
    }

    /**
     * Дневник за горивата.
     *
     * @api-method createFuelDiary
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer farming
     *                         #item integer year
     *                         }
     *
     * @return array|string
     */
    public function createFuelDiary($rpcParams)
    {
        $FarmingController = new FarmingController('Farming');
        $LayersController = new LayersController('Layers');
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbDiaryController = new UserDbDiaryController($this->User->Database);

        $calendar_year_from = $GLOBALS['Farming']['years'][$rpcParams['year']]['year'] . '-01-01 00:00:00';
        $calendar_year_to = $GLOBALS['Farming']['years'][$rpcParams['year']]['year'] . '-12-31 00:00:00';

        $options = [
            'where' => [
                'farming' => ['column' => 'farming', 'compare' => '=', 'prefix' => 't', 'value' => $rpcParams['farming']],
                'year' => ['column' => 'year', 'compare' => 'IN', 'prefix' => 't', 'value' => [$rpcParams['year'], $rpcParams['year'] + 1]],
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'prefix' => 't', 'value' => $this->User->GroupID],
                'layer_type' => ['column' => 'layer_type', 'compare' => '=', 'prefix' => 't', 'value' => 1],
            ],
        ];

        $layer_results = $LayersController->getLayers($options);
        $layerCount = count($layer_results);

        if (0 == $layerCount) {
            return [];
        }

        for ($i = 0; $i < $layerCount; $i++) {
            $layer_tables[] = $layer_results[$i]['table_name'];
            $layer_year[$layer_results[$i]['table_name']] = $layer_results[$i]['year'];
        }

        // check if layer tables exist
        $layer_tables_options = [
            'tablename' => 'pg_class',
            'return' => ['relname AS table_name'],
            'where' => [
                'relname' => ['column' => 'relname', 'compare' => 'IN', 'value' => $layer_tables],
            ],
        ];
        $existing_layer_tables = $UserDbController->getItemsByParams($layer_tables_options, false, false);
        $existingLayersCount = count($existing_layer_tables);
        if (0 == $existingLayersCount) {
            return [];
        }

        $results = [];
        for ($i = 0; $i < $existingLayersCount; $i++) {
            $options = [
                'zp_tablename' => $existing_layer_tables[$i]['table_name'],
                'return' => [
                    'zp.culture', 'e.type_id', 'e.subtype_id',
                    'SUM(total_fuel_cost) AS total_fuel_cost',
                    'SUM(completed_area) AS completed_area',
                    "string_agg(CAST(EXTRACT(MONTH FROM complete_date_to) AS TEXT), ',') as work_months",
                    "string_agg(CAST(machine_id AS TEXT), ',') as used_machines",
                    "string_agg(CAST((CASE WHEN is_rented = TRUE THEN machine_id ELSE NULL END) AS TEXT), ',') as rented_machines",
                ],
                'where' => [
                    'farming' => ['column' => 'farming_id', 'compare' => '=', 'prefix' => 'e', 'value' => $rpcParams['farming']],
                    'year' => ['column' => 'year_id', 'compare' => '=', 'prefix' => 'e', 'value' => $layer_year[$existing_layer_tables[$i]['table_name']]],
                    'phase_id' => ['column' => 'phase_id', 'compare' => '=', 'prefix' => 'e', 'value' => 2],
                    'calendar_year_from' => ['column' => 'complete_date_from', 'compare' => '>=', 'prefix' => 'e', 'value' => $calendar_year_from],
                    'calendar_year_to' => ['column' => 'complete_date_from', 'compare' => '<=', 'prefix' => 'e', 'value' => $calendar_year_to],
                ],
                'group' => 'culture, e.type_id, subtype_id',
                'sort' => 'culture asc, e.type_id asc, subtype_id',
                'order' => 'asc',
            ];

            $results = array_merge($results, $UserDbDiaryController->getDiaryReport($options, false, false));
        }
        $resultsCount = count($results);
        $merged_results = [];
        foreach ($results as $result) {
            $key = $result['culture'] . '-' . $result['type_id'] . '-' . $result['subtype_id'];
            $merged_results[$key]['culture'] = $result['culture'];
            $merged_results[$key]['type_id'] = $result['type_id'];
            $merged_results[$key]['subtype_id'] = $result['subtype_id'];
            $merged_results[$key]['total_fuel_cost'] += (float) $result['total_fuel_cost'];
            $merged_results[$key]['completed_area'] += (float) $result['completed_area'];
            $merged_results[$key]['work_months'] .= ',' . $result['work_months'];
            $merged_results[$key]['used_machines'] .= ',' . $result['used_machines'];
            $merged_results[$key]['rented_machines'] .= ',' . $result['rented_machines'];
        }

        $results = array_values($merged_results);

        // GET CONFIGS FOR GRID INFO
        $options = [
            'tablename' => $UserDbController->DbHandler->tableDiaryConfigs,
            'where' => [
                'config_type' => ['column' => 'config_type', 'compare' => 'IN', 'value' => [1, 2, 4]],
            ],
        ];
        $configs_array = $this->getConfigs($options);

        // END OF GET CONFIGS

        $cultures_array = [];
        $cultures_events = [];
        $j = 1;
        $resultsCount = count($results);

        for ($i = 0; $i < $resultsCount; $i++) {
            $row = [];
            if (!in_array($results[$i]['culture'], $cultures_array)) {
                $cultures_array[] = $results[$i]['culture'];
                $cultures_events[$results[$i]['culture']] = [
                    'headers' => [
                        'culture' => $GLOBALS['Farming']['crops'][$results[$i]['culture']]['crop_name'],
                        'record_number' => $j,
                    ],
                    'data' => [],
                ];
                $j++;
            }

            // format used machines
            $exploded_used_machines = explode(',', trim($results[$i]['used_machines'], ','));
            $used_machines = array_unique($exploded_used_machines);
            foreach ($used_machines as $key => $machine) {
                $used_machines[$key] = $configs_array[$machine]['number'];
            }
            $row['machines'] = implode(',<br/>', $used_machines);

            // format rented machines
            $exploded_rented_machines = explode(',', trim($results[$i]['rented_machines'], ','));
            $rented_machines = array_unique($exploded_rented_machines);
            foreach ($rented_machines as $key => $machine) {
                $rented_machines[$key] = $configs_array[$machine]['number'];
            }
            $row['rented_machines'] = implode(',<br/>', $rented_machines);

            // format work months
            $exploded_work_months = explode(',', trim($results[$i]['work_months'], ','));
            $work_months = array_unique($exploded_work_months);
            foreach ($work_months as $key => $month) {
                $work_months[$key] = $LayersController->StringHelper->getMonthName((int) $month);
            }
            $row['work_months'] = implode(',<br/>', $work_months);

            // format event type
            $row['event_type'] = $configs_array[$results[$i]['type_id']]['name'];
            $row['event_type'] .= ' -<br/>' . $configs_array[$results[$i]['subtype_id']]['name'];

            // format completed area
            $row['completed_area'] = round($results[$i]['completed_area'], 2);

            // format fuel cost
            $row['fuel_cost'] = round($results[$i]['total_fuel_cost'], 2);

            // format completed work
            if (0 == $row['completed_area']) {
                $row['completed_work'] = 0;
            } else {
                $row['completed_work'] = round($row['fuel_cost'] / $row['completed_area'], 2);
            }

            // add all formatted data to it's culture array element
            $cultures_events[$results[$i]['culture']]['data'][] = $row;
        }

        $content = [
            'headers' => [
                'year' => $GLOBALS['Farming']['years'][$rpcParams['year']]['title'],
            ],
            'tableData' => $cultures_events,
        ];

        $ltext = $FarmingController->StringHelper->loadTemplate($GLOBALS['Templates'][13]['template'], $content);
        $ltext = '<meta charset="UTF-8"><style>@page{size: landscape;}</style>' . $ltext;

        return $ltext;
    }

    /**
     * Дневник за агрохимични обработки.
     *
     * @api-method createChemicalDiaryWord
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer farming
     *                         #item integer year
     *                         }
     *
     * @return string
     */
    public function getChemicalDiaryData($rpcParams)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbDiaryController = new UserDbDiaryController($this->User->Database);
        $FarmingController = new FarmingController('Farming');

        $fertilizer_grid = new FertilizersDiaryGrid($this->rpcServer);
        $substance_grid = new SubstancesDiaryGrid($this->rpcServer);

        // get all substance treatment events
        $substance_diary_results = $substance_grid->getSubstancesDiaryGrid($rpcParams);
        // get all event types that are chemical treatments
        $fertilizers_diary_results = $fertilizer_grid->getFertilizersDiaryGrid($rpcParams);
        // required arrays for merge
        $isak_array = []; // holds every found ISAK number

        $content = [];
        // iterate substance treatment events
        foreach ($substance_diary_results['rows'] as &$row) {
            // check if element is in isak array. If not add it and add it's key to the key array
            if (!in_array($row['zp_id'], $isak_array)) {
                $isak_array[] = $row['zp_id'];
                // create main structure for current row and adding it's headers
                $content[$row['zp_id']] = [
                    'headers' => [
                        'ekatte' => $row['ekatte'],
                        'area' => number_format($row['area'], 2, ',', ''),
                        'isak' => strlen($row['isak_prc_uin']) ? $row['isak_prc_uin'] : $row['area_name'],
                        'culture' => $GLOBALS['Farming']['crops'][$row['culture']]['crop_name'],
                    ],
                    'substanceRows' => [],
                    'fertilizersRows' => [],
                ];
            }
            $row['treated_area'] = $row['treated_area'] . ' дка';
            $row['substance_dose'] = $row['substance_dose'] . ' ' . $row['unit_type'];
            $row['quarantine_period'] = $row['quarantine_period'] ? $row['quarantine_period'] : $row['quarantine_period'] . ' Дни';
            $content[$row['zp_id']]['substanceRows'][] = $row;
        }
        // iterate fertilizers treatments
        unset($row);
        // @noinspection ReferenceMismatchInspection
        foreach ($fertilizers_diary_results['rows'] as &$row) {
            // check if element is in isak array. If not add it and add it's key to the key array
            if (!in_array($row['zp_id'], $isak_array)) {
                $isak_array[] = $row['zp_id'];
                // create main structure for current row and adding it's headers
                $content[$row['zp_id']] = [
                    'headers' => [
                        'ekatte' => $row['ekatte'],
                        'area' => number_format($row['area'], 2, ',', ''),
                        'isak' => strlen($row['isak_prc_uin']) ? $row['isak_prc_uin'] : $row['area_name'],
                        'culture' => $row['culture'],
                    ],
                    'substanceRows' => [],
                    'fertilizersRows' => [],
                ];
            }
            $row['treated_area'] = $row['treated_area'] . ' дка';
            $row['used_material_qty'] = $row['used_material_qty'] . ' ' . $row['unit_type'];
            $content[$row['zp_id']]['fertilizersRows'][] = $row;
        }

        $farms = $FarmingController->getFarmings([
            'return' => ['company', 'bulstat', 'mol', 'company_address', 'mol_egn'],
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $rpcParams['farming']],
            ],
        ]);
        $template_data['content'] = $content;
        $template_data['farm'] = $farms[0];

        return $template_data;
    }

    /**
     * Дневник за агрохимични обработки.
     *
     * @api-method createChemicalDiary
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer farming
     *                         #item integer year
     *                         }
     *
     * @return string
     */
    public function createChemicalDiary($rpcParams)
    {
        $FarmingController = new FarmingController('Farming');
        $templateData = $this->getChemicalDiaryData($rpcParams);

        $ltext = $FarmingController->StringHelper->loadTemplate($GLOBALS['Templates'][14]['template'], $templateData);
        $ltext = '<meta charset="UTF-8"><style>@page{size: landscape;}</style>' . $ltext;

        return $ltext;
    }

    public function createChemicalDiaryWord($rpcParams)
    {
        $FarmingController = new FarmingController('Farming');

        $templateData = $this->getChemicalDiaryData($rpcParams);

        $docOpts = [
            'sections' => [
                'WordSection5' => [
                    'size' => '29.7cm 21cm',
                    'margin' => '2cm 0.5cm 2cm 0.5cm',
                    'mso-page-orientation' => 'landscape',
                ],
                'WordSection4' => [
                    'size' => '29.7cm 21cm',
                    'margin' => '2cm 0.5cm 2cm 0.5cm',
                    'mso-page-orientation' => 'landscape',
                ],
                'WordSection3' => [
                    'size' => '29.7cm 21cm',
                    'margin' => '2cm 0.5cm 2cm 0.5cm',
                    'mso-page-orientation' => 'landscape',
                ],
                'WordSection2' => [
                    'size' => '29.7cm 21cm',
                    'margin' => '2cm 0.5cm 2cm 0.5cm',
                    'mso-page-orientation' => 'landscape',
                ],
                'WordSection1' => [
                    'size' => '21cm 29.7cm',
                    'margin' => '1.1cm 2cm 1.5cm 2cm',
                    'mso-page-orientation' => 'portrait',
                ],
            ],
        ];
        $exportWordDoc = new ExportWordDocClass();
        $filename = 'dnevnik_agrohim_' . date('d_m_Y_H_i_s');
        $result = $exportWordDoc->export($filename, $FarmingController->StringHelper->loadTemplate($GLOBALS['Templates'][46]['template'], $templateData), true, $docOpts);

        return [
            'path' => $result,
        ];
    }

    /**
     * Експорт XLS -> Обобщена справка по механизатор/служител.
     *
     * @api-method exportPerformerSummaryReportXLS
     *
     * @param array $rpcParams
     *                         {
     *                         #item string   zp_ekate
     *                         #item string   zp_name
     *                         #item integer  performer
     *                         #item integer  farming
     *                         #item datetime date_from
     *                         #item datetime date_to
     *                         #item integer  event_type
     *                         #item integer  machine
     *                         #item integer  attachment
     *                         #item array filter
     *                         {
     *                         #item string performer
     *                         #item string farming
     *                         #item string ekate
     *                         #item string dateFrom
     *                         #item string eventType
     *                         #item string machine
     *                         #item string attachment
     *                         #item string zpName
     *                         }
     *                         }
     * @param int $page pagination parameter
     * @param int $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array
     */
    public function exportPerformerSummaryReportXLS(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $results = $this->getSummaryReportByPerformer($rpcParams, $page, $rows, $sort, $order);

        $filters = [];

        $filters[] = [
            'name' => 'Филтър:',
        ];
        $filters[] = [
            'name' => 'Механизатор/Служител:',
            'moto_hours' => $rpcParams['filter']['performer'],
        ];
        $filters[] = [
            'name' => 'Стопанство:',
            'moto_hours' => $rpcParams['filter']['farming'],
        ];
        $filters[] = [
            'name' => 'Землище:',
            'moto_hours' => $rpcParams['filter']['ekate'],
        ];
        $filters[] = [
            'name' => 'Период от / до:',
            'moto_hours' => $rpcParams['filter']['dateFrom'] . ' / ' . $rpcParams['filter']['dateTo'],
        ];
        $filters[] = [
            'name' => 'Вид обработка:',
            'moto_hours' => $rpcParams['filter']['eventType'],
        ];
        $filters[] = [
            'name' => 'Машина:',
            'moto_hours' => $rpcParams['filter']['machine'],
        ];
        $filters[] = [
            'name' => 'Прикачен инвентар:',
            'moto_hours' => $rpcParams['filter']['attachment'],
        ];
        $filters[] = [
            'name' => 'Парцел:',
            'moto_hours' => $rpcParams['filter']['zpName'],
        ];

        $filters[] = [];
        $filters[] = [];

        // heading
        $headers = [
            'name' => 'Механизатор/служител',
            'moto_hours' => 'Изработени моточасове за периода',
            'completed_area' => 'Обща обработена площ (дка)',
            'zp_area' => 'Обща площ на парцелите (дка)',
        ];

        foreach ($results['footer'] as $key => $footer) {
            $results['footer'][$key]['name'] = str_replace('<b>', '', $footer['name']);
            $results['footer'][$key]['name'] = str_replace('</b>', '', $results['footer'][$key]['name']);
        }

        $date = date('Y-m-d-H-i-s');
        $path = 'files/uploads/blanks/';
        $name = 'spravka_mehanizatori_' . $this->User->GroupID . '_' . $date;
        $filename = $path . $name . '.xlsx';

        $exportExcelDoc = new ExportToExcelClass();
        $exportExcelDoc->export($results['rows'], $headers, $results['footer'], count($filters));
        $exportExcelDoc->prependData($filters, $headers);
        $exportExcelDoc->saveFile($filename);

        $return = [];
        $return['path'] = $path . $name . '.xlsx';
        $return['name'] = $name . '.xlsx';

        return $return;
    }

    /**
     * Export XLS -> Индивидуална справка по механизатор/служител.
     *
     * @api-method exportPerformerDetailedReportXLS
     *
     * @param array $rpcParams
     *                         {
     *                         #item string   zp_ekate
     *                         #item string   zp_name
     *                         #item integer  performer
     *                         #item integer  farming
     *                         #item datetime date_from
     *                         #item datetime date_to
     *                         #item integer  event_type
     *                         #item integer  machine
     *                         #item integer  attachment
     *                         #item array filter
     *                         {
     *                         #item string performer
     *                         #item string farming
     *                         #item string ekate
     *                         #item string dateFrom
     *                         #item string eventType
     *                         #item string machine
     *                         #item string attachment
     *                         #item string zpName
     *                         }
     *                         }
     * @param int $page pagination parameter
     * @param int $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array
     */
    public function exportPerformerDetailedReportXLS(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $results = $this->getDetailedReportByPerformer($rpcParams, $page, $rows, $sort, $order);

        $filters = [];

        $filters[] = [
            'date' => 'Филтър:',
        ];
        $filters[] = [
            'date' => 'Стопанство:',
            'machine' => $rpcParams['filter']['farming'],
        ];
        $filters[] = [
            'date' => 'Землище:',
            'machine' => $rpcParams['filter']['ekate'],
        ];
        $filters[] = [
            'date' => 'Период от / до:',
            'machine' => $rpcParams['filter']['dateFrom'] . ' / ' . $rpcParams['filter']['dateTo'],
        ];
        $filters[] = [
            'date' => 'Вид обработка:',
            'machine' => $rpcParams['filter']['eventType'],
        ];
        $filters[] = [
            'date' => 'Машина:',
            'machine' => $rpcParams['filter']['machine'],
        ];
        $filters[] = [
            'date' => 'Прикачен инвентар:',
            'machine' => $rpcParams['filter']['attachment'],
        ];
        $filters[] = [
            'date' => 'Стопанска година:',
            'machine' => $rpcParams['filter']['farmingYear'],
        ];
        $filters[] = [
            'date' => 'Парцел:',
            'machine' => $rpcParams['filter']['zpName'],
        ];

        $filters[] = [];

        $filters[] = [
            'date' => 'Механизатор/Служител:',
            'machine' => $rpcParams['filter']['performer'],
        ];

        $filters[] = [];

        // heading
        $headers = [
            'date' => 'Дата',
            'machine' => 'Машина',
            'attachment' => 'Прикачен инвентар',
            'zp_name' => 'Парцел',
            'zp_area' => 'Обща площ на парцела (дка)',
            'farming' => 'Стопанство',
            'year' => 'Година',
            'event_type' => 'Тип обработка',
            'moto_hours' => 'Моточасове',
            'completed_area' => 'Обработена площ (дка)',
            'price' => 'Разход в лв',
        ];

        $date = date('Y-m-d-H-i-s');
        $path = 'files/uploads/blanks/';
        $name = 'podrobna_spravka_mehanizatori_' . $this->User->GroupID . '_' . $date;
        $filename = $path . $name . '.xlsx';

        foreach ($results['footer'] as $key => $footer) {
            $results['footer'][$key]['zp_name'] = str_replace('<b>', '', $footer['zp_name']);
            $results['footer'][$key]['zp_name'] = str_replace('</b>', '', $results['footer'][$key]['zp_name']);
        }

        $exportExcelDoc = new ExportToExcelClass();
        $exportExcelDoc->export($results['rows'], $headers, $results['footer'], count($filters));
        $exportExcelDoc->prependData($filters, $headers);
        $exportExcelDoc->saveFile($filename);

        $return = [];
        $return['path'] = $path . $name . '.xlsx';
        $return['name'] = $name . '.xlsx';

        return $return;
    }

    /**
     * Export XLS -> Индивидуална справка за 'Механизатор' по 'критерии'.
     *
     * @api-method exportPerformerDetailedReportByXLS
     *
     * @param array $rpcParams
     *                         {
     *                         #item string   zp_ekate
     *                         #item string   zp_name
     *                         #item integer  performer
     *                         #item integer  farming
     *                         #item datetime date_from
     *                         #item datetime date_to
     *                         #item integer  event_type
     *                         #item integer  machine
     *                         #item integer  attachment
     *                         #item array filter
     *                         {
     *                         #item string performer
     *                         #item string farming
     *                         #item string ekate
     *                         #item string dateFrom
     *                         #item string eventType
     *                         #item string machine
     *                         #item string attachment
     *                         #item string zpName
     *                         }
     *                         }
     * @param int|string $page pagination parameter
     * @param int|string $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @throws PHPExcel_Exception
     * @throws PHPExcel_Reader_Exception
     * @throws PHPExcel_Writer_Exception
     *
     * @return array
     */
    public function exportPerformerDetailedReportByXLS(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $results = $this->getDetailedReportBy($rpcParams, $page, $rows, $sort, $order);

        $filters = [];

        $filters[] = [
            $rpcParams['group_by'] => 'Филтър:',
        ];
        $filters[] = [
            $rpcParams['group_by'] => 'Стопанство:',
            'moto_hours' => $rpcParams['filter']['farming'],
        ];
        $filters[] = [
            $rpcParams['group_by'] => 'Землище:',
            'moto_hours' => $rpcParams['filter']['ekate'],
        ];
        $filters[] = [
            $rpcParams['group_by'] => 'Период от / до:',
            'moto_hours' => $rpcParams['filter']['dateFrom'] . ' / ' . $rpcParams['filter']['dateTo'],
        ];
        $filters[] = [
            $rpcParams['group_by'] => 'Вид обработка:',
            'moto_hours' => $rpcParams['filter']['eventType'],
        ];
        $filters[] = [
            $rpcParams['group_by'] => 'Машина:',
            'moto_hours' => $rpcParams['filter']['machine'],
        ];
        $filters[] = [
            $rpcParams['group_by'] => 'Прикачен инвентар:',
            'moto_hours' => $rpcParams['filter']['attachment'],
        ];
        $filters[] = [
            $rpcParams['group_by'] => 'Име на парцел:',
            'moto_hours' => $rpcParams['filter']['zpName'],
        ];

        $filters[] = [];
        $filters[] = [];
        $groupByText = str_replace('<b>', '', $rpcParams['filter']['group_by_text']);
        $groupByText = str_replace('</b>', '', $groupByText);
        $filters[] = [
            $rpcParams['group_by'] => 'Механизатор/Служител:',
            'moto_hours' => $rpcParams['filter']['performer'],
            'completed_area' => 'По ' . mb_strtolower($groupByText),
        ];

        $filters[] = [];

        // heading
        $headers = [
            $rpcParams['group_by'] => $groupByText,
            'moto_hours' => 'Моточасове',
            'completed_area' => 'Обработена площ (дка)',
        ];

        foreach ($results['footer'] as $key => $footer) {
            $results['footer'][$key][$rpcParams['group_by']] = str_replace('<b>', '', $footer[$rpcParams['group_by']]);
            $results['footer'][$key][$rpcParams['group_by']] = str_replace('</b>', '', $results['footer'][$key][$rpcParams['group_by']]);
        }

        $date = date('Y-m-d-H-i-s');
        $path = 'files/uploads/blanks/';
        $name = 'individualna_spravka_po_' . $this->User->GroupID . '_' . $date;
        $filename = $path . $name . '.xlsx';
        /** @var ExportToExcelClass $exportExcelDoc */
        $exportExcelDoc = new ExportToExcelClass();
        $exportExcelDoc->export($results['rows'], $headers, $results['footer'], count($filters));
        $exportExcelDoc->prependData($filters, $headers);
        $exportExcelDoc->saveFile($filename);

        $return = [];
        $return['path'] = $path . $name . '.xlsx';
        $return['name'] = $name . '.xlsx';

        return $return;
    }

    /**
     * Delete created XLS file.
     *
     * @api-method deleteFile
     *
     * @param string $fileName
     */
    public function deleteFile($fileName)
    {
        $path = PUBLIC_UPLOAD . '/blanks/';
        unlink($path . $fileName);
    }

    /**
     * Справка по продукт
     *
     * @api-method getReportByProduct
     *
     * @param array $rpcParams
     *                         {
     *                         #item string   zp_ekate
     *                         #item string   zp_name
     *                         #item integer  performer
     *                         #item integer  farming
     *                         #item datetime date_from
     *                         #item datetime date_to
     *                         #item integer  farming_year
     *                         #item integer  event_type
     *                         #item integer  machine
     *                         #item integer  attachment
     *                         }
     * @param int|string $page pagination parameter
     * @param int|string $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array {
     *               #item array rows {
     *               #item string attachment,
     *               #item string complete_date,
     *               #item float completed_area,
     *               #item float event_price,
     *               #item string farming,
     *               #item int farming_id,
     *               #item string machine,
     *               #item string moto_hours,
     *               #item string performer,
     *               #item string type,
     *               #item string year,
     *               #item float zp_area,
     *               }
     *               #item integer total
     *               #item array footer {
     *               #item array {
     *               #item string attachment,
     *               #item float completed_area,
     *               #item float event_price,
     *               #item string moto_hours,
     *               #item float zp_area,
     *               }
     *               }
     *               }
     */
    public function getReportByProduct(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $UserDbDiaryController = new UserDbDiaryController($this->User->Database);

        if (empty($rpcParams['product_type']) && empty($rpcParams['product_name'])) {
            throw new InvalidArgumentException('Missing mandatory perameter');
        }

        if (!empty($rpcParams['product_type'])) {
            $options['where'] = [
                'product_type' => ['column' => "options->>'product_type'", 'prefix' => 'dc8', 'compare' => 'IN', 'value' => $rpcParams['product_type']],
            ];
        }

        if (!empty($rpcParams['product_name'])) {
            $options['where'] = [
                'product_name' => ['column' => 'name', 'prefix' => 'dc8', 'compare' => 'ILIKE', 'value' => $rpcParams['product_name']],
            ];
        }

        if (!empty($rpcParams['product_id'])) {
            $options['where'] = [
                'product_id' => ['column' => 'substance_id', 'prefix' => 'dtp', 'compare' => '=', 'value' => (int)$rpcParams['product_id']],
            ];
        }

        $options['return'] = [
            'dc8.name product_name',
            'dtp.substance_dose product_dose',
            'dtp.sort sort',
            'dc9.name product_measure',
            'ROUND(dtp.substance_consumed::numeric, 3) product_consumed',
            'dtp.substance_unit_price single_price',
            'sum(COALESCE(dtp.price_per_area, 0)) over () total_cost_all_plots',
            'ROUND(sum(dtp.price_per_area)::numeric, 3) event_price',
            'COALESCE(dtp.treated_area::DECIMAL(12, 3), 0) as completed_area',
            'SUM(COALESCE (dtp.treated_area, 0)::DECIMAL(12, 3) ) OVER () total_completed_area',
        ];

        $options['group'] = 'dc8.id, dc9.id, dtp.id';

        return $UserDbDiaryController->getPlotData($rpcParams, $options, $page, $rows, $sort, $order);
    }

    /**
     * Справка по гориво.
     *
     * @api-method getReportByProduct
     *
     * @param array $rpcParams
     *                         {
     *                         #item string   zp_ekate
     *                         #item string   zp_name
     *                         #item integer  performer
     *                         #item integer  farming
     *                         #item datetime date_from
     *                         #item datetime date_to
     *                         #item integer  farming_year
     *                         #item integer  event_type
     *                         #item integer  machine
     *                         #item integer  attachment
     *                         }
     * @param int|string $page pagination parameter
     * @param int|string $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array {
     *               #item array rows {
     *               #item string attachment,
     *               #item string complete_date,
     *               #item float completed_area,
     *               #item float event_price,
     *               #item string farming,
     *               #item int farming_id,
     *               #item string machine,
     *               #item string moto_hours,
     *               #item string performer,
     *               #item string type,
     *               #item string year,
     *               #item float zp_area,
     *               }
     *               #item integer total
     *               #item array footer {
     *               #item array {
     *               #item string attachment,
     *               #item float completed_area,
     *               #item float event_price,
     *               #item string moto_hours,
     *               #item float zp_area,
     *               }
     *               }
     *               }
     */
    public function getReportByFuel(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $UserDbDiaryController = new UserDbDiaryController($this->User->Database);

        $options['where']['total_fuel_cost'] = ['column' => 'total_fuel_cost', 'compare' => '>', 'prefix' => 'de', 'value' => 0];
        $options['where']['total_fuel_cost1'] = ['column' => 'total_fuel_cost', 'compare' => 'IS NOT', 'prefix' => 'de', 'value' => 'NULL'];

        $options['return'] = [
            'ROUND(de.total_fuel_cost::numeric, 3) fuel_quantity',
            'SUM(COALESCE(de.total_fuel_cost, 0)::DECIMAL(12, 2)) OVER () total_fuel',
            'de.price_per_litre single_price',
            '(sum(COALESCE(de.total_fuel_cost::DECIMAL(12, 2) * de.price_per_litre::DECIMAL(12, 2), 0)) over ()) total_cost_all_plots',
            'COALESCE(de.total_fuel_cost::DECIMAL(12, 2) * de.price_per_litre::DECIMAL(12, 2), 0) event_price',
            'COALESCE(max(dtp.treated_area), max(dp.area), de.treated_area, 0)  completed_area',
            'SUM(COALESCE(max(dtp.treated_area), max(dp.area), de.treated_area, 0)) OVER() total_completed_area',
        ];

        return $UserDbDiaryController->getPlotData($rpcParams, $options, $page, $rows, $sort, $order);
    }

    /**
     * Справка по гориво.
     *
     * @api-method getReportByProduct
     *
     * @param array $rpcParams
     *                         {
     *                         #item string   zp_ekate
     *                         #item string   zp_name
     *                         #item integer  performer
     *                         #item integer  farming
     *                         #item datetime date_from
     *                         #item datetime date_to
     *                         #item integer  farming_year
     *                         #item integer  event_type
     *                         #item integer  machine
     *                         #item integer  attachment
     *                         }
     * @param int|string $page pagination parameter
     * @param int|string $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array {
     *               #item array rows {
     *               #item string attachment,
     *               #item string complete_date,
     *               #item float completed_area,
     *               #item float event_price,
     *               #item string farming,
     *               #item int farming_id,
     *               #item string machine,
     *               #item string moto_hours,
     *               #item string performer,
     *               #item string type,
     *               #item string year,
     *               #item float zp_area,
     *               }
     *               #item integer total
     *               #item array footer {
     *               #item array {
     *               #item string attachment,
     *               #item float completed_area,
     *               #item float event_price,
     *               #item string moto_hours,
     *               #item float zp_area,
     *               }
     *               }
     *               }
     */
    public function getReportByProduce(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $UserDbDiaryController = new UserDbDiaryController($this->User->Database);

        $options['where']['produce'] = ['column' => 'produce', 'compare' => 'IS NOT', 'prefix' => 'dp', 'value' => 'NULL'];

        if (!empty($rpcParams['sort'])) {
            $options['where']['sort'] = ['column' => 'sort', 'compare' => 'ILIKE', 'prefix' => 'dp', 'value' => $rpcParams['sort']];
        }

        $options['return'] = [
            'dp.sort as sort',
            'SUM(COALESCE(dp.produce, 0)::DECIMAL(13, 2)) OVER () total_produce',
            'SUM(COALESCE(dp.produce, 0)::DECIMAL(13, 2)) as produce',
            'ROUND(SUM(dp.produce_per_dka), 2) as produce_per_dka',
            'COALESCE(dp.area::DECIMAL(12, 3), 0) as completed_area',
            'SUM(COALESCE (dp.area, 0)::DECIMAL(12, 3) ) OVER () total_completed_area',
        ];

        $options['group'] = 'dp.id, dtp.id';

        return $UserDbDiaryController->getPlotData($rpcParams, $options, $page, $rows, $sort, $order);
    }

    /**
     * Справка по имот
     *
     * @api-method getReportByPlot
     *
     * @param array $rpcParams
     *                         {
     *                         #item string   zp_ekate
     *                         #item string   zp_name
     *                         #item integer  performer
     *                         #item integer  farming
     *                         #item datetime date_from
     *                         #item datetime date_to
     *                         #item integer  farming_year
     *                         #item integer  event_type
     *                         #item integer  machine
     *                         #item integer  attachment
     *                         }
     * @param int|string $page pagination parameter
     * @param int|string $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array {
     *               #item array rows {
     *               #item string attachment,
     *               #item string complete_date,
     *               #item float completed_area,
     *               #item float event_price,
     *               #item string farming,
     *               #item int farming_id,
     *               #item string machine,
     *               #item string moto_hours,
     *               #item string performer,
     *               #item string type,
     *               #item string year,
     *               #item float zp_area,
     *               }
     *               #item integer total
     *               #item array footer {
     *               #item array {
     *               #item string attachment,
     *               #item float completed_area,
     *               #item float event_price,
     *               #item string moto_hours,
     *               #item float zp_area,
     *               }
     *               }
     *               }
     */
    public function getReportByPlot(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $UserDbDiaryController = new UserDbDiaryController($this->User->Database);

        $options['return'] = [
            'ROUND((sum(
                    COALESCE (de.price_per_area, 0)
                    + COALESCE ( de.total_fuel_cost * de.price_per_litre, 0 )
                    + COALESCE (de.amortization_cost, 0)
                    + COALESCE (dx.price, 0)
                ) over ())::numeric, 2) total_cost_all_plots',
            'ROUND((COALESCE (de.price_per_area, 0) + COALESCE ( de.total_fuel_cost * de.price_per_litre, 0 ) + COALESCE (de.amortization_cost, 0) + COALESCE (dx.price, 0))::numeric,2) event_price',
            'COALESCE(max(dtp.treated_area), max(dp.area), de.treated_area, 0)  completed_area',
            'SUM(COALESCE(max(dtp.treated_area), max(dp.area), de.treated_area, 0)) OVER() total_completed_area',
        ];

        return $UserDbDiaryController->getPlotData($rpcParams, $options, $page, $rows, $sort, $order);
    }

    /**
     * Експорт XLS -> Справка по парцел.
     *
     * @api-method exportPlotsReportXLS
     *
     * @param array $rpcParams
     *                         {
     *                         #item string   zp_ekate
     *                         #item string   zp_name
     *                         #item integer  performer
     *                         #item integer  farming
     *                         #item datetime date_from
     *                         #item datetime date_to
     *                         #item integer  farming_year
     *                         #item integer  event_type
     *                         #item integer  machine
     *                         #item integer  attachment
     *                         }
     * @param int|string $page pagination parameter
     * @param int|string $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array
     */
    public function exportPlotsReportXLS(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $filenamePrefix = 'spravka_po_imot';
        if ('getReportByProduct' === $rpcParams['reportName']) {
            if (in_array('seeds', $rpcParams['product_type'])) {
                $filenamePrefix = 'spravka_po_semena';
            } elseif (in_array('fertilizer', $rpcParams['product_type']) || in_array('chemical_treatment', $rpcParams['product_type'])) {
                $filenamePrefix = 'spravka_po_preparati';
            } elseif (in_array('other', $rpcParams['product_type'])) {
                $filenamePrefix = 'spravka_po_drugi_produkti';
            }
        } elseif ('getReportByFuel' === $rpcParams['reportName']) {
            $filenamePrefix = 'spravka_po_gorivo';
        } elseif ('getReportByProduce' === $rpcParams['reportName']) {
            $filenamePrefix = 'spravka_po_dobivi';
        }

        $reportName = $rpcParams['reportName'];
        $results = $this->$reportName($rpcParams, $page, $rows, $sort, $order);

        $filters = [];

        $filters[] = [
            'zp_name' => 'Филтър:',
        ];
        $filters[] = [
            'zp_name' => 'Име на парцел:',
            'complete_date' => $rpcParams['filter']['zpName'],
        ];
        $filters[] = [
            'zp_name' => 'ИСАК номер:',
            'complete_date' => $rpcParams['filter']['isakPrcUin'],
        ];
        $filters[] = [
            'zp_name' => 'Стопанство:',
            'complete_date' => $rpcParams['filter']['farming'],
        ];
        $filters[] = [
            'zp_name' => 'Стопанска година:',
            'complete_date' => $rpcParams['filter']['farmingYear'],
        ];
        $filters[] = [
            'zp_name' => 'Землище:',
            'complete_date' => $rpcParams['filter']['ekate'],
        ];
        $filters[] = [
            'zp_name' => 'Механизатор/Служител:',
            'complete_date' => $rpcParams['filter']['performer'],
        ];
        $filters[] = [
            'zp_name' => 'Период от / до:',
            'complete_date' => $rpcParams['filter']['dateFrom'] . ' / ' . $rpcParams['filter']['dateTo'],
        ];

        $filters[] = [
            'zp_name' => 'Вид обработка:',
            'complete_date' => $rpcParams['filter']['eventType'],
        ];

        $filters[] = [
            'zp_name' => 'Култура:',
            'complete_date' => $rpcParams['zpCrops'],
        ];

        if ('getReportByProduce' === $rpcParams['reportName']) {
            $filters[] = [
                'zp_name' => 'Сорт:',
                'complete_date' => $rpcParams['filter']['sort'],
            ];
        }

        $filters[] = [
            'zp_name' => 'Машина:',
            'complete_date' => $rpcParams['filter']['machine'],
        ];
        $filters[] = [
            'zp_name' => 'Прикачен инвентар:',
            'complete_date' => $rpcParams['filter']['attachment'],
        ];

        if ('getReportByFuel' !== $rpcParams['reportName']) {
            $filters[] = [
                'zp_name' => 'Продукт:',
                'complete_date' => $rpcParams['filter']['product_name'],
            ];
        }

        $filters[] = [];

        $headers = [
            'zp_name' => 'Парцел',
            'isak_prc_uin' => 'ИСАК номер',
            'complete_date' => 'Дата на',
            'performer' => 'Механизатор',
            'machine' => 'Машина',
            'attachment' => 'Прикачен инвентар',
            'zp_area' => 'Обща площ (дка)',
            'farming' => 'Стопанство',
            'year' => 'Година',
            'zp_culture' => 'Култура',
        ];

        if ('getReportByProduce' === $rpcParams['reportName']) {
            $headers['sort'] = 'Сорт';
            $headers['type'] = 'Тип обработка';
            $headers['moto_hours'] = 'Моточасове';
            $headers['completed_area'] = 'Обработена площ (дка)';
            $headers['produce'] = 'Добив';
            $headers['produce_per_dka'] = 'Добив от дка';
        } else {
            $headers['type'] = 'Тип обработка';
            $headers['moto_hours'] = 'Моточасове';
            $headers['completed_area'] = 'Обработена площ (дка)';

            if (isset($results['rows'][0]['fuel_quantity'])) {
                $headers['fuel_quantity'] = 'Общ отчетен разход (л)';
            }

            if (isset($results['rows'][0]['product_name'])) {
                $headers['product_name'] = 'Продукт';
            }
            if (array_key_exists('sort', $results['rows'][0])) {
                $headers['sort'] = 'Сорт';
            }
            if (isset($results['rows'][0]['product_dose'])) {
                $headers['product_dose'] = 'Разходна норма';
            }

            if (isset($results['rows'][0]['product_measure'])) {
                $headers['product_measure'] = 'Мерна единица';
            }

            if (array_key_exists('product_consumed', $results['rows'][0])) {
                $headers['product_consumed'] = 'Употребено количество';
            }

            if (array_key_exists('single_price', $results['rows'][0])) {
                $headers['single_price'] = 'Единична стойност';
            }

            $headers['event_price'] = 'Обща стойност в пари';
        }

        foreach ($results['footer'] as $key => $footer) {
            $results['footer'][$key]['attachment'] = str_replace('<b>', '', $footer['attachment']);
            $results['footer'][$key]['attachment'] = str_replace('</b>', '', $results['footer'][$key]['attachment']);
        }

        $date = date('Y-m-d-H-i-s');
        $path = 'files/uploads/blanks/';
        $name = $filenamePrefix . '_' . $this->User->GroupID . '_' . $date;

        $filename = $path . $name . '.xlsx';

        $exportExcelDoc = new ExportToExcelClass();
        $exportExcelDoc->export($results['rows'], $headers, $results['footer'], count($filters));
        $exportExcelDoc->prependData($filters, $headers);
        $exportExcelDoc->saveFile($filename);

        $return = [];
        $return['path'] = $path . $name . '.xlsx';
        $return['name'] = $name . '.xlsx';

        return $return;
    }

    private function getConfigs($options = [])
    {
        $configs_array = [];
        $UserDbController = new UserDbController($this->User->Database);
        // GET CONFIGS FOR GRID INFO
        $options['tablename'] = $UserDbController->DbHandler->tableDiaryConfigs;
        $configs_results = $UserDbController->getItemsByParams($options);
        if (empty($configs_results)) {
            return $configs_array;
        }
        foreach ($configs_results as $configs_result) {
            $configs_array[$configs_result['id']] = $configs_result;
        }

        return $configs_array;
    }

    private function formatMotoHours($motoHours)
    {
        while ($motoHours[2] >= 60) {
            $motoHours[2] -= 60;
            $motoHours[1]++;
        }
        while ($motoHours[1] >= 60) {
            $motoHours[1] -= 60;
            $motoHours[0]++;
        }

        $motoHours[0] = str_pad($motoHours[0], 2, '0', STR_PAD_LEFT);
        $motoHours[1] = str_pad($motoHours[1], 2, '0', STR_PAD_LEFT);
        $motoHours[2] = str_pad($motoHours[2], 2, '0', STR_PAD_LEFT);

        return $motoHours;
    }

    private function getEventProductTreatmentPrice($eventId)
    {
        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'return' => ['dp.price_per_area'],
            'tablename' => $UserDbController->DbHandler->tableDiaryProducts . ' dp',
            'where' => [
                'event_id' => ['column' => 'event_id', 'compare' => '=', 'value' => $eventId],
            ],
        ];

        $result = $UserDbController->getItemsByParams($options);
        $result = array_shift($result);

        return $result['price_per_area'];
    }
}
