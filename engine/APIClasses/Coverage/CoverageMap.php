<?php

namespace TF\Engine\APIClasses\Coverage;

use Prado\Web\Services\TRpcApiProvider;
use TF\Application\Common\Config;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbCoverage\UserDbCoverageController;
use TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Coverage Map related functionality.
 *
 * @rpc-module Coverage
 *
 * @rpc-service-id coverage-map
 */
class CoverageMap extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'initMap' => ['method' => [$this, 'initMap']],
            'initAltitudeMap' => ['method' => [$this, 'initAltitudeMap']],
        ];
    }

    /**
     * Returns the map data, according to filter criteria.
     *
     * @api-method initMap
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer zp_year
     *                         #item integer plot_id
     *                         #item integer event_id
     *                         }
     *
     * @return array
     */
    public function initMap($rpcParams)
    {
        // set default return
        $return = [];
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbCoverageController = new UserDbCoverageController($this->User->Database);
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);
        $UsersController = new UsersController('Users');

        // assign main variables
        $zp_year = $rpcParams['zp_year'];
        $plot_id = $rpcParams['plot_id'];
        $event_id = $rpcParams['event_id'];

        $return['area_legend'] = $this->generateAreaLegend($zp_farming, $zp_year, $plot_id, $event_id);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableCoverageData,
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $event_id],
            ],
        ];
        $layer_results = $UserDbController->getItemsByParams($options);

        // tablename checks
        if (1 != count($layer_results)) {
            return $return;
        }

        $tableName = $layer_results[0]['tablename'];

        if (!$tableName) {
            return $return;
        }

        $tableExists = $UserDbController->getTableNameExist($tableName);

        if (!$tableExists) {
            return $return;
        }
        // end of tablename checks

        $options = [
            'return' => ['p.geom', 'p.gid'],
            'tablename' => $tableName,
        ];

        $sqlString = $UserDbPlotsController->getPlotData($options, false, true);
        $query = "({$sqlString}) as subquery using unique gid";

        $data = [];

        $border_color = $layer_results[0]['border_color'];
        $layer_color = $layer_results[0]['color'];
        $data['layername'] = 'topic_cov_layer';
        $data['maxextent'] = $layer_results[0]['extent'];
        $data['host'] = DEFAULT_DB_HOST;
        $data['dbname'] = $this->User->Database;
        $data['username'] = DEFAULT_DB_USERNAME;
        $data['password'] = DEFAULT_DB_PASSWORD;
        $data['port'] = DEFAULT_DB_PORT;
        $data['query'] = $query;
        $data['gid'] = 'gid';
        $data['transparency'] = $layer_results[0]['transparency'];
        $data['classes'][0]['name'] = 'topic_cov_layer_1';
        $data['classes'][0]['border_color'] = hexdec(substr($border_color, 0, 2)) . ' ' . hexdec(substr($border_color, 2, 2)) . ' ' . hexdec(substr($border_color, 4, 2));
        $data['classes'][0]['color'] = $layer_results[0]['border_only'] ? false : hexdec(substr($layer_color, 0, 2)) . ' ' . hexdec(substr($layer_color, 2, 2)) . ' ' . hexdec(substr($layer_color, 4, 2));

        $ltext = $UsersController->StringHelper->loadTemplate($GLOBALS['Templates'][2]['template'], $data);

        $options = [
            'tablename' => $tableName,
            'return' => [
                'ST_Intersection(t2.geom,t1.geom) as geom', 't1.gid',
            ],
        ];

        $query = '(SELECT gid, geom FROM ' . $tableName . '_intersection) as subquery using unique gid';

        $color1 = '000000';
        $color2 = 'ff0000';
        $data['layername'] = 'topic_cov_layer_1';
        $data['maxextent'] = $layer_results[0]['extent'];
        $data['host'] = DEFAULT_DB_HOST;
        $data['dbname'] = $this->User->Database;
        $data['username'] = DEFAULT_DB_USERNAME;
        $data['password'] = DEFAULT_DB_PASSWORD;
        $data['port'] = DEFAULT_DB_PORT;
        $data['query'] = $query;
        $data['gid'] = 'gid';
        $data['transparency'] = '100';
        $data['classes'][0]['name'] = 'topic_cov_layer_1';
        $data['classes'][0]['border_color'] = hexdec(substr($color1, 0, 2)) . ' ' . hexdec(substr($color1, 2, 2)) . ' ' . hexdec(substr($color1, 4, 2));
        $data['classes'][0]['color'] = hexdec(substr($color2, 0, 2)) . ' ' . hexdec(substr($color2, 2, 2)) . ' ' . hexdec(substr($color2, 4, 2));

        // adding the red color for legend
        $return['color_array'][] = ['color' => $color2, 'name' => 'Застъпена следа', 'iconCls' => 'no-background no-padding'];

        $ltext .= $UsersController->StringHelper->loadTemplate($GLOBALS['Templates'][2]['template'], $data);

        @unlink(WMS_MAP_PATH . $this->User->GroupID . '_layer_cov.map');
        $fp = fopen(WMS_MAP_PATH . $this->User->GroupID . '_layer_cov.map', 'w');
        $res = fwrite($fp, $ltext);
        fclose($fp);

        // converting max extent
        $maxExtent = $layer_results[0]['extent'];
        $maxExtent = str_replace(' ', ', ', $maxExtent);

        $return['layers'] = [
            0 => ['extent' => $maxExtent, 'name' => 'topic_cov_layer'],
            1 => ['extent' => $maxExtent, 'name' => 'topic_cov_layer_1'],
        ];
        // layer names for removal
        $return['remove_layers'] = ['topic_cov_layer', 'topic_cov_layer_1'];

        return $return;
    }

    /**
     * Initiate altitude map for the selected event.
     *
     * @api-method initAltitudeMap
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer zp_farming
     *                         #item integer zp_year
     *                         #item integer event_id
     *                         }
     *
     * @return array
     */
    public function initAltitudeMap($rpcParams)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $LayersController = new LayersController('Layers');
        $UsersController = new UsersController('Users');
        // set default return
        $return = [];

        // assing main variables
        $zp_farming = $rpcParams['zp_farming'];
        $zp_year = $rpcParams['zp_year'];
        $event_id = $rpcParams['event_id'];
        $value_range = Config::ALTITUDE_MAP_VALUE_RANGE;

        $options = [
            'tablename' => $UserDbController->DbHandler->tableCoverageData,
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $event_id],
            ],
        ];
        $layer_results = $UserDbController->getItemsByParams($options);

        // tablename checks
        if (1 != count($layer_results)) {
            return $return;
        }

        $tableName = $layer_results[0]['tablename'];

        if (!$tableName) {
            return $return;
        }

        $tableExists = $UserDbController->getTableNameExist($tableName);

        if (!$tableExists) {
            return $return;
        }
        // end of tablename checks

        $options = [
            'return' => ['min(height)', 'max(height)'],
            'tablename' => $tableName,
        ];
        $range_results = $UserDbController->getItemsByParams($options, false, false);

        $min_range = floor(round($range_results[0]['min']) / $value_range) * $value_range;
        $max_range = ceil(round($range_results[0]['max']) / $value_range) * $value_range;
        $max_iterations = ($max_range - $min_range) / $value_range;

        // generate color array
        $colorArray = $UsersController->StringHelper->generateColorArray(Config::ALTITUDE_MAP_START_COLOR, $max_iterations + 1);

        for ($j = 1; $j <= $max_iterations; $j++) {
            $color1 = '000000';
            $color2 = $colorArray[$j + 1];

            $arrClass[$j]['display_label'] = 0;
            $arrClass[$j]['name'] = $min_range + $j * $value_range;

            $exp_min_range = $min_range + ($j - 1) * $value_range;
            $exp_max_range = $min_range + $j * $value_range;
            $arrClass[$j]['expression_numeric'] = true;
            $arrClass[$j]['expression'] = '([height] >= ' . $exp_min_range . ' AND [height] < ' . $exp_max_range . ')';
            $arrClass[$j]['border_color'] = hexdec(substr($color1, 0, 2)) . ' ' . hexdec(substr($color1, 2, 2)) . ' ' . hexdec(substr($color1, 4, 2));
            $arrClass[$j]['color'] = hexdec(substr($color2, 0, 2)) . ' ' . hexdec(substr($color2, 2, 2)) . ' ' . hexdec(substr($color2, 4, 2));

            $return['color_array'][] = ['color' => $color2, 'name' => $exp_min_range . ' - ' . $exp_max_range . ' метра', 'iconCls' => 'no-background no-padding'];
        }

        // the excluded values
        $color1 = '000000';
        $color2 = $LayersController->StringHelper->randomColorCode();

        $arrClass[$j]['display_label'] = 0;
        $arrClass[$j]['name'] = $min_range + $j * $value_range;

        $arrClass[$j]['expression'] = '';
        $arrClass[$j]['border_color'] = hexdec(substr($color1, 0, 2)) . ' ' . hexdec(substr($color1, 2, 2)) . ' ' . hexdec(substr($color1, 4, 2));
        $arrClass[$j]['color'] = hexdec(substr($color2, 0, 2)) . ' ' . hexdec(substr($color2, 2, 2)) . ' ' . hexdec(substr($color2, 4, 2));
        // end of excluded values

        $color1 = '000000';
        $color2 = 'ff0000';
        $data['layername'] = 'topic_cov_layer';
        $data['maxextent'] = $layer_results[0]['extent'];
        $data['host'] = DEFAULT_DB_HOST;
        $data['dbname'] = $this->User->Database;
        $data['username'] = DEFAULT_DB_USERNAME;
        $data['password'] = DEFAULT_DB_PASSWORD;
        $data['port'] = DEFAULT_DB_PORT;
        $data['query'] = $tableName;
        $data['gid'] = 'id';
        $data['transparency'] = '100';
        $data['classitem'] = 'height';
        $data['classes'] = $arrClass;
        $ltext = $LayersController->StringHelper->loadTemplate($GLOBALS['Templates'][3]['template'], $data);

        @unlink(WMS_MAP_PATH . $this->User->GroupID . '_layer_cov.map');
        $fp = fopen(WMS_MAP_PATH . $this->User->GroupID . '_layer_cov.map', 'w');
        $res = fwrite($fp, $ltext);
        fclose($fp);

        // converting max extent
        $maxExtent = $layer_results[0]['extent'];
        $maxExtent = str_replace(' ', ', ', $maxExtent);

        $return['layers'] = [
            0 => ['extent' => $maxExtent, 'name' => 'topic_cov_layer'],
        ];
        // layer names for removal
        $return['remove_layers'] = ['topic_cov_layer', 'topic_cov_layer_1'];

        return $return;
    }

    private function generateAreaLegend($zp_farming, $zp_year, $plot_id, $event_id)
    {
        $zp_flag = false;
        $event_flag = false;
        $return = [];

        $LayersController = new LayersController('Layers');
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbCoverageController = new UserDbCoverageController($this->User->Database);

        // check if info is correct for event and get zp tablename
        if ($event_id && (int) $event_id && 0 != $event_id) {
            $options = [
                'tablename' => $UserDbController->DbHandler->tableCoverageData,
                'where' => [
                    'id' => ['column' => 'id', 'compare' => '=', 'value' => $event_id],
                ],
            ];
            $layer_results = $UserDbController->getItemsByParams($options);

            $tableExists = $UserDbController->getTableNameExist($layer_results[0]['tablename']);

            if ($tableExists) {
                $event_tablename = $layer_results[0]['tablename'];
                $event_flag = true;
            }
        }

        // check if info is correct for farming
        if ($zp_farming && $zp_year && (int) $zp_farming && (int) $zp_year && 0 != $zp_farming && 0 != $zp_year) {
            // get ZP tablename data
            $options = [
                'where' => [
                    'group_id' => ['column' => 'group_id', 'compare' => '=', 'prefix' => 't', 'value' => $this->User->GroupID],
                    'farming' => ['column' => 'farming', 'compare' => '=', 'prefix' => 't', 'value' => $zp_farming],
                    'year' => ['column' => 'year', 'compare' => '=', 'prefix' => 't', 'value' => $zp_year],
                    'layer_type' => ['column' => 'layer_type', 'compare' => '=', 'prefix' => 't', 'value' => 1],
                ],
            ];
            $results = $LayersController->getLayers($options, false, false);

            $tableExists = $UserDbController->getTableNameExist($results[0]['table_name']);

            if ($tableExists) {
                $zp_tablename = $results[0]['table_name'];
                $zp_flag = true;
            }
        }

        // get ZP area if data is correct
        if ($zp_flag) {
            // get ZP area
            $options = [
                'tablename' => $zp_tablename,
                'return' => ['SUM(St_Area(geom)) as area'],
                'where' => [
                    'plot_id' => ['column' => 'id', 'compare' => '=', 'value' => $plot_id],
                ],
            ];
            $zp_area_results = $UserDbController->getItemsByParams($options, false, false);
            $zp_area = number_format($zp_area_results[0]['area'] / 1000, 3);
        } else {
            $zp_area = 0;
        }

        $event_area = !empty($event_area) ?: 0;
        $cov_overlap_area = !empty($cov_overlap_area) ?: 0;
        $overlap_percent = !empty($overlap_percent) ?: 0;

        if ($zp_flag && $event_flag) {
            $options = [
                'where' => [
                    'plot_id' => ['column' => 'id', 'compare' => '=', 'prefix' => 'zp', 'value' => $plot_id],
                ],
            ];
            $cov_zp_intersect_result = $UserDbCoverageController->getCoverageZPAreaDifference($zp_tablename, $event_tablename, $options);
            if ($cov_zp_intersect_result) {
                $out_of_zp_area = number_format($cov_zp_intersect_result / 1000, 3);
                $missed_area = ($zp_area - ($event_area - $cov_overlap_area - ($cov_zp_intersect_result / 1000)));
                $missed_area = number_format($missed_area, 3);
            } else {
                $out_of_zp_area = $event_area;
                $missed_area = $zp_area;
            }
        } else {
            $out_of_zp_area = $event_area;
            $missed_area = $zp_area;
        }

        return [
            0 => ['name' => 'Обща следа', 'area' => $event_area],
            1 => ['name' => 'Застъпена следа', 'area' => $cov_overlap_area . ' (' . $overlap_percent . '%)'],
            2 => ['name' => 'Парцел', 'area' => $zp_area],
            3 => ['name' => 'Необработена част', 'area' => $missed_area],
            4 => ['name' => 'Следа извън парцела', 'area' => $out_of_zp_area],
        ];
    }
}
