<?php

namespace TF\Engine\APIClasses\Coverage;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbCoverage\UserDbCoverageController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Земеделски парцели.
 *
 * @rpc-module Coverage
 *
 * @rpc-service-id coverage-zp-data-tree
 */
class ZPLayerDataTree extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getZPLayerDataTree']],
            'requestZPLayerData' => ['method' => [$this, 'requestZPLayerData']],
        ];
    }

    /**
     * Returns the tree data.
     *
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer farming
     *                         #item integer year
     *                         #item integer event_id
     *                         }
     *
     * @return array
     */
    public function getZPLayerDataTree($rpcParams)
    {
        if ($this->User->isGuest) {
            return [];
        }

        if (!$rpcParams['farming'] || !$rpcParams['year'] || !$rpcParams['event_id'] || !(int) $rpcParams['farming'] || !(int) $rpcParams['year'] || !(int) $rpcParams['event_id']) {
            return [];
        }

        // init controllers
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbCoverageController = new UserDbCoverageController($this->User->Database);
        $LayersController = new LayersController('Layers');

        // get layer tablename
        $options = [
            'where' => [
                'farming' => ['column' => 'farming', 'compare' => '=', 'prefix' => 't', 'value' => $rpcParams['farming']],
                'year' => ['column' => 'year', 'compare' => '=', 'prefix' => 't', 'value' => $rpcParams['year']],
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'prefix' => 't', 'value' => $this->User->GroupID],
                'layer_type' => ['column' => 'layer_type', 'compare' => '=', 'prefix' => 't', 'value' => 1],
            ],
        ];

        $zp_results = $LayersController->getLayers($options);

        // get coverage tablename
        $options = [
            'tablename' => $UserDbController->DbHandler->tableCoverageData,
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $rpcParams['event_id']],
            ],
        ];

        $cov_results = $UserDbController->getItemsByParams($options);

        if (1 != count($zp_results) || 1 != count($cov_results)) {
            return [];
        }

        $zpTableExists = $UserDbController->getTableNameExist($zp_results[0]['table_name']);
        if (!$zpTableExists) {
            return [];
        }
        // get zp that intersect with our chosen event(coverage)
        $options = [
            'zp_tablename' => $zp_results[0]['table_name'],
            'cov_tablename' => $cov_results[0]['tablename'],
            'return' => ['DISTINCT(zp.id)', 'zp.isak_prc_uin', 'area_name'],
        ];

        $results = $UserDbCoverageController->getZPCoverageIntersectItems($options);
        $resultsCount = count($results);
        $return = [];

        for ($i = 0; $i < $resultsCount; $i++) {
            if (!$results[$i]['area_name']) {
                $name = 'Парцел ' . $results[$i]['id'];
            } else {
                $name = $results[$i]['area_name'];
            }

            $return[] = [
                'id' => $results[$i]['id'],
                'text' => $name,
            ];
        }

        return $return;
    }

    /**
     * Returns the data for the selected layer.
     *
     * @api-method requestZPLayerData
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer event
     *                         #item integer farming
     *                         #item integer year
     *                         #item integer plot
     *                         }
     *
     * @return array
     */
    public function requestZPLayerData($rpcParams)
    {
        // set default return
        $return = [];

        $LayersController = new LayersController('Layers');
        $UsersController = new UsersController('Users');
        $UserDbController = new UserDbController($this->User->Database);

        $event = $rpcParams['event'];
        $farming = $rpcParams['farming'];
        $year = $rpcParams['year'];
        $plot = $rpcParams['plot'];

        $return['area_legend'] = $this->generateAreaLegend($farming, $year, $plot, $event);
        // default remove layer in any case
        // does not matter if object will be or will not be found
        $return['remove_layers'] = ['topic_zp_layer'];

        $options = [
            'where' => [
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'prefix' => 't', 'value' => $this->User->GroupID],
                'farming' => ['column' => 'farming', 'compare' => '=', 'prefix' => 't', 'value' => $farming],
                'year' => ['column' => 'year', 'compare' => '=', 'prefix' => 't', 'value' => $year],
                'layer_type' => ['column' => 'layer_type', 'compare' => '=', 'prefix' => 't', 'value' => 1],
            ],
        ];

        $results = $LayersController->getLayers($options, false, false);
        $layerData = $results[0];

        if (1 != count($results)) {
            return $return;
        }

        $tableExists = $UserDbController->getTableNameExist($results[0]['table_name']);

        if (!$tableExists) {
            return $return;
        }
        $return['remove_layers'] = ['topic_zp_layer'];
        // create the query
        $options = [
            'tablename' => $results[0]['table_name'],
            'return' => ['id', 'geom'],
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $plot],
            ],
        ];

        $sqlString = $UserDbController->getItemsByParams($options, false, true);
        $query = "({$sqlString}) as subquery using unique id";

        $data = [];

        $border_color = $layerData['border_color'];
        $layer_color = $layerData['color'];
        $data['layername'] = 'topic_zp_layer';
        $data['maxextent'] = $layerData['extent'];
        $data['host'] = DEFAULT_DB_HOST;
        $data['dbname'] = $this->User->Database;
        $data['username'] = DEFAULT_DB_USERNAME;
        $data['password'] = DEFAULT_DB_PASSWORD;
        $data['port'] = DEFAULT_DB_PORT;
        $data['query'] = $query;
        $data['gid'] = 'id';
        $data['transparency'] = $layerData['transparency'];
        $data['classes'][0]['name'] = 'topic_zp_layer';
        $data['classes'][0]['border_color'] = hexdec(substr($border_color, 0, 2)) . ' ' . hexdec(substr($border_color, 2, 2)) . ' ' . hexdec(substr($border_color, 4, 2));
        $data['classes'][0]['color'] = $layerData['border_only'] ? false : hexdec(substr($layer_color, 0, 2)) . ' ' . hexdec(substr($layer_color, 2, 2)) . ' ' . hexdec(substr($layer_color, 4, 2));

        $ltext = $UsersController->StringHelper->loadTemplate($GLOBALS['Templates'][2]['template'], $data);

        @unlink(WMS_MAP_PATH . $this->User->GroupID . '_layer_zp.map');
        $fp = fopen(WMS_MAP_PATH . $this->User->GroupID . '_layer_zp.map', 'w');
        $res = fwrite($fp, $ltext);
        fclose($fp);

        $maxExtent = $UserDbController->getZPMaxExtent($results[0]['table_name'], $plot);
        $maxExtent = str_replace('BOX(', '', $maxExtent);
        $maxExtent = str_replace(')', '', $maxExtent);
        $maxExtent = str_replace(' ', ', ', $maxExtent);

        $return['layers'] = [
            0 => ['extent' => $maxExtent, 'name' => 'topic_zp_layer'],
        ];
        // layer names for removal

        return $return;
    }

    private function generateAreaLegend($zp_farming, $zp_year, $plot_id, $event_id)
    {
        $zp_flag = false;
        $event_flag = false;
        $return = [];

        $LayersController = new LayersController('Layers');
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbCoverageController = new UserDbCoverageController($this->User->Database);

        // check if info is correct for event and get zp tablename
        if ($event_id && (int) $event_id && 0 != $event_id) {
            $options = [
                'tablename' => $UserDbController->DbHandler->tableCoverageData,
                'where' => [
                    'id' => ['column' => 'id', 'compare' => '=', 'value' => $event_id],
                ],
            ];
            $layer_results = $UserDbController->getItemsByParams($options);

            $tableExists = $UserDbController->getTableNameExist($layer_results[0]['tablename']);

            if ($tableExists) {
                $event_tablename = $layer_results[0]['tablename'];
                $event_flag = true;
            }
        }

        // check if info is correct for farming
        if ($zp_farming && $zp_year && (int) $zp_farming && (int) $zp_year && 0 != $zp_farming && 0 != $zp_year) {
            // get ZP tablename data
            $options = [
                'where' => [
                    'group_id' => ['column' => 'group_id', 'compare' => '=', 'prefix' => 't', 'value' => $this->User->GroupID],
                    'farming' => ['column' => 'farming', 'compare' => '=', 'prefix' => 't', 'value' => $zp_farming],
                    'year' => ['column' => 'year', 'compare' => '=', 'prefix' => 't', 'value' => $zp_year],
                    'layer_type' => ['column' => 'layer_type', 'compare' => '=', 'prefix' => 't', 'value' => 1],
                ],
            ];
            $results = $LayersController->getLayers($options, false, false);

            $tableExists = $UserDbController->getTableNameExist($results[0]['table_name']);

            if ($tableExists) {
                $zp_tablename = $results[0]['table_name'];
                $zp_flag = true;
            }
        }

        // get ZP area if data is correct
        if ($zp_flag) {
            // get ZP area
            $options = [
                'tablename' => $zp_tablename,
                'return' => ['SUM(St_Area(geom)) as area'],
                'where' => [
                    'plot_id' => ['column' => 'id', 'compare' => '=', 'value' => $plot_id],
                ],
            ];
            $zp_area_results = $UserDbController->getItemsByParams($options, false, false);
            $zp_area = number_format($zp_area_results[0]['area'] / 1000, 3);
        } else {
            $zp_area = 0;
        }

        if ($event_flag) {
            // get event area
            $options = [
                'tablename' => $event_tablename,
                'return' => ['round((SUM(St_Area(geom))/1000)::numeric, 3) as area'],
            ];
            $event_area_results = $UserDbController->getItemsByParams($options, false, false);

            $event_area = (float) $event_area_results[0]['area'];

            // get inner coverage intersection
            $options = [
                'tablename' => $event_tablename,
                'return' => [
                    'SUM(St_Area(ST_Intersection(t2.geom,t1.geom))) / 2 as area',
                ],
            ];
            $cov_intersect_results = $UserDbCoverageController->getCoverageIntersections($options, false, false);
            $cov_overlap_area = number_format($cov_intersect_results[0]['area'] / 1000, 3);
            $overlap_percent = 0;
            if ($event_area > 0) {
                $overlap_percent = number_format($cov_overlap_area / $event_area * 100, 2);
            }
        } else {
            $event_area = 0;
            $cov_overlap_area = 0;
            $overlap_percent = 0;
        }

        if ($zp_flag && $event_flag) {
            $options = [
                'where' => [
                    'plot_id' => ['column' => 'id', 'compare' => '=', 'prefix' => 'zp', 'value' => $plot_id],
                ],
            ];
            $cov_zp_intersect_result = $UserDbCoverageController->getCoverageZPAreaDifference($zp_tablename, $event_tablename, $options);

            $out_of_zp_area = number_format($cov_zp_intersect_result / 1000, 3);

            $missed_area = $UserDbCoverageController->getCoverageZpMissedArea($zp_tablename, $event_tablename, $plot_id);
        } else {
            $out_of_zp_area = $event_area;
            $missed_area = $zp_area;
        }

        return [
            0 => ['name' => 'Обща следа', 'area' => $event_area],
            1 => ['name' => 'Застъпена следа', 'area' => $cov_overlap_area . ' (' . $overlap_percent . '%)'],
            2 => ['name' => 'Парцел', 'area' => $zp_area],
            3 => ['name' => 'Необработена част', 'area' => $missed_area],
            4 => ['name' => 'Следа извън парцела', 'area' => $out_of_zp_area],
        ];
    }
}
