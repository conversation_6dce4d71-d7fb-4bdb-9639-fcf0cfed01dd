<?php

namespace TF\Engine\APIClasses\Coverage;

use Prado\Web\Services\TRpcApiProvider;
use stdClass;
use TF\Application\Common\Config;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Tree 'Земеделски парцели(слоеве)'.
 *
 * @rpc-module Coverage
 *
 * @rpc-service-id coverage-zp-layers-tree
 */
class ZPLayersTree extends TRpcApiProvider
{
    private $module = 'Coverage';
    private $service_id = 'coverage-zp-layers-tree';

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getZPLayersTree']],
            'markZPForEdit' => ['method' => [$this, 'markZPForEdit']],
            'saveZP' => ['method' => [$this, 'saveZP'],
                'validators' => [
                    'rpcParams' => [
                        'editZPID' => 'validateInteger',
                        'color' => 'validateColor',
                        'border_color' => 'validateColor',
                        'transparency' => 'validateInteger',
                        'border_only' => 'validateText',
                        'border_width' => 'validateText',
                        'plot' => 'validateInteger',
                    ],
                ]],
        ];
    }

    /**
     * Get tree data.
     *
     * @api-method read
     *
     * @return array
     */
    public function getZPLayersTree()
    {
        if ($this->User->isGuest || !$this->User->Database) {
            return [];
        }

        // init all needed controllers
        $UserDbController = new UserDbController($this->User->Database);
        $LayersController = new LayersController('Layers');
        $FarmingController = new FarmingController('Farming');
        $farmings = $FarmingController->getUserFarmings();
        $userFarmingIds = array_keys($farmings);

        // get all farmings
        $options = [
            'where' => [
                'farming' => ['column' => 'id', 'compare' => 'IN', 'prefix' => 'f', 'value' => $userFarmingIds],
                'layer_type' => ['column' => 'layer_type', 'compare' => '=', 'prefix' => 't', 'value' => 1],
            ],
            'sort' => 'farming, year',
            'order' => 'asc',
        ];
        $results = $LayersController->getLayers($options);

        $farming_array = [];
        $farming_keys = [];
        $return = [];
        $resultsCount = count($results);
        for ($i = 0; $i < $resultsCount; $i++) {
            $farming = $results[$i]['farming'];
            $year = $results[$i]['year'];

            if (!in_array($farming, $farming_array)) {
                $farming_array[] = $farming;
                $return[] = [
                    'id' => $farming,
                    'text' => $results[$i]['name'],
                    'children' => [],
                    'state' => 'open',
                ];
                $farming_keys[$farming] = count($return) - 1;
            }

            $return[$farming_keys[$farming]]['children'][] = [
                'text' => $GLOBALS['Farming']['years'][$year]['title'],
                'id' => $year,
                'iconCls' => 'no-background',
                'color' => $results[$i]['color'],
            ];
        }

        return $return;
    }

    /**
     * Returns data for editing the selected layer.
     *
     * @api-method markZPForEdit
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer farming
     *                         #item integer year
     *                         }
     *
     * @return array
     */
    public function markZPForEdit($rpcParams)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $LayersController = new LayersController('Layers');

        $options = [
            'tablename' => $UserDbController->DbHandler->tableCoverageData,
            'return' => ['t.*', 'f.name'],
            'where' => [
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'prefix' => 't', 'value' => $this->User->GroupID],
                'farming' => ['column' => 'farming', 'compare' => '=', 'prefix' => 't', 'value' => $rpcParams['farming']],
                'year' => ['column' => 'year', 'compare' => '=', 'prefix' => 't', 'value' => $rpcParams['year']],
                'layer_type' => ['column' => 'layer_type', 'compare' => '=', 'prefix' => 't', 'value' => 1],
            ],
        ];

        $results = $LayersController->getLayers($options, false, false);

        return $results[0];
    }

    /**
     * Saves the edits of the selected layer.
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer editZPID
     *                         #item string  color
     *                         #item string  border_color
     *                         #item integer transparency
     *                         #item boolean border_only
     *                         #item integer plot
     *                         }
     *
     * @throws MTRpcException
     * @throws Exception
     *
     * @return array
     */
    public function saveZP($rpcParams)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $LayersController = new LayersController('Layers');
        $UsersController = new UsersController('Users');

        if ($this->User->isGuest) {
            return [];
        }

        $oldOptions = [
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'prefix' => 't', 'value' => $rpcParams['editZPID']],
            ],
        ];
        $oldValues = $LayersController->getLayers($oldOptions);

        $style = new stdClass();
        $style->color = $rpcParams['color'];
        $style->border_color = $rpcParams['border_color'];
        $style->transparency = 100 - (int)$rpcParams['transparency'];
        $style->border_only = (int) $rpcParams['border_only'];
        $style->border_width = $rpcParams['border_width'] ?? Config::LAYER_BOUNDARY_DEFAULT_BORDER_WIDTH;
        $style->label_name = $rpcParams['label_name'];
        $style->tags = (int) $rpcParams['tags'];
        $style->label_size = $rpcParams['label_size'];
        $jsonStyle = json_encode($style);

        $options = [
            'id' => $rpcParams['editZPID'],
            'mainData' => [
                'style' => $jsonStyle,
            ],
        ];

        $LayersController->editItem($options);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options['mainData'], $oldValues, 'Edit ZP layer colors');

        $options = [];
        $options['database'] = $this->User->Database;
        $options['user_id'] = $this->User->GroupID;
        $LayersController->generateMapFile($options);

        $plot = $rpcParams['plot'];

        // creating the return array
        $return = [
            'layer_name' => 'topic_zp_layer',
            'color' => $rpcParams['color'],
        ];

        // return color and reload layer name in case there is no plot displayed
        if (0 == $plot) {
            return $return;
        }

        $options = [
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'prefix' => 't', 'value' => $rpcParams['editZPID']],
            ],
        ];
        $results = $LayersController->getLayers($options);

        $layerData = $results[0];

        // create the query
        $options = [
            'tablename' => $layerData['table_name'],
            'return' => ['id', 'geom'],
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $plot],
            ],
        ];

        $sqlString = $UserDbController->getItemsByParams($options, false, true);
        $query = "({$sqlString}) as subquery using unique id";

        $data = [];

        $border_color = $style->border_color;
        $layer_color = $style->color;
        $data['layername'] = 'topic_zp_layer';
        $data['maxextent'] = $layerData['extent'];
        $data['host'] = DEFAULT_DB_HOST;
        $data['dbname'] = $this->User->Database;
        $data['username'] = DEFAULT_DB_USERNAME;
        $data['password'] = DEFAULT_DB_PASSWORD;
        $data['port'] = DEFAULT_DB_PORT;
        $data['query'] = $query;
        $data['gid'] = 'id';
        $data['transparency'] = $style->transparency;
        $data['classes'][0]['name'] = 'topic_zp_layer';
        $data['classes'][0]['border_width'] = $style->border_width ?? Config::LAYER_BOUNDARY_DEFAULT_BORDER_WIDTH;
        $data['classes'][0]['border_color'] = hexdec(substr($border_color, 0, 2)) . ' ' . hexdec(substr($border_color, 2, 2)) . ' ' . hexdec(substr($border_color, 4, 2));
        $data['classes'][0]['color'] = $style->border_only ? false : hexdec(substr($layer_color, 0, 2)) . ' ' . hexdec(substr($layer_color, 2, 2)) . ' ' . hexdec(substr($layer_color, 4, 2));

        $ltext = $UsersController->StringHelper->loadTemplate($GLOBALS['Templates'][2]['template'], $data);

        @unlink(WMS_MAP_PATH . $this->User->GroupID . '_layer_zp.map');
        $fp = fopen(WMS_MAP_PATH . $this->User->GroupID . '_layer_zp.map', 'w');
        $res = fwrite($fp, $ltext);
        fclose($fp);

        $maxExtent = $UserDbController->getZPMaxExtent($layerData['table_name'], $plot);
        $maxExtent = str_replace('BOX(', '', $maxExtent);
        $maxExtent = str_replace(')', '', $maxExtent);
        $maxExtent = str_replace(' ', ', ', $maxExtent);

        return $return;
    }
}
