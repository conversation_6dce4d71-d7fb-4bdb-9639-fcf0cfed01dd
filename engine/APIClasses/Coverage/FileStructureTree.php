<?php

namespace TF\Engine\APIClasses\Coverage;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * 'Данни от обработки' tree.
 *
 * @rpc-module Coverage
 *
 * @rpc-service-id coverage-file-structure-tree
 */
class FileStructureTree extends TRpcApiProvider
{
    private $module = 'Coverage';
    private $service_id = 'coverage-file-structure-tree';

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getFileStructureTree']],
            'markForEdit' => ['method' => [$this, 'markForEdit']],
            'saveEvent' => ['method' => [$this, 'saveEvent'],
                'validators' => [
                    'rpcParams' => [
                        'color' => 'validateColor',
                        'border_color' => 'validateColor',
                        'transparency' => 'validateInteger',
                        'border_only' => 'validateText',
                        'event_id' => 'validateInteger',
                    ],
                ]],
        ];
    }

    /**
     * Get the data for the selected file from files tree.
     *
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer file_id
     *                         }
     *
     * @return array
     */
    public function getFileStructureTree($rpcParams)
    {
        if ($this->User->isGuest) {
            return [];
        }

        if (!$rpcParams['file_id'] || !(int) $rpcParams['file_id']) {
            return [];
        }

        // init controllers
        $UserDbController = new UserDbController($this->User->Database);

        // get files data
        $options = [
            'tablename' => $UserDbController->DbHandler->tableCoverageData,
            'where' => [
                'file_id' => ['column' => 'file_id', 'compare' => '=', 'value' => $rpcParams['file_id']],
            ],
        ];
        $results = $UserDbController->getItemsByParams($options, false, false);
        $resultsCount = count($results);

        if (0 == $resultsCount) {
            return [];
        }

        $clients = [];
        $clients_keys = [];
        $clients_farmings = [];
        $clients_farmings_keys = [];
        $farmings_plots = [];
        $farmings_plots_keys = [];
        $return = [];

        for ($i = 0; $i < $resultsCount; $i++) {
            $client = $results[$i]['client'];
            $farming = $results[$i]['farming'];
            $plot = $results[$i]['plot'];
            $event = $results[$i]['event'];
            $ID = $results[$i]['id'];

            if (!in_array($client, $clients)) {
                $clients[] = $client;

                $return[] = [
                    'text' => $client,
                    'state' => 'open',
                    'children' => [],
                ];

                $clients_keys[$client] = count($return) - 1;
                $clients_farmings[$client] = [];
            }

            if (!in_array($farming, $clients_farmings[$client])) {
                $current_client = $clients_keys[$client];

                $clients_farmings[$client][] = $farming;
                $return[$clients_keys[$client]]['children'][] = [
                    'text' => $farming,
                    'state' => 'open',
                    'children' => [],
                ];

                $clients_farmings_keys[$client][$farming] = count($return[$current_client]['children']) - 1;
                $farmings_plots[$clients_keys[$client]][$farming] = [];
            }

            if (!in_array($plot, $farmings_plots[$clients_keys[$client]][$farming])) {
                $current_client = $clients_keys[$client];
                $current_farming = $clients_farmings_keys[$client][$farming];

                $farmings_plots[$clients_keys[$client]][$farming][] = $plot;
                $return[$current_client]['children'][$current_farming]['children'][] = [
                    'text' => $plot,
                    'state' => 'open',
                    'children' => [],
                ];

                $farmings_plots_keys[$client][$farming][$plot] = count($return[$current_client]['children'][$current_farming]['children']) - 1;
            }

            // adding the leafs //todo check $current_client overriding
            $current_client = $clients_keys[$client];
            $current_farming = $clients_farmings_keys[$client][$farming];
            $current_plot = $farmings_plots_keys[$client][$farming][$plot];

            $return[$current_client]['children'][$current_farming]['children'][$current_plot]['children'][] = [
                'id' => $ID,
                'text' => $event,
                'color' => $results[$i]['color'],
                'iconCls' => 'no-background',
            ];
        }

        return $return;
    }

    /**
     * Return selected layer data for edit.
     *
     * @api-method markForEdit
     *
     * @param array $rpcParam
     *                        {
     *                        #item integer id
     *                        }
     *
     * @return array
     */
    public function markForEdit($rpcParam)
    {
        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableCoverageData,
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $rpcParam],
            ],
        ];

        $results = $UserDbController->getItemsByParams($options, false, false);

        return $results[0];
    }

    /**
     * Save edit layer data.
     *
     * @api-method saveEvent
     *
     * @param array $rpcParams
     *                         {
     *                         #item string color
     *                         #item string border_color
     *                         #item string transparency
     *                         #item string border_only
     *                         #item string event_id
     *                         }
     */
    public function saveEvent($rpcParams)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $oldOptions = [
            'tablename' => $UserDbController->DbHandler->tableCoverageData,
            'return' => [
                '*',
            ],
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $rpcParams['event_id']],
            ],
        ];
        $oldValues = $UserDbController->getItemsByParams($oldOptions, false, false);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableCoverageData,
            'mainData' => [
                'color' => $rpcParams['color'],
                'border_color' => $rpcParams['border_color'],
                'transparency' => $rpcParams['transparency'],
                'border_only' => $rpcParams['border_only'] ? true : false,
            ],
            'where' => [
                'id' => $rpcParams['event_id'],
            ],
        ];

        $UserDbController->editItem($options);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options['mainData'], $oldValues, 'Edit coverage data');
    }
}
