<?php

namespace TF\Engine\APIClasses\Coverage;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * 'Обработки' files tree.
 *
 * @rpc-module Coverage
 *
 * @rpc-service-id coverage-files-tree
 */
class FilesTree extends TRpcApiProvider
{
    private $module = 'Coverage';
    private $service_id = 'coverage-files-tree';

    /**
     * Register the available methods.
     *
     * @returnarray  the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getFilesTree']],
            'deleteCoverageFile' => ['method' => [$this, 'deleteCoverageFile']],
            'getModemFiles' => ['method' => [$this, 'getModemFiles']],
            'loadModemFiles' => ['method' => [$this, 'loadModemFiles']],
        ];
    }

    /**
     * Returns files tree data.
     *
     * @api-method read
     *
     * @return array
     */
    public function getFilesTree()
    {
        if ($this->User->isGuest) {
            return [];
        }

        // init controllers
        $UserDbController = new UserDbController($this->User->Database);

        // get files data
        $options = [
            'tablename' => $UserDbController->DbHandler->tableCoverageFiles,
            'sort' => 'upload_date',
            'order' => 'desc',
        ];
        $results = $UserDbController->getItemsByParams($options, false, false);

        $return = [];
        $resultsCount = count($results);
        for ($i = 0; $i < $resultsCount; $i++) {
            $return[] = [
                'id' => $results[$i]['id'],
                'text' => $results[$i]['filename'],
                'iconCls' => 'icon-tree-files',
            ];
        }

        return $return;
    }

    /**
     * Removes the selected file.
     *
     * @api-method deleteCoverageFile
     *
     * @param int $rpcParam
     */
    public function deleteCoverageFile($rpcParam)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $oldOptions = [
            'tablename' => $UserDbController->DbHandler->tableCoverageFiles,
            'return' => [
                '*',
            ],
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $rpcParam],
            ],
        ];
        $oldValues = $UserDbController->getItemsByParams($oldOptions, false, false);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableCoverageFiles,
            'id_string' => $rpcParam,
        ];

        $UserDbController->deleteItemsByParams($options);

        $options = [
            'tablename' => 'su_users_coverage',
            'where' => [
                'item_id' => ['column' => 'item_id', 'compare' => '=', 'value' => $rpcParam],
            ],
        ];
        $UsersController->deleteItemsByParams($options);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options['mainData'], $oldValues, 'delete coverage data');
    }

    public function getModemFiles($rpcParams)
    {
        $UsersController = new UsersController('Users');
        $result = $UsersController->getModemFiles($rpcParams['user_id']);
        $return['total'] = count($result);
        $return['rows'] = $result;

        return $return;
    }

    public function loadModemFiles($rpcParams)
    {
        $UsersController = new UsersController('Users');
        $UserDbController = new UserDbController($this->User->Database);

        $userId = $this->User->UserID;
        $groupId = $this->User->GroupID;
        $database = $this->User->Database;
        $fileName = $rpcParams['filename'];
        $fileExt = pathinfo($fileName, PATHINFO_EXTENSION);

        $destPath = COVERAGE_QUEUE_PATH . $userId;
        $fullDestPath = $destPath . DIRECTORY_SEPARATOR . $fileName;
        if (!file_exists($destPath)) {
            mkdir($destPath, 0777);
        }
        $result = $UsersController->loadModemFiles($fileName, $destPath);
        $options = [
            'tablename' => $UserDbController->DbHandler->tableCoverageFiles,
            'mainData' => [
                'user_id' => $userId,
                'filename' => $fileName,
            ],
        ];
        $userDbItemID = $UserDbController->DbHandler->addItem($options);

        // add upload item to main database for processing
        $options = [
            'tablename' => 'su_users_coverage',
            'mainData' => [
                'filename' => $fileName,
                'group_id' => $groupId,
                'user_id' => $userId,
                'item_id' => $userDbItemID,
                'database' => $database,
            ],
        ];
        $mainDbItemID = $UsersController->addCustomItem($options);
        $newFileName = $destPath . DIRECTORY_SEPARATOR . $mainDbItemID . '.' . $fileExt;

        rename($fullDestPath, $newFileName);

        return $result;
    }
}
