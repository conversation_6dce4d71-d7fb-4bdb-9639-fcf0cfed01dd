<?php

namespace TF\Engine\APIClasses\Warehouse;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\WarehouseModuleClass;

/**
 * @rpc-module Warehouse
 *
 * @rpc-service-id warehouse-history
 */
class HistoryLog extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'loadHistory' => ['method' => [$this, 'loadHistory']],
        ];
    }

    public function loadHistory($params)
    {
        $warehouseModule = new WarehouseModuleClass();
        $warehouses = $warehouseModule->loadHistory($params);

        return $warehouses['result'];
    }
}
