<?php

namespace TF\Engine\APIClasses\Warehouse;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\WarehouseModuleClass;

/**
 * @rpc-module Warehouse
 *
 * @rpc-service-id warehouse-measures
 */
class Measures extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'read']],
            'addMeasure' => ['method' => [$this, 'addMeasure']],
            'editMeasure' => ['method' => [$this, 'editMeasure']],
            'deleteMeasure' => ['method' => [$this, 'deleteMeasure']],
        ];
    }

    /**
     * @param array $filterObj
     * @param int $page
     * @param int $rows
     * @param string $sort
     * @param string $order
     *
     * @return array
     */
    public function read($filterObj, $page = 1, $rows = 30, $sort = '', $order = '')
    {
        $warehouseModule = new WarehouseModuleClass();
        $filterObj['parameters']['page'] = $page;
        $filterObj['parameters']['rows'] = $rows;
        $filterObj['parameters']['sort'] = $sort;
        $filterObj['parameters']['order'] = $order;

        $measures = $warehouseModule->getMeasures($filterObj);

        return [
            'rows' => $measures['result']['items'],
            'total' => $measures['result']['total'],
        ];
    }

    /**
     * @return array
     */
    public function addMeasure($data)
    {
        $warehouseModule = new WarehouseModuleClass();

        return $warehouseModule->addMeasure($data);
    }

    /**
     * @return array
     */
    public function editMeasure($data)
    {
        $warehouseModule = new WarehouseModuleClass();

        return $warehouseModule->editMeasure($data);
    }

    public function deleteMeasure($data)
    {
        $warehouseModule = new WarehouseModuleClass();

        return $warehouseModule->deleteMeasure($data);
    }
}
