<?php

namespace TF\Engine\APIClasses\Warehouse;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\WarehouseModuleClass;

/**
 * @rpc-module Warehouse
 *
 * @rpc-service-id warehouses
 */
class Warehouses extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'read']],
            'getWarehouse' => ['method' => [$this, 'getWarehouse']],
            'getMainWarehouses' => ['method' => [$this, 'getMainWarehouses']],
            'addWarehouse' => ['method' => [$this, 'addWarehouse']],
            'editWarehouse' => ['method' => [$this, 'editWarehouse']],
            'deleteWarehouse' => ['method' => [$this, 'deleteWarehouse']],
            'getAvailableWarehouses' => ['method' => [$this, 'getAvailableWarehouses']],
        ];
    }

    /**
     * @param int $page
     * @param int $rows
     * @param string $sort
     * @param string $order
     */
    public function read($filterObj, $page = 1, $rows = 30, $sort = '', $order = '')
    {
        $warehouseModule = new WarehouseModuleClass();
        $warehouses = $warehouseModule->getWarehousesTree();

        return $warehouses['result'];
    }

    /**
     * @return array
     */
    public function getWarehouse($filterObj)
    {
        $warehouseModule = new WarehouseModuleClass();
        $warehouses = $warehouseModule->getWarehouse($filterObj);

        return $warehouses['result'];
    }

    /**
     * @return array
     */
    public function getMainWarehouses()
    {
        $warehouseModule = new WarehouseModuleClass();
        $warehouses = $warehouseModule->getMainWarehouses();

        return $warehouses['result']['items'];
    }

    /**
     * @param int $page
     * @param int $rows
     * @param string $sort
     * @param string $order
     *
     * @return array
     */
    public function getAvailableWarehouses($filterObj, $page = 1, $rows = 30, $sort = '', $order = '')
    {
        $warehouseModule = new WarehouseModuleClass();
        $filterObj['parameters']['page'] = $page;
        $filterObj['parameters']['rows'] = $rows;
        $filterObj['parameters']['sort'] = $sort;
        $filterObj['parameters']['order'] = $order;

        $warehouses = $warehouseModule->getAvailableWarehouses($filterObj);

        return $warehouses['result'];
    }

    /**
     * @return array
     */
    public function addWarehouse($data)
    {
        $warehouseModule = new WarehouseModuleClass();

        if (empty($data['parent_id'])) {
            unset($data['parent_id']);
        }
        if (empty($data['code'])) {
            unset($data['code']);
        }

        return $warehouseModule->addWarehouse($data);
    }

    /**
     * @return array
     */
    public function editWarehouse($data)
    {
        $warehouseModule = new WarehouseModuleClass();

        if (empty($data['parent_id'])) {
            unset($data['parent_id']);
        }

        return $warehouseModule->editWarehouse($data);
    }

    /**
     * @return array
     */
    public function deleteWarehouse($data)
    {
        $warehouseModule = new WarehouseModuleClass();

        return $warehouseModule->deleteWarehouse($data);
    }
}
