<?php

namespace TF\Engine\APIClasses\Warehouse;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\WarehouseModuleClass;

/**
 * @rpc-module Warehouse
 *
 * @rpc-service-id warehouse-groups
 */
class Groups extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'getItemGroup' => ['method' => [$this, 'getItemGroup']],
            'getItemGroups' => ['method' => [$this, 'getItemGroups']],
            'addItemGroup' => ['method' => [$this, 'addItemGroup']],
            'editItemGroup' => ['method' => [$this, 'editItemGroup']],
            'deleteItemGroup' => ['method' => [$this, 'deleteItemGroup']],

            'getCompanyGroup' => ['method' => [$this, 'getCompanyGroup']],
            'getCompanyGroups' => ['method' => [$this, 'getCompanyGroups']],
            'addCompanyGroup' => ['method' => [$this, 'addCompanyGroup']],
            'editCompanyGroup' => ['method' => [$this, 'editCompanyGroup']],
            'deleteCompanyGroup' => ['method' => [$this, 'deleteCompanyGroup']],
        ];
    }

    /**
     * @param array $filterObj
     *
     * @return array
     */
    public function getItemGroup($filterObj)
    {
        $warehouseModule = new WarehouseModuleClass();

        $groups = $warehouseModule->getItemGroup($filterObj);

        return [
            'rows' => $groups['result']['items'],
            'total' => $groups['result']['total'],
        ];
    }

    /**
     * @param array $filterObj
     * @param int $page
     * @param int $rows
     * @param string $sort
     * @param string $order
     *
     * @return array
     */
    public function getItemGroups($filterObj, $page = 1, $rows = 30, $sort = '', $order = '')
    {
        $warehouseModule = new WarehouseModuleClass();
        $filterObj['parameters']['page'] = $page;
        $filterObj['parameters']['rows'] = $rows;
        $filterObj['parameters']['sort'] = $sort;
        $filterObj['parameters']['order'] = $order;

        $groups = $warehouseModule->getItemGroups($filterObj);

        return [
            'rows' => $groups['result']['items'],
            'total' => $groups['result']['total'],
        ];
    }

    /**
     * @return array
     */
    public function addItemGroup($data)
    {
        $warehouseModule = new WarehouseModuleClass();

        return $warehouseModule->addItemGroup($data);
    }

    /**
     * @return array
     */
    public function editItemGroup($data)
    {
        $warehouseModule = new WarehouseModuleClass();

        return $warehouseModule->editItemGroup($data);
    }

    public function deleteItemGroup($data)
    {
        $warehouseModule = new WarehouseModuleClass();

        return $warehouseModule->deleteItemGroup($data);
    }

    /**
     * @param array $filterObj
     *
     * @return array
     */
    public function getCompanyGroup($filterObj)
    {
        $warehouseModule = new WarehouseModuleClass();

        $groups = $warehouseModule->getCompanyGroup($filterObj);

        return [
            'rows' => $groups['result']['items'],
            'total' => $groups['result']['total'],
        ];
    }

    /**
     * @param array $filterObj
     * @param int $page
     * @param int $rows
     * @param string $sort
     * @param string $order
     *
     * @return array
     */
    public function getCompanyGroups($filterObj, $page = 1, $rows = 30, $sort = '', $order = '')
    {
        $warehouseModule = new WarehouseModuleClass();
        $filterObj['parameters']['page'] = $page;
        $filterObj['parameters']['rows'] = $rows;
        $filterObj['parameters']['sort'] = $sort;
        $filterObj['parameters']['order'] = $order;

        $groups = $warehouseModule->getCompanyGroups($filterObj);

        return [
            'rows' => $groups['result']['items'],
            'total' => $groups['result']['total'],
        ];
    }

    /**
     * @return array
     */
    public function addCompanyGroup($data)
    {
        $warehouseModule = new WarehouseModuleClass();

        $a = $warehouseModule->addCompanyGroup($data);
    }

    /**
     * @return array
     */
    public function editCompanyGroup($data)
    {
        $warehouseModule = new WarehouseModuleClass();

        return $warehouseModule->editCompanyGroup($data);
    }

    public function deleteCompanyGroup($data)
    {
        $warehouseModule = new WarehouseModuleClass();

        return $warehouseModule->deleteCompanyGroup($data);
    }
}
