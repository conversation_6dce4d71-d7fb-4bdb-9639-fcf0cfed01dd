<?php

namespace TF\Engine\APIClasses\Warehouse;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\WarehouseModuleClass;

/**
 * @rpc-module Warehouse
 *
 * @rpc-service-id warehouse-documents
 */
class Documents extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'read']],
            'getDocument' => ['method' => [$this, 'getDocument']],
            'addDocument' => ['method' => [$this, 'addDocument']],
            'addDocumentsInvoice' => ['method' => [$this, 'addDocumentsInvoice']],
            'editDocument' => ['method' => [$this, 'editDocument']],
            'deleteDocument' => ['method' => [$this, 'deleteDocument']],
            'getPdfDocument' => ['method' => [$this, 'getPdfDocument']],
            'removeDocument' => ['method' => [$this, 'removeDocument']],
        ];
    }

    /**
     * @api-method read
     *
     * @param int $page
     * @param int $rows
     * @param string $sort
     * @param string $order
     *
     * @return array
     */
    public function read($filterObj, $page = 1, $rows = 30, $sort = '', $order = '')
    {
        $warehouseModule = new WarehouseModuleClass();
        $filterObj['parameters']['page'] = $page;
        $filterObj['parameters']['rows'] = $rows;
        $filterObj['parameters']['sort'] = $sort;
        $filterObj['parameters']['order'] = $order;

        $documents = $warehouseModule->getDocuments($filterObj);

        return [
            'rows' => $documents['result']['items'],
            'total' => $documents['result']['total'],
        ];
    }

    /**
     * @api-method read
     *
     * @return array
     */
    public function getDocument($filterObj)
    {
        $warehouseModule = new WarehouseModuleClass();

        $document = $warehouseModule->getDocument($filterObj);

        return $document['result'];
    }

    /**
     * @return array
     */
    public function addDocument($data)
    {
        $warehouseModule = new WarehouseModuleClass();

        return $warehouseModule->addDocument($data);
    }

    /**
     * @return array
     */
    public function addDocumentsInvoice($data)
    {
        $warehouseModule = new WarehouseModuleClass();

        return $warehouseModule->getDocumentsInvoice($data);
    }

    /**
     * @return array
     */
    public function editDocument($data)
    {
        $warehouseModule = new WarehouseModuleClass();

        return $warehouseModule->editDocument($data);
    }

    /**
     * @return array
     */
    public function deleteDocument($data)
    {
        $warehouseModule = new WarehouseModuleClass();

        return $warehouseModule->deleteDocument($data);
    }

    /**
     * @return array
     */
    public function getPdfDocument($data)
    {
        $warehouseModule = new WarehouseModuleClass();
        $response = $warehouseModule->getPdfDocument($data);

        $url = getenv('WAREHOUSE_API_URL') . $response['result']['file_path'];
        $newFilePath = PUBLIC_UPLOAD_EXPORT . '/' . $this->User->GroupID;
        $fileName = transferFile($url, $newFilePath, 'wh_');
        $response['result']['file_path'] = PUBLIC_UPLOAD_EXPORT_RELATIVE_PATH . $this->User->GroupID . '/' . $fileName;

        return $response;
    }

    public function removeDocument($data)
    {
        $warehouseModule = new WarehouseModuleClass();
        $response = $warehouseModule->removeDocument($data);

        return $response['result'];
    }
}
