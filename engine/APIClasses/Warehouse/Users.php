<?php

namespace TF\Engine\APIClasses\Warehouse;

use Exception;
use Prado\Web\Services\TRpcApiProvider;
use TF\Application\Entity\User;
use TF\Engine\Kernel\WarehouseModuleClass;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * @rpc-module Warehouse
 *
 * @rpc-service-id warehouse-users
 */
class Users extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'read']],
            'editUserRole' => ['method' => [$this, 'editUserRole']],
        ];
    }

    /**
     * @param array $filterObj
     * @param int $page
     * @param int $rows
     * @param string $sort
     * @param string $order
     *
     * @return array
     */
    public function read($filterObj, $page = 1, $rows = 30, $sort = '', $order = '')
    {
        $warehouseModule = new WarehouseModuleClass();
        $filterObj['parameters']['page'] = $page;
        $filterObj['parameters']['rows'] = $rows;
        $filterObj['parameters']['sort'] = $sort;
        $filterObj['parameters']['order'] = $order;

        $users = $warehouseModule->getUsers($filterObj);

        return [
            'rows' => $users['result']['items'],
            'total' => $users['result']['total'],
        ];
    }

    /**
     * @param array $data
     *
     * @return array
     */
    public function editUserRole($data)
    {
        try {
            $warehouseModule = new WarehouseModuleClass();

            $result = $warehouseModule->editUserRole($data);

            $this->updateWarehouseRights($data);

            return $result['result'];
        } catch (Exception $e) {
            throw $e;
        }
    }

    private function updateWarehouseRights($data)
    {
        $UsersController = new UsersController();

        $user = User::finder()->find(
            'username = :name',
            [':name' => $data['username']]
        );

        if (!$user) {
            throw new Exception('User not found');
        }

        $rights = array_column($UsersController->getUserOrganizationRights($user->getId(), $this->User->getGroupId()), 'right_id');
        $this->revokeWarehouseRights($user);

        foreach ($data['roles'] as $roleName) {
            if (!array_key_exists($roleName, WarehouseModuleClass::$rolesMap)) {
                throw new Exception('Invalid role');
            }

            if (in_array(WarehouseModuleClass::$rolesMap[$roleName], $rights)) {
                continue;
            }

            $UsersController->addUserRights(
                [
                    'user_id' => $user->getId(),
                    'group_id' => $this->User->getGroupId(),
                    'right_id' => WarehouseModuleClass::$rolesMap[$roleName],
                ]
            );
        }
    }

    private function revokeWarehouseRights(User $user): void
    {
        $UsersController = new UsersController();

        foreach (WarehouseModuleClass::$rolesMap as $roleName => $rightId) {
            $UsersController->deleteUserRightsByUserID($user->getId(), $this->User->getGroupId(), $rightId);
        }
    }
}
