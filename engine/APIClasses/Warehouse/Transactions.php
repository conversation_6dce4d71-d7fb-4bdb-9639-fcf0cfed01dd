<?php

namespace TF\Engine\APIClasses\Warehouse;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\WarehouseModuleClass;

/**
 * @rpc-module Warehouse
 *
 * @rpc-service-id warehouse-transactions
 */
class Transactions extends TRpc<PERSON>piProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'read']],
            'createAddTransaction' => ['method' => [$this, 'createAddTransaction']],
            'updateAddTransaction' => ['method' => [$this, 'updateAddTransaction']],
            'createReturnTransaction' => ['method' => [$this, 'createReturnTransaction']],
            'updateReturnTransaction' => ['method' => [$this, 'updateReturnTransaction']],
            'createSubTransaction' => ['method' => [$this, 'createSubTransaction']],
            'updateSubTransaction' => ['method' => [$this, 'updateSubTransaction']],
            'createTransferTransaction' => ['method' => [$this, 'createTransferTransaction']],
            'updateTransferTransaction' => ['method' => [$this, 'updateTransferTransaction']],
            'getReports' => ['method' => [$this, 'getReports']],
            'exportReports' => ['method' => [$this, 'exportReports']],
            'getTransactionsItems' => ['method' => [$this, 'getTransactionsItems']],
            'getTransactionItemsForReturn' => ['method' => [$this, 'getTransactionItemsForReturn']],
            'removeTransaction' => ['method' => [$this, 'removeTransaction']],
        ];
    }

    /**
     * @param int $page
     * @param int $rows
     * @param string $sort
     * @param string $order
     *
     * @return array
     */
    public function read($filterObj, $page = 1, $rows = 30, $sort = '', $order = '')
    {
        $warehouseModule = new WarehouseModuleClass();
        $filterObj['parameters']['page'] = $page;
        $filterObj['parameters']['rows'] = $rows;
        $filterObj['parameters']['sort'] = $sort;
        $filterObj['parameters']['order'] = $order;

        $transactions = $warehouseModule->getTransactions($filterObj);

        return [
            'rows' => $transactions['result']['items'],
            'total' => $transactions['result']['total'],
        ];
    }

    /**
     * @return array
     */
    public function createAddTransaction($data)
    {
        $warehouseModule = new WarehouseModuleClass();
        $result = $warehouseModule->createAddTransaction($data);

        return $result['result'];
    }

    /**
     * @return array
     */
    public function updateAddTransaction($data)
    {
        $warehouseModule = new WarehouseModuleClass();
        $result = $warehouseModule->updateAddTransaction($data);

        return $result['result'];
    }

    public function createSubTransaction($data)
    {
        $warehouseModule = new WarehouseModuleClass();
        $result = $warehouseModule->createSubTransaction($data);

        return $result['result'];
    }

    /**
     * @return array
     */
    public function updateSubTransaction($data)
    {
        $warehouseModule = new WarehouseModuleClass();
        $result = $warehouseModule->updateSubTransaction($data);

        return $result['result'];
    }

    /**
     * @return array
     */
    public function createTransferTransaction($data)
    {
        $warehouseModule = new WarehouseModuleClass();
        $result = $warehouseModule->createTransferTransaction($data);

        return $result['result'];
    }

    /**
     * @return array
     */
    public function updateTransferTransaction($data)
    {
        $warehouseModule = new WarehouseModuleClass();
        $result = $warehouseModule->updateTransferTransaction($data);

        return $result['result'];
    }

    /**
     * @param int $page
     * @param int $rows
     * @param string $sort
     * @param string $order
     *
     * @return array
     */
    public function getTransactionsItems($filterObj, $page = 1, $rows = 30, $sort = '', $order = '')
    {
        $warehouseModule = new WarehouseModuleClass();
        $filterObj['parameters']['page'] = $page;
        $filterObj['parameters']['rows'] = $rows;
        $filterObj['parameters']['sort'] = $sort;
        $filterObj['parameters']['order'] = $order;

        $items = $warehouseModule->getTransactionItems($filterObj);

        return [
            'rows' => $items['result']['items'],
            'total' => $items['result']['total'],
        ];
    }

    /**
     * @param int $page
     * @param int $rows
     * @param string $sort
     * @param string $order
     *
     * @return array
     */
    public function getTransactionItemsForReturn($filterObj, $page = 1, $rows = 30, $sort = '', $order = '')
    {
        $warehouseModule = new WarehouseModuleClass();
        $filterObj['parameters']['page'] = $page;
        $filterObj['parameters']['rows'] = $rows;
        $filterObj['parameters']['sort'] = $sort;
        $filterObj['parameters']['order'] = $order;

        $items = $warehouseModule->getTransactionItemsForReturn($filterObj);

        return [
            'rows' => $items['result']['items'],
            'total' => $items['result']['total'],
        ];
    }

    /**
     * @param int $page
     * @param int $rows
     * @param string $sort
     * @param string $order
     *
     * @return array
     */
    public function getReports($filterObj, $page = 1, $rows = 30, $sort = '', $order = '')
    {
        $warehouseModule = new WarehouseModuleClass();
        $filterObj['parameters']['page'] = $page;
        $filterObj['parameters']['rows'] = $rows;
        $filterObj['parameters']['sort'] = $sort;
        $filterObj['parameters']['order'] = $order;
        $items = $warehouseModule->getReports($filterObj);

        return [
            'rows' => $items['result']['items'],
            'amounts' => $items['result']['amounts'],
            'total' => $items['result']['total'],
        ];
    }

    /**
     * @return array
     */
    public function exportReports($filterObj)
    {
        $warehouseModule = new WarehouseModuleClass();
        $response = $warehouseModule->exportReports($filterObj);

        $url = getenv('WAREHOUSE_API_URL') . $response['result']['file_path'];
        $newFilePath = PUBLIC_UPLOAD_EXPORT . '/' . $this->User->GroupID;
        $fileName = transferFile($url, $newFilePath, 'wh_');
        $response['result']['file_path'] = PUBLIC_UPLOAD_EXPORT_RELATIVE_PATH . $this->User->GroupID . '/' . $fileName;

        return $response;
    }

    public function removeTransaction($data)
    {
        $warehouseModule = new WarehouseModuleClass();
        $result = $warehouseModule->removeTransaction($data);

        return $result['result'];
    }
}
