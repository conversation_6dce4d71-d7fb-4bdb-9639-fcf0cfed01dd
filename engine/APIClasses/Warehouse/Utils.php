<?php

namespace TF\Engine\APIClasses\Warehouse;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\WarehouseModuleClass;

/**
 * @rpc-module Warehouse
 *
 * @rpc-service-id warehouse-utils
 */
class Utils extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'getDocumentNumber' => ['method' => [$this, 'getDocumentNumber']],
        ];
    }

    /**
     * @param int $page
     * @param int $rows
     * @param string $sort
     * @param string $order
     *
     * @return array
     */
    public function getDocumentNumber($filterObj, $page = 1, $rows = 30, $sort = '', $order = '')
    {
        $warehouseModule = new WarehouseModuleClass();
        $items = $warehouseModule->getDocumentNumber($filterObj);

        return $items['result'];
    }
}
