<?php

namespace TF\Engine\APIClasses\Warehouse;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\WarehouseModuleClass;
use TF\Engine\Plugins\Core\Farming\FarmingController;

/**
 * @rpc-module Warehouse
 *
 * @rpc-service-id warehouse-companies
 */
class Companies extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'read']],
            'addCompany' => ['method' => [$this, 'addCompany']],
            'editCompany' => ['method' => [$this, 'editCompany']],
            'deleteCompany' => ['method' => [$this, 'deleteCompany']],
            'getCompanyTypes' => ['method' => [$this, 'getCompanyTypes']],
            'getTechnofarmCompanies' => ['method' => [$this, 'getTechnofarmCompanies']],
            'syncCompanies' => ['method' => [$this, 'syncCompanies']],
        ];
    }

    /**
     * @api-method read
     *
     * @param int $page
     * @param int $rows
     * @param string $sort
     * @param string $order
     *
     * @return array
     */
    public function read($filterObj, $page = 1, $rows = 30, $sort = '', $order = '')
    {
        $warehouseModule = new WarehouseModuleClass();
        $filterObj['parameters']['page'] = $page;
        $filterObj['parameters']['rows'] = $rows;
        $filterObj['parameters']['sort'] = $sort;
        $filterObj['parameters']['order'] = $order;

        $contragents = $warehouseModule->getCompanies($filterObj);

        return [
            'rows' => $contragents['result']['items'],
            'total' => $contragents['result']['total'],
        ];
    }

    /**
     * @return array
     */
    public function addCompany($data)
    {
        $warehouseModule = new WarehouseModuleClass();

        return $warehouseModule->addCompany($data);
    }

    /**
     * @return array
     */
    public function editCompany($data)
    {
        $warehouseModule = new WarehouseModuleClass();

        return $warehouseModule->editCompany($data);
    }

    /**
     * @return array
     */
    public function deleteCompany($data)
    {
        $warehouseModule = new WarehouseModuleClass();

        return $warehouseModule->deleteCompany($data);
    }

    /**
     * @api-method getCompanyTypes
     *
     * @param int $page
     * @param int $rows
     * @param string $sort
     * @param string $order
     *
     * @return array
     */
    public function getCompanyTypes($filterObj = [], $page = 1, $rows = 30, $sort = '', $order = '')
    {
        $warehouseModule = new WarehouseModuleClass();
        $filterObj['parameters']['page'] = $page;
        $filterObj['parameters']['rows'] = $rows;
        $filterObj['parameters']['sort'] = $sort;
        $filterObj['parameters']['order'] = $order;
        $contragents = $warehouseModule->getCompanyTypes($filterObj);

        return [
            'rows' => $contragents['result']['items'],
            'total' => $contragents['result']['total'],
        ];
    }

    public function getTechnofarmCompanies($filterObj, $page = 1, $rows = 30, $sort = '', $order = '')
    {
        $FarmingController = new FarmingController('Farming');

        $options = [
            'return' => ['id', 'company', 'bulstat as eik', 'company_address', 'mol'],
            'sort' => $sort,
            'order' => $order,
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'where' => [
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'value' => $this->User->GroupID],
                'company' => ['column' => 'company', 'compare' => 'NOTEMPTY'],
            ],
        ];

        if (!empty($filterObj['criteries']['company_name'])) {
            $options['where']['company'] = ['column' => 'company', 'compare' => '=', 'value' => $filterObj['criteries']['company_name']];
        }

        $result = $FarmingController->getFarmings($options, false, false);

        return [
            'rows' => $result,
            'total' => count($result),
        ];
    }

    public function syncCompanies($companies, $confirmed = false)
    {
        $companyIds = array_map(function ($company) {
            return (string)$company['tf'];
        }, $companies);

        // Check weather the farm exists in warehouse db
        $existFarms = $this->read([
            'criteries' => [
                'fields' => [
                    'tf' => $companyIds,
                ],
            ],
        ]);

        if (!empty($existFarms['rows']) && !$confirmed) {
            $existCompaniesNames = array_map(function ($company) {
                return $company['name'];
            }, $existFarms['rows']);

            return [
                'exist' => true,
                'existCompaniesNames' => $existCompaniesNames,
            ];
        }

        foreach ($existFarms['rows'] as $existFarm) {
            foreach ($companies as &$company) {
                if (isset($existFarm['fields']['tf'], $company['tf']) && $company['tf'] === $existFarm['fields']['tf']) {
                    $company['id'] = $existFarm['id'];
                }
            }
        }

        $warehouseModule = new WarehouseModuleClass();

        return $warehouseModule->bulkInsertOrUpdateCompanies($companies);
    }
}
