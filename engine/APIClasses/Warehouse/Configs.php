<?php

use TF\Engine\Kernel\WarehouseModuleClass;

/**
 * @rpc-module Warehouse
 *
 * @rpc-service-id warehouse-config
 */
class Configs extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'getWarehouseConfigParams' => ['method' => [$this, 'getWarehouseConfigParams']],
            'editWarehouseConfigParams' => ['method' => [$this, 'editWarehouseConfigParams']],
        ];
    }

    public function getWarehouseConfigParams()
    {
        $warehouseModule = new WarehouseModuleClass();
        $warehouses = $warehouseModule->getWarehouseConfigParams();

        return $warehouses['result'];
    }

    public function editWarehouseConfigParams($data)
    {
        $warehouseModule = new WarehouseModuleClass();
        $warehouses = $warehouseModule->editWarehouseConfigParams($data);

        return $warehouses['result'];
    }
}
