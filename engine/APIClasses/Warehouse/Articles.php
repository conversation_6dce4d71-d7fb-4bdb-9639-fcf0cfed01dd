<?php

namespace TF\Engine\APIClasses\Warehouse;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\WarehouseModuleClass;

/**
 * @rpc-module Warehouse
 *
 * @rpc-service-id warehouse-articles
 */
class Articles extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'getArticles' => ['method' => [$this, 'getArticles']],
        ];
    }

    /**
     * @param int $page
     * @param int $rows
     * @param string $sort
     * @param string $order
     *
     * @return array
     */
    public function getArticles($filterObj, $page = 1, $rows = 30, $sort = '', $order = '')
    {
        $warehouseModule = new WarehouseModuleClass();
        $filterObj['parameters']['page'] = $page;
        $filterObj['parameters']['rows'] = $rows;
        $filterObj['parameters']['sort'] = $sort;
        $filterObj['parameters']['order'] = $order;

        $items = $warehouseModule->getArticles($filterObj);

        return [
            'rows' => $items['result']['items'],
            'total' => $items['result']['total'],
        ];
    }
}
