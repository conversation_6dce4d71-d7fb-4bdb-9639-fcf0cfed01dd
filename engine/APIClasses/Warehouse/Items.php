<?php

namespace TF\Engine\APIClasses\Warehouse;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\WarehouseModuleClass;

/**
 * @rpc-module Warehouse
 *
 * @rpc-service-id warehouse-items
 */
class Items extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'read']],
            'addItem' => ['method' => [$this, 'addItem']],
            'editItem' => ['method' => [$this, 'editItem']],
            'deleteItem' => ['method' => [$this, 'deleteItem']],
        ];
    }

    /**
     * @param int $page
     * @param int $rows
     * @param string $sort
     * @param string $order
     *
     * @return array
     */
    public function read($filterObj, $page = 1, $rows = 30, $sort = '', $order = '')
    {
        $warehouseModule = new WarehouseModuleClass();
        $filterObj['parameters']['page'] = $page;
        $filterObj['parameters']['rows'] = $rows;
        $filterObj['parameters']['sort'] = $sort;
        $filterObj['parameters']['order'] = $order;

        $items = $warehouseModule->getItems($filterObj);

        return [
            'rows' => $items['result']['items'],
            'total' => $items['result']['total'],
        ];
    }

    /**
     * @return array
     */
    public function addItem($data)
    {
        $warehouseModule = new WarehouseModuleClass();
        if (empty($data['code'])) {
            unset($data['code']);
        }

        return $warehouseModule->addItem($data);
    }

    /**
     * @return array
     */
    public function editItem($data)
    {
        $warehouseModule = new WarehouseModuleClass();

        unset($data['code']);

        return $warehouseModule->editItem($data);
    }

    /**
     * @return array
     */
    public function deleteItem($data)
    {
        $warehouseModule = new WarehouseModuleClass();

        return $warehouseModule->deleteItem($data);
    }
}
