<?php

namespace TF\Engine\APIClasses\Notifications;

use Prado\Web\Services\TJsonRpcProtocol;
use Prado\Web\Services\TRpcApiProvider;
use Prado\Web\Services\TRpcServer;
use TF\Engine\APIClasses\Diary\DiaryConfigsGrid;
use TF\Engine\APIClasses\Plots\ContractsWithOwnerlessPlotsReportGrid;
use TF\Engine\APIClasses\Plots\ExpiringContractsReportGrid;
use TF\Engine\Plugins\Core\Notifications\GlobalNotificationController;
use TF\Engine\Plugins\Core\Notifications\NotificationController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

/**
 * Returns Templates grid information.
 *
 * @rpc-module Notifications
 *
 * @rpc-service-id notifications-grid
 *
 * @property NotificationController $NotificationController
 * @property UserDbController $UserDbController
 */
// Prado::using('Plugins.Core.Notifications.*');
// Prado::using('Plugins.Core.UserDbPlots.*');
// Prado::using('Plugins.Core.Users.*');
// Prado::using('Plugins.Core.Users.conf');
// Prado::using('Plugins.Core.Users.conf');
// Prado::using('APIClasses.Plots.*');
// Prado::using('APIClasses.Diary.DiaryConfigsGrid');
// Prado::using('System.Security.TAuthManager');
// Prado::using('Plugins.Core.GlobalNotification.*');

class NotificationsMainGrid extends TRpcApiProvider
{
    private $module = 'Notifications';
    private $service_id = 'notifications-grid';

    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readTemplatesGrid']],
            'settings' => ['method' => [$this, 'getNotificationSettings']],
            'editNotificationSettings' => ['method' => [$this, 'editNotificationSettings']],
            'getActiveNotClosedGlobalNotificationsByUser' => ['method' => [$this, 'getActiveNotClosedGlobalNotificationsByUser']],
            'closeGlobalNotification' => ['method' => [$this, 'closeGlobalNotification']],
            'getAll' => ['method' => [$this, 'getAll']],
        ];
    }

    /**
     * @api-method settings
     *
     * @return array
     */
    public function getNotificationSettings()
    {
        $alarms = new NotificationController($this->User->Database);
        $getAlarmsSettings = $alarms->getAlarmSettings();

        return [
            'rows' => $getAlarmsSettings,
        ];
    }

    /**
     * @api-method read
     *
     * @return array
     */
    public function readTemplatesGrid()
    {
        $getAlarms = $this->getNotificationSettings();
        $alarmData = $this->getAlarmData($getAlarms);

        return [
            'total' => count($alarmData),
            'rows' => $alarmData,
        ];
    }

    public function editNotificationSettings($rpcParams)
    {
        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableAlarmSettings,
            'where' => [
                'id' => $rpcParams['alert_id'],
            ],
        ];
        if ($rpcParams['alert_interval']) {
            $options['mainData']['alert_interval'] = $rpcParams['alert_interval'];
        }
        if (isset($rpcParams['alert_status'])) {
            $options['mainData']['alert_status'] = $rpcParams['alert_status'];
        }
        if ($rpcParams['last_message']) {
            $options['mainData']['last_message'] = $rpcParams['last_message'];
        }

        return $UserDbController->editItem($options);
    }

    public function getActiveNotClosedGlobalNotificationsByUser()
    {
        $globalNotificationsController = new GlobalNotificationController('GlobalNotification');

        return $globalNotificationsController->getActiveNotClosedByUserId($this->User->userID);
    }

    public function closeGlobalNotification($notificationId)
    {
        $globalNotificationsController = new GlobalNotificationController('GlobalNotification');

        return $globalNotificationsController->close($this->User->userID, $notificationId);
    }

    public function getAllGlobalNotifications()
    {
        $globalNotificationsController = new GlobalNotificationController('GlobalNotification');

        return $globalNotificationsController->getAll();
    }

    private function getAlarmData($alarmSettings)
    {
        $server = new TRpcServer(new TJsonRpcProtocol());
        $data = [];

        foreach ($alarmSettings['rows'] as $alarm) {
            if (!$alarm['alert_status']) {
                continue;
            }
            $result = [];

            switch ($alarm['alert_name']) {
                case 'contract_renew':
                    $result = $this->contractRenew($alarm, $server);

                    break;
                case 'contracts_owners':
                    $result = $this->contractsOwners($alarm, $server);

                    break;
                case 'pending_products':
                    $result = $this->pendingProducts($alarm, $server);

                    break;
            }
            if (!empty($result)) {
                $data[] = $result;
            }
        }

        return $data;
    }

    private function pendingProducts($alarm, $server)
    {
        $diaryConfigsGrid = new DiaryConfigsGrid($server);
        $pendingProducts = $diaryConfigsGrid->getDiaryConfigsGrid(['request_type' => 7, 'status' => 'pending']);
        if (1 === $pendingProducts['total']) {
            $message['notification'] = "<a href='index.php?page=Diary.Home'>Имате " . $pendingProducts['total'] . ' чакащ продукт без въведен тип в модул Агротехника.<br> Моля изберете му тип от Настройки в модул Агротехника -> Чакащи продукти</a>';
        } elseif ($pendingProducts['total'] > 1) {
            $message['notification'] = "<a href='index.php?page=Diary.Home'>Имате " . $pendingProducts['total'] . ' чакащи продукта без въведен тип в модул Агротехника.<br> Моля изберете им тип от Настройки в модул Агротехника -> Чакащи продукти</a>';
        }

        return $message;
    }

    private function contractsOwners($alarm, $server)
    {
        $params = [];
        $params['filters']['report_date'] = date('Y-m-d', strtotime('+' . $alarm['alert_interval'] . ' days'));
        $params['filters']['report_date_from'] = date('Y-m-d');

        $info = $this->checkOwnerlessContracts($server, $params);
        if (!empty($info['plot_id'])) {
            $_SESSION['alert_filters']['owner_less_contracts'] = array_values($info['plot_id']);
        } else {
            if (isset($_SESSION['alert_filters']) && array_key_exists('owner_less_contracts', $_SESSION['alert_filters'])) {
                unset($_SESSION['alert_filters']['owner_less_contracts']);
            }
        }

        $message = 'Имате ' . $info['total'] . ' договор' . ($info['total'] > 1 ? 'a' : '') . ' с ' . $info['total_plots'] . ' парцел' . ($info['total_plots'] > 1 ? 'a' : '') . ' без въведен собственик.';
        $result['notification'] = "<a href='index.php?page=Reports.Home&report=13&to_date=" . $params['filters']['report_date'] . '&report_date_from=' . $params['filters']['report_date_from'] . "'>" . $message . '</a> ';

        return $result;
    }

    private function contractRenew($alarm, $server)
    {
        $info = $this->checkExpiredContracts($server, $alarm['alert_interval']);
        if (!empty($info['contract_id'])) {
            $_SESSION['alert_filters']['expiring_contracts'] = array_values($info['contract_id']);
        } else {
            if (isset($_SESSION['alert_filters']) && array_key_exists('expiring_contracts', $_SESSION['alert_filters'])) {
                unset($_SESSION['alert_filters']['expiring_contracts']);
            }
        }
        if ($info['total'] > 1) {
            $message['notification']
                = "<a href='index.php?page=Contracts.Home&from_alert=1&contract_id="
                . implode(',', $info['contract_id'])
                . "'> Имате " . $info['total']
                . ' договорa, който ще изтекат в периода от ' . date('d.m.Y') . ' до ' . date('d.m.Y', strtotime('+ ' . $alarm['alert_interval'] . 'days')) . '</a>';
            $data = $message;
        } elseif (1 == $info['total']) {
            $message['notification']
                = "<a href='index.php?page=Contracts.Home&from_alert=1&contract_id="
                . implode(',', $info['contract_id'])
                . "'> Имате " . $info['total']
                . ' договор, който ще изтече в периода от ' . date('d.m.Y') . ' до ' . date('d.m.Y', strtotime('+ ' . $alarm['alert_interval'] . 'days')) . '</a>';
            $data = $message;
        }

        return $data;
    }

    private function checkOwnerlessContracts($server, $params)
    {
        $report = new ContractsWithOwnerlessPlotsReportGrid($server);
        $result = $report->getReport($params);
        $contract_ids = [];
        $plots_ids = [];
        $return = [];

        if ($result['total'] > 0) {
            foreach ($result['rows'] as $row) {
                $contract_ids[] = $row['id'];
                $plots_ids[] = $row['plot_id'];
            }
            $return['contract_ids'] = array_unique($contract_ids);
            $return['plots_ids'] = array_unique($plots_ids);
        }

        $return['total'] = count($return['contract_ids']);
        $return['total_plots'] = count($return['plots_ids']);

        return $return;
    }

    private function checkExpiredContracts($server, $interval)
    {
        $params = [];
        $params['filters']['report_date'] = date('Y-m-d', strtotime("+{$interval} days"));
        $params['filters']['report_date_from'] = date('Y-m-d');
        $params['filters']['report_choose_renewed'] = false;

        $expiringContract = new ExpiringContractsReportGrid($server);
        $result = $expiringContract->getExpiringContracts($params);
        $return = [];
        $return['contract_id'] = [];
        if ($result['total'] > 0) {
            foreach ($result['rows'] as $contract) {
                if (!$contract['new_c_info']) {
                    $return['contract_id'][] = $contract['c_id'];
                }
            }
            $return['contract_id'] = array_unique($return['contract_id']);
        }
        $return['total'] = count($return['contract_id']);

        return $return;
    }
}
