{panel:title=Validation types|borderStyle=dashed|borderColor=#eee|titleBGColor=#F7D6C1|bgColor=#FFFFCE}
*validateDigitsOnly(_$param_)* - this methond checks if the field contains only digits from 0 to 9 with leading zeros (ex. 44077077, 00177648 are *valid*, or 445e7 and 0.484 are *invalid*).
*validateNumber(_$param_)* - this method checks if the field contains a valid number, leading zeros are not permitted (ex. 0.77, 12248, 33e+4 etc. are *valid*, 44agef5 and 000.784 are *invalid*).
*validateText(_$param_)* - this method check for any HTML type sintaxis and rejects if any is present( <script>, </script>, <asdasd> etc. are *invalid*).
*validateRequired(_$param_)* - this method checks if $param is equal to empty string and if it's not null. Empty string is *invalid*.
*validateNotNull(_$param_)* - this method checks if $param is equal to null. If it's null then $param is *invalid*. 
{panel}

h2. Using validators with RPC methods}
To define a property and it's validation criteria:
{color:red}It is important that the data format matches the validation criteria format.{color}
h3. To validate this request: 
{code:title=Sample RPC request to prc service with id=plots, rpcapi with id=plots-tree, and rpc method with id=update} 
//@param array params - it's best to use this as the "data" array with values from the Portlets
//@param string someOtherTextParam
//@param float someSimpleNumber
//Those function arguments should be the same in the javascript and the php files.
rpcModuleRequest("plots", "plots-tree", "update", *params*, someOtherTextParam, someSimpleNumber).... 
{code}
{code:title=registerMethod() implementation|borderStyle=solid}
public function registerMethods()
    {
        return array(
            //Methods without validation
        	'read' => array('method' => array($this, 'getPlots')),
        	'load' => array('method' => array($this, 'loadPlotForEdit')),
            //Methods with validation
        	'update' => array('method' => array($this, 'updatePlotValues'),
                            //To trigger the validation there must be a 'validators' array
        					  'validators' => array(
                                 //The array accepts members as arrays or simple key->value pairs
                                 //This example is the _params_ array from the javascript, filled with Portlet data
        					  	'params' => array(
                                    //the keys in the _validators[params]_ array must be the same as the _params_ array from the javascript 
                                    //*multiple validators* can be declared for each key. _comma separated_ (values are split by ',' and trimmed)
        							'ekate' => 'validateDigitsOnly,validateRequired, validateNotNull',
        							'masiv' => 'validateDigitsOnly',
        							'number' => 'validateDigitsOnly',
        							'document_area' => 'validateNumber',
        							'mestnost' => 'validateText',
        							),
        							'someOtherParam' => 'validateNotNull,validateText',
                                    'someSimpleNumber' => 'validateNumber'
        					  	)
        					),
        	'multiEdit' => array('method' => array($this, 'executeMultiEdit'),
        					  'validators' => array(
        					  	'params' => array(
        							'mestnost' => 'validateText',
        							), 
        					  	)
        					)
        );
    }
{code}

h2. To include user friendly error messages
Every layout which will display error messages shoud include:
*<script type="text/javascript" src="lib/js/main/rpc_errors_handler.js"></script>*
This will load the RpcErrorHandler.
Inside the _.done_ method of the RpcRequest promise the received *data* object should be handled with the RpcErrorHandler. 
{code}
rpcModuleRequest("plots", "plots-tree", "update", *params*, someOtherTextParam, someSimpleNumber).done(function(data) {

				if(typeof data.error === 'undefined'){
					jQuery('#plots-tree').tree('reload');
					jQuery('#win-multiedit').window('close');
				} 
				else {
                    //This will display nice error message
					RpcErrorHandler.show(data.error);
					return;	
				}	
			});
{code}