<?php

namespace TF\Engine\APIClasses\Exports;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

/**
 * Class PaymentExports.
 */
class PaymentExports extends TRpcApiProvider
{
    public const UNDERLINED_BANK_TEXT_LENGTH = 55;
    public const IBAN_LENGTH = 22;
    public const BIC_LENGTH = 8;

    public function registerMethods() {}

    /**
     * @return array
     */
    public function transformBankData($bankInfo)
    {
        $name = mb_substr(!empty($bankInfo['name']) ? $bankInfo['name'] : ' ', 0, self::UNDERLINED_BANK_TEXT_LENGTH, 'UTF-8');
        $iban = mb_substr(!empty($bankInfo['iban']) ? $bankInfo['iban'] : '', 0, self::IBAN_LENGTH, 'UTF-8');
        $bic = mb_substr(!empty($bankInfo['bic']) ? $bankInfo['bic'] : '', 0, self::BIC_LENGTH, 'UTF-8');
        $branch = mb_substr(!empty($bankInfo['bank_branch']) ? $bankInfo['bank_branch'] : ' ', 0, self::UNDERLINED_BANK_TEXT_LENGTH, 'UTF-8');
        $address = mb_substr(!empty($bankInfo['bank_branch_address']) ? $bankInfo['bank_branch_address'] : ' ', 0, self::UNDERLINED_BANK_TEXT_LENGTH, 'UTF-8');

        return [
            $name,
            $iban,
            $bic,
            $branch,
            $address,
        ];
    }

    protected function checkIfOwnerIsRecipientWeighingNote($result)
    {
        $recipientNames = preg_split('/\s+/', $result[0]['recipient']);
        $ownerNames = preg_split('/\s+/', $result[0]['owner_names']);
        $recipientCount = count($recipientNames);
        $ownerCount = count($ownerNames);
        for ($i = 0; $i < $recipientCount; $i++) {
            $recipientNames[$i] = strtolower($recipientNames[$i]);
        }

        for ($i = 0; $i < $ownerCount; $i++) {
            $ownerNames[$i] = strtolower($ownerNames[$i]);
        }

        $result = array_diff($recipientNames, $ownerNames);

        return !(count($result) > 0 || (strtolower(trim($result['owner_egn'])) != strtolower(trim($result['recipient_egn']))));
    }

    /**
     * @return mixed|string
     */
    protected function getPaymentSubject($id)
    {
        $UserDbController = new UserDbController($this->User->Database);

        $id = (int) $id;

        $options = [
            'tablename' => $UserDbController->DbHandler->tablePaymentSubjects,
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $id],
            ],
        ];
        $result = $UserDbController->getItemsByParams($options);

        return $result[0]['fulltext'];
    }
}
