<?php

namespace TF\Engine\APIClasses\Hypothecs;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDbHypothecs\UserDbHypothecsController;
use TF\Engine\Plugins\Core\Users\UsersController;

// Prado::using('Plugins.Core.UserDb.*');
// Prado::using('Plugins.Core.Users.*');
// Prado::using('Plugins.Core.UserDbHypothecs.*');

/**
 * HypothecsFilesGrid is responsible for displaying all files for hypothec and related CRUD operations.
 *
 * @rpc-module Hypothecs
 *
 * @rpc-service-id hypothecs-files-grid
 */
class HypothecsFilesGrid extends TRpcApiProvider
{
    private $module = 'Hypothecs';
    private $service_id = 'hypothecs-files-grid';

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getHypothecFiles'],
                'validators' => [
                    'hypothecId' => 'validateStrictInteger',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'delete' => ['method' => [$this, 'deleteHypothecFile'],
                'validators' => [
                    'fileId' => 'validateRequired',
                    'fileName' => 'validateRequired',
                    'groupId' => 'validateRequired',
                    'userId' => 'validateRequired',
                ],
            ],
        ];
    }

    /**
     * Returns list of files for hypothec.
     *
     * @api-method read
     *
     * @param int $page
     * @param int $rows
     * @param ?int $hypothecId
     * @param ?string $sort
     * @param ?string $order
     *
     * @return array {
     *               #item integer total
     *               #item array rows {
     *               #item iteger id
     *               #item string filename
     *               #item string date_text
     *               #item string date
     *               }
     *               }
     */
    public function getHypothecFiles(?int $hypothecId, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $UserDbHypothecsController = new UserDbHypothecsController($this->User->Database);

        if (!$hypothecId) {
            return [
                'rows' => [],
                'total' => 0,
            ];
        }

        $options = [
            'tablename' => $UserDbHypothecsController->DbHandler->tableHypothecsFiles,
            'where' => [
                'hypothec_id' => ['column' => 'hypothec_id', 'compare' => '=', 'value' => $hypothecId],
            ],
        ];

        $counter = $UserDbHypothecsController->getItemsByParams($options, true, false);
        if (0 == $counter[0]['count']) {
            return [
                'rows' => [],
                'total' => 0,
            ];
        }

        $results = $UserDbHypothecsController->getItemsByParams($options, false, false);
        $resultsCount = count($results);
        for ($i = 0; $i < $resultsCount; $i++) {
            $results[$i]['date_text'] = strftime('%d.%m.%Y', strtotime($results[$i]['date']));
        }

        $return['rows'] = $results;
        $return['total'] = $counter[0]['count'];

        return $return;
    }

    /**
     * Deletes file from hypothec.
     *
     * @api-method delete
     *
     * @param int $fileId
     * @param string $fileName
     * @param int $groupId
     * @param int $userId
     */
    public function deleteHypothecFile($fileId, $fileName, $groupId, $userId)
    {
        $UserDbHypothecsController = new UserDbHypothecsController($this->User->Database);
        $UsersController = new UsersController('Users');

        $file_fragments = explode('.', $fileName);
        $ext = end($file_fragments);

        $filePath = HYPOTHECS_FILES_PATH . $groupId . '/' . $userId . '/' . $fileId . '.' . $ext;

        if (file_exists($filePath)) {
            unlink($filePath);
        }

        $options = [
            'tablename' => $UserDbHypothecsController->DbHandler->tableHypothecsFiles,
            'id_string' => $fileId,
        ];

        $UserDbHypothecsController->deleteItemsByParams($options);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['deleted_data' => $options], [], 'Deleting hypothec file');
    }
}
