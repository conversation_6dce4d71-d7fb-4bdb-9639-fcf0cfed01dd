<?php

namespace TF\Engine\APIClasses\Hypothecs;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\Users\UsersController;

// Prado::using('Plugins.Core.UserDb.*');
// Prado::using('Plugins.Core.Users.*');

/**
 * HypothecsCreditors is responsible for displaying all creditors and adding new creditors.
 *
 * @rpc-module Hypothecs
 *
 * @rpc-service-id hypothecs-creditors
 */
class HypothecsCreditors extends TRpcApiProvider
{
    private $module = 'Hypothecs';
    private $service_id = 'hypothecs-creditors';

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'readCombobox' => ['method' => [$this, 'readCombobox']],
            'add' => ['method' => [$this, 'addCreditor'],
                'validators' => [
                    'name' => 'validateRequired',
                ],
            ],
        ];
    }

    /**
     * Returns list of all creditors.
     *
     * @api-method readCombobox
     *
     * @param bool $selected
     * @param bool $recordAll
     *
     * @return array {
     *               #item integer id
     *               #item string name
     *               }
     */
    public function readCombobox($selected, $recordAll)
    {
        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableCreditors,
            'return' => ['id, name'],
            'sort' => 'name',
            'order' => 'ASC',
        ];

        $return = $UserDbController->getItemsByParams($options, false, false);

        if (isset($recordAll) && true == $recordAll) {
            array_unshift($return, [
                'id' => '',
                'name' => 'Всички',
            ]);
        }

        if (isset($selected) && true == $selected) {
            $return[0]['selected'] = true;
        }

        return $return;
    }

    /**
     * Adds new creditor.
     *
     * @api-method add
     *
     * @param string $name
     *
     * @return int
     */
    public function addCreditor($name)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $options = [
            'tablename' => $UserDbController->DbHandler->tableCreditors,
            'mainData' => [
                'name' => $name,
            ],
        ];

        $recordID = $UserDbController->addItem($options);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['new_data' => $options], ['created_id' => $recordID], 'Adding creditor');

        return $recordID;
    }
}
