<?php

namespace TF\Engine\APIClasses\Hypothecs;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbHypothecs\UserDbHypothecsController;
use TF\Engine\Plugins\Core\Users\UsersController;

// Prado::using('Plugins.Core.UserDb.*');
// Prado::using('Plugins.Core.Users.*');
// Prado::using('Plugins.Core.UserDbHypothecs.*');

/**
 * HypothecsPaymentsGrid is responsible for displaying all payments for hypothec and related CRUD operations.
 *
 * @rpc-module Hypothecs
 *
 * @rpc-service-id hypothecs-payments-grid
 */
class HypothecsPaymentsGrid extends TRpcApiProvider
{
    private $module = 'Hypothecs';
    private $service_id = 'hypothecs-payments-grid';

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getHypothecPayments'],
                'validators' => [
                    'hypothecId' => 'validateStrictInteger',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'add' => ['method' => [$this, 'addHypothecPayment'],
                'validators' => [
                    'hypothecId' => 'validateRequired',
                    'amount' => 'validateRequired',
                    'date' => 'validateRequired',
                ],
            ],
            'edit' => ['method' => [$this, 'editHypothecPayment'],
                'validators' => [
                    'paymentId' => 'validateRequired',
                    'amount' => 'validateRequired',
                    'date' => 'validateRequired',
                ],
            ],
            'delete' => ['method' => [$this, 'deleteHypothecPayment'],
                'validators' => [
                    'paymentId' => 'validateRequired',
                ],
            ],
        ];
    }

    /**
     * Returns paymets for hypothec.
     *
     * @api-method read
     *
     * @param int $page
     * @param int $rows
     * @param ?int $hypothecId
     * @param ?string $sort
     * @param ?string $order
     *
     * @return array {
     *               #item integer total
     *               #item array rows {
     *               #item iteger id
     *               #item string amount
     *               #item string date_text
     *               #item string date
     *               #item string comment
     *               }
     *               }
     */
    public function getHypothecPayments(?int $hypothecId, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $UserDbHypothecsController = new UserDbHypothecsController($this->User->Database);

        if (!$hypothecId) {
            return [
                'rows' => [],
                'total' => 0,
            ];
        }

        $options = [
            'tablename' => $UserDbHypothecsController->DbHandler->tableHypothecsPayments,
            'where' => [
                'hypothec_id' => ['column' => 'hypothec_id', 'compare' => '=', 'value' => $hypothecId],
            ],
        ];

        $counter = $UserDbHypothecsController->getItemsByParams($options, true, false);
        if (0 == $counter[0]['count']) {
            return [
                'rows' => [],
                'total' => 0,
            ];
        }

        $results = $UserDbHypothecsController->getItemsByParams($options, false, false);
        $resultsCount = count($results);
        for ($i = 0; $i < $resultsCount; $i++) {
            $results[$i]['date_text'] = strftime('%d.%m.%Y', strtotime($results[$i]['date']));
            $results[$i]['amount'] = BGNtoEURO($results[$i]['amount']);
        }

        $return['rows'] = $results;
        $return['total'] = $counter[0]['count'];

        return $return;
    }

    /**
     * Adds new payment to hypothec.
     *
     * @api-method add
     *
     * @param int $hypothecId
     * @param float $amount
     * @param string $date
     * @param string $comment
     *
     * @return int
     */
    public function addHypothecPayment($hypothecId, $amount, $date, $comment)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $options = [
            'tablename' => $UserDbController->DbHandler->tableHypothecsPayments,
            'mainData' => [
                'hypothec_id' => $hypothecId,
                'amount' => $amount,
                'date' => $date,
                'comment' => $comment,
            ],
        ];

        $recordID = $UserDbController->addItem($options);
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['payment_params' => $options], ['created_id' => $recordID], 'Adding hypothec payment');

        return $recordID;
    }

    /**
     * Edits payment data for hypothec.
     *
     * @api-method edit
     *
     * @param int $paymentId
     * @param float $amount
     * @param string $date
     * @param string $comment
     */
    public function editHypothecPayment($paymentId, $amount, $date, $comment)
    {
        $UserDbHypothecsController = new UserDbHypothecsController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $recordID = $paymentId;

        $options = [
            'tablename' => $UserDbHypothecsController->DbHandler->tableHypothecsPayments,
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $recordID],
            ],
        ];

        $old_payment = $UserDbHypothecsController->getItemsByParams($options, false, false);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableHypothecsPayments,
            'mainData' => [
                'amount' => $amount,
                'date' => $date,
                'comment' => $comment,
            ],
            'where' => ['id' => $recordID],
        ];

        $UserDbController->editItem($options);
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['new_data' => $options], ['old_data' => $old_payment[0]], 'Editing hypothec payment');
    }

    /**
     * Deletes payment from hypothec.
     *
     * @api-method delete
     *
     * @param int $paymentId
     */
    public function deleteHypothecPayment($paymentId)
    {
        $UserDbHypothecsController = new UserDbHypothecsController($this->User->Database);
        $UsersController = new UsersController('Users');

        $options = [
            'tablename' => $UserDbHypothecsController->DbHandler->tableHypothecsPayments,
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $paymentId],
            ],
        ];

        $old_payment = $UserDbHypothecsController->getItemsByParams($options, false, false);

        $options = [
            'tablename' => $UserDbHypothecsController->DbHandler->tableHypothecsPayments,
            'id_string' => $paymentId,
        ];

        $UserDbHypothecsController->deleteItemsByParams($options);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['request_data' => $paymentId], ['old_data' => $old_payment[0]], 'Deleting hypothec payment');
    }
}
