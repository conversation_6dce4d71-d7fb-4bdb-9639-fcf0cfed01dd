<?php

namespace TF\Engine\APIClasses\Hypothecs;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbHypothecs\UserDbHypothecsController;
use TF\Engine\Plugins\Core\Users\UsersController;

// Prado::using('Plugins.Core.UserDb.*');
// Prado::using('Plugins.Core.Users.*');
// Prado::using('Plugins.Core.UserDbHypothecs.*');

/**
 * HypothecsPlotsGrid is responsible for geting hypothecs plots list and CRUD operations with hypothecs plots.
 *
 * @rpc-module Hypothecs
 *
 * @rpc-service-id hypothecs-plots-grid
 */
class HypothecsPlotsGrid extends TRpcApiProvider
{
    private $module = 'Hypothecs';
    private $service_id = 'hypothecs-plots-grid';

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getHypothecPlots'],
                'validators' => [
                    'hypothecId' => 'validateStrictInteger',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'readForAdding' => ['method' => [$this, 'getPlotsToAdd'],
                'validators' => [
                    'filterParam' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'getMaxHypothecArea' => ['method' => [$this, 'getMaxHypothecArea'],
                'validators' => [
                    'plotId' => 'validateRequired',
                ],
            ],
            'add' => ['method' => [$this, 'addPlotsToHypothec']],
            'edit' => ['method' => [$this, 'editHypothecPlot'],
                'validators' => [
                    'recordId' => 'validateRequired',
                    'hypothecArea' => 'validateRequired',
                ],
            ],
            'delete' => ['method' => [$this, 'deletePlotFromHypothec'],
                'validators' => [
                    'recordId' => 'validateRequired',
                ],
            ],
        ];
    }

    /**
     * Returns list of hypothec plots.
     *
     * @api-method read
     *
     * @param int $page
     * @param int $rows
     * @param ?int $hypothecId
     * @param ?string $sort
     * @param ?string $order
     *
     * @return array {
     *               #item integer total
     *               #item array rows {
     *               #item integer gid
     *               #item string land
     *               #item string kad_ident
     *               #item string hypothec_area
     *               #item string category
     *               #item string area_type
     *               }
     *               }
     */
    public function getHypothecPlots(?int $hypothecId, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $UserDbHypothecsController = new UserDbHypothecsController($this->User->Database);
        $UsersController = new UsersController('Users');

        if (!$hypothecId) {
            return [
                'rows' => [],
                'total' => 0,
            ];
        }

        $options = [
            'return' => [
                '*',
                'get_ntp_title_by_code(area_type) as area_type',
                'get_plot_category_by_id(category) as category',
                'get_ekatte_name_by_code(ekate) as land',
            ],
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
            'where' => [
                'hypothec_id' => ['column' => 'hypothec_id', 'compare' => '=', 'value' => $hypothecId],
            ],
        ];

        $counter = $UserDbHypothecsController->getHypothecPlots($options, true, false);
        if (0 == $counter[0]['count']) {
            return [
                'rows' => [],
                'total' => 0,
            ];
        }

        $return['rows'] = $UserDbHypothecsController->getHypothecPlots($options, false, false);
        $return['total'] = $counter[0]['count'];

        return $return;
    }

    /**
     * Returns plots available for adding to hypothec.
     *
     * @api-method readForAdding
     *
     * @param array $filterParams {
     *                            #item integer hypothec_id
     *                            #item integer farming_id
     *                            #item string h_start_date
     *                            #item string c_num
     *                            #item string subleased
     *                            #item string kad_ident
     *                            #item string ekate
     *                            #item string masiv
     *                            #item string number
     *                            #item string category
     *                            #item string area_type
     *                            }
     * @param int $page
     * @param int $rows
     * @param ?string $sort
     * @param ?string $order
     *
     * @return array {
     *               #item integer total
     *               #item array rows {
     *               #item iteger gid
     *               #item string land
     *               #item string kad_ident
     *               #item string hypothec_area
     *               #item string category
     *               #item string area_type
     *               #item string document_area
     *               #item string contracts
     *               #item string contract_area
     *               #item string hypothec_area
     *               #item string subleases
     *               #item boolean is_soled
     *               }
     *               }
     */
    public function getPlotsToAdd(?array $filterParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $UserDbHypothecsController = new UserDbHypothecsController($this->User->Database);
        $UsersController = new UsersController('Users');

        if (!$filterParams['hypothec_id']) {
            return [
                'rows' => [],
                'total' => 0,
            ];
        }

        $options = [
            'tablename' => $UserDbHypothecsController->DbHandler->tableHypothecsPlotsRel,
            'return' => ['plot_id'],
            'where' => [
                'hypothec_id' => ['column' => 'hypothec_id', 'compare' => '=', 'value' => $filterParams['hypothec_id']],
            ],
        ];

        $results = $UserDbHypothecsController->getItemsByParams($options, false, false);

        $notInArray = array_map(function ($el) {
            return $el['plot_id'];
        }, $results);

        $options = [
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
            'return' => [
                'gid', 'ekate', 'get_ekatte_name_by_code(ekate) as land', 'kad_ident', 'get_plot_category_by_id(category) as category', 'get_ntp_title_by_code(area_type) as area_type', 'round(document_area::numeric, 3) as document_area', 'round(SUM(pc.contract_area)::numeric, 3) as contract_area',
                'round(SUM(pc.contract_area)::numeric, 3) as hypothec_area', "string_agg(c.c_num, ', ') as contracts", "string_agg(s.c_num, ', ') as subleases",
                'CASE WHEN count(sl.id) > 0 THEN true ELSE false END as is_soled', 'is_edited',
            ],
            'where' => [
                'plot_id' => ['prefix' => 'p', 'column' => 'gid', 'compare' => 'NOT IN', 'value' => $notInArray],
                'kad_ident' => ['prefix' => 'p', 'column' => 'kad_ident', 'compare' => 'ILIKE', 'value' => $filterParams['kad_ident']],
                'ekate' => ['prefix' => 'p', 'column' => 'ekate', 'compare' => '=', 'value' => $filterParams['ekate']],
                'masiv' => ['prefix' => 'p', 'column' => 'masiv', 'compare' => '=', 'value' => $filterParams['masiv']],
                'number' => ['prefix' => 'p', 'column' => 'number', 'compare' => '=', 'value' => $filterParams['number']],
                'category' => ['prefix' => 'p', 'column' => 'category', 'compare' => '=', 'value' => $filterParams['category']],
                'area_type' => ['prefix' => 'p', 'column' => 'area_type', 'compare' => '=', 'value' => $filterParams['area_type']],
                'contract_farming' => ['prefix' => 'c', 'column' => 'farming_id', 'compare' => '=', 'value' => $filterParams['farming_id']],
                'contract_type' => ['prefix' => 'c', 'column' => 'nm_usage_rights', 'compare' => '=', 'value' => 1],
                'is_annex' => ['prefix' => 'c', 'column' => 'is_annex', 'compare' => '=', 'value' => 'FALSE'],
            ],
            'h_start_date' => $filterParams['h_start_date'],
            'group' => 'gid',
            'having' => [
                'contract_number' => ['column' => "string_agg(c.c_num, ', ')", 'compare' => 'ILIKE', 'value' => $filterParams['c_num']],
                'subleased' => ['column' => 'count(s.c_num)', 'compare' => '=', 'value' => $filterParams['subleased']],
                'sold' => ['column' => 'count(sl.id)', 'compare' => '=', 'value' => $filterParams['sold']],
            ],
        ];

        if ('all' != $filterParams['irrigated_area']) {
            $options['where']['irrigated_area'] = ['column' => 'irrigated_area', 'compare' => '=', 'prefix' => 'p', 'value' => $filterParams['irrigated_area']];
        }

        $counter = $UserDbHypothecsController->getAvailablePlotsForHypothec($options, true, false);

        if (0 == count($counter)) {
            return [
                'rows' => [],
                'total' => 0,
            ];
        }

        $return['rows'] = $UserDbHypothecsController->getAvailablePlotsForHypothec($options, false, false);
        $return['total'] = count($counter);

        return $return;
    }

    /**
     * Returns maximum allowed hypothec area for plot.
     *
     * @api-method getMaxHypothecArea
     *
     * @param int $plotId
     * @param int $farmingId
     *
     * @return float
     */
    public function getMaxHypothecArea($plotId, $farmingId)
    {
        $UserDbHypothecsController = new UserDbHypothecsController($this->User->Database);

        $options = [
            'return' => [
                'SUM(pc.contract_area) as contract_area',
            ],
            'where' => [
                'plot_id' => ['prefix' => 'p', 'column' => 'gid', 'compare' => '=', 'value' => $plotId],
                'contract_farming' => ['prefix' => 'c', 'column' => 'farming_id', 'compare' => '=', 'value' => $farmingId],
                'contract_type' => ['prefix' => 'c', 'column' => 'nm_usage_rights', 'compare' => '=', 'value' => 1],
                'annex_action' => ['prefix' => 'pc', 'column' => 'annex_action', 'compare' => '=', 'value' => 'added'],
            ],
            'group' => 'gid',
        ];

        $results = $UserDbHypothecsController->getAvailablePlotsForHypothec($options);

        return $results[0]['contract_area'];
    }

    /**
     * Adds plots to hypothec.
     *
     * @api-method add
     *
     * @param array $plots {
     *                     array {
     *                     #item integer hypothec_id
     *                     #item integer plot_id
     *                     #item float hypothec_area
     *                     }
     *                     }
     *
     * @return array
     */
    public function addPlotsToHypothec($plots)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        $relationIDs = [];
        $plotsCount = count($plots);
        for ($i = 0; $i < $plotsCount; $i++) {
            $options = [
                'tablename' => $UserDbController->DbHandler->tableHypothecsPlotsRel,
                'mainData' => [
                    'hypothec_id' => $plots[$i]['hypothec_id'],
                    'plot_id' => $plots[$i]['plot_id'],
                    'hypothec_area' => $plots[$i]['hypothec_area'],
                ],
            ];

            $recordID = $UserDbController->addItem($options);
            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['relation_data' => $options], ['created_id' => $recordID], 'Adding hypothec plot rel');
            $relationIDs[] = $recordID;
        }

        return $relationIDs;
    }

    /**
     * Deletes plot from hypothec.
     *
     * @api-method delete
     *
     * @param int $recordId
     */
    public function deletePlotFromHypothec($recordId)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        $UserDbHypothecsController = new UserDbHypothecsController($this->User->Database);

        $options = [
            'tablename' => $UserDbHypothecsController->DbHandler->tableHypothecsPlotsRel,
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $recordId],
            ],
        ];

        $old_rel_info = $UserDbHypothecsController->getItemsByParams($options);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableHypothecsPlotsRel,
            'id_string' => $recordId,
        ];

        $UserDbController->deleteItemsByParams($options);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['deleted_data' => $old_rel_info], ['deleted_id' => $recordId], 'Deleting hypothec plot rel');
    }

    /**
     * Edits hypothec area for plot.
     *
     * @api-method delete
     *
     * @param int $recordId
     * @param float $hypothecArea
     */
    public function editHypothecPlot($recordId, $hypothecArea)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        $UserDbHypothecsController = new UserDbHypothecsController($this->User->Database);

        $options = [
            'tablename' => $UserDbHypothecsController->DbHandler->tableHypothecsPlotsRel,
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $recordId],
            ],
        ];

        $old_plot_info = $UserDbHypothecsController->getItemsByParams($options);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableHypothecsPlotsRel,
            'mainData' => [
                'hypothec_area' => $hypothecArea,
            ],
            'where' => ['id' => $recordId],
        ];

        $UserDbController->editItem($options);
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['old_data' => $old_plot_info], ['new_data' => $options], 'Editing hypothec plot rel');
    }
}
