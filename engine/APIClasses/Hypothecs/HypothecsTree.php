<?php

namespace TF\Engine\APIClasses\Hypothecs;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\ArrayHelper;
use TF\Engine\Kernel\ExportWordDocClass;
use TF\Engine\Kernel\PrintPdf;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbHypothecs\UserDbHypothecsController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * HypothecsTree is responsible for geting hypothecs list and CRUD operations with hypothecs.
 *
 * @rpc-module Hypothecs
 *
 * @rpc-service-id hypothecs-tree
 */
class HypothecsTree extends TRpcApiProvider
{
    private $module = 'Hypothecs';
    private $service_id = 'hypothecs-tree';

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getHypothecs'],
                'validators' => [
                    'filterParam' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'add' => ['method' => [$this, 'addHypothec'],
                'validators' => [
                    'data' => [
                        'num' => 'validateRequired',
                    ],
                ],
            ],
            'edit' => ['method' => [$this, 'editHypothec'],
                'validators' => [
                    'data' => [
                        'id' => 'validateRequired',
                        'num' => 'validateRequired',
                    ],
                ],
            ],
            'deactivate' => ['method' => [$this, 'deactivateHypothec'],
                'validators' => [
                    'hypothec_id' => 'validateRequired',
                ],
            ],
            'activate' => ['method' => [$this, 'activateHypothec'],
                'validators' => [
                    'hypothec_id' => 'validateRequired',
                ],
            ],
            'export' => ['method' => [$this, 'exportHypothecBlank']],
            'removeFile' => ['method' => [$this, 'removeFileFromServer']],
        ];
    }

    /**
     * Returns list of hypothecs matching filters.
     *
     * @api-method read
     *
     * @param array $filterParams {
     *                            #item string hypothec_id
     *                            #item string h_num
     *                            #item boolean is_active
     *                            #item integer farming
     *                            #item integer creditor
     *                            #item string start_date_from
     *                            #item string start_date_to
     *                            #item string due_date_from
     *                            #item string due_date_to
     *                            #item string kad_ident
     *                            #item string ekate
     *                            #item string masiv
     *                            #item string number
     *                            #item string category
     *                            #item string area_type
     *                            }
     * @param int $page
     * @param int $rows
     * @param ?string $sort
     * @param ?string $order
     *
     * @return array {
     *               #item string text
     *               #item string iconCls
     *               #item integer id
     *               #item array attributes {
     *               #item string active_text
     *               #item string comment
     *               #item string court
     *               #item string creditor
     *               #item integer creditor_id
     *               #item string date
     *               #item string date_text
     *               #item string deactivate_date
     *               #item string deactivate_num
     *               #item string delo
     *               #item string due_date
     *               #item string due_date_text
     *               #item string farming
     *               #item integer farming_id
     *               #item integer id
     *               #item boolean is_active
     *               #item string na_num
     *               #item string num
     *               #item string start_date
     *               #item string start_date_text
     *               #item string tom
     *               }
     *               }
     */
    public function getHypothecs(?array $filterParams = null, ?int $page = null, ?int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $FarmingController = new FarmingController('Farming');
        $UserDbHypothecsController = new UserDbHypothecsController($this->User->Database);
        $arrayHelper = new ArrayHelper();

        $userFarmings = $FarmingController->getUserFarmings();
        $farmingIds = array_keys($userFarmings);
        if (isset($filterParams['farming']) && in_array((int) $filterParams['farming'], $farmingIds)) {
            $farmingIds = [(int) $filterParams['farming']];
        }

        // options for contract files query
        $options = [
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
            'return' => ['h.*', 'c.name as creditor'],
            'where' => [
                'hypothec_id' => ['prefix' => 'h', 'column' => 'id', 'compare' => 'IN', 'value' => $arrayHelper->filterEmptyStringArr(explode(',', $filterParams['hypothec_id']))],
                'h_num' => ['prefix' => 'h', 'column' => 'num', 'compare' => 'ILIKE', 'value' => $filterParams['h_num']],
                'status' => ['prefix' => 'h', 'column' => 'is_active', 'compare' => '=', 'value' => $filterParams['is_active']],
                'farming' => ['prefix' => 'h', 'column' => 'farming_id', 'compare' => 'IN', 'value' => $farmingIds],
                'creditor' => ['prefix' => 'h', 'column' => 'creditor_id', 'compare' => '=', 'value' => $filterParams['creditor']],
                'start_date_from' => ['prefix' => 'h', 'column' => 'start_date', 'compare' => '>=', 'value' => $filterParams['start_date_from']],
                'start_date_to' => ['prefix' => 'h', 'column' => 'start_date', 'compare' => '<=', 'value' => $filterParams['start_date_to']],
                'due_date_from' => ['prefix' => 'h', 'column' => 'due_date', 'compare' => '>=', 'value' => $filterParams['due_date_from']],
                'due_date_to' => ['prefix' => 'h', 'column' => 'due_date', 'compare' => '<=', 'value' => $filterParams['due_date_to']],
                'kad_ident' => ['prefix' => 'p', 'column' => 'kad_ident', 'compare' => 'ILIKE', 'value' => $filterParams['kad_ident']],
                'ekate' => ['prefix' => 'p', 'column' => 'ekate', 'compare' => '=', 'value' => $filterParams['ekate']],
                'masiv' => ['prefix' => 'p', 'column' => 'masiv', 'compare' => '=', 'value' => $filterParams['masiv']],
                'number' => ['prefix' => 'p', 'column' => 'number', 'compare' => '=', 'value' => $filterParams['number']],
                'category' => ['prefix' => 'p', 'column' => 'category', 'compare' => '=', 'value' => $filterParams['category']],
                'area_type' => ['prefix' => 'p', 'column' => 'area_type', 'compare' => '=', 'value' => $filterParams['area_type']],
            ],
            'group' => 'h.id, c.name',
        ];

        if ('all' != $filterParams['irrigated_area']) {
            $options['where']['irrigated_area'] = ['column' => 'irrigated_area', 'compare' => '=', 'prefix' => 'p', 'value' => $filterParams['irrigated_area']];
        }

        if (true == $filterParams['h_num_complete_match']) {
            $options['where']['h_num']['compare'] = '=';
        }

        $counter = count($UserDbHypothecsController->getHypothecs($options, true, false));
        if (0 == $counter) {
            return [];
        }

        $farming_array = [];

        $farming_options = [
            'where' => [
                'id' => ['column' => 'id', 'compare' => 'IN', 'value' => $farmingIds],
            ],
        ];
        $farming_results = $FarmingController->getFarmings($farming_options);
        $farmsCount = count($farming_results);
        if (0 != $farmsCount) {
            for ($i = 0; $i < $farmsCount; $i++) {
                $farming_array[$farming_results[$i]['id']] = $farming_results[$i];
            }
        }

        $results = $UserDbHypothecsController->getHypothecs($options);
        $resultsCount = count($results);
        for ($i = 0; $i < $resultsCount; $i++) {
            $results[$i]['active_text'] = $results[$i]['is_active'] ? 'Активен' : 'Прекратен';

            $results[$i]['date_text'] = strftime('%d.%m.%Y', strtotime($results[$i]['date']));
            $results[$i]['start_date_text'] = strftime('%d.%m.%Y', strtotime($results[$i]['start_date']));
            $results[$i]['due_date_text'] = strftime('%d.%m.%Y', strtotime($results[$i]['due_date']));

            if ($results[$i]['deactivate_date']) {
                $results[$i]['deactivate_date_text'] = strftime('%d.%m.%Y', strtotime($results[$i]['deactivate_date']));
            }

            $results[$i]['farming'] = $farming_array[$results[$i]['farming_id']]['name'];

            $return[] = [
                'text' => $results[$i]['num'],
                'id' => $results[$i]['id'],
                'attributes' => $results[$i],
                'iconCls' => 'icon-tree-document',
            ];
        }

        // add attribute to first listed element of three for custom pagination
        $return[0]['attributes']['pagination']['total'] = (int)$counter;
        $return[0]['attributes']['pagination']['limit'] = (int)$rows;

        return $return;
    }

    /**
     * Adds new hypothec.
     *
     * @api-method add
     *
     * @param array $data {
     *                    #item string num
     *                    #item string date
     *                    #item string start_date
     *                    #item string due_date
     *                    #item integer farming_id
     *                    #item integer creditor_id
     *                    #item string tom
     *                    #item string na_num
     *                    #item string delo
     *                    #item string court
     *                    #item string comment
     *                    }
     *
     * @return int
     */
    public function addHypothec($data)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $options = [
            'tablename' => $UserDbController->DbHandler->tableHypothecs,
            'mainData' => [
                'num' => $data['num'],
                'date' => $data['date'],
                'start_date' => $data['start_date'],
                'due_date' => $data['due_date'],
                'farming_id' => $data['farming_id'],
                'creditor_id' => $data['creditor_id'],
                'tom' => $data['tom'],
                'na_num' => $data['na_num'],
                'delo' => $data['delo'],
                'court' => $data['court'],
                'comment' => $data['comment'],
            ],
        ];

        $recordID = $UserDbController->addItem($options);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['new_data' => $options['mainData']], ['created_id' => $recordID], 'Adding hypothec');

        return $recordID;
    }

    /**
     * Edits hypothec.
     *
     * @api-method edit
     *
     * @param array $data {
     *                    #item integer id
     *                    #item string num
     *                    #item string date
     *                    #item string start_date
     *                    #item string due_date
     *                    #item integer farming_id
     *                    #item integer creditor_id
     *                    #item string tom
     *                    #item string na_num
     *                    #item string delo
     *                    #item string court
     *                    #item string comment
     */
    public function editHypothec($data)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $recordID = $data['id'];

        $options = [
            'tablename' => $UserDbController->DbHandler->tableHypothecs,
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $recordID],
            ],
        ];

        $old_data = $UserDbController->getItemsByParams($options);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableHypothecs,
            'mainData' => [
                'num' => $data['num'],
                'date' => $data['date'],
                'start_date' => $data['start_date'],
                'due_date' => $data['due_date'],
                'farming_id' => $data['farming_id'],
                'creditor_id' => $data['creditor_id'],
                'tom' => $data['tom'],
                'na_num' => $data['na_num'],
                'delo' => $data['delo'],
                'court' => $data['court'],
                'comment' => $data['comment'],
            ],
            'where' => ['id' => $recordID],
        ];

        $UserDbController->editItem($options);
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['old_data' => $old_data[0]], ['new_data' => $options['mainData']], 'Editing hypothec');
    }

    /**
     * Deactivates hypothec.
     *
     * @api-method deactivate
     *
     * @param int $hypothecId
     * @param string $date
     * @param string $num
     * @param string $comment
     */
    public function deactivateHypothec($hypothecId, $date, $num, $comment)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $recordID = $hypothecId;

        $options = [
            'tablename' => $UserDbController->DbHandler->tableHypothecs,
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $recordID],
            ],
        ];

        $old_data = $UserDbController->getItemsByParams($options);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableHypothecs,
            'mainData' => [
                'deactivate_num' => $num,
                'deactivate_date' => $date,
                'comment' => $comment,
                'is_active' => false,
            ],
            'where' => ['id' => $recordID],
        ];

        $UserDbController->editItem($options);
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['old_data' => $old_data[0]], ['new_data' => $options], 'Deactivating hypothec');
    }

    /**
     * Activates hypothec.
     *
     * @api-method activate
     *
     * @param int $hypothecId
     */
    public function activateHypothec($hypothecId)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $recordID = $hypothecId;

        $options = [
            'tablename' => $UserDbController->DbHandler->tableHypothecs,
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $recordID],
            ],
        ];

        $old_data = $UserDbController->getItemsByParams($options);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableHypothecs,
            'mainData' => [
                'deactivate_num' => null,
                'deactivate_date' => null,
                'is_active' => true,
            ],
            'where' => ['id' => $recordID],
        ];

        $UserDbController->editItem($options);
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['old_data' => $old_data[0]], ['new_data' => $options], 'Activating hypothec');
    }

    /**
     * Exports hypothec using template in .pdf or .doc.
     *
     * @api-method export
     *
     * @param int $hypothecId
     * @param int $templateId
     * @param string $exportType "pdf" or "doc"
     *
     * @return array|string
     */
    public function exportHypothecBlank($hypothecId, $templateId, $exportType)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbHypothecsController = new UserDbHypothecsController($this->User->Database);
        $FarmingController = new FarmingController('Farming');
        $UsersController = new UsersController('Users');

        // get template data
        $options = [
            'tablename' => $UserDbController->DbHandler->tableTemplates,
            'where' => [
                'template_id' => ['column' => 'id', 'compare' => '=', 'value' => $templateId],
            ],
        ];

        $template_results = $UserDbController->getItemsByParams($options, false, false);

        if (0 == count($template_results)) {
            return [];
        }

        $template = $template_results[0]['html'];

        $userFarmings = $FarmingController->getUserFarmings();
        $farmingIds = array_keys($userFarmings);
        // get contract data
        $options = [
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
            'return' => ['h.*', 'c.name as creditor'],
            'where' => [
                'hypothec_id' => ['prefix' => 'h', 'column' => 'id', 'compare' => '=', 'value' => $hypothecId],
                'farming_id' => ['prefix' => 'h', 'column' => 'farming_id', 'compare' => 'IN', 'value' => $farmingIds],
            ],
        ];

        $counter = $UserDbHypothecsController->getHypothecs($options, true, false);
        if (0 == $counter[0]['count']) {
            return [];
        }

        $results = $UserDbHypothecsController->getHypothecs($options, false, false);

        $hypothecData = $results[0];

        // get farming
        $options = [
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $hypothecData['farming_id']],
            ],
        ];

        $farming_results = $FarmingController->getFarmings($options, false, false);

        if (0 == count($farming_results)) {
            return [];
        }

        // get plots data and create table
        if (strstr($template, '[[imoti]]') || strstr($template, '[[imoti_podrobno')) {
            $options = [
                'where' => [
                    'hypothec_id' => ['column' => 'hypothec_id', 'compare' => '=', 'value' => $hypothecId],
                ],
            ];

            $plotsResults = $UserDbHypothecsController->getHypothecPlots($options, false, false);
            $plotsCount = count($plotsResults);

            $ekates = [];
            for ($i = 0; $i < $plotsCount; $i++) {
                $plotsResults[$i]['land'] = $plotsResults[$i]['virtual_ekatte_name'];
                if (!in_array($plotsResults[$i]['ekate'], $ekates)) {
                    $ekates[] = $plotsResults[$i]['ekate'];
                }

                $plotsResults[$i]['category'] = $plotsResults[$i]['virtual_category_title'];
                $plotsResults[$i]['area_type'] = $plotsResults[$i]['virtual_ntp_title'];
                $plotsResults[$i]['hypothec_area'] = number_format($plotsResults[$i]['hypothec_area'], 3, '.', '');
                $plotsResults[$i]['document_area'] = number_format($plotsResults[$i]['document_area'], 3, '.', '');

                if ('' == $plotsResults[$i]['kad_ident']) {
                    $plotsResults[$i]['kad_ident'] = '[Няма информация]';
                }
            }
            if (strstr($template, '[[imoti]]') && !empty($ekates)) {
                $plotsData = '<table align="center" cellspacing="0" cellpadding="3" border="1">';
                $plotsData .= '<thead>';
                $plotsData .= '<tr align="center">';
                $plotsData .= '<th>№</th>';
                $plotsData .= '<th>Землище</th>';
                $plotsData .= '<th>Местност</th>';
                $plotsData .= '<th>Идентификатор</th>';
                $plotsData .= '<th>НТП</th>';
                $plotsData .= '<th>Площ по <br/>документ(дка)</th>';
                $plotsData .= '<th>Ипотекирана <br/>площ(дка)</th>';
                $plotsData .= '</tr>';
                $plotsData .= '</thead>';
                $plotsData .= '<tbody>';

                $total_area = 0;
                $total_document_area = 0;

                for ($i = 0; $i < $plotsCount; $i++) {
                    $plotsData .= '<tr>';
                    $plotsData .= '<td width="20" align="center">' . ($i + 1) . '</td>';
                    $plotsData .= '<td width="120" align="center">' . $plotsResults[$i]['land'] . '</td>';
                    $plotsData .= '<td width="115" align="center">' . $plotsResults[$i]['mestnost'] . '</td>';
                    $plotsData .= '<td width="115" align="center">' . $plotsResults[$i]['kad_ident'] . '</td>';
                    $plotsData .= '<td width="180" align="center">' . $plotsResults[$i]['area_type'] . '</td>';
                    $plotsData .= '<td width="70" align="center">' . $plotsResults[$i]['document_area'] . '</td>';
                    $plotsData .= '<td width="70" align="center">' . $plotsResults[$i]['hypothec_area'] . '</td>';
                    $plotsData .= '</tr>';

                    $total_area += $plotsResults[$i]['hypothec_area'];
                    $total_document_area += $plotsResults[$i]['document_area'];
                }

                $plotsData .= '<tr>';
                $plotsData .= '<td width="20" align="center"></td>';
                $plotsData .= '<td width="120" align="center"></td>';
                $plotsData .= '<td width="115" align="center"></td>';
                $plotsData .= '<td width="115" align="center"></td>';
                $plotsData .= '<td width="180" align="center"><b>Общо</b></td>';
                $plotsData .= '<td width="70" align="center"><b>' . $total_document_area . '</b></td>';
                $plotsData .= '<td width="70" align="center"><b>' . $total_area . '</b></td>';
                $plotsData .= '</tr>';

                $plotsData .= '</tbody>';
                $plotsData .= '</table>';
            }

            if (strstr($template, '[[imoti_podrobno') && !empty($ekates)) {
                $regex = '/\\[\\[imoti_podrobno (.*)\\]\\]/';
                preg_match_all($regex, $template, $matches);

                $plotsDetailedDataArr = [];
                foreach ($matches[1] as $key => $match) {
                    $columns = explode(' ', $match);
                    $columnsCount = count($columns);
                    if ($columnsCount > 0) {
                        $plotsDetailedData = '<table cellspacing="0" cellpadding="3" border="1">';

                        // header
                        $plotsDetailedData .= '<thead>';
                        $plotsDetailedData .= '<tr align="center">';
                        $plotsDetailedData .= '<th>№</th>';

                        foreach ($columns as $keyC => $column) {
                            $plotsDetailedData .= '<th>' . $GLOBALS['Contracts']['variables_plots_detailed'][$column] . '</th>';
                        }

                        $plotsDetailedData .= '</tr>';
                        $plotsDetailedData .= '</thead>';

                        // body
                        $plotsDetailedData .= '<tbody>';

                        $total_area = 0;
                        $total_document_area = 0;
                        for ($i = 0; $i < $plotsCount; $i++) {
                            $plotsResults[$i]['zemlishte'] = $plotsResults[$i]['land'];
                            $plotsResults[$i]['ntp'] = $plotsResults[$i]['area_type'];

                            $plotsDetailedData .= '<tr>';
                            $plotsDetailedData .= '<td style="white-space: nowrap;" align="center">' . ($i + 1) . '</td>';
                            foreach ($columns as $keyCo => $column) {
                                if ('ntp' === $column) {
                                    $plotsDetailedData .= '<td width="115" align="center">' . $plotsResults[$i][$column] . '</td>';

                                    continue;
                                }
                                if ('contract_area' === $column) {
                                    $plotsDetailedData .= '<td style="white-space: nowrap;" align="center">' . $plotsResults[$i]['hypothec_area'] . '</td>';

                                    continue;
                                }
                                $plotsDetailedData .= '<td style="white-space: nowrap;" align="center">' . $plotsResults[$i][$column] . '</td>';
                            }

                            $plotsDetailedData .= '</tr>';

                            if (in_array('contract_area', $columns) || in_array('document_area', $columns)) {
                                $total_area += $plotsResults[$i]['hypothec_area'];
                                $total_document_area += $plotsResults[$i]['document_area'];
                            }
                        }

                        // footer
                        if ($total_area > 0 || $total_document_area > 0) {
                            $plotsDetailedData .= '<tr>';

                            // column for numeration
                            $plotsDetailedData .= '<td style="white-space: nowrap;">Общо</td>';

                            for ($m = 0; $m < $columnsCount; $m++) {
                                $column = $columns[$m];

                                if ('contract_area' === $column) {
                                    $plotsDetailedData .= '<td style="white-space: nowrap;" align="center"><b>' . number_format($total_area, 3, '.', '') . '</b></td>';
                                } elseif ('document_area' === $column) {
                                    $plotsDetailedData .= '<td style="white-space: nowrap;" align="center"><b>' . number_format($total_document_area, 3, '.', '') . '</b></td>';
                                } else {
                                    $plotsDetailedData .= '<td style="white-space: nowrap;"></td>';
                                }
                            }

                            $plotsDetailedData .= '</tr>';
                        }

                        $plotsDetailedData .= '</tbody>';
                        $plotsDetailedData .= '</table>';

                        $plotsDetailedDataArr[$key] = $plotsDetailedData;
                    }
                }
            }
        }

        if (strstr($template, '[[timespan_farming_years]]')) {
            $timespan_message = 'договорът действа в срок от ';
            if ('01' == strftime('%d', strtotime($hypothecData['start_date']))
                && '10' == strftime('%m', strtotime($hypothecData['start_date']))
                && '30' == strftime('%d', strtotime($hypothecData['due_date']))
                && '09' == strftime('%m', strtotime($hypothecData['due_date']))) {
                $farming_years = strftime('%Y', strtotime($hypothecData['due_date'])) - strftime('%Y', strtotime($hypothecData['start_date']));
                $timespan_message = $timespan_message . $farming_years . ((1 == $farming_years) ? ' стопанска година' : ' стопански години');
            } else {
                $timespan_message = $timespan_message . '...... стопански години';
            }
        }

        // replace the data
        $template = str_replace('[[nomer_na_dogovor]]', $hypothecData['num'], $template);
        $template = str_replace('[[data_na_dogovor]]', strftime('%d.%m.%Y', strtotime($hypothecData['date'])) . 'г.', $template);
        $template = str_replace('[[vlizane_v_sila]]', strftime('%d.%m.%Y', strtotime($hypothecData['start_date'])) . 'г.', $template);
        $template = str_replace('[[kraina_data]]', strftime('%d.%m.%Y', strtotime($hypothecData['due_date'])) . 'г.', $template);
        $template = str_replace('[[stopanstvo]]', $farming_results[0]['name'], $template);
        $template = str_replace('[[stopanstvo_address]]', $farming_results[0]['address'], $template);
        $template = str_replace('[[stopanstvo_firma]]', $farming_results[0]['company'], $template);
        $template = str_replace('[[stopanstvo_bulstat]]', $farming_results[0]['bulstat'], $template);
        $template = str_replace('[[stopanstvo_firma_address]]', $farming_results[0]['company_address'], $template);
        $template = str_replace('[[stopanstvo_mol]]', $farming_results[0]['mol'], $template);
        $template = str_replace('[[kontragent]]', $hypothecData['creditor'], $template);
        $template = str_replace('[[imoti]]', $plotsData, $template);

        // imoti_podrobno
        $countMatches = count($matches[0]);
        for ($n = 0; $n < $countMatches; $n++) {
            $plotDetailedData = $plotsDetailedDataArr[$n];
            $allMatchesPlotDet = $matches[0][$n];

            $template = str_replace($allMatchesPlotDet, $plotDetailedData, $template);
        }

        $template = str_replace('[[imoti_zemlishta]]', $plots_by_ekatte_string, $template);
        $template = str_replace('[[imoti_kategoriq]]', $plots_by_category_string, $template);
        $template = str_replace('[[padej]]', strftime('%d.%m.%Y', strtotime($hypothecData['due_date'])) . 'г.', $template);
        $template = str_replace('[[timespan_farming_years]]', $timespan_message, $template);

        $blankName = $template_results[0]['title'];

        $blankName = preg_replace('/\s+/', '_', $blankName);

        $return = [];

        if ('pdf' == $exportType) {
            $blanksDir = SITE_PATH . 'public/files/uploads/blanks/';
            if (!file_exists($blanksDir)) {
                $makeDir = mkdir($blanksDir, 0700);
            }

            $blanksPath = 'files/uploads/blanks/';
            $pdfPath = $blanksPath . $this->User->GroupID . '_' . $blankName . '.pdf';

            if (file_exists($pdfPath)) {
                unlink($pdfPath);
            }

            $headerTxt = $this->getHeaderFooterTag('page_header', $template);
            $footerTxt = $this->getHeaderFooterTag('page_footer', $template);
            $headerTxt = $this->formatPDFHeaderFooter('page_header', $headerTxt);
            $footerTxt = $this->formatPDFHeaderFooter('page_footer', $footerTxt);
            $template = $headerTxt . $template . $footerTxt;
            $template = '<page style="font-family: freeserif" backtop="50px" backbottom="50px" >' . $template . '</page>';

            $printPdf = new PrintPdf();
            $printPdf->generateFromHtml($template, $pdfPath, [], true);

            $return['file_url'] = $pdfPath;
            $return['filename'] = $this->User->GroupID . '_' . $blankName . '.pdf';
        }

        if ('doc' == $exportType) {
            $exportWordDoc = new ExportWordDocClass();
            $return['file_url'] = $exportWordDoc->export($this->User->GroupID . '_' . $blankName, $template, true);
            $return['filename'] = $this->User->GroupID . '_' . $blankName . '.doc';
        }

        return $return;
    }

    /**
     * remove file from the server.
     *
     * @api-method remove
     *
     * @param string $fileName
     */
    public function removeFileFromServer($fileName)
    {
        @unlink(PUBLIC_UPLOAD_BLANK . '/' . $fileName);
    }

    private function getHeaderFooterTag($tagName, &$template)
    {
        $tagContentRe = "/\[\[{$tagName}_\]\](?P<content>.*?)\[\[_{$tagName}\]\]/s";
        $matches = [];

        if (!preg_match_all($tagContentRe, $template, $matches)) {
            return '';
        }
        $template = preg_replace($tagContentRe, '', $template);

        return $matches['content'][0];
    }

    private function formatPDFHeaderFooter($tagName, $tagCont)
    {
        $imageRe = "/<img\\s[^>]*?src\\s*=\\s*['\\\"](?:(?P<img_src>[^'\\\"]*?)['\\\"][^>]*?>)/s";
        $matches = [];
        $formatedTag = "<{$tagName}>{$tagCont}</{$tagName}>";

        if (preg_match_all($imageRe, $tagCont, $matches)) {
            $newImage = '<img src="' . SITE_URL . '\1" height="50" />';
            $formatedTag = "<{$tagName}>" . preg_replace($imageRe, $newImage, $tagCont) . "</{$tagName}>";
        }

        return $formatedTag;
    }
}
