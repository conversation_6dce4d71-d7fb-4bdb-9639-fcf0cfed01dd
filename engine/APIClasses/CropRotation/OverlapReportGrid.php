<?php

namespace TF\Engine\APIClasses\CropRotation;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbCropRotation\UserDbCropRotationController;

// Prado::using('Plugins.Core.Layers.conf');
// Prado::using('Plugins.Core.Layers.*');

/**
 * Grid 'Проверка за припокриване'.
 *
 * @rpc-module CropRotation
 *
 * @rpc-service-id croprotation-overlap-report
 */
class OverlapReportGrid extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getOverlapReportGrid'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * Проверка за припокриване.
     *
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer layer_id
     *                         #item integer farming
     *                         #item integer year
     *                         }
     * @param int $page pagination parameter
     * @param int $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array
     */
    public function getOverlapReportGrid(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        if ($this->User->isGuest || !$this->User->Database) {
            return [];
        }

        // init all needed controllers
        $UserDbCropRotationController = new UserDbCropRotationController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);
        $LayersController = new LayersController('Layers');

        $cropLayerID = (int) $rpcParams['layer_id'];
        $farming = (int) $rpcParams['farming'];
        $year = (int) $rpcParams['year'];

        $cropLayerItemData = $UserDbCropRotationController->getCropLayersItemData($cropLayerID);

        // getting data for ISAK layer
        $layer_options = [
            'farming' => $cropLayerItemData['farming'],
            'year' => $cropLayerItemData['year'],
            'user_id' => $this->User->GroupID,
            'layer_type' => 6,
            'return' => ['table_name'],
        ];

        $isakLayerData = $LayersController->getLayersIdByParams($layer_options);

        // getting data for ZP layer
        $layer_options = [
            'farming' => $farming,
            'year' => $year,
            'user_id' => $this->User->GroupID,
            'layer_type' => 1,
            'return' => ['table_name'],
        ];
        $zpLayerData = $LayersController->getLayersIdByParams($layer_options);

        $options = [
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
            'crop_layer_id' => $cropLayerID,
        ];

        $data = [];
        $dataCount = 0;

        $tableISAKExists = $UserDbController->getTableNameExist($isakLayerData['table_name']);
        $tableZPExists = $UserDbController->getTableNameExist($zpLayerData['table_name']);

        if ($tableISAKExists && $tableZPExists) {
            $counter = $UserDbCropRotationController->getCroprotationOverlapReport($zpLayerData['table_name'], $isakLayerData['table_name'], $options, $counter = true);
            $return['total'] = $counter[0]['count'];

            // the query is executed only if there are elements found
            if ($counter[0]['count']) {
                $data = $UserDbCropRotationController->getCroprotationOverlapReport($zpLayerData['table_name'], $isakLayerData['table_name'], $options, $counter = false);
                $dataCount = count($data);
            }
            $total_zp_area = 0;
            $total_isak_area = 0;
            $total_intersect = 0;
            $zp_id_array = [];
            $isak_gid_array = [];

            for ($i = 0; $i < $dataCount; $i++) {
                $data[$i]['intersect_percent'] = number_format($data[$i]['intersect_percent'], 2, '.', '');
                $data[$i]['intersect_area'] = number_format($data[$i]['intersect_area'] / 1000, 3, '.', '');
                $data[$i]['zp_area'] = number_format($data[$i]['zp_area'] / 1000, 3, '.', '');
                $data[$i]['isak_area'] = number_format($data[$i]['isak_area'] / 1000, 3, '.', '');

                if (!in_array($data[$i]['zp_id'], $zp_id_array)) {
                    $zp_id_array[] = $data[$i]['zp_id'];
                    $total_zp_area += $data[$i]['zp_area'];
                }

                if (!in_array($data[$i]['isak_gid'], $isak_gid_array)) {
                    $isak_gid_array[] = $data[$i]['isak_gid'];
                    $total_isak_area += $data[$i]['isak_area'];
                }

                $total_intersect += $data[$i]['intersect_area'];
            }

            // escape divide by zero
            if (0 != $total_isak_area) {
                $total_intersect_percent = number_format($total_intersect / $total_isak_area * 100, 2, '.', '');
            } else {
                $total_intersect_percent = 0;
            }
            $return['rows'] = $data;
            $return['footer'] = [
                [
                    'prc_uin' => '<b>ОБЩО</b>',
                    'isak_area' => '<b>' . $total_isak_area . '</b>',
                    'zp_area' => '<b>' . $total_zp_area . '</b>',
                    'intersect_percent' => '<b>' . $total_intersect_percent . '%</b>',
                    'intersect_area' => '<b>' . number_format($total_intersect, 2, '.', '') . '</b>',
                ],
            ];
        } else {
            $return = [
                'rows' => [],
                'total' => 0,
                'footer' => [],
            ];
        }

        return $return;
    }
}
