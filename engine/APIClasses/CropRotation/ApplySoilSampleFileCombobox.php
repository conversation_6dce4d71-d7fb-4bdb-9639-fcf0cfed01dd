<?php

namespace TF\Engine\APIClasses\CropRotation;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbCropRotation\UserDbCropRotationController;

// Prado::using('Plugins.Core.Farming.conf');
// Prado::using('Plugins.Core.Farming.*');

/**
 * 'Прилагане на данни към референтен слой'.
 *
 * @rpc-module CropRotation
 *
 * @rpc-service-id apply-ssf-combobox
 */
class ApplySoilSampleFileCombobox extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getApplySoilSampleFileCombobox']],
        ];
    }

    /**
     * Return combobox data.
     *
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item string  request
     *                         #item integer farming
     *                         #item integer year
     *                         #item boolean selected
     *                         }
     *
     * @return array
     */
    public function getApplySoilSampleFileCombobox($rpcParams)
    {
        if (!isset($rpcParams['request'])) {
            return [];
        }

        // init all needed controllers
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbCropRotationController = new UserDbCropRotationController($this->User->Database);
        $FarmingController = new FarmingController('Farming');
        $return = [];
        switch ($rpcParams['request']) {
            case 'farmings':
                $farmings = $FarmingController->getUserFarmings();
                $farmingIds = array_keys($farmings);

                // get all crop layer farmings
                $options = [
                    'tablename' => $UserDbController->DbHandler->cropLayersTable,
                    'return' => ['DISTINCT(farming)'],
                    'sort' => 'farming',
                    'order' => 'asc',
                    'where' => [
                        'farming' => ['column' => 'farming', 'compare' => 'IN', 'value' => $farmingIds],
                    ],
                ];

                $results = $UserDbController->getItemsByParams($options, false, false);
                $resultsCount = count($results);
                for ($i = 0; $i < $resultsCount; $i++) {
                    $return[] = [
                        'id' => $results[$i]['farming'],
                        'name' => $farmings[$results[$i]['farming']],
                    ];
                }

                break;
            case 'years':
                if (!isset($rpcParams['farming']) || !(int)$rpcParams['farming']) {
                    return [];
                }

                // get all crop layer farmings
                $options = [
                    'tablename' => $UserDbController->DbHandler->cropLayersTable,
                    'return' => ['DISTINCT(year)'],
                    'sort' => 'year',
                    'order' => 'asc',
                    'where' => [
                        'farming' => ['column' => 'farming', 'compare' => '=', 'value' => $rpcParams['farming']],
                    ],
                ];

                $results = $UserDbController->getItemsByParams($options, false, false);
                $resultsCount = count($results);
                for ($i = 0; $i < $resultsCount; $i++) {
                    $return[] = [
                        'id' => $results[$i]['year'],
                        'name' => $GLOBALS['Farming']['years'][$results[$i]['year']]['title'],
                    ];
                }

                break;
            case 'layers':
                if (!isset($rpcParams['farming']) || !(int)$rpcParams['farming']) {
                    return [];
                }
                if (!isset($rpcParams['year']) || !(int)$rpcParams['year']) {
                    return [];
                }

                // get all crop layer farmings
                $options = [
                    'tablename' => $UserDbController->DbHandler->cropLayersTable,
                    'where' => [
                        'farming' => ['column' => 'farming', 'compare' => '=', 'value' => $rpcParams['farming']],
                        'year' => ['column' => 'year', 'compare' => '=', 'value' => $rpcParams['year']],
                    ],
                    'sort' => 'id',
                    'order' => 'asc',
                ];

                $results = $UserDbController->getItemsByParams($options, false, false);
                $resultsCount = count($results);
                for ($i = 0; $i < $resultsCount; $i++) {
                    $return[] = [
                        'id' => $results[$i]['id'],
                        'name' => 'Референтен слой ' . $results[$i]['id'],
                    ];
                }

                break;
            default:
                break;
        }

        if ($rpcParams['selected'] && 'true' == $rpcParams['selected']) {
            $return[0]['selected'] = true;
        }

        return $return;
    }
}
