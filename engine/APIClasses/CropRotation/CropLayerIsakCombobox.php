<?php

namespace TF\Engine\APIClasses\CropRotation;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbCropRotation\UserDbCropRotationController;

// Prado::using('Plugins.Core.Layers.conf');
// Prado::using('Plugins.Core.Layers.*');

/**
 * Crop Layer Isak combobox.
 *
 * @rpc-module CropRotation
 *
 * @rpc-service-id crop-layer-isak-combobox
 */
class CropLayerIsakCombobox extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getCropLayerIsakComboboxData']],
        ];
    }

    /**
     * Return combobox data.
     *
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer cl_id
     *                         }
     *
     * @return array
     *               {
     *               #item float   area
     *               #item integer id
     *               #item string  name
     *               }
     */
    public function getCropLayerIsakComboboxData($rpcParams)
    {
        if ($this->User->isGuest || !$this->User->Database) {
            return [];
        }

        if (!isset($rpcParams['cl_id']) || !(int)$rpcParams['cl_id']) {
            return [];
        }

        // init all needed controllers
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbCropRotationController = new UserDbCropRotationController($this->User->Database);
        $LayersController = new LayersController('Layers');

        // getting crop layer data
        $cropLayerData = $UserDbCropRotationController->getCropLayersItemData($rpcParams['cl_id']);

        $options = [
            'where' => [
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'prefix' => 't', 'value' => $this->User->GroupID],
                'layer_type' => ['column' => 'layer_type', 'compare' => '=', 'prefix' => 't', 'value' => 6],
                'farming' => ['column' => 'farming', 'compare' => '=', 'prefix' => 't', 'value' => $cropLayerData['farming']],
                'year' => ['column' => 'year', 'compare' => '=', 'prefix' => 't', 'value' => $cropLayerData['year']],
            ],
        ];

        $layer_results = $LayersController->getLayers($options, false, false);
        if (1 != count($layer_results)) {
            return [];
        }

        $layerData = $layer_results[0];

        $options = [
            'tablename' => $layerData['table_name'],
            'return' => ['DISTINCT(prc_uin)', 'gid', 'St_Area(geom) as area'],
        ];

        $results = $UserDbController->getItemsByParams($options, false, false);
        $resultsCount = count($results);

        $return = [];

        for ($i = 0; $i < $resultsCount; $i++) {
            $return[] = [
                'name' => $results[$i]['prc_uin'],
                'id' => $results[$i]['gid'],
                'area' => number_format($results[$i]['area'] / 10000, 3),
            ];
        }

        return $return;
    }
}
