<?php

namespace TF\Engine\APIClasses\CropRotation;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

/**
 * Файлов архив за почвени проби.
 *
 * @rpc-module CropRotation
 *
 * @rpc-service-id soil-samples-files-grid
 *
 * @property UserDbController $UserDbController
 */
class SoilSamplesFilesGrid extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getSoilSamplesFilesGrid']],
        ];
    }

    /**
     * Returns grid data.
     *
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer cl_id
     *                         }
     *
     * @return array
     */
    public function getSoilSamplesFilesGrid($rpcParams)
    {
        if ($this->User->isGuest) {
            return [];
        }

        $UserDbController = new UserDbController($this->User->Database);

        $return = [
            'rows' => [],
            'total' => 0,
        ];

        $options = [
            'tablename' => $UserDbController->DbHandler->tableSoilSamplesFiles,
        ];

        $counter = $UserDbController->getItemsByParams($options, true, false);

        if (0 == $counter[0]['count']) {
            return $return;
        }

        $results = $UserDbController->getItemsByParams($options, false, false);
        $resultsCount = count($results);
        // get applied sample files
        $options = [
            'tablename' => $UserDbController->DbHandler->tableAppliedSoilSamples,
            'where' => [
                'crop_layer_id' => ['column' => 'crop_layer_id', 'compare' => '=', 'value' => $rpcParams['cl_id']],
            ],
        ];

        $rel_results = $UserDbController->getItemsByParams($options);
        $rel_resultsCount = count($rel_results);
        $relations = [];
        // iterate rel_results and create predefined array
        for ($i = 0; $i < $rel_resultsCount; $i++) {
            $relations[] = $rel_results[$i]['sample_file_id'];
        }

        for ($i = 0; $i < $resultsCount; $i++) {
            if (in_array($results[$i]['id'], $relations)) {
                $results[$i]['status'] = 'Приложен';
                $results[$i]['apply_status'] = true;
            } else {
                $results[$i]['status'] = 'Неприложен';
                $results[$i]['apply_status'] = false;
            }
        }

        return [
            'rows' => $results,
            'total' => $counter[0]['count'],
        ];
    }
}
