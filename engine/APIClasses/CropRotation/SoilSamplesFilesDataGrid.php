<?php

namespace TF\Engine\APIClasses\CropRotation;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

// Prado::using('Plugins.Core.Farming.conf');

/**
 * [description].
 *
 * @rpc-module CropRotation
 *
 * @rpc-service-id soil-samples-files-datagrid
 */
class SoilSamplesFilesDataGrid extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getSoilSamplesFilesDataGrid'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * Return grid data.
     *
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer file_id
     *                         #item integer cl_id
     *                         }
     * @param int $page pagination parameter
     * @param int $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array
     */
    public function getSoilSamplesFilesDataGrid(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        if ($this->User->isGuest) {
            return [];
        }

        $UserDbController = new UserDbController($this->User->Database);

        // define default empty return
        $return = [
            'rows' => [],
            'total' => 0,
        ];

        // check if file info is given correctly
        if (!$rpcParams['file_id'] || !(int)$rpcParams['file_id'] || !$rpcParams['cl_id'] || !(int)$rpcParams['cl_id']) {
            return $return;
        }

        //
        // HANDLING CROP LAYER DATA
        //
        // getting the crop layer data
        $options = [
            'tablename' => $UserDbController->DbHandler->cropLayersDataTable,
            'where' => [
                'layer_id' => ['column' => 'crop_layer_id', 'compare' => '=', 'value' => $rpcParams['cl_id']],
            ],
        ];

        $cl_results = $UserDbController->getItemsByParams($options, false);

        // iterate crop layer results and create array of ISAK numbers
        $isak_array = [];
        $clResultsCount = count($cl_results);
        if ($clResultsCount) {
            for ($i = 0; $i < $clResultsCount; $i++) {
                if ($cl_results[$i]['has_number']) {
                    // insert into the isak array
                    $isak_array[] = $cl_results[$i]['isak_number'];
                }
            }
        }

        //
        // HANDLING FILE DATA
        //
        // define ss_files directory and targeted file
        $filePath = SOIL_SAMPLES_QUEUE_PATH . $this->User->GroupID . '/' . $rpcParams['file_id'] . '.csv';
        if (!file_exists(SOIL_SAMPLES_QUEUE_PATH . $this->User->GroupID)) {
            mkdir(SOIL_SAMPLES_QUEUE_PATH . $this->User->GroupID, 0777);
            touch($filePath);
        } else {
            touch($filePath);
        }

        // check if file exists
        if (!file_exists($filePath)) {
            return $return;
        }

        // taking file information
        $fileRows = array_map('str_getcsv', file($filePath));
        // taking and removing headers
        $header = array_shift($fileRows);
        // taking every row and putting it as new element in array
        $csv = [];
        foreach ($fileRows as $row) {
            $csv[] = explode(';', $row[0]);
        }

        // check in number of fields is correct
        if (9 != count($csv[0])) {
            return $return;
        }

        //
        // PAGINATION
        //
        $offset = ($page - 1) * $rows;

        // calculating the page limit. If there are less elements than the page limit we calculate it using the maximum elements
        if ($page * $rows > count($fileRows)) {
            $limit = ($page * $rows) - (($page * $rows) - count($fileRows));
        } else {
            $limit = $page * $rows;
        }

        //
        // END OF PAGINATION
        //
        // assume that the first column is BZS
        $results = [];
        for ($i = $offset; $i < $limit; $i++) {
            // rows need to be counted from 0 every time so $i will not work
            $row_num = $i - $offset;

            // check if isak_number exists in the crop layer data grid
            $isak = $csv[$i][1];

            if (in_array($isak, $isak_array)) {
                $results[$row_num]['found'] = true;
            } else {
                $results[$row_num]['found'] = false;
            }

            // transform the other results for grid view
            $results[$row_num]['ss_num'] = $csv[$i][0];
            $results[$row_num]['isak'] = $csv[$i][1];
            $results[$row_num]['ph_h2o'] = $csv[$i][2];
            $results[$row_num]['no3'] = $csv[$i][3];
            $results[$row_num]['nh4'] = $csv[$i][4];
            $results[$row_num]['p2o5'] = $csv[$i][5];
            $results[$row_num]['k2o'] = $csv[$i][6];
            $results[$row_num]['meh_sastav'] = $GLOBALS['Farming']['soil_types'][$csv[$i][7]]['name'];
            $results[$row_num]['sud_humus'] = $GLOBALS['Farming']['sastav_humus'][$csv[$i][8]]['name'];
            $results[$row_num]['row_number'] = $i;
        }

        return [
            'rows' => $results,
            'total' => count($fileRows),
        ];
    }
}
