<?php

namespace TF\Engine\APIClasses\CropRotation;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbCropRotation\UserDbCropRotationController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * [description].
 *
 * @rpc-module CropRotation
 *
 * @rpc-service-id soil-samples-datagrid
 */
class SoilSamplesGrid extends TRpcApiProvider
{
    private $module = 'CropRotation';
    private $service_id = 'soil-samples-datagrid';

    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getSoilSamplesGrid'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'add' => ['method' => [$this, 'addSoilSample']],
            'markForEdit' => ['method' => [$this, 'editSampleMark']],
            'delete' => ['method' => [$this, 'deleteSoilSample']],
        ];
    }

    /**
     * Return grid data.
     *
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer cl_id
     *                         #item date    date_from
     *                         #item date    date_to
     *                         #item integer isak
     *                         }
     * @param int $page pagination parameter
     * @param int $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array
     */
    public function getSoilSamplesGrid(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        // check if "get" info is correct
        if (!(int)$rpcParams['cl_id']) {
            return [];
        }

        // init all needed controllers
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbCropRotationController = new UserDbCropRotationController($this->User->Database);

        $return = [
            'total' => 0,
            'rows' => [],
        ];

        $offset = ($page - 1) > 0 ? ($page - 1) * $rows : 0;
        $options = [
            'return' => [
                '*', 'date(sample_date) as converted_sample_date',
            ],

            'offset' => $offset,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
            'where' => [
                'cl_id' => ['column' => 'crop_layer_id', 'compare' => '=', 'prefix' => 'cld', 'value' => $rpcParams['cl_id']],
                'date_from' => ['column' => 'CAST(sample_date as DATE)', 'compare' => '>=', 'value' => $rpcParams['date_from']],
                'date_to' => ['column' => 'CAST(sample_date as DATE)', 'compare' => '<=', 'value' => $rpcParams['date_to']],
                'isak' => ['column' => 'isak_number', 'compare' => '=', 'value' => $rpcParams['isak']],
            ],
        ];

        if ('sample_num' == $options['sort']) {
            $options['sort'] = 'substring(sample_num from 1 for 3) ' . $options['order'] . ', length(sample_num) ' . $options['order'] . ', substring(sample_num from 4 for 2) ';
        }

        $counter = $UserDbCropRotationController->getCropLayerDataSoilSamples($options, true, false);
        if (0 == $counter[0]['count']) {
            return $return;
        }

        $results = $UserDbCropRotationController->getCropLayerDataSoilSamples($options, false, false);
        $resultsCount = count($results);
        for ($i = 0; $i < $resultsCount; $i++) {
            $results[$i]['azot_mg'] = number_format($results[$i]['azot'], 2);
            $results[$i]['fosfor_mg'] = number_format($results[$i]['fosfor'], 2);
            $results[$i]['kalii_mg'] = number_format($results[$i]['kalii'], 2);

            $meh_sastav = $GLOBALS['Farming']['soil_types'][$results[$i]['meh_sastav']]['coef'];

            $results[$i]['azot'] = number_format($results[$i]['azot'] * $meh_sastav, 2);
            $results[$i]['fosfor'] = number_format($results[$i]['fosfor'] * $meh_sastav, 2);
            $results[$i]['kalii'] = number_format($results[$i]['kalii'] * $meh_sastav, 2);

            $results[$i]['sample_date'] = $results[$i]['converted_sample_date'];
            $results[$i]['meh_sastav'] = $GLOBALS['Farming']['soil_types'][$results[$i]['meh_sastav']]['name'];
            $results[$i]['sud_humus'] = $GLOBALS['Farming']['sastav_humus'][$results[$i]['sud_humus']]['name'];
        }

        $return = [
            'rows' => $results,
            'total' => $counter[0]['count'],
        ];

        return $return;
    }

    /**
     * Add new soil sample for the selected plot.
     *
     * @api-method add
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer crop_layer_id
     *                         #item integer sample_id
     *                         #item date    date
     *                         #item string  num
     *                         #item string  isak
     *                         #item float   azot
     *                         #item float   fosfor
     *                         #item float   kalii
     *                         #item float   ph
     *                         #item integer meh
     *                         #item integer humus
     *                         }
     *
     * @throws MTRpcException
     */
    public function addSoilSample($rpcParams)
    {
        $UserDbCropRotationController = new UserDbCropRotationController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController();

        // getting existing sample numbers numbers
        $options = [
            'where' => [
                'crop_layer_id' => ['column' => 'crop_layer_id', 'compare' => '=', 'value' => $rpcParams['crop_layer_id']],
            ],
        ];

        $ss_results = $UserDbCropRotationController->getCropLayerDataSoilSamples($options, false, false);
        $ss_resultsCount = count($ss_results);
        $sample_num_array = [];

        for ($i = 0; $i < $ss_resultsCount; $i++) {
            $sample_num_array[] = $ss_results[$i]['sample_num'];
        }

        if (0 == strlen($rpcParams['sample_id'])) {
            if (!in_array($rpcParams['num'], $sample_num_array)) {
                $options = [
                    'tablename' => $UserDbController->DbHandler->tableSoilSamples,
                    'mainData' => [
                        'crop_layer_data_id' => $rpcParams['isak_number'],
                        'sample_date' => $rpcParams['sample_date'],
                        'sample_num' => $rpcParams['sample_num'],
                        'azot' => $rpcParams['azot'],
                        'fosfor' => $rpcParams['fosfor'],
                        'kalii' => $rpcParams['kalii'],
                        'ph' => $rpcParams['ph'],
                        'meh_sastav' => $rpcParams['meh_sastav'],
                        'sud_humus' => $rpcParams['sud_humus'],
                    ],
                ];

                $recordID = $UserDbController->addItem($options);

                $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options, $recordID, 'Adding soil sample');
            } else {
                throw new MTRpcException('existing_soil_sample_number', -33251);
            }
        } else {
            $options = [
                'tablename' => $UserDbController->DbHandler->tableSoilSamples,
                'where' => [
                    'id' => $rpcParams['sample_id'],
                ],
                'mainData' => [
                    'crop_layer_data_id' => $rpcParams['isak_number'],
                    'sample_date' => $rpcParams['sample_date'],
                    'sample_num' => $rpcParams['sample_num'],
                    'azot' => $rpcParams['azot'],
                    'fosfor' => $rpcParams['fosfor'],
                    'kalii' => $rpcParams['kalii'],
                    'ph' => $rpcParams['ph'],
                    'meh_sastav' => $rpcParams['meh_sastav'],
                    'sud_humus' => $rpcParams['sud_humus'],
                ],
            ];

            $UserDbController->editItem($options);

            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options, $rpcParams['sample_id'], 'Editing soil sample');
        }
    }

    /**
     * Return data for the selected sample to fill the edit values.
     *
     * @api-method markForEdit
     *
     * @param int $rpcParam
     *
     * @return array
     */
    public function editSampleMark($rpcParam)
    {
        $UserDbCropRotationController = new UserDbCropRotationController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableSoilSamples,
            'return' => [
                '*', 'date(sample_date) as converted_sample_date',
            ],
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'prefix' => 'ss', 'value' => $rpcParam],
            ],
        ];

        $results = $UserDbCropRotationController->getCropLayerDataSoilSamples($options, false, false);
        $results[0]['sample_date'] = date('Y-m-d', strtotime($results[0]['sample_date']));

        return $results[0];
    }

    /**
     * Delete the selected soil sample.
     *
     * @api-method delete
     *
     * @param int $rpcParam
     */
    public function deleteSoilSample($rpcParam)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $sample = $this->editSampleMark($rpcParam);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableSoilSamples,
            'id_string' => $rpcParam,
        ];

        $UserDbController->deleteItemsByParams($options);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $sample, $options, 'Deleting soil sample');
    }
}
