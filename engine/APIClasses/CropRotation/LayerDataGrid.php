<?php

namespace TF\Engine\APIClasses\CropRotation;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Парцели от референтен слой.
 *
 * @rpc-module CropRotation
 *
 * @rpc-service-id layer-data-grid
 */
class LayerDataGrid extends TRpcApiProvider
{
    private $module = 'CropRotation';
    private $service_id = 'layer-data-grid';

    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getCropRotation'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'add' => ['method' => [$this, 'addCropLayerData'],
                'validators' => [
                    'rpcParams' => [
                        'field' => 'validateText',
                        'isak' => 'validateText',
                        'area' => 'validateNumber',
                        'culture_1' => 'validateInteger',
                        'culture_2' => 'validateInteger',
                        'culture_3' => 'validateInteger',
                        'culture_4' => 'validateInteger',
                        'culture_5' => 'validateInteger',
                        'cl_id' => 'validateInteger',
                    ],
                ]],
            'delete' => ['method' => [$this, 'deleteCropLayerData']],
            'saveChanges' => ['method' => [$this, 'saveChangesCropData'],
                'validators' => [
                    'rpcParams' => [
                        'id' => 'validateInteger',
                        'data' => [
                            'culture1' => 'validateInteger',
                            'culture2' => 'validateInteger',
                            'culture3' => 'validateInteger',
                            'culture4' => 'validateInteger',
                            'culture5' => 'validateInteger',
                            'yield1' => 'validateNumber',
                            'yield2' => 'validateNumber',
                            'yield3' => 'validateNumber',
                            'yield4' => 'validateNumber',
                            'yield5' => 'validateNumber',
                        ],
                    ],
                ],
            ],
            'multiEdit' => ['method' => [$this, 'executeMultiEdit'],
                'validators' => [
                    'rpcParams' => [
                        'oldcrop' => 'validateText',
                        'newcrop' => 'validateTextArray',
                        'years' => 'validateIntegerArray',
                        'ids' => 'validateIntegerArray',
                    ],
                ],
            ],
        ];
    }

    /**
     * Return grid data.
     *
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer layer_id
     *                         }
     * @param ?string $sort
     * @param ?string $order
     *
     * @return array
     */
    public function getCropRotation(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        // init all needed controllers
        $UserDbController = new UserDbController($this->User->Database);
        $FarmingController = new FarmingController('Farming');

        $options = [
            'tablename' => $UserDbController->DbHandler->cropLayersDataTable,
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
            'where' => [
                'layer_id' => ['column' => 'crop_layer_id', 'compare' => '=', 'value' => $rpcParams['layer_id']],
            ],
        ];

        $data = $UserDbController->getItemsByParams($options, false);
        $dataCount = count($data);
        $counter = $UserDbController->getItemsByParams($options, true);

        for ($i = 0; $i < $dataCount; $i++) {
            unset($data[$i]['geom']);

            $data[$i]['culture1_code'] = $data[$i]['culture1'];
            $data[$i]['culture2_code'] = $data[$i]['culture2'];
            $data[$i]['culture3_code'] = $data[$i]['culture3'];
            $data[$i]['culture4_code'] = $data[$i]['culture4'];
            $data[$i]['culture5_code'] = $data[$i]['culture5'];

            $data[$i]['culture1'] = $GLOBALS['Farming']['crops'][$data[$i]['culture1']]['crop_name'];
            $data[$i]['culture2'] = $GLOBALS['Farming']['crops'][$data[$i]['culture2']]['crop_name'];
            $data[$i]['culture3'] = $GLOBALS['Farming']['crops'][$data[$i]['culture3']]['crop_name'];
            $data[$i]['culture4'] = $GLOBALS['Farming']['crops'][$data[$i]['culture4']]['crop_name'];
            $data[$i]['culture5'] = $GLOBALS['Farming']['crops'][$data[$i]['culture5']]['crop_name'];
        }

        $return['rows'] = $data;
        $return['total'] = $counter[0]['count'];

        return $return;
    }

    /**
     * Add new crop rotation row.
     *
     * @api-method add
     *
     * @param array $rpcParams
     *                         {
     *                         #item float   area
     *                         #item integer culture_1
     *                         #item integer culture_2
     *                         #item integer culture_3
     *                         #item integer culture_4
     *                         #item integer culture_5
     *                         #item string  field
     *                         #item integer isak
     *                         }
     */
    public function addCropLayerData($rpcParams)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $LayersController = new LayersController('Layers');
        $UsersController = new UsersController('Users');

        $options = [
            'tablename' => $UserDbController->DbHandler->cropLayersTable,
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $rpcParams['cl_id']],
            ],
        ];
        $cropLayerData = $UserDbController->getItemsByParams($options);

        $layer_options = [
            'farming' => $cropLayerData[0]['farming'],
            'year' => $cropLayerData[0]['year'],
            'user_id' => $this->User->GroupID,
            'layer_type' => 6,
            'return' => ['table_name'],
        ];

        $layer_data = $LayersController->getLayersIdByParams($layer_options);

        $hasNumber = $UserDbController->getIsakLayerNumberExist($layer_data['table_name'], $rpcParams['isak']);

        $options = [
            'tablename' => $UserDbController->DbHandler->cropLayersDataTable,
            'mainData' => [
                'plot' => (int)$rpcParams['field'],
                'isak_number' => $rpcParams['isak'],
                'area' => (float)$rpcParams['area'],
                'culture1' => (int)$rpcParams['culture_1'],
                'culture2' => (int)$rpcParams['culture_2'],
                'culture3' => (int)$rpcParams['culture_3'],
                'culture4' => (int)$rpcParams['culture_4'],
                'culture5' => (int)$rpcParams['culture_5'],
                'crop_layer_id' => (int)$rpcParams['cl_id'],
                'has_number' => (int)$hasNumber,
            ],
        ];

        $id = $UserDbController->addItem($options);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options, $id, 'Adding crop layer data');
    }

    /**
     * Removes the selected crop layers.
     *
     * @api-method delete
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer id
     *                         }
     */
    public function deleteCropLayerData($rpcParams)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $id_array = [];
        $rpcParamsCount = count($rpcParams);
        for ($i = 0; $i < $rpcParamsCount; $i++) {
            $id_array[] = $rpcParams[$i]['id'];
        }
        $id_string = implode(', ', $id_array);

        $options = [
            'tablename' => $UserDbController->DbHandler->cropLayersDataTable,
            'where' => [
                'id' => ['column' => 'id', 'compare' => 'IN', 'value' => $id_array],
            ],
        ];
        $cropLayerData = $UserDbController->getItemsByParams($options);

        $options = [
            'tablename' => $UserDbController->DbHandler->cropLayersDataTable,
            'id_string' => $id_string,
        ];

        $UserDbController->deleteItemsByParams($options);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $cropLayerData, $options, 'Deleting crop layer data');
    }

    /**
     * Save the changes when editing row.
     *
     * @api-method saveChanges
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer id
     *                         #item array data
     *                         {
     *                         #item integer culture1
     *                         #item integer culture2
     *                         #item integer culture3
     *                         #item integer culture4
     *                         #item integer culture5
     *                         #item float   yield1
     *                         #item float   yield2
     *                         #item float   yield3
     *                         #item float   yield4
     *                         #item float   yield5
     *                         }
     *                         }
     */
    public function saveChangesCropData($rpcParams)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $options = [
            'tablename' => $UserDbController->DbHandler->cropLayersDataTable,
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $rpcParams['id']],
            ],
        ];
        $cropLayerData = $UserDbController->getItemsByParams($options);

        $options = [
            'tablename' => $UserDbController->DbHandler->cropLayersDataTable,
            'where' => [
                'id' => $rpcParams['id'],
            ],
        ];

        if ($rpcParams['data']['culture1']) {
            $options['mainData']['culture1'] = $rpcParams['data']['culture1'];
        }
        if ($rpcParams['data']['culture2']) {
            $options['mainData']['culture2'] = $rpcParams['data']['culture2'];
        }
        if ($rpcParams['data']['culture3']) {
            $options['mainData']['culture3'] = $rpcParams['data']['culture3'];
        }
        if ($rpcParams['data']['culture4']) {
            $options['mainData']['culture4'] = $rpcParams['data']['culture4'];
        }
        if ($rpcParams['data']['culture5']) {
            $options['mainData']['culture5'] = $rpcParams['data']['culture5'];
        }

        if ($rpcParams['data']['yield1']) {
            $options['mainData']['yield1'] = $rpcParams['data']['yield1'];
        }
        if ($rpcParams['data']['yield2']) {
            $options['mainData']['yield2'] = $rpcParams['data']['yield2'];
        }
        if ($rpcParams['data']['yield3']) {
            $options['mainData']['yield3'] = $rpcParams['data']['yield3'];
        }
        if ($rpcParams['data']['yield4']) {
            $options['mainData']['yield4'] = $rpcParams['data']['yield4'];
        }
        if ($rpcParams['data']['yield5']) {
            $options['mainData']['yield5'] = $rpcParams['data']['yield5'];
        }

        for ($i = 1; $i <= 5; $i++) {
            if ('null' == $options['mainData']['culture' . $i]) {
                $options['mainData']['culture' . $i] = 0;
            }
        }

        $UserDbController->editItem($options);

        // log user action
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $cropLayerData, $options, 'Editing crop layer data');
    }

    /**
     * Edit multiple crop layers at a time.
     *
     * @api-method multiEdit
     *
     * @param array $rpcParams
     *                         {
     *                         #item string oldcrop
     *                         #item array  newcrop
     *                         #item string    #item string crop_id
     *                         #item array  years
     *                         #item string    #item integer year
     *                         #item array  ids
     *                         #item string     #item integer id
     *                         }
     */
    public function executeMultiEdit($rpcParams)
    {
        $UsersController = new UsersController('Users');
        $UserDbController = new UserDbController($this->User->Database);

        $years = $rpcParams['years'];
        $yearsCount = count($years);
        $ids = $rpcParams['ids'];
        $oldCrop = $rpcParams['oldcrop'];
        $newCrop = $rpcParams['newcrop'];
        $dobiv = $rpcParams['dobiv'];
        $update = false;
        $idsCount = count($ids);
        for ($j = 0; $j < $idsCount; $j++) {
            $id = $ids[$j];

            $options = [
                'tablename' => 'su_crop_layers_data',
                'where' => [
                    'id' => $id,
                ],
            ];

            // check if new crop is null and make to work with no crop
            if ('null' == $newCrop[0]) {
                $newCrop = [0 => '0'];
            }

            if (($oldCrop && $newCrop) || '0' == $oldCrop) {
                $cropYearsData = $UserDbController->getCropsByYears($id, $oldCrop, $years);
                $cropYearsDataCount = count($cropYearsData);
                if ($cropYearsData) {
                    for ($i = 0; $i < $cropYearsDataCount; $i++) {
                        $changedYearCrop = $cropYearsData[$i];

                        $options['mainData']['culture' . $changedYearCrop] = $newCrop[0];

                        if ($dobiv) {
                            $options['mainData']['yield' . $changedYearCrop] = $dobiv;
                        }
                    }
                    $update = true;
                }
            }

            if ($dobiv && !$oldCrop && !$newCrop) {
                $dobiv = str_replace(',', '.', $dobiv);
                $dobiv = number_format($dobiv, 2);

                for ($i = 0; $i < $yearsCount; $i++) {
                    if (null == $years[$i]) {
                        continue;
                    }

                    $options['mainData']['yield' . $years[$i]] = $dobiv;
                }
                $update = true;
            }

            if ($update) {
                $update = false;
                $UserDbController->editItem($options);
                $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options, [], 'Executing multi edit');
            }
        }
    }
}
