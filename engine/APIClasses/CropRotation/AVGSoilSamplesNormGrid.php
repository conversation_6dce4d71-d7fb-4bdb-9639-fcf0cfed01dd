<?php

namespace TF\Engine\APIClasses\CropRotation;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbCropRotation\UserDbCropRotationController;

// Prado::using('Plugins.Core.Farming.conf');

/**
 * [description].
 *
 * @rpc-module CropRotation
 *
 * @rpc-service-id avg-soil-samples-norm-grid
 */
class AVGSoilSamplesNormGrid extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getAVGSoilSamplesNormGrid']],
        ];
    }

    /**
     * Return grid data.
     *
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer year
     *                         #item integer cl_id
     *                         }
     *
     * @return array
     */
    public function getAVGSoilSamplesNormGrid($rpcParams)
    {
        // init all needed controllers
        $UserDbCropRotationController = new UserDbCropRotationController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);

        $return = [
            'total' => 0,
            'rows' => [],
        ];

        if (!$rpcParams['year'] || !$rpcParams['cl_id']) {
            return $return;
        }

        // adding requested year and crop_layer_id to new variables
        $req_year = $rpcParams['year'];
        $cl_id = $rpcParams['cl_id'];

        $options = [
            'tablename' => $UserDbController->DbHandler->cropLayersTable,
            'where' => [
                'cl_id' => ['column' => 'id', 'compare' => '=', 'value' => $cl_id],
            ],
        ];

        $cropLayerData = $UserDbController->getItemsByParams($options, false, false);

        if (0 == count($cropLayerData)) {
            return $return;
        }

        // assigning the referent layer year
        $ref_year = $cropLayerData[0]['year'];

        if ($req_year < $ref_year) {
            return $return;
        }

        $options = [
            'where' => [
                'cl_id' => ['column' => 'crop_layer_id', 'compare' => '=', 'prefix' => 'cld', 'value' => $cl_id],
                'isak' => ['column' => 'isak_number', 'compare' => '=', 'prefix' => 'cld', 'value' => $_POST['isak']],
                // 'year' => array('column' => 'EXTRACT(YEAR FROM sample_date)', 'compare' => '=', 'value' => $GLOBALS['Farming']['years'][$ref_year]['year']),
                'has_number' => ['column' => 'has_number', 'compare' => '=', 'prefix' => 'cld', 'value' => 1],
            ],
        ];

        $counter = $UserDbCropRotationController->getCropLayerDataSoilSamples($options, true, false);

        if (0 == $counter[0]['count']) {
            return $return;
        }

        $results = $UserDbCropRotationController->getCropLayerDataSoilSamples($options, false, false);
        $resultsCount = count($results);
        // calculate year iterations
        $year_iterations = $req_year - $ref_year;

        for ($j = 1; $j <= $year_iterations; $j++) {
            // iterate over the results and calculate norm
            for ($i = 0; $i < $resultsCount; $i++) {
                // assign variables for norm calculation
                if (1 == $j) {
                    $meh_sastav = $GLOBALS['Farming']['soil_types'][$results[$i]['meh_sastav']]['coef'];

                    $azot = $results[$i]['azot'] * $meh_sastav;
                    $fosfor = $results[$i]['fosfor'] * $meh_sastav;
                    $kalii = $results[$i]['kalii'] * $meh_sastav;
                    $yield = $results[$i]['yield2'];
                    $culture = $results[$i]['culture2'];
                    $humus = $results[$i]['sud_humus'];
                    $multiyear = false;
                } else {
                    $nextCulture = (string)'culture' . ($j + 1);
                    $nextYeild = (string)'yield' . ($j + 1);
                    $azot = $results[$i]['azot_remaining'];
                    $fosfor = $results[$i]['fosfor_remaining'];
                    $kalii = $results[$i]['kalii_remaining'];
                    $yield = $results[$i][$nextYeild];
                    $culture = $results[$i][$nextCulture];
                    $humus = $results[$i]['sud_humus'];
                    $multiyear = true;
                }

                // calculate azot norm and remaining
                $azot_results = $this->calculateAzotNorm($azot, $yield, $culture, $humus, $multiyear);

                $results[$i]['azot_norm'] = $azot_results['norm'];
                $results[$i]['azot_remaining'] = $azot_results['remaining'];
                $results[$i]['azot_access'] = $azot_results['access'];
                $results[$i]['azot_need'] = $azot_results['need'];

                // calculate fosfor norm and remaining
                $fosfor_results = $this->calculateFosforNorm($fosfor, $yield, $culture, $humus);

                $results[$i]['fosfor_norm'] = $fosfor_results['norm'];
                $results[$i]['fosfor_remaining'] = $fosfor_results['remaining'];
                $results[$i]['fosfor_access'] = $fosfor_results['access'];
                $results[$i]['fosfor_need'] = $fosfor_results['need'];

                // calculate kalii norm and remaining
                $kalii_results = $this->calculateKaliiNorm($kalii, $yield, $culture, $humus);

                $results[$i]['kalii_norm'] = $kalii_results['norm'];
                $results[$i]['kalii_remaining'] = $kalii_results['remaining'];
                $results[$i]['kalii_access'] = $kalii_results['access'];
                $results[$i]['kalii_need'] = $kalii_results['need'];
            }
        }

        // create arrays for ISAK numbers and a temporary array for calculation
        $isak_array = [];
        $math_array = [];
        $return = [];
        $resultsCount = count($results);

        // calculate average of the results for every ISAK number
        for ($i = 0; $i < $resultsCount; $i++) {
            // check if isak number was already iterated or not
            if (!in_array($results[$i]['isak_number'], $isak_array)) {
                // add the isak number to the arrays
                $isak_array[] = $results[$i]['isak_number'];

                $math_array[$results[$i]['isak_number']] = [
                    'azot_norm' => $results[$i]['azot_norm'],
                    'azot_access' => $results[$i]['azot_access'],
                    'azot_need' => $results[$i]['azot_need'],
                    'fosfor_norm' => $results[$i]['fosfor_norm'],
                    'fosfor_access' => $results[$i]['fosfor_access'],
                    'fosfor_need' => $results[$i]['fosfor_need'],
                    'kalii_norm' => $results[$i]['kalii_norm'],
                    'kalii_access' => $results[$i]['kalii_access'],
                    'kalii_need' => $results[$i]['kalii_need'],
                    'item_count' => 1,
                ];
            } else {
                $math_array[$results[$i]['isak_number']]['azot_norm'] += $results[$i]['azot_norm'];
                $math_array[$results[$i]['isak_number']]['azot_access'] += $results[$i]['azot_access'];
                $math_array[$results[$i]['isak_number']]['azot_need'] += $results[$i]['azot_need'];

                $math_array[$results[$i]['isak_number']]['fosfor_norm'] += $results[$i]['fosfor_norm'];
                $math_array[$results[$i]['isak_number']]['fosfor_access'] += $results[$i]['fosfor_access'];
                $math_array[$results[$i]['isak_number']]['fosfor_need'] += $results[$i]['fosfor_need'];

                $math_array[$results[$i]['isak_number']]['kalii_norm'] += $results[$i]['kalii_norm'];
                $math_array[$results[$i]['isak_number']]['kalii_access'] += $results[$i]['kalii_access'];
                $math_array[$results[$i]['isak_number']]['kalii_need'] += $results[$i]['kalii_need'];

                ++$math_array[$results[$i]['isak_number']]['item_count'];
            }
        }

        // formatting to datagrid format
        foreach ($math_array as $key => $values) {
            $return[] = [
                'isak_number' => $key,
                'azot_norm' => number_format($values['azot_norm'] / $values['item_count'], 2),
                'azot_access' => $values['azot_access'] / $values['item_count'],
                'azot_need' => $values['azot_need'] / $values['item_count'],
                'fosfor_norm' => number_format($values['fosfor_norm'] / $values['item_count'], 2),
                'fosfor_access' => $values['fosfor_access'] / $values['item_count'],
                'fosfor_need' => $values['fosfor_need'] / $values['item_count'],
                'kalii_norm' => number_format($values['kalii_norm'] / $values['item_count'], 2),
                'kalii_access' => $values['kalii_access'] / $values['item_count'],
                'kalii_need' => $values['kalii_need'] / $values['item_count'],
                'sample_count' => $values['item_count'],
            ];
        }
        $returnArray = [];
        $returnArray['rows'] = $return;
        $returnArray['total'] = count($return);

        return $returnArray;
    }

    private function calculateAzotNorm($azot, $yield, $culture, $humus, $multiyear)
    {
        if ($multiyear) {
            $content = $azot;
            $access = $content;
        } else {
            $content = $azot + $GLOBALS['Farming']['sastav_humus'][$humus]['coef'];
            $access = $content * $GLOBALS['Farming']['nutrients']['1']['coef'];
        }

        $need = ($yield / 100) * $GLOBALS['Farming']['crops'][$culture]['azot'];

        if (($access - $need) < 0) {
            $norm = ($need - $access) / $GLOBALS['Farming']['nutrients']['1']['coef'];
        } else {
            $norm = 0;
        }

        $remaining = $GLOBALS['Farming']['sastav_humus'][$humus]['coef'];

        return [
            'norm' => round($norm, 2),
            'remaining' => round($remaining, 2),
            'access' => round($access, 2),
            'need' => round($need, 2),
        ];
    }

    private function calculateFosforNorm($fosfor, $yield, $culture, $humus)
    {
        $content = $fosfor;
        $access = $content * $GLOBALS['Farming']['nutrients']['2']['coef'];
        $need = ($yield / 100) * $GLOBALS['Farming']['crops'][$culture]['fosfor'];

        if (($access - $need) < 0) {
            $norm = ($need - $access) / $GLOBALS['Farming']['nutrients']['2']['coef'];
        } else {
            $norm = 0;
        }

        $remaining = $content - $need + $norm;

        return [
            'norm' => round($norm, 2),
            'remaining' => round($remaining, 2),
            'access' => round($access, 2),
            'need' => round($need, 2),
        ];
    }

    private function calculateKaliiNorm($kalii, $yield, $culture, $humus)
    {
        $content = $kalii;
        $access = $content * $GLOBALS['Farming']['nutrients']['3']['coef'];
        $need = ($yield / 100) * $GLOBALS['Farming']['crops'][$culture]['kalii'];

        if (($access - $need) < 0) {
            $norm = ($need - $access) / $GLOBALS['Farming']['nutrients']['3']['coef'];
        } else {
            $norm = 0;
        }

        $remaining = $content - $need + $norm;

        return [
            'norm' => round($norm, 2),
            'remaining' => round($remaining, 2),
            'access' => round($access, 2),
            'need' => round($need, 2),
        ];
    }
}
