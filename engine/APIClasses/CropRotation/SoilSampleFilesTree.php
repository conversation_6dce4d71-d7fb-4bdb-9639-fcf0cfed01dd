<?php

namespace TF\Engine\APIClasses\CropRotation;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbCropRotation\UserDbCropRotationController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Tree 'Файлове за почвени проби'.
 *
 * @rpc-module CropRotation
 *
 * @rpc-service-id sample-files-tree
 */
class SoilSampleFilesTree extends TRpcApiProvider
{
    private $module = 'CropRotation';
    private $service_id = 'sample-files-tree';

    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getSoilSampleFilesTree']],
            'applyFile' => ['method' => [$this, 'applySoilSampleFile'],
                'validators' => [
                    'file_id' => 'validateInteger',
                    'crop_layer_id' => 'validateInteger',
                ]],
            'editRow' => ['method' => [$this, 'editSoilSampleRowData'],
                'validators' => [
                    'file_id' => 'validateInteger',
                    'row_num_array' => 'validateIntegerArray',
                    'editRowText' => 'validateText',
                ]],
            'deleteSoilSampleFile' => ['method' => [$this, 'deleteSoilSampleFile']],
        ];
    }

    /**
     * Return tree data.
     *
     * @api-method read
     *
     * @return array
     */
    public function getSoilSampleFilesTree()
    {
        $UserDbController = new UserDbController($this->User->Database);

        $return = [
            'rows' => [],
            'total' => 0,
        ];

        $options = [
            'tablename' => $UserDbController->DbHandler->tableSoilSamplesFiles,
        ];

        $counter = $UserDbController->getItemsByParams($options, true, false);

        if (0 == $counter[0]['count']) {
            return $return;
        }

        $results = $UserDbController->getItemsByParams($options, false, false);
        $resultsCount = count($results);
        $return = [];

        for ($i = 0; $i < $resultsCount; $i++) {
            $return[] = [
                'text' => $results[$i]['filename'],
                'id' => $results[$i]['id'],
                'iconCls' => 'icon-tree-csv',
            ];
        }

        return $return;
    }

    /**
     * Apply the selected soil sample file.
     *
     * @api-method applyFile
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer file_id
     *                         #item integer crop_layer_id
     *                         }
     *
     * @return array
     */
    public function applySoilSampleFile($rpcParams)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbCropRotationController = new UserDbCropRotationController($this->User->Database);
        $UsersController = new UsersController('Users');
        $return = null;

        $file_id = $rpcParams['file_id'];
        $cl_id = $rpcParams['crop_layer_id'];

        // getting ISAK numbers
        $options = [
            'tablename' => $UserDbController->DbHandler->cropLayersDataTable,
            'where' => [
                'layer_id' => ['column' => 'crop_layer_id', 'compare' => '=', 'value' => $cl_id],
            ],
        ];

        $cl_results = $UserDbController->getItemsByParams($options, false);
        $cl_resultsCount = count($cl_results);
        // forming a predefined array
        $isak_array = [];

        for ($i = 0; $i < $cl_resultsCount; $i++) {
            if ($cl_results[$i]['has_number']) {
                $isak_array[$cl_results[$i]['isak_number']] = $cl_results[$i]['id'];
            }
        }

        // getting existing sample numbers numbers
        $options = [
            'where' => [
                'crop_layer_id' => ['column' => 'crop_layer_id', 'compare' => '=', 'value' => $cl_id],
            ],
        ];

        $ss_results = $UserDbCropRotationController->getCropLayerDataSoilSamples($options, false, false);
        $ss_resultsCount = count($ss_results);

        $sample_num_array = [];

        for ($i = 0; $i < $ss_resultsCount; $i++) {
            $sample_num_array[] = $ss_results[$i]['sample_num'];
        }

        //
        // HANDLING FILE DATA
        //
        // define ss_files directory and targeted file
        $filePath = SOIL_SAMPLES_QUEUE_PATH . $this->User->GroupID . '/' . $file_id . '.csv';
        if (!file_exists(SOIL_SAMPLES_QUEUE_PATH . $this->User->GroupID)) {
            mkdir(SOIL_SAMPLES_QUEUE_PATH . $this->User->GroupID, 0777);
            touch($filePath);
        } else {
            touch($filePath);
        }

        // check if file exists
        if (!file_exists($filePath)) {
            return $return;
        }

        // taking file information
        $rows = array_map('str_getcsv', file($filePath));
        // taking and removing headers
        $header = array_shift($rows);
        // taking every row and putting it as new element in array
        $csv = [];
        foreach ($rows as $row) {
            $csv[] = explode(';', $row[0]);
        }

        // check in number of fields is correct
        if (9 != count($csv[0])) {
            return $return;
        }

        // create an array with ISAK numbers only
        $isak_num_array = array_keys($isak_array);

        // create the main soil sample options that will not be changed
        // the other options that change will be in the for cycle
        $options = [
            'tablename' => $UserDbController->DbHandler->tableSoilSamples,
            'mainData' => [
                'sample_date' => date('Y-m-d H:i:s'),
            ],
        ];

        $inserted_ids = [];

        $csvCount = count($csv);
        for ($i = 0; $i < $csvCount; $i++) {
            $row_isak = $csv[$i][1];

            if (in_array($row_isak, $isak_num_array) && !in_array($csv[$i][0], $sample_num_array)) {
                // create variables that will be used for calculation
                if (0 != strlen($csv[$i][7])) {
                    $numeric_meh_sastav = (int)$csv[$i][7];
                } else {
                    $numeric_meh_sastav = 2;
                }
                $meh_sastav = $GLOBALS['Farming']['soil_types'][$numeric_meh_sastav]['coef'];

                if (0 != strlen($csv[$i][8])) {
                    $numeric_humus = (int)$csv[$i][8];
                } else {
                    $numeric_humus = 2;
                }

                // if isak number is found we add a soil sample
                $options['mainData']['crop_layer_data_id'] = $isak_array[$row_isak];
                $options['mainData']['sample_num'] = $csv[$i][0];
                $options['mainData']['kalii'] = (float)$csv[$i][6];
                $options['mainData']['fosfor'] = (float)$csv[$i][5];
                $options['mainData']['ph'] = $csv[$i][2];
                $options['mainData']['azot'] = (float)($csv[$i][3] + $csv[$i][4]);
                $options['mainData']['meh_sastav'] = $numeric_meh_sastav;
                $options['mainData']['sud_humus'] = $numeric_humus;

                $inserted_ids[] = $UserDbController->addItem($options);
            }
        }

        // log user action
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $filePath, $inserted_ids, 'Applying soil sample file');
    }

    /**
     * Save the edits for the soil sample data file.
     *
     * @api-method editRow
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer file_id
     *                         #item integer row_num_array
     *                         #item string  editRowText
     *                         }
     *
     * @return array
     */
    public function editSoilSampleRowData($rpcParams)
    {
        $file_id = $rpcParams['file_id'];
        $row_num_array = $rpcParams['row_num_array'];
        $row_num_count = count($row_num_array);

        for ($i = 0; $i < $row_num_count; $i++) {
            if (!is_numeric($row_num_array[$i])) {
                return [];
            }
        }

        $filePath = SOIL_SAMPLES_QUEUE_PATH . $this->User->GroupID . '/' . $file_id . '.csv';
        if (!file_exists(SOIL_SAMPLES_QUEUE_PATH . $this->User->GroupID)) {
            mkdir(SOIL_SAMPLES_QUEUE_PATH . $this->User->GroupID, 0777);
            touch($filePath);
        } else {
            touch($filePath);
        }

        // check if file exists
        if (!file_exists($filePath)) {
            return [];
        }

        // taking file information
        $rows = array_map('str_getcsv', file($filePath));
        // taking and removing headers
        $header = array_shift($rows);

        for ($i = 0; $i < $row_num_count; $i++) {
            $current_row_num = $row_num_array[$i];
            $current_row = $rows[$current_row_num][0];
            $row_data = explode(';', $current_row);

            // adding the new ISAK number
            $row_data[1] = $rpcParams['editRowText'];

            // returning the data to the rows array
            $rows[$current_row_num][0] = implode(';', $row_data);
        }

        // rewritting file
        $fp = fopen($filePath, 'w');
        fputcsv($fp, $header);

        $rowsCount = count($rows);
        for ($i = 0; $i < $rowsCount; $i++) {
            fputcsv($fp, $rows[$i]);
        }

        fclose($fp);
    }

    /**
     * Delete the selected soil sample file.
     *
     * @api-method deleteSoilSampleFile
     *
     * @param int $rpcParam
     */
    public function deleteSoilSampleFile($rpcParam)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $file_id = $rpcParam;

        $ss_files_dir = SOIL_SAMPLES_QUEUE_PATH . $this->User->GroupID . '/';

        if (file_exists($ss_files_dir . $file_id . '.csv')) {
            @unlink($ss_files_dir . $file_id . '.csv');
        }

        // assign the id string for the delete query
        $options = [
            'tablename' => $UserDbController->DbHandler->tableSoilSamplesFiles,
            'id_string' => $file_id,
        ];
        $UserDbController->deleteItemsByParams($options);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options, $file_id, 'Deleting soil sample file');
    }
}
