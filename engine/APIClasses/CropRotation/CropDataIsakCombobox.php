<?php

namespace TF\Engine\APIClasses\CropRotation;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbCropRotation\UserDbCropRotationController;

/**
 * [description].
 *
 * @rpc-module CropRotation
 *
 * @rpc-service-id crop-data-isak-combobox
 */
class CropDataIsakCombobox extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getCropDataIsakCombobox']],
        ];
    }

    /**
     * Return combobox data.
     *
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer cl_id
     *                         }
     *
     * @return array
     */
    public function getCropDataIsakCombobox($rpcParams)
    {
        if (!isset($rpcParams['cl_id']) || !(int)$rpcParams['cl_id']) {
            return [];
        }

        // init all needed controllers
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbCropRotationController = new UserDbCropRotationController($this->User->Database);
        $LayersController = new LayersController('Layers');

        // getting crop layer data
        $cropLayerData = $UserDbCropRotationController->getCropLayersItemData($rpcParams['cl_id']);

        $options = [
            'where' => [
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'prefix' => 't', 'value' => $this->User->GroupID],
                'layer_type' => ['column' => 'layer_type', 'compare' => '=', 'prefix' => 't', 'value' => 6],
                'farming' => ['column' => 'farming', 'compare' => '=', 'prefix' => 't', 'value' => $cropLayerData['farming']],
                'year' => ['column' => 'year', 'compare' => '=', 'prefix' => 't', 'value' => $cropLayerData['year']],
            ],
        ];

        $layer_results = $LayersController->getLayers($options, false, false);
        if (1 != count($layer_results)) {
            return [];
        }

        $layerData = $layer_results[0];

        $options = [
            'tablename' => $layerData['table_name'],
            'return' => ['DISTINCT(isak_number)', 'cld.id', 'St_Area(geom) as area'],
            'where' => [
                'cl_id' => ['column' => 'crop_layer_id', 'compare' => '=', 'value' => $rpcParams['cl_id']],
            ],
        ];

        $results = $UserDbCropRotationController->getCropLayerIsakData($options, false, false);
        $resultsCount = count($results);

        $return = [];

        for ($i = 0; $i < $resultsCount; $i++) {
            $return[] = [
                'name' => $results[$i]['isak_number'],
                'id' => $results[$i]['id'],
                'area' => number_format($results[$i]['area'] / 10000, 3),
            ];
        }

        return $return;
    }
}
