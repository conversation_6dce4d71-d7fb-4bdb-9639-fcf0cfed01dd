<?php

namespace TF\Engine\APIClasses\CropRotation;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Kernel\PrintPdf
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbCropRotation\UserDbCropRotationController;

/**
 * 'Прилагане на данни към референтен слой'.
 *
 * @rpc-module CropRotation
 *
 * @rpc-service-id crop-rotation-exports
 */
class CropRotationExports extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'exportSoilSample' => ['method' => [$this, 'exportSoilSample']],
            'exportFullSampleNorm' => ['method' => [$this, 'exportFullSampleNorm']],
            'exportOverlapData' => ['method' => [$this, 'exportOverlapData']],
            'exportCultureData' => ['method' => [$this, 'exportCultureData']],
            'exportSampleNorm' => ['method' => [$this, 'exportSampleNorm']],
            'exportAVGSampleNorm' => ['method' => [$this, 'exportAVGSampleNorm']],
            'exportCropRotationData' => ['method' => [$this, 'exportCropRotationData']],
            'deleteFile' => ['method' => [$this, 'deleteFile']],
        ];
    }

    /**
     * Парцели от референтен слой -> Експорт(CSV).
     *
     * @api-method exportCropRotationData
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer layer_id
     *                         }
     *
     * @return array
     */
    public function exportCropRotationData($rpcParams)
    {
        $UserDbController = new UserDbController($this->User->Database);

        // get layer results for farming year info
        $options = [
            'tablename' => $UserDbController->DbHandler->cropLayersTable,
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $rpcParams['layer_id']],
            ],
        ];

        $return_results = $UserDbController->getItemsByParams($options);
        $layer_results = $return_results[0];

        // forming header array
        $year = $GLOBALS['Farming']['years'][$layer_results['year']]['year'];
        $headers = [
            'POLE', 'PRC_UIN', 'AREA', $year, $year + 1, $year + 2, $year + 3, $year + 4,
        ];

        // get layer data
        $options = [
            'tablename' => $UserDbController->DbHandler->cropLayersDataTable,
            'return' => [
                'plot', 'isak_number', 'area', 'culture1', 'culture2', 'culture3', 'culture4', 'culture5',
            ],
            'where' => [
                'layer_id' => ['column' => 'crop_layer_id', 'compare' => '=', 'value' => $rpcParams['layer_id']],
                'has_number' => ['column' => 'has_number', 'compare' => '=', 'value' => 1],
            ],
        ];

        $results = $UserDbController->getItemsByParams($options, false);
        if (!file_exists(SOIL_SAMPLES_QUEUE_PATH . $this->User->GroupID . '/')) {
            mkdir(SOIL_SAMPLES_QUEUE_PATH . $this->User->GroupID . '/', 0755, true);
        }
        $path = SOIL_SAMPLES_QUEUE_PATH . $this->User->GroupID . '/';
        $name = 'seitbooborot_' . $this->User->GroupID;
        $fp = fopen($path . '/' . $name . '.csv', 'w');

        // puttin the headers
        fputcsv($fp, $headers);
        $resultsCount = count($results);
        for ($i = 0; $i < $resultsCount; $i++) {
            fputcsv($fp, $results[$i], ',');
        }

        fclose($fp);

        return ['file_path' => 'files/soil_samples/' . $this->User->GroupID . '/' . $name . '.csv', 'file_name' => $name . '.csv'];
    }

    /**
     * Анализ на почвите -> ДФЗ Експорт
     *
     * @api-method exportSoilSample
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer cl_id
     *                         #item date    date_from
     *                         #item date    date_to
     *                         #item integer isak
     *                         }
     *
     * @throws MTRpcException
     *
     * @return array|string
     */
    public function exportSoilSample($rpcParams)
    {
        $mainData = [];

        $UserDbCropRotationController = new UserDbCropRotationController($this->User->Database);
        $FarmingController = new FarmingController('Farming');

        $options = [
            'return' => [
                '*', 'date(sample_date) as converted_sample_date',
            ],
            'where' => [
                'cl_id' => ['column' => 'crop_layer_id', 'compare' => '=', 'prefix' => 'cld', 'value' => $rpcParams['cl_id']],
                'date_from' => ['column' => 'CAST(sample_date as DATE)', 'compare' => '>=', 'value' => $rpcParams['date_from']],
                'date_to' => ['column' => 'CAST(sample_date as DATE)', 'compare' => '<=', 'value' => $rpcParams['date_to']],
                'isak' => ['column' => 'isak_number', 'compare' => '=', 'value' => $rpcParams['isak']],
            ],
            'sort' => 'plot asc, substring(sample_num from 1 for 3) asc, length(sample_num) asc, substring(sample_num from 4 for 2) ',
            'order' => 'asc',
        ];
        $results = $UserDbCropRotationController->getCropLayerDataSoilSamples($options, false, false);
        $resultsCount = count($results);

        if (0 == $resultsCount) {
            throw new MTRpcException('empty_report_requested', -33252);
        }

        unset($options['sort'], $options['order']);

        $options['return'] = ['COUNT(DISTINCT(plot)) as plot_count'];
        $count_results = $UserDbCropRotationController->getCropLayerDataSoilSamples($options, false, false);

        $areatotal = 0;
        $isak_num_array = [];

        for ($i = 0; $i < $resultsCount; $i++) {
            $meh_sastav = $GLOBALS['Farming']['soil_types'][$results[$i]['meh_sastav']]['coef'];

            $results[$i]['azot_mg'] = number_format($results[$i]['azot'], 2);
            $results[$i]['fosfor_mg'] = number_format($results[$i]['fosfor'], 2);
            $results[$i]['kalii_mg'] = number_format($results[$i]['kalii'], 2);

            $results[$i]['azot'] = number_format($results[$i]['azot_mg'] * $meh_sastav, 2);
            $results[$i]['fosfor'] = number_format($results[$i]['fosfor_mg'] * $meh_sastav, 2);
            $results[$i]['kalii'] = number_format($results[$i]['kalii_mg'] * $meh_sastav, 2);

            $results[$i]['sample_date'] = $results[$i]['converted_sample_date'];
            $results[$i]['meh_sastav'] = $GLOBALS['Farming']['soil_types'][$results[$i]['meh_sastav']]['name'];
            $results[$i]['sud_humus'] = $GLOBALS['Farming']['sastav_humus'][$results[$i]['sud_humus']]['name'];

            $results[$i]['ekate'] = substr($results[$i]['isak_number'], 0, 5);

            if (!in_array($results[$i]['isak_number'], $isak_num_array)) {
                $isak_num_array[] = $results[$i]['isak_number'];
                $areatotal += $results[$i]['area'];
            }
        }

        $mainData['urn'] = $rpcParams['urn'];
        $mainData['bnf'] = $rpcParams['bnf'];
        $mainData['plot_count'] = $count_results[0]['plot_count'];
        $mainData['content'] = $results;
        $mainData['areatotal'] = $areatotal;

        $ltext = $FarmingController->StringHelper->loadTemplate($GLOBALS['Templates'][10]['template'], $mainData);

        if ($rpcParams['pdf_export']) {
            $newPDFFilePath = SOIL_SAMPLES_QUEUE_PATH . $this->User->GroupID . '/soil_sample.pdf';
            $ltext = '<page style="font-family: freeserif"><br />' . $ltext . '</page>';

            $printPdf = new PrintPdf();
            $printPdf->generateFromHtml($ltext, $newPDFFilePath, ['orientation' => 'Landscape'], true);

            return ['file_path' => 'files/soil_samples/' . $this->User->GroupID . '/soil_sample.pdf', 'file_name' => 'soil_sample.pdf'];
        }

        $ltext = '<meta charset="UTF-8">' . $ltext;

        return $ltext;
    }

    /**
     * План за балансирано торене -> ДФЗ Експорт
     *
     * @api-method exportFullSampleNorm
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer cl_id
     *                         #item string  bnf
     *                         #item string  urn
     *                         }
     *
     * @return string
     */
    public function exportFullSampleNorm($rpcParams)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbCropRotationController = new UserDbCropRotationController($this->User->Database);
        $FarmingController = new FarmingController('Farming');

        // adding requested year and crop_layer_id to new variables
        $req_year = $GLOBALS['Farming']['years'][count($GLOBALS['Farming']['years']) + 3]['id'];
        $cl_id = $rpcParams['cl_id'];
        $bnf = $rpcParams['bnf'];
        $urn = $rpcParams['urn'];
        // creating the template variable
        $data = [];
        $results = [];
        $content = [];
        $count_print_years = 0;

        $options = [
            'tablename' => $UserDbController->DbHandler->cropLayersTable,
            'where' => [
                'cl_id' => ['column' => 'id', 'compare' => '=', 'value' => $cl_id],
            ],
        ];

        $cropLayerData = $UserDbController->getItemsByParams($options, false, false);

        if (0 != count($cropLayerData)) {
            // assigning the referent layer year
            $ref_year = $cropLayerData[0]['year'];

            if ($req_year >= $ref_year) {
                $options = [
                    'where' => [
                        'cl_id' => ['column' => 'crop_layer_id', 'compare' => '=', 'prefix' => 'cld', 'value' => $cl_id],
                        'has_number' => ['column' => 'has_number', 'compare' => '=', 'prefix' => 'cld', 'value' => 1],
                    ],
                    'sort' => 'plot',
                    'order' => 'asc',
                ];

                $counter = $UserDbCropRotationController->getCropLayerDataSoilSamples($options, true, false);

                if (0 != $counter[0]['count']) {
                    $results = $UserDbCropRotationController->getCropLayerDataSoilSamples($options, false, false);
                    $resultsCount = count($results);
                    // calculate year iterations
                    $year_iterations = $req_year - $ref_year;
                    $count_print_years = $year_iterations;

                    for ($j = 1; $j <= $year_iterations; $j++) {
                        // iterate over the results and calculate norm
                        for ($i = 0; $i < $resultsCount; $i++) {
                            // assign variables for norm calculation
                            if (1 == $j) {
                                $meh_sastav = $GLOBALS['Farming']['soil_types'][$results[$i]['meh_sastav']]['coef'];

                                $azot = $results[$i]['azot'] * $meh_sastav;
                                $fosfor = $results[$i]['fosfor'] * $meh_sastav;
                                $kalii = $results[$i]['kalii'] * $meh_sastav;
                                $yield = $results[$i]['yield2'];
                                $culture = $results[$i]['culture2'];
                                $humus = $results[$i]['sud_humus'];
                                $multiyear = false;
                            } else {
                                $nextCulture = (string) 'culture' . ($j + 1);
                                $nextYeild = (string) 'yield' . ($j + 1);
                                $azot = $results[$i]['azot_remaining'];
                                $fosfor = $results[$i]['fosfor_remaining'];
                                $kalii = $results[$i]['kalii_remaining'];
                                $yield = $results[$i][$nextYeild];
                                $culture = $results[$i][$nextCulture];
                                $humus = $results[$i]['sud_humus'];
                                $multiyear = true;
                            }

                            // calculate azot norm and remaining
                            $azot_results = $this->calculateAzotNorm($azot, $yield, $culture, $humus, $multiyear);

                            $results[$i]['azot_norm'] = $azot_results['norm'];
                            $results[$i]['azot_remaining'] = $azot_results['remaining'];
                            $results[$i]['azot_access'] = $azot_results['access'];
                            $results[$i]['azot_need'] = $azot_results['need'];

                            // calculate fosfor norm and remaining
                            $fosfor_results = $this->calculateFosforNorm($fosfor, $yield, $culture, $humus);

                            $results[$i]['fosfor_norm'] = $fosfor_results['norm'];
                            $results[$i]['fosfor_remaining'] = $fosfor_results['remaining'];
                            $results[$i]['fosfor_access'] = $fosfor_results['access'];
                            $results[$i]['fosfor_need'] = $fosfor_results['need'];

                            // calculate kalii norm and remaining
                            $kalii_results = $this->calculateKaliiNorm($kalii, $yield, $culture, $humus);

                            $results[$i]['kalii_norm'] = $kalii_results['norm'];
                            $results[$i]['kalii_remaining'] = $kalii_results['remaining'];
                            $results[$i]['kalii_access'] = $kalii_results['access'];
                            $results[$i]['kalii_need'] = $kalii_results['need'];
                        } // end results iterations

                        $isak_array = [];
                        $math_array = [];
                        $return = [];
                        $resultsCount = count($results);
                        // calculate average of the results for every ISAK number
                        for ($i = 0; $i < $resultsCount; $i++) {
                            $report_year = (string) ($j + 1);
                            // check if isak number was already iterated or not
                            if (!in_array($results[$i]['isak_number'], $isak_array)) {
                                // add the isak number to the arrays
                                $isak_array[] = $results[$i]['isak_number'];
                                $math_array[$results[$i]['isak_number']] = [
                                    'azot_norm' => $results[$i]['azot_norm'],
                                    'azot_access' => $results[$i]['azot_access'],
                                    'azot_need' => $results[$i]['azot_need'],
                                    'fosfor_norm' => $results[$i]['fosfor_norm'],
                                    'fosfor_access' => $results[$i]['fosfor_access'],
                                    'fosfor_need' => $results[$i]['fosfor_need'],
                                    'kalii_norm' => $results[$i]['kalii_norm'],
                                    'kalii_access' => $results[$i]['kalii_access'],
                                    'kalii_need' => $results[$i]['kalii_need'],
                                    'plot' => $results[$i]['plot'],
                                    'area' => $results[$i]['area'],
                                    'culture' => $results[$i]['culture' . $report_year],
                                    'culture_name' => $GLOBALS['Farming']['crops'][$results[$i]['culture' . $report_year]]['crop_name'],
                                    'item_count' => 1,
                                ];
                            } else {
                                $math_array[$results[$i]['isak_number']]['azot_norm'] += $results[$i]['azot_norm'];
                                $math_array[$results[$i]['isak_number']]['azot_access'] += $results[$i]['azot_access'];
                                $math_array[$results[$i]['isak_number']]['azot_need'] += $results[$i]['azot_need'];

                                $math_array[$results[$i]['isak_number']]['fosfor_norm'] += $results[$i]['fosfor_norm'];
                                $math_array[$results[$i]['isak_number']]['fosfor_access'] += $results[$i]['fosfor_access'];
                                $math_array[$results[$i]['isak_number']]['fosfor_need'] += $results[$i]['fosfor_need'];

                                $math_array[$results[$i]['isak_number']]['kalii_norm'] += $results[$i]['kalii_norm'];
                                $math_array[$results[$i]['isak_number']]['kalii_access'] += $results[$i]['kalii_access'];
                                $math_array[$results[$i]['isak_number']]['kalii_need'] += $results[$i]['kalii_need'];

                                $math_array[$results[$i]['isak_number']]['area'] = $results[$i]['area'];
                                $math_array[$results[$i]['isak_number']]['plot'] = $results[$i]['plot'];
                                $math_array[$results[$i]['isak_number']]['culture'] = $results[$i]['culture' . $report_year];
                                $math_array[$results[$i]['isak_number']]['culture_name'] = $GLOBALS['Farming']['crops'][$results[$i]['culture' . $report_year]]['crop_name'];

                                ++$math_array[$results[$i]['isak_number']]['item_count'];
                            }
                        }
                        $totalarea = 0;
                        // formatting to datagrid format
                        foreach ($math_array as $key => $values) {
                            $return[] = [
                                'isak_number' => $key,
                                'ekatte' => substr($key, 0, 5),
                                'azot_norm' => number_format($values['azot_norm'] / $values['item_count'], 2),
                                'azot_access' => number_format($values['azot_access'] / $values['item_count'], 2),
                                'azot_need' => number_format($values['azot_need'] / $values['item_count'], 2),
                                'fosfor_norm' => number_format($values['fosfor_norm'] / $values['item_count'], 2),
                                'fosfor_access' => number_format($values['fosfor_access'] / $values['item_count'], 2),
                                'fosfor_need' => number_format($values['fosfor_need'] / $values['item_count'], 2),
                                'kalii_norm' => number_format($values['kalii_norm'] / $values['item_count'], 2),
                                'kalii_access' => number_format($values['kalii_access'] / $values['item_count'], 2),
                                'kalii_need' => number_format($values['kalii_need'] / $values['item_count'], 2),
                                'sample_count' => $values['item_count'],
                                'area' => $values['area'],
                                'plot' => $values['plot'],
                                'culture' => $values['culture'],
                                'culture_name' => $values['culture_name'],
                            ];
                            $totalarea += $values['area'];
                        }

                        $content['year' . $j] = [
                            'headers' => [
                                'year' => $GLOBALS['Farming']['years'][$ref_year + $j]['title'],
                                'totalarea' => $totalarea,
                            ],
                            'plan_content' => $return,
                        ];
                    } // end year iterations
                } // end if(counter)
            } // end of "check if requested year is < than the ref year"
        } // end crop layer found
        // create arrays for ISAK numbers and a temporaty array for calculation

        $options = [
            'tablename' => $UserDbController->DbHandler->cropLayersDataTable,
            'where' => [
                'layer_id' => ['column' => 'crop_layer_id', 'compare' => '=', 'value' => $cl_id],
                'has_number' => ['column' => 'has_number', 'compare' => '=', 'value' => 1],
            ],
            'sort' => 'plot',
            'order' => 'asc',
        ];

        $cultures = $UserDbController->getItemsByParams($options, false);
        $culturesCount = count($cultures);
        $plots = [];
        for ($i = 0; $i < $culturesCount; $i++) {
            $cultures[$i]['culture1'] = $GLOBALS['Farming']['crops'][$cultures[$i]['culture1']]['crop_name'];
            $cultures[$i]['culture2'] = $GLOBALS['Farming']['crops'][$cultures[$i]['culture2']]['crop_name'];
            $cultures[$i]['culture3'] = $GLOBALS['Farming']['crops'][$cultures[$i]['culture3']]['crop_name'];
            $cultures[$i]['culture4'] = $GLOBALS['Farming']['crops'][$cultures[$i]['culture4']]['crop_name'];
            $cultures[$i]['culture5'] = $GLOBALS['Farming']['crops'][$cultures[$i]['culture5']]['crop_name'];
            $plots[$cultures[$i]['plot']] = true;
        }

        // tests
        $data['count_print_years'] = $count_print_years;
        $data['cultures'] = $cultures;
        $data['content'] = $content;
        $data['urn'] = $urn;
        $data['bnf'] = $bnf;
        $data['plot'] = count($plots);
        $data['urlpath'] = SITE_URL;
        // end of tests

        $ltext = $FarmingController->StringHelper->loadTemplate($GLOBALS['Templates'][12]['template'], $data);
        $ltext = '<meta charset="UTF-8">' . $ltext;

        return $ltext;
    }

    /**
     * Проверка за припокриване -> Експорт
     *
     * @api-method exportOverlapData
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer layer_id
     *                         #item integer farming
     *                         #item integer year
     *                         }
     *
     * @return array
     */
    public function exportOverlapData($rpcParams)
    {
        $UserDbCropRotationController = new UserDbCropRotationController($this->User->Database);
        $LayersController = new LayersController('Layers');
        $UserDbController = new UserDbController($this->User->Database);

        $cropLayerID = (int) $rpcParams['layer_id'];
        $farming = (int) $rpcParams['farming'];
        $year = (int) $rpcParams['year'];

        $cropLayerItemData = $UserDbCropRotationController->getCropLayersItemData($cropLayerID);

        // getting data for ISAK layer
        $layer_options = [
            'farming' => $cropLayerItemData['farming'],
            'year' => $cropLayerItemData['year'],
            'user_id' => $this->User->GroupID,
            'layer_type' => 6,
            'return' => ['table_name'],
        ];

        $isakLayerData = $LayersController->getLayersIdByParams($layer_options);

        // getting data for ZP layer
        $layer_options = [
            'farming' => $farming,
            'year' => $year,
            'user_id' => $this->User->GroupID,
            'layer_type' => 1,
            'return' => ['table_name'],
        ];
        $zpLayerData = $LayersController->getLayersIdByParams($layer_options);

        $tableISAKExists = $UserDbController->getTableNameExist($isakLayerData['table_name']);
        $tableZPExists = $UserDbController->getTableNameExist($zpLayerData['table_name']);

        if ($tableISAKExists && $tableZPExists) {
            $options = [
                'return' => [
                    'a.isak_number', 'ST_AREA(b.geom) as isak_area',
                    'ST_AREA(ST_INTERSECTION(b.geom, c.geom)) as intersect_area',
                    'ST_AREA(ST_INTERSECTION(b.geom, c.geom))/ST_AREA(c.geom)*100 as intersect_percent',
                    'b.prc_uin',
                    'ST_AREA(c.geom) as zp_area',
                ],
                'crop_layer_id' => $cropLayerID,
            ];
            $results = $UserDbCropRotationController->getCroprotationOverlapReport($zpLayerData['table_name'], $isakLayerData['table_name'], $options, $counter = false);
            $resultsCount = count($results);
            for ($i = 0; $i < $resultsCount; $i++) {
                $results[$i]['isak_area'] = number_format($results[$i]['isak_area'] / 1000, 3);
                $results[$i]['intersect_percent'] = number_format($results[$i]['intersect_percent'], 2);
                $results[$i]['intersect_area'] = number_format($results[$i]['intersect_area'] / 1000, 3);
                $results[$i]['zp_area'] = number_format($results[$i]['zp_area'] / 1000, 3);
            }
        }

        if (!file_exists(SOIL_SAMPLES_QUEUE_PATH . $this->User->GroupID . '/')) {
            mkdir(SOIL_SAMPLES_QUEUE_PATH . $this->User->GroupID . '/', 0755, true);
        }
        $path = SOIL_SAMPLES_QUEUE_PATH . $this->User->GroupID . '/';
        $name = 'pripokrivane_' . $this->User->GroupID;
        $fp = fopen($path . '/' . $name . '.csv', 'w');

        $headers = [
            'ZP_NUM', 'ISAK_AREA', 'INTERSECT_AREA', 'INTERSECT_PERCENT', 'ISAK_NUM', 'ZP_AREA',
        ];

        fputcsv($fp, $headers);

        for ($i = 0; $i < $resultsCount; $i++) {
            fputcsv($fp, $results[$i]);
        }

        fclose($fp);

        return ['file_path' => 'files/soil_samples/' . $this->User->GroupID . '/' . $name . '.csv', 'file_name' => $name . '.csv'];
    }

    /**
     * Експорт -> Проверка на въведените култури.
     *
     * @api-method exportCultureData
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer layer_id
     *                         #item integer farming
     *                         #item integer year
     *                         }
     *
     * @return array
     */
    public function exportCultureData($rpcParams)
    {
        $UserDbCropRotationController = new UserDbCropRotationController($this->User->Database);
        $LayersController = new LayersController('Layers');
        $UserDbController = new UserDbController($this->User->Database);

        $cropLayerID = (int) $rpcParams['layer_id'];
        $farming = (int) $rpcParams['farming'];
        $year = (int) $rpcParams['year'];

        $cropLayerItemData = $UserDbCropRotationController->getCropLayersItemData($cropLayerID);

        // getting ISAK layer data
        $layer_options = [
            'farming' => $cropLayerItemData['farming'],
            'year' => $cropLayerItemData['year'],
            'user_id' => $this->User->GroupID,
            'layer_type' => 6,
            'return' => ['table_name'],
        ];

        $isakLayerData = $LayersController->getLayersIdByParams($layer_options);

        // getting ZP layer data
        $layer_options = [
            'farming' => $farming,
            'year' => $year,
            'user_id' => $this->User->GroupID,
            'layer_type' => 1,
            'return' => ['table_name'],
        ];

        $zpLayerData = $LayersController->getLayersIdByParams($layer_options);

        $index = (int) ($GLOBALS['Farming']['years'][$year]['year'] - date('Y') + 1);
        if ($index < 1) {
            $index = 1;
        }
        $tableISAKExists = $UserDbController->getTableNameExist($isakLayerData['table_name']);
        $tableZPExists = $UserDbController->getTableNameExist($zpLayerData['table_name']);

        if ($tableISAKExists && $tableZPExists) {
            $options = [
                'return' => [
                    'a.isak_number', 'a.area', "a.culture{$index} as culture", 'c.isak_prc_uin', 'c.culture as zp_culture',
                ],
                'layer_id' => $_GET['layer_id'],
            ];

            $results = $UserDbCropRotationController->getCultureMatchReport($zpLayerData['table_name'], $isakLayerData['table_name'], $index, $options, $counter = false);
            $resultsCount = count($results);
            if (0 != $resultsCount) {
                for ($i = 0; $i < $resultsCount; $i++) {
                    $results[$i]['zp_culture'] = trim($results[$i]['zp_culture']);
                    $results[$i]['culture'] = trim($results[$i]['culture']);
                }
            }
        }
        if (!file_exists(SOIL_SAMPLES_QUEUE_PATH . $this->User->GroupID . '/')) {
            mkdir(SOIL_SAMPLES_QUEUE_PATH . $this->User->GroupID . '/', 0755, true);
        }
        $path = SOIL_SAMPLES_QUEUE_PATH . $this->User->GroupID . '/';
        $name = 'kulturi_' . $this->User->GroupID;
        $fp = fopen($path . '/' . $name . '.csv', 'w');

        for ($i = 0; $i < $resultsCount; $i++) {
            fputcsv($fp, $results[$i]);
        }

        fclose($fp);

        return ['file_path' => 'files/soil_samples/' . $this->User->GroupID . '/' . $name . '.csv', 'file_name' => $name . '.csv'];
    }

    /**
     * План за балансирано торене -> по години -> ДФЗ Експорт
     *
     * @api-method exportSampleNorm
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer year
     *                         #item integer cl_id
     *                         #item string  bnf
     *                         #item string  urn
     *                         }
     *
     * @return string
     */
    public function exportSampleNorm($rpcParams)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbCropRotationController = new UserDbCropRotationController($this->User->Database);
        $FarmingController = new FarmingController('Farming');

        // $this->getResponse()->setContentType("application/force-download");
        // addigning requested year and crop_layer_id to new variables
        $req_year = $rpcParams['year'];
        $cl_id = $rpcParams['cl_id'];
        $bnf = $rpcParams['bnf'];
        $urn = $rpcParams['urn'];

        // creating the template variable
        $data = [];
        $results = [];

        $options = [
            'tablename' => $UserDbController->DbHandler->cropLayersTable,
            'where' => [
                'cl_id' => ['column' => 'id', 'compare' => '=', 'value' => $cl_id],
            ],
        ];

        $cropLayerData = $UserDbController->getItemsByParams($options, false, false);
        $cropLayerDataCount = count($cropLayerData);
        if (0 != $cropLayerDataCount) {
            // assigning the referent layer year
            $ref_year = $cropLayerData[0]['year'];

            if ($req_year >= $ref_year) {
                $options = [
                    'where' => [
                        'cl_id' => ['column' => 'crop_layer_id', 'compare' => '=', 'prefix' => 'cld', 'value' => $cl_id],
                        // 'year' => array('column' => 'EXTRACT(YEAR FROM sample_date)', 'compare' => '=', 'value' => $GLOBALS['Farming']['years'][$ref_year]['year']),
                        'has_number' => ['column' => 'has_number', 'compare' => '=', 'prefix' => 'cld', 'value' => 1],
                    ],
                    'sort' => 'plot',
                    'order' => 'asc',
                ];

                $counter = $UserDbCropRotationController->getCropLayerDataSoilSamples($options, true, false);

                if (0 != $counter[0]['count']) {
                    $results = $UserDbCropRotationController->getCropLayerDataSoilSamples($options, false, false);
                    $resultsCount = count($results);
                    // calculate year iterations
                    $year_iterations = $req_year - $ref_year;

                    for ($j = 1; $j <= $year_iterations; $j++) {
                        // iterate over the results and calculate norm

                        for ($i = 0; $i < $resultsCount; $i++) {
                            // assign variables for norm calculation
                            if (1 == $j) {
                                $meh_sastav = $GLOBALS['Farming']['soil_types'][$results[$i]['meh_sastav']]['coef'];

                                $azot = $results[$i]['azot'] * $meh_sastav;
                                $fosfor = $results[$i]['fosfor'] * $meh_sastav;
                                $kalii = $results[$i]['kalii'] * $meh_sastav;
                                $yield = $results[$i]['yield2'];
                                $culture = $results[$i]['culture2'];
                                $humus = $results[$i]['sud_humus'];
                                $multiyear = false;
                            } else {
                                $nextCulture = (string) 'culture' . ($j + 1);
                                $nextYeild = (string) 'yield' . ($j + 1);
                                $azot = $results[$i]['azot_remaining'];
                                $fosfor = $results[$i]['fosfor_remaining'];
                                $kalii = $results[$i]['kalii_remaining'];
                                $yield = $results[$i][$nextYeild];
                                $culture = $results[$i][$nextCulture];
                                $humus = $results[$i]['sud_humus'];
                                $multiyear = true;
                            }

                            // calculate azot norm and remaining
                            $azot_results = $this->calculateAzotNorm($azot, $yield, $culture, $humus, $multiyear);

                            $results[$i]['azot_norm'] = $azot_results['norm'];
                            $results[$i]['azot_remaining'] = $azot_results['remaining'];
                            $results[$i]['azot_access'] = $azot_results['access'];
                            $results[$i]['azot_need'] = $azot_results['need'];

                            // calculate fosfor norm and remaining
                            $fosfor_results = $this->calculateFosforNorm($fosfor, $yield, $culture, $humus);

                            $results[$i]['fosfor_norm'] = $fosfor_results['norm'];
                            $results[$i]['fosfor_remaining'] = $fosfor_results['remaining'];
                            $results[$i]['fosfor_access'] = $fosfor_results['access'];
                            $results[$i]['fosfor_need'] = $fosfor_results['need'];

                            // calculate kalii norm and remaining
                            $kalii_results = $this->calculateKaliiNorm($kalii, $yield, $culture, $humus);

                            $results[$i]['kalii_norm'] = $kalii_results['norm'];
                            $results[$i]['kalii_remaining'] = $kalii_results['remaining'];
                            $results[$i]['kalii_access'] = $kalii_results['access'];
                            $results[$i]['kalii_need'] = $kalii_results['need'];
                        } // end results iterations
                    } // end year iterations
                } // end if(counter)
            } // end of "check if requested year is < than the ref year"
        } // end crop layer found
        // create arrays for ISAK numbers and a temporaty array for calculation
        $isak_array = [];
        $math_array = [];
        $return = [];
        $resultsCount = count($results);
        // calculate average of the results for every ISAK number
        for ($i = 0; $i < $resultsCount; $i++) {
            // check if isak number was already iterated or not
            if (!in_array($results[$i]['isak_number'], $isak_array)) {
                // add the isak number to the arrays
                $isak_array[] = $results[$i]['isak_number'];
                $report_year = (string) ($year_iterations + 1);
                $math_array[$results[$i]['isak_number']] = [
                    'azot_norm' => $results[$i]['azot_norm'],
                    'azot_access' => $results[$i]['azot_access'],
                    'azot_need' => $results[$i]['azot_need'],
                    'fosfor_norm' => $results[$i]['fosfor_norm'],
                    'fosfor_access' => $results[$i]['fosfor_access'],
                    'fosfor_need' => $results[$i]['fosfor_need'],
                    'kalii_norm' => $results[$i]['kalii_norm'],
                    'kalii_access' => $results[$i]['kalii_access'],
                    'kalii_need' => $results[$i]['kalii_need'],
                    'plot' => $results[$i]['plot'],
                    'area' => $results[$i]['area'],
                    'culture' => $results[$i]['culture' . $report_year],
                    'culture_name' => $GLOBALS['Farming']['crops'][$results[$i]['culture' . $report_year]]['crop_name'],
                    'item_count' => 1,
                ];
            } else {
                $math_array[$results[$i]['isak_number']]['azot_norm'] += $results[$i]['azot_norm'];
                $math_array[$results[$i]['isak_number']]['azot_access'] += $results[$i]['azot_access'];
                $math_array[$results[$i]['isak_number']]['azot_need'] += $results[$i]['azot_need'];

                $math_array[$results[$i]['isak_number']]['fosfor_norm'] += $results[$i]['fosfor_norm'];
                $math_array[$results[$i]['isak_number']]['fosfor_access'] += $results[$i]['fosfor_access'];
                $math_array[$results[$i]['isak_number']]['fosfor_need'] += $results[$i]['fosfor_need'];

                $math_array[$results[$i]['isak_number']]['kalii_norm'] += $results[$i]['kalii_norm'];
                $math_array[$results[$i]['isak_number']]['kalii_access'] += $results[$i]['kalii_access'];
                $math_array[$results[$i]['isak_number']]['kalii_need'] += $results[$i]['kalii_need'];

                $report_year = (string) ($year_iterations + 1);
                $math_array[$results[$i]['isak_number']]['area'] = $results[$i]['area'];
                $math_array[$results[$i]['isak_number']]['plot'] = $results[$i]['plot'];
                $math_array[$results[$i]['isak_number']]['culture'] = $results[$i]['culture' . $report_year];
                $math_array[$results[$i]['isak_number']]['culture_name'] = $GLOBALS['Farming']['crops'][$results[$i]['culture' . $report_year]]['crop_name'];

                ++$math_array[$results[$i]['isak_number']]['item_count'];
            }
        }
        $totalarea = 0;
        // formating to datagrid format
        foreach ($math_array as $key => $values) {
            $return[] = [
                'isak_number' => $key,
                'ekatte' => substr($key, 0, 5),
                'azot_norm' => number_format($values['azot_norm'] / $values['item_count'], 2),
                'azot_access' => number_format($values['azot_access'] / $values['item_count'], 2),
                'azot_need' => number_format($values['azot_need'] / $values['item_count'], 2),
                'fosfor_norm' => number_format($values['fosfor_norm'] / $values['item_count'], 2),
                'fosfor_access' => number_format($values['fosfor_access'] / $values['item_count'], 2),
                'fosfor_need' => number_format($values['fosfor_need'] / $values['item_count'], 2),
                'kalii_norm' => number_format($values['kalii_norm'] / $values['item_count'], 2),
                'kalii_access' => number_format($values['kalii_access'] / $values['item_count'], 2),
                'kalii_need' => number_format($values['kalii_need'] / $values['item_count'], 2),
                'sample_count' => $values['item_count'],
                'area' => $values['area'],
                'plot' => $values['plot'],
                'culture' => $values['culture'],
                'culture_name' => $values['culture_name'],
            ];
            $totalarea += $values['area'];
        }

        $options = [
            'tablename' => $UserDbController->DbHandler->cropLayersDataTable,
            'where' => [
                'layer_id' => ['column' => 'crop_layer_id', 'compare' => '=', 'value' => $cl_id],
                'has_number' => ['column' => 'has_number', 'compare' => '=', 'value' => 1],
            ],
            'sort' => 'plot',
            'order' => 'asc',
        ];

        $cultures = $UserDbController->getItemsByParams($options, false);
        $culturesCount = count($cultures);
        $year_id = $req_year - $ref_year + 1;
        $plots = [];
        for ($i = 0; $i < $culturesCount; $i++) {
            unset($cultures[$i]['geom']);
            $cultures[$i]['culture1'] = $GLOBALS['Farming']['crops'][$cultures[$i]['culture1']]['crop_name'];
            $cultures[$i]['culture2'] = $GLOBALS['Farming']['crops'][$cultures[$i]['culture2']]['crop_name'];
            $cultures[$i]['culture3'] = $GLOBALS['Farming']['crops'][$cultures[$i]['culture3']]['crop_name'];
            $cultures[$i]['culture4'] = $GLOBALS['Farming']['crops'][$cultures[$i]['culture4']]['crop_name'];
            $cultures[$i]['culture5'] = $GLOBALS['Farming']['crops'][$cultures[$i]['culture5']]['crop_name'];
            $plots[$cultures[$i]['plot']] = true;
        }

        $data['content'] = $return;
        $data['urn'] = $urn;
        $data['bnf'] = $bnf;
        $data['cultures'] = $cultures;
        $data['year_ref'] = $year_id;
        $data['plot'] = count($plots);
        $data['year'] = $GLOBALS['Farming']['years'][$req_year]['year'];
        $data['urlpath'] = SITE_URL;
        $data['totalarea'] = $totalarea;
        $ltext = $FarmingController->StringHelper->loadTemplate($GLOBALS['Templates'][9]['template'], $data);

        // $ltext = '<page style="font-family: freeserif"><br />' . $ltext . '</page>';

        $ltext = '<meta charset="UTF-8"><style>@page{size: landscape;}</style>' . $ltext;

        return $ltext;
    }

    /**
     * Усреднени почвени норми -> Експорт
     *
     * @api-method exportAVGSampleNorm
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer year
     *                         #item integer cl_id
     *                         }
     *
     * @return string
     */
    public function exportAVGSampleNorm($rpcParams)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbCropRotationController = new UserDbCropRotationController($this->User->Database);
        $FarmingController = new FarmingController('Farming');

        // adding requested year and crop_layer_id to new variables
        $req_year = $rpcParams['year'];
        $cl_id = $rpcParams['cl_id'];
        // creating the template variable
        $data = [];
        $results = [];

        $options = [
            'tablename' => $UserDbController->DbHandler->cropLayersTable,
            'where' => [
                'cl_id' => ['column' => 'id', 'compare' => '=', 'value' => $cl_id],
            ],
        ];

        $cropLayerData = $UserDbController->getItemsByParams($options, false, false);
        $cropLayerDataCount = count($cropLayerData);
        if (0 != $cropLayerDataCount) {
            // assigning the referent layer year
            $ref_year = $cropLayerData[0]['year'];

            if ($req_year >= $ref_year) {
                $options = [
                    'where' => [
                        'cl_id' => ['column' => 'crop_layer_id', 'compare' => '=', 'prefix' => 'cld', 'value' => $cl_id],
                        // 'year' => array('column' => 'EXTRACT(YEAR FROM sample_date)', 'compare' => '=', 'value' => $GLOBALS['Farming']['years'][$ref_year]['year']),
                        'has_number' => ['column' => 'has_number', 'compare' => '=', 'prefix' => 'cld', 'value' => 1],
                    ],
                ];

                $counter = $UserDbCropRotationController->getCropLayerDataSoilSamples($options, true, false);

                if (0 != $counter[0]['count']) {
                    $results = $UserDbCropRotationController->getCropLayerDataSoilSamples($options, false, false);

                    // calculate year iterations
                    $year_iterations = $req_year - $ref_year;

                    for ($j = 1; $j <= $year_iterations; $j++) {
                        // iterate over the results and calculate norm
                        $resultsCount = count($results);
                        for ($i = 0; $i < $resultsCount; $i++) {
                            // assign variables for norm calculation
                            if (1 == $j) {
                                $meh_sastav = $GLOBALS['Farming']['soil_types'][$results[$i]['meh_sastav']]['coef'];

                                $azot = $results[$i]['azot'] * $meh_sastav;
                                $fosfor = $results[$i]['fosfor'] * $meh_sastav;
                                $kalii = $results[$i]['kalii'] * $meh_sastav;
                                $yield = $results[$i]['yield2'];
                                $culture = $results[$i]['culture2'];
                                $humus = $results[$i]['sud_humus'];
                                $multiyear = false;
                            } else {
                                $nextCulture = (string) 'culture' . ($j + 1);
                                $nextYeild = (string) 'yield' . ($j + 1);
                                $azot = $results[$i]['azot_remaining'];
                                $fosfor = $results[$i]['fosfor_remaining'];
                                $kalii = $results[$i]['kalii_remaining'];
                                $yield = $results[$i][$nextYeild];
                                $culture = $results[$i][$nextCulture];
                                $humus = $results[$i]['sud_humus'];
                                $multiyear = true;
                            }

                            // calculate azot norm and remaining
                            $azot_results = $this->calculateAzotNorm($azot, $yield, $culture, $humus, $multiyear);

                            $results[$i]['azot_norm'] = $azot_results['norm'];
                            $results[$i]['azot_remaining'] = $azot_results['remaining'];
                            $results[$i]['azot_access'] = $azot_results['access'];
                            $results[$i]['azot_need'] = $azot_results['need'];

                            // calculate fosfor norm and remaining
                            $fosfor_results = $this->calculateFosforNorm($fosfor, $yield, $culture, $humus);

                            $results[$i]['fosfor_norm'] = $fosfor_results['norm'];
                            $results[$i]['fosfor_remaining'] = $fosfor_results['remaining'];
                            $results[$i]['fosfor_access'] = $fosfor_results['access'];
                            $results[$i]['fosfor_need'] = $fosfor_results['need'];

                            // calculate kalii norm and remaining
                            $kalii_results = $this->calculateKaliiNorm($kalii, $yield, $culture, $humus);

                            $results[$i]['kalii_norm'] = $kalii_results['norm'];
                            $results[$i]['kalii_remaining'] = $kalii_results['remaining'];
                            $results[$i]['kalii_access'] = $kalii_results['access'];
                            $results[$i]['kalii_need'] = $kalii_results['need'];
                        } // end results iterations
                    } // end year iterations
                } // end if(counter)
            } // end of "check if requested year is < than the ref year"
        } // end crop layer found
        // create arrays for ISAK numbers and a temporaty array for calculation
        $isak_array = [];
        $math_array = [];
        $return = [];
        $resultsCount = count($results);
        // calculate average of the results for every ISAK number
        for ($i = 0; $i < $resultsCount; $i++) {
            // check if isak number was already iterated or not
            if (!in_array($results[$i]['isak_number'], $isak_array)) {
                // add the isak number to the arrays
                $isak_array[] = $results[$i]['isak_number'];

                $math_array[$results[$i]['isak_number']] = [
                    'azot_norm' => $results[$i]['azot_norm'],
                    'azot_access' => $results[$i]['azot_access'],
                    'azot_need' => $results[$i]['azot_need'],
                    'fosfor_norm' => $results[$i]['fosfor_norm'],
                    'fosfor_access' => $results[$i]['fosfor_access'],
                    'fosfor_need' => $results[$i]['fosfor_need'],
                    'kalii_norm' => $results[$i]['kalii_norm'],
                    'kalii_access' => $results[$i]['kalii_access'],
                    'kalii_need' => $results[$i]['kalii_need'],
                    'item_count' => 1,
                ];
            } else {
                $math_array[$results[$i]['isak_number']]['azot_norm'] += $results[$i]['azot_norm'];
                $math_array[$results[$i]['isak_number']]['azot_access'] += $results[$i]['azot_access'];
                $math_array[$results[$i]['isak_number']]['azot_need'] += $results[$i]['azot_need'];

                $math_array[$results[$i]['isak_number']]['fosfor_norm'] += $results[$i]['fosfor_norm'];
                $math_array[$results[$i]['isak_number']]['fosfor_access'] += $results[$i]['fosfor_access'];
                $math_array[$results[$i]['isak_number']]['fosfor_need'] += $results[$i]['fosfor_need'];

                $math_array[$results[$i]['isak_number']]['kalii_norm'] += $results[$i]['kalii_norm'];
                $math_array[$results[$i]['isak_number']]['kalii_access'] += $results[$i]['kalii_access'];
                $math_array[$results[$i]['isak_number']]['kalii_need'] += $results[$i]['kalii_need'];

                ++$math_array[$results[$i]['isak_number']]['item_count'];
            }
        }

        // formating to datagrid format
        foreach ($math_array as $key => $values) {
            $return[] = [
                'isak_number' => $key,
                'azot_norm' => number_format($values['azot_norm'] / $values['item_count'], 2),
                'azot_access' => $values['azot_access'] / $values['item_count'],
                'azot_need' => $values['azot_need'] / $values['item_count'],
                'fosfor_norm' => number_format($values['fosfor_norm'] / $values['item_count'], 2),
                'fosfor_access' => $values['fosfor_access'] / $values['item_count'],
                'fosfor_need' => $values['fosfor_need'] / $values['item_count'],
                'kalii_norm' => number_format($values['kalii_norm'] / $values['item_count'], 2),
                'kalii_access' => $values['kalii_access'] / $values['item_count'],
                'kalii_need' => $values['kalii_need'] / $values['item_count'],
                'sample_count' => $values['item_count'],
            ];
        }

        $data['content'] = $return;

        $ltext = $FarmingController->StringHelper->loadTemplate($GLOBALS['Templates'][8]['template'], $data);

        $ltext = '<page style="font-family: freeserif"><br />' . $ltext . '</page>';
        if (!file_exists(SOIL_SAMPLES_QUEUE_PATH . $this->User->GroupID . '/')) {
            mkdir(SOIL_SAMPLES_QUEUE_PATH . $this->User->GroupID . '/', 0755, true);
        }
        $newPDFFilePath = SOIL_SAMPLES_QUEUE_PATH . $this->User->GroupID . '/avg_soil_sample.pdf';

        $printPdf = new PrintPdf();
        $printPdf->generateFromHtml($ltext, $newPDFFilePath, [], true);

        return ['file_path' => 'files/soil_samples/' . $this->User->GroupID . '/avg_soil_sample.pdf', 'file_name' => 'avg_soil_sample.pdf'];
    }

    /**
     * Delete created file.
     *
     * @api-method deleteFile
     *
     * @param string $name
     */
    public function deleteFile($name)
    {
        @unlink(SOIL_SAMPLES_QUEUE_PATH . $this->User->GroupID . '/' . $name);
    }

    private function calculateAzotNorm($azot, $yield, $culture, $humus, $multiyear)
    {
        if ($multiyear) {
            $content = $azot;
            $access = $content;
        } else {
            $content = $azot + $GLOBALS['Farming']['sastav_humus'][$humus]['coef'];
            $access = $content * $GLOBALS['Farming']['nutrients']['1']['coef'];
        }

        $need = ($yield / 100) * $GLOBALS['Farming']['crops'][$culture]['azot'];

        if (($access - $need) < 0) {
            $norm = ($need - $access) / $GLOBALS['Farming']['nutrients']['1']['coef'];
        } else {
            $norm = 0;
        }

        $remaining = $GLOBALS['Farming']['sastav_humus'][$humus]['coef'];

        return [
            'norm' => round($norm, 2),
            'remaining' => round($remaining, 2),
            'access' => round($access, 2),
            'need' => round($need, 2),
        ];
    }

    private function calculateFosforNorm($fosfor, $yield, $culture, $humus)
    {
        $content = $fosfor;
        $access = $content * $GLOBALS['Farming']['nutrients']['2']['coef'];
        $need = ($yield / 100) * $GLOBALS['Farming']['crops'][$culture]['fosfor'];

        if (($access - $need) < 0) {
            $norm = ($need - $access) / $GLOBALS['Farming']['nutrients']['2']['coef'];
        } else {
            $norm = 0;
        }

        // $remaining = $content - ($content - $need) / 2 + $norm;
        $remaining = $content - $need + $norm;

        return [
            'norm' => round($norm, 2),
            'remaining' => round($remaining, 2),
            'access' => round($access, 2),
            'need' => round($need, 2),
        ];
    }

    private function calculateKaliiNorm($kalii, $yield, $culture, $humus)
    {
        $content = $kalii;
        $access = $content * $GLOBALS['Farming']['nutrients']['3']['coef'];
        $need = ($yield / 100) * $GLOBALS['Farming']['crops'][$culture]['kalii'];

        if (($access - $need) < 0) {
            $norm = ($need - $access) / $GLOBALS['Farming']['nutrients']['3']['coef'];
        } else {
            $norm = 0;
        }

        // $remaining = $content - ($content - $need) / 4 + $norm;
        $remaining = $content - $need + $norm;

        return [
            'norm' => round($norm, 2),
            'remaining' => round($remaining, 2),
            'access' => round($access, 2),
            'need' => round($need, 2),
        ];
    }
}
