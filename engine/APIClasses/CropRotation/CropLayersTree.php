<?php

namespace TF\Engine\APIClasses\CropRotation;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\Users\UsersController;

// Prado::using('Plugins.Core.Farming.conf');
// Prado::using('Plugins.Core.Farming.*');

/**
 * Tree 'Референтни слоеве'.
 *
 * @rpc-module CropRotation
 *
 * @rpc-service-id crop-layers-tree
 */
class CropLayersTree extends TRpcApiProvider
{
    private $module = 'CropRotation';
    private $service_id = 'crop-layers-tree';

    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getCropLayersTree']],
            'add' => ['method' => [$this, 'addCropLayer']],
            'rename' => ['method' => [$this, 'renameCropLayer']],
            'delete' => ['method' => [$this, 'deleteCropLayer']],
        ];
    }

    /**
     * Return tree data.
     *
     * @api-method read
     *
     * @return array
     */
    public function getCropLayersTree()
    {
        // init all needed controllers
        $UserDbController = new UserDbController($this->User->Database);
        $FarmingController = new FarmingController('Farming');
        $farming_array = $FarmingController->getUserFarmings();
        $farmingIds = array_keys($farming_array);

        // define help arrays and return array
        // hold all found farmings
        $farmings = [];
        // hold all found farmings-years relations
        $farmings_years = [];
        // hold all found years-farmings relations
        $years_layers = [];
        $farmings_keys = [];
        $farmings_years_keys = [];
        $return = [];

        $options = [
            'tablename' => $UserDbController->DbHandler->cropLayersTable,
            'sort' => 'farming, year, id',
            'order' => 'asc',
            'where' => [
                'farming' => ['column' => 'farming', 'compare' => 'IN', 'value' => $farmingIds],
            ],
        ];

        $results = $UserDbController->DbHandler->getItemsByParams($options, false, false);
        $resultsCount = count($results);
        for ($i = 0; $i < $resultsCount; $i++) {
            $farming = $results[$i]['farming'];
            $year = $results[$i]['year'];
            $layer = $results[$i]['id'];
            $layer_name = $results[$i]['layer_name'];

            if (null == $layer_name) {
                $layer_name = 'Референтен слой ' . ($i + 1);
            }
            // check if farming was already added to the main array
            if (!in_array($farming, $farmings)) {
                $farmings[] = $farming;
                $farmings_years[$farming] = [];

                $return[] = [
                    'id' => $farming,
                    'text' => $farming_array[$farming],
                    'state' => 'open',
                    'children' => [],
                ];

                $farmings_keys[$farming] = count($return) - 1;
            }

            // check if year is in farmings-years array
            if (!in_array($year, $farmings_years[$farming])) {
                $farmings_years[$farming][] = $year;
                $years_layers[$year] = [];

                $return[$farmings_keys[$farming]]['children'][] = [
                    'id' => $year,
                    'text' => $GLOBALS['Farming']['years'][$year]['title'],
                    'state' => 'open',
                    'children' => [],
                ];

                $farmings_years_keys[$year] = count($farmings_years[$farming]) - 1;
            }

            if (!in_array($layer, $years_layers[$year])) {
                $years_layers[$year][] = $layer;

                $return[$farmings_keys[$farming]]['children'][$farmings_years_keys[$year]]['children'][] = [
                    'id' => $layer,
                    'text' => $layer_name,
                    'iconCls' => 'icon-tree-layers',
                    'attributes' => [
                        'farming' => $farming,
                        'year' => $year,
                    ],
                ];
            }
        }

        return $return;
    }

    /**
     * Add new ref. layer.
     *
     * @api-method add
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer year
     *                         #item integer farming
     *                         #item boolean copy
     *                         #item integer crop_layer_id
     *                         }
     */
    public function addCropLayer($rpcParams)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        $FarmingController = new FarmingController('Farming');

        $layer_options = [
            'farming' => $rpcParams['farming'],
            'year' => $rpcParams['year'],
            'user_id' => $this->User->UserID,
            'layer_type' => 6,
        ];

        $options['return'] = ['name'];
        $options['whereFields'] = ['id'];
        $options['whereValues'] = [$rpcParams['farming']];

        $tableName = $UserDbController->DbHandler->cropLayersTable;
        $farmingname = $FarmingController->getItem($options);

        unset($options);

        // get max ID
        $optionsMaxId = [
            'tablename' => $tableName,
            'return' => [
                'MAX(id)',
            ],
        ];

        $resultMaxId = $UserDbController->getItemsByParams($optionsMaxId);

        $maxId = $resultMaxId[0]['max'] + 1;
        $options['tablename'] = $tableName;

        $options['mainData'] = [
            'farming' => "{$rpcParams['farming']}",
            'year' => "{$rpcParams['year']}",
            'status' => 4,
            'layer_name' => 'Референтен слой ' . $maxId,
        ];

        if (isset($rpcParams['crop_layer_id'])) {
            $optionsLayerName = [
                'tablename' => $tableName,
                'where' => [
                    'id' => ['column' => 'id', 'compare' => '=', 'value' => $rpcParams['crop_layer_id']],
                ],
            ];
            $resultLayerName = $UserDbController->getItemsByParams($optionsLayerName);

            $options['mainData']['layer_name'] = $resultLayerName[0]['layer_name'] . ' - Копие';
        }

        $recordID = $UserDbController->addItem($options);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options, $recordID, 'Adding crop layer');

        // check if copy crop layer
        if ('copyLayer' === $rpcParams['copy']) {
            $tableNameCropLayers = $UserDbController->DbHandler->cropLayersDataTable;

            $options = [
                'tablename' => $tableNameCropLayers,
                'where' => [
                    'crop_layer_id' => ['column' => 'crop_layer_id', 'compare' => '=', 'value' => $rpcParams['crop_layer_id']],
                ],
                'sort' => 'id',
                'order' => 'asc',
            ];
            $cropLayerData = $UserDbController->getItemsByParams($options);
            $cropLayerDataCount = count($cropLayerData);
            for ($i = 0; $i < $cropLayerDataCount; $i++) {
                $options = [
                    'tablename' => $tableNameCropLayers,
                    'mainData' => [
                        'plot' => (int)$cropLayerData[$i]['plot'],
                        'isak_number' => $cropLayerData[$i]['isak_number'],
                        'area' => (float)$cropLayerData[$i]['area'],
                        'culture1' => (int)$cropLayerData[$i]['culture1'],
                        'culture2' => (int)$cropLayerData[$i]['culture2'],
                        'culture3' => (int)$cropLayerData[$i]['culture3'],
                        'culture4' => (int)$cropLayerData[$i]['culture4'],
                        'culture5' => (int)$cropLayerData[$i]['culture5'],
                        'crop_layer_id' => (int)$recordID,
                        'has_number' => (int)$cropLayerData[$i]['has_number'],
                        'yield1' => (int)$cropLayerData[$i]['yield1'],
                        'yield2' => (int)$cropLayerData[$i]['yield2'],
                        'yield3' => (int)$cropLayerData[$i]['yield3'],
                        'yield4' => (int)$cropLayerData[$i]['yield4'],
                        'yield5' => (int)$cropLayerData[$i]['yield5'],
                    ],
                ];

                $id = $UserDbController->addItem($options);

                $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options, $id, 'Copying crop layer');
            }
        }
    }

    /**
     * Rename the selected ref. layer.
     *
     * @api-method rename
     *
     * @param array $rpcParams
     *                         {
     *                         #item string  layer_name
     *                         #item integer layer_id
     *                         }
     */
    public function renameCropLayer($rpcParams)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $options = [
            'tablename' => $UserDbController->DbHandler->cropLayersTable,
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $rpcParams['layer_id']],
            ],
        ];
        $old_data = $UserDbController->getItemsByParams($options);

        $text = $rpcParams['layer_name'];
        $id = $rpcParams['layer_id'];
        $tableName = $UserDbController->DbHandler->cropLayersTable;
        $options = [
            'tablename' => $tableName,
            'mainData' => [
                'layer_name' => $text,
            ],
            'where' => [
                'id' => $id,
            ],
        ];

        $UserDbController->editItem($options);
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $old_data, $options, 'Renaming crop layer');
    }

    /**
     * Removes the selected ref. layer.
     *
     * @api-method delete
     *
     * @param int $rpcParam
     */
    public function deleteCropLayer($rpcParam)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $options = [
            'tablename' => $UserDbController->DbHandler->cropLayersTable,
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $rpcParam],
            ],
        ];
        $old_data = $UserDbController->getItemsByParams($options);

        $options = [
            'tablename' => $UserDbController->DbHandler->cropLayersTable,
            'id_string' => $rpcParam,
        ];

        $UserDbController->deleteItemsByParams($options);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $old_data, $options, 'Deleting crop layer');
    }
}
