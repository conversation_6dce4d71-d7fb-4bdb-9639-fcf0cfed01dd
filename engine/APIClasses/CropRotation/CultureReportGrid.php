<?php

namespace TF\Engine\APIClasses\CropRotation;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbCropRotation\UserDbCropRotationController;

/**
 * Проверка на въведените култури.
 *
 * @rpc-module CropRotation
 *
 * @rpc-service-id croprotation-culture-report
 */
class CultureReportGrid extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getCultureReportGrid'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * Returns grid information.
     *
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer layer_id
     *                         #item integer farming
     *                         #item integer year
     *                         }
     * @param int $page pagination parameter
     * @param int $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array
     */
    public function getCultureReportGrid(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        // init all needed controllers
        $UserDbCropRotationController = new UserDbCropRotationController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);
        $LayersController = new LayersController('Layers');

        $cropLayerID = (int) $rpcParams['layer_id'];
        $farming = (int) $rpcParams['farming'];
        $year = (int) $rpcParams['year'];

        $cropLayerItemData = $UserDbCropRotationController->getCropLayersItemData($cropLayerID);

        // getting ISAK layer data
        $layer_options = [
            'farming' => $cropLayerItemData['farming'],
            'year' => $cropLayerItemData['year'],
            'user_id' => $this->User->GroupID,
            'layer_type' => 6,
            'return' => ['table_name'],
        ];

        $isakLayerData = $LayersController->getLayersIdByParams($layer_options);

        // getting ZP layer data
        $layer_options = [
            'farming' => $farming,
            'year' => $year,
            'user_id' => $this->User->GroupID,
            'layer_type' => 1,
            'return' => ['table_name'],
        ];

        $zpLayerData = $LayersController->getLayersIdByParams($layer_options);

        $index = (int) ($year - $cropLayerItemData['year']);

        $options = [
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
            'layer_id' => $rpcParams['layer_id'],
        ];

        $tableISAKExists = $UserDbController->getTableNameExist($isakLayerData['table_name']);
        $tableZPExists = $UserDbController->getTableNameExist($zpLayerData['table_name']);

        if ($tableISAKExists && $tableZPExists) {
            $counter = $UserDbCropRotationController->getCultureMatchReport($zpLayerData['table_name'], $isakLayerData['table_name'], $index, $options, $counter = true);
            if ($counter[0]['count']) {
                $return['total'] = $counter[0]['count'];
                $data = $UserDbCropRotationController->getCultureMatchReport($zpLayerData['table_name'], $isakLayerData['table_name'], $index, $options, $counter = false);
                $dataCount = count($data);
                for ($i = 0; $i < $dataCount; $i++) {
                    if (trim($data[$i]['culture']) == trim($data[$i]['zp_culture'])) {
                        $data[$i]['status'] = 1; // "<div style='background: #00ff00;height:20px;width:20px;'></div>";
                    } else {
                        $data[$i]['status'] = 0; // "<div style='background: #ff0000;height:20px;width:20px;'></div>";
                    }
                    $culture = (int) $data[$i]['culture'];
                    $data[$i]['culture'] = $GLOBALS['Farming']['crops'][$culture]['crop_name'];
                    $culture = (int) $data[$i]['zp_culture'];
                    $data[$i]['zp_culture'] = $GLOBALS['Farming']['crops'][$culture]['crop_name'];
                }

                $return['rows'] = $data;
            } else {
                $return['rows'] = [];
                $return['total'] = 0;
            }
        } else {
            $return['rows'] = [];
            $return['total'] = 0;
        }

        return $return;
    }
}
