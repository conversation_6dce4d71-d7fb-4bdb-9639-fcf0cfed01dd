<?php

namespace TF\Engine\APIClasses\Overlaps;

use Prado\Web\Services\TRpcApiProvider;
use TF\Application\Common\Config;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbOverlaps\UserDbOverlapsController;
use TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController;

/**
 * Overlaps map related functionalities.
 *
 * @rpc-module Overlaps
 *
 * @rpc-service-id overlaps-map-tools
 */
class OverlapsMapTools extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'initKVSMap' => ['method' => [$this, 'initKVSMap']],
            'getKVSExtent' => ['method' => [$this, 'getKVSExtent']],
            'getLayerKVS' => ['method' => [$this, 'getLayerKVS']],
        ];
    }

    /**
     * Returns the KVS map extent for the selected EKATTE.
     *
     * @api-method getKVSExtent
     *
     * @param array $rpcParams
     *                         {
     *                         #item string ekatte
     *                         }
     *
     * @return array
     */
    public function getKVSExtent($rpcParams)
    {
        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableKVS,
            'return' => ['St_Extent(geom) as extent'],
            'where' => [
                'ekate' => ['column' => 'ekate', 'compare' => '=', 'value' => $rpcParams['ekate']],
            ],
        ];

        $results = $UserDbController->getItemsByParams($options, false, false);

        if (0 == count($results)) {
            return [];
        }

        $maxExtent = $results[0]['extent'];
        $maxExtent = str_replace('BOX(', '', $maxExtent);
        $maxExtent = str_replace(')', '', $maxExtent);
        $maxExtent = str_replace(',', ' ', $maxExtent);
        $maxExtent = str_replace(' ', ', ', $maxExtent);

        $return['extent'] = $maxExtent;

        return $return;
    }

    /**
     * Return initial map and legend parameters.
     *
     * @api-method initKvsMap
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer overlap_id
     *                         #item integer farming
     *                         #item integer year
     *                         }
     *
     * @return array
     */
    public function initKvsMap($rpcParams)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbOverlapsController = new UserDbOverlapsController($this->User->Database);
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);
        $LayersController = new LayersController('Layers');

        $overlapid = $rpcParams['overlap_id'];
        $farming = $rpcParams['farming'];
        $year = $rpcParams['year'];

        $sqlString = $UserDbController->getOverlapsMapQuery($overlapid);

        $maxextent = $UserDbOverlapsController->getOverlapsMapExtent($overlapid);

        $query = "({$sqlString}) as subquery using unique gid";

        $options = [
            'return' => ['t.id', 't.extent'],
            'sort' => 'id',
            'order' => 'DESC',
            'where' => [
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'prefix' => 't', 'value' => $this->User->GroupID],
                'layer_type' => ['column' => 'layer_type', 'compare' => '=', 'prefix' => 't', 'value' => 5],
            ],
        ];

        $result = $LayersController->getLayers($options);

        $data = [];

        $data['layername'] = 'topic_kvs_layer';
        $data['maxextent'] = $result[0]['extent'];
        $data['host'] = DEFAULT_DB_HOST;
        $data['dbname'] = $this->User->Database;
        $data['username'] = DEFAULT_DB_USERNAME;
        $data['password'] = DEFAULT_DB_PASSWORD;
        $data['port'] = DEFAULT_DB_PORT;
        $data['query'] = $query;
        $data['gid'] = 'gid';
        $data['transparency'] = '70';
        $data['classitem'] = 'nm_usage_rights';

        $color1 = '000000';
        $color2 = $LayersController->StringHelper->randomColorCode();
        $data['classes'][0]['name'] = 'topic_kvs_layer_1';
        $data['classes'][0]['border_color'] = hexdec(substr($color1, 0, 2)) . ' ' . hexdec(substr($color1, 2, 2)) . ' ' . hexdec(substr($color1, 4, 2));
        $data['classes'][0]['color'] = hexdec(substr($color2, 0, 2)) . ' ' . hexdec(substr($color2, 2, 2)) . ' ' . hexdec(substr($color2, 4, 2));
        $data['classes'][0]['expression'] = '1';
        $hexColorArray[] = ['color' => $color2, 'name' => 'Собствени', 'iconCls' => 'no-background no-padding'];

        $color1 = '000000';
        $color2 = $LayersController->StringHelper->randomColorCode();
        $data['classes'][1]['name'] = 'topic_kvs_layer_2';
        $data['classes'][1]['border_color'] = hexdec(substr($color1, 0, 2)) . ' ' . hexdec(substr($color1, 2, 2)) . ' ' . hexdec(substr($color1, 4, 2));
        $data['classes'][1]['color'] = hexdec(substr($color2, 0, 2)) . ' ' . hexdec(substr($color2, 2, 2)) . ' ' . hexdec(substr($color2, 4, 2));
        $data['classes'][1]['expression'] = '2';
        $hexColorArray[] = ['color' => $color2, 'name' => 'Аренда', 'iconCls' => 'no-background no-padding'];

        $color1 = '000000';
        $color2 = $LayersController->StringHelper->randomColorCode();
        $data['classes'][2]['name'] = 'topic_kvs_layer_3';
        $data['classes'][2]['border_color'] = hexdec(substr($color1, 0, 2)) . ' ' . hexdec(substr($color1, 2, 2)) . ' ' . hexdec(substr($color1, 4, 2));
        $data['classes'][2]['color'] = hexdec(substr($color2, 0, 2)) . ' ' . hexdec(substr($color2, 2, 2)) . ' ' . hexdec(substr($color2, 4, 2));
        $data['classes'][2]['expression'] = '3';
        $hexColorArray[] = ['color' => $color2, 'name' => 'Наем', 'iconCls' => 'no-background no-padding'];

        $color1 = '000000';
        $color2 = $LayersController->StringHelper->randomColorCode();
        $data['classes'][3]['name'] = 'topic_kvs_layer_4';
        $data['classes'][3]['border_color'] = hexdec(substr($color1, 0, 2)) . ' ' . hexdec(substr($color1, 2, 2)) . ' ' . hexdec(substr($color1, 4, 2));
        $data['classes'][3]['color'] = hexdec(substr($color2, 0, 2)) . ' ' . hexdec(substr($color2, 2, 2)) . ' ' . hexdec(substr($color2, 4, 2));
        $data['classes'][3]['expression'] = '4';
        $hexColorArray[] = ['color' => $color2, 'name' => 'Споразумение', 'iconCls' => 'no-background no-padding'];

        $color1 = '000000';
        $color2 = $LayersController->StringHelper->randomColorCode();
        $data['classes'][4]['name'] = 'topic_kvs_layer_5';
        $data['classes'][4]['border_color'] = hexdec(substr($color1, 0, 2)) . ' ' . hexdec(substr($color1, 2, 2)) . ' ' . hexdec(substr($color1, 4, 2));
        $data['classes'][4]['color'] = hexdec(substr($color2, 0, 2)) . ' ' . hexdec(substr($color2, 2, 2)) . ' ' . hexdec(substr($color2, 4, 2));
        $data['classes'][4]['expression'] = '5';
        $hexColorArray[] = ['color' => $color2, 'name' => 'Съвместна обработка', 'iconCls' => 'no-background no-padding'];

        $color1 = '000000';
        $color2 = 'ff0000';
        $data['classes'][5]['name'] = 'topic_kvs_layer_6';
        $data['classes'][5]['border_color'] = hexdec(substr($color1, 0, 2)) . ' ' . hexdec(substr($color1, 2, 2)) . ' ' . hexdec(substr($color1, 4, 2));
        $data['classes'][5]['color'] = hexdec(substr($color2, 0, 2)) . ' ' . hexdec(substr($color2, 2, 2)) . ' ' . hexdec(substr($color2, 4, 2));
        $hexColorArray[] = ['color' => $color2, 'name' => 'Без договор', 'iconCls' => 'no-background no-padding'];
        $ltext = $LayersController->StringHelper->loadTemplate($GLOBALS['Templates'][3]['template'], $data);

        $options = [];
        $options['farming'] = $farming;
        $options['year'] = $year;
        $options['group_id'] = $this->User->GroupID;
        $options['layer_type'] = 1;
        $tableName = $LayersController->getTableNameByParams($options);

        if (!$tableName) {
            die();
        }
        $options = [
            'return' => ['p.*'],
            'tablename' => $tableName,
        ];
        $sqlString = $UserDbPlotsController->getPlotData($options, false, true);

        $query = "({$sqlString}) as subquery using unique id";

        $options = [
            'return' => ['t.id', 't.table_name as name', 't.extent', 't.layer_type', 't.farming', 't.year'],
            'sort' => 'id',
            'order' => 'DESC',
            'where' => [
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'prefix' => 't', 'value' => $this->User->GroupID],
                'layer_type' => ['column' => 'layer_type', 'compare' => '=', 'prefix' => 't', 'value' => 1],
                'farming' => ['column' => 'farming', 'compare' => '=', 'prefix' => 't', 'value' => $farming],
                'year' => ['column' => 'year', 'compare' => '=', 'prefix' => 't', 'value' => $year],
            ],
        ];

        $result = $LayersController->getLayers($options);

        $data = [];

        $color1 = '0000ff';
        $data['layername'] = 'topic_kvs_zp_layer';
        $data['maxextent'] = $result[0]['extent'];
        $data['host'] = DEFAULT_DB_HOST;
        $data['dbname'] = $this->User->Database;
        $data['username'] = DEFAULT_DB_USERNAME;
        $data['password'] = DEFAULT_DB_PASSWORD;
        $data['port'] = DEFAULT_DB_PORT;
        $data['query'] = $query;
        $data['gid'] = 'gid';
        $data['transparency'] = '100';
        $data['classes'][0]['name'] = 'topic_kvs_zp_layer';
        $data['classes'][0]['border_color'] = hexdec(substr($color1, 0, 2)) . ' ' . hexdec(substr($color1, 2, 2)) . ' ' . hexdec(substr($color1, 4, 2));
        $data['classes'][0]['color'] = false;
        $data['classes'][0]['width'] = 3;
        $data['classes'][0]['dashed'] = false;
        $ltext .= $LayersController->StringHelper->loadTemplate($GLOBALS['Templates'][2]['template'], $data);

        @unlink(WMS_MAP_PATH . $this->User->GroupID . '_layer_kvs.map');
        $fp = fopen(WMS_MAP_PATH . $this->User->GroupID . '_layer_kvs.map', 'w');
        $res = fwrite($fp, $ltext);
        fclose($fp);

        $maxextent = $maxextent[0]['extent'];
        $maxextent = str_replace('BOX(', '', $maxextent);
        $maxextent = str_replace(')', '', $maxextent);
        $maxextent = str_replace(' ', ',', $maxextent);

        $returnData['colorarray'] = $hexColorArray;
        $returnData['extent'] = $maxextent;

        return $returnData;
    }

    /**
     * Set the initial map settings.
     *
     * @api-method getLayerKVS
     *
     * @return array
     */
    public function getLayerKVS()
    {
        $LayersKvs = [];

        $LayersController = new LayersController('Layers');

        $options = [
            'return' => ['t.id', 't.table_name as name', 't.extent', 't.layer_type', 't.color'],
            'sort' => 'id',
            'order' => 'ASC',
            'where' => [
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'prefix' => 't', 'value' => $this->User->GroupID],
                'layer_type' => ['column' => 'layer_type', 'compare' => '=', 'prefix' => 't', 'value' => Config::LAYER_TYPE_KVS],
            ],
        ];

        $result = $LayersController->getLayers($options);
        $LayersKvs[0] = $result[0];

        $LayersKvs[0]['extent'] = str_replace(' ', ', ', $LayersKvs[0]['extent']);

        $LayersKvs[1]['extent'] = $LayersKvs[0]['extent'];
        $LayersKvs[1]['name'] = 'topic_kvs_layer';
        $LayersKvs[1]['id'] = 1;

        $options = [
            'return' => ['t.id', 't.table_name as name', 't.extent', 't.layer_type', 't.farming', 't.year'],
            'sort' => 'id',
            'order' => 'DESC',
            'where' => [
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'prefix' => 't', 'value' => $this->User->GroupID],
                'layer_type' => ['column' => 'layer_type', 'compare' => '=', 'prefix' => 't', 'value' => Config::LAYER_TYPE_ZP],
            ],
        ];

        $result = $LayersController->getLayers($options);

        $LayersKvs[2] = $result[0];

        $LayersKvs[2]['extent'] = str_replace(' ', ', ', $LayersKvs[2]['extent']);
        $LayersKvs[2]['name'] = 'topic_kvs_zp_layer';

        return $LayersKvs;
    }
}
