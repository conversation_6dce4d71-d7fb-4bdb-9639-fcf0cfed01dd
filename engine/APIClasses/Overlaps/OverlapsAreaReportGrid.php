<?php

namespace TF\Engine\APIClasses\Overlaps;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbOverlaps\UserDbOverlapsController;
use TF\Engine\Plugins\Core\UserDbZPlots\UserDbZPlotsController;

// Prado::using('Plugins.Core.Layers.conf');
// Prado::using('Plugins.Core.Layers.*');

/**
 * Справка пресичане.
 *
 * @rpc-module Overlaps
 *
 * @rpc-service-id overlap-area-report-grid
 */
class OverlapsAreaReportGrid extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getOverlapsAreaReportGrid'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * Return grid data.
     *
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer overlap_id
     *                         #item boolean has_contracts
     *                         }
     * @param string $page pagination parameter
     * @param string $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array
     */
    public function getOverlapsAreaReportGrid(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $LayersController = new LayersController('Layers');

        $overlap_id = $rpcParams['overlap_id'];
        $has_contracts = $rpcParams['has_contracts'];

        if (!$overlap_id) {
            return [];
        }

        // init all needed controllers
        $UserDbZPlotsController = new UserDbZPlotsController($this->User->Database);
        $UserDbOverlapsController = new UserDbOverlapsController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);

        if ('true' == $has_contracts) {
            $has_contract = ['column' => 'has_contracts', 'compare' => '=', 'prefix' => 'p', 'value' => 1];
        } elseif ('false' == $has_contracts) {
            $has_contract = ['column' => 'has_contracts', 'compare' => '<>', 'prefix' => 'p', 'value' => 1];
        } else {
            $has_contract = ['column' => 'has_contracts', 'compare' => '=', 'prefix' => 'p', 'value' => ''];
        }

        $options = [
            'return' => ['o.gid'],
            'where' => [
                'overlap_id' => ['column' => 'overlap_id', 'compare' => '=', 'prefix' => 'o', 'value' => $overlap_id],
                'has_match' => ['column' => 'has_match', 'compare' => '=', 'prefix' => 'o', 'value' => 1],
                'has_contracts' => $has_contract,
            ],
        ];

        $results = $UserDbOverlapsController->getOverlapData($options, false);
        $resultsCount = count($results);
        if (!$resultsCount) {
            return [];
        }

        $kvs_array = [];
        for ($i = 0; $i < $resultsCount; $i++) {
            $kvs_array[] = $results[$i]['gid'];
        }

        $kvs_string = implode(',', $kvs_array);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableOverlaps,
            'return' => ['farming', 'year'],
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $overlap_id],
            ],
        ];

        $overlap = $UserDbController->getItemsByParams($options, false);

        $options = [
            'farming' => $overlap[0]['farming'],
            'year' => $overlap[0]['year'],
            'group_id' => $this->User->GroupID,
            'layer_type' => 1,
        ];
        $tableName = $LayersController->getTableNameByParams($options);
        $tableExists = $UserDbController->getTableNameExist($tableName);

        if (!$tableExists) {
            return [];
        }

        $options = [
            'return' => [
                'SUM(ST_Area(ST_Intersection(a.geom,b.geom))) as area', 'SUM(St_Area(b.geom)) as zp_area', 'isak_prc_uin',
            ],
            'group' => ['isak_prc_uin'],
            'kvs_id_string' => $kvs_string,
        ];

        $count_results = $UserDbZPlotsController->getKVSReportZP($options, $tableName, false, false);
        $counter = count($count_results);

        $options = [
            'return' => [
                'SUM(ST_Area(ST_Intersection(a.geom,b.geom))) as area', 'MAX(St_Area(b.geom)) as zp_area', 'isak_prc_uin',
            ],
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
            'group' => ['isak_prc_uin'],
            'kvs_id_string' => $kvs_string,
        ];

        $results = $UserDbZPlotsController->getKVSReportZP($options, $tableName, false, false);
        $resultsCount = count($results);
        if (!$resultsCount) {
            return [];
        }

        $total_area = 0;
        $total_zp_area = 0;
        for ($i = 0; $i < $resultsCount; $i++) {
            $results[$i]['area'] = number_format($results[$i]['area'] / 1000, 3);
            $results[$i]['zp_area'] = number_format($results[$i]['zp_area'] / 1000, 3);

            $total_area += $results[$i]['area'];
            $total_zp_area += $results[$i]['zp_area'];
        }

        return [
            'rows' => $results,
            'total' => $counter,
            'footer' => [
                [
                    'isak_prc_uin' => '<b>Обща площ:</b>',
                    'zp_area' => number_format($total_zp_area, 3),
                    'area' => number_format($total_area, 3),
                ],
            ],
        ];
    }
}
