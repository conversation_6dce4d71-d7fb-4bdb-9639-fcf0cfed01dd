<?php

namespace TF\Engine\APIClasses\Overlaps;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDbOverlaps\UserDbOverlapsController;

// Prado::using('Plugins.Core.Farming.conf');

/**
 * Grid 'Данни от застъпвания'.
 *
 * @rpc-module Overlaps
 *
 * @rpc-service-id overlaps-data-datagrid
 */
class OverlapsDataGrid extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getOverlapsDataGrid'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * Return grid data.
     *
     * @api-method read
     *
     * @param ?string $sort
     * @param ?string $order
     *
     * @return array
     */
    public function getOverlapsDataGrid(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        // init all needed controllers
        $UserDbOverlapsController = new UserDbOverlapsController($this->User->Database);

        if ('true' == $rpcParams['has_contracts']) {
            $has_contract = ['column' => 'has_contracts', 'compare' => '=', 'prefix' => 'p', 'value' => 1];
        } elseif ('false' == $rpcParams['has_contracts']) {
            $has_contract = ['column' => 'has_contracts', 'compare' => '<>', 'prefix' => 'p', 'value' => 1];
        } else {
            $has_contract = ['column' => 'has_contracts', 'compare' => '=', 'prefix' => 'p', 'value' => ''];
        }

        $options = [
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
            'return' => ['o.*'],
            'where' => [
                'is_edited' => ['column' => 'is_edited', 'compare' => '=', 'prefix' => 'p', 'value' => 'FALSE'],
                'overlap_id' => ['column' => 'overlap_id', 'compare' => '=', 'prefix' => 'o', 'value' => $rpcParams['overlap_id']],
                'has_match' => ['column' => 'has_match', 'compare' => '=', 'prefix' => 'o', 'value' => $rpcParams['has_match']],
                'has_contracts' => $has_contract,
            ],
        ];

        $data = $UserDbOverlapsController->getOverlapData($options, false);
        $counter = $UserDbOverlapsController->getOverlapData($options, true);
        $dataCount = count($data);
        $total_area = 0;

        for ($i = 0; $i < $dataCount; $i++) {
            if (is_numeric($data[$i]['area'])) {
                $total_area += $data[$i]['area'];
            }
        }

        return [
            'rows' => $data,
            'total' => $counter[0]['count'],
            'footer' => [
                ['kad_ident' => '<b>ОБЩО:</b>', 'area' => $total_area . ' (ха)'],
            ],
        ];
    }
}
