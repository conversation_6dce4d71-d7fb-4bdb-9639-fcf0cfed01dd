<?php

namespace TF\Engine\APIClasses\Overlaps;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Дърво 'Застъпвания'.
 *
 * @rpc-module Overlaps
 *
 * @rpc-service-id overlap-layers-tree
 */
class OverlapsLayersTree extends TRpcApiProvider
{
    private $module = 'Overlaps';
    private $service_id = 'overlap-layers-tree';

    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getOverlapLayersTree']],
            'add' => ['method' => [$this, 'addNewOverlap'],
                'validators' => [
                    'farming' => 'validateInteger',
                    'year' => 'validateInteger',
                    'name' => 'validateText',
                ]],
            'delete' => ['method' => [$this, 'deleteOverlapItem']],
        ];
    }

    /**
     * Return tree data.
     *
     * @api-method read
     *
     * @return array
     */
    public function getOverlapLayersTree()
    {
        // init all needed controllers
        $UserDbController = new UserDbController($this->User->Database);
        $FarmingController = new FarmingController('Farming');
        $farming_array = $FarmingController->getUserFarmings();
        $farmingIds = array_keys($farming_array);

        // define help arrays and return array
        // hold all found farmings
        $farmings = [];
        // hold all found farmings-years relations
        $farmings_years = [];
        // hold all found years-farmings relations
        $years_layers = [];
        $farmings_keys = [];
        $farmings_years_keys = [];

        $return = [];

        $options = [
            'tablename' => $UserDbController->DbHandler->tableOverlaps,
            'sort' => 'farming, year',
            'order' => 'asc',
            'where' => [
                'farming_id' => ['column' => 'farming', 'compare' => 'IN', 'value' => $farmingIds],
            ],
        ];

        $results = $UserDbController->DbHandler->getItemsByParams($options, false, false);
        $resultsCount = count($results);
        for ($i = 0; $i < $resultsCount; $i++) {
            $farming = $results[$i]['farming'];
            $year = $results[$i]['year'];
            $layer = $results[$i]['id'];
            $layer_name = $results[$i]['name'];

            // check if farming was already added to the main array
            if (!in_array($farming, $farmings)) {
                $farmings[] = $farming;
                $farmings_years[$farming] = [];

                $return[] = [
                    'id' => $farming,
                    'text' => $farming_array[$farming],
                    'state' => 'open',
                    'children' => [],
                ];

                $farmings_keys[$farming] = count($return) - 1;
            }

            // check if year is in farmings-years array
            if (!in_array($year, $farmings_years[$farming])) {
                $farmings_years[$farming][] = $year;
                $years_layers[$year] = [];

                $return[$farmings_keys[$farming]]['children'][] = [
                    'id' => $year,
                    'text' => $GLOBALS['Farming']['years'][$year]['title'],
                    'state' => 'open',
                    'children' => [],
                ];

                $farmings_years_keys[$year] = count($farmings_years[$farming]) - 1;
            }

            if (!in_array($layer, $years_layers[$year])) {
                $years_layers[$year][] = $layer;

                $return[$farmings_keys[$farming]]['children'][$farmings_years_keys[$year]]['children'][] = [
                    'id' => $layer,
                    'text' => $layer_name,
                    'status' => $results[$i]['status'],
                    'iconCls' => 'icon-tree-intersection',
                ];
            }
        }

        return $return;
    }

    /**
     * Adds new overlap layer to the tree.
     *
     * @api-method add
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer farming
     *                         #item integer year
     *                         #item string  name
     *                         }
     *
     * @return int
     */
    public function addNewOverlap($rpcParams)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $options = [
            'tablename' => $UserDbController->DbHandler->tableOverlaps,
            'mainData' => [
                'farming' => $rpcParams['farming'],
                'year' => $rpcParams['year'],
                'name' => $rpcParams['name'],
                'status' => 0,
            ],
        ];

        $recordID = $UserDbController->addItem($options);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options, ['new_id' => $recordID], 'Аdd New Overlap');

        return $recordID;
    }

    /**
     * Deletes the selected overlap item.
     *
     * @param int $rpcParam
     */
    public function deleteOverlapItem($rpcParam)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $options = [
            'tablename' => $UserDbController->DbHandler->tableOverlaps,
            'id_string' => $rpcParam,
        ];

        $UserDbController->deleteItemsByParams($options);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options, [], 'Delete Overlap Item');
    }
}
