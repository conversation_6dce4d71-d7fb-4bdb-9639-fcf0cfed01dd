<?php

namespace TF\Engine\APIClasses\ThematicMaps;

use Prado\Web\Services\TRpcApiProvider;

/**
 * Използва се за създаване на списъци с колони,
 * по които може да се филтрира, и които могат да се задават
 * като критерии за оцветяване.
 *
 * @rpc-module Thematic-Maps
 *
 * @rpc-service-id main-layer-columns-combobox
 */
class ThematicMapsMainLayerColumns extends TRpcApiProvider
{
    private $UserDbController = false;

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getMainLayerColumns']],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item string ['layer_name'] - име на основния слой
     *                         }
     *
     * @return array - масив с колони, в зависимост от слоя
     */
    public function getMainLayerColumns($rpcParams)
    {
        if ('layer_kvs' == $rpcParams['layer_name']) {
            return $this->getColumnsForLayerKvs($rpcParams);
        }
        if ('layer_isak_' == substr($rpcParams['layer_name'], 0, 11)) {
            return $this->getColumnsForLayerIsak();
        }
    }

    private function getColumnsForLayerKvs($rpcParams)
    {
        $return = [
            0 => [
                'id' => 'kad_ident',
                'prefix' => 'kvs',
                'name' => 'Идентификатор',
                'description' => 'Пълен идентификатор на имот ( ЕКАТТЕ.Масив.Имот ).',
            ],
            1 => [
                'id' => 'ekate',
                'prefix' => 'kvs',
                'name' => 'ЕКАТТЕ',
                'description' => 'ЕКАТТЕ, в което да се намират имотите, които ще бъдат оцветени.',
            ],
            2 => [
                'id' => 'masiv',
                'prefix' => 'kvs',
                'name' => 'Масив',
                'description' => 'Масив, в който да се намират имотите, които да бъдат оцветени.',
            ],
            3 => [
                'id' => 'number',
                'prefix' => 'kvs',
                'name' => 'Имот',
                'description' => 'Номер на имотите, които да бъдат оцветени.',
            ],
            4 => [
                'id' => 'category',
                'prefix' => 'kvs',
                'name' => 'Категория',
                'description' => 'Категория на имотите, които да бъдат оцветени.',
            ],
            5 => [
                'id' => 'area_type',
                'prefix' => 'kvs',
                'name' => 'НТП',
                'description' => 'Начин на трайно ползване на имотите, които да бъдат оцветени.',
            ],
            6 => [
                'id' => 'mestnost',
                'prefix' => 'kvs',
                'name' => 'Местност',
                'description' => 'Местостта, в която се намират имотите, които да бъдат оцветени.',
            ],
            7 => [
                'id' => 'include',
                'prefix' => 'kvs',
                'name' => 'Извън масиви за ползване',
                'description' => 'Имоти, които не участват в масиви за ползване по чл. 37в от ЗСПЗЗ.',
                'type' => 'boolean',
            ],
            8 => [
                'id' => 'participate',
                'prefix' => 'kvs',
                'name' => 'Участвай в масиви за ползване',
                'description' => 'Имоти, за които е маркирано участие в масиви за ползване по чл. 37в от ЗСПЗЗ.',
                'type' => 'boolean',
            ],
            9 => [
                'id' => 'white_spots',
                'prefix' => 'kvs',
                'name' => 'Имоти по чл. 37в (бели петна)',
                'description' => 'Имоти, за които е маркирано да бъдат включени в масиви за ползване като имот по чл. 37в, ал. 3, т. 2 от ЗСПЗЗ, (имоти - бели петна).',
                'type' => 'boolean',
            ],
            10 => [
                'id' => 'usable',
                'prefix' => 'kvs',
                'name' => 'Обработваема',
                'description' => 'Имоти, които са маркирани като обработваема площ.',
                'type' => 'boolean',
            ],
            11 => [
                'id' => 'irrigated_area',
                'prefix' => 'kvs',
                'name' => 'Поливна площ',
                'description' => 'Имоти, които са маркирани като поливна площ',
                'type' => 'boolean',
            ],
            12 => [
                'id' => 'is_subleased',
                'prefix' => 'c',
                'name' => 'Преотдаден',
                'description' => 'Имоти, които са преотдадени за разглеждан период от време.',
                'type' => 'boolean',
            ],
            13 => [
                'id' => 'farming_id',
                'prefix' => 'c',
                'name' => 'Стопанство',
                'description' => 'Имоти, които участват в договор на избраното стопанство.',
            ],
            14 => [
                'id' => 'owner_names',
                'prefix' => 'o',
                'name' => 'Име на собственик',
                'description' => 'Имоти, които имат добавено физическо лице като собственик, чиито имена отговарят на въведените критерии.',
            ],
            15 => [
                'id' => 'egn',
                'prefix' => 'o',
                'name' => 'ЕГН на собственик',
                'description' => 'Имоти, които имат добавено физическо лице като собственик, ЕГН на който отговаря на въведения критерий.',
                'type' => 'numeric',
            ],
            16 => [
                'id' => 'company_name',
                'prefix' => 'o',
                'name' => 'Име на фирма',
                'description' => 'Имоти, които имат добавена фирма като собственик, имената на която отговарят на въведените критерии.',
            ],
            17 => [
                'id' => 'eik',
                'prefix' => 'o',
                'name' => 'ЕИК на фирма',
                'description' => 'Имоти, които имат добавена фирма като собственик, ЕИК на която отговаря на въведения критерии.',
                'type' => 'numeric',
            ],
            18 => [
                'id' => 'nm_usage_rights',
                'prefix' => 'c',
                'name' => 'Тип на договор',
                'description' => 'Имоти, които са въведени в договор от съответния тип.',
            ],
            19 => [
                'id' => 'start_date',
                'prefix' => 'c',
                'name' => 'Начална дата на дог. след',
                'description' => 'Имоти, които участват в договори с начална дата след въведената.',
                'type' => 'datebox',
            ],
            20 => [
                'id' => 'due_date',
                'prefix' => 'c',
                'name' => 'Крайна дата на дог. преди',
                'description' => 'Имоти, които участват в договори с крайна дата преди въведената.',
                'type' => 'datebox',
            ],
            21 => [
                'id' => 'in_hypothec',
                'prefix' => 'h',
                'name' => 'Ипотекиран',
                'description' => 'Имоти, които участват в договори с крайна дата преди въведената.',
                'type' => 'boolean',
            ],
            22 => [
                'id' => 'h_start_date',
                'prefix' => 'c',
                'name' => 'Начална дата на ипот. след',
                'description' => 'Ипотекиран имот, с начална дата на ипотеката след избраната дата.',
                'type' => 'datebox',
            ],
            23 => [
                'id' => 'h_due_date',
                'prefix' => 'c',
                'name' => 'Крайна дата на ипот. преди',
                'description' => 'Ипотекиран имот, с начална дата на ипотеката преди избраната дата.',
                'type' => 'datebox',
            ],
        ];

        if ($rpcParams['grouping']) {
            $return[14]['name'] = 'Име на собственик/фирма';
            $return[15]['name'] = 'ЕГН/ЕИК на собственик/фирма';
            unset($return[16], $return[17]);
            $return = array_values($return);
        }

        return $return;
    }

    private function getColumnsForLayerIsak()
    {
        return [
            0 => [
                'id' => 'prc_uin',
                'prefix' => 'isak',
                'name' => 'Идентификатор',
                'description' => 'Пълен идентификатор на имот ( prc_uin ).',
            ],
            1 => [
                'id' => 'watering',
                'prefix' => 'isak',
                'name' => 'Напояване',
                'description' => 'Напояване ??',
                'type' => 'boolean',
            ],
            2 => [
                'id' => 'cropcode',
                'prefix' => 'isak',
                'name' => 'Култура',
                'description' => 'Културата, която е декларирана за имота',
            ],
            3 => [
                'id' => 'ekatte',
                'prefix' => 'isak',
                'name' => 'ЕКАТТЕ',
                'description' => 'ЕКАТТЕ на землището, в което се намира имота',
            ],
            4 => [
                'id' => 'schemata',
                'prefix' => 'isak',
                'name' => 'Схема / Мярка',
                'description' => 'Схема / Мярка',
            ],
            5 => [
                'id' => 'campaign',
                'prefix' => 'isak',
                'name' => 'Кампания',
                'description' => 'Кампания за директни плащания',
            ],
        ];
    }
}
