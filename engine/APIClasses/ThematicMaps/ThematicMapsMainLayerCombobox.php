<?php

namespace TF\Engine\APIClasses\ThematicMaps;

use Prado\Web\Services\TRpcApiProvider;
use TF\Application\Common\Config;
use TF\Engine\Plugins\Core\Layers\LayersController;

/**
 * Създава списъци с основни слоеве, от които потребителя да може да си избира.
 *
 * @rpc-module Thematic-Maps
 *
 * @rpc-service-id thematic-maps-main-layer-combobox
 */
class ThematicMapsMainLayerCombobox extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getMainLayerComboboxItems'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * @api-method read
     *
     * @param int|string $page pagination parameter
     * @param int|string $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array
     */
    public function getMainLayerComboboxItems(array $rpcParams = [], int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $return = [];
        $LayersController = new LayersController('Layers');

        $kvs = ['layer_id' => 'layer_kvs', 'name' => 'КВС Имоти', 'farming_name' => ''];
        $return[] = $kvs;
        // get all farmings
        $options = [
            'return' => [
                'f.id as farming_id', 'f.name as farming_name', 't.*',
            ],
            'where' => [
                'group_id_layer' => ['column' => 'group_id', 'compare' => '=', 'prefix' => 'f', 'value' => $this->User->GroupID],
                'group_id_farming' => ['column' => 'group_id', 'compare' => '=', 'prefix' => 't', 'value' => $this->User->GroupID],
                'layer_type' => ['column' => 'layer_type', 'compare' => '=', 'prefix' => 't', 'value' => Config::LAYER_TYPE_ISAK],
                'is_exists' => ['column' => 'is_exist', 'compare' => '=', 'prefix' => 't', 'value' => 'true'],
            ],
            'sort' => 'farming asc, year asc, layer_type',
            'order' => 'desc',
        ];
        $results = $LayersController->getLayers($options);

        foreach ($results as $result) {
            $yearTxt = $GLOBALS['Farming']['years'][$result['year']]['title'];
            $tmpLayer = [
                'layer_id' => $result['table_name'],
                'name' => 'От ИСАК / ' . $yearTxt,
                'farming_name' => $result['farming_name']];

            $return[] = $tmpLayer;
        }

        return $return;
    }
}
