<?php

namespace TF\Engine\APIClasses\ThematicMaps;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Kernel\PrintPdf;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\Users\UsersController;

// Prado::using('Plugins.Core.Farming.conf');
// Prado::using('Plugins.Core.Farming.*');
/**
 * Четене на списък със създадени тематични карти, както и изтриването им.
 *
 * @rpc-module Thematic-Maps
 *
 * @rpc-service-id thematic-maps-layers
 *
 * @property LayersController $LayersController
 * @property UserDbController $UserDbController
 * @property UsersController $UsersController
 */
class ThematicMapsLayers extends TRpcApiProvider
{
    private $module = 'ThematicMaps';
    private $service_id = 'thematic-maps-layers';

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getThematicLayersGrid']],
            'print' => ['method' => [$this, 'printThematicLayer']],
            'delete' => ['method' => [$this, 'deleteThematicMap'],
                'validators' => [
                    'id' => 'validateInteger, validateRequired',
                ],
            ],
        ];
    }

    /**
     * Метод, който връща всички вече направени тематични карти за дадения потребител,
     * както и винаги добавя на първо място поле за добавяне на нова тематична карта.
     *
     * @api-method read
     *
     * @return array
     *               {
     *               #item integer total
     *               #item array rows
     *               {
     *               #item integer id             - id на конкретната карта
     *               #item string  name           - име на картата
     *               #item string  description    - основен слой на картата
     *               #item string  role           - роля - нова карта или вече съществуващ слой
     *               #item json    filters        - приложени филтри
     *               #item string  criteria       - критерии за оцветяване
     *               #item string  criteria_text  - четим текст, на критерия за оцветяване
     *               #item integer chart_type     - тип на графиката
     *               #item integer chart_criteria - критерии за групиране на графиката (брой имоти или обща площ)
     *               }
     *               }
     */
    public function getThematicLayersGrid()
    {
        $UserDbController = new UserDbController($this->User->Database);
        $LayersController = new LayersController('Layers');

        // Вземат се всички слоеве, свързани с този потребител,
        // за да се форматират по четлив начин Слой/стопанство/година
        $options = [
            'return' => [
                'f.name as farming_name', 'f.*', 't.*',
            ],
            'where' => [
                'group_id_layer' => ['column' => 'group_id', 'compare' => '=', 'prefix' => 'f', 'value' => $this->User->GroupID],
                'group_id_farming' => ['column' => 'group_id', 'compare' => '=', 'prefix' => 't', 'value' => $this->User->GroupID],
            ],
            'sort' => 'farming asc, year asc, layer_type',
            'order' => 'desc',
        ];

        $results = $LayersController->getLayers($options, false, false);
        $resultsCount = count($results);
        $layers = [];

        for ($i = 0; $i < $resultsCount; $i++) {
            $layers[$results[$i]['table_name']] = $results[$i]['name'] . ' / ' . $GLOBALS['Farming']['years'][$results[$i]['year']]['farming_year_short'] . ' <br> ' . $results[$i]['farming_name'];
        }

        // Двата слоя не са обвързани със стопанство и година, за това се презаписват имената им
        $layers['layer_kvs'] = 'КВС Имоти';
        $layers['layer_gps'] = 'Временни данни';

        $return = [
            'rows' => [],
            'total' => 0,
        ];

        // Създава се първоначалния слой, който да стартира процедура за създаване на нова тематична карта
        $options = [
            'tablename' => $UserDbController->DbHandler->tableThematicMaps,
        ];
        $results = $UserDbController->getItemsByParams($options, false, false);
        $resultsCount = count($results);

        // Преминава се през всички резултати, за да се форматират в четлив вид
        for ($i = 0; $i < $resultsCount; $i++) {
            $return['rows'][$i]['id'] = $results[$i]['id'];
            $return['rows'][$i]['name'] = $results[$i]['name'];
            $return['rows'][$i]['description'] = $layers[$results[$i]['map_layer']];
        }

        // Добавя се ръчно създадения резултат за нов слой на първо място, за да бъде винаги най-отгоре.
        $return['total'] = count($return['rows']);

        return $return;
    }

    /**
     * Изтриване на избрана тематична карта.
     *
     * @param int $id - id на избраната за изтриване тематична карта
     *
     * @throws MTRpcException -33053 Търсеният слой не е намерен
     */
    public function deleteThematicMap($id)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $options = [
            'tablename' => $UserDbController->DbHandler->tableThematicMaps,
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $id],
            ],
        ];

        $results = $UserDbController->getItemsByParams($options, false, false);
        $results = $results[0];

        if (null === $results) {
            throw new MTRpcException('MAP_REQUESTED_LAYER_NOT_FOUND', -33053);
        }

        $options = [
            'tablename' => $UserDbController->DbHandler->tableThematicMaps,
            'id_string' => $id,
        ];

        $UserDbController->deleteItemsByParams($options);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, [$results], [], 'deleting thematic map');
    }

    /**
     * Принтира избраната тематична карта катo PDF файл.
     *
     * @param int $mapID - ID на избраната тематична карта
     * @param string $size
     * @param string $newExtent
     * @param string $newScale
     *
     * @throws MTRpcException
     * @throws \iio\libmergepdf\Exception
     *
     * @return array
     *               {
     *               #item string pdf_blank_file - относителен път до новосъздадения PDF файл.
     *               }
     */
    public function printThematicLayer($mapID, $size = '', $newExtent = '', $newScale = '')
    {
        $UserDbController = new UserDbController($this->User->Database);
        $LayersController = new LayersController('Layers');

        $options = [
            'tablename' => $UserDbController->DbHandler->tableThematicMaps,
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $mapID],
            ],
        ];

        $result = $UserDbController->getItemsByParams($options, false, false);
        $result = $result[0];

        $colors = json_decode($result['colors'], true);

        // Взема се extent на картата от записания в базата резултат и се прилага като extent за export
        if ($newExtent) {
            $extent = explode(',', $newExtent);
        } else {
            $extent = explode(' ', $result['extent']);
        }

        // Размери за формат А4 и се задават за размери на export
        switch ($size) {
            case 'A0':
                $width = 4450;
                $height = 3050;
                $labelSize = 16;

                break;
            case 'A1':
                $width = 3120;
                $height = 2120;
                $labelSize = 14;

                break;
            case 'A2':
                $width = 2200;
                $height = 1460;
                $labelSize = 12;

                break;
            case 'A3':
                $width = 1550;
                $height = 1000;
                $labelSize = 10;

                break;
            default:
                $width = 1080;
                $height = 660;
                $labelSize = 7;

                break;
        }

        // Обикаля се през резултатите, за да се вземе името на полето,
        // което да бъде визуализирано в легендата. След това
        // всеки от новосъздадените класове се добавя към
        // основния слой
        $text = '<table style="font-size: 14px; vertical-align: middle; width:100%;" cellspacing=0>
            <tr style="border-bottom: 1px solid black;">
                <th style="border-bottom: 1px solid black; text-align: center;padding-bottom: 5px;">Цвят</th>
                <th style="border-bottom: 1px solid black; text-align: center">Критерий</th>
                <th style="border-bottom: 1px solid black; text-align: center">Бр. Имоти</th>
                <th style="border-bottom: 1px solid black; text-align: center">Площ</th>
            </tr>
            <tr style="text-align: center; margin-bottom: 5px;">
                <td style="padding-bottom: 5px; border-bottom: 1px solid black;"><div style="width: 15px;"><hr></div></td>
                <td style="padding-bottom: 5px; border-bottom: 1px solid black; padding-top: 5px;">КВС Имоти</td>
                <td style="padding-bottom: 5px; border-bottom: 1px solid black;"></td>
                <td style="padding-bottom: 5px; border-bottom: 1px solid black;"></td>
            </tr>';

        $colorsCount = count($colors);
        for ($i = 0; $i < $colorsCount; $i++) {
            if ($i != count($colors) - 1) {
                $text .= '
                <tr style="text-align: center;page-break-before: always">
                    <td style="border-bottom: 1px solid black;width: 100px;"><div style="width: 15px; height: 15px;color: ' . $colors[$i]['color_field'] . '; background-color: ' . $colors[$i]['color_field'] . '"></div></td>
                    <td style="border-bottom: 1px solid black;width: 175px;padding: 5px;">' . $colors[$i]['value_field'] . '</td>
                    <td style="border-bottom: 1px solid black;width: 175px;">' . $colors[$i]['plot_count'] . '</td>
                    <td style="border-bottom: 1px solid black;width: 175px;">' . $colors[$i]['area'] . '</td>
                </tr>';
            } else {
                $text .= '
                <tr style="text-align: center;page-break-before: always">
                    <td style="width: 100px;"><div style="width: 15px; height: 15px;color: ' . $colors[$i]['color_field'] . '; background-color: ' . $colors[$i]['color_field'] . '"></div></td>
                    <td style="width: 175px;padding: 5px;">' . $colors[$i]['value_field'] . '</td>
                    <td style="width: 175px;">' . $colors[$i]['plot_count'] . '</td>
                    <td style="width: 175px;">' . $colors[$i]['area'] . '</td>
                </tr>';
            }
        }
        $text .= '</table>';
        $usrImgPlotsDir = WMS_IMAGE_PATH . $_SESSION['group_id'];
        if (!file_exists($usrImgPlotsDir)) {
            mkdir($usrImgPlotsDir);
        }
        $mapFile = WMS_MAP_PATH . $_SESSION['group_id'] . '.map';
        $imgPath = $usrImgPlotsDir . '/thematic_map_' . $_SESSION['group_id'] . '.png';
        $url = WMS_IMAGE_URL . $_SESSION['group_id'] . '/thematic_map_' . $_SESSION['group_id'] . '.png';
        $extent = implode(' ', $extent);
        $cmd = MAPSERVER_COMMANDS_PATH . "shp2img -m {$mapFile} -l thematic_map -e {$extent} -s {$width} {$height} -o {$imgPath}";
        system($cmd, $output);
        // Новосъздаденото изображение се добавя в PDF файл.
        $img = '<img style="width:100%;" src="' . $imgPath . '" />';
        $contentFirstPageOfThePDF = '
        <page style="font-family: freesans;">
            <div style="text-align: center;"><h3 style="margin-bottom: 0">' . $result['name'] . '</h3></div>
            <div style="border: 2px solid #000;">' . $img . '</div><br />
        </page>
        ';

        $contentSecondPageOfThePDF = '
        <page>
            <fieldset style="border: 1px solid black; padding: 5px; margin:25px; 25px 0 25px;">
                <legend style="padding: 5px 15px; font-size: 16px; font-weight: bold; border:1px solid black; background: #ffffff">Легенда:</legend>
                    ' . $text . '
                </fieldset>
        </page>
        ';

        $date = date('Y-m-d-H-i-s');
        $pdfPathFirstPart = 'files/uploads/export/' . $this->User->GroupID . '_thematic_map_first_part_' . $date . '.pdf';
        $pdfPathSecondPart = 'files/uploads/export/' . $this->User->GroupID . '_thematic_map_second_part_' . $date . '.pdf';

        $$printPdf = new PrintPdf();
        $printPdf->generateFromHtml($contentFirstPageOfThePDF, $pdfPathFirstPart, [
            'orientation' => 'Landscape',
            'page-size' => $size,
        ], true);

        $printPdf->generateFromHtml($contentSecondPageOfThePDF, $pdfPathSecondPart, [
            'orientation' => 'Portrait',
            'page-size' => 'A4',
        ], true);

        $merger = new \iio\libmergepdf\Merger();
        $merger->addIterator([
            $pdfPathFirstPart,
            $pdfPathSecondPart,
        ]);
        $createdPdf = $merger->merge();

        $pdfPathMerged = 'files/uploads/export/' . $this->User->GroupID . '_thematic_map_full_' . $date . '.pdf';

        if (!file_put_contents($pdfPathMerged, $createdPdf)) {
            throw new MTRpcException('MAP_PRINT_ERROR', -33066);
        }

        unlink($pdfPathFirstPart);
        unlink($pdfPathSecondPart);

        return $return['pdf_blank_file'] = $pdfPathMerged;
    }
}
