<?php

namespace TF\Engine\APIClasses\ThematicMaps;

use Prado\Prado;
use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\ThematicMaps\LayerIsakThematicMap;
use TF\Engine\Kernel\ThematicMaps\LayerKvsThematicMap;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController;
use TF\Engine\Plugins\Core\Users\UsersController;

// Prado::using('Plugins.Core.Layers.conf');
// Prado::using('Plugins.Core.Layers.*');
// Prado::using('APIClasses.ThematicMaps.LayerKvsThematicMap');
// Prado::using('Kernel.Validation');

/**
 * Отговаря за създаване на нови тематични карти,
 * както и за визуализацията им
 *
 * @rpc-module Themаtic-Maps
 *
 * @rpc-service-id thematic-maps-results-grid
 *
 * @property LayerIsakThematicMap|LayerKvsThematicMap $activeLayer
 */
class ThematicMapsResultsGrid extends TRpcApiProvider
{
    private $module = 'thematic-maps';
    private $service_id = 'thematic-maps-results-grid';
    private $boolean_criteria = false;
    private $activeLayer;

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getThematicLayersGrid'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'save' => ['method' => [$this, 'saveThematicMap'],
                'validators' => [
                    'rpcParams' => [
                        'layerData' => [
                            'filters' => 'validateRequired',
                            'mapName' => 'validateText, validateRequired',
                            'criteria' => 'validateText, validateRequired',
                            'criteriaText' => 'validateText, validateRequired',
                            'mapLayer' => 'validateText, validateRequired',
                            'chart_type' => 'validateInteger',
                            'chart_criteria' => 'validateInteger',
                        ],
                    ],
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'load' => ['method' => [$this, 'loadThematicMap'],
                'validators' => [
                    'mapID' => 'validateRequired, validateInteger',
                ],
            ],
        ];
    }

    /**
     * Връща резултати за грида, в който потребителя вижда резултатите
     * и може да избере цветове за отделните категории(резултати).
     *
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item string mapName             - име на новата тематична карта
     *                         #item string mapLayer            - име на основния слой
     *                         #item array filters              - приложени филтри
     *                         {
     *                         #item string column      - колона, по която се филтрира
     *                         #item string filterText  - текст на колоната, по която е филтрирано в четим текст
     *                         #item string filterValue - стойност, на която да отговарят резултатите
     *                         }
     *                         #item string criteria            - критерии за оцветяване
     *                         #item string criteriaText        - критерии за оцветяване в четим текст
     *                         }
     * @param  int string|$page  - pagination parameter
     * @param  int string|$rows  - pagination parameter
     * @param string $sort - pagination parameter
     * @param string $order - pagination parameter
     *
     * @return array
     *               {
     *               #item integer total
     *               #item integer rows
     *               {
     *               #item string original_value     - резултат от критерии за групиране
     *               #item string value_field        - резултат от критерии за групиране в четим текст
     *               #item string color_field        - цвят на конкретния резултат
     *               #item string criteria           - критерия, по които е групирано
     *               #item string plot_count         - брой имоти, които попадат в критерия за групиране
     *               #item string area               - площ на имоти, които попадат в критерия за групиране
     *               }
     *               }
     */
    public function getThematicLayersGrid(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);

        $this->setActiveLayer($rpcParams);

        if (!$this->activeLayer) {
            return ['rows' => [], 'total' => 0];
        }

        return $this->activeLayer->getResults($rpcParams, $page, $rows, $sort, $order);
    }

    /**
     * Записва новата тематична карта и създава map file, за да я визуализира на потребителя.
     *
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item array layerData                       - данни за тематичната карта
     *                         {
     *                         #item array   filters               - приложени филтри
     *                         #item string  mapName               - име на тематичната карта
     *                         #item string  criteria              - критерии за оцветяване
     *                         #item string  criteriaText          - критерии за оцветяване в четим вариант
     *                         #item string  mapLayer              - основен слой, на който се базира картата
     *                         #item integer chart_type            - тип на диаграмата
     *                         #item integer chart_criteria        - критерии за групиране на диаграмата
     *                         }
     *                         #item array colorSet                       - данни за цветовете
     *                         {
     *                         #item array rows                    - отделните редове с резултати и цветовете към тях
     *                         {
     *                         #item string original_value - резултат от критерии за групиране
     *                         #item string value_field    - резултат от критерии за групиране в четим текст
     *                         #item string color_field    - цвят на конкретния резултат
     *                         #item string criteria       - критерия, по които е групирано
     *                         #item string plot_count     - брой имоти, които попадат в критерия за групиране
     *                         #item string area           - площ на имоти, които попадат в критерия за групиране
     *                         }
     *                         }
     *                         }
     * @param int|string $page - pagination parameter
     * @param int|string $rows - pagination parameter
     * @param string $sort - pagination parameter
     * @param string $order - pagination parameter
     *
     * @return array with integer $thematicMapId - id на новосъздадената тематична карта
     */
    public function saveThematicMap(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $this->setActiveLayer($rpcParams);

        // Присвояване на входните параметри към променливи за по-лесна работа
        $filters = $rpcParams['layerData']['filters'];
        $name = $rpcParams['layerData']['mapName'];
        $column = $rpcParams['layerData']['criteria'];
        $criteriaText = $rpcParams['layerData']['criteriaText'];
        $criteriaPrefix = $rpcParams['layerData']['criteriaPrefix'];
        $mapLayer = $rpcParams['layerData']['mapLayer'];
        $chartType = $rpcParams['layerData']['chart_type'];
        $chartCriteria = $rpcParams['layerData']['chart_criteria'];

        $UserDbController = new UserDbController($this->User->Database);
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);
        $UsersController = new UsersController('Users');

        $extent = $this->activeLayer->getExtent($rpcParams);

        $options = [
            'tablename' => $UserDbPlotsController->DbHandler->tableThematicMaps,
            'mainData' => [
                // Филтрите се запазват в json вариант, за да може лесно да бъдат използвани за ново зареждане на вече създадени тематични карти
                'filters' => json_encode($filters, JSON_UNESCAPED_UNICODE),
                'name' => $name,
                'criteria' => $column,
                'criteria_text' => $criteriaText,
                'criteria_prefix' => $criteriaPrefix,
                'map_layer' => $mapLayer,
                'chart_type' => $chartType,
                'chart_criteria' => $chartCriteria,
                // Избраните от потребителя цветове се запазват в json вариант, за да се използват за последваща визуализация на картите
                'colors' => json_encode($rpcParams['colorSet']['rows'], JSON_UNESCAPED_UNICODE),
                'extent' => $extent,
            ],
        ];
        $thematicMapId = $UserDbPlotsController->addItem($options);
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options, $thematicMapId, 'adding new thematic map');

        $this->createThematicMap($rpcParams);

        return ['id' => $thematicMapId, 'extent' => $extent];
    }

    /**
     * Зарежда създадени тематични карти.
     *
     * @param int $mapID id - на избраната за зареждане карта
     *
     * @return array - данни за избраната тематична карта
     *               {
     *               #item array layerData                       - данни за тематичната карта
     *               {
     *               #item array   filters               - приложени филтри
     *               #item string  mapName               - име на тематичната карта
     *               #item string  criteria              - критерии за оцветяване
     *               #item string  criteriaText          - критерии за оцветяване в четим вариант
     *               #item string  mapLayer              - основен слой, на който се базира картата
     *               #item integer chart_type            - тип на диаграмата
     *               #item integer chart_criteria        - критерии за групиране на диаграмата
     *               }
     *               #item array colorSet                       - данни за цветовете
     *               {
     *               #item array rows                    - отделните редове с резултати и цветовете към тях
     *               {
     *               #item string original_value - резултат от критерии за групиране
     *               #item string value_field    - резултат от критерии за групиране в четим текст
     *               #item string color_field    - цвят на конкретния резултат
     *               #item string criteria       - критерия, по които е групирано
     *               #item string plot_count     - брой имоти, които попадат в критерия за групиране
     *               #item string area           - площ на имоти, които попадат в критерия за групиране
     *               }
     *               }
     *               }
     */
    public function loadThematicMap($mapID)
    {
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);
        $mapInfo = $UserDbPlotsController->getThematicMapInfoById($mapID);
        $rpcParams['layerData'] = $mapInfo[0];
        $rpcParams['layerData']['filters'] = json_decode($rpcParams['layerData']['filters'], true);
        $rpcParams['layerData']['criteriaText'] = $rpcParams['layerData']['criteria_text'];
        $rpcParams['layerData']['criteriaPrefix'] = $rpcParams['layerData']['criteria_prefix'];
        $rpcParams['layerData']['mapLayer'] = $rpcParams['layerData']['map_layer'];
        $rpcParams['layerData']['mapName'] = $rpcParams['layerData']['name'];
        $rpcParams['colorSet']['rows'] = json_decode($mapInfo[0]['colors'], true);

        $this->createThematicMap($rpcParams);

        return $rpcParams;
    }

    /**
     * Създава map файл, който да визуализира тематичната карта.
     *
     * @param  [type] $rpcParams [description]
     *
     * @return [type]            [description]
     */
    private function createThematicMap($rpcParams)
    {
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);
        $LayersController = new LayersController('Layers');
        $this->setActiveLayer($rpcParams);
        // Присвояване на входните параметри към нови променливи за по-лесна работа
        $layerData = $rpcParams['layerData'];
        $colorData = $rpcParams['colorSet']['rows'];
        $colorDataCount = count($colorData);
        // Конвертират се всички цветове от hexadecimal (#FFFFFF) във RGB формат,
        // който е подходящ за използване директно в map файла - 255 255 255
        for ($i = 0; $i < $colorDataCount; $i++) {
            list($r, $g, $b) = sscanf($colorData[$i]['color_field'], '#%02x%02x%02x');
            $colorData[$i]['rgb_color'] = $r . ' ' . $g . ' ' . $b;
        }

        // Добавя се флаг, който оказва, че данните са необходими за map fail
        $layerData['forMapFile'] = true;

        $queryString = '';
        $extent = '';

        // Взема се заявката за резултатите, която да бъде поставена в map file
        $queryString = $this->activeLayer->getResults($layerData);
        // Добавя се нов флаг, който да окаже, че е необходима обработката на данните за
        // заявката за слоя със етикети
        $layerData['forLabels'] = true;
        $labelQuery = $this->activeLayer->getResults($layerData);
        // Добавя се флаг, с който да се окаже, че е необходимо да се вземе само extent
        // на тези резулати и се премахват другите 2 флага
        $layerData['forExtent'] = true;
        $layerData['forMapFile'] = false;
        $layerData['forLabels'] = false;
        $extent = $this->activeLayer->getResults($layerData);
        $extent = $extent[0]['extent'];

        // Променливата държи в себе си стринг с данни за връзката към базата данни
        // за конкретния потребител, на база на която map файлът ще генерира визуализация на резултатите
        $connection_string = "CONNECTION 'host=" . DEFAULT_DB_HOST . ' dbname=' . $this->User->Database . ' user=' . DEFAULT_DB_USERNAME . ' password=' . DEFAULT_DB_PASSWORD . ' port=' . DEFAULT_DB_PORT . "'";

        // В тази променлива се държат всички необходими данни, които да бъдат подадени към шаблона за map файлът
        $map_results = [
            'results' => $colorData,
            'connection_string' => $connection_string,
            'query_string' => $queryString,
            'label_string' => $labelQuery,
            'criteria' => $layerData['criteria'],
            'is_boolean_criteria' => $this->activeLayer->boolean_criteria,
            'layer_type' => $this->activeLayer->getLayerType(),
            'table_name' => $layerData['map_layer'] ? $layerData['map_layer'] : $layerData['mapLayer'],
        ];

        // Трансформира extent във вид, подходящ за map файл и го добавя към основния масив с данни
        $extent = str_replace('BOX(', '', $extent);
        $extent = str_replace(')', '', $extent);
        $extent = str_replace(',', ' ', $extent);
        $map_results['extent'] = $extent;

        if (5 == $this->activeLayer->main_layer_type) {
            $this->activeLayer->getKVSQuery($layerData);
            $map_results['ekate_styles'] = $this->activeLayer->kvs_styles;
            $map_results['kvs_query'] = $this->activeLayer->kvs_query;
        }

        // Създаване на текста за map файлът и записването му във визически файл
        $mapString = $LayersController->StringHelper->loadTemplate($GLOBALS['Templates'][$this->activeLayer->getTemplateID()]['template'], $map_results);

        if (!file_exists(WMS_MAP_PATH . '/' . $this->User->GroupID . '_thematic_map.map')) {
            touch(WMS_MAP_PATH . '/' . $this->User->GroupID . '_thematic_map.map');
        }

        $mapFile = fopen(WMS_MAP_PATH . '/' . $this->User->GroupID . '_thematic_map.map', 'w');
        fwrite($mapFile, $mapString);
        fclose($mapFile);

        return true;
    }

    private function setActiveLayer($rpcParams)
    {
        if ('layer_kvs' == $rpcParams['layerData']['mapLayer'] || 'layer_kvs' == $rpcParams['mapLayer']) {
            $this->activeLayer = new LayerKvsThematicMap();
        }
        if ('layer_isak_' == substr($rpcParams['layerData']['mapLayer'], 0, 11) || 'layer_isak_' == substr($rpcParams['mapLayer'], 0, 11)) {
            $this->activeLayer = new LayerIsakThematicMap();
        }
    }
}
