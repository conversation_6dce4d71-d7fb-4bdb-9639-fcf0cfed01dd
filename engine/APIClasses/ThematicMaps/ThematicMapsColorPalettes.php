<?php

namespace TF\Engine\APIClasses\ThematicMaps;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Layers\LayersController;

/**
 * Налични цветови схеми "Тематични карти".
 *
 * @rpc-module Thematic-Maps
 *
 * @rpc-service-id thematic-maps-color-palettes
 *
 * @property LayersController $LayersController
 */
class ThematicMapsColorPalettes extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getColorPalettes']],
        ];
    }

    /**
     * @api-method read
     *
     * @return array
     */
    public function getColorPalettes()
    {
        $return = [
            ['selector' => 'colors',   'name' => 'Палитра 1', 'colors' => $GLOBALS['Layers']['colors'],   'iconCls' => '', 'selected' => true],
            ['selector' => 'palette2', 'name' => 'Палитра 2', 'colors' => $GLOBALS['Layers']['palette2'], 'iconCls' => ''],
            ['selector' => 'palette3', 'name' => 'Палитра 3', 'colors' => $GLOBALS['Layers']['palette3'], 'iconCls' => ''],
        ];

        return $return;
    }
}
