<?php

namespace TF\Engine\APIClasses\Owners;

use DateTime;
use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\APIClasses\Common\OwnersInfo;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbOwners\UserDbOwnersController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * дърво "Собственици".
 *
 * @rpc-module Owners
 *
 * @rpc-service-id owners-tree
 */
class OwnersTree extends TRpcApiProvider
{
    public const OWNERS_TREE_LIMIT = 30;
    private $module = 'Owners';
    private $service_id = 'owners-tree';

    /**
     * Registering all the external RPC methods with their respective hooks.
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readOwnersTree'],
                'validators' => [
                    'filterObj' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'addOwner' => ['method' => [$this, 'addOwner'],
                'validators' => [
                    'rpcParams' => [
                        'name' => 'validateText',
                        'surname' => 'validateText',
                        'lastname' => 'validateText',
                        'egn' => 'validateDigitsOnly',
                        'lk_nomer' => 'validateText',
                        'lk_izdavane' => 'validateText',
                        'phone' => 'validateText',
                        'fax' => 'validateText',
                        'mobile' => 'validateText',
                        'email' => 'validateText',
                        'bank_name' => 'validateText',
                        'iban' => 'validateText',
                        'bic' => 'validateText',
                        'address' => 'validateText',
                        'rent_place' => 'validateText',
                        'prepiska' => 'validateText',
                    ],
                ]],
            'addLegalOwner' => ['method' => [$this, 'addOwner'],
                'validators' => [
                    'rpcParams' => [
                        'company_name' => 'validateText',
                        'eik' => 'validateDigitsOnly',
                        'mol' => 'validateText',
                        'company_address' => 'validateText',
                        'phone' => 'validateText',
                        'fax' => 'validateText',
                        'mobile' => 'validateText',
                        'email' => 'validateText',
                        'bank_name' => 'validateText',
                        'iban' => 'validateText',
                        'bic' => 'validateText',
                        'address' => 'validateText',
                        'rent_place' => 'validateText',
                    ],
                ]],
            'editOwner' => ['method' => [$this, 'editOwner'],
                'validators' => [
                    'rpcParams' => [
                        'name' => 'validateText',
                        'surname' => 'validateText',
                        'lastname' => 'validateText',
                        'egn' => 'validateDigitsOnly',
                        'lk_nomer' => 'validateText',
                        'lk_izdavane' => 'validateText',
                        'phone' => 'validateText',
                        'fax' => 'validateText',
                        'mobile' => 'validateText',
                        'email' => 'validateText',
                        'bank_name' => 'validateText',
                        'iban' => 'validateText',
                        'bic' => 'validateText',
                        'address' => 'validateText',
                        'rent_place' => 'validateText',
                    ],
                ]],
            'editLegalOwner' => ['method' => [$this, 'editOwner'],
                'validators' => [
                    'rpcParams' => [
                        'company_name' => 'validateText',
                        'eik' => 'validateDigitsOnly',
                        'mol' => 'validateText',
                        'company_address' => 'validateText',
                        'phone' => 'validateText',
                        'fax' => 'validateText',
                        'mobile' => 'validateText',
                        'email' => 'validateText',
                        'bank_name' => 'validateText',
                        'iban' => 'validateText',
                        'bic' => 'validateText',
                        'address' => 'validateText',
                        'rent_place' => 'validateText',
                    ],
                ]],
            'deleteOwner' => ['method' => [$this, 'deleteOwner'],
                'validators' => [
                    'rpcParams' => 'validateInteger'],
            ],
            'markForEdit' => ['method' => [$this, 'markForEdit'],
                'validators' => [
                    'rpcParams' => 'validateInteger'],
            ],
            'getOwnerParents' => ['method' => [$this, 'getOwnerParents']],
            'getOwnerAlphanbeticalOrderPosition' => ['method' => [$this, 'getOwnerAlphanbeticalOrderPosition']],
        ];
    }

    /**
     * Returns the owners tree entries, based on filter criteria.
     *
     * @api-method read
     *
     * @param array $filterObj An array of filter parameters
     *                         {
     *                         #item string owner_names  - the names of the owner to be matched
     *                         #item string egn          - the EGN of the owner to be matched
     *                         #item string company_name - the company name to be matched
     *                         #item string eik          - the company's EIK to be matched
     *                         }
     * @param int $page current page for pagination
     * @param int $rows the number of rows to be returned
     * @param string $sort the sorting criteria to be used
     * @param string $order the sorting order in which the results to be ordered
     *
     * @return array the matching results array
     */
    public function readOwnersTree(?array $filterObj = null, ?int $page = null, ?int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $filterObj['company_name'] = $filterObj['company_name'] ? preg_replace('/\s+/', ' ', $filterObj['company_name']) : '';
        $filterObj['owner_names'] = $filterObj['owner_names'] ? preg_replace('/\s+/', ' ', $filterObj['owner_names']) : '';

        // init all needed controllers
        $FarmingController = new FarmingController('Farming');
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);

        $arrayHelper = $FarmingController->ArrayHelper;

        $options = [
            'return' => [
                'o.id', "CONCAT(TRIM(o.name), ' ', TRIM(o.surname), ' ', TRIM(o.lastname)) as owner_names", 'TRIM(o.name) as name',
                'TRIM(o.surname) as surname', 'TRIM(o.lastname) as lastname', 'TRIM(o.egn) as egn', 'TRIM(o.lk_nomer) as lk_nomer', 'TRIM(o.lk_izdavane) as lk_izdavane',
                'TRIM(o.company_name) as company_name', 'TRIM(o.eik) as eik', 'TRIM(o.phone) as phone',
                'TRIM(o.fax) as fax', 'TRIM(o.mobile) as mobile', 'TRIM(o.email) as email', 'TRIM(o.address) as address', 'o.owner_type', 'TRIM(o.mol) as mol', 'remark',
                'TRIM(o.company_address) as company_address', 'o.post_payment_fields', 'o.is_dead', 'o.dead_date', 'TRIM(o.iban) as iban', 'TRIM(o.bic) as bic', 'TRIM(o.bank_name) as bank_name', 'TRIM(o.rent_place) as rent_place',
                'o.is_foreigner', 'TRIM(o.prepiska) as prepiska',
                'count(*) OVER() as total_count',
            ],
            'where' => [
                'egn' => ['column' => 'egn', 'prefix' => 'o', 'compare' => 'ILIKE', 'value' => $filterObj['egn']],
                'rep_egn' => ['column' => 'rep_egn', 'prefix' => 'o_r', 'compare' => 'ILIKE', 'value' => $filterObj['rep_egn']],
                'company_name' => ['column' => 'company_name', 'prefix' => 'o', 'compare' => 'ILIKE', 'value' => $filterObj['company_name']],
                'eik' => ['column' => 'eik', 'prefix' => 'o', 'compare' => 'ILIKE', 'value' => $filterObj['eik']],
                'kad_ident' => ['column' => 'kad_ident', 'prefix' => 'kvs', 'compare' => '=', 'value' => $filterObj['kad_ident']],
                'masiv' => ['column' => 'masiv', 'prefix' => 'kvs', 'compare' => '=', 'value' => $filterObj['masiv']],
                'imot' => ['column' => 'number', 'prefix' => 'kvs', 'compare' => '=', 'value' => $filterObj['imot']],
                'ekate' => ['column' => 'ekate', 'prefix' => 'kvs', 'compare' => 'IN', 'value' => $arrayHelper->filterEmptyStringArr($filterObj['ekate'])],
                'nm_usage_rights' => ['column' => 'nm_usage_rights', 'prefix' => 'c', 'compare' => 'IN', 'value' => $arrayHelper->filterEmptyStringArr($filterObj['c_type'])],
                'c_num' => ['column' => 'c_num', 'prefix' => 'c', 'compare' => 'ILIKE', 'value' => $filterObj['c_num']],
                'owner_note' => ['column' => 'remark', 'prefix' => 'o', 'compare' => 'ILIKE', 'value' => $filterObj['owner_note']],
            ],
            'group' => 'o.id',
        ];

        if (isset($filterObj['owner_phone']) && '' != $filterObj['owner_phone']) {
            $options['where_or']['phone'] = ['column' => 'phone', 'compare' => 'ILIKE', 'prefix' => 'o', 'value' => $filterObj['owner_phone']];
            $options['where_or']['mobile'] = ['column' => 'mobile', 'compare' => 'ILIKE', 'prefix' => 'o', 'value' => $filterObj['owner_phone']];
        }

        if (isset($filterObj['owner_ids'])) {
            $filterObj['owner_ids'] = array_diff($filterObj['owner_ids'], ['']);
            $options['where']['owner_id'] = ['column' => 'id', 'prefix' => 'o', 'compare' => 'IN', 'value' => $filterObj['owner_ids']];
        } else {
            $options['where']['owner_id'] = ['column' => 'id', 'prefix' => 'o', 'compare' => '=', 'value' => $filterObj['owner_id']];
        }

        if (isset($filterObj['owner_names']) && '' != $filterObj['owner_names']) {
            $tmp_owner_names = preg_replace('/\s+/', '.*', $filterObj['owner_names']);
            $tmp_owner_names = mb_strtolower($tmp_owner_names, 'UTF-8');
            $options['where']['owner_names'] = ['column' => "lower(TRIM (o.NAME)) || ' ' || lower(TRIM (o.surname)) || ' ' || lower(TRIM (o.lastname))", 'compare' => '~', 'value' => $tmp_owner_names];
        }

        if (isset($filterObj['mol']) && '' != $filterObj['mol']) {
            $tmp_mol = preg_replace('/\s+/', '.*', $filterObj['mol']);
            $tmp_mol = mb_strtolower($tmp_mol, 'UTF-8');
            $options['where']['mol'] = ['column' => 'lower(mol)', 'compare' => '~', 'value' => $tmp_mol];
        }

        if (isset($filterObj['rep_name']) && '' != $filterObj['rep_name']) {
            $tmp_rep_name = preg_replace('/\s+/', '.*', $filterObj['rep_name']);
            $tmp_rep_name = mb_strtolower($tmp_rep_name, 'UTF-8');
            $options['where']['rep_name'] = ['column' => "lower(TRIM (o_r.rep_name)) || ' ' || lower(TRIM (o_r.rep_surname)) || ' ' || lower(TRIM (o_r.rep_lastname))", 'compare' => '~', 'value' => $tmp_rep_name];
        }

        if (isset($filterObj['c_num']) && '' != $filterObj['c_num']) {
            $options['where']['annex_action'] = ['column' => 'annex_action', 'prefix' => 'cpr', 'compare' => '=', 'value' => 'added'];
            $options['where']['is_sublease'] = ['column' => 'is_sublease', 'prefix' => 'c', 'compare' => '=', 'value' => 'false'];
        }

        // adding limit for pagination
        // define page limit
        $page_limit = self::OWNERS_TREE_LIMIT;
        $options['sort'] = "CASE WHEN o.owner_type = 1 THEN TRIM(o.name) || ' ' || TRIM(o.surname) || ' ' || TRIM(o.lastname) ELSE o.company_name END COLLATE \"alpha_numeric_bg\"";
        $options['order'] = 'asc';
        $options['offset'] = ($page - 1) * $page_limit;
        $options['offset'] = $options['offset'] >= 0 ? $options['offset'] : 0;
        $options['limit'] = $page_limit;

        $results = $UserDbOwnersController->getOwnersData($options, false, false);

        if (0 == $results[0]['total_count']) {
            return [
                'rows' => [],
                'total' => 0,
            ];
        }

        if (null == $results[0]['owner_names'] && null == $results[0]['company_name']) {
            return [
                'rows' => [],
                'total' => 0,
            ];
        }
        $resultsCount = count($results);
        for ($i = 0; $i < $resultsCount; $i++) {
            $results[$i]['owner_type_name'] = $GLOBALS['Owners']['Types'][$results[$i]['owner_type']]['type_name'];

            if (!$results[$i]['lk_nomer']) {
                $results[$i]['lk_nomer'] = '-';
            }
            if (!$results[$i]['lk_izdavane']) {
                $results[$i]['lk_izdavane'] = '-';
            }
            if (!$results[$i]['mol']) {
                $results[$i]['mol'] = '-';
            }
            if (!$results[$i]['company_address']) {
                $results[$i]['company_address'] = '-';
            }
            if (!$results[$i]['phone']) {
                $results[$i]['phone'] = '-';
            }
            if (!$results[$i]['fax']) {
                $results[$i]['fax'] = '-';
            }
            if (!$results[$i]['mobile']) {
                $results[$i]['mobile'] = '-';
            }
            if (!$results[$i]['email']) {
                $results[$i]['email'] = '-';
            }
            if (!$results[$i]['iban']) {
                $results[$i]['iban'] = '-';
            }
            if (!$results[$i]['bank_name']) {
                $results[$i]['bank_name'] = '-';
            }
            if (!$results[$i]['bic']) {
                $results[$i]['bic'] = '-';
            }
            if (!$results[$i]['address']) {
                $results[$i]['address'] = '-';
            }

            $postPaymentFields = json_decode($results[$i]['post_payment_fields'], true);
            $result[$i]['post_payment_fields_text'] = implode('<br>', array_values($postPaymentFields));

            switch ($results[$i]['owner_type']) {
                case 0:
                    $results[$i]['text'] = $results[$i]['company_name'];
                    $results[$i]['iconCls'] = 'icon-tree-users';

                    break;
                case 1:
                    $results[$i]['text'] = $results[$i]['owner_names'];
                    $results[$i]['iconCls'] = 'icon-tree-user';

                    break;

                default:
                    break;
            }

            if ($results[$i]['is_dead']) {
                $results[$i]['is_dead_text'] = 'Да';
                if ($results[$i]['dead_date']) {
                    $dateTime = new DateTime();
                    $deadDate = $dateTime->createFromFormat('Y-m-d H:i:s', $results[$i]['dead_date']);
                    $results[$i]['dead_date'] = $deadDate->format('m.d.Y');
                    $results[$i]['dead_date_text'] = $deadDate->format('d.m.Y');
                } else {
                    $results[$i]['dead_date_text'] = '-';
                }
                $results[$i]['iconCls'] = 'icon-tree-user-rip';
            } else {
                $results[$i]['is_dead_text'] = 'Не';
            }
            $results[$i]['is_foreigner'] = $results[$i]['is_foreigner'] ? 'Да' : 'Не';
            $return[] = [
                'text' => $results[$i]['text'],
                'id' => $results[$i]['id'],
                'attributes' => $results[$i],
                'iconCls' => $results[$i]['iconCls'],
            ];
        }

        // add attribute to first listed element of three for custom pagination
        // information for total can not be sent otherwise
        $return[0]['attributes']['pagination']['total'] = $results[0]['total_count'];
        $return[0]['attributes']['pagination']['limit'] = $page_limit;

        return $return;
    }

    /**
     * The method creates new entry in su_owners table.
     *
     * @api-method addOwner
     *
     * @param array $rpcParams - the RPC parameters to fill the entry fields
     *                         {
     *                         #item string name            - the name of the owner (for physical entities)
     *                         #item string surname         - the surname of the owner (for physical entities)
     *                         #item string lastname        - the lastname of the owner (for physical entities)
     *                         #item string egn             - the EGN (personal number) of the owner (for physical entities)
     *                         #item string lk_nomer        - the number of the owner's ID Card (for physical entities)
     *                         #item string lk_izdavane     - the owner's ID Card information (for physical entities)
     *                         #item string company_name    - the name of the company (for legal entities)
     *                         #item string eik             - the company's registration number (for legal entities)
     *                         #item string mol             - the company's legal representative (for legal entities)
     *                         #item string company_address - the company's registration address (for legal entities)
     *                         #item string phone           - the entity's phone number (for both legal and physical entities)
     *                         #item string fax             - the entity's fax number (for both legal and physical entities)
     *                         #item string mobile          - the entity's mobile number (for both legal and physical entities)
     *                         #item string email           - the entity's email address (for both legal and physical entities)
     *                         #item string bank_name       - the entity's bank name
     *                         #item string iban            - the entity's IBAN (bank account number) (for both legal and physical entities)
     *                         #item string bic             - the entity's bank BIC
     *                         #item string address         - the entity's contact address (for both legal and physical entities)
     *                         #item string rent_place      - the entity's location at which to receive the remuneration (for both legal and physical entities)
     *                         #item string prepiska        - the entity's prepiska (for physical entities only)
     *                         }
     *
     * @throws MTRpcException
     *
     * @return int $ownerId     - The unique ID of the newly added Owner
     */
    public function addOwner($rpcParams)
    {
        $UsersController = new UsersController('Users');
        $UserDbController = new UserDbController($this->User->Database);

        $this->checkForExistingOwner($rpcParams);

        $postPaymentFields = json_encode($rpcParams['post_payment_fields']);

        // 1 -> physical; 0 -> legal
        if ($rpcParams['is_legal']) {
            $options = [
                'tablename' => DEFAULT_DB_PREFIX . 'owners',
                'mainData' => [
                    'name' => '',
                    'surname' => '',
                    'lastname' => '',
                    'egn' => '',
                    'lk_nomer' => '',
                    'lk_izdavane' => '',
                    'company_name' => trim($rpcParams['company_name']),
                    'eik' => trim($rpcParams['eik']),
                    'mol' => trim($rpcParams['mol']),
                    'company_address' => trim($rpcParams['company_address']),
                    'phone' => trim($rpcParams['phone']),
                    'fax' => trim($rpcParams['fax']),
                    'mobile' => trim($rpcParams['mobile']),
                    'email' => trim($rpcParams['email']),
                    'bank_name' => trim($rpcParams['bank_name']),
                    'iban' => trim($rpcParams['iban']),
                    'bic' => trim($rpcParams['bic']),
                    'address' => trim($rpcParams['address']),
                    'country' => trim($rpcParams['country']),
                    'post_payment_fields' => $postPaymentFields,
                    'remark' => trim($rpcParams['remark']),
                    'rent_place' => trim($rpcParams['rent_place']),
                    'owner_type' => 0,
                    'is_dead' => 'FALSE',
                    'dead_date' => null,
                ],
            ];
        } else {
            OwnersInfo::validateDeadDate($rpcParams['dead_date']);

            $options = [
                'tablename' => DEFAULT_DB_PREFIX . 'owners',
                'mainData' => [
                    'name' => trim($rpcParams['name']),
                    'surname' => trim($rpcParams['surname']),
                    'lastname' => trim($rpcParams['lastname']),
                    'egn' => trim($rpcParams['egn']),
                    'lk_nomer' => trim($rpcParams['lk_nomer']),
                    'lk_izdavane' => trim($rpcParams['lk_izdavane']),
                    'birthday' => ($rpcParams['is_foreigner']) ? $rpcParams['birthday'] : null,
                    'eik' => '',
                    'mol' => '',
                    'company_address' => '',
                    'phone' => trim($rpcParams['phone']),
                    'fax' => trim($rpcParams['fax']),
                    'mobile' => trim($rpcParams['mobile']),
                    'email' => trim($rpcParams['email']),
                    'bank_name' => trim($rpcParams['bank_name']),
                    'iban' => trim($rpcParams['iban']),
                    'bic' => trim($rpcParams['bic']),
                    'address' => trim($rpcParams['address']),
                    'country' => trim($rpcParams['country']),
                    'post_payment_fields' => $postPaymentFields,
                    'remark' => trim($rpcParams['remark']),
                    'owner_type' => 1,
                    'rent_place' => trim($rpcParams['rent_place']),
                    'is_dead' => ($rpcParams['is_dead']) ? 'TRUE' : 'FALSE',
                    'is_foreigner' => ($rpcParams['is_foreigner']) ? 'TRUE' : 'FALSE',
                    'prepiska' => ($rpcParams['is_dead']) ? $rpcParams['prepiska'] : null,
                    'dead_date' => ($rpcParams['is_dead']) ? $rpcParams['dead_date'] : null,
                ],
            ];
        }
        $recordID = $UserDbController->addItem($options);

        $addedRecordPage = ceil($this->getOwnerAlphanbeticalOrderPosition($recordID) / self::OWNERS_TREE_LIMIT);
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['request_params' => $rpcParams], ['created_owner_id' => $recordID], 'Adding owner');

        return [$recordID, $addedRecordPage];
    }

    /**
     * The method creates new entry in su_owners table.
     *
     * @api-method editOwner
     *
     * @param array $rpcParams - the RPC parameters to fill the entry fields
     *                         {
     *                         #item string name            - the name of the owner (for physical entities)
     *                         #item string surname         - the surname of the owner (for physical entities)
     *                         #item string lastname        - the lastname of the owner (for physical entities)
     *                         #item string egn             - the EGN (personal number) of the owner (for physical entities)
     *                         #item string lk_nomer        - the number of the owner's ID Card (for physical entities)
     *                         #item string lk_izdavane     - the owner's ID Card information (for physical entities)
     *                         #item string company_name    - the name of the company (for legal entities)
     *                         #item string eik             - the company's registration number (for legal entities)
     *                         #item string mol             - the company's legal representative (for legal entities)
     *                         #item string company_address - the company's registration address (for legal entities)
     *                         #item string phone           - the entity's phone number (for both legal and physical entities)
     *                         #item string fax             - the entity's fax number (for both legal and physical entities)
     *                         #item string mobile          - the entity's mobile number (for both legal and physical entities)
     *                         #item string email           - the entity's email address (for both legal and physical entities)
     *                         #item string bank_name       - the entity's bank name
     *                         #item string iban            - the entity's IBAN (bank account number) (for both legal and physical entities)
     *                         #item string bic             - the entity's bank BIC
     *                         #item string address         - the entity's contact address (for both legal and physical entities)
     *                         #item string rent_place      - the entity's location at which to receive the remuneration (for both legal and physical entities)
     *                         #item string prepiska        - the entity's prepiska (for physical entities only)
     *                         }
     * @param int $recordID - The unique ID of the edited Owner
     *
     * @throws MTRpcException
     *
     * @return int $recordID    - The unique ID of the edited Owner
     */
    public function editOwner($rpcParams, $recordID)
    {
        $UsersController = new UsersController('Users');
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);

        $this->checkIfOwnerExists($recordID);
        $this->checkIfEditIsPermitted($recordID, $rpcParams['is_dead']);

        if (!empty($rpcParams['egn'])) {
            $this->checkForExistingOwnerBeforeEdit($rpcParams, $recordID);
        }

        // Getting the old values for log
        $options = [
            'tablename' => $UserDbController->DbHandler->tableOwners,
            'return' => [
                '*',
            ],
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $recordID],
            ],
        ];
        $owner = $UserDbController->getItemsByParams($options);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableOwnersReps,
            'return' => [
                '*',
            ],
            'where' => [
                'id' => ['column' => 'owner_id', 'compare' => '=', 'value' => $recordID],
            ],
        ];
        $rep = $UserDbController->getItemsByParams($options);

        $postPaymentFields = json_encode($rpcParams['post_payment_fields']);

        // 1 -> physical; 0 -> legal
        if ($rpcParams['is_legal']) {
            $options = [
                'tablename' => DEFAULT_DB_PREFIX . 'owners',
                'mainData' => [
                    'name' => '',
                    'surname' => '',
                    'lastname' => '',
                    'egn' => '',
                    'lk_nomer' => '',
                    'lk_izdavane' => '',
                    'company_name' => trim($rpcParams['company_name']),
                    'eik' => trim($rpcParams['eik']),
                    'mol' => trim($rpcParams['mol']),
                    'company_address' => trim($rpcParams['company_address']),
                    'phone' => trim($rpcParams['phone']),
                    'fax' => trim($rpcParams['fax']),
                    'mobile' => trim($rpcParams['mobile']),
                    'email' => trim($rpcParams['email']),
                    'bank_name' => trim($rpcParams['bank_name']),
                    'iban' => trim($rpcParams['iban']),
                    'bic' => trim($rpcParams['bic']),
                    'address' => trim($rpcParams['address']),
                    'country' => trim($rpcParams['country']),
                    'post_payment_fields' => $postPaymentFields,
                    'remark' => trim($rpcParams['remark']),
                    'rent_place' => trim($rpcParams['rent_place']),
                    'owner_type' => 0,
                    'is_dead' => 'FALSE',
                    'dead_date' => null,
                ],
            ];
        } else {
            OwnersInfo::validateDeadDate($rpcParams['dead_date'], $owner[0]['dead_date'], $owner[0]['id']);

            if ($rpcParams['is_dead']) {
                $UserDbOwnersController->hasPersonalUse($recordID, $rpcParams['dead_date']);
            }

            $options = [
                'tablename' => DEFAULT_DB_PREFIX . 'owners',
                'mainData' => [
                    'name' => trim($rpcParams['name']),
                    'surname' => trim($rpcParams['surname']),
                    'lastname' => trim($rpcParams['lastname']),
                    'egn' => trim($rpcParams['egn']),
                    'lk_nomer' => trim($rpcParams['lk_nomer']),
                    'lk_izdavane' => trim($rpcParams['lk_izdavane']),
                    'birthday' => ($rpcParams['is_foreigner']) ? $rpcParams['birthday'] : null,
                    'company_name' => '',
                    'eik' => '',
                    'mol' => '',
                    'company_address' => '',
                    'phone' => trim($rpcParams['phone']),
                    'fax' => trim($rpcParams['fax']),
                    'mobile' => trim($rpcParams['mobile']),
                    'email' => trim($rpcParams['email']),
                    'bank_name' => trim($rpcParams['bank_name']),
                    'iban' => trim($rpcParams['iban']),
                    'bic' => trim($rpcParams['bic']),
                    'address' => trim($rpcParams['address']),
                    'country' => trim($rpcParams['country']),
                    'post_payment_fields' => $postPaymentFields,
                    'remark' => trim($rpcParams['remark']),
                    'owner_type' => 1,
                    'rent_place' => trim($rpcParams['rent_place']),
                    'is_dead' => ($rpcParams['is_dead']) ? true : false,
                    'is_foreigner' => ($rpcParams['is_foreigner']) ? true : false,
                    'prepiska' => ($rpcParams['is_dead']) ? $rpcParams['prepiska'] : null,
                    'dead_date' => ($rpcParams['is_dead']) ? $rpcParams['dead_date'] : null,
                ],
            ];
        }
        $options['where'] = ['id' => $recordID];
        $UserDbController->editItem($options);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['new_values' => $options], ['old_values' => $owner[0]], 'Editing owner');

        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);
        if ($rpcParams['is_dead']) {
            $UserDbOwnersController->changeContractOwnerSignedDoc(['owner_id' => $recordID], null);
        }

        // when owner is self rep edit owner record in owners_reps table
        $options = [
            'tablename' => DEFAULT_DB_PREFIX . 'owners_reps',
            'mainData' => [
                'rep_name' => trim($rpcParams['name']),
                'rep_surname' => trim($rpcParams['surname']),
                'rep_lastname' => trim($rpcParams['lastname']),
                'rep_egn' => trim($rpcParams['egn']),
                'rep_lk' => trim($rpcParams['lk_nomer']),
                'rep_lk_izdavane' => trim($rpcParams['lk_izdavane']),
                'rep_address' => trim($rpcParams['address']),
                'rent_place' => trim($rpcParams['rent_place']),
                'bank_name' => trim($rpcParams['bank_name']),
                'iban' => trim($rpcParams['iban']),
                'post_payment_fields' => $postPaymentFields,
                'bic' => trim($rpcParams['bic']),
            ],
            'where' => ['owner_id' => $recordID],
        ];

        $UserDbController->editItem($options);
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['new_values' => $options], ['old_values' => $rep[0]], 'Editing owner rep');

        return $recordID;
    }

    /**
     * The method deletes the owner with ID = $rpcParams from the DB and all of
     * it's associated heritors from the respective DB Tables.
     *
     * @api-method deleteOwner
     *
     * @param int $rpcParams the unique owner ID to be deleted
     */
    public function deleteOwner($rpcParams)
    {
        $UsersController = new UsersController('Users');
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);
        $owner_id = $rpcParams;

        $ownerContractsOptions = [
            'return' => [
                'c.id as contract_id',
                'c.is_annex as is_annex',
                'c.c_num',
                "to_char(c.start_date, 'dd-mm-YYYY') as start_date",
                "to_char(c.due_date, 'dd-mm-YYYY') as due_date",
                "(case when c.is_sublease = true then 'Преотдаване' else c.virtual_contract_type end) as contract_type",
                'c.is_sublease',
            ],
            'where' => [
                'id' => ['column' => 'id', 'prefix' => 'o', 'compare' => '=', 'value' => $owner_id],
            ],
            'group' => 'c.id',
        ];

        // Check if owner partisipate in contracts
        $UserDbOwnersController->hasOwnerContracts($ownerContractsOptions);

        // Getting the old values for log
        $options = [
            'tablename' => $UserDbController->DbHandler->tableOwners,
            'return' => [
                '*',
            ],
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $owner_id],
            ],
        ];
        $owner = $UserDbController->getItemsByParams($options);

        $UserDbOwnersController->deleteOwners($owner_id);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['deleted_owner_id' => $owner_id], ['old_values' => $owner[0]], 'Deleting owner');

        // delete all data for deleted owner from table su_heritors
        $options = [
            'tablename' => $UserDbController->DbHandler->tableHeritors,
            'return' => [
                'id',
            ],
            'where' => [
                'path' => ['column' => 'path', 'compare' => '~', 'value' => '*.' . $owner_id . '.*'],
            ],
        ];
        $results = $UserDbController->getItemsByParams($options);
        $resultsCount = count($results);
        if (0 == $resultsCount) {
            return [];
        }
        $id_array = [];
        for ($i = 0; $i < $resultsCount; $i++) {
            $id_array[] = $results[$i]['id'];
        }
        $id_string = implode(',', $id_array);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableHeritors,
            'id_name' => 'id',
            'id_string' => $id_string,
            'where' => [
                'path' => ['column' => 'path', 'compare' => '~', 'value' => '*.' . $owner_id . '.*'],
            ],
        ];

        $UserDbController->deleteItemsByParams($options);
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['deleted_owner_path' => '*.' . $owner_id . '.*'], ['id_string' => $id_string], 'Deleting owner reps');
    }

    /**
     * Gets all the data, associated with current owner.
     *
     * @api-method markForEdit
     *
     * @param int $rpcParam - unique owner ID
     *
     * @return array - associative array with all owner data
     */
    public function markForEdit($rpcParam)
    {
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);

        $options = [
            'return' => [
                'o.id', 'TRIM(o.name) as name', 'TRIM(o.surname) as surname', 'TRIM(o.lastname) as lastname', 'TRIM(o.egn) as egn', 'TRIM(o.lk_nomer) as lk_nomer', 'TRIM(o.lk_izdavane) as lk_izdavane',
                'TRIM(o.company_name) as company_name', 'TRIM(o.eik) as eik', 'TRIM(o.phone) as phone', 'o.country', 'to_char(o.birthday, \'YYYY-mm-dd\')  as birthday',
                'TRIM(o.fax) as fax', 'TRIM(o.mobile) as mobile', 'TRIM(o.email) as email', 'TRIM(o.address) as address', 'o.owner_type', 'TRIM(o.mol) as mol', 'remark',
                'TRIM(o.company_address) as company_address',
                'o.is_dead',
                "CASE WHEN o.dead_date IS NULL THEN null ELSE to_char(o.dead_date, 'YYYY-mm-dd') END as dead_date",
                'o.post_payment_fields::text',
                'TRIM(o.iban) as iban', 'TRIM(o.bic) as bic', 'TRIM(o.bank_name) as bank_name', 'TRIM(o.rent_place) as rent_place', 'TRIM(o.prepiska) as prepiska',
                'o.is_foreigner'],

            'where' => [
                'id' => ['column' => 'id', 'prefix' => 'o', 'compare' => '=', 'value' => $rpcParam],
            ],
        ];
        $result = $UserDbOwnersController->getOwnersData($options, false, false);

        return $result[0];
    }

    /**
     * Информация дали е наследник избрания собственик и ако да на кого.
     *
     * @api-method getOwnerParents
     *
     * @param int $rpcParam - owner id
     *
     * @throws MTRpcException OWNER_IS_NOT_HERITOR -33302
     *
     * @return array
     */
    public function getOwnerParents($rpcParam)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $options = [
            'tablename' => $UserDbController->DbHandler->tableHeritors,
            'return' => [
                'subpath(path, 0, -1) as path',
            ],
            'where' => [
                'path' => ['column' => 'path', 'compare' => '~', 'value' => '*.' . $rpcParam],
            ],
        ];
        $results = $UserDbController->getItemsByParams($options);
        $resultsCount = count($results);
        if (0 == $resultsCount) {
            throw new MTRpcException('OWNER_IS_NOT_HERITOR', -33302);
        }

        for ($i = 0; $i < $resultsCount; $i++) {
            $allPaths[] = $results[$i]['path'];
        }

        usort($allPaths, function ($a, $b) {
            return strlen($a) - strlen($b);
        });
        $uniquePaths = $allPaths;
        $pathCount = count($allPaths);
        for ($i = 0; $i < $pathCount; $i++) {
            for ($j = $i + 1; $j < $pathCount; $j++) {
                if (strstr($allPaths[$j], $allPaths[$i]) && $j != $i) {
                    unset($uniquePaths[$i]);

                    break;
                }
            }
        }
        $uniquePaths = array_merge($uniquePaths);
        $parentRootsArray = [];
        $uniqueCount = count($uniquePaths);
        for ($i = 0; $i < $uniqueCount; $i++) {
            $parents = explode('.', $uniquePaths[$i]);
            $parentRootsArray[] = $parents[0];
        }

        return $parentRootsArray;
    }

    public function getOwnerAlphanbeticalOrderPosition($onwerId)
    {
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);
        $data = $UserDbOwnersController->getAllOwnersSortedAlphabetically();

        return array_search($onwerId, array_column($data, 'id'));
    }

    private function checkIfOwnerExists($recordID)
    {
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);

        $options = [
            'return' => [
                'o.*',
            ],
            'where' => [
                'id' => ['column' => 'id', 'prefix' => 'o', 'compare' => '=', 'value' => $recordID],
            ],
        ];
        $counter = $UserDbOwnersController->getOwnersData($options, true, false);

        if (0 == $counter[0]['count']) {
            throw new MTRpcException('NON_EXISTING_OWNER_ID', -33206);
        }
    }

    private function checkIfEditIsPermitted($ownerId, $isDead)
    {
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableOwners,
            'return' => [
                '*',
            ],
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $ownerId],
            ],
        ];
        $owner = $UserDbController->getItemsByParams($options);
        $options = [
            'return' => [
                'h.id',
            ],
            'where' => [
                'path' => ['column' => 'path', 'compare' => '~', 'value' => $ownerId . '.*'],
            ],
        ];

        $counter = $UserDbOwnersController->getOwnersHeritors($options, true);

        if (0 == $counter[0]['count']) {
            return true;
        }

        if ($counter[0]['count'] > 0 && $owner[0]['is_dead'] && !$isDead) {
            throw new MTRpcException('ILLEGAL_OWNER_EDIT', -33304);
        }

        return false;
    }

    private function checkForExistingOwner($rpcParams)
    {
        if ($rpcParams['is_dead'] || $rpcParams['is_foreigner']) {
            return false;
        }
        $UserDbController = new UserDbController($this->User->Database);

        $ownerType = $rpcParams['is_legal'] ? 0 : 1;
        $column = $rpcParams['is_legal'] ? 'eik' : 'egn';
        $egn_eik = $rpcParams['is_legal'] ? $rpcParams['eik'] : $rpcParams['egn'];
        $options = [
            'tablename' => $UserDbController->DbHandler->tableOwners,
            'where' => [
                'owner_type' => ['column' => 'owner_type', 'compare' => '=', 'value' => $ownerType],
                'egn_eik' => ['column' => $column, 'compare' => '=', 'value' => $egn_eik],
            ],
        ];

        $results = $UserDbController->getItemsByParams($options);

        if (count($results)) {
            throw new MTRpcException('OWNER_ALREADY_EXISTS', -33310);
        }

        return false;
    }

    private function checkForExistingOwnerBeforeEdit($rpcParams, $recordID)
    {
        $UserDbController = new UserDbController($this->User->Database);

        $ownerType = $rpcParams['is_legal'] ? 0 : 1;
        $column = $rpcParams['is_legal'] ? 'eik' : 'egn';
        $egn_eik = $rpcParams['is_legal'] ? $rpcParams['eik'] : $rpcParams['egn'];
        $options = [
            'tablename' => $UserDbController->DbHandler->tableOwners,
            'where' => [
                'owner_type' => ['column' => 'owner_type', 'compare' => '=', 'value' => $ownerType],
                'egn_eik' => ['column' => $column, 'compare' => '=', 'value' => $egn_eik],
                'id' => ['column' => 'id', 'compare' => '<>', 'value' => $recordID],
            ],
        ];

        $results = $UserDbController->getItemsByParams($options);

        if (count($results)) {
            throw new MTRpcException('OWNER_ALREADY_EXISTS', -33310);
        }

        return false;
    }
}
