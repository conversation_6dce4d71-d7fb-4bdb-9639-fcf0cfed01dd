<?php

namespace TF\Engine\APIClasses\Owners;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDbOwners\UserDbOwnersController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * дърво "Собственици".
 *
 * @rpc-module Owners
 *
 * @rpc-service-id representatives-plots-grid
 */
class RepresentativesPlotsGrid extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readRepresentativesPlotsGrid'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * Рeads Owners Plots Grid - Собствени имоти.
     *
     * @api-method read
     *
     * @param array $rpcParams {
     *                         #item  integer $rep_id
     *                         #item  string $year
     *                         #item  array $ekate
     *                         #item  array $farm
     *                         }
     * @param int $page
     * @param int $rows
     * @param ?string $sort
     * @param ?string $order
     *
     * @return array {
     *               #item array {
     *               #item string c_date
     *               #item string c_num
     *               #item integer contract_id
     *               #item string due_date
     *               #item string ekate
     *               #item string farm_name
     *               #item integer farming_id
     *               #item integer gid
     *               #item integer id
     *               #item string kad_ident
     *               #item string land
     *               #item string owner
     *               #item string start_date
     *               #item string due_date
     *               }
     *               }
     */
    public function readRepresentativesPlotsGrid(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $repId = $rpcParams['rep_id'];
        $ekate = $rpcParams['ekate'];
        $farm = $rpcParams['farm'];

        if (!$repId) {
            return [
                'rows' => [],
                'total' => 0,
            ];
        }

        switch ($sort) {
            case 'land':
                $sort = 'o_r.rent_place';

                break;
            case 'owner_type':
                $sort = 'o.owner_type';

                break;
            case 'owner':
                $sort = "(CASE WHEN o.owner_type = 1 THEN TRIM(o.name) || ' ' || TRIM(o.surname) || ' ' || TRIM(o.lastname) ELSE o.company_name END) COLLATE \"alpha_numeric_bg\"";

                break;
            case 'kad_ident':
                $sort = 'kad_ident COLLATE "alpha_numeric_bg"';

                break;
            case 'farm_name':
                $sort = 'c.farming_id';

                break;
        }
        date_default_timezone_set('Europe/Sofia');
        $currentDate = date('Y-m-d', time());

        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);
        $UsersController = new UsersController('Users');
        $FarmingController = new FarmingController('Farming');
        $arrayHelper = $FarmingController->ArrayHelper;

        $userFarmings = $FarmingController->getUserFarmings();
        $userFarmingIds = array_keys($userFarmings);
        $farmingIds = $arrayHelper->filterEmptyStringArr($farm);
        $farmingIds = isset($farmingIds) && count($farmingIds) > 0
            ? array_intersect($farmingIds, $userFarmingIds)
            : [...$userFarmingIds, null];

        $options = [
            'return' => [
                'kad_ident', "(CASE WHEN o.owner_type = 1 THEN TRIM(o.name) || ' ' || TRIM(o.surname) || ' ' || TRIM(o.lastname) ELSE o.company_name END) as owner", 'c.farming_id',
                'c.c_num', 'c.c_date', 'c.start_date', 'c.due_date', 'c.nm_usage_rights', 'c.active', 'kvs.ekate',
                'c.id as c_id', 'o.id as o_id', 'kvs.gid as gid', 'owner_type',
                'count(*) OVER () as total_count',
            ],
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
            'where' => [
                'rep_id' => ['column' => 'rep_id', 'compare' => '=', 'prefix' => 'por', 'value' => $repId],
                'c_type' => ['column' => 'nm_usage_rights', 'compare' => '<>', 'prefix' => 'c', 'value' => 1],
                'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
                'ekate' => ['column' => 'ekate', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $arrayHelper->filterEmptyStringArr($ekate)],
                'farming' => ['column' => 'farming_id', 'compare' => 'IN', 'prefix' => 'c', 'value' => $farmingIds],
            ],
            'group' => 'por.id, kvs.gid, o.id, c.id',
        ];

        $results = $UserDbOwnersController->getRepresentativePlots($options, false, false);
        $resultCount = count($results);
        if ($results[0]['total_count']) {
            $farminOptions = [
                'return' => [
                    'id',
                    'name',
                ],
                'where' => [
                    'group_id' => ['column' => 'group_id', 'compare' => '=', 'value' => $this->User->GroupID],
                ],
            ];
            $farmingResult = $FarmingController->getFarmings($farminOptions);
            $farmingCount = count($farmingResult);
            $ekateOptions = [
                'tablename' => 'ekate_combobox',
            ];
            $ekateData = $UserDbOwnersController->getItemsByParams($ekateOptions);
            $ekateNames = [];
            $ekateCount = count($ekateData);
            for ($i = 0; $i < $ekateCount; $i++) {
                $ekateNames[$ekateData[$i]['ekate']] = $ekateData[$i]['ekatte_name'];
            }

            for ($i = 0; $i < $resultCount; $i++) {
                for ($j = 0; $j < $farmingCount; $j++) {
                    if ($farmingResult[$j]['id'] == $results[$i]['farming_id']) {
                        $results[$i]['farm_name'] = $farmingResult[$j]['name'];

                        break;
                    }
                }

                $results[$i]['land'] = $ekateNames[$results[$i]['ekate']];
                $results[$i]['owner_type'] = $results[$i]['owner_type'] ? 'ФЛ' : 'ЮЛ';
                $results[$i]['c_date'] = strftime('%d.%m.%Y', strtotime($results[$i]['c_date']));
                $results[$i]['start_date'] = strftime('%d.%m.%Y', strtotime($results[$i]['start_date']));
                $results[$i]['nm_usage_rights'] = $GLOBALS['Contracts']['ContractTypes'][$results[$i]['nm_usage_rights']]['name'];

                if ($results[$i]['active']) {
                    $results[$i]['active_text'] = (!$results[$i]['due_date'] || $results[$i]['due_date'] >= $currentDate) ? 'Действащ' : 'Изтекъл';
                } else {
                    $results[$i]['active_text'] = 'Анулиран';
                }

                if (null != $results[$i]['due_date'] && '0000-00-00 00:00:00' != $results[$i]['due_date']) {
                    $results[$i]['due_date'] = strftime('%d.%m.%Y', strtotime($results[$i]['due_date']));
                } else {
                    $results[$i]['due_date'] = '-';
                }
            }

            return [
                'rows' => $results,
                'total' => $results[0]['total_count'],
            ];
        }

        return [
            'rows' => [],
            'total' => 0,
        ];
    }
}
