<?php

namespace TF\Engine\APIClasses\Owners;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbOwners\UserDbOwnersController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Грид "Документи за собственост".
 *
 * @rpc-module Owners
 *
 * @rpc-service-id owners-documents
 */
class OwnersDocuments extends TRpcApiProvider
{
    private $module = 'Owners';
    private $service_id = 'owners-documents';

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readOwnersDocuments'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'saveOwnerDocument' => ['method' => [$this, 'saveOwnerDocument']],
            'deleteOwnerDocument' => ['method' => [$this, 'deleteOwnerDocument']],
            'downloadAttachment' => ['method' => [$this, 'downloadAttachment'],
                'validators' => [
                    'document_id' => 'validateRequired, validateInteger',
                ],
            ],
            'deleteAttachment' => ['method' => [$this, 'deleteAttachment'],
                'validators' => [
                    'document_id' => 'validateRequired, validateInteger',
                ],
            ],
        ];
    }

    /**
     * Save Owner Document.
     *
     * @api-method saveOwnerDocument
     *
     * @param array $param {
     *                     #item int id
     *                     #item int type_id
     *                     #item int owner_id
     *                     #item int number
     *                     #item string date
     *                     #item int plot_id
     *                     }
     *
     * @return null|int
     */
    public function saveOwnerDocument($param)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);
        $UsersController = new UsersController('Users');

        $options = [
            'tablename' => $UserDbController->DbHandler->tableOwnersDocuments,
            'mainData' => [
                'type_id' => $param['type_id'],
                'number' => $param['number'],
                'date' => $param['date'],
                'owner_id' => $param['owner_id'],
                'plot_id' => $param['plot_id'],
            ],
        ];

        if ($param['id']) {
            $old_options = [
                'tablename' => $UserDbController->DbHandler->tableOwnersDocuments,
                'return' => [
                    '*',
                ],
                'where' => [
                    'id' => ['column' => 'id', 'compare' => '=', 'value' => $param['id']],
                ],
            ];

            $old_doc = $UserDbController->getItemsByParams($old_options, false, false);

            $options['where'] = ['id' => $param['id']];
            $UserDbController->editItem($options);

            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['new_values' => $options], ['old_values' => $old_doc[0]], 'Editing owner document');
        } else {
            $item_id = $UserDbController->addItem($options);

            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['new_values' => $options], ['newly_created_doc_ic' => $item_id], 'Adding owner document');

            return $item_id;
        }
    }

    /**
     * Delete Owner Document.
     *
     * @api-method deleteOwnerDocument
     *
     * @param int $id
     */
    public function deleteOwnerDocument($id)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);

        $old_options = [
            'tablename' => $UserDbOwnersController->DbHandler->tableOwnersDocuments,
            'return' => [
                '*',
            ],
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $id],
            ],
        ];

        $old_doc = $UserDbOwnersController->getItemsByParams($old_options, false, false);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableOwnersDocuments,
            'id_string' => $id,
        ];

        $UserDbOwnersController->deleteItemsByParams($options);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['deleted_doc_id' => $id], ['deleted_doc_info' => $old_doc[0]], 'Deleting owner document');
    }

    /**
     * Read Owners Documents.
     *
     * @api-method read
     *
     * @param array $rpcParams {
     *                         #item integer owner_id
     *                         #item integer plot_id
     *                         #item string type grid|combobox
     *                         }
     * @param int|string $page
     * @param int|string $rows
     * @param ?string $sort
     * @param ?string $order
     *
     * @return array
     */
    public function readOwnersDocuments(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);

        $options = [
            'return' => [
                'od.id', 'od.type_id', 'od.number', 'od.date', 'od.owner_id', 'kvs.kad_ident', 'od.plot_id', 'attachment',
            ],
            'sort' => $sort,
            'order' => $order,
            'where' => [
                'name' => ['column' => 'owner_id', 'compare' => '=', 'value' => $rpcParams['owner_id']],
                'plot_id' => ['column' => 'plot_id', 'compare' => '=', 'value' => $rpcParams['plot_id']],
            ],
        ];
        $counter = $UserDbOwnersController->getDocumentPlotData($options, true, false);

        // define default return
        $return = [
            'rows' => [],
            'total' => 0,
        ];

        if (0 == $counter[0]['count']) {
            return $return;
        }

        $results = $UserDbOwnersController->getDocumentPlotData($options, false, false);
        $resultsCount = count($results);
        for ($i = 0; $i < $resultsCount; $i++) {
            $pointer = $results[$i]['type_id'];
            $results[$i]['name'] = $GLOBALS['Contracts']['DSTypes'][$pointer]['name'];
            $results[$i]['date'] = substr($results[$i]['date'], 0, 10);
        }
        if ('grid' == $rpcParams['type']) {
            $data['rows'] = $results;
            $data['total'] = $counter[0]['count'];

            return $data;
        }
        if ('combobox' == $rpcParams['type']) {
            $data = [];
            for ($i = 0; $i < $resultsCount; $i++) {
                $data[] = [
                    'id' => $results[$i]['id'],
                    'text' => $results[$i]['name'] . ' № ' . $results[$i]['number'] . ' / ' . $results[$i]['date'],
                ];
            }

            return $data;
        }

        return $return;
    }

    /**
     * Returns the link for the requested file.
     *
     * @api-method downloadAttachment
     *
     * @param int $rpcParam
     *
     * @throws MTRpcException -33305 - FILE_DOES_NOT_EXIST if file is not on the server
     *
     * @return array|string
     */
    public function downloadAttachment($rpcParam)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $options = [
            'tablename' => $UserDbController->DbHandler->tableOwnersDocuments,
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $rpcParam],
            ],
        ];

        $results = $UserDbController->getItemsByParams($options, false, false);

        if (!count($results)) {
            return [];
        }

        $document = $results[0];
        $fileFragment = $this->User->GroupID . '/' . $document['id'] . '_' . $document['attachment'];
        $filePath = OWNER_DOCUMENT_FILES . $fileFragment;

        if (file_exists($filePath)) {
            return 'files/owners_documents_files/' . $fileFragment;
        }

        throw new MTRpcException('FILE_DOES_NOT_EXIST', -33305);
    }

    /**
     * Returns the link for the requested file.
     *
     * @api-method deleteAttachment
     *
     * @param int $rpcParam
     *
     * @return array
     */
    public function deleteAttachment($rpcParam)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        $options = [
            'tablename' => $UserDbController->DbHandler->tableOwnersDocuments,
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $rpcParam],
            ],
        ];

        $results = $UserDbController->getItemsByParams($options, false, false);

        if (!count($results)) {
            return [];
        }

        $options = [
            'tablename' => $UserDbController->DbHandler->tableOwnersDocuments,
            'mainData' => [
                'attachment' => null,
            ],
            'where' => [
                'id' => $rpcParam,
            ],
        ];

        $UserDbController->editItem($options);

        $fileFragment = $this->User->GroupID . '/' . $results[0]['id'] . '_' . $results[0]['attachment'];
        $filePath = OWNER_DOCUMENT_FILES . $fileFragment;

        @unlink($filePath);
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['delete_doc_owner_attachment' => $rpcParam], ['deleted_owner_attachment_info' => $results[0]], 'Deleting owner document attachment');
    }
}
