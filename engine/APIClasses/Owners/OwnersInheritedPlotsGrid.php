<?php

namespace TF\Engine\APIClasses\Owners;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbOwners\UserDbOwnersController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Inherited plots.
 *
 * @rpc-module Owners
 *
 * @rpc-service-id owners-inherited-plots-grid
 */
class OwnersInheritedPlotsGrid extends TRpcApiProvider
{
    public $relation_id;
    public $percent = [];

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readOwnersInheritedPlotsGrid'],
                'validators' => [
                    'ownerId' => 'validateInteger',
                    '$year' => 'validateString',
                    '$ekate' => 'validateArray',
                    '$farm' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * Reads Owners Inherited Plots Grid - Наследени имоти.
     *
     * @api-method read
     *
     * @param int|string $page
     * @param int|string $rows
     * @param ?string $sort
     * @param ?string $order
     *
     * @return array {
     *               #item integer total
     *               #item array footer {
     *               #item string kad_ident
     *               #item string ownage
     *               }
     *               #item array rows {
     *               #item string c_date
     *               #item string c_num
     *               #item string category
     *               #item integer contract_id
     *               #item string due_date
     *               #item string ekate
     *               #item string farm_name
     *               #item integer farming_id
     *               #item integer gid
     *               #item integer id
     *               #item string kad_ident
     *               #item string land
     *               #item string ntp
     *               #item string ownage
     *               #item string owner
     *               #item integer owner_id
     *               #item float percent
     *               #item string start_date
     *               }
     *               }
     */
    public function readOwnersInheritedPlotsGrid(int $ownerId, string $year, array $ekate, array $farm, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);

        $UsersController = new UsersController('Users');
        $FarmingController = new FarmingController('Farming');
        $arrayHelper = $FarmingController->ArrayHelper;

        $userFarmings = $FarmingController->getUserFarmings();
        $userFarmingIds = array_keys($userFarmings);
        $farmingIds = $arrayHelper->filterEmptyStringArr($farm);
        $farmingIds = isset($farmingIds) && count($farmingIds) > 0
            ? array_intersect($farmingIds, $userFarmingIds)
            : $userFarmingIds;

        $return = [
            'rows' => [],
            'total' => 0,
            'footer' => [
                [
                    'kad_ident' => '<b>ОБЩО</b>',
                    'ownage' => '',
                ],
            ],
        ];

        $options = [
            'tablename' => $UserDbController->DbHandler->tableHeritors,
            'return' => [
                'subpath(path, 0, -1) as path',
            ],
            'where' => [
                'path' => ['column' => 'path', 'compare' => '~', 'value' => '*.' . $ownerId],
            ],
        ];
        $results = $UserDbController->getItemsByParams($options);
        $resultsCount = count($results);
        $parents = [];
        for ($i = 0; $i < $resultsCount; $i++) {
            $parents = array_merge($parents, explode('.', $results[$i]['path']));
        }

        $parents = array_merge(array_unique($parents));

        if (0 == count($parents)) {
            return $return;
        }

        if ('' == $year) {
            $start_date = '9999-09-30';
            $due_date = '1970-10-01';
        } else {
            $start_date = $GLOBALS['Farming']['years'][$year]['year'] . '-09-30';
            $due_date = ($GLOBALS['Farming']['years'][$year]['year'] - 1) . '-10-01';
        }

        $options = [
            'custom_counter' => 'COUNT(DISTINCT(r.id))',
            'return' => [
                'DISTINCT(r.id)', 'o.id as owner_id',
                "o.name || ' ' || o.surname || ' ' || o.lastname as owner",
                'p.kad_ident', 'r.contract_area',
                'p.virtual_ntp_title as ntp', 'p.virtual_category_title as category', 'c.id as contract_id', 'p.gid', 'p.ekate', 'p.virtual_ekatte_name as land',
                'TO_CHAR(c.c_date, \'DD.MM.YYYY\') as c_date', 'TO_CHAR(c.start_date, \'DD.MM.YYYY\') as start_date', 'c.due_date', 'c.farming_id', 're.percent',
                "CASE WHEN c.is_annex = true then c.c_num ||' (Анекс)' else c.c_num end as c_num", 'c.active',
            ],
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
            'where' => [
                'owner_id' => ['column' => 'id', 'compare' => 'IN', 'prefix' => 'o', 'value' => $parents],
                'c_type' => ['column' => 'nm_usage_rights', 'compare' => '<>', 'prefix' => 'c', 'value' => 1],
                'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
                'is_heritor' => ['column' => 'is_heritor', 'compare' => '=', 'prefix' => 're', 'value' => 'FALSE'],
                'ekate' => ['column' => 'ekate', 'compare' => 'IN', 'prefix' => 'p', 'value' => $arrayHelper->filterEmptyStringArr($ekate)],
                'farming' => ['column' => 'farming_id', 'compare' => 'IN', 'prefix' => 'c', 'value' => $farmingIds],
            ],
            'start_date' => $start_date,
            'due_date' => $due_date,
        ];

        $counter = $UserDbOwnersController->getFullOwnerAreaReport($options, true, false);

        if (0 == (int)$counter[0]['count']) {
            return $return;
        }

        $results = $UserDbOwnersController->getFullOwnerAreaReport($options, false, false);
        $resultsCount = count($results);
        $farminOptions = [
            'return' => [
                'id',
                'name',
            ],
            'where' => [
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'value' => $this->User->GroupID],
            ],
        ];
        $farmingResult = $FarmingController->getFarmings($farminOptions);
        $farmResCount = count($farmingResult);
        $total_ownage = 0;
        for ($i = 0; $i < $resultsCount; $i++) {
            for ($j = 0; $j < $farmResCount; $j++) {
                if ($farmingResult[$j]['id'] == $results[$i]['farming_id']) {
                    $results[$i]['farm_name'] = $farmingResult[$j]['name'];

                    break;
                }
            }
            $this->relation_id = $results[$i]['id'];
            $this->percent = [];
            $this->getOwnerHeritors($ownerId, $results[$i]['owner_id'] . '.*{1}', $results[$i]['percent']);
            $results[$i]['percent'] = $this->percent[$results[$i]['id']];

            $results[$i]['ownage'] = (($results[$i]['contract_area'] * $results[$i]['percent']) / 100);

            $results[$i]['ownage'] = number_format($results[$i]['ownage'], 3, '.', '');

            if (null != $results[$i]['due_date'] && '0000-00-00 00:00:00' != $results[$i]['due_date']) {
                $results[$i]['due_date'] = strftime('%d.%m.%Y', strtotime($results[$i]['due_date']));
            } else {
                $results[$i]['due_date'] = '-';
            }

            if ($results[$i]['active']) {
                if ('-' == $results[$i]['due_date']) {
                    $total_ownage += $results[$i]['ownage'];
                    $results[$i]['operational'] = true;
                } else {
                    $startDate = strtotime($results[$i]['start_date']);
                    $dueDate = strtotime($results[$i]['due_date']);
                    $today = strtotime(date('Y-m-d'));

                    if ($startDate <= $today && $today <= $dueDate) {
                        $total_ownage += $results[$i]['ownage'];
                        $results[$i]['operational'] = true;
                    }

                    if ($today > $dueDate) {
                        $results[$i]['operational'] = false;
                    }

                    if ($today < $startDate) {
                        $results[$i]['operational'] = true;
                    }
                }
            }
        }

        return [
            'rows' => $results,
            'total' => $counter[0]['count'],
            'footer' => [
                [
                    'kad_ident' => '<b>ОБЩО</b>',
                    'ownage' => number_format($total_ownage, 3, '.', ''),
                    'isFooter' => true,
                ],
            ],
        ];
    }

    /**
     * Get OwnerHeritors.
     *
     * @param int $ownerId
     * @param string $path
     * @param float $percent
     *
     * @return array
     */
    public function getOwnerHeritors($ownerId, $path, $percent)
    {
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);

        $options = [
            'return' => [
                'owner_id', 'path',
                "(SELECT poi.percent FROM su_plots_owners_rel poi WHERE poi.path = h.path AND poi.is_heritor = true AND poi.pc_rel_id = {$this->relation_id}) as percent",
            ],
            'where' => [
                'path' => ['column' => 'path', 'compare' => '~', 'value' => $path],
            ],
        ];

        $counter = $UserDbOwnersController->getOwnersHeritors($options, true);

        if (0 == $counter[0]['count']) {
            return [
                'rows' => [],
                'total' => 0,
            ];
        }

        $results = $UserDbOwnersController->getOwnersHeritors($options, false, false);
        $resultsCount = count($results);
        $sum_custom_ownage = 0;
        $heritors = 0;
        for ($i = 0; $i < $resultsCount; $i++) {
            if ($results[$i]['percent']) {
                $sum_custom_ownage += $results[$i]['percent'];
            } else {
                if ('0' !== $results[$i]['percent']) {
                    $heritors++;
                }
            }
        }

        for ($i = 0; $i < $resultsCount; $i++) {
            if (!$results[$i]['percent']) {
                $results[$i]['percent'] = '0' === $results[$i]['percent'] ? $results[$i]['percent'] : ($percent - $sum_custom_ownage) / $heritors;
            }

            if ($results[$i]['owner_id'] == $ownerId) {
                $this->percent[$this->relation_id] += $results[$i]['percent'];
            } else {
                $this->getOwnerHeritors($ownerId, $results[$i]['path'] . '.*{1}', $results[$i]['percent']);
            }
        }
    }
}
