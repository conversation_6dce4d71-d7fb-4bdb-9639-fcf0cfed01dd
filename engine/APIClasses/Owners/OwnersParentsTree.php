<?php

namespace TF\Engine\APIClasses\Owners;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbOwners\UserDbOwnersController;

/**
 * Информация за наследник.
 *
 * @rpc-module Owners
 *
 * @rpc-service-id owners-parents-tree
 */
class OwnersParentsTree extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getOwnersParentsTree']],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer root_id
     *                         }
     *
     * @return array
     */
    public function getOwnersParentsTree($rpcParams)
    {
        if ($this->User->isGuest) {
            return [];
        }
        // init all needed controllers
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableOwners,
            'return' => [
                'id', "name || ' ' || surname || ' ' || lastname as owner_names", 'is_dead',
            ],
            'where' => [
                'owner_id' => ['column' => 'id', 'compare' => '=', 'value' => $rpcParams['root_id']],
            ],
        ];

        $counter = $UserDbOwnersController->getItemsByParams($options, true);

        if (0 == $counter[0]['count']) {
            return [];
        }
        $results = $UserDbController->getItemsByParams($options);
        $resultsCount = count($results);
        $return = [];
        for ($i = 0; $i < $resultsCount; $i++) {
            if ($results[$i]['is_dead']) {
                $results[$i]['iconCls'] = 'icon-tree-user-rip';
            } else {
                $results[$i]['iconCls'] = 'icon-tree-user';
            }

            $return[] = [
                'text' => $results[$i]['owner_names'],
                'id' => $results[$i]['id'],
                'state' => 'open',
                'attributes' => $results[$i],
                'iconCls' => $results[$i]['iconCls'],
                'children' => $this->getOwnerHeritors($rpcParams['root_id'] . '.*{1}'),
            ];
        }

        return $return;
    }

    private function getOwnerHeritors($path)
    {
        $options = [
            'return' => [
                'h.id', "name || ' ' || surname || ' ' || lastname as owner_names", 'owner_id', 'is_dead', 'path',
            ],
            'where' => [
                'path' => ['column' => 'path', 'compare' => '~', 'value' => $path],
            ],
        ];
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);
        $counter = $UserDbOwnersController->getOwnersHeritors($options, true);

        if (0 == $counter[0]['count']) {
            return [];
        }

        $results = $UserDbOwnersController->getOwnersHeritors($options, false, false);
        $resultsCount = count($results);
        $return = [];
        for ($i = 0; $i < $resultsCount; $i++) {
            if ($results[$i]['is_dead']) {
                $results[$i]['iconCls'] = 'icon-tree-user-rip';
            } else {
                $results[$i]['iconCls'] = 'icon-tree-user';
            }

            $return[] = [
                'text' => $results[$i]['owner_names'],
                'id' => $results[$i]['owner_id'],
                'state' => 'open',
                'attributes' => $results[$i],
                'iconCls' => $results[$i]['iconCls'],
                'children' => $this->getOwnerHeritors($results[$i]['path'] . '.*{1}'),
            ];
        }

        return $return;
    }
}
