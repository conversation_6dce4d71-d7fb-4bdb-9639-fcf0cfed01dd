<?php

namespace TF\Engine\APIClasses\Owners;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

/**
 * Owners List.
 *
 * @rpc-module Owners
 *
 * @rpc-service-id owners-list
 *
 * @property UserDbController $UserDbController
 */
class OwnersList extends TRpcApiProvider
{
    private $UserDbController = false;

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readOwnersList']],
        ];
    }

    /**
     * read Owners List.
     *
     * @api-method read
     *
     * @param string $ownerType Owners or Reps or Companies
     * @param string $searchedName the searched name
     *
     * @return array result
     *               {
     *               #item string key            The owner id.
     *               #item string value          The owner name.
     *               }
     */
    public function readOwnersList($ownerType, $searchedName)
    {
        // init controller
        $UserDbController = new UserDbController($this->User->Database);
        $return = null;
        if ('owners' == $ownerType) {
            $return = $this->initOwnersSearch($searchedName);
        }

        if ('reps' == $ownerType) {
            $return = $this->initRepsSearch($searchedName);
        }

        if ('both' == $ownerType) {
            $return = $this->initBothSearch($searchedName);
        }

        if ('companies' == $ownerType) {
            $return = $this->initCompaniesSearch($searchedName);
        }

        if ('companies_eik' == $ownerType) {
            $return = $this->initCompaniesEIKSearch($searchedName);
        }

        if ('owners_egn' == $ownerType) {
            $return = $this->initOwnersWithEGNSearch($searchedName);
        }

        if ('egn' == $ownerType) {
            $return = $this->initEGNSearch($searchedName);
        }

        return $return;
    }

    private function initOwnersSearch($searchedName)
    {
        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableOwners,
            'return' => [
                'id::text as key',
                "name || ' ' || surname || ' ' || lastname as value",
            ],
            'where' => [
                'name' => ['column' => "name || ' ' || surname || ' ' || lastname", 'compare' => 'ilike', 'value' => '%' . $searchedName . '%'],
            ],
        ];

        return $UserDbController->getItemsByParams($options, false, false);
    }

    private function initRepsSearch($searchedName)
    {
        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableOwnersReps,
            'return' => [
                'id::text as key',
                "rep_name || ' ' || rep_surname || ' ' || rep_lastname as value",
            ],
            'where' => [
                'name' => ['column' => "rep_name || ' ' || rep_surname || ' ' || rep_lastname", 'compare' => 'ilike', 'value' => '%' . $searchedName . '%'],
            ],
        ];

        return $UserDbController->getItemsByParams($options, false, false);
    }

    private function initBothSearch($searchedName)
    {
        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableOwners,
            'return' => [
                'id::text as key',
                "case when owner_type = 0 then company_name else name || ' ' || surname || ' ' || lastname end as value",
            ],
            'where' => [
                'company_name' => ['column' => "case when owner_type = 0 then company_name else name || ' ' || surname || ' ' || lastname end", 'compare' => 'ilike', 'value' => '%' . $searchedName . '%'],
            ],
        ];

        return $UserDbController->getItemsByParams($options, false, false);
    }

    private function initCompaniesSearch($searchedName)
    {
        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableOwners,
            'return' => [
                'id::text as key',
                'company_name as value',
            ],
            'where' => [
                'company_name' => ['column' => 'company_name', 'compare' => 'ilike', 'value' => '%' . $searchedName . '%'],
                'owner_type' => ['column' => 'owner_type', 'compare' => '=', 'value' => 0],
            ],
        ];

        return $UserDbController->getItemsByParams($options, false, false);
    }

    private function initCompaniesEIKSearch($searchedName)
    {
        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableOwners,
            'return' => [
                'distinct(id)::text as key',
                "company_name || ' (' || eik || ')' as value",
                'eik',
            ],
            'whereOr' => [
                'company_name' => ['column' => 'company_name', 'compare' => 'ilike', 'value' => '%' . $searchedName . '%'],
                'eik' => ['column' => 'eik', 'compare' => 'ilike', 'value' => '%' . $searchedName . '%'],
            ],
            'having' => [
                'owner_type' => ['column' => 'owner_type', 'compare' => '=', 'value' => 0],
            ],
        ];

        return $UserDbController->getItemsByParams($options, false, false);
    }

    private function initOwnersWithEGNSearch($searchedName)
    {
        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableOwners,
            'return' => [
                'id::text as key',
                "name || ' ' || surname || ' ' || lastname || ' (' || egn || ')' as value",
                'egn',
            ],
            'whereOr' => [
                'name' => ['column' => "name || ' ' || surname || ' ' || lastname", 'compare' => 'ilike', 'value' => '%' . $searchedName . '%'],
                'egn' => ['column' => 'egn', 'compare' => 'ilike', 'value' => '%' . $searchedName . '%'],
            ],
        ];

        return $UserDbController->getItemsByParams($options, false, false);
    }

    private function initEGNSearch($searchedName)
    {
        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableOwners,
            'return' => [
                'id::text as key',
                "name || ' ' || surname || ' ' || lastname || ' (' || egn || ')' as value",
                'egn',
            ],
            'where' => [
                'name' => ['column' => 'egn', 'compare' => 'ilike', 'value' => '%' . $searchedName . '%'],
            ],
        ];

        return $UserDbController->getItemsByParams($options, false, false);
    }
}
