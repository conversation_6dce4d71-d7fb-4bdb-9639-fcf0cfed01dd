<?php

namespace TF\Engine\APIClasses\Owners;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbOwners\UserDbOwnersController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Грид "Общи документи".
 *
 * @rpc-module Owners
 *
 * @rpc-service-id owners-files
 */
class OwnersFiles extends TRpcApiProvider
{
    private $module = 'Owners';
    private $service_id = 'owners-files';

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readOwnersFiles'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'saveOwnerFiles' => ['method' => [$this, 'saveOwnerFiles']],
            'deleteOwnerFiles' => ['method' => [$this, 'deleteOwnerFiles']],
            'downloadAttachment' => ['method' => [$this, 'downloadAttachment'],
                'validators' => [
                    'document_id' => 'validateRequired, validateInteger',
                ],
            ],
            'deleteAttachment' => ['method' => [$this, 'deleteAttachment'],
                'validators' => [
                    'document_id' => 'validateRequired, validateInteger',
                ],
            ],
        ];
    }

    /**
     * Save Owner File.
     *
     * @api-method saveOwnerFiles
     *
     * @param array $param {
     *                     #item int id
     *                     #item int owner_id
     *                     #item int number
     *                     #item string date
     *                     }
     *
     * @return null|int
     */
    public function saveOwnerFiles($param)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $options = [
            'tablename' => $UserDbController->DbHandler->tableOwnersFiles,
            'mainData' => [
                'owner_id' => $param['owner_id'],
                'date' => $param['date'] ?: date('Y-m-d'),
                'note' => $param['note'],
            ],
        ];

        if ($param['id']) {
            $oldOptions = [
                'tablename' => $UserDbController->DbHandler->tableOwnersFiles,
                'return' => [
                    'id',
                    'owner_id',
                    'attachment',
                    'date',
                    'note'],
                'where' => [
                    'id' => ['column' => 'id', 'compare' => '=', 'value' => $param['id']],
                ],
            ];
            $oldDoc = $UserDbController->getItemsByParams($oldOptions, false, false);

            $options['where'] = ['id' => $param['id']];
            $UserDbController->editItem($options);

            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['new_values' => $options], ['old_values' => $oldDoc[0]], 'Editing owner document');
        } else {
            $itemId = $UserDbController->addItem($options);

            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['new_values' => $options], ['newly_created_doc_ic' => $itemId], 'Adding owner document');

            return $itemId;
        }
    }

    /**
     * Delete Owner File.
     *
     * @api-method deleteOwnerFile
     *
     * @param int $id
     */
    public function deleteOwnerFiles($id)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);

        $old_options = [
            'tablename' => $UserDbController->DbHandler->tableOwnersFiles,
            'return' => [
                'id',
                'owner_id',
                'attachment',
                'date',
                'note',
            ],
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $id],
            ],
        ];

        $old_doc = $UserDbOwnersController->getItemsByParams($old_options, false, false);

        $UserDbOwnersController->deleteItemsByParams([
            'tablename' => $UserDbController->DbHandler->tableOwnersFiles,
            'id_string' => $id,
        ]);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['deleted_doc_id' => $id], ['deleted_doc_info' => $old_doc[0]], 'Deleting owner document');
    }

    /**
     * Read Owners Files.
     *
     * @api-method read
     *
     * @param array $rpcParams {
     *                         #item integer owner_id
     *                         }
     * @param int|string $page
     * @param int|string $rows
     * @param ?string $sort
     * @param ?string $order
     *
     * @return array
     */
    public function readOwnersFiles(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);
        $options = [
            'return' => [
                'f.id',
                'f.owner_id',
                'f.attachment',
                'to_char(f.date, \'YYYY-MM-DD\') as date',
                'f.note',
            ],
            'sort' => $sort,
            'order' => $order,
            'where' => [
                'owner_id' => ['column' => 'owner_id', 'compare' => '=', 'value' => $rpcParams['owner_id']],
            ],
        ];
        $counter = $UserDbOwnersController->getOwnersFiles($options, true, false);

        // define default return
        $return = [
            'rows' => [],
            'total' => 0,
        ];

        if (0 == $counter[0]['count']) {
            return $return;
        }

        return $UserDbOwnersController->getOwnersFiles($options, false, false);
    }

    /**
     * Returns the link for the requested file.
     *
     * @api-method downloadAttachment
     *
     * @param int $rpcParam
     *
     * @throws MTRpcException -33305 - FILE_DOES_NOT_EXIST if file is not on the server
     *
     * @return array|string
     */
    public function downloadAttachment($rpcParam)
    {
        $UserDbController = new UserDbController($this->User->Database);

        $results = $UserDbController->getItemsByParams(
            [
                'tablename' => $UserDbController->DbHandler->tableOwnersFiles,
                'where' => [
                    'id' => ['column' => 'id', 'compare' => '=', 'value' => $rpcParam],
                ], false, false]
        );

        if (!count($results)) {
            return [];
        }

        $document = $results[0];
        $fileFragment = $this->User->GroupID . '/' . $document['id'] . '_' . $document['attachment'];
        $filePath = OWNER_FILES . $fileFragment;

        if (file_exists($filePath)) {
            return SITE_URL . str_replace(SITE_PATH . 'public', '', $filePath);
        }

        throw new MTRpcException('FILE_DOES_NOT_EXIST', -33305);
    }

    /**
     * Returns the link for the requested file.
     *
     * @api-method deleteAttachment
     *
     * @param int $rpcParam
     *
     * @return array
     */
    public function deleteAttachment($rpcParam)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $results = $UserDbController->getItemsByParams([
            'tablename' => $UserDbController->DbHandler->tableOwnersFiles,
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $rpcParam],
            ],
        ], false, false);

        if (!count($results)) {
            return [];
        }

        $UserDbController->editItem([
            'tablename' => $UserDbController->DbHandler->tableOwnersFiles,
            'mainData' => [
                'attachment' => null,
            ],
            'where' => [
                'id' => $rpcParam,
            ],
        ]);

        $fileFragment = $this->User->GroupID . '/' . $results[0]['id'] . '_' . $results[0]['attachment'];
        $filePath = OWNER_DOCUMENT_FILES . $fileFragment;

        @unlink($filePath);
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['delete_doc_owner_attachment' => $rpcParam], ['deleted_owner_attachment_info' => $results[0]], 'Deleting owner document attachment');
    }
}
