<?php

namespace TF\Engine\APIClasses\Owners;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbPayments\UserDbPaymentsController;

/**
 * Owner payments.
 *
 * @rpc-module Owners
 *
 * @rpc-service-id owners-payments
 */
class OwnersPaymentsGrid extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readOwnersPayments'],
                'validators' => [
                    'ownerId' => 'validateInteger',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * Reads Owners Payments - Grid Изплатени ренти.
     *
     * @api-method read
     *
     * @param int $page
     * @param int $rows
     * @param ?string $sort
     * @param ?string $order
     *
     * @return array {
     *               #item integer total
     *               #item array rows {
     *               #item string amount
     *               #item string amount_nat
     *               #item string bank_acc
     *               #item string c_date
     *               #item string c_num
     *               #item string due_date
     *               #item string farming_year
     *               #item integer paid_from
     *               #item string paid_from_text
     *               #item integer paid_in
     *               #item string paid_in_text
     *               #item string payer_name
     *               #item string payment_date
     *               #item string payment_nat_type
     *               #item string recipient
     *               #item string start_date
     *               #item integer transaction_id: 161
     *               }
     *               }
     */
    public function readOwnersPayments(int $ownerId, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);

        $return = [
            'rows' => [],
            'total' => 0,
        ];

        if (!$ownerId || !(int)$ownerId) {
            return $return;
        }

        $options = [
            'custom_counter' => 'count(DISTINCT(p.id))',
            'return' => [
                'c.c_num', 'c.c_date', 'max(p.date) as payment_date',
                '(CASE WHEN max(a.due_date) IS NULL THEN c.due_date ELSE GREATEST(max(a.due_date), max(c.due_date)) END) as due_date',
                'c.start_date',
                'max(p.farming_year) as farming_year',
                'max(round(p.amount::numeric, 2)) as amount',
                'max(t.id) as transaction_id',
                'max(t.payer_name) as payer_name',
                'max(p.paid_from) as paid_from', 'max(p.paid_in) as paid_in',
                'max(bank_acc) as bank_acc', 'max(recipient) as recipient',
                'array_agg(pn.nat_type) FILTER (WHERE pn.nat_type IS NOT NULL) as payment_nat_type',
                'array_agg(round(pn.amount::numeric, 2)) FILTER (WHERE pn.amount IS NOT NULL) as amount_nat',
            ],
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
            'where' => [
                'owner_id' => ['column' => 'id', 'compare' => '=', 'prefix' => 'o', 'value' => $ownerId],
            ],
            'group' => 'p.id, c.id, o.id',
        ];

        $counter = $UserDbPaymentsController->getPaymentsByParams($options, true, false);
        if (0 == $counter[0]['count']) {
            return $return;
        }

        $results = $UserDbPaymentsController->getPaymentsByParams($options, false, false);
        $resultsCount = count($results);
        // get all renta types
        $options = [
            'tablename' => $UserDbController->DbHandler->tableRentaTypes,
        ];

        $renta_results = $UserDbPaymentsController->getItemsByParams($options, false, false);
        $rentaCount = count($renta_results);
        $renta_types = [];
        for ($i = 0; $i < $rentaCount; $i++) {
            $renta_types[$renta_results[$i]['id']] = $renta_results[$i]['name'] . '(' . $GLOBALS['Contracts']['renta_units'][$renta_results[$i]['unit']]['name'] . ')';
        }
        // end of renta types

        for ($i = 0; $i < $resultsCount; $i++) {
            $results[$i]['c_num'] = $results[$i]['c_num'] . '(' . strftime('%d.%m.%Y', strtotime($results[$i]['start_date'])) . ' - ' . strftime('%d.%m.%Y', strtotime($results[$i]['due_date'])) . ')';
            $results[$i]['payment_date'] = strftime('%d.%m.%Y', strtotime($results[$i]['payment_date']));
            $results[$i]['farming_year_id'] = $results[$i]['farming_year'];
            $results[$i]['farming_year'] = $GLOBALS['Farming']['years'][$results[$i]['farming_year']]['farming_year_short'];
            if (1 == $results[$i]['paid_from']) {
                $results[$i]['paid_from_text'] = BGNtoEURO($results[$i]['amount']);
            } else {
                $payment_nat_types = explode(',', trim($results[$i]['payment_nat_type'], '{}'));
                $paymentsCount = count($payment_nat_types);
                $amount_nat = explode(',', trim($results[$i]['amount_nat'], '{}'));
                for ($j = 0; $j < $paymentsCount; $j++) {
                    $results[$i]['paid_from_text'] .= $amount_nat[$j] . ' X [' . $renta_types[$payment_nat_types[$j]] . ']<br/>';
                }
            }

            if (1 == $results[$i]['paid_in']) {
                $results[$i]['paid_in_text'] = BGNtoEURO($results[$i]['amount']);
            } else {
                $payment_nat_types = explode(',', trim($results[$i]['payment_nat_type'], '{}'));
                $paymentsCount = count($payment_nat_types);
                $amount_nat = explode(',', trim($results[$i]['amount_nat'], '{}'));
                for ($j = 0; $j < $paymentsCount; $j++) {
                    $results[$i]['paid_in_text'] .= $amount_nat[$j] . ' X [' . $renta_types[$payment_nat_types[$j]] . ']<br/>';
                }
            }

            $results[$i]['recipient'] = ('' != $results[$i]['recipient']) ? $results[$i]['recipient'] : '-';
            $results[$i]['bank_acc'] = ('' != $results[$i]['bank_acc']) ? $results[$i]['bank_acc'] : '-';
        }

        return [
            'rows' => $results,
            'total' => $counter[0]['count'],
        ];
    }

    public function getPayments($ownerId, $yearId)
    {
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);

        $options = [
            'custom_counter' => 'count(DISTINCT(p.id))',
            'return' => [
                'c.c_num', 'c.c_date', 'max(p.date) as payment_date',
                '(CASE WHEN max(a.due_date) IS NULL THEN c.due_date ELSE GREATEST(max(a.due_date), max(c.due_date)) END) as due_date',
                'c.start_date',
                'max(p.farming_year) as farming_year',
                'max(round(p.amount::numeric, 2)) as amount',
                'max(t.id) as transaction_id',
                'max(t.payer_name) as payer_name',
                'max(p.paid_from) as paid_from', 'max(p.paid_in) as paid_in',
                'max(bank_acc) as bank_acc', 'max(recipient) as recipient',
                'array_agg(pn.nat_type) FILTER (WHERE pn.nat_type IS NOT NULL) as payment_nat_type',
                'array_agg(round(pn.amount::numeric, 2)) FILTER (WHERE pn.amount IS NOT NULL) as amount_nat',
            ],
            'where' => [
                'owner_id' => ['column' => 'id', 'compare' => '=', 'prefix' => 'o', 'value' => $ownerId],
                'year_id' => ['column' => 'farming_year', 'compare' => '>', 'prefix' => 'p', 'value' => $yearId],
                'status' => ['column' => 'status', 'compare' => '=', 'prefix' => 't', 'value' => true],
            ],
            'group' => 'p.id, c.id, o.id',
        ];

        return $UserDbPaymentsController->getPaymentsByParams($options, false, false);
    }
}
