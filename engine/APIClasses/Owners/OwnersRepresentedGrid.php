<?php

namespace TF\Engine\APIClasses\Owners;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDbOwners\UserDbOwnersController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * дърво "Собственици".
 *
 * @rpc-module Owners
 *
 * @rpc-service-id owners-represented-grid
 */
class OwnersRepresentedGrid extends TRpcApiProvider
{
    private $module = 'Owners';
    private $service_id = 'owners-represented-grid';

    /**
     * Registering all the external RPC methods with their respective hooks.
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readOwnerRepresentedPlots'],
                'validators' => [
                    'filterObj' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'changeOwnerRepresentative' => ['method' => [$this, 'changeOwnerRepresentative']],
        ];
    }

    /**
     * Returns the owners tree entries, based on filter criteria.
     *
     * @api-method read
     *
     * @param array $filterObj An array of filter parameters
     *                         {
     *                         #item string owner_names  - the names of the owner to be matched
     *                         #item string egn          - the EGN of the owner to be matched
     *                         #item string company_name - the company name to be matched
     *                         #item string eik          - the company's EIK to be matched
     *                         }
     * @param int $page current page for pagination
     * @param int $rows the number of rows to be returned
     * @param string $sort the sorting criteria to be used
     * @param string $order the sorting order in which the results to be ordered
     *
     * @return array the matching results array
     */
    public function readOwnerRepresentedPlots(array $filterObj, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $FarmingController = new FarmingController('Farming');
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);

        $arrayHelper = $FarmingController->ArrayHelper;

        $userFarmings = $FarmingController->getUserFarmings();
        $userFarmingIds = array_keys($userFarmings);
        $farmingIds = $arrayHelper->filterEmptyStringArr($filterObj['farming']);
        $farmingIds = isset($farmingIds) && count($farmingIds) > 0
            ? array_intersect($farmingIds, $userFarmingIds)
            : [...$userFarmingIds, null];

        date_default_timezone_set('Europe/Sofia');
        $currentDate = date('Y-m-d', time());

        $options = [
            'return' => [
                'o_r.id', "TRIM(rep_name) || ' ' || TRIM(rep_surname) || ' ' || TRIM(rep_lastname) as rep_owner_names",
                'TRIM(rep_egn) as rep_egn', 'kvs.kad_ident', 'c.farming_id', 'c.c_num', 'c.c_date', 'c.start_date', 'c.due_date',
                'c.nm_usage_rights', 'c.active', 'kvs.ekate', 'c.id as c_id', 'kvs.gid', 'o.id as owner_id', 'por.id as rel_id',
                'count(*) OVER() as total_count', 'c.active',
            ],
            'where' => [
                'owner_id' => ['column' => 'owner_id', 'prefix' => 'por', 'compare' => '=', 'value' => $filterObj['owner_id']],
                'rep_egn' => ['column' => 'rep_egn',  'prefix' => 'o_r', 'compare' => '=', 'value' => $filterObj['rep_egn']],
                'c_num' => ['column' => 'c_num', 'prefix' => 'c', 'compare' => '=', 'value' => $filterObj['c_num']],
                'nm_usage_rights' => ['column' => 'nm_usage_rights', 'prefix' => 'c', 'compare' => 'IN', 'value' => $arrayHelper->filterEmptyStringArr($filterObj['c_type'])],
                'farming_id' => ['column' => 'farming_id', 'prefix' => 'c', 'compare' => 'IN', 'value' => $farmingIds],
                'kad_ident' => ['column' => 'kad_ident', 'prefix' => 'kvs', 'compare' => '=', 'value' => $filterObj['kad_ident']],
                'ekate' => ['column' => 'ekate', 'prefix' => 'kvs', 'compare' => 'IN', 'value' => $arrayHelper->filterEmptyStringArr($filterObj['ekate'])],
                'masiv' => ['column' => 'masiv', 'prefix' => 'kvs', 'compare' => '=', 'value' => $filterObj['masiv']],
                'imot' => ['column' => 'imot', 'prefix' => 'kvs', 'compare' => '=', 'value' => $filterObj['number']],
            ],
            'owner_id' => $filterObj['owner_id'],
            'group' => 'o.id, o_r.id, kvs.gid, c.id, por.id',
        ];

        if (isset($filterObj['rep_names']) && '' != $filterObj['rep_names']) {
            $tmp_rep_names = preg_replace('/\s+/', '.*', $filterObj['rep_names']);
            $tmp_rep_names = mb_strtolower($tmp_rep_names, 'UTF-8');
            $options['where']['rep_names'] = ['column' => "lower(TRIM (o_r.rep_name)) || ' ' || lower(TRIM (o_r.rep_surname)) || ' ' || lower(TRIM (o_r.rep_lastname))", 'compare' => '~', 'value' => $tmp_rep_names];
        }
        switch ($sort) {
            case 'kad_ident':
                $options['sort'] = 'kad_ident COLLATE "alpha_numeric_bg"';

                break;
            case 'rep_owner_names':
                $options['sort'] = 'TRIM(rep_name) || \' \' || TRIM(rep_surname) || \' \' || TRIM(rep_lastname) COLLATE "alpha_numeric_bg"';

                break;
            case 'farm_name':
                $options['sort'] = 'c.farming_id COLLATE "alpha_numeric_bg"';

                break;
            case 'c_num':
                $options['sort'] = 'c_num COLLATE "alpha_numeric_bg"';

                break;
            case 'nm_usage_rights':
                $options['sort'] = 'c.nm_usage_rights COLLATE "alpha_numeric_bg"';

                break;
            default:
                $options['sort'] = $sort;

                break;
        }
        // adding limit for pagination
        // define page limit
        $options['order'] = $order;
        $options['offset'] = ($page - 1) * $rows;
        $options['offset'] = $options['offset'] >= 0 ? $options['offset'] : 0;
        $options['limit'] = $rows;

        $results = $UserDbOwnersController->getOwnerRepresentedPlots($options, false, false);
        $resultsCount = count($results);
        if (0 == $results[0]['total_count']) {
            return [
                'rows' => [],
                'total' => 0,
            ];
        }

        $farminOptions = [
            'return' => [
                'id',
                'name',
            ],
            'where' => [
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'value' => $this->User->GroupID],
            ],
        ];
        $farmingResult = $FarmingController->getFarmings($farminOptions);
        $farmingCount = count($farmingResult);
        $total_ownage = 0;
        for ($i = 0; $i < $resultsCount; $i++) {
            for ($j = 0; $j < $farmingCount; $j++) {
                if ($farmingResult[$j]['id'] == $results[$i]['farming_id']) {
                    $results[$i]['farm_name'] = $farmingResult[$j]['name'];

                    break;
                }
            }

            $results[$i]['c_date'] = strftime('%d.%m.%Y', strtotime($results[$i]['c_date']));
            $results[$i]['start_date'] = strftime('%d.%m.%Y', strtotime($results[$i]['start_date']));

            $results[$i]['nm_usage_rights'] = $GLOBALS['Contracts']['ContractTypes'][$results[$i]['nm_usage_rights']]['name'];

            if ($results[$i]['active']) {
                $results[$i]['active_text'] = (!$results[$i]['due_date'] || $results[$i]['due_date'] >= $currentDate) ? 'Действащ' : 'Изтекъл';
            } else {
                $results[$i]['active_text'] = 'Анулиран';
            }

            if (null != $results[$i]['due_date'] && '0000-00-00 00:00:00' != $results[$i]['due_date']) {
                $results[$i]['due_date'] = strftime('%d.%m.%Y', strtotime($results[$i]['due_date']));
            } else {
                $results[$i]['due_date'] = '-';
            }

            if ($results[$i]['active']) {
                if ('-' == $results[$i]['due_date']) {
                    $total_ownage += $results[$i]['ownage'];
                    $results[$i]['operational'] = true;
                } else {
                    $startDate = strtotime($results[$i]['start_date']);
                    $dueDate = strtotime($results[$i]['due_date']);
                    $today = strtotime(date('Y-m-d'));

                    if ($startDate <= $today && $today <= $dueDate) {
                        $total_ownage += $results[$i]['ownage'];
                        $results[$i]['operational'] = true;
                    }

                    if ($today > $dueDate) {
                        $results[$i]['operational'] = false;
                    }

                    if ($today < $startDate) {
                        $results[$i]['operational'] = true;
                    }
                }
            }

            $results[$i]['iconCls'] = 'icon-tree-user';
        }

        return ['rows' => $results, 'total' => $results[0]['total_count']];
    }

    public function changeOwnerRepresentative($rpcParams)
    {
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);
        $UsersController = new UsersController('Users');

        // Getting the old values for log
        $options = [
            'tablename' => $UserDbOwnersController->DbHandler->plotsOwnersRelTable . ' t',
            'return' => [
                '*',
            ],
            'joins' => [
                'inner join ' . $UserDbOwnersController->DbHandler->contractsPlotsRelTable . ' cpr on cpr.id = t.pc_rel_id',
            ],
            'where' => [
                'contract_id' => ['column' => 'contract_id', 'prefix' => 'cpr', 'compare' => '=', 'value' => $rpcParams['contract_id']],
                'owner_id' => ['column' => 'owner_id', 'prefix' => 't', 'compare' => '=', 'value' => $rpcParams['owner_id']],
            ],
        ];

        $repData = $UserDbOwnersController->getItemsByParams($options);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['rmeoving representative por.id' => $rpcParams], ['old_values' => $repData[0]], 'Removing representative');

        $options = [
            'tablename' => $UserDbOwnersController->DbHandler->plotsOwnersRelTable,
            'mainData' => [
                'rep_id' => $rpcParams['rep_id'],
            ],
            'where' => [
                'owner_id' => $rpcParams['owner_id'],
            ],
            'id_string' => implode(',', array_column($repData, 'pc_rel_id')),
            'id_name' => 'pc_rel_id',
        ];

        $UserDbOwnersController->editItem($options);
    }
}
