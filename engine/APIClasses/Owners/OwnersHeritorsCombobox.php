<?php

namespace TF\Engine\APIClasses\Owners;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbOwners\UserDbOwnersController;

/**
 * Owners heritors list.
 *
 * @rpc-module Owners
 *
 * @rpc-service-id owners-heritors-combobox
 */
class OwnersHeritorsCombobox extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readOwnersHeritorsCombobox']],
        ];
    }

    /**
     * Read Owners Heritors Combobox.
     *
     * @api-method read
     *
     * @param int $root_id
     * @param string $egn
     * @param bool $selected
     *
     * @return array {
     *               #item integer id
     *               #item string owner_names
     *               }
     */
    public function readOwnersHeritorsCombobox($root_id, $egn, $selected)
    {
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);

        $owners_id_array = [];

        if ($root_id) {
            $options = [
                'return' => [
                    'DISTINCT(owner_id)', 'path',
                ],
                'where' => [
                    'path' => ['column' => 'path', 'compare' => '~', 'value' => $root_id . '.*'],
                ],
                'where_or' => [
                    'path1' => ['column' => 'path', 'compare' => '~', 'value' => '*.' . $root_id],
                ],
            ];
            $owners_id_result = $UserDbOwnersController->getOwnersHeritors($options, false, false);
            $ownersIdCount = count($owners_id_result);
            for ($i = 0; $i < $ownersIdCount; $i++) {
                $tmpOwnerIds[] = $owners_id_result[$i]['owner_id'];
                $tmpArray = explode('.', $owners_id_result[$i]['path']);
                foreach ($tmpArray as $item) {
                    $tmpOwnerIds[] = (int)($item);
                }
            }
            $tmpOwnerIds[] = (int)($root_id);
        }

        $tmpOwnerIds = array_unique($tmpOwnerIds);
        foreach ($tmpOwnerIds as $tmpOwnerId) {
            $owners_id_array[] = $tmpOwnerId;
        }

        $options = [
            'tablename' => $UserDbController->DbHandler->tableOwners,
            'sort' => 'id',
            'order' => 'desc',
            'return' => [
                'DISTINCT(id)', "name || ' ' || surname || ' ' || lastname as owner_names",
            ],
            'where' => [
                'owner_type' => ['column' => 'owner_type', 'compare' => '=', 'value' => 1],
                'egn' => ['column' => 'egn', 'compare' => 'ILIKE', 'value' => $egn],
                'anti_id' => ['column' => 'id', 'compare' => 'NOT IN', 'value' => $owners_id_array],
            ],
        ];

        $results = $UserDbController->getItemsByParams($options);

        if ($selected && count($results)) {
            $results[0]['selected'] = true;
        }

        return $results;
    }
}
