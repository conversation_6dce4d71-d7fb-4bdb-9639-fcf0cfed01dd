<?php

namespace TF\Engine\APIClasses\Owners;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbOwners\UserDbOwnersController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * дърво "Собственици".
 *
 * @rpc-module Owners
 *
 * @rpc-service-id representatives-tree
 *
 * @property UserDbOwnersController $UserDbOwnersController
 * @property UserDbController $UserDbController
 * @property UsersController $UsersController
 * @property FarmingController $FarmingController
 */
class RepresentativesTree extends TRpcApiProvider
{
    /**
     * Declaring the necessary database controllers.
     */
    private $UserDbOwnersController = false;
    private $UserDbController = false;
    private $UsersController = false;
    private $FarmingController = false;
    private $module = 'Owners';
    private $service_id = 'representatives-tree';

    /**
     * Registering all the external RPC methods with their respective hooks.
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readRepresentativesTree'],
                'validators' => [
                    'filterObj' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'addRepresentative' => ['method' => [$this, 'addRepresentative'],
                'validators' => [
                    'rpcParams' => [
                        'rep_name' => 'validateRequired, validateText',
                        'rep_surname' => 'validateRequired, validateText',
                        'rep_lastname' => 'validateRequired, validateText',
                        'egn' => 'validateRequired, validateDigitsOnly',
                        'rep_lk' => 'validateText',
                        'rep_lk_izdavane' => 'validateText',
                        'iban' => 'validateText',
                        'rep_address' => 'validateText',
                        'rent_place' => 'validateText',
                    ],
                ],
            ],
            'editRepresentative' => ['method' => [$this, 'editRepresentative'],
                'validators' => [
                    'rpcParams' => [
                        'rep_name' => 'validateRequired, validateText',
                        'rep_surname' => 'validateRequired, validateText',
                        'rep_lastname' => 'validateRequired, validateText',
                        'egn' => 'validateRequired, validateDigitsOnly',
                        'rep_lk' => 'validateText',
                        'rep_lk_izdavane' => 'validateText',
                        'iban' => 'validateText',
                        'rep_address' => 'validateText',
                        'rent_place' => 'validateText',
                    ],
                ],
            ],
            'markForEdit' => ['method' => [$this, 'markForEdit'],
                'validators' => [
                    'rpcParam' => 'validateRequired, validateInteger',
                ],
            ],
            'deleteRepresentative' => ['method' => [$this, 'deleteRepresentative'],
                'validators' => [
                    'rpcParams' => 'validateRequired, validateInteger',
                ],
            ],
        ];
    }

    /**
     * Returns the owners tree entries, based on filter criteria.
     *
     * @api-method read
     *
     * @param array $filterObj An array of filter parameters
     *                         {
     *                         #item string owner_names  - the names of the owner to be matched
     *                         #item string egn          - the EGN of the owner to be matched
     *                         #item string company_name - the company name to be matched
     *                         #item string eik          - the company's EIK to be matched
     *                         }
     * @param int $page current page for pagination
     * @param int $rows the number of rows to be returned
     * @param string $sort the sorting criteria to be used
     * @param string $order the sorting order in which the results to be ordered
     *
     * @return array the matching results array
     */
    public function readRepresentativesTree(array $filterObj, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $filterObj['company_name'] = $filterObj['company_name'] ? preg_replace('/\s+/', ' ', $filterObj['company_name']) : '';
        $filterObj['owner_names'] = $filterObj['owner_names'] ? preg_replace('/\s+/', ' ', $filterObj['owner_names']) : '';

        // init all needed controllers
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);
        $FarmingController = new FarmingController('Farming');

        $arrayHelper = $FarmingController->ArrayHelper;

        $options = [
            'return' => [
                'o_r.id', "TRIM(rep_name) || ' ' || TRIM(rep_surname) || ' ' || TRIM(rep_lastname) as rep_owner_names",
                'TRIM(rep_name) as rep_name', 'TRIM(rep_surname) as rep_surname', 'TRIM(rep_lastname) as rep_lastname',
                'TRIM(rep_egn) as egn', 'TRIM(rep_lk) as lk_nomer', 'TRIM(rep_lk_izdavane) as lk_izdavane', 'TRIM(o_r.iban) as iban',
                'TRIM(rep_address) as address', 'TRIM(rep_phone) as rep_phone', 'TRIM(o_r.rent_place) as rent_place',
                'count(*) OVER() as total_count',
            ],
            'where' => [
                'rep_egn' => ['column' => 'rep_egn', 'prefix' => 'o_r', 'compare' => 'ILIKE', 'value' => $filterObj['rep_egn']],
                'rep_id' => ['column' => 'id', 'prefix' => 'o_r', 'compare' => '=', 'value' => $filterObj['rep_id']],
                'owner_id' => ['column' => 'owner_id', 'prefix' => 'o_r', 'compare' => 'IS', 'value' => 'NULL'],
                'egn' => ['column' => 'egn', 'prefix' => 'o', 'compare' => 'ILIKE', 'value' => $filterObj['egn']],
                'company_name' => ['column' => 'company_name', 'prefix' => 'o', 'compare' => 'ILIKE', 'value' => $filterObj['company']],
                'eik' => ['column' => 'eik', 'prefix' => 'o', 'compare' => 'ILIKE', 'value' => $filterObj['eik']],
                'kad_ident' => ['column' => 'kad_ident', 'prefix' => 'kvs', 'compare' => '=', 'value' => $filterObj['kad_ident']],
                'masiv' => ['column' => 'masiv', 'prefix' => 'kvs', 'compare' => '=', 'value' => $filterObj['masiv']],
                'imot' => ['column' => 'number', 'prefix' => 'kvs', 'compare' => '=', 'value' => $filterObj['imot']],
                'ekate' => ['column' => 'ekate', 'prefix' => 'kvs', 'compare' => 'IN', 'value' => $arrayHelper->filterEmptyStringArr($filterObj['ekate'])],
                'nm_usage_rights' => ['column' => 'nm_usage_rights', 'prefix' => 'c', 'compare' => 'IN', 'value' => $arrayHelper->filterEmptyStringArr($filterObj['c_type'])],
                'c_num' => ['column' => 'c_num', 'prefix' => 'c', 'compare' => 'ILIKE', 'value' => $filterObj['c_num']],
                'anti_rep_id' => ['column' => 'id', 'prefix' => 'o_r', 'compare' => '<>', 'value' => $filterObj['anti_rep_id']],
            ],
            'group' => 'o_r.id',
        ];

        if (isset($filterObj['rep_names']) && '' != $filterObj['rep_names']) {
            $tmp_rep_names = preg_replace('/\s+/', '.*', $filterObj['rep_names']);
            $tmp_rep_names = mb_strtolower($tmp_rep_names, 'UTF-8');
            $options['where']['rep_names'] = ['column' => "lower(TRIM (rep_name)) || ' ' || lower(TRIM (rep_surname)) || ' ' || lower(TRIM (rep_lastname))", 'compare' => '~', 'value' => $tmp_rep_names];
        }

        if (isset($filterObj['owner']) && '' != $filterObj['owner']) {
            $tmp_owner = preg_replace('/\s+/', '.*', $filterObj['owner']);
            $tmp_owner = mb_strtolower($tmp_owner, 'UTF-8');
            $options['where']['owner'] = ['column' => "lower(TRIM (o.NAME)) || ' ' || lower(TRIM (o.surname)) || ' ' || lower(TRIM (o.lastname))", 'compare' => '~', 'value' => $tmp_owner];
        }

        if (isset($filterObj['mol']) && '' != $filterObj['mol']) {
            $tmp_mol = preg_replace('/\s+/', '.*', $filterObj['mol']);
            $tmp_mol = mb_strtolower($tmp_mol, 'UTF-8');
            $options['where']['mol'] = ['column' => 'lower(mol)', 'compare' => '~', 'value' => $tmp_mol];
        }

        if (isset($filterObj['rep_name']) && '' != $filterObj['rep_name']) {
            $tmp_rep_name = preg_replace('/\s+/', '.*', $filterObj['rep_name']);
            $tmp_rep_name = mb_strtolower($tmp_rep_name, 'UTF-8');
            $options['where']['rep_name'] = ['column' => "lower(TRIM (o_r.rep_name)) || ' ' || lower(TRIM (o_r.rep_surname)) || ' ' || lower(TRIM (o_r.rep_lastname))", 'compare' => '~', 'value' => $tmp_rep_name];
        }

        if (isset($filterObj['c_num']) && '' != $filterObj['c_num']) {
            $options['where']['annex_action'] = ['column' => 'annex_action', 'prefix' => 'cpr', 'compare' => '=', 'value' => 'added'];
            $options['where']['active'] = ['column' => 'active', 'prefix' => 'c', 'compare' => '=', 'value' => 'true'];
            $options['where']['is_sublease'] = ['column' => 'is_sublease', 'prefix' => 'c', 'compare' => '=', 'value' => 'false'];
        }

        if ($filterObj['forGrid']) {
            unset($options['where']['owner_id']);
        }

        // adding limit for pagination
        // define page limit
        $page_limit = $rows ? $rows : 30;
        $options['sort'] = "TRIM(rep_name) || ' ' || TRIM(rep_surname) || ' ' || TRIM(rep_lastname) COLLATE \"alpha_numeric_bg\"";
        $options['order'] = 'asc';
        $options['offset'] = ($page - 1) * $page_limit;
        $options['offset'] = $options['offset'] >= 0 ? $options['offset'] : 0;
        $options['limit'] = $page_limit;

        $results = $UserDbOwnersController->getRepresentativesTree($options, false, false);
        $resultsCount = count($results);
        if (0 == $results[0]['total_count']) {
            return [
                'rows' => [],
                'total' => 0,
            ];
        }

        $ekateOptions = [
            'tablename' => 'ekate_combobox',
        ];
        $ekateData = $UserDbOwnersController->getItemsByParams($ekateOptions);
        $ekateCount = count($ekateData);
        $ekateNames = [];
        for ($i = 0; $i < $ekateCount; $i++) {
            $ekateNames[$ekateData[$i]['ekate']] = $ekateData[$i]['ekatte_name'];
        }

        for ($i = 0; $i < $resultsCount; $i++) {
            if ($results[$i]['rent_place']) {
                $results[$i]['land'] = '(' . $results[$i]['rent_place'] . ') ' . $ekateNames[$results[$i]['rent_place']];
            } else {
                $results[$i]['land'] = '-';
            }

            if (!$results[$i]['lk_nomer']) {
                $results[$i]['lk_nomer'] = '-';
            }
            if (!$results[$i]['lk_izdavane']) {
                $results[$i]['lk_izdavane'] = '-';
            }

            if (!$results[$i]['iban']) {
                $results[$i]['iban'] = '-';
            }
            if (!$results[$i]['address']) {
                $results[$i]['address'] = '-';
            }
            if (!$results[$i]['rep_phone']) {
                $results[$i]['rep_phone'] = '-';
            }

            $results[$i]['iconCls'] = 'icon-tree-user';

            $return[] = [
                'text' => $results[$i]['rep_owner_names'],
                'id' => $results[$i]['id'],
                'attributes' => $results[$i],
                'iconCls' => $results[$i]['iconCls'],
            ];
        }

        // add attribute to first listed element of three for custom pagination
        // information for total can not be sent otherwise
        $return[0]['attributes']['pagination']['total'] = $results[0]['total_count'];
        $return[0]['attributes']['pagination']['limit'] = $page_limit;

        if ($filterObj['forGrid']) {
            return ['rows' => $results, 'total' => $results[0]['total_count']];
        }

        return $return;
    }

    /**
     * The method creates new entry in su_owners_reps table.
     *
     * @api-method addRepresentative
     *
     * @param array $rpcParams - the RPC parameters to fill the entry fields
     *                         {
     *                         #item string name            - the name of the representative
     *                         #item string surname         - the surname of the representative
     *                         #item string lastname        - the lastname of the representative
     *                         #item string egn             - the EGN (personal number) of the representative
     *                         #item string lk_nomer        - the number of the rep's ID Card
     *                         #item string lk_izdavane     - the rep's ID Card information
     *                         #item string iban            - the entity's IBAN (bank account number) (for both legal and physical enttities)
     *                         #item string address         - the entity's contact address (for both legal and physical enttities)
     *                         #item string rent_place      - the entity's location at which to receive the renumeration (for both legal and physical enttities)
     *                         }
     *
     * @return int $repId     - The unique ID of the newly added Representative
     */
    public function addRepresentative($rpcParams)
    {
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $options = [
            'tablename' => DEFAULT_DB_PREFIX . 'owners_reps',
            'mainData' => [
                'rep_name' => trim($rpcParams['rep_name']),
                'rep_surname' => trim($rpcParams['rep_surname']),
                'rep_lastname' => trim($rpcParams['rep_lastname']),
                'rep_egn' => trim($rpcParams['egn']),
                'rep_lk' => trim($rpcParams['lk_nomer']),
                'rep_lk_izdavane' => trim($rpcParams['lk_izdavane']),
                'iban' => trim($rpcParams['iban']),
                'rep_address' => trim($rpcParams['address']),
                'rep_phone' => trim($rpcParams['rep_phone']),
                'rent_place' => trim($rpcParams['rent_place']),
            ],
        ];
        $recordID = $UserDbController->addItem($options);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['request_params' => $rpcParams], ['created_rep_id' => $recordID], 'Adding representative');

        return $recordID;
    }

    /**
     * The method edits existing entry in su_owners_reps table.
     *
     * @api-method addRepresentative
     *
     * @param array $rpcParams - the RPC parameters to fill the entry fields
     *                         {
     *                         #item string name            - the name of the representative
     *                         #item string surname         - the surname of the representative
     *                         #item string lastname        - the lastname of the representative
     *                         #item string egn             - the EGN (personal number) of the representative
     *                         #item string lk_nomer        - the number of the rep's ID Card
     *                         #item string lk_izdavane     - the rep's ID Card information
     *                         #item string iban            - the entity's IBAN (bank account number) (for both legal and physical enttities)
     *                         #item string address         - the entity's contact address (for both legal and physical enttities)
     *                         #item string rent_place      - the entity's location at which to receive the renumeration (for both legal and physical enttities)
     *                         }
     * @param int $recordID - the id of the edited rep
     *
     * @return int $repId       - The unique ID of the newly added Representative
     */
    public function editRepresentative($rpcParams, $recordID)
    {
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        $FarmingController = new FarmingController('Farming');

        $options = [
            'tablename' => $UserDbController->DbHandler->tableOwnersReps,
            'return' => [
                '*',
            ],
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $recordID],
            ],
        ];

        $rep = $UserDbController->getItemsByParams($options);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableOwnersReps,
            'mainData' => [
                'rep_name' => trim($rpcParams['rep_name']),
                'rep_surname' => trim($rpcParams['rep_surname']),
                'rep_lastname' => trim($rpcParams['rep_lastname']),
                'rep_egn' => trim($rpcParams['egn']),
                'rep_lk' => trim($rpcParams['lk_nomer']),
                'rep_lk_izdavane' => trim($rpcParams['lk_izdavane']),
                'iban' => trim($rpcParams['iban']),
                'rep_address' => trim($rpcParams['address']),
                'rep_phone' => trim($rpcParams['rep_phone']),
                'rent_place' => trim($rpcParams['rent_place']),
            ],
        ];

        $options['where'] = ['id' => $recordID];

        $UserDbController->editItem($options);

        $molName = $rpcParams['rep_name'] . ' ' . $rpcParams['rep_surname'] . ' ' . $rpcParams['rep_lastname'];
        $updateMolParams = [
            'group_id' => $this->User->GroupID,
            'representative_id' => $recordID,
            'mol_name' => $molName,
            'mol_eng' => $rpcParams['egn'],
        ];
        $FarmingController->updateFarmingMol($updateMolParams);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['new_values' => $options], ['old_values' => $rep[0]], 'Editing rep');

        return $recordID;
    }

    /**
     * The method deletes the representative with ID = $rpcParams from the DB and all of
     * it's associated heritors from the respective DB Tables.
     *
     * @api-method deleteRepresentative
     *
     * @param int $rpcParams the unique owner ID to be deleted
     *
     * @throws MTRpcException
     */
    public function deleteRepresentative($repId)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);
        $UsersController = new UsersController('Users');

        $isRepresentingLegalEntities = $UserDbOwnersController->isRepresentingLegalEntities($repId);
        if ($isRepresentingLegalEntities) {
            throw new MTRpcException('REPRESENTING_LEGAL_ENTITITES', -33306);
        }
        // Getting the old values for log
        $options = [
            'tablename' => $UserDbController->DbHandler->tableOwnersReps,
            'return' => [
                '*',
            ],
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $repId],
            ],
        ];

        $repData = $UserDbController->getItemsByParams($options);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['deleted_rep_id' => $repId], ['old_values' => $repData[0]], 'Deleting representative');

        $UserDbOwnersController->replaceRepresentativeWithSelfRep($repId);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableOwnersReps,
            'id_name' => 'id',
            'id_string' => $repId,
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $repId],
            ],
        ];

        $UserDbController->deleteItemsByParams($options);
    }

    /**
     * Gets all the data, associated with current owner.
     *
     * @api-method markForEdit
     *
     * @param int $rpcParam - unique owner ID
     *
     * @return array - associative array with all owner data
     */
    public function markForEdit($rpcParam)
    {
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);

        $options = [
            'tablename' => $UserDbOwnersController->DbHandler->tableOwnersReps,
            'return' => [
                'DISTINCT (id)', "TRIM(rep_name) || ' ' || TRIM(rep_surname) || ' ' || TRIM(rep_lastname) as rep_owner_names",
                'TRIM(rep_name) as rep_name', 'TRIM(rep_surname) as rep_surname', 'TRIM(rep_lastname) as rep_lastname',
                'TRIM(rep_egn) as egn', 'TRIM(rep_lk) as lk_nomer', 'TRIM(rep_lk_izdavane) as lk_izdavane', 'TRIM(iban) as iban',
                'TRIM(rep_address) as address', 'TRIM(rep_phone) as rep_phone', 'TRIM(rent_place) as rent_place',
            ],
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $rpcParam],
            ],
        ];

        $result = $UserDbOwnersController->getItemsByParams($options, false, false);

        return $result[0];
    }

    /**
     * Информация дали е наследник избрания собственик и ако да на кого.
     *
     * @api-method getOwnerParents
     *
     * @param int $rpcParam - owner id
     *
     * @throws MTRpcException OWNER_IS_NOT_HERITOR -33302
     *
     * @return array
     */
    public function getOwnerParents($rpcParam)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $options = [
            'tablename' => $UserDbController->DbHandler->tableHeritors,
            'return' => [
                'subpath(path, 0, -1) as path',
            ],
            'where' => [
                'path' => ['column' => 'path', 'compare' => '~', 'value' => '*.' . $rpcParam],
            ],
        ];
        $results = $UserDbController->getItemsByParams($options);
        $resultsCount = count($results);
        if (0 == $resultsCount) {
            throw new MTRpcException('OWNER_IS_NOT_HERITOR', -33302);
        }

        for ($i = 0; $i < $resultsCount; $i++) {
            $allPaths[] = $results[$i]['path'];
        }

        usort($allPaths, function ($a, $b) {
            return strlen($a) - strlen($b);
        });
        $uniquePaths = $allPaths;
        $allPathsCount = count($allPaths);
        for ($i = 0; $i < $allPathsCount; $i++) {
            for ($j = $i + 1; $j < $allPathsCount; $j++) {
                if (strstr($allPaths[$j], $allPaths[$i]) && $j != $i) {
                    unset($uniquePaths[$i]);

                    break;
                }
            }
        }
        $uniquePaths = array_merge($uniquePaths);
        $uniquePathsCount = count($uniquePaths);
        $parentRootsArray = [];
        for ($i = 0; $i < $uniquePathsCount; $i++) {
            $parents = explode('.', $uniquePaths[$i]);
            $parentRootsArray[] = $parents[0];
        }

        return $parentRootsArray;
    }

    private function checkIfOwnerExists($recordID)
    {
        $options = [
            'return' => [
                'o.*',
            ],
            'where' => [
                'id' => ['column' => 'id', 'prefix' => 'o', 'compare' => '=', 'value' => $recordID],
            ],
        ];
        $counter = $UserDbOwnersController->getOwnersData($options, true, false);

        if (0 == $counter[0]['count']) {
            throw new MTRpcException('NON_EXISTING_OWNER_ID', -33206);
        }
    }

    private function checkIfEditIsPermitted($ownerId, $isDead)
    {
        $options = [
            'tablename' => $UserDbController->DbHandler->tableOwners,
            'return' => [
                '*',
            ],
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $ownerId],
            ],
        ];
        $owner = $UserDbController->getItemsByParams($options);
        $options = [
            'return' => [
                'h.id',
            ],
            'where' => [
                'path' => ['column' => 'path', 'compare' => '~', 'value' => $ownerId . '.*'],
            ],
        ];

        $counter = $UserDbOwnersController->getOwnersHeritors($options, true);

        if (0 == $counter[0]['count']) {
            return true;
        }

        if ($counter[0]['count'] > 0 && $owner[0]['is_dead'] && !$isDead) {
            throw new MTRpcException('ILLEGAL_OWNER_EDIT', -33304);
        }

        return false;
    }
}
