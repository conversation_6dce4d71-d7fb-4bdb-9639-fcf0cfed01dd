<?php

namespace TF\Engine\APIClasses\Owners;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDbOwners\UserDbOwnersController;

/**
 * дърво "Собственици".
 *
 * @rpc-module Owners
 *
 * @rpc-service-id owners-plots-grid
 */
class OwnersPlotsGrid extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readOwnersPlotsGrid'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * Рeads Owners Plots Grid - Собствени имоти.
     *
     * @api-method read
     *
     * @param array $rpcParams {
     *                         #item  integer $ownerId
     *                         #item  string $year
     *                         #item  array $ekate
     *                         #item  array $farm
     *                         }
     * @param int $page
     * @param int $rows
     * @param ?string $sort
     * @param ?string $order
     *
     * @return array {
     *               #item integer total
     *               #item array footer {
     *               #item string kad_ident
     *               #item string ownage
     *               }
     *               #item array {
     *               #item string c_date
     *               #item string c_num
     *               #item string category
     *               #item integer contract_id
     *               #item string due_date
     *               #item string ekate
     *               #item string farm_name
     *               #item integer farming_id
     *               #item integer gid
     *               #item integer id
     *               #item string kad_ident
     *               #item string land
     *               #item string ntp
     *               #item string ownage
     *               #item string owner
     *               #item float percent
     *               #item string start_date
     *               }
     *               }
     */
    public function readOwnersPlotsGrid(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);
        $FarmingController = new FarmingController('Farming');
        $arrayHelper = $FarmingController->ArrayHelper;

        $ownerId = $rpcParams['owner_id'];
        $year = $rpcParams['year'];
        $ekate = $rpcParams['ekate'];

        $userFarmings = $FarmingController->getUserFarmings();
        $userFarmingIds = array_keys($userFarmings);
        $farmingIds = $arrayHelper->filterEmptyStringArr($rpcParams['farm']);
        $farmingIds = isset($farmingIds) && count($farmingIds) > 0
            ? array_intersect($farmingIds, $userFarmingIds)
            : $userFarmingIds;

        $currentDate = date('Y-m-d', time());

        if ('' == $year) {
            $start_date = '9999-09-30';
            $due_date = '1970-10-01';
        } else {
            $start_date = $GLOBALS['Farming']['years'][$year]['year'] . '-09-30';
            $due_date = ($GLOBALS['Farming']['years'][$year]['year'] - 1) . '-10-01';
        }

        $options = [
            'custom_counter' => 'COUNT(DISTINCT(r.id))',
            'return' => [
                'DISTINCT(r.id)',
                "o.name || ' ' || o.surname || ' ' || o.lastname as owner",
                'p.kad_ident', '(r.contract_area * re.percent / 100) as ownage',
                'p.virtual_ntp_title as ntp', 'p.virtual_category_title as category', 'c.id as contract_id', 'p.gid', 'p.ekate', 'p.virtual_ekatte_name as land',
                'TO_CHAR(c.c_date, \'DD.MM.YYYY\') as c_date', 'TO_CHAR(c.start_date, \'DD.MM.YYYY\') as start_date', 'c.due_date', 'c.farming_id', 're.percent',
                "CASE WHEN c.is_annex = true then c.c_num ||' (Анекс)' else c.c_num end as c_num", 'c.active',
            ],
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
            'where' => [
                'owner_id' => ['column' => 'id', 'compare' => '=', 'prefix' => 'o', 'value' => $ownerId],
                'c_type' => ['column' => 'nm_usage_rights', 'compare' => '<>', 'prefix' => 'c', 'value' => 1],
                'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
                'is_heritor' => ['column' => 'is_heritor', 'compare' => '=', 'prefix' => 're', 'value' => 'FALSE'],
                'ekate' => ['column' => 'ekate', 'compare' => 'IN', 'prefix' => 'p', 'value' => $arrayHelper->filterEmptyStringArr($ekate)],
                'farming' => ['column' => 'farming_id', 'compare' => 'IN', 'prefix' => 'c', 'value' => $farmingIds],
            ],
            'start_date' => $start_date,
            'due_date' => $due_date,
        ];

        $counter = $UserDbOwnersController->getFullOwnerAreaReport($options, true, false);

        if (0 == $counter[0]['count']) {
            return [
                'rows' => [],
                'total' => 0,
                'footer' => [
                    [
                        'kad_ident' => '<b>ОБЩО</b>',
                        'ownage' => '',
                    ],
                ],
            ];
        }
        $options['return'][] = 'c.is_annex';
        $results = $UserDbOwnersController->getFullOwnerAreaReport($options, false, false);
        $resultsCount = count($results);
        $total_ownage = 0;
        $farmingOptions = [
            'return' => [
                'id',
                'name',
            ],
            'where' => [
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'value' => $this->User->GroupID],
            ],
        ];
        $farmingResult = $FarmingController->getFarmings($farmingOptions);
        $farmingCount = count($farmingResult);
        for ($i = 0; $i < $resultsCount; $i++) {
            for ($j = 0; $j < $farmingCount; $j++) {
                if ($farmingResult[$j]['id'] == $results[$i]['farming_id']) {
                    $results[$i]['farm_name'] = $farmingResult[$j]['name'];

                    break;
                }
            }
            $results[$i]['ownage'] = number_format($results[$i]['ownage'], 3, '.', '');

            if ($results[$i]['active']) {
                $results[$i]['active_text'] = (!$results[$i]['due_date'] || strtotime($results[$i]['due_date']) >= strtotime($currentDate)) ? 'Действащ' : 'Изтекъл';
                if (strtotime($results[$i]['start_date']) > strtotime($currentDate)) {
                    $results[$i]['active_text'] = 'Бъдещ';
                }
            } else {
                $results[$i]['active_text'] = 'Анулиран';
            }

            if (null != $results[$i]['due_date'] && '0000-00-00 00:00:00' != $results[$i]['due_date']) {
                $results[$i]['due_date'] = strftime('%d.%m.%Y', strtotime($results[$i]['due_date']));
            } else {
                $results[$i]['due_date'] = '-';
            }

            if ($results[$i]['active']) {
                if ('-' == $results[$i]['due_date']) {
                    $total_ownage += $results[$i]['ownage'];
                    $results[$i]['operational'] = true;
                } else {
                    $startDate = strtotime($results[$i]['start_date']);
                    $dueDate = strtotime($results[$i]['due_date']);
                    $today = strtotime(date('Y-m-d'));

                    if ($startDate <= $today && $today <= $dueDate) {
                        $total_ownage += $results[$i]['ownage'];
                        $results[$i]['operational'] = true;
                    }

                    if ($today > $dueDate) {
                        $results[$i]['operational'] = false;
                    }

                    if ($today < $startDate) {
                        $results[$i]['operational'] = true;
                    }
                }
            }
        }

        return [
            'rows' => $results,
            'total' => $counter[0]['count'],
            'footer' => [
                [
                    'kad_ident' => '<b>ОБЩО</b>',
                    'ownage' => number_format($total_ownage, 3, '.', ''),
                ],
            ],
        ];
    }
}
