<?php

namespace TF\Engine\APIClasses\GlobalNotifications;

use DateTime;
use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\GlobalNotification\GlobalNotificationController;

/**
 * Returns Templates grid information.
 *
 * @rpc-module GlobalNotifications
 *
 * @rpc-service-id global-notifications-grid
 */
// Prado::using('Plugins.Core.Notifications.*');
// Prado::using('Plugins.Core.UserDbPlots.*');
// Prado::using('Plugins.Core.Users.*');
// Prado::using('Plugins.Core.Users.conf');
// Prado::using('Plugins.Core.Users.conf');
// Prado::using('APIClasses.Plots.*');
// Prado::using('System.Security.TAuthManager');
// Prado::using('Plugins.Core.GlobalNotification.*');

class GlobalNotificationsMainGrid extends TRpcApiProvider
{
    private $NotificationController = false;
    private $UserDbController = false;
    private $module = 'GlobalNotifications';
    private $service_id = 'global-notifications-grid';

    public function registerMethods()
    {
        return [
            'getActiveNotClosedByUser' => ['method' => [$this, 'getActiveNotClosedByUser']],
            'close' => ['method' => [$this, 'close']],
            'read' => ['method' => [$this, 'getAll']],
            'add' => ['method' => [$this, 'add']],
            'getById' => ['method' => [$this, 'getById']],
            'edit' => ['method' => [$this, 'edit']],
        ];
    }

    public function getActiveNotClosedByUser()
    {
        $globalNotificationsController = new GlobalNotificationController('GlobalNotification');

        return $globalNotificationsController->getActiveNotClosedByUserId($this->User->userID);
    }

    public function close($notificationId)
    {
        $globalNotificationsController = new GlobalNotificationController('GlobalNotification');

        return $globalNotificationsController->close($this->User->userID, $notificationId);
    }

    public function getAll($args)
    {
        $globalNotificationsController = new GlobalNotificationController('GlobalNotification');

        $startDate = $args['start_time'] ? (new DateTime($args['start_time'])) : null;
        $endDate = $args['end_time'] ? (new DateTime($args['end_time'])) : null;

        if (!empty($startDate)) {
            $startDate = $startDate->setTime(0, 0, 0)->format('Y-m-d H:i:s');
        }
        if (!empty($endDate)) {
            $endDate = $endDate->setTime(23, 59, 59)->format('Y-m-d H:i:s');
        }

        return ['rows' => $globalNotificationsController->getAll($startDate, $endDate)];
    }

    public function add($data)
    {
        $globalNotificationsController = new GlobalNotificationController('GlobalNotification');

        return $globalNotificationsController->add($data);
    }

    public function getById($id)
    {
        $globalNotificationsController = new GlobalNotificationController('GlobalNotification');

        return $globalNotificationsController->getById($id);
    }

    public function edit($data)
    {
        $globalNotificationsController = new GlobalNotificationController('GlobalNotification');

        return $globalNotificationsController->edit($data);
    }
}
