<?php

namespace TF\Engine\APIClasses\GlobalNotifications;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\GlobalNotification\GlobalNotificationController;

/**
 * Returns Templates grid information.
 *
 * @rpc-module GlobalNotifications
 *
 * @rpc-service-id global-notifications-types
 */
class GlobalNotificationsTypes extends TRpcApiProvider
{
    private $module = 'GlobalNotifications';
    private $service_id = 'global-notifications-types';

    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'read']],
        ];
    }

    public function read()
    {
        $globalNotificationsController = new GlobalNotificationController('GlobalNotification');

        return $globalNotificationsController->getTypes();
    }
}
