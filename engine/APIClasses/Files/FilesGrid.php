<?php

namespace TF\Engine\APIClasses\Files;

use Prado\Web\Services\TRpcApiProvider;
use TF\Application\Common\Config;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Грид 'Файлове'.
 *
 * @rpc-module Files
 *
 * @rpc-service-id files
 */
class FilesGrid extends TRpcApiProvider
{
    private $module = 'Files';
    private $service_id = 'files';

    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getFilesGrid'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'deleteSelectedFiles' => ['method' => [$this, 'deleteSelectedFiles']],
            'endUpdate' => ['method' => [$this, 'endUpdate']],
            'checkForExistingOperations' => ['method' => [$this, 'checkForExistingOperations'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer farming
     *                         #item integer year
     *                         #item string name
     *                         }
     * @param int $page pagination parameter
     * @param int $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array
     */
    public function getFilesGrid(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $LayersController = new LayersController('Layers');
        $FarmingController = new FarmingController('Farming');

        $options = [
            'return' => ['f.name as farming', 't.year', 't.id', 't.name as name', 't.date_uploaded', 't.status', 't.shape_type, t.crs'],
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
            'where' => [
                'farming' => ['column' => 'farming', 'compare' => '=', 'prefix' => 't', 'value' => $rpcParams['farming']],
                'year' => ['column' => 'year', 'compare' => '=', 'prefix' => 't', 'value' => $rpcParams['year']],
                'name' => ['column' => 'filename', 'compare' => 'ILIKE', 'prefix' => 't', 'value' => $rpcParams['name']],
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'prefix' => 't', 'value' => $this->User->GroupID],
            ],
        ];

        $counter = $LayersController->getFiles($options, true, false);
        if (0 == $counter[0]['count']) {
            return [
                'rows' => [],
                'total' => 0,
            ];
        }

        $result = $LayersController->getFiles($options, false, false);
        $resultCount = count($result);

        for ($i = 0; $i < $resultCount; $i++) {
            $result[$i]['status_code'] = $result[$i]['status'];
            $result[$i]['shape_type_code'] = $result[$i]['shape_type'];
            $result[$i]['crs'] = $result[$i]['crs'];

            if (LOADING_FILE == $result[$i]['status']) {
                $result[$i]['status'] = 'Обработва се';
            } elseif (SUCCESSFULLY_TREATED == $result[$i]['status']) {
                $result[$i]['status'] = 'Успешно обработен';
            } elseif (ERROR_INVALID_SHAPE == $result[$i]['status']) {
                $result[$i]['status'] = 'Ненамерен Shape файл';
            } elseif (ERROR_INVALID_DBF == $result[$i]['status']) {
                $result[$i]['status'] = 'Ненамерен DBF файл';
            } elseif (ERROR_INVALID_ARCHIVE == $result[$i]['status']) {
                $result[$i]['status'] = 'Невалиден архив';
            } elseif (ERROR_INVALID_GEOMETRY == $result[$i]['status']) {
                $result[$i]['status'] = 'Невалиднa геометрия на полигон';
            } elseif (ERROR_INVALID_ISAK_FILE == $result[$i]['status']) {
                $result[$i]['status'] = 'Невалиден ИСАК файл';
            } elseif (ERROR_RUNTIME == $result[$i]['status']) {
                $result[$i]['status'] = 'Системнa грешка при обработката';
            } elseif (ERROR_INVALID_TABLE_STRUCTURE == $result[$i]['status']) {
                $result[$i]['status'] = 'Невалидна атрибутна информация';
            } elseif (ERROR_INVALID_FILE_DATA == $result[$i]['status']) {
                $result[$i]['status'] = 'Пресичане със съществуващи обекти';
            } elseif (ERROR_WAITING_DEFINITION == $result[$i]['status']) {
                $result[$i]['status'] = 'Очаква дефиниция';
            } elseif (ERROR_WAITING_COPYING == $result[$i]['status']) {
                $result[$i]['status'] = 'Копиране на данни';
            } elseif (ERROR_INVALID_CRS == $result[$i]['status']) {
                $result[$i]['status'] = 'Невалидна проекция';
            } elseif (ERROR_NOT_ALLOWED_ADDING == $result[$i]['status']) {
                $result[$i]['status'] = 'Непозволено добавяне на нови данни';
            } elseif (ERROR_INCORRECT_ENCODING == $result[$i]['status']) {
                $result[$i]['status'] = 'Проблем с енкодинга';
            } elseif (ERROR_INCORRECT_ENCODING_FIELD == $result[$i]['status']) {
                $result[$i]['status'] = 'Проблем с енкодинга в името на колона в таблицата';
            } elseif (ERROR_MISSING_COLUMN == $result[$i]['status']) {
                $result[$i]['status'] = 'Липсваща колона';
            } elseif (PARTIALLY_PROCESSED == $result[$i]['status']) {
                $result[$i]['status'] = 'Частично обработен';
            } elseif (NOT_UPDATED_CONTRACTS == $result[$i]['status']) {
                $result[$i]['status'] = 'Не актуализирани договори';
            } elseif (ERROR_GEOMETRY_COLLECTION == $result[$i]['status']) {
                $result[$i]['status'] = 'Грешка: Наличие на Geometry Collection!';
            } elseif (INCONSISTENT_FILE_TYPE == $result[$i]['status']) {
                $result[$i]['status'] = 'Несъответстващ тип на файла';
            } elseif (LOADING_FILE_NOW == $result[$i]['status']) {
                $result[$i]['status'] = 'Стартирана обработка ...';
            }

            if (null == $result[$i]['farming']) {
                $result[$i]['farming'] = '-';
            } else {
                $result[$i]['farming'] = $result[$i]['farming'] . ' / ' . $GLOBALS['Farming']['years'][$result[$i]['year']]['title'];
            }

            $result[$i]['date_uploaded'] = strftime('%d.%m.%Y %H:%M:%S', strtotime($result[$i]['date_uploaded']));

            foreach ($GLOBALS['Layers']['srid'] as $item) {
                if ($item['type'] == $result[$i]['shape_type']) {
                    $result[$i]['shape_type'] = $item['title'];
                }
            }
        }

        return [
            'rows' => $result,
            'total' => $counter[0]['count'],
        ];
    }

    /**
     * Изтриване на файл.
     *
     * @api-method deleteSelectedFiles
     *
     * @param array $rpcParams
     *                         {
     *                         #items int id
     *                         }
     */
    public function deleteSelectedFiles($rpcParams)
    {
        $rpcCount = count($rpcParams);
        $arrayID = [];
        for ($i = 0;$i < $rpcCount;$i++) {
            $arrayID[] = $rpcParams[$i]['id'];
        }

        if (!count($arrayID)) {
            return;
        }

        $LayersController = new LayersController('Layers');
        $UsersController = new UsersController('Users');
        $UserDbController = new UserDbController($this->User->Database);

        $LayersController->deleteLayersItems($arrayID, $this->User->GroupID, $this->User->Database);

        foreach ($rpcParams as $layer) {
            if (Config::LAYER_TYPE_KVS == $layer['shape_type_code']) {
                $UserDbController->deleteTmpKvsTable($layer['id']);
                $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $layer, [], 'Dropping tmp kvs table from file deletion.');
            }
        }

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $rpcParams, [], 'Delete Selected Files from table ' . DEFAULT_DB_PREFIX . 'users_files');
    }

    public function endUpdate($file_id, $force_update = false)
    {
        // init all needed controllers
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $options = [
            'return' => [
                'ekate',
            ],
            'tablename' => 'su_users_files',
            'where' => [
                'file_id' => ['column' => 'id', 'compare' => '=', 'value' => $file_id],
            ],
        ];
        $ekate = $UsersController->getItemsByParams($options, false, false);
        $ekate = $ekate[0]['ekate'];

        $options = [
            'tablename' => $UserDbController->DbHandler->tableKVS,
            'where' => [
                'ekate' => ['column' => 'ekate', 'compare' => '=', 'value' => $ekate],
                'waiting_update' => ['column' => 'waiting_update', 'compare' => '=', 'value' => true],
            ],
        ];
        $waitingUpdate = $UserDbController->getItemsByParams($options, true, false);

        $invalidGeoms[0]['count'] = 0;
        $tmpTableInvalid = 'layer_tmp_kvs_' . $ekate . '_invalid';
        $tableExists = $UserDbController->getTableNameExist($tmpTableInvalid);

        if ($tableExists) {
            $options = [
                'tablename' => $tmpTableInvalid,
            ];
            $invalidGeoms = $UserDbController->getItemsByParams($options, true, false);
        }

        if (false == $force_update && (0 != $waitingUpdate[0]['count'] || 0 != $invalidGeoms[0]['count'])) {
            throw new MTRpcException('end_update_kvs_not_updated_plots', -33250);
        }

        if ($tableExists) {
            $UserDbController->endUpdate($file_id, $ekate);
        }
    }

    /**
     * Проверавя дали за конкретните стопанска година, стопанство
     * и тип слой съществува незавършена заявка.
     *
     * @param array $rpcParams {
     *                         #item integer farming
     *                         #item integer year
     *                         #item integer type
     *                         }
     * @param ?string $sort
     * @param ?string $order
     *
     * @throws MTRpcException - ACTIVE_OPERATION_EXISTS (-33103) ако съществува активна заявка
     *
     * @return false - ако няма активни заявки
     */
    public function checkForExistingOperations(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $statusArray = [
            LOADING_FILE,
            PARTIALLY_PROCESSED,
            LOADING_FILE_NOW,
            ERROR_WAITING_DEFINITION,
            ERROR_WAITING_COPYING,
        ];

        $LayersController = new LayersController('Layers');

        $options = [
            'return' => ['f.name as farming', 't.year', 't.id', 't.name as name', 't.date_uploaded', 't.status', 't.shape_type, t.crs'],
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
            'where' => [
                'farming' => ['column' => 'farming', 'compare' => '=', 'prefix' => 't', 'value' => $rpcParams['farming']],
                'year' => ['column' => 'year', 'compare' => '=', 'prefix' => 't', 'value' => $rpcParams['year']],
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'prefix' => 't', 'value' => $this->User->GroupID],
                'status' => ['column' => 'status', 'compare' => 'IN', 'prefix' => 't', 'value' => $statusArray],
                'shape_type' => ['column' => 'shape_type', 'compare' => '=', 'prefix' => 't', 'value' => $rpcParams['type']],
            ],
        ];

        $results = $LayersController->getFiles($options, false, false);

        if (count($results)) {
            throw new MTRpcException('ACTIVE_OPERATION_EXISTS', -33103);
        }

        return false;
    }

    private function getPlace($where, $grid)
    {
        return [
            'place' => $where,
            'grid' => $grid,
            'page' => 'json-files',
        ];
    }
}
