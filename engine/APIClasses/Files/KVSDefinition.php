<?php

namespace TF\Engine\APIClasses\Files;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\Users\UsersController;

// Prado::using('Plugins.Core.Farming.conf');
// Prado::using('Plugins.Core.Farming.*');

/**
 * Полета за "КВС Дефиниция".
 *
 * @rpc-module Files
 *
 * @rpc-service-id definition
 */
class KVSDefinition extends TRpcApiProvider
{
    private $module = 'Files';
    private $service_id = 'definition';

    /**
     * Register RPC Methods.
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getKVSDefinition']],
            'saveDefinition' => ['method' => [$this, 'saveDefinition'],
                'validators' => [
                    'rpcParams' => [
                        'fullnumber' => 'validateText',
                        'ekatte' => 'validateText',
                        'masiv' => 'validateText',
                        'number' => 'validateText',
                        'category' => 'validateText',
                        'waytouse' => 'validateText',
                        'mestnost' => 'validateText',
                        'document_area' => 'validateText',
                    ],
                ],
            ],
            'saveKMSDefinition' => ['method' => [$this, 'saveKMSDefinition']],
            'saveForIsakDefinition' => ['method' => [$this, 'saveForIsakDefinition']],
            'saveForWorkDefinition' => ['method' => [$this, 'saveForWorkDefinition']],
            'saveForGpsDefinition' => ['method' => [$this, 'saveForGpsDefinition']],
        ];
    }

    /**
     * @api-method read
     *
     * @return array
     */
    public function getKVSDefinition($rpcParams)
    {
        $FarmingController = new FarmingController('Farming');
        $LayersController = new LayersController('Layers');
        $UserDbController = new UserDbController($this->User->Database);

        if (5 == $rpcParams['layerType']) {
            $data = $UserDbController->getKVSDefinition($this->User->Database, $rpcParams['fileId']);
        } elseif (4 == $rpcParams['layerType'] || 6 == $rpcParams['layerType'] || 9 == $rpcParams['layerType']) {
            $fileData = $LayersController->getFilesDataById($rpcParams['fileId']);
            $options = [
                'farming' => $fileData[0]['farming'],
                'year' => $fileData[0]['year'],
                'group_id' => $fileData[0]['group_id'],
                'layer_type' => $rpcParams['layerType'],
            ];
            $tmpTableName = 'tmp_' . $LayersController->getTableNameByParams($options) . '_' . $rpcParams['fileId'];
            $data = $UserDbController->getKMSDefinition($this->User->Database, $tmpTableName);
        } elseif (19 == $rpcParams['layerType']) {
            $data = $UserDbController->getWorkDefinition($this->User->Database, 'tmp_work_' . $rpcParams['fileId']);
        } elseif (2 == $rpcParams['layerType']) {
            $data = $UserDbController->getWorkDefinition($this->User->Database, 'tmp_geom_' . $rpcParams['fileId']);
        }

        $dataCount = count($data);
        $return_data = [];
        for ($i = 0; $i < $dataCount; $i++) {
            if (!in_array($data[$i]['column_name'], $GLOBALS['Farming']['system_columns'])) {
                $return_data[] = $data[$i];
            }
        }
        $return_data[0]['selected'] = true;

        return $return_data;
    }

    /**
     * КВС Дефиниция.
     *
     * @api-method saveDefinition
     *
     * @param array $rpcParams
     *                         {
     *                         #item string fullnumber
     *                         #item string ekatte
     *                         #item string masiv
     *                         #item string number
     *                         #item string category
     *                         #item string waytouse
     *                         #item string mestnost
     *                         #item string document_area
     *                         }
     */
    public function saveDefinition($rpcParams)
    {
        $LayersController = new LayersController('Layers');
        $UsersController = new UsersController('Users');

        if ($rpcParams['definition_full_number_check']) {
            $array['fullnumber'] = $rpcParams['fullnumber'];
        }
        if ($rpcParams['definition_ekatte_check']) {
            $array['ekatte'] = $rpcParams['ekatte'];
        }
        if ($rpcParams['definition_masiv_check']) {
            $array['masiv'] = $rpcParams['masiv'];
        }
        if ($rpcParams['definition_number_check']) {
            $array['number'] = $rpcParams['number'];
        }
        if ($rpcParams['definition_category_check']) {
            $array['category'] = $rpcParams['category'];
        }
        if ($rpcParams['definition_waytouse_check']) {
            $array['waytouse'] = $rpcParams['waytouse'];
        }
        if ($rpcParams['definition_mestnost_check']) {
            $array['mestnost'] = $rpcParams['mestnost'];
        }
        if ($rpcParams['definition_document_area_check']) {
            $array['document_area'] = $rpcParams['document_area'];
        }

        $options = [];
        $options['mainData'] = [
            'definition' => serialize($array),
            'status' => 11,
        ];

        $options['id'] = (int)$rpcParams['layer']['id'];
        $LayersController->editItemFiles($options);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options, [], 'KVS definition');
    }

    /**
     * КМС Дефиниция.
     *
     * @api-method saveKMSDefinition
     *
     * @param array $rpcParams
     *                         {
     *                         #item string fullnumber
     *                         #item string ekatte
     *                         #item string masiv
     *                         #item string number
     *                         #item string category
     *                         #item string waytouse
     *                         #item string mestnost
     *                         #item string document_area
     *                         }
     */
    public function saveKMSDefinition($rpcParams)
    {
        $LayersController = new LayersController('Layers');
        $UsersController = new UsersController('Users');

        $array['number'] = $rpcParams['number'];

        if ($rpcParams['ekatte_check']) {
            $array['ekatte'] = $rpcParams['ekatte'];
        }
        if ($rpcParams['crop_name_check']) {
            $array['crop_name'] = $rpcParams['crop_name'];
        }
        if ($rpcParams['crop_code_check']) {
            $array['crop_code'] = $rpcParams['crop_code'];
        }

        $options = [];
        $options['mainData'] = [
            'definition' => serialize($array),
            'status' => 11,
        ];

        $options['id'] = (int)$rpcParams['layer']['id'];
        $LayersController->editItemFiles($options);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options, [], 'KMS definition');
    }

    /**
     * КМС Дефиниция.
     *
     * @api-method saveForIsakDefinition
     *
     * @param array $rpcParams
     *                         {
     *                         #item string fullnumber
     *                         #item string ekatte
     *                         #item string masiv
     *                         #item string number
     *                         #item string category
     *                         #item string waytouse
     *                         #item string mestnost
     *                         #item string document_area
     *                         }
     */
    public function saveForIsakDefinition($rpcParams)
    {
        $LayersController = new LayersController('Layers');
        $UsersController = new UsersController('Users');

        if ($rpcParams['number_check']) {
            $array['number'] = $rpcParams['number'];
        }
        if ($rpcParams['ekatte_check']) {
            $array['ekatte'] = $rpcParams['ekatte'];
        }
        if ($rpcParams['crop_name_check']) {
            $array['crop_name'] = $rpcParams['crop_name'];
        }
        if ($rpcParams['crop_code_check']) {
            $array['crop_code'] = $rpcParams['crop_code'];
        }

        $options = [];
        $options['mainData'] = [
            'definition' => serialize($array),
            'status' => 11,
        ];

        $options['id'] = (int)$rpcParams['layer']['id'];
        $LayersController->editItemFiles($options);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options, [], 'for ISAK definition');
    }

    public function saveForWorkDefinition($rpcParams)
    {
        $LayersController = new LayersController('Layers');
        $UsersController = new UsersController('Users');

        if ($rpcParams['crop_name_check']) {
            $array['crop_name'] = $rpcParams['crop_name'];
        }

        $options = [];
        $options['mainData'] = [
            'definition' => serialize($array),
            'status' => 11,
        ];

        $options['id'] = (int)$rpcParams['layer']['id'];
        $LayersController->editItemFiles($options);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options, [], 'for ISAK definition');
    }

    public function saveForGpsDefinition($rpcParams)
    {
        $LayersController = new LayersController('Layers');
        $UsersController = new UsersController('Users');

        if ($rpcParams['crop_name_check']) {
            $array['crop_name'] = $rpcParams['crop_name'];
        }

        $options = [];
        $options['mainData'] = [
            'definition' => serialize($array),
            'status' => 11,
        ];

        $options['id'] = (int)$rpcParams['layer']['id'];
        $LayersController->editItemFiles($options);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options, [], 'for ISAK definition');
    }
}
