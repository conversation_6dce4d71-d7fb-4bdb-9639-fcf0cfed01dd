<?php

namespace TF\Engine\APIClasses\System;

use Exception;
use Prado\Web\Services\TJsonResponse;
use TF\Application\Entity\RequestedEkatte;

/**
 * @json-module System
 *
 * @json kvs-status
 */
class KvsStatus extends TJsonResponse
{
    public function getJsonContent()
    {
        if ('POST' !== $_SERVER['REQUEST_METHOD']) {
            throw new Exception('Request method not allowed');
        }

        $requestedEkatteUuid = $_POST['uuid'];
        $requestedEkatte = RequestedEkatte::finder()->find('kvs_store_uuid = :uid', [':uid' => $requestedEkatteUuid]);
        if (!$requestedEkatte) {
            throw new Exception('No ekatte relation found!');
        }

        $status = $_POST['status'];
        if (!in_array($status, [RequestedEkatte::STATUS_REQUESTED, RequestedEkatte::STATUS_FOR_SYNC, RequestedEkatte::STATUS_RECEIVED, RequestedEkatte::STATUS_FAILED])) {
            throw new Exception('Invalid status');
        }

        try {
            $requestedEkatte->status = $status;
            $requestedEkatte->save();
        } catch (Exception $ex) {
            throw $ex;
        }
    }
}
