<?php

namespace TF\Engine\APIClasses\System;

use Exception;
use Prado;
use Prado\Web\Services\TJsonResponse;
use TF\Application\Entity\RequestedEkatte;
use TF\Application\Entity\User;
use TF\Engine\Plugins\Core\Layers\LayersController;

/**
 * @json-module System
 *
 * @json kvs-store
 */
class KvsStore extends TJsonResponse
{
    public function getJsonContent()
    {
        try {
            if ('POST' != $_SERVER['REQUEST_METHOD']) {
                throw new Exception('Request method not allowed');
            }

            $requestedEkatteUuid = $_POST['uuid'];
            $requestedEkatte = RequestedEkatte::finder()->find('kvs_store_uuid = :uid', [':uid' => $requestedEkatteUuid]);
            if (!$requestedEkatte) {
                throw new Exception('No ekatte relation found!');
            }

            $user = User::finder()->findByPk($requestedEkatte->group_id);

            $groupId = $user->getGroupId();
            $idprojection = (int) $_POST['projection'];

            foreach ($GLOBALS['Layers']['srid'] as $item) {
                if ($item['srid'] == $idprojection) {
                    $type = $item['type'];
                }
            }

            $fname = pathinfo($_FILES['zip_file']['name']);
            $fields['name'] = $fname['filename'];
            $fields['user_id'] = $user->getId();
            $fields['filename'] = $user->getGroupId() . '_' . $_FILES['zip_file']['name'];
            $fields['shape_type'] = $type;
            $fields['group_id'] = $user->getGroupId();
            $fields['add_to_existing'] = 'false';
            $fields['ekate'] = $requestedEkatte->ekatte_code;
            $settings['mainData'] = $fields;

            $LayersController = new LayersController();
            $fileId = $LayersController->addFilesItem($settings);

            $ext_pos = strrpos($fields['filename'], '.');
            $extention = substr($fields['filename'], $ext_pos);

            if ('POST' == $_SERVER['REQUEST_METHOD']) {
                // Check if the form was submitted with a file
                if (isset($_FILES['zip_file'])) {
                    $targetDirectory = LAYERS_QUEUE_PATH . $groupId;

                    if (!is_dir($targetDirectory)) {
                        mkdir($targetDirectory, 0700, true);
                    }

                    $targetFile = LAYERS_QUEUE_PATH . $groupId . DIRECTORY_SEPARATOR . $groupId . '_' . $fileId . $extention;

                    // Move the uploaded file to the target directory
                    if (copy($_FILES['zip_file']['tmp_name'], $targetFile)) {
                        $options = [];
                        $options['mainData'] = [
                            'filename' => $groupId . '_' . $fileId . $extention,
                        ];

                        $options['id'] = $fileId;
                        $LayersController->editItemFiles($options);
                        @unlink($_FILES['zip_file']['tmp_name']);

                        $requestedEkatte->status = RequestedEkatte::STATUS_RECEIVED;
                        $requestedEkatte->save();

                        /** @var KvsOszProcessingClass $objOszKvsProcessing */
                        $objOszKvsProcessing = Prado::getApplication()->getModule('KvsOszProcessingClass');
                        $objOszKvsProcessing->initData();
                        $objOszKvsProcessing->startProcessing();
                    }
                }
            }
        } catch (Exception $ex) {
            throw $ex;
        }
    }
}
