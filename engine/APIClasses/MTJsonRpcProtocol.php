<?php

namespace TF\Engine\APIClasses;

use Exception;
use Prado\Exceptions\TDbException;
use Prado\Exceptions\THttpException;
use <PERSON>rado\Web\Services\TJsonRpcProtocol;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Kernel\Sentry\Sentry;
use TF\Engine\Kernel\Validation;

class MTJsonRpcProtocol extends TJsonRpcProtocol
{
    /**
     * Handles the RPC request.
     *
     * @param string $requestPayload
     *
     * @return string JSON RPC response
     */
    public function callMethod($requestPayload)
    {
        try {
            $_request = $this->decode($requestPayload);
            if (isset($_request['jsonrpc'])) {
                $this->_specificationVersion = $_request['jsonrpc'];
                if ($this->_specificationVersion > 2.0) {
                    throw new MTRpcException('Unsupported specification version', '-32600');
                }
            }

            if (isset($_request['id'])) {
                $this->_id = $_request['id'];
            }

            if (!isset($_request['method'])) {
                throw new MTRpcException('Missing request method', '-32600');
            }

            if (!isset($_request['params'])) {
                $parameters = [];
            } else {
                $parameters = $_request['params'];
            }

            if (!is_array($parameters)) {
                $parameters = [$parameters];
            }

            $ret = $this->callApiMethod($_request['method'], $parameters);
            // a request without an id is a notification that doesn't need a response
            if (null !== $this->_id) {
                if (2.0 == $this->_specificationVersion) {
                    return $this->encode([
                        'jsonrpc' => '2.0',
                        'id' => $this->_id,
                        'result' => $ret,
                    ]);
                }

                return $this->encode([
                    'id' => $this->_id,
                    'result' => $this->callApiMethod($_request['method'], $_request['params']),
                    'error' => null,
                ]);
            }
        } catch (MTRpcException $e) {
            return $this->createCustomErrorResponse($e);
        } catch (THttpException $e) {
            Sentry::logException($e);

            throw new THttpException($e->getCode(), self::createEnvMessage($e));
        } catch (TDbException $e) {
            Sentry::logException($e);

            return $this->createCustomErrorResponse(new MTRpcException(self::createEnvMessage($e), '-33101'));
        } catch (Exception $e) {
            Sentry::logException($e);

            return $this->createCustomErrorResponse(new MTRpcException(self::createEnvMessage($e), '-32603'));
        }
    }

    public function callApiMethod($methodName, $parameters)
    {
        if (!isset($this->rpcMethods[$methodName])) {
            throw new MTRpcException('Method "' . $methodName . '" not found', '-32601');
        }

        if (null === $parameters) {
            $parameters = [];
        }

        if (!is_array($parameters)) {
            $parameters = [$parameters];
        }

        if (!empty($this->rpcMethods[$methodName]['validators'])) {
            $validationRules = $this->rpcMethods[$methodName]['validators'];
        }

        if (isset($validationRules)) {
            Validation::runValidation($validationRules, $parameters);
        }

        return call_user_func_array($this->rpcMethods[$methodName]['method'], $parameters);
    }

    public function createCustomErrorResponse(MTRpcException $exception)
    {
        if (2.0 == $this->_specificationVersion) {
            return $this->encode([
                'id' => $this->_id,
                'result' => null,
                'error' => [
                    'code' => $exception->getErrorCodeNumber(),
                    'message' => $exception->getCustomErrorMessage(),
                    'data' => $exception->getErrorFieldName(),
                ],
            ]);
        }

        return $this->encode([
            'id' => $this->_id,
            'error' => [
                'code' => $exception->getErrorCodeNumber(),
                'message' => $exception->getCustomErrorMessage(),
                'data' => $exception->getErrorFieldName(),
            ],
        ]);
    }

    private static function createEnvMessage(Exception $e): string
    {
        $message = 'System Error. Please try again later.';
        if ('Debug' === getenv('APPLICATION_MODE')) {
            $message = $e->getMessage();
        }

        return $message;
    }
}
