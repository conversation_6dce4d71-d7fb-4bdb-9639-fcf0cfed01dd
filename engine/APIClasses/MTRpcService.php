<?php

namespace TF\Engine\APIClasses;

use Prado\Exceptions\TConfigurationException;
use Prado\Web\Services\TRpcProtocol;
use Prado\Web\Services\TRpcService;

class MTRpcService extends TRpcService
{
    public const BASE_API_PROVIDER = 'Prado\Web\Services\TRpcApiProvider';

    protected $protocolHandlers = [
        'application/json' => 'TF\Engine\APIClasses\MTJsonRpcProtocol',
        'application/json; charset=UTF-8' => 'TF\Engine\APIClasses\MTJsonRpcProtocol',
        'text/xml' => 'TXmlRpcProtocol',
    ];

    public function createApiProvider(TRpcProtocol $protocolHandler, $providerId)
    {
        $_properties = $this->apiProviders[$providerId];

        if (($_providerClass = $_properties->remove('class')) === null) {
            throw new TConfigurationException('rpcservice_apiprovider_required');
        }

        $_providerClassName = $_providerClass;
        if (!is_subclass_of($_providerClassName, self::BASE_API_PROVIDER, true)) {
            throw new TConfigurationException('rpcservice_apiprovider_invalid');
        }

        if (($_rpcServerClass = $_properties->remove('server')) === null) {
            $_rpcServerClass = self::BASE_RPC_SERVER;
        }

        $_rpcServerClassName = $_rpcServerClass;
        if (self::BASE_RPC_SERVER !== $_rpcServerClassName && !is_subclass_of($_rpcServerClassName, self::BASE_RPC_SERVER)) {
            throw new TConfigurationException('rpcservice_rpcserver_invalid');
        }

        $_apiProvider = new $_providerClassName(new $_rpcServerClassName($protocolHandler));
        $_apiProvider->setId($providerId);

        foreach ($_properties as $_key => $_value) {
            $_apiProvider->setSubProperty($_key, $_value);
        }

        return $_apiProvider;
    }
}
