<?php

namespace TF\Engine\APIClasses\KVSInvalidGeometry;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * @rpc-module KVSInvalidGeometry
 *
 * @rpc-service-id kvs-invalid-geometry-rpc
 *
 * @property UserDbController $UserDbController
 * @property LayersController $LayersController
 * @property UsersController $UsersController
 * @property FarmingController $FarmingController
 */
class KVSInvalidGeometryPlotsGrid extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'read'],
                'validators' => [
                    'fileId' => 'validateInteger',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * Reads KVS Invalid Geometry Plots.
     *
     * @api-method read
     *
     * @param int $page pagination parameter
     * @param int $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array {
     *               #item integer total
     *               #item array rows {
     *               #item string fr_gid,
     *               #item string arr_geom
     *               #item string ekatte
     *               #item string kvs_no
     *               #item string kad_no
     *               #item string pl_dka
     *               #item string kategoria
     *               #item string kod_ntp
     *               #item string kod_sobstv
     *               #item string is_system
     *               }
     *               #item array footer {
     *               #item string kad_no,
     *               #item string pl_dka,
     *               }
     *               }
     */
    public function read(int $fileId, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $return = [
            'rows' => [],
            'total' => 0,
            'footer' => [
                [
                    'kad_no' => '<b>ОБЩО</b>',
                    'pl_dka' => '',
                ],
            ],
        ];

        // init all needed controllers
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        $LayersController = new LayersController('Layers');
        $FarmingController = new FarmingController('Farming');

        $result = $LayersController->getFilesDataById($fileId);

        $ekate = $result[0]['ekate'];

        if (!$ekate) {
            return $return;
        }

        $tmpTable = 'layer_tmp_kvs_' . $ekate;
        $tmpTableInvalid = $tmpTable . '_invalid';

        $tableExists = $UserDbController->getTableNameExist($tmpTableInvalid);
        if (!$tableExists) {
            return $return;
        }

        $options = [
            'return' => [
                'i.fr_gid', "array_agg(i.id || '-' ||ST_ASTEXT(i.geom)) as arr_geom", 'tmp.ekatte', 'tmp.kvs_no', 'tmp.kad_no',
                'round((tmp.pl_dka)::numeric, 3) as pl_dka', 'tmp.kategoria', 'tmp.kod_ntp', 'tmp.kod_sobstv', 'tmp.is_system',
            ],
            'tmpTable' => $tmpTable,
            'tmpTableInvalid' => $tmpTableInvalid,
            'sort' => $sort,
            'order' => $order,
            'group' => 'i.fr_gid,tmp.ekatte,tmp.kvs_no,tmp.kad_no,tmp.pl_dka,tmp.kategoria,tmp.kod_ntp,tmp.kod_sobstv,tmp.is_system',
        ];

        $results_total = $UserDbController->getInvalidKvsOszPlots($options, false, false);
        $counter = count($results_total);

        if (0 == $counter) {
            return $return;
        }

        $options['offset'] = ($page - 1) * $rows;
        $options['limit'] = $rows;

        $result = $UserDbController->getInvalidKvsOszPlots($options, false, false);
        $resultCount = count($result);
        $total_pl_dka = 0;
        for ($i = 0; $i < $resultCount; $i++) {
            $total_pl_dka += $result[$i]['pl_dka'];
        }

        $return['rows'] = $result;
        $return['total'] = $counter;
        $return['footer'] = [
            [
                'kad_no' => '<b>ОБЩО</b>',
                'pl_dka' => number_format($total_pl_dka, 3, '.', ''),
            ],
        ];

        return $return;
    }
}
