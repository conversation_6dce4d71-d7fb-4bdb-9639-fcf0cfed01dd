<?php

namespace TF\Engine\APIClasses\KVSInvalidGeometry;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * @rpc-module KVSInvalidGeometry
 *
 * @rpc-service-id kvs-invalid-geometry-rpc
 */
class KVSInvalidGeometryMapTools extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'saveKvsOszInvalidPlotsChanges' => ['method' => [$this, 'saveKvsOszInvalidPlotsChanges']],
        ];
    }

    /**
     * Save Kvs Osz Invalid Plots Changes.
     *
     * @api-method saveKvsOszInvalidPlotsChanges
     *
     * @param array $rpcParam
     *                        {
     *                        #item string ekatte
     *                        #item geom geometry
     *                        #item string kvs_no
     *                        #item string kad_no
     *                        #item string kategoria
     *                        #item string kod_ntp
     *                        #item string kod_sobstv
     *                        #item boolean is_system
     *                        }
     *
     * @throws Exception
     */
    public function saveKvsOszInvalidPlotsChanges($rpcParam)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $LayersController = new LayersController('Layers');
        $UsersController = new UsersController('Users');

        $recordID = $UserDbController->saveKvsOszInvalidPlotsChanges($rpcParam);

        if ($recordID) {
            $options = [
                'tablename' => 'layer_tmp_kvs_' . $rpcParam['ekatte'],
                'return' => [
                    'gid',
                ],
                'where' => [
                    'geom' => ['column' => 'ST_IsValid(geom)', 'compare' => '=', 'value' => 'TRUE'],
                    'gid' => ['column' => 'gid', 'compare' => '=', 'value' => $recordID],
                ],
            ];

            $result = $UserDbController->getItemsByParams($options, false, false);

            // if we add valid geom
            if ($result[0]['gid']) {
                // remove from tmp table
                $options = [
                    'tablename' => 'layer_tmp_kvs_' . $rpcParam['ekatte'],
                    'id_string' => $rpcParam['fr_gid'],
                    'id_name' => 'gid',
                ];
                $UserDbController->deleteItemsByParams($options);

                // remove from tmp_invalid table
                $options = [
                    'tablename' => 'layer_tmp_kvs_' . $rpcParam['ekatte'] . '_invalid',
                    'id_string' => $rpcParam['fr_gid'],
                    'id_name' => 'fr_gid',
                ];
                $UserDbController->deleteItemsByParams($options);
            } else {
                // if the the new geom is invalid

                // remove the new geom from tmp table
                $options = [
                    'tablename' => 'layer_tmp_kvs_' . $rpcParam['ekatte'],
                    'id_string' => $recordID,
                    'id_name' => 'gid',
                ];
                $UserDbController->deleteItemsByParams($options);
            }
        }

        $missingPlots = [];
        // REFRESH VIEW
        $kvsContractsUpdateView = 'kvs_contracts_update_' . $rpcParam['ekatte'];
        $missingPlots = $UserDbController->refreshAndSelectView($kvsContractsUpdateView);

        // Update status в su_users_files на 1 - "Успешно обработен" или "Не актуализирани договори" ако няма вече записи в tmp_invalid table
        $options = [
            'tablename' => 'layer_tmp_kvs_' . $rpcParam['ekatte'] . '_invalid',
            'return' => [
                '*',
            ],
        ];

        $result = $UserDbController->getItemsByParams($options, true, false);

        if (0 == $result[0]['count'] && $rpcParam['file_id']) {
            if (count($missingPlots)) {
                // Status: Не актуализирани договори
                $LayersController->setFilesProcessingStatus($rpcParam['file_id'], NOT_UPDATED_CONTRACTS);

                // Drop tmp_invalid table
                $UserDbController->dropTableKvsEkatteInvalid('layer_tmp_kvs_' . $rpcParam['ekatte']);
            } else {
                // Status: Успешно обработен
                $UserDbController->endUpdate($rpcParam['file_id'], $rpcParam['ekatte']);
            }
        }
    }
}
