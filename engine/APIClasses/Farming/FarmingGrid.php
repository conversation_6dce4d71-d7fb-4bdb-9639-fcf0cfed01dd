<?php

namespace TF\Engine\APIClasses\Farming;

use DateTime;
use Exception;
use InvalidArgumentException;
use Prado\Web\Services\TRpcApiProvider;
use Prado\Web\Services\TRpcServer;
use TF\Application\Common\Config;
use TF\Application\Entity\LayerStyles;
use TF\Application\Entity\ObjectPermissions;
use TF\Application\Entity\User;
use TF\Application\Entity\UserFarmings;
use TF\Application\Providers\ObjectPermissionsProvider;
use TF\Application\Providers\UserFarmingsProvider;
use TF\Engine\APIClasses\Users\UsersMainGrid;
use TF\Engine\Kernel\Export2XlsClass;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbContracts\UserDbContractsController;
use TF\Engine\Plugins\Core\UserDbOwners\UserDbOwnersController;
use TF\Engine\Plugins\Core\UserDbPayments\UserDbPaymentsController;
use TF\Engine\Plugins\Core\Users\UsersController;
use ZipArchive;

/**
 * MTSoapService class file.
 *
 * <AUTHOR> Ltd. <<EMAIL>>
 *
 * @link http://www.devision.bg/
 *
 * @copyright Copyright &copy; 2008 Devision Ltd.
 */

/**
 * Настройки > Стопанства.
 *
 * @rpc-module Farming
 *
 * @rpc-service-id farming-grid
 *
 * @property FarmingController $FarmingController
 * @property UserDbController $UserDbController
 * @property UserDbContractsController $UserDbContractsController
 * @property UserDbOwnersController $UserDbOwnersController
 * @property UserDbPaymentsController $UserDbPaymentsController
 * @property LayersController $LayersController
 * @property UsersController $UsersController
 */
class FarmingGrid extends TRpcApiProvider
{
    /**
     * ********************************************************************************
     * Properties.
     * ********************************************************************************
     */
    private $module = 'Farming';
    private $service_id = 'farming-grid';
    private $default_label_size = 8;
    private $farmProvider;

    public function __construct(TRpcServer $rpcServer)
    {
        parent::__construct($rpcServer);

        $this->farmProvider = new UserFarmingsProvider();
    }

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readFarmingGridInfo'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'add' => ['method' => [$this, 'addNewFarming'],
                'validators' => [
                    'name' => 'validateText',
                    'company' => 'validateText',
                    'bulstat' => 'validateText',
                    'address' => 'validateText',
                    'company_address' => 'validateText',
                    'mol' => 'validateText',
                    'mol_egn' => 'validateText',
                ]],
            'delete' => ['method' => [$this, 'deleteSelectedFarming']],
            'markForEdit' => ['method' => [$this, 'markForEdit']],
            'edit' => ['method' => [$this, 'editSelectedFarming'],
                'validators' => [
                    'name' => 'validateText',
                    'company' => 'validateText',
                    'bulstat' => 'validateText',
                    'address' => 'validateText',
                    'company_address' => 'validateText',
                    'mol' => 'validateText',
                    'mol_egn' => 'validateText',
                ]],
            'deteleZipArchive' => ['method' => [$this, 'deteleZipArchive']],
            'export2xls' => ['method' => [$this, 'exportToExcel']],
            'getFarmingUsers' => ['method' => [$this, 'getFarmingUsers']],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item boolean for-tree
     *                         #item boolean combobox
     *                         }
     * @param int $page RPC pagination parameters
     * @param int $rows RPC pagination parameters
     * @param string $sort RPC pagination parameters
     * @param string $order RPC pagination parameters
     *
     * @return array
     */
    public function readFarmingGridInfo(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $FarmingController = new FarmingController('Farming');
        $userFarmings = $FarmingController->getUserFarmings();
        $farmingIds = array_keys($userFarmings);

        $options = [
            'return' => ['id', 'uuid', 'name', 'company', 'bulstat', 'is_system', 'address', 'company_address', 'mol', 'mol_egn', 'farming_mol_phone', 'iban_arr', 'post_payment_fields', 'company_ekatte'],
            'sort' => $sort,
            'order' => $order,
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'where' => [
                'id' => ['column' => 'id', 'compare' => 'IN', 'value' => $farmingIds],
            ],
        ];

        if (isset($rpcParams['uuid'])) {
            $options['where'] = [
                'uuid' => ['column' => 'uuid', 'compare' => '=', 'value' => $rpcParams['uuid']],
            ];
            $options['offset'] = 0;
            $options['limit'] = 1;
        }

        $return = [];
        $counter = $FarmingController->getFarmings($options, true, false);
        $result = $FarmingController->getFarmings($options, false, false);
        $resultCount = count($result);

        if ($rpcParams['combobox']) {
            for ($i = 0; $i < $resultCount; $i++) {
                $return[$i]['name'] = $result[$i]['name'];
                $return[$i]['id'] = $result[$i]['id'];
                if (0 == $i) {
                    $return[$i]['selected'] = 'true';
                }
            }
        } elseif (!$rpcParams['for-tree']) {
            for ($i = 0; $i < $resultCount; $i++) {
                $ibans = array_map(function ($bank) {
                    if (!empty($bank['iban']) && !empty($bank['name']) && !empty($bank['bank_branch'])) {
                        return $bank['name'] . ': ' . $bank['iban'] . ': ' . $bank['bank_branch'];
                    } elseif (!empty($bank['iban']) && !empty($bank['name'])) {
                        return $bank['name'] . ': ' . $bank['iban'];
                    } elseif (!empty($bank['iban'])) {
                        return $bank['iban'];
                    }
                }, json_decode($result[$i]['iban_arr'], true));

                $result[$i]['iban_text'] = implode('<br>', $ibans);
                $postPaymentFields = json_decode($result[$i]['post_payment_fields'], true);
                $result[$i]['post_payment_fields_text'] = implode('<br>', array_values($postPaymentFields));
            }
            $return['total'] = $counter[0]['count'];
            $return['rows'] = $result;
        } else {
            $array = [];

            for ($i = 0; $i < $resultCount; $i++) {
                $array[$i]['id'] = $result[$i]['id'];
                $array[$i]['text'] = $result[$i]['name'];
                $array[$i]['attributes']['type'] = 'farming';

                if (0 == $i) {
                    $array[$i]['state'] = 'open';
                } else {
                    $array[$i]['state'] = 'closed';
                }

                $child = [];
                $j = 0;
                foreach ($GLOBALS['Farming']['years'] as $item) {
                    $child[$j]['id'] = 'y-' + $item['id'];
                    $child[$j]['text'] = $item['title'];
                    $child[$j]['attributes']['type'] = 'year';
                    $child[$j]['iconCls'] = 'tree-folder';
                    $j++;
                }

                $array[$i]['children'] = $child;
            }
            $return = $array;
        }

        if (isset($rpcParams['uuid'], $return['rows'][0])) {
            $farmingContracts = $this->getFarmContracts(['uuid' => $rpcParams['uuid']]);
            $return['rows'][0]['number_of_contracts'] = count($farmingContracts ?? []);
        }

        return $return;
    }

    /**
     * @api-method markForEdit
     *
     * @param int $rpcParam
     *
     * @throws MTRpcException
     *
     * @return array
     */
    public function markForEdit($rpcParam)
    {
        $id = $rpcParam;

        $FarmingController = new FarmingController('Farming');

        $options['return'] = ['*'];
        $options['whereFields'] = ['id'];
        $options['whereValues'] = [$id];
        $data = $FarmingController->getItem($options);

        if ($data) {
            return $data;
        }

        throw new MTRpcException('non_existing_farming_selected', -33041);
    }

    /**
     * @api-method add
     *
     * @param array $rpcParams
     *                         {
     *                         #item string name
     *                         #item string company
     *                         #item string bulstat
     *                         #item string address
     *                         #item string company_address
     *                         #item string mol
     *                         }
     *
     * @throws MTRpcException
     * @throws Exception
     *
     * @return int
     */
    public function addNewFarming($rpcParams)
    {
        $UsersController = new UsersController('Users');
        $LayersController = new LayersController('Layers');
        $FarmingController = new FarmingController('Farming');
        $userFarmings = $FarmingController->getUserFarmings();
        $farmingIds = array_keys($userFarmings);

        $user_results = $UsersController->getUserDataById($this->User->UserID);
        $farmings_allowed = $user_results['allowed_farmings'];

        // get farmings count
        $options = [
            'where' => [
                'id' => ['column' => 'id', 'compare' => 'IN', 'value' => $farmingIds],
            ],
        ];
        $farmingsData = $FarmingController->getFarmings($options, true, false);
        $farmings_count = $farmingsData[0]['count'];

        foreach ($rpcParams['iban_arr'] as $bank) {
            if (!empty($bank['bic']) && 8 !== strlen($bank['bic'])) {
                throw new InvalidArgumentException('Wrong BIC length');
            }
        }

        // if farmings limit is reached return message
        if ($farmings_count < $farmings_allowed) {
            // create farming item
            $options = [];
            $options['mainData'] = [
                'name' => $rpcParams['name'],
                'user_id' => $this->User->UserID,
                'company' => $rpcParams['company'],
                'bulstat' => $rpcParams['bulstat'],
                'group_id' => $this->User->GroupID,
                'address' => $rpcParams['address'],
                'company_address' => $rpcParams['company_address'],
                'mol' => $rpcParams['mol'],
                'mol_egn' => $rpcParams['mol_egn'],
                'farming_mol_phone' => $rpcParams['farming_mol_phone'],
                'iban_arr' => json_encode($rpcParams['iban_arr']),
                'representative_id' => $rpcParams['representative_id'],
            ];

            $itemID = $FarmingController->addItem($options);
            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options['mainData'], ['farming_id' => $itemID], 'Add farming');

            // if farming was added add layers
            if ($itemID) {
                if (in_array($this->User->UserLevel, [Config::USERS_NORMAL, Config::USERS_ADMIN_FLAG]) && array_key_exists('farmingUsers', $rpcParams) && null !== $rpcParams['farmingUsers']) {
                    $users = $rpcParams['farmingUsers'];
                    // automatically grant parent user permissions
                    array_push($users, $this->User->GroupID);
                    $this->User->updateFarmingUsersPermissions($itemID, $users, ObjectPermissions::$permisionsMap);
                }

                foreach ($GLOBALS['Farming']['years'] as $item) {
                    $year = $item['id'];

                    $tableName = 'layer_zp_' . time();

                    // dobavqne na sloi zemedelski parcel
                    $color = $LayersController->StringHelper->randomColorCode();

                    $fields = [];
                    $style = [
                        'color' => $color,
                        'border_color' => '111111',
                        'transparency' => 100,
                        'border_only' => false,
                        'label_name' => [null],
                        'tags' => true,
                        'label_size' => $this->default_label_size,
                    ];
                    $fields['name'] = 'Земеделски парцели';
                    $fields['user_id'] = $this->User->UserID;
                    $fields['farming'] = $itemID;
                    $fields['year'] = $year;
                    $fields['extent'] = Config::DEFAULT_MAX_EXTENT;
                    $fields['table_name'] = $tableName;
                    $fields['layer_type'] = Config::LAYER_TYPE_ZP;
                    $fields['group_id'] = $this->User->GroupID;
                    $fields['style'] = json_encode($style);
                    $fields['color'] = $color;
                    $settings['mainData'] = $fields;
                    $LayersController->addLayerItem($settings);

                    // dobavqne na sloi dopustimost za godinite predi 2014 (4 = 2013)
                    if ($item['id'] <= 4) {
                        $color = $LayersController->StringHelper->randomColorCode();
                        $style = [
                            'color' => $color,
                            'border_color' => '111111',
                            'transparency' => 100,
                            'border_only' => false,
                            'label_name' => [null],
                            'tags' => true,
                            'label_size' => $this->default_label_size,
                        ];
                        $tableName = 'layer_dss_' . time();
                        $fields['name'] = 'Слой за допустимост';
                        $fields['user_id'] = $this->User->UserID;
                        $fields['farming'] = $itemID;
                        $fields['year'] = $year;
                        $fields['extent'] = Config::DEFAULT_MAX_EXTENT;
                        $fields['table_name'] = $tableName;
                        $fields['layer_type'] = Config::LAYER_TYPE_DSS;
                        $fields['group_id'] = $this->User->GroupID;
                        $fields['style'] = json_encode($style);
                        $fields['color'] = $color;
                        $settings['mainData'] = $fields;
                        $LayersController->addLayerItem($settings);
                    }
                    sleep(1);

                    // dobavqne na sloi komasaciq
                    $color = $LayersController->StringHelper->randomColorCode();
                    $fields = [];
                    $style = [
                        'color' => $color,
                        'border_color' => '111111',
                        'transparency' => 100,
                        'border_only' => false,
                        'label_name' => [null],
                        'tags' => true,
                        'label_size' => $this->default_label_size,
                    ];
                    $tableName = 'layer_kms_' . time();
                    $fields['name'] = 'Данни от комасация';
                    $fields['user_id'] = $this->User->UserID;
                    $fields['farming'] = $itemID;
                    $fields['year'] = $year;
                    $fields['extent'] = Config::DEFAULT_MAX_EXTENT;
                    $fields['table_name'] = $tableName;
                    $fields['layer_type'] = Config::LAYER_TYPE_KMS;
                    $fields['style'] = json_encode($style);
                    $fields['color'] = $color;
                    $fields['group_id'] = $this->User->GroupID;
                    $settings['mainData'] = $fields;
                    $LayersController->addLayerItem($settings);

                    // dobavqne na ISAK
                    $color = $LayersController->StringHelper->randomColorCode();
                    $fields = [];
                    $style = [
                        'color' => $color,
                        'border_color' => '111111',
                        'transparency' => 100,
                        'border_only' => false,
                        'label_name' => [null],
                        'tags' => true,
                        'label_size' => $this->default_label_size,
                    ];
                    $tableName = 'layer_isak_' . time();
                    $fields['name'] = 'от ИСАК';
                    $fields['user_id'] = $this->User->UserID;
                    $fields['farming'] = $itemID;
                    $fields['year'] = $year;
                    $fields['extent'] = Config::DEFAULT_MAX_EXTENT;
                    $fields['table_name'] = $tableName;
                    $fields['layer_type'] = Config::LAYER_TYPE_ISAK;
                    $fields['group_id'] = $this->User->GroupID;
                    $fields['style'] = json_encode($style);
                    $fields['color'] = $color;
                    $settings['mainData'] = $fields;
                    $LayersController->addLayerItem($settings);

                    // dobavqne na za ISAK
                    //                         6 = 2015 year
                    if ($item['id'] >= 6) {
                        $color = $LayersController->StringHelper->randomColorCode();
                        $fields = [];
                        $style = [
                            'color' => $color,
                            'border_color' => '111111',
                            'transparency' => 100,
                            'border_only' => false,
                            'border_width' => Config::LAYER_BOUNDARY_DEFAULT_BORDER_WIDTH,
                            'label_name' => [null],
                            'tags' => true,
                            'label_size' => $this->default_label_size,
                        ];
                        $tableName = 'layer_for_isak_' . time();
                        $fields['name'] = 'за ИСАК';
                        $fields['user_id'] = $this->User->UserID;
                        $fields['farming'] = $itemID;
                        $fields['year'] = $year;
                        $fields['extent'] = Config::DEFAULT_MAX_EXTENT;
                        $fields['table_name'] = $tableName;
                        $fields['layer_type'] = Config::LAYER_TYPE_FOR_ISAK;
                        $fields['group_id'] = $this->User->GroupID;
                        $fields['style'] = json_encode($style);
                        $fields['color'] = $color;
                        $settings['mainData'] = $fields;
                        $LayersController->addLayerItem($settings);
                    }

                    sleep(1);
                }

                /**
                 * @var User
                 */
                $organization = User::finder()->find('group_id = :group_id', [':group_id' => $this->User->GroupID]);
                [$subUser] = $organization->getSubUsers();
                LayerStyles::addUserLayersStyles($organization->id, $subUser->getId());

                $options = [];
                $options['database'] = $this->User->Database;
                $options['user_id'] = $this->User->GroupID;
                $options['maxextent'] = Config::DEFAULT_MAX_EXTENT;
                $LayersController->generateMapFile($options);
            } // end if farming was added
        } // end farmings limit check
        else {
            throw new MTRpcException('maximum_number_of_farmings_reached', -33040);
        }

        return $itemID;
    }

    /**
     * @api-method delete
     *
     * @param array $rpcParams
     *
     * @throws MTRpcException
     * @throws Exception
     */
    public function deleteSelectedFarming($rpcParams)
    {
        if (Config::USERS_NORMAL == $this->User->UserLevel) {
            throw new MTRpcException('NO_FARMING_DELETE_PERMISSIONS', -33045);
        }

        $farmingId = (int) $rpcParams[0]['id'];
        if (count($this->getFarmContracts(['id' => $farmingId])) > 0) {
            throw new MTRpcException('NO_FARMING_DELETE_PERMISSIONS_DUE_CONTRACTS_EXISTENCE', -33046);
        }

        $FarmingController = new FarmingController('Farming');
        $UsersController = new UsersController('Users');
        $data = $rpcParams;
        $dataCount = count($data);

        // flag shows if some of the records are system records
        // if there is system record the flag will clange to false and the script will not proceed
        $flag = true;

        for ($i = 0; $i < $dataCount; $i++) {
            if ($FarmingController->checkIfSystemItem($data[$i]['id'])) {
                $flag = false;
            }
            $arrayID[] = $data[$i]['id'];
        }

        if (!count($arrayID)) {
            return;
        }

        $userFarmings = $FarmingController->getUserFarmings();
        $farmingIds = array_keys($userFarmings);

        if (!in_array($arrayID[0], $farmingIds)) {
            return;
        }

        if (true == $flag) {
            $oldOptions = [
                'where' => [
                    'group_id' => ['column' => 'group_id', 'compare' => '=', 'value' => $this->User->GroupID],
                    'id' => ['column' => 'id', 'compare' => '=', 'value' => $arrayID[0]],
                ],
            ];
            $oldData = $FarmingController->getFarmings($oldOptions, false, false);
            if (empty($oldData)) {
                throw new MTRpcException('FARMING_NOT_FOUND', -33044);
            }
            $FarmingController->deleteItemsClicked($arrayID, true);
            // revoke all user permissions to farming
            $this->User->updateFarmingUsersPermissions($arrayID[0], [], ObjectPermissions::$permisionsMap);
            // $FarmingController->log(1, $this->User->Name, LoggerMessages::ITEM_DELETE, $arrayID);
            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, [], $oldData, 'Delete farming');
        }
    }

    /**
     * @api-method edit
     *
     * @param array $rpcParams
     *                         {
     *                         #item string name
     *                         #item string company
     *                         #item string bulstat
     *                         #item string address
     *                         #item string company_address
     *                         #item string mol
     *                         #item integer representative_id
     *                         }
     *
     * @throws Exception
     */
    public function editSelectedFarming($rpcParams)
    {
        $FarmingController = new FarmingController('Farming');
        $UsersController = new UsersController('Users');

        $oldOptions = [
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $rpcParams['id']],
            ],
        ];
        $oldData = $FarmingController->getFarmings($oldOptions, false, false);

        foreach ($rpcParams['iban_arr'] as $bank) {
            if (!empty($bank['bic']) && 8 !== strlen($bank['bic'])) {
                throw new InvalidArgumentException('Wrong BIC length');
            }
        }

        $options['id'] = $rpcParams['id'];
        // check if the required for edit item is not a system item
        $options['mainData'] = [
            'name' => $rpcParams['name'],
            'company' => $rpcParams['company'],
            'bulstat' => $rpcParams['bulstat'],
            'address' => $rpcParams['address'],
            'company_address' => $rpcParams['company_address'],
            'mol' => $rpcParams['mol'],
            'mol_egn' => $rpcParams['mol_egn'],
            'farming_mol_phone' => $rpcParams['farming_mol_phone'],
            'iban_arr' => json_encode($rpcParams['iban_arr']),
            'representative_id' => $rpcParams['representative_id'],
            'post_payment_fields' => json_encode($rpcParams['post_payment_fields']),
            'company_ekatte' => $rpcParams['company_ekatte'],
        ];

        $FarmingController->editItem($options);

        // only parent users can edit farming permissions
        if (Config::USERS_ADMIN_FLAG == $this->User->UserLevel && array_key_exists('farmingUsers', $rpcParams) && null !== $rpcParams['farmingUsers']) {
            $users = $rpcParams['farmingUsers'];
            array_push($users, $this->User->GroupID);
            $this->User->updateFarmingUsersPermissions($rpcParams['id'], $users, ObjectPermissions::$permisionsMap);
        }

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options['mainData'], $oldData, 'Edit farming');
    }

    /**
     * @api-method export2xls
     *
     * @param int $farmingId
     *
     * @throws MTRpcException
     *
     * @return string ZipFile Path
     */
    public function exportToExcel($farmingId)
    {
        $FarmingController = new FarmingController('Farming');
        $hasRightForPlots = $this->user->HasPlotRightsR;

        $userFarmings = $FarmingController->getUserFarmings();
        $farmingIds = array_keys($userFarmings);

        if (!in_array($farmingId, $farmingIds)) {
            echo '<h1>Нямате права за достъп до избраното стопанство.</h1>';

            return;
        }

        if (!$hasRightForPlots) {
            echo '<h1>В акаунта ви няма функциониращ модул имоти. Следователно няма да бъде засегната информация, която потенциално е необходима за вас.</h1>';

            return;
        }

        $options = [
            'return' => ['name'],
            'where' => [
                'farming_id' => ['column' => 'id', 'compare' => '=', 'value' => $farmingId],
            ],
        ];

        $result = $FarmingController->getFarmings($options, false, false);

        if (empty($result)) {
            throw new MTRpcException('FARMING_NOT_FOUND', -33044);
        }
        $farmingName = $result[0]['name'];

        $contractsExcel = $this->exportToExcelContracts($farmingName, $farmingId);
        $annexesExcel = $this->exportToExcelAnnexes($farmingName, $farmingId);
        $subleasesExcel = $this->exportToExcelSubleases($farmingName, $farmingId);
        $agreementsExcel = $this->exportToExcelAgreements($farmingName, $farmingId);
        $transactionsExcel = $this->exportToExcelTransactions($farmingName, $farmingId);

        if (!$contractsExcel && !$annexesExcel && !$subleasesExcel && !$agreementsExcel && !$transactionsExcel) {
            throw new MTRpcException('FARMING_HAS_NO_CONTRACTS', -33043);
        }

        $dateTime = new DateTime();
        $currentDate = $dateTime->createFromFormat('d.m.Y', date('d.m.Y'));
        $currentDateStr = $currentDate->format('d_m_Y');

        // make directory
        if (!file_exists(PUBLIC_UPLOAD_EXPORT . '/farmings')) {
            @mkdir(PUBLIC_UPLOAD_EXPORT . '/farmings', 0777, true);
        }

        $path = PUBLIC_UPLOAD_EXPORT . '/farmings/' . $farmingName;
        $zipPath = PUBLIC_UPLOAD_EXPORT . '/farmings/' . $farmingName . '/' . $currentDateStr . '_export_farming.zip';
        @mkdir($path, 0777);

        $archive = new ZipArchive();
        $archiveOpened = $archive->open($zipPath, ZipArchive::CREATE);

        if (!$archiveOpened) {
            // ERROR: archive failed to open
            return false;
        }

        // create excel files
        $archive->addFile($contractsExcel);
        $archive->addFile($annexesExcel);
        $archive->addFile($subleasesExcel);
        $archive->addFile($agreementsExcel);
        $archive->addFile($transactionsExcel);
        $archive->close();

        return 'files/uploads/export/farmings/' . $farmingName . '/' . $currentDateStr . '_export_farming.zip';
    }

    /**
     * Return all sub users maped wiht permission to specific farmId.
     *
     * @param [array] $params
     */
    public function getFarmingUsers($params): iterable
    {
        $usersGrid = new UsersMainGrid($this->rpcServer);
        $users = $usersGrid->getUserMainGridData(['skip_user_level_validation' => true])['rows'];
        if (array_key_exists('farmingId', $params) && !is_null($params['farmingId'])) {
            $farmingId = $params['farmingId'];
            $provider = new ObjectPermissionsProvider();
            $farmingUserIds = array_map(function ($permission) {
                return $permission->user_id;
            }, $provider->getObjectPermissionedUsers(UserFarmings::class, $farmingId, ObjectPermissions::PERMISSION_WRITE));

            foreach ($users as &$user) {
                if (in_array($user['id'], $farmingUserIds)) {
                    $user['hasPermissionToFarming'] = true;
                    $user['selected'] = true;
                }
            }
        }

        return $users;
    }

    /**
     * @api-method deteleZipArchive
     *
     * @param string $zipPath
     */
    public function deteleZipArchive($zipPath)
    {
        $FarmingController = new FarmingController('Farming');

        $FarmingController->File->removeFolder($zipPath);
    }

    private function getPlace($where, $grid)
    {
        return [
            'place' => $where,
            'grid' => $grid,
            'page' => 'json-farming',
        ];
    }

    private function getFarmContracts(array $params): iterable
    {
        $farming = $this->farmProvider->findBy($params);

        if (!$farming) {
            return [];
        }

        $userHasFarmPermission = Config::USERS_SUPER_ADMIN_FLAG === $this->User->UserLevel
            || (int)$farming->user_id === $this->User->GroupID
            || (int)$farming->user_id === $this->User->UserID;

        if (!$userHasFarmPermission) {
            throw new MTRpcException('NO_FARMING_READ_PERMISSIONS', -33045);
        }

        $user = User::finder()->find(
            'id = :id',
            [':id' => $farming->user_id]
        );

        if (!$user) {
            return [];
        }

        $UserDbController = new UserDbController($user->Database);

        $agreementsNumber = $GLOBALS['Contracts']['ContractTypes']['4']['id'];

        $options = [
            'tablename' => 'su_contracts su_co',
            'return' => [
                'c_num, to_char(c_date, \'DD.MM.YYYY\') as c_date, nm_usage_rights, to_char(start_date, \'DD.MM.YYYY\') as start_date, to_char(due_date, \'DD.MM.YYYY\') as due_date, active,
                 (SELECT string_agg(DISTINCT farming_year::TEXT, \', \') FROM su_payments su_p WHERE su_p.contract_id = su_co.id) AS paid,
                 case WHEN(SELECT count(*) FROM su_contracts su_co2 WHERE su_co2.parent_id = su_co. ID) > 0 then TRUE else FALSE END AS haveAnex',
            ],
            'where' => [
                'farming_id' => ['column' => 'su_co.farming_id', 'compare' => '=', 'value' => $farming->id],
                'nm_usage_rights' => ['column' => 'su_co.nm_usage_rights', 'compare' => '!=', 'value' => $agreementsNumber],
            ],
        ];

        $data = $UserDbController->getItemsByParams($options, false);

        return $data;
    }

    private function exportToExcelContracts($farmingName, $farmingId)
    {
        $data = $this->getFarmContracts(['id' => $farmingId]);

        $counter = count($data);

        if (0 == $counter) {
            return;
        }

        $printData = [];
        for ($i = 0; $i < $counter; $i++) {
            $farmingYearPaidArr = explode(',', $data[$i]['paid']);
            $farmingYearPaidCount = count($farmingYearPaidArr);
            $farmingYearPaid = '';
            for ($j = 0; $j < $farmingYearPaidCount; $j++) {
                $farmingYearPaid .= $GLOBALS['Farming']['years'][$farmingYearPaidArr[$j]]['farming_year_short'];
            }

            $dateTime = new DateTime();
            $contractDueDate = $dateTime->createFromFormat('d.m.Y', $data[$i]['due_date']);
            $currentDate = $dateTime->createFromFormat('d.m.Y', date('d.m.Y'));

            $contractType = $data[$i]['nm_usage_rights'];
            $contractForOwnership = $GLOBALS['Contracts']['ContractTypes']['1']['id'];

            $activeContract = 'Действащ';
            if (!$data[$i]['active']) {
                $activeContract = 'Анулиран';
            } elseif ($currentDate > $contractDueDate && $contractForOwnership != $contractType) {
                $activeContract = 'Изтекъл';
            }

            $printData[] = [
                'id' => ($i + 1),
                'c_num' => $data[$i]['c_num'],
                'c_date' => $data[$i]['c_date'],
                'farming' => $farmingName,
                'start_date' => $data[$i]['start_date'],
                'due_date' => $data[$i]['due_date'],
                'active' => $activeContract,
                'nm_usage_rights' => $GLOBALS['Contracts']['ContractTypes'][$contractType]['name'],
                'haveAnex' => $data[$i]['haveAnex'] ? 'Да' : 'Не',
                'paid' => $data[$i]['paid'] ? 'Да' : 'Не',
                'farming_year_paid' => $farmingYearPaid,
            ];
        }
        $column_headers = [
            '№ по ред',
            'Номер на договор',
            'Дата на сключване',
            'Стопанство',
            'Начална дата',
            'Крайна дата',
            'Статус към датата на справката',
            'Тип договор',
            'Анексиран',
            'Извършени плащания по договора',
            'Стопанска година за която има плащания',
        ];
        $export2Xls = new Export2XlsClass();

        return $export2Xls->export('DogovoriZaIztrivane', $printData, $column_headers, false);
    }

    private function exportToExcelAnnexes($farmingName, $farmingId)
    {
        $UserDbContractsController = new UserDbContractsController($this->User->Database);
        $options = [
            'tablename' => 'su_contracts',
            'return' => [
                'A.c_num as annex_num, C.c_num as contract_num, to_char(A.c_date, \'DD.MM.YYYY\') as annex_date,to_char(A.start_date, \'DD.MM.YYYY\') as annex_start_date,
                to_char(A.due_date, \'DD.MM.YYYY\') as annex_due_date, A.active as annex_active',
            ],
            'where' => [
                'farming_id' => ['column' => 'A.farming_id', 'compare' => '=', 'value' => $farmingId],
            ],
        ];

        $data = $UserDbContractsController->getAnnexes($options, false, false);

        $counter = count($data);
        if (0 == $counter) {
            return;
        }

        $printData = [];
        for ($i = 0; $i < $counter; $i++) {
            $dateTime = new DateTime();
            $annexDueDate = $dateTime->createFromFormat('d.m.Y', $data[$i]['annex_due_date']);
            $currentDate = $dateTime->createFromFormat('d.m.Y', date('d.m.Y'));

            $activeAnnex = 'Действащ';
            if (!$data[$i]['annex_active']) {
                $activeAnnex = 'Анулиран';
            } elseif ($currentDate > $annexDueDate) {
                $activeAnnex = 'Изтекъл';
            }

            $printData[] = [
                'id' => ($i + 1),
                'annex_num' => $data[$i]['annex_num'],
                'contract_num' => $data[$i]['contract_num'],
                'annex_date' => $data[$i]['annex_date'],
                'farming' => $farmingName,
                'start_date' => $data[$i]['annex_start_date'],
                'due_date' => $data[$i]['annex_due_date'],
                'active' => $activeAnnex,
            ];
        }
        $column_headers = [
            '№ по ред',
            'Номер на анекс',
            'Номер на анексирания договор',
            'Дата на сключване',
            'Стопанство',
            'Начална дата',
            'Крайна дата',
            'Статус към датата на справката',
        ];
        $export2Xls = new Export2XlsClass();

        return $export2Xls->export('AneksiZaIztrivane', $printData, $column_headers, false);
    }

    private function exportToExcelSubleases($farmingName, $farmingId)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);
        $FarmingController = new FarmingController('Farming');
        $userFarmings = $FarmingController->getUserFarmings();
        $farmingIds = array_keys($userFarmings);

        if (!in_array($farmingId, $farmingIds)) {
            echo '<h1>Нямате права за достъп до избраното стопанство.</h1>';

            return;
        }

        $options = [
            'tablename' => 'su_contracts su_co',
            'return' => [
                'c_num, to_char(c_date, \'DD.MM.YYYY\') as c_date, nm_usage_rights, to_char(start_date, \'DD.MM.YYYY\') as start_date, to_char(due_date, \'DD.MM.YYYY\') as due_date, active,
                 case WHEN(SELECT count(*) FROM su_contracts su_co2 WHERE su_co2.parent_id = su_co. ID) > 0 then TRUE else FALSE END AS haveAnex,
                 (SELECT string_agg (DISTINCT owner_id :: TEXT, \', \') FROM su_contracts_contragents su_co_co WHERE su_co_co.contract_id = su_co. ID) AS sublease_owners,
                 (SELECT string_agg (DISTINCT farming_id :: TEXT, \', \') FROM su_contracts_farming_contragents su_co_fa_co WHERE su_co_fa_co.contract_id = su_co. ID) AS sublease_farmings',
            ],
            'where' => [
                'farming_id' => ['column' => 'su_co.farming_id', 'compare' => '=', 'value' => $farmingId],
                'is_sublease' => ['column' => 'su_co.is_sublease', 'compare' => '=', 'value' => 'true'],
            ],
        ];

        $data = $UserDbController->getItemsByParams($options, false);
        $counter = count($data);

        if (0 == $counter) {
            return;
        }

        $printData = [];
        for ($i = 0; $i < $counter; $i++) {
            if (null != $data[$i]['sublease_owners']) {
                $optionsOwners = [
                    'return' => ['(CASE when o.company_name != null or o.company_name != \'\' THEN o.company_name ELSE concat(o.name, \' \', o.lastname) END) as name'],
                    'id_string' => $data[$i]['sublease_owners'],
                ];
                $results = $UserDbOwnersController->getOwnersData($optionsOwners);

                $ownersArr = array_map(function ($result) {
                    return $result['name'];
                }, $results);

                $owners = implode(', ', $ownersArr);
            }

            if (null != $data[$i]['sublease_farmings']) {
                $optionsFarmings = [
                    'return' => ['name'],
                    'where' => [
                        'group_id' => ['column' => 'group_id', 'compare' => '=', 'value' => $this->User->GroupID],
                        'farming_id' => ['column' => 'id', 'compare' => 'IN', 'value' => explode(',', $data[$i]['sublease_farmings'])],
                    ],
                ];
                $results = $FarmingController->getFarmings($optionsFarmings);

                $farmingsNamesArr = array_map(function ($result) {
                    return $result['name'];
                }, $results);

                $farmingsName = implode(', ', $farmingsNamesArr);
            }

            $dateTime = new DateTime();
            $contractDueDate = $dateTime->createFromFormat('d.m.Y', $data[$i]['due_date']);
            $currentDate = $dateTime->createFromFormat('d.m.Y', date('d.m.Y'));

            $activeSublease = 'Действащ';
            if (!$data[$i]['active']) {
                $activeSublease = 'Анулиран';
            } elseif ($currentDate > $contractDueDate) {
                $activeSublease = 'Изтекъл';
            }

            $printData[] = [
                'id' => ($i + 1),
                'c_num' => $data[$i]['c_num'],
                'c_date' => $data[$i]['c_date'],
                'farming' => $farmingName,
                'start_date' => $data[$i]['start_date'],
                'due_date' => $data[$i]['due_date'],
                'active' => $activeSublease,
                'nm_usage_rights' => $GLOBALS['Contracts']['ContractTypes'][$data[$i]['nm_usage_rights']]['name'],
                'haveAnex' => $data[$i]['haveAnex'] ? 'Да' : 'Не',
                'owners' => $owners,
                'farmings' => $farmingsName,
            ];
        }

        $column_headers = [
            '№ по ред',
            'Номер на договор',
            'Дата на сключване',
            'Стопанство',
            'Начална дата',
            'Крайна дата',
            'Статус към датата на справката',
            'Тип договор',
            'Анексиран',
            'Арендатор/Наемател',
            'Стопанства',
        ];

        $export2Xls = new Export2XlsClass();

        return $export2Xls->export('PreotdadeniDogovoriZaIztrivane', $printData, $column_headers, false);
    }

    private function exportToExcelAgreements($farmingName, $farmingId)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $FarmingController = new FarmingController('Farming');
        $options = [
            'tablename' => 'su_agreements su_a',
            'return' => [
                'name, year, agg_type, (SELECT sum(area) FROM su_agreements_data su_a_d WHERE su_a.id = su_a_d.agreement_id) as sum_area,
                    (SELECT count(*) FROM su_agreements_data su_a_d WHERE su_a.id = su_a_d.agreement_id) as count_agreement',
            ],
            'where' => [
                'farming_id' => ['column' => 'farming', 'compare' => '=', 'value' => $farmingId],
            ],
        ];

        $data = $UserDbController->getItemsByParams($options, false, false);
        $counter = count($data);

        if (0 == $counter) {
            return;
        }

        $printData = [];
        for ($i = 0; $i < $counter; $i++) {
            $aggType = 1 == $data[$i]['agg_type'] ? 'Доброволно' : 'Служебно';
            $farmingYearToday = $FarmingController->getCurrentFarmingYearID();
            $farmingYear = $data[$i]['year'] == $farmingYearToday ? 'Действащо' : 'Изтекло';
            $printData[] = [
                'id' => ($i + 1),
                'name' => $data[$i]['name'],
                'farming' => $farmingName,
                'status' => $farmingYear,
                'type' => $aggType,
                'farming_year' => $GLOBALS['Farming']['years'][$data[$i]['year']]['farming_year_short'],
                'numberOfImots' => $data[$i]['count_agreement'],
                'areaSum' => $data[$i]['sum_area'],
            ];
        }

        $column_headers = [
            '№ по ред',
            'Номер на споразумение',
            'Стопанство',
            'Статус към датата на справката',
            'Тип споразумение',
            'За стопанска година',
            'Брой имоти в споразумението',
            'Площи по споразумение (дка)',
        ];

        $export2Xls = new Export2XlsClass();

        return $export2Xls->export('SporazumeniqZaIztrivane', $printData, $column_headers, false);
    }

    private function exportToExcelTransactions($farmingName, $farmingId)
    {
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);
        $options = [
            'custom_counter' => 'count(DISTINCT(p.id))',
            'tablename' => $UserDbController->DbHandler->tablePayments,
            'return' => ["MAX (T . ID) AS id, C .c_num, to_char(C.c_date, 'DD.MM.YYYY') as c_date, to_char(MAX(t.date), 'DD.MM.YYYY') AS date, MAX (T .bank_acc) AS bank_acc,
                                MAX (T .recipient) AS recipient, MAX (T .amount) AS amount, MAX (T .farming_year) AS farming_year,
                                 T .bank_payment AS bank_payment, (SELECT STRING_AGG (round(tn.amount :: NUMERIC, 2) || ' {u=' || rt.unit ||'}' || ' ' || rt.name,',')
                                    FROM su_transactions_natura tn LEFT JOIN su_renta_types rt on tn.nat_type=rt.id WHERE tn.transaction_id = T . ID) AS amount_nat,
                                MAX (T .paid_from) AS paid_from, MAX (T .paid_in) AS paid_in,
                                 (CASE WHEN owner_type = 1 THEN name || ' ' || surname || ' ' || lastname ELSE company_name END) AS owners",
            ],
            'sort' => $_POST['sort'],
            'order' => $_POST['order'],
            'where' => [
                'farming_id' => ['column' => 'farming_id', 'compare' => '=', 'prefix' => 'c', 'value' => $farmingId],
            ],
            'group' => 'p.id, c.id, o.id, t.id, bank_payment',
        ];

        $data = $UserDbPaymentsController->getPaymentsByParams($options, false, false);
        $counter = count($data);

        if (0 == $counter) {
            return;
        }

        $printData = [];
        for ($i = 0; $i < $counter; $i++) {
            $type = 'от ' . (1 == $data[$i]['paid_from'] ? 'Лева' : 'Натура') . ' в ' . (1 == $data[$i]['paid_in'] ? 'Лева' : 'Натура');
            $paid_from_text = $this->getPaidText($data, $i, 'paid_from');
            $paid_in_text = $this->getPaidText($data, $i, 'paid_in');
            $pay = (1 == $data[$i]['paid_in'] ? 'В брой' : 'От склад');

            $bankPayment = 'Не';
            if (null != $data[$i]['bank_acc'] || '' != $data[$i]['bank_acc']) {
                $bankPayment = 'Да';
                $pay = 'Не';
            }
            $tmp_reps_array = explode(', ', $data[$i]['rep_names']);

            $printData[] = [
                'id' => ($i + 1),
                'name' => $data[$i]['id'],
                'date' => $data[$i]['date'],
                'type' => $type,
                'paid_from' => $paid_from_text,
                'paid_in' => $paid_in_text,
                'recipient' => $data[$i]['recipient'],
                'pay' => $pay,
                'bank_payment' => $bankPayment,
                'farming_year' => $GLOBALS['Farming']['years'][$data[$i]['farming_year']]['farming_year_short'],
                'contract_name' => $data[$i]['c_num'],
                'contract_date' => $data[$i]['c_date'],
                'owners' => $data[$i]['owners'],
            ];
        }

        $column_headers = [
            '№ по ред',
            'Номер на транзакция',
            'Дата на транзакция',
            'Вид транзакция',
            'Изплащане на',
            'Изплащане чрез',
            'Изплатено на',
            'Изплатено в брой/от склад',
            'Изплатено по банков път',
            'За година',
            'По договор',
            'Сключен на',
            'Към контрагент',
        ];

        $export2Xls = new Export2XlsClass();

        return $export2Xls->export('TransakciiZaIztrivane', $printData, $column_headers, false);
    }

    private function getPaidText($data, $i, $paidType)
    {
        if (1 == $data[$i][$paidType]) {
            $paid_text = $data[$i]['amount'] . 'лв';
        } else {
            $croptypes = explode(',', trim($data[$i]['amount_nat']));
            $croptypes = array_map(function ($crop) {
                return preg_replace_callback('({u=(?P<unit>[0-9]+)})', function ($matches) {
                    return $GLOBALS['Contracts']['renta_units'][$matches['unit']]['name'];
                }, $crop);
            }, $croptypes);

            $paid_text = implode(', ', $croptypes);
        }

        return $paid_text;
    }
}
