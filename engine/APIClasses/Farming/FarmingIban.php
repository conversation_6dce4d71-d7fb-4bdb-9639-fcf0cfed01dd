<?php

namespace TF\Engine\APIClasses\Farming;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Farming\FarmingController;

/**
 * Банкови сметки на стопанства.
 *
 * @rpc-module Farming
 *
 * @rpc-service-id farming-iban
 */
class FarmingIban extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readFarmingIban']],
        ];
    }

    /**
     * Returns all iban(s) for given farming ids.
     *
     * @api-method read
     *
     * @param string $farmingIds
     *
     * @return array {
     *               #item integer id
     *               #item string text
     *               }
     */
    public function readFarmingIban($farmingIds)
    {
        $return[] = [
            'value' => '',
            'text' => '-',
            'selected' => true,
        ];
        $FarmingController = new FarmingController('Farming');
        $arrayHelper = $FarmingController->ArrayHelper;
        if ('NULL' == $farmingIds) {
            return $return;
        }
        $userFarmings = $FarmingController->getUserFarmings();
        $userFarmingIds = array_keys($userFarmings);
        $farmingIds = $arrayHelper->filterEmptyStringArr(explode(',', $farmingIds ?? ''));
        $farmingIds = array_intersect($userFarmingIds, $farmingIds);

        $options = [
            'return' => ['iban_arr'],
            'where' => [
                'farming_id' => ['column' => 'id', 'compare' => 'IN', 'value' => $farmingIds],
            ],
        ];

        $result = $FarmingController->getFarmings($options, false, false);
        $banks = json_decode($result[0]['iban_arr'], true);

        if (!is_array($banks)) {
            return $return;
        }

        foreach ($banks as $bank) {
            if (empty($bank['iban'])) {
                continue;
            }

            $bankText = [
                'value' => json_encode($bank),
                'text' => $bank['name'] ? $bank['name'] . ': ' . $bank['iban'] : $bank['iban'],
            ];

            if (strlen($bank['bank_branch'])) {
                $bankText['text'] .= ': ' . $bank['bank_branch'];
            }

            $return[] = $bankText;
        }

        return $return;
    }
}
