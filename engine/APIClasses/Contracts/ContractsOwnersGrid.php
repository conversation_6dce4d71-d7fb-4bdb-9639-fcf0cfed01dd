<?php

namespace TF\Engine\APIClasses\Contracts;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\APIClasses\Common\OwnersInfo;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbContracts\UserDbContractsController;
use TF\Engine\Plugins\Core\UserDbOwners\UserDbOwnersController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Грид "Добавяне на собственик към договор".
 *
 * @rpc-module Contracts
 *
 * @rpc-service-id contracts-owners-datagrid
 *
 * @property UserDbController $UserDbController
 * @property UserDbOwnersController $UserDbOwnersController
 * @property UserDbContractsController $UserDbContractsController
 * @property UsersController $UsersController
 * @property FarmingController $FarmingController
 */
class ContractsOwnersGrid extends TRpcApiProvider
{
    /**
     * Class Controllers
     * Define all class controllers as properties.
     */
    public $relation_id;
    private $module = 'Contracts';
    private $service_id = 'contracts-owners-datagrid';

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readContractsOwnersGridData'],
                'validators' => [
                    'filterObj' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'add' => ['method' => [$this, 'addContractsOwnersGridData'],
                'validators' => [
                    'filterObj' => [
                        'owner_names' => 'validateText',
                        'egn' => 'validateText',
                        'eik' => 'validateText',
                        'company_name' => 'validateText',
                    ],
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'getOwnerHeritors' => ['method' => [$this, 'getOwnerHeritors']],
            'addNewOwner' => ['method' => [$this, 'addNewOwner'],
                'validators' => [
                    'rpcParams' => [
                        'name' => 'validateText',
                        'surname' => 'validateText',
                        'lastname' => 'validateText',
                        'egn' => 'validateDigitsOnly',
                        'lk_nomer' => 'validateDigitsOnly',
                        'lk_izdavane' => 'validateText',
                        'company_name' => 'validateText',
                        'eik' => 'validateDigitsOnly',
                        'mol' => 'validateText',
                        'company_address' => 'validateText',
                        'phone' => 'validateText',
                        'fax' => 'validateText',
                        'mobile' => 'validateText',
                        'email' => 'validateText',
                        'bank_name' => 'validateText',
                        'iban' => 'validateText',
                        'biv' => 'validateText',
                        'address' => 'validateText',
                        'rent_place' => 'validateText',
                    ],
                    'documents' => [
                        'type_id' => 'validateText',
                        'number' => 'validateText',
                        'date' => 'validateText',
                        'owner_id' => 'validateText',
                    ],
                ]],
        ];
    }

    /**
     * Gets all cotract owners data, associated with current plot and contract.
     *
     * @api-method read
     *
     * @param array $filterObj - array of parameters
     *                         {
     *                         #item plot_id
     *                         #item contract_id
     *                         }
     * @param int $page pagination parameter
     * @param int $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array associative array with all the relevant results
     */
    public function readContractsOwnersGridData(array $filterObj, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        if ($this->User->isGuest) {
            return [];
        }

        // init all needed controllers
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);
        $UserDbContractsController = new UserDbContractsController($this->User->Database);
        $UsersController = new UsersController('Users');
        $FarmingController = new FarmingController('Farming');

        // define default return
        $return = [
            'rows' => [],
            'total' => 0,
        ];

        if (!$filterObj['plot_id'] || !(int) $filterObj['plot_id'] || !$filterObj['contract_id'] || !(int) $filterObj['contract_id']) {
            return $return;
        }

        $this->relation_id = $UserDbContractsController->getContractPlotRelationID($filterObj['contract_id'], $filterObj['plot_id']);

        if (!$this->relation_id) {
            return $return;
        }

        $options = [
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
            'return' => [
                'CAST(CAST(t.id AS text)||CAST(t.id AS text) AS numeric(24,0)) as fakeid',
                'rel.rep_id as rep_id', 'd.id as document_id', 'rel.percent', 'numerator', 'denominator', 'owner_type', 't.*', 'pc.contract_area',
                '(CASE WHEN owner_type = 1 THEN egn ELSE eik END) as egn_eik',
                "(CASE WHEN owner_type = 1 THEN name || ' ' || surname || ' ' || lastname ELSE company_name END) as owner_names",
                "r.rep_name || ' ' || r.rep_surname || ' ' || r.rep_lastname as rep_names", 'proxy_num', 'proxy_date', 'r.owner_id as self_rep', 'r.*',
                'd.type_id as document_type', 'd.number as document_number', 'd.date as document_date', 'rel.id as id',  't.id as owner_id',
                'case when t.id = r.owner_id then true else false end as self_rep',
                'rel.is_signer as is_signer',
            ],
            'where' => [
                'rel_id' => ['column' => 'pc_rel_id', 'compare' => '=', 'prefix' => 'rel', 'value' => $this->relation_id],
                'is_heritor' => ['column' => 'is_heritor', 'compare' => '=', 'prefix' => 'rel', 'value' => 'FALSE'],
            ],
        ];

        $results = $UserDbOwnersController->getPlotOwnersData($options, false);
        $counter = $UserDbOwnersController->getPlotOwnersData($options, true);
        if ($counter[0]['count'] > 0) {
            $results = $UserDbOwnersController->getPlotOwnersData($options, false);
        }

        $resultsCount = count($results);
        for ($i = 0; $i < $resultsCount; $i++) {
            $results[$i]['dead'] = $results[$i]['is_dead'];
            // replacing empty values with "-"
            if ('' == $results[$i]['lk_nomer']) {
                $results[$i]['lk_nomer'] = '-';
            }
            if ('' == $results[$i]['lk_izdavane']) {
                $results[$i]['lk_izdavane'] = '-';
            }
            if ('' == $results[$i]['mol']) {
                $results[$i]['mol'] = '-';
            }
            if ('' == $results[$i]['company_address']) {
                $results[$i]['company_address'] = '-';
            }
            if ('' == $results[$i]['email']) {
                $results[$i]['email'] = '-';
            }
            if ('' == $results[$i]['phone']) {
                $results[$i]['phone'] = '-';
            }
            if ('' == $results[$i]['fax']) {
                $results[$i]['fax'] = '-';
            }
            if ('' == $results[$i]['mobile']) {
                $results[$i]['mobile'] = '-';
            }
            if ('' == $results[$i]['address']) {
                $results[$i]['address'] = '-';
            }

            if (!$results[$i]['rep_lk']) {
                $results[$i]['rep_lk'] = '-';
            }
            if (!$results[$i]['rep_lk_izdavane']) {
                $results[$i]['rep_lk_izdavane'] = '-';
            }
            if (!$results[$i]['rep_address']) {
                $results[$i]['rep_address'] = '-';
            }
            if (!$results[$i]['proxy_num']) {
                $results[$i]['proxy_num'] = '-';
            }
            if ('' == $results[$i]['proxy_date']) {
                $results[$i]['proxy_date'] = '-';
            } else {
                $results[$i]['proxy_date'] = strftime('%d.%m.%Y', strtotime($results[$i]['proxy_date']));
            }

            if ('' == $results[$i]['document_date']) {
                $results[$i]['document_date'] = '-';
            } else {
                $results[$i]['document_date'] = strftime('%d.%m.%Y', strtotime($results[$i]['document_date']));
            }

            if ($results[$i]['document_type']) {
                $results[$i]['document_type'] = $GLOBALS['Contracts']['DSTypes'][$results[$i]['document_type']]['name'];
            } else {
                $results[$i]['document_type'] = '-';
            }

            if (!$results[$i]['document_number']) {
                $results[$i]['document_number'] = '-';
            }

            if ($results[$i]['is_dead']) {
                $results[$i]['children'] = $this->getOwnerHeritors($results[$i]['owner_id'] . '.*{1}', $results[$i]['percent'], $results[$i]['numerator'], $results[$i]['denominator'], 1, $results[$i]['fakeid']);
                $results[$i]['iconCls'] = 'icon-tree-user-rip';
                $results[$i]['is_dead'] = 'Да';
            } else {
                switch ($results[$i]['owner_type']) {
                    case 0:
                        $results[$i]['iconCls'] = 'icon-tree-users';

                        break;
                    case 1:
                        $results[$i]['iconCls'] = 'icon-tree-user';

                        break;
                    default:
                        break;
                }

                $results[$i]['is_dead'] = 'Не';
            }

            $results[$i]['is_heritor'] = false;
            $results[$i]['level'] = 1;
        }

        $return['rows'] = $results;
        $return['total'] = $counter[0]['count'];

        return $return;
    }

    /**
     * Gets relevant information for adding owners to Contracts.
     *
     * @api-method add
     *
     * @param array $filterObj - array of parameters
     *                         {
     *                         #item plot_id
     *                         #item contract_id
     *                         }
     * @param int $page pagination parameter
     * @param int $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @throws MTRpcException
     *
     * @return array associative array with all the relevant results
     */
    public function addContractsOwnersGridData(array $filterObj, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        if ($this->User->isGuest) {
            return [];
        }

        // init all needed controllers
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);
        $UserDbContractsController = new UserDbContractsController($this->User->Database);
        $UsersController = new UsersController('Users');
        $FarmingController = new FarmingController('Farming');

        // define default return
        $return = [
            'rows' => [],
            'total' => 0,
        ];

        if (!$filterObj['plot_id'] || !(int) $filterObj['plot_id'] || !$filterObj['contract_id'] || !(int) $filterObj['contract_id']) {
            return $return;
        }

        $this->relation_id = $UserDbContractsController->getContractPlotRelationID($filterObj['contract_id'], $filterObj['plot_id']);

        if (!$this->relation_id) {
            return $return;
        }

        $isFromSublease = $UserDbContractsController->isContractFromSublease($filterObj['contract_id']);
        if ($isFromSublease) {
            throw new MTRpcException('CANNOT_MODIFY_FROM_SUBLEASE_CONTRACT', -33752);
        }
        $options = [
            'tablename' => $UserDbController->DbHandler->plotsOwnersRelTable,
            'where' => [
                'pc_rel_id' => ['column' => 'pc_rel_id', 'compare' => '=', 'value' => $this->relation_id],
                'is_heritor' => ['column' => 'is_heritor', 'compare' => '=', 'value' => 'FALSE'],
            ],
        ];

        $po_rel_results = $UserDbController->getItemsByParams($options);
        $po_rel_resultsCount = count($po_rel_results);
        // clear old data
        $owner_id_array = [];

        // create plot owners relation id array
        if ($po_rel_resultsCount) {
            for ($i = 0; $i < $po_rel_resultsCount; $i++) {
                $owner_id_array[] = $po_rel_results[$i]['owner_id'];
            }
        }

        $id_string = implode(',', $owner_id_array);

        $filterObj['owner_names'] = $filterObj['owner_names'] ? preg_replace('/\s+/', ' ', $filterObj['owner_names']) : '';
        $filterObj['company_name'] = $filterObj['company_name'] ? preg_replace('/\s+/', ' ', $filterObj['company_name']) : '';

        $options = [
            'return' => [
                'distinct o.id', 'CAST(CAST(o.id AS text)||CAST(o.id AS text) AS numeric(24,0)) as fakeid', 'o.owner_type', '(CASE WHEN o.owner_type = 1 THEN o.egn ELSE o.eik END) as egn_eik', 'o.is_dead',
                "(CASE WHEN o.owner_type = 1 THEN TRIM(o.name) || ' ' || TRIM(o.surname) || ' ' || TRIM(o.lastname) ELSE o.company_name END) as owner_names", 'count(*) OVER () as total_count',
            ],
            'anti_id_string' => $id_string,
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
            'where' => [
                'egn' => ['column' => 'egn', 'compare' => 'ILIKE', 'prefix' => 'o', 'value' => $filterObj['egn']],
                'eik' => ['column' => 'eik', 'compare' => 'ILIKE', 'prefix' => 'o', 'value' => $filterObj['eik']],
                'company_name' => ['column' => 'TRIM(o.company_name)', 'compare' => 'ILIKE', 'value' => $filterObj['company_name']],
            ],
            'group' => 'o.id',
        ];

        if (isset($filterObj['owner_names']) && '' != $filterObj['owner_names']) {
            $tmp_owner_names = preg_replace('/\s+/', '.*', $filterObj['owner_names']);
            $tmp_owner_names = mb_strtolower($tmp_owner_names, 'UTF-8');
            $options['where']['owner_names'] = ['column' => "lower(TRIM (o.NAME)) || ' ' || lower(TRIM (o.surname)) || ' ' || lower(TRIM (o.lastname))", 'compare' => '~', 'value' => $tmp_owner_names];
        }

        $owners_data = $UserDbOwnersController->getOwnersData($options, false);

        $return['rows'] = $owners_data;
        $return['total'] = $owners_data[0]['total_count'];

        return $return;
    }

    /**
     * The method returns all assigned heritors, to a selected owner.
     *
     * @api-method getOwnerHeritors
     *
     * @param string $path
     * @param float $rat_ownage
     * @param int $numerator
     * @param int $denominator
     * @param int $level
     * @param int $parentFakeId
     *
     * @return array
     */
    public function getOwnerHeritors($path, $rat_ownage, $numerator, $denominator, $level, $parentFakeId = null)
    {
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);

        // parentFakeId се подава като параметър на всеки наследник, за да може да се селектират правилно наследници
        // и родители в treegrid-а. Необходимо е да се премахне, ако излизат нови проблеми със селектирането на
        // наследник за редакция на собствеността.
        if (!$UserDbOwnersController instanceof UserDbOwnersController) {
            $UserDbOwnersController = new UserDbOwnersController($this->User->Database);
        }

        return $UserDbOwnersController->buildHeritorsTree($path, $rat_ownage, $numerator, $denominator, $level, $parentFakeId, $this->relation_id, false);
    }

    /**
     * The method creates new owner, as well as new entries for the ownage documents, assosiated with the new owner.
     *
     * @api-method addNewOwner
     *
     * @param array $rpcParams - the RPC parameters to fill the entry fields
     *                         {
     *                         #item string name            - the name of the owner (for physical enttities)
     *                         #item string surname         - the surname of the owner (for physical enttities)
     *                         #item string lastname        - the lastname of the owner (for physical enttities)
     *                         #item string egn             - the EGN (personal number) of the owner (for physical enttities)
     *                         #item string lk_nomer        - the number of the owner's ID Card (for physical enttities)
     *                         #item string lk_izdavane     - the owner's ID Card information (for physical enttities)
     *                         #item string company_name    - the name of the company (for legal enttities)
     *                         #item string eik             - the company's registration number (for legal enttities)
     *                         #item string mol             - the company's legal representative (for legal enttities)
     *                         #item string company_address - the company's registration address (for legal enttities)
     *                         #item string phone			 - the entity's phone number (for both legal and physical enttities)
     *                         #item string fax			 - the entity's fax number (for both legal and physical enttities)
     *                         #item string mobile			 - the entity's mobile number (for both legal and physical enttities)
     *                         #item string email			 - the entity's email address (for both legal and physical enttities)
     *                         #item string bank_name       - the entity's bank name
     *                         #item string iban            - the entity's IBAN (bank account number) (for both legal and physical entities)
     *                         #item string bic             - the entity's bank BIC
     *                         #item string address		 - the entity's contact address (for both legal and physical enttities)
     *                         #item string rent_place		 - the entity's location at which to receive the renumeration (for both legal and physical enttities)
     *                         }
     * @param array $documents - list of associated documents
     *                         {
     *                         #item timestamp date		     - the date from the contract
     *                         #item string    name		     - the contract name
     *                         #item integer   type_id	     - the contract type
     *                         #item string    number		     - the contract number
     *                         }
     *
     * @throws MTRpcException
     *
     * @return int owner_id
     */
    public function addNewOwner($rpcParams)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $this->checkForExistingOwner($rpcParams);

        $postPaymentFields = json_encode($rpcParams['post_payment_fields']);

        // 1 -> physical; 0 -> legal
        if ($rpcParams['is_legal']) {
            $options = [
                'tablename' => DEFAULT_DB_PREFIX . 'owners',
                'mainData' => [
                    'name' => '',
                    'surname' => '',
                    'lastname' => '',
                    'egn' => '',
                    'lk_nomer' => '',
                    'lk_izdavane' => '',
                    'company_name' => $rpcParams['company_name'],
                    'eik' => $rpcParams['eik'],
                    'mol' => $rpcParams['mol'],
                    'company_address' => $rpcParams['company_address'],
                    'post_payment_fields' => $postPaymentFields,
                    'phone' => $rpcParams['phone'],
                    'fax' => $rpcParams['fax'],
                    'mobile' => $rpcParams['mobile'],
                    'email' => $rpcParams['email'],
                    'bank_name' => $rpcParams['bank_name'],
                    'iban' => $rpcParams['iban'],
                    'bic' => $rpcParams['bic'],
                    'address' => $rpcParams['address'],
                    'remark' => $rpcParams['remark'],
                    'rent_place' => $rpcParams['rent_place'],
                    'owner_type' => 0,
                    'is_dead' => 'FALSE',
                    'dead_date' => null,
                ],
            ];
        } else {
            OwnersInfo::validateDeadDate($rpcParams['dead_date']);

            $options = [
                'tablename' => DEFAULT_DB_PREFIX . 'owners',
                'mainData' => [
                    'name' => $rpcParams['name'],
                    'surname' => $rpcParams['surname'],
                    'lastname' => $rpcParams['lastname'],
                    'egn' => $rpcParams['egn'],
                    'lk_nomer' => $rpcParams['lk_nomer'],
                    'lk_izdavane' => $rpcParams['lk_izdavane'],
                    'company_name' => '',
                    'eik' => '',
                    'mol' => '',
                    'company_address' => '',
                    'post_payment_fields' => $postPaymentFields,
                    'phone' => $rpcParams['phone'],
                    'fax' => $rpcParams['fax'],
                    'mobile' => $rpcParams['mobile'],
                    'email' => $rpcParams['email'],
                    'bank_name' => $rpcParams['bank_name'],
                    'iban' => $rpcParams['iban'],
                    'bic' => $rpcParams['bic'],
                    'address' => $rpcParams['address'],
                    'remark' => $rpcParams['remark'],
                    'rent_place' => $rpcParams['rent_place'],
                    'owner_type' => 1,
                    'is_dead' => ($rpcParams['is_dead']) ? 'TRUE' : 'FALSE',
                    'dead_date' => ($rpcParams['is_dead']) ? $rpcParams['dead_date'] : null,
                ],
            ];
        }

        $recordID = $UserDbController->addItem($options);
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options['mainData'], ['owner_id' => $recordID], 'adds owner');

        return $recordID;
    }

    /**
     * @throws MTRpcException
     *
     * @return bool
     */
    private function checkForExistingOwner($rpcParams)
    {
        if ($rpcParams['is_dead'] || $rpcParams['is_foreigner']) {
            return false;
        }

        $UserDbController = new UserDbController($this->User->Database);

        $ownerType = $rpcParams['is_legal'] ? 0 : 1;
        $column = $rpcParams['is_legal'] ? 'eik' : 'egn';
        $egn_eik = $rpcParams['is_legal'] ? $rpcParams['eik'] : $rpcParams['egn'];
        $options = [
            'tablename' => $UserDbController->DbHandler->tableOwners,
            'where' => [
                'owner_type' => ['column' => 'owner_type', 'compare' => '=', 'value' => $ownerType],
                'egn_eik' => ['column' => $column, 'compare' => '=', 'value' => $egn_eik],
            ],
        ];

        $results = $UserDbController->getItemsByParams($options);

        if (count($results)) {
            throw new MTRpcException('OWNER_ALREADY_EXISTS', -33310);
        }

        return false;
    }
}
