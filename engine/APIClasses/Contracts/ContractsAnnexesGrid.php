<?php

namespace TF\Engine\APIClasses\Contracts;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

// Prado::using('Plugins.Core.Contracts.conf');
// Prado::using('Plugins.Core.UserDb.*');
/**
 * Грид "Анекси".
 *
 * @rpc-module Contracts
 *
 * @rpc-service-id contracts-annexes-grid
 */
class ContractsAnnexesGrid extends TRpcApiProvider
{
    /**
     * Register required controllers.
     */
    private $UserDbController;

    /**
     * Registers the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getContractsAnnexesData'],
                'validators' => [
                    'filterObj' => [
                        'contract_id' => 'validateInteger',
                    ],
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * Gets all annexes associated with a selected contract.
     *
     * @api-method read
     *
     * @param array $filterObj contains filter parameters
     *                         {
     *                         #item int contract_id
     *                         }
     * @param string $page pagination parameter
     * @param string $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array results array with all relevant annexes
     */
    public function getContractsAnnexesData(array $filterObj, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        if ($this->User->isGuest) {
            return [];
        }

        // init controllers
        $UserDbController = new UserDbController($this->User->Database);

        // create default empty return
        $return = [
            'rows' => [],
            'total' => 0,
        ];

        if (!$filterObj['contract_id'] || !(int) $filterObj['contract_id']) {
            return $return;
        }

        $renta_types = [];
        // options for renta nat types
        $options = [
            'tablename' => $UserDbController->DbHandler->tableRentaTypes,
        ];
        $renta_results = $UserDbController->getItemsByParams($options, false, false);

        $rentaResCount = count($renta_results);
        for ($i = 0; $i < $rentaResCount; $i++) {
            $renta_types[$renta_results[$i]['id']] = $renta_results[$i]['name'];
        }

        $options = [
            'tablename' => $UserDbController->DbHandler->tableContracts,
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
            'where' => [
                'is_annex' => ['column' => 'is_annex', 'compare' => '=', 'value' => 'TRUE'],
                'parent_id' => ['column' => 'parent_id', 'compare' => '=', 'value' => $filterObj['contract_id']],
            ],
        ];

        $counter = $UserDbController->getItemsByParams($options, true, false);
        if (isset($counter[0]['count']) && 0 === $counter[0]['count']) {
            return [];
        }

        $results = $UserDbController->getItemsByParams($options, false, false);

        date_default_timezone_set('Europe/Sofia');
        $currentDate = date('Y-m-d');

        $resultCount = count($results);
        for ($i = 0; $i < $resultCount; $i++) {
            $options = [
                'tablename' => $UserDbController->DbHandler->contractsRentsRelTable,
                'where' => [
                    'contract_id' => ['column' => 'contract_id', 'compare' => '=', 'value' => $results[$i]['id']],
                ],
            ];
            $rentaResults = $UserDbController->getItemsByParams($options, false, false);

            if ($results[$i]['active']) {
                $results[$i]['active_text'] = (!$results[$i]['due_date'] || $results[$i]['due_date'] > $currentDate) ? 'Действащ' : 'Изтекъл';
            } else {
                $results[$i]['active_text'] = 'Анулиран';
            }

            $results[$i]['c_date'] = strftime('%d.%m.%Y', strtotime($results[$i]['c_date']));
            $results[$i]['due_date'] = $results[$i]['due_date'] ? strftime('%d.%m.%Y', strtotime($results[$i]['due_date'])) : '-';
            if ('' == $results[$i]['sv_date']) {
                $results[$i]['sv_date'] = '-';
            } else {
                $results[$i]['sv_date'] = strftime('%d.%m.%Y', strtotime($results[$i]['sv_date']));
            }
            $results[$i]['start_date'] = strftime('%d.%m.%Y', strtotime($results[$i]['start_date']));
            $results[$i]['c_type'] = $results[$i]['nm_usage_rights'];
            $results[$i]['nm_usage_rights'] = $GLOBALS['Contracts']['ContractTypes'][$results[$i]['nm_usage_rights']]['name'];
            $results[$i]['renta_nat_type'] = '';
            $results[$i]['renta_nat'] = '';
            foreach ($rentaResults as $renta_nat) {
                $results[$i]['renta_nat_type'] .= $renta_types[$renta_nat['renta_id']] . '<br/>';
                $results[$i]['renta_nat'] .= number_format($renta_nat['renta_value'], 2, '.', '') . '<br/>';
            }
            if (null !== $results[$i]['renta']) {
                $results[$i]['renta_txt'] = BGNtoEURO($results[$i]['renta']);
                $results[$i]['renta'] = number_format($results[$i]['renta'], 2, '.', '');
            }
        }

        return [
            'rows' => $results,
            'total' => $counter[0]['count'],
        ];
    }
}
