<?php

namespace TF\Engine\APIClasses\Contracts;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbContracts\UserDbContractsController;
use TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Грид "Имоти" при "Добавяне на анекс".
 *
 * @rpc-module Contracts
 *
 * @rpc-service-id contracts-annexes-plots-datagrid
 *
 * @property UserDbController $UserDbController
 * @property UserDbContractsController $UserDbContractsController
 * @property UserDbPlotsController $UserDbPlotsController
 * @property UsersController $UsersController
 */
class ContractsAnnexesPlotsGrid extends TRpcApiProvider
{
    private $UserDbController;
    private $UserDbContractsController;
    private $UserDbPlotsController;
    private $UsersController = false;
    private $contractAreaByGid = [];
    private $AreaForRentByGid = [];

    /**
     * Registers the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getContractsAnnexesPlotDatagridData'],
                'validators' => [
                    'filterParams' => [
                        'contract_id' => 'validateInteger',
                    ],
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * Грид "Имоти" при "Добавяне на анекс".
     *
     * @api-method read
     *
     * @param array $filterParams
     *                            {
     *                            #item integer contract_id
     *                            }
     * @param string $page pagination parameter
     * @param string $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array
     */
    public function getContractsAnnexesPlotDatagridData(array $filterParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        // create default empty return
        $empty_return = [
            'rows' => [],
            'total' => 0,
        ];

        if ($this->User->isGuest) {
            return $empty_return;
        }

        // init controllers
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbContractsController = new UserDbContractsController($this->User->Database);
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);
        $UsersController = new UsersController('Users');

        if (!$filterParams['contract_id'] || !(int) $filterParams['contract_id']) {
            return $empty_return;
        }

        $contract_id = $filterParams['contract_id'];

        // get latest annex id
        $options = [
            'tablename' => $UserDbController->DbHandler->tableContracts,
            'order' => 'desc',
            'sort' => 'due_date',
            'return' => ['id'],
            'where' => [
                'parent_id' => ['column' => 'parent_id', 'compare' => '=', 'value' => $contract_id],
                'is_annex' => ['column' => 'is_annex', 'compare' => '=', 'value' => 'TRUE'],
                'active' => ['column' => 'active', 'compare' => '=', 'value' => 'TRUE'],
            ],
        ];
        $latest_annex = $UserDbController->getItemsByParams($options, false, false);

        if (count($latest_annex) > 0) {
            $contract_id = $latest_annex[0]['id'];
        }

        if ('ekate_name' == $sort) {
            $sort = 'ekate';
        }
        $options = [
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
            'return' => [
                'kvs.gid', 'kvs.kad_ident', 'kvs.ekate', 'kvs.virtual_ekatte_name as ekatte_name', 'kvs.virtual_category_title as category', 'kvs.virtual_ntp_title as area_type', 'pc.annex_action',
                'round(pc.contract_area::numeric, 3) as contract_area',
                'round(pc.area_for_rent::numeric, 3) as area_for_rent',
                'round(pc.kvs_allowable_area::numeric, 3) as kvs_allowable_area',
                'round(kvs.document_area::numeric, 3) as document_area',
                'round(pc.rent_per_plot::numeric, 2) as rent_per_plot',
                'round((st_area(geom)/1000)::numeric, 3) as kvs_area',
            ],
            'where' => [
                'contract_id' => ['column' => 'contract_id', 'compare' => '=', 'prefix' => 'pc', 'value' => $contract_id],
            ],
        ];
        $plots = $UserDbContractsController->getContractPlotData($options, false, false);

        $plotIds = [];
        $plotsCount = count($plots);

        for ($i = 0; $i < $plotsCount; $i++) {
            $this->contractAreaByGid[$plots[$i]['gid']] = $plots[$i]['contract_area'];
            $this->AreaForRentByGid[$plots[$i]['gid']] = $plots[$i]['area_for_rent'];

            $plotIds[] = $plots[$i]['gid'];

            $plots[$i]['is_checkable'] = true;
            $plots[$i]['action'] = '';
            $plots[$i]['iconCls'] = 'icon-tree-edit-geometry';
            if (!$plots[$i]['document_area']) {
                $plots[$i]['document_area'] = $plots[$i]['kvs_area'];
            }
        }

        if (0 == count($plotIds)) {
            return $empty_return;
        }

        $index_count = 0;
        foreach ($plots as $index => &$plot) {
            $plot['index'] = $index_count;
            $index_count++;
        }

        $final = array_values($plots);

        // add latest annex id to firs element of grid
        if (count($latest_annex) > 0) {
            $final[0]['annex_id'] = $latest_annex[0]['id'];
        }

        return [
            'rows' => $final,
            'total' => count($final),
        ];
    }

    private function getSplitedPlots($plotIds)
    {
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);
        $UsersController = new UsersController('Users');

        $options = [
            'return' => [
                'kvs_new.ekate AS new_ekate', 'kvs_new.kad_ident AS new_kad_ident',
                'kvs_new.virtual_category_title as category', 'kvs_new.virtual_ntp_title as area_type', 'round(kvs_new.document_area::numeric, 3) as document_area',
                'round((st_area(kvs_new.geom)/1000)::numeric, 3) as kvs_area',
                'kvs_log.old_gid as old_gid',
                'kvs_log.new_gid as new_gid',
                'kvs_old.ekate AS old_ekate',
                'kvs_old.virtual_ekatte_name AS old_ekate_name',
                'kvs_old.kad_ident AS old_kad_ident',
                'kvs_old.virtual_category_title AS old_category',
                'kvs_old.virtual_ntp_title AS old_area_type',
                'round(kvs_old.document_area::numeric, 3) as old_document_area',
            ],
            'where' => [
                'edit_type' => ['column' => 'edit_type', 'compare' => '=', 'prefix' => 'kvs_log', 'value' => 'split'],
                'old_gid' => ['column' => 'old_gid', 'compare' => 'IN', 'prefix' => 'kvs_log', 'value' => $plotIds],
            ],
        ];
        $results = $UserDbPlotsController->getEditedPlots($options, false, false);

        $final = [];
        $count = count($results);
        for ($i = 0; $i < $count; $i++) {
            $final[$results[$i]['old_gid']]['gid'] = $results[$i]['old_gid'];
            $final[$results[$i]['old_gid']]['ekate'] = $results[$i]['old_ekate'];
            $final[$results[$i]['old_gid']]['ekate_name'] = $results[$i]['old_ekate_name'];
            $final[$results[$i]['old_gid']]['kad_ident'] = $results[$i]['old_kad_ident'];
            $final[$results[$i]['old_gid']]['category'] = $results[$i]['old_category'];
            $final[$results[$i]['old_gid']]['area_type'] = $results[$i]['old_area_type'];
            $final[$results[$i]['old_gid']]['contract_area'] = $this->contractAreaByGid[$results[$i]['old_gid']];
            $final[$results[$i]['old_gid']]['document_area'] = $results[$i]['old_document_area'];
            $final[$results[$i]['old_gid']]['area_for_rent'] = $this->AreaForRentByGid[$results[$i]['old_gid']];
            $final[$results[$i]['old_gid']]['is_checkable'] = false;
            $final[$results[$i]['old_gid']]['iconCls'] = 'icon-tree-edit-geometry';

            $documentArea = $results[$i]['document_area'] ? $results[$i]['document_area'] : $results[$i]['kvs_area'];

            $final[$results[$i]['old_gid']]['children'][] = [
                'gid' => $results[$i]['new_gid'],
                'ekate' => $results[$i]['new_ekate'],
                'ekate_name' => $results[$i]['old_ekate_name'],
                'kad_ident' => $results[$i]['new_kad_ident'],
                'is_checkable' => true,
                'iconCls' => 'icon-tree-edit-geometry',
                'category' => $results[$i]['category'],
                'area_type' => $results[$i]['area_type'],
                'contract_area' => $documentArea,
                'document_area' => $documentArea,
                'area_for_rent' => $documentArea,
                'kvs_area' => $results[$i]['kvs_area'],
            ];
        }

        return $final;
    }

    private function getMergedPlots($plotIds)
    {
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);
        $UsersController = new UsersController('Users');

        $options = [
            'return' => [
                'kvs_log.new_gid as new_gid',
            ],
            'where' => [
                'edit_type' => ['column' => 'edit_type', 'compare' => '=', 'prefix' => 'kvs_log', 'value' => 'merge'],
                'old_gid' => ['column' => 'old_gid', 'compare' => 'IN', 'prefix' => 'kvs_log', 'value' => $plotIds],
            ],
        ];
        $results = $UserDbPlotsController->getEditedPlots($options, false, false);

        $newGids = array_values(array_column($results, 'new_gid'));

        if (0 == count($newGids)) {
            return [];
        }

        $options = [
            'return' => [
                'kvs_new.ekate AS new_ekate', 'kvs_new.kad_ident AS new_kad_ident',
                'kvs_new.virtual_category_title as category', 'kvs_new.virtual_ntp_title аs area_type', 'round(kvs_new.document_area::numeric, 3) as document_area',
                'round((st_area(kvs_new.geom)/1000)::numeric, 3) as kvs_area',
                'kvs_log.old_gid as old_gid',
                'kvs_log.new_gid as new_gid',
                'kvs_old.ekate AS old_ekate',
                'kvs_old.virtual_ekatte_name аs old_ekate_name',
                'kvs_old.kad_ident AS old_kad_ident',
            ],
            'where' => [
                'edit_type' => ['column' => 'edit_type', 'compare' => '=', 'prefix' => 'kvs_log', 'value' => 'merge'],
                'new_gid' => ['column' => 'new_gid', 'compare' => 'IN', 'prefix' => 'kvs_log', 'value' => $newGids],
            ],
        ];
        $results = $UserDbPlotsController->getEditedPlots($options, false, false);

        $final = [];
        $resultCount = count($results);
        for ($i = 0; $i < $resultCount; $i++) {
            $new_gid = $results[$i]['new_gid'];
            $final[$new_gid]['gid'] .= ((null === $final[$new_gid]['gid']) ? '' : ',') . $results[$i]['old_gid'];
            $final[$new_gid]['ekate'] = $results[$i]['old_ekate'];
            $final[$new_gid]['ekate_name'] = $results[$i]['old_ekate_name'];
            $final[$new_gid]['kad_ident'] .= ((0 == $i) ? '' : ', ') . $results[$i]['old_kad_ident'];
            $final[$new_gid]['is_checkable'] = false;
            $final[$new_gid]['iconCls'] = 'icon-tree-edit-geometry';

            $documentArea = ($results[$i]['document_area']) ? $results[$i]['document_area'] : $results[$i]['kvs_area'];

            $final[$new_gid]['children'][0] = [
                'gid' => $new_gid,
                'ekate' => $results[$i]['new_ekate'],
                'ekate_name' => $results[$i]['old_ekate_name'],
                'kad_ident' => $results[$i]['new_kad_ident'],
                'is_checkable' => true,
                'iconCls' => 'icon-tree-edit-geometry',
                'category' => $results[$i]['category'],
                'area_type' => $results[$i]['area_type'],
                'contract_area' => $documentArea,
                'document_area' => $documentArea,
                'area_for_rent' => $documentArea,
            ];
        }

        return $final;
    }

    private function getPlotsWithNewBoundries($plotIds)
    {
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);
        $UsersController = new UsersController('Users');

        $options = [
            'return' => [
                'kvs_log.new_gid as new_gid',
            ],
            'where' => [
                'edit_type' => ['column' => 'edit_type', 'compare' => '=', 'prefix' => 'kvs_log', 'value' => 'new_boundary'],
                'old_gid' => ['column' => 'old_gid', 'compare' => 'IN', 'prefix' => 'kvs_log', 'value' => $plotIds],
            ],
        ];
        $results = $UserDbPlotsController->getEditedPlots($options, false, false);

        $newGids = array_values(array_column($results, 'new_gid'));

        if (0 == count($newGids)) {
            return [];
        }

        $options = [
            'return' => [
                'kvs_new.ekate AS new_ekate', 'kvs_new.kad_ident AS new_kad_ident',
                'kvs_new.virtual_category_title as category', 'kvs_new.virtual_ntp_title as area_type', 'round(kvs_new.document_area::numeric, 3) as document_area',
                'round((st_area(kvs_new.geom)/1000)::numeric, 3) as kvs_area',
                'kvs_log.old_gid as old_gid',
                'kvs_log.new_gid as new_gid',
                'kvs_old.ekate AS old_ekate',
                'kvs_old.virtual_ekatte_name AS old_ekate_name',
                'kvs_old.kad_ident AS old_kad_ident',
            ],
            'where' => [
                'edit_type' => ['column' => 'edit_type', 'compare' => '=', 'prefix' => 'kvs_log', 'value' => 'new_boundary'],
                'new_gid' => ['column' => 'new_gid', 'compare' => 'IN', 'prefix' => 'kvs_log', 'value' => $newGids],
            ],
        ];
        $results = $UserDbPlotsController->getEditedPlots($options, false, false);

        $final = [];
        $resultCount = count($results);
        for ($i = 0; $i < $resultCount; $i++) {
            $new_gid = $results[$i]['new_gid'];
            $final[$new_gid]['gid'] .= ((null === $final[$new_gid]['gid']) ? '' : ',') . $results[$i]['old_gid'];
            $final[$new_gid]['ekate'] = $results[$i]['old_ekate'];
            $final[$new_gid]['ekate_name'] = $results[$i]['old_ekate_name'];
            $final[$new_gid]['kad_ident'] .= ((0 == $i) ? '' : ', ') . $results[$i]['old_kad_ident'];
            $final[$new_gid]['is_checkable'] = false;
            $final[$new_gid]['iconCls'] = 'icon-tree-edit-geometry';

            $documentArea = $results[$i]['document_area'] ? $results[$i]['document_area'] : $results[$i]['kvs_area'];

            $final[$new_gid]['children'][0] = [
                'gid' => $new_gid,
                'ekate' => $results[$i]['new_ekate'],
                'ekate_name' => $results[$i]['old_ekate_name'],
                'kad_ident' => $results[$i]['new_kad_ident'],
                'is_checkable' => true,
                'iconCls' => 'icon-tree-edit-geometry',
                'category' => $results[$i]['category'],
                'area_type' => $results[$i]['area_type'],
                'contract_area' => $documentArea,
                'document_area' => $documentArea,
                'area_for_rent' => $documentArea,
            ];
        }

        return $final;
    }
}
