<?php

namespace TF\Engine\APIClasses\Contracts;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbContracts\UserDbContractsController;
use TF\Engine\Plugins\Core\UserDbOwners\UserDbOwnersController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Грид "Добавяне на стопанство към договор".
 *
 * @rpc-module Contracts
 *
 * @rpc-service-id contracts-farming-datagrid
 *
 * @property UserDbController $UserDbController
 * @property UserDbOwnersController $UserDbOwnersController
 * @property UserDbContractsController $UserDbContractsController
 * @property UsersController $UsersController
 * @property FarmingController $FarmingController
 */
class ContractsFarmingGrid extends TRpcApiProvider
{
    /**
     * Class Controllers
     * Define all class controllers as properties.
     */
    private $UserDbController;
    private $UserDbOwnersController;
    private $UserDbContractsController;
    private $UsersController;
    private $FarmingController;

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getContractsFarmingGridInfo'],
                'validators' => [
                    'filterObj' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'add' => ['method' => [$this, 'addContractsFarmingGridInfo'],
                'validators' => [
                    'filterObj' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * The method gets all the farmings, associated with the current plot and current contract.
     *
     * @api-method read
     *
     * @param array $filterObj - the RPC Parameters
     *                         {
     *                         #item int plot_id - the ID if the currently selected plot
     *                         #item int contract_id - the ID of the currently selected contract
     *                         }
     * @param string $page the current page for the pagination
     * @param string $rows the number of results for the pagination
     * @param string $sort the sorting manner for the pagination
     * @param string $order the order for the pagination
     *
     * @return array array of results
     */
    public function getContractsFarmingGridInfo(array $filterObj, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        if ($this->User->isGuest) {
            return [];
        }

        // create default return
        $return = [
            'rows' => [],
            'total' => 0,
        ];

        // init all needed controllers
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);
        $UserDbContractsController = new UserDbContractsController($this->User->Database);
        $UsersController = new UsersController('Users');
        $FarmingController = new FarmingController('Farming');

        if (!$filterObj['plot_id'] || !(int) $filterObj['plot_id'] || !$filterObj['contract_id'] || !(int) $filterObj['contract_id']) {
            return $return;
        }
        $relation_id = $UserDbContractsController->getContractPlotRelationID($filterObj['contract_id'], $filterObj['plot_id']);

        if (!$relation_id) {
            return $return;
        }

        $farmings = $FarmingController->getUserFarmings();
        $farmingIds = array_keys($farmings);

        $options = [
            'return' => [
                'f.id', 'f.farming_id', 'f.percent',
                "r.rep_name || ' ' || r.rep_surname || ' ' || r.rep_lastname as rep_names",
            ],
            'where' => [
                'pc_rel_id' => ['column' => 'pc_rel_id', 'compare' => '=', 'prefix' => 'f', 'value' => $relation_id],
                'farming_id' => ['column' => 'farming_id', 'compare' => 'IN', 'prefix' => 'f', 'value' => $farmingIds],
            ],
        ];

        $results = $UserDbOwnersController->getPlotFarmingData($options);
        $resultsCount = count($results);

        for ($i = 0; $i < $resultsCount; $i++) {
            $results[$i]['farming'] = $farmings[$results[$i]['farming_id']];
        }

        return [
            'rows' => $results,
            'total' => $resultsCount,
        ];
    }

    /**
     * The method gets all the farmings, that could be associated with the current contract and current plot.
     *
     * @api-method add
     *
     * @param array $filterObj - the RPC Parameters
     *                         {
     *                         #item int plot_id - the ID if the currently selected plot
     *                         #item int contract_id - the ID of the currently selected contract
     *                         }
     * @param string $page the current page for the pagination
     * @param string $rows the number of results for the pagination
     * @param string $sort the sorting manner for the pagination
     * @param string $order the order for the pagination
     *
     * @deprecated Not used
     *
     * @return array array of results
     */
    public function addContractsFarmingGridInfo(array $filterObj, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        if ($this->User->isGuest) {
            return [];
        }
        // create default return
        $return = [
            'rows' => [],
            'total' => 0,
        ];

        // init all needed controllers
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);
        $UserDbContractsController = new UserDbContractsController($this->User->Database);
        $UsersController = new UsersController('Users');
        $FarmingController = new FarmingController('Farming');

        if (!$filterObj['plot_id'] || !(int) $filterObj['plot_id'] || !$filterObj['contract_id'] || !(int) $filterObj['contract_id']) {
            return $return;
        }

        $relation_id = $UserDbContractsController->getContractPlotRelationID($filterObj['contract_id'], $filterObj['plot_id']);
        $farmings = $FarmingController->getUserFarmings();
        $farmingIds = array_keys($farmings);

        $options = [
            'return' => [
                'farming_id',
            ],
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => (int)$filterObj['contract_id']],
                'farming_id' => ['column' => 'farming_id', 'compare' => 'IN', 'value' => $farmingIds],
            ],
        ];

        $contractFarmingId = $UserDbContractsController->getContractsData($options, false, false);

        if (!$relation_id) {
            return $return;
        }

        $options = [
            'return' => [
                'f.id', 'f.farming_id', 'f.percent',
                "r.rep_name || ' ' || r.rep_surname || ' ' || r.rep_lastname as rep_names",
            ],
            'where' => [
                'pc_rel_id' => ['column' => 'pc_rel_id', 'compare' => '=', 'prefix' => 'f', 'value' => $relation_id],
            ],
        ];

        $results = $UserDbOwnersController->getPlotFarmingData($options);
        $resultsCount = count($results);
        // get all group farmings and create array like predefined config
        $final_farming = [];
        // options for farming query
        $options = [
            'where' => [
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'value' => $this->User->GroupID],
                'id' => ['column' => 'id', 'compare' => '<>', 'value' => $contractFarmingId[0]['farming_id']],
            ],
        ];
        $farming_array = $FarmingController->getFarmings($options);
        $farming_arrayCount = count($farming_array);
        if (0 != $farming_arrayCount) {
            for ($i = 0; $i < $farming_arrayCount; $i++) {
                $final_farming[$farming_array[$i]['id']] = $farming_array[$i];
            }
        }

        $options = [
            'return' => ['id', 'name', 'company', 'bulstat', 'is_system', 'address', 'company_address', 'mol'],
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
            'where' => [
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'value' => $this->User->GroupID],
            ],
        ];

        $farming_id_array = [];
        $farming_id_array[] = $contractFarmingId[0]['farming_id'];

        if ($resultsCount > 0) {
            // clear old data
            for ($i = 0; $i < $resultsCount; $i++) {
                $farming_id_array[] = $results[$i]['farming_id'];
            }
        }

        $options['where']['id'] = ['column' => 'id', 'compare' => 'NOT IN', 'value' => $farming_id_array];

        $counter = $FarmingController->getFarmings($options, true, false);
        $farming_data = $FarmingController->getFarmings($options, false, false);

        return [
            'rows' => $farming_data,
            'total' => $counter[0]['count'],
        ];
    }
}
