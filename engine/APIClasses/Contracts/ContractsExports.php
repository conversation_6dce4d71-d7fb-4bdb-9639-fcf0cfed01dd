<?php

namespace TF\Engine\APIClasses\Contracts;

use DateTime;
use Exception;
use Prado\Web\Services\TRpcApiProvider;
use RuntimeException;
use TF\Application\Common\Config;
use TF\Engine\Kernel\ExportWordDocClass;
use TF\Engine\Kernel\PrintPdf;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbContracts\UserDbContractsController;
use TF\Engine\Plugins\Core\UserDbOwners\UserDbOwnersController;
use TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController;
use TF\Engine\Plugins\Core\UserDbSubleases\UserDbSubleasesController;
use TF\Engine\Plugins\Core\Users\UsersController;
use ZipArchive;

/**
 * Creates the export files for printing contracts.
 *
 * @rpc-module Contracts
 *
 * @rpc-service-id contracts-exports
 *
 * @property UserDbController $UserDbController
 * @property UserDbPlotsController $UserDbPlotsController
 * @property UserDbOwnersController $UserDbOwnersController
 * @property UsersController $UsersController
 * @property FarmingController $FarmingController
 * @property UserDbSubleasesController $UserDbSubleasesController
 * @property IUser User
 */
class ContractsExports extends TRpcApiProvider
{
    /**
     * Initialize the required database controllers.
     */
    private $UserDbController;
    private $UserDbPlotsController;
    private $UserDbOwnersController;
    private $UsersController;
    private $FarmingController;
    private $UserDbSubleasesController;

    /**
     * Register all public rpc methods.
     */
    public function registerMethods()
    {
        return [
            'exportContractBlank' => ['method' => [$this, 'exportContractBlank']],
            'deleteFile' => ['method' => [$this, 'deleteFile']],
        ];
    }

    /**
     * Creates contract export file for the selected contract with the selected template.
     *
     * @api-method exportContractBlank
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer template_id
     *                         #item integer contract_id
     *                         #item string  blank_type
     *                         }
     *
     * @return array|void
     */
    public function exportContractBlank($rpcParams)
    {
        $UserDbController = new UserDbController($this->User->Database);

        // get template data
        $options = [
            'tablename' => $UserDbController->DbHandler->tableTemplates,
            'where' => [
                'template_id' => ['column' => 'id', 'compare' => '=', 'value' => $rpcParams['template_id']],
            ],
        ];

        $template = $UserDbController->getItemsByParams($options, false, false);

        if (0 == count($template)) {
            return;
        }

        if (!empty($rpcParams['contractsData'])) {
            return $this->generateArchiveWithContracts($template[0], $rpcParams['contractsData'], $rpcParams);
        }

        return $this->generateSingleContract($template[0], $rpcParams['contract_id'], $rpcParams);
    }

    /**
     * Delete created file.
     *
     * @api-method deleteFile
     *
     * @param string $fileName
     */
    public function deleteFile($fileName)
    {
        $ext = pathinfo($fileName, PATHINFO_EXTENSION);

        if ('pdf' == $ext) {
            @unlink(LAYERS_CONTRACTS_PATH . 'blanks/' . $this->User->GroupID . '/' . $fileName);
        } elseif ('doc' == $ext) {
            @unlink('files/uploads/blanks/' . $fileName);
        }
    }

    public function getTemplate($template, $contract_id, $blankType = 'doc')
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);
        $UsersController = new UsersController('Users');
        $FarmingController = new FarmingController('Farming');
        $UserDbSubleasesController = new UserDbSubleasesController($this->User->Database);
        $UserDBContractsController = new UserDbContractsController($this->User->Database);

        $farmings = $FarmingController->getUserFarmings();
        $farmingIds = array_keys($farmings);

        // get contract data
        $options = [
            'tablename' => $UserDbController->DbHandler->tableContracts,
            'return' => ['su_contracts.*', 'cg.name as group_name'],
            'where' => [
                'contract_id' => ['column' => 'su_contracts.id', 'compare' => '=', 'value' => $contract_id],
                'farming_id' => ['column' => 'su_contracts.farming_id', 'compare' => 'IN', 'value' => $farmingIds],
            ],
        ];

        $options['leftjoin'] = [
            'table' => 'su_contract_group cg',
            'condition' => ' ON (cg.id = su_contracts.group)',
        ];

        $contract_results = $UserDbController->getItemsByParams($options, false, false);
        if (0 == count($contract_results)) {
            return;
        }

        $contractData = $contract_results[0];

        $farming_results = $FarmingController->getFarmings([
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $contractData['farming_id']],
            ],
        ]);

        if (0 == count($farming_results)) {
            return;
        }

        // total_subleases_renta is used in SubleasesExports, but the fields are common, so we can use it in blanks of contracts as well.
        // If it is added anyway, we set it to 0.00 as default because actually there is no rents from subleases.
        if (strstr($template, '[[total_subleases_renta]]')) {
            $template = str_replace('[[total_subleases_renta]]', '0.00', $template);
        }

        // get renta_natura data
        $renta_nat_ids = [];
        $renta_matches = $this->addRentaVNatura($template, $contract_id, $renta_nat_ids, $contractData);

        if (strstr($template, '[[owner_detailed')) {
            $template = str_replace('[[owner_detailed', '[[kontragent', $template);
        }

        preg_match_all("/(?:\[\[contract_signer\s)(?<columns>[\w\s-]+)\]\]/", $template, $contractSignerMatches, PREG_UNMATCHED_AS_NULL);
        [$signerMatches, $signerColumnsGroupMatches] = $contractSignerMatches;
        $signerMatches = array_combine($signerMatches, $signerColumnsGroupMatches);

        if (count($signerMatches)) {
            $contractSigners = $this->getContractSigner($contract_id);

            foreach ($signerMatches as $match => $columnsStr) {
                $contractSignersCount = count($contractSigners);
                $signerString = '';
                $signerColumns = array_unique(explode(' ', $columnsStr));

                for ($i = 0; $i < $contractSignersCount; $i++) {
                    $signer = $contractSigners[$i];
                    $signerString .= $UserDBContractsController->setOwnerTemplateStr($signerColumns, $signer);
                    if (true == $signer['is_heritor']) {
                        list($ownerId) = explode('.', $signer['path']);
                        $ownerData = current($this->getOwnerData($ownerId));
                        $signerString .= ', наследник на: ' . $ownerData['owner_names'];
                    }
                    $signerString .= ($i == $contractSignersCount - 1) ? '' : ';<br/>';
                }

                $template = str_replace($match, $signerString, $template);
            }
        }

        if (strstr($template, '[[today')) {
            $template = str_replace('[[today]]', date('d.m.Y'), $template);
        }

        // get owners and owner_reps data
        list($kontragent_matches, $kontragent_rep_matches) = $UserDBContractsController->getKontragentMatchedColumns($template);

        if (!empty(array_filter($kontragent_matches)) || !empty(array_filter($kontragent_rep_matches))) {
            $mainOwners = $UserDBContractsController->getOwnerAndReps($contract_id);
            $owners = $mainOwners;
            $ownersCount = count($owners);
            $kontragentDetailedData = [];
            foreach ($kontragent_matches[1] as $key => $kontragent_match) {
                $kontragent_columns = explode(' ', $kontragent_match);
                $ownersString = '';

                for ($i = 0; $i < $ownersCount; $i++) {
                    $ownersPlotContrDataArr = $UserDBContractsController->setOwnerDataArr($mainOwners[$i]);

                    if ($ownersCount > 1) {
                        $ownersString .= ($i + 1) . '. ';
                    }
                    if (!$mainOwners[$i]['is_dead']) {
                        $ownersString .= $UserDBContractsController->setOwnerTemplateStr($kontragent_columns, $mainOwners[$i]);
                        if ($mainOwners[$i]['owner_type'] == $GLOBALS['Owners']['Types'][1]['id'] && '' !== $mainOwners[$i]['owner_reps'] || in_array('owner_rep', $kontragent_columns, true)) {
                            $rep_string = $UserDBContractsController->concatRepsData(
                                $mainOwners[$i]['owner_reps'],
                                $mainOwners[$i]['proxy_rel']
                            );
                            $ownersString .= $rep_string ? ', представляван от: ' . $rep_string : '';
                        }
                        $ownersString .= ($i == $ownersCount - 1) ? '' : ';<br/>';
                    } else {
                        $path = $mainOwners[$i]['owner_id'] . '.*{1}';
                        $heirsPaths = [];
                        foreach ($ownersPlotContrDataArr['pc_rel_id_array'] as $relkey => $relation_id) {
                            $relHeirs = $UserDBContractsController->getOwnerHeritorsForExports(
                                $path,
                                $ownersPlotContrDataArr['percent'][$relkey],
                                1,
                                $relation_id
                            );

                            foreach ($relHeirs as $heirKey => $heir) {
                                if (in_array($heir['path'], $heirsPaths)) {
                                    continue;
                                }
                                $owners[] = $heir;
                                $heir[$i]['owner_reps'] = $UserDBContractsController->concatRepsData(
                                    $heir['owner_reps'],
                                    $heir['proxy_rel']
                                );
                                if (in_array('owner_rep', $kontragent_columns, true)) {
                                    $rep_string = $UserDBContractsController->concatRepsData($heir['owner_reps'], $heir['proxy_rel']);
                                    $ownersString .= $rep_string ? ', представляван от: ' . $rep_string : '';
                                }
                                $ownersString .= $UserDBContractsController->setOwnerTemplateStr($kontragent_columns, $heir);
                                $ownersString .= ' като наследник на <br/>' . $mainOwners[$i]['owner_names'] . ' ';
                                $ownersString .= (10 == strlen($mainOwners[$i]['egn']) || 9 == strlen($mainOwners[$i]['eik'])) ? ', ' . $mainOwners[$i]['egn_eik'] : '';
                                $ownersString .= '<br/>';
                                $heirsPaths[] = $heir['path'];
                            }
                        }
                    }
                }
                // IF contract is sublease
                if (0 == $ownersCount && $contractData['from_sublease']) {
                    // get all group farmings and create array like predefined config
                    $final_farming = $UserDBContractsController->getFinalFarmings();
                    $options = [
                        'return' => [
                            'cc.id',
                            'cc.farming_id',
                            'cc.proxy_num',
                            "to_char(cc.proxy_date,'DD.MM.YYYY') as proxy_date",
                            "r.rep_name || ' ' || r.rep_surname || ' ' || r.rep_lastname as rep_names",
                        ],
                        'where' => [
                            'sublease_id' => [
                                'column' => 'contract_id',
                                'compare' => '=',
                                'prefix' => 'cc',
                                'value' => $contractData['from_sublease'],
                            ],
                        ],
                    ];

                    $results = $UserDbSubleasesController->getSubleasesFarmingContragentsData($options);
                    $repSublease = null;
                    $countResults = count($results);
                    for ($i = 0; $i < $countResults; $i++) {
                        $results[$i]['farming'] = $final_farming[$results[$i]['farming_id']]['name'];
                        $results[$i]['mol'] = $final_farming[$results[$i]['farming_id']]['mol'];
                        $results[$i]['address'] = $final_farming[$results[$i]['farming_id']]['address'];
                        $results[$i]['bulstat'] = $final_farming[$results[$i]['farming_id']]['bulstat'];
                        $repSublease .= $results[$i]['farming'] ? $results[$i]['farming'] : '';
                        $repSublease .= $results[$i]['mol'] ? ', с управител ' . $results[$i]['mol'] : '';
                        $repSublease .= $results[$i]['address'] ? ', адрес ' . $results[$i]['address'] : '';
                        $repSublease .= $results[$i]['bulstat'] ? ', ЕИК ' . $results[$i]['bulstat'] : '';
                        $repSublease .= $results[$i]['rep_names'] ? ', представляван от ' . $results[$i]['rep_names'] : '';
                        $repSublease .= $results[$i]['proxy_num'] ? ', съгласно пълномощно № ' . $results[$i]['proxy_num'] . '/' . $results[$i]['proxy_date'] : '';
                        $repSublease .= ($i == count($results) - 1) ? '' : ';<br>';
                    }

                    $options = [
                        'tablename' => $UserDbController->DbHandler->tableContracts,
                        'where' => [
                            'contract_id' => [
                                'column' => 'id',
                                'compare' => '=',
                                'value' => $contractData['sublease_id'],
                            ],
                        ],
                    ];

                    $cr = $UserDbController->getItemsByParams($options, false, false);

                    $options = [
                        'where' => [
                            'id' => ['column' => 'id', 'compare' => '=', 'value' => $cr[0]['farming_id']],
                        ],
                    ];

                    $fr = $FarmingController->getFarmings($options, false, false);

                    $ownersString .= $fr[0]['name'] ? $fr[0]['name'] : '';
                    $ownersString .= $fr[0]['mol'] ? ', с управител ' . $fr[0]['mol'] : '';
                    $ownersString .= $fr[0]['address'] ? ', адрес ' . $fr[$i]['address'] : '';
                    $ownersString .= $fr[0]['rep_names'] ? ', представляван от ' . $fr[0]['rep_names'] : '';
                    $ownersString .= $fr[0]['proxy_num'] ? ', съгласно пълномощно № ' . $fr[0]['proxy_num'] . '/' . $fr[0]['proxy_date'] : '';
                    $ownersString .= ';<br>';
                }

                $kontragentDetailedData[$key] = $ownersString;
            }
            unset($relHeirs, $relkey, $relation_id, $heirKey, $heir, $key, $ownersCount, $heirsPaths, $path, $rep_string);
        }

        if (!empty(array_filter($kontragent_rep_matches))) {
            // rappresentative loop
            $kontragentRepDetailedData = [];
            $reps = [];
            foreach ($owners as $i => $owner) {
                if (!isset($owner['rep_id']) && empty($owner['rep_id'])) {
                    continue;
                }
                $reps[] = [
                    'rep_names' => $owner['rep_names'],
                    'rep_egn' => $owner['rep_egn'],
                    'rep_lk' => $owner['rep_lk'],
                    'rep_lk_izdavane' => $owner['rep_lk_izdavane'],
                    'rep_phone' => $owner['rep_phone'],
                    'rep_address' => $owner['rep_address'],
                    'rep_iban' => $owner['rep_iban'],
                ];
            }
            $reps = array_values(array_unique($reps, SORT_REGULAR));
            $repCount = count($reps);
            foreach ($kontragent_rep_matches[1] as $key => $kontragent_rep_match) {
                $kontragent_rep_columns = explode(' ', $kontragent_rep_match);
                $repString = '';
                foreach ($reps as $i => $rep) {
                    if ($repCount > 1) {
                        $repString .= ($i + 1) . '. ';
                    }
                    $repString .= $UserDBContractsController->setRepTemplateStr($kontragent_rep_columns, $rep);
                    $repString .= ($i == $repCount - 1 && '' != $repString) ? '' : ';<br/>';
                }
                $kontragentRepDetailedData[$key] = $repString;
            }
        }

        if (strstr($template, '[[zemlishte_ekatte]]')) {
            $ekateNames = $UserDbPlotsController->getEkateNamesForContract($contract_id);
            $tmpEkateString = '';
            $ekateNamesCount = count($ekateNames);
            for ($ekN = 0; $ekN < $ekateNamesCount; $ekN++) {
                $tmpEkateString .= $ekateNames[$ekN]['zemlishte_ekatte'];

                if ($ekN < $ekateNamesCount - 1) {
                    $tmpEkateString .= ', ';
                }
            }

            $template = str_replace('[[zemlishte_ekatte]]', $tmpEkateString, $template);
        }

        // get plots data and create table
        if (
            false !== strstr($template, '[[obobshtena_rent_area]]')
            || false !== strstr($template, '[[obobshtena_contract_area]]')
            || strstr($template, '[[obobshtena_obrabotvaema_area]]')
            || strstr($template, '[[obobshtena_neobrabotvaema_area]]')
            || false !== strstr($template, '[[imoti]]')
            || false !== strstr($template, '[[imoti_zemlishta]]')
            || false !== strstr($template, '[[imoti_zemlishta_kadident]]')
            || false !== strstr($template, '[[imoti_podrobno')
        ) {
            $options = [
                'return' => [
                    'kvs_gid as gid',
                    'kad_ident',
                    'old_kad_ident',
                    'virtual_ntp_title as area_type',
                    'virtual_category_title as category',
                    'mestnost',
                    'number',
                    'kvs_ekate as ekate',
                    'virtual_ekatte_name as land',
                    'contract_area',
                    'area_for_rent',
                    'document_area',
                    'st_area(kvs_geom) as geom_area',
                    'allowable_area',
                    '(CASE WHEN coalesce(kvs_allowable_area, allowable_area) < contract_area THEN coalesce(kvs_allowable_area, allowable_area) ELSE contract_area END) as kvs_allowable_area',
                    'virtual_non_arable_area as kvs_not_allowable_area',
                    'pc_comment',
                    "LPAD(masiv::text, 3, '0') || LPAD(number::text, 3, '0') as imot_number",
                    "LPAD(masiv::text, 3, '0') || LPAD(number::text, 3, '0') as imoten_nomer",
                    '(CASE WHEN rent_per_plot is not null THEN rent_per_plot ELSE renta END) as rent_per_plot',
                ],
                'where' => [
                    'contract_id' => ['column' => 'c_id', 'compare' => '=', 'value' => $contract_id],
                    'annex_action' => [
                        'column' => 'annex_action',
                        'prefix' => 'data',
                        'compare' => '=',
                        'value' => 'added',
                    ],
                ],
                'include_archived_plots' => true,
                'sort' => 'kad_ident COLLATE "alpha_numeric_bg"',
                'order' => 'asc',
                'group' => 'gid, kad_ident, old_kad_ident, virtual_ntp_title, virtual_category_title, mestnost, "number", ekate, virtual_ekatte_name, contract_area, area_for_rent, document_area, geom_area, allowable_area, imot_number, imoten_nomer, rent_per_plot, renta, kvs_allowable_area, virtual_non_arable_area, pc_comment',
            ];

            $plotsResults = $UserDbPlotsController->getFullPlotData($options, false, false);

            $ekates = [];
            $plotsResCount = count($plotsResults);
            for ($i = 0; $i < $plotsResCount; $i++) {
                if (!in_array($plotsResults[$i]['ekate'], $ekates)) {
                    $ekates[] = $plotsResults[$i]['ekate'];
                }
                $plotsResults[$i]['contract_area'] = number_format($plotsResults[$i]['contract_area'], 3, '.', '');
                $plotsResults[$i]['document_area'] = number_format($plotsResults[$i]['document_area'], 3, '.', '');
                $plotsResults[$i]['area_for_rent'] = number_format($plotsResults[$i]['area_for_rent'], 3, '.', '');
                $plotsResults[$i]['rent_per_plot'] = number_format($plotsResults[$i]['rent_per_plot'], 2, '.', '');
                $plotsResults[$i]['allowable_area'] = number_format($plotsResults[$i]['allowable_area'], 3, '.', '');
                $plotsResults[$i]['kvs_allowable_area'] = number_format($plotsResults[$i]['kvs_allowable_area'], 3, '.', '');
                $plotsResults[$i]['kvs_not_allowable_area'] = number_format($plotsResults[$i]['kvs_not_allowable_area'], 3, '.', '');

                if ('' == $plotsResults[$i]['kad_ident']) {
                    $plotsResults[$i]['kad_ident'] = '[Няма информация]';
                }
            }
            if (false !== strstr($template, '[[imoti_zemlishta]]') && !empty($ekates)) {
                $plots_by_ekatte_string = '';
                $options = [
                    'return' => [
                        'obl.obl_name',
                        'obs.obsht_name',
                        'kmet.kmet_name',
                        'ekatte.ekatte_code',
                        'ekatte.ekatte_name',
                    ],
                    'where' => [
                        'ekate' => [
                            'column' => 'ekatte_code',
                            'compare' => 'IN',
                            'prefix' => 'ekatte',
                            'value' => $ekates,
                        ],
                    ],
                ];
                $now = new DateTime();
                $plots_by_ekatte = $UsersController->getEkatteOnlyItems($options);
                $plotsCount = count($plotsResults);
                foreach ($plots_by_ekatte as $key => $value) {
                    $plots_by_ekatte_string .= '<p>Изброените по долу имоти от землището на с./гр.' . $value['ekatte_name'] . ', с ЕКАТТЕ ' . $value['ekatte_code'] . ', общ. ' . $value['obsht_name'] . ', обл. ' . $value['obl_name'] . ':<br/>';
                    $plotsNumByEkate = 1;
                    for ($i = 0; $i < $plotsCount; $i++) {
                        if ($plotsResults[$i]['ekate'] == $value['ekatte_code']) {
                            $neighbourOptions = [
                                'return' => [
                                    'kvs2.kad_ident',
                                    '(case WHEN pc.contract_area is NULL then (ST_Area(kvs2.geom)/1000) else pc.contract_area end) as area',
                                    'kvs2.virtual_ntp_title as area_type',
                                    "LPAD(kvs2.masiv::text, 3, '0') || LPAD(kvs2.number::text, 3, '0') as imot_number",
                                    'kvs2.is_edited', 'kvs2.edit_active_from',
                                ],
                                'kad_ident' => $plotsResults[$i]['kad_ident'],
                                'contract_id' => $contract_id,
                            ];
                            $neighbours = $UserDbPlotsController->getPlotNeighbours($neighbourOptions, false, false);
                            $neighbourString = '';
                            $neighboursCount = count($neighbours);
                            for ($j = 0; $j < $neighboursCount; $j++) {
                                $editActiveFrom = new DateTime($neighbours[$j]['edit_active_from']);
                                if (true == $neighbours[$j]['is_edited'] && (null !== $neighbours[$j]['edit_active_from'] && $editActiveFrom <= $now)) {
                                    continue;
                                }
                                $neighbourString .= '№ ' . $neighbours[$j]['imot_number'];
                                $neighbourString .= isset($neighbours[$j]['area_type']) ? (' с НТП ' . $neighbours[$j]['area_type']) : '';
                                $neighbourString .= ' с площ ' . number_format(
                                    $neighbours[$j]['area'],
                                    3,
                                    '.',
                                    ''
                                ) . ' дка';
                                $neighbourString .= '<br/>';
                            }

                            $plots_by_ekatte_string .= $plotsNumByEkate . '. ';
                            $plots_by_ekatte_string .= 'Имот';
                            $plots_by_ekatte_string .= ', идентификатор ' . $plotsResults[$i]['imot_number'];
                            $plots_by_ekatte_string .= isset($plotsResults[$i]['area_type']) ? (' с НТП ' . $plotsResults[$i]['area_type']) : '';
                            $plots_by_ekatte_string .= isset($plotsResults[$i]['document_area']) ? (' с площ по документ ' . $plotsResults[$i]['document_area'] . ' дка') : '';
                            $plots_by_ekatte_string .= isset($plotsResults[$i]['contract_area']) ? (', с площ по договор ' . $plotsResults[$i]['contract_area'] . ' дка') : '';
                            $plots_by_ekatte_string .= !empty($plotsResults[$i]['mestnost']) ? (', находяща се в местността ' . $plotsResults[$i]['mestnost']) : '';

                            $plots_by_ekatte_string .= !empty($neighbourString) ? ' ,при граници(съседи): <br/>' . $neighbourString : '';
                            $plots_by_ekatte_string .= '<br/>';
                            $plotsNumByEkate++;
                        }
                    }
                    $plots_by_ekatte_string .= '</p>';
                }
            }

            if (strstr($template, '[[imoti_zemlishta_kadident]]') && !empty($ekates)) {
                $plots_by_ekatte_kadident_string = '';
                $options = [
                    'return' => [
                        'obl.obl_name',
                        'obs.obsht_name',
                        'kmet.kmet_name',
                        'ekatte.ekatte_code',
                        'ekatte.ekatte_name',
                    ],
                    'where' => [
                        'ekate' => [
                            'column' => 'ekatte_code',
                            'compare' => 'IN',
                            'prefix' => 'ekatte',
                            'value' => $ekates,
                        ],
                    ],
                ];

                $plots_by_ekatte = $UsersController->getEkatteOnlyItems($options);
                $plotsCount = count($plotsResults);
                foreach ($plots_by_ekatte as $key => $value) {
                    $plots_by_ekatte_kadident_string .= '<p>Изброените по долу имоти от землището на с./гр.' . $value['ekatte_name'] . ', с ЕКАТТЕ ' . $value['ekatte_code'] . ', общ. ' . $value['obsht_name'] . ', обл. ' . $value['obl_name'] . ':<br/>';
                    $plotsNumByEkate = 1;
                    for ($i = 0; $i < $plotsCount; $i++) {
                        if ($plotsResults[$i]['ekate'] == $value['ekatte_code']) {
                            $neighbourOptions = [
                                'return' => [
                                    'kvs2.kad_ident',
                                    '(case WHEN pc.contract_area is NULL then (ST_Area(kvs2.geom)/1000) else pc.contract_area end) as area',
                                    'kvs2.virtual_ntp_title as area_type',
                                    "LPAD(kvs2.masiv::text, 3, '0') || LPAD(kvs2.number::text, 3, '0') as imot_number",
                                ],
                                'kad_ident' => $plotsResults[$i]['kad_ident'],
                                'contract_id' => $contract_id,
                            ];
                            $neighbours = $UserDbPlotsController->getPlotNeighbours(
                                $neighbourOptions,
                                false,
                                false
                            );
                            $neighbourString = '';
                            $neighboursCount = count($neighbours);
                            for ($j = 0; $j < $neighboursCount; $j++) {
                                $neighbourString .= '№ ' . $neighbours[$j]['kad_ident'];
                                $neighbourString .= isset($neighbours[$j]['area_type']) ? (' с НТП ' . $neighbours[$j]['area_type']) : '';
                                $neighbourString .= ' с площ ' . number_format(
                                    $neighbours[$j]['area'],
                                    3,
                                    '.',
                                    ''
                                ) . ' дка';
                                $neighbourString .= '<br/>';
                            }

                            $plots_by_ekatte_kadident_string .= $plotsNumByEkate . '. ';
                            $plots_by_ekatte_kadident_string .= 'Имот';
                            $plots_by_ekatte_kadident_string .= ', идентификатор ' . $plotsResults[$i]['kad_ident'];
                            $plots_by_ekatte_kadident_string .= isset($plotsResults[$i]['area_type']) ? (' с НТП ' . $plotsResults[$i]['area_type']) : '';
                            $plots_by_ekatte_kadident_string .= isset($plotsResults[$i]['document_area']) ? (' с площ по документ ' . $plotsResults[$i]['document_area'] . ' дка') : '';
                            $plots_by_ekatte_kadident_string .= isset($plotsResults[$i]['contract_area']) ? (', с площ по договор ' . $plotsResults[$i]['contract_area'] . ' дка') : '';
                            $plots_by_ekatte_kadident_string .= !empty($plotsResults[$i]['mestnost']) ? (', находяща се в местността ' . $plotsResults[$i]['mestnost']) : '';

                            $plots_by_ekatte_kadident_string .= !empty($neighbourString) ? ' ,при граници(съседи): <br/>' . $neighbourString : '';
                            $plots_by_ekatte_kadident_string .= '<br/>';
                            $plotsNumByEkate++;
                        }
                    }
                    $plots_by_ekatte_kadident_string .= '</p>';
                }
            }

            if (strstr($template, '[[imoti]]') && !empty($ekates)) {
                $plotsData = '<table align="center" cellspacing="0" cellpadding="3" border="1">';
                $plotsData .= '<thead>';
                $plotsData .= '<tr align="center">';
                $plotsData .= '<th>№</th>';
                $plotsData .= '<th>Идентификатор</th>';
                $plotsData .= '<th>Землище</th>';
                $plotsData .= '<th>Местност</th>';
                $plotsData .= '<th>НТП</th>';
                $plotsData .= '<th>Площ по <br/>договор(дка)</th>';
                $plotsData .= '<th>Площ по <br/>документ(дка)</th>';
                $plotsData .= '</tr>';
                $plotsData .= '</thead>';
                $plotsData .= '<tbody>';

                $total_area = 0;
                $total_document_area = 0;
                $total_kvs_allowable_area = 0;
                $total_kvs_not_allowable_area = 0;

                for ($i = 0; $i < $plotsResCount; $i++) {
                    $plotsData .= '<tr>';
                    $plotsData .= '<td width="20" align="center">' . ($i + 1) . '</td>';
                    $plotsData .= '<td width="115" align="center">' . $plotsResults[$i]['kad_ident'] . '</td>';
                    $plotsData .= '<td width="120" align="center">' . $plotsResults[$i]['land'] . '</td>';
                    $plotsData .= '<td width="115" align="center">' . $plotsResults[$i]['mestnost'] . '</td>';
                    $plotsData .= '<td width="180" align="center">' . $plotsResults[$i]['area_type'] . '</td>';
                    $plotsData .= '<td width="70" align="center">' . $plotsResults[$i]['contract_area'] . '</td>';
                    $plotsData .= '<td width="70" align="center">' . $plotsResults[$i]['document_area'] . '</td>';
                    $plotsData .= '</tr>';

                    $total_area += $plotsResults[$i]['contract_area'];
                    $total_document_area += $plotsResults[$i]['document_area'];
                    $total_kvs_allowable_area += $plotsResults[$i]['kvs_allowable_area'];
                    $total_kvs_non_allowable_area += $plotsResults[$i]['kvs_non_allowable_area'];
                }
                $plotsData .= '<tr>';
                $plotsData .= '<td colspan="4"></td>';
                $plotsData .= '<td width="180" align="center"><b>Общо</b></td>';
                $plotsData .= '<td width="70" align="center"><b>' . number_format(
                    $total_area,
                    3,
                    '.',
                    ''
                ) . '</b></td>';
                $plotsData .= '<td width="70" align="center"><b>' . number_format(
                    $total_document_area,
                    3,
                    '.',
                    ''
                ) . '</b></td>';
                $plotsData .= '</tr>';

                $plotsData .= '</tbody>';
                $plotsData .= '</table>';
            }

            if (strstr($template, '[[imoti_podrobno')) {
                $regex = "/\[\[imoti_podrobno (.*?)\]\]/";
                preg_match_all($regex, $template, $matches);

                $plotsDetailedDataArr = [];
                foreach ($matches[1] as $key => $match) {
                    $columns = explode(' ', $match);
                    $tableFontSize = '';
                    if ('doc' == $blankType && count($columns) >= 7) {
                        $tableFontSize = 'font-size: 12px;';
                    }

                    if (count($columns) > 0) {
                        $plotsDetailedData = '<table style="' . $tableFontSize . '" cellspacing="0" cellpadding="3" border="1">';

                        // header
                        $plotsDetailedData .= '<thead>';
                        $plotsDetailedData .= '<tr align="center">';
                        $plotsDetailedData .= '<th>№</th>';

                        foreach ($columns as $keyC => $column) {
                            $plotsDetailedData .= '<th>' . $GLOBALS['Contracts']['variables_plots_detailed'][$column] . '</th>';
                        }

                        $plotsDetailedData .= '</tr>';
                        $plotsDetailedData .= '</thead>';

                        // body
                        $plotsDetailedData .= '<tbody>';

                        $total_area = 0;
                        $total_document_area = 0;
                        $total_area_for_rent = 0;
                        $total_allowable_area = 0;
                        for ($i = 0; $i < $plotsResCount; $i++) {
                            $plotsResults[$i]['zemlishte'] = $plotsResults[$i]['land'];
                            $plotsResults[$i]['ntp'] = $plotsResults[$i]['area_type'];

                            $plotsDetailedData .= '<tr>';
                            $plotsDetailedData .= '<td style="white-space: nowrap;" align="center">' . ($i + 1) . '</td>';
                            foreach ($columns as $keyCo => $column) {
                                if ('comment' === $column) {
                                    $plotsDetailedData .= '<td width="115" align="center">' . $plotsResults[$i]['pc_comment'] . '</td>';

                                    continue;
                                }
                                if ('ntp' === $column) {
                                    $plotsDetailedData .= '<td width="115" align="center">' . $plotsResults[$i][$column] . '</td>';

                                    continue;
                                }
                                if ('plot_neighbors' === $column) {
                                    $neighbourOptions = [
                                        'return' => [
                                            'kvs2.kad_ident',
                                        ],
                                        'kad_ident' => $plotsResults[$i]['kad_ident'],
                                        'contract_id' => $contract_id,
                                    ];
                                    $neighbours = $UserDbPlotsController->getPlotNeighbours($neighbourOptions, false, false);
                                    $plotsDetailedData .= '<td style="white-space: nowrap;" align="center">' . implode('<br>', array_column($neighbours, 'kad_ident')) . '</td>';

                                    continue;
                                }

                                $plotsDetailedData .= '<td style="white-space: nowrap;" align="center">' . $plotsResults[$i][$column] . '</td>';
                            }

                            $plotsDetailedData .= '</tr>';

                            if (in_array('contract_area', $columns) || in_array(
                                'document_area',
                                $columns
                            ) || in_array('area_for_rent', $columns) || in_array(
                                'allowable_area',
                                $columns
                            )) {
                                $total_area += $plotsResults[$i]['contract_area'];
                                $total_document_area += $plotsResults[$i]['document_area'];
                                $total_area_for_rent += $plotsResults[$i]['area_for_rent'];
                                $total_allowable_area += $plotsResults[$i]['allowable_area'];
                                $total_kvs_allowable_area += $plotsResults[$i]['kvs_allowable_area'];
                                $total_kvs_not_allowable_area += $plotsResults[$i]['kvs_not_allowable_area'];
                            }
                        }

                        // footer
                        if ($total_area > 0 || $total_document_area > 0 || $total_area_for_rent > 0) {
                            $plotsDetailedData .= '<tr>';

                            // column for numeration
                            $plotsDetailedData .= '<td style="white-space: nowrap;">Общо</td>';

                            $columnsCount = count($columns);
                            for ($m = 0; $m < $columnsCount; $m++) {
                                $column = $columns[$m];

                                if ('contract_area' === $column) {
                                    $plotsDetailedData .= '<td style="white-space: nowrap;" align="center"><b>' . number_format(
                                        $total_area,
                                        3,
                                        '.',
                                        ''
                                    ) . '</b></td>';
                                } elseif ('document_area' === $column) {
                                    $plotsDetailedData .= '<td style="white-space: nowrap;" align="center"><b>' . number_format(
                                        $total_document_area,
                                        3,
                                        '.',
                                        ''
                                    ) . '</b></td>';
                                } elseif ('area_for_rent' === $column) {
                                    $plotsDetailedData .= '<td style="white-space: nowrap;" align="center"><b>' . number_format(
                                        $total_area_for_rent,
                                        3,
                                        '.',
                                        ''
                                    ) . '</b></td>';
                                } elseif ('allowable_area' === $column) {
                                    $plotsDetailedData .= '<td style="white-space: nowrap;" align="center"><b>' . number_format(
                                        $total_allowable_area,
                                        3,
                                        '.',
                                        ''
                                    ) . '</b></td>';
                                } elseif ('kvs_allowable_area' === $column) {
                                    $plotsDetailedData .= '<td style="white-space: nowrap;" align="center"><b>' . number_format(
                                        $total_kvs_allowable_area,
                                        3,
                                        '.',
                                        ''
                                    ) . '</b></td>';
                                } elseif ('kvs_not_allowable_area' === $column) {
                                    $plotsDetailedData .= '<td style="white-space: nowrap;" align="center"><b>' . number_format(
                                        $total_kvs_not_allowable_area,
                                        3,
                                        '.',
                                        ''
                                    ) . '</b></td>';
                                } else {
                                    $plotsDetailedData .= '<td style="white-space: nowrap;"></td>';
                                }
                            }

                            $plotsDetailedData .= '</tr>';
                        }

                        $plotsDetailedData .= '</tbody>';
                        $plotsDetailedData .= '</table>';

                        $plotsDetailedDataArr[$key] = $plotsDetailedData;
                    }
                }
            }
        }

        if (strstr($template, '[[imoti_specifichni_renti]]')) {
            $plotRentsText = '';
            $plotsRentsTypes = $UserDBContractsController->getPlotsRents([
                'contract_id' => $contract_id,
            ]);
            $plotsRentsTypesCount = count($plotsRentsTypes);

            $plotRentsText = '<table align="center" cellspacing="0" cellpadding="3" border="1">';
            $plotRentsText .= '<thead>';
            $plotRentsText .= '<tr align="center">';
            $plotRentsText .= '<th>№</th>';
            $plotRentsText .= '<th>Идентификатор</th>';
            $plotRentsText .= '<th>Критерии</th>';
            $plotRentsText .= '<th>Площ</th>';
            $plotRentsText .= '<th>Ренти</th>';
            $plotRentsText .= '</tr>';
            $plotRentsText .= '</thead>';
            $plotRentsText .= '<tbody>';

            $rowNum = 1;
            foreach ($plotsRentsTypes as $plotRentTypes) {
                if (empty($plotRentTypes['area'])) {
                    continue;
                }
                $rentTypeRents = json_decode($plotRentTypes['rents_json'], true);
                $rentTypeTxt = '';
                if ($rentTypeRents['money'] > 0) {
                    $rentTypeTxt .= $rentTypeRents['money'] . ' лв.<br>';
                }

                foreach ($rentTypeRents['rent_nature'] as $key => $rent) {
                    if ($rent['value'] > 0) {
                        $rentTypeTxt .= $rent['name'] . ' - ' . $rent['value'] . ' ' . $GLOBALS['Contracts']['renta_units'][$rent['unit']]['name'] . '<br>';
                    }
                }

                $plotRentsText .= '<tr>';
                $plotRentsText .= '<td width="20" align="center">' . $rowNum . '</td>';
                $plotRentsText .= '<td width="115" align="center">' . $plotRentTypes['kad_ident'] . '</td>';
                $plotRentsText .= '<td width="120" align="center">' . $plotRentTypes['value_txt'] . '</td>';
                $plotRentsText .= '<td width="115" align="center">' . $plotRentTypes['area'] . '</td>';
                $plotRentsText .= '<td width="180" align="center">' . $rentTypeTxt . '</td>';
                $plotRentsText .= '</tr>';

                $rowNum++;
            }

            $plotRentsText .= '</tbody>';
            $plotRentsText .= '</table>';

            $template = str_replace('[[imoti_specifichni_renti]]', $plotRentsText, $template);
        }

        if (strstr($template, '[[imoti_kategoriq]]')) {
            $plots_by_category_string = $this->plotsByCategoryData($contract_id);
        }

        if (strstr($template, '[[timespan_farming_years]]')) {
            $timespan_message = 'договорът действа в срок от ';
            if ('01' == strftime('%d', strtotime($contractData['start_date']))
                && '10' == strftime('%m', strtotime($contractData['start_date']))
                && '30' == strftime('%d', strtotime($contractData['due_date']))
                && '09' == strftime('%m', strtotime($contractData['due_date']))) {
                $farming_years = strftime('%Y', strtotime($contractData['due_date'])) - strftime(
                    '%Y',
                    strtotime($contractData['start_date'])
                );
                $timespan_message = $timespan_message . $farming_years . ((1 == $farming_years) ? ' стопанска година' : ' стопански години');
            } else {
                $timespan_message .= '...... стопански години';
            }
        }

        if (strstr($template, '[[padej]]')) {
            if ($contractData['payday']) {
                $payday = explode('-', $contractData['payday']);
                $contractData['payday'] = $payday[0] . ' ' . $GLOBALS['Months'][$payday[1]];
            } else {
                $contractData['payday'] = '-';
            }
        }

        $template = str_replace('[[renta]]', BGNtoEURO($contractData['overall_renta'] ?? $contractData['renta']), $template);

        $total_renta = 0;
        if (
            false !== strstr($template, '[[total_renta]]')
            || false !== strstr($template, '[[obobshtena_rent_area]]')
            || false !== strstr($template, '[[obobshtena_contract_area]]')
            || strstr($template, '[[obobshtena_obrabotvaema_area]]')
            || strstr($template, '[[obobshtena_neobrabotvaema_area]]')
        ) {
            $options = [
                'return' => [
                    'round(SUM((CASE WHEN area_for_rent is not null THEN area_for_rent ELSE contract_area END) * COALESCE(po_percent, pf_percent) / 100 *
                    (CASE WHEN rent_per_plot is not null THEN rent_per_plot ELSE renta END))::numeric, 2) AS total_renta',
                    'SUM(contract_area::numeric * (COALESCE(po_percent, pf_percent) / 100)) as contract_area',
                    'SUM(area_for_rent::numeric * (COALESCE(po_percent, pf_percent) / 100)) as area_for_rent',
                    'SUM(coalesce(coalesce(kvs_allowable_area::numeric, allowable_area::numeric), 0) * (COALESCE(po_percent, pf_percent) / 100)) as kvs_allowable_area',
                    'SUM(coalesce(coalesce(virtual_non_arable_area::numeric, allowable_area::numeric), 0) * (COALESCE(po_percent, pf_percent) / 100)) as kvs_not_allowable_area',
                ],
                'where' => [
                    'contract_id' => ['column' => 'c_id', 'compare' => '=', 'value' => $contract_id],
                    'is_dead' => ['column' => 'is_dead', 'compare' => '=', 'value' => 'false'],
                    'annex_action' => [
                        'column' => 'annex_action',
                        'prefix' => 'data',
                        'compare' => '=',
                        'value' => 'added',
                    ],
                ],
                'group' => 'c_id',
                'no_mat_view_join' => true,
            ];

            $plotsResults = $UserDbPlotsController->getFullPlotData($options, false, false);

            $total_renta = reset($plotsResults)['total_renta'];

            $total_contract_area = $total_rent_area = $total_kvs_allowabla_area = 0;
            foreach ($plotsResults as $plotData) {
                $total_contract_area += $plotData['contract_area'];
                $total_rent_area += $plotData['area_for_rent'];
                $total_kvs_allowabla_area += $plotData['kvs_allowable_area'];
                $total_kvs_not_allowabla_area += $plotData['kvs_not_allowable_area'];
            }

            $template = str_replace('[[obobshtena_rent_area]]', number_format($total_rent_area, 3, '.', ''), $template);
            $template = str_replace('[[obobshtena_contract_area]]', number_format($total_contract_area, 3, '.', ''), $template);
            $template = str_replace('[[obobshtena_obrabotvaema_area]]', number_format($total_kvs_allowabla_area, 3, '.', ''), $template);
            $template = str_replace('[[obobshtena_neobrabotvaema_area]]', number_format($total_kvs_not_allowabla_area, 3, '.', ''), $template);
            $template = str_replace('[[total_renta]]', BGNtoEURO($total_renta), $template);
        }

        // replace template_vars in template string with db data
        $template = str_replace('[[nomer_na_dogovor]]', $contractData['c_num'], $template);
        $template = str_replace('[[tip_na_dogovor]]', $GLOBALS['Contracts']['ContractTypes'][$contractData['nm_usage_rights']]['name'], $template);
        $template = str_replace('[[grupa_na_dogovor]]', $contractData['group_name'], $template);
        $template = str_replace('[[data_na_dogovor]]', strftime('%d.%m.%Y', strtotime($contractData['c_date'])) . 'г.', $template);
        $template = str_replace('[[vlizane_v_sila]]', strftime('%d.%m.%Y', strtotime($contractData['start_date'])) . 'г.', $template);
        $template = str_replace('[[kraina_data]]', strftime('%d.%m.%Y', strtotime($contractData['due_date'])) . 'г.', $template);
        // Стопанство group
        $template = $FarmingController->farmDetailedTemplate($template, $farming_results[0]);

        $template = str_replace('[[stopanstvo]]', $farming_results[0]['name'], $template);
        $template = str_replace('[[stopanstvo_name]]', $farming_results[0]['name'], $template);
        $template = str_replace('[[stopanstvo_address]]', $farming_results[0]['address'], $template);
        $template = str_replace('[[stopanstvo_firma]]', $farming_results[0]['company'], $template);
        $template = str_replace('[[stopanstvo_bulstat]]', $farming_results[0]['bulstat'], $template);
        $template = str_replace('[[stopanstvo_firma_address]]', $farming_results[0]['company_address'], $template);
        $template = str_replace('[[stopanstvo_mol]]', $farming_results[0]['mol'], $template);
        $template = str_replace('[[stopanstvo_mol_egn]]', $farming_results[0]['mol_egn'], $template);
        $iban_arr = json_decode($farming_results[0]['iban_arr'], true);
        $banksInfo = '';
        if (!empty($iban_arr)) {
            foreach ($iban_arr as $key => $bank) {
                if (empty($bank['iban'])) {
                    continue;
                }
                $banksInfo .= $bank['iban'] . (empty($bank['name']) ? '' : ' при банка ' . $bank['name']) . ', ';
            }
            $banksInfo = rtrim($banksInfo, ', ');
        }
        $template = str_replace('[[stopanstvo_iban_arr]]', $banksInfo, $template);

        $template = str_replace('[[nomer_na_vpisvane]]', $contractData['sv_num'], $template);

        if (!empty($contractData['sv_date'])) {
            $template = str_replace('[[data_na_vpisvane]]', date('d.m.Y', strtotime($contractData['sv_date'])) . 'г.', $template);
        } else {
            $template = str_replace('[[data_na_vpisvane]]', 'Няма въведена', $template);
        }

        // renta_v_natura
        $rentCount = count($renta_matches[0]);
        for ($n = 0; $n < $rentCount; $n++) {
            $rentaNatData = $contractData['renta_natura'][$n];
            $allMatchesRentaNat = $renta_matches[0][$n];
            $template = str_replace($allMatchesRentaNat, $rentaNatData, $template);
        }
        // plot detailed columns
        $matchesCount = count($matches[0]);
        for ($n = 0; $n < $matchesCount; $n++) {
            $plotDetailedData = $plotsDetailedDataArr[$n];
            $allMatchesPlotDet = $matches[0][$n];
            $template = str_replace($allMatchesPlotDet, $plotDetailedData, $template);
        }

        $kontragent_m_count = count($kontragent_matches);
        for ($n = 0; $n < $kontragent_m_count; $n++) {
            $template = str_replace($kontragent_matches[0][$n], $kontragentDetailedData[$n], $template);
        }

        $kontragent_rep_m_count = count($kontragent_rep_matches);
        for ($n = 0; $n < $kontragent_rep_m_count; $n++) {
            $template = str_replace($kontragent_rep_matches[0][$n], $kontragentRepDetailedData[$n], $template);
        }

        $template = str_replace('[[sublease]]', $repSublease, $template);
        $template = str_replace('[[imoti]]', $plotsData, $template);

        // imoti_podrobno detailed columns
        $matchesCount = count($matches[0]);
        for ($n = 0; $n < $matchesCount; $n++) {
            $plotDetailedData = $plotsDetailedDataArr[$n];
            $allMatchesPlotDet = $matches[0][$n];
            $template = str_replace($allMatchesPlotDet, $plotDetailedData, $template);
        }

        // Contract group
        $template = str_replace('[[sv_num]]', $contractData['sv_num'], $template);

        if (!empty($contractData['sv_date'])) {
            $template = str_replace('[[sv_date]]', date('d.m.Y', strtotime($contractData['sv_date'])) . 'г.', $template);
        } else {
            $template = str_replace('[[sv_date]]', 'Няма въведена', $template);
        }

        if (!empty($contractData['osz_date'])) {
            $template = str_replace('[[osz_date]]', date('d.m.Y', strtotime($contractData['osz_date'])) . 'г.', $template);
        } else {
            $template = str_replace('[[osz_date]]', 'Няма въведена', $template);
        }
        $template = str_replace('[[osz_num]]', $contractData['osz_num'], $template);
        $template = str_replace('[[comment]]', $contractData['comment'], $template);

        if (false !== strstr($template, '[[renta_natura_obobshteno]]') || false !== strstr($template, '[[contract_price]]')) {
            $payments = makeApiClass('payments-rpc', 'contract-payments-grid');
            $contractStartYear = date('Y', strtotime($contractData['start_date']));
            $contractEndYear = date('Y', strtotime($contractData['due_date']));

            $farmingYears = $contractStartYear - $contractEndYear;
            $farmingYears = ($farmingYears > 0) ? $farmingYears : 1;

            $contractPriceSum = 0;
            $rentaNat = [];
            foreach ($GLOBALS['Farming']['years'] as $farmingYear) {
                if ($farmingYear['year'] > $contractStartYear && $farmingYear['year'] <= $contractEndYear) {
                    $paymentsData = $payments->getContractPayments($contractData['id'], 0, $farmingYear['id']);
                    if ($paymentsData['footer']) {
                        $totalData = current($paymentsData['footer']);

                        $contractPriceSum += $totalData['renta'];
                        $rentaNatUnits = array_filter(explode('</br>', $totalData['renta_nat_type']));
                        $rentaNat[$farmingYear['id']] = array_filter(explode('<br/>', $totalData['renta_nat_text']));
                    }
                }
            }

            $totalByUnit = [];
            foreach ($rentaNat as $yearId => $rentaQty) {
                foreach ($rentaNatUnits as $key => $unit) {
                    if (!array_key_exists($unit, $totalByUnit)) {
                        $totalByUnit[$unit] = $rentaQty[$key];

                        continue;
                    }
                    $totalByUnit[$unit] += $rentaQty[$key];
                }
            }

            $rentaNatStr = '';
            foreach ($totalByUnit as $unit => $total) {
                $rentaNatStr .= $unit . ':' . $total . ' ';
            }

            $template = str_replace('[[renta_natura_obobshteno]]', ' ' . $rentaNatStr, $template);
            $template = str_replace('[[contract_price]]', ' ' . BGNtoEURO($contractPriceSum), $template);
        }

        $template = str_replace('[[imoti_zemlishta]]', $plots_by_ekatte_string, $template);
        $template = str_replace('[[imoti_zemlishta_kadident]]', $plots_by_ekatte_kadident_string, $template);
        $template = str_replace('[[imoti_kategoriq]]', $plots_by_category_string, $template);
        $template = str_replace('[[padej]]', $contractData['payday'], $template);
        $template = str_replace('[[timespan_farming_years]]', $timespan_message, $template);

        $template = $this->convertImagesPathsToUrls($template);

        $headerTxt = $this->getHeaderFooterTag('page_header', $template);
        $footerTxt = $this->getHeaderFooterTag('page_footer', $template);
        $template = $headerTxt . $template . $footerTxt;
        $template = '<page style="font-family: freeserif" backtop="50px" backbottom="50px" ><bookmark title="Договор№ ' . htmlspecialchars($contractData['c_num'], ENT_QUOTES, 'UTF-8') . '" level="0" ></bookmark>' . $template . '</page>';

        return $template;
    }

    private function generateSingleContract($template, $contract_id, $rpcParams)
    {
        $document = $this->getTemplate($template['html'], $contract_id, $rpcParams['blank_type']);

        $blankName = $template['title'];
        $blankName = str_replace(';', '', $blankName);
        $blankName = preg_replace('/\s+/', '_', $blankName);
        $FarmingController = new FarmingController('Farming');
        $blankName = $FarmingController->StringHelper->transLitString($blankName);
        $date = date('Y-m-d-H-i-s');
        $filename = 'dogovor_' . $blankName . '_' . $this->User->UserID . '_' . $date;

        if ('pdf' === $rpcParams['blank_type']) {
            $docDir = LAYERS_CONTRACTS_PATH . 'blanks/' . $this->User->GroupID . '/';
            if (!is_dir($docDir) && !mkdir($docDir) && !is_dir($docDir)) {
                throw new RuntimeException(sprintf('Directory "%s" was not created', $docDir));
            }

            $newPdfFileName = $filename . '.pdf';
            $newPDFFilePath = PUBLIC_CONTRACTS_BLANKS_RELATIVE_PATH . $this->User->GroupID . '/' . $newPdfFileName;

            $printPdf = new PrintPdf();
            $printPdf->generateFromHtml($document, $newPDFFilePath, ['pageNumber' => $template['show_page_numbers']]);

            $return['file_path'] = $newPDFFilePath;
            $return['file_name'] = $newPdfFileName;

            return $return;
        }

        if ('doc' == $rpcParams['blank_type']) {
            $docOpts = [
                'sections' => [
                    'WordSection' => [
                        'size' => '21cm 29.7cm',
                        'margin' => '1.1cm 2cm 1.5cm 2cm',
                        'mso-page-orientation' => 'portrait',
                    ],
                ],
            ];
            $exportWordDoc = new ExportWordDocClass();
            if (!empty($template['show_page_numbers'])) {
                $document = $exportWordDoc->addPageNumbers($document, $template['show_page_numbers']);
            }

            $newWordDoc = $exportWordDoc->export($filename, $document, true, $docOpts);
            $return['file_path'] = $newWordDoc;
            $return['file_name'] = $filename . '.doc';

            return $return;
        }
    }

    private function generateArchiveWithContracts($template, $contractsData, $rpcParams)
    {
        $FarmingController = new FarmingController('Farming');
        $zip = new ZipArchive();
        $dir = LAYERS_CONTRACTS_PATH . 'blanks/' . $this->User->GroupID;

        if (!is_dir($dir)) {
            mkdir($dir, 0755);
        }

        $archiveName = 'dogovori_' . date('Y-m-d-H-i-s');
        $archievePath = $dir . '/' . $archiveName;

        $zipFile = $archievePath . '.zip';
        $zip->open($zipFile, ZipArchive::CREATE);

        $return['file_path'] = str_replace(SITE_PATH . 'public/', '', $zipFile);
        $return['file_name'] = $archiveName . '.zip';
        $tempFiles = [];

        try {
            if ('pdf' === $rpcParams['blank_type']) {
                foreach ($contractsData as $contract) {
                    $generatedTemplate = $this->getTemplate($template['html'], $contract['id'], $rpcParams['blank_type']);
                    $filename = 'dogovor_' . $FarmingController->StringHelper->transLitString($contract['c_num']) . '_' . $contract['id'] . '.pdf';
                    $newPDFFilePath = $dir . '/' . $filename;

                    $printPdf = new PrintPdf();
                    $printPdf->generateFromHtml($generatedTemplate, $newPDFFilePath);

                    $tempFiles[] = $newPDFFilePath;

                    $zip->addFile($newPDFFilePath, $filename);
                }
            }

            if ('doc' === $rpcParams['blank_type']) {
                $exportWordDoc = new ExportWordDocClass();
                foreach ($contractsData as $contract) {
                    $generatedTemplate = $this->getTemplate($template['html'], $contract['id'], $rpcParams['blank_type']);
                    $filename = 'dogovor_' . $FarmingController->StringHelper->transLitString($contract['c_num']) . '_' . $contract['id'];
                    $newWordDoc = $exportWordDoc->export($filename, $generatedTemplate, true);
                    $tempFiles[] = $newWordDoc;

                    $zip->addFile($newWordDoc, $filename . '.doc');
                }
            }
        } catch (Exception $e) {
            $zip->close();
            deleteFiles($tempFiles);

            throw $e;
        }

        $zip->close();
        deleteFiles($tempFiles);

        return $return;
    }

    private function getHeaderFooterTag($tagName, &$template)
    {
        $tagContentRe = "/\[\[{$tagName}_\]\](?P<content>.*?)\[\[_{$tagName}\]\]/s";
        $matches = [];

        if (!preg_match_all($tagContentRe, $template, $matches)) {
            return '';
        }
        $template = preg_replace($tagContentRe, '', $template);

        return $matches['content'][0];
    }

    private function convertImagesPathsToUrls($template)
    {
        $regex = '/<img\s[^>]*?src\s*=\s*[\'\"]([^\'\"]*?)[\'\"][^>]*?>/m';
        preg_match_all($regex, $template, $matches, PREG_SET_ORDER, 0);
        foreach ($matches as $match) {
            $src = $match[1];
            if ('http' !== substr($src, 0, 4)) {
                $src = PUBLIC_PATH . $src;
            }
            $template = str_replace($match[1], $src, $template);
        }

        return $template;
    }

    private function plotsByCategoryData($contractId)
    {
        $UsersController = new UsersController('Users');
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);

        $options = [
            'return' => [
                'kvs_gid as gid',
                'kad_ident',
                'virtual_ntp_title as area_type',
                'category',
                'virtual_category_title as category_name',
                'mestnost',
                'number',
                'masiv',
                'kvs_ekate as ekate',
                'virtual_ekatte_name as land',
                'round(contract_area::numeric, 3) AS contract_area',
                'round(document_area::numeric, 3) AS document_area',
                'round((st_area(kvs_geom)/1000)::numeric, 3) AS geom_area',
            ],
            'where' => [
                'contract_id' => ['column' => 'c_id', 'compare' => '=', 'value' => $contractId],
                'annex_action' => [
                    'column' => 'annex_action',
                    'prefix' => 'data',
                    'compare' => '=',
                    'value' => 'added',
                ],
            ],
            'sort' => 'kad_ident COLLATE "alpha_numeric_bg"',
            'order' => 'asc',
        ];

        $plotsResults = array_unique($UserDbPlotsController->getFullPlotData($options, false, false), SORT_REGULAR);

        $plotsResults = array_map(function ($item) {
            $item['mestnost'] = !empty($item['mestnost']) ? $item['mestnost'] : '';

            if ('' == $item['kad_ident']) {
                $item['kad_ident'] = '[Няма информация]';
            }

            return $item;
        }, $plotsResults);

        $ekate = [];
        $masiv = [];
        $number = [];

        if (count($plotsResults) > 0) {
            foreach ($plotsResults as $key => $row) {
                $ekate[$key] = $row['ekate'];
                $masiv[$key] = $row['masiv'];
                $number[$key] = $row['number'];
            }

            array_multisort($ekate, SORT_ASC, $masiv, SORT_ASC, $number, SORT_ASC, $plotsResults);
        }

        return $this->plotsByCategoryTemplate($plotsResults);
    }

    private function plotsByCategoryTemplate($plotsResults)
    {
        $plotsData = '<table align="center" cellspacing="0" cellpadding="3" border="1">';
        $plotsData .= '<thead>';
        $plotsData .= '<tr align="center">';
        $plotsData .= '<th>№</th>';
        $plotsData .= '<th>Идентификатор</th>';
        $plotsData .= '<th>Землище</th>';
        $plotsData .= '<th>Категория</th>';
        $plotsData .= '<th>Местност</th>';
        $plotsData .= '<th>НТП</th>';
        $plotsData .= '<th>Площ по <br/>договор(дка)</th>';
        $plotsData .= '<th>Площ по <br/>док. (дка)</th>';
        $plotsData .= '</tr>';
        $plotsData .= '</thead>';
        $plotsData .= '<tbody>';

        $firstCatsTotalArea = 0;
        $firstCatsDocArea = 0;
        $firsCatsPlotsData = '';
        $secondCatsPlotsData = '';

        $lowCategoryCounter = 0;
        $highCategoryCounter = 0;
        $secondCatsTotalArea = 0;
        $secondCatsDocArea = 0;

        $plotsResultsCount = count($plotsResults);
        for ($i = 0; $i < $plotsResultsCount; $i++) {
            $currentPlotArea = $plotsResults[$i]['document_area'];
            if ($plotsResults[$i]['category'] <= 5) {
                $lowCategoryCounter++;
                $firsCatsPlotsData .= '<tr>';
                $firsCatsPlotsData .= '<td width="20" align="center">' . $lowCategoryCounter . '</td>';
                $firsCatsPlotsData .= '<td width="100" align="center">' . $plotsResults[$i]['kad_ident'] . '</td>';
                $firsCatsPlotsData .= '<td width="100" align="center">' . $plotsResults[$i]['land'] . '</td>';
                $firsCatsPlotsData .= '<td width="90" align="center">' . $plotsResults[$i]['category_name'] . '</td>';
                $firsCatsPlotsData .= '<td width="100" align="center">' . $plotsResults[$i]['mestnost'] . '</td>';
                $firsCatsPlotsData .= '<td width="140" align="center">' . $plotsResults[$i]['area_type'] . '</td>';
                $firsCatsPlotsData .= '<td width="70" align="center">' . $plotsResults[$i]['contract_area'] . '</td>';
                $firsCatsPlotsData .= '<td width="60" align="center">' . $currentPlotArea . '</td>';
                $firsCatsPlotsData .= '</tr>';

                $firstCatsTotalArea += $plotsResults[$i]['contract_area'];
                $firstCatsDocArea += $currentPlotArea;
            } elseif ($plotsResults[$i]['category'] > 5) {
                $highCategoryCounter++;
                $secondCatsPlotsData .= '<tr>';
                $secondCatsPlotsData .= '<td width="20" align="center">' . $highCategoryCounter . '</td>';
                $secondCatsPlotsData .= '<td width="100" align="center">' . $plotsResults[$i]['kad_ident'] . '</td>';
                $secondCatsPlotsData .= '<td width="100" align="center">' . $plotsResults[$i]['land'] . '</td>';
                $secondCatsPlotsData .= '<td width="90" align="center">' . $plotsResults[$i]['category_name'] . '</td>';
                $secondCatsPlotsData .= '<td width="100" align="center">' . $plotsResults[$i]['mestnost'] . '</td>';
                $secondCatsPlotsData .= '<td width="140" align="center">' . $plotsResults[$i]['area_type'] . '</td>';
                $secondCatsPlotsData .= '<td width="70" align="center">' . $plotsResults[$i]['contract_area'] . '</td>';
                $secondCatsPlotsData .= '<td width="60" align="center">' . $currentPlotArea . '</td>';
                $secondCatsPlotsData .= '</tr>';

                $secondCatsTotalArea += $plotsResults[$i]['contract_area'];
                $secondCatsDocArea += $currentPlotArea;
            }
        }

        if (0 != $firstCatsTotalArea && 0 != $firstCatsDocArea) {
            $firsCatsPlotsData .= '<tr>';
            $firsCatsPlotsData .= '<td colspan="6"><b>Общо – I-V-та категория</b></td>';
            $firsCatsPlotsData .= '<td width="70" align="center"><b>' . number_format(
                $firstCatsTotalArea,
                3,
                '.',
                ''
            ) . '</b></td>';
            $firsCatsPlotsData .= '<td width="70" align="center"><b>' . number_format(
                $firstCatsDocArea,
                3,
                '.',
                ''
            ) . '</b></td>';
            $firsCatsPlotsData .= '</tr>';
        }

        if (0 != $secondCatsTotalArea && 0 != $secondCatsDocArea) {
            $secondCatsPlotsData .= '<tr>';
            $secondCatsPlotsData .= '<td colspan="6"><b>Общо – над VI-та категория</b></td>';
            $secondCatsPlotsData .= '<td width="70" align="center"><b>' . number_format(
                $secondCatsTotalArea,
                3,
                '.',
                ''
            ) . '</b></td>';
            $secondCatsPlotsData .= '<td width="70" align="center"><b>' . number_format(
                $secondCatsDocArea,
                3,
                '.',
                ''
            ) . '</b></td>';
            $secondCatsPlotsData .= '</tr>';
        }

        $total_area = $firstCatsTotalArea + $secondCatsTotalArea;
        $total_document_area = $firstCatsDocArea + $secondCatsDocArea;

        $plotsData .= $firsCatsPlotsData;
        $plotsData .= $secondCatsPlotsData;

        $plotsData .= '<tr>';
        $plotsData .= '<td colspan="5"></td>';
        $plotsData .= '<td width="140" align="center"><b>Общо</b></td>';
        $plotsData .= '<td width="70" align="center"><b>' . number_format($total_area, 3, '.', '') . '</b></td>';
        $plotsData .= '<td width="60" align="center"><b>' . number_format(
            $total_document_area,
            3,
            '.',
            ''
        ) . '</b></td>';
        $plotsData .= '</tr>';

        $plotsData .= '</tbody>';
        $plotsData .= '</table>';

        return $plotsData;
    }

    /**
     * @param string $template
     * @param int $contract_id
     *
     * @return array
     */
    private function addRentaVNatura($template, $contract_id, &$renta_nat_ids, &$contractData)
    {
        $UserDbController = new UserDbController($this->User->Database);

        if (false === strpos($template, '[[renta_v_natura')) {
            return [];
        }
        // get renta natura types and create predefined array
        $renta_types = [];
        // options for renta nat types
        $options = [
            'tablename' => $UserDbController->DbHandler->tableRentaTypes,
        ];
        $renta_results = $UserDbController->getItemsByParams($options, false, false);
        $rentaCount = count($renta_results);
        for ($i = 0; $i < $rentaCount; $i++) {
            $renta_types[$renta_results[$i]['id']]['name'] = $renta_results[$i]['name'];
            $renta_types[$renta_results[$i]['id']]['unit'] = $GLOBALS['Contracts']['renta_units'][$renta_results[$i]['unit']]['name'];
        }
        $options = [
            'tablename' => $UserDbController->DbHandler->contractsRentsRelTable,
            'where' => [
                'contract_id' => ['column' => 'contract_id', 'compare' => '=', 'value' => $contract_id],
            ],
        ];

        $renta_results = $UserDbController->getItemsByParams($options, false, false);

        $renta_regex = "/\\[\\[renta_v_natura ?([0-9\s]*)\\]\\]/";
        preg_match_all($renta_regex, $template, $renta_matches);

        foreach ($renta_matches[1] as $key => $match) {
            // $match = id of rents from the respective templates
            $renta_nat_ids[$key] = explode(' ', $match);

            $contractData['renta_natura'][$key] = '';
            if ('' == $renta_nat_ids[$key][0]) {
                // manually added rents
                $contractData['renta_natura'][$key] = array_map(function ($contract_renta_natura) use ($renta_types) {
                    return $renta_types[$contract_renta_natura['renta_id']]['name'] . ' ' . $contract_renta_natura['renta_value'] . ' ' . $renta_types[$contract_renta_natura['renta_id']]['unit'] . '/дка';
                }, $renta_results);
            } else {
                $contractData['renta_natura'][$key] = array_map(
                    function ($template_renta_id) use ($renta_types, $renta_results) {
                        $rentaCount = count($renta_results);
                        for ($i = 0; $i < $rentaCount; $i++) {
                            if ($renta_results[$i]['renta_id'] == $template_renta_id) {
                                $rentaValue = $renta_results[$i]['renta_value'];
                            }
                        }

                        if (null == $rentaValue) {
                            return;
                        }

                        return $renta_types[$template_renta_id]['name'] . ' ' . $rentaValue . ' ' . $renta_types[$template_renta_id]['unit'] . '/дка';
                    },
                    $renta_nat_ids[$key]
                );
            }

            $contractData['renta_natura'][$key] = array_filter($contractData['renta_natura'][$key], function ($value) {
                return null != $value;
            });

            $contractData['renta_natura'][$key] = implode(', ', $contractData['renta_natura'][$key]);
        }

        return $renta_matches;
    }

    private function getOwnerData($ownerId)
    {
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);
        $UserDbContractsController = new UserDbContractsController($this->User->Database);

        $dbLink = "'host=" . DBLINK_HOST . ' port=' . DBLINK_PORT . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD . "'::text";
        $ekattesTable = "(SELECT * FROM dblink({$dbLink}, 'SELECT ekatte_code, ekatte_name FROM su_ekatte') AS ekattes(ekatte_code VARCHAR, ekatte_name VARCHAR(255))) as ekattes";

        $options = [
            'return' => $UserDbContractsController->getOwnersSelect(),
            'where' => [
                'owner_id' => [
                    'column' => 'owner_id',
                    'compare' => '=',
                    'prefix' => 'po',
                    'value' => $ownerId,
                ],
                'is_heritor' => ['column' => 'is_heritor', 'compare' => '=', 'prefix' => 'po', 'value' => 'false'],
            ],
            'leftjoin' => [
                'table' => $ekattesTable,
                'condition' => ' ON ekattes.ekatte_code = o.rent_place',
            ],
            'group' => 'o.id, po.is_heritor, po.is_signer, po.path',
        ];

        return $UserDbOwnersController->getOwnersDataByContract($options, false, false);
    }

    private function getContractSigner(int $contractId)
    {
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);
        $UserDbContractsController = new UserDbContractsController($this->User->Database);

        $dbLink = "'host=" . DBLINK_HOST . ' port=' . DBLINK_PORT . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD . "'::text";
        $ekattesTable = "(SELECT * FROM dblink({$dbLink}, 'SELECT ekatte_code, ekatte_name FROM su_ekatte') AS ekattes(ekatte_code VARCHAR, ekatte_name VARCHAR(255))) as ekattes";

        $options = [
            'return' => array_merge(
                $UserDbContractsController->getOwnersSelect(),
                [
                    'ekattes.ekatte_name as rent_place',
                    'round(
                        sum((po.percent / 100) * pc.contract_area)::numeric,
                        3
                    ) AS contract_signer_contract_area',
                    'round(
                        sum((po.percent / 100) * pc.kvs_allowable_area)::numeric,
                        3
                    ) AS contract_signer_cultivated_area',
                    'round(
                        sum((po.percent / 100) * pc.area_for_rent)::numeric,
                        3
                    ) AS contract_signer_contract_rent_area',
                ]
            ),
            'where' => [
                'contract_id' => [
                    'column' => 'contract_id',
                    'compare' => '=',
                    'prefix' => 'pc',
                    'value' => $contractId,
                ],
                'is_signer' => [
                    'column' => 'is_signer',
                    'compare' => '=',
                    'prefix' => 'po',
                    'value' => 1,
                ],
            ],
            'group' => 'o.id, po.is_heritor, po.is_signer, po.path, ekattes.ekatte_name',
            'leftjoin' => [
                'table' => $ekattesTable,
                'condition' => ' ON ekattes.ekatte_code = o.rent_place',
            ],
        ];

        return $UserDbOwnersController->getOwnersDataByContract($options, false, false);
    }
}
