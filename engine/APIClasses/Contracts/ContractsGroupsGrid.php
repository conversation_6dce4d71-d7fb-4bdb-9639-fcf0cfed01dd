<?php

namespace TF\Engine\APIClasses\Contracts;

use Exception;
use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbContracts\UserDbContractsController;

/**
 * @rpc-module Contracts
 *
 * @rpc-service-id contracts-groups-datagrid
 */
class ContractsGroupsGrid extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'create' => ['method' => [$this, 'createContractsGroup'],
                'validators' => [
                    'validateComplexArray' => [
                        'name' => 'validateRequired,validateText',
                    ],
                ],
            ],
            'read' => ['method' => [$this, 'getContractsGroups'],
                'validators' => [
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'update' => ['method' => [$this, 'updateContractsGroup'],
                'validators' => [
                    'validateComplexArray' => [
                        'id' => 'validateRequired,validateInteger',
                        'newName' => 'validateRequired,validateText',
                    ],
                ],
            ],
            'delete' => ['method' => [$this, 'deleteContractsGroup'],
                'validators' => [
                    'validateComplexArray' => [
                        'id' => 'validateRequired,validateInteger',
                    ],
                ],
            ],
        ];
    }

    /**
     * Create a new contract group.
     *
     * @api-method create
     *
     * @param array $rpcParams {
     *                         #item string name Name of the group
     *                         }
     *
     * @return array
     */
    public function createContractsGroup(array $rpcParams)
    {
        $options['tablename'] = 'su_contract_group';
        $options['mainData'] = [
            'name' => $rpcParams['name'],
        ];

        $UserDbContractsController = new UserDbContractsController($this->User->Database);

        try {
            $createdGroupId = $UserDbContractsController->addItem($options);
        } catch (Exception $e) {
            throw new MTRpcException('CONTRACT_GROUP_ALREADY_EXISTS', -33049);
        }

        return [
            'success' => true,
            'group' => [
                'id' => $createdGroupId,
                'name' => $rpcParams['name'],
            ],
        ];
    }

    /**
     * Get the contracts groups.
     *
     * @api-method read
     *
     * @param int $page Page number
     * @param int $rows Number of items per page
     * @param ?string $sort
     * @param ?string $order
     *
     * @return array
     */
    public function getContractsGroups(int $page = 1, int $rows = 10, ?string $sort, ?string $order)
    {
        $UserDbController = new UserDbController($this->User->Database);

        if (empty($sort) && empty($order)) {
            $sort = 'id';
            $order = 'desc';
        }

        $options = [
            'tablename' => 'su_contract_group',
            'return' => ['id', 'name'],
            'limit' => $rows,
            'offset' => ($page - 1) * $rows,
            'sort' => $sort,
            'order' => $order,
        ];

        return $UserDbController->getItemsByParams($options);
    }

    /**
     * Update a contract group.
     *
     * @api-method update
     *
     * @param array $rpcParams {
     *                         #item integer id ID of the group
     *                         #item string newName New name of the group
     *                         }
     *
     * @return array
     */
    public function updateContractsGroup(array $rpcParams)
    {
        $UserDbContractsController = new UserDbContractsController($this->User->Database);

        $options = [
            'tablename' => 'su_contract_group',
            'mainData' => [
                'name' => $rpcParams['newName'],
            ],
            'where' => [
                'id' => $rpcParams['id'],
            ],
        ];

        try {
            $UserDbContractsController->editItem($options);
        } catch (Exception $e) {
            throw new MTRpcException('CONTRACT_GROUP_ALREADY_EXISTS', -33049);
        }

        return [
            'success' => true,
            'group' => [
                'id' => $rpcParams['id'],
                'name' => $rpcParams['newName'],
            ],
        ];
    }

    /**
     * Delete a contract group.
     *
     * @api-method delete
     *
     * @param array $rpcParams {
     *                         #item integer id ID of the group
     *                         }
     *
     * @return array
     */
    public function deleteContractsGroup(array $rpcParams)
    {
        $UserDbContractsController = new UserDbContractsController($this->User->Database);
        $contracts = $UserDbContractsController->getItemsByParams([
            'tablename' => 'su_contracts',
            'return' => ['id'],
            'where' => [
                'id' => ['column' => '"group"', 'compare' => '=', 'value' => $rpcParams['id']],
            ],
        ]);

        if (!empty($contracts)) {
            throw new MTRpcException('CONTRACT_GROUP_HAS_CONTRACTS', -33050);
        }

        $options = [
            'tablename' => 'su_contract_group',
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $rpcParams['id']],
            ],
        ];

        $UserDbContractsController->deleteItemsByParams($options);

        return ['success' => true];
    }
}
