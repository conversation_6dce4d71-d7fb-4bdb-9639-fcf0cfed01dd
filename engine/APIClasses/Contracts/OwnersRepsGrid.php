<?php

namespace TF\Engine\APIClasses\Contracts;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

// Prado::using('Plugins.Core.UserDb.*');

/**
 * Грид "Представители".
 *
 * @rpc-module Contracts
 *
 * @rpc-service-id owners-reps-grid
 *
 * @property UserDbController $UserDbController
 */
class OwnersRepsGrid extends TRpcApiProvider
{
    /**
     * Class Controllers
     * Define all class controllers as properties.
     */
    private $UserDbController;

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readOwnersRepsGrid'],
                'validators' => [
                    'filterObj' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * Грид "Представители".
     *
     * @api-method read
     *
     * @param array $filterObj
     *                         {
     *                         #item string rep_names
     *                         #item string rep_egn
     *                         }
     * @param int $page pagination parameter
     * @param int $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array
     */
    public function readOwnersRepsGrid(array $filterObj, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $return = [
            'rows' => [],
            'total' => 0,
        ];

        if ($this->User->isGuest) {
            return $return;
        }

        // init all needed controllers
        $UserDbController = new UserDbController($this->User->Database);

        $filterObj['rep_names'] = $filterObj['rep_names'] ? preg_replace('/\s+/', ' ', $filterObj['rep_names']) : '';

        $options = [
            'tablename' => $UserDbController->DbHandler->tableOwnersReps,
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
            'where' => [
                'rep_names' => ['column' => "TRIM(rep_name) || ' ' || TRIM(rep_surname) || ' ' || TRIM(rep_lastname)", 'compare' => 'ILIKE', 'value' => $filterObj['rep_names']],
                'rep_egn' => ['column' => 'rep_egn', 'compare' => 'ILIKE', 'value' => $filterObj['rep_egn']],
            ],
        ];

        $counter = $UserDbController->getItemsByParams($options, true, false);

        if (0 == $counter[0]['count']) {
            return $return;
        }

        $results = $UserDbController->getItemsByParams($options, false, false);

        return [
            'rows' => $results,
            'total' => $counter[0]['count'],
        ];
    }
}
