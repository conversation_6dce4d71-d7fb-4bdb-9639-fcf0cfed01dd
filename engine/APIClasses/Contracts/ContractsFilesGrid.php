<?php

namespace TF\Engine\APIClasses\Contracts;

use Prado\Web\Services\TRpcApiProvider;
use TF\Application\Common\Config;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbContracts\UserDbContractsController;
use TF\Engine\Plugins\Core\Users\UsersController;

// Prado::using('Plugins.Core.Users.*');

/**
 * Грид "Архив файлове".
 *
 * @rpc-module Contracts
 *
 * @rpc-service-id contracts-files-maingrid
 */
class ContractsFilesGrid extends TRpcApiProvider
{
    private $module = 'Contracts';
    private $service_id = 'contracts-files-maingrid';

    /**
     * Registers the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getContractsFilesGrid'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'delete' => ['method' => [$this, 'deleteContractsFiles']],
            'download' => ['method' => [$this, 'downloadAttached']],
        ];
    }

    /**
     * Попълва "Архив файлове".
     *
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer contract_id
     *                         }
     * @param string $page pagination parameter
     * @param string $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array
     */
    public function getContractsFilesGrid(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        // create the default return
        $return = [
            'rows' => [],
            'total' => 0,
        ];

        if ($this->User->isGuest) {
            return $return;
        }

        if ('' == $rpcParams['contract_id']) {
            return $return;
        }

        // init controllers
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbContractsController = new UserDbContractsController($this->User->Database);

        // options for contract files query
        $options = [
            'return' => [
                '*',
            ],
            'where' => [
                'contract_id' => ['column' => 'contract_id', 'prefix' => 'cf', 'compare' => '=', 'value' => $rpcParams['contract_id']],
            ],
        ];
        $options['sort'] = $sort;
        $options['order'] = $order;
        $options['offset'] = ($page - 1) * $rows;
        $options['limit'] = $rows;

        $counter = $UserDbContractsController->getContractFiles($options, true, false);

        if (0 == $counter[0]['count']) {
            return $return;
        }

        $results = $UserDbContractsController->getContractFiles($options, false, false);
        $resultsCount = count($results);
        for ($i = 0; $i < $resultsCount; $i++) {
            $results[$i]['date'] = strftime('%d.%m.%Y', strtotime($results[$i]['date']));
        }

        $return['rows'] = $results;
        $return['total'] = $counter[0]['count'];

        return $return;
    }

    /**
     * Изтрива избрания файл.
     *
     * @api-method delete
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer contract_id
     *                         #item integer id
     *                         }
     *
     * @return array|bool
     */
    public function deleteContractsFiles($rpcParams)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbContractsController = new UserDbContractsController($this->User->Database);
        $UsersController = new UsersController('Users');

        // get contractType
        $contractType = $UserDbContractsController->getContractType($rpcParams['contract_id']);

        // No Rights to operate with "Договори за собственост"
        if (Config::CONTRACT_TYPE_OWN == $contractType && !$this->User->HasContractsOwnWriteRights) {
            $UserDbContractsController->setResponseDataContracts($contractType, $this->User->HasContractsOwnWriteRights);

            return false;
        }

        $fileID = $rpcParams['file_id'];

        // delete contract_file_relation
        $UserDbContractsController->deleteContractFileRelation($fileID, $rpcParams['contract_id']);

        // check if there are any relations left with this fileID
        $options = [
            'tablename' => $UserDbController->DbHandler->contractsFilesRelTable,
            'where' => [
                'file_id' => ['column' => 'file_id', 'compare' => '=', 'value' => $fileID],
            ],
        ];

        $relationResults = $UserDbController->getItemsByParams($options, false, false);
        $is_hard_delete = false;
        // if the file is not associated with any other contract - delete it from the file structure
        if (!count($relationResults)) {
            $is_hard_delete = true;
            // getContractFiles
            // delete file
            $options = [
                'tablename' => $UserDbController->DbHandler->userFilesTable,
                'where' => [
                    'file_id' => ['column' => 'id', 'compare' => '=', 'value' => $fileID],
                ],
            ];

            $results = $UserDbController->getItemsByParams($options, false, false);
            if (!count($results)) {
                return [];
            }

            $file_fragments = explode('.', $results[0]['filename']);
            $filename = current($file_fragments);
            $ext = end($file_fragments);

            $filePath = LAYERS_CONTRACTS_PATH . $results[0]['group_id'] . '/' . $results[0]['user_id'] . '/' . $results[0]['id'] . '.' . $ext;
            $newFileName = LAYERS_CONTRACTS_PATH . $results[0]['group_id'] . '/' . $results[0]['user_id'] . '/' . $filename . '_' . $results[0]['id'] . '.' . $ext;

            if (file_exists($filePath)) {
                unlink($filePath);
            }
            if (file_exists($newFileName)) {
                unlink($newFileName);
            }

            // delete record from the table
            $options = [
                'tablename' => $UserDbController->DbHandler->userFilesTable,
                'id_string' => $fileID,
            ];

            $UserDbController->deleteItemsByParams($options);

            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $results[0], [], 'delete file in a contract');
        }

        $UserDbController->logFileDeletion($fileID, $rpcParams['contract_id'], $is_hard_delete, $this->User->UserID);
    }

    /**
     * Returns the link for the requested file.
     *
     * @api-method download
     *
     * @param int $rpcParam
     *
     * @return array|string
     */
    public function downloadAttached($rpcParam)
    {
        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'tablename' => $UserDbController->DbHandler->userFilesTable,
            'where' => [
                'file_id' => ['column' => 'id', 'compare' => '=', 'value' => $rpcParam],
            ],
        ];

        $results = $UserDbController->getItemsByParams($options, false, false);
        if (!count($results)) {
            return [];
        }

        $file_fragments = explode('.', $results[0]['filename']);
        $filename = current($file_fragments);
        $ext = end($file_fragments);

        $filePath = LAYERS_CONTRACTS_PATH . $results[0]['group_id'] . '/' . $results[0]['user_id'] . '/' . $results[0]['id'] . '.' . $ext;
        $newFileName = LAYERS_CONTRACTS_PATH . $results[0]['group_id'] . '/' . $results[0]['user_id'] . '/' . $filename . '_' . $results[0]['id'] . '.' . $ext;

        $relFilePath = 'files/contract_files/' . $results[0]['group_id'] . '/' . $results[0]['user_id'] . '/' . $results[0]['id'] . '.' . $ext;
        $relNewFileName = 'files/contract_files/' . $results[0]['group_id'] . '/' . $results[0]['user_id'] . '/' . $filename . '_' . $results[0]['id'] . '.' . $ext;

        if (file_exists($filePath)) {
            $isRenamed = rename($filePath, $newFileName);

            if ($isRenamed) {
                return $relNewFileName;
            }

            return $relFilePath;
        }

        if (file_exists($newFileName)) {
            return $relNewFileName;
        }
    }
}
