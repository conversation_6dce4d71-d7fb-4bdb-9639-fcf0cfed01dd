<?php

namespace TF\Engine\APIClasses\Contracts;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbContracts\UserDbContractsController;

/**
 * Списък с имоти, които съществуват в съответните договори със съвпадащ период на
 * действие или сумарната площ по договори е по-голяма от площта по документ.
 *
 * @rpc-module Contracts
 *
 * @rpc-service-id contracts-plots-confirmation-datagrid
 */
class ContractsPlotsConfirmationGrid extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getContractsPlotsConfirmationGrid'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * Grid info.
     *
     * @param array $rpcParams - празен масив, сложен заради pagination
     * @param int $page pagination parameter
     * @param int $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @throws MTRpcException
     *
     * @return array
     *               {
     *               #item array rows
     *               {
     *               #item double area
     *               #item string c_num
     *               #item double contract_area
     *               #item integer contract_id
     *               #item double document_area
     *               #item date due_date
     *               #item string farming
     *               #item integer farming_id
     *               #item string kad_ident
     *               #item date start_date
     *               }
     *               #item integer total
     *               }
     */
    public function getContractsPlotsConfirmationGrid(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $UserDbContractsController = new UserDbContractsController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);
        $FarmingController = new FarmingController('Farming');

        // get all group farmings and create array like predefined config
        $final_farming = $FarmingController->getUserFarmings();
        $contractID = $_SESSION['plots_waiting_confirmation']['contract_id'];

        $isFromSublease = $UserDbContractsController->isContractFromSublease($contractID);
        if ($isFromSublease) {
            throw new MTRpcException('CANNOT_MODIFY_FROM_SUBLEASE_CONTRACT', -33752);
        }

        $plotIDS = array_map(function ($plot) {
            return $plot['plot_id'];
        }, $_SESSION['plots_waiting_confirmation']['plot_data_array']);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableKVS,
            'return' => ['gid', 'kad_ident'],
            'where' => [
                'gid' => ['column' => 'gid', 'compare' => 'IN', 'value' => $plotIDS],
            ],
        ];

        $plotInfo = $UserDbController->getItemsByParams($options);
        $newPlots = [];
        foreach ($plotInfo as $tmpCPR) {
            $newPlots[$tmpCPR['gid']] = $tmpCPR['kad_ident'];
        }

        $requestedAreas = [];
        foreach ($_SESSION['plots_waiting_confirmation']['plot_data_array'] as $tmpCPR) {
            $tmpCPR['kad_ident'] = $newPlots[$tmpCPR['plot_id']];
            $requestedAreas[] = $tmpCPR;
        }

        $finalRequest = [];
        foreach ($requestedAreas as $req) {
            $finalRequest[$req['kad_ident']] = $req;
        }

        // create default return
        $return = [
            'rows' => [],
            'total' => 0,
        ];

        $options = [
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
            'return' => [
                'start_date', 'due_date', 'c_num', 'farming_id', 'nm_usage_rights',
            ],
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'prefix' => 'c', 'value' => $contractID],
            ],
        ];

        $counter = $UserDbContractsController->getContractPlotData($options, true, false);

        if (0 == $counter[0]['count']) {
            return $return;
        }

        $newContract = $UserDbContractsController->getContractPlotData($options, false, false);

        $startDate = $newContract[0]['start_date'];
        $dueDate = $newContract[0]['due_date'];
        $newFarming = $newContract[0]['farming_id'];
        $NewContractUsageRights = $newContract[0]['nm_usage_rights'];

        if (null === $dueDate) {
            $dueDate = '9999-12-31 00:00:00';
        }

        $options = [
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => 'gid',
            'order' => $order,
            'return' => [
                'c.id as contract_id', 'c.parent_id as parent_id', 'null as sale_contract_id', 'c.start_date', 'pc.contract_end_date', 'c.c_num', 'c.nm_usage_rights', 'kvs.kad_ident',
                'c.is_annex', 'c.farming_id', 'round(pc.contract_area::numeric,3) as contract_area', 'kvs.document_area',
                'St_Area(geom) as area', 'c.from_sublease', 'kvs.gid',
            ],
            'where' => [
                'plot_ids' => ['column' => 'plot_id', 'compare' => 'IN', 'prefix' => 'pc', 'value' => $plotIDS],
                'start_date' => ['column' => 'start_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $dueDate],
                'due_date' => ['column' => '(CASE WHEN c.nm_usage_rights = 1 THEN \'9999-12-31 00:00:00\' ELSE c.due_date END)', 'compare' => '>=', 'value' => $startDate],
                'nm_usage_rights' => ['column' => 'nm_usage_rights', 'compare' => '<>', 'prefix' => 'c', 'value' => 4],
                'annex_action' => ['column' => 'annex_action', 'compare' => '=', 'prefix' => 'pc', 'value' => 'added'],
                'active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'TRUE'],
                'from_sublease' => ['column' => 'from_sublease', 'compare' => 'IS', 'prefix' => 'c', 'value' => 'NULL'],
                'annex' => ['column' => 'id', 'compare' => 'IS', 'prefix' => 'a', 'value' => 'NULL'],
            ],
            'with_active_annex' => true,
            'union_all_sold_contracts' => [
                'return' => [
                    'null as contract_id', 'null as parent_id', 'c.id as sale_contract_id, c.start_date', 'null', 'c.c_num', '7', 'kvs.kad_ident', 'c.is_annex', 'c.farming_id',
                    'round(scpr.contract_area_for_sale::numeric,3) * -1 as contract_area', 'kvs.document_area',
                    'St_Area(geom) as area', 'null', 'kvs.gid',
                ],
                'where' => [
                    'plot_ids' => ['column' => 'plot_id', 'compare' => 'IN', 'prefix' => 'scpr', 'value' => $plotIDS],
                    'start_date' => ['column' => 'start_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $dueDate],
                    'active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'TRUE'],
                ],
            ],
            'start_date' => $startDate,
            'due_date' => $dueDate,
        ];

        $counter = $UserDbContractsController->getContractPlotData($options, true, false);

        if (0 == $counter[0]['count']) {
            return $return;
        }

        $resultsRaw = $UserDbContractsController->getContractPlotData($options, false, false);

        $results = [];
        $parents = [];
        $parentIds = array_column($resultsRaw, 'parent_id');

        // Filter multiple annexes for one contract and get only the biggest area for validation
        foreach ($resultsRaw as $key => $result) {
            if (false == $result['is_annex'] && in_array($result['contract_id'], $parentIds)) {
                // If a conflicted plot exists simultaneously in an annex and its parent, show only the annex.
                continue;
            }

            $parents[$result['parent_id']] = $result['contract_area'];
            $results[] = $result;
        }

        $resultsCount = count($results);
        for ($i = 0; $i < $resultsCount; $i++) {
            // TS-4186 When I own and then transfer the same property, the area should not be considered as double occupied and duplicated,
            if (null !== $results[$i]['from_sublease'] && $results[$i]['farming_id'] !== $newFarming) {
                unset($results[$i]);
            }
        }
        $resultsCount = count($results);
        if (0 == $resultsCount) {
            return $return;
        }
        $additional = [];
        $calculatedPlots = [];
        for ($i = 0; $i < $resultsCount; $i++) {
            $kadIdent = $results[$i]['kad_ident'];
            if (!isset($additional[$kadIdent])) {
                $additional[$kadIdent] = [];
            }
            if (!$additional[$kadIdent]['document_area']) {
                $additional[$kadIdent]['document_area'] = $results[$i]['document_area'];
            }
            if (!$additional[$kadIdent]['document_area']) {
                $additional[$kadIdent]['document_area'] = number_format($results[$i]['area'] / 1000, 3, '.', '');
            }

            $additional[$kadIdent]['contract_area'] += $results[$i]['contract_area'];
            if (!in_array($kadIdent, $calculatedPlots)) {
                $additional[$kadIdent]['contract_area'] += $finalRequest[$kadIdent]['contract_area'];
                $calculatedPlots[] = $kadIdent;
            }
        }

        $finalResults = [];
        $footerCount = 0;
        for ($i = 0; $i < $resultsCount; $i++) {
            $results[$i]['area'] = number_format($results[$i]['area'] / 1000, 3, '.', '');
            $results[$i]['start_date'] = strftime('%d.%m.%Y', strtotime($results[$i]['start_date']));
            if ('' != $results[$i]['contract_end_date']) {
                $results[$i]['due_date'] = strftime('%d.%m.%Y', strtotime($results[$i]['contract_end_date']));
            } else {
                $results[$i]['due_date'] = '-';
            }

            if (!$results[$i]['document_area']) {
                $results[$i]['document_area'] = $results[$i]['area'];
            }

            $results[$i]['contract_area'] = number_format($results[$i]['contract_area'], 3, '.', '');
            $results[$i]['shares'] = $results[$i]['contract_area'] / $results[$i]['document_area'];

            $results[$i]['document_area'] = number_format($results[$i]['document_area'], 3, '.', '');
            $results[$i]['farming'] = $final_farming[$results[$i]['farming_id']];
            $finalResults[] = $results[$i];
            if ($i + 1 < $resultsCount) {
                if ($results[$i]['kad_ident'] != $results[$i + 1]['kad_ident']) {
                    $finalResults[] = [
                        'kad_ident' => $results[$i]['kad_ident'],
                        'c_num' => $newContract[0]['c_num'],
                        'farming' => $final_farming[$newContract[0]['farming_id']],
                        'start_date' => strftime('%d.%m.%Y', strtotime($startDate)),
                        'due_date' => '9999-12-31 00:00:00' != $dueDate ? strftime('%d.%m.%Y', strtotime($dueDate)) : null,
                        'contract_area' => number_format($finalRequest[$results[$i]['kad_ident']]['contract_area'], 3, '.', ''),
                        'document_area' => number_format($additional[$results[$i]['kad_ident']]['document_area'], 3, '.', ''),
                        'newContract' => true,
                    ];
                    $finalResults[] = [
                        'due_date' => '<b>Общо:</b>',
                        'farming' => '<b>Свободна площ:</b>',
                        'start_date' => number_format($additional[$results[$i]['kad_ident']]['document_area'] - $additional[$results[$i]['kad_ident']]['contract_area'], 3, '.', ''),
                        'contract_area' => number_format($additional[$results[$i]['kad_ident']]['contract_area'], 3, '.', ''),
                        'document_area' => number_format($additional[$results[$i]['kad_ident']]['document_area'], 3, '.', ''),
                        'isFooter' => true,
                    ];
                    $footerCount++;
                }
            } else {
                $finalResults[] = [
                    'kad_ident' => $results[$i]['kad_ident'],
                    'c_num' => $newContract[0]['c_num'],
                    'farming' => $final_farming[$newContract[0]['farming_id']],
                    'start_date' => strftime('%d.%m.%Y', strtotime($startDate)),
                    'due_date' => '9999-12-31 00:00:00' != $dueDate ? strftime('%d.%m.%Y', strtotime($dueDate)) : null,
                    'contract_area' => number_format($finalRequest[$results[$i]['kad_ident']]['contract_area'], 3, '.', ''),
                    'document_area' => number_format($additional[$results[$i]['kad_ident']]['document_area'], 3, '.', ''),
                    'newContract' => true,
                ];
                $finalResults[] = [
                    'due_date' => '<b>Общо:</b>',
                    'farming' => '<b>Свободна площ:</b>',
                    'start_date' => number_format($additional[$results[$i]['kad_ident']]['document_area'] - $additional[$results[$i]['kad_ident']]['contract_area'], 3, '.', ''),
                    'contract_area' => number_format($additional[$results[$i]['kad_ident']]['contract_area'], 3, '.', ''),
                    'document_area' => number_format($additional[$results[$i]['kad_ident']]['document_area'], 3, '.', ''),
                    'isFooter' => true,
                ];
                $footerCount++;
            }
        }

        return [
            'total' => count($finalResults) - $footerCount,
            'rows' => $finalResults,
        ];
    }
}
