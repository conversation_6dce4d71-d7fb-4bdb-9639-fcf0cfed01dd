<?php

namespace TF\Engine\APIClasses\Contracts;

use Exception;
use Prado\Web\Services\TJsonRpcProtocol;
use Prado\Web\Services\TRpcApiProvider;
use Prado\Web\Services\TRpcException;
use Prado\Web\Services\TRpcServer;
use TF\Engine\APIClasses\Common\ContractGroupsCombobox;
use TF\Engine\APIClasses\Plots\PlotsContractsGrid;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\UserDbContracts\UserDbContractsController;

// Prado::using('Plugins.Core.Farming.conf');
// Prado::using('Plugins.Core.Farming.*');
// Prado::using('Plugins.Core.Contracts.conf');
// Prado::using('Plugins.Core.Contracts.*');
// Prado::using('Plugins.Core.UserDbContracts.*');
// Prado::using('Plugins.Core.UserDbSubleases.*');
// Prado::using('Plugins.Core.UserDbPlots.*');
// Prado::using('Plugins.Core.UserDb.*');
// Prado::using('Plugins.Core.Users.*');
// Prado::using('Plugins.Core.UserDbContracts.*');
// Prado::using('APIClasses.Plots.PlotsContractsGrid');

/**
 * @rpc-module Contracts
 *
 * @rpc-service-id plot-contracts-grid
 */
class PlotContractsGrid extends TRpcApiProvider
{
    /**
     * Registers the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getContractsByPlot']],
        ];
    }

    /**
     * reads the contracts that a plot is involved in.
     *
     * @api-method read
     *
     * @param array $params {
     *                      #item integer plot_id            id of the plot
     *                      #item integer contract_id        id of the contract
     *                      }
     * @param int $page pagination rpc parameter
     * @param int $rows pagination rpc parameter
     * @param string $sort pagination rpc parameter
     * @param string $order pagination rpc parameter
     *
     * @throws TRpcException
     *
     * @return array {
     *               #item array rows {
     *               #item array {
     *               #item int id
     *               #item string c_num
     *               #item string c_date
     *               #item string nm_usage_rights
     *               #item string sv_num
     *               #item string sv_date
     *               #item string start_date
     *               #item string renta
     *               #item string due_date
     *               #item string  renta_nat
     *               #item int farming_id
     *               #item string comment
     *               #item string agg_type
     *               #item boolean active
     *               #item int parent_id
     *               #item boolean is_annex
     *               #item int renta_nat_type_id
     *               #item boolean is_sublease
     *               #item string original_due_date
     *               #item string original_renta
     *               #item string original_renta_nat
     *               #item string original_renta_nat_type_id
     *               #item string na_num
     *               #item string tom
     *               #item string delo
     *               #item string court
     *               #item string payday
     *               #item boolean is_declaration_subleased
     *               #item string contract_area
     *               #item string price_per_acre
     *               #item string price_sum
     *               #item string active_text
     *               #item int c_type
     *               #item string farming
     *               }
     *               }
     *               #item int total    Total rows
     *               }
     */
    public function getContractsByPlot($params = [], $page = null, $rows = null, $sort = null, $order = null)
    {
        $contract_id = $params['contract_id'];
        $plot_id = $params['plot_id'];

        // create default return
        $return = [
            'rows' => [],
            'total' => 0,
        ];

        if (!$contract_id || !$plot_id) {
            return $return;
        }

        $server = new TRpcServer(new TJsonRpcProtocol());
        $PlotsContractsGrid = new PlotsContractsGrid($server);

        $results = $PlotsContractsGrid->readContractsDatagrid($params, $page, $rows, $sort, $order);
        $rows = $results['rows'];
        $rowsCount = count($rows);
        for ($i = 0; $i < $rowsCount; $i++) {
            $row = $rows[$i];

            if ($row['id'] != $contract_id) {
                $return['rows'][] = $row;
            }
        }

        $return['total'] = $rowsCount;

        return $return;
    }

}
