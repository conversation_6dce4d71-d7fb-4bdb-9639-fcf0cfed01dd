<?php

namespace TF\Engine\APIClasses\Contracts;

use Prado\Web\Services\TRpcApiProvider;
use TF\Application\Common\Config;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbContracts\UserDbContractsController;
use TF\Engine\Plugins\Core\UserDbOwners\UserDbOwnersController;
use TF\Engine\Plugins\Core\UserDbPayments\UserDbPaymentsController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * "Добавяне на собственици към договор".
 *
 * @rpc-module Contracts
 *
 * @rpc-service-id contract-owner-data
 */
class ContractOwnerData extends TRpcApiProvider
{
    private $module = 'Contracts';
    private $service_id = 'contract-owner-data';

    /**
     * Registers the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'saveRep' => ['method' => [$this, 'saveRep'],
                'validators' => [
                    'rep_name' => 'validateText',
                    'rep_surname' => 'validateText',
                    'rep_lastname' => 'validateText',
                    'rep_egn' => 'validateText',
                    'rep_lk' => 'validateText',
                    'rep_lk_izdavane' => 'validateText',
                    'rep_address' => 'validateText',
                    'rent_place' => 'validateText',
                ]],
            'deleteRep' => ['method' => [$this, 'deleteRep']],
            'addNewPlotOwner' => ['method' => [$this, 'addNewPlotOwner'],
                'validatiors' => [
                    'owner_id' => 'validateInteger, validateNotNull, validateRequired',
                    'percent' => 'validateNumber',
                    'owner_document_id' => 'validateInteger',
                    'proxy_num' => 'validateText',
                    'proxy_date' => 'validateText',
                    'numerator' => 'validateText',
                    'denominator' => 'validateText',
                    'notary_name' => 'validateText',
                    'notary_number' => 'validateText',
                    'notary_address' => 'validateText',
                    'farming_id' => 'validateInteger',
                ]],
            'deletePlotOwner' => ['method' => [$this, 'deletePlotOwner'],
            ],
            'changeIsSigner' => ['method' => [$this, 'changeIsSigner'],
            ],
            'multiplyOwners' => ['method' => [$this, 'multiplyOwners']],
            'multiplyFarming' => ['method' => [$this, 'multiplyFarming']],
            'deletePCToFarmingRelation' => ['method' => [$this, 'deletePCToFarmingRelation']],
            'editOwnerPercent' => ['method' => [$this, 'editOwnerPercent']],
        ];
    }

    /**
     * Saves a plot owner representative.
     *
     * @api-method saveRep
     *
     * @param array $representative {
     *                              #item integer id
     *                              #item integer owner_id
     *                              #item string rep_name
     *                              #item string rep_surname
     *                              #item string rep_lastname
     *                              #item string rep_egn
     *                              #item string rep_lk
     *                              #item string rep_lk_izdavane
     *                              #item string rep_address
     *                              }
     *
     * @throws MTRpcException
     *
     * @return int
     */
    public function saveRep($representative)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        $FarmingController = new FarmingController('Farming');

        $options = [
            'tablename' => $UserDbController->DbHandler->tableOwnersReps,
            'mainData' => [
                'rep_name' => $representative['rep_name'],
                'rep_surname' => $representative['rep_surname'],
                'rep_lastname' => $representative['rep_lastname'],
                'rep_egn' => $representative['rep_egn'],
                'rep_lk' => $representative['rep_lk'],
                'rep_lk_izdavane' => $representative['rep_lk_izdavane'],
                'rep_phone' => $representative['rep_phone'],
                'rep_address' => $representative['rep_address'],
                'rent_place' => $representative['rent_place'],
                'iban' => $representative['iban'],
            ],
        ];

        if (null !== $representative['owner_id']) {
            // ако има и owner_id значи представителя е създаден от собствик и при опит за редакция няма да се променят данните в таблицата su_owners
            throw new MTRpcException('ILLEGAL_REPRESENTATIVE_EDIT_IS_OWNER', -33311);
        }
        if (!$representative['id']) {
            $item_id = $UserDbController->addItem($options);

            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options['mainData'], ['id' => $item_id], 'Add representative');

            return $item_id;
        }

        $oldOptions = [
            'tablename' => $UserDbController->DbHandler->tableOwnersReps,
            'return' => [
                '*',
            ],
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $representative['id']],
            ],
        ];
        $oldValues = $UserDbController->getItemsByParams($oldOptions, false, false);

        $options['where'] = ['id' => $representative['id']];
        $UserDbController->editItem($options);

        $molName = $representative['rep_name'] . ' ' . $representative['rep_surname'] . ' ' . $representative['rep_lastname'];
        $updateMolParams = [
            'group_id' => $this->User->GroupID,
            'representative_id' => $representative['id'],
            'mol_name' => $molName,
            'mol_eng' => $representative['rep_egn'],
        ];
        $FarmingController->updateFarmingMol($updateMolParams);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options['mainData'], $oldValues, 'Edit representative');
    }

    /**
     * Deletes an owner representative.
     *
     * @api-method deleteRep
     *
     * @param int $representativeId
     *
     * @return array
     */
    public function deleteRep($representativeId)
    {
        if ($this->User->isGuest) {
            return [];
        }

        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $id_array[] = $representativeId;
        $options = [
            'tablename' => $UserDbController->DbHandler->tableOwnersReps,
            'id_string' => implode(', ', $id_array),
        ];

        if ($representativeId) {
            $oldOptions = [
                'tablename' => $UserDbController->DbHandler->tableOwnersReps,
                'return' => [
                    '*',
                ],
                'where' => [
                    'id' => ['column' => 'id', 'compare' => '=', 'value' => $representativeId],
                ],
            ];
            $oldValues = $UserDbController->getItemsByParams($oldOptions, false, false);

            $UserDbController->deleteItemsByParams($options);

            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, [], $oldValues, 'Delete representative');
        }
    }

    /**
     * Adds a on owner to a given plot.
     *
     * @api-method addNewPlotOwner
     *
     * @param array $data {
     *                    #item integer contract_id
     *                    #item integer owner_id
     *                    #item integer plot_id
     *                    #item integer farming_id
     *                    #item integer denominator
     *                    #item integer numerator
     *                    #item integer document_id
     *                    #item float percent
     *                    #item date proxy_date The date in DD.MM.YYYY
     *                    #item string proxy_num
     *                    #item integer rep_id
     *                    #item boolean self_rep
     *                    #item string notary_name
     *                    #item integer notary_number
     *                    #item string notary_address
     *                    }
     *
     * @throws MTRpcException "INVALID_USER_CONTRACTS_RIGHTS_OWN_CONTRACTS" -33200
     * @throws MTRpcException "non_existing_owner_id" -33206
     * @throws MTRpcException "NON_EXISTING_CONTRACT_PLOT_RELATION" -33204
     *
     * @return int relation id
     */
    public function addNewPlotOwner($data)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);
        $UserDbContractsController = new UserDbContractsController($this->User->Database);
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);
        $UsersController = new UsersController('Users');
        $FarmingController = new FarmingController('Farming');

        $UserDbController->disableRentaMatViewTriggers();

        // get contractType
        $contractType = $UserDbContractsController->getContractType($data['contract_id']);
        $isFromSublease = $UserDbContractsController->isContractFromSublease($data['contract_id']);
        if ($isFromSublease) {
            throw new MTRpcException('CANNOT_MODIFY_FROM_SUBLEASE_CONTRACT', -33752);
        }
        // No Rights to operate with "Договори за собственост"
        if (Config::CONTRACT_TYPE_OWN == $contractType && !$this->User->HasContractsOwnWriteRights) {
            throw new MTRpcException('INVALID_USER_CONTRACTS_RIGHTS_OWN_CONTRACTS', -33200);
        }

        $UserDbPaymentsController->hasPaymentRestriction($data['contract_id']);

        if ($data['self_rep']) {
            $repID = null;
        } else {
            $repID = $data['rep_id'];
        }

        $allPlotsForContract = $UserDbContractsController->getContractPlotData([
            'return' => [
                'c.*', 'kvs.*', 'pc.*', 'po.owner_id', 'po.rep_id', 'po.id as plot_owner_id',
            ],
            'where' => [
                'contract_id' => [
                    'column' => 'contract_id',
                    'compare' => '=',
                    'prefix' => 'pc',
                    'value' => $data['contract_id'],
                ],
            ],
            'joins' => [
                'LEFT JOIN su_plots_owners_rel po ON (po.pc_rel_id = pc.id) ',
            ],
        ]);

        $plotFromRequestMatch = array_filter($allPlotsForContract, function ($item) use ($data) {
            return $item['plot_id'] === $data['plot_id'];
        });
        $relation_id = current($plotFromRequestMatch)['id'];

        $allOtherPlotsExceptFromTheRequest = array_filter($allPlotsForContract, function ($item) use ($data) {
            return $item['plot_id'] !== $data['plot_id']
                && $item['owner_id'] === $data['owner_id']
                && !empty($item['rep_id']);
        });

        $allOtherPlotsExceptFromTheRequest = array_values($allOtherPlotsExceptFromTheRequest);

        if ($data['self_rep'] && count($allOtherPlotsExceptFromTheRequest) > 0) {
            $repID = $allOtherPlotsExceptFromTheRequest[0]['rep_id'];
        }

        if (null == $relation_id) {
            throw new MTRpcException('NON_EXISTING_CONTRACT_PLOT_RELATION', -33204);
        }

        if ($data['owner_id']) {
            $options = [
                'tablename' => $UserDbController->DbHandler->plotsOwnersRelTable,
                'mainData' => [
                    'pc_rel_id' => $relation_id,
                    'owner_id' => $data['owner_id'],
                    'percent' => $data['percent'],
                    'owner_document_id' => $data['document_id'],
                    'rep_id' => $repID,
                    'proxy_num' => $data['proxy_num'],
                    'proxy_date' => $data['proxy_date'],
                    'numerator' => $data['numerator'],
                    'denominator' => $data['denominator'],
                    'notary_name' => $data['notary_name'],
                    'notary_number' => $data['notary_number'],
                    'notary_address' => $data['notary_address'],
                    'is_set_manual' => true,
                ],
            ];
        } elseif ($data['farming_id']) {
            if (!$repID) {
                // get the default farming representative
                $farming = $FarmingController->getFarmingItemsByIDString($data['farming_id'], $this->User->GroupID);
                if (!empty($farming)) {
                    $repID = $farming[0]['representative_id'];
                }
            }

            $options = [
                'tablename' => $UserDbController->DbHandler->plotsFarmingRelTable,
                'mainData' => [
                    'pc_rel_id' => $relation_id,
                    'farming_id' => $data['farming_id'],
                    'percent' => $data['percent'],
                    'rep_id' => $repID,
                    'proxy_num' => $data['proxy_num'],
                    'proxy_date' => $data['proxy_date'],
                    'numerator' => $data['numerator'],
                    'denominator' => $data['denominator'],
                ],
            ];
        }
        $item_id = $UserDbController->addItem($options);

        if ($data['owner_id']) {
            $UserDbOwnersController->buildHeritorsTree(
                $data['owner_id'] . '.*{1}',
                $data['percent'],
                $data['numerator'],
                $data['denominator'],
                1,
                (string)($data['owner_id'] . $data['owner_id']),
                $relation_id,
                true
            );
        }

        $UserDbController->enableRentaMatViewTriggers();
        $UserDbController->refreshRentaViews();
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options, ['plot_owner_id' => $item_id], 'Adds plot - owner (farming) relation');
        // if owner represents himself a representative record should be added(if not exists)

        if (count($allOtherPlotsExceptFromTheRequest) > 0 && !$data['self_rep']) {
            $options = [
                'tablename' => $UserDbController->DbHandler->plotsOwnersRelTable,
                'mainData' => [
                    'rep_id' => $repID,
                ],
                'id_string' => implode(',', array_column($allOtherPlotsExceptFromTheRequest, 'plot_owner_id')),
            ];

            $UserDbController->editItem($options);
        }

        return $item_id;
    }

    /**
     * @api-method deletePlotOwner
     *
     * @param int $plotOwnerRelId
     * @param int $ownerId
     *
     * @throws MTRpcException
     *
     * @return array|void
     */
    public function deletePlotOwner($plotOwnerRelId, $ownerId)
    {
        if (!$plotOwnerRelId) {
            return [];
        }

        $UserDbContractsController = new UserDbContractsController($this->User->Database);
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);

        $options = [
            'return' => ['pc.id', 'contract_id', 'plot_id'],
            'where' => [
                'po_id' => ['column' => 'id', 'compare' => '=', 'prefix' => 'po', 'value' => $plotOwnerRelId],
            ],
        ];
        $pc_rel = $UserDbContractsController->getPlotOwnerRelData($options, false, false);
        // get contractType
        $options = [
            'tablename' => 'su_contracts c',
            'return' => [
                'c.id',
                'c.parent_id',
                'c.nm_usage_rights',
                'c.start_date',
                'c.due_date',
            ],
            'where' => [
                'contract_id' => ['column' => 'id', 'compare' => '=', 'value' => $pc_rel[0]['contract_id']],
            ],
        ];
        $contractInfo = $UserDbContractsController->getItemsByParams($options);

        // No Rights to operate with "Договори за собственост"
        if (Config::CONTRACT_TYPE_OWN == $contractInfo[0]['nm_usage_rights'] && !$this->User->HasContractsOwnWriteRights) {
            throw new MTRpcException('INVALID_USER_CONTRACTS_RIGHTS_OWN_CONTRACTS', -33200);
        }

        $isFromSublease = $UserDbContractsController->isContractFromSublease($pc_rel[0]['contract_id']);
        if ($isFromSublease) {
            throw new MTRpcException('CANNOT_MODIFY_FROM_SUBLEASE_CONTRACT', -33752);
        }

        $additionalParams = [];
        if (!empty($contractInfo[0]['parent_id'])) {
            $additionalParams = [
                'parent_id' => $contractInfo[0]['parent_id'],
                'start_date' => $contractInfo[0]['start_date'],
                'due_date' => $contractInfo[0]['due_date'],
            ];
        }

        $UserDbPaymentsController->hasPaymentRestriction($pc_rel[0]['contract_id'], $additionalParams);

        $UserDbOwnersController->deletePCToOwnerRelation($pc_rel[0]['id'], $ownerId);
        $UserDbOwnersController->deletePCToHeritorRelation($pc_rel[0]['id'], $ownerId . '.*');

        $oldData = [
            'plot_owner_relation_id' => $plotOwnerRelId,
            'contract_id' => $pc_rel[0]['contract_id'],
            'owner_id' => $ownerId,
            'plot_id' => $pc_rel[0]['plot_id'],
        ];
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, [], $oldData, 'delete plot - owner relation');
    }

    /**
     * Copy all owners of a given plot to all plots in a given contract.
     *
     * @api-method multiplyOwners
     *
     * @param int $contractId
     * @param int $plotId
     *
     * @throws MTRpcException
     *
     * @return string a message on success or fail
     */
    public function multiplyOwners($contractId, $plotId)
    {
        $UserDbContractsController = new UserDbContractsController($this->User->Database);
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);

        $oldOptions = [
            'return' => [
                'c.*',
                'cpr.plot_id',
            ],
            'tablename' => $UserDbController->DbHandler->tableContracts . ' c',
            'innerjoin' => [
                'table' => $UserDbController->DbHandler->contractsPlotsRelTable . ' cpr',
                'condition' => ' ON (c.id = cpr.contract_id)',
            ],
            'where' => [
                'id' => ['column' => 'c.id', 'compare' => '=', 'value' => $contractId],
            ],
        ];
        $oldData = $UserDbController->getItemsByParams($oldOptions);

        // No Rights to operate with "Договори за собственост"
        if (Config::CONTRACT_TYPE_OWN == $oldData[0]['nm_usage_rights'] && !$this->User->HasContractsOwnWriteRights) {
            throw new MTRpcException('INVALID_USER_CONTRACTS_RIGHTS_OWN_CONTRACTS', -33200);
        }
        $isFromSublease = $UserDbContractsController->isContractFromSublease($contractId);
        if ($isFromSublease) {
            throw new MTRpcException('CANNOT_MODIFY_FROM_SUBLEASE_CONTRACT', -33752);
        }

        $additionalParams = [];
        if (!empty($oldData[0]['parent_id'])) {
            $additionalParams = [
                'parent_id' => $oldData[0]['parent_id'],
                'start_date' => $oldData[0]['start_date'],
                'due_date' => $oldData[0]['due_date'],
            ];
        }
        $UserDbPaymentsController->hasPaymentRestriction($contractId, $additionalParams);

        $chosen_relation = $UserDbContractsController->getContractPlotRelationID($contractId, $plotId);

        if (!$chosen_relation) {
            throw new MTRpcException('NON_EXISTING_CONTRACT_PLOT_RELATION', -33204);
        }

        $options = [
            'return' => ['t.id', 'rel.*'],
            'where' => [
                'rel_id' => ['column' => 'pc_rel_id', 'compare' => '=', 'prefix' => 'rel', 'value' => $chosen_relation],
            ],
        ];

        $results = $UserDbOwnersController->getPlotOwnersData($options, false, false);
        $resultsCount = count($results);
        // getPlotContractRelationIDs
        $options = [
            'tablename' => $UserDbController->DbHandler->contractsPlotsRelTable,
            'return' => ['id', 'plot_id'],
            'where' => [
                'contract_id' => ['column' => 'contract_id', 'compare' => '=', 'value' => $contractId],
                'plot_id' => ['column' => 'plot_id', 'compare' => '<>', 'value' => $plotId],
            ],
        ];

        $targets = $UserDbController->getItemsByParams($options);

        $targetsCount = count($targets);
        if (0 == $targetsCount) {
            throw new MTRpcException('NON_EXISTING_CONTRACT_PLOT_RELATION', -33204);
        }

        $target_string = '';

        for ($i = 0; $i < $targetsCount; $i++) {
            $target_string .= $targets[$i]['id'];
            if ($i < $targetsCount - 1) {
                $target_string .= ',';
            }
        }
        $UserDbController->disableRentaMatViewTriggers();
        // delete old owners
        $options = [
            'tablename' => $UserDbController->DbHandler->plotsOwnersRelTable,
            'id_name' => 'pc_rel_id',
            'id_string' => $target_string,
        ];

        $UserDbOwnersController->deleteItemsByParams($options);
        $oldData = [
            'contract_id' => $contractId,
            'contract_plots_rels' => $targets,
        ];
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $oldData, [], 'delete plot - owner relation to multiple plots in a contract');

        for ($i = 0; $i < $targetsCount; $i++) {
            for ($j = 0; $j < $resultsCount; $j++) {
                $relData = $results[$j];

                $options = [
                    'tablename' => $UserDbController->DbHandler->plotsOwnersRelTable,
                    'mainData' => $relData,
                ];
                $options['mainData']['pc_rel_id'] = $targets[$i]['id'];
                unset($options['mainData']['id']);

                $item_id = $UserDbController->addItem($options);

                $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options['mainData'], ['plot_owner_relation_id' => $item_id], 'Adds plot - owner relation');
            }
        }
        $UserDbController->enableRentaMatViewTriggers();
        $UserDbController->refreshRentaViews();

        return Config::STATUS_CODE_OK;
    }

    /**
     * Copy all farms as owners of all plots in a contract.
     *
     * @api-method multiplyFarming
     *
     * @param int $contractId
     * @param int $plotId
     *
     * @throws MTRpcException
     *
     * @return int
     */
    public function multiplyFarming($contractId, $plotId)
    {
        $UserDbContractsController = new UserDbContractsController($this->User->Database);
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        // get contractType
        $contractType = $UserDbContractsController->getContractType($contractId);

        // No Rights to operate with "Договори за собственост"
        if (Config::CONTRACT_TYPE_OWN == $contractType && !$this->User->HasContractsOwnWriteRights) {
            throw new MTRpcException('INVALID_USER_CONTRACTS_RIGHTS_OWN_CONTRACTS', -33200);
        }
        $isFromSublease = $UserDbContractsController->isContractFromSublease($contractId);
        if ($isFromSublease) {
            throw new MTRpcException('CANNOT_MODIFY_FROM_SUBLEASE_CONTRACT', -33752);
        }

        $chosen_relation = $UserDbContractsController->getContractPlotRelationID($contractId, $plotId);

        if (!$chosen_relation) {
            throw new MTRpcException('NON_EXISTING_CONTRACT_PLOT_RELATION', -33204);
        }

        $options = [
            'return' => ['*'],
            'tablename' => $UserDbController->DbHandler->plotsFarmingRelTable,
            'where' => [
                'rel_id' => ['column' => 'pc_rel_id', 'compare' => '=', 'value' => $chosen_relation],
            ],
        ];

        $results = $UserDbController->getItemsByParams($options, false, false);
        $resultsCount = count($results);

        // getPlotContractRelationIDs
        $options = [
            'tablename' => $UserDbController->DbHandler->contractsPlotsRelTable,
            'return' => ['id', 'plot_id'],
            'where' => [
                'contract_id' => ['column' => 'contract_id', 'compare' => '=', 'value' => $contractId],
                'plot_id' => ['column' => 'plot_id', 'compare' => '<>', 'value' => $plotId],
            ],
        ];

        $targets = $UserDbController->getItemsByParams($options);
        $targetCount = count($targets);

        if (0 == $targetCount) {
            throw new MTRpcException('NON_EXISTING_CONTRACT_PLOT_RELATION', -33204);
        }

        $target_string = '';
        for ($i = 0; $i < $targetCount; $i++) {
            $target_string .= $targets[$i]['id'];
            if ($i < $targetCount - 1) {
                $target_string .= ',';
            }
        }
        // delete old farmings
        $options = [
            'tablename' => $UserDbController->DbHandler->plotsFarmingRelTable,
            'id_name' => 'pc_rel_id',
            'id_string' => $target_string,
        ];
        $oldData = [
            'contract_id' => $contractId,
            'contract_plots_rels' => $targets,
        ];
        $UserDbOwnersController->deleteItemsByParams($options);
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $oldData, [], 'delete plot - farming relations of multiple plots in a contract');

        for ($i = 0; $i < $targetCount; $i++) {
            for ($j = 0; $j < $resultsCount; $j++) {
                $relData = $results[$j];

                $options = [
                    'tablename' => $UserDbController->DbHandler->plotsFarmingRelTable,
                    'mainData' => $relData,
                ];
                $options['mainData']['pc_rel_id'] = $targets[$i]['id'];
                unset($options['mainData']['id']);

                $item_id = $UserDbController->addItem($options);
                $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options['mainData'], ['plot_farming_relation_id' => $item_id], 'Adds plot - farming relation');
            }
        }

        return Config::STATUS_CODE_OK;
    }

    /**
     * Delete farm as an owner of a plot.
     *
     * @api-method deletePCToFarmingRelation
     *
     * @param int $farmingRelId
     * @param int $contractId
     *
     * @throws MTRpcException
     *
     * @return array
     */
    public function deletePCToFarmingRelation($farmingRelId, $contractId)
    {
        if (!$farmingRelId) {
            return [];
        }
        $UserDbContractsController = new UserDbContractsController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $options = [
            'return' => ['plot_id', 'pf.farming_id'],
            'where' => [
                'pf_id' => ['column' => 'id', 'compare' => '=', 'prefix' => 'pf', 'value' => $farmingRelId],
            ],
        ];

        $farming_results = $UserDbContractsController->getPlotFarmingRelData($options);

        // get contractType
        $contractType = $UserDbContractsController->getContractType($contractId);

        // No Rights to operate with "Договори за собственост"
        if (Config::CONTRACT_TYPE_OWN == $contractType && !$this->User->HasContractsOwnWriteRights) {
            throw new MTRpcException('INVALID_USER_CONTRACTS_RIGHTS_OWN_CONTRACTS', -33200);
        }

        $isFromSublease = $UserDbContractsController->isContractFromSublease($contractId);
        if ($isFromSublease) {
            throw new MTRpcException('CANNOT_MODIFY_FROM_SUBLEASE_CONTRACT', -33752);
        }

        $options = [
            'tablename' => $UserDbController->DbHandler->plotsFarmingRelTable,
            'id_string' => $farmingRelId,
        ];

        $UserDbController->deleteItemsByParams($options);

        $oldData = [
            'plot_farming_relation_id' => $farmingRelId,
            'contract_id' => $contractId,
            'farming_id' => $farming_results[0]['farming_id'],
            'plot_id' => $farming_results[0]['plot_id'],
        ];

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $oldData, [], 'deletes a plot - farming relation');
    }

    /**
     * Edits owner or farming percent for given plot and contract.
     *
     * @api-method editOwnerPercent
     *
     * @param array $data {
     *                    #item boolean is_heritor
     *                    #item integer contract_id
     *                    #item integer plot_id
     *                    #item string path
     *                    #item integer owner_id
     *                    #item integer numerator
     *                    #item integer denominator
     *                    #item float percent
     *                    #item integer po_id su_plots_owners_rel.id
     *                    #item integer pf_if su_plots_farming_rel.id
     *                    }
     *
     * @throws MTRpcException
     *
     * @return array
     */
    public function editOwnerPercent($data)
    {
        if (!$data) {
            return [];
        }

        $UserDbContractsController = new UserDbContractsController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);

        $UserDbController->disableRentaMatViewTriggers();

        // get contractType
        $contractType = $UserDbContractsController->getContractType($data['contract_id']);

        $oldOptions = [
            'return' => [
                'c.*',
                'cpr.plot_id',
            ],
            'tablename' => $UserDbController->DbHandler->tableContracts . ' c',
            'innerjoin' => [
                'table' => $UserDbController->DbHandler->contractsPlotsRelTable . ' cpr',
                'condition' => ' ON (c.id = cpr.contract_id)',
            ],
            'where' => [
                'id' => ['column' => 'c.id', 'compare' => '=', 'value' => $data['contract_id']],
            ],
        ];
        $oldData = $UserDbController->getItemsByParams($oldOptions);

        // No Rights to operate with "Договори за собственост"
        if (Config::CONTRACT_TYPE_OWN == $oldData[0]['nm_usage_rights'] && !$this->User->HasContractsOwnWriteRights) {
            throw new MTRpcException('INVALID_USER_CONTRACTS_RIGHTS_OWN_CONTRACTS', -33200);
        }
        $isFromSublease = $UserDbContractsController->isContractFromSublease($data['contract_id']);
        if ($isFromSublease) {
            throw new MTRpcException('CANNOT_MODIFY_FROM_SUBLEASE_CONTRACT', -33752);
        }

        $additionalParams = [];
        if (!empty($oldData[0]['parent_id'])) {
            $additionalParams = [
                'parent_id' => $oldData[0]['parent_id'],
                'start_date' => $oldData[0]['start_date'],
                'due_date' => $oldData[0]['due_date'],
            ];
        }
        $UserDbPaymentsController->hasPaymentRestriction($data['contract_id'], $additionalParams);

        if ($data['is_heritor']) {
            $relation_id = $UserDbContractsController->getContractPlotRelationID($data['contract_id'], $data['plot_id']);
            if (!$relation_id) {
                throw new MTRpcException('NON_EXISTING_CONTRACT_PLOT_RELATION', -33204);
            }
            $options = [
                'tablename' => $UserDbController->DbHandler->plotsOwnersRelTable,
                'return' => ['*'],
                'where' => [
                    'pc_rel_id' => ['column' => 'pc_rel_id', 'compare' => '=', 'value' => $relation_id],
                    'path' => ['column' => 'path', 'compare' => '~', 'value' => $data['path']],
                    'is_heritor' => ['column' => 'is_heritor', 'compare' => '=', 'value' => 'TRUE'],
                ],
            ];
            $result = $UserDbController->getItemsByParams($options);

            $options = [
                'tablename' => $UserDbController->DbHandler->plotsOwnersRelTable,
                'mainData' => [
                    'pc_rel_id' => $relation_id,
                    'owner_id' => $data['owner_id'],
                    'percent' => $data['percent'],
                    'path' => $data['path'],
                    'is_heritor' => $data['is_heritor'],
                    'numerator' => $data['numerator'],
                    'denominator' => $data['denominator'],
                    'is_set_manual' => true,
                ],
            ];

            if (0 == count($result)) {
                // add relation item
                $rel_id = $UserDbController->addItem($options);

                $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options['mainData'], ['plot_owner_relation_id' => $rel_id], 'Adds plot - heritor relation');
            } else {
                // edit relation item
                $options = [
                    'tablename' => $UserDbController->DbHandler->plotsOwnersRelTable,
                    'mainData' => [
                        'percent' => $data['percent'],
                        'numerator' => $data['numerator'],
                        'denominator' => $data['denominator'],
                        'is_set_manual' => true,
                    ],
                    'where' => [
                        'id' => $result[0]['id'],
                    ],
                ];

                $UserDbController->editItem($options);
                $oldData = [
                    'plot_owner_rel' => $result[0]['id'],
                    'percent' => $result['percent'],
                    'numerator' => $result['numerator'],
                    'denominator' => $result['denominator'],
                ];
                $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options['mainData'], $oldData, 'edits plot - heritor relation');
            }

            $UserDbOwnersController->createPlotsOwnersRelation($data['path']);
        } else {
            if ($data['po_id']) {
                $options = [
                    'return' => ['pc.id', 'contract_id', 'plot_id', 'numerator', 'denominator', 'owner_id', 'pc_rel_id'],
                    'where' => [
                        'po_id' => ['column' => 'id', 'compare' => '=', 'prefix' => 'po', 'value' => $data['po_id']],
                    ],
                ];
                $pc_rel = $UserDbContractsController->getPlotOwnerRelData($options, false, false);

                $oldData = [
                    'plot_owner_relation_id' => $data['po_id'],
                    'contract_id' => $pc_rel[0]['contract_id'],
                    'owner_id' => $pc_rel[0]['owner_id'],
                    'plot_id' => $pc_rel[0]['plot_id'],
                    'numerator' => $pc_rel[0]['numerator'],
                    'denominator' => $pc_rel[0]['denominator'],
                ];
                // edit relation item
                $options = [
                    'tablename' => $UserDbController->DbHandler->plotsOwnersRelTable,
                    'mainData' => [
                        'percent' => $data['percent'],
                        'numerator' => $data['numerator'],
                        'denominator' => $data['denominator'],
                        'is_set_manual' => true,
                    ],
                    'where' => [
                        'id' => $data['po_id'],
                    ],
                ];
            } elseif ($data['pf_id']) {
                $options = [
                    'return' => ['plot_id', 'contract_id', 'pf.farming_id', 'pf.percent', 'pf.numerator', 'pf.denominator'],
                    'where' => [
                        'pf_id' => ['column' => 'id', 'compare' => '=', 'prefix' => 'pf', 'value' => $data['pf_id']],
                    ],
                ];
                // todo check the line 'contract_id' => $pc_rel[0]['contract_id'], $pc_rel is undefined
                $farming_results = $UserDbContractsController->getPlotFarmingRelData($options);
                $oldData = [
                    'plot_farming_relation_id' => $data['pf_id'],
                    'contract_id' => $pc_rel[0]['contract_id'],
                    'farming_id' => $farming_results[0]['farming_id'],
                    'plot_id' => $farming_results[0]['plot_id'],
                    'numerator' => $farming_results[0]['numerator'],
                    'denominator' => $farming_results[0]['denominator'],
                ];
                // edit farming relation item
                $options = [
                    'tablename' => $UserDbController->DbHandler->plotsFarmingRelTable,
                    'mainData' => [
                        'percent' => $data['percent'],
                        'numerator' => $data['numerator'],
                        'denominator' => $data['denominator'],
                    ],
                    'where' => [
                        'id' => $data['pf_id'],
                    ],
                ];
            }

            $newData = $options['mainData'];
            $newData['id'] = $options['where']['id'];
            $newData['tablename'] = $options['tablename'];

            $UserDbController->editItem($options);

            if ($data['po_id']) {
                $UserDbOwnersController->buildHeritorsTree(
                    $pc_rel[0]['owner_id'] . '.*{1}',
                    $data['percent'],
                    $data['numerator'],
                    $data['denominator'],
                    1,
                    (string)($pc_rel[0]['owner_id'] . $pc_rel[0]['owner_id']),
                    $pc_rel[0]['pc_rel_id'],
                    true
                );
            }

            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $newData, $oldData, 'edits plot - owner/farming relation');
        }

        $UserDbController->enableRentaMatViewTriggers();
        $UserDbController->refreshRentaViews();
    }

    /**
     * @api-method changeIsSigner
     *
     * @param int $plotOwnerRelId
     *
     * @throws MTRpcException
     *
     * @return array|void
     */
    public function changeIsSigner($plotOwnerRelId, $signed)
    {
        if (!$plotOwnerRelId) {
            return [];
        }

        $UserDbContractsController = new UserDbContractsController($this->User->Database);
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);

        $options = [
            'return' => ['pc.id', 'contract_id', 'plot_id', 'is_signer'],
            'where' => [
                'po_id' => ['column' => 'id', 'compare' => '=', 'prefix' => 'po', 'value' => $plotOwnerRelId],
            ],
        ];
        $pc_rel = $UserDbContractsController->getPlotOwnerRelData($options, false, false);

        // get contractType
        $contractType = $UserDbContractsController->getContractType($pc_rel[0]['contract_id']);
        // No Rights to operate with "Договори за собственост"
        if (Config::CONTRACT_TYPE_OWN == $contractType && !$this->User->HasContractsOwnWriteRights) {
            throw new MTRpcException('INVALID_USER_CONTRACTS_RIGHTS_OWN_CONTRACTS', -33200);
        }

        $isFromSublease = $UserDbContractsController->isContractFromSublease($pc_rel[0]['contract_id']);
        if ($isFromSublease) {
            throw new MTRpcException('CANNOT_MODIFY_FROM_SUBLEASE_CONTRACT', -33752);
        }

        $UserDbOwnersController->changeContractOwnerSignedDoc(['id' => $plotOwnerRelId], $signed);
    }
}
