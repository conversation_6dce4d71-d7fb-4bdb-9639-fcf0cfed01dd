<?php

namespace TF\Engine\APIClasses\Contracts;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDbContracts\UserDbContractsController;
use TF\Engine\Plugins\Core\UserDbSubleases\UserDbSubleasesController;

/**
 * Списък с имоти, които съществуват в съответните договори за преотдаване със
 * съвпадащ период на действие или сумарната площ по договори е по-голяма
 * от площта по документ.
 *
 * @rpc-module Contracts
 *
 * @rpc-service-id subleases-plots-confirmation-datagrid
 *
 * @property UserDbSubleasesController $UserDbSubleasesController
 * @property UserDbContractsController $UserDbContractsController
 * @property FarmingController $FarmingController
 */
class SubleasesPlotsConfirmationGrid extends TRpcApiProvider
{
    private $UserDbController;
    private $UserDbSubleasesController;
    private $UserDbContractsController;
    private $FarmingController = false;

    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getSubleasesPlotsConfirmationGrid'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * Grid info.
     *
     * @param array $rpcParams - празен масив, сложен заради pagination
     * @param int $page pagination parameter
     * @param int $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array
     *               {
     *               #item array rows
     *               {
     *               #item double area
     *               #item string c_num
     *               #item double contract_area
     *               #item integer contract_id
     *               #item double document_area
     *               #item date due_date
     *               #item string farming
     *               #item integer farming_id
     *               #item string kad_ident
     *               #item date start_date
     *               }
     *               #item integer total
     *               }
     */
    public function getSubleasesPlotsConfirmationGrid(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        if ($this->User->isGuest) {
            return [];
        }
        $UserDbSubleasesController = new UserDbSubleasesController($this->User->Database);
        $UserDbContractsController = new UserDbContractsController($this->User->Database);

        $FarmingController = new FarmingController('Farming');
        $farmings = $FarmingController->getUserFarmings();
        $farmingIds = array_keys($farmings);

        $contractID = $_SESSION['plots_waiting_confirmation']['contract_id'];

        // create default return
        $return = [
            'rows' => [],
            'total' => 0,
        ];

        $options = [
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
            'return' => [
                'start_date', 'due_date',
            ],
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'prefix' => 'c', 'value' => $contractID],
                'farming_id' => ['column' => 'farming_id', 'compare' => 'IN', 'prefix' => 'c', 'value' => $farmingIds],
            ],
        ];

        $counter = $UserDbContractsController->getContractPlotData($options, true, false);

        if (0 == $counter[0]['count']) {
            return $return;
        }

        $results = $UserDbContractsController->getContractPlotData($options, false, false);

        $startDate = $results[0]['start_date'];
        $dueDate = $results[0]['due_date'];

        if (null === $dueDate) {
            $dueDate = '9999-12-31 00:00:00';
        }
        // create default return
        $return = [
            'rows' => [],
            'total' => 0,
        ];

        $options = [
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
            'return' => [
                'c.id as contract_id', 'c.c_num', 'c.farming_id', 'c.start_date', 'c.due_date', 'St_Area(geom) as area', 'spa.contract_area', 'document_area', 'kad_ident',
            ],
            'where' => [
                'pc_rel_id' => ['column' => 'id', 'compare' => 'IN', 'prefix' => 'pc', 'value' => $_SESSION['plots_waiting_confirmation']['pc_rel_id_array']],
                'start_date' => ['column' => 'start_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $dueDate],
                'due_date' => ['column' => '(CASE WHEN c.nm_usage_rights = 1 THEN \'9999-12-31 00:00:00\' ELSE c.due_date END)', 'compare' => '>=', 'value' => $startDate],
                'farming_id' => ['column' => 'farming_id', 'prefix' => 'c', 'compare' => 'IN', 'value' => $farmingIds],
            ],
        ];

        $counter = $UserDbSubleasesController->getSubleasePlotsData($options, true, false);

        if (0 == $counter[0]['count']) {
            return $return;
        }

        $results = $UserDbSubleasesController->getSubleasePlotsData($options, false, false);
        $resultsCount = count($results);
        $additional = [];
        $finalResults = [];
        for ($i = 0; $i < $resultsCount; $i++) {
            if (!$additional[$results[$i]['kad_ident']]['document_area']) {
                $additional[$results[$i]['kad_ident']]['document_area'] += $results[$i]['document_area'];
            }
            $additional[$results[$i]['kad_ident']]['contract_area'] += $results[$i]['contract_area'];
        }

        for ($i = 0; $i < $resultsCount; $i++) {
            $results[$i]['area'] = number_format($results[$i]['area'] / 1000, 3, '.', '');
            $results[$i]['start_date'] = strftime('%d.%m.%Y', strtotime($results[$i]['start_date']));

            if ('' != $results[$i]['due_date']) {
                $results[$i]['due_date'] = strftime('%d.%m.%Y', strtotime($results[$i]['due_date']));
            } else {
                $results[$i]['due_date'] = '-';
            }

            if (!$results[$i]['document_area']) {
                $results[$i]['document_area'] = $results[$i]['area'];
            }

            $results[$i]['contract_area'] = number_format($results[$i]['contract_area'], 3, '.', '');
            $results[$i]['document_area'] = number_format($results[$i]['document_area'], 3, '.', '');

            $results[$i]['farming'] = $farmings[$results[$i]['farming_id']];
            $finalResults[] = $results[$i];
            if ($i + 1 < count($results)) {
                if ($results[$i]['kad_ident'] != $results[$i + 1]['kad_ident']) {
                    $finalResults[] = [
                        'due_date' => '<b>Общо:</b>',
                        'farming' => '<b>Свободна площ:</b>',
                        'start_date' => number_format($additional[$results[$i]['kad_ident']]['document_area'] - $additional[$results[$i]['kad_ident']]['contract_area'], 3, '.', ''),
                        'contract_area' => number_format($additional[$results[$i]['kad_ident']]['contract_area'], 3, '.', ''),
                        'document_area' => number_format($additional[$results[$i]['kad_ident']]['document_area'], 3, '.', ''),
                    ];
                }
            } else {
                $finalResults[] = [
                    'due_date' => '<b>Общо:</b>',
                    'farming' => '<b>Свободна площ:</b>',
                    'start_date' => number_format($additional[$results[$i]['kad_ident']]['document_area'] - $additional[$results[$i]['kad_ident']]['contract_area'], 3, '.', ''),
                    'contract_area' => number_format($additional[$results[$i]['kad_ident']]['contract_area'], 3, '.', ''),
                    'document_area' => number_format($additional[$results[$i]['kad_ident']]['document_area'], 3, '.', ''),
                ];
            }
        }

        return [
            'total' => count($finalResults),
            'rows' => $finalResults,
        ];
    }
}
