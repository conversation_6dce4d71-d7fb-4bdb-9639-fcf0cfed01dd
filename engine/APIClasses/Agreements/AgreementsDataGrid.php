<?php

namespace TF\Engine\APIClasses\Agreements;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Грид 'Данни от споразумение'.
 *
 * @rpc-module Agreements
 *
 * @rpc-service-id agreements-data-datagrid
 *
 * @property UserDbController $UserDbController
 * @property UsersController $UsersController
 * @property UserDbPlotsController $UserDbPlotsController
 */
class AgreementsDataGrid extends TRpcApiProvider
{
    private $module = 'Agreements';
    private $service_id = 'agreements-data-datagrid';

    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getAgreementsDataGrid']],
            'addAgreementData' => ['method' => [$this, 'addAgreementData'],
                'validators' => [
                    'ekate' => 'validateDigitsOnly',
                    'masiv' => 'validateDigitsOnly',
                    'imot' => 'validateDigitsOnly',
                    'area' => 'validateNumber',
                    'agreement_id' => 'validateInteger',
                ]],
            'deleteAgreementData' => ['method' => [$this, 'deleteAgreementData']],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer agreement_id
     *                         #item string imot
     *                         #item string masiv
     *                         #item string ekate
     *                         }
     * @param int $page pagination parameter
     * @param int $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array {
     *               #item rows array
     *               {
     *               #item integer agreement_id
     *               #item float   area
     *               #item string  ekate
     *               #item integer gid
     *               #item boolean has_match
     *               #item integer id
     *               #item string  imot
     *               #item string  land
     *               #item string  masiv
     *               }
     *               #item integer total
     *               }
     */
    public function getAgreementsDataGrid($rpcParams, $page = null, $rows = null, $sort = null, $order = null)
    {
        if (!$rpcParams['agreement_id'] || !(int)$rpcParams['agreement_id']) {
            return ['rows' => [], 'total' => 0];
        }
        // init all needed controllers
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $options = [
            'tablename' => $UserDbController->DbHandler->tableAgreementsData,
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order . ', id',
            'return' => ['*'],
            'where' => [
                'agreement' => ['column' => 'agreement_id', 'compare' => '=', 'value' => $rpcParams['agreement_id']],
                'imot' => ['column' => 'imot', 'compare' => '=', 'value' => $rpcParams['imot']],
                'masiv' => ['column' => 'masiv', 'compare' => '=', 'value' => $rpcParams['masiv']],
                'ekate' => ['column' => 'ekate', 'compare' => 'IN', 'value' => $rpcParams['ekate']],
            ],
        ];

        $counter = $UserDbController->getItemsByParams($options, true, false);

        if (0 == $counter[0]['count']) {
            return ['rows' => [], 'total' => 0];
        }

        // get all ekate data
        $ekateData = $UsersController->getAllEkatteData();
        $ekateDataCount = count($ekateData);
        for ($i = 0; $i < $ekateDataCount; $i++) {
            $ekateNames[$ekateData[$i]['ekatte_code']] = $ekateData[$i]['ekatte_name'];
        }

        $data = $UserDbController->getItemsByParams($options, false, false);
        $dataCount = count($data);
        for ($i = 0; $i < $dataCount; $i++) {
            $data[$i]['land'] = $ekateNames[$data[$i]['ekate']];

            $data[$i]['area'] = number_format($data[$i]['area'], 3, '.', '');

            if (!$data[$i]['land']) {
                $data[$i]['land'] = '-';
            }
        }

        return [
            'rows' => $data,
            'total' => $counter[0]['count'],
        ];
    }

    /**
     * Добавя нов имот към избраното споразумение.
     *
     * @api-method addAgreementData
     *
     * @param array $rpcParams
     *                         {
     *                         #item string  ekate
     *                         #item string  masiv
     *                         #item string  imot
     *                         #item float   area
     *                         #item integer agreement_id
     *                         }
     *
     * @throws MTRpcException PLOT_ALREADY_IN_AGREEMENT -33210
     *
     * @return bool
     */
    public function addAgreementData($rpcParams)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);

        // checking for KVS item match
        $options = [
            'tablename' => $UserDbController->DbHandler->tableKVS,
            'return' => ['gid'],
            'where' => [
                'ekate' => ['column' => 'ekate', 'compare' => '=', 'prefix' => 'p', 'value' => $rpcParams['ekate']],
                'masiv' => ['column' => 'masiv', 'compare' => '=', 'prefix' => 'p', 'value' => $rpcParams['masiv']],
                'imot' => ['column' => 'number', 'compare' => '=', 'prefix' => 'p', 'value' => $rpcParams['imot']],
            ],
        ];

        $results = $UserDbPlotsController->getPlotData($options, false, false);

        // adding the new agreement data item
        if (null != $results[0]['gid']) {
            // get agreements package data
            $options = [
                'tablename' => $UserDbController->DbHandler->tableAgreements,
                'where' => [
                    'agreement_id' => ['column' => 'id', 'compare' => '=', 'value' => $rpcParams['agreement_id']],
                ],
            ];

            $agreement_results = $UserDbController->getItemsByParams($options);
            $agreementsPackage = $agreement_results[0];
            $contractID = $agreementsPackage['contract_id'];

            // check if the plot is already in the agreement
            $options = [
                'tablename' => $UserDbController->DbHandler->tableAgreementsData,
                'return' => [
                    '*',
                ],
                'where' => [
                    'ekate' => ['column' => 'ekate', 'compare' => '=', 'value' => $rpcParams['ekate']],
                    'masiv' => ['column' => 'masiv', 'compare' => '=', 'value' => $rpcParams['masiv']],
                    'imot' => ['column' => 'imot', 'compare' => '=', 'value' => $rpcParams['imot']],
                    'agreement_id' => ['column' => 'agreement_id', 'compare' => '=', 'value' => $rpcParams['agreement_id']],
                ],
            ];

            $plot_results = $UserDbController->getItemsByParams($options, false, false);

            if ($plot_results[0]) {
                throw new MTRpcException('PLOT_ALREADY_IN_AGREEMENT', -33210);
            }
            // check if the plot is already in the agreement
            $options = [
                'tablename' => $UserDbController->DbHandler->tableAgreementsData,
                'return' => [
                    '*',
                ],
                'where' => [
                    'ekate' => ['column' => 'ekate', 'compare' => '=', 'value' => $rpcParams['ekate']],
                    'masiv' => ['column' => 'masiv', 'compare' => '=', 'value' => $rpcParams['masiv']],
                    'imot' => ['column' => 'imot', 'compare' => '=', 'value' => $rpcParams['imot']],
                    'agreement_id' => ['column' => 'agreement_id', 'compare' => '=', 'value' => $rpcParams['agreement_id']],
                ],
            ];

            $plot_results = $UserDbController->getItemsByParams($options, false, false);

            if ($plot_results[0]) {
                throw new MTRpcException('PLOT_ALREADY_IN_AGREEMENT', -33210);
            }
            // adding the agreement data item
            $options = [
                'tablename' => $UserDbController->DbHandler->tableAgreementsData,
                'mainData' => [
                    'ekate' => $rpcParams['ekate'],
                    'masiv' => $rpcParams['masiv'],
                    'imot' => $rpcParams['imot'],
                    'agreement_id' => $rpcParams['agreement_id'],
                    'has_match' => 1,
                    'gid' => $results[0]['gid'],
                    'area' => $rpcParams['area'],
                ],
            ];

            $id = $UserDbController->DbHandler->addItem($options);
            $return = true;

            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['new_data' => $options], ['created_id' => $id], 'Adding plot to agreement');

            // add plot contract relation
            $options = [
                'tablename' => $UserDbController->DbHandler->contractsPlotsRelTable,
                'mainData' => [
                    'contract_id' => $contractID,
                    'plot_id' => $results[0]['gid'],
                    'contract_area' => $rpcParams['area'],
                    'contract_end_date' => $GLOBALS['Farming']['years'][$agreement_results[0]['year']]['year'] . '-09-30',
                ],
            ];
            $UserDbController->addItem($options);
        } else {
            $return = false;
        }

        return $return;
    }

    /**
     * Изтрива избраният имот от споразумението.
     *
     * @api-method deleteAgreementData
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer agreement_id
     *                         #item integer plot_id
     *                         #item integer agreement_data_id
     *
     *         }
     *
     * @return array
     */
    public function deleteAgreementData($rpcParams)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $options = [
            'tablename' => $UserDbController->DbHandler->tableAgreements,
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $rpcParams['agreement_id']],
            ],
        ];

        $agreement_layer_results = $UserDbController->getItemsByParams($options, false, false);

        if (0 == count($agreement_layer_results)) {
            return [];
        }

        $contract_id = $agreement_layer_results[0]['contract_id'];

        // get contract plot rel data for logging
        $options = [
            'tablename' => $UserDbController->DbHandler->contractsPlotsRelTable,
            'return' => [
                '*',
            ],
            'where' => [
                'plot_id' => ['column' => 'plot_id', 'compare' => '=', 'value' => $rpcParams['plot_id']],
                'contract_id' => ['column' => 'contract_id', 'compare' => '=', 'value' => $contract_id],
            ],
        ];

        $pc_rel_info = $UserDbController->getItemsByParams($options, false, false);

        // get agreement data for logging
        $options = [
            'tablename' => $UserDbController->DbHandler->tableAgreementsData,
            'where' => [
                'plot_id' => ['column' => 'gid', 'compare' => '=', 'value' => $rpcParams['plot_id']],
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $rpcParams['agreement_data_id']],
            ],
        ];

        $agreement_data = $UserDbController->getItemsByParams($options, false, false);

        if (true == $rpcParams['has_math']) {
            $options = [
                'tablename' => $UserDbController->DbHandler->contractsPlotsRelTable,
                'id_name' => 'plot_id',
                'id_string' => $rpcParams['plot_id'],
                'where' => [
                    'contract_id' => ['column' => 'contract_id', 'compare' => '=', 'value' => $contract_id],
                ],
            ];

            $UserDbController->deleteItemsByParams($options);
        }
        unset($options);
        $options = [
            'tablename' => $UserDbController->DbHandler->tableAgreementsData,
            'id_string' => $rpcParams['agreement_data_id'],
        ];

        $UserDbController->deleteItemsByParams($options);

        $UsersController->groupLog(
            $this->User->Name,
            $this->User->UserID,
            $this->User->GroupID,
            $this->module,
            $this->service_id,
            __METHOD__,
            ['request_data' => $rpcParams],
            ['old_data' => [
                'pc_rel_info' => $pc_rel_info[0],
                'agreement_data' => $agreement_data[0],
            ]],
            'Deleting plot from agreement'
        );
    }
}
