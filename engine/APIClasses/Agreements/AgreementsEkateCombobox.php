<?php

namespace TF\Engine\APIClasses\Agreements;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\Users\UsersController;

// Prado::using('Plugins.Core.UserDbPlots.*');
// Prado::using('Plugins.Core.UserDb.*');
// Prado::using('Plugins.Core.Users.*');

/**
 * Връща всички ЕКАТТЕ, които фигурират в избраното споразумение.
 *
 * @rpc-module Agreements
 *
 * @rpc-service-id agreements-ekate-combobox
 */
class AgreementsEkateCombobox extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getAgreementEkateCombobox']],
        ];
    }

    /**
     * Връща всички ЕКАТТЕ, включени в зареденото споразумение.
     *
     * @api-method read
     *
     * @param array $filterObj
     *                         {
     *                         #item integer agreement_id
     *                         #item boolean selected
     *                         }
     *
     * @return array
     */
    public function getAgreementEkateCombobox($filterObj)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $options = [
            'return' => ['DISTINCT(ekate)'],
            'where' => [
                'agreement' => ['column' => 'agreement_id', 'compare' => '=', 'value' => $filterObj['agreement_id']]],
            'tablename' => $UserDbController->DbHandler->tableAgreementsData,
        ];

        $results = $UserDbController->getItemsByParams($options);

        $resultsCount = count($results);
        $return = [];
        for ($i = 0; $i < $resultsCount; $i++) {
            if ('' == $results[$i]['ekate'] || null == $results[$i]['ekate']) {
                continue;
            }

            $return[] = [
                'ekate' => $results[$i]['ekate'],
                'text' => $results[$i]['ekate'] . ' (' . $UsersController->getEkatteName($results[$i]['ekate']) . ')',
            ];
        }

        if (isset($filterObj['selected']) && true == $filterObj['selected']) {
            $return[0]['selected'] = true;
        }

        return $return;
    }
}
