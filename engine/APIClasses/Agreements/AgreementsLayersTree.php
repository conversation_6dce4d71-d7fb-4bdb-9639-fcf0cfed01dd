<?php

namespace TF\Engine\APIClasses\Agreements;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbAgreements\UserDbAgreementsController;
use TF\Engine\Plugins\Core\UserDbContracts\UserDbContractsController;
use TF\Engine\Plugins\Core\Users\UsersController;

// Prado::using('Plugins.Core.Farming.conf');
// Prado::using('Plugins.Core.Farming.*');

/**
 * Дървовидната структура на съществуващите споразумения, разделени по стопанство и година.
 *
 * @rpc-module Agreements
 *
 * @rpc-service-id agreements-layers-tree
 */
class AgreementsLayersTree extends TRpcApiProvider
{
    private $module = 'Agreements';
    private $service_id = 'agreements-layers-tree-tree';

    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getAgreementsLayersTree']],
            'addAgreement' => ['method' => [$this, 'addNewAgreement'],
                'validators' => [
                    'farming' => 'validateInteger',
                    'year' => 'validateInteger',
                    'name' => 'validateText',
                    'agg_type' => 'validateInteger',
                ]],
            'deleteAgreement' => ['method' => [$this, 'deleteAgreement']],
            'recreateAgreement' => ['method' => [$this, 'recreateAgreement']],
            'kmsIntersection' => ['method' => [$this, 'kmsIntersection']],
        ];
    }

    /**
     * Връща съществуващите споразумения.
     *
     * @api-method read
     *
     * @return array
     *               {
     *               #item array children
     *               {
     *               #item string  iconCls
     *               #item integer id
     *               #item integer status
     *               #item string  text
     *               }
     *               #item integer id
     *               #item string  state
     *               #item string  text
     *               }
     */
    public function getAgreementsLayersTree()
    {
        // init all needed controllers
        $UserDbController = new UserDbController($this->User->Database);
        $FarmingController = new FarmingController('Farming');
        $farming_array = $FarmingController->getUserFarmings();
        $farmingIds = array_keys($farming_array);

        // define help arrays and return array
        // hold all found farmings
        $farmings = [];
        // hold all found farmings-years relations
        $farmings_years = [];
        // hold all found years-farmings relations
        $years_layers = [];
        $farmings_keys = [];
        $farmings_years_keys = [];
        $return = [];

        $options = [
            'tablename' => $UserDbController->DbHandler->tableAgreements,
            'sort' => 'farming, year',
            'order' => 'asc',
            'where' => [
                'farming' => ['column' => 'farming', 'compare' => 'IN', 'value' => $farmingIds],
            ],
        ];

        $results = $UserDbController->DbHandler->getItemsByParams($options, false, false);
        $resultsCount = count($results);
        for ($i = 0; $i < $resultsCount; $i++) {
            $farming = $results[$i]['farming'];
            $year = $results[$i]['year'];
            $layer = $results[$i]['id'];
            $layer_name = $results[$i]['name'];

            // check if farming was already added to the main array
            if (!in_array($farming, $farmings)) {
                $farmings[] = $farming;
                $farmings_years[$farming] = [];

                $return[] = [
                    'id' => $farming,
                    'text' => $farming_array[$farming],
                    'state' => 'open',
                    'children' => [],
                ];

                $farmings_keys[$farming] = count($return) - 1;
            }

            // check if year is in farmings-years array
            if (!in_array($year, $farmings_years[$farming])) {
                $farmings_years[$farming][] = $year;
                $years_layers[$year] = [];

                $return[$farmings_keys[$farming]]['children'][] = [
                    'id' => $year,
                    'text' => $GLOBALS['Farming']['years'][$year]['title'],
                    'state' => 'open',
                    'children' => [],
                ];

                $farmings_years_keys[$year] = count($farmings_years[$farming]) - 1;
            }

            if (!in_array($layer, $years_layers[$year])) {
                $years_layers[$year][] = $layer;

                $return[$farmings_keys[$farming]]['children'][$farmings_years_keys[$year]]['children'][] = [
                    'id' => $layer,
                    'text' => $layer_name,
                    'status' => $results[$i]['status'],
                    'iconCls' => 'icon-tree-document',
                ];
            }
        }

        return $return;
    }

    /**
     * Добавя ново споразумение.
     *
     * @api-method addAgreement
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer year
     *                         #item string  name
     *                         #item integer farming
     *                         #item integer agg_type
     *                         }
     *
     * @return int $recordID
     */
    public function addNewAgreement($rpcParams)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $contract_year = $GLOBALS['Farming']['years'][$rpcParams['year']]['year'];
        // options for contract

        $contract_options = [
            'tablename' => $UserDbController->DbHandler->tableContracts,
            'mainData' => [
                'c_num' => 'СП-' . $rpcParams['name'],
                'c_date' => $contract_year . '-10-01',
                'nm_usage_rights' => 4,
                'start_date' => ($contract_year - 1) . '-10-01',
                'due_date' => $contract_year . '-09-30',
                'farming_id' => $rpcParams['farming'],
                'agg_type' => $rpcParams['agg_type'],
            ],
        ];

        $contractID = $UserDbController->addItem($contract_options);

        $agreement_options = [
            'tablename' => $UserDbController->DbHandler->tableAgreements,
            'mainData' => [
                'farming' => $rpcParams['farming'],
                'year' => $rpcParams['year'],
                'name' => $rpcParams['name'],
                'agg_type' => $rpcParams['agg_type'],
                'contract_id' => $contractID,
                'status' => 0,
            ],
        ];

        $recordID = $UserDbController->addItem($agreement_options);

        $UsersController->groupLog(
            $this->User->Name,
            $this->User->UserID,
            $this->User->GroupID,
            $this->module,
            $this->service_id,
            __METHOD__,
            ['new_data' => [
                'contract_options' => $contract_options,
                'agreement_options' => $agreement_options,
            ]],
            ['created_ids' => [
                'contract_id' => $contractID,
                'agreement_id' => $recordID,
            ]],
            'Editing hypothec'
        );

        return $recordID;
    }

    /**
     * Изтрива избраното споразумение.
     *
     * @api-method deleteAgreement
     *
     * @param int $rpcParam
     *
     * @return array
     */
    public function deleteAgreement($rpcParam)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbContractsController = new UserDbContractsController($this->User->Database);
        $UsersController = new UsersController('Users');

        $options = [
            'tablename' => $UserDbController->DbHandler->tableAgreements,
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $rpcParam],
            ],
        ];

        $agreement_results = $UserDbController->getItemsByParams($options, false, false);

        if (0 == count($agreement_results)) {
            return [];
        }

        $contractID = $agreement_results[0]['contract_id'];
        $UserDbController->disableRentaMatViewTriggers();
        // get agreement data for logging
        $options = [
            'tablename' => $UserDbController->DbHandler->tableContracts,
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $contractID],
            ],
        ];

        $contract_data = $UserDbController->getItemsByParams($options, false, false);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableContracts,
            'id_string' => $contractID,
        ];

        $UserDbContractsController->deleteItemsByParams($options);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableAgreements,
            'id_string' => $rpcParam,
        ];

        $UserDbController->deleteItemsByParams($options);
        $UserDbController->enableRentaMatViewTriggers();
        $UserDbController->refreshRentaViews();

        $UsersController->groupLog(
            $this->User->Name,
            $this->User->UserID,
            $this->User->GroupID,
            $this->module,
            $this->service_id,
            __METHOD__,
            ['deleting_agreement_id' => $rpcParam],
            ['old_data' => [
                'contract_data' => $contract_data[0],
                'agreement_data' => $agreement_results[0],
            ]],
            'Deleting agreement'
        );
    }

    /**
     * Добавяне на имоти към споразумение, чрез пресичане със слой "Данни от комасация".
     *
     * @param int $agreementId - ID на избраното споразумение, към което да се добавят данни от слой "Комасация"
     *
     * @throws MTRpcException -33152 NON_EXISTING_CONTRACT_OR_PLOT_NUMBER - aко не е намерено споразумение с това ID
     * @throws MTRpcException -33102 DATABASE_INVALID_TABLE_NAME - ако не съществува таблица за комасация за тази годината и стопанството от споразумението
     * @throws MTRpcException -33061 EMPTY_LAYER_TABLE - ако таблицата съществува, но е празна
     *
     * @return int 200 - статус код 200, ако операцията е била успешна
     */
    public function kmsIntersection($agreementId)
    {
        // Initialize the agreements controller
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbAgreementsController = new UserDbAgreementsController($this->User->Database);
        $LayersController = new LayersController('Layers');
        $UsersController = new UsersController('Users');

        /*
         *  Section: Check all required conditions        *
         *  Operations performed:                         *
         *      - Check if agreements exists              *
         *      - Check if KMS table exists               *
         *      - Check if KMS table is empty             *
         *        - Perform the main intersection         *
         */
        // Create the options array for the first array query
        // the query aims to get all the data for the existing query such as:
        // farming id, farming year, name and agreement type.
        $options = [
            'tablename' => $UserDbController->DbHandler->tableAgreements,
            'return' => [
                '*',
            ],
            'where' => [
                'agreement_id' => ['column' => 'id', 'compare' => '=', 'value' => $agreementId],
            ],
        ];
        // run the query
        $originalAgreement = $UserDbController->getItemsByParams($options, false, false);

        // Check if there isn't agreement existing with the selected id
        if (0 == count($originalAgreement)) {
            throw new MTRpcException('NON_EXISTING_CONTRACT_OR_PLOT_NUMBER', -33152);
        }

        // Create the table options query to retrieve the table name
        // for the KMS table with the loaded data for the selected
        // farming and farming year
        $tableQueryOptions = [
            'farming' => $originalAgreement[0]['farming'],
            'year' => $originalAgreement[0]['year'],
            'group_id' => $this->User->GroupID,
            'layer_type' => 4,
        ];
        // run the query
        $kmsTableName = $LayersController->getTableNameByParams($tableQueryOptions);

        // Throw an exception if there is no KMS table for the selected farming and year
        if (!$kmsTableName) {
            throw new MTRpcException('DATABASE_INVALID_TABLE_NAME', -33102);
        }

        // Throw an exception if the kms table is not physically created.
        $isKmsTableExists = $UserDbController->getTableNameExist($kmsTableName);
        if (!$isKmsTableExists) {
            throw new MTRpcException('DATABASE_INVALID_TABLE_NAME', -33102);
        }

        // Check if the selected table is populated with some data
        $options = [
            'tablename' => $kmsTableName,
            'return' => [
                '*',
            ],
        ];
        // run the query
        $kmsTableResults = $UserDbController->getItemsByParams($options, true, false);

        if (0 == $kmsTableResults[0]['count']) {
            throw new MTRpcException('EMPTY_LAYER_TABLE', -33061);
        }

        // This is the main operation. This method uses the retrieved table.
        // Then the query intersects all the geometries from the layer_kvs table
        // with the geometries of all plots in the selected KMS table
        // where the intersection area is higher than 0.001 da and then retrieves the
        // required parameters to be used for creating the plot - contract relations
        $intersectionResults = $UserDbAgreementsController->getKMSInterceptionForAgreement($kmsTableName);

        if (0 == count($intersectionResults)) {
            throw new MTRpcException('NO_KMS_INTERSECTIONS', -33211);
        }

        /*
         *    Section: Recreate the agreement             *
         *    Operations performed:                       *
         *        - Delete the old agreement              *
         *        - Create new agreement with old data    *
         */

        // After the necessary checks if there agreement exists,
        // if KMS table exists and it is not empty
        // the existing agreement is deleted
        // this step is necessary to remove all the data and contract connections
        $this->deleteAgreement($agreementId);

        // When the old agreement is deleted new agreement is created
        // with the same parameters - name, farming and farming year,
        // so the user does not encounter inconvenience from the operations
        $newAgreementId = $this->addNewAgreement($originalAgreement[0]);

        /*
         *    Section: Start the insertion operations     *
         *    Operations performed:                       *
         *        - Get the new agreement contract ID     *
         *        - Loop through the results to create    *
         *          the insertion queries                 *
         */

        // Run additional query to retrieve the contract ID that is matched
        // to the newly created agreement. This contract ID will be used later
        // to create the new relations between the contract and the resulting plots
        $options = [
            'tablename' => $UserDbController->DbHandler->tableAgreements,
            'return' => [
                '*',
            ],
            'where' => [
                'agreement_id' => ['column' => 'id', 'compare' => '=', 'value' => $newAgreementId],
            ],
        ];
        // run the query
        $newAgreementData = $UserDbController->getItemsByParams($options, false, false);

        // Assign the result to new variable, that is easier to use.
        $contractID = $newAgreementData[0]['contract_id'];
        $farmingYear = $newAgreementData[0]['year'];

        $contract_year = $GLOBALS['Farming']['years'][$farmingYear]['year'];
        $contractDueDate = $contract_year . '-09-30';

        // This query is used to insert all the retrieved plots from the main operation ($intersectionResults)
        // into the su_agreements_data table with one single query, to prevent multiple database calls.
        // The rest of the query will be added in a later stage by looping through all results
        $plotAgreementRelInsertQuery = 'INSERT INTO su_agreements_data (masiv, imot, ekate, has_match, agreement_id, gid, area ) VALUES ';

        // The same logic is used for the plot-contract relations. All the insertions will be made
        // with single database call instead of multiple calls for each plot.
        // The rest of the query will be added in a later stage by looping through all results
        $plotsContractsRelInsertQuery = 'INSERT INTO su_contracts_plots_rel (contract_id, plot_id, contract_area, price_per_acre, price_sum, annex_action, contract_end_date) VALUES ';

        // This is the main result loop that is used to complete the two insertion queries.
        // Each iteration appends result string for each plot, that is returned from the
        // main operation. And in addition a comma (,) is appended if there are more results
        $intersectionResultsCount = count($intersectionResults);
        for ($i = 0; $i < $intersectionResultsCount; $i++) {
            $plotAgreementRelInsertQuery .= $this->createAgreementPlotQuery($intersectionResults[$i], $newAgreementId);

            $plotsContractsRelInsertQuery .= $this->createContractPlotQuery($intersectionResults[$i], $contractID, $contractDueDate);

            if ($i != $intersectionResultsCount - 1) {
                $plotAgreementRelInsertQuery .= ', '; // do not insert ', ' after the last row
                $plotsContractsRelInsertQuery .= ', '; // do not insert ', ' after the last row
            }
        }

        // Both operations perform the pre-build insertion queries
        $UserDbController->DbHandler->getDataByQuery($plotAgreementRelInsertQuery);
        $UserDbController->DbHandler->getDataByQuery($plotsContractsRelInsertQuery);

        $UsersController->groupLog(
            $this->User->Name,
            $this->User->UserID,
            $this->User->GroupID,
            $this->module,
            $this->service_id,
            __METHOD__,
            ['add_kms_intersection' => [
                'new_agreement_data' => $newAgreementData,
                'contract_id' => $contractID,
                'kms_table_name' => $kmsTableName]],
            ['new_data' => $options['mainData']],
            'Adding KMS Intersection'
        );
        // If everything goes according to plan the response is status code OK:200
        return 200;
    }

    /**
     * Пресъздава споразумението, ако към избраното споразумение има добавени данни.
     *
     * @param int $agreementId - ID на избраното споразумение, към което да се добавят данни от слой "Комасация"
     *
     * @throws MTRpcException -33152 NON_EXISTING_CONTRACT_OR_PLOT_NUMBER - aко не е намерено споразумение с това ID
     * @throws MTRpcException -33061 EMPTY_LAYER_TABLE - ако таблицата съществува, но е празна
     *
     * @return int 200 - статус код 200, ако операцията е била успешна
     */
    public function recreateAgreement($agreementId)
    {
        // Initialize the agreements controller
        $UserDbController = new UserDbController($this->User->Database);

        // Create the options array for the first array query
        // the query aims to get all the data for the existing query such as:
        // farming id, farming year, name and agreement type.
        $options = [
            'tablename' => $UserDbController->DbHandler->tableAgreements,
            'return' => [
                '*',
            ],
            'where' => [
                'agreement_id' => ['column' => 'id', 'compare' => '=', 'value' => $agreementId],
            ],
        ];
        // run the query
        $originalAgreement = $UserDbController->getItemsByParams($options, false, false);

        // Check if there isn't agreement existing with the selected id
        if (0 == count($originalAgreement)) {
            throw new MTRpcException('NON_EXISTING_CONTRACT_OR_PLOT_NUMBER', -33152);
        }

        // Check if there is any data, related to the selected agreement
        $options = [
            'tablename' => $UserDbController->DbHandler->tableAgreementsData,
            'return' => [
                '*',
            ],
            'where' => [
                'agreement_id' => ['column' => 'agreement_id', 'compare' => '=', 'value' => $agreementId],
            ],
        ];
        // run the query
        $agreementsResults = $UserDbController->getItemsByParams($options, true, false);

        if (0 == $agreementsResults[0]['count']) {
            throw new MTRpcException('EMPTY_LAYER_TABLE', -33061);
        }

        // After the necessary checks if there agreement exists,
        // and if there is data, related to the selected agreement,
        // the existing agreement is deleted
        // this step is necessary to remove all the data and contract connections
        $this->deleteAgreement($agreementId);

        // When the old agreement is deleted new agreement is created
        // with the same parameters - name, farming and farming year,
        // so the user does not encounter inconvenience from the operations
        return $this->addNewAgreement($originalAgreement[0]);
    }

    /**
     * Helper method to include the necessary data for the plot-agreement insertion data.
     *
     * @return string
     */
    private function createAgreementPlotQuery($row, $newAgreementId)
    {
        return '('
            . $row['masiv'] . ', '
            . $row['number'] . ", '"
            . $row['ekate'] . "', "
            . 'TRUE, '
            . $newAgreementId . ', '
            . $row['gid'] . ', '
            . $row['intersection_area']
            . ')';
    }

    /**
     * Helper method to include the necessary data for the contract-plot insertion data.
     *
     * @return string
     */
    private function createContractPlotQuery($row, $contractID, $contractDueDate)
    {
        return '(' . $contractID . ', ' . $row['gid'] . ', ' . $row['intersection_area'] . ", null, null, 'added', '" . $contractDueDate . "')";
    }
}
