<?php

namespace TF\Engine\APIClasses\Agreements;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

// Prado::using('Plugins.Core.Farming.conf');
// Prado::using('Plugins.Core.Farming.*');
// Prado::using('Plugins.Core.Contracts.conf');

/**
 * Статус на текуща обработка на заредени споразумения.
 *
 * @rpc-module Agreements
 *
 * @rpc-service-id agreements-datagrid
 */
class AgreementsGrid extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getAgreementsGrid']],
        ];
    }

    /**
     * @api-method read
     *
     * @param int $page pagination parameter
     * @param int $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array
     *               {
     *               #item array rows
     *               {
     *               #item string  farming_name
     *               #item string  status
     *               #item integer status_id
     *               #item string  agreement_type
     *               }
     *               #item integer total
     *               }
     */
    public function getAgreementsGrid(?int $page = null, ?int $rows = null, $sort = null, $order = null)
    {
        if ($this->User->isGuest || !$this->User->Database) {
            return [];
        }

        // init all needed controllers
        $UserDbController = new UserDbController($this->User->Database);
        $FarmingController = new FarmingController('Farming');
        $farmings = $FarmingController->getUserFarmings();
        $farmingIds = array_keys($farmings);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableAgreements,
            'offset' => ($page && $rows) ? ($page - 1) * $rows : null,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
            'return' => ['*'],
            'where' => [
                'farming' => ['column' => 'farming', 'compare' => 'IN', 'value' => $farmingIds],
            ],
        ];

        $data = $UserDbController->getItemsByParams($options, false);
        $counter = $UserDbController->getItemsByParams($options, true);

        // converting data into user friendly format
        $dataCount = count($data);
        for ($i = 0; $i < $dataCount; $i++) {
            $data[$i]['farming_name'] = $farmings[$data[$i]['farming']] . ' / ' . $GLOBALS['Farming']['years'][$data[$i]['year']]['title'];
            $data[$i]['status_id'] = $data[$i]['status'];
            $data[$i]['agreement_type'] = $GLOBALS['Contracts']['agg_types'][$data[$i]['agg_type']]['name'];

            if (0 == (int)$data[$i]['status']) {
                $data[$i]['status'] = 'Изчаква добавяне на данни от файл';
            } elseif (1 == (int)$data[$i]['status']) {
                $data[$i]['status'] = 'Успешно обработен';
            } elseif (2 == (int)$data[$i]['status']) {
                $data[$i]['status'] = 'Грешка при обработка';
            } elseif (3 == (int)$data[$i]['status']) {
                $data[$i]['status'] = 'Обработва се';
            }
        }

        $return['rows'] = $data;
        $return['total'] = $counter[0]['count'];

        return $return;
    }
}
