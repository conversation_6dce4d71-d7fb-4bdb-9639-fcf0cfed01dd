<?php

namespace TF\Engine\APIClasses\Agreements;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbAgreements\UserDbAgreementsController;

// Prado::using('Plugins.Core.Farming.conf');
// Prado::using('Plugins.Core.Farming.*');
// Prado::using('Plugins.Core.Contracts.conf');

/**
 * Работа с карта в подмодул "Споразумения".
 *
 * @rpc-module Agreements
 *
 * @rpc-service-id agreements-map
 */
class AgreementsMap extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'initMap' => ['method' => [$this, 'initMap']],
            'initKvs' => ['method' => [$this, 'initKvsMap']],
        ];
    }

    /**
     * @api-method initMap
     *
     * @return array
     *               {
     *               #item array
     *               {
     *               #item string  color
     *               #item string  extent
     *               #item integer id
     *               #item integer layer_type
     *               #item string  name
     *               }
     *               #item array
     *               {
     *               #item string  extent
     *               #item integer id
     *               #item string  name
     *               }
     *               }
     */
    public function initMap()
    {
        $LayersController = new LayersController('Layers');
        $options = [
            'return' => ['t.id', 't.table_name as name', 't.extent', 't.layer_type', 't.color'],
            'sort' => 'id',
            'order' => 'ASC',
            'where' => [
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'prefix' => 't', 'value' => $this->User->GroupID],
                'layer_type' => ['column' => 'layer_type', 'compare' => '=', 'prefix' => 't', 'value' => 5],
            ],
        ];

        $result = $LayersController->getLayers($options);
        $LayersKvs = [];
        $LayersKvs[0] = $result[0];
        $LayersKvs[0]['extent'] = str_replace(' ', ', ', $LayersKvs[0]['extent']);

        $LayersKvs[1]['extent'] = $LayersKvs[0]['extent'];
        $LayersKvs[1]['name'] = 'topic_kvs_layer';
        $LayersKvs[1]['id'] = 1;

        return $LayersKvs;
    }

    /**
     * @api-method initKvs
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer agreement_id
     *                         #item integer farming
     *                         #item integer year
     *                         }
     *
     * @return array
     *               {
     *               #item array colorarray
     *               {
     *               #item string color
     *               #item string iconCls
     *               #item string name
     *               }
     *               #item string extent
     *               }
     */
    public function initKvsMap($rpcParams = null)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbAgreementsController = new UserDbAgreementsController($this->User->Database);
        $LayersController = new LayersController('Layers');

        if ('' != $rpcParams) {
            $agreementid = $rpcParams['agreement_id'];
        }

        $sqlString = $UserDbController->getAgreementsMapQuery($agreementid);

        $maxextent = $UserDbAgreementsController->getAgreementsMapExtent($agreementid);

        $query = "({$sqlString}) as subquery using unique gid";

        $options = [
            'return' => ['t.id', 't.extent'],
            'sort' => 'id',
            'order' => 'DESC',
            'where' => [
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'prefix' => 't', 'value' => $this->User->GroupID],
                'layer_type' => ['column' => 'layer_type', 'compare' => '=', 'prefix' => 't', 'value' => 5],
            ],
        ];

        $result = $LayersController->getLayers($options);

        $data = [];

        $color1 = '000000';
        $color2 = 'ff0000';
        $data['layername'] = 'topic_kvs_layer';
        $data['maxextent'] = $result[0]['extent'];
        $data['host'] = DEFAULT_DB_HOST;
        $data['dbname'] = $this->User->Database;
        $data['username'] = DEFAULT_DB_USERNAME;
        $data['password'] = DEFAULT_DB_PASSWORD;
        $data['port'] = DEFAULT_DB_PORT;
        $data['query'] = $query;
        $data['gid'] = 'gid';
        $data['transparency'] = '60';
        $data['classes'][0]['name'] = 'topic_kvs_layer';
        $data['classes'][0]['border_color'] = hexdec(substr($color1, 0, 2)) . ' ' . hexdec(substr($color1, 2, 2)) . ' ' . hexdec(substr($color1, 4, 2));
        $data['classes'][0]['color'] = hexdec(substr($color2, 0, 2)) . ' ' . hexdec(substr($color2, 2, 2)) . ' ' . hexdec(substr($color2, 4, 2));
        $hexColorArray[] = ['color' => $color2, 'name' => 'Споразумения', 'iconCls' => 'no-background no-padding'];
        $ltext = $LayersController->StringHelper->loadTemplate($GLOBALS['Templates'][2]['template'], $data);

        @unlink(WMS_MAP_PATH . $this->User->GroupID . '_layer_kvs.map');
        $fp = fopen(WMS_MAP_PATH . $this->User->GroupID . '_layer_kvs.map', 'w');
        fwrite($fp, $ltext);
        fclose($fp);

        $maxextent = $maxextent[0]['extent'];
        $maxextent = str_replace('BOX(', '', $maxextent);
        $maxextent = str_replace(')', '', $maxextent);
        $maxextent = str_replace(' ', ',', $maxextent);

        $returnData['colorarray'] = $hexColorArray;
        $returnData['extent'] = $maxextent;

        return $returnData;
    }
}
