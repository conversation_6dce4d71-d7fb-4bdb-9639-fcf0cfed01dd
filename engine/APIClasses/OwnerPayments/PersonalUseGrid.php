<?php

namespace TF\Engine\APIClasses\OwnerPayments;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDbPayments\UserDbPaymentsController;

/**
 * Personal Use Grid Class.
 *
 * @rpc-module OwnerPayments
 *
 * @rpc-service-id owner-personal-use-grid
 */
class PersonalUseGrid extends TRpcApiProvider
{
    public $iterator = 0;
    private $renta_types;
    private $module = 'OwnerPayments';
    private $service_id = 'owner-personal-use-grid';

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readPersonalUseGrid'],
                'validators' => [
                    'filterParam' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * Read personal use grid.
     *
     * @api-method read
     *
     * @param array $filterParam
     *                           {
     *                           #item int owner_id       -The owner id
     *                           #item string year        -The year
     *                           }
     * @param int $page -The current page number
     * @param int $rows -Rows per page
     * @param string $sort -A grid column by which the grid is sorted
     * @param string $order -The sort order ASC/DESC
     *
     * @return array result
     *               {
     *               #item array rows         -The results
     *               #item string total       -The count of all results
     *               }
     */
    public function readPersonalUseGrid(array $filterParam, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        // creating the default return
        $default = [
            'rows' => [],
            'total' => 0,
        ];

        if (!$filterParam['owner_id'] || !(int)$filterParam['owner_id']) {
            return $default;
        }

        if (!(int)$filterParam['year']) {
            return $default;
        }

        $owner_id = $filterParam['owner_id'];
        $year = $filterParam['year'];

        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);

        $personalUse = [];
        $personalUseOptions = [
            'chosen_years' => $year,
            'owner_ids' => [$owner_id],
        ];

        $personalUseByRentaTypes = $UserDbPaymentsController->getPersonalUseForOwners($personalUseOptions, false, false);

        $contractOwnerKeys = [];
        foreach ($personalUseByRentaTypes as $rentaTypeData) {
            $contractOwnerKey = $rentaTypeData['owner_id'] . '_' . $rentaTypeData['contract_id'];

            if (in_array($contractOwnerKey, $contractOwnerKeys)) {
                continue;
            }
            $personalUse[] = $rentaTypeData;

            $contractOwnerKeys[] = $contractOwnerKey;
        }

        return [
            'rows' => $personalUse,
            'total' => count($personalUse),
        ];
    }
}
