<?php

namespace TF\Engine\APIClasses\OwnerPayments;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDbOwners\UserDbOwnersController;

include_once __DIR__ . '/../../Plugins/Core/Farming/conf.php';
include_once __DIR__ . '/../../Plugins/Core/Owners/conf.php';

/**
 * Owners Tree.
 *
 * @rpc-module OwnerPayments
 *
 * @rpc-service-id owners-contracts-tree
 *
 * @property UserDbOwnersController $UserDbOwnersController
 * @property FarmingController $FarmingController
 */
class OwnersTree extends TRpcApiProvider
{
    public $UserDbOwnersController = false;
    public $FarmingController = false;

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getOwners'],
                'validators' => [
                    'filterParam' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * Returns array of living people, participating in any contracts for the given year.
     *
     * @api-method read
     *
     * @param array $filterParams
     *                            {
     *                            #item string year
     *                            #item int page_number
     *                            #item string owner_name
     *                            #item string owner_egn
     *                            #item string company_name
     *                            #item string company_eik
     *                            #item string cnum
     *                            #item array contract_type
     *                            #item array farming
     *                            #item array owner_ids
     *                            #item boolean with_renta_nat
     *                            }
     * @param int|string $page -The current page number
     * @param int|string $rows -Rows per page
     * @param string $sort -A grid column by which the grid is sorted
     * @param string $order -The sort order ASC/DESC
     *
     * @return array {
     *               #item string iconCls
     *               #item integer id
     *               #item string text
     *               #attributes array {
     *               #item string address
     *               #item string company_address
     *               #item string company_name
     *               #item string egn
     *               #item string eik
     *               #item string email
     *               #item string fax
     *               #item string iban
     *               #item string iconCls
     *               #item integer id
     *               #item string is_dead_text
     *               #item string lk_izdavane
     *               #item string lk_nomer
     *               #item string mobile
     *               #item string mol
     *               #item string owner_names
     *               #item integer owner_type
     *               #item string owner_type_name
     *               #item string phone
     *               #item string text
     *               #item integer true_id
     *               }
     *               }
     */
    public function getOwners(array $filterParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = ''): iterable
    {
        if ($this->User->isGuest) {
            return [];
        }

        $FarmingController = new FarmingController('Farming');

        $arrayHelper = $FarmingController->ArrayHelper;
        if (null == $filterParams['year']) {
            $filterParams['year'] = $FarmingController->getCurrentFarmingYearID();
        }

        $farmYearStart = ($GLOBALS['Farming']['years'][$filterParams['year']]['year'] - 1) . '-10-01';
        $farmYearEnd = $GLOBALS['Farming']['years'][$filterParams['year']]['year'] . '-09-30';

        $userFarmings = $FarmingController->getUserFarmings();
        $userFarmingIds = array_keys($userFarmings);
        $farmingIds = $arrayHelper->filterEmptyStringArr($filterParams['farming']);
        $farmingIds = isset($farmingIds) && count($farmingIds) > 0
            ? array_intersect($farmingIds, $userFarmingIds)
            : $userFarmingIds;

        $options = [
            'return' => [
                'distinct on (true_id) o.id as true_id',
                'o.id as id',
                'o.address',
                'o.company_address',
                'o.company_name',
                'o.egn',
                'o.eik',
                'o.email',
                'o.fax',
                'o.iban',
                'o.owner_type',
                'o.lk_nomer',
                'o.lk_izdavane',
                'o.mobile',
                'o.phone',
                'o.remark',
                "o.name || ' ' || o.surname || ' ' || o.lastname as owner_names",
                UserDbOwnersController::isDead('o', [$farmYearStart, $farmYearEnd]),
                UserDbOwnersController::allowOwnerPayment('o', [$farmYearStart, $farmYearEnd]),
                UserDbOwnersController::isDeadDateInCurrentFarmYear('o', [$farmYearStart, $farmYearEnd]),
                'o.dead_date',
                'o.post_payment_fields::text',
                'h.path',
                "o_r.rep_name || ' ' || o_r.rep_surname || ' ' || o_r.rep_lastname as rep_names",
                'o_r.rep_egn',
                'o_r.iban as rep_iban',
                'o_r.rep_address',
                'o_r.rep_lk',
                'o_r.rep_lk_izdavane',
            ],
            'where' => [
                'is_sublease' => [
                    'column' => 'is_sublease',
                    'compare' => '=',
                    'prefix' => 'c',
                    'value' => 'FALSE',
                ],
                'is_annex' => ['column' => 'is_annex', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
                'annex_action' => [
                    'column' => 'annex_action',
                    'compare' => '=',
                    'prefix' => 'pc',
                    'value' => 'added',
                ],
                'active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'TRUE'],
                'c_type' => [
                    'column' => 'nm_usage_rights',
                    'compare' => 'IN',
                    'prefix' => 'c',
                    'value' => [2, 3, 5],
                ],
                'id' => [
                    'column' => 'o.id',
                    'compare' => 'IS NOT',
                    'value' => 'NULL',
                ],
                // filters
                'egn' => [
                    'column' => '(o.egn)',
                    'compare' => 'ILIKE',
                    'value' => $filterParams['owner_egn'],
                ],
                'company_name' => [
                    'column' => '(o.company_name)',
                    'compare' => 'ILIKE',
                    'value' => $filterParams['company_name'],
                ],
                'eik' => [
                    'column' => '(o.eik)',
                    'compare' => 'ILIKE',
                    'value' => $filterParams['company_eik'],
                ],
                'rep_egn' => [
                    'column' => 'o_r.rep_egn',
                    'compare' => 'ILIKE',
                    'value' => $filterParams['rep_egn'],
                ],
                'owner_note' => [
                    'column' => 'remark',
                    'prefix' => 'o',
                    'compare' => 'ILIKE',
                    'value' => $filterParams['owner_note'],
                ],
                // contract filters
                'cnum' => [
                    'column' => 'c_num',
                    'compare' => 'ILIKE',
                    'prefix' => 'c',
                    'value' => $filterParams['cnum'],
                ],
                'contract_type' => [
                    'column' => 'nm_usage_rights',
                    'compare' => 'IN',
                    'prefix' => 'c',
                    'value' => $arrayHelper->filterEmptyStringArr($filterParams['contract_type']),
                ],
                'farming' => [
                    'column' => 'farming_id',
                    'compare' => 'IN',
                    'prefix' => 'c',
                    'value' => $farmingIds,
                ],
                'group' => [
                    'column' => 'CASE 
                                    WHEN a.id IS NOT NULL THEN a.group
                                    ELSE c.group
                                END',
                    'compare' => 'IN',
                    'value' => $arrayHelper->filterEmptyStringArr($filterParams['contract_group']),
                ],
                'percent' => ['column' => 'percent', 'compare' => '>', 'prefix' => 'po', 'value' => '0'],
            ],
            'start_date' => $GLOBALS['Farming']['years'][$filterParams['year']]['year'] . '-09-30',
            'due_date' => ($GLOBALS['Farming']['years'][$filterParams['year']]['year'] - 1) . '-10-01',
        ];

        // from information
        if (isset($filterParams['owner_ids'])) {
            $filterParams['owner_ids'] = array_diff($filterParams['owner_ids'], ['']);
        }

        if ($filterParams['person_name']) {
            $tmp_person_names = preg_replace('/\s+/', '.*', $filterParams['person_name']);
            $tmp_person_names = mb_strtolower($tmp_person_names, 'UTF-8');

            $options['whereOr']['owner_name'] = [
                'column' => "lower(trim(o.name)) || ' ' || lower(trim(o.surname)) || ' ' || lower(trim(o.lastname))",
                'compare' => '~',
                'value' => $tmp_person_names,
            ];

            $options['whereOr']['rep_name'] = [
                'column' => "lower(TRIM (o_r.rep_name)) || ' ' || lower(TRIM (o_r.rep_surname)) || ' ' || lower(TRIM (o_r.rep_lastname))",
                'compare' => '~',
                'value' => $tmp_person_names,
            ];
        }

        if ($filterParams['person_egn']) {
            $options['whereOr']['owner_egn'] = ['column' => 'egn', 'compare' => 'ILIKE', 'prefix' => 'o', 'value' => $filterParams['person_egn']];
            $options['whereOr']['rep_egn'] = ['column' => 'rep_egn', 'compare' => 'ILIKE', 'prefix' => 'o_r', 'value' => $filterParams['person_egn']];
        }

        if (isset($filterParams['owner_phone']) && '' != $filterParams['owner_phone']) {
            $options['whereOr']['phone'] = ['column' => 'phone', 'compare' => 'ILIKE', 'prefix' => 'o', 'value' => $filterParams['owner_phone']];
            $options['whereOr']['mobile'] = ['column' => 'mobile', 'compare' => 'ILIKE', 'prefix' => 'o', 'value' => $filterParams['owner_phone']];
        }

        if (isset($filterParams['owner_name']) && '' != $filterParams['owner_name']) {
            $regexCounter = true;
            $tmp_owner_names = preg_replace('/\s+/', '.*', $filterParams['owner_name']);
            $tmp_owner_names = mb_strtolower($tmp_owner_names, 'UTF-8');

            $tmp_owner_names_string = "regexp_matches(
                    lower(trim(o.name)) || ' ' || lower(trim(o.surname)) || ' ' || lower(trim(o.lastname))
                    ,'{$tmp_owner_names}','g')";
            array_push($options['return'], $tmp_owner_names_string);
        }

        if (isset($filterParams['rep_name']) && '' != $filterParams['rep_name']) {
            $regexCounter = true;
            $tmp_rep_names = preg_replace('/\s+/', '.*', $filterParams['rep_name']);
            $tmp_rep_names = mb_strtolower($tmp_rep_names, 'UTF-8');
            $tmp_rep_names_string = "regexp_matches(lower(TRIM (o_r.rep_name)) || ' ' || lower(TRIM (o_r.rep_surname)) || ' ' || lower(TRIM (o_r.rep_lastname)),'{$tmp_rep_names}','g')";
            array_push($options['return'], $tmp_rep_names_string);
        }

        if (isset($filterParams['heirs_name']) && '' != $filterParams['heirs_name']) {
            $regexCounter = true;
            $tmp_heirs_names = trim(preg_replace('/\\s+/', ' ', $filterParams['heirs_name']));
            $tmp_heirs_names = mb_strtolower($tmp_heirs_names, 'UTF-8');
            $tmp_heirs_names_string = "regexp_matches(lower(trim(o.name)) || ' ' || lower(trim(o.surname)) || ' ' || lower(trim(o.lastname)),'{$tmp_heirs_names}','g')";
            array_push($options['return'], $tmp_heirs_names_string);
            $options['where']['is_heritor']['value'] = 'TRUE';
            if (!in_array('po.is_heritor', $options['return'])) {
                $options['return'][] = 'po.is_heritor';
            }
        }

        if (isset($filterParams['heirs_egn']) && !empty($filterParams['heirs_egn'])) {
            $options['where']['egn'] = ['column' => 'egn', 'compare' => 'ILIKE', 'prefix' => 'o', 'value' => $filterParams['heirs_egn']];
            $options['where']['is_heritor']['value'] = 'TRUE';
            if (!in_array('po.is_heritor', $options['return'])) {
                $options['return'][] = 'po.is_heritor';
            }
        }

        // adding with renta nat filter
        if (isset($filterParams['with_renta_nat']) && 1 == $filterParams['with_renta_nat']) {
            $options['where']['with_renta_nat'] = [
                'column' => 'renta_id',
                'prefix' => 'c_r',
                'compare' => '>',
                'value' => '0',
            ];
        }

        // adding without renta nat filter
        if (isset($filterParams['with_renta_nat']) && 0 == $filterParams['with_renta_nat']) {
            $options['where']['with_renta_nat'] = [
                'column' => 'renta_id',
                'prefix' => 'c_r',
                'compare' => 'IS',
                'value' => 'NULL',
            ];
        }
        // TS-3814 adding filter for owners with or without bank accounts
        if (isset($filterParams['with_bank_acc']) && true == $filterParams['with_bank_acc']) {
            $options['where']['iban'] = [
                'column' => 'length(o.iban)',
                'compare' => '>',
                'value' => '1',
            ];
        }

        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);

        if ($regexCounter) {
            $count = $UserDbOwnersController->getOwnersPayments($options, false, false);
            $counter = [];
            $counter[0]['count'] = count($count);
        } else {
            $counter = $UserDbOwnersController->getOwnersPayments($options, true);
        }
        if (0 == $counter[0]['count']) {
            return [];
        }
        $options['offset'] = ($page - 1) * $rows;
        $options['limit'] = $rows;

        $results = $UserDbOwnersController->getOwnersPayments($options, false, false);
        $result_count = count($results);

        if (!$result_count) {
            return [];
        }

        for ($i = 0; $i < $result_count; $i++) {
            if (true == $results[$i]['is_dead'] && false == $results[$i]['allow_owner_payment']) {
                continue;
            }

            if (false == $results[$i]['is_dead'] && null == $results[$i]['dead_date']) {
                $parentIds = explode('.', $results[$i]['path'], -1);

                $skipRecord = false;
                foreach ($parentIds as $parentid) {
                    $parent = array_filter($results, function ($v, $k) use ($parentid) {
                        return $v['id'] == $parentid;
                    }, ARRAY_FILTER_USE_BOTH);
                    $parent = current($parent);

                    if ($parent && $parent['dead_date'] > $farmYearEnd) {
                        $skipRecord = true;

                        break;
                    }
                }

                if (true == $skipRecord) {
                    continue;
                }
            }

            $results[$i]['owner_type_name'] = $GLOBALS['Owners']['Types'][$results[$i]['owner_type']]['type_name'];

            if (!$results[$i]['lk_nomer']) {
                $results[$i]['lk_nomer'] = '-';
            }
            if (!$results[$i]['lk_izdavane']) {
                $results[$i]['lk_izdavane'] = '-';
            }
            if (!$results[$i]['mol']) {
                $results[$i]['mol'] = '-';
            }
            if (!$results[$i]['company_address']) {
                $results[$i]['company_address'] = '-';
            }
            if (!$results[$i]['phone']) {
                $results[$i]['phone'] = '-';
            }
            if (!$results[$i]['fax']) {
                $results[$i]['fax'] = '-';
            }
            if (!$results[$i]['mobile']) {
                $results[$i]['mobile'] = '-';
            }
            if (!$results[$i]['email']) {
                $results[$i]['email'] = '-';
            }
            if (!$results[$i]['iban']) {
                $results[$i]['iban'] = '-';
            }
            if (!$results[$i]['address']) {
                $results[$i]['address'] = '-';
            }
            if (!$results[$i]['remark']) {
                $results[$i]['remark'] = '-';
            }
            switch ($results[$i]['owner_type']) {
                case 0:
                    $results[$i]['text'] = $results[$i]['company_name'];
                    $results[$i]['iconCls'] = 'icon-tree-users';

                    break;
                case 1:
                    $results[$i]['text'] = $results[$i]['owner_names'];
                    $results[$i]['iconCls'] = 'icon-tree-user';

                    break;

                default:
                    break;
            }

            if ($results[$i]['is_dead']) {
                $results[$i]['is_dead_text'] = 'Да';
                $results[$i]['iconCls'] = 'icon-tree-user-rip';
            } else {
                $results[$i]['is_dead_text'] = 'Не';
            }

            $return[] = [
                'text' => $results[$i]['text'],
                'id' => $results[$i]['id'],
                'attributes' => $results[$i],
                'iconCls' => $results[$i]['iconCls'],
            ];
        }
        // add attribute to first listed element of three for custom pagination
        // information for total can not be sent otherwise
        $return[0]['attributes']['pagination']['total'] = $counter[0]['count'];
        $return[0]['attributes']['pagination']['limit'] = $rows;

        $return = array_values($return);

        return $return;
    }

    /**
     * Returns array of living people, participating in any contracts for the given year.
     *
     * @api-method read
     *
     * @param array $filterParams
     *                            {
     *                            #item string year
     *                            #item int page_number
     *                            #item string owner_name
     *                            #item string owner_egn
     *                            #item string company_name
     *                            #item string company_eik
     *                            #item string cnum
     *                            #item array contract_type
     *                            #item array farming
     *                            #item array owner_ids
     *                            #item boolean with_renta_nat
     *                            }
     * @param int|string $page -The current page number
     * @param int|string $rows -Rows per page
     * @param string $sort -A grid column by which the grid is sorted
     * @param string $order -The sort order ASC/DESC
     *
     * @return array {
     *               #item string iconCls
     *               #item integer id
     *               #item string text
     *               #attributes array {
     *               #item string address
     *               #item string company_address
     *               #item string company_name
     *               #item string egn
     *               #item string eik
     *               #item string email
     *               #item string fax
     *               #item string iban
     *               #item string iconCls
     *               #item integer id
     *               #item string is_dead_text
     *               #item string lk_izdavane
     *               #item string lk_nomer
     *               #item string mobile
     *               #item string mol
     *               #item string owner_names
     *               #item integer owner_type
     *               #item string owner_type_name
     *               #item string phone
     *               #item string text
     *               #item integer true_id
     *               }
     *               }
     */
    public function readOwners(array $filterParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        if ($this->User->isGuest) {
            return [];
        }

        $FarmingController = new FarmingController('Farming');

        $arrayHelper = $FarmingController->ArrayHelper;
        if (null == $filterParams['year']) {
            $filterParams['year'] = $FarmingController->getCurrentFarmingYearID();
        }

        $options = [
            'return' => [
                'distinct on (true_id) (case when o.is_dead = true then h.owner_id else o.id end) as true_id',
                '(case when o.is_dead = true then h.owner_id else o.id end) as id',
                '(case when o.is_dead = true then oh.address else o.address end) as address',
                '(case when o.is_dead = true then oh.company_address else o.company_address end) as company_address',
                '(case when o.is_dead = true then oh.company_name else o.company_name end) as company_name',
                '(case when o.is_dead = true then oh.egn else o.egn end) as egn',
                '(case when o.is_dead = true then oh.eik else o.eik end) as eik',
                '(case when o.is_dead = true then oh.email else o.email end) as email',
                '(case when o.is_dead = true then oh.fax else o.fax end) as fax',
                '(case when o.is_dead = true then oh.iban else o.iban end) as iban',
                '(case when o.is_dead = true then oh.owner_type else o.owner_type end) as owner_type',
                '(case when o.is_dead = true then oh.lk_nomer else o.lk_nomer end) as lk_nomer',
                '(case when o.is_dead = true then oh.lk_izdavane else o.lk_izdavane end) as lk_izdavane',
                '(case when o.is_dead = true then oh.mobile else o.mobile end) as mobile',
                '(case when o.is_dead = true then oh.phone else o.phone end) as phone',
                '(case when o.is_dead = true then oh.remark else o.remark end) as remark',
                '(case when o.is_dead = true then oh.post_payment_fields else o.post_payment_fields end) as post_payment_fields',
                "(case when (o.is_dead = true and oh.owner_type = 1) then oh.name || ' ' || oh.surname || ' ' || oh.lastname
                    when (o.is_dead = false and o.owner_type = 1) then  o.name || ' ' || o.surname || ' ' || o.lastname
                    else ' ' end) as owner_names",
            ],
            'where' => [
                'is_sublease' => [
                    'column' => 'is_sublease',
                    'compare' => '=',
                    'prefix' => 'c',
                    'value' => 'FALSE',
                ],
                'is_annex' => ['column' => 'is_annex', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
                'annex_action' => [
                    'column' => 'annex_action',
                    'compare' => '=',
                    'prefix' => 'pc',
                    'value' => 'added',
                ],
                'is_heritor' => ['column' => 'is_heritor', 'compare' => '=', 'prefix' => 'po', 'value' => 'FALSE'],
                'active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'TRUE'],
                'c_type' => [
                    'column' => 'nm_usage_rights',
                    'compare' => 'IN',
                    'prefix' => 'c',
                    'value' => [2, 3, 5],
                ],
                'id' => [
                    'column' => '(case when o.is_dead = true then h.owner_id else o.id end)',
                    'compare' => 'IS NOT',
                    'value' => 'NULL',
                ],
                // filters
                'egn' => [
                    'column' => '(case when o.is_dead = true then oh.egn else o.egn end)',
                    'compare' => 'ILIKE',
                    'value' => $filterParams['owner_egn'],
                ],
                'company_name' => [
                    'column' => '(case when o.is_dead = true then  oh.company_name else o.company_name end)',
                    'compare' => 'ILIKE',
                    'value' => $filterParams['company_name'],
                ],
                'eik' => [
                    'column' => '(case when o.is_dead = true then oh.eik else o.eik end)',
                    'compare' => 'ILIKE',
                    'value' => $filterParams['company_eik'],
                ],
                'rep_egn' => [
                    'column' => 'o_r.rep_egn',
                    'compare' => 'ILIKE',
                    'value' => $filterParams['rep_egn'],
                ],
                // contract filters
                'cnum' => [
                    'column' => 'c_num',
                    'compare' => 'ILIKE',
                    'prefix' => 'c',
                    'value' => $filterParams['cnum'],
                ],
                'contract_type' => [
                    'column' => 'nm_usage_rights',
                    'compare' => 'IN',
                    'prefix' => 'c',
                    'value' => $arrayHelper->filterEmptyStringArr($filterParams['contract_type']),
                ],
                'farming' => [
                    'column' => 'farming_id',
                    'compare' => 'IN',
                    'prefix' => 'c',
                    'value' => $arrayHelper->filterEmptyStringArr($filterParams['farming']),
                ],
                'percent' => ['column' => 'percent', 'compare' => '>', 'prefix' => 'po', 'value' => '0'],
            ],
            'start_date' => $GLOBALS['Farming']['years'][$filterParams['year']]['year'] . '-09-30',
            'due_date' => ($GLOBALS['Farming']['years'][$filterParams['year']]['year'] - 1) . '-10-01',
        ];

        $regexCounter = false;
        // from information
        if (isset($filterParams['owner_ids'])) {
            $filterParams['owner_ids'] = array_diff($filterParams['owner_ids'], ['']);
        }

        if ($filterParams['person_name']) {
            $tmp_person_names = preg_replace('/\s+/', '.*', $filterParams['person_name']);
            $tmp_person_names = mb_strtolower($tmp_person_names, 'UTF-8');
            $options['whereOr']['owner_name'] = [
                'column' => "(
                case when (o.is_dead = true and oh.owner_type = 1) 
                    then  lower(trim(oh.name)) || ' ' || lower(trim(oh.surname)) || ' ' || lower(trim(oh.lastname)) 
                    when (o.is_dead = false and o.owner_type = 1) 
                    then  lower(trim(o.name)) || ' ' || lower(trim(o.surname)) || ' ' || lower(trim(o.lastname)) 
                    else ' ' 
                end )", 'compare' => '~', 'value' => $tmp_person_names,
            ];
            $options['whereOr']['rep_name'] = [
                'column' => "lower(TRIM (o_r.rep_name)) || ' ' || lower(TRIM (o_r.rep_surname)) || ' ' || lower(TRIM (o_r.rep_lastname))", 'compare' => '~', 'value' => $tmp_person_names,
            ];
        }

        if ($filterParams['person_egn']) {
            $options['whereOr']['owner_egn'] = ['column' => 'egn', 'compare' => 'ILIKE', 'prefix' => 'o', 'value' => $filterParams['person_egn']];
            $options['whereOr']['rep_egn'] = ['column' => 'rep_egn', 'compare' => 'ILIKE', 'prefix' => 'o_r', 'value' => $filterParams['person_egn']];
            $options['whereOr']['her_egn'] = ['column' => 'egn', 'compare' => 'ILIKE', 'prefix' => 'oh', 'value' => $filterParams['person_egn']];
        }

        if (isset($filterParams['owner_name']) && '' != $filterParams['owner_name']) {
            $regexCounter = true;
            $tmp_owner_names = preg_replace('/\s+/', '.*', $filterParams['owner_name']);
            $tmp_owner_names = mb_strtolower($tmp_owner_names, 'UTF-8');
            $tmp_owner_names_string = "regexp_matches((case when (o.is_dead = true and oh.owner_type = 1) then  lower(trim(oh.name)) || ' ' || lower(trim(oh.surname)) || ' ' || lower(trim(oh.lastname))
                    when (o.is_dead = false and o.owner_type = 1) then  lower(trim(o.name)) || ' ' || lower(trim(o.surname)) || ' ' || lower(trim(o.lastname))
                    else ' ' end),'{$tmp_owner_names}','g')";
            array_push($options['return'], $tmp_owner_names_string);
        }

        if (isset($filterParams['rep_name']) && '' != $filterParams['rep_name']) {
            $regexCounter = true;
            $tmp_rep_names = preg_replace('/\s+/', '.*', $filterParams['rep_name']);
            $tmp_rep_names = mb_strtolower($tmp_rep_names, 'UTF-8');
            $tmp_rep_names_string = "regexp_matches(lower(TRIM (o_r.rep_name)) || ' ' || lower(TRIM (o_r.rep_surname)) || ' ' || lower(TRIM (o_r.rep_lastname)),'{$tmp_rep_names}','g')";
            array_push($options['return'], $tmp_rep_names_string);
        }

        if (isset($filterParams['heirs_name']) && '' != $filterParams['heirs_name']) {
            $regexCounter = true;
            $tmp_heirs_names = trim(preg_replace('/\\s+/', ' ', $filterParams['heirs_name']));
            $tmp_heirs_names = mb_strtolower($tmp_heirs_names, 'UTF-8');
            $tmp_heirs_names_string = "regexp_matches(lower(trim(o.name)) || ' ' || lower(trim(o.surname)) || ' ' || lower(trim(o.lastname)),'{$tmp_heirs_names}','g')";
            array_push($options['return'], $tmp_heirs_names_string);
            $options['where']['is_heritor']['value'] = 'TRUE';
            if (!in_array('po.is_heritor', $options['return'])) {
                $options['return'][] = 'po.is_heritor';
            }
        }

        if (isset($filterParams['heirs_egn']) && !empty($filterParams['heirs_egn'])) {
            $options['where']['egn'] = ['column' => 'egn', 'compare' => 'ILIKE', 'prefix' => 'o', 'value' => $filterParams['heirs_egn']];
            $options['where']['is_heritor']['value'] = 'TRUE';
            if (!in_array('po.is_heritor', $options['return'])) {
                $options['return'][] = 'po.is_heritor';
            }
        }

        // adding with renta nat filter
        if (isset($filterParams['with_renta_nat']) && 1 == $filterParams['with_renta_nat']) {
            $options['where']['with_renta_nat'] = [
                'column' => 'renta_id',
                'prefix' => 'c_r',
                'compare' => '>',
                'value' => '0',
            ];
        }

        // adding without renta nat filter
        if (isset($filterParams['with_renta_nat']) && 0 == $filterParams['with_renta_nat']) {
            $options['where']['with_renta_nat'] = [
                'column' => 'renta_id',
                'prefix' => 'c_r',
                'compare' => 'IS',
                'value' => 'NULL',
            ];
        }
        // TS-3814 adding filter for owners with or without bank accounts
        if (isset($filterParams['with_bank_acc']) && true == $filterParams['with_bank_acc']) {
            $options['where']['iban'] = [
                'column' => 'length(case when o.is_dead = true then oh.iban else o.iban end)',
                'compare' => '>',
                'value' => '1',
            ];
        }

        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);

        if ($regexCounter) {
            $count = $UserDbOwnersController->getOwnersPaymentsData($options, false, false);
            $counter = [];
            $counter[0]['count'] = count($count);
        } else {
            $counter = $UserDbOwnersController->getOwnersPaymentsData($options, true);
        }
        if (0 == $counter[0]['count']) {
            return [];
        }
        $options['offset'] = ($page - 1) * $rows;
        $options['limit'] = $rows;

        $results = $UserDbOwnersController->getOwnersPaymentsData($options, false, false);
        $result_count = count($results);

        if (!$result_count) {
            return [];
        }

        for ($i = 0; $i < $result_count; $i++) {
            $results[$i]['owner_type_name'] = $GLOBALS['Owners']['Types'][$results[$i]['owner_type']]['type_name'];

            if (!$results[$i]['lk_nomer']) {
                $results[$i]['lk_nomer'] = '-';
            }
            if (!$results[$i]['lk_izdavane']) {
                $results[$i]['lk_izdavane'] = '-';
            }
            if (!$results[$i]['mol']) {
                $results[$i]['mol'] = '-';
            }
            if (!$results[$i]['company_address']) {
                $results[$i]['company_address'] = '-';
            }
            if (!$results[$i]['phone']) {
                $results[$i]['phone'] = '-';
            }
            if (!$results[$i]['fax']) {
                $results[$i]['fax'] = '-';
            }
            if (!$results[$i]['mobile']) {
                $results[$i]['mobile'] = '-';
            }
            if (!$results[$i]['email']) {
                $results[$i]['email'] = '-';
            }
            if (!$results[$i]['iban']) {
                $results[$i]['iban'] = '-';
            }
            if (!$results[$i]['address']) {
                $results[$i]['address'] = '-';
            }
            if (!$results[$i]['remark']) {
                $results[$i]['remark'] = '-';
            }
            switch ($results[$i]['owner_type']) {
                case 0:
                    $results[$i]['text'] = $results[$i]['company_name'];
                    $results[$i]['iconCls'] = 'icon-tree-users';

                    break;
                case 1:
                    $results[$i]['text'] = $results[$i]['owner_names'];
                    $results[$i]['iconCls'] = 'icon-tree-user';

                    break;

                default:
                    break;
            }

            if ($results[$i]['is_dead']) {
                $results[$i]['is_dead_text'] = 'Да';
                $results[$i]['iconCls'] = 'icon-tree-user-rip';
            } else {
                $results[$i]['is_dead_text'] = 'Не';
            }

            $return[] = [
                'text' => $results[$i]['text'],
                'id' => $results[$i]['id'],
                'attributes' => $results[$i],
                'iconCls' => $results[$i]['iconCls'],
            ];
        }
        // add attribute to first listed element of three for custom pagination
        // information for total can not be sent otherwise
        $return[0]['attributes']['pagination']['total'] = $counter[0]['count'];
        $return[0]['attributes']['pagination']['limit'] = $rows;

        return $return;
    }
}
