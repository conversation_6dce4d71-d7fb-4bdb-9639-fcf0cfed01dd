<?php

namespace TF\Engine\APIClasses\OwnerPayments;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\Payments\PaymentsController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbAreaTypes\UserDbAreaTypesController;
use TF\Engine\Plugins\Core\UserDbOwners\UserDbOwnersController;
use TF\Engine\Plugins\Core\UserDbPayments\UserDbPaymentsController;
use TF\Engine\Plugins\Core\UserDbPlotCategoriesType\UserDbPlotCategoriesTypeController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Contracts By Owner Payments Grid.
 *
 * @rpc-module OwnerPayments
 *
 * @rpc-service-id contracts-owner-payments-grid
 */
class ContractsByOwnerPaymentsGrid extends TRpcApiProvider
{
    public $paid_renta = 0;
    public $paid_renta_nat = [];
    public $unpaid_renta = 0;
    public $unpaid_renta_nat = [];
    public $filterParam = [];
    public $renta_types = [];
    public $renta_types_values = [];
    public $iterator = 0;
    public $relation_id;
    public $all_renta;
    public $all_renta_detailed;
    public $percent = [];
    public $arrayHelper;
    public $ownerArea = 0;

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readContractsByOwnerPaymentsGridRefactoring'],
                'validators' => [
                    'filterParam' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'unpaidRentByOwner' => ['method' => [$this, 'unpaidRentByOwner'],
                'validators' => [
                    'requestParams' => 'validateArray',
                ],
            ],
        ];
    }

    public function readContractsByOwnerPaymentsGridRefactoring(array $filterParam, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $paymentsController = new PaymentsController($this->User->Database);

        return $paymentsController->getOwnerPayments($filterParam['year'], $filterParam['owner_id']);
    }

    /**
     * returns all contracts, and payments done for those contracts, that a person paticipates in.
     *
     * @api-method read
     *
     * @param array $filterParam
     *                           {
     *                           #item string year
     *                           #item int owner_id
     *                           #item int annex_id
     *                           }
     * @param int $page -The current page number
     * @param int $rows -Rows per page
     * @param string $sort -A grid column by which the grid is sorted
     * @param string $order -The sort order ASC/DESC
     *
     * @return array {
     *               #item integer total
     *               #item array rows {
     *               #item string c_num
     *               #item string charged_renta
     *               #item float charged_renta_area
     *               #item string charged_renta_nat
     *               #item integer contract_id
     *               #item integer contract_parent_id
     *               #item integer farming_id
     *               #item string iconCls
     *               #item integer id
     *               #item boolean is_annex
     *               #item boolean is_dead
     *               #item boolean is_heritor
     *               #item integer level
     *               #item array nat_type_ids
     *               #item float owner_area
     *               #item integer owner_id
     *               #item string owner_names
     *               #item integer paid_renta
     *               #item string paid_renta_nat
     *               #item string parent_id
     *               #item string path
     *               #item array path_renta_natura
     *               #item float renta
     *               #item string renta_nat
     *               #item string renta_nat_type
     *               #item array renta_nat_type_id
     *               #item array renta_types
     *               #item string timespan
     *               #item array tmpRenta
     *               #item string type
     *
     *      #item float unpaid_renta
     *      #item string unpaid_renta_nat
     *   }
     * }
     */
    public function readContractsByOwnerPaymentsGrid(array $filterParam, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        // creating the default return
        $default = [
            'rows' => [],
            'total' => 0,
        ];

        // init controllers
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);
        $FarmingController = new FarmingController('Farming');
        $UsersController = new UsersController('Users');
        $UserDbController = new UserDbController($this->User->Database);

        // filter params
        $request = $filterParam;

        $this->filterParam = $request;

        if (!$request['owner_id'] || !is_numeric($request['owner_id'])) {
            return $default;
        }

        $start_date = $GLOBALS['Farming']['years'][$request['year']]['year'] . '-09-30';
        $due_date = ($GLOBALS['Farming']['years'][$request['year']]['year'] - 1) . '-10-01';

        $options = [
            'return' => [
                ' DISTINCT(C . ID) as contract_id',
            ],
            'where' => [
                'nm_usage_rights' => ['column' => 'nm_usage_rights', 'compare' => 'IN', 'prefix' => 'c', 'value' => [2, 3, 5]],
                'active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'TRUE'],
                'annex' => ['column' => 'is_annex', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
            ],
            'custom_counter' => 'COUNT(DISTINCT(o.id))',
            'owner_id' => $request['owner_id'],
            'start_date' => $start_date,
            'due_date' => $due_date,
        ];

        // Взимат се всички договори на собственика за дадения период (избраната стопанска година)
        $ownerContracts = $UserDbPaymentsController->getContractsByOwnerPaymentsData($options, false, false);

        $options = [
            'return' => [
                'parent_id as contract_id',
            ],
            'owner_id' => $request['owner_id'],
            'start_date' => $start_date,
            'due_date' => $due_date,
        ];

        // Взимат се всички анекси на собственика за дадения период (избраната стопанска година)
        $ownerAnnexes = $UserDbPaymentsController->getAnnexesByOwnerPaymentsData($options, false, false);
        $contracts = array_merge($ownerContracts, $ownerAnnexes);

        $contracts = array_map(function ($contract) {
            return $contract['contract_id'];
        }, $contracts);

        $contracts = array_values(array_unique($contracts));

        $rres = [];
        $farming_year_from_id = $UsersController->StringHelper->getFarmingYearByDate($start_date);
        $farming_year_to_id = $UsersController->StringHelper->getFarmingYearByDate($due_date);
        $farming_years = [];

        for ($i = $farming_year_from_id; $i <= $farming_year_to_id; $i++) {
            $farming_years[] = $i;
        }

        $farming_years_string = '(' . implode(',', $farming_years) . ')';

        $options = [
            'return' => [
                // get owners data
                'DISTINCT(o.id) as owner_id',
                'uuid_generate_v4() as uuid',
                $UserDbOwnersController::isDead('o', [$due_date, $start_date]),
                $UserDbOwnersController::allowOwnerPayment('o', [$due_date, $start_date]),
                'o.owner_type', 'o.post_payment_fields::text', 'c.c_num', 'c.farming_id',
                'o.iban as iban',
                "(CASE WHEN o.owner_type = 1 THEN o.name || ' ' || o.surname || ' ' || o.lastname ELSE o.company_name END) as owner_names",
                '(CASE WHEN o.owner_type = 1 THEN o.egn ELSE o.eik END) as egn_eik',
                '(CASE WHEN o.owner_type = 1 THEN o.address ELSE o.company_address END) as owner_address',
                "(CASE WHEN o.owner_type = 1 THEN o.lk_nomer ELSE '' END) as lk_nomer",
                "(CASE WHEN o.owner_type = 1 THEN o.lk_izdavane ELSE '' END) as lk_izdavane",
                // get representative data
                "(r.rep_name || ' ' || r.rep_surname || ' ' || r.rep_lastname) as rep_names",
                'r.rep_egn as rep_egn',
                'r.rep_lk as rep_lk',
                'r.rep_lk_izdavane as rep_lk_izdavane',
                'r.rep_address as rep_address',
                'r.iban as rep_iban',
                'round(c.renta::numeric, 2) as c_renta',
                // get owners area
                'array_agg((pc.area_for_rent * po.percent / 100)) as contr_area',
                'SUM((pc.area_for_rent * po.percent / 100)::numeric) as area',
                'array_agg(cr.id) as charged_renta_id',
                'SUM((CASE WHEN a.renta IS NULL THEN (
                    CASE
                    WHEN pc.rent_per_plot IS NOT NULL THEN
                        0
                    ELSE
                        C .renta
                    END
                ) ELSE (
                    CASE
                    WHEN pc.rent_per_plot IS NOT NULL THEN
                        0
                    ELSE
                        A .renta
                    END
                ) END) * (((pc.area_for_rent * po.percent / 100)::numeric))) as contract_renta',
                "array_agg((CASE WHEN a.renta IS NULL THEN (
                    CASE
                    WHEN pc.rent_per_plot IS NOT NULL THEN
                        0
                    ELSE
                        C .renta
                    END
                ) ELSE (
                    CASE
                    WHEN pc.rent_per_plot IS NOT NULL THEN
                       0
                    ELSE
                        A .renta
                    END
                ) END) * (((pc.area_for_rent * po.percent / 100)::numeric))  || '|' || c.id || '|' || kvs.gid || '|' || pc.id || '|' || (pc.area_for_rent * po.percent / 100)::numeric || '|' || CASE WHEN a.renta IS NULL THEN 
                        (CASE
                        WHEN pc.rent_per_plot IS NOT NULL THEN
                            0
                        ELSE
                            C .renta
                        END
                    ) ELSE (
                        CASE
                        WHEN pc.rent_per_plot IS NOT NULL THEN
                            0
                        ELSE
                            A .renta
                        END
                    ) END) as contracts_plots_renta",

                "ARRAY_AGG (
                    (
                        CASE
                        WHEN pc.rent_per_plot IS NOT NULL THEN
                            pc.rent_per_plot
                        ELSE
                            NULL
                        END
                    ) * (
                        (
                            pc.area_for_rent * po. PERCENT / 100
                        ) - COALESCE (pu.area, 0)
                    ) :: NUMERIC || '|' || C . ID || '|' || kvs.gid 
                ) AS contracts_rent_per_plot",

                'SUM((
                  CASE
                    WHEN pc.rent_per_plot IS NOT NULL THEN
                        0
                        ELSE
                        cr.renta
                   END
                ) * (pc.area_for_rent * po.percent / 100)) as charged_renta',

                '(CASE WHEN SUM (pc.rent_per_plot) IS NULL THEN FALSE ELSE TRUE END) as has_rent_per_plot',

                'MAX(crn.converted_charged_renta_nat) AS converted_charged_renta_nat',
                "ARRAY_AGG ((
                (
                   cr.renta
                ) * (pc.area_for_rent * (po.percent / 100))) :: NUMERIC || '|' || C . ID || '|' || kvs.gid || '|' || pc.id || '|' || (pc.area_for_rent * po.percent / 100)::numeric || '|' || cr.renta) AS contracts_plots_charged_renta",
                "(CASE WHEN a.id NOTNULL
                        THEN to_char(a.start_date, 'DD.MM.YYYY') || ' - ' || to_char(a.due_date, 'DD.MM.YYYY')
                        ELSE to_char(c.start_date, 'DD.MM.YYYY') || ' - ' || to_char(c.due_date, 'DD.MM.YYYY') 
                END ) AS timespan",
                'array_agg(c.id) as contract_array',
                'array_agg(c.nm_usage_rights) as nm_usage_rights',
                'array_agg(DISTINCT
                    COALESCE(a.c_num, c.c_num)
                ) as c_num_array',
                'array_agg(DISTINCT
                    (a.id)
                ) as annex_ids_array',
                'array_agg(kvs.gid) as plots_array', "array_agg(c.id || '|' || kvs.gid || '|' || po.percent) as plots_percent",
                'array_agg(case when a.id is not null then a.id else c.id end) as contract_anex_arr',
                'array_agg(kvs.kad_ident) as plots_name_array',
                "array_agg(c.id || '|' || c.c_num || '|' || kvs.kad_ident || '|' || round((pc.area_for_rent * po.percent / 100)::numeric, 3) || '|' || pc.id || '|' || c.id || '|' || pc.plot_id || '|' || round((CASE WHEN a.renta IS NOT NULL THEN a.renta ELSE c.renta END),2) || '|' || (CASE WHEN pc.rent_per_plot IS NOT NULL THEN pc.rent_per_plot ELSE 0 END)) as pcrel",
                "array_agg(c.id || '|' || kvs.gid || '|' || (pc.area_for_rent * po.percent / 100) || '|' || pc.id) as pcrel_gid",
                "string_agg(pc.id::text, ',') as pc_rel_ids",
                '(SELECT array_agg(cr.id)
                   FROM su_contracts_rents cr
                   WHERE cr.contract_id = case when max(a.id) is not null then max(a.id) else max(c.id) end) 
                AS renta_nat_type_id',
            ],
            'where' => [
                'contract_type' => ['column' => 'nm_usage_rights', 'compare' => 'NOT IN', 'prefix' => 'c', 'value' => [1, 4]],
                'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
                'is_annnex' => ['column' => 'is_annex', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
                'annex_action' => ['column' => 'annex_action', 'compare' => '=', 'prefix' => 'pc', 'value' => 'added'],
                'is_heritor' => ['column' => 'is_heritor', 'compare' => '=', 'prefix' => 'po', 'value' => 'FALSE'],
                'active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'TRUE'],
                'percent' => ['column' => 'percent', 'compare' => '>', 'prefix' => 'po', 'value' => '0'],
                'contracts_ids' => ['column' => 'id', 'compare' => 'IN', 'prefix' => 'c', 'value' => $contracts],
                'is_edited' => ['column' => "(case when kvs.is_edited = false then true else kvs.edit_active_from > '" . $due_date . "' end)", 'compare' => '=', 'value' => 'TRUE'],
            ],
            'sort' => $sort,
            'group' => 'o.id, c.id, r.id, a.id, a.start_date, a.due_date',
            'custom_counter' => 'COUNT(DISTINCT(o.id))',
            'start_date' => $start_date,
            'due_date' => $due_date,
            'year_id' => $farming_years,
        ];

        $results = $UserDbPaymentsController->getPayrollData($options, false, false);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableRentaTypes,
        ];

        $renta_results = $UserDbController->getItemsByParams($options, false, false);
        $rentCount = count($renta_results);
        // if we search by heritor (with $request['by_heritor'] == true)
        // we should return only contracts which were inherited by the heritor, not contracts where the heritor is a regular owner
        if (isset($request['by_heritor']) && true == (bool)$request['by_heritor']) {
            foreach ($results as $id => $row) {
                if ($request['owner_id'] == $row['owner_id']) {
                    unset($results[$id]);
                }
            }
        }

        if (0 == count($results)) {
            return $default;
        }
        // create renta types array
        for ($i = 0; $i < $rentCount; $i++) {
            $this->renta_types[$renta_results[$i]['id']] = $renta_results[$i]['name'] . '(' . $GLOBALS['Contracts']['renta_units'][$renta_results[$i]['unit']]['name'] . ')';
            $this->renta_types_values[$renta_results[$i]['id']] = $renta_results[$i]['unit_value'];
        }

        // build main query(getting all personal use data)
        $personalUseOptions = [
            'owner_ids' => [$request['owner_id']],
            'chosen_years' => $request['year'],
        ];

        $personalUse = $UserDbPaymentsController->getPersonalUseForOwners($personalUseOptions, false, false);

        foreach ($results as $key => $result) {
            // iterate and convert results to grid format
            $ownerParams = [
                'results' => [0 => $result],
            ];

            $results = $this->getContractOwnerProperties($ownerParams, $farming_years, $start_date, $due_date, $farming_years_string, $personalUse);

            $list = [];
            $ownerResults = $UserDbOwnersController->makeOwnersTreeToListById($results['rows'], $list, $request['owner_id']);

            if (!empty($ownerResults)) {
                foreach ($ownerResults as $keyRes => $value) {
                    $fieldsInArray = ['uuid', 'farming_id', 'unpaid_renta', 'unpaid_renta_nat', 'area', 'owner_id', 'contract_id', 'charged_renta', 'renta', 'charged_renta_nat', 'renta_nat'];
                    $ownerResultsByPath = $UserDbOwnersController->saveOwnerResultsByPathInArray($value, $fieldsInArray);

                    $value['owners'] = $ownerResultsByPath;
                    $value['farming_id'] = $results['rows'][0]['farming_id'];
                    $rres[] = $value;
                }
            }
        }

        unset($result, $value);
        $resultsByContract = [];
        $resCount = count($rres);
        $total_area = 0;
        $total_renta = 0;
        $total_owned_area = 0;
        $total_pu_area = 0;
        $total_charged_renta = 0;
        $total_paid_renta = 0;
        $total_unpaid_renta = 0;
        $total_over_paid = 0;
        $total_sum_by_paid_renta = 0;
        for ($i = 0; $i < $resCount; $i++) {
            $result = &$rres[$i];
            $contract_id = $result['contract_id'];
            $puArea = 0;
            $renta = 0;
            $charged_renta = 0;
            foreach ($result['plots_contracts_area_array'] as $contractPlot) {
                $puArea += $contractPlot['pu_area'];
                if (!empty($contractPlot['charged_renta'])) {
                    $charged_renta += $contractPlot['charged_renta'];
                } else {
                    $renta += $contractPlot['renta'];
                }
            }

            $type = $result['type'];
            $farming_id = $result['farming_id'];
            $timespan = $result['timespan'];
            $c_num = $result['c_num'];
            $paid_renta = $result['paid_renta'];
            $has_rent_per_plot = $result['has_rent_per_plot'];

            $path = $result['path'];

            $owners_ids = $result['owners']['id_renta_natura'];
            $owners_paths = $result['owners']['path_renta_natura'];

            $nat_type_ids = $result['nat_type_ids'];

            $paid_renta_by = $result['paid_renta_by_arr'];
            $paid_renta_by_amount = $paid_renta_by['amount'];
            $paid_renta_by_nat_amount = $paid_renta_by['nat_amount'];

            $paid_renta_nat = $result['paid_renta_nat_details'];

            $paid_renta_nat_by = $result['paid_renta_nat_by_arr'];
            $paid_renta_nat_by_amount = $paid_renta_nat_by['amount'];
            $paid_renta_nat_by_nat_amount = $paid_renta_nat_by['nat_amount'];

            $unpaid_renta = $result['unpaid_renta'];
            $over_paid = $result['over_paid'];
            $renta_nat = $result['renta_nat'];
            $renta_nat_type = $result['renta_nat_type'];
            $charged_renta_nat = $result['charged_renta_nat'];

            $all_paid_renta_nat_by_detailed_quantity_arr = $result['all_paid_renta_nat_by_detailed_quantity_arr'];
            $all_paid_renta_nat_by_detailed_unit_value_arr = $result['all_paid_renta_nat_by_detailed_unit_value_arr'];

            $unpaid_renta_nat = $result['unpaid_renta_nat_arr'];
            $unpaid_renta_nat_unit_value = $result['unpaid_renta_nat_unit_value_arr'];

            $over_paid_nat = $result['over_paid_renta_nat_arr'];
            $total_by_renta = $result['total_by_renta_sum'];
            $total_by_renta_nat = $result['total_by_renta_nat_arr'];

            $plots_contracts_area_array_gids = $result['plots_contracts_area_array_gids'];

            if (array_key_exists($contract_id, $resultsByContract)) {
                $resultByCont = &$resultsByContract[$contract_id];
                $resultByCont['area'] += $result['area'];

                if ($puArea) {
                    $resultByCont['pu_area'] += $puArea;
                }

                $resultByCont['renta'] += $renta;
                $resultByCont['charged_renta'] += $charged_renta;
                $resultByCont['paid_renta'] += $paid_renta;
                $resultByCont['total_by_renta'] += $total_by_renta;
                $resultByCont['unpaid_renta'] += $unpaid_renta;
                $resultByCont['over_paid'] += $over_paid;
                $resultByCont['has_rent_per_plot'] = ($resultByCont['has_rent_per_plot'] || $has_rent_per_plot);

                if (!empty($owners_ids)) {
                    $resultByCont['id_renta_natura'] = $owners_ids;
                }
                if (!empty($owners_paths)) {
                    $resultByCont['path_renta_natura'][$path] = $owners_paths[$path];
                }

                if (!empty($nat_type_ids)) {
                    foreach ($nat_type_ids as $key => $rentaType) {
                        if (!in_array($rentaType, $resultByCont['nat_type_ids'])) {
                            $resultByCont['nat_type_ids'][] = $rentaType;
                        }
                    }
                }

                if (null !== $paid_renta_by_amount) {
                    $resultByCont['paid_renta_by_arr']['amount'] += $paid_renta_by_amount;
                }
                if (!empty($paid_renta_by_nat_amount)) {
                    foreach ($paid_renta_by_nat_amount as $key => $value) {
                        if (is_numeric($value)) {
                            $resultByCont['paid_renta_by_arr']['nat_amount'][$key] += $value;
                        }
                    }
                }
                if (null !== $paid_renta_nat_by_amount) {
                    $resultByCont['paid_renta_nat_by_arr']['amount'] += $paid_renta_nat_by_amount;
                }
                if (!empty($paid_renta_nat_by_nat_amount)) {
                    foreach ($paid_renta_nat_by_nat_amount as $key => $value) {
                        if (is_numeric($value)) {
                            $resultByCont['paid_renta_nat_by_arr']['nat_amount'][$key] += $value;
                        }
                    }
                }

                if (!empty($paid_renta_nat)) {
                    foreach ($paid_renta_nat as $key => $value) {
                        if (is_numeric($value)) {
                            $resultByCont['paid_renta_nat'][$key] += $value;
                        }
                    }
                }

                if (!empty($renta_nat)) {
                    foreach ($renta_nat as $key => $value) {
                        if (is_numeric($value)) {
                            $resultByCont['renta_nat'][$key] += $value;
                        }
                    }
                }
                if (!empty($charged_renta_nat)) {
                    foreach ($charged_renta_nat as $key => $value) {
                        if (is_numeric($value)) {
                            $resultByCont['charged_renta_nat'][$key] += $value;
                        }
                    }
                }

                if (!empty($all_paid_renta_nat_by_detailed_quantity_arr)) {
                    foreach ($all_paid_renta_nat_by_detailed_quantity_arr as $key => $value) {
                        if (is_numeric($value)) {
                            $resultByCont['all_paid_renta_nat_by_detailed_quantity_arr'][$key] += $value;
                        }
                    }
                }

                if (!empty($unpaid_renta_nat)) {
                    foreach ($unpaid_renta_nat as $key => $value) {
                        if (is_numeric($value)) {
                            $resultByCont['unpaid_renta_nat'][$key] += $value;
                            {
                                $resultByCont['unpaid_renta_nat'][$key] -= $over_paid_nat[$key];
                                $over_paid_nat[$key] = 0;
                                if ($resultByCont['unpaid_renta_nat'][$key] < 0) {
                                    $over_paid_nat[$key] = abs($resultByCont['unpaid_renta_nat'][$key]);
                                    $resultByCont['unpaid_renta_nat'][$key] = 0;
                                }
                            }
                        }
                    }
                }
                if (!empty($unpaid_renta_nat_unit_value)) {
                    foreach ($unpaid_renta_nat_unit_value as $key => $value) {
                        if (is_numeric($value)) {
                            $resultByCont['unpaid_renta_nat_unit_value'][$key] += $value;
                        }
                    }
                }
                if (!empty($over_paid_nat)) {
                    foreach ($over_paid_nat as $key => $value) {
                        if (is_numeric($value)) {
                            $resultByCont['over_paid_nat'][$key] += $value;
                        }
                    }
                }

                if (!empty($total_by_renta_nat)) {
                    foreach ($total_by_renta_nat as $key => $value) {
                        if (is_numeric($value)) {
                            $resultByCont['total_by_renta_nat_arr_last'][$key] += $value;
                        }
                    }
                }
            } else {
                $resultByCont = &$resultsByContract[$contract_id];
                $resultByCont['area'] = $result['area'];

                if ($puArea) {
                    $resultByCont['pu_area'] = $puArea;
                }

                $resultByCont['c_num'] = $c_num;
                $resultByCont['timespan'] = $timespan;
                $resultByCont['type'] = $type;
                $resultByCont['farming_id'] = $farming_id;
                $resultByCont['owner_names'] = $result['owner_names'];
                $resultByCont['owner_egn'] = $result['egn_eik'];
                $resultByCont['owner_address'] = $result['owner_address'];
                $resultByCont['iban'] = $result['iban'];
                $resultByCont['lk_nomer'] = $result['lk_nomer'];
                $resultByCont['lk_izdavane'] = $result['lk_izdavane'];
                $resultByCont['rep_names'] = $result['rep_names'];
                $resultByCont['rep_egn'] = $result['rep_egn'];
                $resultByCont['rep_address'] = $result['rep_address'];
                $resultByCont['rep_lk'] = $result['rep_lk'];
                $resultByCont['rep_lk_izdavane'] = $result['rep_lk_izdavane'];
                $resultByCont['rep_iban'] = $result['rep_iban'];
                $resultByCont['renta'] = $renta;
                $resultByCont['charged_renta'] = $charged_renta;
                $resultByCont['paid_renta'] = $paid_renta;
                $resultByCont['unpaid_renta'] = $unpaid_renta;
                $resultByCont['over_paid'] = $over_paid;
                $resultByCont['renta_nat_type'] = $renta_nat_type;
                $resultByCont['total_by_renta'] = $total_by_renta;
                $resultByCont['contract_id'] = $contract_id;
                $resultByCont['plots_contracts_charged_renta_arr'] = $result['plots_contracts_charged_renta_arr'];
                $resultByCont['plots_contracts_area_array_gids'] = $result['plots_contracts_area_array_gids'];

                $resultByCont['id_renta_natura'] = [];
                if (!empty($owners_ids)) {
                    $resultByCont['id_renta_natura'] = $owners_ids;
                }

                $resultByCont['path_renta_natura'] = [];
                if (!empty($owners_paths)) {
                    $resultByCont['path_renta_natura'] = $owners_paths;
                }

                $resultByCont['has_rent_per_plot'] = $has_rent_per_plot;

                if (!empty($nat_type_ids)) {
                    foreach ($nat_type_ids as $key => $rentaType) {
                        $resultByCont['nat_type_ids'][] = $rentaType;
                    }
                } else {
                    $resultByCont['nat_type_ids'] = [];
                }

                if (null !== $paid_renta_by_amount) {
                    $resultByCont['paid_renta_by_arr']['amount'] = $paid_renta_by_amount;
                }
                if (!empty($paid_renta_by_nat_amount)) {
                    foreach ($paid_renta_by_nat_amount as $key => $value) {
                        $resultByCont['paid_renta_by_arr']['nat_amount'][$key] = $value;
                    }
                }
                if (null !== $paid_renta_nat_by_amount) {
                    $resultByCont['paid_renta_nat_by_arr']['amount'] = $paid_renta_nat_by_amount;
                }
                if (!empty($paid_renta_nat_by_nat_amount)) {
                    foreach ($paid_renta_nat_by_nat_amount as $key => $value) {
                        $resultByCont['paid_renta_nat_by_arr']['nat_amount'][$key] = $value;
                    }
                }

                if (!empty($paid_renta_nat)) {
                    foreach ($paid_renta_nat as $key => $value) {
                        $resultByCont['paid_renta_nat'][$key] = $value;
                    }
                }

                if (!empty($renta_nat)) {
                    foreach ($renta_nat as $key => $value) {
                        $resultByCont['renta_nat'][$key] = $value;
                    }
                }
                if (!empty($charged_renta_nat)) {
                    foreach ($charged_renta_nat as $key => $value) {
                        $resultByCont['charged_renta_nat'][$key] = $value;
                    }
                }

                if (!empty($all_paid_renta_nat_by_detailed_quantity_arr)) {
                    foreach ($all_paid_renta_nat_by_detailed_quantity_arr as $key => $value) {
                        $resultByCont['all_paid_renta_nat_by_detailed_quantity_arr'][$key] = $value;
                    }
                    foreach ($all_paid_renta_nat_by_detailed_unit_value_arr as $key => $value) {
                        $resultByCont['all_paid_renta_nat_by_detailed_unit_value_arr'][$key] = $value;
                    }
                }

                if (!empty($unpaid_renta_nat)) {
                    foreach ($unpaid_renta_nat as $key => $value) {
                        $resultByCont['unpaid_renta_nat'][$key] = number_format($value, 3, '.', '');
                        if (!empty($over_paid_nat[$key])) {
                            $resultByCont['unpaid_renta_nat'][$key] -= $over_paid_nat[$key];
                            $over_paid_nat[$key] = 0;
                            if ($resultByCont['unpaid_renta_nat'][$key] < 0) {
                                $over_paid_nat[$key] = abs($resultByCont['unpaid_renta_nat'][$key]);
                                $resultByCont['unpaid_renta_nat'][$key] = 0;
                            }
                        }
                    }
                }
                if (!empty($unpaid_renta_nat_unit_value)) {
                    foreach ($unpaid_renta_nat_unit_value as $key => $value) {
                        $resultByCont['unpaid_renta_nat_unit_value'][$key] = $value;
                    }
                }
                if (!empty($over_paid_nat)) {
                    foreach ($over_paid_nat as $key => $value) {
                        $resultByCont['over_paid_nat'][$key] = $value;
                    }
                }

                if (!empty($total_by_renta_nat)) {
                    foreach ($total_by_renta_nat as $key => $value) {
                        $resultByCont['total_by_renta_nat_arr_last'][$key] = $value;
                    }
                }
            }

            $resultByCont['path'] = $path;
            $resultByCont['uuid'] = $result['uuid'];
            $resultByCont['annex_id'] = $result['annex_ids_array'][0] ?? null;
        }

        $farmingOptions = [
            'return' => ['id', 'name', 'post_payment_fields'],
            'where' => [
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'value' => $this->User->GroupID],
            ],
        ];

        $farmings = $FarmingController->getFarmings($farmingOptions, false, false);
        $totalPersonalUse = [];

        foreach ($resultsByContract as $key => &$value) {
            $resultByCont = &$value;

            $filteredFarming = array_filter($farmings, function ($element) use ($resultByCont) {
                return ($element['id'] == $resultByCont['farming_id']);
            });
            $resultByCont['farming_name'] = '-';

            if (!empty($filteredFarming)) {
                $resultByCont['farming_name'] = array_values($filteredFarming)[0]['name'];
                $resultByCont['post_payment_fields'] = array_values($filteredFarming)[0]['post_payment_fields'];
            }

            // This is the owned area before substraction of the personal use
            $resultByCont['owned_area'] = number_format(($resultByCont['area']), 3, '.', '');

            $resultByCont['area'] = number_format($resultByCont['area'] - $resultByCont['pu_area'], 3, '.', '');

            // Init the personal use area
            if ($resultByCont['pu_area']) {
                $resultByCont['pu_area'] = number_format($resultByCont['pu_area'], 3, '.', '');
            }
            $resultByCont['renta'] = number_format($resultByCont['renta'], 2, '.', '');
            $resultByCont['renta_nat_type_id'] = $resultByCont['nat_type_ids'];
            $resultByCont['renta_types'] = $renta_results;

            if ('-' !== $resultByCont['charged_renta']) {
                $resultByCont['charged_renta'] = number_format($resultByCont['charged_renta'], 2, '.', '');
            }
            if ('-' !== $resultByCont['paid_renta']) {
                $resultByCont['paid_renta'] = number_format($resultByCont['paid_renta'], 2, '.', '');
            }

            $paid_renta_by = $resultByCont['paid_renta_by_arr'];

            $paid_renta_by_amount = $paid_renta_by['amount'];
            if (null !== $paid_renta_by_amount && '-' !== $paid_renta_by_amount) {
                $resultByCont['paid_renta_by'] .= $paid_renta_by_amount . ' лв.</br>';
            }
            $paid_renta_by_nat_amount = $paid_renta_by['nat_amount'];
            if (!empty($paid_renta_by_nat_amount)) {
                foreach ($paid_renta_by_nat_amount as $key => $rentaNat) {
                    $rentaNat = '-' != $rentaNat ? number_format($rentaNat, 3, '.', '') : '-';
                    $resultByCont['paid_renta_by'] .= $rentaNat . ' X ' . $this->renta_types[$key] . '</br>';
                }
            }
            if (null === $resultByCont['paid_renta_by']) {
                $resultByCont['paid_renta_by'] = '-';
            }

            $paid_renta_nat_by = $resultByCont['paid_renta_nat_by_arr'];

            $paid_renta_nat_by_amount = $paid_renta_nat_by['amount'];
            if (null !== $paid_renta_nat_by_amount && '-' !== $paid_renta_nat_by_amount) {
                $resultByCont['paid_renta_nat_by'] .= $paid_renta_nat_by_amount . ' лв.</br>';
            }
            $paid_renta_nat_by_nat_amount = $paid_renta_nat_by['nat_amount'];
            if (!empty($paid_renta_nat_by_nat_amount)) {
                foreach ($paid_renta_nat_by_nat_amount as $key => $rentaNat) {
                    $rentaNat = '-' != $rentaNat ? number_format($rentaNat, 3, '.', '') : '-';
                    $resultByCont['paid_renta_nat_by'] .= $rentaNat . ' X ' . $this->renta_types[$key] . '</br>';
                }
            }
            if (is_null($resultByCont['paid_renta_nat_by'])) {
                $resultByCont['paid_renta_nat_by'] = '-';
            }

            if (!is_null($resultByCont['unpaid_renta']) && '-' !== $resultByCont['unpaid_renta']) {
                $resultByCont['unpaid_renta'] = number_format($resultByCont['unpaid_renta'], 2, '.', '');
            }

            if (!is_null($resultByCont['over_paid']) && '-' !== $resultByCont['over_paid']) {
                $resultByCont['over_paid'] = number_format($resultByCont['over_paid'], 2, '.', '');
            }

            if (!empty($resultByCont['paid_renta_nat'])) {
                foreach ($resultByCont['paid_renta_nat'] as $key => $rentaNat) {
                    $rentaNat = '-' != $rentaNat ? number_format($rentaNat, 3, '.', '') : '-';
                    $resultByCont['paid_renta_nat_text'] .= $rentaNat . '</br>';
                }
            } else {
                $resultByCont['paid_renta_nat_text'] .= '-';
            }

            if (!empty($resultByCont['renta_nat'])) {
                foreach ($resultByCont['renta_nat'] as $key => $rentaNat) {
                    $rentaNat = '-' != $rentaNat ? number_format($rentaNat, 3, '.', '') : '-';
                    $resultByCont['renta_nat_text'] .= $rentaNat . '</br>';
                }
            } else {
                $resultByCont['renta_nat_text'] .= '-';
            }

            if (!empty($resultByCont['charged_renta_nat'])) {
                foreach ($resultByCont['charged_renta_nat'] as $key => $rentaNat) {
                    $rentaNat = '-' != $rentaNat ? number_format($rentaNat, 3, '.', '') : '-';
                    $resultByCont['charged_renta_nat_text'] .= $rentaNat . '</br>';
                }
            } else {
                $resultByCont['charged_renta_nat_text'] .= '-';
            }

            $all_paid_renta_nat_by_detailed_quantity_arr = $resultByCont['all_paid_renta_nat_by_detailed_quantity_arr'];
            $all_paid_renta_nat_by_detailed_unit_value_arr = $resultByCont['all_paid_renta_nat_by_detailed_unit_value_arr'];

            if (!empty($all_paid_renta_nat_by_detailed_quantity_arr)) {
                foreach ($all_paid_renta_nat_by_detailed_quantity_arr as $key => $rentaNat) {
                    $rentaNat = '-' != $rentaNat ? number_format($rentaNat, 3, '.', '') : '-';
                    $unitValue = $all_paid_renta_nat_by_detailed_unit_value_arr[$key];

                    $resultByCont['paid_renta_nat_by_detailed'] .= $rentaNat . ' X ' . $this->renta_types[$key] . ' X ' . $unitValue . ' ед.ст.</br>';
                }
            } else {
                $resultByCont['paid_renta_nat_by_detailed'] = '-';
            }

            $unpaid_renta_nat = $resultByCont['unpaid_renta_nat'];

            if (!empty($unpaid_renta_nat)) {
                foreach ($unpaid_renta_nat as $key => $rentaNat) {
                    $rentaNat = '-' != $rentaNat ? number_format($rentaNat, 3, '.', '') : '-';
                    $resultByCont['unpaid_renta_nat_text'] .= $rentaNat . '</br>';
                }
            } else {
                $resultByCont['unpaid_renta_nat_text'] = '-';
            }

            $unpaid_renta_nat_unit_value = $resultByCont['unpaid_renta_nat_unit_value'];
            if (!empty($unpaid_renta_nat_unit_value)) {
                foreach ($unpaid_renta_nat_unit_value as $key => $rentaNat) {
                    $rentaNat = '-' != $rentaNat ? number_format($rentaNat, 2, '.', '') . ' лв.' : '-';
                    $resultByCont['unpaid_renta_nat_unit_value_text'] .= $rentaNat . '</br>';
                }
            } else {
                $resultByCont['unpaid_renta_nat_unit_value_text'] = '-';
            }

            $over_paid_nat = $resultByCont['over_paid_nat'];
            if (!empty($over_paid_nat)) {
                foreach ($over_paid_nat as $key => $rentaNat) {
                    $rentaNat = '-' != $rentaNat ? number_format($rentaNat, 3, '.', '') : '-';
                    $resultByCont['over_paid_nat_text'] .= $rentaNat . '</br>';
                }
            } else {
                $resultByCont['over_paid_nat_text'] = '-';
            }

            $total_by_renta = $resultByCont['total_by_renta'];
            if (!is_null($total_by_renta) && '-' != $total_by_renta) {
                $total_by_renta = $total_by_renta > 0 ? number_format($total_by_renta, 2, '.', '') : '0.00';
                $resultByCont['total_by_renta'] = $total_by_renta . ' лв.';
            } else {
                $resultByCont['total_by_renta'] = '-';
            }

            $total_by_renta_nat = $resultByCont['total_by_renta_nat_arr_last'];
            if (!empty($total_by_renta_nat)) {
                foreach ($total_by_renta_nat as $key => $rentaNat) {
                    $rentaNat = '-' != $rentaNat ? number_format($rentaNat, 3, '.', '') : '-';
                    $resultByCont['total_by_renta_nat_text'] .= $rentaNat . ' X ' . $this->renta_types[$key] . '</br>';
                }
            } else {
                $resultByCont['total_by_renta_nat_text'] = '-';
            }

            // In order to avoid wrong order of the natures in the grid, we need to sort the arrays

            $rentaTypes = [];
            // sort charged_renta_nat array by array key assending
            ksort($resultByCont['charged_renta_nat']);
            ksort($resultByCont['paid_renta_nat']);
            ksort($resultByCont['renta_nat_type_id']);
            ksort($resultByCont['unpaid_renta_nat']);
            ksort($resultByCont['unpaid_renta_nat_unit_value']);
            ksort($resultByCont['over_paid_nat']);
            ksort($resultByCont['renta_nat']);

            // sort renta_nat_type_id by values assending
            asort($resultByCont['renta_nat_type_id']);
            foreach ($resultByCont['renta_nat_type_id'] as $k => $rentaTypeIdOrdered) {
                $rentaTypes[] = $this->renta_types[$rentaTypeIdOrdered];
            }

            $resultByCont['charged_renta_nat_text'] = implode('</br>', $resultByCont['charged_renta_nat']);
            $resultByCont['paid_renta_nat_text'] = implode('</br>', $resultByCont['paid_renta_nat']);
            $resultByCont['renta_nat_type'] = implode('</br>', $rentaTypes);
            $resultByCont['renta_nat'] = implode('</br>', $resultByCont['renta_nat']);

            // put every element of the array in a separate row and replace 0 with '-'
            $resultByCont['unpaid_renta_nat_text'] = implode('</br>', array_map(function ($value) {
                return 0 == $value ? '-' : number_format($value, 3, '.', '');
            }, $resultByCont['unpaid_renta_nat']));

            $resultByCont['unpaid_renta_nat_unit_value_text'] = implode('</br>', array_map(function ($value) {
                return $value . ' лв.';
            }, $resultByCont['unpaid_renta_nat_unit_value']));

            $resultByCont['over_paid_nat_text'] = implode('</br>', array_map(function ($value) {
                return 0 == $value ? '-' : $value;
            }, $resultByCont['over_paid_nat']));

            $resultByCont['personal_use'] = [];
            $resultByCont['personal_use_nat_type_id'] = [];
            $resultByCont['personal_use_nat_types_names_arr'] = [];
            $resultByCont['personal_use_amount_arr'] = [];
            $resultByCont['personal_use_paid_arr'] = [];
            $resultByCont['personal_use_unpaid_arr'] = [];
            $resultByCont['charged_renta_area_without_pu'] = 0;
            $resultByCont['charged_renta_area_with_pu'] = 0;

            foreach ($personalUse as $personalUseValue) {
                if ($personalUseValue['contract_id'] == $resultByCont['contract_id']) {
                    foreach ($resultByCont['plots_contracts_charged_renta_arr'] as $chargedRentaPlotInfo) {
                        $puPlotsAreas = json_decode($personalUseValue['personal_use_plots_area'], true);
                        foreach ($puPlotsAreas as $puPlotArea) {
                            if ($puPlotArea['pc_rel_id'] == $chargedRentaPlotInfo['rel_id']) {
                                // $puArea += $puPlotArea['area'];
                                // $resultByCont['charged_renta_area_without_pu'] += ($chargedRentaPlotInfo['area'] - $puPlotArea['area']);
                                // $resultByCont['charged_renta_area_with_pu'] += $chargedRentaPlotInfo['area'];
                            }
                        }
                    }
                    $resultByCont['personal_use_nat_type_id'][] = $personalUseValue['renta_type'];
                    $resultByCont['personal_use_nat_types_names_arr'][] = $personalUseValue['renta_type_name'];
                    $resultByCont['personal_use_treatments_arr'][] = $personalUseValue['personal_use_treatments_sum'];

                    $resultByCont['personal_use_amount_arr'][] = number_format(($personalUseValue['personal_use_renta'] < 0) ? 0 : $personalUseValue['personal_use_renta'], 3, '.', '');
                    $resultByCont['personal_use_price_sum_arr'][] = number_format(($personalUseValue['personal_use_price_sum'] < 0) ? 0 : $personalUseValue['personal_use_price_sum'], 3, '.', '');

                    $personalUseValue['paid_amount'] = number_format(($personalUseValue['personal_use_paid_renta'] < 0) ? 0 : $personalUseValue['personal_use_paid_renta'], 3, '.', '');
                    $personalUseValue['unpaid_amount'] = number_format(($personalUseValue['personal_use_unpaid_renta']), 3, '.', '');
                    $resultByCont['personal_use_paid_arr'][] = $personalUseValue['paid_amount'];
                    $resultByCont['personal_use_unpaid_arr'][] = $personalUseValue['unpaid_amount'];
                    $resultByCont['personal_use_paid_treatments_arr'][] = number_format($personalUseValue['personal_use_paid_treatments'], 2, '.', '');
                    $resultByCont['personal_use_unpaid_treatments_arr'][] = number_format($personalUseValue['personal_use_unpaid_treatments'], 2, '.', '');

                    if (!array_key_exists($personalUseValue['renta_type'], $totalPersonalUse)) {
                        // subtract personal use area from total area
                        // $resultByCont['area'] -= $personalUseValue['area_by_renta_type'];

                        // set personal use area
                        // $resultByCont['pu_area'] = $personalUseValue['total_personal_use_area'];

                        $totalPersonalUse[$personalUseValue['renta_type']] = [
                            'name' => $personalUseValue['renta_type_name'],
                            'personal_use_renta' => $personalUseValue['personal_use_renta'],
                            'paid_amount' => $personalUseValue['paid_amount'],
                            'unpaid_amount' => $personalUseValue['unpaid_amount'],
                            'personal_use_treatments_sum' => $personalUseValue['personal_use_treatments_sum'],
                            'personal_use_paid_treatments' => $personalUseValue['personal_use_paid_treatments'],
                            'personal_use_unpaid_treatments' => $personalUseValue['personal_use_unpaid_treatments'],
                        ];
                    } else {
                        $totalPersonalUse[$personalUseValue['renta_type']]['personal_use_renta'] += $personalUseValue['personal_use_renta'];
                        $totalPersonalUse[$personalUseValue['renta_type']]['paid_amount'] += $personalUseValue['paid_amount'];
                        $totalPersonalUse[$personalUseValue['renta_type']]['unpaid_amount'] += $personalUseValue['unpaid_amount'];
                        $totalPersonalUse[$personalUseValue['renta_type']]['personal_use_treatments_sum'] += $personalUseValue['personal_use_treatments_sum'];
                        $totalPersonalUse[$personalUseValue['renta_type']]['personal_use_paid_treatments'] += $personalUseValue['personal_use_paid_treatments'];
                        $totalPersonalUse[$personalUseValue['renta_type']]['personal_use_unpaid_treatments'] += $personalUseValue['personal_use_unpaid_treatments'];
                    }

                    $resultByCont['personal_use'][] = $personalUseValue;
                }
            }

            // recalculate rents in case of personal use
            // if (!empty($resultByCont['personal_use'])) {
            //     $UserDbPaymentsController->calculatePersonalUseForOwner($resultByCont);
            // }

            $total_owned_area += $resultByCont['owned_area'];
            $total_pu_area += $resultByCont['pu_area'];
            $total_area += $resultByCont['area'];
            $total_renta += $resultByCont['renta'];
            $total_charged_renta += $resultByCont['charged_renta'];
            $total_paid_renta += $resultByCont['paid_renta'];
            $total_unpaid_renta += $resultByCont['unpaid_renta'];
            $total_over_paid += $resultByCont['over_paid'];
            $total_sum_by_paid_renta += $resultByCont['total_by_renta'];

            $resultByCont['area'] = number_format($resultByCont['area'], 3, '.', '');
            if ($resultByCont['pu_area']) {
                $resultByCont['pu_area'] = number_format($resultByCont['pu_area'], 3, '.', '');
            } else {
                $resultByCont['pu_area'] = '-';
            }
            $resultByCont['personal_use_price_sum'] = number_format(array_sum($resultByCont['personal_use_price_sum_arr']), 3, '.', '');
            $resultByCont['personal_use_nat_types_names'] = implode('</br>', $resultByCont['personal_use_nat_types_names_arr']);
            $resultByCont['personal_use_amount'] = implode('</br>', $resultByCont['personal_use_amount_arr']);
            $resultByCont['personal_use_paid'] = implode('<br>', $resultByCont['personal_use_paid_arr']);
            $resultByCont['personal_use_unpaid'] = implode('<br>', $resultByCont['personal_use_unpaid_arr']);
            $resultByCont['personal_use_treatments_sum'] = implode('<br>', $resultByCont['personal_use_treatments_arr']);
            $resultByCont['personal_use_paid_treatments'] = implode('<br>', $resultByCont['personal_use_paid_treatments_arr']);
            $resultByCont['personal_use_unpaid_treatments'] = implode('<br>', $resultByCont['personal_use_unpaid_treatments_arr']);
        }

        $finalRes = array_values($resultsByContract);

        // Добавено е за да може да се симулира някакъв pagination,
        // въпреки че е изключително неправилно място, защото се обработват всички данни
        // и накрая се връщат само част от тях
        // TODO: Трябва да се намери по-подходящ начин за разделяне на резултатите по страници,
        // за да не се обработва всичко налично, а само тези които ще се визуализират.
        $finalRes = array_slice($finalRes, ($page - 1) * $rows, ($page - 1) * $rows + $rows);

        // Format Personal use totals
        foreach ($totalPersonalUse as $totalKey => $totalValue) {
            $totalPersonalUse[$totalKey]['personal_use_renta'] = number_format($totalValue['personal_use_renta'], 3, '.', '');
            $totalPersonalUse[$totalKey]['paid_amount'] = number_format($totalValue['paid_amount'], 3, '.', '');
            $totalPersonalUse[$totalKey]['unpaid_amount'] = number_format($totalValue['unpaid_amount'], 3, '.', '');
            $totalPersonalUse[$totalKey]['personal_use_treatments_sum'] = number_format($totalValue['personal_use_treatments_sum'], 2, '.', '');
            $totalPersonalUse[$totalKey]['personal_use_paid_treatments'] = number_format($totalValue['personal_use_paid_treatments'], 2, '.', '');
            $totalPersonalUse[$totalKey]['personal_use_unpaid_treatments'] = number_format($totalValue['personal_use_unpaid_treatments'], 2, '.', '');
        }

        return [
            'footer' => [
                [
                    'rep_names' => '<b>ОБЩО за стр.</b>',
                    'pu_area' => number_format($total_pu_area, 3, '.', ''),
                    'owned_area' => number_format($total_owned_area, 3, '.', ''),
                    'area' => number_format($total_area, 3, '.', ''),
                    'renta' => number_format($total_renta, 2, '.', ''),
                    'charged_renta' => number_format($total_charged_renta, 2, '.', ''),
                    'paid_renta' => number_format($total_paid_renta, 2, '.', ''),
                    'unpaid_renta' => number_format($total_unpaid_renta, 2, '.', ''),
                    'over_paid' => number_format($total_over_paid, 2, '.', ''),
                    'total_by_renta' => number_format($total_sum_by_paid_renta, 2, '.', ''),
                    'personal_use_nat_types_names' => implode('</br>', array_column($totalPersonalUse, 'name')),
                    'personal_use_amount' => implode('</br>', array_column($totalPersonalUse, 'personal_use_renta')),
                    'personal_use_paid' => implode('</br>', array_column($totalPersonalUse, 'paid_amount')),
                    'personal_use_unpaid' => implode('</br>', array_column($totalPersonalUse, 'unpaid_amount')),
                    'personal_use_treatments_sum' => implode('</br>', array_column($totalPersonalUse, 'personal_use_treatments_sum')),
                    'personal_use_paid_treatments' => implode('</br>', array_column($totalPersonalUse, 'personal_use_paid_treatments')),
                    'personal_use_unpaid_treatments' => implode('</br>', array_column($totalPersonalUse, 'personal_use_unpaid_treatments')),
                ],
            ],
            'rows' => $finalRes,
            'total' => count($rres),
        ];
    }

    public function unpaidRentByOwner(array $requestParams)
    {
        $paymentsController = new PaymentsController($this->User->Database);
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);
        $farmingYears = $UserDbOwnersController->getPreviousFarmYearsForOwner($requestParams['owner_id'], $requestParams['year']);

        $response = [];
        foreach ($farmingYears as $farmingYear) {
            $farmYear = array_filter($GLOBALS['Farming']['years'], function ($year) use ($farmingYear) {
                return ($year['start_date'] == $farmingYear['farm_year_start'] && $year['end_date'] == $farmingYear['farm_year_end']);
            });

            $farmYear = array_pop($farmYear);
            $result = $paymentsController->getOwnerPayments($farmYear['id'], $requestParams['owner_id']);

            if (!$result['footer'][0]['unpaid_renta'] || '0.00' == $result['footer'][0]['unpaid_renta']) {
                continue;
            }

            $response[] = [
                'farming_year' => $farmYear['farming_year_short'],
                'unpaid_rent' => BGNtoEURO($result['footer'][0]['unpaid_renta']),
            ];
        }

        return ['rows' => $response, 'total' => count($response)];
    }

    /**
     * returns the information about a single contract.
     *
     * @param array $ownerParams{
     *                            #item array results
     *                            }
     * @param array $farming_years{
     *                              #item string year
     *                              }
     * @param string $start_date
     * @param string $due_date
     * @param string $farming_years_string
     * @param array $personalUse
     *
     * @return array
     *               #item int owner_id
     *               #item boolean is_dead
     *               #item string iban
     *               #item int contract_id
     *               #item int contract_parent_id
     *               #item int contract_parent_id
     *               #item string c_num
     *               #item boolean is_annex
     *               #item string timespan
     *               #item string type
     *               #item string charged_renta
     *               #item string charged_renta_nat
     *               #item int converted_renta_nat
     *               #item array renta_nat_type_id
     *               #item string contract_renta
     *               #item string contract_renta_nat
     *               #item string owner_names
     *               #item string owner_area
     *               #item string owner_egn
     *               #item string paid_renta
     *               #item string charged_renta_area
     *               #item string renta
     *               #item array renta_nat
     *               #item float unpaid_renta
     *               #item array paid_renta_nat
     *               #item array unpaid_renta_nat
     *               #item string renta_nat_type
     *               #item array nat_type_ids
     *               #item int id
     *               #item array renta_types
     *               #item string iconCls
     *               #item boolean is_heritor
     *               #item int level
     */
    private function getContractOwnerProperties($ownerParams, $farming_years, $start_date, $due_date, $farming_years_string, $personalUse)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);
        $UsersController = new UsersController('Users');
        $UserDbAreaTypesController = new UserDbAreaTypesController($this->User->Database);
        $UserDbPlotCategoriesTypeController = new UserDbPlotCategoriesTypeController($this->User->Database);

        $results = $ownerParams['results'];
        $resultsCount = count($results);
        for ($i = 0; $i < $resultsCount; $i++) {
            $pca_elements = explode(',', trim($results[$i]['pcrel'], '{}'));
            $pca_elements_gid = explode(',', trim($results[$i]['pcrel_gid'], '{}'));
            $contracts_plots_renta = explode(',', trim($results[$i]['contracts_plots_renta'], '{}'));
            $contracts_plots_charged_renta = explode(',', trim($results[$i]['contracts_plots_charged_renta'], '{}'));
            $contracts_rent_per_plot = explode(',', trim($results[$i]['contracts_rent_per_plot'], '{}'));
            // $results[$i]['pc_rel_ids'] = explode(',', $results[$i]['pc_rel_ids']);

            $plots_percent = explode(',', trim($results[$i]['plots_percent'], '{}'));

            foreach ($plots_percent as $element) {
                $launch = explode('|', $element);

                $results[$i]['owner_plots_percent'][$launch[0]][$launch[1]] = $launch[2];
            }

            foreach ($pca_elements as $element) {
                $launch = explode('|', trim($element, '"'));

                $puArea = $UserDbPaymentsController->getPersonalUseArea($personalUse, $results[$i]['owner_id'], ['contract_id' => $launch[5], 'pc_rel_id' => $launch[4]], $results[$i]['area']);
                $results[$i]['plots_contracts_area_array'][] = [
                    'pc_id' => $launch[0],
                    'c_num' => $launch[1],
                    'plot_name' => $launch[2],
                    'area' => $launch[3] - $puArea,
                    'pc_rel_id' => $launch[4],
                    'contract_id' => $launch[5],
                    'plot_gid' => $launch[6],
                    'c_renta' => $launch[7],
                    'rent_per_plot' => $launch[8],
                    'pu_area' => $puArea,
                ];
            }

            foreach ($pca_elements_gid as $element) {
                $launch = explode('|', $element);
                $puArea = $UserDbPaymentsController->getPersonalUseArea($personalUse, $results[$i]['owner_id'], ['contract_id' => $launch[0], 'pc_rel_id' => $launch[3]], $results[$i]['area']);

                $results[$i]['plots_contracts_area_array_gids'][] = [
                    'pc_id' => $launch[0],
                    'plot_gid' => $launch[1],
                    'area' => $launch[2] - $puArea,
                    'pc_rel_id' => $launch[3],
                    'pu_area' => $puArea,
                ];
            }

            foreach ($contracts_rent_per_plot as $element) {
                $launch = explode('|', $element);

                $results[$i]['plots_contracts_rent_per_plot'][] = [
                    'renta_by_plot' => $launch[0],
                    'contract_id' => $launch[1],
                    'plot_gid' => $launch[2],
                ];
            }

            $plots_contracts_charged_renta = [];

            foreach ($contracts_plots_charged_renta as $element) {
                if ('NULL' == $element) {
                    continue;
                }

                $launch = explode('|', $element);

                $filteredPlotsWithRentPerPlot = array_filter($results[$i]['plots_contracts_rent_per_plot'], function ($elem) use ($launch) {
                    return $elem['plot_gid'] == $launch[2] && $elem['contract_id'] == $launch[1];
                });

                if (!empty($filteredPlotsWithRentPerPlot)) {
                    $RentPerPlot = reset($filteredPlotsWithRentPerPlot);
                    $results[$i]['plots_contracts_rent_per_plot'][key($filteredPlotsWithRentPerPlot)]['has_charged_renta'] = true;
                    $launch[0] = $RentPerPlot['renta_by_plot'];

                    $results[$i]['charged_renta'] += $RentPerPlot['renta_by_plot'];
                }

                $results[$i]['plots_contracts_charged_renta'][] = [
                    'charged_renta_by_plot' => $launch[0],
                    'contract_id' => $launch[1],
                    'plot_gid' => $launch[2],
                ];

                $puArea = $UserDbPaymentsController->getPersonalUseArea($personalUse, $results[$i]['owner_id'], ['contract_id' => $launch[1], 'pc_rel_id' => $launch[3]], $results[$i]['area']);

                // The array above is reinit, so in order to keep the values we need to store them in a new array
                $results[$i]['plots_contracts_charged_renta_arr'][] = [
                    'charged_renta_by_plot' => $launch[0],
                    'contract_id' => $launch[1],
                    'plot_gid' => $launch[2],
                    'rel_id' => $launch[3],
                    'area' => $launch[4] - $puArea,
                ];

                $results[$i]['plots_contracts_charged_renta_values'][$launch[1]][$launch[2]] = $launch[5];

                $plots_contracts_charged_renta[$launch[1]][$launch[2]] = $launch[0];
            }

            foreach ($contracts_plots_renta as $element) {
                $launch = explode('|', $element);
                $filteredPlotsWithRentPerPlot = array_filter($results[$i]['plots_contracts_rent_per_plot'], function ($elem) use ($launch) {
                    return $elem['plot_gid'] == $launch[2] && $elem['contract_id'] == $launch[1];
                });

                if (!empty($filteredPlotsWithRentPerPlot)) {
                    $RentPerPlot = reset($filteredPlotsWithRentPerPlot);

                    if (true != $results[$i]['plots_contracts_rent_per_plot'][key($filteredPlotsWithRentPerPlot)]['has_charged_renta']) {
                        $launch[0] = $RentPerPlot['renta_by_plot'];
                    }
                }

                $puArea = $UserDbPaymentsController->getPersonalUseArea($personalUse, $results[$i]['owner_id'], ['contract_id' => $launch[1], 'pc_rel_id' => $launch[3]], $results[$i]['area']);

                $results[$i]['plots_contracts_renta'][] = [
                    'renta_by_plot' => ($launch[4] - $puArea) * $launch[5],
                    'contract_id' => $launch[1],
                    'plot_gid' => $launch[2],
                    'pc_rel_id' => $launch[3],
                    'area' => $launch[4] - $puArea,
                    'c_renta' => $launch[5],
                ];
            }

            $contractPlotArea = [];
            $contractPlotsAreaSum = [];
            if (!empty($results[$i]['plots_contracts_area_array_gids'])) {
                foreach ($results[$i]['plots_contracts_area_array_gids'] as $key => $value) {
                    $contractId = $value['pc_id'];
                    $plotGid = $value['plot_gid'];
                    $areaPlot = $value['area'];

                    $contractPlotArea[$contractId][$plotGid] = $areaPlot;
                    $contractPlotsAreaSum[$contractId] += $areaPlot;
                }
            }

            $plotsStr = trim($results[$i]['plots_array'], '{}');

            $charged_renta_nat_id = explode(',', trim($results[$i]['charged_renta_id'], '{}'));
            $charged_renta_nat_id = array_unique($charged_renta_nat_id);
            $charged_renta_ids_text = implode(',', $charged_renta_nat_id);

            $contracts_data = [];
            $contr_area_array = explode(',', trim($results[$i]['contr_area'], '{}'));
            $contr_area_array = array_sum($contr_area_array);
            $contract_array = explode(',', trim($results[$i]['contract_array'], '{}'));
            $nm_usage_rights = explode(',', trim($results[$i]['nm_usage_rights'], '{}'));
            $renta_nat_array = explode(',', trim($results[$i]['renta_nat'], '{}'));
            // $owners_pu_renta_nat_array = explode(",", trim($results[$i]['owners_pu_renta_nat'], '{}'));
            $charged_renta_nat_array = explode(',', trim($results[$i]['charged_renta_nat'], '{}'));
            $renta_nat_type_id_array = explode(',', trim($results[$i]['renta_nat_type_id'], '{}'));
            $results[$i]['c_num_array'] = explode(',', trim($results[$i]['c_num_array'], '{}'));
            $results[$i]['annex_ids_array'] = explode(',', trim($results[$i]['annex_ids_array'], '{}'));
            $results[$i]['plots_name_array'] = explode(',', trim($results[$i]['plots_name_array'], '{}'));
            $implodedArr = implode($contract_array, ',');
            $contracts_anex_arr = trim($results[$i]['contract_anex_arr'], '{}');

            $results[$i]['contract_id'] = implode(array_unique($contract_array), ',');
            $results[$i]['type'] = implode(array_unique($nm_usage_rights), ',');
            $results[$i]['type_id'] = implode(array_unique($nm_usage_rights), ',');
            $results[$i]['type'] = $GLOBALS['Contracts']['ContractTypes'][$results[$i]['type']]['name'];
            $results[$i]['c_num'] = implode(',', array_unique($results[$i]['c_num_array']));
            $results[$i]['farming_id'] = $ownerParams['results'][$i]['farming_id'];

            $pathOwner = $results[$i]['owner_id'] . '.*{1}';

            $options = [
                'return' => [
                    'h.id',
                ],
                'where' => [
                    'path' => ['column' => 'path', 'compare' => '~', 'value' => $pathOwner],
                ],
            ];

            $paidOptions = [
                'custom_counter' => 'COUNT(DISTINCT(p.owner_id))',
                'order' => 'asc',
                'sort' => 'p.contract_id',
                'return' => [
                    'p.id as payment_id',
                    'p.owner_id as owner_id',
                    'p.contract_id as contract_id',
                    'case when pn.amount notnull and pn.unit_value notnull  then round((pn.amount * pn.unit_value)::numeric, 2) else round(p.amount::numeric, 2) end as trans_amount',
                    'case when pn.amount notnull then round(pn.amount::numeric,3) else round(p.amount_nat::numeric, 3) end as amount_nat',
                    'round(pn.amount::numeric, 3) as trans_amount_nat',
                    'round(pn.unit_value::numeric, 2) as unit_value',
                    'pn.nat_type as nat_type',
                    'p.paid_in',
                    'p.paid_from',
                    'rent.name as trans_nat_type_text',
                ],
                'where' => [
                    'owner_id' => ['column' => 'owner_id', 'compare' => '=', 'prefix' => 'p', 'value' => $results[$i]['owner_id']],
                    'path' => ['column' => 'path', 'compare' => 'IS', 'prefix' => 'p', 'value' => 'NULL'],
                    'contract_id' => ['column' => 'contract_id', 'compare' => 'IN', 'prefix' => 'p', 'value' => $contract_array],
                    'farming_year' => ['column' => 'farming_year', 'compare' => 'IN', 'prefix' => 'p', 'value' => $farming_years],
                ],
            ];

            $paidResults = $UserDbPaymentsController->getPaidData($paidOptions, false, false);

            $results[$i] = $this->addNewRentaPayrolls($paidResults, $results[$i]);

            $paid_renta = 0;
            $paid_renta_by_nat = 0;
            $paid_renta_nat = [];
            $paid_renta_nat_by_nat = [];
            $paid_renta_nat_by_detailed = [];
            $paid_renta_nat_by_detailed_unit_value = [];
            $paymentIds = [];
            $paidRentaByContract = [];
            $paidRentaNatByContract = [];
            $paidResCount = count($paidResults);
            for ($m = 0; $m < $paidResCount; $m++) {
                $paidResult = $paidResults[$m];
                $renta_type = $paidResult['nat_type'];
                $contract_id = $paidResult['contract_id'];

                if (1 == $paidResult['paid_from']) {
                    $paidRentaByContract[$contract_id] += $paidResult['trans_amount'];
                    if (2 == $paidResult['paid_in']) {
                        $paid_renta_nat[$renta_type] += $paidResult['amount_nat'];

                        continue;
                    }

                    $paid_renta += $paidResult['trans_amount'];
                } elseif (2 == $paidResult['paid_from']) {
                    $paidRentaNatByContract[$contract_id][$renta_type] += $paidResult['trans_amount_nat'];

                    if (2 == $paidResult['paid_in']) {
                        $paid_renta_nat_by_nat[$renta_type] += $paidResult['trans_amount_nat'];

                        continue;
                    }

                    if (array_key_exists($renta_type, $paid_renta_nat_by_detailed)) {
                        $paid_renta_nat_by_detailed[$renta_type] += $paidResult['trans_amount_nat'];
                    } else {
                        $paid_renta_nat_by_detailed[$renta_type] = $paidResult['trans_amount_nat'];
                        $paid_renta_nat_by_detailed_unit_value[$renta_type] = $paidResult['unit_value'];
                    }

                    $paymentIds[] = $paidResult['payment_id'];
                    $paid_renta_by_nat += $paidResult['trans_amount'];
                }
            }

            $paidRentaByPlot = [];
            if (!empty($contractPlotArea)) {
                foreach ($contractPlotArea as $contractId => $plot) {
                    foreach ($plot as $plotGid => $area) {
                        $totalArea = $contractPlotsAreaSum[$contractId];

                        $areaPercent = 0;
                        if ($area > 0) {
                            $areaPercent = $area / $totalArea;
                        }

                        $paidRentaByContractSum = $paidRentaByContract[$contractId];
                        $paidRentaByPlots = $paidRentaByContractSum * $areaPercent;

                        $paidRentaByPlot[$contractId][$plotGid] = $paidRentaByPlots;
                    }
                }
            }

            $results[$i]['paid_renta_by_contract'] = $paidRentaByContract;
            $results[$i]['paid_renta_nat_by_contract'] = $paidRentaNatByContract;

            // Начислена рента в натура и конвертирана от натура в лева
            $charged_rents = $UserDbController->DbHandler->getDataByQuery(
                "SELECT 
                    (CASE WHEN crn.nat_is_converted = TRUE THEN NULL ELSE crn.amount * ((pc.area_for_rent * po.percent / 100))  END) AS charged_renta_nat,
                    c.id as contract_id, 
                    kvs.gid as plot_id, 
                    (CASE WHEN crn.nat_is_converted = TRUE THEN NULL ELSE crn.amount END) AS charged_renta_nat_value,
                    (pc.area_for_rent * po.percent / 100) as area_for_rent,
                    pc.id as pc_rel_id,
                    po.owner_id, 
                    crn.nat_type,
                    (CASE WHEN crn.nat_is_converted = TRUE THEN (crn.amount * crn.nat_unit_price) * ((pc.area_for_rent * po.percent / 100)) ELSE NULL END) AS converted_charged_renta_nat,  
                    (CASE WHEN crn.nat_is_converted = TRUE THEN (crn.amount * crn.nat_unit_price) ELSE NULL END) AS converted_charged_renta_nat_value
                FROM su_contracts c
                LEFT JOIN su_contracts a ON(a.parent_id = c.id
                                            AND a.active = TRUE
                                            AND a.start_date <= '{$start_date}'
                                            AND a.due_date >= '{$due_date}')
                INNER JOIN su_contracts_plots_rel pc ON(pc.contract_id = (CASE WHEN a.id IS NULL THEN c.id ELSE a.id END))
                INNER JOIN layer_kvs kvs ON(kvs.gid = pc.plot_id)
                LEFT JOIN su_plots_owners_rel po ON(po.pc_rel_id = pc.id)
                LEFT JOIN su_personal_use pu ON (pu.owner_id = {$results[$i]['owner_id']} AND pu.year in {$farming_years_string} AND pu.pc_rel_id = pc.id)
                LEFT JOIN su_charged_renta cr ON(cr.plot_id = kvs.gid
                                                 AND cr.contract_id = c.id
                                                 AND cr.YEAR in {$farming_years_string}
                                                 AND cr.owner_id = {$results[$i]['owner_id']})
                LEFT JOIN su_charged_renta_natura crn ON(crn.renta_id = cr.id)
                where c.id in ({$implodedArr})
                and kvs.gid in ({$plotsStr})
                and pc.annex_action ='added'
                and po.owner_id = {$results[$i]['owner_id']}
                and cr.owner_id = {$results[$i]['owner_id']}"
            );

            // По договор рента в натура
            $renta_nats = $UserDbController->DbHandler->getDataByQuery(
                "SELECT 
                    crt.renta_value * ((pc.area_for_rent * po.percent / 100)) AS renta_nat, 
                    kvs.gid as plot_id, 
                    (pc.area_for_rent * po.percent / 100) as area_for_rent,
                    crt.renta_value,
                    pc.id as pc_rel_id,
                    po.percent, 
                    c.id as contract_id, 
                    parent_id, 
                    po.owner_id, 
                    crt.renta_id 
                FROM su_contracts c
                INNER JOIN su_contracts_plots_rel pc ON(pc.contract_id = c.id)
                INNER JOIN layer_kvs kvs ON(kvs.gid = pc.plot_id)
                LEFT JOIN su_plots_owners_rel po ON(po.pc_rel_id = pc.id)
                LEFT JOIN su_personal_use pu ON (pu.owner_id = {$results[$i]['owner_id']} AND pu.year in {$farming_years_string} AND pu.pc_rel_id = pc.id)
                LEFT join su_contracts_rents crt ON (c.id = crt.contract_id) 
                where c.id in ({$contracts_anex_arr})
                and kvs.gid in ({$plotsStr})
                and pc.annex_action ='added'
                and po.owner_id = {$results[$i]['owner_id']}
                and pc.rent_per_plot isnull"
            );

            $contract_rents = [];
            $charged_rents_arr = [];
            $rentaNatByPlot = [];
            foreach ($renta_nats as $renta_nat) {
                $puArea = $UserDbPaymentsController->getPersonalUseArea($personalUse, $results[$i]['owner_id'], $renta_nat, $results[$i]['area']);
                $rentaNat = ($renta_nat['area_for_rent'] - $puArea) * $renta_nat['renta_value'];

                if (!isset($contract_rents[$renta_nat['renta_id']])) {
                    $contract_rents[$renta_nat['renta_id']] = $rentaNat;
                } else {
                    $contract_rents[$renta_nat['renta_id']] += $rentaNat;
                }

                $contractID = $renta_nat['contract_id'];

                if ($renta_nat['parent_id']) {
                    $contractID = $renta_nat['parent_id'];
                }

                $plotID = $renta_nat['plot_id'];
                $rentaNatID = $renta_nat['renta_id'];

                if ($rentaNatID) {
                    $rentaNatByPlot[$contractID][$plotID][$rentaNatID] = $rentaNat;
                }
            }

            $convertedChargedRentaByPlot = [];
            $chargedRentaNaturaByPlot = [];
            $chargedRentaNaturaByPlotValues = [];
            $results[$i]['converted_renta_nat'];
            $results[$i]['plots_contracts_charged_renta_nat_down_grid'] = [];

            foreach ($charged_rents as $charged_rent) {
                $puArea = $UserDbPaymentsController->getPersonalUseArea($personalUse, $results[$i]['owner_id'], $charged_rent, $results[$i]['area']);
                $chargedRentaNatArea = round(($charged_rent['area_for_rent'] - $puArea), 3);
                $chargedRentaNat = $chargedRentaNatArea * $charged_rent['charged_renta_nat_value'];
                if (!isset($charged_rents_arr[$charged_rent['nat_type']])) {
                    $charged_rents_arr[$charged_rent['nat_type']] = $chargedRentaNat;
                    $results[$i]['plots_contracts_charged_renta_values'][$charged_rent['nat_type']] = $charged_rent['charged_renta_nat_value'];
                } else {
                    $charged_rents_arr[$charged_rent['nat_type']] += $chargedRentaNat;
                }

                $contractID = $charged_rent['contract_id'];
                $plotID = $charged_rent['plot_id'];
                $naturaType = $charged_rent['nat_type'];

                if (!is_null($charged_rent['converted_charged_renta_nat_value']) && $convertedChargedRenta >= 0) {
                    $convertedChargedRenta = $charged_rent['converted_charged_renta_nat_value'] * $chargedRentaNatArea;
                    $results[$i]['converted_renta_nat'] += $convertedChargedRenta;

                    $convertedChargedRentaByPlot[$contractID][$plotID] += $convertedChargedRenta;
                }

                if ($naturaType && !is_null($chargedRentaNat)) {
                    $chargedRentaNaturaByPlot[$contractID][$plotID][$naturaType] = $chargedRentaNat;
                    $chargedRentaNaturaByPlotValues[$contractID][$plotID][$naturaType] = $charged_rent['charged_renta_nat_value'];

                    $results[$i]['plots_contracts_charged_renta_nat_down_grid'][$contractID][$plotID][$naturaType] = $chargedRentaNat;
                }
            }

            $sumChargedRentaNaturaByContract = [];
            $sumRentaNatByContract = [];
            $sumDueRentaNat = [];
            $results[$i]['plots_contracts_renta_nat_down_grid'] = [];

            if (!empty($rentaNatByPlot)) {
                foreach ($rentaNatByPlot as $contractID => $rentaValue) {
                    foreach ($rentaValue as $plotID => $value) {
                        foreach ($value as $rentaType => $naturaValue) {
                            if (is_null($chargedRentaNaturaByPlot[$contractID][$plotID][$rentaType])) {
                                $sumDueRentaNat[$rentaType] += $naturaValue;

                                $results[$i]['plots_contracts_renta_nat_down_grid'][$contractID][$plotID][$rentaType] = $naturaValue;
                            } else {
                                if (is_null($sumDueRentaNat[$rentaType])) {
                                    $sumDueRentaNat[$rentaType] = '0.000';
                                }
                            }

                            $sumRentaNatByContract[$contractID][$rentaType] += $naturaValue;
                        }
                    }
                }
            }

            ksort($sumDueRentaNat);

            $results[$i]['plots_contracts_due_renta_nat'] = $rentaNatByPlot;

            if (!empty($chargedRentaNaturaByPlot)) {
                foreach ($chargedRentaNaturaByPlot as $contractID => $chargedValue) {
                    foreach ($chargedValue as $plotID => $value) {
                        foreach ($value as $rentaType => $naturaValue) {
                            $rentaNatByPlot[$contractID][$plotID][$rentaType] = $naturaValue;

                            $sumChargedRentaNaturaByContract[$contractID][$rentaType] += $naturaValue;
                        }
                    }
                }
            }

            $results[$i]['plots_contracts_renta_nat'] = $rentaNatByPlot;
            $results[$i]['plots_contracts_charged_renta_nat'] = $chargedRentaNaturaByPlot;
            $results[$i]['plots_contracts_charged_renta_nat_values'] = $chargedRentaNaturaByPlotValues;

            $merged_renta_nat_array = [];
            foreach ($contract_rents as $key => $rent) {
                if (!is_null($rent)) {
                    $merged_renta_nat_array[$key]['renta_nat'] = $rent;
                }
            }

            foreach ($charged_rents_arr as $key => $rent) {
                if (!is_null($rent)) {
                    $merged_renta_nat_array[$key]['charged_renta_nat'] = $rent;
                }
            }
            ksort($merged_renta_nat_array);

            foreach ($merged_renta_nat_array as $rentaNatTypeID => $rentaNat) {
                if (null != $this->renta_types[$rentaNatTypeID]) {
                    if (!is_null($rentaNat['charged_renta_nat']) && $rentaNat['charged_renta_nat'] >= 0) {
                        $results[$i]['charged_renta_nat_text'] .= number_format($rentaNat['charged_renta_nat'], 3, '.', '') . '<br/>';
                        $results[$i]['charged_renta_nat'][$rentaNatTypeID] = number_format($rentaNat['charged_renta_nat'], 3, '.', '');
                    } else {
                        $results[$i]['charged_renta_nat_text'] .= '-<br/>';
                        $results[$i]['charged_renta_nat'][$rentaNatTypeID] = '-';
                    }
                }
            }

            $results[$i]['nat_type_ids'] = [];
            foreach ($sumDueRentaNat as $rentaNatTypeID => $rentaNat) {
                $rentaNat = number_format($rentaNat, 3, '.', '');

                $results[$i]['renta_nat_text'] .= $rentaNat . '<br/>';
                $results[$i]['renta_nat'][$rentaNatTypeID] = $rentaNat;

                $results[$i]['nat_type_ids'][] = $rentaNatTypeID;
            }
            $results[$i]['renta_nat_type_id'] = $results[$i]['nat_type_ids'];

            $results[$i]['total_by_renta'] = '-';
            $results[$i]['total_by_renta_nat'] = '-';

            if (!$results[$i]['renta_nat_text']) {
                $results[$i]['renta_nat_text'] = '-';
            } else {
                $results[$i]['renta_nat_text'] = rtrim($results[$i]['renta_nat_text'], '<br/>');
            }

            if (!$results[$i]['charged_renta_nat_text']) {
                $results[$i]['charged_renta_nat_text'] = '-';
            } else {
                $results[$i]['charged_renta_nat_text'] = rtrim($results[$i]['charged_renta_nat_text'], '<br/>');
            }

            $UserDbAreaTypesController = new UserDbAreaTypesController($this->User->Database);
            $results[$i]['land'] = $UsersController->getEkatteName($results[$i]['rent_place']);
            $results[$i]['area_type'] = $UserDbAreaTypesController->getNtpTitle($results[$i]['area_type']);

            if (!$results[$i]['mestnost']) {
                $results[$i]['mestnost'] = '-';
            }

            if (!$results[$i]['category']) {
                $results[$i]['category'] = '-';
            } else {
                $results[$i]['category'] = $UserDbPlotCategoriesTypeController->getPlotCategoryTitle($results[$i]['category']);
            }

            $results[$i]['area'] = number_format($results[$i]['area'], 3, '.', '');
            $results[$i]['owned_area'] = number_format(($results[$i]['area'] + $results[$i]['pu_area']), 3, '.', '');
            $results[$i]['paid_renta'] = number_format($results[$i]['paid_renta'], 2, '.', '');

            if ((
                !is_null($results[$i]['charged_renta']) && $results[$i]['charged_renta'] >= 0
            )
                || (!is_null($results[$i]['converted_renta_nat']) && $results[$i]['converted_renta_nat'] >= 0)
            ) {
                $results[$i]['charged_renta'] = number_format($results[$i]['charged_renta'] + $results[$i]['converted_renta_nat'], 2, '.', '');
            } else {
                $results[$i]['charged_renta'] = '-';
            }

            if (0 == $results[$i]['paid_renta']) {
                $results[$i]['paid_renta'] = null;
            }

            if (!empty($results[$i]['plots_contracts_renta'])) {
                if (!empty($convertedChargedRentaByPlot) || !empty($plots_contracts_charged_renta)) {
                    // foreach ($convertedChargedRentaByPlot as $contractId => $convertedValue) {
                    //     foreach ($convertedValue as $plotId => $convertedChargedRentaNatura) {
                    //         if ($plots_contracts_charged_renta[$contractId] && $plots_contracts_charged_renta[$contractId][$plotId]) {
                    //             $plots_contracts_charged_renta[$contractId][$plotId] += $convertedChargedRentaNatura;
                    //         } elseif ($plots_contracts_charged_renta[$contractId] && !$plots_contracts_charged_renta[$contractId][$plotId]) {
                    //             $plots_contracts_charged_renta[$contractId][$plotId] = $convertedChargedRentaNatura;
                    //         } elseif (!$plots_contracts_charged_renta[$contractId] && $plots_contracts_charged_renta[$contractId][$plotId]) {
                    //             $plots_contracts_charged_renta[$contractId][$plotId] += $convertedChargedRentaNatura;
                    //         } elseif (!$plots_contracts_charged_renta[$contractId] && !$plots_contracts_charged_renta[$contractId][$plotId]) {
                    //             $plots_contracts_charged_renta[$contractId][$plotId] = $convertedChargedRentaNatura;
                    //         }
                    //     }
                    // }

                    $results[$i]['plots_contracts_charged_renta'] = [];

                    if ($plots_contracts_charged_renta) {
                        foreach ($plots_contracts_charged_renta as $contractID => $value) {
                            foreach ($value as $plotID => $chargedValue) {
                                $results[$i]['plots_contracts_charged_renta'][] = [
                                    'contract_id' => $contractID,
                                    'plot_gid' => $plotID,
                                    'charged_renta_by_plot' => $chargedValue,
                                ];
                            }
                        }
                    }
                }

                $plotsContractsRenta = $results[$i]['plots_contracts_renta'];
                $plotsContractsChargedRenta = $results[$i]['plots_contracts_charged_renta'];
                $unpaidRenta = 0;
                $sumContractsRenta = [];
                $sumContractsChargedRenta = [];
                $sumContractsDueRenta = [];
                $results[$i]['plots_contracts_charged_renta_down_grid'] = [];
                $results[$i]['plots_contracts_renta_down_grid'] = [];

                if (!empty($plotsContractsChargedRenta)) {
                    foreach ($plotsContractsRenta as $rentaKey => $rentaValue) {
                        foreach ($plotsContractsChargedRenta as $chargedRentaKey => $chargedRentaValue) {
                            $isCharged = false;

                            if ($chargedRentaValue['contract_id'] == $rentaValue['contract_id'] && $chargedRentaValue['plot_gid'] == $rentaValue['plot_gid'] && !is_null($chargedRentaValue['charged_renta_by_plot'])) {
                                $results[$i]['plots_contracts_renta'][$rentaKey]['renta_by_plot'] = $chargedRentaValue['charged_renta_by_plot'];
                                $results[$i]['plots_contracts_charged_renta_down_grid'][$chargedRentaValue['contract_id']][$chargedRentaValue['plot_gid']] = $chargedRentaValue['charged_renta_by_plot'];

                                $sumContractsChargedRenta[$chargedRentaValue['contract_id']] += $chargedRentaValue['charged_renta_by_plot'];
                                $isCharged = true;

                                break;
                            }
                        }

                        if (!$isCharged) {
                            $sumContractsDueRenta[$rentaValue['contract_id']] += $rentaValue['renta_by_plot'];

                            $results[$i]['plots_contracts_renta_down_grid'][$rentaValue['contract_id']][$rentaValue['plot_gid']] = $rentaValue['renta_by_plot'];
                        }

                        $sumContractsRenta[$rentaValue['contract_id']] += $rentaValue['renta_by_plot'];
                    }
                } else {
                    foreach ($plotsContractsRenta as $rentaKey => $rentaValue) {
                        $sumContractsDueRenta[$rentaValue['contract_id']] += $rentaValue['renta_by_plot'];
                        $results[$i]['plots_contracts_renta_down_grid'][$rentaValue['contract_id']][$rentaValue['plot_gid']] = $rentaValue['renta_by_plot'];
                    }
                }

                if (!empty($convertedChargedRentaByPlot)) {
                    foreach ($plotsContractsRenta as $rentaKey => $rentaValue) {
                        foreach ($convertedChargedRentaByPlot as $chargedRentaKey => $chargedRentaValue) {
                            $isCharged = false;
                            if (!empty($convertedChargedRentaByPlot[$rentaValue['contract_id']][$rentaValue['plot_gid']])) {
                                $results[$i]['plots_contracts_renta'][$rentaKey]['renta_by_plot'] = $chargedRentaValue[$rentaValue['plot_gid']];
                                $results[$i]['plots_contracts_charged_renta_down_grid'][$rentaValue['contract_id']][$rentaValue['plot_gid']] = $chargedRentaValue[$rentaValue['plot_gid']];
                                $results[$i]['plots_contracts_converted_charged_renta'][$rentaValue['contract_id']][$rentaValue['plot_gid']] = $chargedRentaValue[$rentaValue['plot_gid']];

                                $sumContractsChargedRenta[$rentaValue['contract_id']] += $chargedRentaValue[$rentaValue['plot_gid']];
                                $isCharged = true;

                                break;
                            }
                        }

                        if (!$isCharged) {
                            $sumContractsDueRenta[$rentaValue['contract_id']] += $rentaValue['renta_by_plot'];

                            $results[$i]['plots_contracts_renta_down_grid'][$rentaValue['contract_id']][$rentaValue['plot_gid']] = $rentaValue['renta_by_plot'];
                        }

                        $sumContractsRenta[$rentaValue['contract_id']] += $rentaValue['renta_by_plot'];
                    }
                } else {
                    foreach ($plotsContractsRenta as $rentaKey => $rentaValue) {
                        $sumContractsDueRenta[$rentaValue['contract_id']] += $rentaValue['renta_by_plot'];
                        $results[$i]['plots_contracts_renta_down_grid'][$rentaValue['contract_id']][$rentaValue['plot_gid']] = $rentaValue['renta_by_plot'];
                    }
                }

                $charged_rent = 0;
                $renta = 0;
                foreach ($results[$i]['plots_contracts_area_array'] as $rentaKey => $rentaValue) {
                    $contractID = $rentaValue['contract_id'];
                    $plotID = $rentaValue['plot_gid'];
                    $results[$i]['plots_contracts_area_array'][$rentaKey]['charged_renta'] = 0;
                    $results[$i]['plots_contracts_area_array'][$rentaKey]['renta'] = 0;

                    if ($rentaValue['rent_per_plot'] > 0) {
                        $rent_per_plot = $rentaValue['rent_per_plot'] * $rentaValue['area'];
                        $results[$i]['plots_contracts_area_array'][$rentaKey]['renta'] = $rent_per_plot;
                    } elseif (!empty($results[$i]['plots_contracts_charged_renta_values'][$contractID][$plotID])) {
                        $plot_charged_renta = $results[$i]['plots_contracts_charged_renta_values'][$contractID][$plotID] * $rentaValue['area'];
                        $results[$i]['plots_contracts_area_array'][$rentaKey]['charged_renta'] = $plot_charged_renta;
                    } elseif (!empty($results[$i]['plots_contracts_converted_charged_renta'][$contractID][$plotID])) {
                        $plot_converted_charged_renta = $results[$i]['plots_contracts_converted_charged_renta'][$contractID][$plotID];
                        $results[$i]['plots_contracts_area_array'][$rentaKey]['charged_renta'] = $plot_converted_charged_renta;
                    } else {
                        $plot_renta = $rentaValue['c_renta'] * $rentaValue['area'];
                        $results[$i]['plots_contracts_area_array'][$rentaKey]['renta'] = $plot_renta;
                    }

                    $rentaByPlot = $results[$i]['plots_contracts_area_array'][$rentaKey]['renta'] + $results[$i]['plots_contracts_area_array'][$rentaKey]['charged_renta'];

                    if ($paidRentaByPlot[$contractID] && $paidRentaByPlot[$contractID][$plotID]) {
                        $rentaToPaid = $paidRentaByPlot[$contractID][$plotID];
                        $rentaLeft = $rentaByPlot - $rentaToPaid;

                        $unpaidRenta += $rentaLeft;
                    } else {
                        $unpaidRenta += $rentaByPlot;
                    }
                }
                $results[$i]['unpaid_renta'] = ($unpaidRenta < 0) ? '0.00' : number_format($unpaidRenta, 2, '.', '');

                // Надплатено рента в лева
                $results[$i]['over_paid'] = ($unpaidRenta >= 0) ? '0.00' : number_format($unpaidRenta * (-1), 2, '.', '');
            }

            $rentaTypes = [];

            if (!empty($rentaNatByPlot)) {
                // sum natura by renta type
                $sumRentaType = [];
                foreach ($rentaNatByPlot as $contractID => $rentaValue) {
                    foreach ($rentaValue as $plotID => $value) {
                        foreach ($value as $rentaType => $naturaValue) {
                            $sumRentaType[$rentaType] += $naturaValue;
                        }
                    }
                }

                // Сортиране на рентата в натура спрямо renta_type_id
                ksort($sumRentaType);

                $unpaid_renta_nat = [];
                $unpaid_renta_nat_arr = [];
                $over_paid_renta_nat = [];
                $over_paid_renta_nat_arr = [];
                $unpaid_renta_nat_unit_value = [];
                $unpaid_renta_nat_unit_value_arr = [];

                foreach ($sumRentaType as $rentaType => $rentaNatura) {
                    $paidRentaNatura = $results[$i]['paid_renta_nat_details'][$rentaType];
                    $unpaidRentaNatura = $rentaNatura - $paidRentaNatura;
                    $rentaTypes[] = $this->renta_types[$rentaType];

                    $quantity = number_format(($unpaidRentaNatura < 0) ? 0 : $unpaidRentaNatura, 3, '.', '');
                    $quantityOverPaid = number_format(($unpaidRentaNatura >= 0) ? '0.00' : $unpaidRentaNatura * (-1), 3, '.', '');
                    $quantityValue = number_format($quantity * $this->renta_types_values[$rentaType], 2, '.', '');

                    $unpaid_renta_nat_unit_value[] = $quantityValue . ' лв.';
                    $unpaid_renta_nat_unit_value_arr[$rentaType] = $quantityValue;

                    $unpaid_renta_nat[] = $quantity;
                    $unpaid_renta_nat_arr[$rentaType] = $quantity;

                    // Надплатено рента в натура
                    $over_paid_renta_nat[] = $quantityOverPaid;
                    $over_paid_renta_nat_arr[$rentaType] = $quantityOverPaid;
                }

                $results[$i]['unpaid_renta_nat_arr'] = $unpaid_renta_nat_arr;
                $results[$i]['over_paid_renta_nat_arr'] = $over_paid_renta_nat_arr;
                $results[$i]['unpaid_renta_nat_unit_value_arr'] = $unpaid_renta_nat_unit_value_arr;
                $results[$i]['unpaid_renta_nat'] = implode('</br>', $unpaid_renta_nat);
                $results[$i]['over_paid_nat'] = implode('</br>', $over_paid_renta_nat);
                $results[$i]['unpaid_renta_nat_unit_value'] = implode('</br>', $unpaid_renta_nat_unit_value);
                $results[$i]['renta_nat_type'] = implode('</br>', $rentaTypes);
            }

            $paid_renta_by = [];
            $paid_renta_by_arr = [];
            if ($paid_renta) {
                $amount = number_format($paid_renta, 2, '.', '');
                $paid_renta_by[] = $amount . ' лв.';

                $paid_renta_by_arr['amount'] = $amount;
            }
            if (!empty($paid_renta_nat)) {
                foreach ($paid_renta_nat as $key => $value) {
                    $quantity = number_format($value, 3, '.', '');
                    $paid_renta_by[] = $quantity . ' X ' . $this->renta_types[$key];

                    $paid_renta_by_arr['nat_amount'][$key] = $quantity;
                }
            }
            $results[$i]['paid_renta_by_arr'] = $paid_renta_by_arr;
            $results[$i]['paid_renta_by'] = implode('</br>', $paid_renta_by);
            $results[$i]['paid_renta_by'] = '' != $results[$i]['paid_renta_by'] ? $results[$i]['paid_renta_by'] : '-';

            $paid_renta_nat_by = [];
            $paid_renta_nat_by_arr = [];
            if ($paid_renta_by_nat) {
                $amount = number_format($paid_renta_by_nat, 2, '.', '');
                $paid_renta_nat_by[] = $amount . ' лв.';

                $paid_renta_nat_by_arr['amount'] = $amount;
            }
            if (!empty($paid_renta_nat_by_nat)) {
                foreach ($paid_renta_nat_by_nat as $key => $value) {
                    $quantity = number_format($value, 3, '.', '');
                    $paid_renta_nat_by[] = $quantity . ' X ' . $this->renta_types[$key];

                    $paid_renta_nat_by_arr['nat_amount'][$key] = $quantity;
                }
            }
            $results[$i]['paid_renta_nat_by_arr'] = $paid_renta_nat_by_arr;
            $results[$i]['paid_renta_nat_by'] = implode('</br>', $paid_renta_nat_by);
            $results[$i]['paid_renta_nat_by'] = '' != $results[$i]['paid_renta_nat_by'] ? $results[$i]['paid_renta_nat_by'] : '-';

            $all_paid_renta_nat_by_detailed = [];
            $all_paid_renta_nat_by_detailed_quantity_arr = [];
            $all_paid_renta_nat_by_detailed_unit_value_arr = [];
            if (!empty($paid_renta_nat_by_detailed)) {
                foreach ($paid_renta_nat_by_detailed as $key => $value) {
                    $quantity = number_format($value, 3, '.', '');
                    $unitValue = null != $paid_renta_nat_by_detailed_unit_value[$key] ? $paid_renta_nat_by_detailed_unit_value[$key] : $this->renta_types_values[$key];

                    if (null == $unitValue) {
                        $unitValue = '-';
                    }

                    $all_paid_renta_nat_by_detailed[] = $quantity . ' X ' . $this->renta_types[$key] . ' X ' . $unitValue . ' ед.ст.';

                    $all_paid_renta_nat_by_detailed_quantity_arr[$key] += $quantity;
                    if ('-' != $unitValue) {
                        $all_paid_renta_nat_by_detailed_unit_value_arr[$key] += $unitValue;
                    }
                }
            }
            $results[$i]['all_paid_renta_nat_by_detailed_quantity_arr'] = $all_paid_renta_nat_by_detailed_quantity_arr;
            $results[$i]['all_paid_renta_nat_by_detailed_unit_value_arr'] = $all_paid_renta_nat_by_detailed_unit_value_arr;
            $results[$i]['paid_renta_nat_by_detailed'] = implode('</br>', $all_paid_renta_nat_by_detailed);
            $results[$i]['paid_renta_nat_by_detailed'] = '' != $results[$i]['paid_renta_nat_by_detailed'] ? $results[$i]['paid_renta_nat_by_detailed'] : '-';

            $sumPaidRenta = 0;
            if ($paid_renta || $paid_renta_by_nat) {
                $sumPaidRenta = $paid_renta + $paid_renta_by_nat;
                $results[$i]['total_by_renta'] = number_format($sumPaidRenta, 2, '.', '') . ' лв.';
                $results[$i]['total_by_renta_sum'] = $sumPaidRenta;
            }
            $results[$i]['total_by_renta'] = '' != $results[$i]['total_by_renta'] ? $results[$i]['total_by_renta'] : '-';

            $totalByRentaNatura = [];
            if (!empty($paid_renta_nat) || !empty($paid_renta_nat_by_nat)) {
                $totalByRentaNatura = $this->arraySumIdenticalKeys($paid_renta_nat, $paid_renta_nat_by_nat);
            }
            $results[$i]['total_by_renta_nat_arr'] = $totalByRentaNatura;
            $results[$i]['total_by_renta_nat'] = implode('</br>', $totalByRentaNatura);
            $results[$i]['total_by_renta_nat'] = '' != $results[$i]['total_by_renta_nat'] ? $results[$i]['total_by_renta_nat'] : '-';

            if (!empty($rentaNatByPlot)) {
                foreach ($sumRentaType as $rentaType => $rentaNatura) {
                    if (!is_null($results[$i]['paid_renta_nat_details'][$rentaType])) {
                        $results[$i]['paid_renta_nat'] .= $results[$i]['paid_renta_nat_details'][$rentaType] . '</br>';
                        $results[$i]['paid_renta_nat_arr'][$rentaType] += $results[$i]['paid_renta_nat_details'][$rentaType];
                    } else {
                        $results[$i]['paid_renta_nat'] .= '0.000</br>';
                        $results[$i]['paid_renta_nat_arr'][$rentaType] += '0.000';
                    }
                }
            }

            if ((empty($rentaNatByPlot) || empty($sumRentaType)) && !empty($results[$i]['paid_renta_nat_details'])) {
                $results[$i]['paid_renta_nat'] = '';

                foreach ($results[$i]['paid_renta_nat_details'] as $rentaType => $rentaNatura) {
                    $results[$i]['paid_renta_nat'] .= $rentaNatura . ' X ' . $this->renta_types[$rentaType] . '</br>';
                }
            }

            if (is_null($results[$i]['paid_renta_nat']) || '' == $results[$i]['paid_renta_nat']) {
                $results[$i]['paid_renta_nat'] = '-';
            }

            if (empty($rentaTypes)) {
                $results[$i]['renta_nat_type'] = '[Без рента в натура]';
                $results[$i]['unpaid_renta_nat'] = '-';
                $results[$i]['over_paid_nat'] = '-';
                $results[$i]['unpaid_renta_nat_unit_value'] = '-';
            }

            foreach ($sumContractsDueRenta as $contractID => $value) {
                $results[$i]['renta'] += $value;
            }

            $results[$i]['renta'] = number_format($results[$i]['renta'], 2, '.', '');
            $results[$i]['contract_renta'] = $results[$i]['renta'];

            $results[$i]['owner_contracts_renta'] = [
                'area' => $contractPlotsAreaSum,
                'renta' => $sumContractsRenta,
                'charged_renta' => $sumContractsChargedRenta,
                'renta_nat' => $sumRentaNatByContract,
                'charged_renta_nat' => $sumChargedRentaNaturaByContract,
            ];

            if (!$results[$i]['sv_num']) {
                $results[$i]['sv_num'] = '-';
            }

            if (!$results[$i]['sv_date']) {
                $results[$i]['sv_date'] = '-';
            }

            if (!$results[$i]['owner_names']) {
                $results[$i]['owner_names'] = '-';
            }

            // increment global iterator
            $this->iterator++;

            $results[$i]['id'] = $this->iterator;
            if ($results[$i]['is_dead']) {
                $results[$i]['children'] = $this->getOwnersHeritorsPayroll($pathOwner, $results[$i]['owner_id'], false, $results[$i], $farming_years, $start_date, $due_date, $farming_years_string, $personalUse);

                $results[$i]['iconCls'] = 'icon-tree-user-rip';
            } elseif (1 == $results[$i]['owner_type']) {
                $results[$i]['iconCls'] = 'icon-tree-user';
            } else {
                $results[$i]['iconCls'] = 'icon-tree-users';
            }

            $results[$i]['is_heritor'] = false;
        }

        $return['rows'] = $results;
        $return['total'] = $counter[0]['count']; // $counter is undefined

        return $return;
    }

    private function getOwnersHeritorsPayroll($path, $root_id, $parent_plots_percent = false, $ownerResults, $farming_years, $start_date, $due_date, $farming_years_string, $personalUse)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);
        $UsersController = new UsersController('Users');

        $heritor_data = [];
        $heritor_percent_by_plots = [];
        $total_heritor_paid_renta = 0;

        $paid_renta_owner = $ownerResults['paid_renta'];
        $paid_renta_nat_owner = $ownerResults['paid_renta_nat_details'];
        $plots_contracts_renta = $ownerResults['plots_contracts_renta'];
        $plots_contracts_renta_nat = $ownerResults['plots_contracts_renta_nat'];
        $plots_contracts_due_renta_nat = $ownerResults['plots_contracts_due_renta_nat'];
        $plots_contracts_charged_renta_nat = $ownerResults['plots_contracts_charged_renta_nat'];
        $legator_has_rent_per_plot = $ownerResults['has_rent_per_plot'];
        $plots_contracts_charged_renta_values = $ownerResults['plots_contracts_charged_renta_values'];
        $owner_plots_percent = $ownerResults['owner_plots_percent'];
        $parent_contract_id = $ownerResults['contract_id'];

        // get all heritors
        $options = [
            'return' => [
                'uuid_generate_v4() as uuid',
                'h.id',
                "o.name || ' ' || o.surname || ' ' || o.lastname as owner_names",
                'h.owner_id', 'o.is_dead', 'h.path', 'egn as egn_eik', 'o.rent_place',
                "r.rep_name || ' ' || r.rep_surname || ' ' || r.rep_lastname as rep_names",
                'r.rep_egn',
                'r.rep_lk as rep_lk',
                'r.rep_lk_izdavane as rep_lk_izdavane',
                'r.rep_address as rep_address',
                'r.iban as rep_iban',
            ],
            'where' => [
                'path' => ['column' => 'h.path', 'compare' => '~', 'value' => $path],
            ],
            'joins' => [
                'left join ' . $UserDbOwnersController->DbHandler->contractsPlotsRelTable . ' scpr on scpr.contract_id = ' . $parent_contract_id,
                'left join ' . $UserDbOwnersController->DbHandler->plotsOwnersRelTable . '  spor on spor.is_heritor = true and spor.pc_rel_id = scpr.id and spor.owner_id = h.owner_id',
                'left join ' . $UserDbOwnersController->DbHandler->tableOwnersReps . ' r on r.id = spor.rep_id',
            ],
            'group' => 'h.id, o.id, r.id',
        ];

        $counter = $UserDbOwnersController->getOwnersHeritors($options, true);

        if (0 == $counter[0]['count']) {
            return [];
        }

        $heritor_results = $UserDbOwnersController->getOwnersHeritors($options, false, false);
        // we have all heritors here

        // РЕНТА В ЛЕВА
        $plotsContractsRentaLegator = [];
        $plotsContractRentCount = count($plots_contracts_renta);
        for ($m = 0; $m < $plotsContractRentCount; $m++) {
            $plot_contract_renta = $plots_contracts_renta[$m];

            $contractID = $plot_contract_renta['contract_id'];
            $plotID = $plot_contract_renta['plot_gid'];
            $renta_by_plot = $plot_contract_renta['renta_by_plot'];

            $plotsContractsRentaLegator[$contractID][$plotID] = $renta_by_plot;
        }

        $sumPlotsContractsRentaLegator = [];
        foreach ($plotsContractsRentaLegator as $contract_ID => $value) {
            foreach ($value as $plot_ID => $renta) {
                $sumPlotsContractsRentaLegator[$contract_ID] += $renta;
            }
        }

        $percentPlotsContractsRentaLegator = [];
        foreach ($plotsContractsRentaLegator as $contract_ID => $value) {
            foreach ($value as $plot_ID => $renta) {
                $rentaByPlot = 0;
                if ($sumPlotsContractsRentaLegator[$contract_ID] > 0) {
                    $rentaByPlot = $renta / $sumPlotsContractsRentaLegator[$contract_ID];
                }

                $percentPlotsContractsRentaLegator[$contract_ID][$plot_ID] = $rentaByPlot * $paid_renta_owner;
            }
        }

        $leftPlotsContractsRentaLegator = [];
        foreach ($plotsContractsRentaLegator as $contract_ID => $value) {
            foreach ($value as $plot_ID => $renta) {
                $percentRenta = $percentPlotsContractsRentaLegator[$contract_ID][$plot_ID];

                $leftPlotsContractsRentaLegator[$contract_ID][$plot_ID] = $renta - $percentRenta;
            }
        }

        // РЕНТА В НАТУРА
        if (!empty($plots_contracts_renta_nat)) {
            $sumPlotsContractsRentaNatLegator = [];
            foreach ($plots_contracts_renta_nat as $contract_ID => $value) {
                foreach ($value as $plot_ID => $rentaValue) {
                    foreach ($rentaValue as $rentaType => $rentaNat) {
                        $sumPlotsContractsRentaNatLegator[$contract_ID][$rentaType] += $rentaNat;
                    }
                }
            }

            $percentPlotsContractsRentaNatLegator = [];
            foreach ($plots_contracts_renta_nat as $contract_ID => $value) {
                foreach ($value as $plot_ID => $rentaValue) {
                    foreach ($rentaValue as $rentaType => $rentaNat) {
                        $rentaNatByPlot = 0;

                        if ($sumPlotsContractsRentaNatLegator[$contract_ID][$rentaType] > 0) {
                            $rentaNatByPlot = $rentaNat / $sumPlotsContractsRentaNatLegator[$contract_ID][$rentaType];
                        }

                        $percentPlotsContractsRentaNatLegator[$contract_ID][$plot_ID][$rentaType] = $rentaNatByPlot * $paid_renta_nat_owner[$rentaType];
                    }
                }
            }

            $leftPlotsContractsRentaNatLegator = [];
            foreach ($plots_contracts_renta_nat as $contract_ID => $value) {
                foreach ($value as $plot_ID => $rentaValue) {
                    foreach ($rentaValue as $rentaType => $rentaNat) {
                        $percentRentaNat = $percentPlotsContractsRentaNatLegator[$contract_ID][$plot_ID][$rentaType];

                        $leftPlotsContractsRentaNatLegator[$contract_ID][$plot_ID][$rentaType] = $rentaNat - $percentRentaNat;
                    }
                }
            }
        }

        $options = [
            'return' => [
                'o.id as root_id', 'po.percent', 'gid', 'c.id as contract_id', 'c.c_num as c_num', 'c.nm_usage_rights as nm_usage_rights', 'pc.id as pc_rel_id', 'c.c_num',
                'pc.area_for_rent as contract_area', 'kvs.kad_ident as plots_name',
                '(case when a.id is not null then a.id else c.id end) as contract_anex_arr',

                '(CASE WHEN a.renta IS NULL THEN 
                    (CASE
                    WHEN pc.rent_per_plot IS NOT NULL THEN
                        0
                    ELSE
                        C .renta
                    END
                ) ELSE (
                    CASE
                    WHEN pc.rent_per_plot IS NOT NULL THEN
                       0
                    ELSE
                        A .renta
                    END
                ) END) as contract_renta',

                '(CASE
                    WHEN pc.rent_per_plot IS NOT NULL THEN
                        pc.rent_per_plot
                    ELSE
                        NULL
                    END
                ) AS contracts_rent_per_plot',

                'cr.renta as charged_renta',
                '(CASE WHEN SUM (pc.rent_per_plot) IS NULL THEN FALSE ELSE TRUE END) as has_rent_per_plot',
                'MAX(crn.converted_charged_renta_nat) AS converted_charged_renta_nat',
                '(CASE WHEN a.renta_nat IS NULL THEN c.renta_nat ELSE a.renta_nat END) as renta_nat',
                'CASE WHEN o.rent_place IS NULL THEN r.rent_place ELSE o.rent_place END AS rent_place',
                "(CASE WHEN C .parent_id != 0 THEN (SELECT ( CASE WHEN C .due_date > P .due_date
                                THEN to_char(P .start_date, 'DD.MM.YYYY') || ' - ' || to_char(C .due_date, 'DD.MM.YYYY')
                                ELSE to_char(P .start_date, 'DD.MM.YYYY') || ' - ' || to_char(P .due_date, 'DD.MM.YYYY')
                                END) FROM su_contracts P WHERE ID = C .parent_id)
                            ELSE to_char(C .start_date, 'DD.MM.YYYY') || ' - ' || to_char(C .due_date, 'DD.MM.YYYY') END ) AS timespan",
                '(CASE WHEN a.renta_nat_type_id IS NULL THEN c.renta_nat_type_id ELSE a.renta_nat_type_id END) as renta_nat_type_id',
            ],
            'where' => [
                'owner_id' => ['column' => 'id', 'compare' => '=', 'prefix' => 'o', 'value' => $root_id],
                'contract_id' => ['column' => 'id', 'compare' => '=', 'prefix' => 'c', 'value' => $parent_contract_id],
                'contract_type' => ['column' => 'nm_usage_rights', 'compare' => 'NOT IN', 'prefix' => 'c', 'value' => [1, 4]],
                'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
                'is_annnex' => ['column' => 'is_annex', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
                'annex_action' => ['column' => 'annex_action', 'compare' => '=', 'prefix' => 'pc', 'value' => 'added'],
                'percent' => ['column' => 'percent', 'compare' => '>', 'prefix' => 'po', 'value' => '0'],
                'is_heritor' => ['column' => 'is_heritor', 'compare' => '=', 'prefix' => 'po', 'value' => 'FALSE'],
                'active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'TRUE'],
            ],
            'start_date' => $start_date,
            'due_date' => $due_date,
            'year_id' => $farming_years,
            'group' => 'gid, o.id, po.percent, c.id, pc.id, cr.renta, cr.nat_is_converted, cr.renta_nat, r.rent_place, a.id, a.renta, a.renta_nat, a.renta_nat_type_id, pu.area',
        ];

        $contract_plot_results = $UserDbPaymentsController->getPayrollData($options, false, false);
        $count_contract_plot_results = count($contract_plot_results);

        // iterate all contracts_plots results
        $contract_arr = [];
        for ($i = 0; $i < $count_contract_plot_results; $i++) {
            $plotID = $contract_plot_results[$i]['gid'];

            // get heritors for the current plot
            $options = [
                'return' => [
                    'h.id', 'owner_id', 'is_dead', 'path',
                    "(SELECT poi.percent FROM su_plots_owners_rel poi WHERE poi.path = h.path AND poi.is_heritor = true AND poi.pc_rel_id = {$contract_plot_results[$i]['pc_rel_id']}) as percent",
                ],
                'where' => [
                    'path' => ['column' => 'path', 'compare' => '~', 'value' => $path],
                ],
            ];

            $results = $UserDbOwnersController->getOwnersHeritors($options, false, false);
            $sum_custom_ownage = 0;
            $heritors = 0;
            $all_heritors_count = count($results);

            $contract_arr[] = $contract_plot_results[$i]['contract_id'];

            for ($j = 0; $j < $all_heritors_count; $j++) {
                if (null != $results[$j]['percent']) {
                    $sum_custom_ownage += $results[$j]['percent'];
                } else {
                    $heritors++;
                }
            }

            $personTotalArea = 0;
            for ($j = 0; $j < $all_heritors_count; $j++) {
                $ownerID = $results[$j]['owner_id'];
                $parent_path_e = explode('.', $results[$j]['path'], -1);
                $parent_path = implode('.', $parent_path_e);

                if (!$results[$j]['percent'] || 0 == $results[$j]['percent']) {
                    if ($parent_plots_percent[$plotID][$parent_path] || '0' == $parent_plots_percent[$plotID][$parent_path]) {
                        if ('0' == $results[$j]['percent']) {
                            $results[$j]['percent'] = '0';
                        } else {
                            $results[$j]['percent'] = ($parent_plots_percent[$plotID][$parent_path] - $sum_custom_ownage) / $heritors;
                        }
                        $heritor_percent_by_plots[$plotID][$results[$j]['path']] = $results[$j]['percent'];
                    } else {
                        if (null == $results[$j]['percent']) {
                            $results[$j]['percent'] = ($contract_plot_results[$i]['percent'] - $sum_custom_ownage) / $heritors;
                            $heritor_percent_by_plots[$plotID][$results[$j]['path']] = $results[$j]['percent'];
                        } elseif ('0' == $results[$j]['percent']) {
                            $heritor_percent_by_plots[$plotID][$results[$j]['path']] = '0';
                        }
                    }
                } else {
                    $heritor_percent_by_plots[$plotID][$results[$j]['path']] = $results[$j]['percent'];
                }

                $results[$j]['percent_heritor'] = 0;

                if ($owner_plots_percent[$contract_plot_results[$i]['contract_id']][$plotID] > 0) {
                    $results[$j]['percent_heritor'] = $results[$j]['percent'] / $owner_plots_percent[$contract_plot_results[$i]['contract_id']][$plotID];
                }
                $heritor_data[$ownerID]['owner_plots_percent'][$contract_plot_results[$i]['contract_id']][$plotID] = $results[$j]['percent'];

                $renta_nats = $UserDbController->DbHandler->getDataByQuery(
                    "SELECT 
                            crt.renta_value * round((pc.area_for_rent - COALESCE(pu.area, 0)) ::numeric, 3) AS renta_nat, 
                            crt.renta_value,
                            po.percent, 
                            c.id,
                            po.owner_id, 
                            crt.renta_id
                        FROM su_contracts c
                        INNER JOIN su_contracts_plots_rel pc ON(pc.contract_id = c.id)
                        INNER JOIN layer_kvs kvs ON(kvs.gid = pc.plot_id)
                        LEFT JOIN su_plots_owners_rel po ON(po.pc_rel_id = pc.id)
                        LEFT JOIN su_personal_use pu ON (pu.owner_id = {$root_id} AND pu.year = {$farming_years_string} AND pu.pc_rel_id = pc.id)
                        LEFT JOIN su_contracts_rents crt ON (c.id = crt.contract_id)
                        where c.id = {$contract_plot_results[$i]['contract_anex_arr']}
                        and pc.annex_action ='added'
                        and po.owner_id = {$root_id}
                        and pc.plot_id = {$plotID}
                        and pc.rent_per_plot isnull
                        ORDER BY crt.renta_id"
                );

                // calculate all needed information about heritor
                $heritor_data[$ownerID]['root_id'] = $contract_plot_results[$i]['root_id'];
                $heritor_data[$ownerID]['c_num_array'][] = $contract_plot_results[$i]['c_num'];
                $puArea = $UserDbPaymentsController->getPersonalUseArea($personalUse, $ownerID, $contract_plot_results[$i], ($ownerResults['area'] * $results[$j]['percent_heritor']));

                $areaWithoutPu = round((($contract_plot_results[$i]['contract_area'] * ($results[$j]['percent'] / 100)) - $puArea), 3);
                $herArea = ($contract_plot_results[$i]['contract_area'] * ($results[$j]['percent'] / 100));

                $heritor_data[$ownerID]['area_array'][$contract_plot_results[$i]['contract_id']][$plotID] = $areaWithoutPu;
                $heritor_data[$ownerID]['area'] += $herArea;
                $heritor_data[$ownerID]['area_without_pu'] += $areaWithoutPu;
                $heritor_data[$ownerID]['pu_area'] += $puArea;

                $heritor_data[$ownerID]['contract_id'] = $contract_plot_results[$i]['contract_id'];
                $heritor_data[$ownerID]['nm_usage_rights'] = $contract_plot_results[$i]['nm_usage_rights'];
                $heritor_data[$ownerID]['c_num'] = $contract_plot_results[$i]['c_num'];
                $heritor_data[$ownerID]['timespan'] = $contract_plot_results[$i]['timespan'];
                $heritor_data[$ownerID]['has_rent_per_plot'] = $contract_plot_results['has_rent_per_plot'];
                // $heritor_data[$ownerID]['plots_contracts_renta_heritors'][$contract_plot_results[$i]['contract_id']][$plotID] = $leftPlotsContractsRentaLegator[$contract_plot_results[$i]['contract_id']][$plotID] * $results[$j]['percent_heritor'];

                if (!empty($leftPlotsContractsRentaNatLegator[$contract_plot_results[$i]['contract_id']])) {
                    if (!empty($leftPlotsContractsRentaNatLegator[$contract_plot_results[$i]['contract_id']][$plotID])) {
                        foreach ($leftPlotsContractsRentaNatLegator[$contract_plot_results[$i]['contract_id']][$plotID] as $rentaType => $rentaNat) {
                            $heritor_data[$ownerID]['plots_contracts_renta_nat_heritors'][$contract_plot_results[$i]['contract_id']][$plotID][$rentaType] = $rentaNat * $results[$j]['percent_heritor'];
                        }
                    }
                }

                $personTotalArea += $heritor_data[$ownerID]['area'];

                $heritor_data[$ownerID]['contracts_renta_plots'][$contract_plot_results[$i]['contract_id']][$plotID] = ($contract_plot_results[$i]['contracts_rent_per_plot'] ?? $contract_plot_results[$i]['contract_renta']) * $areaWithoutPu;

                // $heritor_data[$ownerID]['contracts_renta_nat_plots'] = array();
                $heritor_data[$ownerID]['nat_type_ids'] = [];
                foreach ($renta_nats as $rentaKey => $rentaVal) {
                    if (!is_null($rentaVal['renta_id']) && !is_null($rentaVal['renta_nat'])) {
                        $heritor_data[$ownerID]['contracts_renta_nat_plots'][$contract_plot_results[$i]['contract_id']][$plotID][$rentaVal['renta_id']] += $rentaVal['renta_value'] * $areaWithoutPu;

                        if (!in_array($rentaVal['renta_id'], $heritor_data[$ownerID]['nat_type_ids'])) {
                            $heritor_data[$ownerID]['nat_type_ids'][] = $rentaVal['renta_id'];
                        }
                    }
                }

                if (!is_null($contract_plot_results[$i]['charged_renta']) && $contract_plot_results[$i]['charged_renta'] >= 0) {
                    if (null != $contract_plot_results[$i]['contracts_rent_per_plot']) {
                        $contract_plot_results[$i]['charged_renta'] = $contract_plot_results[$i]['contracts_rent_per_plot'];
                    }

                    $heritor_data[$ownerID]['charged_renta_plots'][$contract_plot_results[$i]['contract_id']][$plotID] = $contract_plot_results[$i]['charged_renta'] * $areaWithoutPu;
                }

                $heritor_data[$ownerID]['plots_contracts_area_array'][] = [
                    'c_num' => $contract_plot_results[$i]['c_num'],
                    'pc_rel_id' => $contract_plot_results[$i]['pc_rel_id'],
                    'pc_id' => $contract_plot_results[$i]['contract_id'],
                    'plot_name' => $contract_plot_results[$i]['plots_name'],
                    'area' => $herArea,
                    'area_without_pu' => $areaWithoutPu,
                    'pu_area' => $puArea ?? 0,
                    'renta' => ($contract_plot_results[$i]['contracts_rent_per_plot'] ?? $contract_plot_results[$i]['contract_renta']) * $areaWithoutPu,
                    'charged_renta' => $contract_plot_results[$i]['charged_renta'] * $areaWithoutPu,
                ];

                if (!is_null($contract_plot_results[$i]['converted_charged_renta_nat']) && $contract_plot_results[$i]['converted_charged_renta_nat'] >= 0) {
                    if (null != $contract_plot_results[$i]['contracts_rent_per_plot']) {
                        $contract_plot_results[$i]['converted_charged_renta_nat'] = $contract_plot_results[$i]['contracts_rent_per_plot'];
                    }

                    $heritor_data[$ownerID]['charged_renta_plots'][$contract_plot_results[$i]['contract_id']][$plotID] += $contract_plot_results[$i]['converted_charged_renta_nat'] * $areaWithoutPu;
                }
            }
        }

        // prepare heritor results for grid format
        $heritorsCount = count($heritor_results);
        for ($i = 0; $i < $heritorsCount; $i++) {
            $ownerID = $heritor_results[$i]['owner_id'];

            $paidOptions = [
                'custom_counter' => 'COUNT(DISTINCT(p.owner_id))',
                'order' => 'asc',
                'sort' => 'p.contract_id',
                'return' => [
                    'p.id as payment_id',
                    'p.owner_id as owner_id',
                    'p.contract_id as contract_id',
                    'case when pn.amount notnull and pn.unit_value notnull  then round((pn.amount * pn.unit_value)::numeric, 2) else round(p.amount::numeric, 2) end as trans_amount',
                    'case when pn.amount notnull then round(pn.amount::numeric,3) else round(p.amount_nat::numeric, 3) end as amount_nat',
                    'round(pn.amount::numeric, 3) as trans_amount_nat',
                    'pn.nat_type as nat_type',
                    'p.paid_in',
                    'p.paid_from',
                    'rent.name as trans_nat_type_text',
                    'round(pn.unit_value::numeric, 2)',
                ],
                'where' => [
                    'path' => ['column' => 'path', 'compare' => '~', 'prefix' => 'p', 'value' => $heritor_results[$i]['path']],
                    'contract_id' => ['column' => 'contract_id', 'compare' => 'IN', 'prefix' => 'p', 'value' => $contract_arr],
                    'year' => ['column' => 'farming_year', 'compare' => 'IN', 'prefix' => 'p', 'value' => $farming_years],
                ],
                'year_id' => $farming_years_string,
            ];

            $paidResults = $UserDbPaymentsController->getPaidData($paidOptions, false, false);

            $paid_renta = 0;
            $paid_renta_by_nat = 0;
            $paid_renta_nat = [];
            $paid_renta_nat_by_nat = [];
            $paid_renta_nat_by_detailed = [];
            $paid_renta_nat_by_detailed_unit_value = [];
            $paymentIds = [];
            $paidRentaByContract = [];
            $paidRentaNatByContract = [];
            $paidResultCount = count($paidResults);
            for ($m = 0; $m < $paidResultCount; $m++) {
                $paidResult = $paidResults[$m];
                $renta_type = $paidResult['nat_type'];
                $contract_id = $paidResult['contract_id'];

                $paidRentaByContract[$contract_id] += $paidResult['trans_amount'];
                if (1 == $paidResult['paid_from']) {
                    if (2 == $paidResult['paid_in']) {
                        $paid_renta_nat[$renta_type] += $paidResult['amount_nat'];

                        continue;
                    }

                    $paid_renta += $paidResult['trans_amount'];
                } elseif (2 == $paidResult['paid_from']) {
                    $paidRentaNatByContract[$contract_id][$renta_type] += $paidResult['trans_amount_nat'];

                    if (2 == $paidResult['paid_in']) {
                        $paid_renta_nat_by_nat[$renta_type] += $paidResult['trans_amount_nat'];

                        continue;
                    }
                    if (array_key_exists($renta_type, $paid_renta_nat_by_detailed)) {
                        $paid_renta_nat_by_detailed[$renta_type] += $paidResult['trans_amount_nat'];
                    } else {
                        $paid_renta_nat_by_detailed[$renta_type] = $paidResult['trans_amount_nat'];
                        $paid_renta_nat_by_detailed_unit_value[$renta_type] = $paidResult['unit_value'];
                    }

                    $paymentIds[] = $paidResult['payment_id'];
                    $paid_renta_by_nat += $paidResult['trans_amount'];
                }
            }

            // Сортиране на платерената рента чрез натура
            ksort($paid_renta_nat_by_nat);
            ksort($paid_renta_nat);

            // count all heritors
            $pathOwner = $heritor_results[$i]['path'] . '.*{1}';

            $options = [
                'return' => [
                    'h.id',
                ],
                'where' => [
                    'path' => ['column' => 'path', 'compare' => '~', 'value' => $pathOwner],
                ],
            ];

            $paid_renta_by = [];
            $paid_renta_by_arr = [];
            if ($paid_renta) {
                $amount = number_format($paid_renta, 2, '.', '');
                $paid_renta_by[] = $amount . ' лв.';

                $paid_renta_by_arr['amount'] = $amount;
            }
            if (!empty($paid_renta_nat)) {
                foreach ($paid_renta_nat as $key => $value) {
                    $quantity = number_format($value, 3, '.', '');
                    $paid_renta_by[] = $quantity . ' X ' . $this->renta_types[$key];

                    $paid_renta_by_arr['nat_amount'][$key] = $quantity;
                }
            }
            $heritor_results[$i]['paid_renta_by_arr'] = $paid_renta_by_arr;
            $heritor_results[$i]['paid_renta_by'] = implode('</br>', $paid_renta_by);
            $heritor_results[$i]['paid_renta_by'] = '' != $heritor_results[$i]['paid_renta_by'] ? $heritor_results[$i]['paid_renta_by'] : '-';
            $heritor_results[$i]['has_rent_per_plot'] = $legator_has_rent_per_plot;

            $paid_renta_nat_by = [];
            $paid_renta_nat_by_arr = [];
            if ($paid_renta_by_nat) {
                $amount = number_format($paid_renta_by_nat, 2, '.', '');
                $paid_renta_nat_by[] = $amount . ' лв.';

                $paid_renta_nat_by_arr['amount'] = $amount;
            }
            if (!empty($paid_renta_nat_by_nat)) {
                foreach ($paid_renta_nat_by_nat as $key => $value) {
                    $quantity = number_format($value, 3, '.', '');
                    $paid_renta_nat_by[] = $quantity . ' X ' . $this->renta_types[$key];

                    $paid_renta_nat_by_arr['nat_amount'][$key] = $quantity;
                }
            }
            $heritor_results[$i]['paid_renta_nat_by_arr'] = $paid_renta_nat_by_arr;
            $heritor_results[$i]['paid_renta_nat_by'] = implode('</br>', $paid_renta_nat_by);

            $all_paid_renta_nat_by_detailed = [];
            $all_paid_renta_nat_by_detailed_quantity_arr = [];
            $all_paid_renta_nat_by_detailed_unit_value_arr = [];
            if (!empty($paid_renta_nat_by_detailed)) {
                foreach ($paid_renta_nat_by_detailed as $key => $value) {
                    $quantity = number_format($value, 3, '.', '');
                    $unitValue = null != $paid_renta_nat_by_detailed_unit_value[$key] ? $paid_renta_nat_by_detailed_unit_value[$key] : $this->renta_types_values[$key];

                    if (null == $unitValue) {
                        $unitValue = '-';
                    }

                    $all_paid_renta_nat_by_detailed[] = $quantity . ' X ' . $this->renta_types[$key] . ' X ' . $unitValue . ' ед.ст.';

                    $all_paid_renta_nat_by_detailed_quantity_arr[$key] += $quantity;
                    if ('-' != $unitValue) {
                        $all_paid_renta_nat_by_detailed_unit_value_arr[$key] += $unitValue;
                    }
                }
            }
            $heritor_results[$i]['all_paid_renta_nat_by_detailed_quantity_arr'] = $all_paid_renta_nat_by_detailed_quantity_arr;
            $heritor_results[$i]['all_paid_renta_nat_by_detailed_unit_value_arr'] = $all_paid_renta_nat_by_detailed_unit_value_arr;
            $heritor_results[$i]['paid_renta_nat_by_detailed'] = implode('</br>', $all_paid_renta_nat_by_detailed);
            $heritor_results[$i]['paid_renta_nat_by_detailed'] = '' != $heritor_results[$i]['paid_renta_nat_by_detailed'] ? $heritor_results[$i]['paid_renta_nat_by_detailed'] : '-';

            $sumPaidRenta = 0;
            if ($paid_renta || $paid_renta_by_nat) {
                $sumPaidRenta = $paid_renta + $paid_renta_by_nat;
                $heritor_results[$i]['total_by_renta'] = number_format($sumPaidRenta, 2, '.', '') . ' лв.';
                $heritor_results[$i]['total_by_renta_sum'] = $sumPaidRenta;
            }
            $heritor_results[$i]['total_by_renta'] = '' != $heritor_results[$i]['total_by_renta'] ? $heritor_results[$i]['total_by_renta'] : '-';

            $totalByRentaNatura = [];
            if (!empty($paid_renta_nat) || !empty($paid_renta_nat_by_nat)) {
                $totalByRentaNatura = $this->arraySumIdenticalKeys($paid_renta_nat, $paid_renta_nat_by_nat);
            }
            $heritor_results[$i]['total_by_renta_nat_arr'] = $totalByRentaNatura;
            $heritor_results[$i]['total_by_renta_nat'] = implode('</br>', $totalByRentaNatura);
            $heritor_results[$i]['total_by_renta_nat'] = '' != $heritor_results[$i]['total_by_renta_nat'] ? $heritor_results[$i]['total_by_renta_nat'] : '-';

            $heritor_results[$i] = $this->addNewRentaPayrolls($paidResults, $heritor_results[$i]);
            $heritor_results[$i]['root_id'] = $heritor_data[$ownerID]['root_id'];

            if (!$heritor_data[$ownerID]['c_num_array']) {
                $heritor_data[$ownerID]['c_num_array'] = [];
            }
            $heritor_results[$i]['c_num_array'] = array_unique($heritor_data[$ownerID]['c_num_array']);
            $heritor_results[$i]['area'] = number_format($heritor_data[$ownerID]['area'], 3, '.', '');
            $heritor_results[$i]['owner_area'] = number_format($heritor_data[$ownerID]['area'], 3, '.', '');
            $heritor_results[$i]['area_array'] = $heritor_data[$ownerID]['area_array'];
            $heritor_results[$i]['owner_plots_percent'] = $heritor_data[$ownerID]['owner_plots_percent'];
            $heritor_results[$i]['contract_id'] = $heritor_data[$ownerID]['contract_id'];
            $heritor_results[$i]['type'] = $GLOBALS['Contracts']['ContractTypes'][$heritor_data[$ownerID]['nm_usage_rights']]['name'];
            $heritor_results[$i]['type_id'] = $heritor_data[$ownerID]['nm_usage_rights'];
            $heritor_results[$i]['c_num'] = $heritor_data[$ownerID]['c_num'];
            $heritor_results[$i]['timespan'] = $heritor_data[$ownerID]['timespan'];
            $heritor_results[$i]['nat_type_ids'] = $heritor_data[$ownerID]['nat_type_ids'];
            $heritor_results[$i]['renta_nat_type_id'] = $heritor_results[$i]['nat_type_ids'];
            $heritor_results[$i]['plots_contracts_area_array'] = $heritor_data[$ownerID]['plots_contracts_area_array'];
            $heritor_results[$i]['plots_contracts_charged_renta_values'] = $ownerResults['plots_contracts_charged_renta_values'];

            // РЕНТА В ЛЕВА
            $heritor_results[$i]['plots_contracts_charged_renta_down_grid'] = [];
            if (!empty($heritor_data[$ownerID]['charged_renta_plots'])) {
                foreach ($heritor_data[$ownerID]['charged_renta_plots'] as $contractID => $value) {
                    foreach ($value as $plotID => $plotRenta) {
                        $plotContractRentaHeritor = $heritor_data[$ownerID]['plots_contracts_renta_heritors'][$contractID];

                        // if (array_key_exists($plotID, $plotContractRentaHeritor)) {
                        $heritor_results[$i]['charged_renta'] += $plotRenta;
                        $heritor_results[$i]['plots_contracts_charged_renta_down_grid'][$contractID][$plotID] = $plotRenta;
                        // }
                    }
                }
            }

            if (!is_null($heritor_results[$i]['charged_renta'])) {
                $heritor_results[$i]['charged_renta'] = $heritor_results[$i]['charged_renta'] > 0 ? number_format($heritor_results[$i]['charged_renta'], 2, '.', '') : '0.00';
            } else {
                $heritor_results[$i]['charged_renta'] = '-';
            }

            $heritor_results[$i]['plots_contracts_renta_down_grid'] = [];
            $heritor_results[$i]['due_renta'] = 0;

            if (!empty($heritor_data[$ownerID]['contracts_renta_plots'])) {
                foreach ($heritor_data[$ownerID]['contracts_renta_plots'] as $contractID => $value) {
                    foreach ($value as $plotID => $plotRenta) {
                        $plotContractRentaHeritor = $heritor_data[$ownerID]['plots_contracts_renta_heritors'][$contractID];

                        $onlyContractChargedRenta = $heritor_results[$i]['plots_contracts_charged_renta_down_grid'][$contractID];
                        if (!array_key_exists($plotID, $onlyContractChargedRenta)) {
                            $heritor_results[$i]['renta'] += $plotRenta;
                            $heritor_results[$i]['plots_contracts_renta_down_grid'][$contractID][$plotID] = $plotRenta;

                            $heritor_results[$i]['due_renta'] += $plotRenta;
                        } else {
                            $heritor_results[$i]['due_renta'] += $onlyContractChargedRenta[$plotID];
                        }
                    }
                }
            }

            $heritor_results[$i]['renta'] = $heritor_results[$i]['renta'] > 0 ? number_format($heritor_results[$i]['renta'], 2, '.', '') : '0.00';

            // РЕНТА В НАТУРА
            $heritor_results[$i]['plots_contracts_charged_renta_nat'] = $plots_contracts_charged_renta_nat;
            $heritor_results[$i]['plots_contracts_charged_renta_nat_down_grid'] = [];
            $heritor_results[$i]['charged_renta_nat'] = [];
            if (!empty($heritor_results[$i]['plots_contracts_charged_renta_nat'])) {
                foreach ($heritor_results[$i]['plots_contracts_charged_renta_nat'] as $contractID => $value) {
                    foreach ($value as $plotID => $plotIDRenta) {
                        foreach ($plotIDRenta as $rentaType => $rentaNat) {
                            $plotContractRentaNatHeritor = $heritor_data[$ownerID]['plots_contracts_renta_nat_heritors'][$contractID];
                            $chargedRentaByNatTye = $heritor_data[$ownerID]['area_array'][$contractID][$plotID] * $plots_contracts_charged_renta_values[$rentaType];
                            if (!empty($plotContractRentaNatHeritor) && array_key_exists($plotID, $plotContractRentaNatHeritor)) {
                                $heritor_results[$i]['cpntracts_charged_renta_nat_plots'][$contractID][$plotID][$rentaType] += $chargedRentaByNatTye;
                                $heritor_results[$i]['charged_renta_nat'][$rentaType] += $chargedRentaByNatTye;
                                $heritor_results[$i]['plots_contracts_charged_renta_nat_down_grid'][$contractID][$plotID][$rentaType] = $chargedRentaByNatTye;
                            }
                        }
                    }
                }
            }
            $heritor_results[$i]['due_renta_nat'] = [];
            $heritor_results[$i]['plots_contracts_renta_nat_down_grid'] = [];
            if (!empty($heritor_data[$ownerID]['contracts_renta_nat_plots'])) {
                foreach ($heritor_data[$ownerID]['contracts_renta_nat_plots'] as $contractID => $value) {
                    foreach ($value as $plotID => $plotIDRenta) {
                        foreach ($plotIDRenta as $rentaType => $rentaNat) {
                            $onlyContractChargedRentaNat = $heritor_results[$i]['plots_contracts_charged_renta_nat_down_grid'][$contractID][$plotID][$rentaType];

                            if (empty(($onlyContractChargedRentaNat))) {
                                $heritor_results[$i]['renta_nat'][$rentaType] += $rentaNat;
                                $heritor_results[$i]['plots_contracts_renta_nat_down_grid'][$contractID][$plotID][$rentaType] = $rentaNat;
                            } else {
                                if (is_null($heritor_results[$i]['renta_nat'][$rentaType])) {
                                    $heritor_results[$i]['renta_nat'][$rentaType] = '0.000';
                                }
                            }
                            $heritor_results[$i]['due_renta_nat'][$rentaType] += $onlyContractChargedRentaNat ?? $rentaNat;
                        }
                    }
                }
            }

            if (!empty($heritor_results[$i]['charged_renta_nat'])) {
                foreach ($heritor_results[$i]['charged_renta_nat'] as $natTypeID => $rentaNat) {
                    $chargedRentaNat = $heritor_results[$i]['charged_renta_nat'][$natTypeID];
                    $chargedRentaNat = $chargedRentaNat > 0 ? $chargedRentaNat : '0.000';

                    if ('-' != $chargedRentaNat && '' != $chargedRentaNat && $chargedRentaNat >= 0 && '0.000' != $chargedRentaNat) {
                        $heritor_results[$i]['charged_renta_nat_text'] .= number_format($chargedRentaNat, 3, '.', '') . '</br>';
                    } else {
                        $heritor_results[$i]['charged_renta_nat_text'] .= '-</br>';
                        $heritor_results[$i]['charged_renta_nat'][$natTypeID] = '-';
                    }
                }
                ksort($heritor_results[$i]['charged_renta_nat']);
            }

            if (!empty($heritor_results[$i]['renta_nat'])) {
                ksort($heritor_results[$i]['renta_nat']);
                foreach ($heritor_results[$i]['renta_nat'] as $rentaNatTypeID => $rentaNat) {
                    if (!empty($heritor_results[$i]['charged_renta_nat'][$rentaNatTypeID])) {
                        $rentaNat = 0;
                    }

                    if (0 == $rentaNatTypeID) {
                        continue;
                    }
                    if ('-' != $rentaNat) {
                        $rentaNat = $rentaNat > 0 ? $rentaNat : '0.000';
                        $rentaNat = number_format($rentaNat, 3, '.', '');
                    }

                    $heritor_results[$i]['renta_nat_text'] .= $rentaNat . '<br/>';
                    $heritor_results[$i]['renta_nat'][$rentaNatTypeID] = $rentaNat;
                }
            }

            if (!$heritor_results[$i]['renta_nat_text']) {
                $heritor_results[$i]['renta_nat_text'] = '-';
            } else {
                $heritor_results[$i]['renta_nat_text'] = rtrim($heritor_results[$i]['renta_nat_text'], '<br/>');
            }

            if (!$heritor_results[$i]['charged_renta_nat_text']) {
                $heritor_results[$i]['charged_renta_nat_text'] = '-';
            }

            $heritor_results[$i]['paid_renta'] = '' != $heritor_results[$i]['paid_renta'] ? number_format($heritor_results[$i]['paid_renta'], 2, '.', '') : '0.00';

            if ($personTotalArea > 0) {
                $unpaid_renta = $heritor_results[$i]['due_renta'] - $heritor_results[$i]['paid_renta'];
                $heritor_results[$i]['unpaid_renta'] = number_format(($unpaid_renta < 0) ? '0.00' : $unpaid_renta, 2, '.', '');
                $heritor_results[$i]['over_paid'] = ($unpaid_renta >= 0) ? '0.00' : number_format($unpaid_renta * (-1), 2, '.', '');
            }

            if (!empty($heritor_results[$i]['renta_nat'])) {
                $unpaid_renta_nat = [];
                $unpaid_renta_nat_arr = [];
                $over_paid_renta_nat = [];
                $over_paid_renta_nat_arr = [];
                $total_unpaid_renta_nat = [];
                $unpaid_renta_nat_unit_value = [];
                $unpaid_renta_nat_unit_value_arr = [];
                $rentaTypes = [];

                foreach ($heritor_results[$i]['due_renta_nat'] as $rentaType => $rentaNatura) {
                    $paidRentaNatura = $heritor_results[$i]['paid_renta_nat_details'];
                    $rentaTypes[] = $this->renta_types[$rentaType];

                    $unpaidRentaNatura = $rentaNatura - $paidRentaNatura[$rentaType];
                    $quantity = number_format(($unpaidRentaNatura < 0) ? '0.000' : $unpaidRentaNatura, 3, '.', '');
                    $quantityOverPaid = number_format(($unpaidRentaNatura >= 0) ? '0.000' : $unpaidRentaNatura * (-1), 3, '.', '');
                    $quantityValue = number_format($quantity * $this->renta_types_values[$rentaType], 2, '.', '');

                    $unpaid_renta_nat_unit_value[] = $quantityValue . ' лв.';
                    $unpaid_renta_nat_unit_value_arr[$rentaType] = $quantityValue;

                    $unpaid_renta_nat[] = $quantity;
                    $unpaid_renta_nat_arr[$rentaType] = $quantity;

                    $over_paid_renta_nat[] = $quantityOverPaid;
                    $over_paid_renta_nat_arr[$rentaType] = $quantityOverPaid;
                }

                $heritor_results[$i]['unpaid_renta_nat_arr'] = $unpaid_renta_nat_arr;
                $heritor_results[$i]['over_paid_renta_nat_arr'] = $over_paid_renta_nat_arr;
                $heritor_results[$i]['unpaid_renta_nat_unit_value_arr'] = $unpaid_renta_nat_unit_value_arr;
                $heritor_results[$i]['unpaid_renta_nat'] = implode('</br>', $unpaid_renta_nat);
                $heritor_results[$i]['over_paid_nat'] = implode('</br>', $over_paid_renta_nat);
                $heritor_results[$i]['unpaid_renta_nat_unit_value'] = implode('</br>', $unpaid_renta_nat_unit_value);
                $heritor_results[$i]['renta_nat_type'] = implode('</br>', $rentaTypes);
            }

            $this->iterator++;
            $heritor_results[$i]['id'] = $this->iterator;

            if (!empty($heritor_data[$ownerID]['plots_contracts_renta_heritors'])) {
                foreach ($heritor_data[$ownerID]['plots_contracts_renta_heritors'] as $contractIDHer => $value) {
                    foreach ($value as $plotIDHer => $rentaHer) {
                        $heritor_results[$i]['plots_contracts_renta'][] = ['renta_by_plot' => $rentaHer, 'contract_id' => $contractIDHer, 'plot_gid' => $plotIDHer];
                    }
                }
            }

            $heritor_results[$i]['plots_contracts_renta_nat'] = $heritor_data[$ownerID]['plots_contracts_renta_nat_heritors'];

            if (!empty($heritor_results[$i]['renta_nat'])) {
                foreach ($heritor_results[$i]['renta_nat'] as $rentaType => $rentaNatura) {
                    if (!is_null($heritor_results[$i]['paid_renta_nat_details'][$rentaType])) {
                        $heritor_results[$i]['paid_renta_nat'] .= $heritor_results[$i]['paid_renta_nat_details'][$rentaType] . '<br/>';
                        $heritor_results[$i]['paid_renta_nat_arr'][$rentaType] += $heritor_results[$i]['paid_renta_nat_details'][$rentaType];
                    } else {
                        $heritor_results[$i]['paid_renta_nat'] .= '0.000<br/>';
                        $heritor_results[$i]['paid_renta_nat_arr'][$rentaType] += '0.000';
                    }
                }
            }

            if (empty($heritor_results[$i]['renta_nat']) && !empty($heritor_results[$i]['paid_renta_nat_details'])) {
                $heritor_results[$i]['paid_renta_nat'] = '';

                foreach ($heritor_results[$i]['paid_renta_nat_details'] as $rentaType => $rentaNatura) {
                    $heritor_results[$i]['paid_renta_nat'] .= $rentaNatura . ' X ' . $this->renta_types[$rentaType] . '</br>';
                }
            }

            if (is_null($results[$i]['paid_renta_nat']) || '' == $results[$i]['paid_renta_nat']) {
                $results[$i]['paid_renta_nat'] = '-';
            }

            if (empty($rentaTypes)) {
                $heritor_results[$i]['renta_nat_type'] = '[Без рента в натура]';
                $heritor_results[$i]['unpaid_renta_nat'] = '-';
                $heritor_results[$i]['over_paid_nat'] = '-';
                $heritor_results[$i]['unpaid_renta_nat_unit_value'] = '-';
            }

            // put new rows if reps are more than one
            $tmp_reps_array = explode(', ', $heritor_results[$i]['rep_names']);
            if (!empty($tmp_reps_array)) {
                $heritor_results[$i]['rep_names'] = implode(', </br>', $tmp_reps_array);
            }

            if ($heritor_results[$i]['is_dead']) {
                $heritor_results[$i]['children'] = $this->getOwnersHeritorsPayroll($pathOwner, $root_id, $heritor_percent_by_plots, $heritor_results[$i], $farming_years, $start_date, $due_date, $farming_years_string, $personalUse);
                $heritor_results[$i]['iconCls'] = 'icon-tree-user-rip';
            } else {
                $heritor_results[$i]['iconCls'] = 'icon-tree-user';
            }

            $heritor_results[$i]['is_heritor'] = true;
        }

        return $heritor_results;
    }

    private function addNewRentaPayrolls($rentas = [], $owner = [], $totalPaidRenta = false)
    {
        $natural_renta = [];
        $paid_renta = 0;

        foreach ($rentas as $rentaData) {
            if ($owner['owner_id'] == $rentaData['owner_id'] || $totalPaidRenta) {
                if (1 == $rentaData['paid_from']) {
                    $paid_renta += $rentaData['trans_amount'];
                } else {
                    if (isset($natural_renta[$rentaData['nat_type']])) {
                        $natural_renta[$rentaData['nat_type']] += $rentaData['trans_amount_nat'];
                    } else {
                        $natural_renta[$rentaData['nat_type']] = $rentaData['trans_amount_nat'];
                    }
                }
            }
        }
        $natural_keys = array_keys($natural_renta);

        $renta_nat_counter = 0;
        foreach ($natural_renta as $nat_rent_key => $nat_renta) {
            if ('NULL' != $nat_rent_key) {
                $owner['paid_renta_nat_details'][$nat_rent_key] += $nat_renta;
                $owner['paid_renta_nat_details'][$nat_rent_key] = number_format($owner['paid_renta_nat_details'][$nat_rent_key], 3, '.', '');
            }
        }

        $owner['paid_renta'] = $paid_renta;

        return $owner;
    }

    private function arraySumIdenticalKeys()
    {
        $arrays = func_get_args();
        $keys = array_keys(array_reduce($arrays, function ($keys, $arr) { return $keys + $arr; }, []));
        $sums = [];

        foreach ($keys as $key) {
            $sums[$key] = array_reduce(
                $arrays,
                function ($sum, $arr) use ($key) {
                    $quantity = number_format($sum + @$arr[$key], 3, '.', '');
                    if ('0.000' != $quantity) {
                        return $quantity;
                    }
                }
            );
        }

        return $sums;
    }
}
