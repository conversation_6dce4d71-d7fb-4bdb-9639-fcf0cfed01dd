<?php

declare(strict_types=1);

beforeEach(function () {
    // DB::statement('TRUNCATE TABLE public.layer_tmp_kvs_02227 RESTART IDENTITY CASCADE;');
});

it('test get method', function () {
    $response = authGetJson('/api/documents/contracts/49');

    // Assert the response is correct
    expect($response->getStatusCode())->toBe(200);
});

// it('test post method', function () {

//     $response = authGetJson('/api/documents/contracts/49');

//     // Assert the response is correct
//     expect($response->getStatusCode())->toBe(200);
// });
