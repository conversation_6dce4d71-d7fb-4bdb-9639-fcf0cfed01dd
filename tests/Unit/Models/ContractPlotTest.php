<?php

declare(strict_types=1);

use App\Models\UserDb\Documents\Contracts\ContractPlot;
use Illuminate\Support\Facades\DB;

describe('ContractPlot::getPlotsForRenew', function () {
    it('has the correct method signature and returns array', function () {
        // Test that the method exists and has the correct signature
        expect(method_exists(ContractPlot::class, 'getPlotsForRenew'))->toBeTrue();

        // Test with reflection to verify method signature
        $reflection = new ReflectionMethod(ContractPlot::class, 'getPlotsForRenew');
        $parameters = $reflection->getParameters();

        expect($parameters)->toHaveCount(4)
            ->and($parameters[0]->getName())->toBe('pcRelIds')
            ->and($parameters[1]->getName())->toBe('startDate')
            ->and($parameters[2]->getName())->toBe('dueDate')
            ->and($parameters[3]->getName())->toBe('canRenew')
            ->and($parameters[3]->allowsNull())->toBeTrue()
            ->and($parameters[3]->isOptional())->toBeTrue();
    });

    it('builds correct CTE query structure', function () {
        // Mock DB to capture the query structure
        $capturedQueries = [];

        DB::shouldReceive('table')
            ->andReturnUsing(function ($table) use (&$capturedQueries) {
                $capturedQueries[] = $table;
                $mockBuilder = Mockery::mock(Illuminate\Database\Query\Builder::class);

                // Mock all the query builder methods to return self
                $mockBuilder->shouldReceive('select')->andReturnSelf();
                $mockBuilder->shouldReceive('join')->andReturnSelf();
                $mockBuilder->shouldReceive('leftJoin')->andReturnSelf();
                $mockBuilder->shouldReceive('where')->andReturnSelf();
                $mockBuilder->shouldReceive('whereRaw')->andReturnSelf();
                $mockBuilder->shouldReceive('groupBy')->andReturnSelf();
                $mockBuilder->shouldReceive('addBinding')->andReturnSelf();
                $mockBuilder->shouldReceive('when')->andReturnSelf();
                $mockBuilder->shouldReceive('withExpression')->andReturnSelf();
                $mockBuilder->shouldReceive('selectRaw')->andReturnSelf();
                $mockBuilder->shouldReceive('get')->andReturn(collect([]));

                return $mockBuilder;
            });

        DB::shouldReceive('raw')->andReturn('mocked_raw');

        // Call the method
        $result = ContractPlot::getPlotsForRenew([1], '2024-01-01', '2024-12-31');

        // Verify the expected tables were accessed
        expect($capturedQueries)->toContain('su_contracts')
            ->and($capturedQueries)->toContain('result')
            ->and($result)->toBeArray();
    });

    it('handles optional canRenew parameter correctly', function () {
        $whenCallCount = 0;

        DB::shouldReceive('table')
            ->andReturnUsing(function () use (&$whenCallCount) {
                $mockBuilder = Mockery::mock(Illuminate\Database\Query\Builder::class);

                $mockBuilder->shouldReceive('select')->andReturnSelf();
                $mockBuilder->shouldReceive('join')->andReturnSelf();
                $mockBuilder->shouldReceive('leftJoin')->andReturnSelf();
                $mockBuilder->shouldReceive('where')->andReturnSelf();
                $mockBuilder->shouldReceive('whereRaw')->andReturnSelf();
                $mockBuilder->shouldReceive('groupBy')->andReturnSelf();
                $mockBuilder->shouldReceive('addBinding')->andReturnSelf();
                $mockBuilder->shouldReceive('withExpression')->andReturnSelf();
                $mockBuilder->shouldReceive('selectRaw')->andReturnSelf();
                $mockBuilder->shouldReceive('get')->andReturn(collect([]));

                // Track when() calls to verify conditional logic
                $mockBuilder->shouldReceive('when')
                    ->andReturnUsing(function ($condition, $callback) use ($mockBuilder, &$whenCallCount) {
                        $whenCallCount++;
                        if ($condition) {
                            return $callback($mockBuilder);
                        }

                        return $mockBuilder;
                    });

                return $mockBuilder;
            });

        DB::shouldReceive('raw')->andReturn('mocked_raw');

        // Test with canRenew = null (should not add where clause)
        ContractPlot::getPlotsForRenew([1], '2024-01-01', '2024-12-31', null);

        // Test with canRenew = true (should add where clause)
        ContractPlot::getPlotsForRenew([1, 2], '2024-01-01', '2024-12-31', true);

        // Test with canRenew = false (should add where clause)
        ContractPlot::getPlotsForRenew([3], '2024-01-01', '2024-12-31', false);

        // Verify that when() was called (indicating conditional logic was executed)
        expect($whenCallCount)->toBeGreaterThan(0);
    });

    it('uses correct CTE names in withExpression calls', function () {
        $cteNames = [];

        DB::shouldReceive('table')
            ->andReturnUsing(function () use (&$cteNames) {
                $mockBuilder = Mockery::mock(Illuminate\Database\Query\Builder::class);

                $mockBuilder->shouldReceive('select')->andReturnSelf();
                $mockBuilder->shouldReceive('join')->andReturnSelf();
                $mockBuilder->shouldReceive('leftJoin')->andReturnSelf();
                $mockBuilder->shouldReceive('where')->andReturnSelf();
                $mockBuilder->shouldReceive('whereRaw')->andReturnSelf();
                $mockBuilder->shouldReceive('groupBy')->andReturnSelf();
                $mockBuilder->shouldReceive('addBinding')->andReturnSelf();
                $mockBuilder->shouldReceive('when')->andReturnSelf();
                $mockBuilder->shouldReceive('selectRaw')->andReturnSelf();
                $mockBuilder->shouldReceive('get')->andReturn(collect([]));

                $mockBuilder->shouldReceive('withExpression')
                    ->andReturnUsing(function ($name, $query) use ($mockBuilder, &$cteNames) {
                        $cteNames[] = $name;

                        return $mockBuilder;
                    });

                return $mockBuilder;
            });

        DB::shouldReceive('raw')->andReturn('mocked_raw');

        ContractPlot::getPlotsForRenew([1, 2, 3], '2024-01-01', '2024-12-31');

        // Verify all expected CTE names are used
        expect($cteNames)->toContain('base_year_contracts')
            ->and($cteNames)->toContain('next_year_contracts')
            ->and($cteNames)->toContain('prepared')
            ->and($cteNames)->toContain('result');
    });

    it('processes result correctly with JSON decoding', function () {
        $mockResult = collect([
            (object) [
                'plot_id' => 1,
                'kad_ident' => 'TEST123',
                'document_area' => 100.0,
                'contract_area' => 80.0,
                'contract_area_next_period' => 0.0,
                'total_rows' => 1,
                'can_renew_count' => 1,
                'cannot_renew_count' => 0,
                'contracts_json' => '{"contract_id": 1, "can_renew": true}',
            ],
        ]);

        DB::shouldReceive('table')
            ->andReturnUsing(function () use ($mockResult) {
                $mockBuilder = Mockery::mock(Illuminate\Database\Query\Builder::class);

                $mockBuilder->shouldReceive('select')->andReturnSelf();
                $mockBuilder->shouldReceive('join')->andReturnSelf();
                $mockBuilder->shouldReceive('leftJoin')->andReturnSelf();
                $mockBuilder->shouldReceive('where')->andReturnSelf();
                $mockBuilder->shouldReceive('whereRaw')->andReturnSelf();
                $mockBuilder->shouldReceive('groupBy')->andReturnSelf();
                $mockBuilder->shouldReceive('addBinding')->andReturnSelf();
                $mockBuilder->shouldReceive('when')->andReturnSelf();
                $mockBuilder->shouldReceive('withExpression')->andReturnSelf();
                $mockBuilder->shouldReceive('selectRaw')->andReturnSelf();
                $mockBuilder->shouldReceive('get')->andReturn($mockResult);

                return $mockBuilder;
            });

        DB::shouldReceive('raw')->andReturn('mocked_raw');

        $result = ContractPlot::getPlotsForRenew([1], '2024-01-01', '2024-12-31');

        expect($result)->toBeArray()
            ->and($result)->toHaveCount(1)
            ->and($result[0]['contracts_json'])->toBeArray()
            ->and($result[0]['contracts_json']['contract_id'])->toBe(1)
            ->and($result[0]['contracts_json']['can_renew'])->toBeTrue();
    });

    it('returns empty array when no results found', function () {
        DB::shouldReceive('table')
            ->andReturnUsing(function () {
                $mockBuilder = Mockery::mock(Illuminate\Database\Query\Builder::class);

                $mockBuilder->shouldReceive('select')->andReturnSelf();
                $mockBuilder->shouldReceive('join')->andReturnSelf();
                $mockBuilder->shouldReceive('leftJoin')->andReturnSelf();
                $mockBuilder->shouldReceive('where')->andReturnSelf();
                $mockBuilder->shouldReceive('whereRaw')->andReturnSelf();
                $mockBuilder->shouldReceive('groupBy')->andReturnSelf();
                $mockBuilder->shouldReceive('addBinding')->andReturnSelf();
                $mockBuilder->shouldReceive('when')->andReturnSelf();
                $mockBuilder->shouldReceive('withExpression')->andReturnSelf();
                $mockBuilder->shouldReceive('selectRaw')->andReturnSelf();
                $mockBuilder->shouldReceive('get')->andReturn(collect([]));

                return $mockBuilder;
            });

        DB::shouldReceive('raw')->andReturn('mocked_raw');

        $result = ContractPlot::getPlotsForRenew([999], '2024-01-01', '2024-12-31');

        expect($result)->toBeArray()
            ->and($result)->toHaveCount(0);
    });
});
