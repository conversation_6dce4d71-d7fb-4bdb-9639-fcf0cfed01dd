<?php

declare(strict_types=1);

use App\Services\Documents\Contracts\ContractsService;
use App\Models\UserDb\Documents\Contracts\ContractPlot;

beforeEach(function () {
    $this->contractsService = new ContractsService();
});

describe('getPlotsForRenew', function () {
    it('has the correct method signature', function () {
        // Test that the method exists and has the correct signature
        expect(method_exists($this->contractsService, 'getPlotsForRenew'))->toBeTrue();

        // Test with reflection to verify method signature
        $reflection = new ReflectionMethod($this->contractsService, 'getPlotsForRenew');
        $parameters = $reflection->getParameters();

        expect($parameters)->toHaveCount(4)
            ->and($parameters[0]->getName())->toBe('pcRelIds')
            ->and($parameters[1]->getName())->toBe('startDate')
            ->and($parameters[2]->getName())->toBe('dueDate')
            ->and($parameters[3]->getName())->toBe('canRenew')
            ->and($parameters[3]->allowsNull())->toBeTrue()
            ->and($parameters[3]->isOptional())->toBeTrue();
    });

    it('delegates to ContractPlot model method', function () {
        // Mock the ContractPlot static method
        $expectedResult = [
            [
                'plot_id' => 1,
                'kad_ident' => 'TEST123',
                'contracts_json' => ['contract_id' => 1, 'can_renew' => true],
            ],
        ];

        // Create a partial mock of ContractPlot
        $contractPlotMock = Mockery::mock('alias:' . ContractPlot::class);
        $contractPlotMock->shouldReceive('getPlotsForRenew')
            ->once()
            ->with([1], '2024-01-01', '2024-12-31', null)
            ->andReturn($expectedResult);

        $result = $this->contractsService->getPlotsForRenew([1], '2024-01-01', '2024-12-31');

        expect($result)->toBe($expectedResult);
    });

    it('passes all parameters correctly to model method', function () {
        // Test with all parameters including canRenew
        $contractPlotMock = Mockery::mock('alias:' . ContractPlot::class);
        $contractPlotMock->shouldReceive('getPlotsForRenew')
            ->once()
            ->with([123], '2023-01-01', '2023-12-31', true)
            ->andReturn([]);

        $result = $this->contractsService->getPlotsForRenew([123], '2023-01-01', '2023-12-31', true);

        expect($result)->toBeArray();
    });

    it('passes canRenew false parameter correctly', function () {
        $contractPlotMock = Mockery::mock('alias:' . ContractPlot::class);
        $contractPlotMock->shouldReceive('getPlotsForRenew')
            ->once()
            ->with([456], '2025-01-01', '2025-12-31', false)
            ->andReturn([]);

        $result = $this->contractsService->getPlotsForRenew([456], '2025-01-01', '2025-12-31', false);

        expect($result)->toBeArray();
    });

    it('returns the exact result from model method', function () {
        $modelResult = [
            [
                'plot_id' => 1,
                'kad_ident' => 'ABC123',
                'document_area' => 150.5,
                'contract_area' => 120.0,
                'contracts_json' => [
                    'contract_id' => 1,
                    'can_renew' => true,
                    'farming_name' => 'Test Farm',
                ],
            ],
            [
                'plot_id' => 2,
                'kad_ident' => 'DEF456',
                'document_area' => 200.0,
                'contract_area' => 180.0,
                'contracts_json' => [
                    'contract_id' => 2,
                    'can_renew' => false,
                    'farming_name' => 'Another Farm',
                ],
            ],
        ];

        $contractPlotMock = Mockery::mock('alias:' . ContractPlot::class);
        $contractPlotMock->shouldReceive('getPlotsForRenew')
            ->once()
            ->andReturn($modelResult);

        $result = $this->contractsService->getPlotsForRenew([1], '2024-01-01', '2024-12-31');

        expect($result)->toBe($modelResult)
            ->and($result)->toHaveCount(2)
            ->and($result[0]['plot_id'])->toBe(1)
            ->and($result[1]['plot_id'])->toBe(2);
    });
});
