image: atlassian/default-image:4

options:
  docker: true

definitions:
  steps:
    - step: &build
        name: "Build"
        runs-on:
          - "self.hosted"
          - "linux"
          - "gs"
          - "build"
        services:
          - docker
        script:
          - echo "Building the image and push to docker hub registry"
          - export PATH=/usr/bin:$PATH
          - export DOCKER_BUILDKIT=0
          - docker login -u $DOCKERHUB_USER -p $DOCKERHUB_PASSWORD
          - docker-compose -f docker-compose.cd.yml build --no-cache --force-rm --compress
          - export BITBUCKET_COMMIT_SHORT=$(echo $BITBUCKET_COMMIT | cut -c1-7)
          - export ISSUE_NUMBER=$(echo $BITBUCKET_BRANCH | sed "s/\//_/g")
          - export IMAGE_TAG_NAME=$ISSUE_NUMBER-$BITBUCKET_COMMIT_SHORT
          - docker tag ${BITBUCKET_REPO_SLUG}:${BITBUCKET_COMMIT} ${DOCKERHUB_REPO_NAME}:${IMAGE_TAG_NAME}
          - docker tag ${BITBUCKET_REPO_SLUG}-nginx:${BITBUCKET_COMMIT} ${DOCKERHUB_REPO_NAME}-nginx:${IMAGE_TAG_NAME}
          - docker push ${DOCKERHUB_REPO_NAME}:${IMAGE_TAG_NAME}
          - docker push ${DOCKERHUB_REPO_NAME}-nginx:${IMAGE_TAG_NAME}
          - docker logout
          - echo ${DOCKERHUB_REPO_NAME}:${IMAGE_TAG_NAME} "is pushed to Docker Hub"
    - step: &deploy
        name: "Deploy on Staging"
        runs-on:
          - self.hosted
          - gs
          - linux
        image: willhallonline/ansible:2.18-alpine-3.20
        services:
          - docker
        script:
          - export DOCKER_REGISTRY_PASSWORD=$DOCKERHUB_PASSWORD
          - export DOCKER_REGISTRY_USERNAME=$DOCKERHUB_USER
          - export ANSIBLE_HOST_KEY_CHECKING="False"
          - export ISSUE_NUMBER=$(echo $BITBUCKET_BRANCH | sed "s/\//_/g")
          - export BITBUCKET_COMMIT_SHORT=$(echo $BITBUCKET_COMMIT | cut -c1-7)
          - export IMAGE_TAG_NAME=$ISSUE_NUMBER-$BITBUCKET_COMMIT_SHORT
          - export DOCKERHUB_REPO_NAME=$DOCKERHUB_REPO_NAME
          - export CONTAINER_NAME=$CONTAINER_NAME
          - export APP_ENV=$APP_ENV
          - export APP_KEY=$APP_KEY
          - export USER_ID=$USER_ID
          - export GROUP_ID=$GROUP_ID
          - export USER_NAME=$USER_NAME
          - export USER_GROUP=$USER_GROUP
          - export APP_DIR=$APP_DIR
          - export MAIN_DB_HOST=$MAIN_DB_HOST
          - export MAIN_DB_PORT=$MAIN_DB_PORT
          - export MAIN_DB_DATABASE=$MAIN_DB_DATABASE
          - export MAIN_DB_USERNAME=$MAIN_DB_USERNAME
          - export MAIN_DB_PASSWORD=$MAIN_DB_PASSWORD
          - export REDIS_HOST=$REDIS_HOST
          - export REDIS_PORT=$REDIS_PORT
          - export LOG_CHANNEL=$LOG_CHANNEL
          - export PLOTS_CHUNK_SIZE=$PLOTS_CHUNK_SIZE
          - export KEYCLOAK_CLIENT_ID=$KEYCLOAK_CLIENT_ID
          - export KEYCLOAK_CLIENT_SECRET=$KEYCLOAK_CLIENT_SECRET
          - export KEYCLOAK_BASE_URL=$KEYCLOAK_BASE_URL
          - export KEYCLOAK_REALM=$KEYCLOAK_REALM
          - export CACHE_STORE=$CACHE_STORE
          - export QUEUE_CONNECTION=$QUEUE_CONNECTION
          - export SESSION_DRIVER=$SESSION_DRIVER
          - export SESSION_CONNECTION=$SESSION_CONNECTION
          - export SENTRY_LARAVEL_DSN=$SENTRY_LARAVEL_DSN
          - export SENTRY_TRACES_SAMPLE_RATE=$SENTRY_TRACES_SAMPLE_RATE
          - export SENTRY_PROFILES_SAMPLE_RATE=$SENTRY_PROFILES_SAMPLE_RATE
          - export NGINX_PORT=$NGINX_PORT
          - export DB_CONNECTION=$DB_CONNECTION
          - ansible-galaxy collection install community.docker
          - ansible-playbook -i .ansible/hosts ansible-deploy.yml -l staging

pipelines:
  custom:
    "Staging":
      - step: *build
      - step:
          <<: *deploy
          deployment: staging

    "Production":
      - step: *build
      - step:
          name: "Deploy on prod"
          runs-on:
            - self.hosted
            - gs
            - linux
          image: atlassian/pipelines-kubectl
          deployment: production
          trigger: manual
          services:
            - docker
          script:
            - export DOCKER_REGISTRY_PASSWORD=$DOCKERHUB_PASSWORD
            - export DOCKER_REGISTRY_USERNAME=$DOCKERHUB_USER
            - export ANSIBLE_HOST_KEY_CHECKING="False"
            - export ISSUE_NUMBER=$(echo $BITBUCKET_BRANCH | sed "s/\//_/g")
            - export BITBUCKET_COMMIT_SHORT=$(echo $BITBUCKET_COMMIT | cut -c1-7)
            - export IMAGE_TAG_NAME=$ISSUE_NUMBER-$BITBUCKET_COMMIT_SHORT
            - export DOCKERHUB_REPO_NAME=$DOCKERHUB_REPO_NAME
            - export CONTAINER_NAME=$CONTAINER_NAME
            - export APP_ENV=$APP_ENV
            - export APP_KEY=$APP_KEY
            - export USER_ID=$USER_ID
            - export GROUP_ID=$GROUP_ID
            - export USER_NAME=$USER_NAME
            - export USER_GROUP=$USER_GROUP
            - export APP_DIR=$APP_DIR
            - export MAIN_DB_HOST=$MAIN_DB_HOST
            - export MAIN_DB_PORT=$MAIN_DB_PORT
            - export MAIN_DB_DATABASE=$MAIN_DB_DATABASE
            - export MAIN_DB_USERNAME=$MAIN_DB_USERNAME
            - export MAIN_DB_PASSWORD=$MAIN_DB_PASSWORD
            - export USER_DB_HOST=$USER_DB_HOST
            - export USER_DB_PORT=$USER_DB_PORT
            - export USER_DB_USERNAME=$USER_DB_USERNAME
            - export USER_DB_PASSWORD=$USER_DB_PASSWORD
            - export REDIS_HOST=$REDIS_HOST
            - export REDIS_PORT=$REDIS_PORT
            - export REDIS_PASSWORD=$REDIS_PASSWORD
            - export REDIS_CLUSTER_MODE=$REDIS_CLUSTER_MODE
            - export REDIS_CACHE_CONNECTION=$REDIS_CACHE_CONNECTION
            - export LOG_CHANNEL=$LOG_CHANNEL
            - export PLOTS_CHUNK_SIZE=$PLOTS_CHUNK_SIZE
            - export KEYCLOAK_CLIENT_ID=$KEYCLOAK_CLIENT_ID
            - export KEYCLOAK_CLIENT_SECRET=$KEYCLOAK_CLIENT_SECRET
            - export KEYCLOAK_BASE_URL=$KEYCLOAK_BASE_URL
            - export KEYCLOAK_REALM=$KEYCLOAK_REALM
            - export CACHE_STORE=$CACHE_STORE
            - export REDIS_CLUSTER=$REDIS_CLUSTER
            - export QUEUE_CONNECTION=$QUEUE_CONNECTION
            - export SESSION_DRIVER=$SESSION_DRIVER
            - export SESSION_CONNECTION=$SESSION_CONNECTION
            - export KUBE_TOKEN=$KUBE_TOKEN
            - export KUBE_CA=$KUBE_CA
            - export CONFIGMAP=$CONFIGMAP
            - export KUBE_NAMESPACE=$KUBE_NAMESPACE
            - echo $KUBE_TOKEN | base64 -d > ./kube_token
            - echo $KUBE_CA | base64 -d > ./kube_ca
            - echo "CONTAINER_NAME=${CONTAINER_NAME}" > .kube/configmap-env-file
            - echo "APP_ENV=${APP_ENV}" >> .kube/configmap-env-file
            - echo "APP_KEY=${APP_KEY}" >> .kube/configmap-env-file
            - echo "USER_ID=${USER_ID}" >> .kube/configmap-env-file
            - echo "GROUP_ID=${GROUP_ID}" >> .kube/configmap-env-file
            - echo "USER_NAME=${USER_NAME}" >> .kube/configmap-env-file
            - echo "USER_GROUP=${USER_GROUP}" >> .kube/configmap-env-file
            - echo "APP_DIR=${APP_DIR}" >> .kube/configmap-env-file
            - echo "MAIN_DB_HOST=${MAIN_DB_HOST}" >> .kube/configmap-env-file
            - echo "MAIN_DB_PORT=${MAIN_DB_PORT}" >> .kube/configmap-env-file
            - echo "MAIN_DB_DATABASE=${MAIN_DB_DATABASE}" >> .kube/configmap-env-file
            - echo "MAIN_DB_USERNAME=${MAIN_DB_USERNAME}" >> .kube/configmap-env-file
            - echo "MAIN_DB_PASSWORD=${MAIN_DB_PASSWORD}" >> .kube/configmap-env-file
            - echo "USER_DB_HOST=${USER_DB_HOST}" >> .kube/configmap-env-file
            - echo "USER_DB_PORT=${USER_DB_PORT}" >> .kube/configmap-env-file
            - echo "USER_DB_USERNAME=${USER_DB_USERNAME}" >> .kube/configmap-env-file
            - echo "USER_DB_PASSWORD=${USER_DB_PASSWORD}" >> .kube/configmap-env-file
            - echo "REDIS_HOST=${REDIS_HOST}" >> .kube/configmap-env-file
            - echo "REDIS_PORT=${REDIS_PORT}" >> .kube/configmap-env-file
            - echo "REDIS_PASSWORD=${REDIS_PASSWORD}" >> .kube/configmap-env-file
            - echo "REDIS_CLUSTER_MODE=${REDIS_CLUSTER_MODE}" >> .kube/configmap-env-file
            - echo "REDIS_CACHE_CONNECTION=${REDIS_CACHE_CONNECTION}" >> .kube/configmap-env-file
            - echo "LOG_CHANNEL=${LOG_CHANNEL}" >> .kube/configmap-env-file
            - echo "PLOTS_CHUNK_SIZE=${PLOTS_CHUNK_SIZE}" >> .kube/configmap-env-file
            - echo "KEYCLOAK_CLIENT_ID=${KEYCLOAK_CLIENT_ID}" >> .kube/configmap-env-file
            - echo "KEYCLOAK_CLIENT_SECRET=${KEYCLOAK_CLIENT_SECRET}" >> .kube/configmap-env-file
            - echo "KEYCLOAK_BASE_URL=${KEYCLOAK_BASE_URL}" >> .kube/configmap-env-file
            - echo "KEYCLOAK_REALM=${KEYCLOAK_REALM}" >> .kube/configmap-env-file
            - echo "CACHE_STORE=${CACHE_STORE}" >> .kube/configmap-env-file
            - echo "REDIS_CLUSTER=${REDIS_CLUSTER}" >> .kube/configmap-env-file
            - echo "QUEUE_CONNECTION=${QUEUE_CONNECTION}" >> .kube/configmap-env-file
            - echo "SESSION_DRIVER=${SESSION_DRIVER}" >> .kube/configmap-env-file
            - echo "SESSION_CONNECTION=${SESSION_CONNECTION}" >> .kube/configmap-env-file
            - echo "BATCH_RETRY_LIMIT=${BATCH_RETRY_LIMIT}" >> .kube/configmap-env-file
            - echo "DB_CONNECTION=${DB_CONNECTION}" >> .kube/configmap-env-file
            - echo "SENTRY_LARAVEL_DSN=${SENTRY_LARAVEL_DSN}" >> .kube/configmap-env-file
            - echo "SENTRY_TRACES_SAMPLE_RATE=${SENTRY_TRACES_SAMPLE_RATE}" >> .kube/configmap-env-file
            - echo "SENTRY_PROFILES_SAMPLE_RATE=${SENTRY_PROFILES_SAMPLE_RATE}" >> .kube/configmap-env-file
            - kubectl config set-cluster $KUBE_CLUSTER --server=$KUBE_SERVER --certificate-authority="$(pwd)/kube_ca"
            - kubectl config set-credentials $KUBE_SA --token="$(cat ./kube_token)"
            - kubectl config set-context $KUBE_NAMESPACE --cluster=$KUBE_CLUSTER --user=$KUBE_SA
            - kubectl config use-context $KUBE_NAMESPACE
            - sed -i "s|REPLACE_CONTAINER_NAME|${CONTAINER_NAME}|g" .kube/*.yaml
            - sed -i "s|REPLACE_KUBE_NAMESPACE|${KUBE_NAMESPACE}|g" .kube/*.yaml
            - sed -i "s|REPLACE_REPO|${DOCKERHUB_REPO_NAME}|g" .kube/*.yaml
            - sed -i "s|REPLACE_TAG|${IMAGE_TAG_NAME}|g" .kube/*.yaml
            - sed -i "s|REPLACE_CONFIGMAP|${CONFIGMAP}|g" .kube/*.yaml
            - sed -i "s|REPLACE_HOSTNAME|${HOSTNAME}|g" .kube/*.yaml
            #Create Configmap
            - kubectl create configmap ${CONFIGMAP} --from-env-file=.kube/configmap-env-file --dry-run=client -o yaml | kubectl -n $KUBE_NAMESPACE apply -f -
            #Api/Queue Deployment
            - kubectl apply -f .kube/deployment-technofarm-laravel.yaml
            - kubectl rollout restart deployment/${CONTAINER_NAME} -n $KUBE_NAMESPACE
            - kubectl apply -f .kube/service-technofarm-laravel.yaml
            #Nginx Deployment
            - kubectl apply -f .kube/deployment-technofarm-laravel-nginx.yaml
            - kubectl rollout restart deployment/${CONTAINER_NAME}-nginx -n $KUBE_NAMESPACE
            - kubectl apply -f .kube/service-technofarm-laravel-nginx.yaml
            #Ingress
            - kubectl apply -f .kube/ingress.yaml
            #Redis
            - kubectl apply -f .kube/redis.yaml